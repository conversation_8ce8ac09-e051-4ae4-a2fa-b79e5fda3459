(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d0c0c06"],{"42b4":function(e,t,a){"use strict";a.r(t);var o=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:e.showLeft}},[a("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],ref:"queryForm",staticClass:"query",attrs:{inline:!0,model:e.queryParams,size:"mini"}},[a("el-form-item",{attrs:{label:"搜索",prop:"accountQuery"}},[a("el-input",{staticStyle:{width:"100%"},attrs:{clearable:"",placeholder:"输入搜索名字"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.accountQuery,callback:function(t){e.$set(e.queryParams,"accountQuery",t)},expression:"queryParams.accountQuery"}})],1),a("el-form-item",{attrs:{label:"币种",prop:"currencyId"}},[a("tree-select",{staticStyle:{width:"100%"},attrs:{pass:e.queryParams.currencyId,placeholder:"默认币种",type:"currency"},on:{return:e.queryCurrencyId}})],1),a("el-form-item",[a("el-button",{attrs:{icon:"el-icon-search",size:"mini",type:"primary"},on:{click:e.handleQuery}},[e._v("搜索")]),a("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1)],1),a("el-col",{attrs:{span:e.showRight}},[a("el-row",{staticClass:"mb8",attrs:{gutter:10}},[a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:account:add"],expression:"['system:account:add']"}],attrs:{icon:"el-icon-plus",plain:"",size:"mini",type:"primary"},on:{click:e.handleAdd}},[e._v("新增 ")])],1),a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:account:remove"],expression:"['system:account:remove']"}],attrs:{disabled:e.multiple,icon:"el-icon-delete",plain:"",size:"mini",type:"danger"},on:{click:e.handleDelete}},[e._v("删除 ")])],1),a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:account:export"],expression:"['system:account:export']"}],attrs:{icon:"el-icon-download",plain:"",size:"mini",type:"warning"},on:{click:e.handleExport}},[e._v("导出 ")])],1),a("right-toolbar",{attrs:{showSearch:e.showSearch},on:{"update:showSearch":function(t){e.showSearch=t},"update:show-search":function(t){e.showSearch=t},queryTable:e.getList}})],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.accountList},on:{"selection-change":e.handleSelectionChange}},[a("el-table-column",{attrs:{align:"left",type:"selection",width:"39px"}}),a("el-table-column",{attrs:{align:"center",label:"银行账户",prop:"accountCode",width:"68px"}}),a("el-table-column",{attrs:{align:"center",label:"归属人","show-tooltip-when-overflow":""},scopedSlots:e._u([{key:"default",fn:function(t){var o=t.row;return["0"==o.belongTo?a("div",{staticStyle:{margin:"0",overflow:"hidden","text-overflow":"ellipsis","white-space":"nowrap"}},[e._v(" "+e._s((null!=o.rsStaff.staffFamilyLocalName?o.rsStaff.staffFamilyLocalName:"")+" "+(null!=o.rsStaff.staffGivingLocalName?o.rsStaff.staffGivingLocalName:""))+" ")]):e._e(),"1"==o.belongTo?a("div",{staticStyle:{margin:"0",overflow:"hidden","text-overflow":"ellipsis","white-space":"nowrap"}},[e._v(" "+e._s(null!=o.company.companyLocalName?o.company.companyLocalName:"")+" ")]):e._e()]}}])}),a("el-table-column",{attrs:{align:"center",label:"币种ID",prop:"currencyId"}}),a("el-table-column",{attrs:{align:"left",label:"银行本名"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(t.row.accountLocalName)+" "+e._s(t.row.accountEnName)+" ")]}}])}),a("el-table-column",{attrs:{align:"center",label:"银行地址",prop:"accountAddressLocalName"}}),a("el-table-column",{attrs:{align:"center",label:"银行英文地址",prop:"accountAddressEnName"}}),a("el-table-column",{attrs:{align:"center",label:"公司税号",prop:"accountTaxcode"}}),a("el-table-column",{attrs:{align:"center",label:"SWIFT编码",prop:"accountSwiftcode"}}),a("el-table-column",{attrs:{align:"center",label:"国际汇款编码",prop:"accountIntlcode"}}),a("el-table-column",{attrs:{align:"center",label:"是否锁定",prop:"isLocked",width:"69px"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("dict-tag",{attrs:{options:e.dict.type.sys_is_lock,value:t.row.isLocked}})]}}])}),a("el-table-column",{attrs:{align:"center",label:"经理确认时间",prop:"deptConfirmedDate",width:"100px"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(e.parseTime(t.row.deptConfirmedDate,"{y}-{m}-{d}")))])]}}])}),a("el-table-column",{attrs:{align:"center",label:"财务确认时间",prop:"financeConfirmedDate",width:"100px"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(e.parseTime(t.row.financeConfirmedDate,"{y}-{m}-{d}")))])]}}])}),a("el-table-column",{attrs:{align:"center","class-name":"small-padding fixed-width",label:"操作"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:account:edit"],expression:"['system:account:edit']"}],staticStyle:{"margin-right":"-8px"},attrs:{icon:"el-icon-edit",size:"mini",type:"success"},on:{click:function(a){return e.handleUpdate(t.row)}}},[e._v("修改 ")]),a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:account:remove"],expression:"['system:account:remove']"}],staticStyle:{"margin-right":"-8px"},attrs:{icon:"el-icon-delete",size:"mini",type:"danger"},on:{click:function(a){return e.handleDelete(t.row)}}},[e._v("删除 ")])]}}])})],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total>0"}],attrs:{limit:e.queryParams.pageSize,page:e.queryParams.pageNum,total:e.total},on:{"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},pagination:e.getList}})],1)],1),a("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"},{name:"dialogDragWidth",rawName:"v-dialogDragWidth"}],attrs:{"close-on-click-modal":!1,"modal-append-to-body":!1,title:e.title,visible:e.open,"append-to-body":"",width:"500px"},on:{"update:visible":function(t){e.open=t}}},[a("el-form",{ref:"form",staticClass:"edit",attrs:{model:e.form,rules:e.rules,"label-width":"100px"}},[a("el-form-item",{attrs:{label:"银行账户",prop:"accountCode"}},[a("el-input",{attrs:{placeholder:"银行账户"},model:{value:e.form.accountCode,callback:function(t){e.$set(e.form,"accountCode",t)},expression:"form.accountCode"}})],1),a("el-form-item",{attrs:{label:"银行账户归属",prop:"belongTo"}},[a("el-input",{attrs:{placeholder:"银行账户归属人"},model:{value:e.form.belongTo,callback:function(t){e.$set(e.form,"belongTo",t)},expression:"form.belongTo"}})],1),"0"==e.form.belongTo?a("el-form-item",{attrs:{label:"账户归属人",prop:"accountOwnerId"}},[a("el-input",{attrs:{placeholder:"银行账户归属公司"},model:{value:e.form.accountOwnerId,callback:function(t){e.$set(e.form,"accountOwnerId",t)},expression:"form.accountOwnerId"}})],1):e._e(),"1"==e.form.belongTo?a("el-form-item",{attrs:{label:"账户归属人",prop:"accountOwnerId"}},[a("el-input",{attrs:{placeholder:"银行账户归属员工"},model:{value:e.form.accountOwnerId,callback:function(t){e.$set(e.form,"accountOwnerId",t)},expression:"form.accountOwnerId"}})],1):e._e(),"2"==e.form.belongTo?a("el-form-item",{attrs:{label:"账户归属人",prop:"accountOwnerId"}},[a("el-input",{attrs:{placeholder:"银行账户归属人"},model:{value:e.form.accountOwnerId,callback:function(t){e.$set(e.form,"accountOwnerId",t)},expression:"form.accountOwnerId"}})],1):e._e(),a("el-form-item",{attrs:{label:"银行本名",prop:"accountLocalName"}},[a("el-input",{attrs:{placeholder:"银行本名"},model:{value:e.form.accountLocalName,callback:function(t){e.$set(e.form,"accountLocalName",t)},expression:"form.accountLocalName"}})],1),a("el-form-item",{attrs:{label:"银行地址",prop:"accountAddressLocalName"}},[a("el-input",{attrs:{placeholder:"银行地址"},model:{value:e.form.accountAddressLocalName,callback:function(t){e.$set(e.form,"accountAddressLocalName",t)},expression:"form.accountAddressLocalName"}})],1),a("el-form-item",{attrs:{label:"银行英文名",prop:"accountEnName"}},[a("el-input",{attrs:{placeholder:"银行英文名"},model:{value:e.form.accountEnName,callback:function(t){e.$set(e.form,"accountEnName",t)},expression:"form.accountEnName"}})],1),a("el-form-item",{attrs:{label:"银行英文地址",prop:"accountAddressEnName"}},[a("el-input",{attrs:{placeholder:"银行英文地址"},model:{value:e.form.accountAddressEnName,callback:function(t){e.$set(e.form,"accountAddressEnName",t)},expression:"form.accountAddressEnName"}})],1),a("el-form-item",{attrs:{label:"公司税号",prop:"accountTaxcode"}},[a("el-input",{attrs:{placeholder:"公司税号"},model:{value:e.form.accountTaxcode,callback:function(t){e.$set(e.form,"accountTaxcode",t)},expression:"form.accountTaxcode"}})],1),a("el-form-item",{attrs:{label:"SWIFT编码",prop:"accountSwiftcode"}},[a("el-input",{attrs:{placeholder:"SWIFT编码"},model:{value:e.form.accountSwiftcode,callback:function(t){e.$set(e.form,"accountSwiftcode",t)},expression:"form.accountSwiftcode"}})],1),a("el-form-item",{attrs:{label:"国际汇款编码",prop:"accountIntlcode"}},[a("el-input",{attrs:{placeholder:"国际汇款编码，用于非SWIFT的备用"},model:{value:e.form.accountIntlcode,callback:function(t){e.$set(e.form,"accountIntlcode",t)},expression:"form.accountIntlcode"}})],1),a("el-form-item",{attrs:{label:"公账还是私账",prop:"accountIsOfficial"}},[a("el-input",{attrs:{placeholder:"公账还是私账"},model:{value:e.form.accountIsOfficial,callback:function(t){e.$set(e.form,"accountIsOfficial",t)},expression:"form.accountIsOfficial"}})],1),a("el-form-item",{attrs:{label:"银行卡优先级",prop:"accountPriority"}},[a("el-input-number",{staticStyle:{width:"100%"},attrs:{controls:!1,placeholder:"银行卡优先级"},model:{value:e.form.accountPriority,callback:function(t){e.$set(e.form,"accountPriority",t)},expression:"form.accountPriority"}})],1),a("el-form-item",{attrs:{label:"备注",prop:"remark"}},[a("el-input",{attrs:{placeholder:"备注"},model:{value:e.form.remark,callback:function(t){e.$set(e.form,"remark",t)},expression:"form.remark"}})],1),a("el-row",[a("el-form-item",{attrs:{label:"是否已锁定",prop:"isLocked"}},[a("dict-tag",{attrs:{options:e.dict.type.sys_is_lock,value:e.form.isLocked}})],1)],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"部门确认",prop:"deptConfirmed"}},[a("el-row",[a("el-col",{attrs:{span:4}},[a("dict-tag",{attrs:{options:e.dict.type.sys_is_confirm,value:e.form.deptConfirmed}})],1),a("el-col",{attrs:{span:20}},[0==e.form.deptConfirmed?a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:agreementrecord:deptlock"],expression:"['system:agreementrecord:deptlock']"}],attrs:{icon:"el-icon-lock",plain:"",size:"mini",type:"primary"},on:{click:e.deptLock}},[e._v("锁定 ")]):e._e(),1==e.form.deptConfirmed?a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:agreementrecord:deptunlock"],expression:"['system:agreementrecord:deptunlock']"}],attrs:{icon:"el-icon-unlock",plain:"",size:"mini",type:"primary"},on:{click:e.deptLock}},[e._v("解锁 ")]):e._e()],1)],1)],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"部门确认时间",prop:"deptConfirmedDate"}},[e._v(" "+e._s(e.form.deptConfirmedDate)+" ")])],1)],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"财务确认",prop:"financeConfirmed"}},[a("el-row",[a("el-col",{attrs:{span:4}},[a("dict-tag",{attrs:{options:e.dict.type.sys_is_confirm,value:e.form.financeConfirmed}})],1),a("el-col",{attrs:{span:20}},[0==e.form.financeConfirmed?a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:agreementrecord:financelock"],expression:"['system:agreementrecord:financelock']"}],attrs:{icon:"el-icon-lock",plain:"",size:"mini",type:"primary"},on:{click:e.financeLock}},[e._v("锁定 ")]):e._e(),1==e.form.financeConfirmed?a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:agreementrecord:financeunlock"],expression:"['system:agreementrecord:financeunlock']"}],attrs:{icon:"el-icon-unlock",plain:"",size:"mini",type:"primary"},on:{click:e.financeLock}},[e._v("解锁 ")]):e._e()],1)],1)],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"财务确认时间",prop:"financeConfirmedDate"}},[e._v(" "+e._s(e.form.financeConfirmedDate)+" ")])],1)],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("确 定")]),a("el-button",{on:{click:e.cancel}},[e._v("取 消")])],1)],1)],1)},n=[],r=a("5530"),c=(a("d81d"),a("a287")),l={name:"Account",dicts:["sys_is_lock","sys_is_confirm"],data:function(){return{showLeft:3,showRight:21,loading:!0,ids:[],single:!0,multiple:!0,showSearch:!1,total:0,accountList:[],title:"",open:!1,queryParams:{pageNum:1,pageSize:20,accountQuery:null,currencyId:null},form:{},rules:{accountOwnerId:[{required:!0,trigger:"blur"}]}}},watch:{showSearch:function(e){1==e?(this.showRight=21,this.showLeft=3):(this.showRight=24,this.showLeft=0)}},created:function(){this.getList()},methods:{getList:function(){var e=this;this.loading=!0,Object(c["e"])(this.queryParams).then((function(t){e.accountList=t.rows,e.total=t.total,e.loading=!1}))},deptLock:function(){var e=this;null!=this.form.accountId?(this.form.deptConfirmed=0==this.form.deptConfirmed?1:0,this.form.deptConfirmedId=this.$store.state.user.sid,Object(c["f"])(this.form).then((function(t){e.$modal.msgSuccess("修改成功"),e.open=!1,e.getList()}))):this.$modal.msgError("错误操作")},financeLock:function(){var e=this;null!=this.form.accountId?(this.form.financeConfirmed=0==this.form.financeConfirmed?1:0,this.form.financeConfirmedId=this.$store.state.user.sid,Object(c["f"])(this.form).then((function(t){e.$modal.msgSuccess("修改成功"),e.open=!1,e.getList()}))):this.$modal.msgError("错误操作")},cancel:function(){this.open=!1,this.reset()},reset:function(){this.form={accountId:null,accountCode:null,accountOwnerId:null,currencyId:null,accountLocalName:null,accountAddressLocalName:null,accountEnName:null,accountAddressEnName:null,accountTaxcode:null,accountSwiftcode:null,accountIntlcode:null,accountIsOfficial:null,accountPriority:null,remark:null},this.resetForm("form")},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.resetForm("queryForm"),this.handleQuery()},handleSelectionChange:function(e){this.ids=e.map((function(e){return e.accountId})),this.single=1!=e.length,this.multiple=!e.length},handleAdd:function(){this.reset(),this.open=!0,this.title="添加银行账户"},handleUpdate:function(e){var t=this;this.reset();var a=e.accountId||this.ids;Object(c["d"])(a).then((function(e){t.form=e.data,t.open=!0,t.title="修改银行账户"}))},submitForm:function(){var e=this;this.$refs["form"].validate((function(t){t&&(null!=e.form.accountId?Object(c["f"])(e.form).then((function(t){e.$modal.msgSuccess("修改成功"),e.open=!1,e.getList()})):Object(c["a"])(e.form).then((function(t){e.$modal.msgSuccess("新增成功"),e.open=!1,e.getList()})))}))},handleDelete:function(e){var t=this,a=e.accountId||this.ids;this.$confirm('是否确认删除银行账户编号为"'+a+'"的数据项？',"提示",{customClass:"modal-confirm"}).then((function(){return Object(c["c"])(a)})).then((function(){t.getList(),t.$modal.msgSuccess("删除成功")})).catch((function(){}))},handleExport:function(){this.download("system/account/export",Object(r["a"])({},this.queryParams),"account_".concat((new Date).getTime(),".xlsx"))},queryCurrencyId:function(e){this.queryParams.currencyId=e}}},i=l,s=a("2877"),m=Object(s["a"])(i,o,n,!1,null,null,null);t["default"]=m.exports}}]);