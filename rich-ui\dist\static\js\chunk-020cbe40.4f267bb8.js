(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-020cbe40","chunk-1255eb66","chunk-2d0ba824"],{"063e":function(e,t,r){"use strict";r("629f")},3809:function(e,t,r){"use strict";r.r(t);var a=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",[r("el-tooltip",{attrs:{placement:"top",disabled:null==e.scope.row.companyGrade||e.scope.row.companyGrade.length<3||((null!=e.scope.row.contractType?e.scope.row.contractType:"")+(null!=e.scope.row.contractType&&null!=e.scope.row.contractNo?"：":"")+(null!=e.scope.row.contractNo?e.scope.row.contractNo:"")).length<10}},[r("div",{attrs:{slot:"content"},slot:"content"},[r("h6",{staticStyle:{margin:"0"}},[e._v(" "+e._s(e.scope.row.companyGrade)+" ")]),r("h6",{staticStyle:{margin:"0","font-weight":"bold","font-size":"small"}},[e._v(" "+e._s((null!=e.scope.row.contractType?e.scope.row.contractType:"")+(null!=e.scope.row.contractType&&null!=e.scope.row.contractNo&&""!=e.scope.row.contractNo?"：":"")+(null!=e.scope.row.contractNo?e.scope.row.contractNo:""))+" ")])]),r("div",[r("h6",{staticStyle:{margin:"0",overflow:"hidden","text-overflow":"ellipsis","white-space":"nowrap"}},[e._v(" "+e._s(e.scope.row.companyGrade)+" ")]),r("h6",{staticStyle:{margin:"0",overflow:"hidden","text-overflow":"ellipsis","white-space":"nowrap"}},[e._v(" "+e._s((null!=e.scope.row.contractType?e.scope.row.contractType:"")+(null!=e.scope.row.contractType&&null!=e.scope.row.contractNo&&""!=e.scope.row.contractNo?"：":"")+(null!=e.scope.row.contractNo?e.scope.row.contractNo:""))+" ")])])])],1)},i=[],s={name:"company",props:["scope"],data:function(){return{size:this.$store.state.app.size||"mini"}}},n=s,l=r("2877"),o=Object(l["a"])(n,a,i,!1,null,"5ca2675e",null);t["default"]=o.exports},"629f":function(e,t,r){},c9a3:function(e,t,r){"use strict";r.r(t);var a=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",[r("el-tooltip",{attrs:{disabled:null==e.scope.row.company||e.scope.row.company.length<3||((null!=e.scope.row.contractType?e.scope.row.contractType:"")+(null!=e.scope.row.contractType&&null!=e.scope.row.contractNo?"：":"")+(null!=e.scope.row.contractNo?e.scope.row.contractNo:"")).length<10,placement:"top"}},[r("div",{attrs:{slot:"content"},slot:"content"},[r("div",{staticStyle:{margin:"0","font-size":"15px","font-weight":"bold"}},[e._v(e._s(e.scope.row.carrierCode)+" "),r("span",{staticClass:"unHighlight-text",staticStyle:{margin:"0","font-weight":"bold","font-size":"12px"}},[e._v(" "+e._s("("+(null!=e.scope.row.contractType?e.scope.row.contractType:"")+")")+" ")])]),e.checkRole(["Operator"])?r("h6",{staticClass:"unHighlight-text",staticStyle:{margin:"0"}},[e._v(" "+e._s(e.scope.row.company)+" ")]):e._e()]),r("div",{staticStyle:{margin:"0",overflow:"hidden","text-overflow":"ellipsis","white-space":"nowrap"}},[r("div",{staticStyle:{margin:"0","font-size":"15px","font-weight":"bold"}},[e._v(e._s(e.scope.row.carrierCode)+" "),r("span",{staticClass:"unHighlight-text",staticStyle:{margin:"0","font-weight":"bold","font-size":"12px"}},[e._v(" "+e._s("("+(null!=e.scope.row.contractType?e.scope.row.contractType:"")+")")+" ")])]),e.checkRole(["Operator"])?r("h6",{staticClass:"unHighlight-text",staticStyle:{margin:"0",overflow:"hidden","text-overflow":"ellipsis","white-space":"nowrap"}},[e._v(" "+e._s(e.scope.row.company)+" ")]):e._e()])])],1)},i=[],s=r("e350"),n={name:"carrier",methods:{checkRole:s["b"]},props:["scope"],data:function(){return{size:this.$store.state.app.size||"mini"}},computed:{contractType:function(){}}},l=n,o=(r("063e"),r("2877")),c=Object(o["a"])(l,a,i,!1,null,"2d15028d",null);t["default"]=c.exports},d370:function(e,t,r){"use strict";r.r(t);var a=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",{staticClass:"app-container"},[r("div",{attrs:{slot:"header"},slot:"header"},[r("div",{staticStyle:{display:"flex"}},[r("h3",{staticStyle:{"font-weight":"bold",margin:"5px"}},[e._v(" 运费查询："+e._s(e.queryParams.serviceType)+" ")]),r("div",{staticStyle:{position:"absolute",right:"10px"}},[r("el-button",{attrs:{size:e.size,icon:"el-icon-search",type:"success"},on:{click:e.handleQuery}},[e._v("运费查询")]),r("el-button",{attrs:{size:e.size,icon:"el-icon-search",type:"primary"},on:{click:e.handleRequire}},[e._v("请求更新")]),r("el-button",{staticStyle:{width:"97px"},attrs:{size:e.size,icon:"el-icon-refresh"},on:{click:e.reset}},[e._v("重置")])],1)]),r("el-form",{ref:"queryForm",attrs:{inline:!0,model:e.queryParams,"label-position":"top",size:"mini"}},[r("el-form-item",{attrs:{label:"服务项目",prop:"serviceTypeId"}},[r("tree-select",{staticStyle:{width:"80px"},attrs:{dbn:!0,flat:!1,multiple:!1,pass:e.queryParams.serviceTypeId,placeholder:"服务项目",type:"serviceType"},on:{returnData:e.queryServiceType}})],1),"8"!=e.queryParams.typeId?r("el-form-item",{attrs:{label:"启运港",prop:"departureId"}},[r("location-select",{staticStyle:{width:"120px"},attrs:{"check-port":e.queryParams.typeId,multiple:!1,pass:e.queryParams.departureId},on:{return:e.queryDepartureId}})],1):e._e(),"5"==e.queryParams.typeId?r("el-form-item",{attrs:{label:"装运区域",prop:"departureId"}},[r("location-select",{staticStyle:{width:"120px"},attrs:{multiple:!0,pass:e.queryParams.loadingIds},on:{return:e.queryPrecarriageRegionId}})],1):e._e(),"4"==e.queryParams.typeId?r("el-form-item",{attrs:{label:"目的区域",prop:"departureId"}},[r("location-select",{staticStyle:{width:"120px"},attrs:{"check-port":e.queryParams.typeId,multiple:!1,pass:e.queryParams.destinationId},on:{return:e.queryDestinationId}})],1):e._e(),"8"!=e.queryParams.typeId&&"4"!=e.queryParams.typeId&&"5"!=e.queryParams.typeId?r("el-form-item",{attrs:{label:"目的港",prop:"destinationId"}},[r("location-select",{staticStyle:{width:"120px"},attrs:{"check-port":e.queryParams.typeId,en:!0,multiple:!1,pass:e.queryParams.destinationId},on:{return:e.queryDestinationId}})],1):e._e(),r("el-form-item",{attrs:{label:"柜型",prop:"unitId"}},[r("tree-select",{staticStyle:{width:"100px"},attrs:{pass:e.queryParams.unitId,placeholder:"单位",type:"unit"},on:{return:e.queryUnitId,returnData:e.getUnitName}})],1),"1"==e.queryParams.typeId||"2"==e.queryParams.typeId||"3"==e.queryParams.typeId?r("el-form-item",{attrs:{label:"船东",prop:"queryCarrierIds"}},[r("treeselect",{staticStyle:{width:"100px"},attrs:{"disable-branch-nodes":!0,"disable-fuzzy-matching":!0,"flatten-search-results":!0,multiple:!0,normalizer:e.carrierNormalizer,options:e.carrierList,"show-count":!0,placeholder:"承运人"},on:{deselect:e.handleDeselectQueryCarrierIds,input:e.deselectAllQueryCarrierIds,open:e.loadCarrier,select:e.handleSelectQueryCarrierIds},scopedSlots:e._u([{key:"value-label",fn:function(t){var a=t.node;return r("div",{},[e._v(" "+e._s(a.raw.carrier.carrierIntlCode)+" "+e._s(null==a.raw.carrier.carrierIntlCode?a.raw.carrier.carrierShortName:"")+" ")])}},{key:"option-label",fn:function(t){var a=t.node,i=t.shouldShowCount,s=t.count,n=t.labelClassName,l=t.countClassName;return r("label",{class:n},[e._v(" "+e._s(-1!=a.label.indexOf(",")?a.label.substring(0,a.label.indexOf(",")):a.label)+" "),i?r("span",{class:l},[e._v("("+e._s(s)+")")]):e._e()])}}],null,!1,4006291921),model:{value:e.queryCarrierIds,callback:function(t){e.queryCarrierIds=t},expression:"queryCarrierIds"}})],1):e._e(),r("el-form-item",{attrs:{label:"相关客户",prop:"companyId"}},[r("treeselect",{staticStyle:{width:"100px"},attrs:{"disabled-branch-nodes":!0,"disabled-fuzzy-matching":!0,flat:!1,"flatten-search-results":!0,multiple:!1,normalizer:e.companyNormalizer,options:e.clients,"show-count":!0,placeholder:"关联单位"},on:{select:e.queryCompany},scopedSlots:e._u([{key:"value-label",fn:function(t){var a=t.node;return r("div",{},[e._v(" "+e._s(a.raw.companyShortName)+" ")])}},{key:"option-label",fn:function(t){var a=t.node,i=t.shouldShowCount,s=t.count,n=t.labelClassName,l=t.countClassName;return r("label",{class:n},[e._v(" "+e._s(-1!=a.label.indexOf(",")?a.label.substring(0,a.label.indexOf(",")):a.label)+" "),i?r("span",{class:l},[e._v("("+e._s(s)+")")]):e._e()])}}]),model:{value:e.queryParams.companyId,callback:function(t){e.$set(e.queryParams,"companyId",t)},expression:"queryParams.companyId"}})],1),r("el-form-item",{attrs:{label:"货物特征",prop:"cargoTypeIds"}},[r("tree-select",{staticStyle:{width:"120px"},attrs:{multiple:!0,pass:e.queryParams.cargoTypeIds,placeholder:"货物特征",type:"cargoType"},on:{return:e.queryCargoTypeIds,returnSelect:e.queryCargoTypes}})],1),r("el-form-item",{attrs:{label:"货好时间",prop:"goodsTime"}},[r("el-date-picker",{staticStyle:{width:"120px"},attrs:{clearable:"",placeholder:"货好时间",type:"date","value-format":"yyyy-MM-dd"},model:{value:e.queryParams.goodsTime,callback:function(t){e.$set(e.queryParams,"goodsTime",t)},expression:"queryParams.goodsTime"}})],1),r("el-form-item",{attrs:{label:"需求备注",prop:"requireRemark"}},[r("el-input",{staticStyle:{width:"500px"},attrs:{autosize:{minRows:1},maxlength:"400",placeholder:"需求备注","show-word-limit":"",type:"textarea"},model:{value:e.queryParams.requireRemark,callback:function(t){e.$set(e.queryParams,"requireRemark",t)},expression:"queryParams.requireRemark"}})],1)],1)],1),e._m(0),r("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.freightList,border:""}},[e._l(e.columns,(function(t){return t.visible&&"商务备注"!=t.label?r("el-table-column",{key:t.key,attrs:{align:t.align,label:t.label,width:t.width},scopedSlots:e._u([{key:"default",fn:function(e){return[r(t.prop,{tag:"component",attrs:{scope:e}})]}}],null,!0)}):e._e()})),r("el-table-column",{attrs:{align:"center",label:"操作","class-name":"small-padding fixed-width",width:"75"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("div",{staticStyle:{height:"15px",padding:"0",margin:"0"}},[r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:freight:edit"],expression:"['system:freight:edit']"}],staticStyle:{display:"flex"},attrs:{size:"mini",type:"success",icon:"el-icon-edit"},on:{click:function(r){return e.handleSelect(t.row)}}},[e._v("确认选择 ")])],1)]}}])})],2),r("pagination",{directives:[{name:"show",rawName:"v-show",value:e.ftotal>0,expression:"ftotal>0"}],attrs:{limit:e.freight.pageSize,page:e.freight.pageNum,total:e.ftotal},on:{"update:limit":function(t){return e.$set(e.freight,"pageSize",t)},"update:page":function(t){return e.$set(e.freight,"pageNum",t)},pagination:e.getFreightList}}),r("h3",{staticStyle:{"font-weight":"bold",margin:"5px"}},[e._v(" 报价参考： ")]),r("el-table",{ref:"cost",attrs:{data:e.costList,"row-class-name":e.rowCostIndex,"show-summary":"",border:""}},[r("el-table-column",{attrs:{label:"运费类型",align:"center",prop:"title",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(t.row.index)+" "+e._s(t.row.title)+" "),r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:freight:remove"],expression:"['system:freight:remove']"}],staticStyle:{margin:"0",padding:"0"},attrs:{size:"mini",type:"danger",icon:"el-icon-delete"},on:{click:function(r){return e.handleDeleteCost(t.row.index)}}})]}}])}),r("el-table-column",{scopedSlots:e._u([{key:"default",fn:function(t){return[r("el-table",{attrs:{data:t.row.freightList,"show-header":!1}},[r("el-table-column",{attrs:{type:"expand",width:"58"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("el-table",{attrs:{data:t.row.locals,"show-header":!1}},[r("el-table-column",{attrs:{width:"58"}}),r("el-table-column",{attrs:{align:"left",label:"local费用",width:"300"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("div",{staticStyle:{display:"flex"}},[r("div",{staticStyle:{padding:"0","align-self":"center"}},[e._v(" "+e._s(null!=t.row.charge?t.row.charge.split("/")[1]+":":"")+" ")]),r("div",{staticStyle:{padding:"0","align-self":"center"}},[e._v(" "+e._s(t.row.currency)+" ")]),r("h2",{staticStyle:{margin:"0","font-weight":"bold"}},[e._v(" "+e._s(t.row.price)+" ")]),null==e.queryParams.unitId?r("div",{staticStyle:{padding:"0","align-self":"center"}},[e._v(" "+e._s("/"+t.row.unit)+" ")]):r("div",{staticStyle:{padding:"0","align-self":"center"}},[e._v(" "+e._s(2==t.row.unitId?"/"+t.row.unitName:"/"+t.row.unit)+" ")])])]}}],null,!0)}),r("el-table-column",{attrs:{align:"center",label:"计费单位",width:"160"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("el-input-number",{attrs:{controls:!1,placeholder:"计费单位",min:0},model:{value:t.row.num,callback:function(r){e.$set(t.row,"num",r)},expression:"price.row.num"}})]}}],null,!0)}),r("el-table-column",{attrs:{align:"center",label:"单位利润",width:"160"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("el-input-number",{attrs:{controls:!1,placeholder:"单位利润"},model:{value:t.row.profit,callback:function(r){e.$set(t.row,"profit",r)},expression:"price.row.profit"}})]}}],null,!0)}),r("el-table-column",{attrs:{align:"center",label:"报价清单"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(t.row.detail)+" ")]}}],null,!0)})],1)]}}],null,!0)}),r("el-table-column",{attrs:{align:"left",label:"海运费",width:"300"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("div",{staticStyle:{display:"flex"}},[r("h6",{staticStyle:{margin:"0","align-self":"center"}},[e._v(" "+e._s(t.row.charge+":")+" ")]),r("h6",{staticStyle:{margin:"0","align-self":"center"}},[e._v(" "+e._s(t.row.currency)+" ")]),r("h2",{staticStyle:{margin:"0","font-weight":"bold"}},[e._v(" "+e._s(t.row.price)+" ")]),null==e.queryParams.unitId?r("h6",{staticStyle:{margin:"0","align-self":"center"}},[e._v(" "+e._s("/"+t.row.unit)+" ")]):r("h6",{staticStyle:{margin:"0","align-self":"center"}},[e._v(" "+e._s(2==t.row.unitId?"/"+t.row.unitName:"/"+t.row.unit)+" ")])])]}}],null,!0)}),r("el-table-column",{attrs:{align:"center",label:"计费单位",width:"160"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("el-input-number",{attrs:{controls:!1,placeholder:"计费单位",min:0},model:{value:t.row.num,callback:function(r){e.$set(t.row,"num",r)},expression:"price.row.num"}})]}}],null,!0)}),r("el-table-column",{attrs:{align:"center",label:"单位利润",width:"160"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("el-input-number",{attrs:{controls:!1,placeholder:"单位利润"},model:{value:t.row.profit,callback:function(r){e.$set(t.row,"profit",r)},expression:"price.row.profit"}})]}}],null,!0)}),r("el-table-column",{attrs:{align:"center",label:"报价清单"},scopedSlots:e._u([{key:"default",fn:function(e){}}],null,!0)})],1)]}}])})],1),r("h3",{staticStyle:{"font-weight":"bold",margin:"5px"}},[e._v(" 相关注意事项： ")]),r("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.characteristicsList,"show-header":!1,border:""}},[r("el-table-column",{attrs:{align:"left","show-tooltip-when-overflow":""},scopedSlots:e._u([{key:"default",fn:function(t){return[r("span",{staticStyle:{margin:"0"}},[e._v(e._s(t.row.serviceType)+" "+e._s(null!=t.row.cargoType&&-1==t.row.cargoType.indexOf("全部")?t.row.cargoType:"")+" "+e._s(t.row.company)+" "+e._s(null!=t.row.locationDeparture&&-1==t.row.locationDeparture.indexOf("全部")?"从"+t.row.locationDeparture+"起步":"")+" "+e._s(null!=t.row.locationDestination&&-1==t.row.locationDestination.indexOf("全部")?"至"+t.row.locationDestination:"")+" "+e._s(t.row.info))]),null!=t.row.essentialDetail?r("div",e._l(t.row.essentialDetail.split("\n"),(function(t){return r("div",[r("span",{staticStyle:{margin:"0"}},[e._v(e._s(t))])])})),0):e._e()]}}])})],1),r("pagination",{directives:[{name:"show",rawName:"v-show",value:e.ctotal>0,expression:"ctotal>0"}],attrs:{limit:e.characteristics.pageSize,page:e.characteristics.pageNum,total:e.ctotal,"auto-scroll":!1},on:{"update:limit":function(t){return e.$set(e.characteristics,"pageSize",t)},"update:page":function(t){return e.$set(e.characteristics,"pageNum",t)},pagination:e.getCharacteristicsList}})],1)},i=[function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",{staticStyle:{display:"flex"}},[r("h3",{staticStyle:{"font-weight":"bold",margin:"5px"}},[e._v(" 查询结果列表： ")])])}],s=r("b85c"),n=(r("14d9"),r("d3b7"),r("4de4"),r("b0b8")),l=r.n(n),o=r("ca17"),c=r.n(o),u=(r("6f8d"),r("4360")),d=r("6b1f"),p=r("98dc"),h=r("3809"),m=r("b66c"),y=r("0f6b"),f=r("87df"),g=r("42dd"),w=r("8eee"),v=r("0c23"),b=r("27cd"),I=r("86a4"),S=r("363f"),_=r("2947"),q=r("46aa"),T=r("2a3a"),P=r("e996"),N=r("2a23"),x=r("efbd"),k=r("8eca"),C=r("bfbe"),L=r("de8e"),$=r("06ee"),z=r("d6c9"),D=r("aff7"),O=r("c9a3"),Q={name:"Search",components:{Treeselect:c.a,serviceType:P["default"],logisticsType:N["default"],cargoPrice:d["default"],cargoType:p["default"],company:h["default"],contract:m["default"],currency:y["default"],demurrage:f["default"],departureTodestination:g["default"],freeStorage:w["default"],loading:v["default"],price:b["default"],recorder:I["default"],salesRemark:S["default"],shippingDate:_["default"],unit:q["default"],validTime:T["default"],carrier:x["default"],local:k["default"],departure:C["default"],destination:L["default"],carrierNoSupplier:O["default"]},dicts:["sys_yes_no"],data:function(){return{size:this.$store.state.app.size||"mini",title:"海运",columns:this.$store.state.listSettings.seafreightSetting2,edit:!1,unitName:null,logisticsType:"整柜海运",freightList:[],carrierList:[],characteristicsList:[],queryCarrierIds:[],costList:[],loading:!1,queryParams:{pageSize:10,pageNum:1,typeId:1,salesId:this.$store.state.user.sid,logisticsTypeId:1,serviceTypeId:1,serviceType:null,departureId:null,departure:null,destinationId:null,destination:null,unitId:null,unit:null,carrierIds:[],carriers:[],cargoTypeIds:[1],cargoTypes:["普货"],contractTypeId:null,contractType:null,requireRemark:null,companyId:null,company:null,requireMark:1,replyMark:0},freight:{pageSize:10,pageNum:1},ftotal:0,characteristics:{pageSize:10,pageNum:1},ctotal:0,form:{},pickerOptions:{shortcuts:[{text:"最近一天",onClick:function(e){var t=new Date,r=new Date;r.setTime(r.getTime()-864e5),e.$emit("pick",[r,t])}},{text:"最近一周",onClick:function(e){var t=new Date,r=new Date;r.setTime(r.getTime()-6048e5),e.$emit("pick",[r,t])}},{text:"最近一个月",onClick:function(e){var t=new Date,r=new Date;r.setTime(r.getTime()-2592e6),e.$emit("pick",[r,t])}},{text:"最近三个月",onClick:function(e){var t=new Date,r=new Date;r.setTime(r.getTime()-7776e6),e.$emit("pick",[r,t])}}]},clients:[]}},watch:{"queryParams.typeId":function(e){"1"==e&&(this.title="海运",this.columns=this.$store.state.listSettings.seafreightSetting),"2"==e&&(this.title="空运",this.columns=this.$store.state.listSettings.airfreightSetting),"3"==e&&(this.title="铁路",this.columns=this.$store.state.listSettings.seafreightSetting),"4"==e&&(this.title="快递",this.columns=this.$store.state.listSettings.expressdeliverySetting),"5"==e&&(this.title="拖车",this.columns=this.$store.state.listSettings.trailerSetting),"6"==e&&(this.title="报关",this.columns=this.$store.state.listSettings.declareSetting),"7"==e&&(this.title="检验",this.columns=this.$store.state.listSettings.benchmarkSetting),"7"==e&&(this.title="保险",this.columns=this.$store.state.listSettings.insuranceSetting),"8"==e&&(this.title="仓储",this.columns=this.$store.state.listSettings.warehouseSetting)}},beforeMount:function(){var e=this;Object(D["h"])({roleClient:"1",permissionLevel:this.$store.state.user.permissionLevelList.C}).then((function(t){e.clients=t.rows}))},created:function(){this.loadCarrier()},methods:{loadCarrier:function(){var e=this;0==this.$store.state.data.serviceTypeCarriers.length||this.$store.state.data.redisList.serviceTypeCarriers?u["a"].dispatch("getServiceTypeCarriersList").then((function(){e.carrierList=e.$store.state.data.serviceTypeCarriers})):this.carrierList=this.$store.state.data.serviceTypeCarriers},getFreightList:function(){var e=this;this.queryParams.pageSize=this.freight.pageSize,this.queryParams.pageNum=this.freight.pageNum,Object($["f"])(this.queryParams).then((function(t){e.freightList=t.rows,e.ftotal=t.total}))},getCharacteristicsList:function(){var e=this;this.queryParams.pageSize=this.characteristics.pageSize,this.queryParams.pageNum=this.characteristics.pageNum,Object($["e"])(this.queryParams).then((function(t){e.characteristicsList=t.rows,e.ctotal=t.total}))},handleQuery:function(){this.getFreightList(),this.getCharacteristicsList()},handleRequire:function(){var e=this;null==this.queryParams.departureId||""==this.queryParams.departureId||null==this.queryParams.destinationId||""==this.queryParams.destinationId?this.$message.error("搜索港口"):this.$msgbox({title:"结果选定",message:"确认请求更新费用",showCancelButton:!0,confirmButtonText:"确定",cancelButtonText:"取消",beforeClose:function(t,r,a){"confirm"==t?Object(z["a"])(e.queryParams).then((function(t){e.$message.success("新增询价成功"),e.$msgbox.close()})):e.$msgbox.close()}}).then((function(){e.$message.success("成功")}))},handleSelect:function(e){if(null==this.unitName||null==this.queryParams.unitId||2==this.queryParams.unitId)this.$message.warning("重新选择柜型");else{this.queryParams.requireType=1,e.num=1;var t,r=!1,a=Object(s["a"])(this.costList);try{for(a.s();!(t=a.n()).done;){var i=t.value;if(i.title==this.title){r=!0;var n,l=!1,o=Object(s["a"])(i.freightList);try{for(o.s();!(n=o.n()).done;){var c=n.value;if(c.freightId==e.freightId){l=!0;break}}}catch(d){o.e(d)}finally{o.f()}l?this.$message.warning("不可重复选"):(this.getLocalList(e),this.slicePrice(e),i.num=1,i.freightList.push(e));break}}}catch(d){a.e(d)}finally{a.f()}if(!r){var u={};u.title=this.title,u.freightList=[],this.getLocalList(e),this.slicePrice(e),u.freightList.push(e),this.costList.push(u)}}},slicePrice:function(e){"20GP"==this.unitName&&(e.price=null==e.priceB?e.priceA:e.priceB),"40GP"==this.unitName&&(e.price=null==e.priceC?e.priceA:e.priceC),"40HQ"==this.unitName&&(e.price=null==e.priceD?e.priceA:e.priceD),"20GP"!=this.unitName&&"40GP"!=this.unitName&&"40HQ"!=this.unitName&&(e.price=null!=e.priceA?e.priceA:0),e.unitName=this.unitName},getLocalList:function(e){var t=e.locals;if(null==t||0==t.length)return new Promise((function(t,r){Object($["g"])(e).then((function(r){e.locals=r.data,t(e)}))}))},rowCostIndex:function(e){var t=e.row,r=e.rowIndex;t.index=r+1},handleDeleteCost:function(e){this.costList=this.costList.filter((function(t){return t.index!=e}))},carrierNormalizer:function(e){var t;return e.children&&!e.children.length&&delete e.children,t=!e.carrier||null==e.carrier.carrierLocalName&&null==e.carrier.carrierEnName?e.serviceLocalName+" "+e.serviceEnName+","+l.a.getFullChars(e.serviceLocalName):(null!=e.carrier.carrierIntlCode?e.carrier.carrierIntlCode:"")+" "+(null!=e.carrier.carrierLocalName?e.carrier.carrierLocalName:"")+" "+(null!=e.carrier.carrierEnName?e.carrier.carrierEnName:"")+","+l.a.getFullChars(null!=e.carrier.carrierLocalName?e.carrier.carrierLocalName:""),{id:e.serviceTypeId,label:t,children:e.children}},reset:function(){this.queryParams={pageSize:10,pageNum:1,typeId:1,logisticsTypeId:21,serviceTypeId:null,serviceType:"整柜海运",departureId:null,departure:null,destinationId:null,destination:null,unitId:null,unit:null,carrierIds:[],carriers:[],cargoTypeIds:[1],cargoTypes:["普货"],goodsTime:null,contractTypeId:null,contractType:null,remark:null,companyId:null,company:null,requireMark:1,replyMark:0},this.queryCarrierIds=[],this.resetForm("queryForm"),this.handleQuery()},queryUnitId:function(e){void 0==e&&(this.unitName=null,this.queryParams.unitId=null,this.queryParams.unitCode=null)},getUnitName:function(e){this.queryParams.unitId=e.unitId,this.queryParams.unitCode=e.unitCode,this.unitName=e.unitShortName,this.queryParams.unit=e.unitShortName,this.handleQuery()},queryDepartureId:function(e){this.queryParams.departureId=e,this.handleQuery()},queryDestinationId:function(e){this.queryParams.destinationId=e,this.handleQuery()},queryPrecarriageRegionId:function(e){this.queryParams.loadingIds=e,this.handleQuery()},handleSelectQueryCarrierIds:function(e){this.queryParams.carrierIds.push(e.carrier.carrierId),this.queryParams.carriers.push(e.carrier.carrierShortName),this.handleQuery()},handleDeselectQueryCarrierIds:function(e){this.queryParams.carrierIds=this.queryParams.carrierIds.filter((function(t){return t!=e.carrier.carrierId})),this.queryParams.carriers=this.queryParams.carriers.filter((function(t){return t!=e.carrier.carrierShortName})),this.handleQuery()},deselectAllQueryCarrierIds:function(e){0==e.length&&(this.queryParams.carrierIds=[],this.handleQuery())},queryLogisticsType:function(e){this.queryParams.logisticsTypeId=e.serviceTypeId;var t,r=!1,a=Object(s["a"])(this.$store.state.data.serviceTypeList);try{for(a.s();!(t=a.n()).done;){var i=t.value;if(e.serviceTypeId==i.serviceTypeId&&null!=i.typeId&&(this.queryParams.typeId=i.typeId,r=!0),e.serviceTypeId==i.serviceTypeId&&null==i.typeId&&(this.queryParams.logisticsTypeId=1,r=!0,this.$message.warning("不存在该服务类型")),r)break;if(i.children){var n,l=Object(s["a"])(i.children);try{for(l.s();!(n=l.n()).done;){var o=n.value;o.serviceTypeId===e.serviceTypeId&&null!==o.typeId&&(this.queryParams.typeId=o.typeId)}}catch(c){l.e(c)}finally{l.f()}}}}catch(c){a.e(c)}finally{a.f()}this.handleQuery()},queryServiceType:function(e){var t=this;this.queryParams.serviceTypeId=e.serviceTypeId,0==this.$store.state.data.serviceTypeList.length||this.$store.state.data.redisList.serviceType?u["a"].dispatch("getServiceTypeList").then((function(){t.getType(e.serviceTypeId)})):this.getType(e.serviceTypeId),this.handleQuery()},getType:function(e){if(e){var t,r=Object(s["a"])(this.$store.state.data.serviceTypeList);try{for(r.s();!(t=r.n()).done;){var a=t.value;if(a.children){var i,n=Object(s["a"])(a.children);try{for(n.s();!(i=n.n()).done;){var l=i.value;l.serviceTypeId==e&&(this.queryParams.typeId=l.typeId)}}catch(o){n.e(o)}finally{n.f()}}a.serviceTypeId==e&&(this.queryParams.typeId=a.typeId)}}catch(o){r.e(o)}finally{r.f()}}},queryCompany:function(e){this.queryParams.company=e.companyShortName,this.queryParams.companyId=e.companyId,this.handleQuery()},queryCargoTypeIds:function(e){this.queryParams.cargoTypeIds=e,this.handleQuery()},queryCargoTypes:function(e){-1==this.queryParams.cargoTypes.indexOf(e.cargoTypeLocalName)?this.queryParams.cargoTypes.push(e.cargoTypeLocalName):this.queryParams.cargoTypes=this.queryParams.cargoTypes.filter((function(t){return t!=e.cargoTypeLocalName}))},companyNormalizer:function(e){return{id:e.companyId,label:(null!=e.companyShortName?e.companyShortName:"")+" "+(null!=e.companyLocalName?e.companyLocalName:"")+","+l.a.getFullChars((null!=e.companyShortName?e.companyShortName:"")+" "+(null!=e.companyLocalName?e.companyLocalName:""))}}}},R=Q,j=r("2877"),A=Object(j["a"])(R,a,i,!1,null,null,null);t["default"]=A.exports},e350:function(e,t,r){"use strict";r.d(t,"a",(function(){return i})),r.d(t,"b",(function(){return s}));r("d3b7"),r("caad"),r("2532");var a=r("4360");function i(e){if(e&&e instanceof Array&&e.length>0){var t=a["a"].getters&&a["a"].getters.permissions,r=e,i="*:*:*",s=t.some((function(e){return i==e||r.includes(e)}));return!!s}return console.error("need roles! Like checkPermi=\"['system:user:add','system:user:edit']\""),!1}function s(e){if(e&&e instanceof Array&&e.length>0){var t=a["a"].getters&&a["a"].getters.roles,r=e,i="admin",s=t.some((function(e){return i==e||r.includes(e)}));return!!s}return console.error("need roles! Like checkRole=\"['admin','editor']\""),!1}}}]);