(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d216214"],{c0c4:function(e,t,n){
/*! @license DOMPurify 3.2.4 | (c) Cure53 and other contributors | Released under the Apache license 2.0 and Mozilla Public License 2.0 | github.com/cure53/DOMPurify/blob/3.2.4/LICENSE */
(function(t,n){e.exports=n()})(0,(function(){"use strict";const{entries:e,setPrototypeOf:t,isFrozen:n,getPrototypeOf:o,getOwnPropertyDescriptor:r}=Object;let{freeze:i,seal:a,create:l}=Object,{apply:c,construct:s}="undefined"!==typeof Reflect&&Reflect;i||(i=function(e){return e}),a||(a=function(e){return e}),c||(c=function(e,t,n){return e.apply(t,n)}),s||(s=function(e,t){return new e(...t)});const u=S(Array.prototype.forEach),m=S(Array.prototype.lastIndexOf),p=S(Array.prototype.pop),f=S(Array.prototype.push),d=S(Array.prototype.splice),h=S(String.prototype.toLowerCase),g=S(String.prototype.toString),T=S(String.prototype.match),y=S(String.prototype.replace),E=S(String.prototype.indexOf),A=S(String.prototype.trim),_=S(Object.prototype.hasOwnProperty),N=S(RegExp.prototype.test),b=w(TypeError);function S(e){return function(t){for(var n=arguments.length,o=new Array(n>1?n-1:0),r=1;r<n;r++)o[r-1]=arguments[r];return c(e,t,o)}}function w(e){return function(){for(var t=arguments.length,n=new Array(t),o=0;o<t;o++)n[o]=arguments[o];return s(e,n)}}function R(e,o){let r=arguments.length>2&&void 0!==arguments[2]?arguments[2]:h;t&&t(e,null);let i=o.length;while(i--){let t=o[i];if("string"===typeof t){const e=r(t);e!==t&&(n(o)||(o[i]=e),t=e)}e[t]=!0}return e}function L(e){for(let t=0;t<e.length;t++){const n=_(e,t);n||(e[t]=null)}return e}function O(t){const n=l(null);for(const[o,r]of e(t)){const e=_(t,o);e&&(Array.isArray(r)?n[o]=L(r):r&&"object"===typeof r&&r.constructor===Object?n[o]=O(r):n[o]=r)}return n}function v(e,t){while(null!==e){const n=r(e,t);if(n){if(n.get)return S(n.get);if("function"===typeof n.value)return S(n.value)}e=o(e)}function n(){return null}return n}const C=i(["a","abbr","acronym","address","area","article","aside","audio","b","bdi","bdo","big","blink","blockquote","body","br","button","canvas","caption","center","cite","code","col","colgroup","content","data","datalist","dd","decorator","del","details","dfn","dialog","dir","div","dl","dt","element","em","fieldset","figcaption","figure","font","footer","form","h1","h2","h3","h4","h5","h6","head","header","hgroup","hr","html","i","img","input","ins","kbd","label","legend","li","main","map","mark","marquee","menu","menuitem","meter","nav","nobr","ol","optgroup","option","output","p","picture","pre","progress","q","rp","rt","ruby","s","samp","section","select","shadow","small","source","spacer","span","strike","strong","style","sub","summary","sup","table","tbody","td","template","textarea","tfoot","th","thead","time","tr","track","tt","u","ul","var","video","wbr"]),D=i(["svg","a","altglyph","altglyphdef","altglyphitem","animatecolor","animatemotion","animatetransform","circle","clippath","defs","desc","ellipse","filter","font","g","glyph","glyphref","hkern","image","line","lineargradient","marker","mask","metadata","mpath","path","pattern","polygon","polyline","radialgradient","rect","stop","style","switch","symbol","text","textpath","title","tref","tspan","view","vkern"]),x=i(["feBlend","feColorMatrix","feComponentTransfer","feComposite","feConvolveMatrix","feDiffuseLighting","feDisplacementMap","feDistantLight","feDropShadow","feFlood","feFuncA","feFuncB","feFuncG","feFuncR","feGaussianBlur","feImage","feMerge","feMergeNode","feMorphology","feOffset","fePointLight","feSpecularLighting","feSpotLight","feTile","feTurbulence"]),k=i(["animate","color-profile","cursor","discard","font-face","font-face-format","font-face-name","font-face-src","font-face-uri","foreignobject","hatch","hatchpath","mesh","meshgradient","meshpatch","meshrow","missing-glyph","script","set","solidcolor","unknown","use"]),I=i(["math","menclose","merror","mfenced","mfrac","mglyph","mi","mlabeledtr","mmultiscripts","mn","mo","mover","mpadded","mphantom","mroot","mrow","ms","mspace","msqrt","mstyle","msub","msup","msubsup","mtable","mtd","mtext","mtr","munder","munderover","mprescripts"]),M=i(["maction","maligngroup","malignmark","mlongdiv","mscarries","mscarry","msgroup","mstack","msline","msrow","semantics","annotation","annotation-xml","mprescripts","none"]),U=i(["#text"]),P=i(["accept","action","align","alt","autocapitalize","autocomplete","autopictureinpicture","autoplay","background","bgcolor","border","capture","cellpadding","cellspacing","checked","cite","class","clear","color","cols","colspan","controls","controlslist","coords","crossorigin","datetime","decoding","default","dir","disabled","disablepictureinpicture","disableremoteplayback","download","draggable","enctype","enterkeyhint","face","for","headers","height","hidden","high","href","hreflang","id","inputmode","integrity","ismap","kind","label","lang","list","loading","loop","low","max","maxlength","media","method","min","minlength","multiple","muted","name","nonce","noshade","novalidate","nowrap","open","optimum","pattern","placeholder","playsinline","popover","popovertarget","popovertargetaction","poster","preload","pubdate","radiogroup","readonly","rel","required","rev","reversed","role","rows","rowspan","spellcheck","scope","selected","shape","size","sizes","span","srclang","start","src","srcset","step","style","summary","tabindex","title","translate","type","usemap","valign","value","width","wrap","xmlns","slot"]),z=i(["accent-height","accumulate","additive","alignment-baseline","amplitude","ascent","attributename","attributetype","azimuth","basefrequency","baseline-shift","begin","bias","by","class","clip","clippathunits","clip-path","clip-rule","color","color-interpolation","color-interpolation-filters","color-profile","color-rendering","cx","cy","d","dx","dy","diffuseconstant","direction","display","divisor","dur","edgemode","elevation","end","exponent","fill","fill-opacity","fill-rule","filter","filterunits","flood-color","flood-opacity","font-family","font-size","font-size-adjust","font-stretch","font-style","font-variant","font-weight","fx","fy","g1","g2","glyph-name","glyphref","gradientunits","gradienttransform","height","href","id","image-rendering","in","in2","intercept","k","k1","k2","k3","k4","kerning","keypoints","keysplines","keytimes","lang","lengthadjust","letter-spacing","kernelmatrix","kernelunitlength","lighting-color","local","marker-end","marker-mid","marker-start","markerheight","markerunits","markerwidth","maskcontentunits","maskunits","max","mask","media","method","mode","min","name","numoctaves","offset","operator","opacity","order","orient","orientation","origin","overflow","paint-order","path","pathlength","patterncontentunits","patterntransform","patternunits","points","preservealpha","preserveaspectratio","primitiveunits","r","rx","ry","radius","refx","refy","repeatcount","repeatdur","restart","result","rotate","scale","seed","shape-rendering","slope","specularconstant","specularexponent","spreadmethod","startoffset","stddeviation","stitchtiles","stop-color","stop-opacity","stroke-dasharray","stroke-dashoffset","stroke-linecap","stroke-linejoin","stroke-miterlimit","stroke-opacity","stroke","stroke-width","style","surfacescale","systemlanguage","tabindex","tablevalues","targetx","targety","transform","transform-origin","text-anchor","text-decoration","text-rendering","textlength","type","u1","u2","unicode","values","viewbox","visibility","version","vert-adv-y","vert-origin-x","vert-origin-y","width","word-spacing","wrap","writing-mode","xchannelselector","ychannelselector","x","x1","x2","xmlns","y","y1","y2","z","zoomandpan"]),H=i(["accent","accentunder","align","bevelled","close","columnsalign","columnlines","columnspan","denomalign","depth","dir","display","displaystyle","encoding","fence","frame","height","href","id","largeop","length","linethickness","lspace","lquote","mathbackground","mathcolor","mathsize","mathvariant","maxsize","minsize","movablelimits","notation","numalign","open","rowalign","rowlines","rowspacing","rowspan","rspace","rquote","scriptlevel","scriptminsize","scriptsizemultiplier","selection","separator","separators","stretchy","subscriptshift","supscriptshift","symmetric","voffset","width","xmlns"]),F=i(["xlink:href","xml:id","xlink:title","xml:space","xmlns:xlink"]),B=a(/\{\{[\w\W]*|[\w\W]*\}\}/gm),W=a(/<%[\w\W]*|[\w\W]*%>/gm),G=a(/\$\{[\w\W]*/gm),Y=a(/^data-[\-\w.\u00B7-\uFFFF]+$/),j=a(/^aria-[\-\w]+$/),X=a(/^(?:(?:(?:f|ht)tps?|mailto|tel|callto|sms|cid|xmpp):|[^a-z]|[a-z+.\-]+(?:[^a-z+.\-:]|$))/i),q=a(/^(?:\w+script|data):/i),$=a(/[\u0000-\u0020\u00A0\u1680\u180E\u2000-\u2029\u205F\u3000]/g),K=a(/^html$/i),V=a(/^[a-z][.\w]*(-[.\w]+)+$/i);var J=Object.freeze({__proto__:null,ARIA_ATTR:j,ATTR_WHITESPACE:$,CUSTOM_ELEMENT:V,DATA_ATTR:Y,DOCTYPE_NAME:K,ERB_EXPR:W,IS_ALLOWED_URI:X,IS_SCRIPT_OR_DATA:q,MUSTACHE_EXPR:B,TMPLIT_EXPR:G});const Z={element:1,attribute:2,text:3,cdataSection:4,entityReference:5,entityNode:6,progressingInstruction:7,comment:8,document:9,documentType:10,documentFragment:11,notation:12},Q=function(){return"undefined"===typeof window?null:window},ee=function(e,t){if("object"!==typeof e||"function"!==typeof e.createPolicy)return null;let n=null;const o="data-tt-policy-suffix";t&&t.hasAttribute(o)&&(n=t.getAttribute(o));const r="dompurify"+(n?"#"+n:"");try{return e.createPolicy(r,{createHTML(e){return e},createScriptURL(e){return e}})}catch(i){return console.warn("TrustedTypes policy "+r+" could not be created."),null}},te=function(){return{afterSanitizeAttributes:[],afterSanitizeElements:[],afterSanitizeShadowDOM:[],beforeSanitizeAttributes:[],beforeSanitizeElements:[],beforeSanitizeShadowDOM:[],uponSanitizeAttribute:[],uponSanitizeElement:[],uponSanitizeShadowNode:[]}};function ne(){let t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:Q();const n=e=>ne(e);if(n.version="3.2.4",n.removed=[],!t||!t.document||t.document.nodeType!==Z.document||!t.Element)return n.isSupported=!1,n;let{document:o}=t;const r=o,a=r.currentScript,{DocumentFragment:c,HTMLTemplateElement:s,Node:S,Element:w,NodeFilter:L,NamedNodeMap:B=t.NamedNodeMap||t.MozNamedAttrMap,HTMLFormElement:W,DOMParser:G,trustedTypes:Y}=t,j=w.prototype,q=v(j,"cloneNode"),$=v(j,"remove"),V=v(j,"nextSibling"),oe=v(j,"childNodes"),re=v(j,"parentNode");if("function"===typeof s){const e=o.createElement("template");e.content&&e.content.ownerDocument&&(o=e.content.ownerDocument)}let ie,ae="";const{implementation:le,createNodeIterator:ce,createDocumentFragment:se,getElementsByTagName:ue}=o,{importNode:me}=r;let pe=te();n.isSupported="function"===typeof e&&"function"===typeof re&&le&&void 0!==le.createHTMLDocument;const{MUSTACHE_EXPR:fe,ERB_EXPR:de,TMPLIT_EXPR:he,DATA_ATTR:ge,ARIA_ATTR:Te,IS_SCRIPT_OR_DATA:ye,ATTR_WHITESPACE:Ee,CUSTOM_ELEMENT:Ae}=J;let{IS_ALLOWED_URI:_e}=J,Ne=null;const be=R({},[...C,...D,...x,...I,...U]);let Se=null;const we=R({},[...P,...z,...H,...F]);let Re=Object.seal(l(null,{tagNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},attributeNameCheck:{writable:!0,configurable:!1,enumerable:!0,value:null},allowCustomizedBuiltInElements:{writable:!0,configurable:!1,enumerable:!0,value:!1}})),Le=null,Oe=null,ve=!0,Ce=!0,De=!1,xe=!0,ke=!1,Ie=!0,Me=!1,Ue=!1,Pe=!1,ze=!1,He=!1,Fe=!1,Be=!0,We=!1;const Ge="user-content-";let Ye=!0,je=!1,Xe={},qe=null;const $e=R({},["annotation-xml","audio","colgroup","desc","foreignobject","head","iframe","math","mi","mn","mo","ms","mtext","noembed","noframes","noscript","plaintext","script","style","svg","template","thead","title","video","xmp"]);let Ke=null;const Ve=R({},["audio","video","img","source","image","track"]);let Je=null;const Ze=R({},["alt","class","for","id","label","name","pattern","placeholder","role","summary","title","value","style","xmlns"]),Qe="http://www.w3.org/1998/Math/MathML",et="http://www.w3.org/2000/svg",tt="http://www.w3.org/1999/xhtml";let nt=tt,ot=!1,rt=null;const it=R({},[Qe,et,tt],g);let at=R({},["mi","mo","mn","ms","mtext"]),lt=R({},["annotation-xml"]);const ct=R({},["title","style","font","a","script"]);let st=null;const ut=["application/xhtml+xml","text/html"],mt="text/html";let pt=null,ft=null;const dt=o.createElement("form"),ht=function(e){return e instanceof RegExp||e instanceof Function},gt=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};if(!ft||ft!==e){if(e&&"object"===typeof e||(e={}),e=O(e),st=-1===ut.indexOf(e.PARSER_MEDIA_TYPE)?mt:e.PARSER_MEDIA_TYPE,pt="application/xhtml+xml"===st?g:h,Ne=_(e,"ALLOWED_TAGS")?R({},e.ALLOWED_TAGS,pt):be,Se=_(e,"ALLOWED_ATTR")?R({},e.ALLOWED_ATTR,pt):we,rt=_(e,"ALLOWED_NAMESPACES")?R({},e.ALLOWED_NAMESPACES,g):it,Je=_(e,"ADD_URI_SAFE_ATTR")?R(O(Ze),e.ADD_URI_SAFE_ATTR,pt):Ze,Ke=_(e,"ADD_DATA_URI_TAGS")?R(O(Ve),e.ADD_DATA_URI_TAGS,pt):Ve,qe=_(e,"FORBID_CONTENTS")?R({},e.FORBID_CONTENTS,pt):$e,Le=_(e,"FORBID_TAGS")?R({},e.FORBID_TAGS,pt):{},Oe=_(e,"FORBID_ATTR")?R({},e.FORBID_ATTR,pt):{},Xe=!!_(e,"USE_PROFILES")&&e.USE_PROFILES,ve=!1!==e.ALLOW_ARIA_ATTR,Ce=!1!==e.ALLOW_DATA_ATTR,De=e.ALLOW_UNKNOWN_PROTOCOLS||!1,xe=!1!==e.ALLOW_SELF_CLOSE_IN_ATTR,ke=e.SAFE_FOR_TEMPLATES||!1,Ie=!1!==e.SAFE_FOR_XML,Me=e.WHOLE_DOCUMENT||!1,ze=e.RETURN_DOM||!1,He=e.RETURN_DOM_FRAGMENT||!1,Fe=e.RETURN_TRUSTED_TYPE||!1,Pe=e.FORCE_BODY||!1,Be=!1!==e.SANITIZE_DOM,We=e.SANITIZE_NAMED_PROPS||!1,Ye=!1!==e.KEEP_CONTENT,je=e.IN_PLACE||!1,_e=e.ALLOWED_URI_REGEXP||X,nt=e.NAMESPACE||tt,at=e.MATHML_TEXT_INTEGRATION_POINTS||at,lt=e.HTML_INTEGRATION_POINTS||lt,Re=e.CUSTOM_ELEMENT_HANDLING||{},e.CUSTOM_ELEMENT_HANDLING&&ht(e.CUSTOM_ELEMENT_HANDLING.tagNameCheck)&&(Re.tagNameCheck=e.CUSTOM_ELEMENT_HANDLING.tagNameCheck),e.CUSTOM_ELEMENT_HANDLING&&ht(e.CUSTOM_ELEMENT_HANDLING.attributeNameCheck)&&(Re.attributeNameCheck=e.CUSTOM_ELEMENT_HANDLING.attributeNameCheck),e.CUSTOM_ELEMENT_HANDLING&&"boolean"===typeof e.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements&&(Re.allowCustomizedBuiltInElements=e.CUSTOM_ELEMENT_HANDLING.allowCustomizedBuiltInElements),ke&&(Ce=!1),He&&(ze=!0),Xe&&(Ne=R({},U),Se=[],!0===Xe.html&&(R(Ne,C),R(Se,P)),!0===Xe.svg&&(R(Ne,D),R(Se,z),R(Se,F)),!0===Xe.svgFilters&&(R(Ne,x),R(Se,z),R(Se,F)),!0===Xe.mathMl&&(R(Ne,I),R(Se,H),R(Se,F))),e.ADD_TAGS&&(Ne===be&&(Ne=O(Ne)),R(Ne,e.ADD_TAGS,pt)),e.ADD_ATTR&&(Se===we&&(Se=O(Se)),R(Se,e.ADD_ATTR,pt)),e.ADD_URI_SAFE_ATTR&&R(Je,e.ADD_URI_SAFE_ATTR,pt),e.FORBID_CONTENTS&&(qe===$e&&(qe=O(qe)),R(qe,e.FORBID_CONTENTS,pt)),Ye&&(Ne["#text"]=!0),Me&&R(Ne,["html","head","body"]),Ne.table&&(R(Ne,["tbody"]),delete Le.tbody),e.TRUSTED_TYPES_POLICY){if("function"!==typeof e.TRUSTED_TYPES_POLICY.createHTML)throw b('TRUSTED_TYPES_POLICY configuration option must provide a "createHTML" hook.');if("function"!==typeof e.TRUSTED_TYPES_POLICY.createScriptURL)throw b('TRUSTED_TYPES_POLICY configuration option must provide a "createScriptURL" hook.');ie=e.TRUSTED_TYPES_POLICY,ae=ie.createHTML("")}else void 0===ie&&(ie=ee(Y,a)),null!==ie&&"string"===typeof ae&&(ae=ie.createHTML(""));i&&i(e),ft=e}},Tt=R({},[...D,...x,...k]),yt=R({},[...I,...M]),Et=function(e){let t=re(e);t&&t.tagName||(t={namespaceURI:nt,tagName:"template"});const n=h(e.tagName),o=h(t.tagName);return!!rt[e.namespaceURI]&&(e.namespaceURI===et?t.namespaceURI===tt?"svg"===n:t.namespaceURI===Qe?"svg"===n&&("annotation-xml"===o||at[o]):Boolean(Tt[n]):e.namespaceURI===Qe?t.namespaceURI===tt?"math"===n:t.namespaceURI===et?"math"===n&&lt[o]:Boolean(yt[n]):e.namespaceURI===tt?!(t.namespaceURI===et&&!lt[o])&&(!(t.namespaceURI===Qe&&!at[o])&&(!yt[n]&&(ct[n]||!Tt[n]))):!("application/xhtml+xml"!==st||!rt[e.namespaceURI]))},At=function(e){f(n.removed,{element:e});try{re(e).removeChild(e)}catch(t){$(e)}},_t=function(e,t){try{f(n.removed,{attribute:t.getAttributeNode(e),from:t})}catch(o){f(n.removed,{attribute:null,from:t})}if(t.removeAttribute(e),"is"===e)if(ze||He)try{At(t)}catch(o){}else try{t.setAttribute(e,"")}catch(o){}},Nt=function(e){let t=null,n=null;if(Pe)e="<remove></remove>"+e;else{const t=T(e,/^[\r\n\t ]+/);n=t&&t[0]}"application/xhtml+xml"===st&&nt===tt&&(e='<html xmlns="http://www.w3.org/1999/xhtml"><head></head><body>'+e+"</body></html>");const r=ie?ie.createHTML(e):e;if(nt===tt)try{t=(new G).parseFromString(r,st)}catch(a){}if(!t||!t.documentElement){t=le.createDocument(nt,"template",null);try{t.documentElement.innerHTML=ot?ae:r}catch(a){}}const i=t.body||t.documentElement;return e&&n&&i.insertBefore(o.createTextNode(n),i.childNodes[0]||null),nt===tt?ue.call(t,Me?"html":"body")[0]:Me?t.documentElement:i},bt=function(e){return ce.call(e.ownerDocument||e,e,L.SHOW_ELEMENT|L.SHOW_COMMENT|L.SHOW_TEXT|L.SHOW_PROCESSING_INSTRUCTION|L.SHOW_CDATA_SECTION,null)},St=function(e){return e instanceof W&&("string"!==typeof e.nodeName||"string"!==typeof e.textContent||"function"!==typeof e.removeChild||!(e.attributes instanceof B)||"function"!==typeof e.removeAttribute||"function"!==typeof e.setAttribute||"string"!==typeof e.namespaceURI||"function"!==typeof e.insertBefore||"function"!==typeof e.hasChildNodes)},wt=function(e){return"function"===typeof S&&e instanceof S};function Rt(e,t,o){u(e,e=>{e.call(n,t,o,ft)})}const Lt=function(e){let t=null;if(Rt(pe.beforeSanitizeElements,e,null),St(e))return At(e),!0;const o=pt(e.nodeName);if(Rt(pe.uponSanitizeElement,e,{tagName:o,allowedTags:Ne}),e.hasChildNodes()&&!wt(e.firstElementChild)&&N(/<[/\w]/g,e.innerHTML)&&N(/<[/\w]/g,e.textContent))return At(e),!0;if(e.nodeType===Z.progressingInstruction)return At(e),!0;if(Ie&&e.nodeType===Z.comment&&N(/<[/\w]/g,e.data))return At(e),!0;if(!Ne[o]||Le[o]){if(!Le[o]&&vt(o)){if(Re.tagNameCheck instanceof RegExp&&N(Re.tagNameCheck,o))return!1;if(Re.tagNameCheck instanceof Function&&Re.tagNameCheck(o))return!1}if(Ye&&!qe[o]){const t=re(e)||e.parentNode,n=oe(e)||e.childNodes;if(n&&t){const o=n.length;for(let r=o-1;r>=0;--r){const o=q(n[r],!0);o.__removalCount=(e.__removalCount||0)+1,t.insertBefore(o,V(e))}}}return At(e),!0}return e instanceof w&&!Et(e)?(At(e),!0):"noscript"!==o&&"noembed"!==o&&"noframes"!==o||!N(/<\/no(script|embed|frames)/i,e.innerHTML)?(ke&&e.nodeType===Z.text&&(t=e.textContent,u([fe,de,he],e=>{t=y(t,e," ")}),e.textContent!==t&&(f(n.removed,{element:e.cloneNode()}),e.textContent=t)),Rt(pe.afterSanitizeElements,e,null),!1):(At(e),!0)},Ot=function(e,t,n){if(Be&&("id"===t||"name"===t)&&(n in o||n in dt))return!1;if(Ce&&!Oe[t]&&N(ge,t));else if(ve&&N(Te,t));else if(!Se[t]||Oe[t]){if(!(vt(e)&&(Re.tagNameCheck instanceof RegExp&&N(Re.tagNameCheck,e)||Re.tagNameCheck instanceof Function&&Re.tagNameCheck(e))&&(Re.attributeNameCheck instanceof RegExp&&N(Re.attributeNameCheck,t)||Re.attributeNameCheck instanceof Function&&Re.attributeNameCheck(t))||"is"===t&&Re.allowCustomizedBuiltInElements&&(Re.tagNameCheck instanceof RegExp&&N(Re.tagNameCheck,n)||Re.tagNameCheck instanceof Function&&Re.tagNameCheck(n))))return!1}else if(Je[t]);else if(N(_e,y(n,Ee,"")));else if("src"!==t&&"xlink:href"!==t&&"href"!==t||"script"===e||0!==E(n,"data:")||!Ke[e]){if(De&&!N(ye,y(n,Ee,"")));else if(n)return!1}else;return!0},vt=function(e){return"annotation-xml"!==e&&T(e,Ae)},Ct=function(e){Rt(pe.beforeSanitizeAttributes,e,null);const{attributes:t}=e;if(!t||St(e))return;const o={attrName:"",attrValue:"",keepAttr:!0,allowedAttributes:Se,forceKeepAttr:void 0};let r=t.length;while(r--){const a=t[r],{name:l,namespaceURI:c,value:s}=a,m=pt(l);let f="value"===l?s:A(s);if(o.attrName=m,o.attrValue=f,o.keepAttr=!0,o.forceKeepAttr=void 0,Rt(pe.uponSanitizeAttribute,e,o),f=o.attrValue,!We||"id"!==m&&"name"!==m||(_t(l,e),f=Ge+f),Ie&&N(/((--!?|])>)|<\/(style|title)/i,f)){_t(l,e);continue}if(o.forceKeepAttr)continue;if(_t(l,e),!o.keepAttr)continue;if(!xe&&N(/\/>/i,f)){_t(l,e);continue}ke&&u([fe,de,he],e=>{f=y(f,e," ")});const d=pt(e.nodeName);if(Ot(d,m,f)){if(ie&&"object"===typeof Y&&"function"===typeof Y.getAttributeType)if(c);else switch(Y.getAttributeType(d,m)){case"TrustedHTML":f=ie.createHTML(f);break;case"TrustedScriptURL":f=ie.createScriptURL(f);break}try{c?e.setAttributeNS(c,l,f):e.setAttribute(l,f),St(e)?At(e):p(n.removed)}catch(i){}}}Rt(pe.afterSanitizeAttributes,e,null)},Dt=function e(t){let n=null;const o=bt(t);Rt(pe.beforeSanitizeShadowDOM,t,null);while(n=o.nextNode())Rt(pe.uponSanitizeShadowNode,n,null),Lt(n),Ct(n),n.content instanceof c&&e(n.content);Rt(pe.afterSanitizeShadowDOM,t,null)};return n.sanitize=function(e){let t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},o=null,i=null,a=null,l=null;if(ot=!e,ot&&(e="\x3c!--\x3e"),"string"!==typeof e&&!wt(e)){if("function"!==typeof e.toString)throw b("toString is not a function");if(e=e.toString(),"string"!==typeof e)throw b("dirty is not a string, aborting")}if(!n.isSupported)return e;if(Ue||gt(t),n.removed=[],"string"===typeof e&&(je=!1),je){if(e.nodeName){const t=pt(e.nodeName);if(!Ne[t]||Le[t])throw b("root node is forbidden and cannot be sanitized in-place")}}else if(e instanceof S)o=Nt("\x3c!----\x3e"),i=o.ownerDocument.importNode(e,!0),i.nodeType===Z.element&&"BODY"===i.nodeName||"HTML"===i.nodeName?o=i:o.appendChild(i);else{if(!ze&&!ke&&!Me&&-1===e.indexOf("<"))return ie&&Fe?ie.createHTML(e):e;if(o=Nt(e),!o)return ze?null:Fe?ae:""}o&&Pe&&At(o.firstChild);const s=bt(je?e:o);while(a=s.nextNode())Lt(a),Ct(a),a.content instanceof c&&Dt(a.content);if(je)return e;if(ze){if(He){l=se.call(o.ownerDocument);while(o.firstChild)l.appendChild(o.firstChild)}else l=o;return(Se.shadowroot||Se.shadowrootmode)&&(l=me.call(r,l,!0)),l}let m=Me?o.outerHTML:o.innerHTML;return Me&&Ne["!doctype"]&&o.ownerDocument&&o.ownerDocument.doctype&&o.ownerDocument.doctype.name&&N(K,o.ownerDocument.doctype.name)&&(m="<!DOCTYPE "+o.ownerDocument.doctype.name+">\n"+m),ke&&u([fe,de,he],e=>{m=y(m,e," ")}),ie&&Fe?ie.createHTML(m):m},n.setConfig=function(){let e=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{};gt(e),Ue=!0},n.clearConfig=function(){ft=null,Ue=!1},n.isValidAttribute=function(e,t,n){ft||gt({});const o=pt(e),r=pt(t);return Ot(o,r,n)},n.addHook=function(e,t){"function"===typeof t&&f(pe[e],t)},n.removeHook=function(e,t){if(void 0!==t){const n=m(pe[e],t);return-1===n?void 0:d(pe[e],n,1)[0]}return p(pe[e])},n.removeHooks=function(e){pe[e]=[]},n.removeAllHooks=function(){pe=te()},n}var oe=ne();return oe}))}}]);