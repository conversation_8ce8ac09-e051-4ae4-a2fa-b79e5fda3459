(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d0e57d3"],{9567:function(e,s,t){"use strict";t.r(s);var o=function(){var e=this,s=e.$createElement,t=e._self._c||s;return t("div",[t("el-tooltip",{attrs:{disabled:null==e.scope.row.serviceType||e.scope.row.serviceType.length<15,placement:"top"}},[t("div",{attrs:{slot:"content"},slot:"content"},[t("h6",{staticStyle:{margin:"0"}},[e._v(" "+e._s(e.scope.row.serviceType)+" ")])]),t("div",[t("h6",{staticStyle:{margin:"0",overflow:"hidden","text-overflow":"ellipsis"}},[e._v(" "+e._s(null!=e.scope.row.serviceType?e.scope.row.serviceType.length>15?e.scope.row.serviceType.substring(0,15)+"...":e.scope.row.serviceType:"")+" ")])])])],1)},c=[],r={name:"serviceType",props:["scope"]},p=r,i=t("2877"),n=Object(i["a"])(p,o,c,!1,null,"56054c56",null);s["default"]=n.exports}}]);