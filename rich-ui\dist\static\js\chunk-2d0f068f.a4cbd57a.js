(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d0f068f"],{"9bf6":function(e,t,l){"use strict";l.r(t);var a=function(){var e=this,t=e.$createElement,l=e._self._c||t;return l("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"},{name:"dialogDragWidth",rawName:"v-dialogDragWidth"}],attrs:{"close-on-click-modal":!1,"modal-append-to-body":!1,visible:e.oopen,"append-to-body":"",width:"1500px"},on:{"update:visible":function(t){e.oopen=t}}},[l("div",{staticClass:"app-container"},[l("el-row",{attrs:{gutter:20}},[l("el-col",{attrs:{span:e.showLeft}},[l("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],ref:"queryForm",staticClass:"query",attrs:{inline:!0,model:e.queryParams,"label-width":"68px",size:"mini"}},[l("el-form-item",{attrs:{label:"所属服务类型id",prop:"sqdServiceTypeId"}},[l("el-input",{attrs:{clearable:"",placeholder:"所属服务类型id"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.sqdServiceTypeId,callback:function(t){e.$set(e.queryParams,"sqdServiceTypeId",t)},expression:"queryParams.sqdServiceTypeId"}})],1),l("el-form-item",{attrs:{label:"所属操作单号",prop:"sqdRctNo"}},[l("el-input",{attrs:{clearable:"",placeholder:"所属操作单号"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.sqdRctNo,callback:function(t){e.$set(e.queryParams,"sqdRctNo",t)},expression:"queryParams.sqdRctNo"}})],1),l("el-form-item",{attrs:{label:"文件来源",prop:"docSourceId"}},[l("el-input",{attrs:{clearable:"",placeholder:"文件来源公司id"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.docSourceId,callback:function(t){e.$set(e.queryParams,"docSourceId",t)},expression:"queryParams.docSourceId"}})],1),l("el-form-item",{attrs:{label:"文件类型",prop:"docTypeId"}},[l("el-input",{attrs:{clearable:"",placeholder:"文件类型HBL,MBL,AWBL,SWBL,CO,BSC,COC"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.docTypeId,callback:function(t){e.$set(e.queryParams,"docTypeId",t)},expression:"queryParams.docTypeId"}})],1),l("el-form-item",{attrs:{label:"文件编号",prop:"docTrackingNo"}},[l("el-input",{attrs:{clearable:"",placeholder:"文件编号提单号等"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.docTrackingNo,callback:function(t){e.$set(e.queryParams,"docTrackingNo",t)},expression:"queryParams.docTrackingNo"}})],1),l("el-form-item",{attrs:{label:"发货人",prop:"shipper"}},[l("el-input",{attrs:{clearable:"",placeholder:"发货人"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.shipper,callback:function(t){e.$set(e.queryParams,"shipper",t)},expression:"queryParams.shipper"}})],1),l("el-form-item",{attrs:{label:"收货人",prop:"consignee"}},[l("el-input",{attrs:{clearable:"",placeholder:"收货人"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.consignee,callback:function(t){e.$set(e.queryParams,"consignee",t)},expression:"queryParams.consignee"}})],1),l("el-form-item",{attrs:{label:"通知人",prop:"notifyParty"}},[l("el-input",{attrs:{clearable:"",placeholder:"通知人"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.notifyParty,callback:function(t){e.$set(e.queryParams,"notifyParty",t)},expression:"queryParams.notifyParty"}})],1),l("el-form-item",{attrs:{label:"唛头",prop:"shippingMark"}},[l("el-input",{attrs:{clearable:"",placeholder:"唛头"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.shippingMark,callback:function(t){e.$set(e.queryParams,"shippingMark",t)},expression:"queryParams.shippingMark"}})],1),l("el-form-item",{attrs:{label:"货描",prop:"goodsDescription"}},[l("el-input",{attrs:{clearable:"",placeholder:"货描"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.goodsDescription,callback:function(t){e.$set(e.queryParams,"goodsDescription",t)},expression:"queryParams.goodsDescription"}})],1),l("el-form-item",{attrs:{label:"资料",prop:"containersSealsList"}},[l("el-input",{attrs:{clearable:"",placeholder:"柜号和封条和分柜资料(list)"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.containersSealsList,callback:function(t){e.$set(e.queryParams,"containersSealsList",t)},expression:"queryParams.containersSealsList"}})],1),l("el-form-item",{attrs:{label:"签单日期",prop:"blIssueDate"}},[l("el-date-picker",{attrs:{clearable:"",placeholder:"签单日期",type:"date","value-format":"yyyy-MM-dd"},model:{value:e.queryParams.blIssueDate,callback:function(t){e.$set(e.queryParams,"blIssueDate",t)},expression:"queryParams.blIssueDate"}})],1),l("el-form-item",{attrs:{label:"签单地点",prop:"blIssueLocation"}},[l("el-input",{attrs:{clearable:"",placeholder:"签单地点"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.blIssueLocation,callback:function(t){e.$set(e.queryParams,"blIssueLocation",t)},expression:"queryParams.blIssueLocation"}})],1),l("el-form-item",{attrs:{label:"是否转单",prop:"isSwitchBl"}},[l("el-input",{attrs:{clearable:"",placeholder:"是否转单"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.isSwitchBl,callback:function(t){e.$set(e.queryParams,"isSwitchBl",t)},expression:"queryParams.isSwitchBl"}})],1),l("el-form-item",{attrs:{label:"是否拆单",prop:"isDividedBl"}},[l("el-input",{attrs:{clearable:"",placeholder:"是否拆单"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.isDividedBl,callback:function(t){e.$set(e.queryParams,"isDividedBl",t)},expression:"queryParams.isDividedBl"}})],1),l("el-form-item",{attrs:{label:"显示套约",prop:"isAgreementShowed"}},[l("el-input",{attrs:{clearable:"",placeholder:"显示套约"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.isAgreementShowed,callback:function(t){e.$set(e.queryParams,"isAgreementShowed",t)},expression:"queryParams.isAgreementShowed"}})],1),l("el-form-item",{attrs:{label:"清关中转",prop:"isCustomsIntransit"}},[l("el-input",{attrs:{clearable:"",placeholder:"清关中转"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.isCustomsIntransit,callback:function(t){e.$set(e.queryParams,"isCustomsIntransit",t)},expression:"queryParams.isCustomsIntransit"}})],1),l("el-form-item",{attrs:{label:"取单方式",prop:"docGettingWay"}},[l("el-input",{attrs:{clearable:"",placeholder:"取单方式"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.docGettingWay,callback:function(t){e.$set(e.queryParams,"docGettingWay",t)},expression:"queryParams.docGettingWay"}})],1),l("el-form-item",{attrs:{label:"交单方式",prop:"docDeliveryWay"}},[l("el-input",{attrs:{clearable:"",placeholder:"交单方式"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.docDeliveryWay,callback:function(t){e.$set(e.queryParams,"docDeliveryWay",t)},expression:"queryParams.docDeliveryWay"}})],1),l("el-form-item",[l("el-button",{attrs:{icon:"el-icon-search",size:"mini",type:"primary"},on:{click:e.handleQuery}},[e._v("搜索")]),l("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1)],1),l("el-col",{attrs:{span:e.showRight}},[l("el-row",{staticClass:"mb8",attrs:{gutter:10}},[l("el-col",{attrs:{span:1.5}},[l("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:doc:add"],expression:"['system:doc:add']"}],attrs:{icon:"el-icon-plus",plain:"",size:"mini",type:"primary"},on:{click:e.handleAdd}},[e._v("新增 ")])],1),l("el-col",{attrs:{span:1.5}},[l("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:doc:edit"],expression:"['system:doc:edit']"}],attrs:{disabled:e.single,icon:"el-icon-edit",plain:"",size:"mini",type:"success"},on:{click:e.handleUpdate}},[e._v("修改 ")])],1),l("el-col",{attrs:{span:1.5}},[l("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:doc:remove"],expression:"['system:doc:remove']"}],attrs:{disabled:e.multiple,icon:"el-icon-delete",plain:"",size:"mini",type:"danger"},on:{click:e.handleDelete}},[e._v("删除 ")])],1),l("right-toolbar",{attrs:{showSearch:e.showSearch},on:{"update:showSearch":function(t){e.showSearch=t},"update:show-search":function(t){e.showSearch=t},queryTable:e.getList}})],1),l("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.docList},on:{"selection-change":e.handleSelectionChange}},[l("el-table-column",{attrs:{align:"center",type:"selection",width:"28"}}),l("el-table-column",{attrs:{align:"center",label:"操作单号",prop:"sqdRctNo"}}),l("el-table-column",{attrs:{align:"center",label:"文件来源公司",prop:"docSourceId"}}),l("el-table-column",{attrs:{align:"center",label:"文件类型",prop:"docTypeId"}}),l("el-table-column",{attrs:{align:"center",label:"文件编号",prop:"docTrackingNo"}}),l("el-table-column",{attrs:{align:"center",label:"发货人",prop:"shipper"}}),l("el-table-column",{attrs:{align:"center",label:"收货人",prop:"consignee"}}),l("el-table-column",{attrs:{align:"center",label:"通知人",prop:"notifyParty"}}),l("el-table-column",{attrs:{align:"center",label:"唛头",prop:"shippingMark"}}),l("el-table-column",{attrs:{align:"center",label:"货描",prop:"goodsDescription"}}),l("el-table-column",{attrs:{align:"center",label:"资料",prop:"containersSealsList"}}),l("el-table-column",{attrs:{align:"center",label:"签单日期",prop:"blIssueDate",width:"180"},scopedSlots:e._u([{key:"default",fn:function(t){return[l("span",[e._v(e._s(e.parseTime(t.row.blIssueDate,"{y}-{m}-{d}")))])]}}])}),l("el-table-column",{attrs:{align:"center",label:"签单地点",prop:"blIssueLocation"}}),l("el-table-column",{attrs:{align:"center",label:"是否转单",prop:"isSwitchBl"}}),l("el-table-column",{attrs:{align:"center",label:"是否拆单",prop:"isDividedBl"}}),l("el-table-column",{attrs:{align:"center",label:"显示套约",prop:"isAgreementShowed"}}),l("el-table-column",{attrs:{align:"center",label:"清关中转",prop:"isCustomsIntransit"}}),l("el-table-column",{attrs:{align:"center",label:"出单方式",prop:"docIssueType"}}),l("el-table-column",{attrs:{align:"center",label:"取单方式",prop:"docGettingWay"}}),l("el-table-column",{attrs:{align:"center",label:"交单方式",prop:"docDeliveryWay"}}),l("el-table-column",{attrs:{align:"center","class-name":"small-padding fixed-width",label:"操作"},scopedSlots:e._u([{key:"default",fn:function(t){return[l("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:doc:edit"],expression:"['system:doc:edit']"}],staticStyle:{"margin-right":"-8px"},attrs:{icon:"el-icon-edit",size:"mini",disabled:e.disabled,type:"success"},on:{click:function(l){return e.handleUpdate(t.row)}}},[e._v("修改 ")]),l("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:doc:remove"],expression:"['system:doc:remove']"}],staticStyle:{"margin-right":"-8px"},attrs:{icon:"el-icon-delete",size:"mini",disabled:e.disabled,type:"danger"},on:{click:function(l){return e.handleDelete(t.row)}}},[e._v("删除 ")])]}}])})],1),l("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total>0"}],attrs:{limit:e.queryParams.pageSize,page:e.queryParams.pageNum,total:e.total},on:{"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},pagination:e.getList}})],1)],1),l("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"},{name:"dialogDragWidth",rawName:"v-dialogDragWidth"}],attrs:{"close-on-click-modal":!1,"modal-append-to-body":!1,title:e.title,visible:e.open,"append-to-body":"",width:"500px"},on:{"update:visible":function(t){e.open=t}}},[l("el-form",{ref:"form",staticClass:"edit",attrs:{model:e.form,rules:e.rules,"label-width":"80px"}},[l("el-form-item",{attrs:{label:"文件来源",prop:"docSourceId"}},[l("company-select",{attrs:{"load-options":e.form.docSourceId,multiple:!1,"no-parent":!0,pass:e.form.docSourceId,placeholder:"委托单位",roleTypeId:1},on:{return:function(t){e.form.docSourceId=t}}})],1),l("el-form-item",{attrs:{label:"文件类型",prop:"docTypeId"}},[l("el-input",{attrs:{placeholder:"文件类型HBL,MBL,AWBL,SWBL,CO,BSC,COC"},model:{value:e.form.docTypeId,callback:function(t){e.$set(e.form,"docTypeId",t)},expression:"form.docTypeId"}})],1),l("el-form-item",{attrs:{label:"文件编号",prop:"docTrackingNo"}},[l("el-input",{attrs:{placeholder:"文件编号提单号等"},model:{value:e.form.docTrackingNo,callback:function(t){e.$set(e.form,"docTrackingNo",t)},expression:"form.docTrackingNo"}})],1),l("el-form-item",{attrs:{label:"发货人",prop:"shipper"}},[l("el-input",{attrs:{placeholder:"发货人"},model:{value:e.form.shipper,callback:function(t){e.$set(e.form,"shipper",t)},expression:"form.shipper"}})],1),l("el-form-item",{attrs:{label:"收货人",prop:"consignee"}},[l("el-input",{attrs:{placeholder:"收货人"},model:{value:e.form.consignee,callback:function(t){e.$set(e.form,"consignee",t)},expression:"form.consignee"}})],1),l("el-form-item",{attrs:{label:"通知人",prop:"notifyParty"}},[l("el-input",{attrs:{placeholder:"通知人"},model:{value:e.form.notifyParty,callback:function(t){e.$set(e.form,"notifyParty",t)},expression:"form.notifyParty"}})],1),l("el-form-item",{attrs:{label:"唛头",prop:"shippingMark"}},[l("el-input",{attrs:{placeholder:"唛头"},model:{value:e.form.shippingMark,callback:function(t){e.$set(e.form,"shippingMark",t)},expression:"form.shippingMark"}})],1),l("el-form-item",{attrs:{label:"货描",prop:"goodsDescription"}},[l("el-input",{attrs:{placeholder:"货描"},model:{value:e.form.goodsDescription,callback:function(t){e.$set(e.form,"goodsDescription",t)},expression:"form.goodsDescription"}})],1),l("el-form-item",{attrs:{label:"资料",prop:"containersSealsList"}},[l("el-input",{attrs:{placeholder:"柜号和封条和分柜资料(list)"},model:{value:e.form.containersSealsList,callback:function(t){e.$set(e.form,"containersSealsList",t)},expression:"form.containersSealsList"}})],1),l("el-form-item",{attrs:{label:"签单日期",prop:"blIssueDate"}},[l("el-date-picker",{staticStyle:{width:"100%"},attrs:{clearable:"",placeholder:"签单日期",type:"date","value-format":"yyyy-MM-dd"},model:{value:e.form.blIssueDate,callback:function(t){e.$set(e.form,"blIssueDate",t)},expression:"form.blIssueDate"}})],1),l("el-form-item",{attrs:{label:"签单地点",prop:"blIssueLocation"}},[l("el-input",{attrs:{placeholder:"签单地点"},model:{value:e.form.blIssueLocation,callback:function(t){e.$set(e.form,"blIssueLocation",t)},expression:"form.blIssueLocation"}})],1),l("el-form-item",{attrs:{label:"是否转单",prop:"isSwitchBl"}},[l("el-input",{attrs:{placeholder:"是否转单"},model:{value:e.form.isSwitchBl,callback:function(t){e.$set(e.form,"isSwitchBl",t)},expression:"form.isSwitchBl"}})],1),l("el-form-item",{attrs:{label:"是否拆单",prop:"isDividedBl"}},[l("el-input",{attrs:{placeholder:"是否拆单"},model:{value:e.form.isDividedBl,callback:function(t){e.$set(e.form,"isDividedBl",t)},expression:"form.isDividedBl"}})],1),l("el-form-item",{attrs:{label:"显示套约",prop:"isAgreementShowed"}},[l("el-input",{attrs:{placeholder:"显示套约"},model:{value:e.form.isAgreementShowed,callback:function(t){e.$set(e.form,"isAgreementShowed",t)},expression:"form.isAgreementShowed"}})],1),l("el-form-item",{attrs:{label:"清关中转",prop:"isCustomsIntransit"}},[l("el-input",{attrs:{placeholder:"清关中转"},model:{value:e.form.isCustomsIntransit,callback:function(t){e.$set(e.form,"isCustomsIntransit",t)},expression:"form.isCustomsIntransit"}})],1),l("el-form-item",{attrs:{label:"取单方式",prop:"docGettingWay"}},[l("el-input",{attrs:{placeholder:"取单方式"},model:{value:e.form.docGettingWay,callback:function(t){e.$set(e.form,"docGettingWay",t)},expression:"form.docGettingWay"}})],1),l("el-form-item",{attrs:{label:"交单方式",prop:"docDeliveryWay"}},[l("el-input",{attrs:{placeholder:"交单方式"},model:{value:e.form.docDeliveryWay,callback:function(t){e.$set(e.form,"docDeliveryWay",t)},expression:"form.docDeliveryWay"}})],1)],1),l("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[l("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("确 定")]),l("el-button",{on:{click:e.cancel}},[e._v("取 消")])],1)],1)],1)])},r=[],n=(l("d81d"),l("a9e4")),o=l("fba1"),s=l("6e71"),i={name:"DocList",components:{CompanySelect:s["a"]},props:["openDocList","docList","disabled"],watch:{logisticsNoInfo:function(){this.$emit("return",this.logisticsNoInfo)},openDocList:function(e){this.oopen=e},oopen:function(e){0==e&&this.$emit("close")},showSearch:function(e){!0===e?(this.showRight=21,this.showLeft=3):(this.showRight=24,this.showLeft=0)}},created:function(){this.loading=!1},data:function(){return{openInsert:!1,oopen:!1,logisticsNoInfo:[],showLeft:0,showRight:24,loading:!0,ids:[],single:!0,multiple:!0,showSearch:!0,total:0,title:"",open:!1,queryParams:{pageNum:1,pageSize:20,serviceId:null,sqdServiceTypeId:null,sqdRctNo:null,docSourceId:null,docTypeId:null,docTrackingNo:null,shipper:null,consignee:null,notifyParty:null,shippingMark:null,goodsDescription:null,containersSealsList:null,blIssueDate:null,blIssueLocation:null,isSwitchBl:null,isDividedBl:null,isAgreementShowed:null,isCustomsIntransit:null,docIssueType:null,docGettingWay:null,docDeliveryWay:null},form:{},rules:{}}},methods:{parseTime:o["f"],cancel:function(){this.open=!1,this.reset()},reset:function(){this.form={docId:null,serviceId:null,sqdServiceTypeId:null,sqdRctNo:null,docSourceId:null,docTypeId:null,docTrackingNo:null,shipper:null,consignee:null,notifyParty:null,shippingMark:null,goodsDescription:null,containersSealsList:null,blIssueDate:null,blIssueLocation:null,isSwitchBl:null,isDividedBl:null,isAgreementShowed:null,isCustomsIntransit:null,docIssueType:null,docGettingWay:null,docDeliveryWay:null},this.resetForm("form")},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.resetForm("queryForm"),this.handleQuery()},handleStatusChange:function(e){var t=this,l="0"===e.status?"启用":"停用";this.$confirm('确认要"'+l+"吗？","提示",{customClass:"modal-confirm"}).then((function(){return changeStatus(e.docId,e.status)})).then((function(){t.$modal.msgSuccess(l+"成功")})).catch((function(){e.status="0"===e.status?"1":"0"}))},handleSelectionChange:function(e){this.ids=e.map((function(e){return e.docId})),this.single=1!==e.length,this.multiple=!e.length},handleAdd:function(){this.reset(),this.open=!0,this.title="添加文件信息"},handleUpdate:function(e){var t=this;this.reset();var l=e.docId||this.ids;Object(n["d"])(l).then((function(e){t.form=e.data,t.open=!0,t.title="修改文件信息"}))},submitForm:function(){var e=this;this.$refs["form"].validate((function(t){t&&(null!=e.form.docId?Object(n["g"])(e.form).then((function(t){e.$modal.msgSuccess("修改成功"),e.open=!1,e.getList()})):Object(n["a"])(e.form).then((function(t){e.$modal.msgSuccess("新增成功"),e.open=!1,e.getList()})))}))},handleDelete:function(e){var t=this,l=e.docId||this.ids;this.$confirm('是否确认删除文件信息编号为"'+l+'"的数据项？',"提示",{customClass:"modal-confirm"}).then((function(){return delDoc(l)})).then((function(){t.getList(),t.$modal.msgSuccess("删除成功")})).catch((function(){}))},getList:function(){var e=this;this.loading=!0,listDoc(this.queryParams).then((function(t){e.docList=t.rows,e.total=t.total,e.loading=!1}))}}},c=i,u=l("2877"),d=Object(u["a"])(c,a,r,!1,null,null,null);t["default"]=d.exports}}]);