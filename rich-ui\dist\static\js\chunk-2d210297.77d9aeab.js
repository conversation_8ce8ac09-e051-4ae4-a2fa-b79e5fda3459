(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d210297"],{b75a:function(e,t,s){"use strict";s.r(t);var a=function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("div",{staticClass:"app-container"},[s("el-row",{attrs:{gutter:20}},[s("el-col",{attrs:{span:e.showLeft}},[s("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],ref:"queryForm",staticClass:"query",attrs:{inline:!0,model:e.queryParams,size:"mini"}},[s("el-form-item",{attrs:{label:"来源",prop:"messageType"}},[s("tree-select",{staticStyle:{width:"100%"},attrs:{pass:e.queryParams.messageType,placeholder:"信息来源",type:"messageType"},on:{return:e.queryMessageTypeId}})],1),s("el-form-item",{attrs:{label:"时间",prop:"messageDate"}},[s("el-date-picker",{staticStyle:{width:"100%"},attrs:{clearable:"",placeholder:"时间",type:"date","value-format":"yyyy-MM-dd"},model:{value:e.queryParams.messageDate,callback:function(t){e.$set(e.queryParams,"messageDate",t)},expression:"queryParams.messageDate"}})],1),s("el-form-item",{attrs:{label:"解决",prop:"isSolve"}},[s("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"是否已解决"},model:{value:e.queryParams.isSolve,callback:function(t){e.$set(e.queryParams,"isSolve",t)},expression:"queryParams.isSolve"}},e._l(e.dict.type.sys_yes_no,(function(e){return s("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),s("el-form-item",[s("el-button",{attrs:{icon:"el-icon-search",size:"mini",type:"primary"},on:{click:e.handleQuery}},[e._v("搜索")]),s("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1)],1),s("el-col",{attrs:{span:e.showRight}},[s("el-row",{staticClass:"mb8",attrs:{gutter:10}},[s("el-col",{attrs:{span:1.5}},[s("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:message:remove"],expression:"['system:message:remove']"}],attrs:{disabled:e.multiple,icon:"el-icon-delete",plain:"",size:"mini",type:"danger"},on:{click:e.handleDelete}},[e._v("删除 ")])],1),s("el-col",{attrs:{span:1.5}},[s("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:message:export"],expression:"['system:message:export']"}],attrs:{icon:"el-icon-download",plain:"",size:"mini",type:"warning"},on:{click:e.handleExport}},[e._v("导出 ")])],1),s("right-toolbar",{attrs:{showSearch:e.showSearch},on:{"update:showSearch":function(t){e.showSearch=t},"update:show-search":function(t){e.showSearch=t},queryTable:e.getList}})],1),s("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.messageList},on:{"selection-change":e.handleSelectionChange}},[s("el-table-column",{attrs:{type:"selection",width:"28",align:"center"}}),s("el-table-column",{attrs:{align:"center",label:"信息来源",width:"170"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(null!=t.row.messageFromName?t.row.messageFromName+":":"")+" "+e._s(t.row.messageTypeName)+" ")]}}])}),s("el-table-column",{attrs:{align:"center",label:"标题",prop:"messageTitle","show-tooltip-when-overflow":"",width:"170"}}),s("el-table-column",{attrs:{align:"left",label:"详细内容",prop:"messageContent","show-tooltip-when-overflow":""}}),s("el-table-column",{attrs:{align:"left",label:"备注",prop:"remark","show-tooltip-when-overflow":""}}),s("el-table-column",{attrs:{align:"center",label:"有效期",prop:"messageDate",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[s("span",[e._v(e._s(e.parseTime(t.row.messageDate,"{y}-{m}-{d}")))])]}}])}),s("el-table-column",{attrs:{align:"center",label:"解决",width:"68"},scopedSlots:e._u([{key:"default",fn:function(t){return[s("app-link",{attrs:{to:e.resolvePath()}},[s("el-button",{staticStyle:{margin:"0",padding:"0"},on:{click:function(s){return e.toPath(t.row.messageFrom)}}},[s("dict-tag",{attrs:{options:e.dict.type.sys_is_solve,value:t.row.isSolve}})],1)],1)]}}])}),s("el-table-column",{attrs:{align:"center",label:"解决人",prop:"solveBy",width:"68"}}),s("el-table-column",{attrs:{align:"center",label:"创建时间",prop:"createTime",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[s("span",[e._v(e._s(e.parseTime(t.row.createTime,"{y}-{m}-{d}")))])]}}])}),s("el-table-column",{attrs:{align:"center","class-name":"small-padding fixed-width",label:"操作",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[s("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:message:edit"],expression:"['system:message:edit']"}],staticStyle:{"margin-right":"-8px"},attrs:{icon:"el-icon-edit",size:"mini",type:"success"},on:{click:function(s){return e.handleUpdate(t.row)}}},[e._v("修改 ")]),s("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:message:remove"],expression:"['system:message:remove']"}],staticStyle:{"margin-right":"-8px"},attrs:{icon:"el-icon-delete",size:"mini",type:"danger"},on:{click:function(s){return e.handleDelete(t.row)}}},[e._v("删除 ")])]}}])})],1),s("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total>0"}],attrs:{limit:e.queryParams.pageSize,page:e.queryParams.pageNum,total:e.total},on:{"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},pagination:e.getList}})],1)],1),s("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"},{name:"dialogDragWidth",rawName:"v-dialogDragWidth"}],attrs:{"close-on-click-modal":!1,"modal-append-to-body":!1,title:e.title,visible:e.open,"append-to-body":"",width:"500px"},on:{"update:visible":function(t){e.open=t}}},[s("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"80px"}},[s("el-form-item",{attrs:{label:"信息来源",prop:"messageType"}},[s("tree-select",{attrs:{pass:e.form.messageType,placeholder:"信息来源",type:"messageType"},on:{return:e.getMessageTypeId}})],1),s("el-form-item",{attrs:{label:"标题",prop:"messageTitle"}},[s("el-input",{attrs:{placeholder:"标题"},model:{value:e.form.messageTitle,callback:function(t){e.$set(e.form,"messageTitle",t)},expression:"form.messageTitle"}})],1),s("el-form-item",{attrs:{label:"详细内容"}},[s("editor",{attrs:{"min-height":192},model:{value:e.form.messageContent,callback:function(t){e.$set(e.form,"messageContent",t)},expression:"form.messageContent"}})],1),s("el-form-item",{attrs:{label:"备注",prop:"remark"}},[s("el-input",{attrs:{placeholder:"备注"},model:{value:e.form.remark,callback:function(t){e.$set(e.form,"remark",t)},expression:"form.remark"}})],1),s("el-form-item",{attrs:{label:"时间",prop:"messageDate"}},[s("el-date-picker",{staticStyle:{width:"100%"},attrs:{clearable:"",placeholder:"时间",type:"date","value-format":"yyyy-MM-dd"},model:{value:e.form.messageDate,callback:function(t){e.$set(e.form,"messageDate",t)},expression:"form.messageDate"}})],1),s("el-form-item",{attrs:{label:"是否已解决",prop:"isSolve"}},[s("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"是否已解决"},model:{value:e.form.isSolve,callback:function(t){e.$set(e.form,"isSolve",t)},expression:"form.isSolve"}},e._l(e.dict.type.sys_yes_no,(function(e){return s("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),s("el-form-item",{attrs:{label:"解决人",prop:"solveBy"}},[e._v(" "+e._s(e.form.solveBy)+" ")])],1),s("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[s("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("确 定")]),s("el-button",{on:{click:e.cancel}},[e._v("取 消")])],1)],1)],1)},l=[],o=s("5530"),r=(s("d81d"),s("0503")),n=s("df7c"),i=s.n(n),m=s("a6e1"),c={name:"Message",dicts:["sys_is_solve","sys_yes_no"],components:{AppLink:m["a"]},data:function(){return{showLeft:3,showRight:21,loading:!0,ids:[],single:!0,multiple:!0,showSearch:!1,total:0,messageList:[],title:"",open:!1,queryParams:{pageNum:1,pageSize:20,messageOwner:this.$store.state.user.sid,messageType:null,messageFrom:null,messageTitle:null,messageContent:null,messageDate:null,isSolve:null,solveBy:null},form:{},rules:{}}},watch:{showSearch:function(e){1==e?(this.showRight=21,this.showLeft=3):(this.showRight=24,this.showLeft=0)}},created:function(){this.getList()},methods:{resolvePath:function(){return i.a.resolve("enter","freight")},toPath:function(e){this.$store.dispatch("getFreightId",e)},getList:function(){var e=this;this.loading=!0,Object(r["f"])(this.queryParams).then((function(t){e.messageList=t.rows,e.total=t.total,e.loading=!1}))},cancel:function(){this.open=!1,this.reset()},reset:function(){this.form={messageOwner:null,messageType:null,messageFrom:null,messageTitle:null,messageContent:null,messageDate:null,isSolve:null,solveBy:null,remark:null,createBy:null,createTime:null,updateBy:null,updateTime:null,deleteBy:null,deleteTime:null,deleteStatus:"0"},this.resetForm("form")},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.resetForm("queryForm"),this.handleQuery()},handleStatusChange:function(e){var t=this,s="0"==e.status?"启用":"停用";this.$confirm('确认要"'+s+"吗？","提示",{customClass:"modal-confirm"}).then((function(){return Object(r["b"])(e.messageId,e.status)})).then((function(){t.$modal.msgSuccess(s+"成功")})).catch((function(){e.status="0"==e.status?"1":"0"}))},handleSelectionChange:function(e){this.ids=e.map((function(e){return e.messageId})),this.single=1!=e.length,this.multiple=!e.length},handleAdd:function(){this.reset(),this.open=!0,this.title="添加消息通知"},handleUpdate:function(e){var t=this;this.reset();var s=e.messageId||this.ids;Object(r["e"])(s).then((function(e){t.form=e.data,t.open=!0,t.title="修改消息通知"}))},submitForm:function(){var e=this;this.$refs["form"].validate((function(t){t&&(null!=e.form.messageId?Object(r["g"])(e.form).then((function(t){e.$modal.msgSuccess("修改成功"),e.open=!1,e.getList()})):Object(r["a"])(e.form).then((function(t){e.$modal.msgSuccess("新增成功"),e.open=!1,e.getList()})))}))},handleDelete:function(e){var t=this,s=e.messageId||this.ids;this.$confirm('是否确认删除消息通知编号为"'+s+'"的数据项？',"提示",{customClass:"modal-confirm"}).then((function(){return Object(r["d"])(s)})).then((function(){t.getList(),t.$modal.msgSuccess("删除成功")})).catch((function(){}))},handleExport:function(){this.download("system/message/export",Object(o["a"])({},this.queryParams),"message_".concat((new Date).getTime(),".xlsx"))},getMessageTypeId:function(e){this.form.messageType=e},queryMessageTypeId:function(e){this.queryParams.messageType=e}}},u=c,d=s("2877"),p=Object(d["a"])(u,a,l,!1,null,null,null);t["default"]=p.exports}}]);