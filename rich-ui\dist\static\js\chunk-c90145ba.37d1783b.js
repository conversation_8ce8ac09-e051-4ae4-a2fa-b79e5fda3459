(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-c90145ba","chunk-2d0d69a4"],{"080c":function(t,e,n){},3524:function(t,e,n){"use strict";n("080c")},"66dc":function(t,e,n){"use strict";n.r(e);var r=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",{staticClass:"app-container"},[n("el-row",{staticStyle:{width:"100%"},attrs:{gutter:20}},[n("el-form",{ref:"queryForm",staticClass:"query",attrs:{inline:!0,model:t.queryParams,size:"mini"},nativeOn:{submit:function(t){t.preventDefault()}}},[n("el-col",{staticStyle:{display:"flex"}},[n("el-form-item",{attrs:{label:"公司",prop:"rctNo"}},[n("company-select",{attrs:{"load-options":t.companyList,multiple:!1,"no-parent":!0,pass:t.queryParams.clearingCompanyId,"role-control":!1},on:{return:function(e){return t.handleSearch(e)}}})],1),n("el-form-item",{attrs:{label:"单号",prop:"rctNo"}},[n("el-input",{attrs:{placeholder:"订单号"},nativeOn:{keydown:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.getChargeList(e)}},model:{value:t.queryParams.sqdRctNo,callback:function(e){t.$set(t.queryParams,"sqdRctNo",e)},expression:"queryParams.sqdRctNo"}})],1),n("div",{staticStyle:{"margin-left":"30px"}},[n("el-switch",{attrs:{"active-text":"应付","inactive-text":"应收"},model:{value:t.isRecievingOrPaying,callback:function(e){t.isRecievingOrPaying=e},expression:"isRecievingOrPaying"}})],1),n("div",{staticStyle:{"padding-left":"100px"}},[n("el-switch",{attrs:{"active-text":"已审","inactive-text":"未审"},model:{value:t.isAccountConfirmed,callback:function(e){t.isAccountConfirmed=e},expression:"isAccountConfirmed"}})],1)],1),n("el-col",{staticStyle:{display:"flex","padding-left":"12vw"}},[n("el-button",{attrs:{disabled:0===t.selectedRow.length,type:"primary"},on:{click:t.handleVerification}},[t._v(" "+t._s(this.isAccountConfirmed?"取消审核":"审核")+" ")]),n("div",{staticStyle:{"padding-left":"50px"}},[t._v("勾选审核金额 : "+t._s(t.verifyTotal))])],1)],1)],1),n("el-row",{attrs:{gutter:20}},[n("el-col",[n("el-table",{staticStyle:{width:"100%"},attrs:{data:t.chargeList,border:"",stripe:""},on:{"selection-change":t.handleSelectionChange}},[n("el-table-column",{attrs:{label:"序号",type:"index",width:"30"}}),n("el-table-column",{attrs:{type:"selection",width:"35",selectable:t.checkSelectable}}),n("el-table-column",{attrs:{align:"center",label:"财务审核",prop:"sqdRctNo",width:"50"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s("1"===e.row.isAccountConfirmed?"√":"-")+" ")]}}])}),n("el-table-column",{attrs:{align:"center",label:"销账状态",prop:"sqdRctNo",width:"50"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(t.currency(e.row.sqdDnCurrencyBalance).value===t.currency(e.row.dnUnitRate).multiply(e.row.dnAmount).value?"-":0===t.currency(e.row.sqdDnCurrencyBalance).value?"√":"=")+" ")]}}])}),n("el-table-column",{attrs:{label:"流水号",prop:"sqdRctNo",width:"100"}}),n("el-table-column",{attrs:{label:"结算单位",prop:"companyName"}}),n("el-table-column",{attrs:{label:"费用名称",prop:"chargeName",width:"80"}}),n("el-table-column",{attrs:{align:"right",label:"单价",prop:"dnUnitRate",width:"80"}}),n("el-table-column",{attrs:{label:"数量",prop:"dnAmount",align:"right",width:"80"}}),n("el-table-column",{attrs:{label:"结算方式",prop:"address"}}),n("el-table-column",{attrs:{label:"币种",prop:"dnCurrencyCode"}}),n("el-table-column",{attrs:{label:"汇率",prop:"basicCurrencyRate"}}),n("el-table-column",{attrs:{label:"税率",prop:"dnCurrencyCode",align:"right"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(e.row.dutyRate)+"% ")]}}])}),n("el-table-column",{attrs:{label:0==t.isRecievingOrPaying?"应收金额":"应付金额",align:"right",prop:"dnUnitRate"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(e.row.subtotal)+" ")]}}])}),n("el-table-column",{attrs:{align:"right",label:"未销账余额",prop:"sqdDnCurrencyBalance"}}),n("el-table-column",{attrs:{align:"right",label:"备注",prop:"chargeRemark"}}),n("el-table-column",{attrs:{align:"right",label:"已收",prop:"dnCurrencyReceived"}}),n("el-table-column",{attrs:{align:"right",label:"已付",prop:"dnCurrencyPaid"}}),n("el-table-column",{attrs:{align:"right",label:"付款抬头",prop:"paymentTitleCode"}}),n("el-table-column",{attrs:{align:"right",label:"结款方式",prop:"logisticsPaymentTermsCode"}}),n("el-table-column",{attrs:{align:"right",label:"获取取值日期",prop:"currencyRateCalculateDate"},scopedSlots:t._u([{key:"default",fn:function(e){return[n("span",[t._v(t._s(t.parseTime(e.row.currencyRateCalculateDate,"{y}-{m}-{d}")))])]}}])})],1),n("pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total>0"}],attrs:{limit:t.queryParams.pageSize,page:t.queryParams.pageNum,total:t.total},on:{"update:limit":function(e){return t.$set(t.queryParams,"pageSize",e)},"update:page":function(e){return t.$set(t.queryParams,"pageNum",e)},pagination:t.getChargeList}})],1)],1)],1)},a=[],i=(n("d3b7"),n("159b"),n("d81d"),n("4ec9"),n("3ca3"),n("ddb0"),n("99af"),n("14d9"),n("13d5"),n("7db0"),n("e9c4"),n("ac1f"),n("841c"),n("4de4"),n("25f0"),n("a9e3"),n("aff7")),o=n("c2aa"),c=n("fff5"),s=n("72f9"),u=n.n(s),l=(n("7b38"),n("fba1")),d=n("6e71"),f={name:"Verification",components:{CompanySelect:d["a"]},data:function(){return{companyList:[],chargeList:[],selectedRow:[],writeOffList:[],writeOffSelectedRow:[],open:!1,total:0,isRecievingOrPaying:"0",isAccountConfirmed:"0",form:{},queryParams:{pageNum:1,pageSize:20,isRecievingOrPaying:"0",companyQuery:null,sqdRctNo:null},search:!1,defaultProps:{children:"children",label:"rctNo"},verifyTotal:0}},methods:{handleSearch:function(t){this.queryParams.clearingCompanyId=t,this.getChargeList()},parseTime:l["f"],handleVerification:function(){var t=this;Object(c["m"])({rsChargeList:this.selectedRow}).then((function(e){t.$message.success("成功操作"+t.selectedRow.length+"条费用"),t.chargeList.length===t.selectedRow.length?(t.form.clearingCompanyId=null,t.form.sqdRctNo=null,t.chargeList=[]):t.getChargeList(t.form)}))},writeOffSelectedNone:function(){var t=this;this.writeOffList.forEach((function(e){t.$refs.writeOffTable.toggleRowSelection(e)}))},handleWriteOffSelectionChange:function(t){this.writeOffSelectedRow=t},submitForm:function(){},handleWriteOff:function(){this.open=!0,this.writeOffList=this.selectedRow},handleSelectionChange:function(t){var e=this;this.verifyTotal=0;var n=0,r=0;t.length>0&&t.map((function(t){"USD"===t.dnCurrencyCode?n=u()(n).add(u()(t.subtotal)).value:r=u()(r).add(t.subtotal).value,e.verifyTotal="USD: "+u()(n).format()+"  RMB: "+u()(r,{symbol:"¥"}).format()})),this.selectedRow=t},currency:u.a,getCompanyList:function(t){var e=this;Object(c["h"])(t).then((function(t){console.log(t);var n=new Map;t.data&&t.data.length>0&&(t.data.map((function(t){n.get(t.clearingCompanyId)?n.set(t.clearingCompanyId,n.get(t.clearingCompanyId).concat([{sqdRctNo:t.sqdRctNo,clearingCompanyId:t.clearingCompanyId,companyName:t.companyName}])):n.set(t.clearingCompanyId,[{sqdRctNo:t.sqdRctNo,clearingCompanyId:t.clearingCompanyId,companyName:t.companyName}])})),e.companyList=[],n.forEach((function(t,n){e.companyList.push({companyShortName:t[0].companyName,clearingCompanyId:t[0].clearingCompanyId,rctNoList:t.reduce((function(t,e){var n=t.find((function(t){return JSON.stringify(t)===JSON.stringify(e)}));return n||t.push(e),t}),[])})})))}))},getChargeList:function(){var t=this;Object(c["h"])(this.queryParams).then((function(e){t.chargeList=e.rows,t.total=e.total}))},getList:function(t){var e=this;Object(i["g"])(t).then((function(t){e.companyList=t.data.map((function(t){return t.rctNoList=t.rctNoList.map((function(t){return t.sqdRctNo=t.rctNo,t})),t})),e.total=t.total}))},handleClick:function(t){this.search&&Object(o["l"])(t.companyId).then((function(e){t.rctNoList=e.data?e.data:[]}))},handleChange:function(t){0===t.length?this.search=!1:this.search=!0},handleNodeClick:function(t,e){var n=document.querySelectorAll(".rct-item");n.forEach((function(t){t.style.backgroundColor="transparent"}));var r=t.target;r.style.backgroundColor="rgb(232, 244, 255)",this.queryParams.clearingCompanyId=e.clearingCompanyId,this.queryParams.sqdRctNo=e.sqdRctNo,this.getChargeList()},cancel:function(){this.open=!1},getName:function(t){if(t){var e=this.$store.state.data.allRsStaffList.filter((function(e){return e.staffId==t}))[0];if(e)return e.staffFamilyLocalName+e.staffGivingLocalName+e.staffShortName}},checkSelectable:function(t){return u()(t.sqdDnCurrencyBalance).value===u()(t.subtotal).value}},beforeMount:function(){},watch:{isRecievingOrPaying:function(t){this.queryParams.isRecievingOrPaying=Number(this.isRecievingOrPaying).toString(),this.queryParams.isAccountConfirmed=Number(this.isAccountConfirmed).toString(),this.chargeList=[],this.getCompanyList({isRecievingOrPaying:Number(this.isRecievingOrPaying).toString(),isAccountConfirmed:Number(this.isAccountConfirmed).toString()}),this.queryParams.sqdRctNo=this.queryParams.sqdRctNo?this.queryParams.sqdRctNo:null,this.getChargeList()},isAccountConfirmed:function(t){this.queryParams.isAccountConfirmed=Number(this.isAccountConfirmed).toString(),this.queryParams.isRecievingOrPaying=Number(this.isRecievingOrPaying).toString(),this.chargeList=[],this.getCompanyList({isRecievingOrPaying:Number(this.isRecievingOrPaying).toString(),isAccountConfirmed:Number(this.isAccountConfirmed).toString()}),this.queryParams.sqdRctNo=this.queryParams.sqdRctNo?this.queryParams.sqdRctNo:null,this.getChargeList()},selectedRow:function(t){this.selectedRow.map((function(t){}))}}},m=f,h=(n("3524"),n("2877")),g=Object(h["a"])(m,r,a,!1,null,"4c4cca54",null);e["default"]=g.exports},"72f9":function(t,e,n){(function(e,n){t.exports=n()})(0,(function(){function t(i,o){if(!(this instanceof t))return new t(i,o);o=Object.assign({},n,o);var c=Math.pow(10,o.precision);this.intValue=i=e(i,o),this.value=i/c,o.increment=o.increment||1/c,o.groups=o.useVedic?a:r,this.s=o,this.p=c}function e(e,n){var r=!(2<arguments.length&&void 0!==arguments[2])||arguments[2],a=n.decimal,i=n.errorOnInvalid,o=n.fromCents,c=Math.pow(10,n.precision),s=e instanceof t;if(s&&o)return e.intValue;if("number"===typeof e||s)a=s?e.value:e;else if("string"===typeof e)i=new RegExp("[^-\\d"+a+"]","g"),a=new RegExp("\\"+a,"g"),a=(a=e.replace(/\((.*)\)/,"-$1").replace(i,"").replace(a,"."))||0;else{if(i)throw Error("Invalid Input");a=0}return o||(a=(a*c).toFixed(4)),r?Math.round(a):a}var n={symbol:"$",separator:",",decimal:".",errorOnInvalid:!1,precision:2,pattern:"!#",negativePattern:"-!#",format:function(t,e){var n=e.pattern,r=e.negativePattern,a=e.symbol,i=e.separator,o=e.decimal;e=e.groups;var c=(""+t).replace(/^-/,"").split("."),s=c[0];return c=c[1],(0<=t.value?n:r).replace("!",a).replace("#",s.replace(e,"$1"+i)+(c?o+c:""))},fromCents:!1},r=/(\d)(?=(\d{3})+\b)/g,a=/(\d)(?=(\d\d)+\d\b)/g;return t.prototype={add:function(n){var r=this.s,a=this.p;return t((this.intValue+e(n,r))/(r.fromCents?1:a),r)},subtract:function(n){var r=this.s,a=this.p;return t((this.intValue-e(n,r))/(r.fromCents?1:a),r)},multiply:function(e){var n=this.s;return t(this.intValue*e/(n.fromCents?1:Math.pow(10,n.precision)),n)},divide:function(n){var r=this.s;return t(this.intValue/e(n,r,!1),r)},distribute:function(e){var n=this.intValue,r=this.p,a=this.s,i=[],o=Math[0<=n?"floor":"ceil"](n/e),c=Math.abs(n-o*e);for(r=a.fromCents?1:r;0!==e;e--){var s=t(o/r,a);0<c--&&(s=s[0<=n?"add":"subtract"](1/r)),i.push(s)}return i},dollars:function(){return~~this.value},cents:function(){return~~(this.intValue%this.p)},format:function(t){var e=this.s;return"function"===typeof t?t(this,e):e.format(this,Object.assign({},e,t))},toString:function(){var t=this.s,e=t.increment;return(Math.round(this.intValue/this.p/e)*e).toFixed(t.precision)},toJSON:function(){return this.value}},t}))},"7b38":function(t,e,n){"use strict";n.d(e,"e",(function(){return a})),n.d(e,"d",(function(){return i})),n.d(e,"a",(function(){return o})),n.d(e,"f",(function(){return c})),n.d(e,"c",(function(){return s})),n.d(e,"b",(function(){return u}));var r=n("b775");function a(t){return Object(r["a"])({url:"/system/quotationstrategy/list",method:"get",params:t})}function i(t){return Object(r["a"])({url:"/system/quotationstrategy/"+t,method:"get"})}function o(t){return Object(r["a"])({url:"/system/quotationstrategy",method:"post",data:t})}function c(t){return Object(r["a"])({url:"/system/quotationstrategy",method:"put",data:t})}function s(t){return Object(r["a"])({url:"/system/quotationstrategy/"+t,method:"delete"})}function u(t,e){var n={strategyCode:t,status:e};return Object(r["a"])({url:"/system/quotationstrategy/changeStatus",method:"put",data:n})}},c2aa:function(t,e,n){"use strict";n.d(e,"o",(function(){return a})),n.d(e,"n",(function(){return i})),n.d(e,"p",(function(){return o})),n.d(e,"r",(function(){return c})),n.d(e,"q",(function(){return s})),n.d(e,"i",(function(){return u})),n.d(e,"g",(function(){return l})),n.d(e,"v",(function(){return d})),n.d(e,"w",(function(){return f})),n.d(e,"h",(function(){return m})),n.d(e,"c",(function(){return h})),n.d(e,"a",(function(){return g})),n.d(e,"f",(function(){return p})),n.d(e,"d",(function(){return y})),n.d(e,"e",(function(){return b})),n.d(e,"k",(function(){return v})),n.d(e,"j",(function(){return C})),n.d(e,"m",(function(){return O})),n.d(e,"t",(function(){return w})),n.d(e,"u",(function(){return R})),n.d(e,"l",(function(){return q})),n.d(e,"s",(function(){return N}));var r=n("b775");function a(t){return Object(r["a"])({url:"/system/rct/list",method:"get",params:t})}function i(t){return Object(r["a"])({url:"/system/rct/aggregator",method:"get",params:t})}function o(t){return Object(r["a"])({url:"/system/rct/listVerifyAggregatorList",method:"get",params:t})}function c(t){return Object(r["a"])({url:"/system/rct/op",method:"get",params:t})}function s(t){return Object(r["a"])({url:"/system/rct/listVerifyList",method:"get",params:t})}function u(t){return Object(r["a"])({url:"/system/rct/"+t,method:"get"})}function l(t){return Object(r["a"])({url:"/system/rct",method:"post",data:t})}function d(t){return Object(r["a"])({url:"/system/rct/saveAs",method:"post",data:t})}function f(t){return Object(r["a"])({url:"/system/rct",method:"put",data:t})}function m(t){return Object(r["a"])({url:"/system/rct/"+t,method:"delete"})}function h(t){return Object(r["a"])({url:"/system/rct/saveClientMessage",method:"post",data:t})}function g(t){return Object(r["a"])({url:"/system/rct/saveBasicLogistics",method:"post",data:t})}function p(t){return Object(r["a"])({url:"/system/rct/savePreCarriage",method:"post",data:t})}function y(t){return Object(r["a"])({url:"/system/rct/saveExportDeclaration",method:"post",data:t})}function b(t){return Object(r["a"])({url:"/system/rct/saveImportClearance",method:"post",data:t})}function v(){return Object(r["a"])({url:"/system/rct/mon",method:"get"})}function C(){return Object(r["a"])({url:"/system/rct/CFmon",method:"get"})}function O(){return Object(r["a"])({url:"/system/rct/RSWHMon",method:"get"})}function w(t){return Object(r["a"])({url:"/system/rct/saveAllService",method:"post",data:t})}function R(t){return Object(r["a"])({url:"/system/rct/saveAsAllService",method:"post",data:t})}function q(t){return Object(r["a"])({url:"/system/rct/listRctNoByCompany/"+t,method:"get"})}function N(t){return Object(r["a"])({url:"/system/rct/writeoff",method:"post",data:t})}},fff5:function(t,e,n){"use strict";n.d(e,"i",(function(){return a})),n.d(e,"b",(function(){return i})),n.d(e,"g",(function(){return o})),n.d(e,"a",(function(){return c})),n.d(e,"l",(function(){return s})),n.d(e,"e",(function(){return u})),n.d(e,"c",(function(){return l})),n.d(e,"h",(function(){return d})),n.d(e,"j",(function(){return f})),n.d(e,"f",(function(){return m})),n.d(e,"d",(function(){return h})),n.d(e,"m",(function(){return g})),n.d(e,"k",(function(){return p}));var r=n("b775");function a(t){return Object(r["a"])({url:"/system/rscharge/list",method:"get",params:t})}function i(t){return Object(r["a"])({url:"/system/rscharge/aggregator",method:"post",data:t})}function o(t){return Object(r["a"])({url:"/system/rscharge/"+t,method:"get"})}function c(t){return Object(r["a"])({url:"/system/rscharge",method:"post",data:t})}function s(t){return Object(r["a"])({url:"/system/rscharge",method:"put",data:t})}function u(t){return Object(r["a"])({url:"/system/rscharge/"+t,method:"delete"})}function l(t,e){var n={chargeId:t,status:e};return Object(r["a"])({url:"/system/rscharge/changeStatus",method:"put",data:n})}function d(t){return Object(r["a"])({url:"/system/rscharge/charges",method:"get",params:t})}function f(t){return Object(r["a"])({url:"/system/rscharge/selectList",method:"get",params:t})}function m(t){return Object(r["a"])({url:"/system/rscharge/findHedging",method:"get",params:t})}function h(t){return Object(r["a"])({url:"/system/rscharge/writeoff",method:"post",data:t})}function g(t){return Object(r["a"])({url:"/system/rscharge/verify",method:"post",data:t})}function p(t){return Object(r["a"])({url:"/system/rscharge/turnback",method:"post",data:t})}}}]);