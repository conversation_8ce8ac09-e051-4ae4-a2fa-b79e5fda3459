(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d0c77ad"],{5194:function(n,r,t){"use strict";t.r(r);var e=function(){var n=this,r=n.$createElement,t=n._self._c||r;return t("i-frame",{attrs:{src:n.url}})},a=[],u=t("061b"),c={name:"Druid",components:{iFrame:u["a"]},data:function(){return{url:"/prod-api/druid/login.html"}}},i=c,l=t("2877"),o=Object(l["a"])(i,e,a,!1,null,null,null);r["default"]=o.exports}}]);