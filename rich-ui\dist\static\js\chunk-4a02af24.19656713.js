(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-4a02af24"],{"2a23":function(t,e,s){"use strict";s.r(e);var i=function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",{staticClass:"box"},[s("div",{staticStyle:{margin:"0","font-size":"14px","line-height":"13px"}},[t._v(t._s(t.scope.row.serviceEnType))]),s("div",{staticClass:"unHighlight-text",staticStyle:{margin:"0"}},[t._v(" "+t._s(0==t.scope.row.imExPort?"进口":"出口"+t.scope.row.logisticsEnType)+" ")])])},n=[],a={name:"logisticsType",props:["scope"],data:function(){return{size:this.$store.state.app.size||"mini"}}},c=a,o=(s("4a1a"),s("2877")),r=Object(o["a"])(c,i,n,!1,null,"5e13bb51",null);e["default"]=r.exports},"4a1a":function(t,e,s){"use strict";s("efd5")},efd5:function(t,e,s){}}]);