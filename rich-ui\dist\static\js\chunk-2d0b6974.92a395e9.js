(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d0b6974"],{"1e8b":function(e,t,a){"use strict";a.r(t);var l=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-form",{ref:"form",attrs:{model:e.user,rules:e.rules,"label-width":"88px"}},[a("el-form-item",{attrs:{label:"管理分配"}},[a("el-row",[a("el-col",{attrs:{span:4}},[a("tree-select",{attrs:{pass:e.user.sqdDeptId,placeholder:"归属部门",type:"dept"},on:{return:function(t){e.user.sqdDeptId=t}}})],1),a("el-col",{attrs:{span:5}},[a("el-input",{attrs:{disabled:"",placeholder:"员工编号",maxlength:"10"},model:{value:e.user.staffCode,callback:function(t){e.$set(e.user,"staffCode",t)},expression:"user.staffCode"}})],1),a("el-col",{attrs:{span:5}},[a("el-input",{attrs:{disabled:"",placeholder:"登录账户",maxlength:"30"},model:{value:e.user.staffUsername,callback:function(t){e.$set(e.user,"staffUsername",t)},expression:"user.staffUsername"}})],1),a("el-col",{attrs:{span:5}},[a("el-select",{staticStyle:{width:"100%"},model:{value:e.user.staffJobStatus,callback:function(t){e.$set(e.user,"staffJobStatus",t)},expression:"user.staffJobStatus"}},e._l(e.dict.type.sys_normal_disable,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),a("el-col",{attrs:{span:5}},[a("el-input",{attrs:{disabled:"admin"!=e.$store.state.user.userRole,placeholder:"mac地址"},model:{value:e.user.macAddress,callback:function(t){e.$set(e.user,"macAddress",t)},expression:"user.macAddress"}})],1)],1)],1),a("el-form-item",{attrs:{label:"基础资料"}},[a("el-row",[a("el-col",{attrs:{span:4}},[a("el-input",{attrs:{placeholder:"员工昵称",maxlength:"30"},model:{value:e.user.staffShortName,callback:function(t){e.$set(e.user,"staffShortName",t)},expression:"user.staffShortName"}})],1),a("el-col",{attrs:{span:5}},[a("el-input",{attrs:{placeholder:"中文姓氏",maxlength:"30"},model:{value:e.user.staffFamilyLocalName,callback:function(t){e.$set(e.user,"staffFamilyLocalName",t)},expression:"user.staffFamilyLocalName"}})],1),a("el-col",{attrs:{span:5}},[a("el-input",{attrs:{placeholder:"中文名称",maxlength:"30"},model:{value:e.user.staffGivingLocalName,callback:function(t){e.$set(e.user,"staffGivingLocalName",t)},expression:"user.staffGivingLocalName"}})],1),a("el-col",{attrs:{span:5}},[a("el-input",{attrs:{placeholder:"英文姓氏",maxlength:"30"},model:{value:e.user.staffFamilyEnName,callback:function(t){e.$set(e.user,"staffFamilyEnName",t)},expression:"user.staffFamilyEnName"}})],1),a("el-col",{attrs:{span:5}},[a("el-input",{attrs:{placeholder:"英文名称",maxlength:"30"},model:{value:e.user.staffGivingEnName,callback:function(t){e.$set(e.user,"staffGivingEnName",t)},expression:"user.staffGivingEnName"}})],1),a("el-col",{attrs:{span:4}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"性别"},model:{value:e.user.staffGender,callback:function(t){e.$set(e.user,"staffGender",t)},expression:"user.staffGender"}},e._l(e.dict.type.sys_user_sex,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),a("el-col",{attrs:{span:5}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{format:"yyyy 年 MM 月 dd 日",placeholder:"生日",type:"date","value-format":"yyyy-MM-dd"},model:{value:e.user.staffBirthday,callback:function(t){e.$set(e.user,"staffBirthday",t)},expression:"user.staffBirthday"}})],1),a("el-col",{attrs:{span:5}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"证件类型"},model:{value:e.user.credentialType,callback:function(t){e.$set(e.user,"credentialType",t)},expression:"user.credentialType"}},e._l(e.dict.type.sys_credential_type,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),a("el-col",{attrs:{span:10}},[a("el-input",{attrs:{placeholder:"身份证号",maxlength:"50"},model:{value:e.user.staffIdentity,callback:function(t){e.$set(e.user,"staffIdentity",t)},expression:"user.staffIdentity"}})],1),a("el-col",{attrs:{span:4}},[a("el-input",{attrs:{placeholder:"民族",maxlength:"50"},model:{value:e.user.staffNativeplace,callback:function(t){e.$set(e.user,"staffNativeplace",t)},expression:"user.staffNativeplace"}})],1),a("el-col",{attrs:{span:5}},[a("el-input",{attrs:{placeholder:"国籍",maxlength:"50"},model:{value:e.user.staffNation,callback:function(t){e.$set(e.user,"staffNation",t)},expression:"user.staffNation"}})],1),a("el-col",{attrs:{span:5}},[a("el-input",{attrs:{placeholder:"宗教信仰",maxlength:"50"},model:{value:e.user.staffReligion,callback:function(t){e.$set(e.user,"staffReligion",t)},expression:"user.staffReligion"}})],1),a("el-col",{attrs:{span:5}},[a("el-input",{attrs:{placeholder:"政治面貌",maxlength:"50"},model:{value:e.user.staffPoliticalCountenance,callback:function(t){e.$set(e.user,"staffPoliticalCountenance",t)},expression:"user.staffPoliticalCountenance"}})],1),a("el-col",{attrs:{span:5}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"婚姻状况"},model:{value:e.user.staffMarital,callback:function(t){e.$set(e.user,"staffMarital",t)},expression:"user.staffMarital"}},e._l(e.dict.type.sys_user_marriage,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1)],1)],1),a("el-form-item",{attrs:{label:"联系方式"}},[a("el-row",[a("el-col",{attrs:{span:4}},[a("el-input",{attrs:{placeholder:"手机号码",maxlength:"11"},model:{value:e.user.staffTelNum,callback:function(t){e.$set(e.user,"staffTelNum",t)},expression:"user.staffTelNum"}})],1),a("el-col",{attrs:{span:5}},[a("el-input",{attrs:{placeholder:"工作QQ",maxlength:"20"},model:{value:e.user.staffQ,callback:function(t){e.$set(e.user,"staffQ",t)},expression:"user.staffQ"}})],1),a("el-col",{attrs:{span:5}},[a("el-input",{attrs:{placeholder:"固定电话",maxlength:"20"},model:{value:e.user.staffPhoneNum,callback:function(t){e.$set(e.user,"staffPhoneNum",t)},expression:"user.staffPhoneNum"}})],1),a("el-col",{attrs:{span:10}},[a("el-input",{attrs:{placeholder:"公司邮箱",maxlength:"50"},model:{value:e.user.staffEmailEnterprise,callback:function(t){e.$set(e.user,"staffEmailEnterprise",t)},expression:"user.staffEmailEnterprise"}})],1),a("el-col",{attrs:{span:4}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"居住性质"},model:{value:e.user.residentType,callback:function(t){e.$set(e.user,"residentType",t)},expression:"user.residentType"}},e._l(e.dict.type.sys_resident_type,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),a("el-col",{attrs:{span:5}},[a("location-select",{ref:"location",attrs:{pass:e.user.locationId,type:"location",placeholder:"居住区域"},on:{return:function(t){e.user.locationId=t}}})],1),a("el-col",{attrs:{span:15}},[a("el-input",{attrs:{maxlength:"50",placeholder:"详细地址"},model:{value:e.user.address,callback:function(t){e.$set(e.user,"address",t)},expression:"user.address"}})],1),a("el-col",{attrs:{span:4}},[a("el-input",{attrs:{placeholder:"紧急联系人",maxlength:"20"},model:{value:e.user.emergencyContactor,callback:function(t){e.$set(e.user,"emergencyContactor",t)},expression:"user.emergencyContactor"}})],1),a("el-col",{attrs:{span:5}},[a("el-input",{attrs:{placeholder:"联系人关系",maxlength:"20"},model:{value:e.user.emergencyContactorRelation,callback:function(t){e.$set(e.user,"emergencyContactorRelation",t)},expression:"user.emergencyContactorRelation"}})],1),a("el-col",{attrs:{span:15}},[a("el-input",{attrs:{placeholder:"紧急联系方式",maxlength:"20"},model:{value:e.user.emergencyPhone,callback:function(t){e.$set(e.user,"emergencyPhone",t)},expression:"user.emergencyPhone"}})],1)],1)],1),a("el-form-item",{attrs:{label:"学历信息"}},[a("el-row",[a("el-col",{attrs:{span:4}},[a("el-input",{attrs:{maxlength:"50",placeholder:"第一学历"},model:{value:e.user.firstDegree,callback:function(t){e.$set(e.user,"firstDegree",t)},expression:"user.firstDegree"}})],1),a("el-col",{attrs:{span:5}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{format:"yyyy 年 MM 月 dd 日",placeholder:"毕业时间",type:"date","value-format":"yyyy-MM-dd"},model:{value:e.user.graduationDate,callback:function(t){e.$set(e.user,"graduationDate",t)},expression:"user.graduationDate"}})],1),a("el-col",{attrs:{span:5}},[a("el-input",{attrs:{maxlength:"50",placeholder:"毕业院校"},model:{value:e.user.graduationFrom,callback:function(t){e.$set(e.user,"graduationFrom",t)},expression:"user.graduationFrom"}})],1),a("el-col",{attrs:{span:5}},[a("el-input",{attrs:{maxlength:"50",placeholder:"专业"},model:{value:e.user.major,callback:function(t){e.$set(e.user,"major",t)},expression:"user.major"}})],1),a("el-col",{attrs:{span:5}},[a("el-input",{attrs:{maxlength:"50",placeholder:"最高学历"},model:{value:e.user.degree,callback:function(t){e.$set(e.user,"degree",t)},expression:"user.degree"}})],1)],1)],1),a("el-form-item",{attrs:{label:"人事备忘"}},[a("el-row",[a("el-col",{attrs:{span:4}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{format:"yyyy 年 MM 月 dd 日",placeholder:"入职时间",type:"date","value-format":"yyyy-MM-dd"},model:{value:e.user.inductionDate,callback:function(t){e.$set(e.user,"inductionDate",t)},expression:"user.inductionDate"}})],1),a("el-col",{attrs:{span:5}},[a("el-input",{attrs:{placeholder:"健康状况",maxlength:"100"},model:{value:e.user.healthy,callback:function(t){e.$set(e.user,"healthy",t)},expression:"user.healthy"}})],1),a("el-col",{attrs:{span:5}},[a("el-input",{attrs:{maxlength:"50",placeholder:"过敏源"},model:{value:e.user.allergySource,callback:function(t){e.$set(e.user,"allergySource",t)},expression:"user.allergySource"}})],1),a("el-col",{attrs:{span:10}},[a("el-input",{attrs:{maxlength:"100",placeholder:"特别注意"},model:{value:e.user.specifically,callback:function(t){e.$set(e.user,"specifically",t)},expression:"user.specifically"}})],1)],1),a("el-input",{attrs:{autosize:{minRows:5,maxRows:20},maxlength:"200",placeholder:"人事部备注","show-word-limit":"",type:"textarea"},model:{value:e.user.remark,callback:function(t){e.$set(e.user,"remark",t)},expression:"user.remark"}})],1),a("el-form-item",[a("el-button",{attrs:{size:"mini",type:"primary"},on:{click:e.submit}},[e._v("保存")]),a("el-button",{attrs:{size:"mini",type:"danger"},on:{click:e.close}},[e._v("关闭")])],1)],1)},s=[],r=a("c0c7"),n={dicts:["sys_user_marriage","sys_normal_disable","sys_upload_status","sys_user_sex"],props:{user:{type:Object}},data:function(){return{rules:{staffUsername:[{required:!0,trigger:"blur"}],staffFamilyLocalName:[{required:!0,trigger:"blur"}],staffGivingLocalName:[{required:!0,trigger:"blur"}],staffFamilyEnName:[{required:!0,trigger:"blur"}],staffGivingEnName:[{required:!0,trigger:"blur"}],staffIdentity:[{pattern:/(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/,message:"身份证格式错误",trigger:"blur"}],staffEmailEnterprise:[{required:!0,trigger:"blur"},{type:"email",trigger:["blur","change"]}],staffPhoneNum:[{required:!0,trigger:"blur"},{pattern:/^1[3|4|5|6|7|8|9][0-9]\d{8}$/,message:"正确的手机号码",trigger:"blur"}]}}},methods:{submit:function(){var e=this;this.$refs["form"].validate((function(t){t&&Object(r["l"])(e.user).then((function(t){e.$modal.msgSuccess("修改成功")}))}))},close:function(){this.$tab.closePage()}}},o=n,u=a("2877"),c=Object(u["a"])(o,l,s,!1,null,null,null);t["default"]=c.exports}}]);