(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d0c514d"],{"3e27":function(e,t,r){"use strict";r.r(t);var a=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",{staticClass:"app-container"},[r("el-row",{attrs:{gutter:20}},[r("el-col",{attrs:{span:e.showLeft}},[r("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],ref:"queryForm",staticClass:"query",attrs:{inline:!0,model:e.queryParams,size:"mini"}},[r("el-form-item",{attrs:{label:"搜索",prop:"serviceQuery"}},[r("el-input",{staticStyle:{width:"100%"},attrs:{clearable:"",placeholder:"中文英文简称"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.serviceQuery,callback:function(t){e.$set(e.queryParams,"serviceQuery",t)},expression:"queryParams.serviceQuery"}})],1),r("el-form-item",[r("el-button",{attrs:{icon:"el-icon-search",size:"mini",type:"primary"},on:{click:e.handleQuery}},[e._v("搜索")]),r("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1)],1),r("el-col",{attrs:{span:e.showRight}},[r("el-row",{staticClass:"mb8",attrs:{gutter:10}},[r("el-col",{attrs:{span:1.5}},[r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:servicetype:add"],expression:"['system:servicetype:add']"}],attrs:{plain:"",icon:"el-icon-plus",size:"mini",type:"primary"},on:{click:e.handleAdd}},[e._v("新增 ")])],1),r("el-col",{attrs:{span:1.5}},[r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:servicetype:remove"],expression:"['system:servicetype:remove']"}],attrs:{disabled:e.multiple,icon:"el-icon-delete",plain:"",size:"mini",type:"danger"},on:{click:e.handleDelete}},[e._v("删除 ")])],1),r("el-col",{attrs:{span:1.5}},[r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:type:export"],expression:"['system:type:export']"}],attrs:{icon:"el-icon-download",plain:"",size:"mini",type:"warning"},on:{click:e.handleExport}},[e._v("导出 ")])],1),r("right-toolbar",{attrs:{showSearch:e.showSearch},on:{"update:showSearch":function(t){e.showSearch=t},"update:show-search":function(t){e.showSearch=t},queryTable:e.getList}})],1),e.refreshTable?r("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.typeList,"default-expand-all":e.isExpandAll,"tree-props":{children:"children",hasChildren:"hasChildren"},"row-key":"serviceTypeId"}},[r("el-table-column",{attrs:{align:"left",label:"服务类型名称",width:"300"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(t.row.serviceShortName)+" "),r("a",{staticStyle:{margin:"0","font-weight":"bold","font-size":"small"}},[e._v(e._s(t.row.serviceLocalName))]),e._v(" "+e._s(t.row.serviceEnName)+" ")]}}],null,!1,1172687831)}),r("el-table-column",{attrs:{align:"center",label:"基础物流",prop:"isBasicTransport",width:"68"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("dict-tag",{attrs:{options:e.dict.type.sys_yes_no,value:t.row.isBasicTransport}})]}}],null,!1,3234238135)}),r("el-table-column",{attrs:{align:"center",label:"服务大类",width:"60"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(e.getType(t.row.typeId))+" ")]}}],null,!1,2663559719)}),r("el-table-column",{attrs:{align:"center",label:"排序",prop:"orderNum",width:"48"}}),r("el-table-column",{attrs:{align:"center",label:"知名度",prop:"verticalSort",width:"58"}}),r("el-table-column",{attrs:{align:"center",label:"等级",prop:"serviceTypeLevel",width:"48"}}),r("el-table-column",{key:"status",attrs:{align:"center",label:"状态",width:"68"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("el-switch",{attrs:{"active-value":"0","inactive-value":"1"},on:{change:function(r){return e.handleStatusChange(t.row)}},model:{value:t.row.status,callback:function(r){e.$set(t.row,"status",r)},expression:"scope.row.status"}})]}}],null,!1,3955094654)}),r("el-table-column",{attrs:{align:"left",label:"备注",prop:"remark"}}),r("el-table-column",{attrs:{align:"center","class-name":"small-padding fixed-width",label:"操作",width:"170"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:servicetype:edit"],expression:"['system:servicetype:edit']"}],staticStyle:{"margin-right":"-8px"},attrs:{icon:"el-icon-edit",size:"mini",type:"success"},on:{click:function(r){return e.handleUpdate(t.row)}}},[e._v("修改 ")]),0!=t.row.parentId?r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:servicetype:remove"],expression:"['system:servicetype:remove']"}],staticStyle:{"margin-right":"-8px"},attrs:{icon:"el-icon-delete",size:"mini",type:"danger"},on:{click:function(r){return e.handleDelete(t.row)}}},[e._v("删除 ")]):e._e()]}}],null,!1,4045246896)})],1):e._e()],1)],1),r("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"},{name:"dialogDragWidth",rawName:"v-dialogDragWidth"}],attrs:{"close-on-click-modal":!1,"modal-append-to-body":!1,title:e.title,visible:e.open,"append-to-body":"",width:"500px"},on:{"update:visible":function(t){e.open=t}}},[r("el-form",{ref:"form",staticClass:"edit",attrs:{model:e.form,rules:e.rules,"label-width":"80px"}},[r("el-form-item",{attrs:{label:"简称",prop:"serviceShortName"}},[r("el-input",{attrs:{placeholder:"简称"},model:{value:e.form.serviceShortName,callback:function(t){e.$set(e.form,"serviceShortName",t)},expression:"form.serviceShortName"}})],1),r("el-form-item",{attrs:{label:"中文名",prop:"serviceLocalName"}},[r("el-input",{attrs:{placeholder:"中文名"},model:{value:e.form.serviceLocalName,callback:function(t){e.$set(e.form,"serviceLocalName",t)},expression:"form.serviceLocalName"}})],1),r("el-form-item",{attrs:{label:"英文名",prop:"serviceEnName"}},[r("el-input",{attrs:{placeholder:"英文名"},model:{value:e.form.serviceEnName,callback:function(t){e.$set(e.form,"serviceEnName",t)},expression:"form.serviceEnName"}})],1),r("el-form-item",{attrs:{label:"服务大类",prop:"typeId"}},[r("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"服务大类"},model:{value:e.form.typeId,callback:function(t){e.$set(e.form,"typeId",t)},expression:"form.typeId"}},[r("el-option",{attrs:{label:"海运",value:"1"}},[e._v("海运-SEA")]),r("el-option",{attrs:{label:"空运",value:"2"}},[e._v("空运-AIR")]),r("el-option",{attrs:{label:"铁路",value:"3"}},[e._v("铁路-RAIL")]),r("el-option",{attrs:{label:"快递",value:"4"}},[e._v("快递-EXPRESS")]),r("el-option",{attrs:{label:"拖车",value:"5"}},[e._v("拖车-TRUCK")]),r("el-option",{attrs:{label:"报关",value:"6"}},[e._v("报关-CUSTOM")]),r("el-option",{attrs:{label:"清关",value:"7"}},[e._v("清关-CLEAR")]),r("el-option",{attrs:{label:"仓储",value:"8"}},[e._v("仓储-WHS")]),r("el-option",{attrs:{label:"拓展服务",value:"9"}},[e._v("拓展服务-EXT")])],1)],1),r("el-form-item",{attrs:{label:"基础物流",prop:"isBasicTransport"}},[r("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"是否基础物流服务"},model:{value:e.form.isBasicTransport,callback:function(t){e.$set(e.form,"isBasicTransport",t)},expression:"form.isBasicTransport"}},e._l(e.dict.type.sys_yes_no,(function(e){return r("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),r("el-form-item",{attrs:{label:"纵向优先级",prop:"orderNum"}},[r("el-input-number",{staticStyle:{width:"100%"},attrs:{controls:!1,placeholder:"同级别知名度"},model:{value:e.form.orderNum,callback:function(t){e.$set(e.form,"orderNum",t)},expression:"form.orderNum"}})],1),r("el-form-item",{attrs:{label:"横向优先级",prop:"verticalSort"}},[r("el-input-number",{staticStyle:{width:"100%"},attrs:{controls:!1,placeholder:"上下层知名度"},model:{value:e.form.verticalSort,callback:function(t){e.$set(e.form,"verticalSort",t)},expression:"form.verticalSort"}})],1),r("el-form-item",{attrs:{label:"备注",prop:"remark"}},[r("el-input",{attrs:{placeholder:"备注"},model:{value:e.form.remark,callback:function(t){e.$set(e.form,"remark",t)},expression:"form.remark"}})],1)],1),r("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[r("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("确 定")]),r("el-button",{on:{click:e.cancel}},[e._v("取 消")])],1)],1)],1)},l=[],s=r("5530"),i=(r("d81d"),r("a0de")),n={name:"ServiceType",dicts:["sys_yes_no"],data:function(){return{showLeft:3,showRight:21,loading:!0,ids:[],single:!0,multiple:!0,showSearch:!0,typeList:[],title:"",isExpandAll:!0,refreshTable:!0,open:!1,queryParams:{serviceQuery:null},form:{},rules:{}}},watch:{showSearch:function(e){1==e?(this.showRight=21,this.showLeft=3):(this.showRight=24,this.showLeft=0)}},created:function(){this.getList()},methods:{getType:function(e){return 1==e?"SEA":2==e?"AIR":3==e?"RAIL":4==e?"EXPRESS":5==e?"TRUCK":6==e?"CUSTOM":7==e?"CLEAR":8==e?"WHS":9==e?"EXTEND":void 0},getList:function(){var e=this;this.loading=!0,Object(i["f"])(this.queryParams).then((function(t){e.typeList=t.rows,e.loading=!1}))},cancel:function(){this.open=!1,this.reset()},reset:function(){this.form={serviceTypeId:null,parentId:null,serviceShortName:null,serviceLocalName:null,serviceEnName:null,isLocked:null,chargeIdList:null,isBasicTransport:null,fitPortRoleCodeList:null,usedDocTypeCodeList:null,createdDocTypeCodeList:null,orderNum:null,verticalSort:null,serviceTypeLevel:null,status:0,remark:null,createBy:null,createTime:null,updateBy:null,updateTime:null,deleteBy:null,deleteTime:null,deleteStatus:0},this.resetForm("form")},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.resetForm("queryForm"),this.handleQuery()},handleSelectionChange:function(e){this.ids=e.map((function(e){return e.serviceTypeId})),this.single=1!=e.length,this.multiple=!e.length},handleAdd:function(e){this.reset(),this.open=!0,e&&(this.form.parentId=e.serviceTypeId),this.title="添加服务类型"},handleUpdate:function(e){var t=this;this.reset();var r=e.serviceTypeId||this.ids;Object(i["e"])(r).then((function(e){t.form=e.data,t.form.carrierIds=e.carrierIds,t.open=!0,t.title="修改服务类型"}))},submitForm:function(){var e=this;this.$refs["form"].validate((function(t){t&&(null!=e.form.serviceTypeId?Object(i["h"])(e.form).then((function(t){e.$modal.msgSuccess("修改成功"),e.open=!1,e.getList()})):Object(i["a"])(e.form).then((function(t){e.$modal.msgSuccess("新增成功"),e.open=!1,e.getList()})))}))},handleDelete:function(e){var t=this,r=e.serviceTypeId||this.ids;this.$confirm('是否确认删除服务类型编号为"'+r+'"的数据项？',"提示",{customClass:"modal-confirm"}).then((function(){return Object(i["d"])(r)})).then((function(){t.getList(),t.$modal.msgSuccess("删除成功")})).catch((function(){}))},handleExport:function(){this.download("system/type/export",Object(s["a"])({},this.queryParams),"type_".concat((new Date).getTime(),".xlsx"))},handleStatusChange:function(e){var t=this,r="0"==e.status?"启用":"停用";this.$confirm("确认要修改状态吗？","提示",{customClass:"modal-confirm"}).then((function(){return Object(i["c"])(e.serviceTypeId,e.status)})).then((function(){t.$modal.msgSuccess(r+"成功")})).catch((function(){e.status="0"==e.status?"1":"0"}))},handleLockedChange:function(e){var t=this,r="Y"==e.isLocked?"解锁":"锁定";this.$confirm("确认要修改状态吗？","提示",{customClass:"modal-confirm"}).then((function(){return Object(i["b"])(e.serviceTypeId,e.isLocked)})).then((function(){t.$modal.msgSuccess(r+"成功")})).catch((function(){e.isLocked="N"==e.isLocked?"Y":"N"}))},toggleExpandAll:function(){var e=this;this.refreshTable=!1,this.isExpandAll=!this.isExpandAll,this.$nextTick((function(){e.refreshTable=!0}))},getCompanyCarrierIds:function(e){this.form.carrierIds=e},ParentId:function(e){this.form.parentId=e}}},o=n,c=r("2877"),u=Object(c["a"])(o,a,l,!1,null,null,null);t["default"]=u.exports}}]);