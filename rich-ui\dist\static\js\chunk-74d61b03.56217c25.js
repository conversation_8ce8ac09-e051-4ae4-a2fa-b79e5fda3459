(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-74d61b03","chunk-68127a17","chunk-90e79546","chunk-689f2f24","chunk-68702101","chunk-7587eac6"],{"0062":function(e,t,a){"use strict";a.r(t);var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],ref:"queryForm",staticClass:"query",attrs:{inline:!0,model:e.queryParams,"label-width":"68px",size:"mini"}},[a("el-form-item",{attrs:{label:"任务名称",prop:"jobName"}},[a("el-input",{staticStyle:{width:"240px"},attrs:{clearable:"",placeholder:"任务名称"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.jobName,callback:function(t){e.$set(e.queryParams,"jobName",t)},expression:"queryParams.jobName"}})],1),a("el-form-item",{attrs:{label:"任务组名",prop:"jobGroup"}},[a("el-select",{staticStyle:{width:"240px"},attrs:{clearable:"",placeholder:"任务组名"},model:{value:e.queryParams.jobGroup,callback:function(t){e.$set(e.queryParams,"jobGroup",t)},expression:"queryParams.jobGroup"}},e._l(e.dict.type.sys_job_group,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),a("el-form-item",{attrs:{label:"执行状态",prop:"status"}},[a("el-select",{staticStyle:{width:"240px"},attrs:{clearable:"",placeholder:"执行状态"},model:{value:e.queryParams.status,callback:function(t){e.$set(e.queryParams,"status",t)},expression:"queryParams.status"}},e._l(e.dict.type.sys_common_status,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),a("el-form-item",{attrs:{label:"执行时间"}},[a("el-date-picker",{staticStyle:{width:"240px"},attrs:{"end-placeholder":"结束日期","range-separator":"-","start-placeholder":"开始日期",type:"daterange","default-time":["00:00:00","23:59:59"],"value-format":"yyyy-MM-dd HH:mm:ss"},model:{value:e.dateRange,callback:function(t){e.dateRange=t},expression:"dateRange"}})],1),a("el-form-item",[a("el-button",{attrs:{icon:"el-icon-search",size:"mini",type:"primary"},on:{click:e.handleQuery}},[e._v("搜索")]),a("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1),a("el-row",{staticClass:"mb8",attrs:{gutter:10}},[a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["monitor:job:remove"],expression:"['monitor:job:remove']"}],attrs:{disabled:e.multiple,icon:"el-icon-delete",plain:"",size:"mini",type:"danger"},on:{click:e.handleDelete}},[e._v("删除 ")])],1),a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["monitor:job:remove"],expression:"['monitor:job:remove']"}],attrs:{icon:"el-icon-delete",plain:"",size:"mini",type:"danger"},on:{click:e.handleClean}},[e._v("清空 ")])],1),a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["monitor:job:export"],expression:"['monitor:job:export']"}],attrs:{icon:"el-icon-download",plain:"",size:"mini",type:"warning"},on:{click:e.handleExport}},[e._v("导出 ")])],1),a("el-col",{attrs:{span:1.5}},[a("el-button",{attrs:{icon:"el-icon-close",plain:"",size:"mini",type:"warning"},on:{click:e.handleClose}},[e._v("关闭 ")])],1),a("right-toolbar",{attrs:{showSearch:e.showSearch},on:{"update:showSearch":function(t){e.showSearch=t},"update:show-search":function(t){e.showSearch=t},queryTable:e.getList}})],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.jobLogList},on:{"selection-change":e.handleSelectionChange}},[a("el-table-column",{attrs:{type:"selection",width:"28",align:"center"}}),a("el-table-column",{attrs:{align:"center",label:"日志编号",prop:"jobLogId",width:"80"}}),a("el-table-column",{attrs:{"show-tooltip-when-overflow":!0,align:"center",label:"任务名称",prop:"jobName"}}),a("el-table-column",{attrs:{"show-tooltip-when-overflow":!0,align:"center",label:"任务组名",prop:"jobGroup"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("dict-tag",{attrs:{options:e.dict.type.sys_job_group,value:t.row.jobGroup}})]}}])}),a("el-table-column",{attrs:{"show-tooltip-when-overflow":!0,align:"center",label:"调用目标字符串",prop:"invokeTarget"}}),a("el-table-column",{attrs:{"show-tooltip-when-overflow":!0,align:"center",label:"日志信息",prop:"jobMessage"}}),a("el-table-column",{attrs:{align:"center",label:"执行状态",prop:"status"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("dict-tag",{attrs:{options:e.dict.type.sys_common_status,value:t.row.status}})]}}])}),a("el-table-column",{attrs:{align:"center",label:"执行时间",prop:"createTime",width:"180"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(e.parseTime(t.row.createTime)))])]}}])}),a("el-table-column",{attrs:{align:"center","class-name":"small-padding fixed-width",label:"操作"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["monitor:job:query"],expression:"['monitor:job:query']"}],attrs:{icon:"el-icon-view",size:"mini",type:"text"},on:{click:function(a){return e.handleView(t.row)}}},[e._v("详细 ")])]}}])})],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total>0"}],attrs:{limit:e.queryParams.pageSize,page:e.queryParams.pageNum,total:e.total},on:{"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},pagination:e.getList}}),a("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"},{name:"dialogDragWidth",rawName:"v-dialogDragWidth"}],attrs:{"close-on-click-modal":!1,"modal-append-to-body":!1,visible:e.open,"append-to-body":"",title:"调度日志详细",width:"700px"},on:{"update:visible":function(t){e.open=t}}},[a("el-form",{ref:"form",attrs:{model:e.form,"label-width":"100px",size:"mini"}},[a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"日志序号："}},[e._v(e._s(e.form.jobLogId))]),a("el-form-item",{attrs:{label:"任务名称："}},[e._v(e._s(e.form.jobName))])],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"任务分组："}},[e._v(e._s(e.form.jobGroup))]),a("el-form-item",{attrs:{label:"执行时间："}},[e._v(e._s(e.form.createTime))])],1),a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"调用方法："}},[e._v(e._s(e.form.invokeTarget))])],1),a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"日志信息："}},[e._v(e._s(e.form.jobMessage))])],1),a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"执行状态："}},[0==e.form.status?a("div",[e._v("正常")]):1==e.form.status?a("div",[e._v("失败")]):e._e()])],1),a("el-col",{attrs:{span:24}},[1==e.form.status?a("el-form-item",{attrs:{label:"异常信息："}},[e._v(e._s(e.form.exceptionInfo))]):e._e()],1)],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(t){e.open=!1}}},[e._v("关 闭")])],1)],1)],1)},i=[],l=a("5530"),r=(a("d81d"),a("a159")),o=a("b775");function s(e){return Object(o["a"])({url:"/monitor/jobLog/list",method:"get",params:e})}function d(e){return Object(o["a"])({url:"/monitor/jobLog/"+e,method:"delete"})}function c(){return Object(o["a"])({url:"/monitor/jobLog/clean",method:"delete"})}var u={name:"JobLog",dicts:["sys_common_status","sys_job_group"],data:function(){return{loading:!0,ids:[],multiple:!0,showSearch:!0,total:0,jobLogList:[],open:!1,dateRange:[],form:{},queryParams:{pageNum:1,pageSize:10,jobName:void 0,jobGroup:void 0,status:void 0}}},created:function(){var e=this,t=this.$route.params&&this.$route.params.jobId;void 0!=t&&0!=t?Object(r["d"])(t).then((function(t){e.queryParams.jobName=t.data.jobName,e.queryParams.jobGroup=t.data.jobGroup,e.getList()})):this.getList()},methods:{getList:function(){var e=this;this.loading=!0,s(this.addDateRange(this.queryParams,this.dateRange)).then((function(t){e.jobLogList=t.rows,e.total=t.total,e.loading=!1}))},handleClose:function(){var e={path:"/monitor/job"};this.$tab.closeOpenPage(e)},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.dateRange=[],this.resetForm("queryForm"),this.handleQuery()},handleSelectionChange:function(e){this.ids=e.map((function(e){return e.jobLogId})),this.multiple=!e.length},handleView:function(e){this.open=!0,this.form=e},handleDelete:function(e){var t=this,a=this.ids;this.$confirm('是否确认删除调度日志编号为"'+a+'"的数据项？',"提示",{customClass:"modal-confirm"}).then((function(){return d(a)})).then((function(){t.getList(),t.$modal.msgSuccess("删除成功")})).catch((function(){}))},handleClean:function(){var e=this;this.$confirm("是否确认清空所有调度日志数据项？","提示",{customClass:"modal-confirm"}).then((function(){return c()})).then((function(){e.getList(),e.$modal.msgSuccess("清空成功")})).catch((function(){}))},handleExport:function(){this.download("/monitor/jobLog/export",Object(l["a"])({},this.queryParams),"log_".concat((new Date).getTime(),".xlsx"))}}},g=u,p=a("2877"),m=Object(p["a"])(g,n,i,!1,null,null,null);t["default"]=m.exports},"0ccb":function(e,t,a){var n=a("e330"),i=a("50c4"),l=a("577e"),r=a("1148"),o=a("1d80"),s=n(r),d=n("".slice),c=Math.ceil,u=function(e){return function(t,a,n){var r,u,g=l(o(t)),p=i(a),m=g.length,f=void 0===n?" ":l(n);return p<=m||""==f?g:(r=p-m,u=s(f,c(r/f.length)),u.length>r&&(u=d(u,0,r)),e?g+u:u+g)}};e.exports={start:u(!1),end:u(!0)}},4450:function(e,t,a){},4678:function(e,t,a){var n={"./af":"2bfb","./af.js":"2bfb","./ar":"8e73","./ar-dz":"a356","./ar-dz.js":"a356","./ar-kw":"423e","./ar-kw.js":"423e","./ar-ly":"1cfd","./ar-ly.js":"1cfd","./ar-ma":"0a84","./ar-ma.js":"0a84","./ar-ps":"4c98","./ar-ps.js":"4c98","./ar-sa":"8230","./ar-sa.js":"8230","./ar-tn":"6d83","./ar-tn.js":"6d83","./ar.js":"8e73","./az":"485c","./az.js":"485c","./be":"1fc1","./be.js":"1fc1","./bg":"84aa","./bg.js":"84aa","./bm":"a7fa","./bm.js":"a7fa","./bn":"9043","./bn-bd":"9686","./bn-bd.js":"9686","./bn.js":"9043","./bo":"d26a","./bo.js":"d26a","./br":"6887","./br.js":"6887","./bs":"25548","./bs.js":"25548","./ca":"d716","./ca.js":"d716","./cs":"3c0d","./cs.js":"3c0d","./cv":"03ec","./cv.js":"03ec","./cy":"9797","./cy.js":"9797","./da":"0f14","./da.js":"0f14","./de":"b469","./de-at":"b3eb","./de-at.js":"b3eb","./de-ch":"bb71","./de-ch.js":"bb71","./de.js":"b469","./dv":"598a","./dv.js":"598a","./el":"8d47","./el.js":"8d47","./en-au":"0e6b","./en-au.js":"0e6b","./en-ca":"3886","./en-ca.js":"3886","./en-gb":"39a6","./en-gb.js":"39a6","./en-ie":"e1d3","./en-ie.js":"e1d3","./en-il":"7333","./en-il.js":"7333","./en-in":"ec2e","./en-in.js":"ec2e","./en-nz":"6f50","./en-nz.js":"6f50","./en-sg":"b7e9","./en-sg.js":"b7e9","./eo":"65db","./eo.js":"65db","./es":"898b","./es-do":"0a3c","./es-do.js":"0a3c","./es-mx":"b5b7","./es-mx.js":"b5b7","./es-us":"55c9","./es-us.js":"55c9","./es.js":"898b","./et":"ec18","./et.js":"ec18","./eu":"0ff2","./eu.js":"0ff2","./fa":"8df48","./fa.js":"8df48","./fi":"81e9","./fi.js":"81e9","./fil":"d69a","./fil.js":"d69a","./fo":"0721","./fo.js":"0721","./fr":"9f26","./fr-ca":"d9f8","./fr-ca.js":"d9f8","./fr-ch":"0e49","./fr-ch.js":"0e49","./fr.js":"9f26","./fy":"7118","./fy.js":"7118","./ga":"5120","./ga.js":"5120","./gd":"f6b46","./gd.js":"f6b46","./gl":"8840","./gl.js":"8840","./gom-deva":"aaf2","./gom-deva.js":"aaf2","./gom-latn":"0caa","./gom-latn.js":"0caa","./gu":"e0c5","./gu.js":"e0c5","./he":"c7aa","./he.js":"c7aa","./hi":"dc4d","./hi.js":"dc4d","./hr":"4ba9","./hr.js":"4ba9","./hu":"5b14","./hu.js":"5b14","./hy-am":"d6b6","./hy-am.js":"d6b6","./id":"5038","./id.js":"5038","./is":"0558","./is.js":"0558","./it":"6e98","./it-ch":"6f12","./it-ch.js":"6f12","./it.js":"6e98","./ja":"079e","./ja.js":"079e","./jv":"b540","./jv.js":"b540","./ka":"201b","./ka.js":"201b","./kk":"6d79","./kk.js":"6d79","./km":"e81d","./km.js":"e81d","./kn":"3e92","./kn.js":"3e92","./ko":"22f8","./ko.js":"22f8","./ku":"2421","./ku-kmr":"7558","./ku-kmr.js":"7558","./ku.js":"2421","./ky":"9609","./ky.js":"9609","./lb":"440c","./lb.js":"440c","./lo":"b29d","./lo.js":"b29d","./lt":"26f9","./lt.js":"26f9","./lv":"b97c","./lv.js":"b97c","./me":"293c","./me.js":"293c","./mi":"688b","./mi.js":"688b","./mk":"6909","./mk.js":"6909","./ml":"02fb","./ml.js":"02fb","./mn":"958b","./mn.js":"958b","./mr":"39bd","./mr.js":"39bd","./ms":"ebe4","./ms-my":"6403","./ms-my.js":"6403","./ms.js":"ebe4","./mt":"1b45","./mt.js":"1b45","./my":"8689","./my.js":"8689","./nb":"6ce3","./nb.js":"6ce3","./ne":"3a39","./ne.js":"3a39","./nl":"facd","./nl-be":"db29","./nl-be.js":"db29","./nl.js":"facd","./nn":"b84c","./nn.js":"b84c","./oc-lnc":"167b","./oc-lnc.js":"167b","./pa-in":"f3ff","./pa-in.js":"f3ff","./pl":"8d57","./pl.js":"8d57","./pt":"f260","./pt-br":"d2d4","./pt-br.js":"d2d4","./pt.js":"f260","./ro":"972c","./ro.js":"972c","./ru":"957c","./ru.js":"957c","./sd":"6784","./sd.js":"6784","./se":"ffff","./se.js":"ffff","./si":"eda5","./si.js":"eda5","./sk":"7be6","./sk.js":"7be6","./sl":"8155","./sl.js":"8155","./sq":"c8f3","./sq.js":"c8f3","./sr":"cf1e","./sr-cyrl":"13e9","./sr-cyrl.js":"13e9","./sr.js":"cf1e","./ss":"52bd","./ss.js":"52bd","./sv":"5fbd","./sv.js":"5fbd","./sw":"74dc","./sw.js":"74dc","./ta":"3de5","./ta.js":"3de5","./te":"5cbb","./te.js":"5cbb","./tet":"576c","./tet.js":"576c","./tg":"3b1b","./tg.js":"3b1b","./th":"10e8","./th.js":"10e8","./tk":"5aff","./tk.js":"5aff","./tl-ph":"0f38","./tl-ph.js":"0f38","./tlh":"cf75","./tlh.js":"cf75","./tr":"0e81","./tr.js":"0e81","./tzl":"cf51","./tzl.js":"cf51","./tzm":"c109","./tzm-latn":"b53d","./tzm-latn.js":"b53d","./tzm.js":"c109","./ug-cn":"6117","./ug-cn.js":"6117","./uk":"ada2","./uk.js":"ada2","./ur":"5294","./ur.js":"5294","./uz":"2e8c","./uz-latn":"010e","./uz-latn.js":"010e","./uz.js":"2e8c","./vi":"2921","./vi.js":"2921","./x-pseudo":"fd7e","./x-pseudo.js":"fd7e","./yo":"7f33","./yo.js":"7f33","./zh-cn":"5c3a","./zh-cn.js":"5c3a","./zh-hk":"49ab","./zh-hk.js":"49ab","./zh-mo":"3a6c","./zh-mo.js":"3a6c","./zh-tw":"90ea","./zh-tw.js":"90ea"};function i(e){var t=l(e);return a(t)}function l(e){if(!a.o(n,e)){var t=new Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}return n[e]}i.keys=function(){return Object.keys(n)},i.resolve=l,e.exports=i,i.id="4678"},"4ba95":function(e,t,a){"use strict";a("4450")},"4d90":function(e,t,a){"use strict";var n=a("23e7"),i=a("0ccb").start,l=a("9a0c");n({target:"String",proto:!0,forced:l},{padStart:function(e){return i(this,e,arguments.length>1?arguments[1]:void 0)}})},"53cf":function(e,t,a){"use strict";a("53f5")},"53f5":function(e,t,a){},"58cf":function(e,t,a){"use strict";a.d(t,"a",(function(){return n}));var n={rctNo:{name:"操作单号",display:"text",aggregated:!1,align:"left",width:"120"},rctCreateTime:{name:"操作日期",display:"date",aggregated:!1,align:"left",width:"120"},emergencyLevel:{name:"紧急",display:"text",aggregated:!1,align:"left",width:"80"},difficultyLevel:{name:"难度",display:"text",aggregated:!1,align:"left",width:"80"},clientId:{name:"委托单位",display:"text",aggregated:!1,align:"left",width:"120"},clientSummary:{name:"委托单位全称",display:"text",aggregated:!1,align:"left",width:"150"},clientRoleId:{name:"客户角色",display:"text",aggregated:!1,align:"left",width:"100"},clientJobNo:{name:"客户单号",display:"text",aggregated:!1,align:"left",width:"120"},relationClientIdList:{name:"相关单位",display:"text",aggregated:!1,align:"left",width:"120"},orderBelongsTo:{name:"订单所属",display:"text",aggregated:!1,align:"left",width:"100"},goodsNameSummary:{name:"货名概要",display:"text",aggregated:!1,align:"left",width:"150"},cargoTypeCodeSum:{name:"货物特征",display:"text",aggregated:!1,align:"left",width:"100"},packageQuantity:{name:"件数",display:"number",aggregated:!0,align:"right",width:"100"},grossWeight:{name:"毛重",display:"number",aggregated:!0,align:"right",width:"100"},goodsVolume:{name:"体积",display:"number",aggregated:!0,align:"right",width:"100"},goodsCurrencyCode:{name:"货值币种",display:"text",aggregated:!1,align:"left",width:"80"},goodsValue:{name:"货值",display:"number",aggregated:!0,align:"right",width:"100"},logisticsTypeId:{name:"物流类型",display:"text",aggregated:!1,align:"left",width:"100"},impExpType:{name:"进出口类型",display:"text",aggregated:!1,align:"left",width:"100"},logisticsTerms:{name:"运输条款",display:"text",aggregated:!1,align:"left",width:"100"},tradingTerms:{name:"贸易条款",display:"text",aggregated:!1,align:"left",width:"100"},tradingPaymentChannel:{name:"收汇方式",display:"text",aggregated:!1,align:"left",width:"100"},freightPaidWayCode:{name:"运费付于",display:"text",aggregated:!1,align:"left",width:"100"},pol:{name:"启运港",display:"text",aggregated:!1,align:"left",width:"100"},pod:{name:"卸货港",display:"text",aggregated:!1,align:"left",width:"100"},destinationPort:{name:"目的港",display:"text",aggregated:!1,align:"left",width:"100"},revenueTon:{name:"计费货量",display:"text",aggregated:!0,align:"left",width:"100"},ctnrTypeCode:{name:"箱型特征",display:"text",aggregated:!1,align:"left",width:"100"},serviceTypeIdList:{name:"服务类型",display:"text",aggregated:!1,align:"left",width:"100"},noAgreementShowed:{name:"不可套约",display:"boolean",aggregated:!1,align:"center",width:"80"},isCustomsIntransitShowed:{name:"属地清关",display:"boolean",aggregated:!1,align:"center",width:"80"},sqdExportCustomsType:{name:"报关方式",display:"text",aggregated:!1,align:"left",width:"100"},sqdTrailerType:{name:"拖车方式",display:"text",aggregated:!1,align:"left",width:"100"},sqdInsuranceType:{name:"投保方式",display:"text",aggregated:!1,align:"left",width:"100"},blTypeCode:{name:"提单类别",display:"text",aggregated:!1,align:"left",width:"100"},blFormCode:{name:"提单形式",display:"text",aggregated:!1,align:"left",width:"100"},sqdPodHandleAgent:{name:"换单代理",display:"text",aggregated:!1,align:"left",width:"120"},psaNo:{name:"订舱单号",display:"text",aggregated:!1,align:"left",width:"120"},carrierCode:{name:"承运人",display:"text",aggregated:!1,align:"left",width:"120"},polBookingAgent:{name:"订舱口",display:"text",aggregated:!1,align:"left",width:"100"},agreementTypeCode:{name:"合约类型",display:"text",aggregated:!1,align:"left",width:"100"},warehousingNo:{name:"入仓号",display:"text",aggregated:!1,align:"left",width:"120"},soNo:{name:"SO号码",display:"text",aggregated:!1,align:"left",width:"120"},blNoSum:{name:"提单号码",display:"text",aggregated:!1,align:"left",width:"120"},containersSum:{name:"柜号汇总",display:"text",aggregated:!1,align:"left",width:"150"},firstVessel:{name:"船名",display:"text",aggregated:!1,align:"left",width:"120"},firstVoyage:{name:"航次",display:"text",aggregated:!1,align:"left",width:"100"},firstCyOpenTime:{name:"开舱",display:"date",aggregated:!1,align:"left",width:"120"},firstCyClosingTime:{name:"截重",display:"date",aggregated:!1,align:"left",width:"120"},cvClosingTime:{name:"截关",display:"date",aggregated:!1,align:"left",width:"120"},siClosingTime:{name:"截补料",display:"date",aggregated:!1,align:"left",width:"120"},etd:{name:"ETD",display:"date",aggregated:!1,align:"left",width:"120"},podEta:{name:"ATD",display:"date",aggregated:!1,align:"left",width:"120"},eta:{name:"ETA",display:"date",aggregated:!1,align:"left",width:"120"},destinationPortAta:{name:"ATA",display:"date",aggregated:!1,align:"left",width:"120"},precarriageSupplierNo:{name:"拖车公司",display:"text",aggregated:!1,align:"left",width:"120"},precarriageRegionId:{name:"装运区域",display:"text",aggregated:!1,align:"left",width:"100"},precarriageAddress:{name:"装运详址",display:"text",aggregated:!1,align:"left",width:"150"},rctProcessStatus:{name:"订单进度",display:"text",aggregated:!1,align:"left",width:"100"},processStatusId:{name:"物流进度",display:"text",aggregated:!1,align:"left",width:"100"},processStatusTime:{name:"进度时间",display:"date",aggregated:!1,align:"left",width:"120"},transportStatusA:{name:"物流进度a",display:"text",aggregated:!1,align:"left",width:"100"},transportStatusB:{name:"物流进度b",display:"text",aggregated:!1,align:"left",width:"100"},warehousingTime:{name:"入仓",display:"date",aggregated:!1,align:"left",width:"120"},bookingTime:{name:"订舱",display:"date",aggregated:!1,align:"left",width:"120"},spaceCfmTime:{name:"放舱",display:"date",aggregated:!1,align:"left",width:"120"},trailerBookedTime:{name:"约车",display:"date",aggregated:!1,align:"left",width:"120"},containerCfmTime:{name:"约柜",display:"date",aggregated:!1,align:"left",width:"120"},containerLoadedTime:{name:"装柜",display:"date",aggregated:!1,align:"left",width:"120"},vesselCfmTime:{name:"配船",display:"date",aggregated:!1,align:"left",width:"120"},vgmSentTime:{name:"VGM",display:"date",aggregated:!1,align:"left",width:"120"},customDocsCfmTime:{name:"单证",display:"date",aggregated:!1,align:"left",width:"120"},customAuthorizedTime:{name:"授权",display:"date",aggregated:!1,align:"left",width:"120"},customExamineTime:{name:"查验",display:"date",aggregated:!1,align:"left",width:"120"},customReleasedTime:{name:"放行",display:"date",aggregated:!1,align:"left",width:"120"},siVerifyTime:{name:"对单",display:"date",aggregated:!1,align:"left",width:"120"},siPostedTime:{name:"补料",display:"date",aggregated:!1,align:"left",width:"120"},amsEnsPostedTime:{name:"AMS/ENS",display:"date",aggregated:!1,align:"left",width:"120"},isfEmnfPostedTime:{name:"ISF/EMNF",display:"date",aggregated:!1,align:"left",width:"120"},docStatus:{name:"文件进度",display:"text",aggregated:!1,align:"left",width:"100"},dnCompletedTime:{name:"录完应收",display:"date",aggregated:!1,align:"left",width:"120"},cnCompletedTime:{name:"录完应付",display:"date",aggregated:!1,align:"left",width:"120"},dnCfmTime:{name:"应收确认",display:"date",aggregated:!1,align:"left",width:"120"},cnCfmTime:{name:"应付确认",display:"date",aggregated:!1,align:"left",width:"120"},clientBlReleaseType:{name:"放单方式",display:"text",aggregated:!1,align:"left",width:"100"},supplierBlReleaseType:{name:"赎单方式",display:"text",aggregated:!1,align:"left",width:"100"},clientPaymentNode:{name:"收款节点",display:"text",aggregated:!1,align:"left",width:"100"},supplierPaymentNode:{name:"付款节点",display:"text",aggregated:!1,align:"left",width:"100"},estimatedRecieveTime:{name:"预收款日",display:"date",aggregated:!1,align:"left",width:"120"},estimatedPayTime:{name:"预付款日",display:"date",aggregated:!1,align:"left",width:"120"},cnAccCfmTime:{name:"应收审核",display:"date",aggregated:!1,align:"left",width:"120"},dnAccCfmTime:{name:"应付审核",display:"date",aggregated:!1,align:"left",width:"120"},cnInvIssuedTime:{name:"进项发票",display:"date",aggregated:!1,align:"left",width:"120"},dnInvIssuedTime:{name:"销项发票",display:"date",aggregated:!1,align:"left",width:"120"},dnReceiveSlipTime:{name:"应收水单",display:"date",aggregated:!1,align:"left",width:"120"},cnPaySlipTime:{name:"应付水单",display:"date",aggregated:!1,align:"left",width:"120"},dnInRmbBalance:{name:"折合未收",display:"number",aggregated:!0,align:"right",width:"120"},mainServicePaidStatus:{name:"主服务付款",display:"text",aggregated:!1,align:"left",width:"100"},allowReleaseBl:{name:"准许放单",display:"boolean",aggregated:!1,align:"center",width:"80"},allowGettingBl:{name:"准许赎单",display:"boolean",aggregated:!1,align:"center",width:"80"},accPromissBlReleaseTime:{name:"预计放单",display:"date",aggregated:!1,align:"left",width:"120"},accPromissBlGetTime:{name:"预计赎单",display:"date",aggregated:!1,align:"left",width:"120"},opAskingBlReleaseTime:{name:"期望放单",display:"date",aggregated:!1,align:"left",width:"120"},opAskingBlGetTime:{name:"期望赎单",display:"date",aggregated:!1,align:"left",width:"120"},actualBlReleaseTime:{name:"提单交付",display:"date",aggregated:!1,align:"left",width:"120"},actualBlGotTime:{name:"提单赎回",display:"date",aggregated:!1,align:"left",width:"120"},docDeliveryWay:{name:"交单方式",display:"text",aggregated:!1,align:"left",width:"100"},docTrackingRefer:{name:"邮递信息",display:"text",aggregated:!1,align:"left",width:"150"},agentNoticeTime:{name:"通知代理",display:"date",aggregated:!1,align:"left",width:"120"},qoutationInRmb:{name:"折合报价",display:"number",aggregated:!0,align:"right",width:"120"},inquiryInRmb:{name:"折合询价",display:"number",aggregated:!0,align:"right",width:"120"},estimatedProfitInRmb:{name:"预期利润",display:"number",aggregated:!0,align:"right",width:"120"},dnUsd:{name:"应收USD",display:"number",aggregated:!0,align:"right",width:"120"},dnRmb:{name:"应收RMB",display:"number",aggregated:!0,align:"right",width:"120"},dnUsdBalance:{name:"未收USD",display:"number",aggregated:!0,align:"right",width:"120"},dnRmbBalance:{name:"未收RMB",display:"number",aggregated:!0,align:"right",width:"120"},dnInRmb:{name:"折合应收",display:"number",aggregated:!0,align:"right",width:"120"},cnUsd:{name:"应付USD",display:"number",aggregated:!0,align:"right",width:"120"},cnRmb:{name:"应付RMB",display:"number",aggregated:!0,align:"right",width:"120"},cnUsdBalance:{name:"未付USD",display:"number",aggregated:!0,align:"right",width:"120"},cnRmbBalance:{name:"未付RMB",display:"number",aggregated:!0,align:"right",width:"120"},cnInRmb:{name:"折合应付",display:"number",aggregated:!0,align:"right",width:"120"},cnInRmbBalance:{name:"折合未付",display:"number",aggregated:!0,align:"right",width:"120"},profitUsd:{name:"USD利润",display:"number",aggregated:!0,align:"right",width:"120"},profitRmb:{name:"RMB利润",display:"number",aggregated:!0,align:"right",width:"120"},profitInRmb:{name:"折合利润",display:"number",aggregated:!0,align:"right",width:"120"},differenceInRmb:{name:"利润差额",display:"number",aggregated:!0,align:"right",width:"120"},profitRate:{name:"毛利率",display:"percentage",aggregated:!0,align:"right",width:"100"},profitRateAgg:{name:"统计毛利率",display:"percentage",aggregated:!0,align:"right",width:"100"},salesDept:{name:"所属部门",display:"getDept",aggregated:!1,align:"left",width:"100"},salesId:{name:"业务员",display:"getName",aggregated:!1,align:"left",width:"100"},salesAssistantId:{name:"业务助理",display:"getName",aggregated:!1,align:"left",width:"100"},salesObserverId:{name:"协助业务员",display:"getName",aggregated:!1,align:"left",width:"120"},statisticsSalesId:{name:"统计业务",display:"getName",aggregated:!1,align:"left",width:"120"},qoutationNo:{name:"报价单号",display:"text",aggregated:!1,align:"left",width:"120"},qoutationTime:{name:"报价日期",display:"date",aggregated:!1,align:"left",width:"120"},newBookingNo:{name:"订舱单号",display:"text",aggregated:!1,align:"left",width:"120"},newBookingTime:{name:"订舱日期",display:"date",aggregated:!1,align:"left",width:"120"},verifyPsaId:{name:"审核商务",display:"getName",aggregated:!1,align:"left",width:"100"},psaVerifyTime:{name:"审核时间",display:"date",aggregated:!1,align:"left",width:"120"},verifyOpLeaderId:{name:"操作主管",display:"text",aggregated:!1,align:"left",width:"100"},opLeaderVerifyTime:{name:"批示时间",display:"date",aggregated:!1,align:"left",width:"120"},opId:{name:"操作员",display:"getName",aggregated:!1,align:"left",width:"100"},opInnerRemark:{name:"操作备注",display:"text",aggregated:!1,align:"left",width:"150"},statusUpdateTime:{name:"状态日期",display:"date",aggregated:!1,align:"left",width:"120"},deleteStatus:{name:"数据状态",display:"text",aggregated:!1,align:"left",width:"100"}}},"5fb3":function(e,t,a){"use strict";a.d(t,"h",(function(){return i})),a.d(t,"g",(function(){return l})),a.d(t,"i",(function(){return r})),a.d(t,"e",(function(){return o})),a.d(t,"f",(function(){return s})),a.d(t,"a",(function(){return d})),a.d(t,"n",(function(){return c})),a.d(t,"d",(function(){return u})),a.d(t,"c",(function(){return g})),a.d(t,"j",(function(){return p})),a.d(t,"m",(function(){return m})),a.d(t,"l",(function(){return f})),a.d(t,"k",(function(){return h})),a.d(t,"b",(function(){return b}));var n=a("b775");function i(e){return Object(n["a"])({url:"/system/inventory/list",method:"get",params:e})}function l(e){return Object(n["a"])({url:"/system/inventory/aggregator",method:"get",params:e})}function r(e){return Object(n["a"])({url:"/system/inventory/lists",method:"get",params:e})}function o(e){return Object(n["a"])({url:"/system/inventory/"+e,method:"get"})}function s(e){return Object(n["a"])({url:"/system/inventory/package",method:"get",params:e})}function d(e){return Object(n["a"])({url:"/system/inventory",method:"post",data:e})}function c(e){return Object(n["a"])({url:"/system/inventory",method:"put",data:e})}function u(e){return Object(n["a"])({url:"/system/inventory/"+e,method:"delete"})}function g(e,t){var a={inventoryId:e,status:t};return Object(n["a"])({url:"/system/inventory/changeStatus",method:"put",data:a})}function p(e){return Object(n["a"])({url:"/system/inventory/outbound",method:"put",data:e})}function m(e){return Object(n["a"])({url:"/system/inventory/settlement",method:"put",data:e})}function f(e){return Object(n["a"])({url:"/system/inventory/preOutbound",method:"put",data:e})}function h(e){return Object(n["a"])({url:"/system/inventory/packUp",method:"put",data:e})}function b(e){return Object(n["a"])({url:"/system/inventory/cancelPkg",method:"put",data:e})}},6926:function(e,t,a){"use strict";a("8af8")},"712d":function(e,t,a){},"7a64":function(e,t,a){e.exports=a.p+"static/img/pageFoot.png"},"80f8":function(e,t,a){"use strict";a.r(t);var n,i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:e.showLeft}},[a("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],ref:"queryForm",staticClass:"query",attrs:{inline:!0,model:e.queryParams,size:"mini"}},[a("el-form-item",{attrs:{label:"流水",prop:"inboundSerialNo"}},[a("el-input",{attrs:{clearable:"",placeholder:"入仓流水号"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.inboundSerialNo,callback:function(t){e.$set(e.queryParams,"inboundSerialNo",t)},expression:"queryParams.inboundSerialNo"}})],1),a("el-form-item",{attrs:{label:"快递",prop:"inboundSerialNo"}},[a("el-input",{attrs:{clearable:"",placeholder:"入仓快递单号"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.driverInfo,callback:function(t){e.$set(e.queryParams,"driverInfo",t)},expression:"queryParams.driverInfo"}})],1),a("el-form-item",{attrs:{label:"打包"}},[a("el-select",{attrs:{clearable:""},on:{change:e.handleQuery},model:{value:e.queryParams.packageRecord,callback:function(t){e.$set(e.queryParams,"packageRecord",t)},expression:"queryParams.packageRecord"}},[a("el-option",{attrs:{label:"打包箱",value:"1"}}),a("el-option",{attrs:{label:"-",value:"0"}})],1)],1),a("el-form-item",{attrs:{label:"日期",prop:"inboundDate"}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{clearable:"",placeholder:"入仓日期",type:"date","value-format":"yyyy-MM-dd"},model:{value:e.queryParams.inboundDate,callback:function(t){e.$set(e.queryParams,"inboundDate",t)},expression:"queryParams.inboundDate"}})],1),a("el-form-item",{attrs:{label:"单号",prop:"forwarderNo"}},[a("el-input",{attrs:{clearable:"",placeholder:"货代单号"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.forwarderNo,callback:function(t){e.$set(e.queryParams,"forwarderNo",t)},expression:"queryParams.forwarderNo"}})],1),a("el-form-item",{attrs:{label:"唛头",prop:"forwarderNo"}},[a("el-input",{attrs:{clearable:"",placeholder:"唛头"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.sqdShippingMark,callback:function(t){e.$set(e.queryParams,"sqdShippingMark",t)},expression:"queryParams.sqdShippingMark"}})],1),a("el-form-item",{attrs:{label:"结算",prop:"rentalSettlementDate"}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{clearable:"",placeholder:"仓租结算至",type:"date","value-format":"yyyy-MM-dd"},model:{value:e.queryParams.rentalSettlementDate,callback:function(t){e.$set(e.queryParams,"rentalSettlementDate",t)},expression:"queryParams.rentalSettlementDate"}})],1),a("el-form-item",{attrs:{label:"代码",prop:"clientCode"}},[a("el-input",{attrs:{clearable:"",placeholder:"客户代码"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.clientCode,callback:function(t){e.$set(e.queryParams,"clientCode",t)},expression:"queryParams.clientCode"}})],1),a("el-form-item",{attrs:{label:"分单",prop:"subOrderNo"}},[a("el-input",{attrs:{clearable:"",placeholder:"分单号"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.subOrderNo,callback:function(t){e.$set(e.queryParams,"subOrderNo",t)},expression:"queryParams.subOrderNo"}})],1),a("el-form-item",{attrs:{label:"供货",prop:"supplier"}},[a("el-input",{attrs:{clearable:"",placeholder:"供货商"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.supplier,callback:function(t){e.$set(e.queryParams,"supplier",t)},expression:"queryParams.supplier"}})],1),a("el-form-item",{attrs:{label:"货名",prop:"cargoName"}},[a("el-input",{attrs:{clearable:"",placeholder:"总货名"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.cargoName,callback:function(t){e.$set(e.queryParams,"cargoName",t)},expression:"queryParams.cargoName"}})],1),a("el-form-item",[a("el-button",{attrs:{icon:"el-icon-search",size:"mini",type:"primary"},on:{click:e.handleQuery}},[e._v("搜索")]),a("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1)],1),a("el-col",{attrs:{span:e.showRight}},[a("el-row",{staticClass:"mb8",attrs:{gutter:10}},[a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:inventory:add"],expression:"['system:inventory:add']"}],attrs:{icon:"el-icon-plus",plain:"",size:"mini",type:"primary"},on:{click:e.handleAdd}},[e._v("入仓 ")])],1),a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:inventory:add"],expression:"['system:inventory:add']"}],attrs:{icon:"el-icon-plus",plain:"",size:"mini",type:"primary"},on:{click:e.handleAddPkg}},[e._v("添加打包箱 ")])],1),a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:inventory:add"],expression:"['system:inventory:add']"}],attrs:{disabled:0===e.ids.length,icon:"el-icon-plus",plain:"",size:"mini",type:"primary"},on:{click:e.handlePackingTo}},[e._v("打包至 ")])],1),a("el-col",{attrs:{span:1.5}},[a("el-button",{on:{click:e.handleOpenAggregator}},[e._v("数据汇总")]),a("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"},{name:"dialogDragWidth",rawName:"v-dialogDragWidth"}],attrs:{visible:e.openAggregator,"append-to-body":"",width:"80%"},on:{"update:visible":function(t){e.openAggregator=t}}},[a("data-aggregator-back-ground",{attrs:{"aggregate-function":e.listAggregatorRsInventory,"config-type":"warehouse-agg","data-source":e.aggregatorRctList,"data-source-type":"warehouse","field-label-map":e.fieldLabelMap}})],1)],1),a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:inventory:import"],expression:"['system:inventory:import']"}],attrs:{icon:"el-icon-upload2",plain:"",size:"mini",type:"info"},on:{click:e.handleImport}},[e._v("导入 ")])],1),a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:inventory:export"],expression:"['system:inventory:export']"}],attrs:{icon:"el-icon-download",plain:"",size:"mini",type:"warning"},on:{click:e.handleExport}},[e._v("导出 ")])],1),a("right-toolbar",{attrs:{showSearch:e.showSearch},on:{"update:showSearch":function(t){e.showSearch=t},"update:show-search":function(t){e.showSearch=t},queryTable:e.getList}})],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],ref:"inventoryTable",staticStyle:{width:"100%"},attrs:{data:e.inventoryList,"summary-method":e.getSummariesInventory,"show-summary":"",load:e.loadChildInventory,"tree-props":{children:"children",hasChildren:"hasChildren"},lazy:"","row-key":"inventoryId"},on:{"selection-change":e.handleSelectionChange,"row-dblclick":e.handleUpdate}},[a("el-table-column",{attrs:{align:"center",type:"selection",width:"28"}}),a("el-table-column",{attrs:{align:"center",label:"入仓流水号",prop:"inboundSerialNo",width:"120"}}),a("el-table-column",{attrs:{align:"center",label:"入仓日期",prop:"actualInboundTime",width:"80"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(e.parseTime(t.row.actualInboundTime,"{y}-{m}-{d}")))])]}}])}),a("el-table-column",{attrs:{align:"center",label:"库存标志",prop:"inventoryStatus",width:"60"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(0==t.row.inventoryStatus?"在库":1==t.row.inventoryStatus?"出库":"被打包"))])]}}])}),a("el-table-column",{attrs:{align:"center",label:"被打包至",prop:"packageIntoNo","show-overflow-tooltip":"",width:"100"}}),a("el-table-column",{attrs:{align:"center",label:"打包标志",prop:"repackingStatus","show-overflow-tooltip":"",width:"100"}}),a("el-table-column",{attrs:{align:"center",label:"仓租结算至",prop:"rentalSettlementDate",width:"80"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(e.parseTime(t.row.rentalSettlementDate,"{y}-{m}-{d}")))])]}}])}),a("el-table-column",{attrs:{align:"center",label:"客户代码",prop:"clientCode","show-overflow-tooltip":"",width:"60"}}),a("el-table-column",{attrs:{align:"center",label:"分单号",prop:"subOrderNo","show-overflow-tooltip":"",width:"200"}}),a("el-table-column",{attrs:{align:"center",label:"收货人名称",prop:"consigneeName","show-overflow-tooltip":"",width:"100"}}),a("el-table-column",{attrs:{align:"center",label:"收货人电话",prop:"consigneeTel","show-overflow-tooltip":"",width:"100"}}),a("el-table-column",{attrs:{align:"center",label:"供货商",prop:"supplier","show-overflow-tooltip":"",width:"60"}}),a("el-table-column",{attrs:{align:"center",label:"司机信息",prop:"driverInfo","show-overflow-tooltip":"",width:"120"}}),a("el-table-column",{attrs:{align:"center",label:"唛头",prop:"sqdShippingMark","show-overflow-tooltip":"",width:"80"}}),a("el-table-column",{attrs:{align:"center",label:"总货名",prop:"cargoName","show-overflow-tooltip":""}}),a("el-table-column",{attrs:{align:"center",label:"总箱数",prop:"totalBoxes"}}),a("el-table-column",{attrs:{align:"center",label:"总毛重",prop:"totalGrossWeight"}}),a("el-table-column",{attrs:{align:"center",label:"总体积",prop:"totalVolume"}}),a("el-table-column",{attrs:{align:"center",label:"已收供应商",prop:"receivedSupplier"}}),a("el-table-column",{attrs:{align:"center",label:"入仓费标准",prop:"inboundFee"}}),a("el-table-column",{attrs:{align:"center",label:"已收入仓费",prop:"receivedStorageFee"}}),a("el-table-column",{attrs:{align:"center",label:"未收卸货费",prop:"unpaidUnloadingFee"}}),a("el-table-column",{attrs:{align:"center",label:"实付卸货费",prop:"receivedUnloadingFee"}}),a("el-table-column",{attrs:{align:"center",label:"未收打包费",prop:"unpaidPackingFee"}}),a("el-table-column",{attrs:{align:"center",label:"实付打包费",prop:"receivedPackingFee"}}),a("el-table-column",{attrs:{align:"center",label:"物流代垫费",prop:"logisticsAdvanceFee"}}),a("el-table-column",{attrs:{align:"center",label:"免堆期",prop:"freeStackPeriod"}}),a("el-table-column",{attrs:{align:"center",label:"超期租金单价",prop:"overdueRentalUnitPrice"}}),a("el-table-column",{attrs:{align:"center",label:"超期租金",prop:"overdueRentalFee"}}),a("el-table-column",{attrs:{align:"center",label:"备注",prop:"notes","show-overflow-tooltip":""}}),a("el-table-column",{attrs:{align:"center","class-name":"small-padding fixed-width",label:"操作"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:inventory:edit"],expression:"['system:inventory:edit']"}],staticStyle:{"margin-right":"-8px"},attrs:{icon:"el-icon-edit",size:"mini",type:"success"},on:{click:function(a){return e.handleUpdate(t.row)}}},[e._v("修改 ")]),a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:inventory:remove"],expression:"['system:inventory:remove']"}],staticStyle:{"margin-right":"-8px"},attrs:{icon:"el-icon-delete",size:"mini",type:"danger"},on:{click:function(a){return e.handleDelete(t.row)}}},[e._v("删除 ")])]}}])})],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total>0"}],attrs:{limit:e.queryParams.pageSize,page:e.queryParams.pageNum,total:e.total},on:{"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},pagination:e.getList}})],1)],1),a("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"},{name:"dialogDragWidth",rawName:"v-dialogDragWidth"}],attrs:{"close-on-click-modal":!1,title:e.title,visible:e.open,"append-to-body":"",width:"70%"},on:{"update:visible":function(t){e.open=t},open:e.loadCargoDetail}},[a("el-form",{ref:"form",staticClass:"edit",attrs:{model:e.form,rules:e.rules,"label-width":"80px"}},[a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"流水号",prop:"inboundSerialNo"}},[a("el-input",{staticClass:"disable-form",attrs:{disabled:"",placeholder:"入仓流水号"},model:{value:e.form.inboundSerialNo,callback:function(t){e.$set(e.form,"inboundSerialNo",t)},expression:"form.inboundSerialNo"}})],1)],1),a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"客户代码",prop:"clientCode"}},[a("tree-select",{attrs:{flat:!1,multiple:!1,pass:e.form.clientCode,placeholder:"客户代码",type:"warehouseClient"},on:{returnData:function(t){return e.selectWarehouseClient(t)}}})],1)],1),a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"客户名称",prop:"clientCode"}},[a("el-input",{staticClass:"disable-form",attrs:{disabled:"",placeholder:"客户名"},model:{value:e.form.clientName,callback:function(t){e.$set(e.form,"clientName",t)},expression:"form.clientName"}})],1)],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"入仓时间",prop:"inboundDate"}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{"default-value":new Date,clearable:"",placeholder:"实际入仓日期",type:"datetime","value-format":"yyyy-MM-dd HH:mm:ss"},model:{value:e.form.actualInboundTime,callback:function(t){e.$set(e.form,"actualInboundTime",t)},expression:"form.actualInboundTime"}})],1)],1),a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"入仓费",prop:"forwarderNo"}},[a("el-row",[a("el-col",{attrs:{span:12}},[a("el-input",{staticClass:"disable-form number",attrs:{disabled:"",placeholder:"入仓费标准"},model:{value:e.form.inboundFee,callback:function(t){e.$set(e.form,"inboundFee",t)},expression:"form.inboundFee"}})],1),a("el-col",{attrs:{span:12}},[a("el-input",{staticClass:"number",attrs:{placeholder:"已收入仓费"},model:{value:e.form.receivedStorageFee,callback:function(t){e.$set(e.form,"receivedStorageFee",t)},expression:"form.receivedStorageFee"}})],1)],1)],1)],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:10}},[a("el-form-item",{attrs:{label:"分单号",prop:"subOrderNo"}},[a("el-input",{attrs:{placeholder:"分单号(最多只能输入八个字符)"},model:{value:e.form.subOrderNo,callback:function(t){e.$set(e.form,"subOrderNo",t)},expression:"form.subOrderNo"}})],1)],1),a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"收货人名称",prop:"consigneeName"}},[a("el-input",{attrs:{placeholder:"收货人名称"},model:{value:e.form.consigneeName,callback:function(t){e.$set(e.form,"consigneeName",t)},expression:"form.consigneeName"}})],1)],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"收货人电话",prop:"consigneeTel"}},[a("el-input",{attrs:{placeholder:"收货人电话"},model:{value:e.form.consigneeTel,callback:function(t){e.$set(e.form,"consigneeTel",t)},expression:"form.consigneeTel"}})],1)],1),a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"卸货费",prop:"supplier"}},[a("el-row",[a("el-col",{attrs:{span:12}},[a("el-input",{staticClass:"number",attrs:{placeholder:"未收卸货费"},model:{value:e.form.unpaidUnloadingFee,callback:function(t){e.$set(e.form,"unpaidUnloadingFee",t)},expression:"form.unpaidUnloadingFee"}})],1),a("el-col",{attrs:{span:12}},[a("el-input",{staticClass:"number",attrs:{placeholder:"实付卸货费"},model:{value:e.form.receivedUnloadingFee,callback:function(t){e.$set(e.form,"receivedUnloadingFee",t)},expression:"form.receivedUnloadingFee"}})],1)],1)],1)],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:10}},[a("el-form-item",{attrs:{label:"货物描述",prop:"cargoName"}},[a("el-input",{attrs:{placeholder:"总货名"},model:{value:e.form.cargoName,callback:function(t){e.$set(e.form,"cargoName",t)},expression:"form.cargoName"}})],1)],1),a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"货物性质",prop:"cargoNature"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择货物性质"},model:{value:e.form.cargoNature,callback:function(t){e.$set(e.form,"cargoNature",t)},expression:"form.cargoNature"}},[a("el-option",{attrs:{label:"普货",value:"普货"}}),a("el-option",{attrs:{label:"大件",value:"大件"}}),a("el-option",{attrs:{label:"鲜活",value:"鲜活"}}),a("el-option",{attrs:{label:"危品",value:"危品"}}),a("el-option",{attrs:{label:"冷冻",value:"冷冻"}}),a("el-option",{attrs:{label:"标记",value:"标记"}})],1)],1)],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"记录方式",prop:"recordType"}},[a("el-select",{attrs:{placeholder:"请选择记录方式"},on:{change:e.selectInboundFee},model:{value:e.form.recordType,callback:function(t){e.$set(e.form,"recordType",t)},expression:"form.recordType"}},[a("el-option",{attrs:{label:"标准",value:"标准"}}),a("el-option",{attrs:{label:"精确",value:"精确"}}),a("el-option",{attrs:{label:"快递",value:"快递"}})],1)],1)],1),a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"打包费"}},[a("el-row",[a("el-col",{attrs:{span:12}},[a("el-input",{staticClass:"number",class:1==e.form.includesPackingFee?"disable-form":"",attrs:{disabled:1==e.form.includesPackingFee,placeholder:"未收打包费"},model:{value:e.form.unpaidPackingFee,callback:function(t){e.$set(e.form,"unpaidPackingFee",t)},expression:"form.unpaidPackingFee"}})],1),a("el-col",{attrs:{span:12}},[a("el-input",{staticClass:"number",attrs:{placeholder:"实付打包费"},model:{value:e.form.receivedPackingFee,callback:function(t){e.$set(e.form,"receivedPackingFee",t)},expression:"form.receivedPackingFee"}})],1)],1)],1)],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:10}},[a("el-form-item",{attrs:{label:"送货人信息",prop:"driverInfo"}},[a("el-input",{attrs:{placeholder:"内容"},model:{value:e.form.driverInfo,callback:function(t){e.$set(e.form,"driverInfo",t)},expression:"form.driverInfo"}})],1)],1),a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"入仓方式",prop:"inboundType"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择包装类型"},model:{value:e.form.inboundType,callback:function(t){e.$set(e.form,"inboundType",t)},expression:"form.inboundType"}},[a("el-option",{attrs:{label:"入仓",value:"入仓"}}),a("el-option",{attrs:{label:"外置",value:"外置"}}),a("el-option",{attrs:{label:"对装",value:"对装"}}),a("el-option",{attrs:{label:"自提",value:"自提"}})],1)],1)],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"库存标志",prop:"inventoryStatus"}},[a("el-select",{staticClass:"disable-form",attrs:{disabled:"",placeholder:"请选择库存标志"},model:{value:e.form.inventoryStatus,callback:function(t){e.$set(e.form,"inventoryStatus",t)},expression:"form.inventoryStatus"}},[a("el-option",{attrs:{label:"在仓",value:"0"}}),a("el-option",{attrs:{label:"出库",value:"1"}}),a("el-option",{attrs:{label:"被打包",value:"-1"}})],1)],1)],1),a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"物流代垫费",prop:"logisticsAdvanceFee"}},[a("el-input",{staticClass:"number",attrs:{placeholder:"物流代垫费"},model:{value:e.form.logisticsAdvanceFee,callback:function(t){e.$set(e.form,"logisticsAdvanceFee",t)},expression:"form.logisticsAdvanceFee"}})],1)],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:10}},[a("el-form-item",{attrs:{label:"入仓备注",prop:"notes"}},[a("el-input",{attrs:{autosize:{minRows:1},maxlength:"150",placeholder:"内容","show-word-limit":"",type:"textarea"},model:{value:e.form.notes,callback:function(t){e.$set(e.form,"notes",t)},expression:"form.notes"}})],1)],1),a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"被打包至"}},[a("el-input",{staticClass:"disable-form",attrs:{disabled:""},model:{value:e.form.packageIntoNo,callback:function(t){e.$set(e.form,"packageIntoNo",t)},expression:"form.packageIntoNo"}})],1)],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{staticClass:"disable-form",attrs:{disabled:"",label:"父级流水号"}},[a("el-input",{staticClass:"disable-form",attrs:{disabled:""},model:{value:e.form.repackedInto,callback:function(t){e.$set(e.form,"repackedInto",t)},expression:"form.repackedInto"}})],1)],1),a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"已收供应商",prop:"receivedSupplier"}},[a("el-input",{class:1==e.form.includesUnloadingFee?"disable-form":"",attrs:{disabled:1==e.form.includesUnloadingFee,placeholder:"已收供应商总额"},model:{value:e.form.receivedSupplier,callback:function(t){e.$set(e.form,"receivedSupplier",t)},expression:"form.receivedSupplier"}})],1)],1)],1),a("el-row",[a("el-col",[e._v(" 货物明细："),a("span",{staticStyle:{color:"#b7bbc2",margin:"0"}},[e._v("(填写毛重/体积小计时如果单件毛重/长宽高不为0则小计不可更改,系统自动计算)")]),a("el-table",{staticStyle:{width:"100%"},attrs:{data:e.form.rsCargoDetailsList,"summary-method":e.getSummaries,border:"","show-summary":"",stripe:""},on:{"row-dblclick":e.handleUpdateCargoDetail}},[a("el-table-column",{attrs:{align:"center",type:"selection",width:"30"}}),a("el-table-column",{attrs:{align:"center",label:"唛头",prop:"shippingMark",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-input",{model:{value:t.row.shippingMark,callback:function(a){e.$set(t.row,"shippingMark",a)},expression:"scope.row.shippingMark"}})]}}])}),a("el-table-column",{attrs:{align:"center",label:"货名",prop:"itemName",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-input",{model:{value:t.row.itemName,callback:function(a){e.$set(t.row,"itemName",a)},expression:"scope.row.itemName"}})]}}])}),a("el-table-column",{attrs:{align:"center",label:"英文货名",prop:"itemName",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-input",{model:{value:t.row.itemEnName,callback:function(a){e.$set(t.row,"itemEnName",a)},expression:"scope.row.itemEnName"}})]}}])}),a("el-table-column",{staticClass:"no-spinner",attrs:{align:"right",label:"件数",prop:"boxCount"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-row",[a("el-col",{attrs:{span:12}},[a("el-input",{staticClass:"number",on:{input:function(a){return e.countCargomeasure(t.row,"")}},model:{value:t.row.boxCount,callback:function(a){e.$set(t.row,"boxCount",a)},expression:"scope.row.boxCount"}})],1),a("el-col",{attrs:{span:12}},[a("el-select",{attrs:{placeholder:"请选择包装类型"},model:{value:t.row.packageType,callback:function(a){e.$set(t.row,"packageType",a)},expression:"scope.row.packageType"}},[a("el-option",{attrs:{label:"纸箱",value:"纸箱"}}),a("el-option",{attrs:{label:"木箱",value:"木箱"}}),a("el-option",{attrs:{label:"托盘",value:"托盘"}}),a("el-option",{attrs:{label:"吨袋",value:"吨袋"}})],1)],1)],1)]}}])}),a("el-table-column",{staticClass:"no-spinner",attrs:{align:"right",label:"单箱件数",prop:"boxItemCount",width:"60"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-input",{staticClass:"number",on:{blur:function(a){return e.parseInput(t.row,"boxItemCount")},input:function(a){return e.countCargomeasure(t.row,"boxItemCount")}},model:{value:t.row.boxItemCount,callback:function(a){e.$set(t.row,"boxItemCount",a)},expression:"scope.row.boxItemCount"}})]}}])}),a("el-table-column",{attrs:{align:"right",label:"单件毛重(KGS)",prop:"singlePieceWeight"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-input",{staticClass:"number",on:{blur:function(a){return e.parseInput(t.row,"singlePieceWeight")},input:function(a){return e.countCargomeasure(t.row,"singlePieceWeight")}},model:{value:t.row.singlePieceWeightFormatter,callback:function(a){e.$set(t.row,"singlePieceWeightFormatter",a)},expression:"scope.row.singlePieceWeightFormatter"}})]}}])}),a("el-table-column",{staticClass:"no-spinner",attrs:{align:"right",label:"单件长(cm)",prop:"unitLength",width:"70"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-input",{staticClass:"number",on:{blur:function(a){return e.parseInput(t.row,"unitLength")},input:function(a){return e.countCargomeasure(t.row,"unitLength")}},model:{value:t.row.unitLengthFormatter,callback:function(a){e.$set(t.row,"unitLengthFormatter",a)},expression:"scope.row.unitLengthFormatter"}})]}}])}),a("el-table-column",{staticClass:"no-spinner",attrs:{align:"right",label:"单件宽(cm)",prop:"unitWidth",width:"70"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-input",{staticClass:"number",on:{blur:function(a){return e.parseInput(t.row,"unitWidth")},input:function(a){return e.countCargomeasure(t.row,"unitWidth")}},model:{value:t.row.unitWidthFormatter,callback:function(a){e.$set(t.row,"unitWidthFormatter",a)},expression:"scope.row.unitWidthFormatter"}})]}}])}),a("el-table-column",{staticClass:"no-spinner",attrs:{align:"right",label:"单件高(cm)",prop:"unitHeight",width:"70"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-input",{staticClass:"number",on:{blur:function(a){return e.parseInput(t.row,"unitHeight")},input:function(a){return e.countCargomeasure(t.row,"unitHeight")}},model:{value:t.row.unitHeightFormatter,callback:function(a){e.$set(t.row,"unitHeightFormatter",a)},expression:"scope.row.unitHeightFormatter"}})]}}])}),a("el-table-column",{staticClass:"no-spinner",attrs:{align:"right",label:"单件体积(CBM)",prop:"singlePieceVolume"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-input",{staticClass:"number",on:{blur:function(a){return e.parseInput(t.row,"singlePieceVolume")},input:function(a){return e.countCargomeasure(t.row,"singlePieceVolume")}},model:{value:t.row.singlePieceVolumeFormatter,callback:function(a){e.$set(t.row,"singlePieceVolumeFormatter",a)},expression:"scope.row.singlePieceVolumeFormatter"}})]}}])}),a("el-table-column",{staticClass:"no-spinner",attrs:{align:"right",label:"件数小计",prop:"subtotalItemCount",width:"60"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-input",{staticClass:"number",on:{blur:function(a){return e.parseInput(t.row,"subtotalItemCount")},input:function(a){return e.countCargomeasure(t.row,"subtotalItemCount")}},model:{value:t.row.subtotalItemCount,callback:function(a){e.$set(t.row,"subtotalItemCount",a)},expression:"scope.row.subtotalItemCount"}})]}}])}),a("el-table-column",{staticClass:"no-spinner",attrs:{align:"right",label:"毛重小计(KGS)",prop:"unitGrossWeight"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-input",{staticClass:"number",on:{blur:function(a){return e.parseInput(t.row,"unitGrossWeight")},input:function(a){return e.countCargomeasure(t.row,"unitGrossWeight")}},model:{value:t.row.unitGrossWeightFormatter,callback:function(a){e.$set(t.row,"unitGrossWeightFormatter",a)},expression:"scope.row.unitGrossWeightFormatter"}})]}}])}),a("el-table-column",{staticClass:"no-spinner",attrs:{align:"right",label:"体积小计(CBM)",prop:"unitVolume"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-input",{staticClass:"number",on:{blur:function(a){return e.parseInput(t.row,"unitVolume")},input:function(a){return e.countCargomeasure(t.row,"unitVolume")}},model:{value:t.row.unitVolumeFormatter,callback:function(a){e.$set(t.row,"unitVolumeFormatter",a)},expression:"scope.row.unitVolumeFormatter"}})]}}])}),a("el-table-column",{attrs:{align:"right",label:"破损标志",prop:"damageStatus",width:"60"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-input",{model:{value:t.row.damageStatus,callback:function(a){e.$set(t.row,"damageStatus",a)},expression:"scope.row.damageStatus"}})]}}])}),a("el-table-column",{attrs:{align:"center","class-name":"small-padding fixed-width",label:"操作",width:"48px"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("div",{staticStyle:{height:"15px",padding:"0",margin:"0"}},[a("el-button",{staticStyle:{display:"flex"},attrs:{icon:"el-icon-delete",type:"danger"},on:{click:function(a){return e.deleteCargoDetail(t.row)}}},[e._v("删除 ")])],1)]}}])})],1)],1),a("el-button",{staticStyle:{padding:"0"},attrs:{type:"text"},on:{click:e.addCargoDetail}},[e._v("[＋] ")])],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("div",{staticStyle:{float:"left"}},[a("span",[e._v("仓管："+e._s(e.form.sqdInboundHandler))])]),a("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("保 存")]),a("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.printInboundBill("旧模板")}}},[e._v("打印旧版入仓单")]),a("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.printInboundBill("新模板")}}},[e._v("打印新版入仓单")]),a("el-button",{on:{click:e.cancel}},[e._v("关 闭")])],1)],1),a("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"},{name:"dialogDragWidth",rawName:"v-dialogDragWidth"}],attrs:{"close-on-click-modal":!1,title:e.title,visible:e.openPkg,"append-to-body":"",width:"70%"},on:{"update:visible":function(t){e.openPkg=t},open:e.loadPkgDetail}},[a("el-form",{ref:"form",staticClass:"edit",attrs:{model:e.form,rules:e.rules,"label-width":"80px"}},[a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"流水号",prop:"inboundSerialNo"}},[a("el-input",{staticClass:"disable-form",attrs:{disabled:"",placeholder:"入仓流水号"},model:{value:e.form.inboundSerialNo,callback:function(t){e.$set(e.form,"inboundSerialNo",t)},expression:"form.inboundSerialNo"}})],1)],1),a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"客户代码",prop:"clientCode"}},[a("tree-select",{attrs:{flat:!1,multiple:!1,pass:e.form.clientCode,placeholder:"客户代码",type:"warehouseClient"},on:{returnData:function(t){return e.selectWarehouseClient(t)}}})],1)],1),a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"客户名称",prop:"clientCode"}},[a("el-input",{staticClass:"disable-form",attrs:{disabled:"",placeholder:"客户名"},model:{value:e.form.clientName,callback:function(t){e.$set(e.form,"clientName",t)},expression:"form.clientName"}})],1)],1),a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"分单号",prop:"subOrderNo"}},[a("el-input",{class:null!==this.form.inventoryId?"disable-form":"",attrs:{disabled:null!==this.form.inventoryId,placeholder:"分单号(最多只能输入八个字符)"},model:{value:e.form.subOrderNo,callback:function(t){e.$set(e.form,"subOrderNo",t)},expression:"form.subOrderNo"}})],1)],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"生成时间",prop:"inboundDate"}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{"default-value":new Date,clearable:"",placeholder:"实际入仓日期",type:"datetime","value-format":"yyyy-MM-dd HH:mm:ss"},model:{value:e.form.actualInboundTime,callback:function(t){e.$set(e.form,"actualInboundTime",t)},expression:"form.actualInboundTime"}})],1)],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:10}},[a("el-form-item",{attrs:{label:"入仓备注",prop:"notes"}},[a("el-input",{attrs:{autosize:{minRows:1},maxlength:"150",placeholder:"内容","show-word-limit":"",type:"textarea"},model:{value:e.form.notes,callback:function(t){e.$set(e.form,"notes",t)},expression:"form.notes"}})],1)],1),a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"打包标志",prop:"inventoryStatus"}},[a("el-input",{staticClass:"disable-form",attrs:{disabled:""},model:{value:e.form.repackingStatus,callback:function(t){e.$set(e.form,"repackingStatus",t)},expression:"form.repackingStatus"}})],1)],1),a("el-col",{attrs:{span:5}},[a("el-button",{attrs:{type:"primary"},on:{click:e.pkgFinish}},[e._v(e._s("打包完"===e.form.repackingStatus?"取消打包":"打包完成")+" ")])],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"库存标志",prop:"inventoryStatus"}},[a("el-select",{staticClass:"disable-form",attrs:{disabled:"",placeholder:"请选择库存标志"},model:{value:e.form.inventoryStatus,callback:function(t){e.$set(e.form,"inventoryStatus",t)},expression:"form.inventoryStatus"}},[a("el-option",{attrs:{label:"在仓",value:"0"}}),a("el-option",{attrs:{label:"出库",value:"1"}}),a("el-option",{attrs:{label:"被打包",value:"-1"}})],1)],1)],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"打包箱重量",prop:"receivedSupplier"}},[a("el-input",{attrs:{placeholder:"打包箱重量"},model:{value:e.form.totalGrossWeight,callback:function(t){e.$set(e.form,"totalGrossWeight",t)},expression:"form.totalGrossWeight"}})],1)],1),a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"打包箱体积",prop:"receivedSupplier"}},[a("el-input",{attrs:{placeholder:"打包箱体积"},model:{value:e.form.totalVolume,callback:function(t){e.$set(e.form,"totalVolume",t)},expression:"form.totalVolume"}})],1)],1)],1),a("el-row",[a("el-col",[e._v(" 包装箱明细： "),a("el-table",{staticStyle:{width:"100%"},attrs:{data:e.pkgDetailsList,"summary-method":e.getSummaries,border:"","show-summary":"",stripe:""},on:{"selection-change":e.handlePkgSelectionChange,"row-dblclick":e.handleUpdateCargoDetail}},[a("el-table-column",{attrs:{align:"center",type:"selection",width:"30"}}),a("el-table-column",{attrs:{align:"center",label:"入仓流水号",prop:"inboundSerialNo",width:"100"}}),a("el-table-column",{attrs:{align:"center",label:"入仓日期",prop:"actualInboundTime",width:"80"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(e.parseTime(t.row.actualInboundTime,"{y}-{m}-{d}")))])]}}])}),a("el-table-column",{attrs:{align:"center",label:"库存标志",prop:"inventoryStatus",width:"60"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(0==t.row.inventoryStatus?"在库":1==t.row.inventoryStatus?"出库":"被打包"))])]}}])}),a("el-table-column",{attrs:{align:"center",label:"被打包至",prop:"packageIntoNo","show-overflow-tooltip":"",width:"100"}}),a("el-table-column",{attrs:{align:"center",label:"打包标志",prop:"repackedInto","show-overflow-tooltip":"",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(0==t.row.packageRecord?"-":"打包箱"))])]}}])}),a("el-table-column",{attrs:{align:"center",label:"仓租结算至",prop:"rentalSettlementDate",width:"80"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(e.parseTime(t.row.rentalSettlementDate,"{y}-{m}-{d}")))])]}}])}),a("el-table-column",{attrs:{align:"center",label:"客户代码",prop:"clientCode","show-overflow-tooltip":"",width:"60"}}),a("el-table-column",{attrs:{align:"center",label:"分单号",prop:"subOrderNo","show-overflow-tooltip":"",width:"200"}}),a("el-table-column",{attrs:{align:"center",label:"收货人名称",prop:"consigneeName","show-overflow-tooltip":"",width:"100"}}),a("el-table-column",{attrs:{align:"center",label:"收货人电话",prop:"consigneeTel","show-overflow-tooltip":"",width:"100"}}),a("el-table-column",{attrs:{align:"center",label:"供货商",prop:"supplier","show-overflow-tooltip":"",width:"60"}}),a("el-table-column",{attrs:{align:"center",label:"司机信息",prop:"driverInfo","show-overflow-tooltip":"",width:"120"}}),a("el-table-column",{attrs:{align:"center",label:"唛头",prop:"sqdShippingMark","show-overflow-tooltip":"",width:"80"}}),a("el-table-column",{attrs:{align:"center",label:"货物性质",prop:"cargoNature","show-overflow-tooltip":""}}),a("el-table-column",{attrs:{align:"center",label:"总货名",prop:"cargoName","show-overflow-tooltip":""}}),a("el-table-column",{attrs:{align:"center",label:"总箱数",prop:"totalBoxes"}}),a("el-table-column",{attrs:{align:"center",label:"总毛重",prop:"totalGrossWeight"}}),a("el-table-column",{attrs:{align:"center",label:"总体积",prop:"totalVolume"}}),a("el-table-column",{attrs:{align:"center",label:"已收供应商",prop:"receivedSupplier"}}),a("el-table-column",{attrs:{align:"center",label:"入仓费标准",prop:"inboundFee"}}),a("el-table-column",{attrs:{align:"center",label:"已收入仓费",prop:"receivedStorageFee"}}),a("el-table-column",{attrs:{align:"center",label:"未收卸货费",prop:"unpaidUnloadingFee"}}),a("el-table-column",{attrs:{align:"center",label:"实付卸货费",prop:"receivedUnloadingFee"}}),a("el-table-column",{attrs:{align:"center",label:"未收打包费",prop:"unpaidPackingFee"}}),a("el-table-column",{attrs:{align:"center",label:"实付打包费",prop:"receivedPackingFee"}}),a("el-table-column",{attrs:{align:"center",label:"物流代垫费",prop:"logisticsAdvanceFee"}}),a("el-table-column",{attrs:{align:"center",label:"免堆期",prop:"freeStackPeriod"}}),a("el-table-column",{attrs:{align:"center",label:"超期租金单价",prop:"overdueRentalUnitPrice"}}),a("el-table-column",{attrs:{align:"center",label:"超期租金",prop:"overdueRentalFee"}}),a("el-table-column",{attrs:{align:"center",label:"备注",prop:"notes","show-overflow-tooltip":""}})],1)],1),a("el-button",{staticStyle:{padding:"0"},attrs:{type:"text"},on:{click:e.addCargoDetail}},[e._v("[＋] ")])],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("div",{staticStyle:{float:"left"}},[a("span",[e._v("仓管："+e._s(e.form.sqdInboundHandler))])]),a("el-button",{class:"打包完"===e.form.repackingStatus?"disable-form":"",attrs:{disabled:"打包完"===e.form.repackingStatus,type:"danger"},on:{click:e.pkgCancel}},[e._v("移出打包箱 ")]),a("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.submitForm("pkg")}}},[e._v("保 存")]),a("el-button",{on:{click:function(t){e.openPkg=!1}}},[e._v("关 闭")])],1)],1),a("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"},{name:"dialogDragWidth",rawName:"v-dialogDragWidth"}],attrs:{"close-on-click-modal":!1,title:e.title,visible:e.openPkgTo,"append-to-body":"",width:"30%"},on:{"update:visible":function(t){e.openPkgTo=t},open:e.loadPkgToList}},[a("el-select",{staticClass:"field-select",attrs:{filterable:"",placeholder:"请选择搜索字段"},model:{value:e.form.packageTo,callback:function(t){e.$set(e.form,"packageTo",t)},expression:"form.packageTo"}},e._l(e.pkgList,(function(e){return a("el-option",{key:e.inventoryId,attrs:{label:e.subOrderNo,value:e.inventoryId}})})),1),a("el-button",{attrs:{type:"primary"},on:{click:e.packingTo}},[e._v("保 存")])],1),a("print-preview",{ref:"preView"}),a("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"},{name:"dialogDragWidth",rawName:"v-dialogDragWidth"}],attrs:{"close-on-click-modal":!1,"modal-append-to-body":!1,title:e.upload.title,visible:e.upload.open,"append-to-body":"",width:"400px"},on:{"update:visible":function(t){return e.$set(e.upload,"open",t)}}},[a("el-upload",{ref:"upload",attrs:{action:e.upload.url+"?updateSupport="+e.upload.updateSupport,"auto-upload":!1,disabled:e.upload.isUploading,headers:e.upload.headers,limit:1,"on-progress":e.handleFileUploadProgress,"on-success":e.handleFileSuccess,accept:".xlsx, .xls",drag:""}},[a("i",{staticClass:"el-icon-upload"}),a("div",{staticClass:"el-upload__text"},[e._v("将文件拖到此处，或"),a("em",[e._v("点击上传")])]),a("div",{staticClass:"el-upload__tip text-center",attrs:{slot:"tip"},slot:"tip"},[a("div",{staticClass:"el-upload__tip",attrs:{slot:"tip"},slot:"tip"},[a("el-checkbox",{model:{value:e.upload.updateSupport,callback:function(t){e.$set(e.upload,"updateSupport",t)},expression:"upload.updateSupport"}}),e._v(" 是否更新已经存在的用户数据 ")],1),a("span",[e._v("仅允许导入xls、xlsx格式文件。")])])]),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:e.submitFileForm}},[e._v("确 定")]),a("el-button",{on:{click:function(t){e.upload.open=!1}}},[e._v("取 消")])],1)],1)],1)},l=[],r=a("5530"),o=a("2909"),s=a("53ca"),d=(a("d9e2"),a("2ca0"),a("caad"),a("2532"),a("d3b7"),a("159b"),a("14d9"),a("4d90"),a("ac1f"),a("5319"),a("d81d"),a("a9e3"),a("4de4"),a("13d5"),a("b680"),a("e9c4"),a("7db0"),a("a434"),a("c740"),a("b0c0"),a("a15b"),a("5fb3")),c=a("b635"),u=a("9d56"),g={panels:[{index:0,name:1,paperType:"A4",height:297,width:210,paperHeader:0,paperFooter:634.5,printElements:[{options:{left:103.5,top:85.5,height:28.5,width:97,title:"文本",field:"clientCode",coordinateSync:!1,widthHeightSync:!1,hideTitle:!0,qrCodeLevel:0,fontSize:20,textAlign:"center",fontWeight:"bold",testData:"KNL",textContentVerticalAlign:"middle"},printElementType:{title:"文本",type:"text"}},{options:{left:111,top:117,height:9.75,width:86,title:"文本",field:"subOrderNo",coordinateSync:!1,widthHeightSync:!1,hideTitle:!0,qrCodeLevel:0,testData:"name"},printElementType:{title:"文本",type:"text"}},{options:{left:241.5,top:118.5,height:9.75,width:92,title:"文本",field:"actualInboundTime",coordinateSync:!1,widthHeightSync:!1,hideTitle:!0,qrCodeLevel:0},printElementType:{title:"文本",type:"text"}},{options:{left:90,top:168,height:9.75,width:34.5,title:"文本",right:77.25,bottom:177.75,vCenter:60,hCenter:172.875,field:"sqdShippingMark",coordinateSync:!1,widthHeightSync:!1,hideTitle:!0,qrCodeLevel:0},printElementType:{title:"文本",type:"text"}},{options:{left:127.5,top:166.5,height:9.75,width:85.5,title:"文本",right:115.74609375,bottom:177.99609375,vCenter:98.49609375,hCenter:173.12109375,field:"cargoName",coordinateSync:!1,widthHeightSync:!1,hideTitle:!0,qrCodeLevel:0},printElementType:{title:"文本",type:"text"}},{options:{left:217.5,top:166.5,height:9.75,width:27,title:"文本",right:197.25,bottom:176.25,vCenter:183.75,hCenter:171.375,field:"totalBoxes",coordinateSync:!1,widthHeightSync:!1,hideTitle:!0,qrCodeLevel:0},printElementType:{title:"文本",type:"text"}},{options:{left:246,top:166.5,height:9.75,width:27,title:"文本",right:227.2448272705078,bottom:175.4973907470703,vCenter:213.7448272705078,hCenter:170.6223907470703,field:"totalGrossWeight",coordinateSync:!1,widthHeightSync:!1,hideTitle:!0,qrCodeLevel:0},printElementType:{title:"文本",type:"text"}},{options:{left:280.5,top:166.5,height:9.75,width:27,title:"文本",right:260.4895935058594,bottom:176.49739837646484,vCenter:246.98959350585938,hCenter:171.62239837646484,field:"totalVolume",coordinateSync:!1,widthHeightSync:!1,hideTitle:!0,qrCodeLevel:0},printElementType:{title:"文本",type:"text"}},{options:{left:313.5,top:166.5,height:9.75,width:27,title:"文本",right:292.7395935058594,bottom:176.49739837646484,vCenter:279.2395935058594,hCenter:171.62239837646484,field:"inboundNotes",coordinateSync:!1,widthHeightSync:!1,hideTitle:!0,qrCodeLevel:0},printElementType:{title:"文本",type:"text"}},{options:{left:403.5,top:87,height:13.5,width:51,title:"文本",field:"clientCode",coordinateSync:!1,widthHeightSync:!1,hideTitle:!0,qrCodeLevel:0,right:438.99742126464844,bottom:116.49217987060547,vCenter:397.74742126464844,hCenter:102.24217987060547},printElementType:{title:"文本",type:"text"}},{options:{left:403.5,top:102,height:9.75,width:49.5,title:"文本",field:"subOrderNo",coordinateSync:!1,widthHeightSync:!1,hideTitle:!0,qrCodeLevel:0,right:441.7500305175781,bottom:112.73959350585938,vCenter:398.7500305175781,hCenter:107.86459350585938},printElementType:{title:"文本",type:"text"}},{options:{left:477,top:100.5,height:9.75,width:102,title:"文本",field:"consigneeName",coordinateSync:!1,widthHeightSync:!1,hideTitle:!0,qrCodeLevel:0,right:538.4895935058594,bottom:111.98959350585938,vCenter:492.4895935058594,hCenter:107.11459350585938},printElementType:{title:"文本",type:"text"}},{options:{left:477,top:118.5,height:9.75,width:100.5,title:"文本",field:"actualInboundTime",coordinateSync:!1,widthHeightSync:!1,hideTitle:!0,qrCodeLevel:0,right:540.7395935058594,bottom:126.25000762939453,vCenter:494.7395935058594,hCenter:121.37500762939453},printElementType:{title:"文本",type:"text"}},{options:{left:406.5,top:120,height:9.75,width:48,title:"文本",field:"supplier",coordinateSync:!1,widthHeightSync:!1,hideTitle:!0,qrCodeLevel:0,right:452.9895935058594,bottom:129.25000762939453,vCenter:406.9895935058594,hCenter:124.37500762939453},printElementType:{title:"文本",type:"text"}},{options:{left:406.5,top:145.5,height:9.75,width:130.5,title:"文本",right:447.2395935058594,bottom:150.9974072277546,vCenter:404.4895935058594,hCenter:146.1224072277546,field:"cargoName",coordinateSync:!1,widthHeightSync:!1,hideTitle:!0,qrCodeLevel:0},printElementType:{title:"文本",type:"text"}},{options:{left:406.5,top:166.5,height:9.75,width:157.5,title:"文本",right:564,bottom:186.75,vCenter:485.25,hCenter:181.875,field:"inboundNotes",coordinateSync:!1,widthHeightSync:!1,hideTitle:!0,qrCodeLevel:0},printElementType:{title:"文本",type:"text"}},{options:{left:73.5,top:276,height:9.75,width:270,title:"文本",field:"operationRecord",coordinateSync:!1,widthHeightSync:!1,hideTitle:!0,qrCodeLevel:0},printElementType:{title:"文本",type:"text"}},{options:{left:540.75,top:146.25,height:9.75,width:27,title:"文本",right:197.25,bottom:176.25,vCenter:183.75,hCenter:171.375,field:"totalBoxes",coordinateSync:!1,widthHeightSync:!1,hideTitle:!0,qrCodeLevel:0},printElementType:{title:"文本",type:"text"}},{options:{left:406.5,top:181.5,height:9.75,width:120,field:"inboundSerialNo",coordinateSync:!1,widthHeightSync:!1,hideTitle:!0,qrCodeLevel:0,right:526.5,bottom:201.75,vCenter:466.5,hCenter:196.875},printElementType:{title:"文本",type:"text"}},{options:{left:406.5,top:216,height:9.75,width:120,field:"expressNo",coordinateSync:!1,widthHeightSync:!1,hideTitle:!0,qrCodeLevel:0,right:526.5,bottom:235.5,vCenter:466.5,hCenter:230.625},printElementType:{title:"文本",type:"text"}},{options:{left:75,top:251.25,height:9.75,width:120,field:"inboundSerialNo",coordinateSync:!1,widthHeightSync:!1,hideTitle:!0,qrCodeLevel:0,right:526.5,bottom:201.75,vCenter:466.5,hCenter:196.875},printElementType:{title:"文本",type:"text"}},{options:{left:208.5,top:250.5,height:9.75,width:102,title:"文本",field:"consigneeName",coordinateSync:!1,widthHeightSync:!1,hideTitle:!0,qrCodeLevel:0,right:307.5,bottom:245.25,vCenter:256.5,hCenter:240.375},printElementType:{title:"文本",type:"text"}},{options:{left:73.5,top:315,height:9.75,width:120,field:"expressNo",coordinateSync:!1,widthHeightSync:!1,hideTitle:!0,qrCodeLevel:0,right:195.99609375,bottom:309.24609375,vCenter:135.99609375,hCenter:304.37109375},printElementType:{title:"文本",type:"text"}}],paperNumberLeft:540,paperNumberTop:21,paperNumberDisabled:!0,panelPageRule:"none",watermarkOptions:{content:"",fillStyle:"rgba(184, 184, 184, 0.3)",fontSize:"14px",rotate:25,width:200,height:200,timestamp:!1,format:"YYYY-MM-DD HH:mm"}}]},p=a("f870"),m=a("b782"),f=a("72f9"),h=a.n(f),b=(a("0062"),a("c1df")),y=a.n(b),v=(a("4360"),a("fba1")),w=a("cd43"),k=(a("58cf"),{inventoryId:{name:"库存ID",display:"text",aggregated:!1,align:"left",width:"80"},inventoryStatus:{name:"库存状态",display:"text",aggregated:!1,align:"left",width:"100"},inboundSerialNo:{name:"入仓流水号",display:"text",aggregated:!1,align:"left",width:"120"},inboundSerialSplit:{name:"入仓拆分号",display:"text",aggregated:!1,align:"left",width:"120"},outboundNo:{name:"出仓单号",display:"text",aggregated:!1,align:"left",width:"120"},subOrderNo:{name:"分单号",display:"text",aggregated:!1,align:"left",width:"120"},forwarderNo:{name:"货代单号",display:"text",aggregated:!1,align:"left",width:"120"},rentalSettlementDate:{name:"计租日期",display:"date",aggregated:!1,align:"left",width:"120"},outboundDate:{name:"出仓日期",display:"date",aggregated:!1,align:"left",width:"120"},clientCode:{name:"客户代码",display:"text",aggregated:!1,align:"left",width:"120"},supplier:{name:"供货商",display:"text",aggregated:!1,align:"left",width:"120"},driverInfo:{name:"送货司机",display:"text",aggregated:!1,align:"left",width:"150"},sqdShippingMark:{name:"唛头",display:"text",aggregated:!1,align:"left",width:"150"},cargoName:{name:"货物描述",display:"text",aggregated:!1,align:"left",width:"150"},totalBoxes:{name:"总箱数",display:"number",aggregated:!0,align:"right",width:"100"},packageType:{name:"包装类型",display:"text",aggregated:!1,align:"left",width:"100"},totalGrossWeight:{name:"总毛重",display:"number",aggregated:!0,align:"right",width:"100"},totalVolume:{name:"总体积",display:"number",aggregated:!0,align:"right",width:"100"},damageStatus:{name:"破损状态",display:"text",aggregated:!1,align:"left",width:"100"},storageLocation1:{name:"存放位置1",display:"text",aggregated:!1,align:"left",width:"100"},storageLocation2:{name:"存放位置2",display:"text",aggregated:!1,align:"left",width:"100"},storageLocation3:{name:"存放位置3",display:"text",aggregated:!1,align:"left",width:"100"},receivedStorageFee:{name:"已收入仓费",display:"number",aggregated:!0,align:"right",width:"120"},unpaidUnloadingFee:{name:"未收卸货费",display:"number",aggregated:!0,align:"right",width:"120"},logisticsAdvanceFee:{name:"物流代垫费",display:"number",aggregated:!0,align:"right",width:"120"},rentalBalanceFee:{name:"租金平衡费",display:"number",aggregated:!0,align:"right",width:"120"},freeStackPeriod:{name:"免堆期",display:"number",aggregated:!1,align:"right",width:"100"},overdueRentalUnitPrice:{name:"超期租金单价",display:"number",aggregated:!0,align:"right",width:"120"},overdueRentalFee:{name:"超期租金",display:"number",aggregated:!0,align:"right",width:"120"},notes:{name:"备注",display:"text",aggregated:!1,align:"left",width:"150"},warehouseCode:{name:"仓库代码",display:"text",aggregated:!1,align:"left",width:"100"},recordType:{name:"记录方式",display:"text",aggregated:!1,align:"left",width:"100"},inboundType:{name:"入仓方式",display:"text",aggregated:!1,align:"left",width:"100"},cargoNature:{name:"货物性质",display:"text",aggregated:!1,align:"left",width:"100"},createdAt:{name:"创建时间",display:"date",aggregated:!1,align:"left",width:"120"},preOutboundFlag:{name:"预出库标记",display:"text",aggregated:!1,align:"left",width:"100"},outboundRequestFlag:{name:"出仓申请标记",display:"text",aggregated:!1,align:"left",width:"120"},sqdPlannedOutboundDate:{name:"计划出仓日期",display:"date",aggregated:!1,align:"left",width:"120"},confirmInboundRequestFlag:{name:"入仓申请标记",display:"text",aggregated:!1,align:"left",width:"120"},confirmOutboundRequestFlag:{name:"出仓申请确认",display:"text",aggregated:!1,align:"left",width:"120"},sqdInboundHandler:{name:"入仓申请人",display:"text",aggregated:!1,align:"left",width:"100"},partialOutboundFlag:{name:"部分出库标记",display:"boolean",aggregated:!1,align:"center",width:"100"},outboundRecordId:{name:"出仓记录ID",display:"text",aggregated:!1,align:"left",width:"120"},actualInboundTime:{name:"实际入仓时间",display:"date",aggregated:!1,align:"left",width:"120"},actualOutboundTime:{name:"实际出仓时间",display:"date",aggregated:!1,align:"left",width:"120"},cargoDetailRows:{name:"货物明细行数",display:"number",aggregated:!0,align:"right",width:"120"},unpaidPackingFee:{name:"未收打包费",display:"number",aggregated:!0,align:"right",width:"120"},unpaidInboundFee:{name:"未收入仓费",display:"number",aggregated:!0,align:"right",width:"120"},inboundFee:{name:"入仓费",display:"number",aggregated:!0,align:"right",width:"120"},immediatePaymentFee:{name:"费用现结",display:"number",aggregated:!0,align:"right",width:"120"},includesUnloadingFee:{name:"含卸货费",display:"boolean",aggregated:!1,align:"center",width:"80"},includesInboundFee:{name:"含入仓费",display:"boolean",aggregated:!1,align:"center",width:"80"},includesPackingFee:{name:"含打包费",display:"boolean",aggregated:!1,align:"center",width:"80"},outboundType:{name:"出仓方式",display:"text",aggregated:!1,align:"left",width:"100"},rentalDays:{name:"计租天数",display:"number",aggregated:!0,align:"right",width:"100"},receivedUnloadingFee:{name:"已收卸货费",display:"number",aggregated:!0,align:"right",width:"120"},receivedPackingFee:{name:"已收打包费",display:"number",aggregated:!0,align:"right",width:"120"},contractType:{name:"联系方式",display:"text",aggregated:!1,align:"left",width:"100"},receivedSupplier:{name:"已收供应商总额",display:"number",aggregated:!0,align:"right",width:"120"},consigneeName:{name:"收货人姓名",display:"text",aggregated:!1,align:"left",width:"120"},consigneeTel:{name:"收货人电话",display:"text",aggregated:!1,align:"left",width:"120"}}),x=(a("c2aa"),a("5f87")),S={name:"Inventory",data:function(){var e=this;return{cargoDetailOpen:!1,cargoDetailRow:{cargoDetailsId:null,inboundSerialNo:null,inboundSerialSplit:null,clientCode:null,shippingMark:null,itemName:null,boxCount:null,boxItemCount:null,subtotalItemCount:null,expressDate:y()().format("yyyy-MM-DD"),additionalFee:null,packageType:"纸箱",unitGrossWeight:null,unitLength:null,unitWidth:null,unitHeight:null,unitVolume:null,damageStatus:"0",barcode:null},showLeft:0,showRight:24,loading:!0,ids:[],aggregatorRctList:[],fieldLabelMap:k,single:!0,openAggregator:!1,multiple:!0,selectedInventoryList:[],selectedPkgList:[],showSearch:!1,total:0,inventoryList:[],outboundList:[],title:"",openPkgTo:!1,outboundType:null,open:!1,openPkg:!1,pkgList:[],upload:{open:!1,title:"",isUploading:!1,updateSupport:!0,headers:{Authorization:"Bearer "+Object(x["a"])()},url:"/prod-api/system/inventory/importData"},queryParams:{pageNum:1,pageSize:20,inventoryStatus:null,inboundSerialNo:null,inboundSerialSplit:null,inboundDate:null,outboundNo:null,forwarderNo:null,rentalSettlementDate:null,outboundDate:null,clientCode:null,subOrderNo:null,supplier:null,driverInfo:null,sqdShippingMark:null,cargoName:null,totalBoxes:null,packageType:null,totalGrossWeight:null,totalVolume:null,damageStatus:null,storageLocation1:null,storageLocation2:null,storageLocation3:null,receivedStorageFee:null,unpaidUnloadingFee:null,logisticsAdvanceFee:null,rentalBalanceFee:null,freeStackPeriod:null,overdueRentalUnitPrice:null,overdueRentalFee:null,notes:null,isTopLevel:!0},form:{},pkgDetailsList:[],rules:{subOrderNo:[{validator:function(t,a,n){if(a)if(e.form.clientCode){var i="".concat(e.form.clientCode,"-");a.startsWith(i)?n():n(new Error("分单号必须以 ".concat(i," 开头")))}else n(new Error("请先选择客户代码"));else n()},trigger:"blur"}]},selectedClient:null}},components:{DataAggregatorBackGround:w["default"],printPreview:p["default"],PrintTemplate:m["default"]},watch:{showSearch:function(e){!0===e?(this.showRight=21,this.showLeft=3):(this.showRight=24,this.showLeft=0)}},mounted:function(){this.initPrint()},created:function(){this.getList()},methods:{submitFileForm:function(){this.$refs.upload.submit()},handleFileUploadProgress:function(e,t,a){this.upload.isUploading=!0},handleFileSuccess:function(e,t,a){this.upload.open=!1,this.upload.isUploading=!1,this.$refs.upload.clearFiles(),this.$message.info(e.msg),"全部上传成功"!=e.msg&&this.download("system/inventory/failList",{},"上传失败列表.xlsx"),this.getList()},loadPkgDetail:function(){var e=this;this.form.clientCode&&Object(d["h"])({packageTo:this.form.inventoryId}).then((function(t){e.pkgDetailsList=t.rows}))},loadChildInventory:function(e,t,a){var n=this;Object(d["h"])({packageTo:e.inventoryId}).then((function(t){var i=t.rows;a(i),e.children=i,n.ids.includes(e.inventoryId)&&setTimeout((function(){i.forEach((function(e){n.ids.includes(e.inventoryId)||(n.ids.push(e.inventoryId),n.selectedInventoryList.push(e)),n.$refs.inventoryTable.toggleRowSelection(e,!0)}))}),50)}))},handleAddPkg:function(){this.reset(),this.openPkg=!0},handlePackingTo:function(){if(0!==this.ids.length){var e=this.selectedInventoryList[0].clientCode,t=this.selectedInventoryList.every((function(t){return t.clientCode===e})),a=this.selectedInventoryList.every((function(e){return null==e.packageIntoNo}));if(t)if(a){this.reset(),this.form.clientCode=e,this.form.clientName=this.selectedInventoryList[0].clientName,this.form.repackingStatus="打包中";var n=new Date;this.form.actualInboundTime=n,this.form.inventoryStatus="0",this.form.subOrderNo=e+"-",this.form.packingSourceIds=this.ids,this.title="打包装箱至",this.openPkgTo=!0}else this.$message.warning("有货物已被打包");else this.$message.warning("只能打包同一个客户的货物")}else this.$message.warning("请先选择需要打包的货物")},pkgCancel:function(){var e=this;Object(d["b"])(this.selectedPkgList).then((function(t){e.$message.success("移出成功"),e.openPkg=!1,e.getList()}))},loadPkgToList:function(){var e=this;Object(d["f"])({clientCode:this.form.clientCode,repackingStatus:"打包中"}).then((function(t){e.pkgList=t.data}))},packingTo:function(){var e=this;this.selectedInventoryList.forEach((function(t){t.packageTo=e.form.packageTo,t.repackingStatus="被打包"})),Object(d["k"])(this.selectedInventoryList).then((function(t){e.$message.success("打包成功"),e.openPkgTo=!1,e.getList()}))},parseTime:v["f"],handleBlur:function(){this.form.inboundSerialNoSub=this.form.inboundSerialNoSub.padStart(4,"0")},selectInboundFee:function(){switch(this.form.recordType){case"标准":this.form.inboundFee=this.selectedClient.standardInboundFee;break;case"精确":this.form.inboundFee=this.selectedClient.preciseInboundFee;break;case"快递":this.form.inboundFee=this.selectedClient.expressInboundFee;break}},cmToCbm:function(e){return h()(e).divide(1e6).value},formatNumber:function(e){return h()(e,{symbol:"",precision:2}).format()},formatInput:function(e){var t=e.replace(/,/g,"");if(!isNaN(t))return this.amount=parseFloat(t),this.formatNumber(t)},loadCargoDetail:function(){var e=this;this.form.rsCargoDetailsList.map((function(t){e.updateFormatter(t)})),this.$forceUpdate()},updateFormatter:function(e){e.singlePieceWeightFormatter=this.formatNumber(e.singlePieceWeight),e.unitLengthFormatter=this.formatNumber(e.unitLength),e.unitHeightFormatter=this.formatNumber(e.unitHeight),e.singlePieceVolumeFormatter=this.formatNumber(e.singlePieceVolume),e.unitGrossWeightFormatter=this.formatNumber(e.unitGrossWeight),e.unitVolumeFormatter=this.formatNumber(e.unitVolume),e.unitWidthFormatter=this.formatNumber(e.unitWidth),e.boxItemCountFormatter=Number(e.boxItemCount)?Number(e.boxItemCount):0,e.subtotalItemCountFormatter=Number(e.subtotalItemCount)?Number(e.subtotalItemCount):0},parseInput:function(e,t){if(e&&"object"===Object(s["a"])(e))switch(t){case"singlePieceWeight":var a=String(e.singlePieceWeightFormatter||"").replace(/,/g,"");isNaN(a)||""===a||(e.singlePieceWeightFormatter=this.formatNumber(a));break;case"unitLength":var n=String(e.unitLengthFormatter||"").replace(/,/g,"");isNaN(n)||""===n||(e.unitLengthFormatter=this.formatNumber(n));break;case"unitWidth":var i=String(e.unitWidthFormatter||"").replace(/,/g,"");isNaN(i)||""===i||(e.unitWidthFormatter=this.formatNumber(i));break;case"unitHeight":var l=String(e.unitHeightFormatter||"").replace(/,/g,"");isNaN(l)||""===l||(e.unitHeightFormatter=this.formatNumber(l));break;case"singlePieceVolume":var r=String(e.singlePieceVolumeFormatter||"").replace(/,/g,"");isNaN(r)||""===r||(e.singlePieceVolumeFormatter=this.formatNumber(r));break;case"unitGrossWeight":var o=String(e.unitGrossWeightFormatter||"").replace(/,/g,"");isNaN(o)||""===o||(e.unitGrossWeightFormatter=this.formatNumber(o));break;case"unitVolume":var d=String(e.unitVolumeFormatter||"").replace(/,/g,"");isNaN(d)||""===d||(e.unitVolumeFormatter=this.formatNumber(d));break}},countCargomeasure:function(e,t){var a=this;if(e&&"object"===Object(s["a"])(e)){switch(t){case"singlePieceWeight":var n=String(e.singlePieceWeightFormatter||"").replace(/,/g,"");isNaN(n)||""===n||(e.singlePieceWeight=parseFloat(n));break;case"unitLength":var i=String(e.unitLengthFormatter||"").replace(/,/g,"");isNaN(i)||""===i||(e.unitLength=parseFloat(i));break;case"unitWidth":var l=String(e.unitWidthFormatter||"").replace(/,/g,"");isNaN(l)||""===l||(e.unitWidth=parseFloat(l));break;case"unitHeight":var r=String(e.unitHeightFormatter||"").replace(/,/g,"");isNaN(r)||""===r||(e.unitHeight=parseFloat(r));break;case"singlePieceVolume":var o=String(e.singlePieceVolumeFormatter||"").replace(/,/g,"");isNaN(o)||""===o||(e.singlePieceVolume=parseFloat(o));break;case"unitGrossWeight":var d=String(e.unitGrossWeightFormatter||"").replace(/,/g,"");isNaN(d)||""===d||(e.unitGrossWeight=parseFloat(d));break;case"unitVolume":var c=String(e.unitVolumeFormatter||"").replace(/,/g,"");isNaN(c)||""===c||(e.unitVolume=parseFloat(c));break}this.form.rsCargoDetailsList=this.form.rsCargoDetailsList.map((function(t){if(e===t){if(t.unitLength&&t.unitWidth&&t.unitHeight){var n=h()(t.unitLength).multiply(t.unitWidth).multiply(t.unitHeight).value;t.singlePieceVolume=a.cmToCbm(n),t.singlePieceVolumeFormatter=a.formatNumber(t.singlePieceVolume)}t.singlePieceVolume&&t.boxCount&&(t.unitVolume=h()(t.singlePieceVolume).multiply(t.boxCount).value,t.unitVolumeFormatter=a.formatNumber(t.unitVolume)),t.singlePieceWeight&&t.boxCount&&(t.unitGrossWeight=h()(t.singlePieceWeight).multiply(t.boxCount).value,t.unitGrossWeightFormatter=a.formatNumber(t.unitGrossWeight)),t.boxItemCount&&(t.boxCount?t.subtotalItemCount=h()(t.boxItemCount).multiply(t.boxCount).value:t.subtotalItemCount=h()(t.boxItemCount).value)}return t}))}},selectWarehouseClient:function(e){if(this.form.clientCode=e.clientCode,this.form.clientName=e.clientName,this.form.overdueRentalUnitPrice=e.overdueRent,this.form.freeStackPeriod=e.freeStackPeriod,this.selectedClient=e,null!=e.includesUnloadingFee&&(this.form.includesUnloadingFee=e.includesUnloadingFee),null!=e.includesPackingFee&&(this.form.includesPackingFee=e.includesPackingFee,1==e.includesPackingFee&&(this.form.unpaidPackingFee=0)),null!=e.includesInboundFee&&(this.form.includesInboundFee=e.includesInboundFee,1==e.includesInboundFee&&(this.form.inboundFee=0)),e.immediatePaymentFee&&(this.form.immediatePaymentFee=e.immediatePaymentFee),this.form.recordType&&0==e.includesInboundFee)switch(this.form.recordType){case"标准":this.form.inboundFee=e.standardInboundFee;break;case"精确":this.form.inboundFee=e.preciseInboundFee;break;case"快递":this.form.inboundFee=e.expressInboundFee;break}this.form.subOrderNo="",this.form.subOrderNo=e.clientCode+"-"},getSummariesInventory:function(e){var t=this,a=e.columns,n=e.data,i=[],l=["totalBoxes","totalGrossWeight","totalVolume","inboundFee","receivedStorageFee","unpaidUnloadingFee","receivedUnloadingFee","unpaidPackingFee","receivedPackingFee","logisticsAdvanceFee"];return a.forEach((function(e,a){if(0!==a)if(l.includes(e.property)){var r=n.map((function(t){return Number(t[e.property])})).filter((function(e){return!isNaN(e)}));if(r.length>0){var o=r.reduce((function(e,t){return e+t}),0);i[a]=t.formatNumberFixed(o)+("totalVolume"===e.property?" CBM":"totalGrossWeight"===e.property?" KGS":"totalBoxes"===e.property?" 件":"")}else i[a]=" "}else i[a]=" ";else i[a]="总计:"})),i},getSummaries:function(e){var t=this,a=e.columns,n=e.data,i=[],l=["unitVolume","unitGrossWeight","boxCount"];return a.forEach((function(e,a){if(0!==a)if(l.includes(e.property)){var r=n.map((function(t){return Number(t[e.property])})).filter((function(e){return!isNaN(e)}));if(r.length>0){var o=r.reduce((function(e,t){return e+t}),0);i[a]=t.formatNumberFixed(o)+("unitVolume"===e.property?" CBM":"unitGrossWeight"===e.property?" KGS":"boxCount"===e.property?" 件":"")}else i[a]=" "}else i[a]=" ";else i[a]="总计:"})),i},formatNumberFixed:function(e){return Number(e).toFixed(2)},handleOpOutbound:function(){this.openOutbound=!0},initPrint:function(){c["c"].init({providers:[new c["a"]]})},printInboundBill:function(e){var t={};t.clientCode=this.form.clientCode,t.subOrderNo=this.form.subOrderNo,t.inboundSerialNo=this.form.inboundSerialNo,t.forwarderNo=this.form.forwarderNo,t.actualInboundTime=y()(this.form.actualInboundTime).format("yyyy-MM-DD HH:mm:ss"),t.sqdShippingMark=this.form.sqdShippingMark,t.cargoName=this.form.cargoName,t.totalBoxes=this.form.totalBoxes,t.totalGrossWeight=this.form.totalGrossWeight,t.totalVolume=this.form.totalVolume,t.notes=this.form.notes,t.supplier=this.form.supplier,t.driverInfo=this.form.driverInfo,t.inboundAddr=this.form.inboundAddr,t.consigneeName=this.form.consigneeName,t.inboundSerialNo=this.form.inboundSerialNo,t.expressNo=this.form.driverInfo,n="旧模板"===e?new c["c"].PrintTemplate({template:u["a"]}):new c["c"].PrintTemplate({template:g}),this.$refs.preView.print(n,t)},deleteCargoDetail:function(e){this.form.rsCargoDetailsList=this.form.rsCargoDetailsList.filter((function(t){return t!==e})),this.$forceUpdate()},handleUpdateCargoDetail:function(e){this.cargoDetailOpen=!0,this.cargoDetailRow=e},handlePkgSelectionChange:function(e){this.selectedPkgList=e},pkgFinish:function(){var e=this;"打包完"===this.form.repackingStatus?this.form.repackingStatus="打包中":this.form.repackingStatus="打包完",Object(d["n"])(this.form).then((function(t){e.$message.success("修改成功")}))},addCargoDetailRow:function(e){},addCargoDetail:function(){this.form.rsCargoDetailsList.push(this._.cloneDeep(this.cargoDetailRow)),this.cargoDetailOpen=!0},listAggregatorRsInventory:function(e){return e.config=JSON.stringify(e.config),this.queryParams.params=e,Object(d["g"])(this.queryParams)},getList:function(){var e=this;this.loading=!0,this.queryParams.permissionLevel=this.$store.state.user.permissionLevelList.C,this.queryParams.isTopLevel=!0,Object(d["h"])(this.queryParams).then((function(t){var a=t.rows;e.queryParams.isTopLevel&&!e.queryParams.driverInfo&&(a=a.filter((function(e){return!e.packageTo}))),e.inventoryList=a.map((function(e){return"1"===e.packageRecord&&(e.hasChildren=!0),e})),e.total=t.total,e.loading=!1}))},cancel:function(){this.open=!1,this.reset()},reset:function(){this.pkgDetailsList=[],this.form={repackingStatus:"-",inventoryId:null,inventoryStatus:"0",inboundSerialNo:null,inboundSerialNoPre:null,inboundSerialSplit:null,inboundDate:null,outboundNo:null,forwarderNo:null,rentalSettlementDate:null,outboundDate:null,clientCode:null,subOrderNo:null,supplier:null,driverInfo:null,sqdShippingMark:null,cargoName:null,totalBoxes:null,packageType:null,totalGrossWeight:null,totalVolume:null,damageStatus:"0",storageLocation1:null,storageLocation2:null,storageLocation3:null,receivedStorageFee:null,unpaidUnloadingFee:null,logisticsAdvanceFee:null,rentalBalanceFee:null,freeStackPeriod:null,overdueRentalUnitPrice:null,overdueRentalFee:null,notes:null,warehouseCode:null,recordType:null,inboundType:null,cargoNature:null,createdAt:null,preOutboundFlag:null,outboundRequestFlag:null,sqdPlannedOutboundDate:null,confirmInboundRequestFlag:null,confirmOutboundRequestFlag:null,sqdInboundHandler:null,partialOutboundFlag:null,outboundRecordId:null,actualInboundTime:y()().format("yyyy-MM-DD HH:mm:ss"),actualOutboundTime:null,cargoDetailRows:null,includesUnloadingFee:null,unpaidPackingFee:null,inboundFee:null,singlePieceWeightFormatter:null,unitLengthFormatter:null,unitWidthFormatter:null,unitHeightFormatter:null,singlePieceVolumeFormatter:null,unitGrossWeightFormatter:null,unitVolumeFormatter:null,rsCargoDetailsList:[{shippingMark:"",itemName:"",boxCount:0,packageType:"纸箱",singlePieceWeightFormatter:"",unitLengthFormatter:"",unitWidthFormatter:"",unitHeightFormatter:"",singlePieceVolumeFormatter:"",unitGrossWeightFormatter:"",unitVolumeFormatter:"",damageStatus:"",boxItemCount:0,subtotalItemCount:0,expressDate:y()().format("yyyy-MM-DD"),additionalFee:0}]},this.resetForm("form")},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.resetForm("queryForm"),this.queryParams.isTopLevel=!0,this.handleQuery()},handleStatusChange:function(e){var t=this,a="0"===e.status?"启用":"停用";this.$modal.confirm('确认要"'+a+"吗？").then((function(){return Object(d["c"])(e.inventoryId,e.status)})).then((function(){t.$modal.msgSuccess(a+"成功")})).catch((function(){e.status="0"===e.status?"1":"0"}))},handleSelectionChange:function(e){var t=this,a=this.$refs.inventoryTable.store.states.data,n=Object(o["a"])(this.ids);this.ids=[],this.selectedInventoryList=[],this.ids=e.map((function(e){return e.inventoryId})),this.single=1!==e.length,this.multiple=!e.length,this.selectedInventoryList=e;var i=this.ids.filter((function(e){return!n.includes(e)})),l=n.filter((function(e){return!t.ids.includes(e)}));e.forEach((function(e){if("1"===e.packageRecord&&i.includes(e.inventoryId)){var n=a.find((function(t){return t.inventoryId===e.inventoryId}));n&&n.children&&n.children.length>0?setTimeout((function(){n.children.forEach((function(e){t.ids.includes(e.inventoryId)||(t.ids.push(e.inventoryId),t.selectedInventoryList.push(e),t.$refs.inventoryTable.toggleRowSelection(e,!0))}))}),50):n&&!n.childrenLoaded&&n.hasChildren&&(n.childrenLoaded=!0,t.$refs.inventoryTable.toggleRowExpansion(n,!0))}})),l.forEach((function(e){var n=a.find((function(t){return t.inventoryId===e&&"1"===t.packageRecord}));n&&n.children&&n.children.length>0&&n.children.forEach((function(e){var a=t.ids.indexOf(e.inventoryId);if(a>-1){t.ids.splice(a,1);var n=t.selectedInventoryList.findIndex((function(t){return t.inventoryId===e.inventoryId}));n>-1&&t.selectedInventoryList.splice(n,1),t.$refs.inventoryTable.toggleRowSelection(e,!1)}}))}))},handleOpenAggregator:function(){this.openAggregator=!0},handleAdd:function(){var e=this;this.reset(),this.open=!0,this.title="入仓",this.form.sqdInboundHandler=this.$store.state.user.name.split(" ")[1],this.form.actualInboundTime=new Date,this.form.inventoryStatus="0",this.$nextTick((function(){e.form.inboundSerialNoPre="RS.91",e.form.cargoNature="普货",e.form.recordType="标准",e.form.inboundType="入仓"}))},handleUpdate:function(e){var t=this;this.reset();var a=e.inventoryId||this.ids;Object(d["e"])(a).then((function(e){t.form=e.data,t.form.rsCargoDetailsList=e.data.rsCargoDetailsList.map((function(e){return Object(r["a"])(Object(r["a"])({},e),{},{shippingMark:e.shippingMark||"",itemName:e.itemName||"",boxCount:e.boxCount||0,packageType:e.packageType||"",singlePieceWeight:e.singlePieceWeight||0,singlePieceWeightFormatter:t.formatNumber(e.singlePieceWeight||0),unitLength:e.unitLength||0,unitLengthFormatter:t.formatNumber(e.unitLength||0),unitWidth:e.unitWidth||0,unitWidthFormatter:t.formatNumber(e.unitWidth||0),unitHeight:e.unitHeight||0,unitHeightFormatter:t.formatNumber(e.unitHeight||0),singlePieceVolume:e.singlePieceVolume||0,singlePieceVolumeFormatter:t.formatNumber(e.singlePieceVolume||0),unitGrossWeight:e.unitGrossWeight||0,unitGrossWeightFormatter:t.formatNumber(e.unitGrossWeight||0),unitVolume:e.unitVolume||0,unitVolumeFormatter:t.formatNumber(e.unitVolume||0),damageStatus:e.damageStatus||""})})),"0"===e.data.packageRecord?t.open=!0:t.openPkg=!0,t.title="修改库存"}))},submitForm:function(e){var t=this;this.$refs["form"].validate((function(a){if(a){if(t.form.subOrderNo&&!t.form.subOrderNo.startsWith("".concat(t.form.clientCode,"-")))return void t.$modal.msgError("分单号必须以 ".concat(t.form.clientCode,"- 开头"));t.form.inboundDate=t.form.inboundDate?y()(t.form.inboundDate).format("yyyy-MM-DD HH:mm:ss"):null,t.form.actualInboundTime=t.form.actualInboundTime?y()(t.form.actualInboundTime).format("yyyy-MM-DD HH:mm:ss"):null,t.form.actualInboundTime=y()(t.form.actualInboundTime).format("yyyy-MM-DD HH:mm:ss"),"pkg"!==e&&(t.form.totalBoxes=0,t.form.totalGrossWeight=0,t.form.totalVolume=0,t.form.rsCargoDetailsList.forEach((function(e,a){t.form.totalBoxes=h()(e.boxCount||0).add(t.form.totalBoxes).value,t.form.totalGrossWeight=h()(e.unitGrossWeight||0).add(t.form.totalGrossWeight).value,t.form.totalVolume=h()(e.unitVolume||0).add(t.form.totalVolume).value})));var n=[];t.form.rsCargoDetailsList.map((function(e){n.push(e.shippingMark)})),t.form.sqdShippingMark=n.join(","),null!=t.form.inventoryId?(!t.form.rentalSettlementDate&&(t.form.rentalSettlementDate=t.form.actualInboundTime),Object(d["n"])(t.form).then((function(e){t.$modal.msgSuccess("修改成功"),t.getList()}))):("pkg"===e&&(t.form.packageRecord="1",t.form.repackingStatus="打包中"),t.form.rentalSettlementDate=t.form.actualInboundTime,Object(d["a"])(t.form).then((function(e){t.form=e.data,t.$modal.msgSuccess("新增成功"),t.getList()})))}}))},handleDelete:function(e){var t=this,a=e.inventoryId||this.ids;this.$modal.confirm('是否确认删除库存编号为"'+a+'"的数据项？').then((function(){return Object(d["d"])(a)})).then((function(){t.getList(),t.$modal.msgSuccess("删除成功")})).catch((function(){}))},handleImport:function(){this.upload.title="用户导入",this.upload.open=!0},handleExport:function(){this.download("system/inventory/export",Object(r["a"])(Object(r["a"])({},this.queryParams),{},{pageSize:999}),"inventory_".concat((new Date).getTime(),".xlsx"))}}},C=S,F=(a("6926"),a("2877")),N=Object(F["a"])(C,i,l,!1,null,"4ff94f56",null);t["default"]=N.exports},"8af8":function(e,t,a){},"9a0c":function(e,t,a){var n=a("342f");e.exports=/Version\/10(?:\.\d+){1,2}(?: [\w./]+)?(?: Mobile\/\w+)? Safari\//.test(n)},"9d56":function(e,t,a){"use strict";t["a"]={panels:[{index:0,name:1,paperType:"A4",height:297,width:210,paperHeader:0,paperFooter:634.5,printElements:[{options:{left:103.5,top:85.5,height:28.5,width:97,title:"文本",field:"clientCode",coordinateSync:!1,widthHeightSync:!1,hideTitle:!0,qrCodeLevel:0,fontSize:20,textAlign:"center",fontWeight:"bold",testData:"KNL",textContentVerticalAlign:"middle"},printElementType:{title:"文本",type:"text"}},{options:{left:111,top:117,height:9.75,width:86,title:"文本",field:"subOrderNo",coordinateSync:!1,widthHeightSync:!1,hideTitle:!0,qrCodeLevel:0,testData:"name"},printElementType:{title:"文本",type:"text"}},{options:{left:241.5,top:106.5,height:9.75,width:92,title:"文本",field:"forwarderNo",coordinateSync:!1,widthHeightSync:!1,hideTitle:!0,qrCodeLevel:0},printElementType:{title:"文本",type:"text"}},{options:{left:241.5,top:118.5,height:9.75,width:92,title:"文本",field:"actualInboundTime",coordinateSync:!1,widthHeightSync:!1,hideTitle:!0,qrCodeLevel:0},printElementType:{title:"文本",type:"text"}},{options:{left:90,top:168,height:9.75,width:34.5,title:"文本",right:77.25,bottom:177.75,vCenter:60,hCenter:172.875,field:"sqdShippingMark",coordinateSync:!1,widthHeightSync:!1,hideTitle:!0,qrCodeLevel:0},printElementType:{title:"文本",type:"text"}},{options:{left:127.5,top:166.5,height:9.75,width:85.5,title:"文本",right:115.74609375,bottom:177.99609375,vCenter:98.49609375,hCenter:173.12109375,field:"cargoName",coordinateSync:!1,widthHeightSync:!1,hideTitle:!0,qrCodeLevel:0},printElementType:{title:"文本",type:"text"}},{options:{left:217.5,top:166.5,height:9.75,width:27,title:"文本",right:197.25,bottom:176.25,vCenter:183.75,hCenter:171.375,field:"totalBoxes",coordinateSync:!1,widthHeightSync:!1,hideTitle:!0,qrCodeLevel:0},printElementType:{title:"文本",type:"text"}},{options:{left:246,top:166.5,height:9.75,width:27,title:"文本",right:227.2448272705078,bottom:175.4973907470703,vCenter:213.7448272705078,hCenter:170.6223907470703,field:"totalGrossWeight",coordinateSync:!1,widthHeightSync:!1,hideTitle:!0,qrCodeLevel:0},printElementType:{title:"文本",type:"text"}},{options:{left:280.5,top:166.5,height:9.75,width:27,title:"文本",right:260.4895935058594,bottom:176.49739837646484,vCenter:246.98959350585938,hCenter:171.62239837646484,field:"totalVolume",coordinateSync:!1,widthHeightSync:!1,hideTitle:!0,qrCodeLevel:0},printElementType:{title:"文本",type:"text"}},{options:{left:313.5,top:166.5,height:9.75,width:27,title:"文本",right:292.7395935058594,bottom:176.49739837646484,vCenter:279.2395935058594,hCenter:171.62239837646484,field:"inboundNotes",coordinateSync:!1,widthHeightSync:!1,hideTitle:!0,qrCodeLevel:0},printElementType:{title:"文本",type:"text"}},{options:{left:403.5,top:87,height:13.5,width:51,title:"文本",field:"clientCode",coordinateSync:!1,widthHeightSync:!1,hideTitle:!0,qrCodeLevel:0,right:438.99742126464844,bottom:116.49217987060547,vCenter:397.74742126464844,hCenter:102.24217987060547},printElementType:{title:"文本",type:"text"}},{options:{left:403.5,top:102,height:9.75,width:49.5,title:"文本",field:"subOrderNo",coordinateSync:!1,widthHeightSync:!1,hideTitle:!0,qrCodeLevel:0,right:441.7500305175781,bottom:112.73959350585938,vCenter:398.7500305175781,hCenter:107.86459350585938},printElementType:{title:"文本",type:"text"}},{options:{left:477,top:100.5,height:9.75,width:102,title:"文本",field:"forwarderNo",coordinateSync:!1,widthHeightSync:!1,hideTitle:!0,qrCodeLevel:0,right:538.4895935058594,bottom:111.98959350585938,vCenter:492.4895935058594,hCenter:107.11459350585938},printElementType:{title:"文本",type:"text"}},{options:{left:477,top:118.5,height:9.75,width:100.5,title:"文本",field:"actualInboundTime",coordinateSync:!1,widthHeightSync:!1,hideTitle:!0,qrCodeLevel:0,right:540.7395935058594,bottom:126.25000762939453,vCenter:494.7395935058594,hCenter:121.37500762939453},printElementType:{title:"文本",type:"text"}},{options:{left:406.5,top:120,height:9.75,width:48,title:"文本",field:"supplier",coordinateSync:!1,widthHeightSync:!1,hideTitle:!0,qrCodeLevel:0,right:452.9895935058594,bottom:129.25000762939453,vCenter:406.9895935058594,hCenter:124.37500762939453},printElementType:{title:"文本",type:"text"}},{options:{left:406.5,top:145.5,height:9.75,width:130.5,title:"文本",right:447.2395935058594,bottom:150.9974072277546,vCenter:404.4895935058594,hCenter:146.1224072277546,field:"cargoName",coordinateSync:!1,widthHeightSync:!1,hideTitle:!0,qrCodeLevel:0},printElementType:{title:"文本",type:"text"}},{options:{left:406.5,top:177,height:9.75,width:157.5,title:"文本",right:389.49220275878906,bottom:184.74739837646484,vCenter:375.99220275878906,hCenter:179.87239837646484,field:"inboundNotes",coordinateSync:!1,widthHeightSync:!1,hideTitle:!0,qrCodeLevel:0},printElementType:{title:"文本",type:"text"}},{options:{left:406.5,top:207,height:9.75,width:157.5,title:"文本",right:514.7500762939453,bottom:214.74479484558105,vCenter:436.0000762939453,hCenter:209.86979484558105,field:"driverInfo",coordinateSync:!1,widthHeightSync:!1,hideTitle:!0,qrCodeLevel:0},printElementType:{title:"文本",type:"text"}},{options:{left:406.5,top:237,height:9.75,width:157.5,title:"文本",right:514.7422027587891,bottom:241.75001621246338,vCenter:435.99220275878906,hCenter:236.87501621246338,field:"inboundAddr",coordinateSync:!1,widthHeightSync:!1,hideTitle:!0,qrCodeLevel:0},printElementType:{title:"文本",type:"text"}},{options:{left:406.5,top:252,height:9.75,width:157.5,title:"文本",right:516.2422027587891,bottom:256.7500162124634,vCenter:437.49220275878906,hCenter:251.87501621246338,field:"deliveryDriver",coordinateSync:!1,widthHeightSync:!1,hideTitle:!0,qrCodeLevel:0},printElementType:{title:"文本",type:"text"}},{options:{left:73.5,top:252,height:9.75,width:270,title:"文本",field:"operationRecord",coordinateSync:!1,widthHeightSync:!1,hideTitle:!0,qrCodeLevel:0},printElementType:{title:"文本",type:"text"}},{options:{left:540.75,top:146.25,height:9.75,width:27,title:"文本",right:197.25,bottom:176.25,vCenter:183.75,hCenter:171.375,field:"totalBoxes",coordinateSync:!1,widthHeightSync:!1,hideTitle:!0,qrCodeLevel:0},printElementType:{title:"文本",type:"text"}}],paperNumberLeft:540,paperNumberTop:21,paperNumberDisabled:!0,panelPageRule:"none",watermarkOptions:{content:"",fillStyle:"rgba(184, 184, 184, 0.3)",fontSize:"14px",rotate:25,width:200,height:200,timestamp:!1,format:"YYYY-MM-DD HH:mm"}}]}},a159:function(e,t,a){"use strict";a.d(t,"e",(function(){return i})),a.d(t,"d",(function(){return l})),a.d(t,"a",(function(){return r})),a.d(t,"g",(function(){return o})),a.d(t,"c",(function(){return s})),a.d(t,"b",(function(){return d})),a.d(t,"f",(function(){return c}));var n=a("b775");function i(e){return Object(n["a"])({url:"/monitor/job/list",method:"get",params:e})}function l(e){return Object(n["a"])({url:"/monitor/job/"+e,method:"get"})}function r(e){return Object(n["a"])({url:"/monitor/job",method:"post",data:e})}function o(e){return Object(n["a"])({url:"/monitor/job",method:"put",data:e})}function s(e){return Object(n["a"])({url:"/monitor/job/"+e,method:"delete"})}function d(e,t){var a={jobId:e,status:t};return Object(n["a"])({url:"/monitor/job/changeStatus",method:"put",data:a})}function c(e,t){var a={jobId:e,jobGroup:t};return Object(n["a"])({url:"/monitor/job/run",method:"put",data:a})}},b782:function(e,t,a){"use strict";a.r(t);var n=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"print-content",attrs:{id:"print-content"}},[e._m(0),n("div",{staticClass:"main-content",attrs:{id:"main-content"}},[n("h2",{staticClass:"title"},[e._v("DEBIT NOTE")]),n("el-row",[n("el-col",{attrs:{span:8}},[n("div",{staticClass:"form-item"},[e._v(" TO： ")]),n("div",{staticClass:"form-item"},[e._v(" ATTN： ")]),n("div",{staticClass:"form-item"},[e._v(" REF No： ")]),n("div",{staticClass:"form-item"},[e._v(" S/O No： ")]),n("div",{staticClass:"form-item"},[e._v(" LoadTime： ")])]),n("el-col",{attrs:{span:8}},[n("div",{staticClass:"form-item",staticStyle:{width:"100%",height:"21px"}}),n("div",{staticClass:"form-item"},[e._v(" Contract No： ")]),n("div",{staticClass:"form-item"},[e._v(" POL： ")]),n("div",{staticClass:"form-item"},[e._v(" POD： ")]),n("div",{staticClass:"form-item"},[e._v(" CARRIER： ")])]),n("el-col",{attrs:{span:8}},[n("div",{staticClass:"form-item"},[e._v(" Printing Date： ")]),n("div",{staticClass:"form-item"},[e._v(" Trans Type： ")]),n("div",{staticClass:"form-item"},[e._v(" RevTon： ")]),n("div",{staticClass:"form-item"},[e._v(" CtnrNo： ")])])],1),n("div",{staticStyle:{"font-size":"13px"}},[e._v("本次运杂费明细如下:")]),n("div",{staticStyle:{"font-size":"13px"}},[e._v("(The Rates Details is as following)：")]),n("el-table",{staticStyle:{width:"100%"},attrs:{data:e.tableData}},[n("el-table-column",{attrs:{align:"center",prop:"address"},scopedSlots:e._u([{key:"header",fn:function(t){return[n("div",{staticClass:"table-title"},[n("div",[e._v("收费项目")]),n("div",{staticStyle:{"line-height":"15px"}},[e._v("(FreightName)")])])]}}])}),n("el-table-column",{attrs:{align:"center",prop:"address"},scopedSlots:e._u([{key:"header",fn:function(t){return[n("div",{staticClass:"table-title"},[n("div",[e._v("备注")]),n("div",[e._v("(Remark)")])])]}}])}),n("el-table-column",{attrs:{align:"center",prop:"address"},scopedSlots:e._u([{key:"header",fn:function(t){return[n("div",{staticClass:"table-title"},[n("div",[e._v("币制")]),n("div",[e._v("(CrcSys)")])])]}}])}),n("el-table-column",{attrs:{align:"center",prop:"address"},scopedSlots:e._u([{key:"header",fn:function(t){return[n("div",{staticClass:"table-title"},[n("div",[e._v("单价")]),n("div",[e._v("(UnitPrice)")])])]}}])}),n("el-table-column",{attrs:{align:"center",prop:"address"},scopedSlots:e._u([{key:"header",fn:function(t){return[n("div",{staticClass:"table-title"},[n("div",[e._v("数量")]),n("div",[e._v("(Counter)")])])]}}])}),n("el-table-column",{attrs:{align:"center",prop:"address"},scopedSlots:e._u([{key:"header",fn:function(t){return[n("div",{staticClass:"table-title"},[n("div",[e._v("税率")]),n("div",[e._v("(CrcSys)")])])]}}])}),n("el-table-column",{attrs:{align:"center",prop:"address"},scopedSlots:e._u([{key:"header",fn:function(t){return[n("div",{staticClass:"table-title"},[n("div",[e._v("小计")]),n("div",[e._v("(SubTotal)")])])]}}])})],1),n("el-row",{staticStyle:{"border-top":"solid 2px black"}},[n("el-col",{attrs:{span:15}},[n("div",{staticStyle:{"font-size":"14px",color:"blue"}},[n("div",[e._v("关于汇率：")]),n("div",[e._v("请按本账单约定的币种和汇率对应金额进行结算 ----------\x3e>")]),n("div",[e._v("如需更改结算币种，则需以我司财务部重新确认的汇率为准。")])])]),n("el-col",{attrs:{span:9}},[n("div",{staticStyle:{"font-size":"14px"}},[e._v("总计(Total)：")])])],1),n("el-row",[n("el-col",{attrs:{span:14}},[n("div",{staticStyle:{"font-size":"13px","margin-top":"20px"}},[n("div",[e._v("我司公账如下："),n("span",{staticStyle:{"font-weight":"bold"}},[e._v("(不接受私账付款）")])]),n("div",[e._v("(Our Company Account Details is as following)：")]),n("div",{staticStyle:{"margin-left":"20px"}},[e._v(" 开户行：中国工商银行广州东城支行"),n("br"),e._v(" 户 名：广州瑞旗国际货运代理有限公司"),n("br"),e._v(" 账 号：3602 2015 0910 0052 834（RMB）"),n("br"),e._v(" 开户行：中国工商银行广州东城支行"),n("br"),e._v(" 户 名：广州瑞旗国际货运代理有限公司"),n("br"),e._v(" 账 号：3602 2015 1910 0371 687 (USD) 银行行号: ************"),n("br"),e._v(" 开户行代码（SWIFT CODE）：ICBKCNBJGDG ")])])]),n("el-col",{attrs:{span:10}},[n("img",{staticStyle:{width:"130px"},attrs:{src:a("ca0c")}})])],1)],1),e._m(1)])},i=[function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{attrs:{id:"header"}},[n("img",{staticClass:"image",attrs:{src:a("e881")}})])},function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{attrs:{id:"footer"}},[n("img",{staticClass:"image",attrs:{src:a("7a64")}})])}],l=(a("aae1"),{name:"PrintTemplate",created:function(){this.$route.params},data:function(){return{tableData:[]}}}),r=l,o=(a("53cf"),a("2877")),s=Object(o["a"])(r,n,i,!1,null,"3a08728d",null);t["default"]=s.exports},ba54:function(e,t,a){"use strict";a("712d")},c211:function(e,t,a){"use strict";a.d(t,"c",(function(){return i})),a.d(t,"b",(function(){return l})),a.d(t,"a",(function(){return r}));var n=a("b775");function i(e){return Object(n["a"])({url:"/system/aggregatorconfigs",method:"post",data:e})}function l(e){return Object(n["a"])({url:"/system/aggregatorconfigs/list",method:"get",params:e})}function r(e){return Object(n["a"])({url:"/system/aggregatorconfigs/".concat(e),method:"delete"})}},c2aa:function(e,t,a){"use strict";a.d(t,"o",(function(){return i})),a.d(t,"n",(function(){return l})),a.d(t,"p",(function(){return r})),a.d(t,"r",(function(){return o})),a.d(t,"q",(function(){return s})),a.d(t,"i",(function(){return d})),a.d(t,"g",(function(){return c})),a.d(t,"v",(function(){return u})),a.d(t,"w",(function(){return g})),a.d(t,"h",(function(){return p})),a.d(t,"c",(function(){return m})),a.d(t,"a",(function(){return f})),a.d(t,"f",(function(){return h})),a.d(t,"d",(function(){return b})),a.d(t,"e",(function(){return y})),a.d(t,"k",(function(){return v})),a.d(t,"j",(function(){return w})),a.d(t,"m",(function(){return k})),a.d(t,"t",(function(){return x})),a.d(t,"u",(function(){return S})),a.d(t,"l",(function(){return C})),a.d(t,"s",(function(){return F}));var n=a("b775");function i(e){return Object(n["a"])({url:"/system/rct/list",method:"get",params:e})}function l(e){return Object(n["a"])({url:"/system/rct/aggregator",method:"get",params:e})}function r(e){return Object(n["a"])({url:"/system/rct/listVerifyAggregatorList",method:"get",params:e})}function o(e){return Object(n["a"])({url:"/system/rct/op",method:"get",params:e})}function s(e){return Object(n["a"])({url:"/system/rct/listVerifyList",method:"get",params:e})}function d(e){return Object(n["a"])({url:"/system/rct/"+e,method:"get"})}function c(e){return Object(n["a"])({url:"/system/rct",method:"post",data:e})}function u(e){return Object(n["a"])({url:"/system/rct/saveAs",method:"post",data:e})}function g(e){return Object(n["a"])({url:"/system/rct",method:"put",data:e})}function p(e){return Object(n["a"])({url:"/system/rct/"+e,method:"delete"})}function m(e){return Object(n["a"])({url:"/system/rct/saveClientMessage",method:"post",data:e})}function f(e){return Object(n["a"])({url:"/system/rct/saveBasicLogistics",method:"post",data:e})}function h(e){return Object(n["a"])({url:"/system/rct/savePreCarriage",method:"post",data:e})}function b(e){return Object(n["a"])({url:"/system/rct/saveExportDeclaration",method:"post",data:e})}function y(e){return Object(n["a"])({url:"/system/rct/saveImportClearance",method:"post",data:e})}function v(){return Object(n["a"])({url:"/system/rct/mon",method:"get"})}function w(){return Object(n["a"])({url:"/system/rct/CFmon",method:"get"})}function k(){return Object(n["a"])({url:"/system/rct/RSWHMon",method:"get"})}function x(e){return Object(n["a"])({url:"/system/rct/saveAllService",method:"post",data:e})}function S(e){return Object(n["a"])({url:"/system/rct/saveAsAllService",method:"post",data:e})}function C(e){return Object(n["a"])({url:"/system/rct/listRctNoByCompany/"+e,method:"get"})}function F(e){return Object(n["a"])({url:"/system/rct/writeoff",method:"post",data:e})}},ca0c:function(e,t,a){e.exports=a.p+"static/img/signet.png"},cd43:function(e,t,a){"use strict";a.r(t);var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"data-aggregator"},[a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:(e.showResult,10)}},[a("el-card",{staticClass:"config-card",scopedSlots:e._u([{key:"header",fn:function(){return[a("div",{staticClass:"header-with-operations"},[a("span",[e._v("汇总配置")])])]},proxy:!0}])},[a("el-form",{staticClass:"edit",attrs:{"label-width":"80px"}},[a("el-form-item",{attrs:{label:"速查名称",required:""}},[a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:18}},[a("el-input",{attrs:{placeholder:"请输入速查名称"},model:{value:e.config.name,callback:function(t){e.$set(e.config,"name",t)},expression:"config.name"}})],1),a("el-col",{attrs:{span:5}},[a("el-button",{attrs:{size:"small",type:"text"},on:{click:e.saveConfig}},[e._v("[↗]")]),a("el-button",{attrs:{size:"small",type:"text"},on:{click:e.loadConfigs}},[e._v("[...]")])],1)],1)],1),a("el-form-item",{attrs:{label:"分组依据"}},[a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:8}},[a("el-select",{attrs:{clearable:"",filterable:"",placeholder:"操作单号"},model:{value:e.config.primaryField,callback:function(t){e.$set(e.config,"primaryField",t)},expression:"config.primaryField"}},e._l(e.availableFields,(function(t){return a("el-option",{key:t,attrs:{label:e.getFieldLabel(t),value:t}})})),1)],1),a("el-col",{attrs:{span:15}},[a("el-checkbox",{model:{value:e.config.matchOptions.exact,callback:function(t){e.$set(e.config.matchOptions,"exact",t)},expression:"config.matchOptions.exact"}},[e._v("精确匹配")]),a("el-checkbox",{model:{value:e.config.matchOptions.caseSensitive,callback:function(t){e.$set(e.config.matchOptions,"caseSensitive",t)},expression:"config.matchOptions.caseSensitive"}},[e._v("区分大小写")])],1)],1)],1),a("el-form-item",{attrs:{label:"分组日期"}},[a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:8}},[a("el-select",{attrs:{clearable:"",filterable:"",placeholder:"分组日期"},model:{value:e.config.dateField,callback:function(t){e.$set(e.config,"dateField",t)},expression:"config.dateField"}},e._l(e.dateFields,(function(t){return a("el-option",{key:t,attrs:{label:e.getFieldLabel(t),value:t}})})),1)],1),a("el-col",{attrs:{span:15}},[a("el-checkbox",{model:{value:e.config.dateOptions.convertToNumber,callback:function(t){e.$set(e.config.dateOptions,"convertToNumber",t)},expression:"config.dateOptions.convertToNumber"}},[e._v("转换为数字")]),a("el-radio-group",{staticStyle:{display:"flex","line-height":"26px"},model:{value:e.config.dateOptions.formatType,callback:function(t){e.$set(e.config.dateOptions,"formatType",t)},expression:"config.dateOptions.formatType"}},[a("el-radio",{attrs:{label:"year"}},[e._v("按年")]),a("el-radio",{attrs:{label:"month"}},[e._v("按月")]),a("el-radio",{attrs:{label:"day"}},[e._v("按天")])],1)],1)],1)],1),a("el-form-item",{attrs:{label:"显示方式"}},[a("el-checkbox",{staticStyle:{"padding-left":"5px"},model:{value:e.config.showDetails,callback:function(t){e.$set(e.config,"showDetails",t)},expression:"config.showDetails"}},[e._v("含明细")])],1),a("el-table",{staticStyle:{width:"100%"},attrs:{data:e.config.fields,border:""}},[a("el-table-column",{attrs:{align:"center",label:"序号",type:"index",width:"60"}}),a("el-table-column",{attrs:{label:"表头名称","min-width":"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-select",{staticStyle:{width:"100%"},attrs:{filterable:"",placeholder:"选择字段"},on:{change:function(a){return e.handleFieldSelect(t.$index)}},model:{value:t.row.fieldKey,callback:function(a){e.$set(t.row,"fieldKey",a)},expression:"scope.row.fieldKey"}},e._l(e.fieldLabelMap,(function(e,t){return a("el-option",{key:t,attrs:{label:e.name,value:t}})})),1)]}}])}),a("el-table-column",{attrs:{label:"排序",width:"80"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-select",{staticStyle:{width:"100%"},model:{value:t.row.sort,callback:function(a){e.$set(t.row,"sort",a)},expression:"scope.row.sort"}},[a("el-option",{attrs:{label:"-",value:"none"}}),a("el-option",{attrs:{label:"∧",value:"asc"}}),a("el-option",{attrs:{label:"∨ ",value:"desc"}})],1)]}}])}),a("el-table-column",{attrs:{label:"汇总方式",width:"80"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-select",{staticStyle:{width:"100%"},attrs:{disabled:!e.isAggregatable(t.row.fieldKey)},model:{value:t.row.aggregation,callback:function(a){e.$set(t.row,"aggregation",a)},expression:"scope.row.aggregation"}},[a("el-option",{attrs:{label:"-",value:"none"}}),a("el-option",{attrs:{label:"计数",value:"count"}}),a("el-option",{attrs:{label:"求和",value:"sum"}}),a("el-option",{attrs:{label:"平均值",value:"avg"}}),a("el-option",{attrs:{label:"最大值",value:"max"}}),a("el-option",{attrs:{label:"最小值",value:"min"}}),a("el-option",{attrs:{label:"方差",value:"variance"}})],1)]}}])}),a("el-table-column",{attrs:{label:"显示格式",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-select",{staticStyle:{width:"100%"},model:{value:t.row.format,callback:function(a){e.$set(t.row,"format",a)},expression:"scope.row.format"}},[a("el-option",{attrs:{label:"-",value:"none"}}),"date"===e.getFieldDisplay(t.row.fieldKey)?[a("el-option",{attrs:{label:"YYYYMM",value:"YYYYMM"}}),a("el-option",{attrs:{label:"MM-DD",value:"MM-DD"}}),a("el-option",{attrs:{label:"YYYY-MM-DD",value:"YYYY-MM-DD"}})]:e._e(),"number"===e.getFieldDisplay(t.row.fieldKey)?[a("el-option",{attrs:{label:"0.00",value:"decimal"}}),a("el-option",{attrs:{label:"0.00%",value:"percent"}}),a("el-option",{attrs:{label:"¥0.00",value:"currency"}}),a("el-option",{attrs:{label:"$0.00",value:"usd"}})]:e._e()],2)]}}])}),a("el-table-column",{attrs:{align:"center",label:"操作",width:"120"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button-group",[a("el-button",{attrs:{disabled:0===t.$index,size:"mini",type:"text"},on:{click:function(a){return e.moveField(t.$index,"up")}}},[e._v("[∧] ")]),a("el-button",{attrs:{disabled:t.$index===e.config.fields.length-1,size:"mini",type:"text"},on:{click:function(a){return e.moveField(t.$index,"down")}}},[e._v("[∨] ")]),a("el-button",{staticStyle:{color:"red"},attrs:{icon:"el-icon-delete",size:"mini",type:"text"},on:{click:function(a){return e.removeField(t.$index)}}})],1)]}}])})],1),a("div",{staticStyle:{"margin-top":"10px"}},[a("el-button",{attrs:{plain:"",type:"text"},on:{click:e.addField}},[e._v("[ + ]")])],1),a("el-form-item",[a("el-button",{attrs:{type:"primary"},on:{click:e.handleServerAggregate}},[e._v("分类汇总")]),a("el-button",{on:{click:e.resetConfig}},[e._v("重置")])],1)],1)],1)],1),e.showResult?a("el-col",{attrs:{span:14}},[a("el-card",{staticClass:"result-card",scopedSlots:e._u([{key:"header",fn:function(){return[a("div",{staticClass:"header-with-operations"},[a("span",[e._v("汇总结果")]),a("div",{staticClass:"operations"},[a("el-switch",{staticStyle:{"margin-right":"15px"},attrs:{"active-text":"横向","inactive-text":"纵向"},model:{value:e.isLandscape,callback:function(t){e.isLandscape=t},expression:"isLandscape"}}),a("el-button",{attrs:{size:"small"},on:{click:e.printTable}},[e._v("打印")]),a("el-button",{attrs:{size:"small",type:"primary"},on:{click:e.exportToPDF}},[e._v("导出PDF")])],1)])]},proxy:!0}],null,!1,1080603383)},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],ref:"resultTable",staticStyle:{width:"100%"},attrs:{data:e.processedData,"summary-method":e.getSummary,border:"","show-summary":""}},[a("el-table-column",{attrs:{align:e.config.primaryField?e.fieldLabelMap[e.config.primaryField].align:"left",label:e.groupFieldName,width:e.config.primaryField?e.fieldLabelMap[e.config.primaryField].width:""},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(e.formatGroupKey(t.row.groupKey))+" ")]}}],null,!1,2877943199)}),e._l(e.config.fields,(function(t,n){return[t.fieldKey?a("el-table-column",{key:t.fieldKey+"_"+n,attrs:{align:e.getColumnAlign(t.fieldKey),label:e.getResultLabel(t),width:e.getColumnWidth(t.fieldKey)},scopedSlots:e._u([{key:"default",fn:function(a){return[e._v(" "+e._s(e.formatCellValue(a.row[e.getResultProp(t)],t))+" ")]}}],null,!0)}):e._e()]}))],2)],1)],1):e._e()],1),a("el-dialog",{attrs:{visible:e.configDialogVisible,"append-to-body":"",title:"加载配置",width:"550px"},on:{"update:visible":function(t){e.configDialogVisible=t}}},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.configLoading,expression:"configLoading"}],staticStyle:{width:"100%"},attrs:{data:e.savedConfigs},on:{"row-click":e.handleConfigSelect}},[a("el-table-column",{attrs:{label:"配置名称",prop:"name"}}),a("el-table-column",{attrs:{label:"创建时间",prop:"createTime"}}),a("el-table-column",{attrs:{width:"50"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{attrs:{type:"text"},on:{click:function(a){return a.stopPropagation(),e.deleteConfig(t.row)}}},[e._v("删除")])]}}])})],1)],1),a("el-dialog",{attrs:{visible:e.filterDialogVisible,title:"设置筛选条件",width:"650px"},on:{"update:visible":function(t){e.filterDialogVisible=t}}},[a("el-form",{attrs:{model:e.currentFilter,"label-width":"100px"}},[a("el-form-item",{attrs:{label:"字段"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{filterable:"",placeholder:"选择字段"},model:{value:e.currentFilter.field,callback:function(t){e.$set(e.currentFilter,"field",t)},expression:"currentFilter.field"}},e._l(e.fieldLabelMap,(function(e,t){return a("el-option",{key:t,attrs:{label:e.name,value:t}})})),1)],1),a("el-form-item",{attrs:{label:"操作符"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"选择操作符"},model:{value:e.currentFilter.operator,callback:function(t){e.$set(e.currentFilter,"operator",t)},expression:"currentFilter.operator"}},[a("el-option",{attrs:{label:"等于",value:"eq"}}),a("el-option",{attrs:{label:"不等于",value:"ne"}}),a("el-option",{attrs:{label:"大于",value:"gt"}}),a("el-option",{attrs:{label:"大于等于",value:"ge"}}),a("el-option",{attrs:{label:"小于",value:"lt"}}),a("el-option",{attrs:{label:"小于等于",value:"le"}}),a("el-option",{attrs:{label:"包含",value:"contains"}}),a("el-option",{attrs:{label:"开始于",value:"startsWith"}}),a("el-option",{attrs:{label:"结束于",value:"endsWith"}})],1)],1),a("el-form-item",{attrs:{label:"值"}},[a("el-input",{attrs:{placeholder:"输入筛选值"},model:{value:e.currentFilter.value,callback:function(t){e.$set(e.currentFilter,"value",t)},expression:"currentFilter.value"}})],1),a("el-form-item",[a("el-button",{attrs:{type:"primary"},on:{click:e.addFilter}},[e._v("添加筛选条件")])],1)],1),e.config.filters&&e.config.filters.length?a("div",[a("h4",[e._v("已添加的筛选条件")]),a("el-table",{attrs:{data:e.config.filters,border:""}},[a("el-table-column",{attrs:{label:"字段",prop:"field"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(e.getFieldLabel(t.row.field))+" ")]}}],null,!1,3496384076)}),a("el-table-column",{attrs:{label:"操作符",prop:"operator"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(e.getOperatorLabel(t.row.operator))+" ")]}}],null,!1,1753700364)}),a("el-table-column",{attrs:{label:"值",prop:"value"}}),a("el-table-column",{attrs:{label:"操作",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{attrs:{circle:"",icon:"el-icon-delete",size:"mini",type:"danger"},on:{click:function(a){return e.removeFilter(t.$index)}}})]}}],null,!1,3366023889)})],1)],1):e._e(),a("div",{attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(t){e.filterDialogVisible=!1}}},[e._v("关闭")])],1)],1)],1)},i=[],l=a("53ca"),r=a("c7eb"),o=a("1da1"),s=a("2909"),d=a("5530"),c=(a("b64b"),a("4de4"),a("d3b7"),a("99af"),a("b0c0"),a("7db0"),a("14d9"),a("a434"),a("4e82"),a("a9e3"),a("b680"),a("159b"),a("d81d"),a("13d5"),a("ac1f"),a("5319"),a("caad"),a("2532"),a("c1df")),u=a.n(c),g=(a("72f9"),a("c211")),p=a("d67e"),m=a.n(p),f={name:"DataAggregatorBackGround",props:{fieldLabelMap:{type:Object,required:!0,default:function(){return{}}},dataSourceType:{type:String,required:!0,default:"rct"},aggregateFunction:{type:Function,required:!0},configType:{type:String,required:!1}},data:function(){return{configName:"",config:{name:"",primaryField:"",matchOptions:{exact:!0,caseSensitive:!1},dateField:"",dateOptions:{convertToNumber:!1,formatType:"day"},showDetails:!1,fields:[],filters:[]},dateOptions:[{label:"按年",value:"year"},{label:"按月",value:"month"},{label:"按周",value:"week"},{label:"按日",value:"day"},{label:"按时",value:"hour"},{label:"按分",value:"minute"}],aggregationOptions:[{label:"计数",value:"count"},{label:"求和",value:"sum"},{label:"平均值",value:"avg"},{label:"方差",value:"variance"},{label:"最大值",value:"max"},{label:"最小值",value:"min"}],operatorOptions:[{label:"等于",value:"eq"},{label:"不等于",value:"ne"},{label:"大于",value:"gt"},{label:"大于等于",value:"ge"},{label:"小于",value:"lt"},{label:"小于等于",value:"le"},{label:"包含",value:"contains"},{label:"开始于",value:"startsWith"},{label:"结束于",value:"endsWith"}],loading:!1,configDialogVisible:!1,filterDialogVisible:!1,savedConfigs:[],configLoading:!1,isLandscape:!1,showResult:!1,processedData:[],currentFilter:{field:"",operator:"eq",value:""}}},computed:{availableFields:function(){return Object.keys(this.fieldLabelMap)},dateFields:function(){var e=this;return this.availableFields.filter((function(t){return e.fieldLabelMap[t]&&"date"===e.fieldLabelMap[t].display}))},groupFieldName:function(){return this.config.primaryField&&this.config.dateField?"".concat(this.getFieldLabel(this.config.dateField),"+").concat(this.getFieldLabel(this.config.primaryField)):this.config.primaryField?this.getFieldLabel(this.config.primaryField):this.config.dateField?this.getFieldLabel(this.config.dateField):"分组"}},methods:{getFieldLabel:function(e){var t;return(null===(t=this.fieldLabelMap[e])||void 0===t?void 0:t.name)||e},getOperatorLabel:function(e){var t=this.operatorOptions.find((function(t){return t.value===e}));return t?t.label:e},openFilterDialog:function(){this.currentFilter={field:"",operator:"eq",value:""},this.filterDialogVisible=!0},addFilter:function(){this.currentFilter.field?this.currentFilter.value||0===this.currentFilter.value?(this.config.filters||this.$set(this.config,"filters",[]),this.config.filters.push(Object(d["a"])({},this.currentFilter)),this.currentFilter={field:"",operator:"eq",value:""}):this.$message.warning("请输入筛选值"):this.$message.warning("请选择筛选字段")},removeFilter:function(e){this.config.filters.splice(e,1)},getFieldDisplay:function(e){var t=this.fieldLabelMap[e];return t?t.display&&"function"===typeof this[t.display]?"custom":t.display||"text":"text"},isAggregatable:function(e){var t=this.fieldLabelMap[e];return(null===t||void 0===t?void 0:t.aggregated)||!1},addField:function(){this.config.fields.push({fieldKey:"",aggregation:"none",format:"none",sort:"none"})},removeField:function(e){this.config.fields.splice(e,1)},moveField:function(e,t){var a=Object(s["a"])(this.config.fields);if("up"===t&&e>0){var n=[a[e-1],a[e]];a[e]=n[0],a[e-1]=n[1]}else if("down"===t&&e<a.length-1){var i=[a[e+1],a[e]];a[e]=i[0],a[e+1]=i[1]}this.$set(this.config,"fields",a)},handleFieldSelect:function(e){var t=this.config.fields[e],a=this.fieldLabelMap[t.fieldKey];a&&(t.format=this.getDefaultFormat(a.display),t.aggregation=a.aggregated?"sum":"none",t.sort="none")},getDefaultFormat:function(e){switch(e){case"date":return"YYYY-MM-DD";case"number":return"decimal";default:return"none"}},resetConfig:function(){this.config={name:"",primaryField:"",matchOptions:{exact:!0,caseSensitive:!1},dateField:"",dateOptions:{convertToNumber:!1,formatType:"day"},showDetails:!1,fields:[],filters:[]},this.showResult=!1},getDateFormat:function(){switch(this.config.dateOptions.formatType){case"year":return"YYYY";case"month":return"YYYY-MM";case"day":default:return"YYYY-MM-DD"}},handleServerAggregate:function(){var e=this;return Object(o["a"])(Object(r["a"])().mark((function t(){var a,n,i;return Object(r["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(e.config.primaryField||e.config.dateField){t.next=3;break}return e.$message.warning("请至少选择分组依据或分组日期其中之一"),t.abrupt("return");case 3:if(e.config.fields.length){t.next=6;break}return e.$message.warning("请添加至少一个字段"),t.abrupt("return");case 6:if(a=e.config.fields.find((function(e){return!e.fieldKey})),!a){t.next=10;break}return e.$message.warning("请完成所有字段的配置"),t.abrupt("return");case 10:if(e.aggregateFunction){t.next=13;break}return e.$message.error("汇总函数未定义"),t.abrupt("return");case 13:return t.prev=13,e.loading=!0,n={dataSourceType:e.dataSourceType,config:{primaryField:e.config.primaryField,matchOptions:e.config.matchOptions,dateField:e.config.dateField,dateOptions:e.config.dateOptions,showDetails:e.config.showDetails,fields:e.config.fields,filters:e.config.filters||[]}},t.next=18,e.aggregateFunction(n);case 18:i=t.sent,200===i.code?(e.processedData=i.data,e.showResult=!0):e.$message.error(i.msg||"汇总数据失败"),t.next=26;break;case 22:t.prev=22,t.t0=t["catch"](13),console.error("数据汇总失败:",t.t0),e.$message.error("汇总处理失败："+(t.t0.message||"未知错误"));case 26:return t.prev=26,e.loading=!1,t.finish(26);case 29:case"end":return t.stop()}}),t,null,[[13,22,26,29]])})))()},getResultProp:function(e){return e.aggregation&&"none"!==e.aggregation?"".concat(e.fieldKey,"_").concat(e.aggregation):e.fieldKey},getResultLabel:function(e){var t=this.getFieldLabel(e.fieldKey);if(e.aggregation&&"none"!==e.aggregation){var a,n=(null===(a=this.aggregationOptions.find((function(t){return t.value===e.aggregation})))||void 0===a?void 0:a.label)||e.aggregation;return"".concat(t,"(").concat(n,")")}return t},formatCellValue:function(e,t){if(null==e)return"-";var a=this.fieldLabelMap[t.fieldKey];if(!a)return e;if(a.display&&"function"===typeof this[a.display])return this[a.display](e);switch(a.display){case"number":var n=Number(e);if(isNaN(n))return"-";switch(t.format){case"decimal":return n.toFixed(2);case"percent":return(100*n).toFixed(2)+"%";case"currency":return"¥"+n.toFixed(2);case"usd":return"$"+n.toFixed(2);default:return n.toFixed(2)}case"date":return u()(e).format(t.format||"YYYY-MM-DD");case"boolean":return"avg"===t.aggregation?(100*Number(e)).toFixed(2)+"%":e?"是":"否";default:return e}},formatGroupKey:function(e){if("object"===Object(l["a"])(e)&&null!==e&&void 0!==e.primary&&void 0!==e.date){var t=this.fieldLabelMap[this.config.primaryField],a=e.primary;return t&&t.display&&"function"===typeof this[t.display]&&(a=this[t.display](a)),"".concat(e.date," ").concat(a)}if(this.config.primaryField){var n=this.fieldLabelMap[this.config.primaryField];if(n&&n.display&&"function"===typeof this[n.display])return this[n.display](e)}return String(e||"")},getSummary:function(e){var t=this,a=e.columns,n=e.data,i=[];return a.forEach((function(e,a){if(0!==a){var l=a-1,r=t.config.fields[l];if(r&&r.fieldKey)if(r.aggregation&&"none"!==r.aggregation){var o=t.fieldLabelMap[r.fieldKey];if(o)if("number"===o.display||"percentage"===o.display||"function"===typeof t[o.display]){var d=n.map((function(e){var a=t.getResultProp(r),n=Number(e[a]);return isNaN(n)?0:n})).filter((function(e){return!isNaN(e)}));if(0!==d.length){var c=0;switch(r.aggregation){case"sum":c=d.reduce((function(e,t){return e+t}),0);break;case"avg":c=d.reduce((function(e,t){return e+t}),0)/d.length;break;case"max":c=Math.max.apply(Math,Object(s["a"])(d));break;case"min":c=Math.min.apply(Math,Object(s["a"])(d));break;case"variance":var u=d.reduce((function(e,t){return e+t}),0)/d.length;c=d.reduce((function(e,t){return e+Math.pow(t-u,2)}),0)/d.length;break;default:c=d.reduce((function(e,t){return e+t}),0)}"percentage"===o.display||"percent"===o.display?i[a]=t.percentage(c):"decimal"===r.format?i[a]=c.toFixed(2):"percent"===r.format?i[a]=(100*c).toFixed(2)+"%":"currency"===r.format?i[a]="¥"+c.toFixed(2):"usd"===r.format?i[a]="$"+c.toFixed(2):i[a]=c.toFixed(2)}else i[a]=""}else i[a]="";else i[a]=""}else i[a]="";else i[a]=""}else i[a]="合计"})),i},saveConfig:function(){var e=this;return Object(o["a"])(Object(r["a"])().mark((function t(){var a,n;return Object(r["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(t.prev=0,e.config.name){t.next=4;break}return e.$message.warning("请输入速查名称"),t.abrupt("return");case 4:if(e.config.primaryField||e.config.dateField){t.next=7;break}return e.$message.warning("请至少选择分组依据或分组日期其中之一"),t.abrupt("return");case 7:if(e.config.fields.length){t.next=10;break}return e.$message.warning("请添加至少一个字段"),t.abrupt("return");case 10:if(a=e.config.fields.find((function(e){return!e.fieldKey})),!a){t.next=14;break}return e.$message.warning("请完成所有字段的配置"),t.abrupt("return");case 14:return n={name:e.config.name,type:e.configType,config:e.config},t.next=17,Object(g["c"])(n);case 17:e.$message.success("配置保存成功"),t.next=23;break;case 20:t.prev=20,t.t0=t["catch"](0),"cancel"!==t.t0&&e.$message.error("保存配置失败："+(t.t0.message||"未知错误"));case 23:case"end":return t.stop()}}),t,null,[[0,20]])})))()},loadConfigs:function(){var e=this;return Object(o["a"])(Object(r["a"])().mark((function t(){var a,n;return Object(r["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.configLoading=!0,e.configDialogVisible=!0,t.prev=2,t.next=5,Object(g["b"])({configType:e.configType});case 5:a=t.sent,e.savedConfigs=a.rows,t.next=13;break;case 9:t.prev=9,t.t0=t["catch"](2),console.error("加载配置失败:",t.t0),e.$message.error((null===(n=t.t0.response)||void 0===n||null===(n=n.data)||void 0===n?void 0:n.message)||t.t0.message||"加载配置列表失败，请稍后重试");case 13:return t.prev=13,e.configLoading=!1,t.finish(13);case 16:case"end":return t.stop()}}),t,null,[[2,9,13,16]])})))()},handleConfigSelect:function(e){var t=this;return Object(o["a"])(Object(r["a"])().mark((function a(){var n;return Object(r["a"])().wrap((function(a){while(1)switch(a.prev=a.next){case 0:try{n=JSON.parse(e.config),n.name=e.name,t.config=n,t.configDialogVisible=!1,t.$message.success("配置加载成功")}catch(i){console.error("加载配置失败:",i),t.$message.error("加载配置失败："+i.message)}case 1:case"end":return a.stop()}}),a)})))()},deleteConfig:function(e){var t=this;return Object(o["a"])(Object(r["a"])().mark((function a(){return Object(r["a"])().wrap((function(a){while(1)switch(a.prev=a.next){case 0:return a.prev=0,a.next=3,t.$confirm("确认删除该配置？","提示",{type:"warning"});case 3:return a.next=5,Object(g["a"])(e.id);case 5:t.savedConfigs=t.savedConfigs.filter((function(t){return t.id!==e.id})),t.$message.success("配置删除成功"),a.next=12;break;case 9:a.prev=9,a.t0=a["catch"](0),"cancel"!==a.t0&&t.$message.error("删除配置失败："+a.t0.message);case 12:case"end":return a.stop()}}),a,null,[[0,9]])})))()},printTable:function(){var e=window.open("","_blank"),t=this.$refs.resultTable.$el.cloneNode(!0),a="汇总数据",n=((new Date).toLocaleDateString(),'\n        <div class="company-header">\n          <div class="company-logo">\n            <img src="/logo.png" alt="Rich Shipping Logo" />\n            <div class="company-name">\n              <div class="company-name-cn">广州瑞旗国际货运代理有限公司</div>\n              <div class="company-name-en">GUANGZHOU RICH SHIPPING INT\'L CO.,LTD.</div>\n            </div>\n          </div>\n          <div class="document-title">\n            <div class="title-cn"></div>\n            <div class="title-en"></div>\n          </div>\n        </div>\n      ');e.document.write('\n        <html lang="">\n          <head>\n            <title>'.concat(a,"</title>\n            <style>\n              /* 基础样式 */\n              body {\n                margin: 0;\n                padding: 0;\n                font-family: Arial, sans-serif;\n              }\n\n              /* 打印样式 - 必须放在这里才能生效 */\n              @media print {\n                @page {\n                  size: ").concat(this.isLandscape?"landscape":"portrait",';\n                  margin: 1.5cm 1cm 1cm 1cm;\n                }\n\n                /* 重要：使用重复表头技术 */\n                thead {\n                  display: table-header-group;\n                }\n\n                /* 页眉作为表格的一部分，放在thead中 */\n                .page-header {\n                  display: table-header-group;\n                }\n\n                /* 内容部分 */\n                .page-content {\n                  display: table-row-group;\n                }\n\n                /* 避免元素内部分页 */\n                .company-header, .header-content {\n                  page-break-inside: avoid;\n                }\n\n                /* 表格样式 */\n                table.main-table {\n                  width: 100%;\n                  border-collapse: collapse;\n                  border: none;\n                }\n\n                /* 确保表头在每页都显示 */\n                table.data-table thead {\n                  display: table-header-group;\n                }\n\n                /* 避免行内分页 */\n                table.data-table tr {\n                  page-break-inside: avoid;\n                }\n              }\n\n              /* 表格样式 */\n              table.data-table {\n                border-collapse: collapse;\n                width: 100%;\n                margin-top: 20px;\n                table-layout: fixed;\n              }\n\n              table.data-table th, table.data-table td {\n                border: 1px solid #ddd;\n                padding: 8px;\n                text-align: left;\n                font-size: 12px;\n                word-wrap: break-word;\n                word-break: break-all;\n                white-space: normal;\n              }\n\n              table.data-table th {\n                background-color: #f2f2f2;\n              }\n\n              /* Element UI 表格样式模拟 */\n              .el-table {\n                border-collapse: collapse;\n                width: 100%;\n                table-layout: fixed;\n              }\n\n              .el-table th, .el-table td {\n                border: 1px solid #ddd;\n                padding: 8px;\n                text-align: left;\n                font-size: 12px;\n                word-wrap: break-word;\n                word-break: break-all;\n                white-space: normal;\n              }\n\n              .el-table th {\n                background-color: #f2f2f2;\n                font-weight: bold;\n              }\n\n              .el-table__footer {\n                background-color: #f8f8f9;\n                font-weight: bold;\n              }\n\n              .el-table__footer td {\n                border: 1px solid #ddd;\n                padding: 8px;\n              }\n\n              /* 公司标题和标志样式 */\n              .company-header {\n                display: flex;\n                justify-content: space-between;\n                align-items: center;\n                border-bottom: 2px solid #000;\n                padding-bottom: 10px;\n                width: 100%;\n              }\n\n              .company-logo {\n                display: flex;\n                align-items: center;\n              }\n\n              .company-logo img {\n                height: 50px;\n                margin-right: 10px;\n              }\n\n              .company-name {\n                display: flex;\n                flex-direction: column;\n              }\n\n              .company-name-cn {\n                font-size: 18px;\n                font-weight: bold;\n                color: #ff0000;\n              }\n\n              .company-name-en {\n                font-size: 14px;\n              }\n\n              .document-title {\n                text-align: right;\n              }\n\n              .title-cn {\n                font-size: 18px;\n                font-weight: bold;\n              }\n\n              .title-en {\n                font-size: 16px;\n                font-weight: bold;\n              }\n\n              /* 清除表格边框 */\n              table.main-table, table.main-table td {\n                border: none;\n              }\n\n              /* 页眉容器 */\n              .header-container {\n                width: 100%;\n                margin-bottom: 20px;\n              }\n            </style>\n          </head>\n          <body>\n            \x3c!-- 使用表格布局确保页眉在每页重复 --\x3e\n            <table class="main-table">\n              <thead class="page-header">\n                <tr>\n                  <td>\n                    <div class="header-container">\n                      ').concat(n,'\n                    </div>\n                  </td>\n                </tr>\n              </thead>\n              <tbody class="page-content">\n                <tr>\n                  <td>\n                    \x3c!-- 保留原始表格的类名并添加data-table类 --\x3e\n                    ').concat(t.outerHTML.replace("<table",'<table class="el-table data-table"'),"\n                  </td>\n                </tr>\n              </tbody>\n            </table>\n          </body>\n        </html>\n      ")),e.document.close(),setTimeout((function(){try{e.focus(),e.print()}catch(t){console.error("打印过程中发生错误:",t)}}),1e3)},exportToPDF:function(){var e=this;return Object(o["a"])(Object(r["a"])().mark((function t(){var a,n;return Object(r["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,e.loading=!0,a=e.$refs.resultTable.$el,n={margin:[.8,.8,.8,.8],filename:"汇总数据.pdf",image:{type:"jpeg",quality:.98},html2canvas:{scale:2},jsPDF:{unit:"in",format:"a3",orientation:e.isLandscape?"landscape":"portrait"},pagebreak:{mode:["avoid-all","css","legacy"]},header:[{text:"汇总数据",style:"headerStyle"},{text:(new Date).toLocaleDateString(),style:"headerStyle",alignment:"right"}],footer:{height:"20px",contents:{default:'<span style="float:right">{{page}}/{{pages}}</span>'}}},t.next=6,m()().set(n).from(a).save();case 6:e.$message.success("PDF导出成功"),t.next=12;break;case 9:t.prev=9,t.t0=t["catch"](0),e.$message.error("PDF导出失败："+t.t0.message);case 12:return t.prev=12,e.loading=!1,t.finish(12);case 15:case"end":return t.stop()}}),t,null,[[0,9,12,15]])})))()},getName:function(e){if(null!==e){var t=this.$store.state.data.allRsStaffList.filter((function(t){return t.staffId==e}))[0];if(t&&void 0!==t)return t.staffShortName+t.staffFamilyEnName}return""},percentage:function(e){if(null==e||""===e)return"-";if("string"===typeof e&&e.includes("%"))return e;var t=Number(e);if(isNaN(t))return"-";var a=t>0&&t<=1,n=a?100*t:t;return n.toFixed(2)+"%"},getColumnAlign:function(e){var t=this.fieldLabelMap[e];return t&&t.align?t.align:"left"},getColumnWidth:function(e){var t=this.fieldLabelMap[e];return t&&t.width?t.width:""}}},h=f,b=(a("ba54"),a("2877")),y=Object(b["a"])(h,n,i,!1,null,"641cae22",null);t["default"]=y.exports},e881:function(e,t,a){e.exports=a.p+"static/img/pageHead.png"},f870:function(e,t,a){"use strict";a.r(t);var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-dialog",{attrs:{visible:e.visible,width:"220mm"},on:{"update:visible":function(t){e.visible=t},cancel:e.hideModal}},[a("template",{slot:"title"},[a("el-row",{attrs:{gutter:10}},[a("div",{staticStyle:{"margin-right":"20px"}},[e._v("打印预览")]),a("el-button",{attrs:{loading:e.waitShowPrinter,icon:"printer",type:"primary"},on:{click:function(t){return t.stopPropagation(),e.print(t)}}},[e._v("打印")]),a("el-button",{attrs:{icon:"printer",type:"primary"},on:{click:function(t){return t.stopPropagation(),e.toPdf(t)}}},[e._v("pdf")])],1)],1),a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.spinning,expression:"spinning"}],staticStyle:{"min-height":"120px"}},[a("div",{attrs:{id:"preview_content_design"}})])],2)},i=[],l=a("c1df"),r=a.n(l),o={name:"printPreview",props:["customWidth"],data:function(){return{visible:!1,spinning:!0,waitShowPrinter:!1,width:this.customWidth?this.customWidth:"220mm",hiprintTemplate:{},printData:{},pdfName:null}},computed:{},watch:{},created:function(){},mounted:function(){},methods:{hideModal:function(){this.visible=!1},show:function(e,t){var a=this,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"210";this.visible=!0,this.spinning=!0,this.pdfName=t.pdfName?t.pdfName:r()().format("YYYYMMDD"),this.width=e.editingPanel?e.editingPanel.width:n,this.hiprintTemplate=e,this.printData=t,setTimeout((function(){$("#preview_content_design").html(e.getHtml(t)),a.spinning=!1}),500)},print:function(e,t){var a=this;this.hiprintTemplate=e||this.hiprintTemplate,this.printData=t||this.printData,this.waitShowPrinter=!0,this.hiprintTemplate.print(this.printData,{},{callback:function(){console.log("callback"),a.waitShowPrinter=!1}})},toPdf:function(){this.hiprintTemplate.toPdf(this.printData,this.pdfName)}}},s=o,d=(a("4ba95"),a("2877")),c=Object(d["a"])(s,n,i,!1,null,"f7e23638",null);t["default"]=c.exports}}]);