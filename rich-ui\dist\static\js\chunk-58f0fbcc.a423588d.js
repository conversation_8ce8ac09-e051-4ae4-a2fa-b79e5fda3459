(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-58f0fbcc","chunk-7587eac6","chunk-2d0d69a4"],{"2de3":function(e,t,o){"use strict";o.r(t);var n,r=function(){var e=this,t=e.$createElement,o=e._self._c||t;return o("div",{staticClass:"app-container"},[o("el-row",{attrs:{gutter:20}},[o("el-col",{attrs:{span:e.showLeft}},[o("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],ref:"queryForm",staticClass:"query",attrs:{inline:!0,model:e.queryParams,size:"mini"}},[o("el-form-item",{attrs:{label:"单号",prop:"outboundNo"}},[o("el-input",{attrs:{clearable:"",placeholder:"出仓单号"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.outboundNo,callback:function(t){e.$set(e.queryParams,"outboundNo",t)},expression:"queryParams.outboundNo"}})],1),o("el-form-item",{attrs:{label:"客户",prop:"clientCode"}},[o("el-input",{attrs:{clearable:"",placeholder:"客户代码"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.clientCode,callback:function(t){e.$set(e.queryParams,"clientCode",t)},expression:"queryParams.clientCode"}})],1),o("el-form-item",{attrs:{label:"名称",prop:"clientName"}},[o("el-input",{attrs:{clearable:"",placeholder:"客户名称"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.clientName,callback:function(t){e.$set(e.queryParams,"clientName",t)},expression:"queryParams.clientName"}})],1),o("el-form-item",{attrs:{label:"柜号",prop:"containerNo"}},[o("el-input",{attrs:{clearable:"",placeholder:"柜号"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.containerNo,callback:function(t){e.$set(e.queryParams,"containerNo",t)},expression:"queryParams.containerNo"}})],1),o("el-form-item",{attrs:{label:"封号",prop:"sealNo"}},[o("el-input",{attrs:{clearable:"",placeholder:"封号"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.sealNo,callback:function(t){e.$set(e.queryParams,"sealNo",t)},expression:"queryParams.sealNo"}})],1),o("el-form-item",{attrs:{label:"日期",prop:"outboundDate"}},[o("el-date-picker",{staticStyle:{width:"100%"},attrs:{clearable:"",placeholder:"出仓日期",type:"date","value-format":"yyyy-MM-dd"},model:{value:e.queryParams.outboundDate,callback:function(t){e.$set(e.queryParams,"outboundDate",t)},expression:"queryParams.outboundDate"}})],1),o("el-form-item",[o("el-button",{attrs:{icon:"el-icon-search",size:"mini",type:"primary"},on:{click:e.handleQuery}},[e._v("搜索")]),o("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1)],1),o("el-col",{attrs:{span:e.showRight}},[o("el-row",{staticClass:"mb8",attrs:{gutter:10}},[o("el-col",{attrs:{span:1.5}},[o("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:inventory:edit"],expression:"['system:inventory:edit']"}],attrs:{icon:"el-icon-edit",plain:"",size:"mini",type:"success"},on:{click:function(t){return e.handlePreOutbound()}}},[e._v("操作预出仓 ")])],1),o("el-col",{attrs:{span:1.5}},[o("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:inventory:edit"],expression:"['system:inventory:edit']"}],attrs:{icon:"el-icon-edit",plain:"",size:"mini",type:"success"},on:{click:function(t){return e.handleDirectOutbound()}}},[e._v("直接出仓 ")])],1),o("el-col",{attrs:{span:1.5}},[o("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:inventory:edit"],expression:"['system:inventory:edit']"}],attrs:{icon:"el-icon-edit",plain:"",size:"mini",type:"success"},on:{click:function(t){return e.handleRentSettlement()}}},[e._v("结算仓租 ")])],1),o("right-toolbar",{attrs:{showSearch:e.showSearch},on:{"update:showSearch":function(t){e.showSearch=t},"update:show-search":function(t){e.showSearch=t},queryTable:e.getList}})],1),o("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.outboundrecordList},on:{"selection-change":e.handleSelectionChange,"row-dblclick":function(t){return e.handleOutbound(t)}}},[o("el-table-column",{attrs:{align:"center",type:"selection",width:"28"}}),o("el-table-column",{attrs:{align:"center",label:"出仓单号",prop:"outboundNo"}}),o("el-table-column",{attrs:{align:"center",label:"客户代码",prop:"clientCode"}}),o("el-table-column",{attrs:{align:"center",label:"客户名称",prop:"clientName"}}),o("el-table-column",{attrs:{align:"center",label:"操作员",prop:"operator"}}),o("el-table-column",{attrs:{align:"center",label:"柜型",prop:"containerType"}}),o("el-table-column",{attrs:{align:"center",label:"柜号",prop:"containerNo"}}),o("el-table-column",{attrs:{align:"center",label:"封号",prop:"sealNo"}}),o("el-table-column",{attrs:{align:"center",label:"出仓日期",prop:"outboundDate",width:"180"},scopedSlots:e._u([{key:"default",fn:function(t){return[o("span",[e._v(e._s(e.parseTime(t.row.outboundDate,"{y}-{m}-{d}")))])]}}])}),o("el-table-column",{attrs:{align:"center",label:"仓库报价",prop:"warehouseQuote"}}),o("el-table-column",{attrs:{align:"center",label:"工人装柜费",prop:"workerLoadingFee"}}),o("el-table-column",{attrs:{align:"center",label:"仓管代收",prop:"warehouseCollection"}}),o("el-table-column",{attrs:{align:"center",label:"代收备注",prop:"collectionNotes"}}),o("el-table-column",{attrs:{align:"center",label:"总箱数",prop:"totalBoxes"}}),o("el-table-column",{attrs:{align:"center",label:"总毛重",prop:"totalGrossWeight"}}),o("el-table-column",{attrs:{align:"center",label:"总体积",prop:"totalVolume"}}),o("el-table-column",{attrs:{align:"center",label:"总行数",prop:"totalRows"}}),o("el-table-column",{attrs:{align:"center",label:"已收入仓费",prop:"receivedStorageFee"}}),o("el-table-column",{attrs:{align:"center",label:"未收卸货费",prop:"unpaidUnloadingFee"}}),o("el-table-column",{attrs:{align:"center",label:"未收打包费",prop:"unpaidPackagingFee"}}),o("el-table-column",{attrs:{align:"center",label:"物流代垫费",prop:"logisticsAdvanceFee"}}),o("el-table-column",{attrs:{align:"center",label:"租金平衡费",prop:"rentalBalanceFee"}}),o("el-table-column",{attrs:{align:"center",label:"超期仓租",prop:"overdueRent"}}),o("el-table-column",{attrs:{align:"center",label:"免堆天数",prop:"freeStackDays"}}),o("el-table-column",{attrs:{align:"center",label:"超期单价",prop:"overdueUnitPrice"}}),o("el-table-column",{attrs:{align:"center","class-name":"small-padding fixed-width",label:"操作"},scopedSlots:e._u([{key:"default",fn:function(t){return[o("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:outboundrecord:edit"],expression:"['system:outboundrecord:edit']"}],staticStyle:{"margin-right":"-8px"},attrs:{icon:"el-icon-edit",size:"mini",type:"success"},on:{click:function(o){return e.handleUpdate(t.row)}}},[e._v("修改 ")]),o("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:outboundrecord:remove"],expression:"['system:outboundrecord:remove']"}],staticStyle:{"margin-right":"-8px"},attrs:{icon:"el-icon-delete",size:"mini",type:"danger"},on:{click:function(o){return e.handleDelete(t.row)}}},[e._v("删除 ")])]}}])})],1),o("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total>0"}],attrs:{limit:e.queryParams.pageSize,page:e.queryParams.pageNum,total:e.total},on:{"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},pagination:e.getList}})],1)],1),o("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"},{name:"dialogDragWidth",rawName:"v-dialogDragWidth"}],attrs:{"close-on-click-modal":!1,"modal-append-to-body":!1,title:"出库单",visible:e.openOutbound,"append-to-body":"",width:"70%"},on:{"update:visible":function(t){e.openOutbound=t}}},[o("el-form",{ref:"outboundForm",staticClass:"edit",attrs:{model:e.outboundForm,rules:e.rules,"label-width":"80px"}},[o("el-row",{attrs:{gutter:10}},[o("el-row",{attrs:{gutter:10}},[o("el-col",[o("el-col",{attrs:{span:6}},[o("el-form-item",{attrs:{label:"出仓单号",prop:"outboundNo"}},[o("el-input",{staticClass:"disable-form",attrs:{disabled:"",placeholder:"出仓单号"},model:{value:e.outboundForm.outboundNo,callback:function(t){e.$set(e.outboundForm,"outboundNo",t)},expression:"outboundForm.outboundNo"}})],1)],1),o("el-col",{attrs:{span:6}},[o("el-form-item",{attrs:{label:"客户单号",prop:"outboundNo"}},[o("el-input",{attrs:{placeholder:"客户单号"},model:{value:e.outboundForm.customerOrderNo,callback:function(t){e.$set(e.outboundForm,"customerOrderNo",t)},expression:"outboundForm.customerOrderNo"}})],1)],1),o("el-col",{attrs:{span:6}},[o("el-form-item",{attrs:{label:"客户代码",prop:"outboundNo"}},[o("tree-select",{attrs:{flat:!1,multiple:!1,pass:e.outboundForm.clientCode,placeholder:"客户代码",type:"warehouseClient"},on:{return:function(t){e.outboundForm.clientCode=t},returnData:function(t){return e.outboundClient(t)}}})],1)],1),o("el-col",{attrs:{span:6}},[o("el-form-item",{attrs:{label:"客户名称",prop:"outboundNo"}},[o("el-input",{staticClass:"disable-form",attrs:{disabled:"",placeholder:"客户名称"},model:{value:e.outboundForm.clientName,callback:function(t){e.$set(e.outboundForm,"clientName",t)},expression:"outboundForm.clientName"}})],1)],1)],1)],1),o("el-row",{attrs:{gutter:10}},[o("el-col",[o("el-col",{attrs:{span:6}},[o("el-form-item",{attrs:{label:"计划出仓",prop:"inboundDate"}},[o("el-date-picker",{staticStyle:{width:"100%"},attrs:{clearable:"",placeholder:"计划出仓日期",type:"date","value-format":"yyyy-MM-dd"},model:{value:e.outboundForm.plannedOutboundDate,callback:function(t){e.$set(e.outboundForm,"plannedOutboundDate",t)},expression:"outboundForm.plannedOutboundDate"}})],1)],1),o("el-col",{attrs:{span:6}},[o("el-form-item",{attrs:{label:"出仓方式",prop:"outboundType"}},[o("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"出仓方式"},model:{value:e.outboundForm.outboundType,callback:function(t){e.$set(e.outboundForm,"outboundType",t)},expression:"outboundForm.outboundType"}},[o("el-option",{attrs:{label:"整柜",value:"整柜"}}),o("el-option",{attrs:{label:"散货",value:"散货"}}),o("el-option",{attrs:{label:"快递",value:"快递"}}),o("el-option",{attrs:{label:"其他",value:"其他"}})],1)],1)],1),o("el-col",{attrs:{span:6}},[o("el-form-item",{attrs:{label:"柜型",prop:"containerType"}},[o("el-select",{staticStyle:{width:"100%"},on:{change:e.selectContainerType},model:{value:e.outboundForm.containerType,callback:function(t){e.$set(e.outboundForm,"containerType",t)},expression:"outboundForm.containerType"}},[o("el-option",{attrs:{label:"20GP",value:"20GP"}}),o("el-option",{attrs:{label:"20OT",value:"20OT"}}),o("el-option",{attrs:{label:"20FR",value:"20FR"}}),o("el-option",{attrs:{label:"TANK",value:"TANK"}}),o("el-option",{attrs:{label:"40GP",value:"40GP"}}),o("el-option",{attrs:{label:"40HQ",value:"40HQ"}}),o("el-option",{attrs:{label:"40NOR",value:"40NOR"}}),o("el-option",{attrs:{label:"40OT",value:"40OT"}}),o("el-option",{attrs:{label:"40FR",value:"40FR"}}),o("el-option",{attrs:{label:"40RH",value:"40RH"}}),o("el-option",{attrs:{label:"45HQ",value:"45HQ"}})],1)],1)],1),o("el-col",{attrs:{span:6}},[o("el-form-item",{attrs:{label:"货物类型",prop:"cargoType"}},[o("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择货物类型"},model:{value:e.form.cargoType,callback:function(t){e.$set(e.form,"cargoType",t)},expression:"form.cargoType"}},[o("el-option",{attrs:{label:"普货",value:"普货"}}),o("el-option",{attrs:{label:"大件",value:"大件"}}),o("el-option",{attrs:{label:"鲜活",value:"鲜活"}}),o("el-option",{attrs:{label:"危品",value:"危品"}}),o("el-option",{attrs:{label:"冷冻",value:"冷冻"}}),o("el-option",{attrs:{label:"标记",value:"标记"}})],1)],1)],1)],1)],1),o("el-row",{attrs:{gutter:10}},[o("el-col",[o("el-col",{attrs:{span:6}},[o("el-form-item",{attrs:{label:"柜号",prop:"containerNo"}},[o("el-input",{attrs:{placeholder:"柜号"},model:{value:e.outboundForm.containerNo,callback:function(t){e.$set(e.outboundForm,"containerNo",t)},expression:"outboundForm.containerNo"}})],1)],1),o("el-col",{attrs:{span:6}},[o("el-form-item",{attrs:{label:"封号",prop:"sealNo"}},[o("el-input",{attrs:{placeholder:"封号"},model:{value:e.outboundForm.sealNo,callback:function(t){e.$set(e.outboundForm,"sealNo",t)},expression:"outboundForm.sealNo"}})],1)],1),o("el-col",{attrs:{span:6}},[o("el-form-item",{attrs:{label:"车牌",prop:"plateNumber"}},[o("el-input",{attrs:{placeholder:"车牌"},model:{value:e.outboundForm.plateNumber,callback:function(t){e.$set(e.outboundForm,"plateNumber",t)},expression:"outboundForm.plateNumber"}})],1)],1),o("el-col",{attrs:{span:6}},[o("el-form-item",{attrs:{label:"司机电话",prop:"driverPhone"}},[o("el-input",{attrs:{placeholder:"司机电话"},model:{value:e.outboundForm.driverPhone,callback:function(t){e.$set(e.outboundForm,"driverPhone",t)},expression:"outboundForm.driverPhone"}})],1)],1)],1)],1),o("el-row",{attrs:{gutter:10}},[o("el-col",[o("el-col",{attrs:{span:6}},[o("el-form-item",{attrs:{label:"仓库报价",prop:"warehouseQuote"}},[o("el-input",{staticClass:"number",attrs:{placeholder:"仓库报价"},model:{value:e.outboundForm.warehouseQuote,callback:function(t){e.$set(e.outboundForm,"warehouseQuote",t)},expression:"outboundForm.warehouseQuote"}})],1)],1),o("el-col",{attrs:{span:6}},[o("el-form-item",{attrs:{label:"仓管代收",prop:"outboundNotes"}},[o("el-input",{staticClass:"number",attrs:{placeholder:"仓管代收"},model:{value:e.outboundForm.warehouseCollection,callback:function(t){e.$set(e.outboundForm,"warehouseCollection",t)},expression:"outboundForm.warehouseCollection"}})],1)],1),o("el-col",{attrs:{span:6}},[o("el-form-item",{attrs:{label:"工人装柜费",prop:"workerLoadingFee"}},[o("el-input",{staticClass:"number",attrs:{placeholder:"工人装柜费"},model:{value:e.outboundForm.workerLoadingFee,callback:function(t){e.$set(e.outboundForm,"workerLoadingFee",t)},expression:"outboundForm.workerLoadingFee"}})],1)],1),o("el-col",{attrs:{span:6}},[o("el-form-item",{attrs:{label:"仓管代付",prop:"outboundNotes"}},[o("el-input",{staticClass:"number",attrs:{placeholder:"仓管代收"},model:{value:e.outboundForm.warehousePay,callback:function(t){e.$set(e.outboundForm,"warehousePay",t)},expression:"outboundForm.warehousePay"}})],1)],1)],1)],1),o("el-row",{attrs:{gutter:10}},[o("el-col",[o("el-col",{attrs:{span:12}},[o("el-form-item",{attrs:{label:"操作要求",prop:"operationRequirement"}},[o("el-input",{attrs:{autosize:{minRows:4},maxlength:"250",placeholder:"内容","show-word-limit":"",type:"textarea"},model:{value:e.outboundForm.operationRequirement,callback:function(t){e.$set(e.outboundForm,"operationRequirement",t)},expression:"outboundForm.operationRequirement"}})],1)],1),o("el-col",{attrs:{span:12}},[o("el-form-item",{attrs:{label:"仓管指示",prop:"outboundNote"}},[o("el-input",{attrs:{autosize:{minRows:4},maxlength:"250",placeholder:"内容","show-word-limit":"",type:"textarea"},model:{value:e.outboundForm.outboundNote,callback:function(t){e.$set(e.outboundForm,"outboundNote",t)},expression:"outboundForm.outboundNote"}})],1)],1)],1)],1),o("el-row",{attrs:{gutter:10}},[o("el-col",[o("el-col",{attrs:{span:6}},[o("el-form-item",{attrs:{label:"操作员",prop:"operator"}},[o("el-input",{staticClass:"disable-form",attrs:{disabled:"",placeholder:"操作员"},model:{value:e.outboundForm.operator,callback:function(t){e.$set(e.outboundForm,"operator",t)},expression:"outboundForm.operator"}})],1)],1),o("el-col",{attrs:{span:6}},[o("el-form-item",{attrs:{label:"下单日期",prop:"orderDate"}},[o("el-date-picker",{staticClass:"disable-form",staticStyle:{width:"100%"},attrs:{clearable:"",disabled:"",placeholder:"出仓日期",type:"date","value-format":"yyyy-MM-dd"},model:{value:e.outboundForm.orderDate,callback:function(t){e.$set(e.outboundForm,"orderDate",t)},expression:"outboundForm.orderDate"}})],1)],1),o("el-col",{attrs:{span:6}},[o("el-form-item",{attrs:{label:"出仓经手人",prop:"outboundHandler"}},[o("el-input",{staticClass:"disable-form",attrs:{disabled:"",placeholder:"出仓经手人"},model:{value:e.outboundForm.outboundHandler,callback:function(t){e.$set(e.outboundForm,"outboundHandler",t)},expression:"outboundForm.outboundHandler"}})],1)],1),o("el-col",{attrs:{span:6}},[o("el-row",[o("el-col",{attrs:{span:12}},[o("el-button",{attrs:{disabled:null===e.outboundForm.clientCode,type:"primary"},on:{click:e.warehouseConfirm}},[e._v(" "+e._s("仓管确认")+" ")])],1),o("el-col",{attrs:{span:12}},[o("el-button",{attrs:{disabled:null===e.outboundForm.clientCode,type:"primary"},on:{click:e.loadPreOutboundInventoryList}},[e._v(" "+e._s("加载待出库")+" ")])],1)],1)],1)],1)],1)],1),o("el-row",{attrs:{gutter:10}},[o("el-col",[o("el-col",[o("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.preOutboundInventoryListLoading,expression:"preOutboundInventoryListLoading"}],ref:"table",staticStyle:{width:"100%"},attrs:{data:e.preOutboundInventoryList,"summary-method":e.getSummaries,"max-height":"300","show-summary":"",load:e.loadChildInventory,"tree-props":{children:"children",hasChildren:"hasChildren"},lazy:"","element-loading-text":"加载中...","row-key":"inventoryId"},on:{"selection-change":e.handleOutboundSelectionChange}},[o("el-table-column",{attrs:{align:"center",fixed:"",type:"selection",width:"28"}}),o("el-table-column",{attrs:{align:"center",fixed:"",label:"序号",type:"index",width:"28"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(t.$index+1)+" ")]}}])}),o("el-table-column",{attrs:{align:"center",label:"入仓流水号",prop:"inboundSerialNo",width:"120"},scopedSlots:e._u([{key:"header",fn:function(t){return[o("el-input",{attrs:{clearable:"",placeholder:"输入流水号搜索",size:"mini"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleSearchEnter(t)}},model:{value:e.search,callback:function(t){e.search=t},expression:"search"}})]}}])}),o("el-table-column",{attrs:{align:"center",label:"部分出库",prop:"inboundDate",width:"50"},scopedSlots:e._u([{key:"default",fn:function(t){return[o("el-switch",{model:{value:t.row.partialOutboundFlag,callback:function(o){e.$set(t.row,"partialOutboundFlag",o)},expression:"scope.row.partialOutboundFlag"}})]}}])}),o("el-table-column",{attrs:{align:"center",label:"货物明细"},scopedSlots:e._u([{key:"default",fn:function(t){return[o("el-popover",{attrs:{disabled:0==t.row.partialOutboundFlag,trigger:"click",width:"800"}},[o("el-table",{attrs:{data:t.row.rsCargoDetailsList},on:{"selection-change":function(o){return e.handleOutboundCargoDetailSelectionChange(o,t.row)}}},[o("el-table-column",{attrs:{align:"center",type:"selection",width:"28"}}),o("el-table-column",{attrs:{label:"唛头",prop:"shippingMark",width:"150"}}),o("el-table-column",{attrs:{label:"货名",prop:"itemName",width:"150"}}),o("el-table-column",{attrs:{label:"箱数",prop:"boxCount"},scopedSlots:e._u([{key:"default",fn:function(t){return[o("el-input",{attrs:{disabled:!e.isRowSelected(t.row)},model:{value:t.row.boxCount,callback:function(o){e.$set(t.row,"boxCount",o)},expression:"scope.row.boxCount"}})]}}],null,!0)}),o("el-table-column",{attrs:{label:"包装类型",prop:"packageType"}}),o("el-table-column",{attrs:{label:"单件长",prop:"unitLength"}}),o("el-table-column",{attrs:{label:"单件宽",prop:"unitWidth"}}),o("el-table-column",{attrs:{label:"单件高",prop:"unitHeight"}}),o("el-table-column",{attrs:{label:"体积小计",prop:"unitVolume"}}),o("el-table-column",{attrs:{label:"毛重小计",prop:"unitGrossWeight"}}),o("el-table-column",{attrs:{label:"破损标志",prop:"damageStatus"}})],1),o("el-button",{staticStyle:{margin:"0",padding:"5px"},attrs:{slot:"reference"},slot:"reference"},[e._v("查看")])],1)]}}])}),o("el-table-column",{attrs:{align:"center",label:"最新计租日",prop:"inboundDate",width:"80"},scopedSlots:e._u([{key:"default",fn:function(t){return[o("span",[e._v(e._s(e.parseTime(t.row.rentalSettlementDate,"{y}-{m}-{d}")))])]}}])}),o("el-table-column",{attrs:{align:"center",label:"货代单号",prop:"forwarderNo","show-overflow-tooltip":""}}),o("el-table-column",{attrs:{align:"center",label:"客户代码",prop:"clientCode"}}),o("el-table-column",{attrs:{align:"center",label:"箱数",prop:"totalBoxes"}}),o("el-table-column",{attrs:{align:"center",label:"毛重",prop:"totalGrossWeight"}}),o("el-table-column",{attrs:{align:"center",label:"体积",prop:"totalVolume"}}),o("el-table-column",{attrs:{align:"center",label:"已收供应商",prop:"receivedSupplier"}}),o("el-table-column",{attrs:{align:"center",label:"已收入仓费",prop:"receivedStorageFee"}}),o("el-table-column",{attrs:{align:"center",label:"补收入仓费",prop:"additionalStorageFee"}}),o("el-table-column",{attrs:{align:"center",label:"补收卸货费",prop:"unpaidUnloadingFee"}}),o("el-table-column",{attrs:{align:"center",label:"应付卸货费",prop:"receivedUnloadingFee"}}),o("el-table-column",{attrs:{align:"center",label:"补收打包费",prop:"unpaidPackingFee"}}),o("el-table-column",{attrs:{align:"center",label:"应付打包费",prop:"receivedPackingFee"}}),o("el-table-column",{attrs:{align:"center",label:"物流代垫费",prop:"logisticsAdvanceFee"}}),o("el-table-column",{attrs:{align:"center",label:"免堆期",prop:"freeStackPeriod"}}),o("el-table-column",{attrs:{align:"center",label:"超期租金单价",prop:"overdueRentalUnitPrice"}}),o("el-table-column",{attrs:{align:"center",label:"超租天数",prop:"rentalDays"}}),o("el-table-column",{attrs:{align:"center",label:"超期租金",prop:"overdueRentalFee"}})],1)],1)],1)],1),o("el-row",[o("el-col",{attrs:{span:4}},[o("span",[e._v("未收客户："+e._s(e.outboundForm.unreceivedFromCustomer))])]),o("el-col",{attrs:{span:4}},[o("span",[e._v("已收客户："+e._s(e.outboundForm.receivedFromCustomer))])]),o("el-col",{attrs:{span:4}},[o("span",[e._v("应收客户余额："+e._s(e.outboundForm.customerReceivableBalance))])]),o("el-col",{attrs:{span:4}},[o("span",[e._v("应付工人："+e._s(e.outboundForm.payableToWorker))])])],1),o("el-row",[o("el-col",{attrs:{span:4}},[o("span",[e._v("本票销售额："+e._s(e.outboundForm.promissoryNoteSales))])]),o("el-col",{attrs:{span:4}},[o("span",[e._v("本票成本："+e._s(e.outboundForm.promissoryNoteCost))])]),o("el-col",{attrs:{span:4}},[o("span",[e._v("本票结余："+e._s(e.outboundForm.promissoryNoteGrossProfit))])]),o("el-col",{attrs:{span:4}},[o("span",[e._v("已收供应商总额："+e._s(e.outboundForm.receivedFromSupplier))])])],1)],1),o("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[o("el-button",{on:{click:e.printOutboundPlant}},[e._v("打印出仓计划")]),0===e.outboundType?o("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.outboundConfirm(0)}}},[e._v("确定预出仓")]):o("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.outboundConfirm(e.outboundType)}}},[e._v(e._s(3===e.outboundType?"结 算":"出 仓"))]),o("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("保存")]),o("el-button",{on:{click:function(t){e.openOutbound=!1}}},[e._v("关 闭")])],1)],1),o("print-preview",{ref:"preView"})],1)},i=[],l=o("5530"),a=o("2909"),u=(o("99af"),o("d81d"),o("b0c0"),o("d3b7"),o("caad"),o("2532"),o("159b"),o("14d9"),o("a9e3"),o("4de4"),o("13d5"),o("b64b"),o("9129"),o("7db0"),o("a434"),o("c740"),o("ac1f"),o("841c"),o("82ad")),s=o("fba1"),d=o("5fb3"),c=o("c1df"),p=o.n(c),h=o("72f9"),b=o.n(h),m=(o("9d56"),o("b635")),f=o("f870"),y={panels:[{index:0,name:1,height:297,width:210,paperHeader:0,paperFooter:0,printElements:[{options:{left:216,top:13.5,height:20,width:172.5,title:"标题",field:"title",coordinateSync:!1,widthHeightSync:!1,hideTitle:!0,qrCodeLevel:0,fontSize:19,fontWeight:"bold",textAlign:"center",textContentVerticalAlign:"middle",fixed:!0,content:"瑞旗仓库出仓计划",right:189.99609375,bottom:29.24609375,vCenter:129.99609375,hCenter:19.24609375},printElementType:{title:"文本",type:"text"}},{options:{left:11.5,top:42.5,height:10,width:46,title:"出仓单号",coordinateSync:!1,widthHeightSync:!1,qrCodeLevel:0,fontSize:10},printElementType:{title:"文本",type:"text"}},{options:{left:58.5,top:42,height:10,width:90,field:"outboundNo",coordinateSync:!1,widthHeightSync:!1,hideTitle:!0,qrCodeLevel:0,fontSize:10,borderStyle:"border:1px solid #000",title:"文本",right:152,bottom:44.5,vCenter:112,hCenter:39.5},printElementType:{title:"文本",type:"text"}},{options:{left:155.5,top:42.5,height:10,width:46,title:"客户单号",coordinateSync:!1,widthHeightSync:!1,qrCodeLevel:0,fontSize:10},printElementType:{title:"文本",type:"text"}},{options:{left:203,top:42.5,height:10,width:90,field:"customerOrderNo",coordinateSync:!1,widthHeightSync:!1,hideTitle:!0,qrCodeLevel:0,fontSize:10,borderStyle:"border:1px solid #000",title:"文本"},printElementType:{title:"文本",type:"text"}},{options:{left:304,top:42.5,height:10,width:46,title:"客户代码",coordinateSync:!1,widthHeightSync:!1,qrCodeLevel:0,fontSize:10},printElementType:{title:"文本",type:"text"}},{options:{left:351.5,top:42.5,height:10,width:90,field:"clientCode",coordinateSync:!1,widthHeightSync:!1,hideTitle:!0,qrCodeLevel:0,fontSize:10,borderStyle:"border:1px solid #000",title:"文本"},printElementType:{title:"文本",type:"text"}},{options:{left:449.5,top:42.5,height:10,width:46,title:"客户名称",coordinateSync:!1,widthHeightSync:!1,qrCodeLevel:0,fontSize:10},printElementType:{title:"文本",type:"text"}},{options:{left:497,top:42.5,height:10,width:90,field:"clientName",coordinateSync:!1,widthHeightSync:!1,hideTitle:!0,qrCodeLevel:0,fontSize:10,borderStyle:"border:1px solid #000",title:"文本"},printElementType:{title:"文本",type:"text"}},{options:{left:11.5,top:57.5,height:10,width:46,title:"计划出仓",coordinateSync:!1,widthHeightSync:!1,qrCodeLevel:0,fontSize:10},printElementType:{title:"文本",type:"text"}},{options:{left:58.5,top:57,height:10,width:90,field:"plannedOutboundDate",coordinateSync:!1,widthHeightSync:!1,hideTitle:!0,qrCodeLevel:0,fontSize:10,borderStyle:"border:1px solid #000",title:"文本",right:146.4921875,bottom:59.9921875,vCenter:106.4921875,hCenter:54.9921875},printElementType:{title:"文本",type:"text"}},{options:{left:155.5,top:57.5,height:10,width:46,title:"出仓方式",coordinateSync:!1,widthHeightSync:!1,qrCodeLevel:0,fontSize:10},printElementType:{title:"文本",type:"text"}},{options:{left:203,top:57.5,height:10,width:90,field:"outboundType",coordinateSync:!1,widthHeightSync:!1,hideTitle:!0,qrCodeLevel:0,fontSize:10,borderStyle:"border:1px solid #000",title:"文本"},printElementType:{title:"文本",type:"text"}},{options:{left:304.5,top:57,height:10,width:46,title:"柜型",coordinateSync:!1,widthHeightSync:!1,qrCodeLevel:0,fontSize:10,right:352.99609375,bottom:59.9921875,vCenter:340.49609375,hCenter:54.9921875},printElementType:{title:"文本",type:"text"}},{options:{left:351.5,top:57.5,height:10,width:90,field:"containerType",coordinateSync:!1,widthHeightSync:!1,hideTitle:!0,qrCodeLevel:0,fontSize:10,borderStyle:"border:1px solid #000",title:"文本"},printElementType:{title:"文本",type:"text"}},{options:{left:450,top:57,height:10,width:46,title:"货物类型",coordinateSync:!1,widthHeightSync:!1,qrCodeLevel:0,fontSize:10,right:517.99609375,bottom:59.9921875,vCenter:494.99609375,hCenter:54.9921875},printElementType:{title:"文本",type:"text"}},{options:{left:497,top:57.5,height:10,width:90,field:"cargoType",coordinateSync:!1,widthHeightSync:!1,hideTitle:!0,qrCodeLevel:0,fontSize:10,borderStyle:"border:1px solid #000",title:"文本"},printElementType:{title:"文本",type:"text"}},{options:{left:11.5,top:72.5,height:10,width:46,title:"柜号",coordinateSync:!1,widthHeightSync:!1,qrCodeLevel:0,fontSize:10},printElementType:{title:"文本",type:"text"}},{options:{left:58.5,top:72,height:10,width:90,field:"containerNo",coordinateSync:!1,widthHeightSync:!1,hideTitle:!0,qrCodeLevel:0,fontSize:10,borderStyle:"border:1px solid #000",title:"文本",right:146,bottom:74.5,vCenter:106,hCenter:69.5},printElementType:{title:"文本",type:"text"}},{options:{left:155.5,top:72.5,height:10,width:46,title:"封号",coordinateSync:!1,widthHeightSync:!1,qrCodeLevel:0,fontSize:10},printElementType:{title:"文本",type:"text"}},{options:{left:203,top:72.5,height:10,width:90,field:"sealNo",coordinateSync:!1,widthHeightSync:!1,hideTitle:!0,qrCodeLevel:0,fontSize:10,borderStyle:"border:1px solid #000",title:"文本"},printElementType:{title:"文本",type:"text"}},{options:{left:304,top:72.5,height:10,width:46,title:"车牌",coordinateSync:!1,widthHeightSync:!1,qrCodeLevel:0,fontSize:10},printElementType:{title:"文本",type:"text"}},{options:{left:351,top:72,height:10,width:90,field:"plateNumber",coordinateSync:!1,widthHeightSync:!1,hideTitle:!0,qrCodeLevel:0,fontSize:10,borderStyle:"border:1px solid #000",title:"文本",right:428.4921875,bottom:74.9921875,vCenter:388.4921875,hCenter:69.9921875},printElementType:{title:"文本",type:"text"}},{options:{left:449.5,top:72.5,height:10,width:46,title:"司机电话",coordinateSync:!1,widthHeightSync:!1,qrCodeLevel:0,fontSize:10},printElementType:{title:"文本",type:"text"}},{options:{left:496.5,top:72,height:10,width:90,field:"driverPhone",coordinateSync:!1,widthHeightSync:!1,hideTitle:!0,qrCodeLevel:0,fontSize:10,borderStyle:"border:1px solid #000",right:553.9921875,bottom:74.9921875,vCenter:523.9921875,hCenter:69.9921875,title:"文本"},printElementType:{title:"文本",type:"text"}},{options:{left:11.5,top:87.5,height:10,width:46,title:"仓库报价",coordinateSync:!1,widthHeightSync:!1,qrCodeLevel:0,fontSize:10},printElementType:{title:"文本",type:"text"}},{options:{left:58.5,top:87,height:10,width:90,field:"warehouseQuote",coordinateSync:!1,widthHeightSync:!1,hideTitle:!0,qrCodeLevel:0,fontSize:10,borderStyle:"border:1px solid #000",title:"文本",right:146,bottom:89.5,vCenter:106,hCenter:84.5},printElementType:{title:"文本",type:"text"}},{options:{left:155.5,top:87.5,height:10,width:46,title:"仓管代收",coordinateSync:!1,widthHeightSync:!1,qrCodeLevel:0,fontSize:10},printElementType:{title:"文本",type:"text"}},{options:{left:203,top:87.5,height:10,width:90,field:"warehouseCollection",coordinateSync:!1,widthHeightSync:!1,hideTitle:!0,qrCodeLevel:0,fontSize:10,borderStyle:"border:1px solid #000",title:"文本"},printElementType:{title:"文本",type:"text"}},{options:{left:304,top:87.5,height:10,width:46,title:"人工费用",coordinateSync:!1,widthHeightSync:!1,qrCodeLevel:0,fontSize:10},printElementType:{title:"文本",type:"text"}},{options:{left:351,top:87,height:10,width:90,field:"workerLoadingFee",coordinateSync:!1,widthHeightSync:!1,hideTitle:!0,qrCodeLevel:0,fontSize:10,borderStyle:"border:1px solid #000",right:408.4921875,bottom:89.9921875,vCenter:378.4921875,hCenter:84.9921875,title:"文本"},printElementType:{title:"文本",type:"text"}},{options:{left:449.5,top:87.5,height:10,width:46,title:"仓管代付",coordinateSync:!1,widthHeightSync:!1,qrCodeLevel:0,fontSize:10},printElementType:{title:"文本",type:"text"}},{options:{left:497,top:87.5,height:10,width:90,field:"warehousePay",coordinateSync:!1,widthHeightSync:!1,hideTitle:!0,qrCodeLevel:0,fontSize:10,borderStyle:"border:1px solid #000",title:"文本"},printElementType:{title:"文本",type:"text"}},{options:{left:11.5,top:102.5,height:10,width:46,title:"操作要求",coordinateSync:!1,widthHeightSync:!1,qrCodeLevel:0,fontSize:10},printElementType:{title:"文本",type:"text"}},{options:{left:12,top:112.5,height:172.5,width:373.5,field:"operationRequirement",coordinateSync:!1,widthHeightSync:!1,hideTitle:!0,qrCodeLevel:0,fontSize:10,borderStyle:"border:1px solid #000",right:199.99609375,bottom:145,vCenter:112.49609375,hCenter:125},printElementType:{title:"文本",type:"text"}},{options:{left:394,top:102.5,height:10,width:46,title:"仓管指示",coordinateSync:!1,widthHeightSync:!1,qrCodeLevel:0,fontSize:10},printElementType:{title:"文本",type:"text"}},{options:{left:394,top:112.5,height:172.5,width:192,field:"outboundNote",coordinateSync:!1,widthHeightSync:!1,hideTitle:!0,qrCodeLevel:0,fontSize:10,borderStyle:"border:1px solid #000"},printElementType:{title:"文本",type:"text"}},{options:{left:11.5,top:292.5,height:10,width:46,title:"操作员",coordinateSync:!1,widthHeightSync:!1,qrCodeLevel:0,fontSize:10},printElementType:{title:"文本",type:"text"}},{options:{left:60,top:292.5,height:10,width:60,field:"operator",coordinateSync:!1,widthHeightSync:!1,hideTitle:!0,qrCodeLevel:0,fontSize:10,borderStyle:"border:1px solid #000"},printElementType:{title:"文本",type:"text"}},{options:{left:164.5,top:292.5,height:10,width:46,title:"下单日期",coordinateSync:!1,widthHeightSync:!1,qrCodeLevel:0,fontSize:10},printElementType:{title:"文本",type:"text"}},{options:{left:211.5,top:292.5,height:10,width:60,field:"orderDate",coordinateSync:!1,widthHeightSync:!1,hideTitle:!0,qrCodeLevel:0,fontSize:10,borderStyle:"border:1px solid #000"},printElementType:{title:"文本",type:"text"}},{options:{left:304,top:292.5,height:10,width:46,title:"仓管员",coordinateSync:!1,widthHeightSync:!1,qrCodeLevel:0,fontSize:10},printElementType:{title:"文本",type:"text"}},{options:{left:351,top:292.5,height:10,width:60,field:"outboundHandler",coordinateSync:!1,widthHeightSync:!1,hideTitle:!0,qrCodeLevel:0,fontSize:10,borderStyle:"border:1px solid #000"},printElementType:{title:"文本",type:"text"}},{options:{left:463.5,top:292.5,height:10,width:46,title:"仓管确认",coordinateSync:!1,widthHeightSync:!1,qrCodeLevel:0,fontSize:10,right:496.99609375,bottom:160,vCenter:484.49609375,hCenter:155},printElementType:{title:"文本",type:"text"}},{options:{left:510.5,top:292.5,height:10,width:60,field:"warehouseConfirm",coordinateSync:!1,widthHeightSync:!1,hideTitle:!0,qrCodeLevel:0,fontSize:10,borderStyle:"border:1px solid #000"},printElementType:{title:"文本",type:"text"}},{options:{left:10.5,top:312,height:10,width:561,title:"库存明细",coordinateSync:!1,widthHeightSync:!1,hideTitle:!0,qrCodeLevel:0,fontSize:10,fontWeight:"bold",content:"入仓流水号    分单号+客户名称    唛头/货名/件数/毛重/体积    司机信息    入仓经手人 入仓日期 出仓数量",textAlign:"left",backgroundColor:"#f0f0f0",borderStyle:"border:1px solid #000",right:382.24609375,bottom:173.5,vCenter:204.74609375,hCenter:168.5},printElementType:{title:"文本",type:"text"}},{options:{left:29.5,top:502,height:10,width:30,title:"合计",coordinateSync:!1,widthHeightSync:!1,hideTitle:!0,qrCodeLevel:0,fontSize:10,fontWeight:"bold",content:"Total:",textAlign:"left",backgroundColor:"#f0f0f0",borderStyle:"border:1px solid #000"},printElementType:{title:"文本",type:"text"}},{options:{left:59.5,top:502,height:10,width:200,field:"totalSummary",coordinateSync:!1,widthHeightSync:!1,hideTitle:!0,qrCodeLevel:0,fontSize:10,content:"件数/毛重/体积",textAlign:"center",borderStyle:"border:1px solid #000"},printElementType:{title:"文本",type:"text"}},{options:{left:10.5,top:322.5,height:120,width:559.5,field:"inventoryList",coordinateSync:!1,widthHeightSync:!1,textAlign:"center",tableBorder:"border",tableHeaderBorder:"border",tableHeaderCellBorder:"border",tableBodyRowBorder:"topBottomBorder",tableBodyCellBorder:"noBorder",tableFooterCellBorder:"noBorder",right:362.5,bottom:462.75,vCenter:185,hCenter:402.75,tableFooterBorder:"noBorder",columns:[[{width:94.56338028169014,title:"入仓流水号",field:"inboundSerialNo",checked:!0,columnId:"inboundSerialNo",fixed:!1,rowspan:1,colspan:1,tableQRCodeLevel:0,tableSummaryTitle:!0,tableSummary:""},{width:126.08450704225352,title:"分单号+客户名称",field:"clientCode",checked:!0,columnId:"clientCode",fixed:!1,rowspan:1,colspan:1,tableQRCodeLevel:0,tableSummaryTitle:!0,tableSummary:""},{width:157.6056338028169,title:"唛头/货名/件数/毛重/体积",field:"totalBoxes",checked:!0,columnId:"totalBoxes",fixed:!1,rowspan:1,colspan:1,tableQRCodeLevel:0,tableSummaryTitle:!0,tableSummary:""},{width:78.80281690140845,title:"司机信息",field:"driverInfo",checked:!0,columnId:"driverInfo",fixed:!1,rowspan:1,colspan:1,tableQRCodeLevel:0,tableSummaryTitle:!0,tableSummary:""},{width:102.44366197183099,title:"出仓数量",field:"outboundQuantity",checked:!0,columnId:"outboundQuantity",fixed:!1,rowspan:1,colspan:1,tableQRCodeLevel:0,tableSummaryTitle:!0,tableSummary:""}]]},printElementType:{title:"表格",type:"table",editable:!0,columnDisplayEditable:!0,columnDisplayIndexEditable:!0,columnTitleEditable:!0,columnResizable:!0,columnAlignEditable:!0,isEnableEditField:!0,isEnableContextMenu:!0,isEnableInsertRow:!0,isEnableDeleteRow:!0,isEnableInsertColumn:!0,isEnableDeleteColumn:!0,isEnableMergeCell:!0}},{options:{left:12,top:114,height:171,width:375},printElementType:{title:"矩形",type:"rect"}},{options:{left:396,top:114,height:171,width:190.5},printElementType:{title:"矩形",type:"rect"}}],paperNumberLeft:547.5,paperNumberTop:661.5,paperNumberDisabled:!0,watermarkOptions:{content:"",fillStyle:"rgba(184, 184, 184, 0.3)",fontSize:"14px",rotate:25,width:200,height:200,timestamp:!1,format:"YYYY-MM-DD HH:mm"}}]},g={name:"Outboundrecord",components:{printPreview:f["default"]},data:function(){return{showLeft:0,showRight:24,loading:!0,selectOutboundList:[],ids:[],single:!0,multiple:!0,showSearch:!1,total:0,outboundrecordList:[],title:"",open:!1,queryParams:{pageNum:1,pageSize:20,outboundNo:null,clientCode:null,clientName:null,operator:null,containerType:null,containerNo:null,sealNo:null,outboundDate:null,warehouseQuote:null,workerLoadingFee:null,warehouseCollection:null,collectionNotes:null,totalBoxes:null,totalGrossWeight:null,totalVolume:null,totalRows:null,receivedStorageFee:null,unpaidUnloadingFee:null,unpaidPackagingFee:null,logisticsAdvanceFee:null,rentalBalanceFee:null,overdueRent:null,freeStackDays:null,overdueUnitPrice:null},form:{},outboundType:null,preOutboundInventoryListLoading:!1,search:null,rules:{clientCode:[{required:!0,message:"客户代码不能为空",trigger:"blur"}]},outboundForm:{outboundDate:p()().format("yyyy-MM-DD")},clientRow:{},openOutbound:!1,preOutboundInventoryList:[],selectedCargoDetail:[]}},watch:{showSearch:function(e){!0===e?(this.showRight=21,this.showLeft=3):(this.showRight=24,this.showLeft=0)}},created:function(){this.getList()},mounted:function(){this.initPrint()},methods:{initPrint:function(){m["c"].init({providers:[new m["a"]]})},printOutboundPlant:function(){var e=this,t={title:"瑞旗仓库出仓计划",outboundNo:this.outboundForm.outboundNo||"",customerOrderNo:this.outboundForm.customerOrderNo||"",clientCode:this.outboundForm.clientCode||"",clientName:this.outboundForm.clientName||"",plannedOutboundDate:p()(this.outboundForm.plannedOutboundDate).format("yyyy-MM-DD HH:mm")||"",outboundType:this.outboundForm.outboundType||"",containerType:this.outboundForm.containerType||"",cargoType:this.form.cargoType||"",containerNo:this.outboundForm.containerNo||"",sealNo:this.outboundForm.sealNo||"",plateNumber:this.outboundForm.plateNumber||"",driverPhone:this.outboundForm.driverPhone||"",warehouseQuote:this.outboundForm.warehouseQuote||"",warehouseCollection:this.outboundForm.warehouseCollection||"",workerLoadingFee:this.outboundForm.workerLoadingFee||"",warehousePay:this.outboundForm.warehousePay||"",operationRequirement:this.outboundForm.operationRequirement||"",outboundNote:this.outboundForm.outboundNote||"",operator:this.outboundForm.operator||"",orderDate:this.outboundForm.orderDate||"",outboundHandler:this.outboundForm.outboundHandler||"",warehouseConfirm:"√ 已确认 "+this.parseTime(new Date,"{y}-{m}-{d}"),totalBoxes:this.outboundForm.totalBoxes||0,totalGrossWeight:this.outboundForm.totalGrossWeight||0,totalVolume:this.outboundForm.totalVolume||0,totalSummary:"件数: ".concat(this.outboundForm.totalBoxes||0," / 毛重: ").concat(this.outboundForm.totalGrossWeight||0," / 体积: ").concat(this.outboundForm.totalVolume||0),totalQuantity:this.outboundForm.totalBoxes||0,inventoryList:this.selectOutboundList.map((function(t){return{inboundSerialNo:t.inboundSerialNo||"",clientCode:"".concat(t.subOrderNo||""," ").concat(t.consigneeName||""),totalBoxes:(e.outboundForm.sqdShippingMark||"")+" / "+(t.itemName||"")+" / "+(t.totalBoxes||0)+" / "+(t.totalGrossWeight||0)+"KGS / "+(t.totalVolume||0)+"CBM",totalGrossWeight:t.totalGrossWeight||0,totalVolume:t.totalVolume||0,driverInfo:t.driverInfo||"",outboundQuantity:t.totalBoxes||0}}))};n=new m["c"].PrintTemplate({template:y}),this.$refs.preView.print(n,t)},warehouseConfirm:function(){this.outboundForm.clientCode?(this.outboundForm.operator=this.$store.state.user.name.split(" ")[1],this.outboundForm.orderDate=p()().format("yyyy-MM-DD"),this.$message.success("仓管确认成功")):this.$message.warning("请先选择客户")},loadChildInventory:function(e,t,o){var n=this;this.$set(e,"loading",!0),Object(d["h"])({packageTo:e.inventoryId}).then((function(t){var r=t.rows;o(r),e.children=r,n.ids.includes(e.inventoryId)&&setTimeout((function(){r.forEach((function(e){n.ids.includes(e.inventoryId)||(n.ids.push(e.inventoryId),n.selectOutboundList.push(e)),n.$refs.table.toggleRowSelection(e,!0)}))}),50)})).finally((function(){n.$set(e,"loading",!1)}))},warehouseRentSettlement:function(){this.outboundReset(),this.outboundForm.outboundHandler=this.$store.state.user.name.split(" ")[1],this.outboundType=4,this.openOutbound=!0},countSummary:function(){this.outboundForm.unreceivedFromCustomer=b()(this.outboundForm.warehouseQuote).add(this.outboundForm.additionalStorageFee).add(this.outboundForm.unpaidUnloadingFee).add(this.outboundForm.unpaidPackagingFee).add(this.outboundForm.logisticsAdvanceFee).add(this.outboundForm.overdueRentalFee).add(this.outboundForm.difficultyWorkFee).value,this.outboundForm.receivedFromCustomer=b()(this.outboundForm.warehouseCollection).value,this.outboundForm.customerReceivableBalance=b()(this.outboundForm.unreceivedFromCustomer).subtract(this.outboundForm.receivedFromCustomer).value,this.outboundForm.payableToWorker=b()(this.outboundForm.receivedUnloadingFee).add(this.outboundForm.receivedPackingFee).add(this.outboundForm.workerLoadingFee).value,this.outboundForm.receivedFromSupplier=b()(this.outboundForm.receivedSupplier).add(this.outboundForm.receivedStorageFee).value,this.outboundForm.promissoryNoteSales=b()(this.outboundForm.unreceivedFromCustomer).add(this.outboundForm.receivedFromSupplier).value,this.outboundForm.promissoryNoteCost=b()(this.outboundForm.payableToWorker).add(this.outboundForm.logisticsAdvanceFee).add(this.outboundForm.warehouseAdvanceOtherFee).value,this.outboundForm.promissoryNoteGrossProfit=b()(this.outboundForm.promissoryNoteSales).subtract(this.outboundForm.promissoryNoteCost).value},currency:b.a,outboundConfirm:function(e){var t=this;this.$confirm("确定要"+(0===e?"预出仓":1===e?"出仓":"直接出仓")+"吗？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){if(t.selectOutboundList.map((function(e){1!=e.cargoDeduction||t.$message.error("有扣货库存请重新勾选，流水号："+e.inboundSerialNoSub)})),t.selectOutboundList.map((function(e){e.partialOutboundFlag=Number(e.partialOutboundFlag)})),t.outboundForm.totalBoxes=0,t.outboundForm.totalGrossWeight=0,t.outboundForm.totalVolume=0,t.selectOutboundList.map((function(e){return e.rsCargoDetailsList&&e.rsCargoDetailsList.map((function(e){return t.outboundForm.totalBoxes=b()(e.boxCount).add(t.outboundForm.totalBoxes).value,t.outboundForm.totalGrossWeight=b()(e.unitGrossWeight).add(t.outboundForm.totalGrossWeight).value,t.outboundForm.totalVolume=b()(e.unitVolume).add(t.outboundForm.totalVolume).value,e})),e})),0===e)Object(u["a"])(t.outboundForm).then((function(e){var o=t.selectOutboundList.map((function(e){return e.preOutboundFlag="1",e.rsCargoDetailsList&&e.rsCargoDetailsList.map((function(e){return e.preOutboundFlag="1",e})),e}));Object(d["l"])(o).then((function(e){t.getList(),t.$message.success("预出仓成功"),t.openOutbound=!1}))}));else if(1===e)t.outboundForm.preOutboundFlag="1",t.outboundForm.outboundDate=p()().format("yyyy-MM-DD"),Object(u["k"])(t.outboundForm).then((function(e){var o=e.data,n=t.selectOutboundList.map((function(e){return e.outboundRecordId=o,e.rsCargoDetailsList&&e.rsCargoDetailsList.map((function(e){return e.outboundRecordId=o,e})),e}));Object(d["j"])(n).then((function(e){t.getList(),t.$message.success("出仓成功"),t.openOutbound=!1}))}));else if(2===e)t.outboundForm.preOutboundFlag="0",t.outboundForm.outboundDate=p()().format("yyyy-MM-DD"),Object(u["a"])(t.outboundForm).then((function(e){var o=e.data,n=t.selectOutboundList.map((function(e){return e.outboundRecordId=o,e.rsCargoDetailsList&&e.rsCargoDetailsList.map((function(e){return e.outboundRecordId=o,e})),e}));Object(d["j"])(n).then((function(e){t.getList(),t.$message.success("出仓成功"),t.openOutbound=!1}))}));else if(3===e)t.outboundForm.isRentSettlement=1,Object(u["a"])(t.outboundForm).then((function(e){var o=e.data,n=t.selectOutboundList.map((function(e){return e.outboundRecordId=o,e.rentalSettlementDate=t.outboundForm.outboundDate,e.rsCargoDetailsList&&e.rsCargoDetailsList.map((function(e){return e.outboundRecordId=o,e})),e}));Object(d["m"])(n).then((function(e){t.$message.success("结算成功"),t.loadPreOutboundInventoryList()}))}));else{var o=t.outboundForm.outboundRecordId;t.outboundForm.preOutboundFlag="0",t.outboundForm.outboundDate=p()().format("yyyy-MM-DD"),Object(u["k"])(t.outboundForm).then((function(e){var n=t.selectOutboundList.map((function(e){return e.outboundRecordId=o,e.rsCargoDetailsList&&e.rsCargoDetailsList.map((function(e){return e.outboundRecordId=o,e})),e}));Object(d["j"])(n).then((function(e){t.getList(),t.$message.success("出仓成功"),t.openOutbound=!1}))}))}}))},loadPreOutboundInventoryList:function(){var e=this;this.preOutboundInventoryListLoading=!0,this.loading=!0;var t={sqdPlannedOutboundDate:this.outboundForm.plannedOutboundDate,clientCode:this.outboundForm.clientCode,inventoryStatus:"0"};this.queryParams.preOutboundFlag&&(t.preOutboundFlag=this.queryParams.preOutboundFlag),this.queryParams.preOutboundRecordId&&(t.preOutboundRecordId=this.queryParams.preOutboundRecordId),Object(d["i"])(t).then((function(t){e.preOutboundInventoryList=t.rows.filter((function(e){return!e.packageTo})),!e.preOutboundInventoryList||t.rows.map((function(t){if(0===t.includesInboundFee){var o=Number(t.receivedStorageFee||0),n=Number(t.inboundFee||0),r=b()(n).subtract(o).value;t.additionalStorageFee=r>0?r:0}else t.additionalStorageFee=0;return"1"===t.packageRecord&&(t.hasChildren=!0),e.outboundForm.outboundRecordId===t.preOutboundRecordId&&(e.selectOutboundList.push(t),e.$nextTick((function(){e.$refs.table.toggleRowSelection(t,!0)}))),t})),e.total=t.total||0,0===e.outboundType&&e.$refs.table&&e.$nextTick((function(){e.preOutboundInventoryList.forEach((function(t){1===t.preOutboundFlag&&e.$refs.table.toggleRowSelection(t,!0)}))}))})).catch((function(t){console.error("加载预出库库存列表失败:",t),e.$message.error("加载预出库库存列表失败")})).finally((function(){e.queryParams.preOutboundRecordId=null,e.queryParams.preOutboundFlag=null,e.loading=!1,e.preOutboundInventoryListLoading=!1}))},handleOutbound:function(e){this.outboundReset(),this.outboundForm=e,this.outboundType=1,this.queryParams.preOutboundRecordId=this.outboundForm.outboundRecordId,this.queryParams.preOutboundFlag="1",this.loadPreOutboundInventoryList(),this.openOutbound=!0},handlePreOutbound:function(){this.outboundReset(),this.outboundForm.outboundHandler=this.$store.state.user.name.split(" ")[1],this.outboundType=0,this.openOutbound=!0},handleDirectOutbound:function(){this.outboundReset(),this.outboundForm.outboundHandler=this.$store.state.user.name.split(" ")[1],this.outboundType=2,this.openOutbound=!0},handleRentSettlement:function(){this.outboundReset(),this.outboundForm.outboundHandler=this.$store.state.user.name.split(" ")[1],this.outboundType=3,this.openOutbound=!0},parseTime:s["f"],handleOutboundCargoDetailSelectionChange:function(e,t){t.outboundCargoDetailsList=e,this.selectedCargoDetail=e},isRowSelected:function(e){return this.selectedCargoDetail.includes(e)},getSummaries:function(e){var t=this,o=e.columns,n=(e.data,[]),r=["receivedSupplier","totalBoxes","unpaidInboundFee","totalGrossWeight","totalVolume","receivedStorageFee","unpaidUnloadingFee","logisticsAdvanceFee","rentalBalanceFee","overdueRentalFee","additionalStorageFee","unpaidUnloadingFee","unpaidPackingFee","receivedUnloadingFee","receivedPackingFee"],i={};return o.forEach((function(e,o){if(0===o)n[o]="汇总";else{var l=e.property;if(r.includes(l)){var a=t.selectOutboundList.reduce((function(e,t){return b()(e).add(Number(t[l])||0).value}),0);n[o]=a,i[e.property]=a}else n[o]=" "}})),Object.keys(i).forEach((function(e){t.outboundForm&&(t.outboundForm[e]=i[e])})),this.countSummary(),n},handleOutboundSelectionChange:function(e){var t=this,o=this.$refs.table.store.states.data,n=Object(a["a"])(this.ids);this.ids=[],this.ids=e.map((function(e){return e.inventoryId}));var r=this.ids.filter((function(e){return!n.includes(e)})),i=n.filter((function(e){return!t.ids.includes(e)}));this.selectOutboundList=e,this.$refs.table.doLayout(),e.map((function(e){var n=p()(t.outboundForm.outboundDate),i=p()(e.rentalSettlementDate);e.rentalDays=n.diff(i,"days")+1;var l=e.totalVolume;if(!Number.isNaN(e.rentalDays)&&e.rentalDays>0)if("整柜"!==t.outboundForm.outboundType)e.overdueRentalFee=b()(e.rentalDays).multiply(e.overdueRentalUnitPrice).multiply(l).value;else{var a=b()(e.rentalDays).subtract(e.freeStackPeriod).value;a=a>0?a:0,e.rentalDays=a,e.overdueRentalFee=b()(a).multiply(e.overdueRentalUnitPrice).multiply(l).value}if("1"===e.packageRecord&&r.includes(e.inventoryId)){var u=o.find((function(t){return t.inventoryId===e.inventoryId}));u&&u.children&&u.children.length>0?setTimeout((function(){u.children.forEach((function(e){t.ids.includes(e.inventoryId)||(t.ids.push(e.inventoryId),t.selectOutboundList.push(e),t.$refs.table.toggleRowSelection(e,!0))}))}),50):u&&!u.childrenLoaded&&u.hasChildren&&(u.childrenLoaded=!0,t.$refs.table.toggleRowExpansion(u,!0))}})),i.forEach((function(e){var n=o.find((function(t){return t.inventoryId===e&&"1"===t.packageRecord}));n&&n.children&&n.children.length>0&&n.children.forEach((function(e){var o=t.ids.indexOf(e.inventoryId);if(o>-1){t.ids.splice(o,1);var n=t.selectOutboundList.findIndex((function(t){return t.inventoryId===e.inventoryId}));n>-1&&t.selectOutboundList.splice(n,1),t.$refs.table.toggleRowSelection(e,!1)}}))})),this.countSummary()},selectContainerType:function(e){switch(e){case"20GP":this.outboundForm.warehouseQuote=this.clientRow.rate20gp;break;case"40HQ":this.outboundForm.warehouseQuote=this.clientRow.rate40hq;break}},outboundClient:function(e){this.outboundForm.warehouseQuote=e.rateLcl,this.outboundForm.freeStackDays=e.freeStackPeriod,this.clientRow=e,this.outboundForm.overdueRentalUnitPrice=e.overdueRent,this.outboundForm.clientName=e.clientName,this.$forceUpdate()},getList:function(){var e=this;this.loading=!0,Object(u["h"])(this.queryParams).then((function(t){e.outboundrecordList=t.rows,e.total=t.total})).finally((function(){e.loading=!1}))},cancel:function(){this.open=!1,this.reset()},outboundReset:function(){this.outboundForm={outboundRecordId:null,receivedSupplier:null,outboundNo:null,clientCode:null,clientName:null,operator:null,containerType:null,containerNo:null,sealNo:null,warehouseQuote:null,workerLoadingFee:null,warehouseCollection:null,collectionNotes:null,totalBoxes:null,totalGrossWeight:null,totalVolume:null,totalRows:null,receivedStorageFee:null,unpaidUnloadingFee:null,unpaidPackagingFee:null,logisticsAdvanceFee:null,rentalBalanceFee:null,overdueRent:null,operationRequirement:null,freeStackDays:null,overdueUnitPrice:null,receivedFromSupplier:null,unreceivedFromCustomer:null,receivedFromCustomer:null,customerReceivableBalance:null,payableToWorker:null,promissoryNoteSales:null,promissoryNoteCost:null,promissoryNoteGrossProfit:null,outboundDate:p()().format("yyyy-MM-DD")},this.preOutboundInventoryList=[],this.resetForm("outboundForm")},reset:function(){this.form={outboundDate:p()().format("yyyy-MM-DD"),outboundRecordId:null,outboundNo:null,clientCode:null,clientName:null,operator:null,containerType:null,containerNo:null,sealNo:null,warehouseQuote:null,workerLoadingFee:null,warehouseCollection:null,collectionNotes:null,totalBoxes:null,totalGrossWeight:null,totalVolume:null,totalRows:null,receivedStorageFee:null,unpaidUnloadingFee:null,unpaidPackagingFee:null,logisticsAdvanceFee:null,rentalBalanceFee:null,overdueRent:null,freeStackDays:null,overdueUnitPrice:null},this.resetForm("form")},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.resetForm("queryForm"),this.handleQuery()},handleStatusChange:function(e){var t=this,o="0"===e.status?"启用":"停用";this.$modal.confirm('确认要"'+o+"吗？").then((function(){return Object(u["b"])(e.outboundRecordId,e.status)})).then((function(){t.$modal.msgSuccess(o+"成功")})).catch((function(){e.status="0"===e.status?"1":"0"}))},handleSelectionChange:function(e){this.ids=e.map((function(e){return e.outboundRecordId})),this.single=1!==e.length,this.multiple=!e.length},handleAdd:function(){this.reset(),this.open=!0,this.title="添加出仓记录"},handleUpdate:function(e){var t=this;this.reset();var o=e.outboundRecordId||this.ids;Object(u["e"])(o).then((function(e){t.form=e.data,t.open=!0,t.title="修改出仓记录"}))},submitForm:function(){var e=this;this.$refs["outboundForm"].validate((function(t){t&&(null!=e.outboundForm.outboundRecordId?Object(u["k"])(e.outboundForm).then((function(t){e.$modal.msgSuccess("修改成功"),e.open=!1,e.getList()})):Object(u["a"])(e.outboundForm).then((function(t){e.$modal.msgSuccess("新增成功"),e.open=!1,e.getList()})))}))},handleDelete:function(e){var t=this,o=e.outboundRecordId||this.ids;this.$modal.confirm('是否确认删除出仓记录编号为"'+o+'"的数据项？').then((function(){return Object(u["c"])(o)})).then((function(){t.getList(),t.$modal.msgSuccess("删除成功")})).catch((function(){}))},handleExport:function(){this.download("system/outboundrecord/export",Object(l["a"])({},this.queryParams),"outboundrecord_".concat((new Date).getTime(),".xlsx"))},handleSearchEnter:function(){var e=this;if(this.search){var t=this.preOutboundInventoryList.findIndex((function(t){var o=String(t.inboundSerialNo||""),n=String(e.search);return o.includes(n)}));if(t>-1){var o=this.$refs.table;this.$nextTick((function(){var t=o.$el.querySelector(".el-table__body-wrapper"),n=t.querySelectorAll(".el-table__row"),r=-1;if(n.forEach((function(t,o){var n=t.textContent;n.includes(e.search)&&(r=o)})),r>-1){var i=n[r],l=i.offsetTop;t.scrollTo({top:l-t.clientHeight/2,behavior:"smooth"}),i.classList.add("highlight-row"),setTimeout((function(){i.classList.remove("highlight-row")}),2e3)}}))}else this.$message.warning("未找到匹配的记录")}}}},v=g,w=(o("9558"),o("2877")),S=Object(w["a"])(v,r,i,!1,null,"90a72406",null);t["default"]=S.exports},4450:function(e,t,o){},4678:function(e,t,o){var n={"./af":"2bfb","./af.js":"2bfb","./ar":"8e73","./ar-dz":"a356","./ar-dz.js":"a356","./ar-kw":"423e","./ar-kw.js":"423e","./ar-ly":"1cfd","./ar-ly.js":"1cfd","./ar-ma":"0a84","./ar-ma.js":"0a84","./ar-ps":"4c98","./ar-ps.js":"4c98","./ar-sa":"8230","./ar-sa.js":"8230","./ar-tn":"6d83","./ar-tn.js":"6d83","./ar.js":"8e73","./az":"485c","./az.js":"485c","./be":"1fc1","./be.js":"1fc1","./bg":"84aa","./bg.js":"84aa","./bm":"a7fa","./bm.js":"a7fa","./bn":"9043","./bn-bd":"9686","./bn-bd.js":"9686","./bn.js":"9043","./bo":"d26a","./bo.js":"d26a","./br":"6887","./br.js":"6887","./bs":"25548","./bs.js":"25548","./ca":"d716","./ca.js":"d716","./cs":"3c0d","./cs.js":"3c0d","./cv":"03ec","./cv.js":"03ec","./cy":"9797","./cy.js":"9797","./da":"0f14","./da.js":"0f14","./de":"b469","./de-at":"b3eb","./de-at.js":"b3eb","./de-ch":"bb71","./de-ch.js":"bb71","./de.js":"b469","./dv":"598a","./dv.js":"598a","./el":"8d47","./el.js":"8d47","./en-au":"0e6b","./en-au.js":"0e6b","./en-ca":"3886","./en-ca.js":"3886","./en-gb":"39a6","./en-gb.js":"39a6","./en-ie":"e1d3","./en-ie.js":"e1d3","./en-il":"7333","./en-il.js":"7333","./en-in":"ec2e","./en-in.js":"ec2e","./en-nz":"6f50","./en-nz.js":"6f50","./en-sg":"b7e9","./en-sg.js":"b7e9","./eo":"65db","./eo.js":"65db","./es":"898b","./es-do":"0a3c","./es-do.js":"0a3c","./es-mx":"b5b7","./es-mx.js":"b5b7","./es-us":"55c9","./es-us.js":"55c9","./es.js":"898b","./et":"ec18","./et.js":"ec18","./eu":"0ff2","./eu.js":"0ff2","./fa":"8df48","./fa.js":"8df48","./fi":"81e9","./fi.js":"81e9","./fil":"d69a","./fil.js":"d69a","./fo":"0721","./fo.js":"0721","./fr":"9f26","./fr-ca":"d9f8","./fr-ca.js":"d9f8","./fr-ch":"0e49","./fr-ch.js":"0e49","./fr.js":"9f26","./fy":"7118","./fy.js":"7118","./ga":"5120","./ga.js":"5120","./gd":"f6b46","./gd.js":"f6b46","./gl":"8840","./gl.js":"8840","./gom-deva":"aaf2","./gom-deva.js":"aaf2","./gom-latn":"0caa","./gom-latn.js":"0caa","./gu":"e0c5","./gu.js":"e0c5","./he":"c7aa","./he.js":"c7aa","./hi":"dc4d","./hi.js":"dc4d","./hr":"4ba9","./hr.js":"4ba9","./hu":"5b14","./hu.js":"5b14","./hy-am":"d6b6","./hy-am.js":"d6b6","./id":"5038","./id.js":"5038","./is":"0558","./is.js":"0558","./it":"6e98","./it-ch":"6f12","./it-ch.js":"6f12","./it.js":"6e98","./ja":"079e","./ja.js":"079e","./jv":"b540","./jv.js":"b540","./ka":"201b","./ka.js":"201b","./kk":"6d79","./kk.js":"6d79","./km":"e81d","./km.js":"e81d","./kn":"3e92","./kn.js":"3e92","./ko":"22f8","./ko.js":"22f8","./ku":"2421","./ku-kmr":"7558","./ku-kmr.js":"7558","./ku.js":"2421","./ky":"9609","./ky.js":"9609","./lb":"440c","./lb.js":"440c","./lo":"b29d","./lo.js":"b29d","./lt":"26f9","./lt.js":"26f9","./lv":"b97c","./lv.js":"b97c","./me":"293c","./me.js":"293c","./mi":"688b","./mi.js":"688b","./mk":"6909","./mk.js":"6909","./ml":"02fb","./ml.js":"02fb","./mn":"958b","./mn.js":"958b","./mr":"39bd","./mr.js":"39bd","./ms":"ebe4","./ms-my":"6403","./ms-my.js":"6403","./ms.js":"ebe4","./mt":"1b45","./mt.js":"1b45","./my":"8689","./my.js":"8689","./nb":"6ce3","./nb.js":"6ce3","./ne":"3a39","./ne.js":"3a39","./nl":"facd","./nl-be":"db29","./nl-be.js":"db29","./nl.js":"facd","./nn":"b84c","./nn.js":"b84c","./oc-lnc":"167b","./oc-lnc.js":"167b","./pa-in":"f3ff","./pa-in.js":"f3ff","./pl":"8d57","./pl.js":"8d57","./pt":"f260","./pt-br":"d2d4","./pt-br.js":"d2d4","./pt.js":"f260","./ro":"972c","./ro.js":"972c","./ru":"957c","./ru.js":"957c","./sd":"6784","./sd.js":"6784","./se":"ffff","./se.js":"ffff","./si":"eda5","./si.js":"eda5","./sk":"7be6","./sk.js":"7be6","./sl":"8155","./sl.js":"8155","./sq":"c8f3","./sq.js":"c8f3","./sr":"cf1e","./sr-cyrl":"13e9","./sr-cyrl.js":"13e9","./sr.js":"cf1e","./ss":"52bd","./ss.js":"52bd","./sv":"5fbd","./sv.js":"5fbd","./sw":"74dc","./sw.js":"74dc","./ta":"3de5","./ta.js":"3de5","./te":"5cbb","./te.js":"5cbb","./tet":"576c","./tet.js":"576c","./tg":"3b1b","./tg.js":"3b1b","./th":"10e8","./th.js":"10e8","./tk":"5aff","./tk.js":"5aff","./tl-ph":"0f38","./tl-ph.js":"0f38","./tlh":"cf75","./tlh.js":"cf75","./tr":"0e81","./tr.js":"0e81","./tzl":"cf51","./tzl.js":"cf51","./tzm":"c109","./tzm-latn":"b53d","./tzm-latn.js":"b53d","./tzm.js":"c109","./ug-cn":"6117","./ug-cn.js":"6117","./uk":"ada2","./uk.js":"ada2","./ur":"5294","./ur.js":"5294","./uz":"2e8c","./uz-latn":"010e","./uz-latn.js":"010e","./uz.js":"2e8c","./vi":"2921","./vi.js":"2921","./x-pseudo":"fd7e","./x-pseudo.js":"fd7e","./yo":"7f33","./yo.js":"7f33","./zh-cn":"5c3a","./zh-cn.js":"5c3a","./zh-hk":"49ab","./zh-hk.js":"49ab","./zh-mo":"3a6c","./zh-mo.js":"3a6c","./zh-tw":"90ea","./zh-tw.js":"90ea"};function r(e){var t=i(e);return o(t)}function i(e){if(!o.o(n,e)){var t=new Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}return n[e]}r.keys=function(){return Object.keys(n)},r.resolve=i,e.exports=r,r.id="4678"},"4ba95":function(e,t,o){"use strict";o("4450")},"5fb3":function(e,t,o){"use strict";o.d(t,"h",(function(){return r})),o.d(t,"g",(function(){return i})),o.d(t,"i",(function(){return l})),o.d(t,"e",(function(){return a})),o.d(t,"f",(function(){return u})),o.d(t,"a",(function(){return s})),o.d(t,"n",(function(){return d})),o.d(t,"d",(function(){return c})),o.d(t,"c",(function(){return p})),o.d(t,"j",(function(){return h})),o.d(t,"m",(function(){return b})),o.d(t,"l",(function(){return m})),o.d(t,"k",(function(){return f})),o.d(t,"b",(function(){return y}));var n=o("b775");function r(e){return Object(n["a"])({url:"/system/inventory/list",method:"get",params:e})}function i(e){return Object(n["a"])({url:"/system/inventory/aggregator",method:"get",params:e})}function l(e){return Object(n["a"])({url:"/system/inventory/lists",method:"get",params:e})}function a(e){return Object(n["a"])({url:"/system/inventory/"+e,method:"get"})}function u(e){return Object(n["a"])({url:"/system/inventory/package",method:"get",params:e})}function s(e){return Object(n["a"])({url:"/system/inventory",method:"post",data:e})}function d(e){return Object(n["a"])({url:"/system/inventory",method:"put",data:e})}function c(e){return Object(n["a"])({url:"/system/inventory/"+e,method:"delete"})}function p(e,t){var o={inventoryId:e,status:t};return Object(n["a"])({url:"/system/inventory/changeStatus",method:"put",data:o})}function h(e){return Object(n["a"])({url:"/system/inventory/outbound",method:"put",data:e})}function b(e){return Object(n["a"])({url:"/system/inventory/settlement",method:"put",data:e})}function m(e){return Object(n["a"])({url:"/system/inventory/preOutbound",method:"put",data:e})}function f(e){return Object(n["a"])({url:"/system/inventory/packUp",method:"put",data:e})}function y(e){return Object(n["a"])({url:"/system/inventory/cancelPkg",method:"put",data:e})}},"72f9":function(e,t,o){(function(t,o){e.exports=o()})(0,(function(){function e(i,l){if(!(this instanceof e))return new e(i,l);l=Object.assign({},o,l);var a=Math.pow(10,l.precision);this.intValue=i=t(i,l),this.value=i/a,l.increment=l.increment||1/a,l.groups=l.useVedic?r:n,this.s=l,this.p=a}function t(t,o){var n=!(2<arguments.length&&void 0!==arguments[2])||arguments[2],r=o.decimal,i=o.errorOnInvalid,l=o.fromCents,a=Math.pow(10,o.precision),u=t instanceof e;if(u&&l)return t.intValue;if("number"===typeof t||u)r=u?t.value:t;else if("string"===typeof t)i=new RegExp("[^-\\d"+r+"]","g"),r=new RegExp("\\"+r,"g"),r=(r=t.replace(/\((.*)\)/,"-$1").replace(i,"").replace(r,"."))||0;else{if(i)throw Error("Invalid Input");r=0}return l||(r=(r*a).toFixed(4)),n?Math.round(r):r}var o={symbol:"$",separator:",",decimal:".",errorOnInvalid:!1,precision:2,pattern:"!#",negativePattern:"-!#",format:function(e,t){var o=t.pattern,n=t.negativePattern,r=t.symbol,i=t.separator,l=t.decimal;t=t.groups;var a=(""+e).replace(/^-/,"").split("."),u=a[0];return a=a[1],(0<=e.value?o:n).replace("!",r).replace("#",u.replace(t,"$1"+i)+(a?l+a:""))},fromCents:!1},n=/(\d)(?=(\d{3})+\b)/g,r=/(\d)(?=(\d\d)+\d\b)/g;return e.prototype={add:function(o){var n=this.s,r=this.p;return e((this.intValue+t(o,n))/(n.fromCents?1:r),n)},subtract:function(o){var n=this.s,r=this.p;return e((this.intValue-t(o,n))/(n.fromCents?1:r),n)},multiply:function(t){var o=this.s;return e(this.intValue*t/(o.fromCents?1:Math.pow(10,o.precision)),o)},divide:function(o){var n=this.s;return e(this.intValue/t(o,n,!1),n)},distribute:function(t){var o=this.intValue,n=this.p,r=this.s,i=[],l=Math[0<=o?"floor":"ceil"](o/t),a=Math.abs(o-l*t);for(n=r.fromCents?1:n;0!==t;t--){var u=e(l/n,r);0<a--&&(u=u[0<=o?"add":"subtract"](1/n)),i.push(u)}return i},dollars:function(){return~~this.value},cents:function(){return~~(this.intValue%this.p)},format:function(e){var t=this.s;return"function"===typeof e?e(this,t):t.format(this,Object.assign({},t,e))},toString:function(){var e=this.s,t=e.increment;return(Math.round(this.intValue/this.p/t)*t).toFixed(e.precision)},toJSON:function(){return this.value}},e}))},"82ad":function(e,t,o){"use strict";o.d(t,"h",(function(){return r})),o.d(t,"e",(function(){return i})),o.d(t,"a",(function(){return l})),o.d(t,"k",(function(){return a})),o.d(t,"c",(function(){return u})),o.d(t,"b",(function(){return s})),o.d(t,"i",(function(){return d})),o.d(t,"j",(function(){return c})),o.d(t,"f",(function(){return p})),o.d(t,"g",(function(){return h})),o.d(t,"d",(function(){return b}));var n=o("b775");function r(e){return Object(n["a"])({url:"/system/outboundrecord/list",method:"get",params:e})}function i(e){return Object(n["a"])({url:"/system/outboundrecord/"+e,method:"get"})}function l(e){return Object(n["a"])({url:"/system/outboundrecord",method:"post",data:e})}function a(e){return Object(n["a"])({url:"/system/outboundrecord",method:"put",data:e})}function u(e){return Object(n["a"])({url:"/system/outboundrecord/"+e,method:"delete"})}function s(e,t){var o={outboundRecordId:e,status:t};return Object(n["a"])({url:"/system/outboundrecord/changeStatus",method:"put",data:o})}function d(e){return Object(n["a"])({url:"/system/outboundrecord/outboundrecords",method:"get",params:e})}function c(e){return Object(n["a"])({url:"/system/outboundrecord/listRental",method:"get",params:e})}function p(e){return Object(n["a"])({url:"/system/outboundrecord/outboundrecords/"+e,method:"get"})}function h(e){return Object(n["a"])({url:"/system/outboundrecord/rentals/"+e,method:"get"})}function b(e){return Object(n["a"])({url:"/system/outboundrecord/outboundBill",method:"put",data:e,responseType:"arraybuffer"})}},9129:function(e,t,o){var n=o("23e7");n({target:"Number",stat:!0},{isNaN:function(e){return e!=e}})},9558:function(e,t,o){"use strict";o("bc70")},"9d56":function(e,t,o){"use strict";t["a"]={panels:[{index:0,name:1,paperType:"A4",height:297,width:210,paperHeader:0,paperFooter:634.5,printElements:[{options:{left:103.5,top:85.5,height:28.5,width:97,title:"文本",field:"clientCode",coordinateSync:!1,widthHeightSync:!1,hideTitle:!0,qrCodeLevel:0,fontSize:20,textAlign:"center",fontWeight:"bold",testData:"KNL",textContentVerticalAlign:"middle"},printElementType:{title:"文本",type:"text"}},{options:{left:111,top:117,height:9.75,width:86,title:"文本",field:"subOrderNo",coordinateSync:!1,widthHeightSync:!1,hideTitle:!0,qrCodeLevel:0,testData:"name"},printElementType:{title:"文本",type:"text"}},{options:{left:241.5,top:106.5,height:9.75,width:92,title:"文本",field:"forwarderNo",coordinateSync:!1,widthHeightSync:!1,hideTitle:!0,qrCodeLevel:0},printElementType:{title:"文本",type:"text"}},{options:{left:241.5,top:118.5,height:9.75,width:92,title:"文本",field:"actualInboundTime",coordinateSync:!1,widthHeightSync:!1,hideTitle:!0,qrCodeLevel:0},printElementType:{title:"文本",type:"text"}},{options:{left:90,top:168,height:9.75,width:34.5,title:"文本",right:77.25,bottom:177.75,vCenter:60,hCenter:172.875,field:"sqdShippingMark",coordinateSync:!1,widthHeightSync:!1,hideTitle:!0,qrCodeLevel:0},printElementType:{title:"文本",type:"text"}},{options:{left:127.5,top:166.5,height:9.75,width:85.5,title:"文本",right:115.74609375,bottom:177.99609375,vCenter:98.49609375,hCenter:173.12109375,field:"cargoName",coordinateSync:!1,widthHeightSync:!1,hideTitle:!0,qrCodeLevel:0},printElementType:{title:"文本",type:"text"}},{options:{left:217.5,top:166.5,height:9.75,width:27,title:"文本",right:197.25,bottom:176.25,vCenter:183.75,hCenter:171.375,field:"totalBoxes",coordinateSync:!1,widthHeightSync:!1,hideTitle:!0,qrCodeLevel:0},printElementType:{title:"文本",type:"text"}},{options:{left:246,top:166.5,height:9.75,width:27,title:"文本",right:227.2448272705078,bottom:175.4973907470703,vCenter:213.7448272705078,hCenter:170.6223907470703,field:"totalGrossWeight",coordinateSync:!1,widthHeightSync:!1,hideTitle:!0,qrCodeLevel:0},printElementType:{title:"文本",type:"text"}},{options:{left:280.5,top:166.5,height:9.75,width:27,title:"文本",right:260.4895935058594,bottom:176.49739837646484,vCenter:246.98959350585938,hCenter:171.62239837646484,field:"totalVolume",coordinateSync:!1,widthHeightSync:!1,hideTitle:!0,qrCodeLevel:0},printElementType:{title:"文本",type:"text"}},{options:{left:313.5,top:166.5,height:9.75,width:27,title:"文本",right:292.7395935058594,bottom:176.49739837646484,vCenter:279.2395935058594,hCenter:171.62239837646484,field:"inboundNotes",coordinateSync:!1,widthHeightSync:!1,hideTitle:!0,qrCodeLevel:0},printElementType:{title:"文本",type:"text"}},{options:{left:403.5,top:87,height:13.5,width:51,title:"文本",field:"clientCode",coordinateSync:!1,widthHeightSync:!1,hideTitle:!0,qrCodeLevel:0,right:438.99742126464844,bottom:116.49217987060547,vCenter:397.74742126464844,hCenter:102.24217987060547},printElementType:{title:"文本",type:"text"}},{options:{left:403.5,top:102,height:9.75,width:49.5,title:"文本",field:"subOrderNo",coordinateSync:!1,widthHeightSync:!1,hideTitle:!0,qrCodeLevel:0,right:441.7500305175781,bottom:112.73959350585938,vCenter:398.7500305175781,hCenter:107.86459350585938},printElementType:{title:"文本",type:"text"}},{options:{left:477,top:100.5,height:9.75,width:102,title:"文本",field:"forwarderNo",coordinateSync:!1,widthHeightSync:!1,hideTitle:!0,qrCodeLevel:0,right:538.4895935058594,bottom:111.98959350585938,vCenter:492.4895935058594,hCenter:107.11459350585938},printElementType:{title:"文本",type:"text"}},{options:{left:477,top:118.5,height:9.75,width:100.5,title:"文本",field:"actualInboundTime",coordinateSync:!1,widthHeightSync:!1,hideTitle:!0,qrCodeLevel:0,right:540.7395935058594,bottom:126.25000762939453,vCenter:494.7395935058594,hCenter:121.37500762939453},printElementType:{title:"文本",type:"text"}},{options:{left:406.5,top:120,height:9.75,width:48,title:"文本",field:"supplier",coordinateSync:!1,widthHeightSync:!1,hideTitle:!0,qrCodeLevel:0,right:452.9895935058594,bottom:129.25000762939453,vCenter:406.9895935058594,hCenter:124.37500762939453},printElementType:{title:"文本",type:"text"}},{options:{left:406.5,top:145.5,height:9.75,width:130.5,title:"文本",right:447.2395935058594,bottom:150.9974072277546,vCenter:404.4895935058594,hCenter:146.1224072277546,field:"cargoName",coordinateSync:!1,widthHeightSync:!1,hideTitle:!0,qrCodeLevel:0},printElementType:{title:"文本",type:"text"}},{options:{left:406.5,top:177,height:9.75,width:157.5,title:"文本",right:389.49220275878906,bottom:184.74739837646484,vCenter:375.99220275878906,hCenter:179.87239837646484,field:"inboundNotes",coordinateSync:!1,widthHeightSync:!1,hideTitle:!0,qrCodeLevel:0},printElementType:{title:"文本",type:"text"}},{options:{left:406.5,top:207,height:9.75,width:157.5,title:"文本",right:514.7500762939453,bottom:214.74479484558105,vCenter:436.0000762939453,hCenter:209.86979484558105,field:"driverInfo",coordinateSync:!1,widthHeightSync:!1,hideTitle:!0,qrCodeLevel:0},printElementType:{title:"文本",type:"text"}},{options:{left:406.5,top:237,height:9.75,width:157.5,title:"文本",right:514.7422027587891,bottom:241.75001621246338,vCenter:435.99220275878906,hCenter:236.87501621246338,field:"inboundAddr",coordinateSync:!1,widthHeightSync:!1,hideTitle:!0,qrCodeLevel:0},printElementType:{title:"文本",type:"text"}},{options:{left:406.5,top:252,height:9.75,width:157.5,title:"文本",right:516.2422027587891,bottom:256.7500162124634,vCenter:437.49220275878906,hCenter:251.87501621246338,field:"deliveryDriver",coordinateSync:!1,widthHeightSync:!1,hideTitle:!0,qrCodeLevel:0},printElementType:{title:"文本",type:"text"}},{options:{left:73.5,top:252,height:9.75,width:270,title:"文本",field:"operationRecord",coordinateSync:!1,widthHeightSync:!1,hideTitle:!0,qrCodeLevel:0},printElementType:{title:"文本",type:"text"}},{options:{left:540.75,top:146.25,height:9.75,width:27,title:"文本",right:197.25,bottom:176.25,vCenter:183.75,hCenter:171.375,field:"totalBoxes",coordinateSync:!1,widthHeightSync:!1,hideTitle:!0,qrCodeLevel:0},printElementType:{title:"文本",type:"text"}}],paperNumberLeft:540,paperNumberTop:21,paperNumberDisabled:!0,panelPageRule:"none",watermarkOptions:{content:"",fillStyle:"rgba(184, 184, 184, 0.3)",fontSize:"14px",rotate:25,width:200,height:200,timestamp:!1,format:"YYYY-MM-DD HH:mm"}}]}},bc70:function(e,t,o){},f870:function(e,t,o){"use strict";o.r(t);var n=function(){var e=this,t=e.$createElement,o=e._self._c||t;return o("el-dialog",{attrs:{visible:e.visible,width:"220mm"},on:{"update:visible":function(t){e.visible=t},cancel:e.hideModal}},[o("template",{slot:"title"},[o("el-row",{attrs:{gutter:10}},[o("div",{staticStyle:{"margin-right":"20px"}},[e._v("打印预览")]),o("el-button",{attrs:{loading:e.waitShowPrinter,icon:"printer",type:"primary"},on:{click:function(t){return t.stopPropagation(),e.print(t)}}},[e._v("打印")]),o("el-button",{attrs:{icon:"printer",type:"primary"},on:{click:function(t){return t.stopPropagation(),e.toPdf(t)}}},[e._v("pdf")])],1)],1),o("div",{directives:[{name:"loading",rawName:"v-loading",value:e.spinning,expression:"spinning"}],staticStyle:{"min-height":"120px"}},[o("div",{attrs:{id:"preview_content_design"}})])],2)},r=[],i=o("c1df"),l=o.n(i),a={name:"printPreview",props:["customWidth"],data:function(){return{visible:!1,spinning:!0,waitShowPrinter:!1,width:this.customWidth?this.customWidth:"220mm",hiprintTemplate:{},printData:{},pdfName:null}},computed:{},watch:{},created:function(){},mounted:function(){},methods:{hideModal:function(){this.visible=!1},show:function(e,t){var o=this,n=arguments.length>2&&void 0!==arguments[2]?arguments[2]:"210";this.visible=!0,this.spinning=!0,this.pdfName=t.pdfName?t.pdfName:l()().format("YYYYMMDD"),this.width=e.editingPanel?e.editingPanel.width:n,this.hiprintTemplate=e,this.printData=t,setTimeout((function(){$("#preview_content_design").html(e.getHtml(t)),o.spinning=!1}),500)},print:function(e,t){var o=this;this.hiprintTemplate=e||this.hiprintTemplate,this.printData=t||this.printData,this.waitShowPrinter=!0,this.hiprintTemplate.print(this.printData,{},{callback:function(){console.log("callback"),o.waitShowPrinter=!1}})},toPdf:function(){this.hiprintTemplate.toPdf(this.printData,this.pdfName)}}},u=a,s=(o("4ba95"),o("2877")),d=Object(s["a"])(u,n,r,!1,null,"f7e23638",null);t["default"]=d.exports}}]);