(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-7ddd66a2"],{"30ec":function(e,t,a){"use strict";a("edda")},8586:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("el-row",[a("el-col",{attrs:{span:e.showLeft}},[e.showSearch?a("el-form",{ref:"queryForm",staticClass:"query",attrs:{inline:!0,model:e.queryParams,size:e.size,"label-width":"35px"}},["1"===e.roleTypeId?a("el-form-item",{attrs:{label:"所属",prop:"queryStaffId"}},[a("treeselect",{staticStyle:{width:"100%"},attrs:{"disable-branch-nodes":!0,"disable-fuzzy-matching":!0,"flatten-search-results":!0,normalizer:e.staffNormalizer,options:e.belongList,"show-count":!0,placeholder:"所属人"},on:{input:e.cleanBFStaffId,open:e.loadSales,select:e.handleSelectBFStaffId},scopedSlots:e._u([{key:"value-label",fn:function(t){var r=t.node;return a("div",{},[e._v(" "+e._s(void 0!=r.raw.staff?r.raw.staff.staffFamilyLocalName+r.raw.staff.staffGivingLocalName+" "+r.raw.staff.staffGivingEnName:"")+" ")])}},{key:"option-label",fn:function(t){var r=t.node,o=t.shouldShowCount,s=t.count,n=t.labelClassName,i=t.countClassName;return a("label",{class:n},[e._v(" "+e._s(-1!=r.label.indexOf(",")?r.label.substring(0,r.label.indexOf(",")):r.label)+" "),o?a("span",{class:i},[e._v("("+e._s(s)+")")]):e._e()])}}],null,!1,*********),model:{value:e.queryBFStaffId,callback:function(t){e.queryBFStaffId=t},expression:"queryBFStaffId"}})],1):e._e(),"2"===e.roleTypeId?a("el-form-item",{attrs:{label:"所属",prop:"queryStaffId"}},[a("treeselect",{staticStyle:{width:"100%"},attrs:{"disable-branch-nodes":!0,"disable-fuzzy-matching":!0,"flatten-search-results":!0,normalizer:e.staffNormalizer,options:e.businessList,"show-count":!0,placeholder:"所属人"},on:{input:e.cleanBStaffId,open:e.loadBusinesses,select:e.handleSelectBStaffId},scopedSlots:e._u([{key:"value-label",fn:function(t){var r=t.node;return a("div",{},[e._v(" "+e._s(void 0!=r.raw.staff?r.raw.staff.staffFamilyLocalName+r.raw.staff.staffGivingLocalName+" "+r.raw.staff.staffGivingEnName:"")+" ")])}},{key:"option-label",fn:function(t){var r=t.node,o=t.shouldShowCount,s=t.count,n=t.labelClassName,i=t.countClassName;return a("label",{class:n},[e._v(" "+e._s(-1!=r.label.indexOf(",")?r.label.substring(0,r.label.indexOf(",")):r.label)+" "),o?a("span",{class:i},[e._v("("+e._s(s)+")")]):e._e()])}}],null,!1,*********),model:{value:e.queryBStaffId,callback:function(t){e.queryBStaffId=t},expression:"queryBStaffId"}})],1):e._e(),a("el-form-item",{attrs:{label:"搜索",prop:"companyQuery"}},[a("el-input",{staticStyle:{width:"100%"},attrs:{clearable:"",placeholder:"名称/代码"},on:{change:e.handleQuery},model:{value:e.queryParams.companyQuery,callback:function(t){e.$set(e.queryParams,"companyQuery",t)},expression:"queryParams.companyQuery"}})],1),a("el-form-item",{attrs:{label:"地址",prop:"locationId"}},[a("location-select",{staticStyle:{width:"100%"},attrs:{"load-options":e.locationOptions,multiple:!1,pass:e.queryParams.locationId},on:{return:e.queryLocationId}})],1),a("el-form-item",{attrs:{label:"角色",prop:"roleIds"}},[a("tree-select",{staticStyle:{width:"100%"},attrs:{"d-load":!0,flat:!1,multiple:!0,pass:e.queryParams.roleIds,placeholder:"公司角色",type:"companyRole"},on:{return:e.queryCompanyRoleIds}})],1),a("el-form-item",{attrs:{label:"服务",prop:"serviceTypeIds"}},[a("tree-select",{staticStyle:{width:"100%"},attrs:{"d-load":!0,flat:!1,multiple:!0,pass:e.queryParams.serviceTypeIds,placeholder:"服务类型",type:"serviceType"},on:{return:e.queryServiceTypeIds}})],1),a("el-form-item",{attrs:{label:"启运",prop:"locationDepartureIds"}},[a("location-select",{staticStyle:{width:"100%"},attrs:{"load-options":e.locationOptions,multiple:!0,pass:e.queryParams.locationDepartureIds},on:{return:e.queryLocationDepartureIds}})],1),a("el-form-item",{attrs:{label:"目的",prop:"locationDestinationIds"}},[a("location-select",{staticStyle:{width:"100%"},attrs:{en:!0,"load-options":e.locationOptions,multiple:!0,pass:e.queryParams.locationDestinationIds},on:{return:e.queryLocationDestinationIds}})],1),a("el-form-item",{attrs:{label:"航线",prop:"lineDestinationIds"}},[a("tree-select",{staticStyle:{width:"100%"},attrs:{"d-load":!0,flat:!1,multiple:!0,pass:e.queryParams.lineDestinationIds,placeholder:"目的航线",type:"line"},on:{return:e.queryLineDestinationIds}})],1),a("el-form-item",{attrs:{label:"货物",prop:"cargoTypeIds"}},[a("tree-select",{staticStyle:{width:"100%"},attrs:{"d-load":!0,flat:!1,multiple:!0,pass:e.queryParams.cargoTypeIds,placeholder:"货物特征",type:"cargoType"},on:{return:e.queryCargoTypeIds}})],1),a("el-form-item",{attrs:{label:"承运",prop:"carrierIds"}},[a("treeselect",{staticStyle:{width:"100%"},attrs:{"disable-branch-nodes":!0,"disable-fuzzy-matching":!0,flat:!0,"flatten-search-results":!0,multiple:!0,normalizer:e.carrierNormalizer,options:e.carrierList,"show-count":!0,placeholder:"优选承运人"},on:{deselect:e.handleDeselectQueryCarrierIds,open:e.loadCarrier,select:e.handleSelectQueryCarrierIds},scopedSlots:e._u([{key:"value-label",fn:function(t){var r=t.node;return a("div",{},[e._v(" "+e._s(r.raw.carrier.carrierIntlCode)+" "+e._s(null==r.raw.carrier.carrierIntlCode?r.raw.carrier.carrierShortName:"")+" ")])}},{key:"option-label",fn:function(t){var r=t.node,o=t.shouldShowCount,s=t.count,n=t.labelClassName,i=t.countClassName;return a("label",{class:n},[e._v(" "+e._s(-1!=r.label.indexOf(",")?r.label.substring(0,r.label.indexOf(",")):r.label)+" "),o?a("span",{class:i},[e._v("("+e._s(s)+")")]):e._e()])}}],null,!1,4006291921),model:{value:e.queryCarrierIds,callback:function(t){e.queryCarrierIds=t},expression:"queryCarrierIds"}})],1),a("el-form-item",{attrs:{label:"评级",prop:"creditLevel"}},[a("el-input",{staticStyle:{width:"100%"},attrs:{placeholder:"A~E"},on:{change:e.handleQuery},model:{value:e.queryParams.creditLevel,callback:function(t){e.$set(e.queryParams,"creditLevel",t)},expression:"queryParams.creditLevel"}})],1),a("el-form-item",[a("el-button",{attrs:{size:e.size,icon:"el-icon-search",type:"primary"},on:{click:e.handleQuery}},[e._v("搜索")]),a("el-button",{attrs:{size:e.size,icon:"el-icon-refresh"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1):e._e()],1),a("el-col",{attrs:{span:e.showRight}},[a("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"},{name:"dialogDragWidth",rawName:"v-dialogDragWidth"}],attrs:{"close-on-click-modal":!1,"modal-append-to-body":!1,visible:e.merge,title:"选择保留公司",width:"800px"},on:{"update:visible":function(t){e.merge=t}}},[e.mergeList.length>0?a("el-row",[a("el-col",{attrs:{span:12}},[a("el-descriptions",{attrs:{border:"",direction:"vertical",title:"公司1"}},[a("el-descriptions-item",{attrs:{label:"简称"}},[e._v(e._s(e.mergeList[0].companyShortName))]),a("el-descriptions-item",{attrs:{label:"中文名"}},[e._v(e._s(e.mergeList[0].companyLocalName))]),a("el-descriptions-item",{attrs:{label:"英文名"}},[e._v(e._s(e.mergeList[0].companyEnName))]),a("el-descriptions-item",{attrs:{label:"选择"}},[a("el-button",{on:{click:function(t){return e.handleMerge(e.mergeList[0].companyId,e.mergeList[1].companyId)}}},[e._v(" 留下"+e._s(e.mergeList[0].companyShortName)+" ")])],1)],1)],1),a("el-col",{attrs:{span:12}},[a("el-descriptions",{attrs:{border:"",direction:"vertical",title:"公司2"}},[a("el-descriptions-item",{attrs:{label:"简称"}},[e._v(e._s(e.mergeList[1].companyShortName))]),a("el-descriptions-item",{attrs:{label:"中文名"}},[e._v(e._s(e.mergeList[1].companyLocalName))]),a("el-descriptions-item",{attrs:{label:"英文名"}},[e._v(e._s(e.mergeList[1].companyEnName))]),a("el-descriptions-item",{attrs:{label:"选择"}},[a("el-button",{on:{click:function(t){return e.handleMerge(e.mergeList[1].companyId,e.mergeList[0].companyId)}}},[e._v(" 留下"+e._s(e.mergeList[1].companyShortName)+" ")])],1)],1)],1)],1):e._e()],1),e.refreshTable?a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.companyList,border:""},on:{"selection-change":e.handleSelectionChange}},[e._l(e.columns,(function(t){return t.visible?a("el-table-column",{key:t.key,attrs:{align:t.align,label:t.label,width:t.width},scopedSlots:e._u([{key:"default",fn:function(r){return[a(t.prop,{tag:"component",attrs:{scope:r},on:{return:e.getReturn}})]}}],null,!0)}):e._e()})),a("el-table-column",{attrs:{align:"center",label:"结款方式",width:"70"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("h6",{staticStyle:{margin:"0"}},[e._v(e._s())])]}}],null,!1,*********)}),a("el-table-column",{attrs:{align:"center",label:"额度",width:"70"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",{staticStyle:{margin:"0"}},[e._v(e._s(t.row.agreementCurrencyCode))]),a("span",{staticStyle:{margin:"0"}},[e._v(e._s(e.formatDisplayCreditLimit(t.row.creditLimit)))])]}}],null,!1,2654794016)}),a("el-table-column",{attrs:{align:"center",label:"录入人",width:"70"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("h6",{staticStyle:{margin:"0"}},[e._v(e._s(t.row.updateByName))]),a("h6",{staticStyle:{margin:"0"}},[e._v(e._s(e.parseTime(t.row.updateTime,"{y}.{m}.{d}")))])]}}],null,!1,1439469094)}),a("el-table-column",{attrs:{align:"center","class-name":"small-padding fixed-width",label:"操作",width:"90px"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:company:edit"],expression:"['system:company:edit']"}],staticStyle:{margin:"0 3px"},attrs:{size:e.size,icon:"el-icon-edit",type:"success"},on:{click:function(a){return e.handleSelect(t.row)}}},[e._v("选中 ")])]}}],null,!1,2250204597)})],2):e._e(),a("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total>0"}],attrs:{limit:e.queryParams.pageSize,page:e.queryParams.pageNum,total:e.total},on:{"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},pagination:e.getList}})],1)],1),a("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"},{name:"dialogDragWidth",rawName:"v-dialogDragWidth"}],attrs:{"close-on-click-modal":!1,title:e.title,visible:e.openCompany,"append-to-body":"",width:"800px"},on:{"update:visible":function(t){e.openCompany=t}}},[a("el-form",{ref:"form",staticClass:"edit",attrs:{model:e.form,rules:e.rules,"label-width":"68px"}},["1"==e.roleTypeId?a("el-row",[a("el-divider",{attrs:{"content-position":"left"}},[e._v("从属信息")]),a("el-row",[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"业务员"}},[a("treeselect",{staticClass:"sss",attrs:{"disable-branch-nodes":!0,"disable-fuzzy-matching":!0,disabled:!e.add,"flatten-search-results":!0,normalizer:e.staffNormalizer,options:e.belongList,"show-count":!0,placeholder:"选择所属人"},on:{input:e.handleDeselectBelongTo,open:e.loadSales,select:e.handleSelectBelongTo},scopedSlots:e._u([{key:"value-label",fn:function(t){var r=t.node;return a("div",{},[e._v(" "+e._s(void 0!=r.raw.staff?r.raw.staff.staffFamilyLocalName+r.raw.staff.staffGivingLocalName+" "+r.raw.staff.staffGivingEnName:"")+" ")])}},{key:"option-label",fn:function(t){var r=t.node,o=t.shouldShowCount,s=t.count,n=t.labelClassName,i=t.countClassName;return a("label",{class:n},[e._v(" "+e._s(-1!=r.label.indexOf(",")?r.label.substring(0,r.label.indexOf(",")):r.label)+" "),o?a("span",{class:i},[e._v("("+e._s(s)+")")]):e._e()])}}],null,!1,*********),model:{value:e.belongTo,callback:function(t){e.belongTo=t},expression:"belongTo"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"业务助理"}},[a("treeselect",{staticClass:"sss",attrs:{"disable-branch-nodes":!0,"disable-fuzzy-matching":!0,"flatten-search-results":!0,normalizer:e.staffNormalizer,options:e.belongList,"show-count":!0,placeholder:"业务员自己跟进的情况无须填写"},on:{input:e.handleDeselectFollowUp,open:e.loadSales,select:e.handleSelectFollowUp},scopedSlots:e._u([{key:"value-label",fn:function(t){var r=t.node;return a("div",{},[e._v(" "+e._s(void 0!=r.raw.staff?r.raw.staff.staffFamilyLocalName+r.raw.staff.staffGivingLocalName+" "+r.raw.staff.staffGivingEnName:"")+" ")])}},{key:"option-label",fn:function(t){var r=t.node,o=t.shouldShowCount,s=t.count,n=t.labelClassName,i=t.countClassName;return a("label",{class:n},[e._v(" "+e._s(-1!=r.label.indexOf(",")?r.label.substring(0,r.label.indexOf(",")):r.label)+" "),o?a("span",{class:i},[e._v("("+e._s(s)+")")]):e._e()])}}],null,!1,*********),model:{value:e.followUp,callback:function(t){e.followUp=t},expression:"followUp"}})],1)],1)],1)],1):e._e(),a("el-row",[a("el-divider",{attrs:{"content-position":"left"}},[e._v("基本信息")]),a("el-row",[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"单位代码",prop:"companyTaxCode"}},[a("el-input",{staticClass:"sss",attrs:{disabled:"",placeholder:"国际通用简称"},model:{value:e.form.companyTaxCode,callback:function(t){e.$set(e.form,"companyTaxCode",t)},expression:"form.companyTaxCode"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"信用等级",prop:"creditLevel"}},[a("tree-select",{staticClass:"sss",attrs:{disabled:e.isLock,pass:e.form.creditLevel,type:"creditLevel"},on:{return:e.getcreditLevel}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{rules:[{required:null==e.form.companyEnShortName||0==e.form.companyEnShortName.length,trigger:"blur"}],label:"中文简称",prop:"companyShortName"}},[a("el-input",{staticClass:"sss",attrs:{disabled:e.isLock,placeholder:"公司简称"},model:{value:e.form.companyShortName,callback:function(t){e.$set(e.form,"companyShortName",t)},expression:"form.companyShortName"}})],1)],1),a("el-col",{attrs:{span:16}},[a("el-form-item",{attrs:{label:"中文全称",prop:"companyLocalName"}},[a("el-input",{attrs:{disabled:e.isLock,placeholder:"公司母语名"},model:{value:e.form.companyLocalName,callback:function(t){e.$set(e.form,"companyLocalName",t)},expression:"form.companyLocalName"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{rules:[{required:null==e.form.companyShortName||0==e.form.companyShortName.length,trigger:"blur"}],label:"英文简称",prop:"companyEnShortName"}},[a("el-input",{staticClass:"sss",staticStyle:{"padding-left":"5px"},attrs:{disabled:e.isLock,placeholder:"公司简称"},model:{value:e.form.companyEnShortName,callback:function(t){e.$set(e.form,"companyEnShortName",t)},expression:"form.companyEnShortName"}})],1)],1),a("el-col",{attrs:{span:16}},[a("el-form-item",{attrs:{label:"英文全称",prop:"companyEnName"}},[a("el-input",{staticStyle:{"padding-left":"5px"},attrs:{disabled:e.isLock,placeholder:"公司英文名"},model:{value:e.form.companyEnName,callback:function(t){e.$set(e.form,"companyEnName",t)},expression:"form.companyEnName"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"行政区域",prop:"locationId"}},[a("location-select",{staticClass:"sss",attrs:{"load-options":e.locationOptions,multiple:!1,pass:e.form.locationId},on:{return:e.getLocationId}})],1)],1),a("el-col",{attrs:{span:16}},[a("el-form-item",{attrs:{label:"详细地址",prop:"locationDetail"}},[a("el-input",{staticStyle:{"padding-left":"5px"},attrs:{placeholder:"详细地址信息"},model:{value:e.form.locationDetail,callback:function(t){e.$set(e.form,"locationDetail",t)},expression:"form.locationDetail"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"客户来源",prop:"sourceId"}},[a("tree-select",{staticClass:"sss",attrs:{pass:e.form.sourceId,type:"source"},on:{return:e.getSourceId}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"所属组织",prop:"organizationIds"}},[a("tree-select",{staticClass:"sss",attrs:{multiple:!0,pass:e.form.organizationIds,type:"organization"},on:{return:e.getOrganizationIds}})],1)],1),a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"备注",prop:"remark"}},[a("el-input",{staticStyle:{"padding-left":"5px"},attrs:{autosize:{minRows:2,maxRows:10},maxlength:"150",placeholder:"备注","show-word-limit":"",type:"textarea"},model:{value:e.form.remark,callback:function(t){e.$set(e.form,"remark",t)},expression:"form.remark"}})],1)],1)],1)],1),a("el-row",[a("el-divider",{attrs:{"content-position":"left"}},[e._v(" 分类信息 ")]),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"公司角色",prop:"roleIds"}},[a("tree-select",{staticClass:"sss",attrs:{flat:!1,multiple:!0,pass:e.form.roleIds,type:"companyRole"},on:{return:e.getCompanyRoleIds}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"服务类型",prop:"serviceTypeIds"}},[a("tree-select",{staticClass:"sss",attrs:{flat:!1,multiple:!0,pass:e.form.serviceTypeIds,type:"serviceType"},on:{return:e.getServiceTypeIds}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"货物特征",prop:"cargoTypeIds"}},[a("tree-select",{staticClass:"sss",attrs:{flat:!1,multiple:!0,pass:e.form.cargoTypeIds,type:"cargoType"},on:{return:e.getCargoTypeIds}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"优选承运",prop:"carrierIds"}},[a("treeselect",{staticClass:"sss",attrs:{"disable-branch-nodes":!0,"disable-fuzzy-matching":!0,flat:!0,"flatten-search-results":!0,multiple:!0,normalizer:e.carrierNormalizer,options:e.temCarrierList,"show-count":!0,placeholder:"选择承运人"},on:{deselect:e.handleDeselectCarrierIds,open:e.loadCarrier,select:e.handleSelectCarrierIds},scopedSlots:e._u([{key:"value-label",fn:function(t){var r=t.node;return a("div",{},[e._v(" "+e._s(r.raw.carrier.carrierIntlCode)+" "+e._s(null==r.raw.carrier.carrierIntlCode?r.raw.carrier.carrierShortName:"")+" ")])}},{key:"option-label",fn:function(t){var r=t.node,o=t.shouldShowCount,s=t.count,n=t.labelClassName,i=t.countClassName;return a("label",{class:n},[e._v(" "+e._s(-1!=r.label.indexOf(",")?r.label.substring(0,r.label.indexOf(",")):r.label)+" "),o?a("span",{class:i},[e._v("("+e._s(s)+")")]):e._e()])}}]),model:{value:e.carrierIds,callback:function(t){e.carrierIds=t},expression:"carrierIds"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"启运区域",prop:"locationDepartureIds"}},[a("location-select",{staticClass:"sss",staticStyle:{"padding-left":"5px"},attrs:{"load-options":e.locationOptions,multiple:!0,pass:e.form.locationDepartureIds},on:{return:e.getLocationDepartureIds}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"目的区域",prop:"locationDestinationIds"}},[a("location-select",{staticClass:"sss",staticStyle:{"padding-left":"5px"},attrs:{en:!0,"load-options":e.locationOptions,multiple:!0,pass:e.form.locationDestinationIds},on:{return:e.getLocationDestinationIds}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"目的航线",prop:"lineDestinationIds"}},[a("tree-select",{staticClass:"sss",staticStyle:{"padding-left":"5px"},attrs:{flat:!1,multiple:!0,pass:e.form.lineDestinationIds,type:"line"},on:{return:e.getLineDestinationIds}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"开发权重",prop:"companyIntlCode"}},[a("el-input",{staticClass:"sss",staticStyle:{"padding-left":"5px",width:"100%"},attrs:{placeholder:"重要程度"},model:{value:e.form.companyIntlCode,callback:function(t){e.$set(e.form,"companyIntlCode",t)},expression:"form.companyIntlCode"}})],1)],1)],1)],1),a("el-row",[a("el-divider",{attrs:{"content-position":"left"}},[e._v("协议信息")]),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"协议号",prop:"agreementNumber"}},[a("el-input",{staticClass:"sss",staticStyle:{"padding-left":"5px"},attrs:{disabled:e.isLock,placeholder:"协议号"},model:{value:e.form.agreementNumber,callback:function(t){e.$set(e.form,"agreementNumber",t)},expression:"form.agreementNumber"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"协议时间",prop:"agreementStartDate"}},[a("el-date-picker",{staticClass:"sss date-select",staticStyle:{"margin-left":"5px"},attrs:{disabled:e.isLock,"end-placeholder":"协议结束日期","range-separator":"至","start-placeholder":"协议开始日期","default-time":["00:00:00","23:59:59"],"value-format":"yyyy-MM-dd HH:mm:ss",type:"daterange"},on:{input:e.changeDate},model:{value:e.form.agreementDateRange,callback:function(t){e.$set(e.form,"agreementDateRange",t)},expression:"form.agreementDateRange"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"信用额度",prop:"creditLimit"}},[a("div",{staticClass:"creditLimit"},[a("tree-select",{staticClass:"sss currency",attrs:{disabled:e.isLock,pass:e.form.agreementCurrencyCode,type:"currency"},on:{return:e.getCurrencyCode}}),a("el-input",{staticClass:"sss limit",staticStyle:{"padding-left":"5px"},attrs:{disabled:e.isLock,placeholder:"信用额度(填入整数)"},on:{change:e.formatCreditLimit},model:{value:e.form.creditLimit,callback:function(t){e.$set(e.form,"creditLimit",t)},expression:"form.creditLimit"}})],1)])],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"信用周期",prop:"creditCycleMonth"}},[a("el-input",{staticStyle:{"padding-left":"5px"},attrs:{disabled:e.isLock,placeholder:"信用周期(自然月)"},model:{value:e.form.creditCycleMonth,callback:function(t){e.$set(e.form,"creditCycleMonth",t)},expression:"form.creditCycleMonth"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"放单方式",prop:"agreementTypeId"}},[a("tree-select",{staticClass:"sss",attrs:{disabled:e.isLock,flat:!1,multiple:!1,pass:e.form.releaseType,placeholder:"收款方式",type:"releaseType"},on:{return:function(t){e.form.releaseType=t}}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"结款日",prop:"settlementDate"}},[a("el-input",{staticClass:"sss",staticStyle:{"padding-left":"5px"},attrs:{disabled:e.isLock,placeholder:"结款日(每月的几号)"},model:{value:e.form.settlementDate,callback:function(t){e.$set(e.form,"settlementDate",t)},expression:"form.settlementDate"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"收付路径",prop:"creditLevel"}},[a("tree-select",{staticStyle:{"padding-left":"5px"},attrs:{"d-load":!0,disabled:e.isLock,flat:!1,multiple:!0,pass:e.form.rsPaymentTitles,placeholder:"收付路径",type:"rsPaymentTitle"},on:{return:e.getRsPaymentTitle}})],1)],1)],1),a("el-row",[a("el-form-item",{attrs:{label:"协议备注",prop:"agreementRemark"}},[a("el-input",{staticStyle:{"padding-left":"5px"},attrs:{autosize:{minRows:2,maxRows:10},disabled:e.isLock,maxlength:"150",placeholder:"协议备注","show-word-limit":"",type:"textarea"},model:{value:e.form.agreementRemark,callback:function(t){e.$set(e.form,"agreementRemark",t)},expression:"form.agreementRemark"}})],1)],1)],1),e.showConfirm?a("el-row",[a("el-divider",{attrs:{"content-position":"left"}},[e._v(" 审核意见 ")]),a("el-row",[a("el-col",{attrs:{span:4}},[a("confirmed",{attrs:{id:"companyId",confirmed:0==this.form.salesConfirmed,row:e.form,type:"sales"},on:{lockMethod:e.updateCompany}})],1),a("el-col",{attrs:{span:4}},[a("confirmed",{attrs:{id:"companyId",confirmed:0==this.form.psaConfirmed,row:e.form,type:"psa"},on:{lockMethod:e.updateCompany}})],1),a("el-col",{attrs:{span:4}},[a("confirmed",{attrs:{id:"companyId",confirmed:0==this.form.opConfirmed,row:e.form,type:"op"},on:{lockMethod:e.updateCompany}})],1),a("el-col",{attrs:{span:4}},[a("confirmed",{attrs:{id:"companyId",confirmed:0==this.form.accConfirmed,row:e.form,type:"acc"},on:{lockMethod:e.updateCompany}})],1)],1)],1):e._e()],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("确 定")]),e.edit?e._e():a("el-button",{attrs:{size:e.size,type:"primary"},on:{click:e.querySame}},[e._v("查重")]),a("el-button",{on:{click:e.cancel}},[e._v("取 消")])],1)],1),a("div",[a("staff-info",{attrs:{company:e.companyInfo,"load-options":e.staffList,open:e.openStaff},on:{openStaffs:e.cancel,refresh:e.getList}}),a("account-info",{attrs:{company:e.companyInfo,"is-lock":e.isLock,"load-options":e.accountList,open:e.openAccount,type:"company"},on:{openAccounts:e.accountCancel}}),a("communications",{attrs:{company:e.companyInfo,"load-options":e.communicationList,open:e.openCommunication,totle:e.ctotle},on:{openCommunications:e.cancel}}),a("agreement-record",{attrs:{company:e.companyInfo,"load-options":e.agreementList,open:e.openAgreement,totle:e.atotle},on:{openCommunications:e.cancel}}),a("BlackList",{attrs:{company:e.companyInfo,open:e.openBlackList},on:{openBlackList:e.cancel}})],1)],1)},o=[],s=a("c7eb"),n=a("5530"),i=a("1da1"),l=a("b85c"),c=(a("caad"),a("2532"),a("14d9"),a("d3b7"),a("25f0"),a("b0c0"),a("d81d"),a("ac1f"),a("5319"),a("4de4"),a("aff7")),d=a("3528"),m=a("0503"),f=a("998b"),u=a("3964"),p=a("b0b8"),h=a.n(p),y=a("4360"),g=a("ca17"),I=a.n(g),v=(a("6f8d"),a("388a")),b=a("59e5"),L=a("e0aa"),C=a("8f75"),S=a("9dd5"),D=a("e18c"),w=a("a603"),T=a("e9b7"),N=a("0313"),k=a("9567"),_=a("2ed4"),q=a("1975"),O=a("1ce2"),x=a("89da"),$=a("9c4f"),P=a("ac5e"),j=a("c06f"),z=a("1296"),E=a("8256"),B=a("18f1"),F=a("c747"),R=a("dce4"),U=a("fba1"),Q=a("5c96"),M=a("37ff"),A=a("1401"),G={name:"SelectCompany",dicts:["sys_is_idle"],components:{Confirmed:M["a"],Treeselect:I.a,communication:j["default"],communications:b["default"],BlackList:v["a"],belong:F["default"],company:D["default"],contactor:w["default"],staffInfo:C["default"],location:T["default"],role:N["default"],serviceType:k["default"],departure:_["default"],destination:q["default"],cargoType:O["default"],carrier:x["default"],account:$["default"],accountInfo:S["default"],agreement:P["default"],agreementRecord:L["default"],grade:z["default"],achievement:E["default"],remark:B["default"],rsPaymentTitle:A["default"]},props:["roleTypeId"],data:function(){return{loading:!0,showLeft:3,showRight:21,single:!0,multiple:!0,ids:[],showSearch:!0,total:0,add:!1,selectTwo:!0,size:this.$store.state.app.size||"mini",mergeList:[],companyList:[],staffList:[],accountList:[],communicationList:[],agreementList:[],belongList:[],carrierList:[],businessList:[],temCarrierList:[],locationOptions:[],carrierIds:[],companyInfo:{},queryCarrierIds:[],title:"",merge:!1,openCompany:!1,openStaff:!1,openAccount:!1,openCommunication:!1,openAgreement:!1,openBlackList:!1,edit:!1,belongTo:null,followUp:null,queryBFStaffId:null,queryBStaffId:null,refreshTable:!0,ctotle:null,atotle:null,queryParams:{pageNum:1,pageSize:20,roleTypeId:this.roleTypeId,companyQuery:null,locationId:null,idleStatus:null,queryStaffId:null,showPriority:null,serviceTypeIds:[],cargoTypeIds:[],locationDepartureIds:[],lineDepartureIds:[],locationDestinationIds:[],lineDestinationIds:[],roleIds:[],carrierIds:[]},form:{agreementStartDate:null,agreementEndDate:null,settlementDate:null},rules:{},companyRow:null,isLock:!0,showConfirm:!1}},computed:{columns:{get:function(){return"2"==this.roleTypeId?this.$store.state.listSettings.supplierSetting:"1"==this.roleTypeId?this.$store.state.listSettings.clientSetting:void 0}}},watch:{showSearch:function(e){1==e?(this.showRight=21,this.showLeft=3):(this.showRight=24,this.showLeft=0)},queryStaffId:function(){this.queryParams.queryStaffId=this.queryStaffId},"form.belongTo":function(){this.form.belongTo==this.form.followUp&&(this.form.followUp=0,this.followUp=null)},"form.serviceTypeIds":function(e){this.loadCarrier();var t=[];if(void 0!=this.carrierList&&null!=e&&e.includes(-1)){this.temCarrierList=this.carrierList;var a,r=Object(l["a"])(this.carrierList);try{for(r.s();!(a=r.n()).done;){var o=a.value;if(void 0!=o.children&&o.children.length>0){var s,n=Object(l["a"])(o.children);try{for(n.s();!(s=n.n()).done;){var i=s.value;if(void 0!=i.children&&i.children.length>0){var c,d=Object(l["a"])(i.children);try{for(d.s();!(c=d.n()).done;){var m=c.value;null!=this.form.carrierIds&&this.form.carrierIds.includes(m.carrier.carrierId)&&!this.carrierIds.includes(m.serviceTypeId)&&this.carrierIds.push(m.serviceTypeId)}}catch(N){d.e(N)}finally{d.f()}}}}catch(N){n.e(N)}finally{n.f()}}}}catch(N){r.e(N)}finally{r.f()}}if(void 0!=this.carrierList&&null!=e&&!e.includes(-1)){var f,u=Object(l["a"])(this.carrierList);try{for(u.s();!(f=u.n()).done;){var p=f.value;if(null!=e&&void 0!=e){var h,y=Object(l["a"])(e);try{for(y.s();!(h=y.n()).done;){var g=h.value;if(p.serviceTypeId==g&&t.push(p),void 0!=p.children&&p.children.length>0){var I,v=Object(l["a"])(p.children);try{for(v.s();!(I=v.n()).done;){var b=I.value;b.serviceTypeId==g&&t.push(b)}}catch(N){v.e(N)}finally{v.f()}}}}catch(N){y.e(N)}finally{y.f()}}}}catch(N){u.e(N)}finally{u.f()}if(this.temCarrierList=t,this.temCarrierList.length>0){var L,C=Object(l["a"])(this.temCarrierList);try{for(C.s();!(L=C.n()).done;){var S=L.value;if(void 0!=S.children&&S.children.length>0){var D,w=Object(l["a"])(S.children);try{for(w.s();!(D=w.n()).done;){var T=D.value;null!=this.form.carrierIds&&this.form.carrierIds.includes(T.carrier.carrierId)&&!this.carrierIds.includes(T.serviceTypeId)&&this.carrierIds.push(T.serviceTypeId)}}catch(N){w.e(N)}finally{w.f()}}}}catch(N){C.e(N)}finally{C.f()}}}},form:function(){1==this.form.salesConfirmed||1==this.form.accConfirmed||1==this.form.psaConfirmed||1==this.form.opConfirmed?this.isLock=!0:this.isLock=!1}},created:function(){var e=this;this.getList().then((function(){e.loadBusinesses(),e.loadCarrier(),e.loadSales()}))},methods:{handleSelect:function(e){this.$emit("return",e)},loadSales:function(){var e=this;0==this.$store.state.data.salesList.length||this.$store.state.data.redisList.salesList?y["a"].dispatch("getSalesListC").then((function(){e.belongList=e.$store.state.data.salesList})):this.belongList=this.$store.state.data.salesList},loadBusinesses:function(){var e=this;0==this.$store.state.data.businessesList.length||this.$store.state.data.redisList.businessesList?y["a"].dispatch("getBusinessesList").then((function(){e.businessList=e.$store.state.data.businessesList})):this.businessList=this.$store.state.data.businessesList},loadCarrier:function(){var e=this;0==this.$store.state.data.serviceTypeCarriers.length||this.$store.state.data.redisList.serviceTypeCarriers?y["a"].dispatch("getServiceTypeCarriersList").then((function(){e.carrierList=e.$store.state.data.serviceTypeCarriers})):this.carrierList=this.$store.state.data.serviceTypeCarriers},querySame:function(){var e=this,t={cargoTypeIds:[],locationDepartureIds:[],locationDestinationIds:[],lineDepartureIds:[],lineDestinationIds:[],companyShortName:this.form.companyShortName,companyEnShortName:this.form.companyEnShortName,serviceTypeIds:this.form.serviceTypeIds,roleTypeId:this.roleTypeId,belongTo:this.form.belongTo,followUp:this.form.followUp,deleteStatus:1};if(Object(d["d"])(this.$store.state.user.sid).then((function(e){t.cargoTypeIds=e.cargoTypeIds,t.locationDepartureIds=e.locationDepartureIds,t.locationDestinationIds=e.locationDestinationIds,t.lineDepartureIds=e.lineDepartureIds,t.lineDestinationIds=e.lineDestinationIds})),null==t.belongTo&&void 0!=this.belongList){var a,r=Object(l["a"])(this.belongList);try{for(r.s();!(a=r.n()).done;){var o=a.value;if(void 0!=o.children){var s,n=Object(l["a"])(o.children);try{for(n.s();!(s=n.n()).done;){var i=s.value;if(void 0!=i.children){var f,u=Object(l["a"])(i.children);try{for(u.s();!(f=u.n()).done;){var p=f.value;p.staffId==this.$store.state.user.sid&&(t.belongTo=this.$store.state.user.sid)}}catch(h){u.e(h)}finally{u.f()}}}}catch(h){n.e(h)}finally{n.f()}}}}catch(h){r.e(h)}finally{r.f()}}this.$refs["form"].validate((function(a){a&&Object(c["k"])(t).then((function(a){var r=a.data;void 0!=r&&e.$confirm(0==r.deleteStatus?"存在重复的数据，是否要显示该数据":"存在重复数据，但已删除，是否重新读取","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){if(r.deleteStatus=0,e.form=r,e.form.roleTypeId=e.roleTypeId,void 0!=e.belongList){var t,o=Object(l["a"])(e.belongList);try{for(o.s();!(t=o.n()).done;){var s=t.value;if(void 0!=s.children){var n,i=Object(l["a"])(s.children);try{for(i.s();!(n=i.n()).done;){var c=n.value;if(void 0!=c.children){var d,m=Object(l["a"])(c.children);try{for(m.s();!(d=m.n()).done;){var f=d.value;f.staffId==a.data.belongTo&&(e.belongTo=f.deptId),f.staffId==a.data.followUp&&(e.followUp=f.deptId)}}catch(h){m.e(h)}finally{m.f()}}}}catch(h){i.e(h)}finally{i.f()}}}}catch(h){o.e(h)}finally{o.f()}}e.form.roleIds=a.roleIds,e.form.serviceTypeIds=a.serviceTypeIds,e.form.cargoTypeIds=a.cargoTypeIds,e.form.lineDepartureIds=a.lineDepartureIds,e.form.locationDepartureIds=a.locationDepartureIds,e.form.lineDestinationIds=a.lineDestinationIds,e.form.locationDestinationIds=a.locationDestinationIds,e.form.carrierIds=a.carrierIds,e.form.organizationIds=a.companyOrganizationIds,e.locationOptions=a.locationOptions,e.openCompany=!0,e.title="修改公司信息",e.loading=!1})),a.msg.toString().indexOf("Error")>-1&&e.$confirm(a.msg,"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){var t={messageOwner:1,messageType:3,messageFrom:null,messageTitle:e.$store.state.user.name.split(" ")[1]+"请求更新公司",messageContent:a.msg};Object(m["a"])(t).then((function(t){e.$message({type:"success",message:"已发送请求!"})}))})),a.msg.toString().indexOf("Success")>-1&&e.$confirm("不存在重复的公司简称，是否确定新增客户？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(){t.deleteStatus=0,Object(c["k"])(t).then((function(t){if(t.data){if(e.$message.success("添加成功"),e.form=t.data,e.form.roleTypeId=e.roleTypeId,void 0!=e.belongList){var a,r=Object(l["a"])(e.belongList);try{for(r.s();!(a=r.n()).done;){var o=a.value;if(void 0!=o.children){var s,n=Object(l["a"])(o.children);try{for(n.s();!(s=n.n()).done;){var i=s.value;if(void 0!=i.children){var c,d=Object(l["a"])(i.children);try{for(d.s();!(c=d.n()).done;){var m=c.value;m.staffId==t.data.belongTo&&(e.belongTo=m.deptId),m.staffId==t.data.followUp&&(e.followUp=m.deptId)}}catch(h){d.e(h)}finally{d.f()}}}}catch(h){n.e(h)}finally{n.f()}}}}catch(h){r.e(h)}finally{r.f()}}e.form.roleIds=t.roleIds,e.form.serviceTypeIds=t.serviceTypeIds,e.form.cargoTypeIds=t.cargoTypeIds,e.form.lineDepartureIds=t.lineDepartureIds,e.form.locationDepartureIds=t.locationDepartureIds,e.form.lineDestinationIds=t.lineDestinationIds,e.form.locationDestinationIds=t.locationDestinationIds,e.form.carrierIds=t.carrierIds,e.form.organizationIds=t.companyOrganizationIds,e.locationOptions=t.locationOptions,e.openCompany=!0,e.title="修改公司信息",e.loading=!1}}))}))}))}))},getList:function(){var e=this;return Object(i["a"])(Object(s["a"])().mark((function t(){return Object(s["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.loading=!0,t.next=3,Object(c["f"])(Object(n["a"])(Object(n["a"])({},e.queryParams),{},{roleClient:1,permissionLevel:e.$store.state.user.permissionLevelList.C})).then((function(t){e.companyList=t.rows,isNaN(t.total)||(e.total=t.total),e.loading=!1}));case 3:case"end":return t.stop()}}),t)})))()},staffNormalizer:function(e){var t;return e.children&&!e.children.length&&delete e.children,e.staff&&(t=null==e.staff.staffFamilyLocalName&&null==e.staff.staffGivingLocalName?null!=e.role.roleLocalName?e.role.roleLocalName+","+h.a.getFullChars(e.role.roleLocalName):e.dept.deptLocalName+","+h.a.getFullChars(e.dept.deptLocalName):e.staff.staffCode+" "+e.staff.staffFamilyLocalName+e.staff.staffGivingLocalName+" "+e.staff.staffGivingEnName+","+h.a.getFullChars(e.staff.staffFamilyLocalName+e.staff.staffGivingLocalName)),e.roleId?{id:e.roleId,label:t,children:e.children,isDisabled:null==e.staffId&&void 0==e.children}:{id:e.deptId,label:t,children:e.children,isDisabled:null==e.staffId&&void 0==e.children}},carrierNormalizer:function(e){var t;return e.children&&!e.children.length&&delete e.children,t=!e.carrier||null==e.carrier.carrierLocalName&&null==e.carrier.carrierEnName?e.serviceLocalName+" "+e.serviceEnName+","+h.a.getFullChars(e.serviceLocalName):(null!=e.carrier.carrierIntlCode?e.carrier.carrierIntlCode:"")+" "+(null!=e.carrier.carrierEnName?e.carrier.carrierEnName:"")+" "+(null!=e.carrier.carrierLocalName?e.carrier.carrierLocalName:"")+","+h.a.getFullChars(null!=e.carrier.carrierLocalName?e.carrier.carrierLocalName:""),{id:e.serviceTypeId,label:t,children:e.children}},cancel:function(){this.openCompany=!1,this.openAccount=!1,this.openStaff=!1,this.openCommunication=!1,this.openAgreement=!1,this.openBlackList=!1,this.add=!1,this.merge=!1,this.edit=!1,this.reset(),this.belongTo=null,this.followUp=null},accountCancel:function(){this.openAccount=!1},reset:function(){this.belongTo=null,this.followUp=null,this.carrierIds=[],this.form={belongTo:null,followUp:null,carrierIds:null,locationDetail:null,companyId:null,companyIntlCode:null,companyShortName:null,companyEnShortName:null,companyLocalName:null,companyEnName:null,companyTaxCode:null,roleIds:null,serviceTypeIds:null,cargoTypeIds:null,locationDepartureIds:null,lineDepartureIds:null,locationDestinationIds:null,lineDestinationIds:null,organizationIds:null,companyPortIds:null,roleTypeId:this.roleTypeId,locationId:null,salesConfirmed:0,salesConfirmedId:null,salesConfirmedName:null,salesConfirmedDate:null,psaConfirmed:0,psaConfirmedId:null,psaConfirmedName:null,psaConfirmedDate:null,accConfirmed:0,accConfirmedId:null,accConfirmedName:null,accConfirmedDate:null,opConfirmed:0,opConfirmedId:null,opConfirmedName:null,opConfirmedDate:null,remark:null,rsPaymentTitles:[]},this.carrierIds=[],this.companyRow=null,this.resetForm("form")},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.resetForm("queryForm"),this.queryBStaffId=null,this.queryParams.locationId=null,this.queryParams.serviceTypeIds=null,this.queryParams.cargoTypeIds=null,this.queryParams.locationDepartureIds=null,this.queryParams.lineDepartureIds=null,this.queryParams.locationDestinationIds=null,this.queryParams.lineDestinationIds=null,this.queryParams.organizationIds=null,this.handleQuery()},handleSelectionChange:function(e){this.ids=e.map((function(e){return e.companyId})),this.single=1!=e.length,this.multiple=!e.length,this.selectTwo=2!=e.length,1==e.length&&this.setCompanyInfo(e[0]),2==e.length&&(this.mergeList=e)},handleAdd:function(){if(this.reset(),this.edit=!1,this.form.belongTo=null,this.openCompany=!0,this.title="新增公司信息",this.form.serviceTypeIds=[],this.temCarrierList=this.carrierList,void 0!=this.temCarrierList&&null!=this.form.serviceTypeIds){var e,t=Object(l["a"])(this.temCarrierList);try{for(t.s();!(e=t.n()).done;){var a=e.value;this.form.serviceTypeIds.push(a.serviceTypeId)}}catch(u){t.e(u)}finally{t.f()}}if(this.add=!0,void 0!=this.belongList){var r,o=Object(l["a"])(this.belongList);try{for(o.s();!(r=o.n()).done;){var s=r.value;if(void 0!=s.children){var n,i=Object(l["a"])(s.children);try{for(i.s();!(n=i.n()).done;){var c=n.value;if(void 0!=c.children){var d,m=Object(l["a"])(c.children);try{for(m.s();!(d=m.n()).done;){var f=d.value;f.staffId==this.$store.state.user.sid&&(this.belongTo=f.deptId)}}catch(u){m.e(u)}finally{m.f()}}}}catch(u){i.e(u)}finally{i.f()}}}}catch(u){o.e(u)}finally{o.f()}}this.form.agreementCurrencyCode="RMB",this.showConfirm=!1},getReturn:function(e){var t=this;"contactor"==e.key&&(this.setCompanyInfo(e.value),Object(c["d"])(e.value.companyId).then((function(e){t.staffList=e.staffList,t.openStaff=!0}))),"communication"==e.key&&(this.setCompanyInfo(e.value),Object(f["d"])({sqdCompanyId:e.value.companyId}).then((function(e){t.communicationList=e.rows,t.ctotle=e.totle,t.openCommunication=!0}))),"agreement"==e.key&&(this.setCompanyInfo(e.value),Object(u["d"])({sqdCompanyId:e.value.companyId}).then((function(e){t.agreementList=e.rows,t.atotle=e.totle,t.openAgreement=!0}))),"account"==e.key&&(this.setCompanyInfo(e.value),Object(c["b"])(e.value.companyId).then((function(e){t.accountList=e.accountList,t.openAccount=!0})))},setCompanyInfo:function(e){this.companyInfo={companyId:null!=e.companyId?e.companyId:"",companyTaxCode:null!=e.companyTaxCode?e.companyTaxCode:"",companyShortName:null!=e.companyShortName?e.companyShortName:"",companyEnShortName:null!=e.companyEnShortName?e.companyEnShortName:"",companyLocalName:null!=e.companyLocalName?e.companyLocalName:"",companyEnName:null!=e.companyEnName?e.companyEnName:"",companyLocation:null!=e.locationId?e.locationId:"",mainStaffId:null!=e.staff?e.staff.staffId:""}},handleUpdate:function(e){var t=this;this.reset(),this.loading=!0,this.edit=!0,this.companyRow=e,this.add=R["a"].hasPermi("system:client:distribute");var a=e.companyId||this.ids;this.showConfirm=!0,Object(c["c"])(a).then((function(e){if(t.form=e.data,void 0!=t.belongList){var a,r=Object(l["a"])(t.belongList);try{for(r.s();!(a=r.n()).done;){var o=a.value;if(void 0!=o.children){var s,n=Object(l["a"])(o.children);try{for(n.s();!(s=n.n()).done;){var i=s.value;if(void 0!=i.children){var c,d=Object(l["a"])(i.children);try{for(d.s();!(c=d.n()).done;){var m=c.value;m.staffId==e.data.belongTo&&(t.belongTo=m.deptId),m.staffId==e.data.followUp&&(t.followUp=m.deptId)}}catch(u){d.e(u)}finally{d.f()}}}}catch(u){n.e(u)}finally{n.f()}}}}catch(u){r.e(u)}finally{r.f()}}t.form.roleIds=e.roleIds,t.form.serviceTypeIds=e.serviceTypeIds,t.form.cargoTypeIds=e.cargoTypeIds,t.form.lineDepartureIds=e.lineDepartureIds,t.form.locationDepartureIds=e.locationDepartureIds,t.form.lineDestinationIds=e.lineDestinationIds,t.form.locationDestinationIds=e.locationDestinationIds,t.form.carrierIds=e.carrierIds,t.form.organizationIds=e.companyOrganizationIds,t.locationOptions=e.locationOptions,t.openCompany=!0,t.title="修改公司信息",t.form.rsPaymentTitles=e.data.rsPaymentTitle?e.data.rsPaymentTitle.split(","):[],t.loading=!1,null!==e.data.agreementStartDate&&null!==e.data.agreementEndDate&&(t.form.agreementDateRange=[],t.form.agreementDateRange.push(e.data.agreementStartDate),t.form.agreementDateRange.push(e.data.agreementEndDate));var f=new Intl.NumberFormat("en-US",{style:"decimal",minimumFractionDigits:2,maximumFractionDigits:2});t.form.creditLimit=e.data.creditLimit.toLocaleString("en-US"),t.form.creditLimit=t.form.creditLimit.replace(/,/g,""),t.form.creditLimit=f.format(t.form.creditLimit)}))},submitForm:function(){var e=this;this.$refs["form"].validate((function(t){e.form.creditLimit=e.form.creditLimit?e.form.creditLimit.replace(/,/g,""):0,e.form.agreementDateRange&&e.form.agreementDateRange.length>0&&(e.form.agreementStartDate=e.form.agreementDateRange[0],e.form.agreementEndDate=e.form.agreementDateRange[1]);var a=new Date(e.form.agreementStartDate),r=new Date(e.form.agreementEndDate);a>r?Object(Q["Message"])({message:"协议开始时间不能大于结束时间",type:"error"}):null==e.form.creditCycleMonth||e.form.creditCycleMonth%1==0?t&&(null!=e.form.companyId?(Object(c["m"])(e.form).then((function(t){e.$modal.msgSuccess("修改成功"),e.openCompany=!1,e.getList()})),e.reset()):e.$message.info("未查重，先对简称查重吧")):Object(Q["Message"])({message:"信用周期必须为整数",type:"error"})}))},handleDelete:function(e){var t=this,a=e.companyId||this.ids;this.$confirm('是否确认删除公司编号为"'+a+'"的数据项？',"提示",{customClass:"modal-confirm"}).then((function(){return Object(c["a"])(a)})).then((function(){t.getList(),t.$modal.msgSuccess("删除成功")})).catch((function(){}))},handleBlackList:function(){this.openBlackList=!0},handleExport:function(){this.download("system/company/export",Object(n["a"])({},this.queryParams),"company_".concat((new Date).getTime(),".xlsx"))},queryLocationId:function(e){this.queryParams.locationId=e,this.handleQuery()},getLocationId:function(e){this.form.locationId=e},getSourceId:function(e){this.form.sourceId=e.sourceShortName},getOrganizationIds:function(e){this.form.organizationIds=e},getServiceTypeIds:function(e){this.form.serviceTypeIds=e,void 0==e&&(this.carrierIds=null,this.form.carrierIds=null)},queryServiceTypeIds:function(e){this.queryParams.serviceTypeIds=e,this.handleQuery()},getCargoTypeIds:function(e){this.form.cargoTypeIds=e},queryCargoTypeIds:function(e){this.queryParams.cargoTypeIds=e,this.handleQuery()},getCompanyRoleIds:function(e){this.form.roleIds=e},queryCompanyRoleIds:function(e){this.queryParams.roleIds=e,this.handleQuery()},queryLocationDepartureIds:function(e){this.queryParams.locationDepartureIds=e,this.handleQuery()},getLineDepartureIds:function(e){this.form.lineDepartureIds=e},getLocationDestinationIds:function(e){this.form.locationDestinationIds=e},queryLineDepartureIds:function(e){this.queryParams.lineDepartureIds=e,this.handleQuery()},getLocationDepartureIds:function(e){this.form.locationDepartureIds=e},queryLocationDestinationIds:function(e){this.queryParams.locationDestinationIds=e,this.handleQuery()},getLineDestinationIds:function(e){this.form.lineDestinationIds=e},queryLineDestinationIds:function(e){this.queryParams.lineDestinationIds=e,this.handleQuery()},handleSelectBelongTo:function(e){this.form.belongTo=e.staffId},handleDeselectBelongTo:function(e){void 0==e&&(this.form.belongTo=0,this.belongTo=null)},handleSelectFollowUp:function(e){this.form.followUp=e.staffId},handleDeselectFollowUp:function(e){void 0==e&&(this.form.followUp=0,this.followUp=null)},handleSelectBFStaffId:function(e){this.queryParams.queryBFStaffId=e.staffId,this.handleQuery()},cleanBFStaffId:function(e){void 0==e&&(this.queryParams.queryBFStaffId=null,this.handleQuery())},cleanBStaffId:function(e){void 0==e&&(this.queryParams.queryBStaffId=null,this.handleQuery())},handleSelectBStaffId:function(e){var t=this;this.queryParams.queryBStaffId=e.staffId,Object(d["d"])(e.staffId).then((function(e){t.queryParams.cargoTypeIds=e.cargoTypeIds,t.queryParams.serviceTypeIds=e.serviceTypeIds,t.queryParams.locationDepartureIds=e.locationDepartureIds,t.queryParams.lineDepartureIds=e.lineDepartureIds,t.queryParams.locationDestinationIds=e.locationDestinationIds,t.queryParams.lineDestinationIds=e.lineDestinationIds,t.locationOptions=e.locationOptions,t.handleQuery()}))},handleSelectCarrierIds:function(e){this.form.carrierIds.push(e.carrier.carrierId)},handleSelectQueryCarrierIds:function(e){this.queryParams.carrierIds.push(e.carrier.carrierId),this.handleQuery()},handleDeselectCarrierIds:function(e){this.form.carrierIds=this.form.carrierIds.filter((function(t){return t!=e.carrier.carrierId}))},handleDeselectQueryCarrierIds:function(e){this.queryParams.carrierIds=this.queryParams.carrierIds.filter((function(t){return t!=e.carrier.carrierId})),this.handleQuery()},refreshColumns:function(){var e=this;this.refreshTable=!1,this.$nextTick((function(){e.refreshTable=!0}))},handleMergeCompany:function(){this.merge=!0},handleMerge:function(e,t){var a=this;Object(c["i"])(e,t).then((function(e){a.$message.success(e.msg),a.merge=!1,a.getList()}))},deptLock:function(){var e=this;null!=this.form.companyId?(this.form.deptConfirmed=0==this.form.deptConfirmed?1:0,this.form.deptConfirmedId=this.$store.state.user.sid,this.form.deptConfirmedName=this.$store.state.user.name.split(" ")[1],this.form.deptConfirmedDate=Object(U["f"])(new Date),Object(c["m"])(this.form).then((function(t){e.$modal.msgSuccess("修改成功")}))):this.$modal.msgError("错误操作")},financeLock:function(){var e=this;null!=this.form.companyId?(this.form.financeConfirmed=0==this.form.financeConfirmed?1:0,this.form.financeConfirmedId=this.$store.state.user.sid,this.form.financeConfirmedName=this.$store.state.user.name.split(" ")[1],this.form.financeConfirmedDate=Object(U["f"])(new Date),Object(c["m"])(this.form).then((function(t){e.$modal.msgSuccess("修改成功")}))):this.$modal.msgError("错误操作")},psaLock:function(){var e=this;null!=this.form.companyId?(this.form.psaConfirmed=0==this.form.psaConfirmed?1:0,this.form.psaConfirmedId=this.$store.state.user.sid,this.form.psaConfirmedName=this.$store.state.user.name.split(" ")[1],this.form.psaConfirmedDate=Object(U["f"])(new Date),Object(c["m"])(this.form).then((function(t){e.$modal.msgSuccess("修改成功")}))):this.$modal.msgError("错误操作")},opLock:function(){var e=this;null!=this.form.companyId?(this.form.opConfirmed=0===this.form.opConfirmed?1:0,this.form.opConfirmedId=this.$store.state.user.sid,this.form.opConfirmedName=this.$store.state.user.name.split(" ")[1],this.form.opConfirmedDate=Object(U["f"])(new Date),Object(c["m"])(this.form).then((function(t){e.$modal.msgSuccess("修改成功")}))):this.$modal.msgError("错误操作")},getCurrencyCode:function(e){this.form.agreementCurrencyCode=e},getcreditLevel:function(e){this.form.creditLevel=e},getRsPaymentTitle:function(e){this.form.rsPaymentTitle=e.toString()},updateCompany:function(e){var t=this;this.form.creditLimit=this.form.creditLimit.replace(/,/g,""),this.form.agreementDateRange&&this.form.agreementDateRange.length>0&&(this.form.agreementStartDate=this.form.agreementDateRange[0],this.form.agreementEndDate=this.form.agreementDateRange[1]),Object(c["m"])(this.form).then((function(e){t.$modal.msgSuccess("修改成功"),Object(c["c"])(t.form.companyId).then((function(e){if(t.form=e.data,void 0!=t.belongList){var a,r=Object(l["a"])(t.belongList);try{for(r.s();!(a=r.n()).done;){var o=a.value;if(void 0!=o.children){var s,n=Object(l["a"])(o.children);try{for(n.s();!(s=n.n()).done;){var i=s.value;if(void 0!=i.children){var c,d=Object(l["a"])(i.children);try{for(d.s();!(c=d.n()).done;){var m=c.value;m.staffId==e.data.belongTo&&(t.belongTo=m.deptId),m.staffId==e.data.followUp&&(t.followUp=m.deptId)}}catch(u){d.e(u)}finally{d.f()}}}}catch(u){n.e(u)}finally{n.f()}}}}catch(u){r.e(u)}finally{r.f()}}t.form.roleIds=e.roleIds,t.form.serviceTypeIds=e.serviceTypeIds,t.form.cargoTypeIds=e.cargoTypeIds,t.form.lineDepartureIds=e.lineDepartureIds,t.form.locationDepartureIds=e.locationDepartureIds,t.form.lineDestinationIds=e.lineDestinationIds,t.form.locationDestinationIds=e.locationDestinationIds,t.form.carrierIds=e.carrierIds,t.form.organizationIds=e.companyOrganizationIds,t.locationOptions=e.locationOptions,t.openCompany=!0,t.title="修改公司信息",t.loading=!1;var f=new Intl.NumberFormat("en-US",{style:"decimal",minimumFractionDigits:2,maximumFractionDigits:2});t.form.creditLimit=e.data.creditLimit.toLocaleString("en-US"),t.form.creditLimit=t.form.creditLimit.replace(/,/g,""),t.form.creditLimit=f.format(t.form.creditLimit),t.form.agreementDateRange=[e.data.agreementStartDate,e.data.agreementEndDate]}))}))},formatCreditLimit:function(){if(null!=this.form.creditLimit){var e=new Intl.NumberFormat("en-US",{style:"decimal",minimumFractionDigits:2,maximumFractionDigits:2});this.form.creditLimit=this.form.creditLimit.replace(/,/g,""),this.form.creditLimit=e.format(this.form.creditLimit)}},formatDisplayCreditLimit:function(e){var t=new Intl.NumberFormat("en-US",{notation:"compact"});return t.format(e)},changeDate:function(){this.$forceUpdate()}}},W=G,H=(a("30ec"),a("2877")),J=Object(H["a"])(W,r,o,!1,null,"000bb043",null);t["default"]=J.exports},edda:function(e,t,a){}}]);