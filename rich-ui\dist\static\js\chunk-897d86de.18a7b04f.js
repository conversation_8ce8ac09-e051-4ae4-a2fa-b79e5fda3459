(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-897d86de","chunk-2d0d69a4"],{"72f9":function(t,e,n){(function(e,n){t.exports=n()})(0,(function(){function t(o,s){if(!(this instanceof t))return new t(o,s);s=Object.assign({},n,s);var a=Math.pow(10,s.precision);this.intValue=o=e(o,s),this.value=o/a,s.increment=s.increment||1/a,s.groups=s.useVedic?i:r,this.s=s,this.p=a}function e(e,n){var r=!(2<arguments.length&&void 0!==arguments[2])||arguments[2],i=n.decimal,o=n.errorOnInvalid,s=n.fromCents,a=Math.pow(10,n.precision),c=e instanceof t;if(c&&s)return e.intValue;if("number"===typeof e||c)i=c?e.value:e;else if("string"===typeof e)o=new RegExp("[^-\\d"+i+"]","g"),i=new RegExp("\\"+i,"g"),i=(i=e.replace(/\((.*)\)/,"-$1").replace(o,"").replace(i,"."))||0;else{if(o)throw Error("Invalid Input");i=0}return s||(i=(i*a).toFixed(4)),r?Math.round(i):i}var n={symbol:"$",separator:",",decimal:".",errorOnInvalid:!1,precision:2,pattern:"!#",negativePattern:"-!#",format:function(t,e){var n=e.pattern,r=e.negativePattern,i=e.symbol,o=e.separator,s=e.decimal;e=e.groups;var a=(""+t).replace(/^-/,"").split("."),c=a[0];return a=a[1],(0<=t.value?n:r).replace("!",i).replace("#",c.replace(e,"$1"+o)+(a?s+a:""))},fromCents:!1},r=/(\d)(?=(\d{3})+\b)/g,i=/(\d)(?=(\d\d)+\d\b)/g;return t.prototype={add:function(n){var r=this.s,i=this.p;return t((this.intValue+e(n,r))/(r.fromCents?1:i),r)},subtract:function(n){var r=this.s,i=this.p;return t((this.intValue-e(n,r))/(r.fromCents?1:i),r)},multiply:function(e){var n=this.s;return t(this.intValue*e/(n.fromCents?1:Math.pow(10,n.precision)),n)},divide:function(n){var r=this.s;return t(this.intValue/e(n,r,!1),r)},distribute:function(e){var n=this.intValue,r=this.p,i=this.s,o=[],s=Math[0<=n?"floor":"ceil"](n/e),a=Math.abs(n-s*e);for(r=i.fromCents?1:r;0!==e;e--){var c=t(s/r,i);0<a--&&(c=c[0<=n?"add":"subtract"](1/r)),o.push(c)}return o},dollars:function(){return~~this.value},cents:function(){return~~(this.intValue%this.p)},format:function(t){var e=this.s;return"function"===typeof t?t(this,e):e.format(this,Object.assign({},e,t))},toString:function(){var t=this.s,e=t.increment;return(Math.round(this.intValue/this.p/e)*e).toFixed(t.precision)},toJSON:function(){return this.value}},t}))},"76d2":function(t,e,n){"use strict";n.r(e),function(t){n.d(e,"AcroForm",(function(){return Pt})),n.d(e,"AcroFormAppearance",(function(){return Lt})),n.d(e,"AcroFormButton",(function(){return yt})),n.d(e,"AcroFormCheckBox",(function(){return xt})),n.d(e,"AcroFormChoiceField",(function(){return mt})),n.d(e,"AcroFormComboBox",(function(){return vt})),n.d(e,"AcroFormEditBox",(function(){return bt})),n.d(e,"AcroFormListBox",(function(){return gt})),n.d(e,"AcroFormPasswordField",(function(){return At})),n.d(e,"AcroFormPushButton",(function(){return wt})),n.d(e,"AcroFormRadioButton",(function(){return jt})),n.d(e,"AcroFormTextField",(function(){return Nt})),n.d(e,"GState",(function(){return M})),n.d(e,"ShadingPattern",(function(){return T})),n.d(e,"TilingPattern",(function(){return q})),n.d(e,"jsPDF",(function(){return D}));var r=n("7037"),i=n.n(r),o=n("72ba"),s=function(){return"undefined"!=typeof window?window:"undefined"!=typeof t?t:"undefined"!=typeof self?self:this}();function a(){s.console&&"function"==typeof s.console.log&&s.console.log.apply(s.console,arguments)}var c={log:a,warn:function(t){s.console&&("function"==typeof s.console.warn?s.console.warn.apply(s.console,arguments):a.call(null,arguments))},error:function(t){s.console&&("function"==typeof s.console.error?s.console.error.apply(s.console,arguments):a(t))}};function u(t,e,n){var r=new XMLHttpRequest;r.open("GET",t),r.responseType="blob",r.onload=function(){p(r.response,e,n)},r.onerror=function(){c.error("could not download file")},r.send()}function l(t){var e=new XMLHttpRequest;e.open("HEAD",t,!1);try{e.send()}catch(t){}return e.status>=200&&e.status<=299}function h(t){try{t.dispatchEvent(new MouseEvent("click"))}catch(n){var e=document.createEvent("MouseEvents");e.initMouseEvent("click",!0,!0,window,0,0,0,80,20,!1,!1,!1,!1,0,null),t.dispatchEvent(e)}}var f,d,p=s.saveAs||("object"!==("undefined"==typeof window?"undefined":i()(window))||window!==s?function(){}:"undefined"!=typeof HTMLAnchorElement&&"download"in HTMLAnchorElement.prototype?function(t,e,n){var r=s.URL||s.webkitURL,i=document.createElement("a");e=e||t.name||"download",i.download=e,i.rel="noopener","string"==typeof t?(i.href=t,i.origin!==location.origin?l(i.href)?u(t,e,n):h(i,i.target="_blank"):h(i)):(i.href=r.createObjectURL(t),setTimeout((function(){r.revokeObjectURL(i.href)}),4e4),setTimeout((function(){h(i)}),0))}:"msSaveOrOpenBlob"in navigator?function(t,e,n){if(e=e||t.name||"download","string"==typeof t)if(l(t))u(t,e,n);else{var r=document.createElement("a");r.href=t,r.target="_blank",setTimeout((function(){h(r)}))}else navigator.msSaveOrOpenBlob(function(t,e){return void 0===e?e={autoBom:!1}:"object"!==i()(e)&&(c.warn("Deprecated: Expected third argument to be a object"),e={autoBom:!e}),e.autoBom&&/^\s*(?:text\/\S*|application\/xml|\S*\/\S*\+xml)\s*;.*charset\s*=\s*utf-8/i.test(t.type)?new Blob([String.fromCharCode(65279),t],{type:t.type}):t}(t,n),e)}:function(t,e,n,r){if((r=r||open("","_blank"))&&(r.document.title=r.document.body.innerText="downloading..."),"string"==typeof t)return u(t,e,n);var o="application/octet-stream"===t.type,a=/constructor/i.test(s.HTMLElement)||s.safari,c=/CriOS\/[\d]+/.test(navigator.userAgent);if((c||o&&a)&&"object"===("undefined"==typeof FileReader?"undefined":i()(FileReader))){var l=new FileReader;l.onloadend=function(){var t=l.result;t=c?t:t.replace(/^data:[^;]*;/,"data:attachment/file;"),r?r.location.href=t:location=t,r=null},l.readAsDataURL(t)}else{var h=s.URL||s.webkitURL,f=h.createObjectURL(t);r?r.location=f:location.href=f,r=null,setTimeout((function(){h.revokeObjectURL(f)}),4e4)}});
/**
 * A class to parse color values
 * <AUTHOR> Stefanov <<EMAIL>>
 * {@link   http://www.phpied.com/rgb-color-parser-in-javascript/}
 * @license Use it if you like it
 */function m(t){var e;t=t||"",this.ok=!1,"#"==t.charAt(0)&&(t=t.substr(1,6)),t={aliceblue:"f0f8ff",antiquewhite:"faebd7",aqua:"00ffff",aquamarine:"7fffd4",azure:"f0ffff",beige:"f5f5dc",bisque:"ffe4c4",black:"000000",blanchedalmond:"ffebcd",blue:"0000ff",blueviolet:"8a2be2",brown:"a52a2a",burlywood:"deb887",cadetblue:"5f9ea0",chartreuse:"7fff00",chocolate:"d2691e",coral:"ff7f50",cornflowerblue:"6495ed",cornsilk:"fff8dc",crimson:"dc143c",cyan:"00ffff",darkblue:"00008b",darkcyan:"008b8b",darkgoldenrod:"b8860b",darkgray:"a9a9a9",darkgreen:"006400",darkkhaki:"bdb76b",darkmagenta:"8b008b",darkolivegreen:"556b2f",darkorange:"ff8c00",darkorchid:"9932cc",darkred:"8b0000",darksalmon:"e9967a",darkseagreen:"8fbc8f",darkslateblue:"483d8b",darkslategray:"2f4f4f",darkturquoise:"00ced1",darkviolet:"9400d3",deeppink:"ff1493",deepskyblue:"00bfff",dimgray:"696969",dodgerblue:"1e90ff",feldspar:"d19275",firebrick:"b22222",floralwhite:"fffaf0",forestgreen:"228b22",fuchsia:"ff00ff",gainsboro:"dcdcdc",ghostwhite:"f8f8ff",gold:"ffd700",goldenrod:"daa520",gray:"808080",green:"008000",greenyellow:"adff2f",honeydew:"f0fff0",hotpink:"ff69b4",indianred:"cd5c5c",indigo:"4b0082",ivory:"fffff0",khaki:"f0e68c",lavender:"e6e6fa",lavenderblush:"fff0f5",lawngreen:"7cfc00",lemonchiffon:"fffacd",lightblue:"add8e6",lightcoral:"f08080",lightcyan:"e0ffff",lightgoldenrodyellow:"fafad2",lightgrey:"d3d3d3",lightgreen:"90ee90",lightpink:"ffb6c1",lightsalmon:"ffa07a",lightseagreen:"20b2aa",lightskyblue:"87cefa",lightslateblue:"8470ff",lightslategray:"778899",lightsteelblue:"b0c4de",lightyellow:"ffffe0",lime:"00ff00",limegreen:"32cd32",linen:"faf0e6",magenta:"ff00ff",maroon:"800000",mediumaquamarine:"66cdaa",mediumblue:"0000cd",mediumorchid:"ba55d3",mediumpurple:"9370d8",mediumseagreen:"3cb371",mediumslateblue:"7b68ee",mediumspringgreen:"00fa9a",mediumturquoise:"48d1cc",mediumvioletred:"c71585",midnightblue:"191970",mintcream:"f5fffa",mistyrose:"ffe4e1",moccasin:"ffe4b5",navajowhite:"ffdead",navy:"000080",oldlace:"fdf5e6",olive:"808000",olivedrab:"6b8e23",orange:"ffa500",orangered:"ff4500",orchid:"da70d6",palegoldenrod:"eee8aa",palegreen:"98fb98",paleturquoise:"afeeee",palevioletred:"d87093",papayawhip:"ffefd5",peachpuff:"ffdab9",peru:"cd853f",pink:"ffc0cb",plum:"dda0dd",powderblue:"b0e0e6",purple:"800080",red:"ff0000",rosybrown:"bc8f8f",royalblue:"4169e1",saddlebrown:"8b4513",salmon:"fa8072",sandybrown:"f4a460",seagreen:"2e8b57",seashell:"fff5ee",sienna:"a0522d",silver:"c0c0c0",skyblue:"87ceeb",slateblue:"6a5acd",slategray:"708090",snow:"fffafa",springgreen:"00ff7f",steelblue:"4682b4",tan:"d2b48c",teal:"008080",thistle:"d8bfd8",tomato:"ff6347",turquoise:"40e0d0",violet:"ee82ee",violetred:"d02090",wheat:"f5deb3",white:"ffffff",whitesmoke:"f5f5f5",yellow:"ffff00",yellowgreen:"9acd32"}[t=(t=t.replace(/ /g,"")).toLowerCase()]||t;for(var n=[{re:/^rgb\((\d{1,3}),\s*(\d{1,3}),\s*(\d{1,3})\)$/,example:["rgb(123, 234, 45)","rgb(255,234,245)"],process:function(t){return[parseInt(t[1]),parseInt(t[2]),parseInt(t[3])]}},{re:/^(\w{2})(\w{2})(\w{2})$/,example:["#00ff00","336699"],process:function(t){return[parseInt(t[1],16),parseInt(t[2],16),parseInt(t[3],16)]}},{re:/^(\w{1})(\w{1})(\w{1})$/,example:["#fb0","f0f"],process:function(t){return[parseInt(t[1]+t[1],16),parseInt(t[2]+t[2],16),parseInt(t[3]+t[3],16)]}}],r=0;r<n.length;r++){var i=n[r].re,o=n[r].process,s=i.exec(t);s&&(e=o(s),this.r=e[0],this.g=e[1],this.b=e[2],this.ok=!0)}this.r=this.r<0||isNaN(this.r)?0:this.r>255?255:this.r,this.g=this.g<0||isNaN(this.g)?0:this.g>255?255:this.g,this.b=this.b<0||isNaN(this.b)?0:this.b>255?255:this.b,this.toRGB=function(){return"rgb("+this.r+", "+this.g+", "+this.b+")"},this.toHex=function(){var t=this.r.toString(16),e=this.g.toString(16),n=this.b.toString(16);return 1==t.length&&(t="0"+t),1==e.length&&(e="0"+e),1==n.length&&(n="0"+n),"#"+t+e+n
/**
 * @license
 * Joseph Myers does not specify a particular license for his work.
 *
 * Author: Joseph Myers
 * Accessed from: http://www.myersdaily.org/joseph/javascript/md5.js
 *
 * Modified by: Owen Leong
 */}}function g(t,e){var n=t[0],r=t[1],i=t[2],o=t[3];n=b(n,r,i,o,e[0],7,-680876936),o=b(o,n,r,i,e[1],12,-389564586),i=b(i,o,n,r,e[2],17,606105819),r=b(r,i,o,n,e[3],22,-**********),n=b(n,r,i,o,e[4],7,-176418897),o=b(o,n,r,i,e[5],12,**********),i=b(i,o,n,r,e[6],17,-**********),r=b(r,i,o,n,e[7],22,-45705983),n=b(n,r,i,o,e[8],7,**********),o=b(o,n,r,i,e[9],12,-**********),i=b(i,o,n,r,e[10],17,-42063),r=b(r,i,o,n,e[11],22,-**********),n=b(n,r,i,o,e[12],7,**********),o=b(o,n,r,i,e[13],12,-40341101),i=b(i,o,n,r,e[14],17,-**********),n=y(n,r=b(r,i,o,n,e[15],22,**********),i,o,e[1],5,-165796510),o=y(o,n,r,i,e[6],9,-**********),i=y(i,o,n,r,e[11],14,643717713),r=y(r,i,o,n,e[0],20,-373897302),n=y(n,r,i,o,e[5],5,-701558691),o=y(o,n,r,i,e[10],9,38016083),i=y(i,o,n,r,e[15],14,-660478335),r=y(r,i,o,n,e[4],20,-405537848),n=y(n,r,i,o,e[9],5,568446438),o=y(o,n,r,i,e[14],9,-1019803690),i=y(i,o,n,r,e[3],14,-187363961),r=y(r,i,o,n,e[8],20,1163531501),n=y(n,r,i,o,e[13],5,-1444681467),o=y(o,n,r,i,e[2],9,-51403784),i=y(i,o,n,r,e[7],14,1735328473),n=w(n,r=y(r,i,o,n,e[12],20,-1926607734),i,o,e[5],4,-378558),o=w(o,n,r,i,e[8],11,-2022574463),i=w(i,o,n,r,e[11],16,1839030562),r=w(r,i,o,n,e[14],23,-35309556),n=w(n,r,i,o,e[1],4,-1530992060),o=w(o,n,r,i,e[4],11,1272893353),i=w(i,o,n,r,e[7],16,-155497632),r=w(r,i,o,n,e[10],23,-1094730640),n=w(n,r,i,o,e[13],4,681279174),o=w(o,n,r,i,e[0],11,-358537222),i=w(i,o,n,r,e[3],16,-722521979),r=w(r,i,o,n,e[6],23,76029189),n=w(n,r,i,o,e[9],4,-640364487),o=w(o,n,r,i,e[12],11,-421815835),i=w(i,o,n,r,e[15],16,530742520),n=j(n,r=w(r,i,o,n,e[2],23,-995338651),i,o,e[0],6,-198630844),o=j(o,n,r,i,e[7],10,1126891415),i=j(i,o,n,r,e[14],15,-1416354905),r=j(r,i,o,n,e[5],21,-57434055),n=j(n,r,i,o,e[12],6,1700485571),o=j(o,n,r,i,e[3],10,-1894986606),i=j(i,o,n,r,e[10],15,-1051523),r=j(r,i,o,n,e[1],21,-2054922799),n=j(n,r,i,o,e[8],6,1873313359),o=j(o,n,r,i,e[15],10,-30611744),i=j(i,o,n,r,e[6],15,-1560198380),r=j(r,i,o,n,e[13],21,1309151649),n=j(n,r,i,o,e[4],6,-145523070),o=j(o,n,r,i,e[11],10,-1120210379),i=j(i,o,n,r,e[2],15,718787259),r=j(r,i,o,n,e[9],21,-343485551),t[0]=k(n,t[0]),t[1]=k(r,t[1]),t[2]=k(i,t[2]),t[3]=k(o,t[3])}function v(t,e,n,r,i,o){return e=k(k(e,t),k(r,o)),k(e<<i|e>>>32-i,n)}function b(t,e,n,r,i,o,s){return v(e&n|~e&r,t,e,i,o,s)}function y(t,e,n,r,i,o,s){return v(e&r|n&~r,t,e,i,o,s)}function w(t,e,n,r,i,o,s){return v(e^n^r,t,e,i,o,s)}function j(t,e,n,r,i,o,s){return v(n^(e|~r),t,e,i,o,s)}function _(t){var e,n=t.length,r=[1732584193,-271733879,-1732584194,271733878];for(e=64;e<=t.length;e+=64)g(r,x(t.substring(e-64,e)));t=t.substring(e-64);var i=[0,0,0,0,0,0,0,0,0,0,0,0,0,0,0,0];for(e=0;e<t.length;e++)i[e>>2]|=t.charCodeAt(e)<<(e%4<<3);if(i[e>>2]|=128<<(e%4<<3),e>55)for(g(r,i),e=0;e<16;e++)i[e]=0;return i[14]=8*n,g(r,i),r}function x(t){var e,n=[];for(e=0;e<64;e+=4)n[e>>2]=t.charCodeAt(e)+(t.charCodeAt(e+1)<<8)+(t.charCodeAt(e+2)<<16)+(t.charCodeAt(e+3)<<24);return n}f=s.atob.bind(s),d=s.btoa.bind(s);var N="0123456789abcdef".split("");function A(t){for(var e="",n=0;n<4;n++)e+=N[t>>8*n+4&15]+N[t>>8*n&15];return e}function L(t){return String.fromCharCode((255&t)>>0,(65280&t)>>8,(16711680&t)>>16,(**********&t)>>24)}function S(t){return _(t).map(L).join("")}var P="5d41402abc4b2a76b9719d911017c592"!=function(t){for(var e=0;e<t.length;e++)t[e]=A(t[e]);return t.join("")}(_("hello"));function k(t,e){if(P){var n=(65535&t)+(65535&e);return(t>>16)+(e>>16)+(n>>16)<<16|65535&n}return t+e&**********}
/**
 * @license
 * FPDF is released under a permissive license: there is no usage restriction.
 * You may embed it freely in your application (commercial or not), with or
 * without modifications.
 *
 * Reference: http://www.fpdf.org/en/script/script37.php
 */function I(t,e){var n,r,i,o;if(t!==n){for(var s=(i=t,o=1+(256/t.length>>0),new Array(o+1).join(i)),a=[],c=0;c<256;c++)a[c]=c;var u=0;for(c=0;c<256;c++){var l=a[c];u=(u+l+s.charCodeAt(c))%256,a[c]=a[u],a[u]=l}n=t,r=a}else a=r;var h=e.length,f=0,d=0,p="";for(c=0;c<h;c++)d=(d+(l=a[f=(f+1)%256]))%256,a[f]=a[d],a[d]=l,s=a[(a[f]+a[d])%256],p+=String.fromCharCode(e.charCodeAt(c)^s);return p}
/**
 * @license
 * Licensed under the MIT License.
 * http://opensource.org/licenses/mit-license
 * Author: Owen Leong (@owenl131)
 * Date: 15 Oct 2020
 * References:
 * https://www.cs.cmu.edu/~dst/Adobe/Gallery/anon21jul01-pdf-encryption.txt
 * https://github.com/foliojs/pdfkit/blob/master/lib/security.js
 * http://www.fpdf.org/en/script/script37.php
 */var C={print:4,modify:8,copy:16,"annot-forms":32};function F(t,e,n,r){this.v=1,this.r=2;var i=192;t.forEach((function(t){if(void 0!==C.perm)throw new Error("Invalid permission: "+t);i+=C[t]})),this.padding="(¿N^NuAd\0NVÿú\b..\0¶Ðh>/\f©þdSiz";var o=(e+this.padding).substr(0,32),s=(n+this.padding).substr(0,32);this.O=this.processOwnerPassword(o,s),this.P=-(1+(255^i)),this.encryptionKey=S(o+this.O+this.lsbFirstWord(this.P)+this.hexToBytes(r)).substr(0,5),this.U=I(this.encryptionKey,this.padding)}function O(t){if(/[^\u0000-\u00ff]/.test(t))throw new Error("Invalid PDF Name Object: "+t+", Only accept ASCII characters.");for(var e="",n=t.length,r=0;r<n;r++){var i=t.charCodeAt(r);e+=i<33||35===i||37===i||40===i||41===i||47===i||60===i||62===i||91===i||93===i||123===i||125===i||i>126?"#"+("0"+i.toString(16)).slice(-2):t[r]}return e}function E(t){if("object"!==i()(t))throw new Error("Invalid Context passed to initialize PubSub (jsPDF-module)");var e={};this.subscribe=function(t,n,r){if(r=r||!1,"string"!=typeof t||"function"!=typeof n||"boolean"!=typeof r)throw new Error("Invalid arguments passed to PubSub.subscribe (jsPDF-module)");e.hasOwnProperty(t)||(e[t]={});var i=Math.random().toString(35);return e[t][i]=[n,!!r],i},this.unsubscribe=function(t){for(var n in e)if(e[n][t])return delete e[n][t],0===Object.keys(e[n]).length&&delete e[n],!0;return!1},this.publish=function(n){if(e.hasOwnProperty(n)){var r=Array.prototype.slice.call(arguments,1),i=[];for(var o in e[n]){var a=e[n][o];try{a[0].apply(t,r)}catch(n){s.console&&c.error("jsPDF PubSub Error",n.message,n)}a[1]&&i.push(o)}i.length&&i.forEach(this.unsubscribe)}},this.getTopics=function(){return e}}function M(t){if(!(this instanceof M))return new M(t);var e="opacity,stroke-opacity".split(",");for(var n in t)t.hasOwnProperty(n)&&e.indexOf(n)>=0&&(this[n]=t[n]);this.id="",this.objectNumber=-1}function B(t,e){this.gState=t,this.matrix=e,this.id="",this.objectNumber=-1}function T(t,e,n,r,i){if(!(this instanceof T))return new T(t,e,n,r,i);this.type="axial"===t?2:3,this.coords=e,this.colors=n,B.call(this,r,i)}function q(t,e,n,r,i){if(!(this instanceof q))return new q(t,e,n,r,i);this.boundingBox=t,this.xStep=e,this.yStep=n,this.stream="",this.cloneIndex=0,B.call(this,r,i)}function D(t){var e,n="string"==typeof arguments[0]?arguments[0]:"p",r=arguments[1],o=arguments[2],a=arguments[3],u=[],l=1,h=16,f="S",g=null;"object"===i()(t=t||{})&&(n=t.orientation,r=t.unit||r,o=t.format||o,a=t.compress||t.compressPdf||a,null!==(g=t.encryption||null)&&(g.userPassword=g.userPassword||"",g.ownerPassword=g.ownerPassword||"",g.userPermissions=g.userPermissions||[]),l="number"==typeof t.userUnit?Math.abs(t.userUnit):1,void 0!==t.precision&&(e=t.precision),void 0!==t.floatPrecision&&(h=t.floatPrecision),f=t.defaultPathOperation||"S"),u=t.filters||(!0===a?["FlateEncode"]:u),r=r||"mm",n=(""+(n||"P")).toLowerCase();var v=t.putOnlyUsedFonts||!1,b={},y={internal:{},__private__:{}};y.__private__.PubSub=E;var w="1.3",j=y.__private__.getPdfVersion=function(){return w};y.__private__.setPdfVersion=function(t){w=t};var _={a0:[2383.94,3370.39],a1:[1683.78,2383.94],a2:[1190.55,1683.78],a3:[841.89,1190.55],a4:[595.28,841.89],a5:[419.53,595.28],a6:[297.64,419.53],a7:[209.76,297.64],a8:[147.4,209.76],a9:[104.88,147.4],a10:[73.7,104.88],b0:[2834.65,4008.19],b1:[2004.09,2834.65],b2:[1417.32,2004.09],b3:[1000.63,1417.32],b4:[708.66,1000.63],b5:[498.9,708.66],b6:[354.33,498.9],b7:[249.45,354.33],b8:[175.75,249.45],b9:[124.72,175.75],b10:[87.87,124.72],c0:[2599.37,3676.54],c1:[1836.85,2599.37],c2:[1298.27,1836.85],c3:[918.43,1298.27],c4:[649.13,918.43],c5:[459.21,649.13],c6:[323.15,459.21],c7:[229.61,323.15],c8:[161.57,229.61],c9:[113.39,161.57],c10:[79.37,113.39],dl:[311.81,623.62],letter:[612,792],"government-letter":[576,756],legal:[612,1008],"junior-legal":[576,360],ledger:[1224,792],tabloid:[792,1224],"credit-card":[153,243]};y.__private__.getPageFormats=function(){return _};var x=y.__private__.getPageFormat=function(t){return _[t]};o=o||"a4";var N={COMPAT:"compat",ADVANCED:"advanced"},A=N.COMPAT;function L(){this.saveGraphicsState(),ht(new Wt(Lt,0,0,-Lt,0,Rn()*Lt).toString()+" cm"),this.setFontSize(this.getFontSize()/Lt),f="n",A=N.ADVANCED}function S(){this.restoreGraphicsState(),f="S",A=N.COMPAT}var P=y.__private__.combineFontStyleAndFontWeight=function(t,e){if("bold"==t&&"normal"==e||"bold"==t&&400==e||"normal"==t&&"italic"==e||"bold"==t&&"italic"==e)throw new Error("Invalid Combination of fontweight and fontstyle");return e&&(t=400==e||"normal"===e?"italic"===t?"italic":"normal":700!=e&&"bold"!==e||"normal"!==t?(700==e?"bold":e)+""+t:"bold"),t};y.advancedAPI=function(t){var e=A===N.COMPAT;return e&&L.call(this),"function"!=typeof t||(t(this),e&&S.call(this)),this},y.compatAPI=function(t){var e=A===N.ADVANCED;return e&&S.call(this),"function"!=typeof t||(t(this),e&&L.call(this)),this},y.isAdvancedAPI=function(){return A===N.ADVANCED};var k,I=function(t){if(A!==N.ADVANCED)throw new Error(t+" is only available in 'advanced' API mode. You need to call advancedAPI() first.")},C=y.roundToPrecision=y.__private__.roundToPrecision=function(t,n){var r=e||n;if(isNaN(t)||isNaN(r))throw new Error("Invalid argument passed to jsPDF.roundToPrecision");return t.toFixed(r).replace(/0+$/,"")};k=y.hpf=y.__private__.hpf="number"==typeof h?function(t){if(isNaN(t))throw new Error("Invalid argument passed to jsPDF.hpf");return C(t,h)}:"smart"===h?function(t){if(isNaN(t))throw new Error("Invalid argument passed to jsPDF.hpf");return C(t,t>-1&&t<1?16:5)}:function(t){if(isNaN(t))throw new Error("Invalid argument passed to jsPDF.hpf");return C(t,16)};var B=y.f2=y.__private__.f2=function(t){if(isNaN(t))throw new Error("Invalid argument passed to jsPDF.f2");return C(t,2)},R=y.__private__.f3=function(t){if(isNaN(t))throw new Error("Invalid argument passed to jsPDF.f3");return C(t,3)},z=y.scale=y.__private__.scale=function(t){if(isNaN(t))throw new Error("Invalid argument passed to jsPDF.scale");return A===N.COMPAT?t*Lt:A===N.ADVANCED?t:void 0},U=function(t){return A===N.COMPAT?Rn()-t:A===N.ADVANCED?t:void 0},H=function(t){return z(U(t))};y.__private__.setPrecision=y.setPrecision=function(t){"number"==typeof parseInt(t,10)&&(e=parseInt(t,10))};var V,W="00000000000000000000000000000000",G=y.__private__.getFileId=function(){return W},Y=y.__private__.setFileId=function(t){return W=void 0!==t&&/^[a-fA-F0-9]{32}$/.test(t)?t.toUpperCase():W.split("").map((function(){return"ABCDEF0123456789".charAt(Math.floor(16*Math.random()))})).join(""),null!==g&&(Ye=new F(g.userPermissions,g.userPassword,g.ownerPassword,W)),W};y.setFileId=function(t){return Y(t),this},y.getFileId=function(){return G()};var J=y.__private__.convertDateToPDFDate=function(t){var e=t.getTimezoneOffset(),n=e<0?"+":"-",r=Math.floor(Math.abs(e/60)),i=Math.abs(e%60),o=[n,Q(r),"'",Q(i),"'"].join("");return["D:",t.getFullYear(),Q(t.getMonth()+1),Q(t.getDate()),Q(t.getHours()),Q(t.getMinutes()),Q(t.getSeconds()),o].join("")},X=y.__private__.convertPDFDateToDate=function(t){var e=parseInt(t.substr(2,4),10),n=parseInt(t.substr(6,2),10)-1,r=parseInt(t.substr(8,2),10),i=parseInt(t.substr(10,2),10),o=parseInt(t.substr(12,2),10),s=parseInt(t.substr(14,2),10);return new Date(e,n,r,i,o,s,0)},K=y.__private__.setCreationDate=function(t){var e;if(void 0===t&&(t=new Date),t instanceof Date)e=J(t);else{if(!/^D:(20[0-2][0-9]|203[0-7]|19[7-9][0-9])(0[0-9]|1[0-2])([0-2][0-9]|3[0-1])(0[0-9]|1[0-9]|2[0-3])(0[0-9]|[1-5][0-9])(0[0-9]|[1-5][0-9])(\+0[0-9]|\+1[0-4]|-0[0-9]|-1[0-1])'(0[0-9]|[1-5][0-9])'?$/.test(t))throw new Error("Invalid argument passed to jsPDF.setCreationDate");e=t}return V=e},$=y.__private__.getCreationDate=function(t){var e=V;return"jsDate"===t&&(e=X(V)),e};y.setCreationDate=function(t){return K(t),this},y.getCreationDate=function(t){return $(t)};var Z,Q=y.__private__.padd2=function(t){return("0"+parseInt(t)).slice(-2)},tt=y.__private__.padd2Hex=function(t){return("00"+(t=t.toString())).substr(t.length)},et=0,nt=[],rt=[],it=0,ot=[],st=[],at=!1,ct=rt,ut=function(){et=0,it=0,rt=[],nt=[],ot=[],Qt=Kt(),te=Kt()};y.__private__.setCustomOutputDestination=function(t){at=!0,ct=t};var lt=function(t){at||(ct=t)};y.__private__.resetCustomOutputDestination=function(){at=!1,ct=rt};var ht=y.__private__.out=function(t){return t=t.toString(),it+=t.length+1,ct.push(t),ct},ft=y.__private__.write=function(t){return ht(1===arguments.length?t.toString():Array.prototype.join.call(arguments," "))},dt=y.__private__.getArrayBuffer=function(t){for(var e=t.length,n=new ArrayBuffer(e),r=new Uint8Array(n);e--;)r[e]=t.charCodeAt(e);return n},pt=[["Helvetica","helvetica","normal","WinAnsiEncoding"],["Helvetica-Bold","helvetica","bold","WinAnsiEncoding"],["Helvetica-Oblique","helvetica","italic","WinAnsiEncoding"],["Helvetica-BoldOblique","helvetica","bolditalic","WinAnsiEncoding"],["Courier","courier","normal","WinAnsiEncoding"],["Courier-Bold","courier","bold","WinAnsiEncoding"],["Courier-Oblique","courier","italic","WinAnsiEncoding"],["Courier-BoldOblique","courier","bolditalic","WinAnsiEncoding"],["Times-Roman","times","normal","WinAnsiEncoding"],["Times-Bold","times","bold","WinAnsiEncoding"],["Times-Italic","times","italic","WinAnsiEncoding"],["Times-BoldItalic","times","bolditalic","WinAnsiEncoding"],["ZapfDingbats","zapfdingbats","normal",null],["Symbol","symbol","normal",null]];y.__private__.getStandardFonts=function(){return pt};var mt=t.fontSize||16;y.__private__.setFontSize=y.setFontSize=function(t){return mt=A===N.ADVANCED?t/Lt:t,this};var gt,vt=y.__private__.getFontSize=y.getFontSize=function(){return A===N.COMPAT?mt:mt*Lt},bt=t.R2L||!1;y.__private__.setR2L=y.setR2L=function(t){return bt=t,this},y.__private__.getR2L=y.getR2L=function(){return bt};var yt,wt=y.__private__.setZoomMode=function(t){var e=[void 0,null,"fullwidth","fullheight","fullpage","original"];if(/^(?:\d+\.\d*|\d*\.\d+|\d+)%$/.test(t))gt=t;else if(isNaN(t)){if(-1===e.indexOf(t))throw new Error('zoom must be Integer (e.g. 2), a percentage Value (e.g. 300%) or fullwidth, fullheight, fullpage, original. "'+t+'" is not recognized.');gt=t}else gt=parseInt(t,10)};y.__private__.getZoomMode=function(){return gt};var jt,_t=y.__private__.setPageMode=function(t){if(-1==[void 0,null,"UseNone","UseOutlines","UseThumbs","FullScreen"].indexOf(t))throw new Error('Page mode must be one of UseNone, UseOutlines, UseThumbs, or FullScreen. "'+t+'" is not recognized.');yt=t};y.__private__.getPageMode=function(){return yt};var xt=y.__private__.setLayoutMode=function(t){if(-1==[void 0,null,"continuous","single","twoleft","tworight","two"].indexOf(t))throw new Error('Layout mode must be one of continuous, single, twoleft, tworight. "'+t+'" is not recognized.');jt=t};y.__private__.getLayoutMode=function(){return jt},y.__private__.setDisplayMode=y.setDisplayMode=function(t,e,n){return wt(t),xt(e),_t(n),this};var Nt={title:"",subject:"",author:"",keywords:"",creator:""};y.__private__.getDocumentProperty=function(t){if(-1===Object.keys(Nt).indexOf(t))throw new Error("Invalid argument passed to jsPDF.getDocumentProperty");return Nt[t]},y.__private__.getDocumentProperties=function(){return Nt},y.__private__.setDocumentProperties=y.setProperties=y.setDocumentProperties=function(t){for(var e in Nt)Nt.hasOwnProperty(e)&&t[e]&&(Nt[e]=t[e]);return this},y.__private__.setDocumentProperty=function(t,e){if(-1===Object.keys(Nt).indexOf(t))throw new Error("Invalid arguments passed to jsPDF.setDocumentProperty");return Nt[t]=e};var At,Lt,St,Pt,kt,It={},Ct={},Ft=[],Ot={},Et={},Mt={},Bt={},Tt=null,qt=0,Dt=[],Rt=new E(y),zt=t.hotfixes||[],Ut={},Ht={},Vt=[],Wt=function t(e,n,r,i,o,s){if(!(this instanceof t))return new t(e,n,r,i,o,s);isNaN(e)&&(e=1),isNaN(n)&&(n=0),isNaN(r)&&(r=0),isNaN(i)&&(i=1),isNaN(o)&&(o=0),isNaN(s)&&(s=0),this._matrix=[e,n,r,i,o,s]};Object.defineProperty(Wt.prototype,"sx",{get:function(){return this._matrix[0]},set:function(t){this._matrix[0]=t}}),Object.defineProperty(Wt.prototype,"shy",{get:function(){return this._matrix[1]},set:function(t){this._matrix[1]=t}}),Object.defineProperty(Wt.prototype,"shx",{get:function(){return this._matrix[2]},set:function(t){this._matrix[2]=t}}),Object.defineProperty(Wt.prototype,"sy",{get:function(){return this._matrix[3]},set:function(t){this._matrix[3]=t}}),Object.defineProperty(Wt.prototype,"tx",{get:function(){return this._matrix[4]},set:function(t){this._matrix[4]=t}}),Object.defineProperty(Wt.prototype,"ty",{get:function(){return this._matrix[5]},set:function(t){this._matrix[5]=t}}),Object.defineProperty(Wt.prototype,"a",{get:function(){return this._matrix[0]},set:function(t){this._matrix[0]=t}}),Object.defineProperty(Wt.prototype,"b",{get:function(){return this._matrix[1]},set:function(t){this._matrix[1]=t}}),Object.defineProperty(Wt.prototype,"c",{get:function(){return this._matrix[2]},set:function(t){this._matrix[2]=t}}),Object.defineProperty(Wt.prototype,"d",{get:function(){return this._matrix[3]},set:function(t){this._matrix[3]=t}}),Object.defineProperty(Wt.prototype,"e",{get:function(){return this._matrix[4]},set:function(t){this._matrix[4]=t}}),Object.defineProperty(Wt.prototype,"f",{get:function(){return this._matrix[5]},set:function(t){this._matrix[5]=t}}),Object.defineProperty(Wt.prototype,"rotation",{get:function(){return Math.atan2(this.shx,this.sx)}}),Object.defineProperty(Wt.prototype,"scaleX",{get:function(){return this.decompose().scale.sx}}),Object.defineProperty(Wt.prototype,"scaleY",{get:function(){return this.decompose().scale.sy}}),Object.defineProperty(Wt.prototype,"isIdentity",{get:function(){return 1===this.sx&&0===this.shy&&0===this.shx&&1===this.sy&&0===this.tx&&0===this.ty}}),Wt.prototype.join=function(t){return[this.sx,this.shy,this.shx,this.sy,this.tx,this.ty].map(k).join(t)},Wt.prototype.multiply=function(t){var e=t.sx*this.sx+t.shy*this.shx,n=t.sx*this.shy+t.shy*this.sy,r=t.shx*this.sx+t.sy*this.shx,i=t.shx*this.shy+t.sy*this.sy,o=t.tx*this.sx+t.ty*this.shx+this.tx,s=t.tx*this.shy+t.ty*this.sy+this.ty;return new Wt(e,n,r,i,o,s)},Wt.prototype.decompose=function(){var t=this.sx,e=this.shy,n=this.shx,r=this.sy,i=this.tx,o=this.ty,s=Math.sqrt(t*t+e*e),a=(t/=s)*n+(e/=s)*r;n-=t*a,r-=e*a;var c=Math.sqrt(n*n+r*r);return a/=c,t*(r/=c)<e*(n/=c)&&(t=-t,e=-e,a=-a,s=-s),{scale:new Wt(s,0,0,c,0,0),translate:new Wt(1,0,0,1,i,o),rotate:new Wt(t,e,-e,t,0,0),skew:new Wt(1,0,a,1,0,0)}},Wt.prototype.toString=function(t){return this.join(" ")},Wt.prototype.inversed=function(){var t=this.sx,e=this.shy,n=this.shx,r=this.sy,i=this.tx,o=this.ty,s=1/(t*r-e*n),a=r*s,c=-e*s,u=-n*s,l=t*s;return new Wt(a,c,u,l,-a*i-u*o,-c*i-l*o)},Wt.prototype.applyToPoint=function(t){var e=t.x*this.sx+t.y*this.shx+this.tx,n=t.x*this.shy+t.y*this.sy+this.ty;return new Fn(e,n)},Wt.prototype.applyToRectangle=function(t){var e=this.applyToPoint(t),n=this.applyToPoint(new Fn(t.x+t.w,t.y+t.h));return new On(e.x,e.y,n.x-e.x,n.y-e.y)},Wt.prototype.clone=function(){var t=this.sx,e=this.shy,n=this.shx,r=this.sy,i=this.tx,o=this.ty;return new Wt(t,e,n,r,i,o)},y.Matrix=Wt;var Gt=y.matrixMult=function(t,e){return e.multiply(t)},Yt=new Wt(1,0,0,1,0,0);y.unitMatrix=y.identityMatrix=Yt;var Jt=function(t,e){if(!Et[t]){var n=(e instanceof T?"Sh":"P")+(Object.keys(Ot).length+1).toString(10);e.id=n,Et[t]=n,Ot[n]=e,Rt.publish("addPattern",e)}};y.ShadingPattern=T,y.TilingPattern=q,y.addShadingPattern=function(t,e){return I("addShadingPattern()"),Jt(t,e),this},y.beginTilingPattern=function(t){I("beginTilingPattern()"),Mn(t.boundingBox[0],t.boundingBox[1],t.boundingBox[2]-t.boundingBox[0],t.boundingBox[3]-t.boundingBox[1],t.matrix)},y.endTilingPattern=function(t,e){I("endTilingPattern()"),e.stream=st[Z].join("\n"),Jt(t,e),Rt.publish("endTilingPattern",e),Vt.pop().restore()};var Xt=y.__private__.newObject=function(){var t=Kt();return $t(t,!0),t},Kt=y.__private__.newObjectDeferred=function(){return et++,nt[et]=function(){return it},et},$t=function(t,e){return e="boolean"==typeof e&&e,nt[t]=it,e&&ht(t+" 0 obj"),t},Zt=y.__private__.newAdditionalObject=function(){var t={objId:Kt(),content:""};return ot.push(t),t},Qt=Kt(),te=Kt(),ee=y.__private__.decodeColorString=function(t){var e=t.split(" ");if(2!==e.length||"g"!==e[1]&&"G"!==e[1])5!==e.length||"k"!==e[4]&&"K"!==e[4]||(e=[(1-e[0])*(1-e[3]),(1-e[1])*(1-e[3]),(1-e[2])*(1-e[3]),"r"]);else{var n=parseFloat(e[0]);e=[n,n,n,"r"]}for(var r="#",i=0;i<3;i++)r+=("0"+Math.floor(255*parseFloat(e[i])).toString(16)).slice(-2);return r},ne=y.__private__.encodeColorString=function(t){var e;"string"==typeof t&&(t={ch1:t});var n=t.ch1,r=t.ch2,o=t.ch3,s=t.ch4,a="draw"===t.pdfColorType?["G","RG","K"]:["g","rg","k"];if("string"==typeof n&&"#"!==n.charAt(0)){var c=new m(n);if(c.ok)n=c.toHex();else if(!/^\d*\.?\d*$/.test(n))throw new Error('Invalid color "'+n+'" passed to jsPDF.encodeColorString.')}if("string"==typeof n&&/^#[0-9A-Fa-f]{3}$/.test(n)&&(n="#"+n[1]+n[1]+n[2]+n[2]+n[3]+n[3]),"string"==typeof n&&/^#[0-9A-Fa-f]{6}$/.test(n)){var u=parseInt(n.substr(1),16);n=u>>16&255,r=u>>8&255,o=255&u}if(void 0===r||void 0===s&&n===r&&r===o)if("string"==typeof n)e=n+" "+a[0];else switch(t.precision){case 2:e=B(n/255)+" "+a[0];break;case 3:default:e=R(n/255)+" "+a[0]}else if(void 0===s||"object"===i()(s)){if(s&&!isNaN(s.a)&&0===s.a)return["1.","1.","1.",a[1]].join(" ");if("string"==typeof n)e=[n,r,o,a[1]].join(" ");else switch(t.precision){case 2:e=[B(n/255),B(r/255),B(o/255),a[1]].join(" ");break;default:case 3:e=[R(n/255),R(r/255),R(o/255),a[1]].join(" ")}}else if("string"==typeof n)e=[n,r,o,s,a[2]].join(" ");else switch(t.precision){case 2:e=[B(n),B(r),B(o),B(s),a[2]].join(" ");break;case 3:default:e=[R(n),R(r),R(o),R(s),a[2]].join(" ")}return e},re=y.__private__.getFilters=function(){return u},ie=y.__private__.putStream=function(t){var e=(t=t||{}).data||"",n=t.filters||re(),r=t.alreadyAppliedFilters||[],i=t.addLength1||!1,o=e.length,s=t.objectId,a=function(t){return t};if(null!==g&&void 0===s)throw new Error("ObjectId must be passed to putStream for file encryption");null!==g&&(a=Ye.encryptor(s,0));var c={};!0===n&&(n=["FlateEncode"]);var u=t.additionalKeyValues||[],l=(c=void 0!==D.API.processDataByFilters?D.API.processDataByFilters(e,n):{data:e,reverseChain:[]}).reverseChain+(Array.isArray(r)?r.join(" "):r.toString());if(0!==c.data.length&&(u.push({key:"Length",value:c.data.length}),!0===i&&u.push({key:"Length1",value:o})),0!=l.length)if(l.split("/").length-1==1)u.push({key:"Filter",value:l});else{u.push({key:"Filter",value:"["+l+"]"});for(var h=0;h<u.length;h+=1)if("DecodeParms"===u[h].key){for(var f=[],d=0;d<c.reverseChain.split("/").length-1;d+=1)f.push("null");f.push(u[h].value),u[h].value="["+f.join(" ")+"]"}}ht("<<");for(var p=0;p<u.length;p++)ht("/"+u[p].key+" "+u[p].value);ht(">>"),0!==c.data.length&&(ht("stream"),ht(a(c.data)),ht("endstream"))},oe=y.__private__.putPage=function(t){var e=t.number,n=t.data,r=t.objId,i=t.contentsObjId;$t(r,!0),ht("<</Type /Page"),ht("/Parent "+t.rootDictionaryObjId+" 0 R"),ht("/Resources "+t.resourceDictionaryObjId+" 0 R"),ht("/MediaBox ["+parseFloat(k(t.mediaBox.bottomLeftX))+" "+parseFloat(k(t.mediaBox.bottomLeftY))+" "+k(t.mediaBox.topRightX)+" "+k(t.mediaBox.topRightY)+"]"),null!==t.cropBox&&ht("/CropBox ["+k(t.cropBox.bottomLeftX)+" "+k(t.cropBox.bottomLeftY)+" "+k(t.cropBox.topRightX)+" "+k(t.cropBox.topRightY)+"]"),null!==t.bleedBox&&ht("/BleedBox ["+k(t.bleedBox.bottomLeftX)+" "+k(t.bleedBox.bottomLeftY)+" "+k(t.bleedBox.topRightX)+" "+k(t.bleedBox.topRightY)+"]"),null!==t.trimBox&&ht("/TrimBox ["+k(t.trimBox.bottomLeftX)+" "+k(t.trimBox.bottomLeftY)+" "+k(t.trimBox.topRightX)+" "+k(t.trimBox.topRightY)+"]"),null!==t.artBox&&ht("/ArtBox ["+k(t.artBox.bottomLeftX)+" "+k(t.artBox.bottomLeftY)+" "+k(t.artBox.topRightX)+" "+k(t.artBox.topRightY)+"]"),"number"==typeof t.userUnit&&1!==t.userUnit&&ht("/UserUnit "+t.userUnit),Rt.publish("putPage",{objId:r,pageContext:Dt[e],pageNumber:e,page:n}),ht("/Contents "+i+" 0 R"),ht(">>"),ht("endobj");var o=n.join("\n");return A===N.ADVANCED&&(o+="\nQ"),$t(i,!0),ie({data:o,filters:re(),objectId:i}),ht("endobj"),r},se=y.__private__.putPages=function(){var t,e,n=[];for(t=1;t<=qt;t++)Dt[t].objId=Kt(),Dt[t].contentsObjId=Kt();for(t=1;t<=qt;t++)n.push(oe({number:t,data:st[t],objId:Dt[t].objId,contentsObjId:Dt[t].contentsObjId,mediaBox:Dt[t].mediaBox,cropBox:Dt[t].cropBox,bleedBox:Dt[t].bleedBox,trimBox:Dt[t].trimBox,artBox:Dt[t].artBox,userUnit:Dt[t].userUnit,rootDictionaryObjId:Qt,resourceDictionaryObjId:te}));$t(Qt,!0),ht("<</Type /Pages");var r="/Kids [";for(e=0;e<qt;e++)r+=n[e]+" 0 R ";ht(r+"]"),ht("/Count "+qt),ht(">>"),ht("endobj"),Rt.publish("postPutPages")},ae=function(t){Rt.publish("putFont",{font:t,out:ht,newObject:Xt,putStream:ie}),!0!==t.isAlreadyPutted&&(t.objectNumber=Xt(),ht("<<"),ht("/Type /Font"),ht("/BaseFont /"+O(t.postScriptName)),ht("/Subtype /Type1"),"string"==typeof t.encoding&&ht("/Encoding /"+t.encoding),ht("/FirstChar 32"),ht("/LastChar 255"),ht(">>"),ht("endobj"))},ce=function(){for(var t in It)It.hasOwnProperty(t)&&(!1===v||!0===v&&b.hasOwnProperty(t))&&ae(It[t])},ue=function(t){t.objectNumber=Xt();var e=[];e.push({key:"Type",value:"/XObject"}),e.push({key:"Subtype",value:"/Form"}),e.push({key:"BBox",value:"["+[k(t.x),k(t.y),k(t.x+t.width),k(t.y+t.height)].join(" ")+"]"}),e.push({key:"Matrix",value:"["+t.matrix.toString()+"]"});var n=t.pages[1].join("\n");ie({data:n,additionalKeyValues:e,objectId:t.objectNumber}),ht("endobj")},le=function(){for(var t in Ut)Ut.hasOwnProperty(t)&&ue(Ut[t])},he=function(t,e){var n,r=[],i=1/(e-1);for(n=0;n<1;n+=i)r.push(n);if(r.push(1),0!=t[0].offset){var o={offset:0,color:t[0].color};t.unshift(o)}if(1!=t[t.length-1].offset){var s={offset:1,color:t[t.length-1].color};t.push(s)}for(var a="",c=0,u=0;u<r.length;u++){for(n=r[u];n>t[c+1].offset;)c++;var l=t[c].offset,h=(n-l)/(t[c+1].offset-l),f=t[c].color,d=t[c+1].color;a+=tt(Math.round((1-h)*f[0]+h*d[0]).toString(16))+tt(Math.round((1-h)*f[1]+h*d[1]).toString(16))+tt(Math.round((1-h)*f[2]+h*d[2]).toString(16))}return a.trim()},fe=function(t,e){e||(e=21);var n=Xt(),r=he(t.colors,e),i=[];i.push({key:"FunctionType",value:"0"}),i.push({key:"Domain",value:"[0.0 1.0]"}),i.push({key:"Size",value:"["+e+"]"}),i.push({key:"BitsPerSample",value:"8"}),i.push({key:"Range",value:"[0.0 1.0 0.0 1.0 0.0 1.0]"}),i.push({key:"Decode",value:"[0.0 1.0 0.0 1.0 0.0 1.0]"}),ie({data:r,additionalKeyValues:i,alreadyAppliedFilters:["/ASCIIHexDecode"],objectId:n}),ht("endobj"),t.objectNumber=Xt(),ht("<< /ShadingType "+t.type),ht("/ColorSpace /DeviceRGB");var o="/Coords ["+k(parseFloat(t.coords[0]))+" "+k(parseFloat(t.coords[1]))+" ";2===t.type?o+=k(parseFloat(t.coords[2]))+" "+k(parseFloat(t.coords[3])):o+=k(parseFloat(t.coords[2]))+" "+k(parseFloat(t.coords[3]))+" "+k(parseFloat(t.coords[4]))+" "+k(parseFloat(t.coords[5])),ht(o+="]"),t.matrix&&ht("/Matrix ["+t.matrix.toString()+"]"),ht("/Function "+n+" 0 R"),ht("/Extend [true true]"),ht(">>"),ht("endobj")},de=function(t,e){var n=Kt(),r=Xt();e.push({resourcesOid:n,objectOid:r}),t.objectNumber=r;var i=[];i.push({key:"Type",value:"/Pattern"}),i.push({key:"PatternType",value:"1"}),i.push({key:"PaintType",value:"1"}),i.push({key:"TilingType",value:"1"}),i.push({key:"BBox",value:"["+t.boundingBox.map(k).join(" ")+"]"}),i.push({key:"XStep",value:k(t.xStep)}),i.push({key:"YStep",value:k(t.yStep)}),i.push({key:"Resources",value:n+" 0 R"}),t.matrix&&i.push({key:"Matrix",value:"["+t.matrix.toString()+"]"}),ie({data:t.stream,additionalKeyValues:i,objectId:t.objectNumber}),ht("endobj")},pe=function(t){var e;for(e in Ot)Ot.hasOwnProperty(e)&&(Ot[e]instanceof T?fe(Ot[e]):Ot[e]instanceof q&&de(Ot[e],t))},me=function(t){for(var e in t.objectNumber=Xt(),ht("<<"),t)switch(e){case"opacity":ht("/ca "+B(t[e]));break;case"stroke-opacity":ht("/CA "+B(t[e]))}ht(">>"),ht("endobj")},ge=function(){var t;for(t in Mt)Mt.hasOwnProperty(t)&&me(Mt[t])},ve=function(){for(var t in ht("/XObject <<"),Ut)Ut.hasOwnProperty(t)&&Ut[t].objectNumber>=0&&ht("/"+t+" "+Ut[t].objectNumber+" 0 R");Rt.publish("putXobjectDict"),ht(">>")},be=function(){Ye.oid=Xt(),ht("<<"),ht("/Filter /Standard"),ht("/V "+Ye.v),ht("/R "+Ye.r),ht("/U <"+Ye.toHexString(Ye.U)+">"),ht("/O <"+Ye.toHexString(Ye.O)+">"),ht("/P "+Ye.P),ht(">>"),ht("endobj")},ye=function(){for(var t in ht("/Font <<"),It)It.hasOwnProperty(t)&&(!1===v||!0===v&&b.hasOwnProperty(t))&&ht("/"+t+" "+It[t].objectNumber+" 0 R");ht(">>")},we=function(){if(Object.keys(Ot).length>0){for(var t in ht("/Shading <<"),Ot)Ot.hasOwnProperty(t)&&Ot[t]instanceof T&&Ot[t].objectNumber>=0&&ht("/"+t+" "+Ot[t].objectNumber+" 0 R");Rt.publish("putShadingPatternDict"),ht(">>")}},je=function(t){if(Object.keys(Ot).length>0){for(var e in ht("/Pattern <<"),Ot)Ot.hasOwnProperty(e)&&Ot[e]instanceof y.TilingPattern&&Ot[e].objectNumber>=0&&Ot[e].objectNumber<t&&ht("/"+e+" "+Ot[e].objectNumber+" 0 R");Rt.publish("putTilingPatternDict"),ht(">>")}},_e=function(){if(Object.keys(Mt).length>0){var t;for(t in ht("/ExtGState <<"),Mt)Mt.hasOwnProperty(t)&&Mt[t].objectNumber>=0&&ht("/"+t+" "+Mt[t].objectNumber+" 0 R");Rt.publish("putGStateDict"),ht(">>")}},xe=function(t){$t(t.resourcesOid,!0),ht("<<"),ht("/ProcSet [/PDF /Text /ImageB /ImageC /ImageI]"),ye(),we(),je(t.objectOid),_e(),ve(),ht(">>"),ht("endobj")},Ne=function(){var t=[];ce(),ge(),le(),pe(t),Rt.publish("putResources"),t.forEach(xe),xe({resourcesOid:te,objectOid:Number.MAX_SAFE_INTEGER}),Rt.publish("postPutResources")},Ae=function(){Rt.publish("putAdditionalObjects");for(var t=0;t<ot.length;t++){var e=ot[t];$t(e.objId,!0),ht(e.content),ht("endobj")}Rt.publish("postPutAdditionalObjects")},Le=function(t){Ct[t.fontName]=Ct[t.fontName]||{},Ct[t.fontName][t.fontStyle]=t.id},Se=function(t,e,n,r,i){var o={id:"F"+(Object.keys(It).length+1).toString(10),postScriptName:t,fontName:e,fontStyle:n,encoding:r,isStandardFont:i||!1,metadata:{}};return Rt.publish("addFont",{font:o,instance:this}),It[o.id]=o,Le(o),o.id},Pe=function(t){for(var e=0,n=pt.length;e<n;e++){var r=Se.call(this,t[e][0],t[e][1],t[e][2],pt[e][3],!0);!1===v&&(b[r]=!0);var i=t[e][0].split("-");Le({id:r,fontName:i[0],fontStyle:i[1]||""})}Rt.publish("addFonts",{fonts:It,dictionary:Ct})},ke=function(t){return t.foo=function(){try{return t.apply(this,arguments)}catch(t){var e=t.stack||"";~e.indexOf(" at ")&&(e=e.split(" at ")[1]);var n="Error in function "+e.split("\n")[0].split("<")[0]+": "+t.message;if(!s.console)throw new Error(n);s.console.error(n,t),s.alert&&alert(n)}},t.foo.bar=t,t.foo},Ie=function(t,e){var n,r,i,o,s,a,c,u,l;if(i=(e=e||{}).sourceEncoding||"Unicode",s=e.outputEncoding,(e.autoencode||s)&&It[At].metadata&&It[At].metadata[i]&&It[At].metadata[i].encoding&&(o=It[At].metadata[i].encoding,!s&&It[At].encoding&&(s=It[At].encoding),!s&&o.codePages&&(s=o.codePages[0]),"string"==typeof s&&(s=o[s]),s)){for(c=!1,a=[],n=0,r=t.length;n<r;n++)(u=s[t.charCodeAt(n)])?a.push(String.fromCharCode(u)):a.push(t[n]),a[n].charCodeAt(0)>>8&&(c=!0);t=a.join("")}for(n=t.length;void 0===c&&0!==n;)t.charCodeAt(n-1)>>8&&(c=!0),n--;if(!c)return t;for(a=e.noBOM?[]:[254,255],n=0,r=t.length;n<r;n++){if((l=(u=t.charCodeAt(n))>>8)>>8)throw new Error("Character at position "+n+" of string '"+t+"' exceeds 16bits. Cannot be encoded into UCS-2 BE");a.push(l),a.push(u-(l<<8))}return String.fromCharCode.apply(void 0,a)},Ce=y.__private__.pdfEscape=y.pdfEscape=function(t,e){return Ie(t,e).replace(/\\/g,"\\\\").replace(/\(/g,"\\(").replace(/\)/g,"\\)")},Fe=y.__private__.beginPage=function(t){st[++qt]=[],Dt[qt]={objId:0,contentsObjId:0,userUnit:Number(l),artBox:null,bleedBox:null,cropBox:null,trimBox:null,mediaBox:{bottomLeftX:0,bottomLeftY:0,topRightX:Number(t[0]),topRightY:Number(t[1])}},Me(qt),lt(st[Z])},Oe=function(t,e){var r,i,s;switch(n=e||n,"string"==typeof t&&(r=x(t.toLowerCase()),Array.isArray(r)&&(i=r[0],s=r[1])),Array.isArray(t)&&(i=t[0]*Lt,s=t[1]*Lt),isNaN(i)&&(i=o[0],s=o[1]),(i>14400||s>14400)&&(c.warn("A page in a PDF can not be wider or taller than 14400 userUnit. jsPDF limits the width/height to 14400"),i=Math.min(14400,i),s=Math.min(14400,s)),o=[i,s],n.substr(0,1)){case"l":s>i&&(o=[s,i]);break;case"p":i>s&&(o=[s,i])}Fe(o),mn(dn),ht(xn),0!==kn&&ht(kn+" J"),0!==In&&ht(In+" j"),Rt.publish("addPage",{pageNumber:qt})},Ee=function(t){t>0&&t<=qt&&(st.splice(t,1),Dt.splice(t,1),qt--,Z>qt&&(Z=qt),this.setPage(Z))},Me=function(t){t>0&&t<=qt&&(Z=t)},Be=y.__private__.getNumberOfPages=y.getNumberOfPages=function(){return st.length-1},Te=function(t,e,n){var r,i=void 0;return n=n||{},t=void 0!==t?t:It[At].fontName,e=void 0!==e?e:It[At].fontStyle,r=t.toLowerCase(),void 0!==Ct[r]&&void 0!==Ct[r][e]?i=Ct[r][e]:void 0!==Ct[t]&&void 0!==Ct[t][e]?i=Ct[t][e]:!1===n.disableWarning&&c.warn("Unable to look up font label for font '"+t+"', '"+e+"'. Refer to getFontList() for available fonts."),i||n.noFallback||null==(i=Ct.times[e])&&(i=Ct.times.normal),i},qe=y.__private__.putInfo=function(){var t=Xt(),e=function(t){return t};for(var n in null!==g&&(e=Ye.encryptor(t,0)),ht("<<"),ht("/Producer ("+Ce(e("jsPDF "+D.version))+")"),Nt)Nt.hasOwnProperty(n)&&Nt[n]&&ht("/"+n.substr(0,1).toUpperCase()+n.substr(1)+" ("+Ce(e(Nt[n]))+")");ht("/CreationDate ("+Ce(e(V))+")"),ht(">>"),ht("endobj")},De=y.__private__.putCatalog=function(t){var e=(t=t||{}).rootDictionaryObjId||Qt;switch(Xt(),ht("<<"),ht("/Type /Catalog"),ht("/Pages "+e+" 0 R"),gt||(gt="fullwidth"),gt){case"fullwidth":ht("/OpenAction [3 0 R /FitH null]");break;case"fullheight":ht("/OpenAction [3 0 R /FitV null]");break;case"fullpage":ht("/OpenAction [3 0 R /Fit]");break;case"original":ht("/OpenAction [3 0 R /XYZ null null 1]");break;default:var n=""+gt;"%"===n.substr(n.length-1)&&(gt=parseInt(gt)/100),"number"==typeof gt&&ht("/OpenAction [3 0 R /XYZ null null "+B(gt)+"]")}switch(jt||(jt="continuous"),jt){case"continuous":ht("/PageLayout /OneColumn");break;case"single":ht("/PageLayout /SinglePage");break;case"two":case"twoleft":ht("/PageLayout /TwoColumnLeft");break;case"tworight":ht("/PageLayout /TwoColumnRight")}yt&&ht("/PageMode /"+yt),Rt.publish("putCatalog"),ht(">>"),ht("endobj")},Re=y.__private__.putTrailer=function(){ht("trailer"),ht("<<"),ht("/Size "+(et+1)),ht("/Root "+et+" 0 R"),ht("/Info "+(et-1)+" 0 R"),null!==g&&ht("/Encrypt "+Ye.oid+" 0 R"),ht("/ID [ <"+W+"> <"+W+"> ]"),ht(">>")},ze=y.__private__.putHeader=function(){ht("%PDF-"+w),ht("%ºß¬à")},Ue=y.__private__.putXRef=function(){var t="0000000000";ht("xref"),ht("0 "+(et+1)),ht("0000000000 65535 f ");for(var e=1;e<=et;e++)"function"==typeof nt[e]?ht((t+nt[e]()).slice(-10)+" 00000 n "):void 0!==nt[e]?ht((t+nt[e]).slice(-10)+" 00000 n "):ht("0000000000 00000 n ")},He=y.__private__.buildDocument=function(){ut(),lt(rt),Rt.publish("buildDocument"),ze(),se(),Ae(),Ne(),null!==g&&be(),qe(),De();var t=it;return Ue(),Re(),ht("startxref"),ht(""+t),ht("%%EOF"),lt(st[Z]),rt.join("\n")},Ve=y.__private__.getBlob=function(t){return new Blob([dt(t)],{type:"application/pdf"})},We=y.output=y.__private__.output=ke((function(t,e){switch("string"==typeof(e=e||{})?e={filename:e}:e.filename=e.filename||"generated.pdf",t){case void 0:return He();case"save":y.save(e.filename);break;case"arraybuffer":return dt(He());case"blob":return Ve(He());case"bloburi":case"bloburl":if(void 0!==s.URL&&"function"==typeof s.URL.createObjectURL)return s.URL&&s.URL.createObjectURL(Ve(He()))||void 0;c.warn("bloburl is not supported by your system, because URL.createObjectURL is not supported by your browser.");break;case"datauristring":case"dataurlstring":var n="",r=He();try{n=d(r)}catch(t){n=d(unescape(encodeURIComponent(r)))}return"data:application/pdf;filename="+e.filename+";base64,"+n;case"pdfobjectnewwindow":if("[object Window]"===Object.prototype.toString.call(s)){var i="https://cdnjs.cloudflare.com/ajax/libs/pdfobject/2.1.1/pdfobject.min.js",o=' integrity="sha512-4ze/a9/4jqu+tX9dfOqJYSvyYd5M6qum/3HpCLr+/Jqf0whc37VUbkpNGHR7/8pSnCFw47T1fmIpwBV7UySh3g==" crossorigin="anonymous"';e.pdfObjectUrl&&(i=e.pdfObjectUrl,o="");var a='<html><style>html, body { padding: 0; margin: 0; } iframe { width: 100%; height: 100%; border: 0;}  </style><body><script src="'+i+'"'+o+'><\/script><script >PDFObject.embed("'+this.output("dataurlstring")+'", '+JSON.stringify(e)+");<\/script></body></html>",u=s.open();return null!==u&&u.document.write(a),u}throw new Error("The option pdfobjectnewwindow just works in a browser-environment.");case"pdfjsnewwindow":if("[object Window]"===Object.prototype.toString.call(s)){var l='<html><style>html, body { padding: 0; margin: 0; } iframe { width: 100%; height: 100%; border: 0;}  </style><body><iframe id="pdfViewer" src="'+(e.pdfJsUrl||"examples/PDF.js/web/viewer.html")+"?file=&downloadName="+e.filename+'" width="500px" height="400px" /></body></html>',h=s.open();if(null!==h){h.document.write(l);var f=this;h.document.documentElement.querySelector("#pdfViewer").onload=function(){h.document.title=e.filename,h.document.documentElement.querySelector("#pdfViewer").contentWindow.PDFViewerApplication.open(f.output("bloburl"))}}return h}throw new Error("The option pdfjsnewwindow just works in a browser-environment.");case"dataurlnewwindow":if("[object Window]"!==Object.prototype.toString.call(s))throw new Error("The option dataurlnewwindow just works in a browser-environment.");var p='<html><style>html, body { padding: 0; margin: 0; } iframe { width: 100%; height: 100%; border: 0;}  </style><body><iframe src="'+this.output("datauristring",e)+'"></iframe></body></html>',m=s.open();if(null!==m&&(m.document.write(p),m.document.title=e.filename),m||"undefined"==typeof safari)return m;break;case"datauri":case"dataurl":return s.document.location.href=this.output("datauristring",e);default:return null}})),Ge=function(t){return!0===Array.isArray(zt)&&zt.indexOf(t)>-1};switch(r){case"pt":Lt=1;break;case"mm":Lt=72/25.4;break;case"cm":Lt=72/2.54;break;case"in":Lt=72;break;case"px":Lt=1==Ge("px_scaling")?.75:96/72;break;case"pc":case"em":Lt=12;break;case"ex":Lt=6;break;default:if("number"!=typeof r)throw new Error("Invalid unit: "+r);Lt=r}var Ye=null;K(),Y();var Je=function(t){return null!==g?Ye.encryptor(t,0):function(t){return t}},Xe=y.__private__.getPageInfo=y.getPageInfo=function(t){if(isNaN(t)||t%1!=0)throw new Error("Invalid argument passed to jsPDF.getPageInfo");return{objId:Dt[t].objId,pageNumber:t,pageContext:Dt[t]}},Ke=y.__private__.getPageInfoByObjId=function(t){if(isNaN(t)||t%1!=0)throw new Error("Invalid argument passed to jsPDF.getPageInfoByObjId");for(var e in Dt)if(Dt[e].objId===t)break;return Xe(e)},$e=y.__private__.getCurrentPageInfo=y.getCurrentPageInfo=function(){return{objId:Dt[Z].objId,pageNumber:Z,pageContext:Dt[Z]}};y.addPage=function(){return Oe.apply(this,arguments),this},y.setPage=function(){return Me.apply(this,arguments),lt.call(this,st[Z]),this},y.insertPage=function(t){return this.addPage(),this.movePage(Z,t),this},y.movePage=function(t,e){var n,r;if(t>e){n=st[t],r=Dt[t];for(var i=t;i>e;i--)st[i]=st[i-1],Dt[i]=Dt[i-1];st[e]=n,Dt[e]=r,this.setPage(e)}else if(t<e){n=st[t],r=Dt[t];for(var o=t;o<e;o++)st[o]=st[o+1],Dt[o]=Dt[o+1];st[e]=n,Dt[e]=r,this.setPage(e)}return this},y.deletePage=function(){return Ee.apply(this,arguments),this},y.__private__.text=y.text=function(t,e,n,r,o){var s,a,c,u,l,h,f,d,p,m=(r=r||{}).scope||this;if("number"==typeof t&&"number"==typeof e&&("string"==typeof n||Array.isArray(n))){var g=n;n=e,e=t,t=g}if(arguments[3]instanceof Wt==0?(c=arguments[4],u=arguments[5],"object"===i()(f=arguments[3])&&null!==f||("string"==typeof c&&(u=c,c=null),"string"==typeof f&&(u=f,f=null),"number"==typeof f&&(c=f,f=null),r={flags:f,angle:c,align:u})):(I("The transform parameter of text() with a Matrix value"),p=o),isNaN(e)||isNaN(n)||null==t)throw new Error("Invalid arguments passed to jsPDF.text");if(0===t.length)return m;var v="",y=!1,w="number"==typeof r.lineHeightFactor?r.lineHeightFactor:fn,j=m.internal.scaleFactor;function _(t){return t=t.split("\t").join(Array(r.TabLen||9).join(" ")),Ce(t,f)}function x(t){for(var e,n=t.concat(),r=[],i=n.length;i--;)"string"==typeof(e=n.shift())?r.push(e):Array.isArray(t)&&(1===e.length||void 0===e[1]&&void 0===e[2])?r.push(e[0]):r.push([e[0],e[1],e[2]]);return r}function L(t,e){var n;if("string"==typeof t)n=e(t)[0];else if(Array.isArray(t)){for(var r,i,o=t.concat(),s=[],a=o.length;a--;)"string"==typeof(r=o.shift())?s.push(e(r)[0]):Array.isArray(r)&&"string"==typeof r[0]&&(i=e(r[0],r[1],r[2]),s.push([i[0],i[1],i[2]]));n=s}return n}var S=!1,P=!0;if("string"==typeof t)S=!0;else if(Array.isArray(t)){var C=t.concat();a=[];for(var F,O=C.length;O--;)("string"!=typeof(F=C.shift())||Array.isArray(F)&&"string"!=typeof F[0])&&(P=!1);S=P}if(!1===S)throw new Error('Type of text must be string or Array. "'+t+'" is not recognized.');"string"==typeof t&&(t=t.match(/[\r?\n]/)?t.split(/\r\n|\r|\n/g):[t]);var E=mt/m.internal.scaleFactor,M=E*(w-1);switch(r.baseline){case"bottom":n-=M;break;case"top":n+=E-M;break;case"hanging":n+=E-2*M;break;case"middle":n+=E/2-M}if((h=r.maxWidth||0)>0&&("string"==typeof t?t=m.splitTextToSize(t,h):"[object Array]"===Object.prototype.toString.call(t)&&(t=t.reduce((function(t,e){return t.concat(m.splitTextToSize(e,h))}),[]))),s={text:t,x:e,y:n,options:r,mutex:{pdfEscape:Ce,activeFontKey:At,fonts:It,activeFontSize:mt}},Rt.publish("preProcessText",s),t=s.text,c=(r=s.options).angle,p instanceof Wt==0&&c&&"number"==typeof c){c*=Math.PI/180,0===r.rotationDirection&&(c=-c),A===N.ADVANCED&&(c=-c);var B=Math.cos(c),T=Math.sin(c);p=new Wt(B,T,-T,B,0,0)}else c&&c instanceof Wt&&(p=c);A!==N.ADVANCED||p||(p=Yt),void 0!==(l=r.charSpace||Sn)&&(v+=k(z(l))+" Tc\n",this.setCharSpace(this.getCharSpace()||0)),void 0!==(d=r.horizontalScale)&&(v+=k(100*d)+" Tz\n"),r.lang;var q=-1,D=void 0!==r.renderingMode?r.renderingMode:r.stroke,R=m.internal.getCurrentPageInfo().pageContext;switch(D){case 0:case!1:case"fill":q=0;break;case 1:case!0:case"stroke":q=1;break;case 2:case"fillThenStroke":q=2;break;case 3:case"invisible":q=3;break;case 4:case"fillAndAddForClipping":q=4;break;case 5:case"strokeAndAddPathForClipping":q=5;break;case 6:case"fillThenStrokeAndAddToPathForClipping":q=6;break;case 7:case"addToPathForClipping":q=7}var U=void 0!==R.usedRenderingMode?R.usedRenderingMode:-1;-1!==q?v+=q+" Tr\n":-1!==U&&(v+="0 Tr\n"),-1!==q&&(R.usedRenderingMode=q),u=r.align||"left";var H,V=mt*w,W=m.internal.pageSize.getWidth(),G=It[At];l=r.charSpace||Sn,h=r.maxWidth||0,f=Object.assign({autoencode:!0,noBOM:!0},r.flags);var Y=[],J=function(t){return m.getStringUnitWidth(t,{font:G,charSpace:l,fontSize:mt,doKerning:!1})*mt/j};if("[object Array]"===Object.prototype.toString.call(t)){var X;a=x(t),"left"!==u&&(H=a.map(J));var K,$=0;if("right"===u){e-=H[0],t=[],O=a.length;for(var Z=0;Z<O;Z++)0===Z?(K=yn(e),X=wn(n)):(K=z($-H[Z]),X=-V),t.push([a[Z],K,X]),$=H[Z]}else if("center"===u){e-=H[0]/2,t=[],O=a.length;for(var Q=0;Q<O;Q++)0===Q?(K=yn(e),X=wn(n)):(K=z(($-H[Q])/2),X=-V),t.push([a[Q],K,X]),$=H[Q]}else if("left"===u){t=[],O=a.length;for(var tt=0;tt<O;tt++)t.push(a[tt])}else if("justify"===u&&"Identity-H"===G.encoding){t=[],O=a.length,h=0!==h?h:W;for(var et=0,nt=0;nt<O;nt++)if(X=0===nt?wn(n):-V,K=0===nt?yn(e):et,nt<O-1){var rt=z((h-H[nt])/(a[nt].split(" ").length-1)),it=a[nt].split(" ");t.push([it[0]+" ",K,X]),et=0;for(var ot=1;ot<it.length;ot++){var st=(J(it[ot-1]+" "+it[ot])-J(it[ot]))*j+rt;ot==it.length-1?t.push([it[ot],st,0]):t.push([it[ot]+" ",st,0]),et-=st}}else t.push([a[nt],K,X]);t.push(["",et,0])}else{if("justify"!==u)throw new Error('Unrecognized alignment option, use "left", "center", "right" or "justify".');for(t=[],O=a.length,h=0!==h?h:W,nt=0;nt<O;nt++)X=0===nt?wn(n):-V,K=0===nt?yn(e):0,nt<O-1?Y.push(k(z((h-H[nt])/(a[nt].split(" ").length-1)))):Y.push(0),t.push([a[nt],K,X])}}var at="boolean"==typeof r.R2L?r.R2L:bt;!0===at&&(t=L(t,(function(t,e,n){return[t.split("").reverse().join(""),e,n]}))),s={text:t,x:e,y:n,options:r,mutex:{pdfEscape:Ce,activeFontKey:At,fonts:It,activeFontSize:mt}},Rt.publish("postProcessText",s),t=s.text,y=s.mutex.isHex||!1;var ct=It[At].encoding;"WinAnsiEncoding"!==ct&&"StandardEncoding"!==ct||(t=L(t,(function(t,e,n){return[_(t),e,n]}))),a=x(t),t=[];for(var ut,lt,ft,dt=0,pt=1,gt=Array.isArray(a[0])?pt:dt,vt="",yt=function(t,e,n){var i="";return n instanceof Wt?(n="number"==typeof r.angle?Gt(n,new Wt(1,0,0,1,t,e)):Gt(new Wt(1,0,0,1,t,e),n),A===N.ADVANCED&&(n=Gt(new Wt(1,0,0,-1,0,0),n)),i=n.join(" ")+" Tm\n"):i=k(t)+" "+k(e)+" Td\n",i},wt=0;wt<a.length;wt++){switch(vt="",gt){case pt:ft=(y?"<":"(")+a[wt][0]+(y?">":")"),ut=parseFloat(a[wt][1]),lt=parseFloat(a[wt][2]);break;case dt:ft=(y?"<":"(")+a[wt]+(y?">":")"),ut=yn(e),lt=wn(n)}void 0!==Y&&void 0!==Y[wt]&&(vt=Y[wt]+" Tw\n"),0===wt?t.push(vt+yt(ut,lt,p)+ft):gt===dt?t.push(vt+ft):gt===pt&&t.push(vt+yt(ut,lt,p)+ft)}t=gt===dt?t.join(" Tj\nT* "):t.join(" Tj\n"),t+=" Tj\n";var jt="BT\n/";return jt+=At+" "+mt+" Tf\n",jt+=k(mt*w)+" TL\n",jt+=An+"\n",jt+=v,jt+=t,ht(jt+="ET"),b[At]=!0,m};var Ze=y.__private__.clip=y.clip=function(t){return ht("evenodd"===t?"W*":"W"),this};y.clipEvenOdd=function(){return Ze("evenodd")},y.__private__.discardPath=y.discardPath=function(){return ht("n"),this};var Qe=y.__private__.isValidStyle=function(t){var e=!1;return-1!==[void 0,null,"S","D","F","DF","FD","f","f*","B","B*","n"].indexOf(t)&&(e=!0),e};y.__private__.setDefaultPathOperation=y.setDefaultPathOperation=function(t){return Qe(t)&&(f=t),this};var tn=y.__private__.getStyle=y.getStyle=function(t){var e=f;switch(t){case"D":case"S":e="S";break;case"F":e="f";break;case"FD":case"DF":e="B";break;case"f":case"f*":case"B":case"B*":e=t}return e},en=y.close=function(){return ht("h"),this};y.stroke=function(){return ht("S"),this},y.fill=function(t){return nn("f",t),this},y.fillEvenOdd=function(t){return nn("f*",t),this},y.fillStroke=function(t){return nn("B",t),this},y.fillStrokeEvenOdd=function(t){return nn("B*",t),this};var nn=function(t,e){"object"===i()(e)?sn(e,t):ht(t)},rn=function(t){null===t||A===N.ADVANCED&&void 0===t||(t=tn(t),ht(t))};function on(t,e,n,r,i){var o=new q(e||this.boundingBox,n||this.xStep,r||this.yStep,this.gState,i||this.matrix);o.stream=this.stream;var s=t+"$$"+this.cloneIndex+++"$$";return Jt(s,o),o}var sn=function(t,e){var n=Et[t.key],r=Ot[n];if(r instanceof T)ht("q"),ht(an(e)),r.gState&&y.setGState(r.gState),ht(t.matrix.toString()+" cm"),ht("/"+n+" sh"),ht("Q");else if(r instanceof q){var i=new Wt(1,0,0,-1,0,Rn());t.matrix&&(i=i.multiply(t.matrix||Yt),n=on.call(r,t.key,t.boundingBox,t.xStep,t.yStep,i).id),ht("q"),ht("/Pattern cs"),ht("/"+n+" scn"),r.gState&&y.setGState(r.gState),ht(e),ht("Q")}},an=function(t){switch(t){case"f":case"F":return"W n";case"f*":return"W* n";case"B":return"W S";case"B*":return"W* S";case"S":return"W S";case"n":return"W n"}},cn=y.moveTo=function(t,e){return ht(k(z(t))+" "+k(H(e))+" m"),this},un=y.lineTo=function(t,e){return ht(k(z(t))+" "+k(H(e))+" l"),this},ln=y.curveTo=function(t,e,n,r,i,o){return ht([k(z(t)),k(H(e)),k(z(n)),k(H(r)),k(z(i)),k(H(o)),"c"].join(" ")),this};y.__private__.line=y.line=function(t,e,n,r,i){if(isNaN(t)||isNaN(e)||isNaN(n)||isNaN(r)||!Qe(i))throw new Error("Invalid arguments passed to jsPDF.line");return A===N.COMPAT?this.lines([[n-t,r-e]],t,e,[1,1],i||"S"):this.lines([[n-t,r-e]],t,e,[1,1]).stroke()},y.__private__.lines=y.lines=function(t,e,n,r,i,o){var s,a,c,u,l,h,f,d,p,m,g,v;if("number"==typeof t&&(v=n,n=e,e=t,t=v),r=r||[1,1],o=o||!1,isNaN(e)||isNaN(n)||!Array.isArray(t)||!Array.isArray(r)||!Qe(i)||"boolean"!=typeof o)throw new Error("Invalid arguments passed to jsPDF.lines");for(cn(e,n),s=r[0],a=r[1],u=t.length,m=e,g=n,c=0;c<u;c++)2===(l=t[c]).length?(m=l[0]*s+m,g=l[1]*a+g,un(m,g)):(h=l[0]*s+m,f=l[1]*a+g,d=l[2]*s+m,p=l[3]*a+g,m=l[4]*s+m,g=l[5]*a+g,ln(h,f,d,p,m,g));return o&&en(),rn(i),this},y.path=function(t){for(var e=0;e<t.length;e++){var n=t[e],r=n.c;switch(n.op){case"m":cn(r[0],r[1]);break;case"l":un(r[0],r[1]);break;case"c":ln.apply(this,r);break;case"h":en()}}return this},y.__private__.rect=y.rect=function(t,e,n,r,i){if(isNaN(t)||isNaN(e)||isNaN(n)||isNaN(r)||!Qe(i))throw new Error("Invalid arguments passed to jsPDF.rect");return A===N.COMPAT&&(r=-r),ht([k(z(t)),k(H(e)),k(z(n)),k(z(r)),"re"].join(" ")),rn(i),this},y.__private__.triangle=y.triangle=function(t,e,n,r,i,o,s){if(isNaN(t)||isNaN(e)||isNaN(n)||isNaN(r)||isNaN(i)||isNaN(o)||!Qe(s))throw new Error("Invalid arguments passed to jsPDF.triangle");return this.lines([[n-t,r-e],[i-n,o-r],[t-i,e-o]],t,e,[1,1],s,!0),this},y.__private__.roundedRect=y.roundedRect=function(t,e,n,r,i,o,s){if(isNaN(t)||isNaN(e)||isNaN(n)||isNaN(r)||isNaN(i)||isNaN(o)||!Qe(s))throw new Error("Invalid arguments passed to jsPDF.roundedRect");var a=4/3*(Math.SQRT2-1);return i=Math.min(i,.5*n),o=Math.min(o,.5*r),this.lines([[n-2*i,0],[i*a,0,i,o-o*a,i,o],[0,r-2*o],[0,o*a,-i*a,o,-i,o],[2*i-n,0],[-i*a,0,-i,-o*a,-i,-o],[0,2*o-r],[0,-o*a,i*a,-o,i,-o]],t+i,e,[1,1],s,!0),this},y.__private__.ellipse=y.ellipse=function(t,e,n,r,i){if(isNaN(t)||isNaN(e)||isNaN(n)||isNaN(r)||!Qe(i))throw new Error("Invalid arguments passed to jsPDF.ellipse");var o=4/3*(Math.SQRT2-1)*n,s=4/3*(Math.SQRT2-1)*r;return cn(t+n,e),ln(t+n,e-s,t+o,e-r,t,e-r),ln(t-o,e-r,t-n,e-s,t-n,e),ln(t-n,e+s,t-o,e+r,t,e+r),ln(t+o,e+r,t+n,e+s,t+n,e),rn(i),this},y.__private__.circle=y.circle=function(t,e,n,r){if(isNaN(t)||isNaN(e)||isNaN(n)||!Qe(r))throw new Error("Invalid arguments passed to jsPDF.circle");return this.ellipse(t,e,n,n,r)},y.setFont=function(t,e,n){return n&&(e=P(e,n)),At=Te(t,e,{disableWarning:!1}),this};var hn=y.__private__.getFont=y.getFont=function(){return It[Te.apply(y,arguments)]};y.__private__.getFontList=y.getFontList=function(){var t,e,n={};for(t in Ct)if(Ct.hasOwnProperty(t))for(e in n[t]=[],Ct[t])Ct[t].hasOwnProperty(e)&&n[t].push(e);return n},y.addFont=function(t,e,n,r,i){var o=["StandardEncoding","MacRomanEncoding","Identity-H","WinAnsiEncoding"];return arguments[3]&&-1!==o.indexOf(arguments[3])?i=arguments[3]:arguments[3]&&-1==o.indexOf(arguments[3])&&(n=P(n,r)),i=i||"Identity-H",Se.call(this,t,e,n,i)};var fn,dn=t.lineWidth||.200025,pn=y.__private__.getLineWidth=y.getLineWidth=function(){return dn},mn=y.__private__.setLineWidth=y.setLineWidth=function(t){return dn=t,ht(k(z(t))+" w"),this};y.__private__.setLineDash=D.API.setLineDash=D.API.setLineDashPattern=function(t,e){if(t=t||[],e=e||0,isNaN(e)||!Array.isArray(t))throw new Error("Invalid arguments passed to jsPDF.setLineDash");return t=t.map((function(t){return k(z(t))})).join(" "),e=k(z(e)),ht("["+t+"] "+e+" d"),this};var gn=y.__private__.getLineHeight=y.getLineHeight=function(){return mt*fn};y.__private__.getLineHeight=y.getLineHeight=function(){return mt*fn};var vn=y.__private__.setLineHeightFactor=y.setLineHeightFactor=function(t){return"number"==typeof(t=t||1.15)&&(fn=t),this},bn=y.__private__.getLineHeightFactor=y.getLineHeightFactor=function(){return fn};vn(t.lineHeight);var yn=y.__private__.getHorizontalCoordinate=function(t){return z(t)},wn=y.__private__.getVerticalCoordinate=function(t){return A===N.ADVANCED?t:Dt[Z].mediaBox.topRightY-Dt[Z].mediaBox.bottomLeftY-z(t)},jn=y.__private__.getHorizontalCoordinateString=y.getHorizontalCoordinateString=function(t){return k(yn(t))},_n=y.__private__.getVerticalCoordinateString=y.getVerticalCoordinateString=function(t){return k(wn(t))},xn=t.strokeColor||"0 G";y.__private__.getStrokeColor=y.getDrawColor=function(){return ee(xn)},y.__private__.setStrokeColor=y.setDrawColor=function(t,e,n,r){return xn=ne({ch1:t,ch2:e,ch3:n,ch4:r,pdfColorType:"draw",precision:2}),ht(xn),this};var Nn=t.fillColor||"0 g";y.__private__.getFillColor=y.getFillColor=function(){return ee(Nn)},y.__private__.setFillColor=y.setFillColor=function(t,e,n,r){return Nn=ne({ch1:t,ch2:e,ch3:n,ch4:r,pdfColorType:"fill",precision:2}),ht(Nn),this};var An=t.textColor||"0 g",Ln=y.__private__.getTextColor=y.getTextColor=function(){return ee(An)};y.__private__.setTextColor=y.setTextColor=function(t,e,n,r){return An=ne({ch1:t,ch2:e,ch3:n,ch4:r,pdfColorType:"text",precision:3}),this};var Sn=t.charSpace,Pn=y.__private__.getCharSpace=y.getCharSpace=function(){return parseFloat(Sn||0)};y.__private__.setCharSpace=y.setCharSpace=function(t){if(isNaN(t))throw new Error("Invalid argument passed to jsPDF.setCharSpace");return Sn=t,this};var kn=0;y.CapJoinStyles={0:0,butt:0,but:0,miter:0,1:1,round:1,rounded:1,circle:1,2:2,projecting:2,project:2,square:2,bevel:2},y.__private__.setLineCap=y.setLineCap=function(t){var e=y.CapJoinStyles[t];if(void 0===e)throw new Error("Line cap style of '"+t+"' is not recognized. See or extend .CapJoinStyles property for valid styles");return kn=e,ht(e+" J"),this};var In=0;y.__private__.setLineJoin=y.setLineJoin=function(t){var e=y.CapJoinStyles[t];if(void 0===e)throw new Error("Line join style of '"+t+"' is not recognized. See or extend .CapJoinStyles property for valid styles");return In=e,ht(e+" j"),this},y.__private__.setLineMiterLimit=y.__private__.setMiterLimit=y.setLineMiterLimit=y.setMiterLimit=function(t){if(t=t||0,isNaN(t))throw new Error("Invalid argument passed to jsPDF.setLineMiterLimit");return ht(k(z(t))+" M"),this},y.GState=M,y.setGState=function(t){(t="string"==typeof t?Mt[Bt[t]]:Cn(null,t)).equals(Tt)||(ht("/"+t.id+" gs"),Tt=t)};var Cn=function(t,e){if(!t||!Bt[t]){var n=!1;for(var r in Mt)if(Mt.hasOwnProperty(r)&&Mt[r].equals(e)){n=!0;break}if(n)e=Mt[r];else{var i="GS"+(Object.keys(Mt).length+1).toString(10);Mt[i]=e,e.id=i}return t&&(Bt[t]=e.id),Rt.publish("addGState",e),e}};y.addGState=function(t,e){return Cn(t,e),this},y.saveGraphicsState=function(){return ht("q"),Ft.push({key:At,size:mt,color:An}),this},y.restoreGraphicsState=function(){ht("Q");var t=Ft.pop();return At=t.key,mt=t.size,An=t.color,Tt=null,this},y.setCurrentTransformationMatrix=function(t){return ht(t.toString()+" cm"),this},y.comment=function(t){return ht("#"+t),this};var Fn=function(t,e){var n=t||0;Object.defineProperty(this,"x",{enumerable:!0,get:function(){return n},set:function(t){isNaN(t)||(n=parseFloat(t))}});var r=e||0;Object.defineProperty(this,"y",{enumerable:!0,get:function(){return r},set:function(t){isNaN(t)||(r=parseFloat(t))}});var i="pt";return Object.defineProperty(this,"type",{enumerable:!0,get:function(){return i},set:function(t){i=t.toString()}}),this},On=function(t,e,n,r){Fn.call(this,t,e),this.type="rect";var i=n||0;Object.defineProperty(this,"w",{enumerable:!0,get:function(){return i},set:function(t){isNaN(t)||(i=parseFloat(t))}});var o=r||0;return Object.defineProperty(this,"h",{enumerable:!0,get:function(){return o},set:function(t){isNaN(t)||(o=parseFloat(t))}}),this},En=function(){this.page=qt,this.currentPage=Z,this.pages=st.slice(0),this.pagesContext=Dt.slice(0),this.x=St,this.y=Pt,this.matrix=kt,this.width=qn(Z),this.height=Rn(Z),this.outputDestination=ct,this.id="",this.objectNumber=-1};En.prototype.restore=function(){qt=this.page,Z=this.currentPage,Dt=this.pagesContext,st=this.pages,St=this.x,Pt=this.y,kt=this.matrix,Dn(Z,this.width),zn(Z,this.height),ct=this.outputDestination};var Mn=function(t,e,n,r,i){Vt.push(new En),qt=Z=0,st=[],St=t,Pt=e,kt=i,Fe([n,r])},Bn=function(t){if(Ht[t])Vt.pop().restore();else{var e=new En,n="Xo"+(Object.keys(Ut).length+1).toString(10);e.id=n,Ht[t]=n,Ut[n]=e,Rt.publish("addFormObject",e),Vt.pop().restore()}};for(var Tn in y.beginFormObject=function(t,e,n,r,i){return Mn(t,e,n,r,i),this},y.endFormObject=function(t){return Bn(t),this},y.doFormObject=function(t,e){var n=Ut[Ht[t]];return ht("q"),ht(e.toString()+" cm"),ht("/"+n.id+" Do"),ht("Q"),this},y.getFormObject=function(t){var e=Ut[Ht[t]];return{x:e.x,y:e.y,width:e.width,height:e.height,matrix:e.matrix}},y.save=function(t,e){return t=t||"generated.pdf",(e=e||{}).returnPromise=e.returnPromise||!1,!1===e.returnPromise?(p(Ve(He()),t),"function"==typeof p.unload&&s.setTimeout&&setTimeout(p.unload,911),this):new Promise((function(e,n){try{var r=p(Ve(He()),t);"function"==typeof p.unload&&s.setTimeout&&setTimeout(p.unload,911),e(r)}catch(t){n(t.message)}}))},D.API)D.API.hasOwnProperty(Tn)&&("events"===Tn&&D.API.events.length?function(t,e){var n,r,i;for(i=e.length-1;-1!==i;i--)n=e[i][0],r=e[i][1],t.subscribe.apply(t,[n].concat("function"==typeof r?[r]:r))}(Rt,D.API.events):y[Tn]=D.API[Tn]);var qn=y.getPageWidth=function(t){return(Dt[t=t||Z].mediaBox.topRightX-Dt[t].mediaBox.bottomLeftX)/Lt},Dn=y.setPageWidth=function(t,e){Dt[t].mediaBox.topRightX=e*Lt+Dt[t].mediaBox.bottomLeftX},Rn=y.getPageHeight=function(t){return(Dt[t=t||Z].mediaBox.topRightY-Dt[t].mediaBox.bottomLeftY)/Lt},zn=y.setPageHeight=function(t,e){Dt[t].mediaBox.topRightY=e*Lt+Dt[t].mediaBox.bottomLeftY};return y.internal={pdfEscape:Ce,getStyle:tn,getFont:hn,getFontSize:vt,getCharSpace:Pn,getTextColor:Ln,getLineHeight:gn,getLineHeightFactor:bn,getLineWidth:pn,write:ft,getHorizontalCoordinate:yn,getVerticalCoordinate:wn,getCoordinateString:jn,getVerticalCoordinateString:_n,collections:{},newObject:Xt,newAdditionalObject:Zt,newObjectDeferred:Kt,newObjectDeferredBegin:$t,getFilters:re,putStream:ie,events:Rt,scaleFactor:Lt,pageSize:{getWidth:function(){return qn(Z)},setWidth:function(t){Dn(Z,t)},getHeight:function(){return Rn(Z)},setHeight:function(t){zn(Z,t)}},encryptionOptions:g,encryption:Ye,getEncryptor:Je,output:We,getNumberOfPages:Be,pages:st,out:ht,f2:B,f3:R,getPageInfo:Xe,getPageInfoByObjId:Ke,getCurrentPageInfo:$e,getPDFVersion:j,Point:Fn,Rectangle:On,Matrix:Wt,hasHotfix:Ge},Object.defineProperty(y.internal.pageSize,"width",{get:function(){return qn(Z)},set:function(t){Dn(Z,t)},enumerable:!0,configurable:!0}),Object.defineProperty(y.internal.pageSize,"height",{get:function(){return Rn(Z)},set:function(t){zn(Z,t)},enumerable:!0,configurable:!0}),Pe.call(y,pt),At="F1",Oe(o,n),Rt.publish("initialized"),y}F.prototype.lsbFirstWord=function(t){return String.fromCharCode(t>>0&255,t>>8&255,t>>16&255,t>>24&255)},F.prototype.toHexString=function(t){return t.split("").map((function(t){return("0"+(255&t.charCodeAt(0)).toString(16)).slice(-2)})).join("")},F.prototype.hexToBytes=function(t){for(var e=[],n=0;n<t.length;n+=2)e.push(String.fromCharCode(parseInt(t.substr(n,2),16)));return e.join("")},F.prototype.processOwnerPassword=function(t,e){return I(S(e).substr(0,5),t)},F.prototype.encryptor=function(t,e){var n=S(this.encryptionKey+String.fromCharCode(255&t,t>>8&255,t>>16&255,255&e,e>>8&255)).substr(0,10);return function(t){return I(n,t)}},M.prototype.equals=function(t){var e,n="id,objectNumber,equals";if(!t||i()(t)!==i()(this))return!1;var r=0;for(e in this)if(!(n.indexOf(e)>=0)){if(this.hasOwnProperty(e)&&!t.hasOwnProperty(e))return!1;if(this[e]!==t[e])return!1;r++}for(e in t)t.hasOwnProperty(e)&&n.indexOf(e)<0&&r--;return 0===r},D.API={events:[]},D.version="2.5.2";var R=D.API,z=1,U=function(t){return t.replace(/\\/g,"\\\\").replace(/\(/g,"\\(").replace(/\)/g,"\\)")},H=function(t){return t.replace(/\\\\/g,"\\").replace(/\\\(/g,"(").replace(/\\\)/g,")")},V=function(t){return t.toFixed(2)},W=function(t){return t.toFixed(5)};R.__acroform__={};var G=function(t,e){t.prototype=Object.create(e.prototype),t.prototype.constructor=t},Y=function(t){return t*z},J=function(t){var e=new ft,n=Lt.internal.getHeight(t)||0,r=Lt.internal.getWidth(t)||0;return e.BBox=[0,0,Number(V(r)),Number(V(n))],e},X=R.__acroform__.setBit=function(t,e){if(t=t||0,e=e||0,isNaN(t)||isNaN(e))throw new Error("Invalid arguments passed to jsPDF.API.__acroform__.setBit");return t|1<<e},K=R.__acroform__.clearBit=function(t,e){if(t=t||0,e=e||0,isNaN(t)||isNaN(e))throw new Error("Invalid arguments passed to jsPDF.API.__acroform__.clearBit");return t&~(1<<e)},$=R.__acroform__.getBit=function(t,e){if(isNaN(t)||isNaN(e))throw new Error("Invalid arguments passed to jsPDF.API.__acroform__.getBit");return 0==(t&1<<e)?0:1},Z=R.__acroform__.getBitForPdf=function(t,e){if(isNaN(t)||isNaN(e))throw new Error("Invalid arguments passed to jsPDF.API.__acroform__.getBitForPdf");return $(t,e-1)},Q=R.__acroform__.setBitForPdf=function(t,e){if(isNaN(t)||isNaN(e))throw new Error("Invalid arguments passed to jsPDF.API.__acroform__.setBitForPdf");return X(t,e-1)},tt=R.__acroform__.clearBitForPdf=function(t,e){if(isNaN(t)||isNaN(e))throw new Error("Invalid arguments passed to jsPDF.API.__acroform__.clearBitForPdf");return K(t,e-1)},et=R.__acroform__.calculateCoordinates=function(t,e){var n=e.internal.getHorizontalCoordinate,r=e.internal.getVerticalCoordinate,i=t[0],o=t[1],s=t[2],a=t[3],c={};return c.lowerLeft_X=n(i)||0,c.lowerLeft_Y=r(o+a)||0,c.upperRight_X=n(i+s)||0,c.upperRight_Y=r(o)||0,[Number(V(c.lowerLeft_X)),Number(V(c.lowerLeft_Y)),Number(V(c.upperRight_X)),Number(V(c.upperRight_Y))]},nt=function(t){if(t.appearanceStreamContent)return t.appearanceStreamContent;if(t.V||t.DV){var e=[],n=t._V||t.DV,r=rt(t,n),i=t.scope.internal.getFont(t.fontName,t.fontStyle).id;e.push("/Tx BMC"),e.push("q"),e.push("BT"),e.push(t.scope.__private__.encodeColorString(t.color)),e.push("/"+i+" "+V(r.fontSize)+" Tf"),e.push("1 0 0 1 0 0 Tm"),e.push(r.text),e.push("ET"),e.push("Q"),e.push("EMC");var o=J(t);return o.scope=t.scope,o.stream=e.join("\n"),o}},rt=function(t,e){var n=0===t.fontSize?t.maxFontSize:t.fontSize,r={text:"",fontSize:""},i=(e=")"==(e="("==e.substr(0,1)?e.substr(1):e).substr(e.length-1)?e.substr(0,e.length-1):e).split(" ");i=t.multiline?i.map((function(t){return t.split("\n")})):i.map((function(t){return[t]}));var o=n,s=Lt.internal.getHeight(t)||0;s=s<0?-s:s;var a=Lt.internal.getWidth(t)||0;a=a<0?-a:a;var c=function(e,n,r){if(e+1<i.length){var o=n+" "+i[e+1][0];return it(o,t,r).width<=a-4}return!1};o++;t:for(;o>0;){e="",o--;var u,l,h=it("3",t,o).height,f=t.multiline?s-o:(s-h)/2,d=f+=2,p=0,m=0,g=0;if(o<=0){e="(...) Tj\n",e+="% Width of Text: "+it(e,t,o=12).width+", FieldWidth:"+a+"\n";break}for(var v="",b=0,y=0;y<i.length;y++)if(i.hasOwnProperty(y)){var w=!1;if(1!==i[y].length&&g!==i[y].length-1){if((h+2)*(b+2)+2>s)continue t;v+=i[y][g],w=!0,m=y,y--}else{v=" "==(v+=i[y][g]+" ").substr(v.length-1)?v.substr(0,v.length-1):v;var j=parseInt(y),_=c(j,v,o),x=y>=i.length-1;if(_&&!x){v+=" ",g=0;continue}if(_||x){if(x)m=j;else if(t.multiline&&(h+2)*(b+2)+2>s)continue t}else{if(!t.multiline)continue t;if((h+2)*(b+2)+2>s)continue t;m=j}}for(var N="",A=p;A<=m;A++){var L=i[A];if(t.multiline){if(A===m){N+=L[g]+" ",g=(g+1)%L.length;continue}if(A===p){N+=L[L.length-1]+" ";continue}}N+=L[0]+" "}switch(N=" "==N.substr(N.length-1)?N.substr(0,N.length-1):N,l=it(N,t,o).width,t.textAlign){case"right":u=a-l-2;break;case"center":u=(a-l)/2;break;case"left":default:u=2}e+=V(u)+" "+V(d)+" Td\n",e+="("+U(N)+") Tj\n",e+=-V(u)+" 0 Td\n",d=-(o+2),l=0,p=w?m:m+1,b++,v=""}break}return r.text=e,r.fontSize=o,r},it=function(t,e,n){var r=e.scope.internal.getFont(e.fontName,e.fontStyle),i=e.scope.getStringUnitWidth(t,{font:r,fontSize:parseFloat(n),charSpace:0})*parseFloat(n);return{height:e.scope.getStringUnitWidth("3",{font:r,fontSize:parseFloat(n),charSpace:0})*parseFloat(n)*1.5,width:i}},ot={fields:[],xForms:[],acroFormDictionaryRoot:null,printedOut:!1,internal:null,isInitialized:!1},st=function(t,e){var n={type:"reference",object:t};void 0===e.internal.getPageInfo(t.page).pageContext.annotations.find((function(t){return t.type===n.type&&t.object===n.object}))&&e.internal.getPageInfo(t.page).pageContext.annotations.push(n)},at=function(t,e){for(var n in t)if(t.hasOwnProperty(n)){var r=n,o=t[n];e.internal.newObjectDeferredBegin(o.objId,!0),"object"===i()(o)&&"function"==typeof o.putStream&&o.putStream(),delete t[r]}},ct=function(t,e){if(e.scope=t,void 0!==t.internal&&(void 0===t.internal.acroformPlugin||!1===t.internal.acroformPlugin.isInitialized)){if(pt.FieldNum=0,t.internal.acroformPlugin=JSON.parse(JSON.stringify(ot)),t.internal.acroformPlugin.acroFormDictionaryRoot)throw new Error("Exception while creating AcroformDictionary");z=t.internal.scaleFactor,t.internal.acroformPlugin.acroFormDictionaryRoot=new dt,t.internal.acroformPlugin.acroFormDictionaryRoot.scope=t,t.internal.acroformPlugin.acroFormDictionaryRoot._eventID=t.internal.events.subscribe("postPutResources",(function(){!function(t){t.internal.events.unsubscribe(t.internal.acroformPlugin.acroFormDictionaryRoot._eventID),delete t.internal.acroformPlugin.acroFormDictionaryRoot._eventID,t.internal.acroformPlugin.printedOut=!0}(t)})),t.internal.events.subscribe("buildDocument",(function(){!function(t){t.internal.acroformPlugin.acroFormDictionaryRoot.objId=void 0;var e=t.internal.acroformPlugin.acroFormDictionaryRoot.Fields;for(var n in e)if(e.hasOwnProperty(n)){var r=e[n];r.objId=void 0,r.hasAnnotation&&st(r,t)}}(t)})),t.internal.events.subscribe("putCatalog",(function(){!function(t){if(void 0===t.internal.acroformPlugin.acroFormDictionaryRoot)throw new Error("putCatalogCallback: Root missing.");t.internal.write("/AcroForm "+t.internal.acroformPlugin.acroFormDictionaryRoot.objId+" 0 R")}(t)})),t.internal.events.subscribe("postPutPages",(function(e){!function(t,e){var n=!t;for(var r in t||(e.internal.newObjectDeferredBegin(e.internal.acroformPlugin.acroFormDictionaryRoot.objId,!0),e.internal.acroformPlugin.acroFormDictionaryRoot.putStream()),t=t||e.internal.acroformPlugin.acroFormDictionaryRoot.Kids)if(t.hasOwnProperty(r)){var o=t[r],s=[],a=o.Rect;if(o.Rect&&(o.Rect=et(o.Rect,e)),e.internal.newObjectDeferredBegin(o.objId,!0),o.DA=Lt.createDefaultAppearanceStream(o),"object"===i()(o)&&"function"==typeof o.getKeyValueListForStream&&(s=o.getKeyValueListForStream()),o.Rect=a,o.hasAppearanceStream&&!o.appearanceStreamContent){var c=nt(o);s.push({key:"AP",value:"<</N "+c+">>"}),e.internal.acroformPlugin.xForms.push(c)}if(o.appearanceStreamContent){var u="";for(var l in o.appearanceStreamContent)if(o.appearanceStreamContent.hasOwnProperty(l)){var h=o.appearanceStreamContent[l];if(u+="/"+l+" ",u+="<<",Object.keys(h).length>=1||Array.isArray(h)){for(var r in h)if(h.hasOwnProperty(r)){var f=h[r];"function"==typeof f&&(f=f.call(e,o)),u+="/"+r+" "+f+" ",e.internal.acroformPlugin.xForms.indexOf(f)>=0||e.internal.acroformPlugin.xForms.push(f)}}else"function"==typeof(f=h)&&(f=f.call(e,o)),u+="/"+r+" "+f,e.internal.acroformPlugin.xForms.indexOf(f)>=0||e.internal.acroformPlugin.xForms.push(f);u+=">>"}s.push({key:"AP",value:"<<\n"+u+">>"})}e.internal.putStream({additionalKeyValues:s,objectId:o.objId}),e.internal.out("endobj")}n&&at(e.internal.acroformPlugin.xForms,e)}(e,t)})),t.internal.acroformPlugin.isInitialized=!0}},ut=R.__acroform__.arrayToPdfArray=function(t,e,n){var r=function(t){return t};if(Array.isArray(t)){for(var o="[",s=0;s<t.length;s++)switch(0!==s&&(o+=" "),i()(t[s])){case"boolean":case"number":case"object":o+=t[s].toString();break;case"string":"/"!==t[s].substr(0,1)?(void 0!==e&&n&&(r=n.internal.getEncryptor(e)),o+="("+U(r(t[s].toString()))+")"):o+=t[s].toString()}return o+"]"}throw new Error("Invalid argument passed to jsPDF.__acroform__.arrayToPdfArray")},lt=function(t,e,n){var r=function(t){return t};return void 0!==e&&n&&(r=n.internal.getEncryptor(e)),(t=t||"").toString(),"("+U(r(t))+")"},ht=function(){this._objId=void 0,this._scope=void 0,Object.defineProperty(this,"objId",{get:function(){if(void 0===this._objId){if(void 0===this.scope)return;this._objId=this.scope.internal.newObjectDeferred()}return this._objId},set:function(t){this._objId=t}}),Object.defineProperty(this,"scope",{value:this._scope,writable:!0})};ht.prototype.toString=function(){return this.objId+" 0 R"},ht.prototype.putStream=function(){var t=this.getKeyValueListForStream();this.scope.internal.putStream({data:this.stream,additionalKeyValues:t,objectId:this.objId}),this.scope.internal.out("endobj")},ht.prototype.getKeyValueListForStream=function(){var t=[],e=Object.getOwnPropertyNames(this).filter((function(t){return"content"!=t&&"appearanceStreamContent"!=t&&"scope"!=t&&"objId"!=t&&"_"!=t.substring(0,1)}));for(var n in e)if(!1===Object.getOwnPropertyDescriptor(this,e[n]).configurable){var r=e[n],i=this[r];i&&(Array.isArray(i)?t.push({key:r,value:ut(i,this.objId,this.scope)}):i instanceof ht?(i.scope=this.scope,t.push({key:r,value:i.objId+" 0 R"})):"function"!=typeof i&&t.push({key:r,value:i}))}return t};var ft=function(){ht.call(this),Object.defineProperty(this,"Type",{value:"/XObject",configurable:!1,writable:!0}),Object.defineProperty(this,"Subtype",{value:"/Form",configurable:!1,writable:!0}),Object.defineProperty(this,"FormType",{value:1,configurable:!1,writable:!0});var t,e=[];Object.defineProperty(this,"BBox",{configurable:!1,get:function(){return e},set:function(t){e=t}}),Object.defineProperty(this,"Resources",{value:"2 0 R",configurable:!1,writable:!0}),Object.defineProperty(this,"stream",{enumerable:!1,configurable:!0,set:function(e){t=e.trim()},get:function(){return t||null}})};G(ft,ht);var dt=function(){ht.call(this);var t,e=[];Object.defineProperty(this,"Kids",{enumerable:!1,configurable:!0,get:function(){return e.length>0?e:void 0}}),Object.defineProperty(this,"Fields",{enumerable:!1,configurable:!1,get:function(){return e}}),Object.defineProperty(this,"DA",{enumerable:!1,configurable:!1,get:function(){if(t){var e=function(t){return t};return this.scope&&(e=this.scope.internal.getEncryptor(this.objId)),"("+U(e(t))+")"}},set:function(e){t=e}})};G(dt,ht);var pt=function t(){ht.call(this);var e=4;Object.defineProperty(this,"F",{enumerable:!1,configurable:!1,get:function(){return e},set:function(t){if(isNaN(t))throw new Error('Invalid value "'+t+'" for attribute F supplied.');e=t}}),Object.defineProperty(this,"showWhenPrinted",{enumerable:!0,configurable:!0,get:function(){return Boolean(Z(e,3))},set:function(t){!0===Boolean(t)?this.F=Q(e,3):this.F=tt(e,3)}});var n=0;Object.defineProperty(this,"Ff",{enumerable:!1,configurable:!1,get:function(){return n},set:function(t){if(isNaN(t))throw new Error('Invalid value "'+t+'" for attribute Ff supplied.');n=t}});var r=[];Object.defineProperty(this,"Rect",{enumerable:!1,configurable:!1,get:function(){if(0!==r.length)return r},set:function(t){r=void 0!==t?t:[]}}),Object.defineProperty(this,"x",{enumerable:!0,configurable:!0,get:function(){return!r||isNaN(r[0])?0:r[0]},set:function(t){r[0]=t}}),Object.defineProperty(this,"y",{enumerable:!0,configurable:!0,get:function(){return!r||isNaN(r[1])?0:r[1]},set:function(t){r[1]=t}}),Object.defineProperty(this,"width",{enumerable:!0,configurable:!0,get:function(){return!r||isNaN(r[2])?0:r[2]},set:function(t){r[2]=t}}),Object.defineProperty(this,"height",{enumerable:!0,configurable:!0,get:function(){return!r||isNaN(r[3])?0:r[3]},set:function(t){r[3]=t}});var i="";Object.defineProperty(this,"FT",{enumerable:!0,configurable:!1,get:function(){return i},set:function(t){switch(t){case"/Btn":case"/Tx":case"/Ch":case"/Sig":i=t;break;default:throw new Error('Invalid value "'+t+'" for attribute FT supplied.')}}});var o=null;Object.defineProperty(this,"T",{enumerable:!0,configurable:!1,get:function(){if(!o||o.length<1){if(this instanceof _t)return;o="FieldObject"+t.FieldNum++}var e=function(t){return t};return this.scope&&(e=this.scope.internal.getEncryptor(this.objId)),"("+U(e(o))+")"},set:function(t){o=t.toString()}}),Object.defineProperty(this,"fieldName",{configurable:!0,enumerable:!0,get:function(){return o},set:function(t){o=t}});var s="helvetica";Object.defineProperty(this,"fontName",{enumerable:!0,configurable:!0,get:function(){return s},set:function(t){s=t}});var a="normal";Object.defineProperty(this,"fontStyle",{enumerable:!0,configurable:!0,get:function(){return a},set:function(t){a=t}});var c=0;Object.defineProperty(this,"fontSize",{enumerable:!0,configurable:!0,get:function(){return c},set:function(t){c=t}});var u=void 0;Object.defineProperty(this,"maxFontSize",{enumerable:!0,configurable:!0,get:function(){return void 0===u?50/z:u},set:function(t){u=t}});var l="black";Object.defineProperty(this,"color",{enumerable:!0,configurable:!0,get:function(){return l},set:function(t){l=t}});var h="/F1 0 Tf 0 g";Object.defineProperty(this,"DA",{enumerable:!0,configurable:!1,get:function(){if(!(!h||this instanceof _t||this instanceof Nt))return lt(h,this.objId,this.scope)},set:function(t){t=t.toString(),h=t}});var f=null;Object.defineProperty(this,"DV",{enumerable:!1,configurable:!1,get:function(){if(f)return this instanceof yt==0?lt(f,this.objId,this.scope):f},set:function(t){t=t.toString(),f=this instanceof yt==0?"("===t.substr(0,1)?H(t.substr(1,t.length-2)):H(t):t}}),Object.defineProperty(this,"defaultValue",{enumerable:!0,configurable:!0,get:function(){return this instanceof yt==1?H(f.substr(1,f.length-1)):f},set:function(t){t=t.toString(),f=this instanceof yt==1?"/"+t:t}});var d=null;Object.defineProperty(this,"_V",{enumerable:!1,configurable:!1,get:function(){if(d)return d},set:function(t){this.V=t}}),Object.defineProperty(this,"V",{enumerable:!1,configurable:!1,get:function(){if(d)return this instanceof yt==0?lt(d,this.objId,this.scope):d},set:function(t){t=t.toString(),d=this instanceof yt==0?"("===t.substr(0,1)?H(t.substr(1,t.length-2)):H(t):t}}),Object.defineProperty(this,"value",{enumerable:!0,configurable:!0,get:function(){return this instanceof yt==1?H(d.substr(1,d.length-1)):d},set:function(t){t=t.toString(),d=this instanceof yt==1?"/"+t:t}}),Object.defineProperty(this,"hasAnnotation",{enumerable:!0,configurable:!0,get:function(){return this.Rect}}),Object.defineProperty(this,"Type",{enumerable:!0,configurable:!1,get:function(){return this.hasAnnotation?"/Annot":null}}),Object.defineProperty(this,"Subtype",{enumerable:!0,configurable:!1,get:function(){return this.hasAnnotation?"/Widget":null}});var p,m=!1;Object.defineProperty(this,"hasAppearanceStream",{enumerable:!0,configurable:!0,get:function(){return m},set:function(t){t=Boolean(t),m=t}}),Object.defineProperty(this,"page",{enumerable:!0,configurable:!0,get:function(){if(p)return p},set:function(t){p=t}}),Object.defineProperty(this,"readOnly",{enumerable:!0,configurable:!0,get:function(){return Boolean(Z(this.Ff,1))},set:function(t){!0===Boolean(t)?this.Ff=Q(this.Ff,1):this.Ff=tt(this.Ff,1)}}),Object.defineProperty(this,"required",{enumerable:!0,configurable:!0,get:function(){return Boolean(Z(this.Ff,2))},set:function(t){!0===Boolean(t)?this.Ff=Q(this.Ff,2):this.Ff=tt(this.Ff,2)}}),Object.defineProperty(this,"noExport",{enumerable:!0,configurable:!0,get:function(){return Boolean(Z(this.Ff,3))},set:function(t){!0===Boolean(t)?this.Ff=Q(this.Ff,3):this.Ff=tt(this.Ff,3)}});var g=null;Object.defineProperty(this,"Q",{enumerable:!0,configurable:!1,get:function(){if(null!==g)return g},set:function(t){if(-1===[0,1,2].indexOf(t))throw new Error('Invalid value "'+t+'" for attribute Q supplied.');g=t}}),Object.defineProperty(this,"textAlign",{get:function(){var t;switch(g){case 0:default:t="left";break;case 1:t="center";break;case 2:t="right"}return t},configurable:!0,enumerable:!0,set:function(t){switch(t){case"right":case 2:g=2;break;case"center":case 1:g=1;break;case"left":case 0:default:g=0}}})};G(pt,ht);var mt=function(){pt.call(this),this.FT="/Ch",this.V="()",this.fontName="zapfdingbats";var t=0;Object.defineProperty(this,"TI",{enumerable:!0,configurable:!1,get:function(){return t},set:function(e){t=e}}),Object.defineProperty(this,"topIndex",{enumerable:!0,configurable:!0,get:function(){return t},set:function(e){t=e}});var e=[];Object.defineProperty(this,"Opt",{enumerable:!0,configurable:!1,get:function(){return ut(e,this.objId,this.scope)},set:function(t){var n,r;r=[],"string"==typeof(n=t)&&(r=function(t,e,n){n||(n=1);for(var r,i=[];r=e.exec(t);)i.push(r[n]);return i}(n,/\((.*?)\)/g)),e=r}}),this.getOptions=function(){return e},this.setOptions=function(t){e=t,this.sort&&e.sort()},this.addOption=function(t){t=(t=t||"").toString(),e.push(t),this.sort&&e.sort()},this.removeOption=function(t,n){for(n=n||!1,t=(t=t||"").toString();-1!==e.indexOf(t)&&(e.splice(e.indexOf(t),1),!1!==n););},Object.defineProperty(this,"combo",{enumerable:!0,configurable:!0,get:function(){return Boolean(Z(this.Ff,18))},set:function(t){!0===Boolean(t)?this.Ff=Q(this.Ff,18):this.Ff=tt(this.Ff,18)}}),Object.defineProperty(this,"edit",{enumerable:!0,configurable:!0,get:function(){return Boolean(Z(this.Ff,19))},set:function(t){!0===this.combo&&(!0===Boolean(t)?this.Ff=Q(this.Ff,19):this.Ff=tt(this.Ff,19))}}),Object.defineProperty(this,"sort",{enumerable:!0,configurable:!0,get:function(){return Boolean(Z(this.Ff,20))},set:function(t){!0===Boolean(t)?(this.Ff=Q(this.Ff,20),e.sort()):this.Ff=tt(this.Ff,20)}}),Object.defineProperty(this,"multiSelect",{enumerable:!0,configurable:!0,get:function(){return Boolean(Z(this.Ff,22))},set:function(t){!0===Boolean(t)?this.Ff=Q(this.Ff,22):this.Ff=tt(this.Ff,22)}}),Object.defineProperty(this,"doNotSpellCheck",{enumerable:!0,configurable:!0,get:function(){return Boolean(Z(this.Ff,23))},set:function(t){!0===Boolean(t)?this.Ff=Q(this.Ff,23):this.Ff=tt(this.Ff,23)}}),Object.defineProperty(this,"commitOnSelChange",{enumerable:!0,configurable:!0,get:function(){return Boolean(Z(this.Ff,27))},set:function(t){!0===Boolean(t)?this.Ff=Q(this.Ff,27):this.Ff=tt(this.Ff,27)}}),this.hasAppearanceStream=!1};G(mt,pt);var gt=function(){mt.call(this),this.fontName="helvetica",this.combo=!1};G(gt,mt);var vt=function(){gt.call(this),this.combo=!0};G(vt,gt);var bt=function(){vt.call(this),this.edit=!0};G(bt,vt);var yt=function(){pt.call(this),this.FT="/Btn",Object.defineProperty(this,"noToggleToOff",{enumerable:!0,configurable:!0,get:function(){return Boolean(Z(this.Ff,15))},set:function(t){!0===Boolean(t)?this.Ff=Q(this.Ff,15):this.Ff=tt(this.Ff,15)}}),Object.defineProperty(this,"radio",{enumerable:!0,configurable:!0,get:function(){return Boolean(Z(this.Ff,16))},set:function(t){!0===Boolean(t)?this.Ff=Q(this.Ff,16):this.Ff=tt(this.Ff,16)}}),Object.defineProperty(this,"pushButton",{enumerable:!0,configurable:!0,get:function(){return Boolean(Z(this.Ff,17))},set:function(t){!0===Boolean(t)?this.Ff=Q(this.Ff,17):this.Ff=tt(this.Ff,17)}}),Object.defineProperty(this,"radioIsUnison",{enumerable:!0,configurable:!0,get:function(){return Boolean(Z(this.Ff,26))},set:function(t){!0===Boolean(t)?this.Ff=Q(this.Ff,26):this.Ff=tt(this.Ff,26)}});var t,e={};Object.defineProperty(this,"MK",{enumerable:!1,configurable:!1,get:function(){var t=function(t){return t};if(this.scope&&(t=this.scope.internal.getEncryptor(this.objId)),0!==Object.keys(e).length){var n,r=[];for(n in r.push("<<"),e)r.push("/"+n+" ("+U(t(e[n]))+")");return r.push(">>"),r.join("\n")}},set:function(t){"object"===i()(t)&&(e=t)}}),Object.defineProperty(this,"caption",{enumerable:!0,configurable:!0,get:function(){return e.CA||""},set:function(t){"string"==typeof t&&(e.CA=t)}}),Object.defineProperty(this,"AS",{enumerable:!1,configurable:!1,get:function(){return t},set:function(e){t=e}}),Object.defineProperty(this,"appearanceState",{enumerable:!0,configurable:!0,get:function(){return t.substr(1,t.length-1)},set:function(e){t="/"+e}})};G(yt,pt);var wt=function(){yt.call(this),this.pushButton=!0};G(wt,yt);var jt=function(){yt.call(this),this.radio=!0,this.pushButton=!1;var t=[];Object.defineProperty(this,"Kids",{enumerable:!0,configurable:!1,get:function(){return t},set:function(e){t=void 0!==e?e:[]}})};G(jt,yt);var _t=function(){var t,e;pt.call(this),Object.defineProperty(this,"Parent",{enumerable:!1,configurable:!1,get:function(){return t},set:function(e){t=e}}),Object.defineProperty(this,"optionName",{enumerable:!1,configurable:!0,get:function(){return e},set:function(t){e=t}});var n,r={};Object.defineProperty(this,"MK",{enumerable:!1,configurable:!1,get:function(){var t=function(t){return t};this.scope&&(t=this.scope.internal.getEncryptor(this.objId));var e,n=[];for(e in n.push("<<"),r)n.push("/"+e+" ("+U(t(r[e]))+")");return n.push(">>"),n.join("\n")},set:function(t){"object"===i()(t)&&(r=t)}}),Object.defineProperty(this,"caption",{enumerable:!0,configurable:!0,get:function(){return r.CA||""},set:function(t){"string"==typeof t&&(r.CA=t)}}),Object.defineProperty(this,"AS",{enumerable:!1,configurable:!1,get:function(){return n},set:function(t){n=t}}),Object.defineProperty(this,"appearanceState",{enumerable:!0,configurable:!0,get:function(){return n.substr(1,n.length-1)},set:function(t){n="/"+t}}),this.caption="l",this.appearanceState="Off",this._AppearanceType=Lt.RadioButton.Circle,this.appearanceStreamContent=this._AppearanceType.createAppearanceStream(this.optionName)};G(_t,pt),jt.prototype.setAppearance=function(t){if(!("createAppearanceStream"in t)||!("getCA"in t))throw new Error("Couldn't assign Appearance to RadioButton. Appearance was Invalid!");for(var e in this.Kids)if(this.Kids.hasOwnProperty(e)){var n=this.Kids[e];n.appearanceStreamContent=t.createAppearanceStream(n.optionName),n.caption=t.getCA()}},jt.prototype.createOption=function(t){var e=new _t;return e.Parent=this,e.optionName=t,this.Kids.push(e),St.call(this.scope,e),e};var xt=function(){yt.call(this),this.fontName="zapfdingbats",this.caption="3",this.appearanceState="On",this.value="On",this.textAlign="center",this.appearanceStreamContent=Lt.CheckBox.createAppearanceStream()};G(xt,yt);var Nt=function(){pt.call(this),this.FT="/Tx",Object.defineProperty(this,"multiline",{enumerable:!0,configurable:!0,get:function(){return Boolean(Z(this.Ff,13))},set:function(t){!0===Boolean(t)?this.Ff=Q(this.Ff,13):this.Ff=tt(this.Ff,13)}}),Object.defineProperty(this,"fileSelect",{enumerable:!0,configurable:!0,get:function(){return Boolean(Z(this.Ff,21))},set:function(t){!0===Boolean(t)?this.Ff=Q(this.Ff,21):this.Ff=tt(this.Ff,21)}}),Object.defineProperty(this,"doNotSpellCheck",{enumerable:!0,configurable:!0,get:function(){return Boolean(Z(this.Ff,23))},set:function(t){!0===Boolean(t)?this.Ff=Q(this.Ff,23):this.Ff=tt(this.Ff,23)}}),Object.defineProperty(this,"doNotScroll",{enumerable:!0,configurable:!0,get:function(){return Boolean(Z(this.Ff,24))},set:function(t){!0===Boolean(t)?this.Ff=Q(this.Ff,24):this.Ff=tt(this.Ff,24)}}),Object.defineProperty(this,"comb",{enumerable:!0,configurable:!0,get:function(){return Boolean(Z(this.Ff,25))},set:function(t){!0===Boolean(t)?this.Ff=Q(this.Ff,25):this.Ff=tt(this.Ff,25)}}),Object.defineProperty(this,"richText",{enumerable:!0,configurable:!0,get:function(){return Boolean(Z(this.Ff,26))},set:function(t){!0===Boolean(t)?this.Ff=Q(this.Ff,26):this.Ff=tt(this.Ff,26)}});var t=null;Object.defineProperty(this,"MaxLen",{enumerable:!0,configurable:!1,get:function(){return t},set:function(e){t=e}}),Object.defineProperty(this,"maxLength",{enumerable:!0,configurable:!0,get:function(){return t},set:function(e){Number.isInteger(e)&&(t=e)}}),Object.defineProperty(this,"hasAppearanceStream",{enumerable:!0,configurable:!0,get:function(){return this.V||this.DV}})};G(Nt,pt);var At=function(){Nt.call(this),Object.defineProperty(this,"password",{enumerable:!0,configurable:!0,get:function(){return Boolean(Z(this.Ff,14))},set:function(t){!0===Boolean(t)?this.Ff=Q(this.Ff,14):this.Ff=tt(this.Ff,14)}}),this.password=!0};G(At,Nt);var Lt={CheckBox:{createAppearanceStream:function(){return{N:{On:Lt.CheckBox.YesNormal},D:{On:Lt.CheckBox.YesPushDown,Off:Lt.CheckBox.OffPushDown}}},YesPushDown:function(t){var e=J(t);e.scope=t.scope;var n=[],r=t.scope.internal.getFont(t.fontName,t.fontStyle).id,i=t.scope.__private__.encodeColorString(t.color),o=rt(t,t.caption);return n.push("0.749023 g"),n.push("0 0 "+V(Lt.internal.getWidth(t))+" "+V(Lt.internal.getHeight(t))+" re"),n.push("f"),n.push("BMC"),n.push("q"),n.push("0 0 1 rg"),n.push("/"+r+" "+V(o.fontSize)+" Tf "+i),n.push("BT"),n.push(o.text),n.push("ET"),n.push("Q"),n.push("EMC"),e.stream=n.join("\n"),e},YesNormal:function(t){var e=J(t);e.scope=t.scope;var n=t.scope.internal.getFont(t.fontName,t.fontStyle).id,r=t.scope.__private__.encodeColorString(t.color),i=[],o=Lt.internal.getHeight(t),s=Lt.internal.getWidth(t),a=rt(t,t.caption);return i.push("1 g"),i.push("0 0 "+V(s)+" "+V(o)+" re"),i.push("f"),i.push("q"),i.push("0 0 1 rg"),i.push("0 0 "+V(s-1)+" "+V(o-1)+" re"),i.push("W"),i.push("n"),i.push("0 g"),i.push("BT"),i.push("/"+n+" "+V(a.fontSize)+" Tf "+r),i.push(a.text),i.push("ET"),i.push("Q"),e.stream=i.join("\n"),e},OffPushDown:function(t){var e=J(t);e.scope=t.scope;var n=[];return n.push("0.749023 g"),n.push("0 0 "+V(Lt.internal.getWidth(t))+" "+V(Lt.internal.getHeight(t))+" re"),n.push("f"),e.stream=n.join("\n"),e}},RadioButton:{Circle:{createAppearanceStream:function(t){var e={D:{Off:Lt.RadioButton.Circle.OffPushDown},N:{}};return e.N[t]=Lt.RadioButton.Circle.YesNormal,e.D[t]=Lt.RadioButton.Circle.YesPushDown,e},getCA:function(){return"l"},YesNormal:function(t){var e=J(t);e.scope=t.scope;var n=[],r=Lt.internal.getWidth(t)<=Lt.internal.getHeight(t)?Lt.internal.getWidth(t)/4:Lt.internal.getHeight(t)/4;r=Number((.9*r).toFixed(5));var i=Lt.internal.Bezier_C,o=Number((r*i).toFixed(5));return n.push("q"),n.push("1 0 0 1 "+W(Lt.internal.getWidth(t)/2)+" "+W(Lt.internal.getHeight(t)/2)+" cm"),n.push(r+" 0 m"),n.push(r+" "+o+" "+o+" "+r+" 0 "+r+" c"),n.push("-"+o+" "+r+" -"+r+" "+o+" -"+r+" 0 c"),n.push("-"+r+" -"+o+" -"+o+" -"+r+" 0 -"+r+" c"),n.push(o+" -"+r+" "+r+" -"+o+" "+r+" 0 c"),n.push("f"),n.push("Q"),e.stream=n.join("\n"),e},YesPushDown:function(t){var e=J(t);e.scope=t.scope;var n=[],r=Lt.internal.getWidth(t)<=Lt.internal.getHeight(t)?Lt.internal.getWidth(t)/4:Lt.internal.getHeight(t)/4;r=Number((.9*r).toFixed(5));var i=Number((2*r).toFixed(5)),o=Number((i*Lt.internal.Bezier_C).toFixed(5)),s=Number((r*Lt.internal.Bezier_C).toFixed(5));return n.push("0.749023 g"),n.push("q"),n.push("1 0 0 1 "+W(Lt.internal.getWidth(t)/2)+" "+W(Lt.internal.getHeight(t)/2)+" cm"),n.push(i+" 0 m"),n.push(i+" "+o+" "+o+" "+i+" 0 "+i+" c"),n.push("-"+o+" "+i+" -"+i+" "+o+" -"+i+" 0 c"),n.push("-"+i+" -"+o+" -"+o+" -"+i+" 0 -"+i+" c"),n.push(o+" -"+i+" "+i+" -"+o+" "+i+" 0 c"),n.push("f"),n.push("Q"),n.push("0 g"),n.push("q"),n.push("1 0 0 1 "+W(Lt.internal.getWidth(t)/2)+" "+W(Lt.internal.getHeight(t)/2)+" cm"),n.push(r+" 0 m"),n.push(r+" "+s+" "+s+" "+r+" 0 "+r+" c"),n.push("-"+s+" "+r+" -"+r+" "+s+" -"+r+" 0 c"),n.push("-"+r+" -"+s+" -"+s+" -"+r+" 0 -"+r+" c"),n.push(s+" -"+r+" "+r+" -"+s+" "+r+" 0 c"),n.push("f"),n.push("Q"),e.stream=n.join("\n"),e},OffPushDown:function(t){var e=J(t);e.scope=t.scope;var n=[],r=Lt.internal.getWidth(t)<=Lt.internal.getHeight(t)?Lt.internal.getWidth(t)/4:Lt.internal.getHeight(t)/4;r=Number((.9*r).toFixed(5));var i=Number((2*r).toFixed(5)),o=Number((i*Lt.internal.Bezier_C).toFixed(5));return n.push("0.749023 g"),n.push("q"),n.push("1 0 0 1 "+W(Lt.internal.getWidth(t)/2)+" "+W(Lt.internal.getHeight(t)/2)+" cm"),n.push(i+" 0 m"),n.push(i+" "+o+" "+o+" "+i+" 0 "+i+" c"),n.push("-"+o+" "+i+" -"+i+" "+o+" -"+i+" 0 c"),n.push("-"+i+" -"+o+" -"+o+" -"+i+" 0 -"+i+" c"),n.push(o+" -"+i+" "+i+" -"+o+" "+i+" 0 c"),n.push("f"),n.push("Q"),e.stream=n.join("\n"),e}},Cross:{createAppearanceStream:function(t){var e={D:{Off:Lt.RadioButton.Cross.OffPushDown},N:{}};return e.N[t]=Lt.RadioButton.Cross.YesNormal,e.D[t]=Lt.RadioButton.Cross.YesPushDown,e},getCA:function(){return"8"},YesNormal:function(t){var e=J(t);e.scope=t.scope;var n=[],r=Lt.internal.calculateCross(t);return n.push("q"),n.push("1 1 "+V(Lt.internal.getWidth(t)-2)+" "+V(Lt.internal.getHeight(t)-2)+" re"),n.push("W"),n.push("n"),n.push(V(r.x1.x)+" "+V(r.x1.y)+" m"),n.push(V(r.x2.x)+" "+V(r.x2.y)+" l"),n.push(V(r.x4.x)+" "+V(r.x4.y)+" m"),n.push(V(r.x3.x)+" "+V(r.x3.y)+" l"),n.push("s"),n.push("Q"),e.stream=n.join("\n"),e},YesPushDown:function(t){var e=J(t);e.scope=t.scope;var n=Lt.internal.calculateCross(t),r=[];return r.push("0.749023 g"),r.push("0 0 "+V(Lt.internal.getWidth(t))+" "+V(Lt.internal.getHeight(t))+" re"),r.push("f"),r.push("q"),r.push("1 1 "+V(Lt.internal.getWidth(t)-2)+" "+V(Lt.internal.getHeight(t)-2)+" re"),r.push("W"),r.push("n"),r.push(V(n.x1.x)+" "+V(n.x1.y)+" m"),r.push(V(n.x2.x)+" "+V(n.x2.y)+" l"),r.push(V(n.x4.x)+" "+V(n.x4.y)+" m"),r.push(V(n.x3.x)+" "+V(n.x3.y)+" l"),r.push("s"),r.push("Q"),e.stream=r.join("\n"),e},OffPushDown:function(t){var e=J(t);e.scope=t.scope;var n=[];return n.push("0.749023 g"),n.push("0 0 "+V(Lt.internal.getWidth(t))+" "+V(Lt.internal.getHeight(t))+" re"),n.push("f"),e.stream=n.join("\n"),e}}},createDefaultAppearanceStream:function(t){var e=t.scope.internal.getFont(t.fontName,t.fontStyle).id,n=t.scope.__private__.encodeColorString(t.color);return"/"+e+" "+t.fontSize+" Tf "+n}};Lt.internal={Bezier_C:.551915024494,calculateCross:function(t){var e=Lt.internal.getWidth(t),n=Lt.internal.getHeight(t),r=Math.min(e,n);return{x1:{x:(e-r)/2,y:(n-r)/2+r},x2:{x:(e-r)/2+r,y:(n-r)/2},x3:{x:(e-r)/2,y:(n-r)/2},x4:{x:(e-r)/2+r,y:(n-r)/2+r}}}},Lt.internal.getWidth=function(t){var e=0;return"object"===i()(t)&&(e=Y(t.Rect[2])),e},Lt.internal.getHeight=function(t){var e=0;return"object"===i()(t)&&(e=Y(t.Rect[3])),e};var St=R.addField=function(t){if(ct(this,t),!(t instanceof pt))throw new Error("Invalid argument passed to jsPDF.addField.");var e;return(e=t).scope.internal.acroformPlugin.printedOut&&(e.scope.internal.acroformPlugin.printedOut=!1,e.scope.internal.acroformPlugin.acroFormDictionaryRoot=null),e.scope.internal.acroformPlugin.acroFormDictionaryRoot.Fields.push(e),t.page=t.scope.internal.getCurrentPageInfo().pageNumber,this};R.AcroFormChoiceField=mt,R.AcroFormListBox=gt,R.AcroFormComboBox=vt,R.AcroFormEditBox=bt,R.AcroFormButton=yt,R.AcroFormPushButton=wt,R.AcroFormRadioButton=jt,R.AcroFormCheckBox=xt,R.AcroFormTextField=Nt,R.AcroFormPasswordField=At,R.AcroFormAppearance=Lt,R.AcroForm={ChoiceField:mt,ListBox:gt,ComboBox:vt,EditBox:bt,Button:yt,PushButton:wt,RadioButton:jt,CheckBox:xt,TextField:Nt,PasswordField:At,Appearance:Lt},D.AcroForm={ChoiceField:mt,ListBox:gt,ComboBox:vt,EditBox:bt,Button:yt,PushButton:wt,RadioButton:jt,CheckBox:xt,TextField:Nt,PasswordField:At,Appearance:Lt};var Pt=D.AcroForm;function kt(t){return t.reduce((function(t,e,n){return t[e]=n,t}),{})}!function(t){t.__addimage__={};var e="UNKNOWN",n={PNG:[[137,80,78,71]],TIFF:[[77,77,0,42],[73,73,42,0]],JPEG:[[255,216,255,224,void 0,void 0,74,70,73,70,0],[255,216,255,225,void 0,void 0,69,120,105,102,0,0],[255,216,255,219],[255,216,255,238]],JPEG2000:[[0,0,0,12,106,80,32,32]],GIF87a:[[71,73,70,56,55,97]],GIF89a:[[71,73,70,56,57,97]],WEBP:[[82,73,70,70,void 0,void 0,void 0,void 0,87,69,66,80]],BMP:[[66,77],[66,65],[67,73],[67,80],[73,67],[80,84]]},r=t.__addimage__.getImageFileTypeByImageData=function(t,r){var i,o,s,a,c,u=e;if("RGBA"===(r=r||e)||void 0!==t.data&&t.data instanceof Uint8ClampedArray&&"height"in t&&"width"in t)return"RGBA";if(N(t))for(c in n)for(s=n[c],i=0;i<s.length;i+=1){for(a=!0,o=0;o<s[i].length;o+=1)if(void 0!==s[i][o]&&s[i][o]!==t[o]){a=!1;break}if(!0===a){u=c;break}}else for(c in n)for(s=n[c],i=0;i<s.length;i+=1){for(a=!0,o=0;o<s[i].length;o+=1)if(void 0!==s[i][o]&&s[i][o]!==t.charCodeAt(o)){a=!1;break}if(!0===a){u=c;break}}return u===e&&r!==e&&(u=r),u},o=function t(e){for(var n=this.internal.write,r=this.internal.putStream,i=(0,this.internal.getFilters)();-1!==i.indexOf("FlateEncode");)i.splice(i.indexOf("FlateEncode"),1);e.objectId=this.internal.newObject();var o=[];if(o.push({key:"Type",value:"/XObject"}),o.push({key:"Subtype",value:"/Image"}),o.push({key:"Width",value:e.width}),o.push({key:"Height",value:e.height}),e.colorSpace===b.INDEXED?o.push({key:"ColorSpace",value:"[/Indexed /DeviceRGB "+(e.palette.length/3-1)+" "+("sMask"in e&&void 0!==e.sMask?e.objectId+2:e.objectId+1)+" 0 R]"}):(o.push({key:"ColorSpace",value:"/"+e.colorSpace}),e.colorSpace===b.DEVICE_CMYK&&o.push({key:"Decode",value:"[1 0 1 0 1 0 1 0]"})),o.push({key:"BitsPerComponent",value:e.bitsPerComponent}),"decodeParameters"in e&&void 0!==e.decodeParameters&&o.push({key:"DecodeParms",value:"<<"+e.decodeParameters+">>"}),"transparency"in e&&Array.isArray(e.transparency)){for(var s="",a=0,c=e.transparency.length;a<c;a++)s+=e.transparency[a]+" "+e.transparency[a]+" ";o.push({key:"Mask",value:"["+s+"]"})}void 0!==e.sMask&&o.push({key:"SMask",value:e.objectId+1+" 0 R"});var u=void 0!==e.filter?["/"+e.filter]:void 0;if(r({data:e.data,additionalKeyValues:o,alreadyAppliedFilters:u,objectId:e.objectId}),n("endobj"),"sMask"in e&&void 0!==e.sMask){var l="/Predictor "+e.predictor+" /Colors 1 /BitsPerComponent "+e.bitsPerComponent+" /Columns "+e.width,h={width:e.width,height:e.height,colorSpace:"DeviceGray",bitsPerComponent:e.bitsPerComponent,decodeParameters:l,data:e.sMask};"filter"in e&&(h.filter=e.filter),t.call(this,h)}if(e.colorSpace===b.INDEXED){var f=this.internal.newObject();r({data:L(new Uint8Array(e.palette)),objectId:f}),n("endobj")}},s=function(){var t=this.internal.collections.addImage_images;for(var e in t)o.call(this,t[e])},a=function(){var t,e=this.internal.collections.addImage_images,n=this.internal.write;for(var r in e)n("/I"+(t=e[r]).index,t.objectId,"0","R")},c=function(){this.internal.collections.addImage_images||(this.internal.collections.addImage_images={},this.internal.events.subscribe("putResources",s),this.internal.events.subscribe("putXobjectDict",a))},u=function(){var t=this.internal.collections.addImage_images;return c.call(this),t},l=function(){return Object.keys(this.internal.collections.addImage_images).length},h=function(e){return"function"==typeof t["process"+e.toUpperCase()]},d=function(t){return"object"===i()(t)&&1===t.nodeType},p=function(e,n){if("IMG"===e.nodeName&&e.hasAttribute("src")){var r=""+e.getAttribute("src");if(0===r.indexOf("data:image/"))return f(unescape(r).split("base64,").pop());var i=t.loadFile(r,!0);if(void 0!==i)return i}if("CANVAS"===e.nodeName){if(0===e.width||0===e.height)throw new Error("Given canvas must have data. Canvas width: "+e.width+", height: "+e.height);var o;switch(n){case"PNG":o="image/png";break;case"WEBP":o="image/webp";break;case"JPEG":case"JPG":default:o="image/jpeg"}return f(e.toDataURL(o,1).split("base64,").pop())}},m=function(t){var e=this.internal.collections.addImage_images;if(e)for(var n in e)if(t===e[n].alias)return e[n]},g=function(t,e,n){return t||e||(t=-96,e=-96),t<0&&(t=-1*n.width*72/t/this.internal.scaleFactor),e<0&&(e=-1*n.height*72/e/this.internal.scaleFactor),0===t&&(t=e*n.width/n.height),0===e&&(e=t*n.height/n.width),[t,e]},v=function(t,e,n,r,i,o){var s=g.call(this,n,r,i),a=this.internal.getCoordinateString,c=this.internal.getVerticalCoordinateString,l=u.call(this);if(n=s[0],r=s[1],l[i.index]=i,o){o*=Math.PI/180;var h=Math.cos(o),f=Math.sin(o),d=function(t){return t.toFixed(4)},p=[d(h),d(f),d(-1*f),d(h),0,0,"cm"]}this.internal.write("q"),o?(this.internal.write([1,"0","0",1,a(t),c(e+r),"cm"].join(" ")),this.internal.write(p.join(" ")),this.internal.write([a(n),"0","0",a(r),"0","0","cm"].join(" "))):this.internal.write([a(n),"0","0",a(r),a(t),c(e+r),"cm"].join(" ")),this.isAdvancedAPI()&&this.internal.write([1,0,0,-1,0,0,"cm"].join(" ")),this.internal.write("/I"+i.index+" Do"),this.internal.write("Q")},b=t.color_spaces={DEVICE_RGB:"DeviceRGB",DEVICE_GRAY:"DeviceGray",DEVICE_CMYK:"DeviceCMYK",CAL_GREY:"CalGray",CAL_RGB:"CalRGB",LAB:"Lab",ICC_BASED:"ICCBased",INDEXED:"Indexed",PATTERN:"Pattern",SEPARATION:"Separation",DEVICE_N:"DeviceN"};t.decode={DCT_DECODE:"DCTDecode",FLATE_DECODE:"FlateDecode",LZW_DECODE:"LZWDecode",JPX_DECODE:"JPXDecode",JBIG2_DECODE:"JBIG2Decode",ASCII85_DECODE:"ASCII85Decode",ASCII_HEX_DECODE:"ASCIIHexDecode",RUN_LENGTH_DECODE:"RunLengthDecode",CCITT_FAX_DECODE:"CCITTFaxDecode"};var y=t.image_compression={NONE:"NONE",FAST:"FAST",MEDIUM:"MEDIUM",SLOW:"SLOW"},w=t.__addimage__.sHashCode=function(t){var e,n,r=0;if("string"==typeof t)for(n=t.length,e=0;e<n;e++)r=(r<<5)-r+t.charCodeAt(e),r|=0;else if(N(t))for(n=t.byteLength/2,e=0;e<n;e++)r=(r<<5)-r+t[e],r|=0;return r},j=t.__addimage__.validateStringAsBase64=function(t){(t=t||"").toString().trim();var e=!0;return 0===t.length&&(e=!1),t.length%4!=0&&(e=!1),!1===/^[A-Za-z0-9+/]+$/.test(t.substr(0,t.length-2))&&(e=!1),!1===/^[A-Za-z0-9/][A-Za-z0-9+/]|[A-Za-z0-9+/]=|==$/.test(t.substr(-2))&&(e=!1),e},_=t.__addimage__.extractImageFromDataUrl=function(t){var e=(t=t||"").split("base64,"),n=null;if(2===e.length){var r=/^data:(\w*\/\w*);*(charset=(?!charset=)[\w=-]*)*;*$/.exec(e[0]);Array.isArray(r)&&(n={mimeType:r[1],charset:r[2],data:e[1]})}return n},x=t.__addimage__.supportsArrayBuffer=function(){return"undefined"!=typeof ArrayBuffer&&"undefined"!=typeof Uint8Array};t.__addimage__.isArrayBuffer=function(t){return x()&&t instanceof ArrayBuffer};var N=t.__addimage__.isArrayBufferView=function(t){return x()&&"undefined"!=typeof Uint32Array&&(t instanceof Int8Array||t instanceof Uint8Array||"undefined"!=typeof Uint8ClampedArray&&t instanceof Uint8ClampedArray||t instanceof Int16Array||t instanceof Uint16Array||t instanceof Int32Array||t instanceof Uint32Array||t instanceof Float32Array||t instanceof Float64Array)},A=t.__addimage__.binaryStringToUint8Array=function(t){for(var e=t.length,n=new Uint8Array(e),r=0;r<e;r++)n[r]=t.charCodeAt(r);return n},L=t.__addimage__.arrayBufferToBinaryString=function(t){for(var e="",n=N(t)?t:new Uint8Array(t),r=0;r<n.length;r+=8192)e+=String.fromCharCode.apply(null,n.subarray(r,r+8192));return e};t.addImage=function(){var t,n,r,o,s,a,u,l,h;if("number"==typeof arguments[1]?(n=e,r=arguments[1],o=arguments[2],s=arguments[3],a=arguments[4],u=arguments[5],l=arguments[6],h=arguments[7]):(n=arguments[1],r=arguments[2],o=arguments[3],s=arguments[4],a=arguments[5],u=arguments[6],l=arguments[7],h=arguments[8]),"object"===i()(t=arguments[0])&&!d(t)&&"imageData"in t){var f=t;t=f.imageData,n=f.format||n||e,r=f.x||r||0,o=f.y||o||0,s=f.w||f.width||s,a=f.h||f.height||a,u=f.alias||u,l=f.compression||l,h=f.rotation||f.angle||h}var p=this.internal.getFilters();if(void 0===l&&-1!==p.indexOf("FlateEncode")&&(l="SLOW"),isNaN(r)||isNaN(o))throw new Error("Invalid coordinates passed to jsPDF.addImage");c.call(this);var m=S.call(this,t,n,u,l);return v.call(this,r,o,s,a,m,h),this};var S=function(n,i,o,s){var a,c,u;if("string"==typeof n&&r(n)===e){n=unescape(n);var f=P(n,!1);(""!==f||void 0!==(f=t.loadFile(n,!0)))&&(n=f)}if(d(n)&&(n=p(n,i)),i=r(n,i),!h(i))throw new Error("addImage does not support files of type '"+i+"', please ensure that a plugin for '"+i+"' support is added.");if((null==(u=o)||0===u.length)&&(o=function(t){return"string"==typeof t||N(t)?w(t):N(t.data)?w(t.data):null}(n)),(a=m.call(this,o))||(x()&&(n instanceof Uint8Array||"RGBA"===i||(c=n,n=A(n))),a=this["process"+i.toUpperCase()](n,l.call(this),o,function(e){return e&&"string"==typeof e&&(e=e.toUpperCase()),e in t.image_compression?e:y.NONE}(s),c)),!a)throw new Error("An unknown error occurred whilst processing the image.");return a},P=t.__addimage__.convertBase64ToBinaryString=function(t,e){var n;e="boolean"!=typeof e||e;var r,i="";if("string"==typeof t){r=null!==(n=_(t))?n.data:t;try{i=f(r)}catch(t){if(e)throw j(r)?new Error("atob-Error in jsPDF.convertBase64ToBinaryString "+t.message):new Error("Supplied Data is not a valid base64-String jsPDF.convertBase64ToBinaryString ")}}return i};t.getImageProperties=function(n){var i,o,s="";if(d(n)&&(n=p(n)),"string"==typeof n&&r(n)===e&&(""===(s=P(n,!1))&&(s=t.loadFile(n)||""),n=s),o=r(n),!h(o))throw new Error("addImage does not support files of type '"+o+"', please ensure that a plugin for '"+o+"' support is added.");if(!x()||n instanceof Uint8Array||(n=A(n)),!(i=this["process"+o.toUpperCase()](n)))throw new Error("An unknown error occurred whilst processing the image");return i.fileType=o,i}}(D.API),
/**
 * @license
 * Copyright (c) 2014 Steven Spungin (TwelveTone LLC)  <EMAIL>
 *
 * Licensed under the MIT License.
 * http://opensource.org/licenses/mit-license
 */
function(t){var e=function(t){if(void 0!==t&&""!=t)return!0};D.API.events.push(["addPage",function(t){this.internal.getPageInfo(t.pageNumber).pageContext.annotations=[]}]),t.events.push(["putPage",function(t){for(var n,r,i,o=this.internal.getCoordinateString,s=this.internal.getVerticalCoordinateString,a=this.internal.getPageInfoByObjId(t.objId),c=t.pageContext.annotations,u=!1,l=0;l<c.length&&!u;l++)switch((n=c[l]).type){case"link":(e(n.options.url)||e(n.options.pageNumber))&&(u=!0);break;case"reference":case"text":case"freetext":u=!0}if(0!=u){this.internal.write("/Annots [");for(var h=0;h<c.length;h++){n=c[h];var f=this.internal.pdfEscape,d=this.internal.getEncryptor(t.objId);switch(n.type){case"reference":this.internal.write(" "+n.object.objId+" 0 R ");break;case"text":var p=this.internal.newAdditionalObject(),m=this.internal.newAdditionalObject(),g=this.internal.getEncryptor(p.objId),v=n.title||"Note";i="<</Type /Annot /Subtype /Text "+(r="/Rect ["+o(n.bounds.x)+" "+s(n.bounds.y+n.bounds.h)+" "+o(n.bounds.x+n.bounds.w)+" "+s(n.bounds.y)+"] ")+"/Contents ("+f(g(n.contents))+")",i+=" /Popup "+m.objId+" 0 R",i+=" /P "+a.objId+" 0 R",i+=" /T ("+f(g(v))+") >>",p.content=i;var b=p.objId+" 0 R";i="<</Type /Annot /Subtype /Popup "+(r="/Rect ["+o(n.bounds.x+30)+" "+s(n.bounds.y+n.bounds.h)+" "+o(n.bounds.x+n.bounds.w+30)+" "+s(n.bounds.y)+"] ")+" /Parent "+b,n.open&&(i+=" /Open true"),i+=" >>",m.content=i,this.internal.write(p.objId,"0 R",m.objId,"0 R");break;case"freetext":r="/Rect ["+o(n.bounds.x)+" "+s(n.bounds.y)+" "+o(n.bounds.x+n.bounds.w)+" "+s(n.bounds.y+n.bounds.h)+"] ";var y=n.color||"#000000";i="<</Type /Annot /Subtype /FreeText "+r+"/Contents ("+f(d(n.contents))+")",i+=" /DS(font: Helvetica,sans-serif 12.0pt; text-align:left; color:#"+y+")",i+=" /Border [0 0 0]",i+=" >>",this.internal.write(i);break;case"link":if(n.options.name){var w=this.annotations._nameMap[n.options.name];n.options.pageNumber=w.page,n.options.top=w.y}else n.options.top||(n.options.top=0);if(r="/Rect ["+n.finalBounds.x+" "+n.finalBounds.y+" "+n.finalBounds.w+" "+n.finalBounds.h+"] ",i="",n.options.url)i="<</Type /Annot /Subtype /Link "+r+"/Border [0 0 0] /A <</S /URI /URI ("+f(d(n.options.url))+") >>";else if(n.options.pageNumber)switch(i="<</Type /Annot /Subtype /Link "+r+"/Border [0 0 0] /Dest ["+this.internal.getPageInfo(n.options.pageNumber).objId+" 0 R",n.options.magFactor=n.options.magFactor||"XYZ",n.options.magFactor){case"Fit":i+=" /Fit]";break;case"FitH":i+=" /FitH "+n.options.top+"]";break;case"FitV":n.options.left=n.options.left||0,i+=" /FitV "+n.options.left+"]";break;case"XYZ":default:var j=s(n.options.top);n.options.left=n.options.left||0,void 0===n.options.zoom&&(n.options.zoom=0),i+=" /XYZ "+n.options.left+" "+j+" "+n.options.zoom+"]"}""!=i&&(i+=" >>",this.internal.write(i))}}this.internal.write("]")}}]),t.createAnnotation=function(t){var e=this.internal.getCurrentPageInfo();switch(t.type){case"link":this.link(t.bounds.x,t.bounds.y,t.bounds.w,t.bounds.h,t);break;case"text":case"freetext":e.pageContext.annotations.push(t)}},t.link=function(t,e,n,r,i){var o=this.internal.getCurrentPageInfo(),s=this.internal.getCoordinateString,a=this.internal.getVerticalCoordinateString;o.pageContext.annotations.push({finalBounds:{x:s(t),y:a(e),w:s(t+n),h:a(e+r)},options:i,type:"link"})},t.textWithLink=function(t,e,n,r){var i,o,s=this.getTextWidth(t),a=this.internal.getLineHeight()/this.internal.scaleFactor;if(void 0!==r.maxWidth){o=r.maxWidth;var c=this.splitTextToSize(t,o).length;i=Math.ceil(a*c)}else o=s,i=a;return this.text(t,e,n,r),n+=.2*a,"center"===r.align&&(e-=s/2),"right"===r.align&&(e-=s),this.link(e,n-a,o,i,r),s},t.getTextWidth=function(t){var e=this.internal.getFontSize();return this.getStringUnitWidth(t)*e/this.internal.scaleFactor}}(D.API),
/**
 * @license
 * Copyright (c) 2017 Aras Abbasi
 *
 * Licensed under the MIT License.
 * http://opensource.org/licenses/mit-license
 */
function(t){var e={1569:[65152],1570:[65153,65154],1571:[65155,65156],1572:[65157,65158],1573:[65159,65160],1574:[65161,65162,65163,65164],1575:[65165,65166],1576:[65167,65168,65169,65170],1577:[65171,65172],1578:[65173,65174,65175,65176],1579:[65177,65178,65179,65180],1580:[65181,65182,65183,65184],1581:[65185,65186,65187,65188],1582:[65189,65190,65191,65192],1583:[65193,65194],1584:[65195,65196],1585:[65197,65198],1586:[65199,65200],1587:[65201,65202,65203,65204],1588:[65205,65206,65207,65208],1589:[65209,65210,65211,65212],1590:[65213,65214,65215,65216],1591:[65217,65218,65219,65220],1592:[65221,65222,65223,65224],1593:[65225,65226,65227,65228],1594:[65229,65230,65231,65232],1601:[65233,65234,65235,65236],1602:[65237,65238,65239,65240],1603:[65241,65242,65243,65244],1604:[65245,65246,65247,65248],1605:[65249,65250,65251,65252],1606:[65253,65254,65255,65256],1607:[65257,65258,65259,65260],1608:[65261,65262],1609:[65263,65264,64488,64489],1610:[65265,65266,65267,65268],1649:[64336,64337],1655:[64477],1657:[64358,64359,64360,64361],1658:[64350,64351,64352,64353],1659:[64338,64339,64340,64341],1662:[64342,64343,64344,64345],1663:[64354,64355,64356,64357],1664:[64346,64347,64348,64349],1667:[64374,64375,64376,64377],1668:[64370,64371,64372,64373],1670:[64378,64379,64380,64381],1671:[64382,64383,64384,64385],1672:[64392,64393],1676:[64388,64389],1677:[64386,64387],1678:[64390,64391],1681:[64396,64397],1688:[64394,64395],1700:[64362,64363,64364,64365],1702:[64366,64367,64368,64369],1705:[64398,64399,64400,64401],1709:[64467,64468,64469,64470],1711:[64402,64403,64404,64405],1713:[64410,64411,64412,64413],1715:[64406,64407,64408,64409],1722:[64414,64415],1723:[64416,64417,64418,64419],1726:[64426,64427,64428,64429],1728:[64420,64421],1729:[64422,64423,64424,64425],1733:[64480,64481],1734:[64473,64474],1735:[64471,64472],1736:[64475,64476],1737:[64482,64483],1739:[64478,64479],1740:[64508,64509,64510,64511],1744:[64484,64485,64486,64487],1746:[64430,64431],1747:[64432,64433]},n={65247:{65154:65269,65156:65271,65160:65273,65166:65275},65248:{65154:65270,65156:65272,65160:65274,65166:65276},65165:{65247:{65248:{65258:65010}}},1617:{1612:64606,1613:64607,1614:64608,1615:64609,1616:64610}},r={1612:64606,1613:64607,1614:64608,1615:64609,1616:64610},i=[1570,1571,1573,1575];t.__arabicParser__={};var o=t.__arabicParser__.isInArabicSubstitutionA=function(t){return void 0!==e[t.charCodeAt(0)]},s=t.__arabicParser__.isArabicLetter=function(t){return"string"==typeof t&&/^[\u0600-\u06FF\u0750-\u077F\u08A0-\u08FF\uFB50-\uFDFF\uFE70-\uFEFF]+$/.test(t)},a=t.__arabicParser__.isArabicEndLetter=function(t){return s(t)&&o(t)&&e[t.charCodeAt(0)].length<=2},c=t.__arabicParser__.isArabicAlfLetter=function(t){return s(t)&&i.indexOf(t.charCodeAt(0))>=0};t.__arabicParser__.arabicLetterHasIsolatedForm=function(t){return s(t)&&o(t)&&e[t.charCodeAt(0)].length>=1};var u=t.__arabicParser__.arabicLetterHasFinalForm=function(t){return s(t)&&o(t)&&e[t.charCodeAt(0)].length>=2};t.__arabicParser__.arabicLetterHasInitialForm=function(t){return s(t)&&o(t)&&e[t.charCodeAt(0)].length>=3};var l=t.__arabicParser__.arabicLetterHasMedialForm=function(t){return s(t)&&o(t)&&4==e[t.charCodeAt(0)].length},h=t.__arabicParser__.resolveLigatures=function(t){var e=0,r=n,i="",o=0;for(e=0;e<t.length;e+=1)void 0!==r[t.charCodeAt(e)]?(o++,"number"==typeof(r=r[t.charCodeAt(e)])&&(i+=String.fromCharCode(r),r=n,o=0),e===t.length-1&&(r=n,i+=t.charAt(e-(o-1)),e-=o-1,o=0)):(r=n,i+=t.charAt(e-o),e-=o,o=0);return i};t.__arabicParser__.isArabicDiacritic=function(t){return void 0!==t&&void 0!==r[t.charCodeAt(0)]};var f=t.__arabicParser__.getCorrectForm=function(t,e,n){return s(t)?!1===o(t)?-1:!u(t)||!s(e)&&!s(n)||!s(n)&&a(e)||a(t)&&!s(e)||a(t)&&c(e)||a(t)&&a(e)?0:l(t)&&s(e)&&!a(e)&&s(n)&&u(n)?3:a(t)||!s(n)?1:2:-1},d=function(t){var n=0,r=0,i=0,o="",a="",c="",u=(t=t||"").split("\\s+"),l=[];for(n=0;n<u.length;n+=1){for(l.push(""),r=0;r<u[n].length;r+=1)o=u[n][r],a=u[n][r-1],c=u[n][r+1],s(o)?(i=f(o,a,c),l[n]+=-1!==i?String.fromCharCode(e[o.charCodeAt(0)][i]):o):l[n]+=o;l[n]=h(l[n])}return l.join(" ")},p=t.__arabicParser__.processArabic=t.processArabic=function(){var t,e="string"==typeof arguments[0]?arguments[0]:arguments[0].text,n=[];if(Array.isArray(e)){var r=0;for(n=[],r=0;r<e.length;r+=1)Array.isArray(e[r])?n.push([d(e[r][0]),e[r][1],e[r][2]]):n.push([d(e[r])]);t=n}else t=d(e);return"string"==typeof arguments[0]?t:(arguments[0].text=t,arguments[0])};t.events.push(["preProcessText",p])}(D.API),D.API.autoPrint=function(t){var e;switch((t=t||{}).variant=t.variant||"non-conform",t.variant){case"javascript":this.addJS("print({});");break;case"non-conform":default:this.internal.events.subscribe("postPutResources",(function(){e=this.internal.newObject(),this.internal.out("<<"),this.internal.out("/S /Named"),this.internal.out("/Type /Action"),this.internal.out("/N /Print"),this.internal.out(">>"),this.internal.out("endobj")})),this.internal.events.subscribe("putCatalog",(function(){this.internal.out("/OpenAction "+e+" 0 R")}))}return this},
/**
 * @license
 * Copyright (c) 2014 Steven Spungin (TwelveTone LLC)  <EMAIL>
 *
 * Licensed under the MIT License.
 * http://opensource.org/licenses/mit-license
 */
function(t){var e=function(){var t=void 0;Object.defineProperty(this,"pdf",{get:function(){return t},set:function(e){t=e}});var e=150;Object.defineProperty(this,"width",{get:function(){return e},set:function(t){e=isNaN(t)||!1===Number.isInteger(t)||t<0?150:t,this.getContext("2d").pageWrapXEnabled&&(this.getContext("2d").pageWrapX=e+1)}});var n=300;Object.defineProperty(this,"height",{get:function(){return n},set:function(t){n=isNaN(t)||!1===Number.isInteger(t)||t<0?300:t,this.getContext("2d").pageWrapYEnabled&&(this.getContext("2d").pageWrapY=n+1)}});var r=[];Object.defineProperty(this,"childNodes",{get:function(){return r},set:function(t){r=t}});var i={};Object.defineProperty(this,"style",{get:function(){return i},set:function(t){i=t}}),Object.defineProperty(this,"parentNode",{})};e.prototype.getContext=function(t,e){var n;if("2d"!==(t=t||"2d"))return null;for(n in e)this.pdf.context2d.hasOwnProperty(n)&&(this.pdf.context2d[n]=e[n]);return this.pdf.context2d._canvas=this,this.pdf.context2d},e.prototype.toDataURL=function(){throw new Error("toDataURL is not implemented.")},t.events.push(["initialized",function(){this.canvas=new e,this.canvas.pdf=this}])}(D.API),function(t){var e={left:0,top:0,bottom:0,right:0},n=!1,r=function(){void 0===this.internal.__cell__&&(this.internal.__cell__={},this.internal.__cell__.padding=3,this.internal.__cell__.headerFunction=void 0,this.internal.__cell__.margins=Object.assign({},e),this.internal.__cell__.margins.width=this.getPageWidth(),o.call(this))},o=function(){this.internal.__cell__.lastCell=new s,this.internal.__cell__.pages=1},s=function(){var t=arguments[0];Object.defineProperty(this,"x",{enumerable:!0,get:function(){return t},set:function(e){t=e}});var e=arguments[1];Object.defineProperty(this,"y",{enumerable:!0,get:function(){return e},set:function(t){e=t}});var n=arguments[2];Object.defineProperty(this,"width",{enumerable:!0,get:function(){return n},set:function(t){n=t}});var r=arguments[3];Object.defineProperty(this,"height",{enumerable:!0,get:function(){return r},set:function(t){r=t}});var i=arguments[4];Object.defineProperty(this,"text",{enumerable:!0,get:function(){return i},set:function(t){i=t}});var o=arguments[5];Object.defineProperty(this,"lineNumber",{enumerable:!0,get:function(){return o},set:function(t){o=t}});var s=arguments[6];return Object.defineProperty(this,"align",{enumerable:!0,get:function(){return s},set:function(t){s=t}}),this};s.prototype.clone=function(){return new s(this.x,this.y,this.width,this.height,this.text,this.lineNumber,this.align)},s.prototype.toArray=function(){return[this.x,this.y,this.width,this.height,this.text,this.lineNumber,this.align]},t.setHeaderFunction=function(t){return r.call(this),this.internal.__cell__.headerFunction="function"==typeof t?t:void 0,this},t.getTextDimensions=function(t,e){r.call(this);var n=(e=e||{}).fontSize||this.getFontSize(),i=e.font||this.getFont(),o=e.scaleFactor||this.internal.scaleFactor,s=0,a=0,c=0,u=this;if(!Array.isArray(t)&&"string"!=typeof t){if("number"!=typeof t)throw new Error("getTextDimensions expects text-parameter to be of type String or type Number or an Array of Strings.");t=String(t)}var l=e.maxWidth;l>0?"string"==typeof t?t=this.splitTextToSize(t,l):"[object Array]"===Object.prototype.toString.call(t)&&(t=t.reduce((function(t,e){return t.concat(u.splitTextToSize(e,l))}),[])):t=Array.isArray(t)?t:[t];for(var h=0;h<t.length;h++)s<(c=this.getStringUnitWidth(t[h],{font:i})*n)&&(s=c);return 0!==s&&(a=t.length),{w:s/=o,h:Math.max((a*n*this.getLineHeightFactor()-n*(this.getLineHeightFactor()-1))/o,0)}},t.cellAddPage=function(){r.call(this),this.addPage();var t=this.internal.__cell__.margins||e;return this.internal.__cell__.lastCell=new s(t.left,t.top,void 0,void 0),this.internal.__cell__.pages+=1,this};var a=t.cell=function(){var t;t=arguments[0]instanceof s?arguments[0]:new s(arguments[0],arguments[1],arguments[2],arguments[3],arguments[4],arguments[5]),r.call(this);var i=this.internal.__cell__.lastCell,o=this.internal.__cell__.padding,a=this.internal.__cell__.margins||e,c=this.internal.__cell__.tableHeaderRow,u=this.internal.__cell__.printHeaders;return void 0!==i.lineNumber&&(i.lineNumber===t.lineNumber?(t.x=(i.x||0)+(i.width||0),t.y=i.y||0):i.y+i.height+t.height+a.bottom>this.getPageHeight()?(this.cellAddPage(),t.y=a.top,u&&c&&(this.printHeaderRow(t.lineNumber,!0),t.y+=c[0].height)):t.y=i.y+i.height||t.y),void 0!==t.text[0]&&(this.rect(t.x,t.y,t.width,t.height,!0===n?"FD":void 0),"right"===t.align?this.text(t.text,t.x+t.width-o,t.y+o,{align:"right",baseline:"top"}):"center"===t.align?this.text(t.text,t.x+t.width/2,t.y+o,{align:"center",baseline:"top",maxWidth:t.width-o-o}):this.text(t.text,t.x+o,t.y+o,{align:"left",baseline:"top",maxWidth:t.width-o-o})),this.internal.__cell__.lastCell=t,this};t.table=function(t,n,u,l,h){if(r.call(this),!u)throw new Error("No data for PDF table.");var f,d,p,m,g=[],v=[],b=[],y={},w={},j=[],_=[],x=(h=h||{}).autoSize||!1,N=!1!==h.printHeaders,A=h.css&&void 0!==h.css["font-size"]?16*h.css["font-size"]:h.fontSize||12,L=h.margins||Object.assign({width:this.getPageWidth()},e),S="number"==typeof h.padding?h.padding:3,P=h.headerBackgroundColor||"#c8c8c8",k=h.headerTextColor||"#000";if(o.call(this),this.internal.__cell__.printHeaders=N,this.internal.__cell__.margins=L,this.internal.__cell__.table_font_size=A,this.internal.__cell__.padding=S,this.internal.__cell__.headerBackgroundColor=P,this.internal.__cell__.headerTextColor=k,this.setFontSize(A),null==l)v=g=Object.keys(u[0]),b=g.map((function(){return"left"}));else if(Array.isArray(l)&&"object"===i()(l[0]))for(g=l.map((function(t){return t.name})),v=l.map((function(t){return t.prompt||t.name||""})),b=l.map((function(t){return t.align||"left"})),f=0;f<l.length;f+=1)w[l[f].name]=l[f].width*(19.049976/25.4);else Array.isArray(l)&&"string"==typeof l[0]&&(v=g=l,b=g.map((function(){return"left"})));if(x||Array.isArray(l)&&"string"==typeof l[0])for(f=0;f<g.length;f+=1){for(y[m=g[f]]=u.map((function(t){return t[m]})),this.setFont(void 0,"bold"),j.push(this.getTextDimensions(v[f],{fontSize:this.internal.__cell__.table_font_size,scaleFactor:this.internal.scaleFactor}).w),d=y[m],this.setFont(void 0,"normal"),p=0;p<d.length;p+=1)j.push(this.getTextDimensions(d[p],{fontSize:this.internal.__cell__.table_font_size,scaleFactor:this.internal.scaleFactor}).w);w[m]=Math.max.apply(null,j)+S+S,j=[]}if(N){var I={};for(f=0;f<g.length;f+=1)I[g[f]]={},I[g[f]].text=v[f],I[g[f]].align=b[f];var C=c.call(this,I,w);_=g.map((function(e){return new s(t,n,w[e],C,I[e].text,void 0,I[e].align)})),this.setTableHeaderRow(_),this.printHeaderRow(1,!1)}var F=l.reduce((function(t,e){return t[e.name]=e.align,t}),{});for(f=0;f<u.length;f+=1){"rowStart"in h&&h.rowStart instanceof Function&&h.rowStart({row:f,data:u[f]},this);var O=c.call(this,u[f],w);for(p=0;p<g.length;p+=1){var E=u[f][g[p]];"cellStart"in h&&h.cellStart instanceof Function&&h.cellStart({row:f,col:p,data:E},this),a.call(this,new s(t,n,w[g[p]],O,E,f+2,F[g[p]]))}}return this.internal.__cell__.table_x=t,this.internal.__cell__.table_y=n,this};var c=function(t,e){var n=this.internal.__cell__.padding,r=this.internal.__cell__.table_font_size,i=this.internal.scaleFactor;return Object.keys(t).map((function(r){var i=t[r];return this.splitTextToSize(i.hasOwnProperty("text")?i.text:i,e[r]-n-n)}),this).map((function(t){return this.getLineHeightFactor()*t.length*r/i+n+n}),this).reduce((function(t,e){return Math.max(t,e)}),0)};t.setTableHeaderRow=function(t){r.call(this),this.internal.__cell__.tableHeaderRow=t},t.printHeaderRow=function(t,e){if(r.call(this),!this.internal.__cell__.tableHeaderRow)throw new Error("Property tableHeaderRow does not exist.");var i;if(n=!0,"function"==typeof this.internal.__cell__.headerFunction){var o=this.internal.__cell__.headerFunction(this,this.internal.__cell__.pages);this.internal.__cell__.lastCell=new s(o[0],o[1],o[2],o[3],void 0,-1)}this.setFont(void 0,"bold");for(var c=[],u=0;u<this.internal.__cell__.tableHeaderRow.length;u+=1){i=this.internal.__cell__.tableHeaderRow[u].clone(),e&&(i.y=this.internal.__cell__.margins.top||0,c.push(i)),i.lineNumber=t;var l=this.getTextColor();this.setTextColor(this.internal.__cell__.headerTextColor),this.setFillColor(this.internal.__cell__.headerBackgroundColor),a.call(this,i),this.setTextColor(l)}c.length>0&&this.setTableHeaderRow(c),this.setFont(void 0,"normal"),n=!1}}(D.API);var It={italic:["italic","oblique","normal"],oblique:["oblique","italic","normal"],normal:["normal","oblique","italic"]},Ct=["ultra-condensed","extra-condensed","condensed","semi-condensed","normal","semi-expanded","expanded","extra-expanded","ultra-expanded"],Ft=kt(Ct),Ot=[100,200,300,400,500,600,700,800,900],Et=kt(Ot);function Mt(t){var e=t.family.replace(/"|'/g,"").toLowerCase(),n=function(t){return It[t=t||"normal"]?t:"normal"}(t.style),r=function(t){if(!t)return 400;if("number"==typeof t)return t>=100&&t<=900&&t%100==0?t:400;if(/^\d00$/.test(t))return parseInt(t);switch(t){case"bold":return 700;case"normal":default:return 400}}(t.weight),i=function(t){return"number"==typeof Ft[t=t||"normal"]?t:"normal"}(t.stretch);return{family:e,style:n,weight:r,stretch:i,src:t.src||[],ref:t.ref||{name:e,style:[i,n,r].join(" ")}}}function Bt(t,e,n,r){var i;for(i=n;i>=0&&i<e.length;i+=r)if(t[e[i]])return t[e[i]];for(i=n;i>=0&&i<e.length;i-=r)if(t[e[i]])return t[e[i]]}var Tt={"sans-serif":"helvetica",fixed:"courier",monospace:"courier",terminal:"courier",cursive:"times",fantasy:"times",serif:"times"},qt={caption:"times",icon:"times",menu:"times","message-box":"times","small-caption":"times","status-bar":"times"};function Dt(t){return[t.stretch,t.style,t.weight,t.family].join(" ")}function Rt(t,e,n){for(var r=(n=n||{}).defaultFontFamily||"times",i=Object.assign({},Tt,n.genericFontFamilies||{}),o=null,s=null,a=0;a<e.length;++a)if(i[(o=Mt(e[a])).family]&&(o.family=i[o.family]),t.hasOwnProperty(o.family)){s=t[o.family];break}if(!(s=s||t[r]))throw new Error("Could not find a font-family for the rule '"+Dt(o)+"' and default family '"+r+"'.");if(s=function(t,e){if(e[t])return e[t];var n=Ft[t],r=n<=Ft.normal?-1:1,i=Bt(e,Ct,n,r);if(!i)throw new Error("Could not find a matching font-stretch value for "+t);return i}(o.stretch,s),s=function(t,e){if(e[t])return e[t];for(var n=It[t],r=0;r<n.length;++r)if(e[n[r]])return e[n[r]];throw new Error("Could not find a matching font-style for "+t)}(o.style,s),!(s=function(t,e){if(e[t])return e[t];if(400===t&&e[500])return e[500];if(500===t&&e[400])return e[400];var n=Et[t],r=Bt(e,Ot,n,t<400?-1:1);if(!r)throw new Error("Could not find a matching font-weight for value "+t);return r}(o.weight,s)))throw new Error("Failed to resolve a font for the rule '"+Dt(o)+"'.");return s}function zt(t){return t.trimLeft()}function Ut(t,e){for(var n=0;n<t.length;){if(t.charAt(n)===e)return[t.substring(0,n),t.substring(n+1)];n+=1}return null}function Ht(t){var e=t.match(/^(-[a-z_]|[a-z_])[a-z0-9_-]*/i);return null===e?null:[e[0],t.substring(e[0].length)]}var Vt,Wt,Gt,Yt=["times"];!function(t){var e,n,r,o,s,a,u,l,h,f=function(t){return t=t||{},this.isStrokeTransparent=t.isStrokeTransparent||!1,this.strokeOpacity=t.strokeOpacity||1,this.strokeStyle=t.strokeStyle||"#000000",this.fillStyle=t.fillStyle||"#000000",this.isFillTransparent=t.isFillTransparent||!1,this.fillOpacity=t.fillOpacity||1,this.font=t.font||"10px sans-serif",this.textBaseline=t.textBaseline||"alphabetic",this.textAlign=t.textAlign||"left",this.lineWidth=t.lineWidth||1,this.lineJoin=t.lineJoin||"miter",this.lineCap=t.lineCap||"butt",this.path=t.path||[],this.transform=void 0!==t.transform?t.transform.clone():new l,this.globalCompositeOperation=t.globalCompositeOperation||"normal",this.globalAlpha=t.globalAlpha||1,this.clip_path=t.clip_path||[],this.currentPoint=t.currentPoint||new a,this.miterLimit=t.miterLimit||10,this.lastPoint=t.lastPoint||new a,this.lineDashOffset=t.lineDashOffset||0,this.lineDash=t.lineDash||[],this.margin=t.margin||[0,0,0,0],this.prevPageLastElemOffset=t.prevPageLastElemOffset||0,this.ignoreClearRect="boolean"!=typeof t.ignoreClearRect||t.ignoreClearRect,this};t.events.push(["initialized",function(){this.context2d=new d(this),e=this.internal.f2,n=this.internal.getCoordinateString,r=this.internal.getVerticalCoordinateString,o=this.internal.getHorizontalCoordinate,s=this.internal.getVerticalCoordinate,a=this.internal.Point,u=this.internal.Rectangle,l=this.internal.Matrix,h=new f}]);var d=function(t){Object.defineProperty(this,"canvas",{get:function(){return{parentNode:!1,style:!1}}});var e=t;Object.defineProperty(this,"pdf",{get:function(){return e}});var n=!1;Object.defineProperty(this,"pageWrapXEnabled",{get:function(){return n},set:function(t){n=Boolean(t)}});var r=!1;Object.defineProperty(this,"pageWrapYEnabled",{get:function(){return r},set:function(t){r=Boolean(t)}});var i=0;Object.defineProperty(this,"posX",{get:function(){return i},set:function(t){isNaN(t)||(i=t)}});var o=0;Object.defineProperty(this,"posY",{get:function(){return o},set:function(t){isNaN(t)||(o=t)}}),Object.defineProperty(this,"margin",{get:function(){return h.margin},set:function(t){var e;"number"==typeof t?e=[t,t,t,t]:((e=new Array(4))[0]=t[0],e[1]=t.length>=2?t[1]:e[0],e[2]=t.length>=3?t[2]:e[0],e[3]=t.length>=4?t[3]:e[1]),h.margin=e}});var s=!1;Object.defineProperty(this,"autoPaging",{get:function(){return s},set:function(t){s=t}});var a=0;Object.defineProperty(this,"lastBreak",{get:function(){return a},set:function(t){a=t}});var c=[];Object.defineProperty(this,"pageBreaks",{get:function(){return c},set:function(t){c=t}}),Object.defineProperty(this,"ctx",{get:function(){return h},set:function(t){t instanceof f&&(h=t)}}),Object.defineProperty(this,"path",{get:function(){return h.path},set:function(t){h.path=t}});var u=[];Object.defineProperty(this,"ctxStack",{get:function(){return u},set:function(t){u=t}}),Object.defineProperty(this,"fillStyle",{get:function(){return this.ctx.fillStyle},set:function(t){var e;e=p(t),this.ctx.fillStyle=e.style,this.ctx.isFillTransparent=0===e.a,this.ctx.fillOpacity=e.a,this.pdf.setFillColor(e.r,e.g,e.b,{a:e.a}),this.pdf.setTextColor(e.r,e.g,e.b,{a:e.a})}}),Object.defineProperty(this,"strokeStyle",{get:function(){return this.ctx.strokeStyle},set:function(t){var e=p(t);this.ctx.strokeStyle=e.style,this.ctx.isStrokeTransparent=0===e.a,this.ctx.strokeOpacity=e.a,0===e.a?this.pdf.setDrawColor(255,255,255):(e.a,this.pdf.setDrawColor(e.r,e.g,e.b))}}),Object.defineProperty(this,"lineCap",{get:function(){return this.ctx.lineCap},set:function(t){-1!==["butt","round","square"].indexOf(t)&&(this.ctx.lineCap=t,this.pdf.setLineCap(t))}}),Object.defineProperty(this,"lineWidth",{get:function(){return this.ctx.lineWidth},set:function(t){isNaN(t)||(this.ctx.lineWidth=t,this.pdf.setLineWidth(t))}}),Object.defineProperty(this,"lineJoin",{get:function(){return this.ctx.lineJoin},set:function(t){-1!==["bevel","round","miter"].indexOf(t)&&(this.ctx.lineJoin=t,this.pdf.setLineJoin(t))}}),Object.defineProperty(this,"miterLimit",{get:function(){return this.ctx.miterLimit},set:function(t){isNaN(t)||(this.ctx.miterLimit=t,this.pdf.setMiterLimit(t))}}),Object.defineProperty(this,"textBaseline",{get:function(){return this.ctx.textBaseline},set:function(t){this.ctx.textBaseline=t}}),Object.defineProperty(this,"textAlign",{get:function(){return this.ctx.textAlign},set:function(t){-1!==["right","end","center","left","start"].indexOf(t)&&(this.ctx.textAlign=t)}});var l=null;function d(t,e){if(null===l){var n=function(t){var e=[];return Object.keys(t).forEach((function(n){t[n].forEach((function(t){var r=null;switch(t){case"bold":r={family:n,weight:"bold"};break;case"italic":r={family:n,style:"italic"};break;case"bolditalic":r={family:n,weight:"bold",style:"italic"};break;case"":case"normal":r={family:n}}null!==r&&(r.ref={name:n,style:t},e.push(r))}))})),e}(t.getFontList());l=function(t){for(var e={},n=0;n<t.length;++n){var r=Mt(t[n]),i=r.family,o=r.stretch,s=r.style,a=r.weight;e[i]=e[i]||{},e[i][o]=e[i][o]||{},e[i][o][s]=e[i][o][s]||{},e[i][o][s][a]=r}return e}(n.concat(e))}return l}var m=null;Object.defineProperty(this,"fontFaces",{get:function(){return m},set:function(t){l=null,m=t}}),Object.defineProperty(this,"font",{get:function(){return this.ctx.font},set:function(t){var e;if(this.ctx.font=t,null!==(e=/^\s*(?=(?:(?:[-a-z]+\s*){0,2}(italic|oblique))?)(?=(?:(?:[-a-z]+\s*){0,2}(small-caps))?)(?=(?:(?:[-a-z]+\s*){0,2}(bold(?:er)?|lighter|[1-9]00))?)(?:(?:normal|\1|\2|\3)\s*){0,3}((?:xx?-)?(?:small|large)|medium|smaller|larger|[.\d]+(?:\%|in|[cem]m|ex|p[ctx]))(?:\s*\/\s*(normal|[.\d]+(?:\%|in|[cem]m|ex|p[ctx])))?\s*([-_,\"\'\sa-z]+?)\s*$/i.exec(t))){var n=e[1],r=(e[2],e[3]),i=e[4],o=(e[5],e[6]),s=/^([.\d]+)((?:%|in|[cem]m|ex|p[ctx]))$/i.exec(i)[2];i="px"===s?Math.floor(parseFloat(i)*this.pdf.internal.scaleFactor):"em"===s?Math.floor(parseFloat(i)*this.pdf.getFontSize()):Math.floor(parseFloat(i)*this.pdf.internal.scaleFactor),this.pdf.setFontSize(i);var a=function(t){var e,n,r=[],i=t.trim();if(""===i)return Yt;if(i in qt)return[qt[i]];for(;""!==i;){switch(n=null,e=(i=zt(i)).charAt(0)){case'"':case"'":n=Ut(i.substring(1),e);break;default:n=Ht(i)}if(null===n)return Yt;if(r.push(n[0]),""!==(i=zt(n[1]))&&","!==i.charAt(0))return Yt;i=i.replace(/^,/,"")}return r}(o);if(this.fontFaces){var c=Rt(d(this.pdf,this.fontFaces),a.map((function(t){return{family:t,stretch:"normal",weight:r,style:n}})));this.pdf.setFont(c.ref.name,c.ref.style)}else{var u="";("bold"===r||parseInt(r,10)>=700||"bold"===n)&&(u="bold"),"italic"===n&&(u+="italic"),0===u.length&&(u="normal");for(var l="",h={arial:"Helvetica",Arial:"Helvetica",verdana:"Helvetica",Verdana:"Helvetica",helvetica:"Helvetica",Helvetica:"Helvetica","sans-serif":"Helvetica",fixed:"Courier",monospace:"Courier",terminal:"Courier",cursive:"Times",fantasy:"Times",serif:"Times"},f=0;f<a.length;f++){if(void 0!==this.pdf.internal.getFont(a[f],u,{noFallback:!0,disableWarning:!0})){l=a[f];break}if("bolditalic"===u&&void 0!==this.pdf.internal.getFont(a[f],"bold",{noFallback:!0,disableWarning:!0}))l=a[f],u="bold";else if(void 0!==this.pdf.internal.getFont(a[f],"normal",{noFallback:!0,disableWarning:!0})){l=a[f],u="normal";break}}if(""===l)for(var p=0;p<a.length;p++)if(h[a[p]]){l=h[a[p]];break}l=""===l?"Times":l,this.pdf.setFont(l,u)}}}}),Object.defineProperty(this,"globalCompositeOperation",{get:function(){return this.ctx.globalCompositeOperation},set:function(t){this.ctx.globalCompositeOperation=t}}),Object.defineProperty(this,"globalAlpha",{get:function(){return this.ctx.globalAlpha},set:function(t){this.ctx.globalAlpha=t}}),Object.defineProperty(this,"lineDashOffset",{get:function(){return this.ctx.lineDashOffset},set:function(t){this.ctx.lineDashOffset=t,R.call(this)}}),Object.defineProperty(this,"lineDash",{get:function(){return this.ctx.lineDash},set:function(t){this.ctx.lineDash=t,R.call(this)}}),Object.defineProperty(this,"ignoreClearRect",{get:function(){return this.ctx.ignoreClearRect},set:function(t){this.ctx.ignoreClearRect=Boolean(t)}})};d.prototype.setLineDash=function(t){this.lineDash=t},d.prototype.getLineDash=function(){return this.lineDash.length%2?this.lineDash.concat(this.lineDash):this.lineDash.slice()},d.prototype.fill=function(){x.call(this,"fill",!1)},d.prototype.stroke=function(){x.call(this,"stroke",!1)},d.prototype.beginPath=function(){this.path=[{type:"begin"}]},d.prototype.moveTo=function(t,e){if(isNaN(t)||isNaN(e))throw c.error("jsPDF.context2d.moveTo: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.moveTo");var n=this.ctx.transform.applyToPoint(new a(t,e));this.path.push({type:"mt",x:n.x,y:n.y}),this.ctx.lastPoint=new a(t,e)},d.prototype.closePath=function(){var t=new a(0,0),e=0;for(e=this.path.length-1;-1!==e;e--)if("begin"===this.path[e].type&&"object"===i()(this.path[e+1])&&"number"==typeof this.path[e+1].x){t=new a(this.path[e+1].x,this.path[e+1].y);break}this.path.push({type:"close"}),this.ctx.lastPoint=new a(t.x,t.y)},d.prototype.lineTo=function(t,e){if(isNaN(t)||isNaN(e))throw c.error("jsPDF.context2d.lineTo: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.lineTo");var n=this.ctx.transform.applyToPoint(new a(t,e));this.path.push({type:"lt",x:n.x,y:n.y}),this.ctx.lastPoint=new a(n.x,n.y)},d.prototype.clip=function(){this.ctx.clip_path=JSON.parse(JSON.stringify(this.path)),x.call(this,null,!0)},d.prototype.quadraticCurveTo=function(t,e,n,r){if(isNaN(n)||isNaN(r)||isNaN(t)||isNaN(e))throw c.error("jsPDF.context2d.quadraticCurveTo: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.quadraticCurveTo");var i=this.ctx.transform.applyToPoint(new a(n,r)),o=this.ctx.transform.applyToPoint(new a(t,e));this.path.push({type:"qct",x1:o.x,y1:o.y,x:i.x,y:i.y}),this.ctx.lastPoint=new a(i.x,i.y)},d.prototype.bezierCurveTo=function(t,e,n,r,i,o){if(isNaN(i)||isNaN(o)||isNaN(t)||isNaN(e)||isNaN(n)||isNaN(r))throw c.error("jsPDF.context2d.bezierCurveTo: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.bezierCurveTo");var s=this.ctx.transform.applyToPoint(new a(i,o)),u=this.ctx.transform.applyToPoint(new a(t,e)),l=this.ctx.transform.applyToPoint(new a(n,r));this.path.push({type:"bct",x1:u.x,y1:u.y,x2:l.x,y2:l.y,x:s.x,y:s.y}),this.ctx.lastPoint=new a(s.x,s.y)},d.prototype.arc=function(t,e,n,r,i,o){if(isNaN(t)||isNaN(e)||isNaN(n)||isNaN(r)||isNaN(i))throw c.error("jsPDF.context2d.arc: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.arc");if(o=Boolean(o),!this.ctx.transform.isIdentity){var s=this.ctx.transform.applyToPoint(new a(t,e));t=s.x,e=s.y;var u=this.ctx.transform.applyToPoint(new a(0,n)),l=this.ctx.transform.applyToPoint(new a(0,0));n=Math.sqrt(Math.pow(u.x-l.x,2)+Math.pow(u.y-l.y,2))}Math.abs(i-r)>=2*Math.PI&&(r=0,i=2*Math.PI),this.path.push({type:"arc",x:t,y:e,radius:n,startAngle:r,endAngle:i,counterclockwise:o})},d.prototype.arcTo=function(t,e,n,r,i){throw new Error("arcTo not implemented.")},d.prototype.rect=function(t,e,n,r){if(isNaN(t)||isNaN(e)||isNaN(n)||isNaN(r))throw c.error("jsPDF.context2d.rect: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.rect");this.moveTo(t,e),this.lineTo(t+n,e),this.lineTo(t+n,e+r),this.lineTo(t,e+r),this.lineTo(t,e),this.lineTo(t+n,e),this.lineTo(t,e)},d.prototype.fillRect=function(t,e,n,r){if(isNaN(t)||isNaN(e)||isNaN(n)||isNaN(r))throw c.error("jsPDF.context2d.fillRect: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.fillRect");if(!g.call(this)){var i={};"butt"!==this.lineCap&&(i.lineCap=this.lineCap,this.lineCap="butt"),"miter"!==this.lineJoin&&(i.lineJoin=this.lineJoin,this.lineJoin="miter"),this.beginPath(),this.rect(t,e,n,r),this.fill(),i.hasOwnProperty("lineCap")&&(this.lineCap=i.lineCap),i.hasOwnProperty("lineJoin")&&(this.lineJoin=i.lineJoin)}},d.prototype.strokeRect=function(t,e,n,r){if(isNaN(t)||isNaN(e)||isNaN(n)||isNaN(r))throw c.error("jsPDF.context2d.strokeRect: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.strokeRect");v.call(this)||(this.beginPath(),this.rect(t,e,n,r),this.stroke())},d.prototype.clearRect=function(t,e,n,r){if(isNaN(t)||isNaN(e)||isNaN(n)||isNaN(r))throw c.error("jsPDF.context2d.clearRect: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.clearRect");this.ignoreClearRect||(this.fillStyle="#ffffff",this.fillRect(t,e,n,r))},d.prototype.save=function(t){t="boolean"!=typeof t||t;for(var e=this.pdf.internal.getCurrentPageInfo().pageNumber,n=0;n<this.pdf.internal.getNumberOfPages();n++)this.pdf.setPage(n+1),this.pdf.internal.out("q");if(this.pdf.setPage(e),t){this.ctx.fontSize=this.pdf.internal.getFontSize();var r=new f(this.ctx);this.ctxStack.push(this.ctx),this.ctx=r}},d.prototype.restore=function(t){t="boolean"!=typeof t||t;for(var e=this.pdf.internal.getCurrentPageInfo().pageNumber,n=0;n<this.pdf.internal.getNumberOfPages();n++)this.pdf.setPage(n+1),this.pdf.internal.out("Q");this.pdf.setPage(e),t&&0!==this.ctxStack.length&&(this.ctx=this.ctxStack.pop(),this.fillStyle=this.ctx.fillStyle,this.strokeStyle=this.ctx.strokeStyle,this.font=this.ctx.font,this.lineCap=this.ctx.lineCap,this.lineWidth=this.ctx.lineWidth,this.lineJoin=this.ctx.lineJoin,this.lineDash=this.ctx.lineDash,this.lineDashOffset=this.ctx.lineDashOffset)},d.prototype.toDataURL=function(){throw new Error("toDataUrl not implemented.")};var p=function(t){var e,n,r,i;if(!0===t.isCanvasGradient&&(t=t.getColor()),!t)return{r:0,g:0,b:0,a:0,style:t};if(/transparent|rgba\s*\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*,\s*0+\s*\)/.test(t))e=0,n=0,r=0,i=0;else{var o=/rgb\s*\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*\)/.exec(t);if(null!==o)e=parseInt(o[1]),n=parseInt(o[2]),r=parseInt(o[3]),i=1;else if(null!==(o=/rgba\s*\(\s*(\d+)\s*,\s*(\d+)\s*,\s*(\d+)\s*,\s*([\d.]+)\s*\)/.exec(t)))e=parseInt(o[1]),n=parseInt(o[2]),r=parseInt(o[3]),i=parseFloat(o[4]);else{if(i=1,"string"==typeof t&&"#"!==t.charAt(0)){var s=new m(t);t=s.ok?s.toHex():"#000000"}4===t.length?(e=t.substring(1,2),e+=e,n=t.substring(2,3),n+=n,r=t.substring(3,4),r+=r):(e=t.substring(1,3),n=t.substring(3,5),r=t.substring(5,7)),e=parseInt(e,16),n=parseInt(n,16),r=parseInt(r,16)}}return{r:e,g:n,b:r,a:i,style:t}},g=function(){return this.ctx.isFillTransparent||0==this.globalAlpha},v=function(){return Boolean(this.ctx.isStrokeTransparent||0==this.globalAlpha)};d.prototype.fillText=function(t,e,n,r){if(isNaN(e)||isNaN(n)||"string"!=typeof t)throw c.error("jsPDF.context2d.fillText: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.fillText");if(r=isNaN(r)?void 0:r,!g.call(this)){var i=T(this.ctx.transform.rotation),o=this.ctx.transform.scaleX;C.call(this,{text:t,x:e,y:n,scale:o,angle:i,align:this.textAlign,maxWidth:r})}},d.prototype.strokeText=function(t,e,n,r){if(isNaN(e)||isNaN(n)||"string"!=typeof t)throw c.error("jsPDF.context2d.strokeText: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.strokeText");if(!v.call(this)){r=isNaN(r)?void 0:r;var i=T(this.ctx.transform.rotation),o=this.ctx.transform.scaleX;C.call(this,{text:t,x:e,y:n,scale:o,renderingMode:"stroke",angle:i,align:this.textAlign,maxWidth:r})}},d.prototype.measureText=function(t){if("string"!=typeof t)throw c.error("jsPDF.context2d.measureText: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.measureText");var e=this.pdf,n=this.pdf.internal.scaleFactor,r=e.internal.getFontSize(),i=e.getStringUnitWidth(t)*r/e.internal.scaleFactor,o=function(t){var e=(t=t||{}).width||0;return Object.defineProperty(this,"width",{get:function(){return e}}),this};return new o({width:i*=Math.round(96*n/72*1e4)/1e4})},d.prototype.scale=function(t,e){if(isNaN(t)||isNaN(e))throw c.error("jsPDF.context2d.scale: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.scale");var n=new l(t,0,0,e,0,0);this.ctx.transform=this.ctx.transform.multiply(n)},d.prototype.rotate=function(t){if(isNaN(t))throw c.error("jsPDF.context2d.rotate: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.rotate");var e=new l(Math.cos(t),Math.sin(t),-Math.sin(t),Math.cos(t),0,0);this.ctx.transform=this.ctx.transform.multiply(e)},d.prototype.translate=function(t,e){if(isNaN(t)||isNaN(e))throw c.error("jsPDF.context2d.translate: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.translate");var n=new l(1,0,0,1,t,e);this.ctx.transform=this.ctx.transform.multiply(n)},d.prototype.transform=function(t,e,n,r,i,o){if(isNaN(t)||isNaN(e)||isNaN(n)||isNaN(r)||isNaN(i)||isNaN(o))throw c.error("jsPDF.context2d.transform: Invalid arguments",arguments),new Error("Invalid arguments passed to jsPDF.context2d.transform");var s=new l(t,e,n,r,i,o);this.ctx.transform=this.ctx.transform.multiply(s)},d.prototype.setTransform=function(t,e,n,r,i,o){t=isNaN(t)?1:t,e=isNaN(e)?0:e,n=isNaN(n)?0:n,r=isNaN(r)?1:r,i=isNaN(i)?0:i,o=isNaN(o)?0:o,this.ctx.transform=new l(t,e,n,r,i,o)};var b=function(){return this.margin[0]>0||this.margin[1]>0||this.margin[2]>0||this.margin[3]>0};d.prototype.drawImage=function(t,e,n,r,i,o,s,a,c){var h=this.pdf.getImageProperties(t),f=1,d=1,p=1,m=1;void 0!==r&&void 0!==a&&(p=a/r,m=c/i,f=h.width/r*a/r,d=h.height/i*c/i),void 0===o&&(o=e,s=n,e=0,n=0),void 0!==r&&void 0===a&&(a=r,c=i),void 0===r&&void 0===a&&(a=h.width,c=h.height);for(var g,v=this.ctx.transform.decompose(),w=T(v.rotate.shx),x=new l,A=(x=(x=(x=x.multiply(v.translate)).multiply(v.skew)).multiply(v.scale)).applyToRectangle(new u(o-e*p,s-n*m,r*f,i*d)),L=y.call(this,A),S=[],P=0;P<L.length;P+=1)-1===S.indexOf(L[P])&&S.push(L[P]);if(_(S),this.autoPaging)for(var k=S[0],I=S[S.length-1],C=k;C<I+1;C++){this.pdf.setPage(C);var F=this.pdf.internal.pageSize.width-this.margin[3]-this.margin[1],O=1===C?this.posY+this.margin[0]:this.margin[0],E=this.pdf.internal.pageSize.height-this.posY-this.margin[0]-this.margin[2],M=this.pdf.internal.pageSize.height-this.margin[0]-this.margin[2],B=1===C?0:E+(C-2)*M;if(0!==this.ctx.clip_path.length){var q=this.path;g=JSON.parse(JSON.stringify(this.ctx.clip_path)),this.path=j(g,this.posX+this.margin[3],-B+O+this.ctx.prevPageLastElemOffset),N.call(this,"fill",!0),this.path=q}var D=JSON.parse(JSON.stringify(A));D=j([D],this.posX+this.margin[3],-B+O+this.ctx.prevPageLastElemOffset)[0];var R=(C>k||C<I)&&b.call(this);R&&(this.pdf.saveGraphicsState(),this.pdf.rect(this.margin[3],this.margin[0],F,M,null).clip().discardPath()),this.pdf.addImage(t,"JPEG",D.x,D.y,D.w,D.h,null,null,w),R&&this.pdf.restoreGraphicsState()}else this.pdf.addImage(t,"JPEG",A.x,A.y,A.w,A.h,null,null,w)};var y=function(t,e,n){var r=[];e=e||this.pdf.internal.pageSize.width,n=n||this.pdf.internal.pageSize.height-this.margin[0]-this.margin[2];var i=this.posY+this.ctx.prevPageLastElemOffset;switch(t.type){default:case"mt":case"lt":r.push(Math.floor((t.y+i)/n)+1);break;case"arc":r.push(Math.floor((t.y+i-t.radius)/n)+1),r.push(Math.floor((t.y+i+t.radius)/n)+1);break;case"qct":var o=q(this.ctx.lastPoint.x,this.ctx.lastPoint.y,t.x1,t.y1,t.x,t.y);r.push(Math.floor((o.y+i)/n)+1),r.push(Math.floor((o.y+o.h+i)/n)+1);break;case"bct":var s=D(this.ctx.lastPoint.x,this.ctx.lastPoint.y,t.x1,t.y1,t.x2,t.y2,t.x,t.y);r.push(Math.floor((s.y+i)/n)+1),r.push(Math.floor((s.y+s.h+i)/n)+1);break;case"rect":r.push(Math.floor((t.y+i)/n)+1),r.push(Math.floor((t.y+t.h+i)/n)+1)}for(var a=0;a<r.length;a+=1)for(;this.pdf.internal.getNumberOfPages()<r[a];)w.call(this);return r},w=function(){var t=this.fillStyle,e=this.strokeStyle,n=this.font,r=this.lineCap,i=this.lineWidth,o=this.lineJoin;this.pdf.addPage(),this.fillStyle=t,this.strokeStyle=e,this.font=n,this.lineCap=r,this.lineWidth=i,this.lineJoin=o},j=function(t,e,n){for(var r=0;r<t.length;r++)switch(t[r].type){case"bct":t[r].x2+=e,t[r].y2+=n;case"qct":t[r].x1+=e,t[r].y1+=n;case"mt":case"lt":case"arc":default:t[r].x+=e,t[r].y+=n}return t},_=function(t){return t.sort((function(t,e){return t-e}))},x=function(t,e){for(var n,r,i=this.fillStyle,o=this.strokeStyle,s=this.lineCap,a=this.lineWidth,c=Math.abs(a*this.ctx.transform.scaleX),u=this.lineJoin,l=JSON.parse(JSON.stringify(this.path)),h=JSON.parse(JSON.stringify(this.path)),f=[],d=0;d<h.length;d++)if(void 0!==h[d].x)for(var p=y.call(this,h[d]),m=0;m<p.length;m+=1)-1===f.indexOf(p[m])&&f.push(p[m]);for(var g=0;g<f.length;g++)for(;this.pdf.internal.getNumberOfPages()<f[g];)w.call(this);if(_(f),this.autoPaging)for(var v=f[0],x=f[f.length-1],A=v;A<x+1;A++){this.pdf.setPage(A),this.fillStyle=i,this.strokeStyle=o,this.lineCap=s,this.lineWidth=c,this.lineJoin=u;var L=this.pdf.internal.pageSize.width-this.margin[3]-this.margin[1],S=1===A?this.posY+this.margin[0]:this.margin[0],P=this.pdf.internal.pageSize.height-this.posY-this.margin[0]-this.margin[2],k=this.pdf.internal.pageSize.height-this.margin[0]-this.margin[2],I=1===A?0:P+(A-2)*k;if(0!==this.ctx.clip_path.length){var C=this.path;n=JSON.parse(JSON.stringify(this.ctx.clip_path)),this.path=j(n,this.posX+this.margin[3],-I+S+this.ctx.prevPageLastElemOffset),N.call(this,t,!0),this.path=C}if(r=JSON.parse(JSON.stringify(l)),this.path=j(r,this.posX+this.margin[3],-I+S+this.ctx.prevPageLastElemOffset),!1===e||0===A){var F=(A>v||A<x)&&b.call(this);F&&(this.pdf.saveGraphicsState(),this.pdf.rect(this.margin[3],this.margin[0],L,k,null).clip().discardPath()),N.call(this,t,e),F&&this.pdf.restoreGraphicsState()}this.lineWidth=a}else this.lineWidth=c,N.call(this,t,e),this.lineWidth=a;this.path=l},N=function(t,e){if(("stroke"!==t||e||!v.call(this))&&("stroke"===t||e||!g.call(this))){for(var n,r,i=[],o=this.path,s=0;s<o.length;s++){var a=o[s];switch(a.type){case"begin":i.push({begin:!0});break;case"close":i.push({close:!0});break;case"mt":i.push({start:a,deltas:[],abs:[]});break;case"lt":var c=i.length;if(o[s-1]&&!isNaN(o[s-1].x)&&(n=[a.x-o[s-1].x,a.y-o[s-1].y],c>0))for(;c>=0;c--)if(!0!==i[c-1].close&&!0!==i[c-1].begin){i[c-1].deltas.push(n),i[c-1].abs.push(a);break}break;case"bct":n=[a.x1-o[s-1].x,a.y1-o[s-1].y,a.x2-o[s-1].x,a.y2-o[s-1].y,a.x-o[s-1].x,a.y-o[s-1].y],i[i.length-1].deltas.push(n);break;case"qct":var u=o[s-1].x+2/3*(a.x1-o[s-1].x),l=o[s-1].y+2/3*(a.y1-o[s-1].y),h=a.x+2/3*(a.x1-a.x),f=a.y+2/3*(a.y1-a.y),d=a.x,p=a.y;n=[u-o[s-1].x,l-o[s-1].y,h-o[s-1].x,f-o[s-1].y,d-o[s-1].x,p-o[s-1].y],i[i.length-1].deltas.push(n);break;case"arc":i.push({deltas:[],abs:[],arc:!0}),Array.isArray(i[i.length-1].abs)&&i[i.length-1].abs.push(a)}}r=e?null:"stroke"===t?"stroke":"fill";for(var m=!1,b=0;b<i.length;b++)if(i[b].arc)for(var y=i[b].abs,w=0;w<y.length;w++){var j=y[w];"arc"===j.type?S.call(this,j.x,j.y,j.radius,j.startAngle,j.endAngle,j.counterclockwise,void 0,e,!m):F.call(this,j.x,j.y),m=!0}else if(!0===i[b].close)this.pdf.internal.out("h"),m=!1;else if(!0!==i[b].begin){var _=i[b].start.x,x=i[b].start.y;O.call(this,i[b].deltas,_,x),m=!0}r&&P.call(this,r),e&&k.call(this)}},A=function(t){var e=this.pdf.internal.getFontSize()/this.pdf.internal.scaleFactor,n=e*(this.pdf.internal.getLineHeightFactor()-1);switch(this.ctx.textBaseline){case"bottom":return t-n;case"top":return t+e-n;case"hanging":return t+e-2*n;case"middle":return t+e/2-n;case"ideographic":return t;case"alphabetic":default:return t}},L=function(t){return t+this.pdf.internal.getFontSize()/this.pdf.internal.scaleFactor*(this.pdf.internal.getLineHeightFactor()-1)};d.prototype.createLinearGradient=function(){var t=function(){};return t.colorStops=[],t.addColorStop=function(t,e){this.colorStops.push([t,e])},t.getColor=function(){return 0===this.colorStops.length?"#000000":this.colorStops[0][1]},t.isCanvasGradient=!0,t},d.prototype.createPattern=function(){return this.createLinearGradient()},d.prototype.createRadialGradient=function(){return this.createLinearGradient()};var S=function(t,e,n,r,i,o,s,a,c){for(var u=M.call(this,n,r,i,o),l=0;l<u.length;l++){var h=u[l];0===l&&(c?I.call(this,h.x1+t,h.y1+e):F.call(this,h.x1+t,h.y1+e)),E.call(this,t,e,h.x2,h.y2,h.x3,h.y3,h.x4,h.y4)}a?k.call(this):P.call(this,s)},P=function(t){switch(t){case"stroke":this.pdf.internal.out("S");break;case"fill":this.pdf.internal.out("f")}},k=function(){this.pdf.clip(),this.pdf.discardPath()},I=function(t,e){this.pdf.internal.out(n(t)+" "+r(e)+" m")},C=function(t){var e;switch(t.align){case"right":case"end":e="right";break;case"center":e="center";break;case"left":case"start":default:e="left"}var n=this.pdf.getTextDimensions(t.text),r=A.call(this,t.y),i=L.call(this,r)-n.h,o=this.ctx.transform.applyToPoint(new a(t.x,r)),s=this.ctx.transform.decompose(),c=new l;c=(c=(c=c.multiply(s.translate)).multiply(s.skew)).multiply(s.scale);for(var h,f,d,p=this.ctx.transform.applyToRectangle(new u(t.x,r,n.w,n.h)),m=c.applyToRectangle(new u(t.x,i,n.w,n.h)),g=y.call(this,m),v=[],w=0;w<g.length;w+=1)-1===v.indexOf(g[w])&&v.push(g[w]);if(_(v),this.autoPaging)for(var x=v[0],S=v[v.length-1],P=x;P<S+1;P++){this.pdf.setPage(P);var k=1===P?this.posY+this.margin[0]:this.margin[0],I=this.pdf.internal.pageSize.height-this.posY-this.margin[0]-this.margin[2],C=this.pdf.internal.pageSize.height-this.margin[2],F=C-this.margin[0],O=this.pdf.internal.pageSize.width-this.margin[1],E=O-this.margin[3],M=1===P?0:I+(P-2)*F;if(0!==this.ctx.clip_path.length){var B=this.path;h=JSON.parse(JSON.stringify(this.ctx.clip_path)),this.path=j(h,this.posX+this.margin[3],-1*M+k),N.call(this,"fill",!0),this.path=B}var T=j([JSON.parse(JSON.stringify(m))],this.posX+this.margin[3],-M+k+this.ctx.prevPageLastElemOffset)[0];t.scale>=.01&&(f=this.pdf.internal.getFontSize(),this.pdf.setFontSize(f*t.scale),d=this.lineWidth,this.lineWidth=d*t.scale);var q="text"!==this.autoPaging;if(q||T.y+T.h<=C){if(q||T.y>=k&&T.x<=O){var D=q?t.text:this.pdf.splitTextToSize(t.text,t.maxWidth||O-T.x)[0],R=j([JSON.parse(JSON.stringify(p))],this.posX+this.margin[3],-M+k+this.ctx.prevPageLastElemOffset)[0],z=q&&(P>x||P<S)&&b.call(this);z&&(this.pdf.saveGraphicsState(),this.pdf.rect(this.margin[3],this.margin[0],E,F,null).clip().discardPath()),this.pdf.text(D,R.x,R.y,{angle:t.angle,align:e,renderingMode:t.renderingMode}),z&&this.pdf.restoreGraphicsState()}}else T.y<C&&(this.ctx.prevPageLastElemOffset+=C-T.y);t.scale>=.01&&(this.pdf.setFontSize(f),this.lineWidth=d)}else t.scale>=.01&&(f=this.pdf.internal.getFontSize(),this.pdf.setFontSize(f*t.scale),d=this.lineWidth,this.lineWidth=d*t.scale),this.pdf.text(t.text,o.x+this.posX,o.y+this.posY,{angle:t.angle,align:e,renderingMode:t.renderingMode,maxWidth:t.maxWidth}),t.scale>=.01&&(this.pdf.setFontSize(f),this.lineWidth=d)},F=function(t,e,i,o){i=i||0,o=o||0,this.pdf.internal.out(n(t+i)+" "+r(e+o)+" l")},O=function(t,e,n){return this.pdf.lines(t,e,n,null,null)},E=function(t,n,r,i,a,c,u,l){this.pdf.internal.out([e(o(r+t)),e(s(i+n)),e(o(a+t)),e(s(c+n)),e(o(u+t)),e(s(l+n)),"c"].join(" "))},M=function(t,e,n,r){for(var i=2*Math.PI,o=Math.PI/2;e>n;)e-=i;var s=Math.abs(n-e);s<i&&r&&(s=i-s);for(var a=[],c=r?-1:1,u=e;s>1e-5;){var l=u+c*Math.min(s,o);a.push(B.call(this,t,u,l)),s-=Math.abs(l-u),u=l}return a},B=function(t,e,n){var r=(n-e)/2,i=t*Math.cos(r),o=t*Math.sin(r),s=i,a=-o,c=s*s+a*a,u=c+s*i+a*o,l=4/3*(Math.sqrt(2*c*u)-u)/(s*o-a*i),h=s-l*a,f=a+l*s,d=h,p=-f,m=r+e,g=Math.cos(m),v=Math.sin(m);return{x1:t*Math.cos(e),y1:t*Math.sin(e),x2:h*g-f*v,y2:h*v+f*g,x3:d*g-p*v,y3:d*v+p*g,x4:t*Math.cos(n),y4:t*Math.sin(n)}},T=function(t){return 180*t/Math.PI},q=function(t,e,n,r,i,o){var s=t+.5*(n-t),a=e+.5*(r-e),c=i+.5*(n-i),l=o+.5*(r-o),h=Math.min(t,i,s,c),f=Math.max(t,i,s,c),d=Math.min(e,o,a,l),p=Math.max(e,o,a,l);return new u(h,d,f-h,p-d)},D=function(t,e,n,r,i,o,s,a){var c,l,h,f,d,p,m,g,v,b,y,w,j,_,x=n-t,N=r-e,A=i-n,L=o-r,S=s-i,P=a-o;for(l=0;l<41;l++)v=(m=(h=t+(c=l/40)*x)+c*((d=n+c*A)-h))+c*(d+c*(i+c*S-d)-m),b=(g=(f=e+c*N)+c*((p=r+c*L)-f))+c*(p+c*(o+c*P-p)-g),0==l?(y=v,w=b,j=v,_=b):(y=Math.min(y,v),w=Math.min(w,b),j=Math.max(j,v),_=Math.max(_,b));return new u(Math.round(y),Math.round(w),Math.round(j-y),Math.round(_-w))},R=function(){if(this.prevLineDash||this.ctx.lineDash.length||this.ctx.lineDashOffset){var t,e,n=(t=this.ctx.lineDash,e=this.ctx.lineDashOffset,JSON.stringify({lineDash:t,lineDashOffset:e}));this.prevLineDash!==n&&(this.pdf.setLineDash(this.ctx.lineDash,this.ctx.lineDashOffset),this.prevLineDash=n)}}}(D.API),
/**
 * @license
 * jsPDF filters PlugIn
 * Copyright (c) 2014 Aras Abbasi
 *
 * Licensed under the MIT License.
 * http://opensource.org/licenses/mit-license
 */
function(t){var e=function(t){var e,n,r,i,o,s,a,c,u,l;for(/[^\x00-\xFF]/.test(t),n=[],r=0,i=(t+=e="\0\0\0\0".slice(t.length%4||4)).length;i>r;r+=4)0!==(o=(t.charCodeAt(r)<<24)+(t.charCodeAt(r+1)<<16)+(t.charCodeAt(r+2)<<8)+t.charCodeAt(r+3))?(s=(o=((o=((o=((o=(o-(l=o%85))/85)-(u=o%85))/85)-(c=o%85))/85)-(a=o%85))/85)%85,n.push(s+33,a+33,c+33,u+33,l+33)):n.push(122);return function(t,e){for(var n=e;n>0;n--)t.pop()}(n,e.length),String.fromCharCode.apply(String,n)+"~>"},n=function(t){var e,n,r,i,o,s=String,a="length",c=255,u="charCodeAt",l="slice",h="replace";for(t[l](-2),t=t[l](0,-2)[h](/\s/g,"")[h]("z","!!!!!"),r=[],i=0,o=(t+=e="uuuuu"[l](t[a]%5||5))[a];o>i;i+=5)n=52200625*(t[u](i)-33)+614125*(t[u](i+1)-33)+7225*(t[u](i+2)-33)+85*(t[u](i+3)-33)+(t[u](i+4)-33),r.push(c&n>>24,c&n>>16,c&n>>8,c&n);return function(t,e){for(var n=e;n>0;n--)t.pop()}(r,e[a]),s.fromCharCode.apply(s,r)},r=function(t){var e=new RegExp(/^([0-9A-Fa-f]{2})+$/);if(-1!==(t=t.replace(/\s/g,"")).indexOf(">")&&(t=t.substr(0,t.indexOf(">"))),t.length%2&&(t+="0"),!1===e.test(t))return"";for(var n="",r=0;r<t.length;r+=2)n+=String.fromCharCode("0x"+(t[r]+t[r+1]));return n},i=function(t){for(var e=new Uint8Array(t.length),n=t.length;n--;)e[n]=t.charCodeAt(n);return(e=Object(o["b"])(e)).reduce((function(t,e){return t+String.fromCharCode(e)}),"")};t.processDataByFilters=function(t,o){var s=0,a=t||"",c=[];for("string"==typeof(o=o||[])&&(o=[o]),s=0;s<o.length;s+=1)switch(o[s]){case"ASCII85Decode":case"/ASCII85Decode":a=n(a),c.push("/ASCII85Encode");break;case"ASCII85Encode":case"/ASCII85Encode":a=e(a),c.push("/ASCII85Decode");break;case"ASCIIHexDecode":case"/ASCIIHexDecode":a=r(a),c.push("/ASCIIHexEncode");break;case"ASCIIHexEncode":case"/ASCIIHexEncode":a=a.split("").map((function(t){return("0"+t.charCodeAt().toString(16)).slice(-2)})).join("")+">",c.push("/ASCIIHexDecode");break;case"FlateEncode":case"/FlateEncode":a=i(a),c.push("/FlateDecode");break;default:throw new Error('The filter: "'+o[s]+'" is not implemented')}return{data:a,reverseChain:c.reverse().join(" ")}}}(D.API),
/**
 * @license
 * jsPDF fileloading PlugIn
 * Copyright (c) 2018 Aras Abbasi (<EMAIL>)
 *
 * Licensed under the MIT License.
 * http://opensource.org/licenses/mit-license
 */
function(t){t.loadFile=function(t,e,n){return function(t,e,n){e=!1!==e,n="function"==typeof n?n:function(){};var r=void 0;try{r=function(t,e,n){var r=new XMLHttpRequest,i=0,o=function(t){var e=t.length,n=[],r=String.fromCharCode;for(i=0;i<e;i+=1)n.push(r(255&t.charCodeAt(i)));return n.join("")};if(r.open("GET",t,!e),r.overrideMimeType("text/plain; charset=x-user-defined"),!1===e&&(r.onload=function(){200===r.status?n(o(this.responseText)):n(void 0)}),r.send(null),e&&200===r.status)return o(r.responseText)}(t,e,n)}catch(t){}return r}(t,e,n)},t.loadImageFile=t.loadFile}(D.API),function(t){function e(){return(s.html2canvas?Promise.resolve(s.html2canvas):Promise.resolve().then(n.t.bind(null,"c0e9",7))).catch((function(t){return Promise.reject(new Error("Could not load html2canvas: "+t))})).then((function(t){return t.default?t.default:t}))}function r(){return(s.DOMPurify?Promise.resolve(s.DOMPurify):n.e("chunk-2d209639").then(n.t.bind(null,"a99d",7))).catch((function(t){return Promise.reject(new Error("Could not load dompurify: "+t))})).then((function(t){return t.default?t.default:t}))}var o=function(t){var e=i()(t);return"undefined"===e?"undefined":"string"===e||t instanceof String?"string":"number"===e||t instanceof Number?"number":"function"===e||t instanceof Function?"function":t&&t.constructor===Array?"array":t&&1===t.nodeType?"element":"object"===e?"object":"unknown"},a=function(t,e){var n=document.createElement(t);for(var r in e.className&&(n.className=e.className),e.innerHTML&&e.dompurify&&(n.innerHTML=e.dompurify.sanitize(e.innerHTML)),e.style)n.style[r]=e.style[r];return n},c=function t(e){var n=Object.assign(t.convert(Promise.resolve()),JSON.parse(JSON.stringify(t.template))),r=t.convert(Promise.resolve(),n);return(r=r.setProgress(1,t,1,[t])).set(e)};(c.prototype=Object.create(Promise.prototype)).constructor=c,c.convert=function(t,e){return t.__proto__=e||c.prototype,t},c.template={prop:{src:null,container:null,overlay:null,canvas:null,img:null,pdf:null,pageSize:null,callback:function(){}},progress:{val:0,state:null,n:0,stack:[]},opt:{filename:"file.pdf",margin:[0,0,0,0],enableLinks:!0,x:0,y:0,html2canvas:{},jsPDF:{},backgroundColor:"transparent"}},c.prototype.from=function(t,e){return this.then((function(){switch(e=e||function(t){switch(o(t)){case"string":return"string";case"element":return"canvas"===t.nodeName.toLowerCase()?"canvas":"element";default:return"unknown"}}(t)){case"string":return this.then(r).then((function(e){return this.set({src:a("div",{innerHTML:t,dompurify:e})})}));case"element":return this.set({src:t});case"canvas":return this.set({canvas:t});case"img":return this.set({img:t});default:return this.error("Unknown source type.")}}))},c.prototype.to=function(t){switch(t){case"container":return this.toContainer();case"canvas":return this.toCanvas();case"img":return this.toImg();case"pdf":return this.toPdf();default:return this.error("Invalid target.")}},c.prototype.toContainer=function(){return this.thenList([function(){return this.prop.src||this.error("Cannot duplicate - no source HTML.")},function(){return this.prop.pageSize||this.setPageSize()}]).then((function(){var t={position:"relative",display:"inline-block",width:("number"!=typeof this.opt.width||isNaN(this.opt.width)||"number"!=typeof this.opt.windowWidth||isNaN(this.opt.windowWidth)?Math.max(this.prop.src.clientWidth,this.prop.src.scrollWidth,this.prop.src.offsetWidth):this.opt.windowWidth)+"px",left:0,right:0,top:0,margin:"auto",backgroundColor:this.opt.backgroundColor},e=function t(e,n){for(var r=3===e.nodeType?document.createTextNode(e.nodeValue):e.cloneNode(!1),i=e.firstChild;i;i=i.nextSibling)!0!==n&&1===i.nodeType&&"SCRIPT"===i.nodeName||r.appendChild(t(i,n));return 1===e.nodeType&&("CANVAS"===e.nodeName?(r.width=e.width,r.height=e.height,r.getContext("2d").drawImage(e,0,0)):"TEXTAREA"!==e.nodeName&&"SELECT"!==e.nodeName||(r.value=e.value),r.addEventListener("load",(function(){r.scrollTop=e.scrollTop,r.scrollLeft=e.scrollLeft}),!0)),r}(this.prop.src,this.opt.html2canvas.javascriptEnabled);"BODY"===e.tagName&&(t.height=Math.max(document.body.scrollHeight,document.body.offsetHeight,document.documentElement.clientHeight,document.documentElement.scrollHeight,document.documentElement.offsetHeight)+"px"),this.prop.overlay=a("div",{className:"html2pdf__overlay",style:{position:"fixed",overflow:"hidden",zIndex:1e3,left:"-100000px",right:0,bottom:0,top:0}}),this.prop.container=a("div",{className:"html2pdf__container",style:t}),this.prop.container.appendChild(e),this.prop.container.firstChild.appendChild(a("div",{style:{clear:"both",border:"0 none transparent",margin:0,padding:0,height:0}})),this.prop.container.style.float="none",this.prop.overlay.appendChild(this.prop.container),document.body.appendChild(this.prop.overlay),this.prop.container.firstChild.style.position="relative",this.prop.container.height=Math.max(this.prop.container.firstChild.clientHeight,this.prop.container.firstChild.scrollHeight,this.prop.container.firstChild.offsetHeight)+"px"}))},c.prototype.toCanvas=function(){var t=[function(){return document.body.contains(this.prop.container)||this.toContainer()}];return this.thenList(t).then(e).then((function(t){var e=Object.assign({},this.opt.html2canvas);return delete e.onrendered,t(this.prop.container,e)})).then((function(t){(this.opt.html2canvas.onrendered||function(){})(t),this.prop.canvas=t,document.body.removeChild(this.prop.overlay)}))},c.prototype.toContext2d=function(){var t=[function(){return document.body.contains(this.prop.container)||this.toContainer()}];return this.thenList(t).then(e).then((function(t){var e=this.opt.jsPDF,n=this.opt.fontFaces,r="number"!=typeof this.opt.width||isNaN(this.opt.width)||"number"!=typeof this.opt.windowWidth||isNaN(this.opt.windowWidth)?1:this.opt.width/this.opt.windowWidth,i=Object.assign({async:!0,allowTaint:!0,scale:r,scrollX:this.opt.scrollX||0,scrollY:this.opt.scrollY||0,backgroundColor:"#ffffff",imageTimeout:15e3,logging:!0,proxy:null,removeContainer:!0,foreignObjectRendering:!1,useCORS:!1},this.opt.html2canvas);if(delete i.onrendered,e.context2d.autoPaging=void 0===this.opt.autoPaging||this.opt.autoPaging,e.context2d.posX=this.opt.x,e.context2d.posY=this.opt.y,e.context2d.margin=this.opt.margin,e.context2d.fontFaces=n,n)for(var o=0;o<n.length;++o){var s=n[o],a=s.src.find((function(t){return"truetype"===t.format}));a&&e.addFont(a.url,s.ref.name,s.ref.style)}return i.windowHeight=i.windowHeight||0,i.windowHeight=0==i.windowHeight?Math.max(this.prop.container.clientHeight,this.prop.container.scrollHeight,this.prop.container.offsetHeight):i.windowHeight,e.context2d.save(!0),t(this.prop.container,i)})).then((function(t){this.opt.jsPDF.context2d.restore(!0),(this.opt.html2canvas.onrendered||function(){})(t),this.prop.canvas=t,document.body.removeChild(this.prop.overlay)}))},c.prototype.toImg=function(){return this.thenList([function(){return this.prop.canvas||this.toCanvas()}]).then((function(){var t=this.prop.canvas.toDataURL("image/"+this.opt.image.type,this.opt.image.quality);this.prop.img=document.createElement("img"),this.prop.img.src=t}))},c.prototype.toPdf=function(){return this.thenList([function(){return this.toContext2d()}]).then((function(){this.prop.pdf=this.prop.pdf||this.opt.jsPDF}))},c.prototype.output=function(t,e,n){return"img"===(n=n||"pdf").toLowerCase()||"image"===n.toLowerCase()?this.outputImg(t,e):this.outputPdf(t,e)},c.prototype.outputPdf=function(t,e){return this.thenList([function(){return this.prop.pdf||this.toPdf()}]).then((function(){return this.prop.pdf.output(t,e)}))},c.prototype.outputImg=function(t){return this.thenList([function(){return this.prop.img||this.toImg()}]).then((function(){switch(t){case void 0:case"img":return this.prop.img;case"datauristring":case"dataurlstring":return this.prop.img.src;case"datauri":case"dataurl":return document.location.href=this.prop.img.src;default:throw'Image output type "'+t+'" is not supported.'}}))},c.prototype.save=function(t){return this.thenList([function(){return this.prop.pdf||this.toPdf()}]).set(t?{filename:t}:null).then((function(){this.prop.pdf.save(this.opt.filename)}))},c.prototype.doCallback=function(){return this.thenList([function(){return this.prop.pdf||this.toPdf()}]).then((function(){this.prop.callback(this.prop.pdf)}))},c.prototype.set=function(t){if("object"!==o(t))return this;var e=Object.keys(t||{}).map((function(e){if(e in c.template.prop)return function(){this.prop[e]=t[e]};switch(e){case"margin":return this.setMargin.bind(this,t.margin);case"jsPDF":return function(){return this.opt.jsPDF=t.jsPDF,this.setPageSize()};case"pageSize":return this.setPageSize.bind(this,t.pageSize);default:return function(){this.opt[e]=t[e]}}}),this);return this.then((function(){return this.thenList(e)}))},c.prototype.get=function(t,e){return this.then((function(){var n=t in c.template.prop?this.prop[t]:this.opt[t];return e?e(n):n}))},c.prototype.setMargin=function(t){return this.then((function(){switch(o(t)){case"number":t=[t,t,t,t];case"array":if(2===t.length&&(t=[t[0],t[1],t[0],t[1]]),4===t.length)break;default:return this.error("Invalid margin array.")}this.opt.margin=t})).then(this.setPageSize)},c.prototype.setPageSize=function(t){function e(t,e){return Math.floor(t*e/72*96)}return this.then((function(){(t=t||D.getPageSize(this.opt.jsPDF)).hasOwnProperty("inner")||(t.inner={width:t.width-this.opt.margin[1]-this.opt.margin[3],height:t.height-this.opt.margin[0]-this.opt.margin[2]},t.inner.px={width:e(t.inner.width,t.k),height:e(t.inner.height,t.k)},t.inner.ratio=t.inner.height/t.inner.width),this.prop.pageSize=t}))},c.prototype.setProgress=function(t,e,n,r){return null!=t&&(this.progress.val=t),null!=e&&(this.progress.state=e),null!=n&&(this.progress.n=n),null!=r&&(this.progress.stack=r),this.progress.ratio=this.progress.val/this.progress.state,this},c.prototype.updateProgress=function(t,e,n,r){return this.setProgress(t?this.progress.val+t:null,e||null,n?this.progress.n+n:null,r?this.progress.stack.concat(r):null)},c.prototype.then=function(t,e){var n=this;return this.thenCore(t,e,(function(t,e){return n.updateProgress(null,null,1,[t]),Promise.prototype.then.call(this,(function(e){return n.updateProgress(null,t),e})).then(t,e).then((function(t){return n.updateProgress(1),t}))}))},c.prototype.thenCore=function(t,e,n){n=n||Promise.prototype.then,t&&(t=t.bind(this)),e&&(e=e.bind(this));var r=-1!==Promise.toString().indexOf("[native code]")&&"Promise"===Promise.name?this:c.convert(Object.assign({},this),Promise.prototype),i=n.call(r,t,e);return c.convert(i,this.__proto__)},c.prototype.thenExternal=function(t,e){return Promise.prototype.then.call(this,t,e)},c.prototype.thenList=function(t){var e=this;return t.forEach((function(t){e=e.thenCore(t)})),e},c.prototype.catch=function(t){t&&(t=t.bind(this));var e=Promise.prototype.catch.call(this,t);return c.convert(e,this)},c.prototype.catchExternal=function(t){return Promise.prototype.catch.call(this,t)},c.prototype.error=function(t){return this.then((function(){throw new Error(t)}))},c.prototype.using=c.prototype.set,c.prototype.saveAs=c.prototype.save,c.prototype.export=c.prototype.output,c.prototype.run=c.prototype.then,D.getPageSize=function(t,e,n){if("object"===i()(t)){var r=t;t=r.orientation,e=r.unit||e,n=r.format||n}e=e||"mm",n=n||"a4",t=(""+(t||"P")).toLowerCase();var o,s=(""+n).toLowerCase(),a={a0:[2383.94,3370.39],a1:[1683.78,2383.94],a2:[1190.55,1683.78],a3:[841.89,1190.55],a4:[595.28,841.89],a5:[419.53,595.28],a6:[297.64,419.53],a7:[209.76,297.64],a8:[147.4,209.76],a9:[104.88,147.4],a10:[73.7,104.88],b0:[2834.65,4008.19],b1:[2004.09,2834.65],b2:[1417.32,2004.09],b3:[1000.63,1417.32],b4:[708.66,1000.63],b5:[498.9,708.66],b6:[354.33,498.9],b7:[249.45,354.33],b8:[175.75,249.45],b9:[124.72,175.75],b10:[87.87,124.72],c0:[2599.37,3676.54],c1:[1836.85,2599.37],c2:[1298.27,1836.85],c3:[918.43,1298.27],c4:[649.13,918.43],c5:[459.21,649.13],c6:[323.15,459.21],c7:[229.61,323.15],c8:[161.57,229.61],c9:[113.39,161.57],c10:[79.37,113.39],dl:[311.81,623.62],letter:[612,792],"government-letter":[576,756],legal:[612,1008],"junior-legal":[576,360],ledger:[1224,792],tabloid:[792,1224],"credit-card":[153,243]};switch(e){case"pt":o=1;break;case"mm":o=72/25.4;break;case"cm":o=72/2.54;break;case"in":o=72;break;case"px":o=.75;break;case"pc":case"em":o=12;break;case"ex":o=6;break;default:throw"Invalid unit: "+e}var c,u=0,l=0;if(a.hasOwnProperty(s))u=a[s][1]/o,l=a[s][0]/o;else try{u=n[1],l=n[0]}catch(h){throw new Error("Invalid format: "+n)}if("p"===t||"portrait"===t)t="p",l>u&&(c=l,l=u,u=c);else{if("l"!==t&&"landscape"!==t)throw"Invalid orientation: "+t;t="l",u>l&&(c=l,l=u,u=c)}return{width:l,height:u,unit:e,k:o,orientation:t}},t.html=function(t,e){(e=e||{}).callback=e.callback||function(){},e.html2canvas=e.html2canvas||{},e.html2canvas.canvas=e.html2canvas.canvas||this.canvas,e.jsPDF=e.jsPDF||this,e.fontFaces=e.fontFaces?e.fontFaces.map(Mt):null;var n=new c(e);return e.worker?n:n.from(t).doCallback()}}(D.API),D.API.addJS=function(t){return Gt=t,this.internal.events.subscribe("postPutResources",(function(){Vt=this.internal.newObject(),this.internal.out("<<"),this.internal.out("/Names [(EmbeddedJS) "+(Vt+1)+" 0 R]"),this.internal.out(">>"),this.internal.out("endobj"),Wt=this.internal.newObject(),this.internal.out("<<"),this.internal.out("/S /JavaScript"),this.internal.out("/JS ("+Gt+")"),this.internal.out(">>"),this.internal.out("endobj")})),this.internal.events.subscribe("putCatalog",(function(){void 0!==Vt&&void 0!==Wt&&this.internal.out("/Names <</JavaScript "+Vt+" 0 R>>")})),this
/**
 * @license
 * Copyright (c) 2014 Steven Spungin (TwelveTone LLC)  <EMAIL>
 *
 * Licensed under the MIT License.
 * http://opensource.org/licenses/mit-license
 */},function(t){var e;t.events.push(["postPutResources",function(){var t=this,n=/^(\d+) 0 obj$/;if(this.outline.root.children.length>0)for(var r=t.outline.render().split(/\r\n/),i=0;i<r.length;i++){var o=r[i],s=n.exec(o);if(null!=s){var a=s[1];t.internal.newObjectDeferredBegin(a,!1)}t.internal.write(o)}if(this.outline.createNamedDestinations){var c=this.internal.pages.length,u=[];for(i=0;i<c;i++){var l=t.internal.newObject();u.push(l);var h=t.internal.getPageInfo(i+1);t.internal.write("<< /D["+h.objId+" 0 R /XYZ null null null]>> endobj")}var f=t.internal.newObject();for(t.internal.write("<< /Names [ "),i=0;i<u.length;i++)t.internal.write("(page_"+(i+1)+")"+u[i]+" 0 R");t.internal.write(" ] >>","endobj"),e=t.internal.newObject(),t.internal.write("<< /Dests "+f+" 0 R"),t.internal.write(">>","endobj")}}]),t.events.push(["putCatalog",function(){this.outline.root.children.length>0&&(this.internal.write("/Outlines",this.outline.makeRef(this.outline.root)),this.outline.createNamedDestinations&&this.internal.write("/Names "+e+" 0 R"))}]),t.events.push(["initialized",function(){var t=this;t.outline={createNamedDestinations:!1,root:{children:[]}},t.outline.add=function(t,e,n){var r={title:e,options:n,children:[]};return null==t&&(t=this.root),t.children.push(r),r},t.outline.render=function(){return this.ctx={},this.ctx.val="",this.ctx.pdf=t,this.genIds_r(this.root),this.renderRoot(this.root),this.renderItems(this.root),this.ctx.val},t.outline.genIds_r=function(e){e.id=t.internal.newObjectDeferred();for(var n=0;n<e.children.length;n++)this.genIds_r(e.children[n])},t.outline.renderRoot=function(t){this.objStart(t),this.line("/Type /Outlines"),t.children.length>0&&(this.line("/First "+this.makeRef(t.children[0])),this.line("/Last "+this.makeRef(t.children[t.children.length-1]))),this.line("/Count "+this.count_r({count:0},t)),this.objEnd()},t.outline.renderItems=function(e){for(var n=this.ctx.pdf.internal.getVerticalCoordinateString,r=0;r<e.children.length;r++){var i=e.children[r];this.objStart(i),this.line("/Title "+this.makeString(i.title)),this.line("/Parent "+this.makeRef(e)),r>0&&this.line("/Prev "+this.makeRef(e.children[r-1])),r<e.children.length-1&&this.line("/Next "+this.makeRef(e.children[r+1])),i.children.length>0&&(this.line("/First "+this.makeRef(i.children[0])),this.line("/Last "+this.makeRef(i.children[i.children.length-1])));var o=this.count=this.count_r({count:0},i);if(o>0&&this.line("/Count "+o),i.options&&i.options.pageNumber){var s=t.internal.getPageInfo(i.options.pageNumber);this.line("/Dest ["+s.objId+" 0 R /XYZ 0 "+n(0)+" 0]")}this.objEnd()}for(var a=0;a<e.children.length;a++)this.renderItems(e.children[a])},t.outline.line=function(t){this.ctx.val+=t+"\r\n"},t.outline.makeRef=function(t){return t.id+" 0 R"},t.outline.makeString=function(e){return"("+t.internal.pdfEscape(e)+")"},t.outline.objStart=function(t){this.ctx.val+="\r\n"+t.id+" 0 obj\r\n<<\r\n"},t.outline.objEnd=function(){this.ctx.val+=">> \r\nendobj\r\n"},t.outline.count_r=function(t,e){for(var n=0;n<e.children.length;n++)t.count++,this.count_r(t,e.children[n]);return t.count}}])}(D.API),
/**
 * @license
 *
 * Licensed under the MIT License.
 * http://opensource.org/licenses/mit-license
 */
function(t){var e=[192,193,194,195,196,197,198,199];t.processJPEG=function(t,n,r,i,o,s){var a,c=this.decode.DCT_DECODE,u=null;if("string"==typeof t||this.__addimage__.isArrayBuffer(t)||this.__addimage__.isArrayBufferView(t)){switch(t=o||t,t=this.__addimage__.isArrayBuffer(t)?new Uint8Array(t):t,(a=function(t){for(var n,r=256*t.charCodeAt(4)+t.charCodeAt(5),i=t.length,o={width:0,height:0,numcomponents:1},s=4;s<i;s+=2){if(s+=r,-1!==e.indexOf(t.charCodeAt(s+1))){n=256*t.charCodeAt(s+5)+t.charCodeAt(s+6),o={width:256*t.charCodeAt(s+7)+t.charCodeAt(s+8),height:n,numcomponents:t.charCodeAt(s+9)};break}r=256*t.charCodeAt(s+2)+t.charCodeAt(s+3)}return o}(t=this.__addimage__.isArrayBufferView(t)?this.__addimage__.arrayBufferToBinaryString(t):t)).numcomponents){case 1:s=this.color_spaces.DEVICE_GRAY;break;case 4:s=this.color_spaces.DEVICE_CMYK;break;case 3:s=this.color_spaces.DEVICE_RGB}u={data:t,width:a.width,height:a.height,colorSpace:s,bitsPerComponent:8,filter:c,index:n,alias:r}}return u}}(D.API);var Jt,Xt,Kt,$t,Zt,Qt=function(){var t,e,n;function r(t){var e,n,r,i,o,s,a,c,u,l,h,f,d,p;for(this.data=t,this.pos=8,this.palette=[],this.imgData=[],this.transparency={},this.animation=null,this.text={},s=null;;){switch(e=this.readUInt32(),u=function(){var t,e;for(e=[],t=0;t<4;++t)e.push(String.fromCharCode(this.data[this.pos++]));return e}.call(this).join("")){case"IHDR":this.width=this.readUInt32(),this.height=this.readUInt32(),this.bits=this.data[this.pos++],this.colorType=this.data[this.pos++],this.compressionMethod=this.data[this.pos++],this.filterMethod=this.data[this.pos++],this.interlaceMethod=this.data[this.pos++];break;case"acTL":this.animation={numFrames:this.readUInt32(),numPlays:this.readUInt32()||1/0,frames:[]};break;case"PLTE":this.palette=this.read(e);break;case"fcTL":s&&this.animation.frames.push(s),this.pos+=4,s={width:this.readUInt32(),height:this.readUInt32(),xOffset:this.readUInt32(),yOffset:this.readUInt32()},o=this.readUInt16(),i=this.readUInt16()||100,s.delay=1e3*o/i,s.disposeOp=this.data[this.pos++],s.blendOp=this.data[this.pos++],s.data=[];break;case"IDAT":case"fdAT":for("fdAT"===u&&(this.pos+=4,e-=4),t=(null!=s?s.data:void 0)||this.imgData,f=0;0<=e?f<e:f>e;0<=e?++f:--f)t.push(this.data[this.pos++]);break;case"tRNS":switch(this.transparency={},this.colorType){case 3:if(r=this.palette.length/3,this.transparency.indexed=this.read(e),this.transparency.indexed.length>r)throw new Error("More transparent colors than palette size");if((l=r-this.transparency.indexed.length)>0)for(d=0;0<=l?d<l:d>l;0<=l?++d:--d)this.transparency.indexed.push(255);break;case 0:this.transparency.grayscale=this.read(e)[0];break;case 2:this.transparency.rgb=this.read(e)}break;case"tEXt":a=(h=this.read(e)).indexOf(0),c=String.fromCharCode.apply(String,h.slice(0,a)),this.text[c]=String.fromCharCode.apply(String,h.slice(a+1));break;case"IEND":return s&&this.animation.frames.push(s),this.colors=function(){switch(this.colorType){case 0:case 3:case 4:return 1;case 2:case 6:return 3}}.call(this),this.hasAlphaChannel=4===(p=this.colorType)||6===p,n=this.colors+(this.hasAlphaChannel?1:0),this.pixelBitlength=this.bits*n,this.colorSpace=function(){switch(this.colors){case 1:return"DeviceGray";case 3:return"DeviceRGB"}}.call(this),void(this.imgData=new Uint8Array(this.imgData));default:this.pos+=e}if(this.pos+=4,this.pos>this.data.length)throw new Error("Incomplete or corrupt PNG file")}}r.prototype.read=function(t){var e,n;for(n=[],e=0;0<=t?e<t:e>t;0<=t?++e:--e)n.push(this.data[this.pos++]);return n},r.prototype.readUInt32=function(){return this.data[this.pos++]<<24|this.data[this.pos++]<<16|this.data[this.pos++]<<8|this.data[this.pos++]},r.prototype.readUInt16=function(){return this.data[this.pos++]<<8|this.data[this.pos++]},r.prototype.decodePixels=function(t){var e=this.pixelBitlength/8,n=new Uint8Array(this.width*this.height*e),r=0,i=this;if(null==t&&(t=this.imgData),0===t.length)return new Uint8Array(0);function s(o,s,a,c){var u,l,h,f,d,p,m,g,v,b,y,w,j,_,x,N,A,L,S,P,k,I=Math.ceil((i.width-o)/a),C=Math.ceil((i.height-s)/c),F=i.width==I&&i.height==C;for(_=e*I,w=F?n:new Uint8Array(_*C),p=t.length,j=0,l=0;j<C&&r<p;){switch(t[r++]){case 0:for(f=A=0;A<_;f=A+=1)w[l++]=t[r++];break;case 1:for(f=L=0;L<_;f=L+=1)u=t[r++],d=f<e?0:w[l-e],w[l++]=(u+d)%256;break;case 2:for(f=S=0;S<_;f=S+=1)u=t[r++],h=(f-f%e)/e,x=j&&w[(j-1)*_+h*e+f%e],w[l++]=(x+u)%256;break;case 3:for(f=P=0;P<_;f=P+=1)u=t[r++],h=(f-f%e)/e,d=f<e?0:w[l-e],x=j&&w[(j-1)*_+h*e+f%e],w[l++]=(u+Math.floor((d+x)/2))%256;break;case 4:for(f=k=0;k<_;f=k+=1)u=t[r++],h=(f-f%e)/e,d=f<e?0:w[l-e],0===j?x=N=0:(x=w[(j-1)*_+h*e+f%e],N=h&&w[(j-1)*_+(h-1)*e+f%e]),m=d+x-N,g=Math.abs(m-d),b=Math.abs(m-x),y=Math.abs(m-N),v=g<=b&&g<=y?d:b<=y?x:N,w[l++]=(u+v)%256;break;default:throw new Error("Invalid filter algorithm: "+t[r-1])}if(!F){var O=((s+j*c)*i.width+o)*e,E=j*_;for(f=0;f<I;f+=1){for(var M=0;M<e;M+=1)n[O++]=w[E++];O+=(a-1)*e}}j++}}return t=Object(o["a"])(t),1==i.interlaceMethod?(s(0,0,8,8),s(4,0,8,8),s(0,4,4,8),s(2,0,4,4),s(0,2,2,4),s(1,0,2,2),s(0,1,1,2)):s(0,0,1,1),n},r.prototype.decodePalette=function(){var t,e,n,r,i,o,s,a,c;for(n=this.palette,o=this.transparency.indexed||[],i=new Uint8Array((o.length||0)+n.length),r=0,t=0,e=s=0,a=n.length;s<a;e=s+=3)i[r++]=n[e],i[r++]=n[e+1],i[r++]=n[e+2],i[r++]=null!=(c=o[t++])?c:255;return i},r.prototype.copyToImageData=function(t,e){var n,r,i,o,s,a,c,u,l,h,f;if(r=this.colors,l=null,n=this.hasAlphaChannel,this.palette.length&&(l=null!=(f=this._decodedPalette)?f:this._decodedPalette=this.decodePalette(),r=4,n=!0),u=(i=t.data||t).length,s=l||e,o=a=0,1===r)for(;o<u;)c=l?4*e[o/4]:a,h=s[c++],i[o++]=h,i[o++]=h,i[o++]=h,i[o++]=n?s[c++]:255,a=c;else for(;o<u;)c=l?4*e[o/4]:a,i[o++]=s[c++],i[o++]=s[c++],i[o++]=s[c++],i[o++]=n?s[c++]:255,a=c},r.prototype.decode=function(){var t;return t=new Uint8Array(this.width*this.height*4),this.copyToImageData(t,this.decodePixels()),t};var i=function(){if("[object Window]"===Object.prototype.toString.call(s)){try{e=s.document.createElement("canvas"),n=e.getContext("2d")}catch(t){return!1}return!0}return!1};return i(),t=function(t){var r;if(!0===i())return n.width=t.width,n.height=t.height,n.clearRect(0,0,t.width,t.height),n.putImageData(t,0,0),(r=new Image).src=e.toDataURL(),r;throw new Error("This method requires a Browser with Canvas-capability.")},r.prototype.decodeFrames=function(e){var n,r,i,o,s,a,c,u;if(this.animation){for(u=[],r=s=0,a=(c=this.animation.frames).length;s<a;r=++s)n=c[r],i=e.createImageData(n.width,n.height),o=this.decodePixels(new Uint8Array(n.data)),this.copyToImageData(i,o),n.imageData=i,u.push(n.image=t(i));return u}},r.prototype.renderFrame=function(t,e){var n,r,i;return n=(r=this.animation.frames)[e],i=r[e-1],0===e&&t.clearRect(0,0,this.width,this.height),1===(null!=i?i.disposeOp:void 0)?t.clearRect(i.xOffset,i.yOffset,i.width,i.height):2===(null!=i?i.disposeOp:void 0)&&t.putImageData(i.imageData,i.xOffset,i.yOffset),0===n.blendOp&&t.clearRect(n.xOffset,n.yOffset,n.width,n.height),t.drawImage(n.image,n.xOffset,n.yOffset)},r.prototype.animate=function(t){var e,n,r,i,o,s,a=this;return n=0,s=this.animation,i=s.numFrames,r=s.frames,o=s.numPlays,(e=function(){var s,c;if(s=n++%i,c=r[s],a.renderFrame(t,s),i>1&&n/i<o)return a.animation._timeout=setTimeout(e,c.delay)})()},r.prototype.stopAnimation=function(){var t;return clearTimeout(null!=(t=this.animation)?t._timeout:void 0)},r.prototype.render=function(t){var e,n;return t._png&&t._png.stopAnimation(),t._png=this,t.width=this.width,t.height=this.height,e=t.getContext("2d"),this.animation?(this.decodeFrames(e),this.animate(e)):(n=e.createImageData(this.width,this.height),this.copyToImageData(n,this.decodePixels()),e.putImageData(n,0,0))},r}();
/**
 * @license
 *
 * Copyright (c) 2014 James Robb, https://github.com/jamesbrobb
 *
 * Permission is hereby granted, free of charge, to any person obtaining
 * a copy of this software and associated documentation files (the
 * "Software"), to deal in the Software without restriction, including
 * without limitation the rights to use, copy, modify, merge, publish,
 * distribute, sublicense, and/or sell copies of the Software, and to
 * permit persons to whom the Software is furnished to do so, subject to
 * the following conditions:
 *
 * The above copyright notice and this permission notice shall be
 * included in all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
 * EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
 * NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE
 * LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION
 * OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION
 * WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
 * ====================================================================
 */
/**
 * @license
 * (c) Dean McNamee <<EMAIL>>, 2013.
 *
 * https://github.com/deanm/omggif
 *
 * Permission is hereby granted, free of charge, to any person obtaining a copy
 * of this software and associated documentation files (the "Software"), to
 * deal in the Software without restriction, including without limitation the
 * rights to use, copy, modify, merge, publish, distribute, sublicense, and/or
 * sell copies of the Software, and to permit persons to whom the Software is
 * furnished to do so, subject to the following conditions:
 *
 * The above copyright notice and this permission notice shall be included in
 * all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR
 * IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY,
 * FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE
 * AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER
 * LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING
 * FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS
 * IN THE SOFTWARE.
 *
 * omggif is a JavaScript implementation of a GIF 89a encoder and decoder,
 * including animation and compression.  It does not rely on any specific
 * underlying system, so should run in the browser, Node, or Plask.
 */function te(t){var e=0;if(71!==t[e++]||73!==t[e++]||70!==t[e++]||56!==t[e++]||56!=(t[e++]+1&253)||97!==t[e++])throw new Error("Invalid GIF 87a/89a header.");var n=t[e++]|t[e++]<<8,r=t[e++]|t[e++]<<8,i=t[e++],o=i>>7,s=1<<1+(7&i);t[e++],t[e++];var a=null,c=null;o&&(a=e,c=s,e+=3*s);var u=!0,l=[],h=0,f=null,d=0,p=null;for(this.width=n,this.height=r;u&&e<t.length;)switch(t[e++]){case 33:switch(t[e++]){case 255:if(11!==t[e]||78==t[e+1]&&69==t[e+2]&&84==t[e+3]&&83==t[e+4]&&67==t[e+5]&&65==t[e+6]&&80==t[e+7]&&69==t[e+8]&&50==t[e+9]&&46==t[e+10]&&48==t[e+11]&&3==t[e+12]&&1==t[e+13]&&0==t[e+16])e+=14,p=t[e++]|t[e++]<<8,e++;else for(e+=12;;){if(!((S=t[e++])>=0))throw Error("Invalid block size");if(0===S)break;e+=S}break;case 249:if(4!==t[e++]||0!==t[e+4])throw new Error("Invalid graphics extension block.");var m=t[e++];h=t[e++]|t[e++]<<8,f=t[e++],0==(1&m)&&(f=null),d=m>>2&7,e++;break;case 254:for(;;){if(!((S=t[e++])>=0))throw Error("Invalid block size");if(0===S)break;e+=S}break;default:throw new Error("Unknown graphic control label: 0x"+t[e-1].toString(16))}break;case 44:var g=t[e++]|t[e++]<<8,v=t[e++]|t[e++]<<8,b=t[e++]|t[e++]<<8,y=t[e++]|t[e++]<<8,w=t[e++],j=w>>6&1,_=1<<1+(7&w),x=a,N=c,A=!1;w>>7&&(A=!0,x=e,N=_,e+=3*_);var L=e;for(e++;;){var S;if(!((S=t[e++])>=0))throw Error("Invalid block size");if(0===S)break;e+=S}l.push({x:g,y:v,width:b,height:y,has_local_palette:A,palette_offset:x,palette_size:N,data_offset:L,data_length:e-L,transparent_index:f,interlaced:!!j,delay:h,disposal:d});break;case 59:u=!1;break;default:throw new Error("Unknown gif block: 0x"+t[e-1].toString(16))}this.numFrames=function(){return l.length},this.loopCount=function(){return p},this.frameInfo=function(t){if(t<0||t>=l.length)throw new Error("Frame index out of range.");return l[t]},this.decodeAndBlitFrameBGRA=function(e,r){var i=this.frameInfo(e),o=i.width*i.height,s=new Uint8Array(o);ee(t,i.data_offset,s,o);var a=i.palette_offset,c=i.transparent_index;null===c&&(c=256);var u=i.width,l=n-u,h=u,f=4*(i.y*n+i.x),d=4*((i.y+i.height)*n+i.x),p=f,m=4*l;!0===i.interlaced&&(m+=4*n*7);for(var g=8,v=0,b=s.length;v<b;++v){var y=s[v];if(0===h&&(h=u,(p+=m)>=d&&(m=4*l+4*n*(g-1),p=f+(u+l)*(g<<1),g>>=1)),y===c)p+=4;else{var w=t[a+3*y],j=t[a+3*y+1],_=t[a+3*y+2];r[p++]=_,r[p++]=j,r[p++]=w,r[p++]=255}--h}},this.decodeAndBlitFrameRGBA=function(e,r){var i=this.frameInfo(e),o=i.width*i.height,s=new Uint8Array(o);ee(t,i.data_offset,s,o);var a=i.palette_offset,c=i.transparent_index;null===c&&(c=256);var u=i.width,l=n-u,h=u,f=4*(i.y*n+i.x),d=4*((i.y+i.height)*n+i.x),p=f,m=4*l;!0===i.interlaced&&(m+=4*n*7);for(var g=8,v=0,b=s.length;v<b;++v){var y=s[v];if(0===h&&(h=u,(p+=m)>=d&&(m=4*l+4*n*(g-1),p=f+(u+l)*(g<<1),g>>=1)),y===c)p+=4;else{var w=t[a+3*y],j=t[a+3*y+1],_=t[a+3*y+2];r[p++]=w,r[p++]=j,r[p++]=_,r[p++]=255}--h}}}function ee(t,e,n,r){for(var i=t[e++],o=1<<i,s=o+1,a=s+1,u=i+1,l=(1<<u)-1,h=0,f=0,d=0,p=t[e++],m=new Int32Array(4096),g=null;;){for(;h<16&&0!==p;)f|=t[e++]<<h,h+=8,1===p?p=t[e++]:--p;if(h<u)break;var v=f&l;if(f>>=u,h-=u,v!==o){if(v===s)break;for(var b=v<a?v:g,y=0,w=b;w>o;)w=m[w]>>8,++y;var j=w;if(d+y+(b!==v?1:0)>r)return void c.log("Warning, gif stream longer than expected.");n[d++]=j;var _=d+=y;for(b!==v&&(n[d++]=j),w=b;y--;)w=m[w],n[--_]=255&w,w>>=8;null!==g&&a<4096&&(m[a++]=g<<8|j,a>=l+1&&u<12&&(++u,l=l<<1|1)),g=v}else a=s+1,l=(1<<(u=i+1))-1,g=null}return d!==r&&c.log("Warning, gif stream shorter than expected."),n}
/**
 * @license
  Copyright (c) 2008, Adobe Systems Incorporated
  All rights reserved.

  Redistribution and use in source and binary forms, with or without 
  modification, are permitted provided that the following conditions are
  met:

  * Redistributions of source code must retain the above copyright notice, 
    this list of conditions and the following disclaimer.
  
  * Redistributions in binary form must reproduce the above copyright
    notice, this list of conditions and the following disclaimer in the 
    documentation and/or other materials provided with the distribution.
  
  * Neither the name of Adobe Systems Incorporated nor the names of its 
    contributors may be used to endorse or promote products derived from 
    this software without specific prior written permission.

  THIS SOFTWARE IS PROVIDED BY THE COPYRIGHT HOLDERS AND CONTRIBUTORS "AS
  IS" AND ANY EXPRESS OR IMPLIED WARRANTIES, INCLUDING, BUT NOT LIMITED TO,
  THE IMPLIED WARRANTIES OF MERCHANTABILITY AND FITNESS FOR A PARTICULAR
  PURPOSE ARE DISCLAIMED. IN NO EVENT SHALL THE COPYRIGHT OWNER OR 
  CONTRIBUTORS BE LIABLE FOR ANY DIRECT, INDIRECT, INCIDENTAL, SPECIAL,
  EXEMPLARY, OR CONSEQUENTIAL DAMAGES (INCLUDING, BUT NOT LIMITED TO,
  PROCUREMENT OF SUBSTITUTE GOODS OR SERVICES; LOSS OF USE, DATA, OR
  PROFITS; OR BUSINESS INTERRUPTION) HOWEVER CAUSED AND ON ANY THEORY OF
  LIABILITY, WHETHER IN CONTRACT, STRICT LIABILITY, OR TORT (INCLUDING
  NEGLIGENCE OR OTHERWISE) ARISING IN ANY WAY OUT OF THE USE OF THIS
  SOFTWARE, EVEN IF ADVISED OF THE POSSIBILITY OF SUCH DAMAGE.
*/function ne(t){var e,n,r,i,o,s=Math.floor,a=new Array(64),c=new Array(64),u=new Array(64),l=new Array(64),h=new Array(65535),f=new Array(65535),d=new Array(64),p=new Array(64),m=[],g=0,v=7,b=new Array(64),y=new Array(64),w=new Array(64),j=new Array(256),_=new Array(2048),x=[0,1,5,6,14,15,27,28,2,4,7,13,16,26,29,42,3,8,12,17,25,30,41,43,9,11,18,24,31,40,44,53,10,19,23,32,39,45,52,54,20,22,33,38,46,51,55,60,21,34,37,47,50,56,59,61,35,36,48,49,57,58,62,63],N=[0,0,1,5,1,1,1,1,1,1,0,0,0,0,0,0,0],A=[0,1,2,3,4,5,6,7,8,9,10,11],L=[0,0,2,1,3,3,2,4,3,5,5,4,4,0,0,1,125],S=[1,2,3,0,4,17,5,18,33,49,65,6,19,81,97,7,34,113,20,50,129,145,161,8,35,66,177,193,21,82,209,240,36,51,98,114,130,9,10,22,23,24,25,26,37,38,39,40,41,42,52,53,54,55,56,57,58,67,68,69,70,71,72,73,74,83,84,85,86,87,88,89,90,99,100,101,102,103,104,105,106,115,116,117,118,119,120,121,122,131,132,133,134,135,136,137,138,146,147,148,149,150,151,152,153,154,162,163,164,165,166,167,168,169,170,178,179,180,181,182,183,184,185,186,194,195,196,197,198,199,200,201,202,210,211,212,213,214,215,216,217,218,225,226,227,228,229,230,231,232,233,234,241,242,243,244,245,246,247,248,249,250],P=[0,0,3,1,1,1,1,1,1,1,1,1,0,0,0,0,0],k=[0,1,2,3,4,5,6,7,8,9,10,11],I=[0,0,2,1,2,4,4,3,4,7,5,4,4,0,1,2,119],C=[0,1,2,3,17,4,5,33,49,6,18,65,81,7,97,113,19,34,50,129,8,20,66,145,161,177,193,9,35,51,82,240,21,98,114,209,10,22,36,52,225,37,241,23,24,25,26,38,39,40,41,42,53,54,55,56,57,58,67,68,69,70,71,72,73,74,83,84,85,86,87,88,89,90,99,100,101,102,103,104,105,106,115,116,117,118,119,120,121,122,130,131,132,133,134,135,136,137,138,146,147,148,149,150,151,152,153,154,162,163,164,165,166,167,168,169,170,178,179,180,181,182,183,184,185,186,194,195,196,197,198,199,200,201,202,210,211,212,213,214,215,216,217,218,226,227,228,229,230,231,232,233,234,242,243,244,245,246,247,248,249,250];function F(t,e){for(var n=0,r=0,i=new Array,o=1;o<=16;o++){for(var s=1;s<=t[o];s++)i[e[r]]=[],i[e[r]][0]=n,i[e[r]][1]=o,r++,n++;n*=2}return i}function O(t){for(var e=t[0],n=t[1]-1;n>=0;)e&1<<n&&(g|=1<<v),n--,--v<0&&(255==g?(E(255),E(0)):E(g),v=7,g=0)}function E(t){m.push(t)}function M(t){E(t>>8&255),E(255&t)}function B(t,e,n,r,i){for(var o,s=i[0],a=i[240],c=function(t,e){var n,r,i,o,s,a,c,u,l,h,f=0;for(l=0;l<8;++l){n=t[f],r=t[f+1],i=t[f+2],o=t[f+3],s=t[f+4],a=t[f+5],c=t[f+6];var p=n+(u=t[f+7]),m=n-u,g=r+c,v=r-c,b=i+a,y=i-a,w=o+s,j=o-s,_=p+w,x=p-w,N=g+b,A=g-b;t[f]=_+N,t[f+4]=_-N;var L=.707106781*(A+x);t[f+2]=x+L,t[f+6]=x-L;var S=.382683433*((_=j+y)-(A=v+m)),P=.5411961*_+S,k=1.306562965*A+S,I=.707106781*(N=y+v),C=m+I,F=m-I;t[f+5]=F+P,t[f+3]=F-P,t[f+1]=C+k,t[f+7]=C-k,f+=8}for(f=0,l=0;l<8;++l){n=t[f],r=t[f+8],i=t[f+16],o=t[f+24],s=t[f+32],a=t[f+40],c=t[f+48];var O=n+(u=t[f+56]),E=n-u,M=r+c,B=r-c,T=i+a,q=i-a,D=o+s,R=o-s,z=O+D,U=O-D,H=M+T,V=M-T;t[f]=z+H,t[f+32]=z-H;var W=.707106781*(V+U);t[f+16]=U+W,t[f+48]=U-W;var G=.382683433*((z=R+q)-(V=B+E)),Y=.5411961*z+G,J=1.306562965*V+G,X=.707106781*(H=q+B),K=E+X,$=E-X;t[f+40]=$+Y,t[f+24]=$-Y,t[f+8]=K+J,t[f+56]=K-J,f++}for(l=0;l<64;++l)h=t[l]*e[l],d[l]=h>0?h+.5|0:h-.5|0;return d}(t,e),u=0;u<64;++u)p[x[u]]=c[u];var l=p[0]-n;n=p[0],0==l?O(r[0]):(O(r[f[o=32767+l]]),O(h[o]));for(var m=63;m>0&&0==p[m];)m--;if(0==m)return O(s),n;for(var g,v=1;v<=m;){for(var b=v;0==p[v]&&v<=m;)++v;var y=v-b;if(y>=16){g=y>>4;for(var w=1;w<=g;++w)O(a);y&=15}o=32767+p[v],O(i[(y<<4)+f[o]]),O(h[o]),v++}return 63!=m&&O(s),n}function T(t){t=Math.min(Math.max(t,1),100),o!=t&&(function(t){for(var e=[16,11,10,16,24,40,51,61,12,12,14,19,26,58,60,55,14,13,16,24,40,57,69,56,14,17,22,29,51,87,80,62,18,22,37,56,68,109,103,77,24,35,55,64,81,104,113,92,49,64,78,87,103,121,120,101,72,92,95,98,112,100,103,99],n=0;n<64;n++){var r=s((e[n]*t+50)/100);r=Math.min(Math.max(r,1),255),a[x[n]]=r}for(var i=[17,18,24,47,99,99,99,99,18,21,26,66,99,99,99,99,24,26,56,99,99,99,99,99,47,66,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99,99],o=0;o<64;o++){var h=s((i[o]*t+50)/100);h=Math.min(Math.max(h,1),255),c[x[o]]=h}for(var f=[1,1.387039845,1.306562965,1.175875602,1,.785694958,.5411961,.275899379],d=0,p=0;p<8;p++)for(var m=0;m<8;m++)u[d]=1/(a[x[d]]*f[p]*f[m]*8),l[d]=1/(c[x[d]]*f[p]*f[m]*8),d++}(t<50?Math.floor(5e3/t):Math.floor(200-2*t)),o=t)}this.encode=function(t,o){o&&T(o),m=new Array,g=0,v=7,M(65496),M(65504),M(16),E(74),E(70),E(73),E(70),E(0),E(1),E(1),E(0),M(1),M(1),E(0),E(0),function(){M(65499),M(132),E(0);for(var t=0;t<64;t++)E(a[t]);E(1);for(var e=0;e<64;e++)E(c[e])}(),function(t,e){M(65472),M(17),E(8),M(e),M(t),E(3),E(1),E(17),E(0),E(2),E(17),E(1),E(3),E(17),E(1)}(t.width,t.height),function(){M(65476),M(418),E(0);for(var t=0;t<16;t++)E(N[t+1]);for(var e=0;e<=11;e++)E(A[e]);E(16);for(var n=0;n<16;n++)E(L[n+1]);for(var r=0;r<=161;r++)E(S[r]);E(1);for(var i=0;i<16;i++)E(P[i+1]);for(var o=0;o<=11;o++)E(k[o]);E(17);for(var s=0;s<16;s++)E(I[s+1]);for(var a=0;a<=161;a++)E(C[a])}(),M(65498),M(12),E(3),E(1),E(0),E(2),E(17),E(3),E(17),E(0),E(63),E(0);var s=0,h=0,f=0;g=0,v=7,this.encode.displayName="_encode_";for(var d,p,j,x,F,q,D,R,z,U=t.data,H=t.width,V=t.height,W=4*H,G=0;G<V;){for(d=0;d<W;){for(F=W*G+d,D=-1,R=0,z=0;z<64;z++)q=F+(R=z>>3)*W+(D=4*(7&z)),G+R>=V&&(q-=W*(G+1+R-V)),d+D>=W&&(q-=d+D-W+4),p=U[q++],j=U[q++],x=U[q++],b[z]=(_[p]+_[j+256>>0]+_[x+512>>0]>>16)-128,y[z]=(_[p+768>>0]+_[j+1024>>0]+_[x+1280>>0]>>16)-128,w[z]=(_[p+1280>>0]+_[j+1536>>0]+_[x+1792>>0]>>16)-128;s=B(b,u,s,e,r),h=B(y,l,h,n,i),f=B(w,l,f,n,i),d+=32}G+=8}if(v>=0){var Y=[];Y[1]=v+1,Y[0]=(1<<v+1)-1,O(Y)}return M(65497),new Uint8Array(m)},t=t||50,function(){for(var t=String.fromCharCode,e=0;e<256;e++)j[e]=t(e)}(),e=F(N,A),n=F(P,k),r=F(L,S),i=F(I,C),function(){for(var t=1,e=2,n=1;n<=15;n++){for(var r=t;r<e;r++)f[32767+r]=n,h[32767+r]=[],h[32767+r][1]=n,h[32767+r][0]=r;for(var i=-(e-1);i<=-t;i++)f[32767+i]=n,h[32767+i]=[],h[32767+i][1]=n,h[32767+i][0]=e-1+i;t<<=1,e<<=1}}(),function(){for(var t=0;t<256;t++)_[t]=19595*t,_[t+256>>0]=38470*t,_[t+512>>0]=7471*t+32768,_[t+768>>0]=-11059*t,_[t+1024>>0]=-21709*t,_[t+1280>>0]=32768*t+8421375,_[t+1536>>0]=-27439*t,_[t+1792>>0]=-5329*t}(),T(t)}
/**
 * @license
 * Copyright (c) 2017 Aras Abbasi
 *
 * Licensed under the MIT License.
 * http://opensource.org/licenses/mit-license
 */function re(t,e){if(this.pos=0,this.buffer=t,this.datav=new DataView(t.buffer),this.is_with_alpha=!!e,this.bottom_up=!0,this.flag=String.fromCharCode(this.buffer[0])+String.fromCharCode(this.buffer[1]),this.pos+=2,-1===["BM","BA","CI","CP","IC","PT"].indexOf(this.flag))throw new Error("Invalid BMP File");this.parseHeader(),this.parseBGR()}function ie(t){function e(t){if(!t)throw Error("assert :P")}function n(t,e,n){for(var r=0;4>r;r++)if(t[e+r]!=n.charCodeAt(r))return!0;return!1}function r(t,e,n,r,i){for(var o=0;o<i;o++)t[e+o]=n[r+o]}function i(t,e,n,r){for(var i=0;i<r;i++)t[e+i]=n}function o(t){return new Int32Array(t)}function s(t,e){for(var n=[],r=0;r<t;r++)n.push(new e);return n}function a(t,e){var n=[];return function t(n,r,i){for(var o=i[r],s=0;s<o&&(n.push(i.length>r+1?[]:new e),!(i.length<r+1));s++)t(n[s],r+1,i)}(n,0,t),n}var c=function(){var t=this;function c(t,e){for(var n=1<<e-1>>>0;t&n;)n>>>=1;return n?(t&n-1)+n:t}function u(t,n,r,i,o){e(!(i%r));do{t[n+(i-=r)]=o}while(0<i)}function l(t,n,r,i,s){if(e(2328>=s),512>=s)var a=o(512);else if(null==(a=o(s)))return 0;return function(t,n,r,i,s,a){var l,f,d=n,p=1<<r,m=o(16),g=o(16);for(e(0!=s),e(null!=i),e(null!=t),e(0<r),f=0;f<s;++f){if(15<i[f])return 0;++m[i[f]]}if(m[0]==s)return 0;for(g[1]=0,l=1;15>l;++l){if(m[l]>1<<l)return 0;g[l+1]=g[l]+m[l]}for(f=0;f<s;++f)l=i[f],0<i[f]&&(a[g[l]++]=f);if(1==g[15])return(i=new h).g=0,i.value=a[0],u(t,d,1,p,i),p;var v,b=-1,y=p-1,w=0,j=1,_=1,x=1<<r;for(f=0,l=1,s=2;l<=r;++l,s<<=1){if(j+=_<<=1,0>(_-=m[l]))return 0;for(;0<m[l];--m[l])(i=new h).g=l,i.value=a[f++],u(t,d+w,s,x,i),w=c(w,l)}for(l=r+1,s=2;15>=l;++l,s<<=1){if(j+=_<<=1,0>(_-=m[l]))return 0;for(;0<m[l];--m[l]){if(i=new h,(w&y)!=b){for(d+=x,v=1<<(b=l)-r;15>b&&!(0>=(v-=m[b]));)++b,v<<=1;p+=x=1<<(v=b-r),t[n+(b=w&y)].g=v+r,t[n+b].value=d-n-b}i.g=l-r,i.value=a[f++],u(t,d+(w>>r),s,x,i),w=c(w,l)}}return j!=2*g[15]-1?0:p}(t,n,r,i,s,a)}function h(){this.value=this.g=0}function f(){this.value=this.g=0}function d(){this.G=s(5,h),this.H=o(5),this.jc=this.Qb=this.qb=this.nd=0,this.pd=s(Dn,f)}function p(t,n,r,i){e(null!=t),e(null!=n),e(2147483648>i),t.Ca=254,t.I=0,t.b=-8,t.Ka=0,t.oa=n,t.pa=r,t.Jd=n,t.Yc=r+i,t.Zc=4<=i?r+i-4+1:r,L(t)}function m(t,e){for(var n=0;0<e--;)n|=P(t,128)<<e;return n}function g(t,e){var n=m(t,e);return S(t)?-n:n}function v(t,n,r,i){var o,s=0;for(e(null!=t),e(null!=n),e(4294967288>i),t.Sb=i,t.Ra=0,t.u=0,t.h=0,4<i&&(i=4),o=0;o<i;++o)s+=n[r+o]<<8*o;t.Ra=s,t.bb=i,t.oa=n,t.pa=r}function b(t){for(;8<=t.u&&t.bb<t.Sb;)t.Ra>>>=8,t.Ra+=t.oa[t.pa+t.bb]<<Un-8>>>0,++t.bb,t.u-=8;x(t)&&(t.h=1,t.u=0)}function y(t,n){if(e(0<=n),!t.h&&n<=zn){var r=_(t)&Rn[n];return t.u+=n,b(t),r}return t.h=1,t.u=0}function w(){this.b=this.Ca=this.I=0,this.oa=[],this.pa=0,this.Jd=[],this.Yc=0,this.Zc=[],this.Ka=0}function j(){this.Ra=0,this.oa=[],this.h=this.u=this.bb=this.Sb=this.pa=0}function _(t){return t.Ra>>>(t.u&Un-1)>>>0}function x(t){return e(t.bb<=t.Sb),t.h||t.bb==t.Sb&&t.u>Un}function N(t,e){t.u=e,t.h=x(t)}function A(t){t.u>=Hn&&(e(t.u>=Hn),b(t))}function L(t){e(null!=t&&null!=t.oa),t.pa<t.Zc?(t.I=(t.oa[t.pa++]|t.I<<8)>>>0,t.b+=8):(e(null!=t&&null!=t.oa),t.pa<t.Yc?(t.b+=8,t.I=t.oa[t.pa++]|t.I<<8):t.Ka?t.b=0:(t.I<<=8,t.b+=8,t.Ka=1))}function S(t){return m(t,1)}function P(t,e){var n=t.Ca;0>t.b&&L(t);var r=t.b,i=n*e>>>8,o=(t.I>>>r>i)+0;for(o?(n-=i,t.I-=i+1<<r>>>0):n=i+1,r=n,i=0;256<=r;)i+=8,r>>=8;return r=7^i+Vn[r],t.b-=r,t.Ca=(n<<r)-1,o}function k(t,e,n){t[e+0]=n>>24&255,t[e+1]=n>>16&255,t[e+2]=n>>8&255,t[e+3]=n>>0&255}function I(t,e){return t[e+0]<<0|t[e+1]<<8}function C(t,e){return I(t,e)|t[e+2]<<16}function F(t,e){return I(t,e)|I(t,e+2)<<16}function O(t,n){var r=1<<n;return e(null!=t),e(0<n),t.X=o(r),null==t.X?0:(t.Mb=32-n,t.Xa=n,1)}function E(t,n){e(null!=t),e(null!=n),e(t.Xa==n.Xa),r(n.X,0,t.X,0,1<<n.Xa)}function M(){this.X=[],this.Xa=this.Mb=0}function B(t,n,r,i){e(null!=r),e(null!=i);var o=r[0],s=i[0];return 0==o&&(o=(t*s+n/2)/n),0==s&&(s=(n*o+t/2)/t),0>=o||0>=s?0:(r[0]=o,i[0]=s,1)}function T(t,e){return t+(1<<e)-1>>>e}function q(t,e){return((4278255360&t)+(4278255360&e)>>>0&4278255360)+((16711935&t)+(16711935&e)>>>0&16711935)>>>0}function D(e,n){t[n]=function(n,r,i,o,s,a,c){var u;for(u=0;u<s;++u){var l=t[e](a[c+u-1],i,o+u);a[c+u]=q(n[r+u],l)}}}function R(){this.ud=this.hd=this.jd=0}function z(t,e){return((4278124286&(t^e))>>>1)+(t&e)>>>0}function U(t){return 0<=t&&256>t?t:0>t?0:255<t?255:void 0}function H(t,e){return U(t+(t-e+.5>>1))}function V(t,e,n){return Math.abs(e-n)-Math.abs(t-n)}function W(t,e,n,r,i,o,s){for(r=o[s-1],n=0;n<i;++n)o[s+n]=r=q(t[e+n],r)}function G(t,e,n,r,i){var o;for(o=0;o<n;++o){var s=t[e+o],a=s>>8&255,c=16711935&(c=(c=16711935&s)+((a<<16)+a));r[i+o]=(4278255360&s)+c>>>0}}function Y(t,e){e.jd=t>>0&255,e.hd=t>>8&255,e.ud=t>>16&255}function J(t,e,n,r,i,o){var s;for(s=0;s<r;++s){var a=e[n+s],c=a>>>8,u=a,l=255&(l=(l=a>>>16)+((t.jd<<24>>24)*(c<<24>>24)>>>5));u=255&(u=(u+=(t.hd<<24>>24)*(c<<24>>24)>>>5)+((t.ud<<24>>24)*(l<<24>>24)>>>5)),i[o+s]=(4278255360&a)+(l<<16)+u}}function X(e,n,r,i,o){t[n]=function(t,e,n,r,s,a,c,u,l){for(r=c;r<u;++r)for(c=0;c<l;++c)s[a++]=o(n[i(t[e++])])},t[e]=function(e,n,s,a,c,u,l){var h=8>>e.b,f=e.Ea,d=e.K[0],p=e.w;if(8>h)for(e=(1<<e.b)-1,p=(1<<h)-1;n<s;++n){var m,g=0;for(m=0;m<f;++m)m&e||(g=i(a[c++])),u[l++]=o(d[g&p]),g>>=h}else t["VP8LMapColor"+r](a,c,d,p,u,l,n,s,f)}}function K(t,e,n,r,i){for(n=e+n;e<n;){var o=t[e++];r[i++]=o>>16&255,r[i++]=o>>8&255,r[i++]=o>>0&255}}function $(t,e,n,r,i){for(n=e+n;e<n;){var o=t[e++];r[i++]=o>>16&255,r[i++]=o>>8&255,r[i++]=o>>0&255,r[i++]=o>>24&255}}function Z(t,e,n,r,i){for(n=e+n;e<n;){var o=(s=t[e++])>>16&240|s>>12&15,s=s>>0&240|s>>28&15;r[i++]=o,r[i++]=s}}function Q(t,e,n,r,i){for(n=e+n;e<n;){var o=(s=t[e++])>>16&248|s>>13&7,s=s>>5&224|s>>3&31;r[i++]=o,r[i++]=s}}function tt(t,e,n,r,i){for(n=e+n;e<n;){var o=t[e++];r[i++]=o>>0&255,r[i++]=o>>8&255,r[i++]=o>>16&255}}function et(t,e,n,i,o,s){if(0==s)for(n=e+n;e<n;)k(i,((s=t[e++])[0]>>24|s[1]>>8&65280|s[2]<<8&16711680|s[3]<<24)>>>0),o+=32;else r(i,o,t,e,n)}function nt(e,n){t[n][0]=t[e+"0"],t[n][1]=t[e+"1"],t[n][2]=t[e+"2"],t[n][3]=t[e+"3"],t[n][4]=t[e+"4"],t[n][5]=t[e+"5"],t[n][6]=t[e+"6"],t[n][7]=t[e+"7"],t[n][8]=t[e+"8"],t[n][9]=t[e+"9"],t[n][10]=t[e+"10"],t[n][11]=t[e+"11"],t[n][12]=t[e+"12"],t[n][13]=t[e+"13"],t[n][14]=t[e+"0"],t[n][15]=t[e+"0"]}function rt(t){return t==Hr||t==Vr||t==Wr||t==Gr}function it(){this.eb=[],this.size=this.A=this.fb=0}function ot(){this.y=[],this.f=[],this.ea=[],this.F=[],this.Tc=this.Ed=this.Cd=this.Fd=this.lb=this.Db=this.Ab=this.fa=this.J=this.W=this.N=this.O=0}function st(){this.Rd=this.height=this.width=this.S=0,this.f={},this.f.RGBA=new it,this.f.kb=new ot,this.sd=null}function at(){this.width=[0],this.height=[0],this.Pd=[0],this.Qd=[0],this.format=[0]}function ct(){this.Id=this.fd=this.Md=this.hb=this.ib=this.da=this.bd=this.cd=this.j=this.v=this.Da=this.Sd=this.ob=0}function ut(t){return alert("todo:WebPSamplerProcessPlane"),t.T}function lt(t,e){var n=t.T,i=e.ba.f.RGBA,o=i.eb,s=i.fb+t.ka*i.A,a=vi[e.ba.S],c=t.y,u=t.O,l=t.f,h=t.N,f=t.ea,d=t.W,p=e.cc,m=e.dc,g=e.Mc,v=e.Nc,b=t.ka,y=t.ka+t.T,w=t.U,j=w+1>>1;for(0==b?a(c,u,null,null,l,h,f,d,l,h,f,d,o,s,null,null,w):(a(e.ec,e.fc,c,u,p,m,g,v,l,h,f,d,o,s-i.A,o,s,w),++n);b+2<y;b+=2)p=l,m=h,g=f,v=d,h+=t.Rc,d+=t.Rc,s+=2*i.A,a(c,(u+=2*t.fa)-t.fa,c,u,p,m,g,v,l,h,f,d,o,s-i.A,o,s,w);return u+=t.fa,t.j+y<t.o?(r(e.ec,e.fc,c,u,w),r(e.cc,e.dc,l,h,j),r(e.Mc,e.Nc,f,d,j),n--):1&y||a(c,u,null,null,l,h,f,d,l,h,f,d,o,s+i.A,null,null,w),n}function ht(t,n,r){var i=t.F,o=[t.J];if(null!=i){var s=t.U,a=n.ba.S,c=a==Rr||a==Wr;n=n.ba.f.RGBA;var u=[0],l=t.ka;u[0]=t.T,t.Kb&&(0==l?--u[0]:(--l,o[0]-=t.width),t.j+t.ka+t.T==t.o&&(u[0]=t.o-t.j-l));var h=n.eb;l=n.fb+l*n.A,t=Ar(i,o[0],t.width,s,u,h,l+(c?0:3),n.A),e(r==u),t&&rt(a)&&xr(h,l,c,s,u,n.A)}return 0}function ft(t){var e=t.ma,n=e.ba.S,r=11>n,i=n==Tr||n==Dr||n==Rr||n==zr||12==n||rt(n);if(e.memory=null,e.Ib=null,e.Jb=null,e.Nd=null,!Bn(e.Oa,t,i?11:12))return 0;if(i&&rt(n)&&yn(),t.da)alert("todo:use_scaling");else{if(r){if(e.Ib=ut,t.Kb){if(n=t.U+1>>1,e.memory=o(t.U+2*n),null==e.memory)return 0;e.ec=e.memory,e.fc=0,e.cc=e.ec,e.dc=e.fc+t.U,e.Mc=e.cc,e.Nc=e.dc+n,e.Ib=lt,yn()}}else alert("todo:EmitYUV");i&&(e.Jb=ht,r&&vn())}if(r&&!Ci){for(t=0;256>t;++t)Fi[t]=89858*(t-128)+Li>>Ai,Mi[t]=-22014*(t-128)+Li,Ei[t]=-45773*(t-128),Oi[t]=113618*(t-128)+Li>>Ai;for(t=Si;t<Pi;++t)e=76283*(t-16)+Li>>Ai,Bi[t-Si]=Wt(e,255),Ti[t-Si]=Wt(e+8>>4,15);Ci=1}return 1}function dt(t){var n=t.ma,r=t.U,i=t.T;return e(!(1&t.ka)),0>=r||0>=i?0:(r=n.Ib(t,n),null!=n.Jb&&n.Jb(t,n,r),n.Dc+=r,1)}function pt(t){t.ma.memory=null}function mt(t,e,n,r){return 47!=y(t,8)?0:(e[0]=y(t,14)+1,n[0]=y(t,14)+1,r[0]=y(t,1),0!=y(t,3)?0:!t.h)}function gt(t,e){if(4>t)return t+1;var n=t-2>>1;return(2+(1&t)<<n)+y(e,n)+1}function vt(t,e){return 120<e?e-120:1<=(n=((n=Zr[e-1])>>4)*t+(8-(15&n)))?n:1;var n}function bt(t,e,n){var r=_(n),i=t[e+=255&r].g-8;return 0<i&&(N(n,n.u+8),r=_(n),e+=t[e].value,e+=r&(1<<i)-1),N(n,n.u+t[e].g),t[e].value}function yt(t,n,r){return r.g+=t.g,r.value+=t.value<<n>>>0,e(8>=r.g),t.g}function wt(t,n,r){var i=t.xc;return e((n=0==i?0:t.vc[t.md*(r>>i)+(n>>i)])<t.Wb),t.Ya[n]}function jt(t,n,i,o){var s=t.ab,a=t.c*n,c=t.C;n=c+n;var u=i,l=o;for(o=t.Ta,i=t.Ua;0<s--;){var h=t.gc[s],f=c,d=n,p=u,m=l,g=(l=o,u=i,h.Ea);switch(e(f<d),e(d<=h.nc),h.hc){case 2:Yn(p,m,(d-f)*g,l,u);break;case 0:var v=f,b=d,y=l,w=u,j=(L=h).Ea;0==v&&(Wn(p,m,null,null,1,y,w),W(p,m+1,0,0,j-1,y,w+1),m+=j,w+=j,++v);for(var _=1<<L.b,x=_-1,N=T(j,L.b),A=L.K,L=L.w+(v>>L.b)*N;v<b;){var S=A,P=L,k=1;for(Gn(p,m,y,w-j,1,y,w);k<j;){var I=(k&~x)+_;I>j&&(I=j),(0,Zn[S[P++]>>8&15])(p,m+ +k,y,w+k-j,I-k,y,w+k),k=I}m+=j,w+=j,++v&x||(L+=N)}d!=h.nc&&r(l,u-g,l,u+(d-f-1)*g,g);break;case 1:for(g=p,b=m,j=(p=h.Ea)-(w=p&~(y=(m=1<<h.b)-1)),v=T(p,h.b),_=h.K,h=h.w+(f>>h.b)*v;f<d;){for(x=_,N=h,A=new R,L=b+w,S=b+p;b<L;)Y(x[N++],A),Qn(A,g,b,m,l,u),b+=m,u+=m;b<S&&(Y(x[N++],A),Qn(A,g,b,j,l,u),b+=j,u+=j),++f&y||(h+=v)}break;case 3:if(p==l&&m==u&&0<h.b){for(b=l,p=g=u+(d-f)*g-(w=(d-f)*T(h.Ea,h.b)),m=l,y=u,v=[],w=(j=w)-1;0<=w;--w)v[w]=m[y+w];for(w=j-1;0<=w;--w)b[p+w]=v[w];Jn(h,f,d,l,g,l,u)}else Jn(h,f,d,p,m,l,u)}u=o,l=i}l!=i&&r(o,i,u,l,a)}function _t(t,n){var r=t.V,i=t.Ba+t.c*t.C,o=n-t.C;if(e(n<=t.l.o),e(16>=o),0<o){var s=t.l,a=t.Ta,c=t.Ua,u=s.width;if(jt(t,o,r,i),o=c=[c],e((r=t.C)<(i=n)),e(s.v<s.va),i>s.o&&(i=s.o),r<s.j){var l=s.j-r;r=s.j,o[0]+=l*u}if(r>=i?r=0:(o[0]+=4*s.v,s.ka=r-s.j,s.U=s.va-s.v,s.T=i-r,r=1),r){if(c=c[0],11>(r=t.ca).S){var h=r.f.RGBA,f=(i=r.S,o=s.U,s=s.T,l=h.eb,h.A),d=s;for(h=h.fb+t.Ma*h.A;0<d--;){var p=a,m=c,g=o,v=l,b=h;switch(i){case Br:tr(p,m,g,v,b);break;case Tr:er(p,m,g,v,b);break;case Hr:er(p,m,g,v,b),xr(v,b,0,g,1,0);break;case qr:ir(p,m,g,v,b);break;case Dr:et(p,m,g,v,b,1);break;case Vr:et(p,m,g,v,b,1),xr(v,b,0,g,1,0);break;case Rr:et(p,m,g,v,b,0);break;case Wr:et(p,m,g,v,b,0),xr(v,b,1,g,1,0);break;case zr:nr(p,m,g,v,b);break;case Gr:nr(p,m,g,v,b),Nr(v,b,g,1,0);break;case Ur:rr(p,m,g,v,b);break;default:e(0)}c+=u,h+=f}t.Ma+=s}else alert("todo:EmitRescaledRowsYUVA");e(t.Ma<=r.height)}}t.C=n,e(t.C<=t.i)}function xt(t){var e;if(0<t.ua)return 0;for(e=0;e<t.Wb;++e){var n=t.Ya[e].G,r=t.Ya[e].H;if(0<n[1][r[1]+0].g||0<n[2][r[2]+0].g||0<n[3][r[3]+0].g)return 0}return 1}function Nt(t,n,r,i,o,s){if(0!=t.Z){var a=t.qd,c=t.rd;for(e(null!=gi[t.Z]);n<r;++n)gi[t.Z](a,c,i,o,i,o,s),a=i,c=o,o+=s;t.qd=a,t.rd=c}}function At(t,n){var r=t.l.ma,i=0==r.Z||1==r.Z?t.l.j:t.C;if(i=t.C<i?i:t.C,e(n<=t.l.o),n>i){var o=t.l.width,s=r.ca,a=r.tb+o*i,c=t.V,u=t.Ba+t.c*i,l=t.gc;e(1==t.ab),e(3==l[0].hc),Kn(l[0],i,n,c,u,s,a),Nt(r,i,n,s,a,o)}t.C=t.Ma=n}function Lt(t,n,r,i,o,s,a){var c=t.$/i,u=t.$%i,l=t.m,h=t.s,f=r+t.$,d=f;o=r+i*o;var p=r+i*s,m=280+h.ua,g=t.Pb?c:16777216,v=0<h.ua?h.Wa:null,b=h.wc,y=f<p?wt(h,u,c):null;e(t.C<s),e(p<=o);var w=!1;t:for(;;){for(;w||f<p;){var j=0;if(c>=g){var L=f-r;e((g=t).Pb),g.wd=g.m,g.xd=L,0<g.s.ua&&E(g.s.Wa,g.s.vb),g=c+ti}if(u&b||(y=wt(h,u,c)),e(null!=y),y.Qb&&(n[f]=y.qb,w=!0),!w)if(A(l),y.jc){j=l,L=n;var S=f,P=y.pd[_(j)&Dn-1];e(y.jc),256>P.g?(N(j,j.u+P.g),L[S]=P.value,j=0):(N(j,j.u+P.g-256),e(256<=P.value),j=P.value),0==j&&(w=!0)}else j=bt(y.G[0],y.H[0],l);if(l.h)break;if(w||256>j){if(!w)if(y.nd)n[f]=(y.qb|j<<8)>>>0;else{if(A(l),w=bt(y.G[1],y.H[1],l),A(l),L=bt(y.G[2],y.H[2],l),S=bt(y.G[3],y.H[3],l),l.h)break;n[f]=(S<<24|w<<16|j<<8|L)>>>0}if(w=!1,++f,++u>=i&&(u=0,++c,null!=a&&c<=s&&!(c%16)&&a(t,c),null!=v))for(;d<f;)j=n[d++],v.X[(506832829*j&**********)>>>v.Mb]=j}else if(280>j){if(j=gt(j-256,l),L=bt(y.G[4],y.H[4],l),A(l),L=vt(i,L=gt(L,l)),l.h)break;if(f-r<L||o-f<j)break t;for(S=0;S<j;++S)n[f+S]=n[f+S-L];for(f+=j,u+=j;u>=i;)u-=i,++c,null!=a&&c<=s&&!(c%16)&&a(t,c);if(e(f<=o),u&b&&(y=wt(h,u,c)),null!=v)for(;d<f;)j=n[d++],v.X[(506832829*j&**********)>>>v.Mb]=j}else{if(!(j<m))break t;for(w=j-280,e(null!=v);d<f;)j=n[d++],v.X[(506832829*j&**********)>>>v.Mb]=j;j=f,e(!(w>>>(L=v).Xa)),n[j]=L.X[w],w=!0}w||e(l.h==x(l))}if(t.Pb&&l.h&&f<o)e(t.m.h),t.a=5,t.m=t.wd,t.$=t.xd,0<t.s.ua&&E(t.s.vb,t.s.Wa);else{if(l.h)break t;null!=a&&a(t,c>s?s:c),t.a=0,t.$=f-r}return 1}return t.a=3,0}function St(t){e(null!=t),t.vc=null,t.yc=null,t.Ya=null;var n=t.Wa;null!=n&&(n.X=null),t.vb=null,e(null!=t)}function Pt(){var e=new an;return null==e?null:(e.a=0,e.xb=mi,nt("Predictor","VP8LPredictors"),nt("Predictor","VP8LPredictors_C"),nt("PredictorAdd","VP8LPredictorsAdd"),nt("PredictorAdd","VP8LPredictorsAdd_C"),Yn=G,Qn=J,tr=K,er=$,nr=Z,rr=Q,ir=tt,t.VP8LMapColor32b=Xn,t.VP8LMapColor8b=$n,e)}function kt(t,n,r,a,c){var u=1,f=[t],p=[n],m=a.m,g=a.s,v=null,b=0;t:for(;;){if(r)for(;u&&y(m,1);){var w=f,j=p,x=a,L=1,S=x.m,P=x.gc[x.ab],k=y(S,2);if(x.Oc&1<<k)u=0;else{switch(x.Oc|=1<<k,P.hc=k,P.Ea=w[0],P.nc=j[0],P.K=[null],++x.ab,e(4>=x.ab),k){case 0:case 1:P.b=y(S,3)+2,L=kt(T(P.Ea,P.b),T(P.nc,P.b),0,x,P.K),P.K=P.K[0];break;case 3:var I,C=y(S,8)+1,F=16<C?0:4<C?1:2<C?2:3;if(w[0]=T(P.Ea,F),P.b=F,I=L=kt(C,1,0,x,P.K)){var E,M=C,B=P,D=1<<(8>>B.b),R=o(D);if(null==R)I=0;else{var z=B.K[0],U=B.w;for(R[0]=B.K[0][0],E=1;E<1*M;++E)R[E]=q(z[U+E],R[E-1]);for(;E<4*D;++E)R[E]=0;B.K[0]=null,B.K[0]=R,I=1}}L=I;break;case 2:break;default:e(0)}u=L}}if(f=f[0],p=p[0],u&&y(m,1)&&!(u=1<=(b=y(m,4))&&11>=b)){a.a=3;break t}var H;if(H=u)e:{var V,W,G,Y=a,J=f,X=p,K=b,$=r,Z=Y.m,Q=Y.s,tt=[null],et=1,nt=0,rt=Qr[K];n:for(;;){if($&&y(Z,1)){var it=y(Z,3)+2,ot=T(J,it),st=T(X,it),at=ot*st;if(!kt(ot,st,0,Y,tt))break n;for(tt=tt[0],Q.xc=it,V=0;V<at;++V){var ct=tt[V]>>8&65535;tt[V]=ct,ct>=et&&(et=ct+1)}}if(Z.h)break n;for(W=0;5>W;++W){var ut=Xr[W];!W&&0<K&&(ut+=1<<K),nt<ut&&(nt=ut)}var lt=s(et*rt,h),ht=et,ft=s(ht,d);if(null==ft)var dt=null;else e(65536>=ht),dt=ft;var pt=o(nt);if(null==dt||null==pt||null==lt){Y.a=1;break n}var mt=lt;for(V=G=0;V<et;++V){var gt=dt[V],vt=gt.G,bt=gt.H,wt=0,jt=1,_t=0;for(W=0;5>W;++W){ut=Xr[W],vt[W]=mt,bt[W]=G,!W&&0<K&&(ut+=1<<K);r:{var xt,Nt=ut,At=Y,Pt=pt,It=mt,Ct=G,Ft=0,Ot=At.m,Et=y(Ot,1);if(i(Pt,0,0,Nt),Et){var Mt=y(Ot,1)+1,Bt=y(Ot,1),Tt=y(Ot,0==Bt?1:8);Pt[Tt]=1,2==Mt&&(Pt[Tt=y(Ot,8)]=1);var qt=1}else{var Dt=o(19),Rt=y(Ot,4)+4;if(19<Rt){At.a=3;var zt=0;break r}for(xt=0;xt<Rt;++xt)Dt[$r[xt]]=y(Ot,3);var Ut=void 0,Ht=void 0,Vt=At,Wt=Dt,Gt=Nt,Yt=Pt,Jt=0,Xt=Vt.m,Kt=8,$t=s(128,h);i:for(;l($t,0,7,Wt,19);){if(y(Xt,1)){var Zt=2+2*y(Xt,3);if((Ut=2+y(Xt,Zt))>Gt)break i}else Ut=Gt;for(Ht=0;Ht<Gt&&Ut--;){A(Xt);var Qt=$t[0+(127&_(Xt))];N(Xt,Xt.u+Qt.g);var te=Qt.value;if(16>te)Yt[Ht++]=te,0!=te&&(Kt=te);else{var ee=16==te,ne=te-16,re=Jr[ne],ie=y(Xt,Yr[ne])+re;if(Ht+ie>Gt)break i;for(var oe=ee?Kt:0;0<ie--;)Yt[Ht++]=oe}}Jt=1;break i}Jt||(Vt.a=3),qt=Jt}(qt=qt&&!Ot.h)&&(Ft=l(It,Ct,8,Pt,Nt)),qt&&0!=Ft?zt=Ft:(At.a=3,zt=0)}if(0==zt)break n;if(jt&&1==Kr[W]&&(jt=0==mt[G].g),wt+=mt[G].g,G+=zt,3>=W){var se,ae=pt[0];for(se=1;se<ut;++se)pt[se]>ae&&(ae=pt[se]);_t+=ae}}if(gt.nd=jt,gt.Qb=0,jt&&(gt.qb=(vt[3][bt[3]+0].value<<24|vt[1][bt[1]+0].value<<16|vt[2][bt[2]+0].value)>>>0,0==wt&&256>vt[0][bt[0]+0].value&&(gt.Qb=1,gt.qb+=vt[0][bt[0]+0].value<<8)),gt.jc=!gt.Qb&&6>_t,gt.jc){var ce,ue=gt;for(ce=0;ce<Dn;++ce){var le=ce,he=ue.pd[le],fe=ue.G[0][ue.H[0]+le];256<=fe.value?(he.g=fe.g+256,he.value=fe.value):(he.g=0,he.value=0,le>>=yt(fe,8,he),le>>=yt(ue.G[1][ue.H[1]+le],16,he),le>>=yt(ue.G[2][ue.H[2]+le],0,he),yt(ue.G[3][ue.H[3]+le],24,he))}}}Q.vc=tt,Q.Wb=et,Q.Ya=dt,Q.yc=lt,H=1;break e}H=0}if(!(u=H)){a.a=3;break t}if(0<b){if(g.ua=1<<b,!O(g.Wa,b)){a.a=1,u=0;break t}}else g.ua=0;var de=a,pe=f,me=p,ge=de.s,ve=ge.xc;if(de.c=pe,de.i=me,ge.md=T(pe,ve),ge.wc=0==ve?-1:(1<<ve)-1,r){a.xb=pi;break t}if(null==(v=o(f*p))){a.a=1,u=0;break t}u=(u=Lt(a,v,0,f,p,p,null))&&!m.h;break t}return u?(null!=c?c[0]=v:(e(null==v),e(r)),a.$=0,r||St(g)):St(g),u}function It(t,n){var r=t.c*t.i,i=r+n+16*n;return e(t.c<=n),t.V=o(i),null==t.V?(t.Ta=null,t.Ua=0,t.a=1,0):(t.Ta=t.V,t.Ua=t.Ba+r+n,1)}function Ct(t,n){var r=t.C,i=n-r,o=t.V,s=t.Ba+t.c*r;for(e(n<=t.l.o);0<i;){var a=16<i?16:i,c=t.l.ma,u=t.l.width,l=u*a,h=c.ca,f=c.tb+u*r,d=t.Ta,p=t.Ua;jt(t,a,o,s),Lr(d,p,h,f,l),Nt(c,r,r+a,h,f,u),i-=a,o+=a*t.c,r+=a}e(r==n),t.C=t.Ma=n}function Ft(){this.ub=this.yd=this.td=this.Rb=0}function Ot(){this.Kd=this.Ld=this.Ud=this.Td=this.i=this.c=0}function Et(){this.Fb=this.Bb=this.Cb=0,this.Zb=o(4),this.Lb=o(4)}function Mt(){this.Yb=function(){var t=[];return function t(e,n,r){for(var i=r[n],o=0;o<i&&(e.push(r.length>n+1?[]:0),!(r.length<n+1));o++)t(e[o],n+1,r)}(t,0,[3,11]),t}()}function Bt(){this.jb=o(3),this.Wc=a([4,8],Mt),this.Xc=a([4,17],Mt)}function Tt(){this.Pc=this.wb=this.Tb=this.zd=0,this.vd=new o(4),this.od=new o(4)}function qt(){this.ld=this.La=this.dd=this.tc=0}function Dt(){this.Na=this.la=0}function Rt(){this.Sc=[0,0],this.Eb=[0,0],this.Qc=[0,0],this.ia=this.lc=0}function zt(){this.ad=o(384),this.Za=0,this.Ob=o(16),this.$b=this.Ad=this.ia=this.Gc=this.Hc=this.Dd=0}function Ut(){this.uc=this.M=this.Nb=0,this.wa=Array(new qt),this.Y=0,this.ya=Array(new zt),this.aa=0,this.l=new Gt}function Ht(){this.y=o(16),this.f=o(8),this.ea=o(8)}function Vt(){this.cb=this.a=0,this.sc="",this.m=new w,this.Od=new Ft,this.Kc=new Ot,this.ed=new Tt,this.Qa=new Et,this.Ic=this.$c=this.Aa=0,this.D=new Ut,this.Xb=this.Va=this.Hb=this.zb=this.yb=this.Ub=this.za=0,this.Jc=s(8,w),this.ia=0,this.pb=s(4,Rt),this.Pa=new Bt,this.Bd=this.kc=0,this.Ac=[],this.Bc=0,this.zc=[0,0,0,0],this.Gd=Array(new Ht),this.Hd=0,this.rb=Array(new Dt),this.sb=0,this.wa=Array(new qt),this.Y=0,this.oc=[],this.pc=0,this.sa=[],this.ta=0,this.qa=[],this.ra=0,this.Ha=[],this.B=this.R=this.Ia=0,this.Ec=[],this.M=this.ja=this.Vb=this.Fc=0,this.ya=Array(new zt),this.L=this.aa=0,this.gd=a([4,2],qt),this.ga=null,this.Fa=[],this.Cc=this.qc=this.P=0,this.Gb=[],this.Uc=0,this.mb=[],this.nb=0,this.rc=[],this.Ga=this.Vc=0}function Wt(t,e){return 0>t?0:t>e?e:t}function Gt(){this.T=this.U=this.ka=this.height=this.width=0,this.y=[],this.f=[],this.ea=[],this.Rc=this.fa=this.W=this.N=this.O=0,this.ma="void",this.put="VP8IoPutHook",this.ac="VP8IoSetupHook",this.bc="VP8IoTeardownHook",this.ha=this.Kb=0,this.data=[],this.hb=this.ib=this.da=this.o=this.j=this.va=this.v=this.Da=this.ob=this.w=0,this.F=[],this.J=0}function Yt(){var t=new Vt;return null!=t&&(t.a=0,t.sc="OK",t.cb=0,t.Xb=0,ri||(ri=$t)),t}function Jt(t,e,n){return 0==t.a&&(t.a=e,t.sc=n,t.cb=0),0}function Xt(t,e,n){return 3<=n&&157==t[e+0]&&1==t[e+1]&&42==t[e+2]}function Kt(t,n){if(null==t)return 0;if(t.a=0,t.sc="OK",null==n)return Jt(t,2,"null VP8Io passed to VP8GetHeaders()");var r=n.data,o=n.w,s=n.ha;if(4>s)return Jt(t,7,"Truncated header.");var a=r[o+0]|r[o+1]<<8|r[o+2]<<16,c=t.Od;if(c.Rb=!(1&a),c.td=a>>1&7,c.yd=a>>4&1,c.ub=a>>5,3<c.td)return Jt(t,3,"Incorrect keyframe parameters.");if(!c.yd)return Jt(t,4,"Frame not displayable.");o+=3,s-=3;var u=t.Kc;if(c.Rb){if(7>s)return Jt(t,7,"cannot parse picture header");if(!Xt(r,o,s))return Jt(t,3,"Bad code word");u.c=16383&(r[o+4]<<8|r[o+3]),u.Td=r[o+4]>>6,u.i=16383&(r[o+6]<<8|r[o+5]),u.Ud=r[o+6]>>6,o+=7,s-=7,t.za=u.c+15>>4,t.Ub=u.i+15>>4,n.width=u.c,n.height=u.i,n.Da=0,n.j=0,n.v=0,n.va=n.width,n.o=n.height,n.da=0,n.ib=n.width,n.hb=n.height,n.U=n.width,n.T=n.height,i((a=t.Pa).jb,0,255,a.jb.length),e(null!=(a=t.Qa)),a.Cb=0,a.Bb=0,a.Fb=1,i(a.Zb,0,0,a.Zb.length),i(a.Lb,0,0,a.Lb)}if(c.ub>s)return Jt(t,7,"bad partition length");p(a=t.m,r,o,c.ub),o+=c.ub,s-=c.ub,c.Rb&&(u.Ld=S(a),u.Kd=S(a)),u=t.Qa;var l,h=t.Pa;if(e(null!=a),e(null!=u),u.Cb=S(a),u.Cb){if(u.Bb=S(a),S(a)){for(u.Fb=S(a),l=0;4>l;++l)u.Zb[l]=S(a)?g(a,7):0;for(l=0;4>l;++l)u.Lb[l]=S(a)?g(a,6):0}if(u.Bb)for(l=0;3>l;++l)h.jb[l]=S(a)?m(a,8):255}else u.Bb=0;if(a.Ka)return Jt(t,3,"cannot parse segment header");if((u=t.ed).zd=S(a),u.Tb=m(a,6),u.wb=m(a,3),u.Pc=S(a),u.Pc&&S(a)){for(h=0;4>h;++h)S(a)&&(u.vd[h]=g(a,6));for(h=0;4>h;++h)S(a)&&(u.od[h]=g(a,6))}if(t.L=0==u.Tb?0:u.zd?1:2,a.Ka)return Jt(t,3,"cannot parse filter header");var f=s;if(s=l=o,o=l+f,u=f,t.Xb=(1<<m(t.m,2))-1,f<3*(h=t.Xb))r=7;else{for(l+=3*h,u-=3*h,f=0;f<h;++f){var d=r[s+0]|r[s+1]<<8|r[s+2]<<16;d>u&&(d=u),p(t.Jc[+f],r,l,d),l+=d,u-=d,s+=3}p(t.Jc[+h],r,l,u),r=l<o?0:5}if(0!=r)return Jt(t,r,"cannot parse partitions");for(r=m(l=t.m,7),s=S(l)?g(l,4):0,o=S(l)?g(l,4):0,u=S(l)?g(l,4):0,h=S(l)?g(l,4):0,l=S(l)?g(l,4):0,f=t.Qa,d=0;4>d;++d){if(f.Cb){var v=f.Zb[d];f.Fb||(v+=r)}else{if(0<d){t.pb[d]=t.pb[0];continue}v=r}var b=t.pb[d];b.Sc[0]=ei[Wt(v+s,127)],b.Sc[1]=ni[Wt(v+0,127)],b.Eb[0]=2*ei[Wt(v+o,127)],b.Eb[1]=101581*ni[Wt(v+u,127)]>>16,8>b.Eb[1]&&(b.Eb[1]=8),b.Qc[0]=ei[Wt(v+h,117)],b.Qc[1]=ni[Wt(v+l,127)],b.lc=v+l}if(!c.Rb)return Jt(t,4,"Not a key frame.");for(S(a),c=t.Pa,r=0;4>r;++r){for(s=0;8>s;++s)for(o=0;3>o;++o)for(u=0;11>u;++u)h=P(a,ui[r][s][o][u])?m(a,8):ai[r][s][o][u],c.Wc[r][s].Yb[o][u]=h;for(s=0;17>s;++s)c.Xc[r][s]=c.Wc[r][li[s]]}return t.kc=S(a),t.kc&&(t.Bd=m(a,8)),t.cb=1}function $t(t,e,n,r,i,o,s){var a=e[i].Yb[n];for(n=0;16>i;++i){if(!P(t,a[n+0]))return i;for(;!P(t,a[n+1]);)if(a=e[++i].Yb[0],n=0,16==i)return 16;var c=e[i+1].Yb;if(P(t,a[n+2])){var u=t,l=0;if(P(u,(f=a)[(h=n)+3]))if(P(u,f[h+6])){for(a=0,h=2*(l=P(u,f[h+8]))+(f=P(u,f[h+9+l])),l=0,f=ii[h];f[a];++a)l+=l+P(u,f[a]);l+=3+(8<<h)}else P(u,f[h+7])?(l=7+2*P(u,165),l+=P(u,145)):l=5+P(u,159);else l=P(u,f[h+4])?3+P(u,f[h+5]):2;a=c[2]}else l=1,a=c[1];c=s+oi[i],0>(u=t).b&&L(u);var h,f=u.b,d=(h=u.Ca>>1)-(u.I>>f)>>31;--u.b,u.Ca+=d,u.Ca|=1,u.I-=(h+1&d)<<f,o[c]=((l^d)-d)*r[(0<i)+0]}return 16}function Zt(t){var e=t.rb[t.sb-1];e.la=0,e.Na=0,i(t.zc,0,0,t.zc.length),t.ja=0}function Qt(t,n){if(null==t)return 0;if(null==n)return Jt(t,2,"NULL VP8Io parameter in VP8Decode().");if(!t.cb&&!Kt(t,n))return 0;if(e(t.cb),null==n.ac||n.ac(n)){n.ob&&(t.L=0);var a=Di[t.L];if(2==t.L?(t.yb=0,t.zb=0):(t.yb=n.v-a>>4,t.zb=n.j-a>>4,0>t.yb&&(t.yb=0),0>t.zb&&(t.zb=0)),t.Va=n.o+15+a>>4,t.Hb=n.va+15+a>>4,t.Hb>t.za&&(t.Hb=t.za),t.Va>t.Ub&&(t.Va=t.Ub),0<t.L){var c=t.ed;for(a=0;4>a;++a){var u;if(t.Qa.Cb){var l=t.Qa.Lb[a];t.Qa.Fb||(l+=c.Tb)}else l=c.Tb;for(u=0;1>=u;++u){var h=t.gd[a][u],f=l;if(c.Pc&&(f+=c.vd[0],u&&(f+=c.od[0])),0<(f=0>f?0:63<f?63:f)){var d=f;0<c.wb&&(d=4<c.wb?d>>2:d>>1)>9-c.wb&&(d=9-c.wb),1>d&&(d=1),h.dd=d,h.tc=2*f+d,h.ld=40<=f?2:15<=f?1:0}else h.tc=0;h.La=u}}}a=0}else Jt(t,6,"Frame setup failed"),a=t.a;if(a=0==a){if(a){t.$c=0,0<t.Aa||(t.Ic=zi);t:{a=t.Ic,c=4*(d=t.za);var p=32*d,m=d+1,g=0<t.L?d*(0<t.Aa?2:1):0,v=(2==t.Aa?2:1)*d;if((h=c+832+(u=3*(16*a+Di[t.L])/2*p)+(l=null!=t.Fa&&0<t.Fa.length?t.Kc.c*t.Kc.i:0))!=h)a=0;else{if(h>t.Vb){if(t.Vb=0,t.Ec=o(h),t.Fc=0,null==t.Ec){a=Jt(t,1,"no memory during frame initialization.");break t}t.Vb=h}h=t.Ec,f=t.Fc,t.Ac=h,t.Bc=f,f+=c,t.Gd=s(p,Ht),t.Hd=0,t.rb=s(m+1,Dt),t.sb=1,t.wa=g?s(g,qt):null,t.Y=0,t.D.Nb=0,t.D.wa=t.wa,t.D.Y=t.Y,0<t.Aa&&(t.D.Y+=d),e(!0),t.oc=h,t.pc=f,f+=832,t.ya=s(v,zt),t.aa=0,t.D.ya=t.ya,t.D.aa=t.aa,2==t.Aa&&(t.D.aa+=d),t.R=16*d,t.B=8*d,d=(p=Di[t.L])*t.R,p=p/2*t.B,t.sa=h,t.ta=f+d,t.qa=t.sa,t.ra=t.ta+16*a*t.R+p,t.Ha=t.qa,t.Ia=t.ra+8*a*t.B+p,t.$c=0,f+=u,t.mb=l?h:null,t.nb=l?f:null,e(f+l<=t.Fc+t.Vb),Zt(t),i(t.Ac,t.Bc,0,c),a=1}}if(a){if(n.ka=0,n.y=t.sa,n.O=t.ta,n.f=t.qa,n.N=t.ra,n.ea=t.Ha,n.Vd=t.Ia,n.fa=t.R,n.Rc=t.B,n.F=null,n.J=0,!Cr){for(a=-255;255>=a;++a)Sr[255+a]=0>a?-a:a;for(a=-1020;1020>=a;++a)Pr[1020+a]=-128>a?-128:127<a?127:a;for(a=-112;112>=a;++a)kr[112+a]=-16>a?-16:15<a?15:a;for(a=-255;510>=a;++a)Ir[255+a]=0>a?0:255<a?255:a;Cr=1}or=ue,sr=oe,cr=se,ur=ae,lr=ce,ar=ie,hr=Je,fr=Xe,dr=Ze,pr=Qe,mr=Ke,gr=$e,vr=tn,br=en,yr=Ue,wr=He,jr=Ve,_r=We,fi[0]=Ne,fi[1]=he,fi[2]=_e,fi[3]=xe,fi[4]=Ae,fi[5]=Se,fi[6]=Le,fi[7]=Pe,fi[8]=Ie,fi[9]=ke,hi[0]=ve,hi[1]=de,hi[2]=pe,hi[3]=me,hi[4]=be,hi[5]=ye,hi[6]=we,di[0]=Ee,di[1]=fe,di[2]=Ce,di[3]=Fe,di[4]=Be,di[5]=Me,di[6]=Te,a=1}else a=0}a&&(a=function(t,n){for(t.M=0;t.M<t.Va;++t.M){var s,a=t.Jc[t.M&t.Xb],c=t.m,u=t;for(s=0;s<u.za;++s){var l=c,h=u,f=h.Ac,d=h.Bc+4*s,p=h.zc,m=h.ya[h.aa+s];if(h.Qa.Bb?m.$b=P(l,h.Pa.jb[0])?2+P(l,h.Pa.jb[2]):P(l,h.Pa.jb[1]):m.$b=0,h.kc&&(m.Ad=P(l,h.Bd)),m.Za=!P(l,145)+0,m.Za){var g=m.Ob,v=0;for(h=0;4>h;++h){var b,y=p[0+h];for(b=0;4>b;++b){y=ci[f[d+b]][y];for(var w=si[P(l,y[0])];0<w;)w=si[2*w+P(l,y[w])];y=-w,f[d+b]=y}r(g,v,f,d,4),v+=4,p[0+h]=y}}else y=P(l,156)?P(l,128)?1:3:P(l,163)?2:0,m.Ob[0]=y,i(f,d,y,4),i(p,0,y,4);m.Dd=P(l,142)?P(l,114)?P(l,183)?1:3:2:0}if(u.m.Ka)return Jt(t,7,"Premature end-of-partition0 encountered.");for(;t.ja<t.za;++t.ja){if(u=a,l=(c=t).rb[c.sb-1],f=c.rb[c.sb+c.ja],s=c.ya[c.aa+c.ja],d=c.kc?s.Ad:0)l.la=f.la=0,s.Za||(l.Na=f.Na=0),s.Hc=0,s.Gc=0,s.ia=0;else{var j,_;if(l=f,f=u,d=c.Pa.Xc,p=c.ya[c.aa+c.ja],m=c.pb[p.$b],h=p.ad,g=0,v=c.rb[c.sb-1],y=b=0,i(h,g,0,384),p.Za)var x=0,N=d[3];else{w=o(16);var A=l.Na+v.Na;if(A=ri(f,d[1],A,m.Eb,0,w,0),l.Na=v.Na=(0<A)+0,1<A)or(w,0,h,g);else{var L=w[0]+3>>3;for(w=0;256>w;w+=16)h[g+w]=L}x=1,N=d[0]}var S=15&l.la,k=15&v.la;for(w=0;4>w;++w){var I=1&k;for(L=_=0;4>L;++L)S=S>>1|(I=(A=ri(f,N,A=I+(1&S),m.Sc,x,h,g))>x)<<7,_=_<<2|(3<A?3:1<A?2:0!=h[g+0]),g+=16;S>>=4,k=k>>1|I<<7,b=(b<<8|_)>>>0}for(N=S,x=k>>4,j=0;4>j;j+=2){for(_=0,S=l.la>>4+j,k=v.la>>4+j,w=0;2>w;++w){for(I=1&k,L=0;2>L;++L)A=I+(1&S),S=S>>1|(I=0<(A=ri(f,d[2],A,m.Qc,0,h,g)))<<3,_=_<<2|(3<A?3:1<A?2:0!=h[g+0]),g+=16;S>>=2,k=k>>1|I<<5}y|=_<<4*j,N|=S<<4<<j,x|=(240&k)<<j}l.la=N,v.la=x,p.Hc=b,p.Gc=y,p.ia=43690&y?0:m.ia,d=!(b|y)}if(0<c.L&&(c.wa[c.Y+c.ja]=c.gd[s.$b][s.Za],c.wa[c.Y+c.ja].La|=!d),u.Ka)return Jt(t,7,"Premature end-of-file encountered.")}if(Zt(t),c=n,u=1,s=(a=t).D,l=0<a.L&&a.M>=a.zb&&a.M<=a.Va,0==a.Aa)t:{if(s.M=a.M,s.uc=l,En(a,s),u=1,s=(_=a.D).Nb,l=(y=Di[a.L])*a.R,f=y/2*a.B,w=16*s*a.R,L=8*s*a.B,d=a.sa,p=a.ta-l+w,m=a.qa,h=a.ra-f+L,g=a.Ha,v=a.Ia-f+L,k=0==(S=_.M),b=S>=a.Va-1,2==a.Aa&&En(a,_),_.uc)for(I=(A=a).D.M,e(A.D.uc),_=A.yb;_<A.Hb;++_){x=_,N=I;var C=(F=(z=A).D).Nb;j=z.R;var F=F.wa[F.Y+x],O=z.sa,E=z.ta+16*C*j+16*x,M=F.dd,B=F.tc;if(0!=B)if(e(3<=B),1==z.L)0<x&&wr(O,E,j,B+4),F.La&&_r(O,E,j,B),0<N&&yr(O,E,j,B+4),F.La&&jr(O,E,j,B);else{var T=z.B,q=z.qa,D=z.ra+8*C*T+8*x,R=z.Ha,z=z.Ia+8*C*T+8*x;C=F.ld,0<x&&(fr(O,E,j,B+4,M,C),pr(q,D,R,z,T,B+4,M,C)),F.La&&(gr(O,E,j,B,M,C),br(q,D,R,z,T,B,M,C)),0<N&&(hr(O,E,j,B+4,M,C),dr(q,D,R,z,T,B+4,M,C)),F.La&&(mr(O,E,j,B,M,C),vr(q,D,R,z,T,B,M,C))}}if(a.ia&&alert("todo:DitherRow"),null!=c.put){if(_=16*S,S=16*(S+1),k?(c.y=a.sa,c.O=a.ta+w,c.f=a.qa,c.N=a.ra+L,c.ea=a.Ha,c.W=a.Ia+L):(_-=y,c.y=d,c.O=p,c.f=m,c.N=h,c.ea=g,c.W=v),b||(S-=y),S>c.o&&(S=c.o),c.F=null,c.J=null,null!=a.Fa&&0<a.Fa.length&&_<S&&(c.J=fn(a,c,_,S-_),c.F=a.mb,null==c.F&&0==c.F.length)){u=Jt(a,3,"Could not decode alpha data.");break t}_<c.j&&(y=c.j-_,_=c.j,e(!(1&y)),c.O+=a.R*y,c.N+=a.B*(y>>1),c.W+=a.B*(y>>1),null!=c.F&&(c.J+=c.width*y)),_<S&&(c.O+=c.v,c.N+=c.v>>1,c.W+=c.v>>1,null!=c.F&&(c.J+=c.v),c.ka=_-c.j,c.U=c.va-c.v,c.T=S-_,u=c.put(c))}s+1!=a.Ic||b||(r(a.sa,a.ta-l,d,p+16*a.R,l),r(a.qa,a.ra-f,m,h+8*a.B,f),r(a.Ha,a.Ia-f,g,v+8*a.B,f))}if(!u)return Jt(t,6,"Output aborted.")}return 1}(t,n)),null!=n.bc&&n.bc(n),a&=1}return a?(t.cb=0,a):0}function te(t,e,n,r,i){i=t[e+n+32*r]+(i>>3),t[e+n+32*r]=-256&i?0>i?0:255:i}function ee(t,e,n,r,i,o){te(t,e,0,n,r+i),te(t,e,1,n,r+o),te(t,e,2,n,r-o),te(t,e,3,n,r-i)}function ne(t){return(20091*t>>16)+t}function re(t,e,n,r){var i,s=0,a=o(16);for(i=0;4>i;++i){var c=t[e+0]+t[e+8],u=t[e+0]-t[e+8],l=(35468*t[e+4]>>16)-ne(t[e+12]),h=ne(t[e+4])+(35468*t[e+12]>>16);a[s+0]=c+h,a[s+1]=u+l,a[s+2]=u-l,a[s+3]=c-h,s+=4,e++}for(i=s=0;4>i;++i)c=(t=a[s+0]+4)+a[s+8],u=t-a[s+8],l=(35468*a[s+4]>>16)-ne(a[s+12]),te(n,r,0,0,c+(h=ne(a[s+4])+(35468*a[s+12]>>16))),te(n,r,1,0,u+l),te(n,r,2,0,u-l),te(n,r,3,0,c-h),s++,r+=32}function ie(t,e,n,r){var i=t[e+0]+4,o=35468*t[e+4]>>16,s=ne(t[e+4]),a=35468*t[e+1]>>16;ee(n,r,0,i+s,t=ne(t[e+1]),a),ee(n,r,1,i+o,t,a),ee(n,r,2,i-o,t,a),ee(n,r,3,i-s,t,a)}function oe(t,e,n,r,i){re(t,e,n,r),i&&re(t,e+16,n,r+4)}function se(t,e,n,r){sr(t,e+0,n,r,1),sr(t,e+32,n,r+128,1)}function ae(t,e,n,r){var i;for(t=t[e+0]+4,i=0;4>i;++i)for(e=0;4>e;++e)te(n,r,e,i,t)}function ce(t,e,n,r){t[e+0]&&ur(t,e+0,n,r),t[e+16]&&ur(t,e+16,n,r+4),t[e+32]&&ur(t,e+32,n,r+128),t[e+48]&&ur(t,e+48,n,r+128+4)}function ue(t,e,n,r){var i,s=o(16);for(i=0;4>i;++i){var a=t[e+0+i]+t[e+12+i],c=t[e+4+i]+t[e+8+i],u=t[e+4+i]-t[e+8+i],l=t[e+0+i]-t[e+12+i];s[0+i]=a+c,s[8+i]=a-c,s[4+i]=l+u,s[12+i]=l-u}for(i=0;4>i;++i)a=(t=s[0+4*i]+3)+s[3+4*i],c=s[1+4*i]+s[2+4*i],u=s[1+4*i]-s[2+4*i],l=t-s[3+4*i],n[r+0]=a+c>>3,n[r+16]=l+u>>3,n[r+32]=a-c>>3,n[r+48]=l-u>>3,r+=64}function le(t,e,n){var r,i=e-32,o=Er,s=255-t[i-1];for(r=0;r<n;++r){var a,c=o,u=s+t[e-1];for(a=0;a<n;++a)t[e+a]=c[u+t[i+a]];e+=32}}function he(t,e){le(t,e,4)}function fe(t,e){le(t,e,8)}function de(t,e){le(t,e,16)}function pe(t,e){var n;for(n=0;16>n;++n)r(t,e+32*n,t,e-32,16)}function me(t,e){var n;for(n=16;0<n;--n)i(t,e,t[e-1],16),e+=32}function ge(t,e,n){var r;for(r=0;16>r;++r)i(e,n+32*r,t,16)}function ve(t,e){var n,r=16;for(n=0;16>n;++n)r+=t[e-1+32*n]+t[e+n-32];ge(r>>5,t,e)}function be(t,e){var n,r=8;for(n=0;16>n;++n)r+=t[e-1+32*n];ge(r>>4,t,e)}function ye(t,e){var n,r=8;for(n=0;16>n;++n)r+=t[e+n-32];ge(r>>4,t,e)}function we(t,e){ge(128,t,e)}function je(t,e,n){return t+2*e+n+2>>2}function _e(t,e){var n,i=e-32;for(i=new Uint8Array([je(t[i-1],t[i+0],t[i+1]),je(t[i+0],t[i+1],t[i+2]),je(t[i+1],t[i+2],t[i+3]),je(t[i+2],t[i+3],t[i+4])]),n=0;4>n;++n)r(t,e+32*n,i,0,i.length)}function xe(t,e){var n=t[e-1],r=t[e-1+32],i=t[e-1+64],o=t[e-1+96];k(t,e+0,16843009*je(t[e-1-32],n,r)),k(t,e+32,16843009*je(n,r,i)),k(t,e+64,16843009*je(r,i,o)),k(t,e+96,16843009*je(i,o,o))}function Ne(t,e){var n,r=4;for(n=0;4>n;++n)r+=t[e+n-32]+t[e-1+32*n];for(r>>=3,n=0;4>n;++n)i(t,e+32*n,r,4)}function Ae(t,e){var n=t[e-1+0],r=t[e-1+32],i=t[e-1+64],o=t[e-1-32],s=t[e+0-32],a=t[e+1-32],c=t[e+2-32],u=t[e+3-32];t[e+0+96]=je(r,i,t[e-1+96]),t[e+1+96]=t[e+0+64]=je(n,r,i),t[e+2+96]=t[e+1+64]=t[e+0+32]=je(o,n,r),t[e+3+96]=t[e+2+64]=t[e+1+32]=t[e+0+0]=je(s,o,n),t[e+3+64]=t[e+2+32]=t[e+1+0]=je(a,s,o),t[e+3+32]=t[e+2+0]=je(c,a,s),t[e+3+0]=je(u,c,a)}function Le(t,e){var n=t[e+1-32],r=t[e+2-32],i=t[e+3-32],o=t[e+4-32],s=t[e+5-32],a=t[e+6-32],c=t[e+7-32];t[e+0+0]=je(t[e+0-32],n,r),t[e+1+0]=t[e+0+32]=je(n,r,i),t[e+2+0]=t[e+1+32]=t[e+0+64]=je(r,i,o),t[e+3+0]=t[e+2+32]=t[e+1+64]=t[e+0+96]=je(i,o,s),t[e+3+32]=t[e+2+64]=t[e+1+96]=je(o,s,a),t[e+3+64]=t[e+2+96]=je(s,a,c),t[e+3+96]=je(a,c,c)}function Se(t,e){var n=t[e-1+0],r=t[e-1+32],i=t[e-1+64],o=t[e-1-32],s=t[e+0-32],a=t[e+1-32],c=t[e+2-32],u=t[e+3-32];t[e+0+0]=t[e+1+64]=o+s+1>>1,t[e+1+0]=t[e+2+64]=s+a+1>>1,t[e+2+0]=t[e+3+64]=a+c+1>>1,t[e+3+0]=c+u+1>>1,t[e+0+96]=je(i,r,n),t[e+0+64]=je(r,n,o),t[e+0+32]=t[e+1+96]=je(n,o,s),t[e+1+32]=t[e+2+96]=je(o,s,a),t[e+2+32]=t[e+3+96]=je(s,a,c),t[e+3+32]=je(a,c,u)}function Pe(t,e){var n=t[e+0-32],r=t[e+1-32],i=t[e+2-32],o=t[e+3-32],s=t[e+4-32],a=t[e+5-32],c=t[e+6-32],u=t[e+7-32];t[e+0+0]=n+r+1>>1,t[e+1+0]=t[e+0+64]=r+i+1>>1,t[e+2+0]=t[e+1+64]=i+o+1>>1,t[e+3+0]=t[e+2+64]=o+s+1>>1,t[e+0+32]=je(n,r,i),t[e+1+32]=t[e+0+96]=je(r,i,o),t[e+2+32]=t[e+1+96]=je(i,o,s),t[e+3+32]=t[e+2+96]=je(o,s,a),t[e+3+64]=je(s,a,c),t[e+3+96]=je(a,c,u)}function ke(t,e){var n=t[e-1+0],r=t[e-1+32],i=t[e-1+64],o=t[e-1+96];t[e+0+0]=n+r+1>>1,t[e+2+0]=t[e+0+32]=r+i+1>>1,t[e+2+32]=t[e+0+64]=i+o+1>>1,t[e+1+0]=je(n,r,i),t[e+3+0]=t[e+1+32]=je(r,i,o),t[e+3+32]=t[e+1+64]=je(i,o,o),t[e+3+64]=t[e+2+64]=t[e+0+96]=t[e+1+96]=t[e+2+96]=t[e+3+96]=o}function Ie(t,e){var n=t[e-1+0],r=t[e-1+32],i=t[e-1+64],o=t[e-1+96],s=t[e-1-32],a=t[e+0-32],c=t[e+1-32],u=t[e+2-32];t[e+0+0]=t[e+2+32]=n+s+1>>1,t[e+0+32]=t[e+2+64]=r+n+1>>1,t[e+0+64]=t[e+2+96]=i+r+1>>1,t[e+0+96]=o+i+1>>1,t[e+3+0]=je(a,c,u),t[e+2+0]=je(s,a,c),t[e+1+0]=t[e+3+32]=je(n,s,a),t[e+1+32]=t[e+3+64]=je(r,n,s),t[e+1+64]=t[e+3+96]=je(i,r,n),t[e+1+96]=je(o,i,r)}function Ce(t,e){var n;for(n=0;8>n;++n)r(t,e+32*n,t,e-32,8)}function Fe(t,e){var n;for(n=0;8>n;++n)i(t,e,t[e-1],8),e+=32}function Oe(t,e,n){var r;for(r=0;8>r;++r)i(e,n+32*r,t,8)}function Ee(t,e){var n,r=8;for(n=0;8>n;++n)r+=t[e+n-32]+t[e-1+32*n];Oe(r>>4,t,e)}function Me(t,e){var n,r=4;for(n=0;8>n;++n)r+=t[e+n-32];Oe(r>>3,t,e)}function Be(t,e){var n,r=4;for(n=0;8>n;++n)r+=t[e-1+32*n];Oe(r>>3,t,e)}function Te(t,e){Oe(128,t,e)}function qe(t,e,n){var r=t[e-n],i=t[e+0],o=3*(i-r)+Fr[1020+t[e-2*n]-t[e+n]],s=Or[112+(o+4>>3)];t[e-n]=Er[255+r+Or[112+(o+3>>3)]],t[e+0]=Er[255+i-s]}function De(t,e,n,r){var i=t[e+0],o=t[e+n];return Mr[255+t[e-2*n]-t[e-n]]>r||Mr[255+o-i]>r}function Re(t,e,n,r){return 4*Mr[255+t[e-n]-t[e+0]]+Mr[255+t[e-2*n]-t[e+n]]<=r}function ze(t,e,n,r,i){var o=t[e-3*n],s=t[e-2*n],a=t[e-n],c=t[e+0],u=t[e+n],l=t[e+2*n],h=t[e+3*n];return 4*Mr[255+a-c]+Mr[255+s-u]>r?0:Mr[255+t[e-4*n]-o]<=i&&Mr[255+o-s]<=i&&Mr[255+s-a]<=i&&Mr[255+h-l]<=i&&Mr[255+l-u]<=i&&Mr[255+u-c]<=i}function Ue(t,e,n,r){var i=2*r+1;for(r=0;16>r;++r)Re(t,e+r,n,i)&&qe(t,e+r,n)}function He(t,e,n,r){var i=2*r+1;for(r=0;16>r;++r)Re(t,e+r*n,1,i)&&qe(t,e+r*n,1)}function Ve(t,e,n,r){var i;for(i=3;0<i;--i)Ue(t,e+=4*n,n,r)}function We(t,e,n,r){var i;for(i=3;0<i;--i)He(t,e+=4,n,r)}function Ge(t,e,n,r,i,o,s,a){for(o=2*o+1;0<i--;){if(ze(t,e,n,o,s))if(De(t,e,n,a))qe(t,e,n);else{var c=t,u=e,l=n,h=c[u-2*l],f=c[u-l],d=c[u+0],p=c[u+l],m=c[u+2*l],g=27*(b=Fr[1020+3*(d-f)+Fr[1020+h-p]])+63>>7,v=18*b+63>>7,b=9*b+63>>7;c[u-3*l]=Er[255+c[u-3*l]+b],c[u-2*l]=Er[255+h+v],c[u-l]=Er[255+f+g],c[u+0]=Er[255+d-g],c[u+l]=Er[255+p-v],c[u+2*l]=Er[255+m-b]}e+=r}}function Ye(t,e,n,r,i,o,s,a){for(o=2*o+1;0<i--;){if(ze(t,e,n,o,s))if(De(t,e,n,a))qe(t,e,n);else{var c=t,u=e,l=n,h=c[u-l],f=c[u+0],d=c[u+l],p=Or[112+(4+(m=3*(f-h))>>3)],m=Or[112+(m+3>>3)],g=p+1>>1;c[u-2*l]=Er[255+c[u-2*l]+g],c[u-l]=Er[255+h+m],c[u+0]=Er[255+f-p],c[u+l]=Er[255+d-g]}e+=r}}function Je(t,e,n,r,i,o){Ge(t,e,n,1,16,r,i,o)}function Xe(t,e,n,r,i,o){Ge(t,e,1,n,16,r,i,o)}function Ke(t,e,n,r,i,o){var s;for(s=3;0<s;--s)Ye(t,e+=4*n,n,1,16,r,i,o)}function $e(t,e,n,r,i,o){var s;for(s=3;0<s;--s)Ye(t,e+=4,1,n,16,r,i,o)}function Ze(t,e,n,r,i,o,s,a){Ge(t,e,i,1,8,o,s,a),Ge(n,r,i,1,8,o,s,a)}function Qe(t,e,n,r,i,o,s,a){Ge(t,e,1,i,8,o,s,a),Ge(n,r,1,i,8,o,s,a)}function tn(t,e,n,r,i,o,s,a){Ye(t,e+4*i,i,1,8,o,s,a),Ye(n,r+4*i,i,1,8,o,s,a)}function en(t,e,n,r,i,o,s,a){Ye(t,e+4,1,i,8,o,s,a),Ye(n,r+4,1,i,8,o,s,a)}function nn(){this.ba=new st,this.ec=[],this.cc=[],this.Mc=[],this.Dc=this.Nc=this.dc=this.fc=0,this.Oa=new ct,this.memory=0,this.Ib="OutputFunc",this.Jb="OutputAlphaFunc",this.Nd="OutputRowFunc"}function rn(){this.data=[],this.offset=this.kd=this.ha=this.w=0,this.na=[],this.xa=this.gb=this.Ja=this.Sa=this.P=0}function on(){this.nc=this.Ea=this.b=this.hc=0,this.K=[],this.w=0}function sn(){this.ua=0,this.Wa=new M,this.vb=new M,this.md=this.xc=this.wc=0,this.vc=[],this.Wb=0,this.Ya=new d,this.yc=new h}function an(){this.xb=this.a=0,this.l=new Gt,this.ca=new st,this.V=[],this.Ba=0,this.Ta=[],this.Ua=0,this.m=new j,this.Pb=0,this.wd=new j,this.Ma=this.$=this.C=this.i=this.c=this.xd=0,this.s=new sn,this.ab=0,this.gc=s(4,on),this.Oc=0}function cn(){this.Lc=this.Z=this.$a=this.i=this.c=0,this.l=new Gt,this.ic=0,this.ca=[],this.tb=0,this.qd=null,this.rd=0}function un(t,e,n,r,i,o,s){for(t=null==t?0:t[e+0],e=0;e<s;++e)i[o+e]=t+n[r+e]&255,t=i[o+e]}function ln(t,e,n,r,i,o,s){var a;if(null==t)un(null,null,n,r,i,o,s);else for(a=0;a<s;++a)i[o+a]=t[e+a]+n[r+a]&255}function hn(t,e,n,r,i,o,s){if(null==t)un(null,null,n,r,i,o,s);else{var a,c=t[e+0],u=c,l=c;for(a=0;a<s;++a)u=l+(c=t[e+a])-u,l=n[r+a]+(-256&u?0>u?0:255:u)&255,u=c,i[o+a]=l}}function fn(t,n,i,s){var a=n.width,c=n.o;if(e(null!=t&&null!=n),0>i||0>=s||i+s>c)return null;if(!t.Cc){if(null==t.ga){var u;if(t.ga=new cn,(u=null==t.ga)||(u=n.width*n.o,e(0==t.Gb.length),t.Gb=o(u),t.Uc=0,null==t.Gb?u=0:(t.mb=t.Gb,t.nb=t.Uc,t.rc=null,u=1),u=!u),!u){u=t.ga;var l=t.Fa,h=t.P,f=t.qc,d=t.mb,p=t.nb,m=h+1,g=f-1,b=u.l;if(e(null!=l&&null!=d&&null!=n),gi[0]=null,gi[1]=un,gi[2]=ln,gi[3]=hn,u.ca=d,u.tb=p,u.c=n.width,u.i=n.height,e(0<u.c&&0<u.i),1>=f)n=0;else if(u.$a=l[h+0]>>0&3,u.Z=l[h+0]>>2&3,u.Lc=l[h+0]>>4&3,h=l[h+0]>>6&3,0>u.$a||1<u.$a||4<=u.Z||1<u.Lc||h)n=0;else if(b.put=dt,b.ac=ft,b.bc=pt,b.ma=u,b.width=n.width,b.height=n.height,b.Da=n.Da,b.v=n.v,b.va=n.va,b.j=n.j,b.o=n.o,u.$a)t:{e(1==u.$a),n=Pt();e:for(;;){if(null==n){n=0;break t}if(e(null!=u),u.mc=n,n.c=u.c,n.i=u.i,n.l=u.l,n.l.ma=u,n.l.width=u.c,n.l.height=u.i,n.a=0,v(n.m,l,m,g),!kt(u.c,u.i,1,n,null))break e;if(1==n.ab&&3==n.gc[0].hc&&xt(n.s)?(u.ic=1,l=n.c*n.i,n.Ta=null,n.Ua=0,n.V=o(l),n.Ba=0,null==n.V?(n.a=1,n=0):n=1):(u.ic=0,n=It(n,u.c)),!n)break e;n=1;break t}u.mc=null,n=0}else n=g>=u.c*u.i;u=!n}if(u)return null;1!=t.ga.Lc?t.Ga=0:s=c-i}e(null!=t.ga),e(i+s<=c);t:{if(n=(l=t.ga).c,c=l.l.o,0==l.$a){if(m=t.rc,g=t.Vc,b=t.Fa,h=t.P+1+i*n,f=t.mb,d=t.nb+i*n,e(h<=t.P+t.qc),0!=l.Z)for(e(null!=gi[l.Z]),u=0;u<s;++u)gi[l.Z](m,g,b,h,f,d,n),m=f,g=d,d+=n,h+=n;else for(u=0;u<s;++u)r(f,d,b,h,n),m=f,g=d,d+=n,h+=n;t.rc=m,t.Vc=g}else{if(e(null!=l.mc),n=i+s,e(null!=(u=l.mc)),e(n<=u.i),u.C>=n)n=1;else if(l.ic||vn(),l.ic){l=u.V,m=u.Ba,g=u.c;var y=u.i,w=(b=1,h=u.$/g,f=u.$%g,d=u.m,p=u.s,u.$),j=g*y,_=g*n,N=p.wc,L=w<_?wt(p,f,h):null;e(w<=j),e(n<=y),e(xt(p));e:for(;;){for(;!d.h&&w<_;){if(f&N||(L=wt(p,f,h)),e(null!=L),A(d),256>(y=bt(L.G[0],L.H[0],d)))l[m+w]=y,++w,++f>=g&&(f=0,++h<=n&&!(h%16)&&At(u,h));else{if(!(280>y)){b=0;break e}y=gt(y-256,d);var S,P=bt(L.G[4],L.H[4],d);if(A(d),!(w>=(P=vt(g,P=gt(P,d)))&&j-w>=y)){b=0;break e}for(S=0;S<y;++S)l[m+w+S]=l[m+w+S-P];for(w+=y,f+=y;f>=g;)f-=g,++h<=n&&!(h%16)&&At(u,h);w<_&&f&N&&(L=wt(p,f,h))}e(d.h==x(d))}At(u,h>n?n:h);break e}!b||d.h&&w<j?(b=0,u.a=d.h?5:3):u.$=w,n=b}else n=Lt(u,u.V,u.Ba,u.c,u.i,n,Ct);if(!n){s=0;break t}}i+s>=c&&(t.Cc=1),s=1}if(!s)return null;if(t.Cc&&(null!=(s=t.ga)&&(s.mc=null),t.ga=null,0<t.Ga))return alert("todo:WebPDequantizeLevels"),null}return t.nb+i*a}function dn(t,e,n,r,i,o){for(;0<i--;){var s,a=t,c=e+(n?1:0),u=t,l=e+(n?0:3);for(s=0;s<r;++s){var h=u[l+4*s];255!=h&&(h*=32897,a[c+4*s+0]=a[c+4*s+0]*h>>23,a[c+4*s+1]=a[c+4*s+1]*h>>23,a[c+4*s+2]=a[c+4*s+2]*h>>23)}e+=o}}function pn(t,e,n,r,i){for(;0<r--;){var o;for(o=0;o<n;++o){var s=t[e+2*o+0],a=15&(u=t[e+2*o+1]),c=4369*a,u=(240&u|u>>4)*c>>16;t[e+2*o+0]=(240&s|s>>4)*c>>16&240|(15&s|s<<4)*c>>16>>4&15,t[e+2*o+1]=240&u|a}e+=i}}function mn(t,e,n,r,i,o,s,a){var c,u,l=255;for(u=0;u<i;++u){for(c=0;c<r;++c){var h=t[e+c];o[s+4*c]=h,l&=h}e+=n,s+=a}return 255!=l}function gn(t,e,n,r,i){var o;for(o=0;o<i;++o)n[r+o]=t[e+o]>>8}function vn(){xr=dn,Nr=pn,Ar=mn,Lr=gn}function bn(n,r,i){t[n]=function(t,n,o,s,a,c,u,l,h,f,d,p,m,g,v,b,y){var w,j=y-1>>1,_=a[c+0]|u[l+0]<<16,x=h[f+0]|d[p+0]<<16;e(null!=t);var N=3*_+x+131074>>2;for(r(t[n+0],255&N,N>>16,m,g),null!=o&&(N=3*x+_+131074>>2,r(o[s+0],255&N,N>>16,v,b)),w=1;w<=j;++w){var A=a[c+w]|u[l+w]<<16,L=h[f+w]|d[p+w]<<16,S=_+A+x+L+524296,P=S+2*(A+x)>>3;N=P+_>>1,_=(S=S+2*(_+L)>>3)+A>>1,r(t[n+2*w-1],255&N,N>>16,m,g+(2*w-1)*i),r(t[n+2*w-0],255&_,_>>16,m,g+(2*w-0)*i),null!=o&&(N=S+x>>1,_=P+L>>1,r(o[s+2*w-1],255&N,N>>16,v,b+(2*w-1)*i),r(o[s+2*w+0],255&_,_>>16,v,b+(2*w+0)*i)),_=A,x=L}1&y||(N=3*_+x+131074>>2,r(t[n+y-1],255&N,N>>16,m,g+(y-1)*i),null!=o&&(N=3*x+_+131074>>2,r(o[s+y-1],255&N,N>>16,v,b+(y-1)*i)))}}function yn(){vi[Br]=bi,vi[Tr]=wi,vi[qr]=yi,vi[Dr]=ji,vi[Rr]=_i,vi[zr]=xi,vi[Ur]=Ni,vi[Hr]=wi,vi[Vr]=ji,vi[Wr]=_i,vi[Gr]=xi}function wn(t){return t&~Ii?0>t?0:255:t>>ki}function jn(t,e){return wn((19077*t>>8)+(26149*e>>8)-14234)}function _n(t,e,n){return wn((19077*t>>8)-(6419*e>>8)-(13320*n>>8)+8708)}function xn(t,e){return wn((19077*t>>8)+(33050*e>>8)-17685)}function Nn(t,e,n,r,i){r[i+0]=jn(t,n),r[i+1]=_n(t,e,n),r[i+2]=xn(t,e)}function An(t,e,n,r,i){r[i+0]=xn(t,e),r[i+1]=_n(t,e,n),r[i+2]=jn(t,n)}function Ln(t,e,n,r,i){var o=_n(t,e,n);e=o<<3&224|xn(t,e)>>3,r[i+0]=248&jn(t,n)|o>>5,r[i+1]=e}function Sn(t,e,n,r,i){var o=240&xn(t,e)|15;r[i+0]=240&jn(t,n)|_n(t,e,n)>>4,r[i+1]=o}function Pn(t,e,n,r,i){r[i+0]=255,Nn(t,e,n,r,i+1)}function kn(t,e,n,r,i){An(t,e,n,r,i),r[i+3]=255}function In(t,e,n,r,i){Nn(t,e,n,r,i),r[i+3]=255}function Wt(t,e){return 0>t?0:t>e?e:t}function Cn(e,n,r){t[e]=function(t,e,i,o,s,a,c,u,l){for(var h=u+(-2&l)*r;u!=h;)n(t[e+0],i[o+0],s[a+0],c,u),n(t[e+1],i[o+0],s[a+0],c,u+r),e+=2,++o,++a,u+=2*r;1&l&&n(t[e+0],i[o+0],s[a+0],c,u)}}function Fn(t,e,n){return 0==n?0==t?0==e?6:5:0==e?4:0:n}function On(t,e,n,r,i){switch(t>>>30){case 3:sr(e,n,r,i,0);break;case 2:ar(e,n,r,i);break;case 1:ur(e,n,r,i)}}function En(t,e){var n,o,s=e.M,a=e.Nb,c=t.oc,u=t.pc+40,l=t.oc,h=t.pc+584,f=t.oc,d=t.pc+600;for(n=0;16>n;++n)c[u+32*n-1]=129;for(n=0;8>n;++n)l[h+32*n-1]=129,f[d+32*n-1]=129;for(0<s?c[u-1-32]=l[h-1-32]=f[d-1-32]=129:(i(c,u-32-1,127,21),i(l,h-32-1,127,9),i(f,d-32-1,127,9)),o=0;o<t.za;++o){var p=e.ya[e.aa+o];if(0<o){for(n=-1;16>n;++n)r(c,u+32*n-4,c,u+32*n+12,4);for(n=-1;8>n;++n)r(l,h+32*n-4,l,h+32*n+4,4),r(f,d+32*n-4,f,d+32*n+4,4)}var m=t.Gd,g=t.Hd+o,v=p.ad,b=p.Hc;if(0<s&&(r(c,u-32,m[g].y,0,16),r(l,h-32,m[g].f,0,8),r(f,d-32,m[g].ea,0,8)),p.Za){var y=c,w=u-32+16;for(0<s&&(o>=t.za-1?i(y,w,m[g].y[15],4):r(y,w,m[g+1].y,0,4)),n=0;4>n;n++)y[w+128+n]=y[w+256+n]=y[w+384+n]=y[w+0+n];for(n=0;16>n;++n,b<<=2)y=c,w=u+qi[n],fi[p.Ob[n]](y,w),On(b,v,16*+n,y,w)}else if(y=Fn(o,s,p.Ob[0]),hi[y](c,u),0!=b)for(n=0;16>n;++n,b<<=2)On(b,v,16*+n,c,u+qi[n]);for(n=p.Gc,y=Fn(o,s,p.Dd),di[y](l,h),di[y](f,d),b=v,y=l,w=h,255&(p=n>>0)&&(170&p?cr(b,256,y,w):lr(b,256,y,w)),p=f,b=d,255&(n>>=8)&&(170&n?cr(v,320,p,b):lr(v,320,p,b)),s<t.Ub-1&&(r(m[g].y,0,c,u+480,16),r(m[g].f,0,l,h+224,8),r(m[g].ea,0,f,d+224,8)),n=8*a*t.B,m=t.sa,g=t.ta+16*o+16*a*t.R,v=t.qa,p=t.ra+8*o+n,b=t.Ha,y=t.Ia+8*o+n,n=0;16>n;++n)r(m,g+n*t.R,c,u+32*n,16);for(n=0;8>n;++n)r(v,p+n*t.B,l,h+32*n,8),r(b,y+n*t.B,f,d+32*n,8)}}function Mn(t,r,i,o,s,a,c,u,l){var h=[0],f=[0],d=0,p=null!=l?l.kd:0,m=null!=l?l:new rn;if(null==t||12>i)return 7;m.data=t,m.w=r,m.ha=i,r=[r],i=[i],m.gb=[m.gb];t:{var g=r,b=i,y=m.gb;if(e(null!=t),e(null!=b),e(null!=y),y[0]=0,12<=b[0]&&!n(t,g[0],"RIFF")){if(n(t,g[0]+8,"WEBP")){y=3;break t}var w=F(t,g[0]+4);if(12>w||4294967286<w){y=3;break t}if(p&&w>b[0]-8){y=7;break t}y[0]=w,g[0]+=12,b[0]-=12}y=0}if(0!=y)return y;for(w=0<m.gb[0],i=i[0];;){t:{var _=t;b=r,y=i;var x=h,N=f,A=g=[0];if((P=d=[d])[0]=0,8>y[0])y=7;else{if(!n(_,b[0],"VP8X")){if(10!=F(_,b[0]+4)){y=3;break t}if(18>y[0]){y=7;break t}var L=F(_,b[0]+8),S=1+C(_,b[0]+12);if(2147483648<=S*(_=1+C(_,b[0]+15))){y=3;break t}null!=A&&(A[0]=L),null!=x&&(x[0]=S),null!=N&&(N[0]=_),b[0]+=18,y[0]-=18,P[0]=1}y=0}}if(d=d[0],g=g[0],0!=y)return y;if(b=!!(2&g),!w&&d)return 3;if(null!=a&&(a[0]=!!(16&g)),null!=c&&(c[0]=b),null!=u&&(u[0]=0),c=h[0],g=f[0],d&&b&&null==l){y=0;break}if(4>i){y=7;break}if(w&&d||!w&&!d&&!n(t,r[0],"ALPH")){i=[i],m.na=[m.na],m.P=[m.P],m.Sa=[m.Sa];t:{L=t,y=r,w=i;var P=m.gb;x=m.na,N=m.P,A=m.Sa,S=22,e(null!=L),e(null!=w),_=y[0];var k=w[0];for(e(null!=x),e(null!=A),x[0]=null,N[0]=null,A[0]=0;;){if(y[0]=_,w[0]=k,8>k){y=7;break t}var I=F(L,_+4);if(4294967286<I){y=3;break t}var O=8+I+1&-2;if(S+=O,0<P&&S>P){y=3;break t}if(!n(L,_,"VP8 ")||!n(L,_,"VP8L")){y=0;break t}if(k[0]<O){y=7;break t}n(L,_,"ALPH")||(x[0]=L,N[0]=_+8,A[0]=I),_+=O,k-=O}}if(i=i[0],m.na=m.na[0],m.P=m.P[0],m.Sa=m.Sa[0],0!=y)break}i=[i],m.Ja=[m.Ja],m.xa=[m.xa];t:if(P=t,y=r,w=i,x=m.gb[0],N=m.Ja,A=m.xa,L=y[0],_=!n(P,L,"VP8 "),S=!n(P,L,"VP8L"),e(null!=P),e(null!=w),e(null!=N),e(null!=A),8>w[0])y=7;else{if(_||S){if(P=F(P,L+4),12<=x&&P>x-12){y=3;break t}if(p&&P>w[0]-8){y=7;break t}N[0]=P,y[0]+=8,w[0]-=8,A[0]=S}else A[0]=5<=w[0]&&47==P[L+0]&&!(P[L+4]>>5),N[0]=w[0];y=0}if(i=i[0],m.Ja=m.Ja[0],m.xa=m.xa[0],r=r[0],0!=y)break;if(4294967286<m.Ja)return 3;if(null==u||b||(u[0]=m.xa?2:1),c=[c],g=[g],m.xa){if(5>i){y=7;break}u=c,p=g,b=a,null==t||5>i?t=0:5<=i&&47==t[r+0]&&!(t[r+4]>>5)?(w=[0],P=[0],x=[0],v(N=new j,t,r,i),mt(N,w,P,x)?(null!=u&&(u[0]=w[0]),null!=p&&(p[0]=P[0]),null!=b&&(b[0]=x[0]),t=1):t=0):t=0}else{if(10>i){y=7;break}u=g,null==t||10>i||!Xt(t,r+3,i-3)?t=0:(p=t[r+0]|t[r+1]<<8|t[r+2]<<16,b=16383&(t[r+7]<<8|t[r+6]),t=16383&(t[r+9]<<8|t[r+8]),1&p||3<(p>>1&7)||!(p>>4&1)||p>>5>=m.Ja||!b||!t?t=0:(c&&(c[0]=b),u&&(u[0]=t),t=1))}if(!t)return 3;if(c=c[0],g=g[0],d&&(h[0]!=c||f[0]!=g))return 3;null!=l&&(l[0]=m,l.offset=r-l.w,e(4294967286>r-l.w),e(l.offset==l.ha-i));break}return 0==y||7==y&&d&&null==l?(null!=a&&(a[0]|=null!=m.na&&0<m.na.length),null!=o&&(o[0]=c),null!=s&&(s[0]=g),0):y}function Bn(t,e,n){var r=e.width,i=e.height,o=0,s=0,a=r,c=i;if(e.Da=null!=t&&0<t.Da,e.Da&&(a=t.cd,c=t.bd,o=t.v,s=t.j,11>n||(o&=-2,s&=-2),0>o||0>s||0>=a||0>=c||o+a>r||s+c>i))return 0;if(e.v=o,e.j=s,e.va=o+a,e.o=s+c,e.U=a,e.T=c,e.da=null!=t&&0<t.da,e.da){if(!B(a,c,n=[t.ib],o=[t.hb]))return 0;e.ib=n[0],e.hb=o[0]}return e.ob=null!=t&&t.ob,e.Kb=null==t||!t.Sd,e.da&&(e.ob=e.ib<3*r/4&&e.hb<3*i/4,e.Kb=0),1}function Tn(t){if(null==t)return 2;if(11>t.S){var e=t.f.RGBA;e.fb+=(t.height-1)*e.A,e.A=-e.A}else e=t.f.kb,t=t.height,e.O+=(t-1)*e.fa,e.fa=-e.fa,e.N+=(t-1>>1)*e.Ab,e.Ab=-e.Ab,e.W+=(t-1>>1)*e.Db,e.Db=-e.Db,null!=e.F&&(e.J+=(t-1)*e.lb,e.lb=-e.lb);return 0}function qn(t,e,n,r){if(null==r||0>=t||0>=e)return 2;if(null!=n){if(n.Da){var i=n.cd,s=n.bd,a=-2&n.v,c=-2&n.j;if(0>a||0>c||0>=i||0>=s||a+i>t||c+s>e)return 2;t=i,e=s}if(n.da){if(!B(t,e,i=[n.ib],s=[n.hb]))return 2;t=i[0],e=s[0]}}r.width=t,r.height=e;t:{var u=r.width,l=r.height;if(t=r.S,0>=u||0>=l||!(t>=Br&&13>t))t=2;else{if(0>=r.Rd&&null==r.sd){a=s=i=e=0;var h=(c=u*Ui[t])*l;if(11>t||(s=(l+1)/2*(e=(u+1)/2),12==t&&(a=(i=u)*l)),null==(l=o(h+2*s+a))){t=1;break t}r.sd=l,11>t?((u=r.f.RGBA).eb=l,u.fb=0,u.A=c,u.size=h):((u=r.f.kb).y=l,u.O=0,u.fa=c,u.Fd=h,u.f=l,u.N=0+h,u.Ab=e,u.Cd=s,u.ea=l,u.W=0+h+s,u.Db=e,u.Ed=s,12==t&&(u.F=l,u.J=0+h+2*s),u.Tc=a,u.lb=i)}if(e=1,i=r.S,s=r.width,a=r.height,i>=Br&&13>i)if(11>i)t=r.f.RGBA,e&=(c=Math.abs(t.A))*(a-1)+s<=t.size,e&=c>=s*Ui[i],e&=null!=t.eb;else{t=r.f.kb,c=(s+1)/2,h=(a+1)/2,u=Math.abs(t.fa),l=Math.abs(t.Ab);var f=Math.abs(t.Db),d=Math.abs(t.lb),p=d*(a-1)+s;e&=u*(a-1)+s<=t.Fd,e&=l*(h-1)+c<=t.Cd,e=(e&=f*(h-1)+c<=t.Ed)&u>=s&l>=c&f>=c,e&=null!=t.y,e&=null!=t.f,e&=null!=t.ea,12==i&&(e&=d>=s,e&=p<=t.Tc,e&=null!=t.F)}else e=0;t=e?0:2}}return 0!=t||null!=n&&n.fd&&(t=Tn(r)),t}var Dn=64,Rn=[0,1,3,7,15,31,63,127,255,511,1023,2047,4095,8191,16383,32767,65535,131071,262143,524287,1048575,2097151,4194303,8388607,16777215],zn=24,Un=32,Hn=8,Vn=[0,0,1,1,2,2,2,2,3,3,3,3,3,3,3,3,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,4,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,5,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,6,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7,7];D("Predictor0","PredictorAdd0"),t.Predictor0=function(){return **********},t.Predictor1=function(t){return t},t.Predictor2=function(t,e,n){return e[n+0]},t.Predictor3=function(t,e,n){return e[n+1]},t.Predictor4=function(t,e,n){return e[n-1]},t.Predictor5=function(t,e,n){return z(z(t,e[n+1]),e[n+0])},t.Predictor6=function(t,e,n){return z(t,e[n-1])},t.Predictor7=function(t,e,n){return z(t,e[n+0])},t.Predictor8=function(t,e,n){return z(e[n-1],e[n+0])},t.Predictor9=function(t,e,n){return z(e[n+0],e[n+1])},t.Predictor10=function(t,e,n){return z(z(t,e[n-1]),z(e[n+0],e[n+1]))},t.Predictor11=function(t,e,n){var r=e[n+0];return 0>=V(r>>24&255,t>>24&255,(e=e[n-1])>>24&255)+V(r>>16&255,t>>16&255,e>>16&255)+V(r>>8&255,t>>8&255,e>>8&255)+V(255&r,255&t,255&e)?r:t},t.Predictor12=function(t,e,n){var r=e[n+0];return(U((t>>24&255)+(r>>24&255)-((e=e[n-1])>>24&255))<<24|U((t>>16&255)+(r>>16&255)-(e>>16&255))<<16|U((t>>8&255)+(r>>8&255)-(e>>8&255))<<8|U((255&t)+(255&r)-(255&e)))>>>0},t.Predictor13=function(t,e,n){var r=e[n-1];return(H((t=z(t,e[n+0]))>>24&255,r>>24&255)<<24|H(t>>16&255,r>>16&255)<<16|H(t>>8&255,r>>8&255)<<8|H(t>>0&255,r>>0&255))>>>0};var Wn=t.PredictorAdd0;t.PredictorAdd1=W,D("Predictor2","PredictorAdd2"),D("Predictor3","PredictorAdd3"),D("Predictor4","PredictorAdd4"),D("Predictor5","PredictorAdd5"),D("Predictor6","PredictorAdd6"),D("Predictor7","PredictorAdd7"),D("Predictor8","PredictorAdd8"),D("Predictor9","PredictorAdd9"),D("Predictor10","PredictorAdd10"),D("Predictor11","PredictorAdd11"),D("Predictor12","PredictorAdd12"),D("Predictor13","PredictorAdd13");var Gn=t.PredictorAdd2;X("ColorIndexInverseTransform","MapARGB","32b",(function(t){return t>>8&255}),(function(t){return t})),X("VP8LColorIndexInverseTransformAlpha","MapAlpha","8b",(function(t){return t}),(function(t){return t>>8&255}));var Yn,Jn=t.ColorIndexInverseTransform,Xn=t.MapARGB,Kn=t.VP8LColorIndexInverseTransformAlpha,$n=t.MapAlpha,Zn=t.VP8LPredictorsAdd=[];Zn.length=16,(t.VP8LPredictors=[]).length=16,(t.VP8LPredictorsAdd_C=[]).length=16,(t.VP8LPredictors_C=[]).length=16;var Qn,tr,er,nr,rr,ir,or,sr,ar,cr,ur,lr,hr,fr,dr,pr,mr,gr,vr,br,yr,wr,jr,_r,xr,Nr,Ar,Lr,Sr=o(511),Pr=o(2041),kr=o(225),Ir=o(767),Cr=0,Fr=Pr,Or=kr,Er=Ir,Mr=Sr,Br=0,Tr=1,qr=2,Dr=3,Rr=4,zr=5,Ur=6,Hr=7,Vr=8,Wr=9,Gr=10,Yr=[2,3,7],Jr=[3,3,11],Xr=[280,256,256,256,40],Kr=[0,1,1,1,0],$r=[17,18,0,1,2,3,4,5,16,6,7,8,9,10,11,12,13,14,15],Zr=[24,7,23,25,40,6,39,41,22,26,38,42,56,5,55,57,21,27,54,58,37,43,72,4,71,73,20,28,53,59,70,74,36,44,88,69,75,52,60,3,87,89,19,29,86,90,35,45,68,76,85,91,51,61,104,2,103,105,18,30,102,106,34,46,84,92,67,77,101,107,50,62,120,1,119,121,83,93,17,31,100,108,66,78,118,122,33,47,117,123,49,63,99,109,82,94,0,116,124,65,79,16,32,98,110,48,115,125,81,95,64,114,126,97,111,80,113,127,96,112],Qr=[2954,2956,2958,2962,2970,2986,3018,3082,3212,3468,3980,5004],ti=8,ei=[4,5,6,7,8,9,10,10,11,12,13,14,15,16,17,17,18,19,20,20,21,21,22,22,23,23,24,25,25,26,27,28,29,30,31,32,33,34,35,36,37,37,38,39,40,41,42,43,44,45,46,46,47,48,49,50,51,52,53,54,55,56,57,58,59,60,61,62,63,64,65,66,67,68,69,70,71,72,73,74,75,76,76,77,78,79,80,81,82,83,84,85,86,87,88,89,91,93,95,96,98,100,101,102,104,106,108,110,112,114,116,118,122,124,126,128,130,132,134,136,138,140,143,145,148,151,154,157],ni=[4,5,6,7,8,9,10,11,12,13,14,15,16,17,18,19,20,21,22,23,24,25,26,27,28,29,30,31,32,33,34,35,36,37,38,39,40,41,42,43,44,45,46,47,48,49,50,51,52,53,54,55,56,57,58,60,62,64,66,68,70,72,74,76,78,80,82,84,86,88,90,92,94,96,98,100,102,104,106,108,110,112,114,116,119,122,125,128,131,134,137,140,143,146,149,152,155,158,161,164,167,170,173,177,181,185,189,193,197,201,205,209,213,217,221,225,229,234,239,245,249,254,259,264,269,274,279,284],ri=null,ii=[[173,148,140,0],[176,155,140,135,0],[180,157,141,134,130,0],[254,254,243,230,196,177,153,140,133,130,129,0]],oi=[0,1,4,8,5,2,3,6,9,12,13,10,7,11,14,15],si=[-0,1,-1,2,-2,3,4,6,-3,5,-4,-5,-6,7,-7,8,-8,-9],ai=[[[[128,128,128,128,128,128,128,128,128,128,128],[128,128,128,128,128,128,128,128,128,128,128],[128,128,128,128,128,128,128,128,128,128,128]],[[253,136,254,255,228,219,128,128,128,128,128],[189,129,242,255,227,213,255,219,128,128,128],[106,126,227,252,214,209,255,255,128,128,128]],[[1,98,248,255,236,226,255,255,128,128,128],[181,133,238,254,221,234,255,154,128,128,128],[78,134,202,247,198,180,255,219,128,128,128]],[[1,185,249,255,243,255,128,128,128,128,128],[184,150,247,255,236,224,128,128,128,128,128],[77,110,216,255,236,230,128,128,128,128,128]],[[1,101,251,255,241,255,128,128,128,128,128],[170,139,241,252,236,209,255,255,128,128,128],[37,116,196,243,228,255,255,255,128,128,128]],[[1,204,254,255,245,255,128,128,128,128,128],[207,160,250,255,238,128,128,128,128,128,128],[102,103,231,255,211,171,128,128,128,128,128]],[[1,152,252,255,240,255,128,128,128,128,128],[177,135,243,255,234,225,128,128,128,128,128],[80,129,211,255,194,224,128,128,128,128,128]],[[1,1,255,128,128,128,128,128,128,128,128],[246,1,255,128,128,128,128,128,128,128,128],[255,128,128,128,128,128,128,128,128,128,128]]],[[[198,35,237,223,193,187,162,160,145,155,62],[131,45,198,221,172,176,220,157,252,221,1],[68,47,146,208,149,167,221,162,255,223,128]],[[1,149,241,255,221,224,255,255,128,128,128],[184,141,234,253,222,220,255,199,128,128,128],[81,99,181,242,176,190,249,202,255,255,128]],[[1,129,232,253,214,197,242,196,255,255,128],[99,121,210,250,201,198,255,202,128,128,128],[23,91,163,242,170,187,247,210,255,255,128]],[[1,200,246,255,234,255,128,128,128,128,128],[109,178,241,255,231,245,255,255,128,128,128],[44,130,201,253,205,192,255,255,128,128,128]],[[1,132,239,251,219,209,255,165,128,128,128],[94,136,225,251,218,190,255,255,128,128,128],[22,100,174,245,186,161,255,199,128,128,128]],[[1,182,249,255,232,235,128,128,128,128,128],[124,143,241,255,227,234,128,128,128,128,128],[35,77,181,251,193,211,255,205,128,128,128]],[[1,157,247,255,236,231,255,255,128,128,128],[121,141,235,255,225,227,255,255,128,128,128],[45,99,188,251,195,217,255,224,128,128,128]],[[1,1,251,255,213,255,128,128,128,128,128],[203,1,248,255,255,128,128,128,128,128,128],[137,1,177,255,224,255,128,128,128,128,128]]],[[[253,9,248,251,207,208,255,192,128,128,128],[175,13,224,243,193,185,249,198,255,255,128],[73,17,171,221,161,179,236,167,255,234,128]],[[1,95,247,253,212,183,255,255,128,128,128],[239,90,244,250,211,209,255,255,128,128,128],[155,77,195,248,188,195,255,255,128,128,128]],[[1,24,239,251,218,219,255,205,128,128,128],[201,51,219,255,196,186,128,128,128,128,128],[69,46,190,239,201,218,255,228,128,128,128]],[[1,191,251,255,255,128,128,128,128,128,128],[223,165,249,255,213,255,128,128,128,128,128],[141,124,248,255,255,128,128,128,128,128,128]],[[1,16,248,255,255,128,128,128,128,128,128],[190,36,230,255,236,255,128,128,128,128,128],[149,1,255,128,128,128,128,128,128,128,128]],[[1,226,255,128,128,128,128,128,128,128,128],[247,192,255,128,128,128,128,128,128,128,128],[240,128,255,128,128,128,128,128,128,128,128]],[[1,134,252,255,255,128,128,128,128,128,128],[213,62,250,255,255,128,128,128,128,128,128],[55,93,255,128,128,128,128,128,128,128,128]],[[128,128,128,128,128,128,128,128,128,128,128],[128,128,128,128,128,128,128,128,128,128,128],[128,128,128,128,128,128,128,128,128,128,128]]],[[[202,24,213,235,186,191,220,160,240,175,255],[126,38,182,232,169,184,228,174,255,187,128],[61,46,138,219,151,178,240,170,255,216,128]],[[1,112,230,250,199,191,247,159,255,255,128],[166,109,228,252,211,215,255,174,128,128,128],[39,77,162,232,172,180,245,178,255,255,128]],[[1,52,220,246,198,199,249,220,255,255,128],[124,74,191,243,183,193,250,221,255,255,128],[24,71,130,219,154,170,243,182,255,255,128]],[[1,182,225,249,219,240,255,224,128,128,128],[149,150,226,252,216,205,255,171,128,128,128],[28,108,170,242,183,194,254,223,255,255,128]],[[1,81,230,252,204,203,255,192,128,128,128],[123,102,209,247,188,196,255,233,128,128,128],[20,95,153,243,164,173,255,203,128,128,128]],[[1,222,248,255,216,213,128,128,128,128,128],[168,175,246,252,235,205,255,255,128,128,128],[47,116,215,255,211,212,255,255,128,128,128]],[[1,121,236,253,212,214,255,255,128,128,128],[141,84,213,252,201,202,255,219,128,128,128],[42,80,160,240,162,185,255,205,128,128,128]],[[1,1,255,128,128,128,128,128,128,128,128],[244,1,255,128,128,128,128,128,128,128,128],[238,1,255,128,128,128,128,128,128,128,128]]]],ci=[[[231,120,48,89,115,113,120,152,112],[152,179,64,126,170,118,46,70,95],[175,69,143,80,85,82,72,155,103],[56,58,10,171,218,189,17,13,152],[114,26,17,163,44,195,21,10,173],[121,24,80,195,26,62,44,64,85],[144,71,10,38,171,213,144,34,26],[170,46,55,19,136,160,33,206,71],[63,20,8,114,114,208,12,9,226],[81,40,11,96,182,84,29,16,36]],[[134,183,89,137,98,101,106,165,148],[72,187,100,130,157,111,32,75,80],[66,102,167,99,74,62,40,234,128],[41,53,9,178,241,141,26,8,107],[74,43,26,146,73,166,49,23,157],[65,38,105,160,51,52,31,115,128],[104,79,12,27,217,255,87,17,7],[87,68,71,44,114,51,15,186,23],[47,41,14,110,182,183,21,17,194],[66,45,25,102,197,189,23,18,22]],[[88,88,147,150,42,46,45,196,205],[43,97,183,117,85,38,35,179,61],[39,53,200,87,26,21,43,232,171],[56,34,51,104,114,102,29,93,77],[39,28,85,171,58,165,90,98,64],[34,22,116,206,23,34,43,166,73],[107,54,32,26,51,1,81,43,31],[68,25,106,22,64,171,36,225,114],[34,19,21,102,132,188,16,76,124],[62,18,78,95,85,57,50,48,51]],[[193,101,35,159,215,111,89,46,111],[60,148,31,172,219,228,21,18,111],[112,113,77,85,179,255,38,120,114],[40,42,1,196,245,209,10,25,109],[88,43,29,140,166,213,37,43,154],[61,63,30,155,67,45,68,1,209],[100,80,8,43,154,1,51,26,71],[142,78,78,16,255,128,34,197,171],[41,40,5,102,211,183,4,1,221],[51,50,17,168,209,192,23,25,82]],[[138,31,36,171,27,166,38,44,229],[67,87,58,169,82,115,26,59,179],[63,59,90,180,59,166,93,73,154],[40,40,21,116,143,209,34,39,175],[47,15,16,183,34,223,49,45,183],[46,17,33,183,6,98,15,32,183],[57,46,22,24,128,1,54,17,37],[65,32,73,115,28,128,23,128,205],[40,3,9,115,51,192,18,6,223],[87,37,9,115,59,77,64,21,47]],[[104,55,44,218,9,54,53,130,226],[64,90,70,205,40,41,23,26,57],[54,57,112,184,5,41,38,166,213],[30,34,26,133,152,116,10,32,134],[39,19,53,221,26,114,32,73,255],[31,9,65,234,2,15,1,118,73],[75,32,12,51,192,255,160,43,51],[88,31,35,67,102,85,55,186,85],[56,21,23,111,59,205,45,37,192],[55,38,70,124,73,102,1,34,98]],[[125,98,42,88,104,85,117,175,82],[95,84,53,89,128,100,113,101,45],[75,79,123,47,51,128,81,171,1],[57,17,5,71,102,57,53,41,49],[38,33,13,121,57,73,26,1,85],[41,10,67,138,77,110,90,47,114],[115,21,2,10,102,255,166,23,6],[101,29,16,10,85,128,101,196,26],[57,18,10,102,102,213,34,20,43],[117,20,15,36,163,128,68,1,26]],[[102,61,71,37,34,53,31,243,192],[69,60,71,38,73,119,28,222,37],[68,45,128,34,1,47,11,245,171],[62,17,19,70,146,85,55,62,70],[37,43,37,154,100,163,85,160,1],[63,9,92,136,28,64,32,201,85],[75,15,9,9,64,255,184,119,16],[86,6,28,5,64,255,25,248,1],[56,8,17,132,137,255,55,116,128],[58,15,20,82,135,57,26,121,40]],[[164,50,31,137,154,133,25,35,218],[51,103,44,131,131,123,31,6,158],[86,40,64,135,148,224,45,183,128],[22,26,17,131,240,154,14,1,209],[45,16,21,91,64,222,7,1,197],[56,21,39,155,60,138,23,102,213],[83,12,13,54,192,255,68,47,28],[85,26,85,85,128,128,32,146,171],[18,11,7,63,144,171,4,4,246],[35,27,10,146,174,171,12,26,128]],[[190,80,35,99,180,80,126,54,45],[85,126,47,87,176,51,41,20,32],[101,75,128,139,118,146,116,128,85],[56,41,15,176,236,85,37,9,62],[71,30,17,119,118,255,17,18,138],[101,38,60,138,55,70,43,26,142],[146,36,19,30,171,255,97,27,20],[138,45,61,62,219,1,81,188,64],[32,41,20,117,151,142,20,21,163],[112,19,12,61,195,128,48,4,24]]],ui=[[[[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[176,246,255,255,255,255,255,255,255,255,255],[223,241,252,255,255,255,255,255,255,255,255],[249,253,253,255,255,255,255,255,255,255,255]],[[255,244,252,255,255,255,255,255,255,255,255],[234,254,254,255,255,255,255,255,255,255,255],[253,255,255,255,255,255,255,255,255,255,255]],[[255,246,254,255,255,255,255,255,255,255,255],[239,253,254,255,255,255,255,255,255,255,255],[254,255,254,255,255,255,255,255,255,255,255]],[[255,248,254,255,255,255,255,255,255,255,255],[251,255,254,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,253,254,255,255,255,255,255,255,255,255],[251,254,254,255,255,255,255,255,255,255,255],[254,255,254,255,255,255,255,255,255,255,255]],[[255,254,253,255,254,255,255,255,255,255,255],[250,255,254,255,254,255,255,255,255,255,255],[254,255,255,255,255,255,255,255,255,255,255]],[[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]]],[[[217,255,255,255,255,255,255,255,255,255,255],[225,252,241,253,255,255,254,255,255,255,255],[234,250,241,250,253,255,253,254,255,255,255]],[[255,254,255,255,255,255,255,255,255,255,255],[223,254,254,255,255,255,255,255,255,255,255],[238,253,254,254,255,255,255,255,255,255,255]],[[255,248,254,255,255,255,255,255,255,255,255],[249,254,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,253,255,255,255,255,255,255,255,255,255],[247,254,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,253,254,255,255,255,255,255,255,255,255],[252,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,254,254,255,255,255,255,255,255,255,255],[253,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,254,253,255,255,255,255,255,255,255,255],[250,255,255,255,255,255,255,255,255,255,255],[254,255,255,255,255,255,255,255,255,255,255]],[[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]]],[[[186,251,250,255,255,255,255,255,255,255,255],[234,251,244,254,255,255,255,255,255,255,255],[251,251,243,253,254,255,254,255,255,255,255]],[[255,253,254,255,255,255,255,255,255,255,255],[236,253,254,255,255,255,255,255,255,255,255],[251,253,253,254,254,255,255,255,255,255,255]],[[255,254,254,255,255,255,255,255,255,255,255],[254,254,254,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,254,255,255,255,255,255,255,255,255,255],[254,254,255,255,255,255,255,255,255,255,255],[254,255,255,255,255,255,255,255,255,255,255]],[[255,255,255,255,255,255,255,255,255,255,255],[254,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]]],[[[248,255,255,255,255,255,255,255,255,255,255],[250,254,252,254,255,255,255,255,255,255,255],[248,254,249,253,255,255,255,255,255,255,255]],[[255,253,253,255,255,255,255,255,255,255,255],[246,253,253,255,255,255,255,255,255,255,255],[252,254,251,254,254,255,255,255,255,255,255]],[[255,254,252,255,255,255,255,255,255,255,255],[248,254,253,255,255,255,255,255,255,255,255],[253,255,254,254,255,255,255,255,255,255,255]],[[255,251,254,255,255,255,255,255,255,255,255],[245,251,254,255,255,255,255,255,255,255,255],[253,253,254,255,255,255,255,255,255,255,255]],[[255,251,253,255,255,255,255,255,255,255,255],[252,253,254,255,255,255,255,255,255,255,255],[255,254,255,255,255,255,255,255,255,255,255]],[[255,252,255,255,255,255,255,255,255,255,255],[249,255,254,255,255,255,255,255,255,255,255],[255,255,254,255,255,255,255,255,255,255,255]],[[255,255,253,255,255,255,255,255,255,255,255],[250,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]],[[255,255,255,255,255,255,255,255,255,255,255],[254,255,255,255,255,255,255,255,255,255,255],[255,255,255,255,255,255,255,255,255,255,255]]]],li=[0,1,2,3,6,4,5,6,6,6,6,6,6,6,6,7,0],hi=[],fi=[],di=[],pi=1,mi=2,gi=[],vi=[];bn("UpsampleRgbLinePair",Nn,3),bn("UpsampleBgrLinePair",An,3),bn("UpsampleRgbaLinePair",In,4),bn("UpsampleBgraLinePair",kn,4),bn("UpsampleArgbLinePair",Pn,4),bn("UpsampleRgba4444LinePair",Sn,2),bn("UpsampleRgb565LinePair",Ln,2);var bi=t.UpsampleRgbLinePair,yi=t.UpsampleBgrLinePair,wi=t.UpsampleRgbaLinePair,ji=t.UpsampleBgraLinePair,_i=t.UpsampleArgbLinePair,xi=t.UpsampleRgba4444LinePair,Ni=t.UpsampleRgb565LinePair,Ai=16,Li=1<<Ai-1,Si=-227,Pi=482,ki=6,Ii=(256<<ki)-1,Ci=0,Fi=o(256),Oi=o(256),Ei=o(256),Mi=o(256),Bi=o(Pi-Si),Ti=o(Pi-Si);Cn("YuvToRgbRow",Nn,3),Cn("YuvToBgrRow",An,3),Cn("YuvToRgbaRow",In,4),Cn("YuvToBgraRow",kn,4),Cn("YuvToArgbRow",Pn,4),Cn("YuvToRgba4444Row",Sn,2),Cn("YuvToRgb565Row",Ln,2);var qi=[0,4,8,12,128,132,136,140,256,260,264,268,384,388,392,396],Di=[0,2,8],Ri=[8,7,6,4,4,2,2,2,1,1,1,1],zi=1;this.WebPDecodeRGBA=function(t,n,r,i,o){var s=Tr,a=new nn,c=new st;a.ba=c,c.S=s,c.width=[c.width],c.height=[c.height];var u=c.width,l=c.height,h=new at;if(null==h||null==t)var f=2;else e(null!=h),f=Mn(t,n,r,h.width,h.height,h.Pd,h.Qd,h.format,null);if(0!=f?u=0:(null!=u&&(u[0]=h.width[0]),null!=l&&(l[0]=h.height[0]),u=1),u){c.width=c.width[0],c.height=c.height[0],null!=i&&(i[0]=c.width),null!=o&&(o[0]=c.height);t:{if(i=new Gt,(o=new rn).data=t,o.w=n,o.ha=r,o.kd=1,n=[0],e(null!=o),(0==(t=Mn(o.data,o.w,o.ha,null,null,null,n,null,o))||7==t)&&n[0]&&(t=4),0==(n=t)){if(e(null!=a),i.data=o.data,i.w=o.w+o.offset,i.ha=o.ha-o.offset,i.put=dt,i.ac=ft,i.bc=pt,i.ma=a,o.xa){if(null==(t=Pt())){a=1;break t}if(function(t,n){var r=[0],i=[0],o=[0];e:for(;;){if(null==t)return 0;if(null==n)return t.a=2,0;if(t.l=n,t.a=0,v(t.m,n.data,n.w,n.ha),!mt(t.m,r,i,o)){t.a=3;break e}if(t.xb=mi,n.width=r[0],n.height=i[0],!kt(r[0],i[0],1,t,null))break e;return 1}return e(0!=t.a),0}(t,i)){if(i=0==(n=qn(i.width,i.height,a.Oa,a.ba))){e:{i=t;n:for(;;){if(null==i){i=0;break e}if(e(null!=i.s.yc),e(null!=i.s.Ya),e(0<i.s.Wb),e(null!=(r=i.l)),e(null!=(o=r.ma)),0!=i.xb){if(i.ca=o.ba,i.tb=o.tb,e(null!=i.ca),!Bn(o.Oa,r,Dr)){i.a=2;break n}if(!It(i,r.width))break n;if(r.da)break n;if((r.da||rt(i.ca.S))&&vn(),11>i.ca.S||(alert("todo:WebPInitConvertARGBToYUV"),null!=i.ca.f.kb.F&&vn()),i.Pb&&0<i.s.ua&&null==i.s.vb.X&&!O(i.s.vb,i.s.Wa.Xa)){i.a=1;break n}i.xb=0}if(!Lt(i,i.V,i.Ba,i.c,i.i,r.o,_t))break n;o.Dc=i.Ma,i=1;break e}e(0!=i.a),i=0}i=!i}i&&(n=t.a)}else n=t.a}else{if(null==(t=new Yt)){a=1;break t}if(t.Fa=o.na,t.P=o.P,t.qc=o.Sa,Kt(t,i)){if(0==(n=qn(i.width,i.height,a.Oa,a.ba))){if(t.Aa=0,r=a.Oa,e(null!=(o=t)),null!=r){if(0<(u=0>(u=r.Md)?0:100<u?255:255*u/100)){for(l=h=0;4>l;++l)12>(f=o.pb[l]).lc&&(f.ia=u*Ri[0>f.lc?0:f.lc]>>3),h|=f.ia;h&&(alert("todo:VP8InitRandom"),o.ia=1)}o.Ga=r.Id,100<o.Ga?o.Ga=100:0>o.Ga&&(o.Ga=0)}Qt(t,i)||(n=t.a)}}else n=t.a}0==n&&null!=a.Oa&&a.Oa.fd&&(n=Tn(a.ba))}a=n}s=0!=a?null:11>s?c.f.RGBA.eb:c.f.kb.y}else s=null;return s};var Ui=[3,4,3,4,4,2,2,4,4,4,2,1,1]};function u(t,e){for(var n="",r=0;r<4;r++)n+=String.fromCharCode(t[e++]);return n}function l(t,e){return(t[e+0]<<0|t[e+1]<<8|t[e+2]<<16)>>>0}function h(t,e){return(t[e+0]<<0|t[e+1]<<8|t[e+2]<<16|t[e+3]<<24)>>>0}new c;var f=[0],d=[0],p=[],m=new c,g=t,v=function(t,e){var n={},r=0,i=!1,o=0,s=0;if(n.frames=[],!
/** @license
   * Copyright (c) 2017 Dominik Homberger
  Permission is hereby granted, free of charge, to any person obtaining a copy of this software and associated documentation files (the "Software"), to deal in the Software without restriction, including without limitation the rights to use, copy, modify, merge, publish, distribute, sublicense, and/or sell copies of the Software, and to permit persons to whom the Software is furnished to do so, subject to the following conditions:
  The above copyright notice and this permission notice shall be included in all copies or substantial portions of the Software.
  THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND, EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
  https://webpjs.appspot.com
  WebPRiffParser <EMAIL>
  */
function(t,e,n,r){for(var i=0;i<r;i++)if(t[e+i]!=n.charCodeAt(i))return!0;return!1}(t,e,"RIFF",4)){var a,c;for(h(t,e+=4),e+=8;e<t.length;){var f=u(t,e),d=h(t,e+=4);e+=4;var p=d+(1&d);switch(f){case"VP8 ":case"VP8L":void 0===n.frames[r]&&(n.frames[r]={}),(v=n.frames[r]).src_off=i?s:e-8,v.src_size=o+d+8,r++,i&&(i=!1,o=0,s=0);break;case"VP8X":(v=n.header={}).feature_flags=t[e];var m=e+4;v.canvas_width=1+l(t,m),m+=3,v.canvas_height=1+l(t,m),m+=3;break;case"ALPH":i=!0,o=p+8,s=e-8;break;case"ANIM":(v=n.header).bgcolor=h(t,e),m=e+4,v.loop_count=(a=t)[(c=m)+0]<<0|a[c+1]<<8,m+=2;break;case"ANMF":var g,v;(v=n.frames[r]={}).offset_x=2*l(t,e),e+=3,v.offset_y=2*l(t,e),e+=3,v.width=1+l(t,e),e+=3,v.height=1+l(t,e),e+=3,v.duration=l(t,e),e+=3,g=t[e++],v.dispose=1&g,v.blend=g>>1&1}"ANMF"!=f&&(e+=p)}return n}}(g,0);v.response=g,v.rgbaoutput=!0,v.dataurl=!1;var b=v.header?v.header:null,y=v.frames?v.frames:null;if(b){b.loop_counter=b.loop_count,f=[b.canvas_height],d=[b.canvas_width];for(var w=0;w<y.length&&0!=y[w].blend;w++);}var j=y[0],_=m.WebPDecodeRGBA(g,j.src_off,j.src_size,d,f);j.rgba=_,j.imgwidth=d[0],j.imgheight=f[0];for(var x=0;x<d[0]*f[0]*4;x++)p[x]=_[x];return this.width=d,this.height=f,this.data=p,this}!function(t){var e=function(){return"function"==typeof o["b"]},n=function(e,n,i,l){var h=4,f=a;switch(l){case t.image_compression.FAST:h=1,f=s;break;case t.image_compression.MEDIUM:h=6,f=c;break;case t.image_compression.SLOW:h=9,f=u}e=r(e,n,i,f);var d=Object(o["b"])(e,{level:h});return t.__addimage__.arrayBufferToBinaryString(d)},r=function(t,e,n,r){for(var i,o,s,a=t.length/e,c=new Uint8Array(t.length+a),u=h(),l=0;l<a;l+=1){if(s=l*e,i=t.subarray(s,s+e),r)c.set(r(i,n,o),s+l);else{for(var d,p=u.length,m=[];d<p;d+=1)m[d]=u[d](i,n,o);var g=f(m.concat());c.set(m[g],s+l)}o=i}return c},i=function(t){var e=Array.apply([],t);return e.unshift(0),e},s=function(t,e){var n,r=[],i=t.length;r[0]=1;for(var o=0;o<i;o+=1)n=t[o-e]||0,r[o+1]=t[o]-n+256&255;return r},a=function(t,e,n){var r,i=[],o=t.length;i[0]=2;for(var s=0;s<o;s+=1)r=n&&n[s]||0,i[s+1]=t[s]-r+256&255;return i},c=function(t,e,n){var r,i,o=[],s=t.length;o[0]=3;for(var a=0;a<s;a+=1)r=t[a-e]||0,i=n&&n[a]||0,o[a+1]=t[a]+256-(r+i>>>1)&255;return o},u=function(t,e,n){var r,i,o,s,a=[],c=t.length;a[0]=4;for(var u=0;u<c;u+=1)r=t[u-e]||0,i=n&&n[u]||0,o=n&&n[u-e]||0,s=l(r,i,o),a[u+1]=t[u]-s+256&255;return a},l=function(t,e,n){if(t===e&&e===n)return t;var r=Math.abs(e-n),i=Math.abs(t-n),o=Math.abs(t+e-n-n);return r<=i&&r<=o?t:i<=o?e:n},h=function(){return[i,s,a,c,u]},f=function(t){var e=t.map((function(t){return t.reduce((function(t,e){return t+Math.abs(e)}),0)}));return e.indexOf(Math.min.apply(null,e))};t.processPNG=function(r,i,o,s){var a,c,u,l,h,f,d,p,m,g,v,b,y,w,j,_=this.decode.FLATE_DECODE,x="";if(this.__addimage__.isArrayBuffer(r)&&(r=new Uint8Array(r)),this.__addimage__.isArrayBufferView(r)){if(r=(u=new Qt(r)).imgData,c=u.bits,a=u.colorSpace,h=u.colors,-1!==[4,6].indexOf(u.colorType)){if(8===u.bits){m=(p=32==u.pixelBitlength?new Uint32Array(u.decodePixels().buffer):16==u.pixelBitlength?new Uint16Array(u.decodePixels().buffer):new Uint8Array(u.decodePixels().buffer)).length,v=new Uint8Array(m*u.colors),g=new Uint8Array(m);var N,A=u.pixelBitlength-u.bits;for(w=0,j=0;w<m;w++){for(y=p[w],N=0;N<A;)v[j++]=y>>>N&255,N+=u.bits;g[w]=y>>>N&255}}if(16===u.bits){m=(p=new Uint32Array(u.decodePixels().buffer)).length,v=new Uint8Array(m*(32/u.pixelBitlength)*u.colors),g=new Uint8Array(m*(32/u.pixelBitlength)),b=u.colors>1,w=0,j=0;for(var L=0;w<m;)y=p[w++],v[j++]=y>>>0&255,b&&(v[j++]=y>>>16&255,y=p[w++],v[j++]=y>>>0&255),g[L++]=y>>>16&255;c=8}s!==t.image_compression.NONE&&e()?(r=n(v,u.width*u.colors,u.colors,s),d=n(g,u.width,1,s)):(r=v,d=g,_=void 0)}if(3===u.colorType&&(a=this.color_spaces.INDEXED,f=u.palette,u.transparency.indexed)){var S=u.transparency.indexed,P=0;for(w=0,m=S.length;w<m;++w)P+=S[w];if((P/=255)===m-1&&-1!==S.indexOf(0))l=[S.indexOf(0)];else if(P!==m){for(p=u.decodePixels(),g=new Uint8Array(p.length),w=0,m=p.length;w<m;w++)g[w]=S[p[w]];d=n(g,u.width,1)}}var k=function(e){var n;switch(e){case t.image_compression.FAST:n=11;break;case t.image_compression.MEDIUM:n=13;break;case t.image_compression.SLOW:n=14;break;default:n=12}return n}(s);return _===this.decode.FLATE_DECODE&&(x="/Predictor "+k+" "),x+="/Colors "+h+" /BitsPerComponent "+c+" /Columns "+u.width,(this.__addimage__.isArrayBuffer(r)||this.__addimage__.isArrayBufferView(r))&&(r=this.__addimage__.arrayBufferToBinaryString(r)),(d&&this.__addimage__.isArrayBuffer(d)||this.__addimage__.isArrayBufferView(d))&&(d=this.__addimage__.arrayBufferToBinaryString(d)),{alias:o,data:r,index:i,filter:_,decodeParameters:x,transparency:l,palette:f,sMask:d,predictor:k,width:u.width,height:u.height,bitsPerComponent:c,colorSpace:a}}}}(D.API),function(t){t.processGIF89A=function(e,n,r,i){var o=new te(e),s=o.width,a=o.height,c=[];o.decodeAndBlitFrameRGBA(0,c);var u={data:c,width:s,height:a},l=new ne(100).encode(u,100);return t.processJPEG.call(this,l,n,r,i)},t.processGIF87A=t.processGIF89A}(D.API),re.prototype.parseHeader=function(){if(this.fileSize=this.datav.getUint32(this.pos,!0),this.pos+=4,this.reserved=this.datav.getUint32(this.pos,!0),this.pos+=4,this.offset=this.datav.getUint32(this.pos,!0),this.pos+=4,this.headerSize=this.datav.getUint32(this.pos,!0),this.pos+=4,this.width=this.datav.getUint32(this.pos,!0),this.pos+=4,this.height=this.datav.getInt32(this.pos,!0),this.pos+=4,this.planes=this.datav.getUint16(this.pos,!0),this.pos+=2,this.bitPP=this.datav.getUint16(this.pos,!0),this.pos+=2,this.compress=this.datav.getUint32(this.pos,!0),this.pos+=4,this.rawSize=this.datav.getUint32(this.pos,!0),this.pos+=4,this.hr=this.datav.getUint32(this.pos,!0),this.pos+=4,this.vr=this.datav.getUint32(this.pos,!0),this.pos+=4,this.colors=this.datav.getUint32(this.pos,!0),this.pos+=4,this.importantColors=this.datav.getUint32(this.pos,!0),this.pos+=4,16===this.bitPP&&this.is_with_alpha&&(this.bitPP=15),this.bitPP<15){var t=0===this.colors?1<<this.bitPP:this.colors;this.palette=new Array(t);for(var e=0;e<t;e++){var n=this.datav.getUint8(this.pos++,!0),r=this.datav.getUint8(this.pos++,!0),i=this.datav.getUint8(this.pos++,!0),o=this.datav.getUint8(this.pos++,!0);this.palette[e]={red:i,green:r,blue:n,quad:o}}}this.height<0&&(this.height*=-1,this.bottom_up=!1)},re.prototype.parseBGR=function(){this.pos=this.offset;try{var t="bit"+this.bitPP,e=this.width*this.height*4;this.data=new Uint8Array(e),this[t]()}catch(t){c.log("bit decode error:"+t)}},re.prototype.bit1=function(){var t,e=Math.ceil(this.width/8),n=e%4;for(t=this.height-1;t>=0;t--){for(var r=this.bottom_up?t:this.height-1-t,i=0;i<e;i++)for(var o=this.datav.getUint8(this.pos++,!0),s=r*this.width*4+8*i*4,a=0;a<8&&8*i+a<this.width;a++){var c=this.palette[o>>7-a&1];this.data[s+4*a]=c.blue,this.data[s+4*a+1]=c.green,this.data[s+4*a+2]=c.red,this.data[s+4*a+3]=255}0!==n&&(this.pos+=4-n)}},re.prototype.bit4=function(){for(var t=Math.ceil(this.width/2),e=t%4,n=this.height-1;n>=0;n--){for(var r=this.bottom_up?n:this.height-1-n,i=0;i<t;i++){var o=this.datav.getUint8(this.pos++,!0),s=r*this.width*4+2*i*4,a=o>>4,c=15&o,u=this.palette[a];if(this.data[s]=u.blue,this.data[s+1]=u.green,this.data[s+2]=u.red,this.data[s+3]=255,2*i+1>=this.width)break;u=this.palette[c],this.data[s+4]=u.blue,this.data[s+4+1]=u.green,this.data[s+4+2]=u.red,this.data[s+4+3]=255}0!==e&&(this.pos+=4-e)}},re.prototype.bit8=function(){for(var t=this.width%4,e=this.height-1;e>=0;e--){for(var n=this.bottom_up?e:this.height-1-e,r=0;r<this.width;r++){var i=this.datav.getUint8(this.pos++,!0),o=n*this.width*4+4*r;if(i<this.palette.length){var s=this.palette[i];this.data[o]=s.red,this.data[o+1]=s.green,this.data[o+2]=s.blue,this.data[o+3]=255}else this.data[o]=255,this.data[o+1]=255,this.data[o+2]=255,this.data[o+3]=255}0!==t&&(this.pos+=4-t)}},re.prototype.bit15=function(){for(var t=this.width%3,e=parseInt("11111",2),n=this.height-1;n>=0;n--){for(var r=this.bottom_up?n:this.height-1-n,i=0;i<this.width;i++){var o=this.datav.getUint16(this.pos,!0);this.pos+=2;var s=(o&e)/e*255|0,a=(o>>5&e)/e*255|0,c=(o>>10&e)/e*255|0,u=o>>15?255:0,l=r*this.width*4+4*i;this.data[l]=c,this.data[l+1]=a,this.data[l+2]=s,this.data[l+3]=u}this.pos+=t}},re.prototype.bit16=function(){for(var t=this.width%3,e=parseInt("11111",2),n=parseInt("111111",2),r=this.height-1;r>=0;r--){for(var i=this.bottom_up?r:this.height-1-r,o=0;o<this.width;o++){var s=this.datav.getUint16(this.pos,!0);this.pos+=2;var a=(s&e)/e*255|0,c=(s>>5&n)/n*255|0,u=(s>>11)/e*255|0,l=i*this.width*4+4*o;this.data[l]=u,this.data[l+1]=c,this.data[l+2]=a,this.data[l+3]=255}this.pos+=t}},re.prototype.bit24=function(){for(var t=this.height-1;t>=0;t--){for(var e=this.bottom_up?t:this.height-1-t,n=0;n<this.width;n++){var r=this.datav.getUint8(this.pos++,!0),i=this.datav.getUint8(this.pos++,!0),o=this.datav.getUint8(this.pos++,!0),s=e*this.width*4+4*n;this.data[s]=o,this.data[s+1]=i,this.data[s+2]=r,this.data[s+3]=255}this.pos+=this.width%4}},re.prototype.bit32=function(){for(var t=this.height-1;t>=0;t--)for(var e=this.bottom_up?t:this.height-1-t,n=0;n<this.width;n++){var r=this.datav.getUint8(this.pos++,!0),i=this.datav.getUint8(this.pos++,!0),o=this.datav.getUint8(this.pos++,!0),s=this.datav.getUint8(this.pos++,!0),a=e*this.width*4+4*n;this.data[a]=o,this.data[a+1]=i,this.data[a+2]=r,this.data[a+3]=s}},re.prototype.getData=function(){return this.data},
/**
 * @license
 * Copyright (c) 2018 Aras Abbasi
 *
 * Licensed under the MIT License.
 * http://opensource.org/licenses/mit-license
 */
function(t){t.processBMP=function(e,n,r,i){var o=new re(e,!1),s=o.width,a=o.height,c={data:o.getData(),width:s,height:a},u=new ne(100).encode(c,100);return t.processJPEG.call(this,u,n,r,i)}}(D.API),ie.prototype.getData=function(){return this.data},
/**
 * @license
 * Copyright (c) 2019 Aras Abbasi
 *
 * Licensed under the MIT License.
 * http://opensource.org/licenses/mit-license
 */
function(t){t.processWEBP=function(e,n,r,i){var o=new ie(e,!1),s=o.width,a=o.height,c={data:o.getData(),width:s,height:a},u=new ne(100).encode(c,100);return t.processJPEG.call(this,u,n,r,i)}}(D.API),D.API.processRGBA=function(t,e,n){for(var r=t.data,i=r.length,o=new Uint8Array(i/4*3),s=new Uint8Array(i/4),a=0,c=0,u=0;u<i;u+=4){var l=r[u],h=r[u+1],f=r[u+2],d=r[u+3];o[a++]=l,o[a++]=h,o[a++]=f,s[c++]=d}var p=this.__addimage__.arrayBufferToBinaryString(o);return{alpha:this.__addimage__.arrayBufferToBinaryString(s),data:p,index:e,alias:n,colorSpace:"DeviceRGB",bitsPerComponent:8,width:t.width,height:t.height}},D.API.setLanguage=function(t){return void 0===this.internal.languageSettings&&(this.internal.languageSettings={},this.internal.languageSettings.isSubscribed=!1),void 0!=={af:"Afrikaans",sq:"Albanian",ar:"Arabic (Standard)","ar-DZ":"Arabic (Algeria)","ar-BH":"Arabic (Bahrain)","ar-EG":"Arabic (Egypt)","ar-IQ":"Arabic (Iraq)","ar-JO":"Arabic (Jordan)","ar-KW":"Arabic (Kuwait)","ar-LB":"Arabic (Lebanon)","ar-LY":"Arabic (Libya)","ar-MA":"Arabic (Morocco)","ar-OM":"Arabic (Oman)","ar-QA":"Arabic (Qatar)","ar-SA":"Arabic (Saudi Arabia)","ar-SY":"Arabic (Syria)","ar-TN":"Arabic (Tunisia)","ar-AE":"Arabic (U.A.E.)","ar-YE":"Arabic (Yemen)",an:"Aragonese",hy:"Armenian",as:"Assamese",ast:"Asturian",az:"Azerbaijani",eu:"Basque",be:"Belarusian",bn:"Bengali",bs:"Bosnian",br:"Breton",bg:"Bulgarian",my:"Burmese",ca:"Catalan",ch:"Chamorro",ce:"Chechen",zh:"Chinese","zh-HK":"Chinese (Hong Kong)","zh-CN":"Chinese (PRC)","zh-SG":"Chinese (Singapore)","zh-TW":"Chinese (Taiwan)",cv:"Chuvash",co:"Corsican",cr:"Cree",hr:"Croatian",cs:"Czech",da:"Danish",nl:"Dutch (Standard)","nl-BE":"Dutch (Belgian)",en:"English","en-AU":"English (Australia)","en-BZ":"English (Belize)","en-CA":"English (Canada)","en-IE":"English (Ireland)","en-JM":"English (Jamaica)","en-NZ":"English (New Zealand)","en-PH":"English (Philippines)","en-ZA":"English (South Africa)","en-TT":"English (Trinidad & Tobago)","en-GB":"English (United Kingdom)","en-US":"English (United States)","en-ZW":"English (Zimbabwe)",eo:"Esperanto",et:"Estonian",fo:"Faeroese",fj:"Fijian",fi:"Finnish",fr:"French (Standard)","fr-BE":"French (Belgium)","fr-CA":"French (Canada)","fr-FR":"French (France)","fr-LU":"French (Luxembourg)","fr-MC":"French (Monaco)","fr-CH":"French (Switzerland)",fy:"Frisian",fur:"Friulian",gd:"Gaelic (Scots)","gd-IE":"Gaelic (Irish)",gl:"Galacian",ka:"Georgian",de:"German (Standard)","de-AT":"German (Austria)","de-DE":"German (Germany)","de-LI":"German (Liechtenstein)","de-LU":"German (Luxembourg)","de-CH":"German (Switzerland)",el:"Greek",gu:"Gujurati",ht:"Haitian",he:"Hebrew",hi:"Hindi",hu:"Hungarian",is:"Icelandic",id:"Indonesian",iu:"Inuktitut",ga:"Irish",it:"Italian (Standard)","it-CH":"Italian (Switzerland)",ja:"Japanese",kn:"Kannada",ks:"Kashmiri",kk:"Kazakh",km:"Khmer",ky:"Kirghiz",tlh:"Klingon",ko:"Korean","ko-KP":"Korean (North Korea)","ko-KR":"Korean (South Korea)",la:"Latin",lv:"Latvian",lt:"Lithuanian",lb:"Luxembourgish",mk:"North Macedonia",ms:"Malay",ml:"Malayalam",mt:"Maltese",mi:"Maori",mr:"Marathi",mo:"Moldavian",nv:"Navajo",ng:"Ndonga",ne:"Nepali",no:"Norwegian",nb:"Norwegian (Bokmal)",nn:"Norwegian (Nynorsk)",oc:"Occitan",or:"Oriya",om:"Oromo",fa:"Persian","fa-IR":"Persian/Iran",pl:"Polish",pt:"Portuguese","pt-BR":"Portuguese (Brazil)",pa:"Punjabi","pa-IN":"Punjabi (India)","pa-PK":"Punjabi (Pakistan)",qu:"Quechua",rm:"Rhaeto-Romanic",ro:"Romanian","ro-MO":"Romanian (Moldavia)",ru:"Russian","ru-MO":"Russian (Moldavia)",sz:"Sami (Lappish)",sg:"Sango",sa:"Sanskrit",sc:"Sardinian",sd:"Sindhi",si:"Singhalese",sr:"Serbian",sk:"Slovak",sl:"Slovenian",so:"Somani",sb:"Sorbian",es:"Spanish","es-AR":"Spanish (Argentina)","es-BO":"Spanish (Bolivia)","es-CL":"Spanish (Chile)","es-CO":"Spanish (Colombia)","es-CR":"Spanish (Costa Rica)","es-DO":"Spanish (Dominican Republic)","es-EC":"Spanish (Ecuador)","es-SV":"Spanish (El Salvador)","es-GT":"Spanish (Guatemala)","es-HN":"Spanish (Honduras)","es-MX":"Spanish (Mexico)","es-NI":"Spanish (Nicaragua)","es-PA":"Spanish (Panama)","es-PY":"Spanish (Paraguay)","es-PE":"Spanish (Peru)","es-PR":"Spanish (Puerto Rico)","es-ES":"Spanish (Spain)","es-UY":"Spanish (Uruguay)","es-VE":"Spanish (Venezuela)",sx:"Sutu",sw:"Swahili",sv:"Swedish","sv-FI":"Swedish (Finland)","sv-SV":"Swedish (Sweden)",ta:"Tamil",tt:"Tatar",te:"Teluga",th:"Thai",tig:"Tigre",ts:"Tsonga",tn:"Tswana",tr:"Turkish",tk:"Turkmen",uk:"Ukrainian",hsb:"Upper Sorbian",ur:"Urdu",ve:"Venda",vi:"Vietnamese",vo:"Volapuk",wa:"Walloon",cy:"Welsh",xh:"Xhosa",ji:"Yiddish",zu:"Zulu"}[t]&&(this.internal.languageSettings.languageCode=t,!1===this.internal.languageSettings.isSubscribed&&(this.internal.events.subscribe("putCatalog",(function(){this.internal.write("/Lang ("+this.internal.languageSettings.languageCode+")")})),this.internal.languageSettings.isSubscribed=!0)),this},Jt=D.API,Xt=Jt.getCharWidthsArray=function(t,e){var n,r,o=(e=e||{}).font||this.internal.getFont(),s=e.fontSize||this.internal.getFontSize(),a=e.charSpace||this.internal.getCharSpace(),c=e.widths?e.widths:o.metadata.Unicode.widths,u=c.fof?c.fof:1,l=e.kerning?e.kerning:o.metadata.Unicode.kerning,h=l.fof?l.fof:1,f=!1!==e.doKerning,d=0,p=t.length,m=0,g=c[0]||u,v=[];for(n=0;n<p;n++)r=t.charCodeAt(n),"function"==typeof o.metadata.widthOfString?v.push((o.metadata.widthOfGlyph(o.metadata.characterToGlyph(r))+a*(1e3/s)||0)/1e3):(d=f&&"object"===i()(l[r])&&!isNaN(parseInt(l[r][m],10))?l[r][m]/h:0,v.push((c[r]||g)/u+d)),m=r;return v},Kt=Jt.getStringUnitWidth=function(t,e){var n=(e=e||{}).fontSize||this.internal.getFontSize(),r=e.font||this.internal.getFont(),i=e.charSpace||this.internal.getCharSpace();return Jt.processArabic&&(t=Jt.processArabic(t)),"function"==typeof r.metadata.widthOfString?r.metadata.widthOfString(t,n,i)/n:Xt.apply(this,arguments).reduce((function(t,e){return t+e}),0)},$t=function(t,e,n,r){for(var i=[],o=0,s=t.length,a=0;o!==s&&a+e[o]<n;)a+=e[o],o++;i.push(t.slice(0,o));var c=o;for(a=0;o!==s;)a+e[o]>r&&(i.push(t.slice(c,o)),a=0,c=o),a+=e[o],o++;return c!==o&&i.push(t.slice(c,o)),i},Zt=function(t,e,n){n||(n={});var r,i,o,s,a,c,u,l=[],h=[l],f=n.textIndent||0,d=0,p=0,m=t.split(" "),g=Xt.apply(this,[" ",n])[0];if(c=-1===n.lineIndent?m[0].length+2:n.lineIndent||0){var v=Array(c).join(" "),b=[];m.map((function(t){(t=t.split(/\s*\n/)).length>1?b=b.concat(t.map((function(t,e){return(e&&t.length?"\n":"")+t}))):b.push(t[0])})),m=b,c=Kt.apply(this,[v,n])}for(o=0,s=m.length;o<s;o++){var y=0;if(r=m[o],c&&"\n"==r[0]&&(r=r.substr(1),y=1),f+d+(p=(i=Xt.apply(this,[r,n])).reduce((function(t,e){return t+e}),0))>e||y){if(p>e){for(a=$t.apply(this,[r,i,e-(f+d),e]),l.push(a.shift()),l=[a.pop()];a.length;)h.push([a.shift()]);p=i.slice(r.length-(l[0]?l[0].length:0)).reduce((function(t,e){return t+e}),0)}else l=[r];h.push(l),f=p+c,d=g}else l.push(r),f+=d+p,d=g}return u=c?function(t,e){return(e?v:"")+t.join(" ")}:function(t){return t.join(" ")},h.map(u)},Jt.splitTextToSize=function(t,e,n){var r,i=(n=n||{}).fontSize||this.internal.getFontSize(),o=function(t){if(t.widths&&t.kerning)return{widths:t.widths,kerning:t.kerning};var e=this.internal.getFont(t.fontName,t.fontStyle);return e.metadata.Unicode?{widths:e.metadata.Unicode.widths||{0:1},kerning:e.metadata.Unicode.kerning||{}}:{font:e.metadata,fontSize:this.internal.getFontSize(),charSpace:this.internal.getCharSpace()}}.call(this,n);r=Array.isArray(t)?t:String(t).split(/\r?\n/);var s=1*this.internal.scaleFactor*e/i;o.textIndent=n.textIndent?1*n.textIndent*this.internal.scaleFactor/i:0,o.lineIndent=n.lineIndent;var a,c,u=[];for(a=0,c=r.length;a<c;a++)u=u.concat(Zt.apply(this,[r[a],s,o]));return u},function(t){t.__fontmetrics__=t.__fontmetrics__||{};for(var e="klmnopqrstuvwxyz",n={},r={},o=0;o<e.length;o++)n[e[o]]="0123456789abcdef"[o],r["0123456789abcdef"[o]]=e[o];var s=function(t){return"0x"+parseInt(t,10).toString(16)},a=t.__fontmetrics__.compress=function(t){var e,n,o,c,u=["{"];for(var l in t){if(e=t[l],isNaN(parseInt(l,10))?n="'"+l+"'":(l=parseInt(l,10),n=(n=s(l).slice(2)).slice(0,-1)+r[n.slice(-1)]),"number"==typeof e)e<0?(o=s(e).slice(3),c="-"):(o=s(e).slice(2),c=""),o=c+o.slice(0,-1)+r[o.slice(-1)];else{if("object"!==i()(e))throw new Error("Don't know what to do with value type "+i()(e)+".");o=a(e)}u.push(n+o)}return u.push("}"),u.join("")},c=t.__fontmetrics__.uncompress=function(t){if("string"!=typeof t)throw new Error("Invalid argument passed to uncompress.");for(var e,r,i,o,s={},a=1,c=s,u=[],l="",h="",f=t.length-1,d=1;d<f;d+=1)"'"==(o=t[d])?e?(i=e.join(""),e=void 0):e=[]:e?e.push(o):"{"==o?(u.push([c,i]),c={},i=void 0):"}"==o?((r=u.pop())[0][r[1]]=c,i=void 0,c=r[0]):"-"==o?a=-1:void 0===i?n.hasOwnProperty(o)?(l+=n[o],i=parseInt(l,16)*a,a=1,l=""):l+=o:n.hasOwnProperty(o)?(h+=n[o],c[i]=parseInt(h,16)*a,a=1,i=void 0,h=""):h+=o;return s},u={codePages:["WinAnsiEncoding"],WinAnsiEncoding:c("{19m8n201n9q201o9r201s9l201t9m201u8m201w9n201x9o201y8o202k8q202l8r202m9p202q8p20aw8k203k8t203t8v203u9v2cq8s212m9t15m8w15n9w2dw9s16k8u16l9u17s9z17x8y17y9y}")},l={Unicode:{Courier:u,"Courier-Bold":u,"Courier-BoldOblique":u,"Courier-Oblique":u,Helvetica:u,"Helvetica-Bold":u,"Helvetica-BoldOblique":u,"Helvetica-Oblique":u,"Times-Roman":u,"Times-Bold":u,"Times-BoldItalic":u,"Times-Italic":u}},h={Unicode:{"Courier-Oblique":c("{'widths'{k3w'fof'6o}'kerning'{'fof'-6o}}"),"Times-BoldItalic":c("{'widths'{k3o2q4ycx2r201n3m201o6o201s2l201t2l201u2l201w3m201x3m201y3m2k1t2l2r202m2n2n3m2o3m2p5n202q6o2r1w2s2l2t2l2u3m2v3t2w1t2x2l2y1t2z1w3k3m3l3m3m3m3n3m3o3m3p3m3q3m3r3m3s3m203t2l203u2l3v2l3w3t3x3t3y3t3z3m4k5n4l4m4m4m4n4m4o4s4p4m4q4m4r4s4s4y4t2r4u3m4v4m4w3x4x5t4y4s4z4s5k3x5l4s5m4m5n3r5o3x5p4s5q4m5r5t5s4m5t3x5u3x5v2l5w1w5x2l5y3t5z3m6k2l6l3m6m3m6n2w6o3m6p2w6q2l6r3m6s3r6t1w6u1w6v3m6w1w6x4y6y3r6z3m7k3m7l3m7m2r7n2r7o1w7p3r7q2w7r4m7s3m7t2w7u2r7v2n7w1q7x2n7y3t202l3mcl4mal2ram3man3mao3map3mar3mas2lat4uau1uav3maw3way4uaz2lbk2sbl3t'fof'6obo2lbp3tbq3mbr1tbs2lbu1ybv3mbz3mck4m202k3mcm4mcn4mco4mcp4mcq5ycr4mcs4mct4mcu4mcv4mcw2r2m3rcy2rcz2rdl4sdm4sdn4sdo4sdp4sdq4sds4sdt4sdu4sdv4sdw4sdz3mek3mel3mem3men3meo3mep3meq4ser2wes2wet2weu2wev2wew1wex1wey1wez1wfl3rfm3mfn3mfo3mfp3mfq3mfr3tfs3mft3rfu3rfv3rfw3rfz2w203k6o212m6o2dw2l2cq2l3t3m3u2l17s3x19m3m}'kerning'{cl{4qu5kt5qt5rs17ss5ts}201s{201ss}201t{cks4lscmscnscoscpscls2wu2yu201ts}201x{2wu2yu}2k{201ts}2w{4qx5kx5ou5qx5rs17su5tu}2x{17su5tu5ou}2y{4qx5kx5ou5qx5rs17ss5ts}'fof'-6ofn{17sw5tw5ou5qw5rs}7t{cksclscmscnscoscps4ls}3u{17su5tu5os5qs}3v{17su5tu5os5qs}7p{17su5tu}ck{4qu5kt5qt5rs17ss5ts}4l{4qu5kt5qt5rs17ss5ts}cm{4qu5kt5qt5rs17ss5ts}cn{4qu5kt5qt5rs17ss5ts}co{4qu5kt5qt5rs17ss5ts}cp{4qu5kt5qt5rs17ss5ts}6l{4qu5ou5qw5rt17su5tu}5q{ckuclucmucnucoucpu4lu}5r{ckuclucmucnucoucpu4lu}7q{cksclscmscnscoscps4ls}6p{4qu5ou5qw5rt17sw5tw}ek{4qu5ou5qw5rt17su5tu}el{4qu5ou5qw5rt17su5tu}em{4qu5ou5qw5rt17su5tu}en{4qu5ou5qw5rt17su5tu}eo{4qu5ou5qw5rt17su5tu}ep{4qu5ou5qw5rt17su5tu}es{17ss5ts5qs4qu}et{4qu5ou5qw5rt17sw5tw}eu{4qu5ou5qw5rt17ss5ts}ev{17ss5ts5qs4qu}6z{17sw5tw5ou5qw5rs}fm{17sw5tw5ou5qw5rs}7n{201ts}fo{17sw5tw5ou5qw5rs}fp{17sw5tw5ou5qw5rs}fq{17sw5tw5ou5qw5rs}7r{cksclscmscnscoscps4ls}fs{17sw5tw5ou5qw5rs}ft{17su5tu}fu{17su5tu}fv{17su5tu}fw{17su5tu}fz{cksclscmscnscoscps4ls}}}"),"Helvetica-Bold":c("{'widths'{k3s2q4scx1w201n3r201o6o201s1w201t1w201u1w201w3m201x3m201y3m2k1w2l2l202m2n2n3r2o3r2p5t202q6o2r1s2s2l2t2l2u2r2v3u2w1w2x2l2y1w2z1w3k3r3l3r3m3r3n3r3o3r3p3r3q3r3r3r3s3r203t2l203u2l3v2l3w3u3x3u3y3u3z3x4k6l4l4s4m4s4n4s4o4s4p4m4q3x4r4y4s4s4t1w4u3r4v4s4w3x4x5n4y4s4z4y5k4m5l4y5m4s5n4m5o3x5p4s5q4m5r5y5s4m5t4m5u3x5v2l5w1w5x2l5y3u5z3r6k2l6l3r6m3x6n3r6o3x6p3r6q2l6r3x6s3x6t1w6u1w6v3r6w1w6x5t6y3x6z3x7k3x7l3x7m2r7n3r7o2l7p3x7q3r7r4y7s3r7t3r7u3m7v2r7w1w7x2r7y3u202l3rcl4sal2lam3ran3rao3rap3rar3ras2lat4tau2pav3raw3uay4taz2lbk2sbl3u'fof'6obo2lbp3xbq3rbr1wbs2lbu2obv3rbz3xck4s202k3rcm4scn4sco4scp4scq6ocr4scs4mct4mcu4mcv4mcw1w2m2zcy1wcz1wdl4sdm4ydn4ydo4ydp4ydq4yds4ydt4sdu4sdv4sdw4sdz3xek3rel3rem3ren3reo3rep3req5ter3res3ret3reu3rev3rew1wex1wey1wez1wfl3xfm3xfn3xfo3xfp3xfq3xfr3ufs3xft3xfu3xfv3xfw3xfz3r203k6o212m6o2dw2l2cq2l3t3r3u2l17s4m19m3r}'kerning'{cl{4qs5ku5ot5qs17sv5tv}201t{2ww4wy2yw}201w{2ks}201x{2ww4wy2yw}2k{201ts201xs}2w{7qs4qu5kw5os5qw5rs17su5tu7tsfzs}2x{5ow5qs}2y{7qs4qu5kw5os5qw5rs17su5tu7tsfzs}'fof'-6o7p{17su5tu5ot}ck{4qs5ku5ot5qs17sv5tv}4l{4qs5ku5ot5qs17sv5tv}cm{4qs5ku5ot5qs17sv5tv}cn{4qs5ku5ot5qs17sv5tv}co{4qs5ku5ot5qs17sv5tv}cp{4qs5ku5ot5qs17sv5tv}6l{17st5tt5os}17s{2kwclvcmvcnvcovcpv4lv4wwckv}5o{2kucltcmtcntcotcpt4lt4wtckt}5q{2ksclscmscnscoscps4ls4wvcks}5r{2ks4ws}5t{2kwclvcmvcnvcovcpv4lv4wwckv}eo{17st5tt5os}fu{17su5tu5ot}6p{17ss5ts}ek{17st5tt5os}el{17st5tt5os}em{17st5tt5os}en{17st5tt5os}6o{201ts}ep{17st5tt5os}es{17ss5ts}et{17ss5ts}eu{17ss5ts}ev{17ss5ts}6z{17su5tu5os5qt}fm{17su5tu5os5qt}fn{17su5tu5os5qt}fo{17su5tu5os5qt}fp{17su5tu5os5qt}fq{17su5tu5os5qt}fs{17su5tu5os5qt}ft{17su5tu5ot}7m{5os}fv{17su5tu5ot}fw{17su5tu5ot}}}"),Courier:c("{'widths'{k3w'fof'6o}'kerning'{'fof'-6o}}"),"Courier-BoldOblique":c("{'widths'{k3w'fof'6o}'kerning'{'fof'-6o}}"),"Times-Bold":c("{'widths'{k3q2q5ncx2r201n3m201o6o201s2l201t2l201u2l201w3m201x3m201y3m2k1t2l2l202m2n2n3m2o3m2p6o202q6o2r1w2s2l2t2l2u3m2v3t2w1t2x2l2y1t2z1w3k3m3l3m3m3m3n3m3o3m3p3m3q3m3r3m3s3m203t2l203u2l3v2l3w3t3x3t3y3t3z3m4k5x4l4s4m4m4n4s4o4s4p4m4q3x4r4y4s4y4t2r4u3m4v4y4w4m4x5y4y4s4z4y5k3x5l4y5m4s5n3r5o4m5p4s5q4s5r6o5s4s5t4s5u4m5v2l5w1w5x2l5y3u5z3m6k2l6l3m6m3r6n2w6o3r6p2w6q2l6r3m6s3r6t1w6u2l6v3r6w1w6x5n6y3r6z3m7k3r7l3r7m2w7n2r7o2l7p3r7q3m7r4s7s3m7t3m7u2w7v2r7w1q7x2r7y3o202l3mcl4sal2lam3man3mao3map3mar3mas2lat4uau1yav3maw3tay4uaz2lbk2sbl3t'fof'6obo2lbp3rbr1tbs2lbu2lbv3mbz3mck4s202k3mcm4scn4sco4scp4scq6ocr4scs4mct4mcu4mcv4mcw2r2m3rcy2rcz2rdl4sdm4ydn4ydo4ydp4ydq4yds4ydt4sdu4sdv4sdw4sdz3rek3mel3mem3men3meo3mep3meq4ser2wes2wet2weu2wev2wew1wex1wey1wez1wfl3rfm3mfn3mfo3mfp3mfq3mfr3tfs3mft3rfu3rfv3rfw3rfz3m203k6o212m6o2dw2l2cq2l3t3m3u2l17s4s19m3m}'kerning'{cl{4qt5ks5ot5qy5rw17sv5tv}201t{cks4lscmscnscoscpscls4wv}2k{201ts}2w{4qu5ku7mu5os5qx5ru17su5tu}2x{17su5tu5ou5qs}2y{4qv5kv7mu5ot5qz5ru17su5tu}'fof'-6o7t{cksclscmscnscoscps4ls}3u{17su5tu5os5qu}3v{17su5tu5os5qu}fu{17su5tu5ou5qu}7p{17su5tu5ou5qu}ck{4qt5ks5ot5qy5rw17sv5tv}4l{4qt5ks5ot5qy5rw17sv5tv}cm{4qt5ks5ot5qy5rw17sv5tv}cn{4qt5ks5ot5qy5rw17sv5tv}co{4qt5ks5ot5qy5rw17sv5tv}cp{4qt5ks5ot5qy5rw17sv5tv}6l{17st5tt5ou5qu}17s{ckuclucmucnucoucpu4lu4wu}5o{ckuclucmucnucoucpu4lu4wu}5q{ckzclzcmzcnzcozcpz4lz4wu}5r{ckxclxcmxcnxcoxcpx4lx4wu}5t{ckuclucmucnucoucpu4lu4wu}7q{ckuclucmucnucoucpu4lu}6p{17sw5tw5ou5qu}ek{17st5tt5qu}el{17st5tt5ou5qu}em{17st5tt5qu}en{17st5tt5qu}eo{17st5tt5qu}ep{17st5tt5ou5qu}es{17ss5ts5qu}et{17sw5tw5ou5qu}eu{17sw5tw5ou5qu}ev{17ss5ts5qu}6z{17sw5tw5ou5qu5rs}fm{17sw5tw5ou5qu5rs}fn{17sw5tw5ou5qu5rs}fo{17sw5tw5ou5qu5rs}fp{17sw5tw5ou5qu5rs}fq{17sw5tw5ou5qu5rs}7r{cktcltcmtcntcotcpt4lt5os}fs{17sw5tw5ou5qu5rs}ft{17su5tu5ou5qu}7m{5os}fv{17su5tu5ou5qu}fw{17su5tu5ou5qu}fz{cksclscmscnscoscps4ls}}}"),Symbol:c("{'widths'{k3uaw4r19m3m2k1t2l2l202m2y2n3m2p5n202q6o3k3m2s2l2t2l2v3r2w1t3m3m2y1t2z1wbk2sbl3r'fof'6o3n3m3o3m3p3m3q3m3r3m3s3m3t3m3u1w3v1w3w3r3x3r3y3r3z2wbp3t3l3m5v2l5x2l5z3m2q4yfr3r7v3k7w1o7x3k}'kerning'{'fof'-6o}}"),Helvetica:c("{'widths'{k3p2q4mcx1w201n3r201o6o201s1q201t1q201u1q201w2l201x2l201y2l2k1w2l1w202m2n2n3r2o3r2p5t202q6o2r1n2s2l2t2l2u2r2v3u2w1w2x2l2y1w2z1w3k3r3l3r3m3r3n3r3o3r3p3r3q3r3r3r3s3r203t2l203u2l3v1w3w3u3x3u3y3u3z3r4k6p4l4m4m4m4n4s4o4s4p4m4q3x4r4y4s4s4t1w4u3m4v4m4w3r4x5n4y4s4z4y5k4m5l4y5m4s5n4m5o3x5p4s5q4m5r5y5s4m5t4m5u3x5v1w5w1w5x1w5y2z5z3r6k2l6l3r6m3r6n3m6o3r6p3r6q1w6r3r6s3r6t1q6u1q6v3m6w1q6x5n6y3r6z3r7k3r7l3r7m2l7n3m7o1w7p3r7q3m7r4s7s3m7t3m7u3m7v2l7w1u7x2l7y3u202l3rcl4mal2lam3ran3rao3rap3rar3ras2lat4tau2pav3raw3uay4taz2lbk2sbl3u'fof'6obo2lbp3rbr1wbs2lbu2obv3rbz3xck4m202k3rcm4mcn4mco4mcp4mcq6ocr4scs4mct4mcu4mcv4mcw1w2m2ncy1wcz1wdl4sdm4ydn4ydo4ydp4ydq4yds4ydt4sdu4sdv4sdw4sdz3xek3rel3rem3ren3reo3rep3req5ter3mes3ret3reu3rev3rew1wex1wey1wez1wfl3rfm3rfn3rfo3rfp3rfq3rfr3ufs3xft3rfu3rfv3rfw3rfz3m203k6o212m6o2dw2l2cq2l3t3r3u1w17s4m19m3r}'kerning'{5q{4wv}cl{4qs5kw5ow5qs17sv5tv}201t{2wu4w1k2yu}201x{2wu4wy2yu}17s{2ktclucmucnu4otcpu4lu4wycoucku}2w{7qs4qz5k1m17sy5ow5qx5rsfsu5ty7tufzu}2x{17sy5ty5oy5qs}2y{7qs4qz5k1m17sy5ow5qx5rsfsu5ty7tufzu}'fof'-6o7p{17sv5tv5ow}ck{4qs5kw5ow5qs17sv5tv}4l{4qs5kw5ow5qs17sv5tv}cm{4qs5kw5ow5qs17sv5tv}cn{4qs5kw5ow5qs17sv5tv}co{4qs5kw5ow5qs17sv5tv}cp{4qs5kw5ow5qs17sv5tv}6l{17sy5ty5ow}do{17st5tt}4z{17st5tt}7s{fst}dm{17st5tt}dn{17st5tt}5o{ckwclwcmwcnwcowcpw4lw4wv}dp{17st5tt}dq{17st5tt}7t{5ow}ds{17st5tt}5t{2ktclucmucnu4otcpu4lu4wycoucku}fu{17sv5tv5ow}6p{17sy5ty5ow5qs}ek{17sy5ty5ow}el{17sy5ty5ow}em{17sy5ty5ow}en{5ty}eo{17sy5ty5ow}ep{17sy5ty5ow}es{17sy5ty5qs}et{17sy5ty5ow5qs}eu{17sy5ty5ow5qs}ev{17sy5ty5ow5qs}6z{17sy5ty5ow5qs}fm{17sy5ty5ow5qs}fn{17sy5ty5ow5qs}fo{17sy5ty5ow5qs}fp{17sy5ty5qs}fq{17sy5ty5ow5qs}7r{5ow}fs{17sy5ty5ow5qs}ft{17sv5tv5ow}7m{5ow}fv{17sv5tv5ow}fw{17sv5tv5ow}}}"),"Helvetica-BoldOblique":c("{'widths'{k3s2q4scx1w201n3r201o6o201s1w201t1w201u1w201w3m201x3m201y3m2k1w2l2l202m2n2n3r2o3r2p5t202q6o2r1s2s2l2t2l2u2r2v3u2w1w2x2l2y1w2z1w3k3r3l3r3m3r3n3r3o3r3p3r3q3r3r3r3s3r203t2l203u2l3v2l3w3u3x3u3y3u3z3x4k6l4l4s4m4s4n4s4o4s4p4m4q3x4r4y4s4s4t1w4u3r4v4s4w3x4x5n4y4s4z4y5k4m5l4y5m4s5n4m5o3x5p4s5q4m5r5y5s4m5t4m5u3x5v2l5w1w5x2l5y3u5z3r6k2l6l3r6m3x6n3r6o3x6p3r6q2l6r3x6s3x6t1w6u1w6v3r6w1w6x5t6y3x6z3x7k3x7l3x7m2r7n3r7o2l7p3x7q3r7r4y7s3r7t3r7u3m7v2r7w1w7x2r7y3u202l3rcl4sal2lam3ran3rao3rap3rar3ras2lat4tau2pav3raw3uay4taz2lbk2sbl3u'fof'6obo2lbp3xbq3rbr1wbs2lbu2obv3rbz3xck4s202k3rcm4scn4sco4scp4scq6ocr4scs4mct4mcu4mcv4mcw1w2m2zcy1wcz1wdl4sdm4ydn4ydo4ydp4ydq4yds4ydt4sdu4sdv4sdw4sdz3xek3rel3rem3ren3reo3rep3req5ter3res3ret3reu3rev3rew1wex1wey1wez1wfl3xfm3xfn3xfo3xfp3xfq3xfr3ufs3xft3xfu3xfv3xfw3xfz3r203k6o212m6o2dw2l2cq2l3t3r3u2l17s4m19m3r}'kerning'{cl{4qs5ku5ot5qs17sv5tv}201t{2ww4wy2yw}201w{2ks}201x{2ww4wy2yw}2k{201ts201xs}2w{7qs4qu5kw5os5qw5rs17su5tu7tsfzs}2x{5ow5qs}2y{7qs4qu5kw5os5qw5rs17su5tu7tsfzs}'fof'-6o7p{17su5tu5ot}ck{4qs5ku5ot5qs17sv5tv}4l{4qs5ku5ot5qs17sv5tv}cm{4qs5ku5ot5qs17sv5tv}cn{4qs5ku5ot5qs17sv5tv}co{4qs5ku5ot5qs17sv5tv}cp{4qs5ku5ot5qs17sv5tv}6l{17st5tt5os}17s{2kwclvcmvcnvcovcpv4lv4wwckv}5o{2kucltcmtcntcotcpt4lt4wtckt}5q{2ksclscmscnscoscps4ls4wvcks}5r{2ks4ws}5t{2kwclvcmvcnvcovcpv4lv4wwckv}eo{17st5tt5os}fu{17su5tu5ot}6p{17ss5ts}ek{17st5tt5os}el{17st5tt5os}em{17st5tt5os}en{17st5tt5os}6o{201ts}ep{17st5tt5os}es{17ss5ts}et{17ss5ts}eu{17ss5ts}ev{17ss5ts}6z{17su5tu5os5qt}fm{17su5tu5os5qt}fn{17su5tu5os5qt}fo{17su5tu5os5qt}fp{17su5tu5os5qt}fq{17su5tu5os5qt}fs{17su5tu5os5qt}ft{17su5tu5ot}7m{5os}fv{17su5tu5ot}fw{17su5tu5ot}}}"),ZapfDingbats:c("{'widths'{k4u2k1w'fof'6o}'kerning'{'fof'-6o}}"),"Courier-Bold":c("{'widths'{k3w'fof'6o}'kerning'{'fof'-6o}}"),"Times-Italic":c("{'widths'{k3n2q4ycx2l201n3m201o5t201s2l201t2l201u2l201w3r201x3r201y3r2k1t2l2l202m2n2n3m2o3m2p5n202q5t2r1p2s2l2t2l2u3m2v4n2w1t2x2l2y1t2z1w3k3m3l3m3m3m3n3m3o3m3p3m3q3m3r3m3s3m203t2l203u2l3v2l3w4n3x4n3y4n3z3m4k5w4l3x4m3x4n4m4o4s4p3x4q3x4r4s4s4s4t2l4u2w4v4m4w3r4x5n4y4m4z4s5k3x5l4s5m3x5n3m5o3r5p4s5q3x5r5n5s3x5t3r5u3r5v2r5w1w5x2r5y2u5z3m6k2l6l3m6m3m6n2w6o3m6p2w6q1w6r3m6s3m6t1w6u1w6v2w6w1w6x4s6y3m6z3m7k3m7l3m7m2r7n2r7o1w7p3m7q2w7r4m7s2w7t2w7u2r7v2s7w1v7x2s7y3q202l3mcl3xal2ram3man3mao3map3mar3mas2lat4wau1vav3maw4nay4waz2lbk2sbl4n'fof'6obo2lbp3mbq3obr1tbs2lbu1zbv3mbz3mck3x202k3mcm3xcn3xco3xcp3xcq5tcr4mcs3xct3xcu3xcv3xcw2l2m2ucy2lcz2ldl4mdm4sdn4sdo4sdp4sdq4sds4sdt4sdu4sdv4sdw4sdz3mek3mel3mem3men3meo3mep3meq4mer2wes2wet2weu2wev2wew1wex1wey1wez1wfl3mfm3mfn3mfo3mfp3mfq3mfr4nfs3mft3mfu3mfv3mfw3mfz2w203k6o212m6m2dw2l2cq2l3t3m3u2l17s3r19m3m}'kerning'{cl{5kt4qw}201s{201sw}201t{201tw2wy2yy6q-t}201x{2wy2yy}2k{201tw}2w{7qs4qy7rs5ky7mw5os5qx5ru17su5tu}2x{17ss5ts5os}2y{7qs4qy7rs5ky7mw5os5qx5ru17su5tu}'fof'-6o6t{17ss5ts5qs}7t{5os}3v{5qs}7p{17su5tu5qs}ck{5kt4qw}4l{5kt4qw}cm{5kt4qw}cn{5kt4qw}co{5kt4qw}cp{5kt4qw}6l{4qs5ks5ou5qw5ru17su5tu}17s{2ks}5q{ckvclvcmvcnvcovcpv4lv}5r{ckuclucmucnucoucpu4lu}5t{2ks}6p{4qs5ks5ou5qw5ru17su5tu}ek{4qs5ks5ou5qw5ru17su5tu}el{4qs5ks5ou5qw5ru17su5tu}em{4qs5ks5ou5qw5ru17su5tu}en{4qs5ks5ou5qw5ru17su5tu}eo{4qs5ks5ou5qw5ru17su5tu}ep{4qs5ks5ou5qw5ru17su5tu}es{5ks5qs4qs}et{4qs5ks5ou5qw5ru17su5tu}eu{4qs5ks5qw5ru17su5tu}ev{5ks5qs4qs}ex{17ss5ts5qs}6z{4qv5ks5ou5qw5ru17su5tu}fm{4qv5ks5ou5qw5ru17su5tu}fn{4qv5ks5ou5qw5ru17su5tu}fo{4qv5ks5ou5qw5ru17su5tu}fp{4qv5ks5ou5qw5ru17su5tu}fq{4qv5ks5ou5qw5ru17su5tu}7r{5os}fs{4qv5ks5ou5qw5ru17su5tu}ft{17su5tu5qs}fu{17su5tu5qs}fv{17su5tu5qs}fw{17su5tu5qs}}}"),"Times-Roman":c("{'widths'{k3n2q4ycx2l201n3m201o6o201s2l201t2l201u2l201w2w201x2w201y2w2k1t2l2l202m2n2n3m2o3m2p5n202q6o2r1m2s2l2t2l2u3m2v3s2w1t2x2l2y1t2z1w3k3m3l3m3m3m3n3m3o3m3p3m3q3m3r3m3s3m203t2l203u2l3v1w3w3s3x3s3y3s3z2w4k5w4l4s4m4m4n4m4o4s4p3x4q3r4r4s4s4s4t2l4u2r4v4s4w3x4x5t4y4s4z4s5k3r5l4s5m4m5n3r5o3x5p4s5q4s5r5y5s4s5t4s5u3x5v2l5w1w5x2l5y2z5z3m6k2l6l2w6m3m6n2w6o3m6p2w6q2l6r3m6s3m6t1w6u1w6v3m6w1w6x4y6y3m6z3m7k3m7l3m7m2l7n2r7o1w7p3m7q3m7r4s7s3m7t3m7u2w7v3k7w1o7x3k7y3q202l3mcl4sal2lam3man3mao3map3mar3mas2lat4wau1vav3maw3say4waz2lbk2sbl3s'fof'6obo2lbp3mbq2xbr1tbs2lbu1zbv3mbz2wck4s202k3mcm4scn4sco4scp4scq5tcr4mcs3xct3xcu3xcv3xcw2l2m2tcy2lcz2ldl4sdm4sdn4sdo4sdp4sdq4sds4sdt4sdu4sdv4sdw4sdz3mek2wel2wem2wen2weo2wep2weq4mer2wes2wet2weu2wev2wew1wex1wey1wez1wfl3mfm3mfn3mfo3mfp3mfq3mfr3sfs3mft3mfu3mfv3mfw3mfz3m203k6o212m6m2dw2l2cq2l3t3m3u1w17s4s19m3m}'kerning'{cl{4qs5ku17sw5ou5qy5rw201ss5tw201ws}201s{201ss}201t{ckw4lwcmwcnwcowcpwclw4wu201ts}2k{201ts}2w{4qs5kw5os5qx5ru17sx5tx}2x{17sw5tw5ou5qu}2y{4qs5kw5os5qx5ru17sx5tx}'fof'-6o7t{ckuclucmucnucoucpu4lu5os5rs}3u{17su5tu5qs}3v{17su5tu5qs}7p{17sw5tw5qs}ck{4qs5ku17sw5ou5qy5rw201ss5tw201ws}4l{4qs5ku17sw5ou5qy5rw201ss5tw201ws}cm{4qs5ku17sw5ou5qy5rw201ss5tw201ws}cn{4qs5ku17sw5ou5qy5rw201ss5tw201ws}co{4qs5ku17sw5ou5qy5rw201ss5tw201ws}cp{4qs5ku17sw5ou5qy5rw201ss5tw201ws}6l{17su5tu5os5qw5rs}17s{2ktclvcmvcnvcovcpv4lv4wuckv}5o{ckwclwcmwcnwcowcpw4lw4wu}5q{ckyclycmycnycoycpy4ly4wu5ms}5r{cktcltcmtcntcotcpt4lt4ws}5t{2ktclvcmvcnvcovcpv4lv4wuckv}7q{cksclscmscnscoscps4ls}6p{17su5tu5qw5rs}ek{5qs5rs}el{17su5tu5os5qw5rs}em{17su5tu5os5qs5rs}en{17su5qs5rs}eo{5qs5rs}ep{17su5tu5os5qw5rs}es{5qs}et{17su5tu5qw5rs}eu{17su5tu5qs5rs}ev{5qs}6z{17sv5tv5os5qx5rs}fm{5os5qt5rs}fn{17sv5tv5os5qx5rs}fo{17sv5tv5os5qx5rs}fp{5os5qt5rs}fq{5os5qt5rs}7r{ckuclucmucnucoucpu4lu5os}fs{17sv5tv5os5qx5rs}ft{17ss5ts5qs}fu{17sw5tw5qs}fv{17sw5tw5qs}fw{17ss5ts5qs}fz{ckuclucmucnucoucpu4lu5os5rs}}}"),"Helvetica-Oblique":c("{'widths'{k3p2q4mcx1w201n3r201o6o201s1q201t1q201u1q201w2l201x2l201y2l2k1w2l1w202m2n2n3r2o3r2p5t202q6o2r1n2s2l2t2l2u2r2v3u2w1w2x2l2y1w2z1w3k3r3l3r3m3r3n3r3o3r3p3r3q3r3r3r3s3r203t2l203u2l3v1w3w3u3x3u3y3u3z3r4k6p4l4m4m4m4n4s4o4s4p4m4q3x4r4y4s4s4t1w4u3m4v4m4w3r4x5n4y4s4z4y5k4m5l4y5m4s5n4m5o3x5p4s5q4m5r5y5s4m5t4m5u3x5v1w5w1w5x1w5y2z5z3r6k2l6l3r6m3r6n3m6o3r6p3r6q1w6r3r6s3r6t1q6u1q6v3m6w1q6x5n6y3r6z3r7k3r7l3r7m2l7n3m7o1w7p3r7q3m7r4s7s3m7t3m7u3m7v2l7w1u7x2l7y3u202l3rcl4mal2lam3ran3rao3rap3rar3ras2lat4tau2pav3raw3uay4taz2lbk2sbl3u'fof'6obo2lbp3rbr1wbs2lbu2obv3rbz3xck4m202k3rcm4mcn4mco4mcp4mcq6ocr4scs4mct4mcu4mcv4mcw1w2m2ncy1wcz1wdl4sdm4ydn4ydo4ydp4ydq4yds4ydt4sdu4sdv4sdw4sdz3xek3rel3rem3ren3reo3rep3req5ter3mes3ret3reu3rev3rew1wex1wey1wez1wfl3rfm3rfn3rfo3rfp3rfq3rfr3ufs3xft3rfu3rfv3rfw3rfz3m203k6o212m6o2dw2l2cq2l3t3r3u1w17s4m19m3r}'kerning'{5q{4wv}cl{4qs5kw5ow5qs17sv5tv}201t{2wu4w1k2yu}201x{2wu4wy2yu}17s{2ktclucmucnu4otcpu4lu4wycoucku}2w{7qs4qz5k1m17sy5ow5qx5rsfsu5ty7tufzu}2x{17sy5ty5oy5qs}2y{7qs4qz5k1m17sy5ow5qx5rsfsu5ty7tufzu}'fof'-6o7p{17sv5tv5ow}ck{4qs5kw5ow5qs17sv5tv}4l{4qs5kw5ow5qs17sv5tv}cm{4qs5kw5ow5qs17sv5tv}cn{4qs5kw5ow5qs17sv5tv}co{4qs5kw5ow5qs17sv5tv}cp{4qs5kw5ow5qs17sv5tv}6l{17sy5ty5ow}do{17st5tt}4z{17st5tt}7s{fst}dm{17st5tt}dn{17st5tt}5o{ckwclwcmwcnwcowcpw4lw4wv}dp{17st5tt}dq{17st5tt}7t{5ow}ds{17st5tt}5t{2ktclucmucnu4otcpu4lu4wycoucku}fu{17sv5tv5ow}6p{17sy5ty5ow5qs}ek{17sy5ty5ow}el{17sy5ty5ow}em{17sy5ty5ow}en{5ty}eo{17sy5ty5ow}ep{17sy5ty5ow}es{17sy5ty5qs}et{17sy5ty5ow5qs}eu{17sy5ty5ow5qs}ev{17sy5ty5ow5qs}6z{17sy5ty5ow5qs}fm{17sy5ty5ow5qs}fn{17sy5ty5ow5qs}fo{17sy5ty5ow5qs}fp{17sy5ty5qs}fq{17sy5ty5ow5qs}7r{5ow}fs{17sy5ty5ow5qs}ft{17sv5tv5ow}7m{5ow}fv{17sv5tv5ow}fw{17sv5tv5ow}}}")}};t.events.push(["addFont",function(t){var e=t.font,n=h.Unicode[e.postScriptName];n&&(e.metadata.Unicode={},e.metadata.Unicode.widths=n.widths,e.metadata.Unicode.kerning=n.kerning);var r=l.Unicode[e.postScriptName];r&&(e.metadata.Unicode.encoding=r,e.encoding=r.codePages[0])}])}(D.API),
/**
 * @license
 * Licensed under the MIT License.
 * http://opensource.org/licenses/mit-license
 */
function(t){var e=function(t){for(var e=t.length,n=new Uint8Array(e),r=0;r<e;r++)n[r]=t.charCodeAt(r);return n};t.API.events.push(["addFont",function(n){var r=void 0,i=n.font,o=n.instance;if(!i.isStandardFont){if(void 0===o)throw new Error("Font does not exist in vFS, import fonts or remove declaration doc.addFont('"+i.postScriptName+"').");if("string"!=typeof(r=!1===o.existsFileInVFS(i.postScriptName)?o.loadFile(i.postScriptName):o.getFileFromVFS(i.postScriptName)))throw new Error("Font is not stored as string-data in vFS, import fonts or remove declaration doc.addFont('"+i.postScriptName+"').");!function(n,r){r=/^\x00\x01\x00\x00/.test(r)?e(r):e(f(r)),n.metadata=t.API.TTFFont.open(r),n.metadata.Unicode=n.metadata.Unicode||{encoding:{},kerning:{},widths:[]},n.metadata.glyIdsUsed=[0]}(i,r)}}])}(D),
/** @license
 * Copyright (c) 2012 Willow Systems Corporation, https://github.com/willowsystems
 *
 * Permission is hereby granted, free of charge, to any person obtaining
 * a copy of this software and associated documentation files (the
 * "Software"), to deal in the Software without restriction, including
 * without limitation the rights to use, copy, modify, merge, publish,
 * distribute, sublicense, and/or sell copies of the Software, and to
 * permit persons to whom the Software is furnished to do so, subject to
 * the following conditions:
 *
 * The above copyright notice and this permission notice shall be
 * included in all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
 * EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
 * NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE
 * LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION
 * OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION
 * WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
 * ====================================================================
 */
function(t){function e(){return(s.canvg?Promise.resolve(s.canvg):Promise.resolve().then(n.bind(null,"0d73"))).catch((function(t){return Promise.reject(new Error("Could not load canvg: "+t))})).then((function(t){return t.default?t.default:t}))}D.API.addSvgAsImage=function(t,n,r,i,o,s,a,u){if(isNaN(n)||isNaN(r))throw c.error("jsPDF.addSvgAsImage: Invalid coordinates",arguments),new Error("Invalid coordinates passed to jsPDF.addSvgAsImage");if(isNaN(i)||isNaN(o))throw c.error("jsPDF.addSvgAsImage: Invalid measurements",arguments),new Error("Invalid measurements (width and/or height) passed to jsPDF.addSvgAsImage");var l=document.createElement("canvas");l.width=i,l.height=o;var h=l.getContext("2d");h.fillStyle="#fff",h.fillRect(0,0,l.width,l.height);var f={ignoreMouse:!0,ignoreAnimation:!0,ignoreDimensions:!0},d=this;return e().then((function(e){return e.fromString(h,t,f)}),(function(){return Promise.reject(new Error("Could not load canvg."))})).then((function(t){return t.render(f)})).then((function(){d.addImage(l.toDataURL("image/jpeg",1),n,r,i,o,a,u)}))}}(),D.API.putTotalPages=function(t){var e,n=0;parseInt(this.internal.getFont().id.substr(1),10)<15?(e=new RegExp(t,"g"),n=this.internal.getNumberOfPages()):(e=new RegExp(this.pdfEscape16(t,this.internal.getFont()),"g"),n=this.pdfEscape16(this.internal.getNumberOfPages()+"",this.internal.getFont()));for(var r=1;r<=this.internal.getNumberOfPages();r++)for(var i=0;i<this.internal.pages[r].length;i++)this.internal.pages[r][i]=this.internal.pages[r][i].replace(e,n);return this},D.API.viewerPreferences=function(t,e){var n;t=t||{},e=e||!1;var r,o,s,a={HideToolbar:{defaultValue:!1,value:!1,type:"boolean",explicitSet:!1,valueSet:[!0,!1],pdfVersion:1.3},HideMenubar:{defaultValue:!1,value:!1,type:"boolean",explicitSet:!1,valueSet:[!0,!1],pdfVersion:1.3},HideWindowUI:{defaultValue:!1,value:!1,type:"boolean",explicitSet:!1,valueSet:[!0,!1],pdfVersion:1.3},FitWindow:{defaultValue:!1,value:!1,type:"boolean",explicitSet:!1,valueSet:[!0,!1],pdfVersion:1.3},CenterWindow:{defaultValue:!1,value:!1,type:"boolean",explicitSet:!1,valueSet:[!0,!1],pdfVersion:1.3},DisplayDocTitle:{defaultValue:!1,value:!1,type:"boolean",explicitSet:!1,valueSet:[!0,!1],pdfVersion:1.4},NonFullScreenPageMode:{defaultValue:"UseNone",value:"UseNone",type:"name",explicitSet:!1,valueSet:["UseNone","UseOutlines","UseThumbs","UseOC"],pdfVersion:1.3},Direction:{defaultValue:"L2R",value:"L2R",type:"name",explicitSet:!1,valueSet:["L2R","R2L"],pdfVersion:1.3},ViewArea:{defaultValue:"CropBox",value:"CropBox",type:"name",explicitSet:!1,valueSet:["MediaBox","CropBox","TrimBox","BleedBox","ArtBox"],pdfVersion:1.4},ViewClip:{defaultValue:"CropBox",value:"CropBox",type:"name",explicitSet:!1,valueSet:["MediaBox","CropBox","TrimBox","BleedBox","ArtBox"],pdfVersion:1.4},PrintArea:{defaultValue:"CropBox",value:"CropBox",type:"name",explicitSet:!1,valueSet:["MediaBox","CropBox","TrimBox","BleedBox","ArtBox"],pdfVersion:1.4},PrintClip:{defaultValue:"CropBox",value:"CropBox",type:"name",explicitSet:!1,valueSet:["MediaBox","CropBox","TrimBox","BleedBox","ArtBox"],pdfVersion:1.4},PrintScaling:{defaultValue:"AppDefault",value:"AppDefault",type:"name",explicitSet:!1,valueSet:["AppDefault","None"],pdfVersion:1.6},Duplex:{defaultValue:"",value:"none",type:"name",explicitSet:!1,valueSet:["Simplex","DuplexFlipShortEdge","DuplexFlipLongEdge","none"],pdfVersion:1.7},PickTrayByPDFSize:{defaultValue:!1,value:!1,type:"boolean",explicitSet:!1,valueSet:[!0,!1],pdfVersion:1.7},PrintPageRange:{defaultValue:"",value:"",type:"array",explicitSet:!1,valueSet:null,pdfVersion:1.7},NumCopies:{defaultValue:1,value:1,type:"integer",explicitSet:!1,valueSet:null,pdfVersion:1.7}},c=Object.keys(a),u=[],l=0,h=0,f=0;function d(t,e){var n,r=!1;for(n=0;n<t.length;n+=1)t[n]===e&&(r=!0);return r}if(void 0===this.internal.viewerpreferences&&(this.internal.viewerpreferences={},this.internal.viewerpreferences.configuration=JSON.parse(JSON.stringify(a)),this.internal.viewerpreferences.isSubscribed=!1),n=this.internal.viewerpreferences.configuration,"reset"===t||!0===e){var p=c.length;for(f=0;f<p;f+=1)n[c[f]].value=n[c[f]].defaultValue,n[c[f]].explicitSet=!1}if("object"===i()(t))for(o in t)if(s=t[o],d(c,o)&&void 0!==s){if("boolean"===n[o].type&&"boolean"==typeof s)n[o].value=s;else if("name"===n[o].type&&d(n[o].valueSet,s))n[o].value=s;else if("integer"===n[o].type&&Number.isInteger(s))n[o].value=s;else if("array"===n[o].type){for(l=0;l<s.length;l+=1)if(r=!0,1===s[l].length&&"number"==typeof s[l][0])u.push(String(s[l]-1));else if(s[l].length>1){for(h=0;h<s[l].length;h+=1)"number"!=typeof s[l][h]&&(r=!1);!0===r&&u.push([s[l][0]-1,s[l][1]-1].join(" "))}n[o].value="["+u.join(" ")+"]"}else n[o].value=n[o].defaultValue;n[o].explicitSet=!0}return!1===this.internal.viewerpreferences.isSubscribed&&(this.internal.events.subscribe("putCatalog",(function(){var t,e=[];for(t in n)!0===n[t].explicitSet&&("name"===n[t].type?e.push("/"+t+" /"+n[t].value):e.push("/"+t+" "+n[t].value));0!==e.length&&this.internal.write("/ViewerPreferences\n<<\n"+e.join("\n")+"\n>>")})),this.internal.viewerpreferences.isSubscribed=!0),this.internal.viewerpreferences.configuration=n,this
/** ====================================================================
 * @license
 * jsPDF XMP metadata plugin
 * Copyright (c) 2016 Jussi Utunen, <EMAIL>
 *
 * Permission is hereby granted, free of charge, to any person obtaining
 * a copy of this software and associated documentation files (the
 * "Software"), to deal in the Software without restriction, including
 * without limitation the rights to use, copy, modify, merge, publish,
 * distribute, sublicense, and/or sell copies of the Software, and to
 * permit persons to whom the Software is furnished to do so, subject to
 * the following conditions:
 *
 * The above copyright notice and this permission notice shall be
 * included in all copies or substantial portions of the Software.
 *
 * THE SOFTWARE IS PROVIDED "AS IS", WITHOUT WARRANTY OF ANY KIND,
 * EXPRESS OR IMPLIED, INCLUDING BUT NOT LIMITED TO THE WARRANTIES OF
 * MERCHANTABILITY, FITNESS FOR A PARTICULAR PURPOSE AND
 * NONINFRINGEMENT. IN NO EVENT SHALL THE AUTHORS OR COPYRIGHT HOLDERS BE
 * LIABLE FOR ANY CLAIM, DAMAGES OR OTHER LIABILITY, WHETHER IN AN ACTION
 * OF CONTRACT, TORT OR OTHERWISE, ARISING FROM, OUT OF OR IN CONNECTION
 * WITH THE SOFTWARE OR THE USE OR OTHER DEALINGS IN THE SOFTWARE.
 * ====================================================================
 */},function(t){var e=function(){var t='<rdf:RDF xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#"><rdf:Description rdf:about="" xmlns:jspdf="'+this.internal.__metadata__.namespaceuri+'"><jspdf:metadata>',e=unescape(encodeURIComponent('<x:xmpmeta xmlns:x="adobe:ns:meta/">')),n=unescape(encodeURIComponent(t)),r=unescape(encodeURIComponent(this.internal.__metadata__.metadata)),i=unescape(encodeURIComponent("</jspdf:metadata></rdf:Description></rdf:RDF>")),o=unescape(encodeURIComponent("</x:xmpmeta>")),s=n.length+r.length+i.length+e.length+o.length;this.internal.__metadata__.metadata_object_number=this.internal.newObject(),this.internal.write("<< /Type /Metadata /Subtype /XML /Length "+s+" >>"),this.internal.write("stream"),this.internal.write(e+n+r+i+o),this.internal.write("endstream"),this.internal.write("endobj")},n=function(){this.internal.__metadata__.metadata_object_number&&this.internal.write("/Metadata "+this.internal.__metadata__.metadata_object_number+" 0 R")};t.addMetadata=function(t,r){return void 0===this.internal.__metadata__&&(this.internal.__metadata__={metadata:t,namespaceuri:r||"http://jspdf.default.namespaceuri/"},this.internal.events.subscribe("putCatalog",n),this.internal.events.subscribe("postPutResources",e)),this}}(D.API),function(t){var e=t.API,n=e.pdfEscape16=function(t,e){for(var n,r=e.metadata.Unicode.widths,i=["","0","00","000","0000"],o=[""],s=0,a=t.length;s<a;++s){if(n=e.metadata.characterToGlyph(t.charCodeAt(s)),e.metadata.glyIdsUsed.push(n),e.metadata.toUnicode[n]=t.charCodeAt(s),-1==r.indexOf(n)&&(r.push(n),r.push([parseInt(e.metadata.widthOfGlyph(n),10)])),"0"==n)return o.join("");n=n.toString(16),o.push(i[4-n.length],n)}return o.join("")},r=function(t){var e,n,r,i,o,s,a;for(o="/CIDInit /ProcSet findresource begin\n12 dict begin\nbegincmap\n/CIDSystemInfo <<\n  /Registry (Adobe)\n  /Ordering (UCS)\n  /Supplement 0\n>> def\n/CMapName /Adobe-Identity-UCS def\n/CMapType 2 def\n1 begincodespacerange\n<0000><ffff>\nendcodespacerange",r=[],s=0,a=(n=Object.keys(t).sort((function(t,e){return t-e}))).length;s<a;s++)e=n[s],r.length>=100&&(o+="\n"+r.length+" beginbfchar\n"+r.join("\n")+"\nendbfchar",r=[]),void 0!==t[e]&&null!==t[e]&&"function"==typeof t[e].toString&&(i=("0000"+t[e].toString(16)).slice(-4),e=("0000"+(+e).toString(16)).slice(-4),r.push("<"+e+"><"+i+">"));return r.length&&(o+="\n"+r.length+" beginbfchar\n"+r.join("\n")+"\nendbfchar\n"),o+"endcmap\nCMapName currentdict /CMap defineresource pop\nend\nend"};e.events.push(["putFont",function(e){!function(e){var n=e.font,i=e.out,o=e.newObject,s=e.putStream;if(n.metadata instanceof t.API.TTFFont&&"Identity-H"===n.encoding){for(var a=n.metadata.Unicode.widths,c=n.metadata.subset.encode(n.metadata.glyIdsUsed,1),u="",l=0;l<c.length;l++)u+=String.fromCharCode(c[l]);var h=o();s({data:u,addLength1:!0,objectId:h}),i("endobj");var f=o();s({data:r(n.metadata.toUnicode),addLength1:!0,objectId:f}),i("endobj");var d=o();i("<<"),i("/Type /FontDescriptor"),i("/FontName /"+O(n.fontName)),i("/FontFile2 "+h+" 0 R"),i("/FontBBox "+t.API.PDFObject.convert(n.metadata.bbox)),i("/Flags "+n.metadata.flags),i("/StemV "+n.metadata.stemV),i("/ItalicAngle "+n.metadata.italicAngle),i("/Ascent "+n.metadata.ascender),i("/Descent "+n.metadata.decender),i("/CapHeight "+n.metadata.capHeight),i(">>"),i("endobj");var p=o();i("<<"),i("/Type /Font"),i("/BaseFont /"+O(n.fontName)),i("/FontDescriptor "+d+" 0 R"),i("/W "+t.API.PDFObject.convert(a)),i("/CIDToGIDMap /Identity"),i("/DW 1000"),i("/Subtype /CIDFontType2"),i("/CIDSystemInfo"),i("<<"),i("/Supplement 0"),i("/Registry (Adobe)"),i("/Ordering ("+n.encoding+")"),i(">>"),i(">>"),i("endobj"),n.objectNumber=o(),i("<<"),i("/Type /Font"),i("/Subtype /Type0"),i("/ToUnicode "+f+" 0 R"),i("/BaseFont /"+O(n.fontName)),i("/Encoding /"+n.encoding),i("/DescendantFonts ["+p+" 0 R]"),i(">>"),i("endobj"),n.isAlreadyPutted=!0}}(e)}]),e.events.push(["putFont",function(e){!function(e){var n=e.font,i=e.out,o=e.newObject,s=e.putStream;if(n.metadata instanceof t.API.TTFFont&&"WinAnsiEncoding"===n.encoding){for(var a=n.metadata.rawData,c="",u=0;u<a.length;u++)c+=String.fromCharCode(a[u]);var l=o();s({data:c,addLength1:!0,objectId:l}),i("endobj");var h=o();s({data:r(n.metadata.toUnicode),addLength1:!0,objectId:h}),i("endobj");var f=o();i("<<"),i("/Descent "+n.metadata.decender),i("/CapHeight "+n.metadata.capHeight),i("/StemV "+n.metadata.stemV),i("/Type /FontDescriptor"),i("/FontFile2 "+l+" 0 R"),i("/Flags 96"),i("/FontBBox "+t.API.PDFObject.convert(n.metadata.bbox)),i("/FontName /"+O(n.fontName)),i("/ItalicAngle "+n.metadata.italicAngle),i("/Ascent "+n.metadata.ascender),i(">>"),i("endobj"),n.objectNumber=o();for(var d=0;d<n.metadata.hmtx.widths.length;d++)n.metadata.hmtx.widths[d]=parseInt(n.metadata.hmtx.widths[d]*(1e3/n.metadata.head.unitsPerEm));i("<</Subtype/TrueType/Type/Font/ToUnicode "+h+" 0 R/BaseFont/"+O(n.fontName)+"/FontDescriptor "+f+" 0 R/Encoding/"+n.encoding+" /FirstChar 29 /LastChar 255 /Widths "+t.API.PDFObject.convert(n.metadata.hmtx.widths)+">>"),i("endobj"),n.isAlreadyPutted=!0}}(e)}]);var i=function(t){var e,r=t.text||"",i=t.x,o=t.y,s=t.options||{},a=t.mutex||{},c=a.pdfEscape,u=a.activeFontKey,l=a.fonts,h=u,f="",d=0,p="",m=l[h].encoding;if("Identity-H"!==l[h].encoding)return{text:r,x:i,y:o,options:s,mutex:a};for(p=r,h=u,Array.isArray(r)&&(p=r[0]),d=0;d<p.length;d+=1)l[h].metadata.hasOwnProperty("cmap")&&(e=l[h].metadata.cmap.unicode.codeMap[p[d].charCodeAt(0)]),e||p[d].charCodeAt(0)<256&&l[h].metadata.hasOwnProperty("Unicode")?f+=p[d]:f+="";var g="";return parseInt(h.slice(1))<14||"WinAnsiEncoding"===m?g=c(f,h).split("").map((function(t){return t.charCodeAt(0).toString(16)})).join(""):"Identity-H"===m&&(g=n(f,l[h])),a.isHex=!0,{text:g,x:i,y:o,options:s,mutex:a}};e.events.push(["postProcessText",function(t){var e=t.text||"",n=[],r={text:e,x:t.x,y:t.y,options:t.options,mutex:t.mutex};if(Array.isArray(e)){var o=0;for(o=0;o<e.length;o+=1)Array.isArray(e[o])&&3===e[o].length?n.push([i(Object.assign({},r,{text:e[o][0]})).text,e[o][1],e[o][2]]):n.push(i(Object.assign({},r,{text:e[o]})).text);t.text=n}else t.text=i(Object.assign({},r,{text:e})).text}])}(D),
/**
 * @license
 * jsPDF virtual FileSystem functionality
 *
 * Licensed under the MIT License.
 * http://opensource.org/licenses/mit-license
 */
function(t){var e=function(){return void 0===this.internal.vFS&&(this.internal.vFS={}),!0};t.existsFileInVFS=function(t){return e.call(this),void 0!==this.internal.vFS[t]},t.addFileToVFS=function(t,n){return e.call(this),this.internal.vFS[t]=n,this},t.getFileFromVFS=function(t){return e.call(this),void 0!==this.internal.vFS[t]?this.internal.vFS[t]:null}}(D.API),
/**
 * @license
 * Unicode Bidi Engine based on the work of Alex Shensis (@asthensis)
 * MIT License
 */
function(t){t.__bidiEngine__=t.prototype.__bidiEngine__=function(t){var n,r,i,o,s,a,c,u=e,l=[[0,3,0,1,0,0,0],[0,3,0,1,2,2,0],[0,3,0,17,2,0,1],[0,3,5,5,4,1,0],[0,3,21,21,4,0,1],[0,3,5,5,4,2,0]],h=[[2,0,1,1,0,1,0],[2,0,1,1,0,2,0],[2,0,2,1,3,2,0],[2,0,2,33,3,1,1]],f={L:0,R:1,EN:2,AN:3,N:4,B:5,S:6},d={0:0,5:1,6:2,7:3,32:4,251:5,254:6,255:7},p=["(",")","(","<",">","<","[","]","[","{","}","{","«","»","«","‹","›","‹","⁅","⁆","⁅","⁽","⁾","⁽","₍","₎","₍","≤","≥","≤","〈","〉","〈","﹙","﹚","﹙","﹛","﹜","﹛","﹝","﹞","﹝","﹤","﹥","﹤"],m=new RegExp(/^([1-4|9]|1[0-9]|2[0-9]|3[0168]|4[04589]|5[012]|7[78]|159|16[0-9]|17[0-2]|21[569]|22[03489]|250)$/),g=!1,v=0;this.__bidiEngine__={};var b=function(t){var e=t.charCodeAt(),n=e>>8,r=d[n];return void 0!==r?u[256*r+(255&e)]:252===n||253===n?"AL":m.test(n)?"L":8===n?"R":"N"},y=function(t){for(var e,n=0;n<t.length;n++){if("L"===(e=b(t.charAt(n))))return!1;if("R"===e)return!0}return!1},w=function(t,e,s,a){var c,u,l,h,f=e[a];switch(f){case"L":case"R":g=!1;break;case"N":case"AN":break;case"EN":g&&(f="AN");break;case"AL":g=!0,f="R";break;case"WS":f="N";break;case"CS":a<1||a+1>=e.length||"EN"!==(c=s[a-1])&&"AN"!==c||"EN"!==(u=e[a+1])&&"AN"!==u?f="N":g&&(u="AN"),f=u===c?u:"N";break;case"ES":f="EN"===(c=a>0?s[a-1]:"B")&&a+1<e.length&&"EN"===e[a+1]?"EN":"N";break;case"ET":if(a>0&&"EN"===s[a-1]){f="EN";break}if(g){f="N";break}for(l=a+1,h=e.length;l<h&&"ET"===e[l];)l++;f=l<h&&"EN"===e[l]?"EN":"N";break;case"NSM":if(i&&!o){for(h=e.length,l=a+1;l<h&&"NSM"===e[l];)l++;if(l<h){var d=t[a],p=d>=1425&&d<=2303||64286===d;if(c=e[l],p&&("R"===c||"AL"===c)){f="R";break}}}f=a<1||"B"===(c=e[a-1])?"N":s[a-1];break;case"B":g=!1,n=!0,f=v;break;case"S":r=!0,f="N";break;case"LRE":case"RLE":case"LRO":case"RLO":case"PDF":g=!1;break;case"BN":f="N"}return f},j=function(t,e,n){var r=t.split("");return n&&_(r,n,{hiLevel:v}),r.reverse(),e&&e.reverse(),r.join("")},_=function(t,e,i){var o,s,a,c,u,d=-1,p=t.length,m=0,y=[],j=v?h:l,_=[];for(g=!1,n=!1,r=!1,s=0;s<p;s++)_[s]=b(t[s]);for(a=0;a<p;a++){if(u=m,y[a]=w(t,_,y,a),o=240&(m=j[u][f[y[a]]]),m&=15,e[a]=c=j[m][5],o>0)if(16===o){for(s=d;s<a;s++)e[s]=1;d=-1}else d=-1;if(j[m][6])-1===d&&(d=a);else if(d>-1){for(s=d;s<a;s++)e[s]=c;d=-1}"B"===_[a]&&(e[a]=0),i.hiLevel|=c}r&&function(t,e,n){for(var r=0;r<n;r++)if("S"===t[r]){e[r]=v;for(var i=r-1;i>=0&&"WS"===t[i];i--)e[i]=v}}(_,e,p)},x=function(t,e,r,i,o){if(!(o.hiLevel<t)){if(1===t&&1===v&&!n)return e.reverse(),void(r&&r.reverse());for(var s,a,c,u,l=e.length,h=0;h<l;){if(i[h]>=t){for(c=h+1;c<l&&i[c]>=t;)c++;for(u=h,a=c-1;u<a;u++,a--)s=e[u],e[u]=e[a],e[a]=s,r&&(s=r[u],r[u]=r[a],r[a]=s);h=c}h++}}},N=function(t,e,n){var r=t.split(""),i={hiLevel:v};return n||(n=[]),_(r,n,i),function(t,e,n){if(0!==n.hiLevel&&c)for(var r,i=0;i<t.length;i++)1===e[i]&&(r=p.indexOf(t[i]))>=0&&(t[i]=p[r+1])}(r,n,i),x(2,r,e,n,i),x(1,r,e,n,i),r.join("")};return this.__bidiEngine__.doBidiReorder=function(t,e,n){if(function(t,e){if(e)for(var n=0;n<t.length;n++)e[n]=n;void 0===o&&(o=y(t)),void 0===a&&(a=y(t))}(t,e),i||!s||a)if(i&&s&&o^a)v=o?1:0,t=j(t,e,n);else if(!i&&s&&a)v=o?1:0,t=N(t,e,n),t=j(t,e);else if(!i||o||s||a){if(i&&!s&&o^a)t=j(t,e),o?(v=0,t=N(t,e,n)):(v=1,t=N(t,e,n),t=j(t,e));else if(i&&o&&!s&&a)v=1,t=N(t,e,n),t=j(t,e);else if(!i&&!s&&o^a){var r=c;o?(v=1,t=N(t,e,n),v=0,c=!1,t=N(t,e,n),c=r):(v=0,t=N(t,e,n),t=j(t,e),v=1,c=!1,t=N(t,e,n),c=r,t=j(t,e))}}else v=0,t=N(t,e,n);else v=o?1:0,t=N(t,e,n);return t},this.__bidiEngine__.setOptions=function(t){t&&(i=t.isInputVisual,s=t.isOutputVisual,o=t.isInputRtl,a=t.isOutputRtl,c=t.isSymmetricSwapping)},this.__bidiEngine__.setOptions(t),this.__bidiEngine__};var e=["BN","BN","BN","BN","BN","BN","BN","BN","BN","S","B","S","WS","B","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","B","B","B","S","WS","N","N","ET","ET","ET","N","N","N","N","N","ES","CS","ES","CS","CS","EN","EN","EN","EN","EN","EN","EN","EN","EN","EN","CS","N","N","N","N","N","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","N","N","N","N","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","N","N","N","BN","BN","BN","BN","BN","BN","B","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","BN","CS","N","ET","ET","ET","ET","N","N","N","N","L","N","N","BN","N","N","ET","ET","EN","EN","N","L","N","N","N","EN","L","N","N","N","N","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","N","L","L","L","L","L","L","L","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","L","N","N","N","N","N","ET","N","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","R","NSM","R","NSM","NSM","R","NSM","NSM","R","NSM","N","N","N","N","N","N","N","N","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","N","N","N","N","N","R","R","R","R","R","N","N","N","N","N","N","N","N","N","N","N","AN","AN","AN","AN","AN","AN","N","N","AL","ET","ET","AL","CS","AL","N","N","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","AL","AL","N","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","AN","AN","AN","AN","AN","AN","AN","AN","AN","AN","ET","AN","AN","AL","AL","AL","NSM","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","NSM","NSM","NSM","NSM","NSM","NSM","NSM","AN","N","NSM","NSM","NSM","NSM","NSM","NSM","AL","AL","NSM","NSM","N","NSM","NSM","NSM","NSM","AL","AL","EN","EN","EN","EN","EN","EN","EN","EN","EN","EN","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","N","AL","AL","NSM","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","N","N","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","AL","N","N","N","N","N","N","N","N","N","N","N","N","N","N","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","R","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","R","R","N","N","N","N","R","N","N","N","N","N","WS","WS","WS","WS","WS","WS","WS","WS","WS","WS","WS","BN","BN","BN","L","R","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","WS","B","LRE","RLE","PDF","LRO","RLO","CS","ET","ET","ET","ET","ET","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","CS","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","WS","BN","BN","BN","BN","BN","N","LRI","RLI","FSI","PDI","BN","BN","BN","BN","BN","BN","EN","L","N","N","EN","EN","EN","EN","EN","EN","ES","ES","N","N","N","L","EN","EN","EN","EN","EN","EN","EN","EN","EN","EN","ES","ES","N","N","N","N","L","L","L","L","L","L","L","L","L","L","L","L","L","N","N","N","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","ET","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","L","L","L","L","L","L","L","N","N","N","N","N","N","N","N","N","N","N","N","L","L","L","L","L","N","N","N","N","N","R","NSM","R","R","R","R","R","R","R","R","R","R","ES","R","R","R","R","R","R","R","R","R","R","R","R","R","N","R","R","R","R","R","N","R","N","R","R","N","R","R","N","R","R","R","R","R","R","R","R","R","R","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","NSM","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","CS","N","CS","N","N","CS","N","N","N","N","N","N","N","N","N","ET","N","N","ES","ES","N","N","N","N","N","ET","ET","N","N","N","N","N","AL","AL","AL","AL","AL","N","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","AL","N","N","BN","N","N","N","ET","ET","ET","N","N","N","N","N","ES","CS","ES","CS","CS","EN","EN","EN","EN","EN","EN","EN","EN","EN","EN","CS","N","N","N","N","N","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","N","N","N","N","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","N","N","N","N","N","N","N","N","N","N","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","L","N","N","N","L","L","L","L","L","L","N","N","L","L","L","L","L","L","N","N","L","L","L","L","L","L","N","N","L","L","L","N","N","N","ET","ET","N","N","N","ET","ET","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N","N"],n=new t.__bidiEngine__({isInputVisual:!0});t.API.events.push(["postProcessText",function(t){var e=t.text,r=(t.x,t.y,t.options||{}),i=(t.mutex,r.lang,[]);if(r.isInputVisual="boolean"!=typeof r.isInputVisual||r.isInputVisual,n.setOptions(r),"[object Array]"===Object.prototype.toString.call(e)){var o=0;for(i=[],o=0;o<e.length;o+=1)"[object Array]"===Object.prototype.toString.call(e[o])?i.push([n.doBidiReorder(e[o][0]),e[o][1],e[o][2]]):i.push([n.doBidiReorder(e[o])]);t.text=i}else t.text=n.doBidiReorder(e);n.setOptions({isInputVisual:!0})}])}(D),D.API.TTFFont=function(){function t(t){var e;if(this.rawData=t,e=this.contents=new se(t),this.contents.pos=4,"ttcf"===e.readString(4))throw new Error("TTCF not supported.");e.pos=0,this.parse(),this.subset=new Ae(this),this.registerTTF()}return t.open=function(e){return new t(e)},t.prototype.parse=function(){return this.directory=new ae(this.contents),this.head=new le(this),this.name=new ve(this),this.cmap=new fe(this),this.toUnicode={},this.hhea=new de(this),this.maxp=new be(this),this.hmtx=new ye(this),this.post=new me(this),this.os2=new pe(this),this.loca=new Ne(this),this.glyf=new je(this),this.ascender=this.os2.exists&&this.os2.ascender||this.hhea.ascender,this.decender=this.os2.exists&&this.os2.decender||this.hhea.decender,this.lineGap=this.os2.exists&&this.os2.lineGap||this.hhea.lineGap,this.bbox=[this.head.xMin,this.head.yMin,this.head.xMax,this.head.yMax]},t.prototype.registerTTF=function(){var t,e,n,r,i;if(this.scaleFactor=1e3/this.head.unitsPerEm,this.bbox=function(){var e,n,r,i;for(i=[],e=0,n=(r=this.bbox).length;e<n;e++)t=r[e],i.push(Math.round(t*this.scaleFactor));return i}.call(this),this.stemV=0,this.post.exists?(n=255&(r=this.post.italic_angle),0!=(32768&(e=r>>16))&&(e=-(1+(65535^e))),this.italicAngle=+(e+"."+n)):this.italicAngle=0,this.ascender=Math.round(this.ascender*this.scaleFactor),this.decender=Math.round(this.decender*this.scaleFactor),this.lineGap=Math.round(this.lineGap*this.scaleFactor),this.capHeight=this.os2.exists&&this.os2.capHeight||this.ascender,this.xHeight=this.os2.exists&&this.os2.xHeight||0,this.familyClass=(this.os2.exists&&this.os2.familyClass||0)>>8,this.isSerif=1===(i=this.familyClass)||2===i||3===i||4===i||5===i||7===i,this.isScript=10===this.familyClass,this.flags=0,this.post.isFixedPitch&&(this.flags|=1),this.isSerif&&(this.flags|=2),this.isScript&&(this.flags|=8),0!==this.italicAngle&&(this.flags|=64),this.flags|=32,!this.cmap.unicode)throw new Error("No unicode cmap for font")},t.prototype.characterToGlyph=function(t){var e;return(null!=(e=this.cmap.unicode)?e.codeMap[t]:void 0)||0},t.prototype.widthOfGlyph=function(t){var e;return e=1e3/this.head.unitsPerEm,this.hmtx.forGlyph(t).advance*e},t.prototype.widthOfString=function(t,e,n){var r,i,o,s;for(o=0,i=0,s=(t=""+t).length;0<=s?i<s:i>s;i=0<=s?++i:--i)r=t.charCodeAt(i),o+=this.widthOfGlyph(this.characterToGlyph(r))+n*(1e3/e)||0;return o*(e/1e3)},t.prototype.lineHeight=function(t,e){var n;return null==e&&(e=!1),n=e?this.lineGap:0,(this.ascender+n-this.decender)/1e3*t},t}();var oe,se=function(){function t(t){this.data=null!=t?t:[],this.pos=0,this.length=this.data.length}return t.prototype.readByte=function(){return this.data[this.pos++]},t.prototype.writeByte=function(t){return this.data[this.pos++]=t},t.prototype.readUInt32=function(){return 16777216*this.readByte()+(this.readByte()<<16)+(this.readByte()<<8)+this.readByte()},t.prototype.writeUInt32=function(t){return this.writeByte(t>>>24&255),this.writeByte(t>>16&255),this.writeByte(t>>8&255),this.writeByte(255&t)},t.prototype.readInt32=function(){var t;return(t=this.readUInt32())>=2147483648?t-4294967296:t},t.prototype.writeInt32=function(t){return t<0&&(t+=4294967296),this.writeUInt32(t)},t.prototype.readUInt16=function(){return this.readByte()<<8|this.readByte()},t.prototype.writeUInt16=function(t){return this.writeByte(t>>8&255),this.writeByte(255&t)},t.prototype.readInt16=function(){var t;return(t=this.readUInt16())>=32768?t-65536:t},t.prototype.writeInt16=function(t){return t<0&&(t+=65536),this.writeUInt16(t)},t.prototype.readString=function(t){var e,n;for(n=[],e=0;0<=t?e<t:e>t;e=0<=t?++e:--e)n[e]=String.fromCharCode(this.readByte());return n.join("")},t.prototype.writeString=function(t){var e,n,r;for(r=[],e=0,n=t.length;0<=n?e<n:e>n;e=0<=n?++e:--e)r.push(this.writeByte(t.charCodeAt(e)));return r},t.prototype.readShort=function(){return this.readInt16()},t.prototype.writeShort=function(t){return this.writeInt16(t)},t.prototype.readLongLong=function(){var t,e,n,r,i,o,s,a;return t=this.readByte(),e=this.readByte(),n=this.readByte(),r=this.readByte(),i=this.readByte(),o=this.readByte(),s=this.readByte(),a=this.readByte(),128&t?-1*(72057594037927940*(255^t)+281474976710656*(255^e)+1099511627776*(255^n)+4294967296*(255^r)+16777216*(255^i)+65536*(255^o)+256*(255^s)+(255^a)+1):72057594037927940*t+281474976710656*e+1099511627776*n+4294967296*r+16777216*i+65536*o+256*s+a},t.prototype.writeLongLong=function(t){var e,n;return e=Math.floor(t/4294967296),n=**********&t,this.writeByte(e>>24&255),this.writeByte(e>>16&255),this.writeByte(e>>8&255),this.writeByte(255&e),this.writeByte(n>>24&255),this.writeByte(n>>16&255),this.writeByte(n>>8&255),this.writeByte(255&n)},t.prototype.readInt=function(){return this.readInt32()},t.prototype.writeInt=function(t){return this.writeInt32(t)},t.prototype.read=function(t){var e,n;for(e=[],n=0;0<=t?n<t:n>t;n=0<=t?++n:--n)e.push(this.readByte());return e},t.prototype.write=function(t){var e,n,r,i;for(i=[],n=0,r=t.length;n<r;n++)e=t[n],i.push(this.writeByte(e));return i},t}(),ae=function(){var t;function e(t){var e,n,r;for(this.scalarType=t.readInt(),this.tableCount=t.readShort(),this.searchRange=t.readShort(),this.entrySelector=t.readShort(),this.rangeShift=t.readShort(),this.tables={},n=0,r=this.tableCount;0<=r?n<r:n>r;n=0<=r?++n:--n)e={tag:t.readString(4),checksum:t.readInt(),offset:t.readInt(),length:t.readInt()},this.tables[e.tag]=e}return e.prototype.encode=function(e){var n,r,i,o,s,a,c,u,l,h,f,d,p;for(p in f=Object.keys(e).length,a=Math.log(2),l=16*Math.floor(Math.log(f)/a),o=Math.floor(l/a),u=16*f-l,(r=new se).writeInt(this.scalarType),r.writeShort(f),r.writeShort(l),r.writeShort(o),r.writeShort(u),i=16*f,c=r.pos+i,s=null,d=[],e)for(h=e[p],r.writeString(p),r.writeInt(t(h)),r.writeInt(c),r.writeInt(h.length),d=d.concat(h),"head"===p&&(s=c),c+=h.length;c%4;)d.push(0),c++;return r.write(d),n=2981146554-t(r.data),r.pos=s+8,r.writeUInt32(n),r.data},t=function(t){var e,n,r,i;for(t=we.call(t);t.length%4;)t.push(0);for(r=new se(t),n=0,e=0,i=t.length;e<i;e=e+=4)n+=r.readUInt32();return **********&n},e}(),ce={}.hasOwnProperty,ue=function(t,e){for(var n in e)ce.call(e,n)&&(t[n]=e[n]);function r(){this.constructor=t}return r.prototype=e.prototype,t.prototype=new r,t.__super__=e.prototype,t};oe=function(){function t(t){var e;this.file=t,e=this.file.directory.tables[this.tag],this.exists=!!e,e&&(this.offset=e.offset,this.length=e.length,this.parse(this.file.contents))}return t.prototype.parse=function(){},t.prototype.encode=function(){},t.prototype.raw=function(){return this.exists?(this.file.contents.pos=this.offset,this.file.contents.read(this.length)):null},t}();var le=function(t){function e(){return e.__super__.constructor.apply(this,arguments)}return ue(e,oe),e.prototype.tag="head",e.prototype.parse=function(t){return t.pos=this.offset,this.version=t.readInt(),this.revision=t.readInt(),this.checkSumAdjustment=t.readInt(),this.magicNumber=t.readInt(),this.flags=t.readShort(),this.unitsPerEm=t.readShort(),this.created=t.readLongLong(),this.modified=t.readLongLong(),this.xMin=t.readShort(),this.yMin=t.readShort(),this.xMax=t.readShort(),this.yMax=t.readShort(),this.macStyle=t.readShort(),this.lowestRecPPEM=t.readShort(),this.fontDirectionHint=t.readShort(),this.indexToLocFormat=t.readShort(),this.glyphDataFormat=t.readShort()},e.prototype.encode=function(t){var e;return(e=new se).writeInt(this.version),e.writeInt(this.revision),e.writeInt(this.checkSumAdjustment),e.writeInt(this.magicNumber),e.writeShort(this.flags),e.writeShort(this.unitsPerEm),e.writeLongLong(this.created),e.writeLongLong(this.modified),e.writeShort(this.xMin),e.writeShort(this.yMin),e.writeShort(this.xMax),e.writeShort(this.yMax),e.writeShort(this.macStyle),e.writeShort(this.lowestRecPPEM),e.writeShort(this.fontDirectionHint),e.writeShort(t),e.writeShort(this.glyphDataFormat),e.data},e}(),he=function(){function t(t,e){var n,r,i,o,s,a,c,u,l,h,f,d,p,m,g,v,b;switch(this.platformID=t.readUInt16(),this.encodingID=t.readShort(),this.offset=e+t.readInt(),l=t.pos,t.pos=this.offset,this.format=t.readUInt16(),this.length=t.readUInt16(),this.language=t.readUInt16(),this.isUnicode=3===this.platformID&&1===this.encodingID&&4===this.format||0===this.platformID&&4===this.format,this.codeMap={},this.format){case 0:for(a=0;a<256;++a)this.codeMap[a]=t.readByte();break;case 4:for(f=t.readUInt16(),h=f/2,t.pos+=6,i=function(){var e,n;for(n=[],a=e=0;0<=h?e<h:e>h;a=0<=h?++e:--e)n.push(t.readUInt16());return n}(),t.pos+=2,p=function(){var e,n;for(n=[],a=e=0;0<=h?e<h:e>h;a=0<=h?++e:--e)n.push(t.readUInt16());return n}(),c=function(){var e,n;for(n=[],a=e=0;0<=h?e<h:e>h;a=0<=h?++e:--e)n.push(t.readUInt16());return n}(),u=function(){var e,n;for(n=[],a=e=0;0<=h?e<h:e>h;a=0<=h?++e:--e)n.push(t.readUInt16());return n}(),r=(this.length-t.pos+this.offset)/2,s=function(){var e,n;for(n=[],a=e=0;0<=r?e<r:e>r;a=0<=r?++e:--e)n.push(t.readUInt16());return n}(),a=g=0,b=i.length;g<b;a=++g)for(m=i[a],n=v=d=p[a];d<=m?v<=m:v>=m;n=d<=m?++v:--v)0===u[a]?o=n+c[a]:0!==(o=s[u[a]/2+(n-d)-(h-a)]||0)&&(o+=c[a]),this.codeMap[n]=65535&o}t.pos=l}return t.encode=function(t,e){var n,r,i,o,s,a,c,u,l,h,f,d,p,m,g,v,b,y,w,j,_,x,N,A,L,S,P,k,I,C,F,O,E,M,B,T,q,D,R,z,U,H,V,W,G,Y;switch(k=new se,o=Object.keys(t).sort((function(t,e){return t-e})),e){case"macroman":for(p=0,m=function(){var t=[];for(d=0;d<256;++d)t.push(0);return t}(),v={0:0},i={},I=0,E=o.length;I<E;I++)null==v[V=t[r=o[I]]]&&(v[V]=++p),i[r]={old:t[r],new:v[t[r]]},m[r]=v[t[r]];return k.writeUInt16(1),k.writeUInt16(0),k.writeUInt32(12),k.writeUInt16(0),k.writeUInt16(262),k.writeUInt16(0),k.write(m),{charMap:i,subtable:k.data,maxGlyphID:p+1};case"unicode":for(S=[],l=[],b=0,v={},n={},g=c=null,C=0,M=o.length;C<M;C++)null==v[w=t[r=o[C]]]&&(v[w]=++b),n[r]={old:w,new:v[w]},s=v[w]-r,null!=g&&s===c||(g&&l.push(g),S.push(r),c=s),g=r;for(g&&l.push(g),l.push(65535),S.push(65535),A=2*(N=S.length),x=2*Math.pow(Math.log(N)/Math.LN2,2),h=Math.log(x/2)/Math.LN2,_=2*N-x,a=[],j=[],f=[],d=F=0,B=S.length;F<B;d=++F){if(L=S[d],u=l[d],65535===L){a.push(0),j.push(0);break}if(L-(P=n[L].new)>=32768)for(a.push(0),j.push(2*(f.length+N-d)),r=O=L;L<=u?O<=u:O>=u;r=L<=u?++O:--O)f.push(n[r].new);else a.push(P-L),j.push(0)}for(k.writeUInt16(3),k.writeUInt16(1),k.writeUInt32(12),k.writeUInt16(4),k.writeUInt16(16+8*N+2*f.length),k.writeUInt16(0),k.writeUInt16(A),k.writeUInt16(x),k.writeUInt16(h),k.writeUInt16(_),U=0,T=l.length;U<T;U++)r=l[U],k.writeUInt16(r);for(k.writeUInt16(0),H=0,q=S.length;H<q;H++)r=S[H],k.writeUInt16(r);for(W=0,D=a.length;W<D;W++)s=a[W],k.writeUInt16(s);for(G=0,R=j.length;G<R;G++)y=j[G],k.writeUInt16(y);for(Y=0,z=f.length;Y<z;Y++)p=f[Y],k.writeUInt16(p);return{charMap:n,subtable:k.data,maxGlyphID:b+1}}},t}(),fe=function(t){function e(){return e.__super__.constructor.apply(this,arguments)}return ue(e,oe),e.prototype.tag="cmap",e.prototype.parse=function(t){var e,n,r;for(t.pos=this.offset,this.version=t.readUInt16(),r=t.readUInt16(),this.tables=[],this.unicode=null,n=0;0<=r?n<r:n>r;n=0<=r?++n:--n)e=new he(t,this.offset),this.tables.push(e),e.isUnicode&&null==this.unicode&&(this.unicode=e);return!0},e.encode=function(t,e){var n,r;return null==e&&(e="macroman"),n=he.encode(t,e),(r=new se).writeUInt16(0),r.writeUInt16(1),n.table=r.data.concat(n.subtable),n},e}(),de=function(t){function e(){return e.__super__.constructor.apply(this,arguments)}return ue(e,oe),e.prototype.tag="hhea",e.prototype.parse=function(t){return t.pos=this.offset,this.version=t.readInt(),this.ascender=t.readShort(),this.decender=t.readShort(),this.lineGap=t.readShort(),this.advanceWidthMax=t.readShort(),this.minLeftSideBearing=t.readShort(),this.minRightSideBearing=t.readShort(),this.xMaxExtent=t.readShort(),this.caretSlopeRise=t.readShort(),this.caretSlopeRun=t.readShort(),this.caretOffset=t.readShort(),t.pos+=8,this.metricDataFormat=t.readShort(),this.numberOfMetrics=t.readUInt16()},e}(),pe=function(t){function e(){return e.__super__.constructor.apply(this,arguments)}return ue(e,oe),e.prototype.tag="OS/2",e.prototype.parse=function(t){if(t.pos=this.offset,this.version=t.readUInt16(),this.averageCharWidth=t.readShort(),this.weightClass=t.readUInt16(),this.widthClass=t.readUInt16(),this.type=t.readShort(),this.ySubscriptXSize=t.readShort(),this.ySubscriptYSize=t.readShort(),this.ySubscriptXOffset=t.readShort(),this.ySubscriptYOffset=t.readShort(),this.ySuperscriptXSize=t.readShort(),this.ySuperscriptYSize=t.readShort(),this.ySuperscriptXOffset=t.readShort(),this.ySuperscriptYOffset=t.readShort(),this.yStrikeoutSize=t.readShort(),this.yStrikeoutPosition=t.readShort(),this.familyClass=t.readShort(),this.panose=function(){var e,n;for(n=[],e=0;e<10;++e)n.push(t.readByte());return n}(),this.charRange=function(){var e,n;for(n=[],e=0;e<4;++e)n.push(t.readInt());return n}(),this.vendorID=t.readString(4),this.selection=t.readShort(),this.firstCharIndex=t.readShort(),this.lastCharIndex=t.readShort(),this.version>0&&(this.ascent=t.readShort(),this.descent=t.readShort(),this.lineGap=t.readShort(),this.winAscent=t.readShort(),this.winDescent=t.readShort(),this.codePageRange=function(){var e,n;for(n=[],e=0;e<2;e=++e)n.push(t.readInt());return n}(),this.version>1))return this.xHeight=t.readShort(),this.capHeight=t.readShort(),this.defaultChar=t.readShort(),this.breakChar=t.readShort(),this.maxContext=t.readShort()},e}(),me=function(t){function e(){return e.__super__.constructor.apply(this,arguments)}return ue(e,oe),e.prototype.tag="post",e.prototype.parse=function(t){var e,n,r;switch(t.pos=this.offset,this.format=t.readInt(),this.italicAngle=t.readInt(),this.underlinePosition=t.readShort(),this.underlineThickness=t.readShort(),this.isFixedPitch=t.readInt(),this.minMemType42=t.readInt(),this.maxMemType42=t.readInt(),this.minMemType1=t.readInt(),this.maxMemType1=t.readInt(),this.format){case 65536:break;case 131072:var i;for(n=t.readUInt16(),this.glyphNameIndex=[],i=0;0<=n?i<n:i>n;i=0<=n?++i:--i)this.glyphNameIndex.push(t.readUInt16());for(this.names=[],r=[];t.pos<this.offset+this.length;)e=t.readByte(),r.push(this.names.push(t.readString(e)));return r;case 151552:return n=t.readUInt16(),this.offsets=t.read(n);case 196608:break;case 262144:return this.map=function(){var e,n,r;for(r=[],i=e=0,n=this.file.maxp.numGlyphs;0<=n?e<n:e>n;i=0<=n?++e:--e)r.push(t.readUInt32());return r}.call(this)}},e}(),ge=function(t,e){this.raw=t,this.length=t.length,this.platformID=e.platformID,this.encodingID=e.encodingID,this.languageID=e.languageID},ve=function(t){function e(){return e.__super__.constructor.apply(this,arguments)}return ue(e,oe),e.prototype.tag="name",e.prototype.parse=function(t){var e,n,r,i,o,s,a,c,u,l,h;for(t.pos=this.offset,t.readShort(),e=t.readShort(),s=t.readShort(),n=[],i=0;0<=e?i<e:i>e;i=0<=e?++i:--i)n.push({platformID:t.readShort(),encodingID:t.readShort(),languageID:t.readShort(),nameID:t.readShort(),length:t.readShort(),offset:this.offset+s+t.readShort()});for(a={},i=u=0,l=n.length;u<l;i=++u)r=n[i],t.pos=r.offset,c=t.readString(r.length),o=new ge(c,r),null==a[h=r.nameID]&&(a[h]=[]),a[r.nameID].push(o);this.strings=a,this.copyright=a[0],this.fontFamily=a[1],this.fontSubfamily=a[2],this.uniqueSubfamily=a[3],this.fontName=a[4],this.version=a[5];try{this.postscriptName=a[6][0].raw.replace(/[\x00-\x19\x80-\xff]/g,"")}catch(t){this.postscriptName=a[4][0].raw.replace(/[\x00-\x19\x80-\xff]/g,"")}return this.trademark=a[7],this.manufacturer=a[8],this.designer=a[9],this.description=a[10],this.vendorUrl=a[11],this.designerUrl=a[12],this.license=a[13],this.licenseUrl=a[14],this.preferredFamily=a[15],this.preferredSubfamily=a[17],this.compatibleFull=a[18],this.sampleText=a[19]},e}(),be=function(t){function e(){return e.__super__.constructor.apply(this,arguments)}return ue(e,oe),e.prototype.tag="maxp",e.prototype.parse=function(t){return t.pos=this.offset,this.version=t.readInt(),this.numGlyphs=t.readUInt16(),this.maxPoints=t.readUInt16(),this.maxContours=t.readUInt16(),this.maxCompositePoints=t.readUInt16(),this.maxComponentContours=t.readUInt16(),this.maxZones=t.readUInt16(),this.maxTwilightPoints=t.readUInt16(),this.maxStorage=t.readUInt16(),this.maxFunctionDefs=t.readUInt16(),this.maxInstructionDefs=t.readUInt16(),this.maxStackElements=t.readUInt16(),this.maxSizeOfInstructions=t.readUInt16(),this.maxComponentElements=t.readUInt16(),this.maxComponentDepth=t.readUInt16()},e}(),ye=function(t){function e(){return e.__super__.constructor.apply(this,arguments)}return ue(e,oe),e.prototype.tag="hmtx",e.prototype.parse=function(t){var e,n,r,i,o,s,a;for(t.pos=this.offset,this.metrics=[],e=0,s=this.file.hhea.numberOfMetrics;0<=s?e<s:e>s;e=0<=s?++e:--e)this.metrics.push({advance:t.readUInt16(),lsb:t.readInt16()});for(r=this.file.maxp.numGlyphs-this.file.hhea.numberOfMetrics,this.leftSideBearings=function(){var n,i;for(i=[],e=n=0;0<=r?n<r:n>r;e=0<=r?++n:--n)i.push(t.readInt16());return i}(),this.widths=function(){var t,e,n,r;for(r=[],t=0,e=(n=this.metrics).length;t<e;t++)i=n[t],r.push(i.advance);return r}.call(this),n=this.widths[this.widths.length-1],a=[],e=o=0;0<=r?o<r:o>r;e=0<=r?++o:--o)a.push(this.widths.push(n));return a},e.prototype.forGlyph=function(t){return t in this.metrics?this.metrics[t]:{advance:this.metrics[this.metrics.length-1].advance,lsb:this.leftSideBearings[t-this.metrics.length]}},e}(),we=[].slice,je=function(t){function e(){return e.__super__.constructor.apply(this,arguments)}return ue(e,oe),e.prototype.tag="glyf",e.prototype.parse=function(){return this.cache={}},e.prototype.glyphFor=function(t){var e,n,r,i,o,s,a,c,u,l;return t in this.cache?this.cache[t]:(i=this.file.loca,e=this.file.contents,n=i.indexOf(t),0===(r=i.lengthOf(t))?this.cache[t]=null:(e.pos=this.offset+n,o=(s=new se(e.read(r))).readShort(),c=s.readShort(),l=s.readShort(),a=s.readShort(),u=s.readShort(),this.cache[t]=-1===o?new xe(s,c,l,a,u):new _e(s,o,c,l,a,u),this.cache[t]))},e.prototype.encode=function(t,e,n){var r,i,o,s,a;for(o=[],i=[],s=0,a=e.length;s<a;s++)r=t[e[s]],i.push(o.length),r&&(o=o.concat(r.encode(n)));return i.push(o.length),{table:o,offsets:i}},e}(),_e=function(){function t(t,e,n,r,i,o){this.raw=t,this.numberOfContours=e,this.xMin=n,this.yMin=r,this.xMax=i,this.yMax=o,this.compound=!1}return t.prototype.encode=function(){return this.raw.data},t}(),xe=function(){function t(t,e,n,r,i){var o,s;for(this.raw=t,this.xMin=e,this.yMin=n,this.xMax=r,this.yMax=i,this.compound=!0,this.glyphIDs=[],this.glyphOffsets=[],o=this.raw;s=o.readShort(),this.glyphOffsets.push(o.pos),this.glyphIDs.push(o.readUInt16()),32&s;)o.pos+=1&s?4:2,128&s?o.pos+=8:64&s?o.pos+=4:8&s&&(o.pos+=2)}return t.prototype.encode=function(){var t,e,n;for(e=new se(we.call(this.raw.data)),t=0,n=this.glyphIDs.length;t<n;++t)e.pos=this.glyphOffsets[t];return e.data},t}(),Ne=function(t){function e(){return e.__super__.constructor.apply(this,arguments)}return ue(e,oe),e.prototype.tag="loca",e.prototype.parse=function(t){var e,n;return t.pos=this.offset,e=this.file.head.indexToLocFormat,this.offsets=0===e?function(){var e,r;for(r=[],n=0,e=this.length;n<e;n+=2)r.push(2*t.readUInt16());return r}.call(this):function(){var e,r;for(r=[],n=0,e=this.length;n<e;n+=4)r.push(t.readUInt32());return r}.call(this)},e.prototype.indexOf=function(t){return this.offsets[t]},e.prototype.lengthOf=function(t){return this.offsets[t+1]-this.offsets[t]},e.prototype.encode=function(t,e){for(var n=new Uint32Array(this.offsets.length),r=0,i=0,o=0;o<n.length;++o)if(n[o]=r,i<e.length&&e[i]==o){++i,n[o]=r;var s=this.offsets[o],a=this.offsets[o+1]-s;a>0&&(r+=a)}for(var c=new Array(4*n.length),u=0;u<n.length;++u)c[4*u+3]=255&n[u],c[4*u+2]=(65280&n[u])>>8,c[4*u+1]=(16711680&n[u])>>16,c[4*u]=(**********&n[u])>>24;return c},e}(),Ae=function(){function t(t){this.font=t,this.subset={},this.unicodes={},this.next=33}return t.prototype.generateCmap=function(){var t,e,n,r,i;for(e in r=this.font.cmap.tables[0].codeMap,t={},i=this.subset)n=i[e],t[e]=r[n];return t},t.prototype.glyphsFor=function(t){var e,n,r,i,o,s,a;for(r={},o=0,s=t.length;o<s;o++)r[i=t[o]]=this.font.glyf.glyphFor(i);for(i in e=[],r)(null!=(n=r[i])?n.compound:void 0)&&e.push.apply(e,n.glyphIDs);if(e.length>0)for(i in a=this.glyphsFor(e))n=a[i],r[i]=n;return r},t.prototype.encode=function(t,e){var n,r,i,o,s,a,c,u,l,h,f,d,p,m,g;for(r in n=fe.encode(this.generateCmap(),"unicode"),o=this.glyphsFor(t),f={0:0},g=n.charMap)f[(a=g[r]).old]=a.new;for(d in h=n.maxGlyphID,o)d in f||(f[d]=h++);return u=function(t){var e,n;for(e in n={},t)n[t[e]]=e;return n}(f),l=Object.keys(u).sort((function(t,e){return t-e})),p=function(){var t,e,n;for(n=[],t=0,e=l.length;t<e;t++)s=l[t],n.push(u[s]);return n}(),i=this.font.glyf.encode(o,p,f),c=this.font.loca.encode(i.offsets,p),m={cmap:this.font.cmap.raw(),glyf:i.table,loca:c,hmtx:this.font.hmtx.raw(),hhea:this.font.hhea.raw(),maxp:this.font.maxp.raw(),post:this.font.post.raw(),name:this.font.name.raw(),head:this.font.head.encode(e)},this.font.os2.exists&&(m["OS/2"]=this.font.os2.raw()),this.font.directory.encode(m)},t}();D.API.PDFObject=function(){var t;function e(){}return t=function(t,e){return(Array(e+1).join("0")+t).slice(-e)},e.convert=function(n){var r,i,o,s;if(Array.isArray(n))return"["+function(){var t,i,o;for(o=[],t=0,i=n.length;t<i;t++)r=n[t],o.push(e.convert(r));return o}().join(" ")+"]";if("string"==typeof n)return"/"+n;if(null!=n?n.isString:void 0)return"("+n+")";if(n instanceof Date)return"(D:"+t(n.getUTCFullYear(),4)+t(n.getUTCMonth(),2)+t(n.getUTCDate(),2)+t(n.getUTCHours(),2)+t(n.getUTCMinutes(),2)+t(n.getUTCSeconds(),2)+"Z)";if("[object Object]"==={}.toString.call(n)){for(i in o=["<<"],n)s=n[i],o.push("/"+i+" "+e.convert(s));return o.push(">>"),o.join("\n")}return""+n},e}(),e["default"]=D}.call(this,n("c8ba"))},d67e:function(t,e,n){(function(e,r){
/*!
 * html2pdf.js v0.10.2
 * Copyright (c) 2024 Erik Koopmans
 * Released under the MIT License.
 */
(function(e,r){t.exports=r(n("76d2"),n("c0e9"))})(self,(function(t,n){return function(){var i={"./src/plugin/hyperlinks.js":
/*!**********************************!*\
  !*** ./src/plugin/hyperlinks.js ***!
  \**********************************/function(t,e,n){"use strict";n.r(e);n(/*! core-js/modules/web.dom-collections.for-each.js */"./node_modules/core-js/modules/web.dom-collections.for-each.js"),n(/*! core-js/modules/es.string.link.js */"./node_modules/core-js/modules/es.string.link.js");var r=n(/*! ../worker.js */"./src/worker.js"),i=n(/*! ../utils.js */"./src/utils.js"),o=[],s={toContainer:r.default.prototype.toContainer,toPdf:r.default.prototype.toPdf};r.default.prototype.toContainer=function(){return s.toContainer.call(this).then((function(){if(this.opt.enableLinks){var t=this.prop.container,e=t.querySelectorAll("a"),n=(0,i.unitConvert)(t.getBoundingClientRect(),this.prop.pageSize.k);o=[],Array.prototype.forEach.call(e,(function(t){for(var e=t.getClientRects(),r=0;r<e.length;r++){var s=(0,i.unitConvert)(e[r],this.prop.pageSize.k);s.left-=n.left,s.top-=n.top;var a=Math.floor(s.top/this.prop.pageSize.inner.height)+1,c=this.opt.margin[0]+s.top%this.prop.pageSize.inner.height,u=this.opt.margin[1]+s.left;o.push({page:a,top:c,left:u,clientRect:s,link:t})}}),this)}}))},r.default.prototype.toPdf=function(){return s.toPdf.call(this).then((function(){if(this.opt.enableLinks){o.forEach((function(t){this.prop.pdf.setPage(t.page),this.prop.pdf.link(t.left,t.top,t.clientRect.width,t.clientRect.height,{url:t.link.href})}),this);var t=this.prop.pdf.internal.getNumberOfPages();this.prop.pdf.setPage(t)}}))}},"./src/plugin/jspdf-plugin.js":
/*!************************************!*\
  !*** ./src/plugin/jspdf-plugin.js ***!
  \************************************/function(t,e,n){"use strict";n.r(e);n(/*! core-js/modules/es.symbol.js */"./node_modules/core-js/modules/es.symbol.js"),n(/*! core-js/modules/es.symbol.description.js */"./node_modules/core-js/modules/es.symbol.description.js"),n(/*! core-js/modules/es.object.to-string.js */"./node_modules/core-js/modules/es.object.to-string.js"),n(/*! core-js/modules/es.symbol.iterator.js */"./node_modules/core-js/modules/es.symbol.iterator.js"),n(/*! core-js/modules/es.array.iterator.js */"./node_modules/core-js/modules/es.array.iterator.js"),n(/*! core-js/modules/es.string.iterator.js */"./node_modules/core-js/modules/es.string.iterator.js"),n(/*! core-js/modules/web.dom-collections.iterator.js */"./node_modules/core-js/modules/web.dom-collections.iterator.js");var r=n(/*! jspdf */"jspdf");function i(t){return i="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"===typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},i(t)}r.jsPDF.getPageSize=function(t,e,n){if("object"===i(t)){var r=t;t=r.orientation,e=r.unit||e,n=r.format||n}e=e||"mm",n=n||"a4",t=(""+(t||"P")).toLowerCase();var o=(""+n).toLowerCase(),s={a0:[2383.94,3370.39],a1:[1683.78,2383.94],a2:[1190.55,1683.78],a3:[841.89,1190.55],a4:[595.28,841.89],a5:[419.53,595.28],a6:[297.64,419.53],a7:[209.76,297.64],a8:[147.4,209.76],a9:[104.88,147.4],a10:[73.7,104.88],b0:[2834.65,4008.19],b1:[2004.09,2834.65],b2:[1417.32,2004.09],b3:[1000.63,1417.32],b4:[708.66,1000.63],b5:[498.9,708.66],b6:[354.33,498.9],b7:[249.45,354.33],b8:[175.75,249.45],b9:[124.72,175.75],b10:[87.87,124.72],c0:[2599.37,3676.54],c1:[1836.85,2599.37],c2:[1298.27,1836.85],c3:[918.43,1298.27],c4:[649.13,918.43],c5:[459.21,649.13],c6:[323.15,459.21],c7:[229.61,323.15],c8:[161.57,229.61],c9:[113.39,161.57],c10:[79.37,113.39],dl:[311.81,623.62],letter:[612,792],"government-letter":[576,756],legal:[612,1008],"junior-legal":[576,360],ledger:[1224,792],tabloid:[792,1224],"credit-card":[153,243]};switch(e){case"pt":var a=1;break;case"mm":a=72/25.4;break;case"cm":a=72/2.54;break;case"in":a=72;break;case"px":a=.75;break;case"pc":a=12;break;case"em":a=12;break;case"ex":a=6;break;default:throw"Invalid unit: "+e}if(s.hasOwnProperty(o))var c=s[o][1]/a,u=s[o][0]/a;else try{c=n[1],u=n[0]}catch(f){throw new Error("Invalid format: "+n)}if("p"===t||"portrait"===t){if(t="p",u>c){var l=u;u=c,c=l}}else{if("l"!==t&&"landscape"!==t)throw"Invalid orientation: "+t;if(t="l",c>u){l=u;u=c,c=l}}var h={width:u,height:c,unit:e,k:a};return h},e["default"]=r.jsPDF},"./src/plugin/pagebreaks.js":
/*!**********************************!*\
  !*** ./src/plugin/pagebreaks.js ***!
  \**********************************/function(t,e,n){"use strict";n.r(e);n(/*! core-js/modules/es.array.concat.js */"./node_modules/core-js/modules/es.array.concat.js"),n(/*! core-js/modules/es.array.slice.js */"./node_modules/core-js/modules/es.array.slice.js"),n(/*! core-js/modules/es.array.join.js */"./node_modules/core-js/modules/es.array.join.js"),n(/*! core-js/modules/web.dom-collections.for-each.js */"./node_modules/core-js/modules/web.dom-collections.for-each.js"),n(/*! core-js/modules/es.object.keys.js */"./node_modules/core-js/modules/es.object.keys.js");var r=n(/*! ../worker.js */"./src/worker.js"),i=n(/*! ../utils.js */"./src/utils.js"),o={toContainer:r.default.prototype.toContainer};r.default.template.opt.pagebreak={mode:["css","legacy"],before:[],after:[],avoid:[]},r.default.prototype.toContainer=function(){return o.toContainer.call(this).then((function(){var t=this.prop.container,e=this.prop.pageSize.inner.px.height,n=[].concat(this.opt.pagebreak.mode),r={avoidAll:-1!==n.indexOf("avoid-all"),css:-1!==n.indexOf("css"),legacy:-1!==n.indexOf("legacy")},o={},s=this;["before","after","avoid"].forEach((function(e){var n=r.avoidAll&&"avoid"===e;o[e]=n?[]:[].concat(s.opt.pagebreak[e]||[]),o[e].length>0&&(o[e]=Array.prototype.slice.call(t.querySelectorAll(o[e].join(", "))))}));var a=t.querySelectorAll(".html2pdf__page-break");a=Array.prototype.slice.call(a);var c=t.querySelectorAll("*");Array.prototype.forEach.call(c,(function(t){var n={before:!1,after:r.legacy&&-1!==a.indexOf(t),avoid:r.avoidAll};if(r.css){var s=window.getComputedStyle(t),c=["always","page","left","right"],u=["avoid","avoid-page"];n={before:n.before||-1!==c.indexOf(s.breakBefore||s.pageBreakBefore),after:n.after||-1!==c.indexOf(s.breakAfter||s.pageBreakAfter),avoid:n.avoid||-1!==u.indexOf(s.breakInside||s.pageBreakInside)}}Object.keys(n).forEach((function(e){n[e]=n[e]||-1!==o[e].indexOf(t)}));var l=t.getBoundingClientRect();if(n.avoid&&!n.before){var h=Math.floor(l.top/e),f=Math.floor(l.bottom/e),d=Math.abs(l.bottom-l.top)/e;f!==h&&d<=1&&(n.before=!0)}if(n.before){var p=(0,i.createElement)("div",{style:{display:"block",height:e-l.top%e+"px"}});t.parentNode.insertBefore(p,t)}if(n.after){p=(0,i.createElement)("div",{style:{display:"block",height:e-l.bottom%e+"px"}});t.parentNode.insertBefore(p,t.nextSibling)}}))}))}},"./src/utils.js":
/*!**********************!*\
  !*** ./src/utils.js ***!
  \**********************/function(t,e,n){"use strict";n.r(e),n.d(e,{objType:function(){return i},createElement:function(){return o},cloneNode:function(){return s},unitConvert:function(){return a},toPx:function(){return c}});n(/*! core-js/modules/es.number.constructor.js */"./node_modules/core-js/modules/es.number.constructor.js"),n(/*! core-js/modules/es.symbol.js */"./node_modules/core-js/modules/es.symbol.js"),n(/*! core-js/modules/es.symbol.description.js */"./node_modules/core-js/modules/es.symbol.description.js"),n(/*! core-js/modules/es.object.to-string.js */"./node_modules/core-js/modules/es.object.to-string.js"),n(/*! core-js/modules/es.symbol.iterator.js */"./node_modules/core-js/modules/es.symbol.iterator.js"),n(/*! core-js/modules/es.array.iterator.js */"./node_modules/core-js/modules/es.array.iterator.js"),n(/*! core-js/modules/es.string.iterator.js */"./node_modules/core-js/modules/es.string.iterator.js"),n(/*! core-js/modules/web.dom-collections.iterator.js */"./node_modules/core-js/modules/web.dom-collections.iterator.js");function r(t){return r="function"===typeof Symbol&&"symbol"===typeof Symbol.iterator?function(t){return typeof t}:function(t){return t&&"function"===typeof Symbol&&t.constructor===Symbol&&t!==Symbol.prototype?"symbol":typeof t},r(t)}var i=function(t){var e=r(t);return"undefined"===e?"undefined":"string"===e||t instanceof String?"string":"number"===e||t instanceof Number?"number":"function"===e||t instanceof Function?"function":t&&t.constructor===Array?"array":t&&1===t.nodeType?"element":"object"===e?"object":"unknown"},o=function(t,e){var n=document.createElement(t);if(e.className&&(n.className=e.className),e.innerHTML){n.innerHTML=e.innerHTML;for(var r=n.getElementsByTagName("script"),i=r.length;i-- >0;null)r[i].parentNode.removeChild(r[i])}for(var o in e.style)n.style[o]=e.style[o];return n},s=function t(e,n){for(var r=3===e.nodeType?document.createTextNode(e.nodeValue):e.cloneNode(!1),i=e.firstChild;i;i=i.nextSibling)!0!==n&&1===i.nodeType&&"SCRIPT"===i.nodeName||r.appendChild(t(i,n));return 1===e.nodeType&&("CANVAS"===e.nodeName?(r.width=e.width,r.height=e.height,r.getContext("2d").drawImage(e,0,0)):"TEXTAREA"!==e.nodeName&&"SELECT"!==e.nodeName||(r.value=e.value),r.addEventListener("load",(function(){r.scrollTop=e.scrollTop,r.scrollLeft=e.scrollLeft}),!0)),r},a=function(t,e){if("number"===i(t))return 72*t/96/e;var n={};for(var r in t)n[r]=72*t[r]/96/e;return n},c=function(t,e){return Math.floor(t*e/72*96)}},"./src/worker.js":
/*!***********************!*\
  !*** ./src/worker.js ***!
  \***********************/function(t,e,n){"use strict";n.r(e);n(/*! core-js/modules/es.object.assign.js */"./node_modules/core-js/modules/es.object.assign.js"),n(/*! core-js/modules/es.array.map.js */"./node_modules/core-js/modules/es.array.map.js"),n(/*! core-js/modules/es.object.keys.js */"./node_modules/core-js/modules/es.object.keys.js"),n(/*! core-js/modules/es.array.concat.js */"./node_modules/core-js/modules/es.array.concat.js"),n(/*! core-js/modules/es.object.to-string.js */"./node_modules/core-js/modules/es.object.to-string.js"),n(/*! core-js/modules/es.regexp.to-string.js */"./node_modules/core-js/modules/es.regexp.to-string.js"),n(/*! core-js/modules/es.function.name.js */"./node_modules/core-js/modules/es.function.name.js"),n(/*! core-js/modules/web.dom-collections.for-each.js */"./node_modules/core-js/modules/web.dom-collections.for-each.js");var r=n(/*! jspdf */"jspdf"),i=n(/*! html2canvas */"html2canvas"),o=n(/*! ./utils.js */"./src/utils.js"),s=n(/*! es6-promise */"./node_modules/es6-promise/dist/es6-promise.js"),a=n.n(s),c=a().Promise,u=function t(e){var n=Object.assign(t.convert(c.resolve()),JSON.parse(JSON.stringify(t.template))),r=t.convert(c.resolve(),n);return r=r.setProgress(1,t,1,[t]),r=r.set(e),r};u.prototype=Object.create(c.prototype),u.prototype.constructor=u,u.convert=function(t,e){return t.__proto__=e||u.prototype,t},u.template={prop:{src:null,container:null,overlay:null,canvas:null,img:null,pdf:null,pageSize:null},progress:{val:0,state:null,n:0,stack:[]},opt:{filename:"file.pdf",margin:[0,0,0,0],image:{type:"jpeg",quality:.95},enableLinks:!0,html2canvas:{},jsPDF:{}}},u.prototype.from=function(t,e){function n(t){switch((0,o.objType)(t)){case"string":return"string";case"element":return t.nodeName.toLowerCase&&"canvas"===t.nodeName.toLowerCase()?"canvas":"element";default:return"unknown"}}return this.then((function(){switch(e=e||n(t),e){case"string":return this.set({src:(0,o.createElement)("div",{innerHTML:t})});case"element":return this.set({src:t});case"canvas":return this.set({canvas:t});case"img":return this.set({img:t});default:return this.error("Unknown source type.")}}))},u.prototype.to=function(t){switch(t){case"container":return this.toContainer();case"canvas":return this.toCanvas();case"img":return this.toImg();case"pdf":return this.toPdf();default:return this.error("Invalid target.")}},u.prototype.toContainer=function(){var t=[function(){return this.prop.src||this.error("Cannot duplicate - no source HTML.")},function(){return this.prop.pageSize||this.setPageSize()}];return this.thenList(t).then((function(){var t={position:"fixed",overflow:"hidden",zIndex:1e3,left:0,right:0,bottom:0,top:0,backgroundColor:"rgba(0,0,0,0.8)"},e={position:"absolute",width:this.prop.pageSize.inner.width+this.prop.pageSize.unit,left:0,right:0,top:0,height:"auto",margin:"auto",backgroundColor:"white"};t.opacity=0;var n=(0,o.cloneNode)(this.prop.src,this.opt.html2canvas.javascriptEnabled);this.prop.overlay=(0,o.createElement)("div",{className:"html2pdf__overlay",style:t}),this.prop.container=(0,o.createElement)("div",{className:"html2pdf__container",style:e}),this.prop.container.appendChild(n),this.prop.overlay.appendChild(this.prop.container),document.body.appendChild(this.prop.overlay)}))},u.prototype.toCanvas=function(){var t=[function(){return document.body.contains(this.prop.container)||this.toContainer()}];return this.thenList(t).then((function(){var t=Object.assign({},this.opt.html2canvas);return delete t.onrendered,i(this.prop.container,t)})).then((function(t){var e=this.opt.html2canvas.onrendered||function(){};e(t),this.prop.canvas=t,document.body.removeChild(this.prop.overlay)}))},u.prototype.toImg=function(){var t=[function(){return this.prop.canvas||this.toCanvas()}];return this.thenList(t).then((function(){var t=this.prop.canvas.toDataURL("image/"+this.opt.image.type,this.opt.image.quality);this.prop.img=document.createElement("img"),this.prop.img.src=t}))},u.prototype.toPdf=function(){var t=[function(){return this.prop.canvas||this.toCanvas()},function(){return this.prop.pageSize||this.setPageSize()}];return this.thenList(t).then((function(){var t=this.prop.canvas,e=this.opt,n=t.height,i=Math.floor(t.width*this.prop.pageSize.inner.ratio),o=Math.ceil(n/i),s=this.prop.pageSize.inner.height,a=document.createElement("canvas"),c=a.getContext("2d");a.width=t.width,a.height=i,this.prop.pdf=this.prop.pdf||new r.jsPDF(e.jsPDF);for(var u=0;u<o;u++){u===o-1&&n%i!==0&&(a.height=n%i,s=a.height*this.prop.pageSize.inner.width/a.width);var l=a.width,h=a.height;c.fillStyle="white",c.fillRect(0,0,l,h),c.drawImage(t,0,u*i,l,h,0,0,l,h),u&&this.prop.pdf.addPage();var f=a.toDataURL("image/"+e.image.type,e.image.quality);this.prop.pdf.addImage(f,e.image.type,e.margin[1],e.margin[0],this.prop.pageSize.inner.width,s)}}))},u.prototype.output=function(t,e,n){return n=n||"pdf","img"===n.toLowerCase()||"image"===n.toLowerCase()?this.outputImg(t,e):this.outputPdf(t,e)},u.prototype.outputPdf=function(t,e){var n=[function(){return this.prop.pdf||this.toPdf()}];return this.thenList(n).then((function(){return this.prop.pdf.output(t,e)}))},u.prototype.outputImg=function(t,e){var n=[function(){return this.prop.img||this.toImg()}];return this.thenList(n).then((function(){switch(t){case void 0:case"img":return this.prop.img;case"datauristring":case"dataurlstring":return this.prop.img.src;case"datauri":case"dataurl":return document.location.href=this.prop.img.src;default:throw'Image output type "'+t+'" is not supported.'}}))},u.prototype.save=function(t){var e=[function(){return this.prop.pdf||this.toPdf()}];return this.thenList(e).set(t?{filename:t}:null).then((function(){this.prop.pdf.save(this.opt.filename)}))},u.prototype.set=function(t){if("object"!==(0,o.objType)(t))return this;var e=Object.keys(t||{}).map((function(e){switch(e){case"margin":return this.setMargin.bind(this,t.margin);case"jsPDF":return function(){return this.opt.jsPDF=t.jsPDF,this.setPageSize()};case"pageSize":return this.setPageSize.bind(this,t.pageSize);default:return e in u.template.prop?function(){this.prop[e]=t[e]}:function(){this.opt[e]=t[e]}}}),this);return this.then((function(){return this.thenList(e)}))},u.prototype.get=function(t,e){return this.then((function(){var n=t in u.template.prop?this.prop[t]:this.opt[t];return e?e(n):n}))},u.prototype.setMargin=function(t){return this.then((function(){switch((0,o.objType)(t)){case"number":t=[t,t,t,t];case"array":if(2===t.length&&(t=[t[0],t[1],t[0],t[1]]),4===t.length)break;default:return this.error("Invalid margin array.")}this.opt.margin=t})).then(this.setPageSize)},u.prototype.setPageSize=function(t){return this.then((function(){t=t||r.jsPDF.getPageSize(this.opt.jsPDF),t.hasOwnProperty("inner")||(t.inner={width:t.width-this.opt.margin[1]-this.opt.margin[3],height:t.height-this.opt.margin[0]-this.opt.margin[2]},t.inner.px={width:(0,o.toPx)(t.inner.width,t.k),height:(0,o.toPx)(t.inner.height,t.k)},t.inner.ratio=t.inner.height/t.inner.width),this.prop.pageSize=t}))},u.prototype.setProgress=function(t,e,n,r){return null!=t&&(this.progress.val=t),null!=e&&(this.progress.state=e),null!=n&&(this.progress.n=n),null!=r&&(this.progress.stack=r),this.progress.ratio=this.progress.val/this.progress.state,this},u.prototype.updateProgress=function(t,e,n,r){return this.setProgress(t?this.progress.val+t:null,e||null,n?this.progress.n+n:null,r?this.progress.stack.concat(r):null)},u.prototype.then=function(t,e){var n=this;return this.thenCore(t,e,(function(t,e){return n.updateProgress(null,null,1,[t]),c.prototype.then.call(this,(function(e){return n.updateProgress(null,t),e})).then(t,e).then((function(t){return n.updateProgress(1),t}))}))},u.prototype.thenCore=function(t,e,n){n=n||c.prototype.then;var r=this;t&&(t=t.bind(r)),e&&(e=e.bind(r));var i=-1!==c.toString().indexOf("[native code]")&&"Promise"===c.name,o=i?r:u.convert(Object.assign({},r),c.prototype),s=n.call(o,t,e);return u.convert(s,r.__proto__)},u.prototype.thenExternal=function(t,e){return c.prototype.then.call(this,t,e)},u.prototype.thenList=function(t){var e=this;return t.forEach((function(t){e=e.thenCore(t)})),e},u.prototype["catch"]=function(t){t&&(t=t.bind(this));var e=c.prototype["catch"].call(this,t);return u.convert(e,this)},u.prototype.catchExternal=function(t){return c.prototype["catch"].call(this,t)},u.prototype.error=function(t){return this.then((function(){throw new Error(t)}))},u.prototype.using=u.prototype.set,u.prototype.saveAs=u.prototype.save,u.prototype.export=u.prototype.output,u.prototype.run=u.prototype.then,e["default"]=u},"./node_modules/core-js/internals/a-function.js":
/*!******************************************************!*\
  !*** ./node_modules/core-js/internals/a-function.js ***!
  \******************************************************/function(t){t.exports=function(t){if("function"!=typeof t)throw TypeError(String(t)+" is not a function");return t}},"./node_modules/core-js/internals/a-possible-prototype.js":
/*!****************************************************************!*\
  !*** ./node_modules/core-js/internals/a-possible-prototype.js ***!
  \****************************************************************/function(t,e,n){var r=n(/*! ../internals/is-object */"./node_modules/core-js/internals/is-object.js");t.exports=function(t){if(!r(t)&&null!==t)throw TypeError("Can't set "+String(t)+" as a prototype");return t}},"./node_modules/core-js/internals/add-to-unscopables.js":
/*!**************************************************************!*\
  !*** ./node_modules/core-js/internals/add-to-unscopables.js ***!
  \**************************************************************/function(t,e,n){var r=n(/*! ../internals/well-known-symbol */"./node_modules/core-js/internals/well-known-symbol.js"),i=n(/*! ../internals/object-create */"./node_modules/core-js/internals/object-create.js"),o=n(/*! ../internals/object-define-property */"./node_modules/core-js/internals/object-define-property.js"),s=r("unscopables"),a=Array.prototype;void 0==a[s]&&o.f(a,s,{configurable:!0,value:i(null)}),t.exports=function(t){a[s][t]=!0}},"./node_modules/core-js/internals/an-object.js":
/*!*****************************************************!*\
  !*** ./node_modules/core-js/internals/an-object.js ***!
  \*****************************************************/function(t,e,n){var r=n(/*! ../internals/is-object */"./node_modules/core-js/internals/is-object.js");t.exports=function(t){if(!r(t))throw TypeError(String(t)+" is not an object");return t}},"./node_modules/core-js/internals/array-for-each.js":
/*!**********************************************************!*\
  !*** ./node_modules/core-js/internals/array-for-each.js ***!
  \**********************************************************/function(t,e,n){"use strict";var r=n(/*! ../internals/array-iteration */"./node_modules/core-js/internals/array-iteration.js").forEach,i=n(/*! ../internals/array-method-is-strict */"./node_modules/core-js/internals/array-method-is-strict.js"),o=i("forEach");t.exports=o?[].forEach:function(t){return r(this,t,arguments.length>1?arguments[1]:void 0)}},"./node_modules/core-js/internals/array-includes.js":
/*!**********************************************************!*\
  !*** ./node_modules/core-js/internals/array-includes.js ***!
  \**********************************************************/function(t,e,n){var r=n(/*! ../internals/to-indexed-object */"./node_modules/core-js/internals/to-indexed-object.js"),i=n(/*! ../internals/to-length */"./node_modules/core-js/internals/to-length.js"),o=n(/*! ../internals/to-absolute-index */"./node_modules/core-js/internals/to-absolute-index.js"),s=function(t){return function(e,n,s){var a,c=r(e),u=i(c.length),l=o(s,u);if(t&&n!=n){while(u>l)if(a=c[l++],a!=a)return!0}else for(;u>l;l++)if((t||l in c)&&c[l]===n)return t||l||0;return!t&&-1}};t.exports={includes:s(!0),indexOf:s(!1)}},"./node_modules/core-js/internals/array-iteration.js":
/*!***********************************************************!*\
  !*** ./node_modules/core-js/internals/array-iteration.js ***!
  \***********************************************************/function(t,e,n){var r=n(/*! ../internals/function-bind-context */"./node_modules/core-js/internals/function-bind-context.js"),i=n(/*! ../internals/indexed-object */"./node_modules/core-js/internals/indexed-object.js"),o=n(/*! ../internals/to-object */"./node_modules/core-js/internals/to-object.js"),s=n(/*! ../internals/to-length */"./node_modules/core-js/internals/to-length.js"),a=n(/*! ../internals/array-species-create */"./node_modules/core-js/internals/array-species-create.js"),c=[].push,u=function(t){var e=1==t,n=2==t,u=3==t,l=4==t,h=6==t,f=7==t,d=5==t||h;return function(p,m,g,v){for(var b,y,w=o(p),j=i(w),_=r(m,g,3),x=s(j.length),N=0,A=v||a,L=e?A(p,x):n||f?A(p,0):void 0;x>N;N++)if((d||N in j)&&(b=j[N],y=_(b,N,w),t))if(e)L[N]=y;else if(y)switch(t){case 3:return!0;case 5:return b;case 6:return N;case 2:c.call(L,b)}else switch(t){case 4:return!1;case 7:c.call(L,b)}return h?-1:u||l?l:L}};t.exports={forEach:u(0),map:u(1),filter:u(2),some:u(3),every:u(4),find:u(5),findIndex:u(6),filterReject:u(7)}},"./node_modules/core-js/internals/array-method-has-species-support.js":
/*!****************************************************************************!*\
  !*** ./node_modules/core-js/internals/array-method-has-species-support.js ***!
  \****************************************************************************/function(t,e,n){var r=n(/*! ../internals/fails */"./node_modules/core-js/internals/fails.js"),i=n(/*! ../internals/well-known-symbol */"./node_modules/core-js/internals/well-known-symbol.js"),o=n(/*! ../internals/engine-v8-version */"./node_modules/core-js/internals/engine-v8-version.js"),s=i("species");t.exports=function(t){return o>=51||!r((function(){var e=[],n=e.constructor={};return n[s]=function(){return{foo:1}},1!==e[t](Boolean).foo}))}},"./node_modules/core-js/internals/array-method-is-strict.js":
/*!******************************************************************!*\
  !*** ./node_modules/core-js/internals/array-method-is-strict.js ***!
  \******************************************************************/function(t,e,n){"use strict";var r=n(/*! ../internals/fails */"./node_modules/core-js/internals/fails.js");t.exports=function(t,e){var n=[][t];return!!n&&r((function(){n.call(null,e||function(){throw 1},1)}))}},"./node_modules/core-js/internals/array-species-constructor.js":
/*!*********************************************************************!*\
  !*** ./node_modules/core-js/internals/array-species-constructor.js ***!
  \*********************************************************************/function(t,e,n){var r=n(/*! ../internals/is-object */"./node_modules/core-js/internals/is-object.js"),i=n(/*! ../internals/is-array */"./node_modules/core-js/internals/is-array.js"),o=n(/*! ../internals/well-known-symbol */"./node_modules/core-js/internals/well-known-symbol.js"),s=o("species");t.exports=function(t){var e;return i(t)&&(e=t.constructor,"function"!=typeof e||e!==Array&&!i(e.prototype)?r(e)&&(e=e[s],null===e&&(e=void 0)):e=void 0),void 0===e?Array:e}},"./node_modules/core-js/internals/array-species-create.js":
/*!****************************************************************!*\
  !*** ./node_modules/core-js/internals/array-species-create.js ***!
  \****************************************************************/function(t,e,n){var r=n(/*! ../internals/array-species-constructor */"./node_modules/core-js/internals/array-species-constructor.js");t.exports=function(t,e){return new(r(t))(0===e?0:e)}},"./node_modules/core-js/internals/classof-raw.js":
/*!*******************************************************!*\
  !*** ./node_modules/core-js/internals/classof-raw.js ***!
  \*******************************************************/function(t){var e={}.toString;t.exports=function(t){return e.call(t).slice(8,-1)}},"./node_modules/core-js/internals/classof.js":
/*!***************************************************!*\
  !*** ./node_modules/core-js/internals/classof.js ***!
  \***************************************************/function(t,e,n){var r=n(/*! ../internals/to-string-tag-support */"./node_modules/core-js/internals/to-string-tag-support.js"),i=n(/*! ../internals/classof-raw */"./node_modules/core-js/internals/classof-raw.js"),o=n(/*! ../internals/well-known-symbol */"./node_modules/core-js/internals/well-known-symbol.js"),s=o("toStringTag"),a="Arguments"==i(function(){return arguments}()),c=function(t,e){try{return t[e]}catch(n){}};t.exports=r?i:function(t){var e,n,r;return void 0===t?"Undefined":null===t?"Null":"string"==typeof(n=c(e=Object(t),s))?n:a?i(e):"Object"==(r=i(e))&&"function"==typeof e.callee?"Arguments":r}},"./node_modules/core-js/internals/copy-constructor-properties.js":
/*!***********************************************************************!*\
  !*** ./node_modules/core-js/internals/copy-constructor-properties.js ***!
  \***********************************************************************/function(t,e,n){var r=n(/*! ../internals/has */"./node_modules/core-js/internals/has.js"),i=n(/*! ../internals/own-keys */"./node_modules/core-js/internals/own-keys.js"),o=n(/*! ../internals/object-get-own-property-descriptor */"./node_modules/core-js/internals/object-get-own-property-descriptor.js"),s=n(/*! ../internals/object-define-property */"./node_modules/core-js/internals/object-define-property.js");t.exports=function(t,e){for(var n=i(e),a=s.f,c=o.f,u=0;u<n.length;u++){var l=n[u];r(t,l)||a(t,l,c(e,l))}}},"./node_modules/core-js/internals/correct-prototype-getter.js":
/*!********************************************************************!*\
  !*** ./node_modules/core-js/internals/correct-prototype-getter.js ***!
  \********************************************************************/function(t,e,n){var r=n(/*! ../internals/fails */"./node_modules/core-js/internals/fails.js");t.exports=!r((function(){function t(){}return t.prototype.constructor=null,Object.getPrototypeOf(new t)!==t.prototype}))},"./node_modules/core-js/internals/create-html.js":
/*!*******************************************************!*\
  !*** ./node_modules/core-js/internals/create-html.js ***!
  \*******************************************************/function(t,e,n){var r=n(/*! ../internals/require-object-coercible */"./node_modules/core-js/internals/require-object-coercible.js"),i=n(/*! ../internals/to-string */"./node_modules/core-js/internals/to-string.js"),o=/"/g;t.exports=function(t,e,n,s){var a=i(r(t)),c="<"+e;return""!==n&&(c+=" "+n+'="'+i(s).replace(o,"&quot;")+'"'),c+">"+a+"</"+e+">"}},"./node_modules/core-js/internals/create-iterator-constructor.js":
/*!***********************************************************************!*\
  !*** ./node_modules/core-js/internals/create-iterator-constructor.js ***!
  \***********************************************************************/function(t,e,n){"use strict";var r=n(/*! ../internals/iterators-core */"./node_modules/core-js/internals/iterators-core.js").IteratorPrototype,i=n(/*! ../internals/object-create */"./node_modules/core-js/internals/object-create.js"),o=n(/*! ../internals/create-property-descriptor */"./node_modules/core-js/internals/create-property-descriptor.js"),s=n(/*! ../internals/set-to-string-tag */"./node_modules/core-js/internals/set-to-string-tag.js"),a=n(/*! ../internals/iterators */"./node_modules/core-js/internals/iterators.js"),c=function(){return this};t.exports=function(t,e,n){var u=e+" Iterator";return t.prototype=i(r,{next:o(1,n)}),s(t,u,!1,!0),a[u]=c,t}},"./node_modules/core-js/internals/create-non-enumerable-property.js":
/*!**************************************************************************!*\
  !*** ./node_modules/core-js/internals/create-non-enumerable-property.js ***!
  \**************************************************************************/function(t,e,n){var r=n(/*! ../internals/descriptors */"./node_modules/core-js/internals/descriptors.js"),i=n(/*! ../internals/object-define-property */"./node_modules/core-js/internals/object-define-property.js"),o=n(/*! ../internals/create-property-descriptor */"./node_modules/core-js/internals/create-property-descriptor.js");t.exports=r?function(t,e,n){return i.f(t,e,o(1,n))}:function(t,e,n){return t[e]=n,t}},"./node_modules/core-js/internals/create-property-descriptor.js":
/*!**********************************************************************!*\
  !*** ./node_modules/core-js/internals/create-property-descriptor.js ***!
  \**********************************************************************/function(t){t.exports=function(t,e){return{enumerable:!(1&t),configurable:!(2&t),writable:!(4&t),value:e}}},"./node_modules/core-js/internals/create-property.js":
/*!***********************************************************!*\
  !*** ./node_modules/core-js/internals/create-property.js ***!
  \***********************************************************/function(t,e,n){"use strict";var r=n(/*! ../internals/to-property-key */"./node_modules/core-js/internals/to-property-key.js"),i=n(/*! ../internals/object-define-property */"./node_modules/core-js/internals/object-define-property.js"),o=n(/*! ../internals/create-property-descriptor */"./node_modules/core-js/internals/create-property-descriptor.js");t.exports=function(t,e,n){var s=r(e);s in t?i.f(t,s,o(0,n)):t[s]=n}},"./node_modules/core-js/internals/define-iterator.js":
/*!***********************************************************!*\
  !*** ./node_modules/core-js/internals/define-iterator.js ***!
  \***********************************************************/function(t,e,n){"use strict";var r=n(/*! ../internals/export */"./node_modules/core-js/internals/export.js"),i=n(/*! ../internals/create-iterator-constructor */"./node_modules/core-js/internals/create-iterator-constructor.js"),o=n(/*! ../internals/object-get-prototype-of */"./node_modules/core-js/internals/object-get-prototype-of.js"),s=n(/*! ../internals/object-set-prototype-of */"./node_modules/core-js/internals/object-set-prototype-of.js"),a=n(/*! ../internals/set-to-string-tag */"./node_modules/core-js/internals/set-to-string-tag.js"),c=n(/*! ../internals/create-non-enumerable-property */"./node_modules/core-js/internals/create-non-enumerable-property.js"),u=n(/*! ../internals/redefine */"./node_modules/core-js/internals/redefine.js"),l=n(/*! ../internals/well-known-symbol */"./node_modules/core-js/internals/well-known-symbol.js"),h=n(/*! ../internals/is-pure */"./node_modules/core-js/internals/is-pure.js"),f=n(/*! ../internals/iterators */"./node_modules/core-js/internals/iterators.js"),d=n(/*! ../internals/iterators-core */"./node_modules/core-js/internals/iterators-core.js"),p=d.IteratorPrototype,m=d.BUGGY_SAFARI_ITERATORS,g=l("iterator"),v="keys",b="values",y="entries",w=function(){return this};t.exports=function(t,e,n,l,d,j,_){i(n,e,l);var x,N,A,L=function(t){if(t===d&&C)return C;if(!m&&t in k)return k[t];switch(t){case v:return function(){return new n(this,t)};case b:return function(){return new n(this,t)};case y:return function(){return new n(this,t)}}return function(){return new n(this)}},S=e+" Iterator",P=!1,k=t.prototype,I=k[g]||k["@@iterator"]||d&&k[d],C=!m&&I||L(d),F="Array"==e&&k.entries||I;if(F&&(x=o(F.call(new t)),p!==Object.prototype&&x.next&&(h||o(x)===p||(s?s(x,p):"function"!=typeof x[g]&&c(x,g,w)),a(x,S,!0,!0),h&&(f[S]=w))),d==b&&I&&I.name!==b&&(P=!0,C=function(){return I.call(this)}),h&&!_||k[g]===C||c(k,g,C),f[e]=C,d)if(N={values:L(b),keys:j?C:L(v),entries:L(y)},_)for(A in N)(m||P||!(A in k))&&u(k,A,N[A]);else r({target:e,proto:!0,forced:m||P},N);return N}},"./node_modules/core-js/internals/define-well-known-symbol.js":
/*!********************************************************************!*\
  !*** ./node_modules/core-js/internals/define-well-known-symbol.js ***!
  \********************************************************************/function(t,e,n){var r=n(/*! ../internals/path */"./node_modules/core-js/internals/path.js"),i=n(/*! ../internals/has */"./node_modules/core-js/internals/has.js"),o=n(/*! ../internals/well-known-symbol-wrapped */"./node_modules/core-js/internals/well-known-symbol-wrapped.js"),s=n(/*! ../internals/object-define-property */"./node_modules/core-js/internals/object-define-property.js").f;t.exports=function(t){var e=r.Symbol||(r.Symbol={});i(e,t)||s(e,t,{value:o.f(t)})}},"./node_modules/core-js/internals/descriptors.js":
/*!*******************************************************!*\
  !*** ./node_modules/core-js/internals/descriptors.js ***!
  \*******************************************************/function(t,e,n){var r=n(/*! ../internals/fails */"./node_modules/core-js/internals/fails.js");t.exports=!r((function(){return 7!=Object.defineProperty({},1,{get:function(){return 7}})[1]}))},"./node_modules/core-js/internals/document-create-element.js":
/*!*******************************************************************!*\
  !*** ./node_modules/core-js/internals/document-create-element.js ***!
  \*******************************************************************/function(t,e,n){var r=n(/*! ../internals/global */"./node_modules/core-js/internals/global.js"),i=n(/*! ../internals/is-object */"./node_modules/core-js/internals/is-object.js"),o=r.document,s=i(o)&&i(o.createElement);t.exports=function(t){return s?o.createElement(t):{}}},"./node_modules/core-js/internals/dom-iterables.js":
/*!*********************************************************!*\
  !*** ./node_modules/core-js/internals/dom-iterables.js ***!
  \*********************************************************/function(t){t.exports={CSSRuleList:0,CSSStyleDeclaration:0,CSSValueList:0,ClientRectList:0,DOMRectList:0,DOMStringList:0,DOMTokenList:1,DataTransferItemList:0,FileList:0,HTMLAllCollection:0,HTMLCollection:0,HTMLFormElement:0,HTMLSelectElement:0,MediaList:0,MimeTypeArray:0,NamedNodeMap:0,NodeList:1,PaintRequestList:0,Plugin:0,PluginArray:0,SVGLengthList:0,SVGNumberList:0,SVGPathSegList:0,SVGPointList:0,SVGStringList:0,SVGTransformList:0,SourceBufferList:0,StyleSheetList:0,TextTrackCueList:0,TextTrackList:0,TouchList:0}},"./node_modules/core-js/internals/engine-user-agent.js":
/*!*************************************************************!*\
  !*** ./node_modules/core-js/internals/engine-user-agent.js ***!
  \*************************************************************/function(t,e,n){var r=n(/*! ../internals/get-built-in */"./node_modules/core-js/internals/get-built-in.js");t.exports=r("navigator","userAgent")||""},"./node_modules/core-js/internals/engine-v8-version.js":
/*!*************************************************************!*\
  !*** ./node_modules/core-js/internals/engine-v8-version.js ***!
  \*************************************************************/function(t,e,n){var r,i,o=n(/*! ../internals/global */"./node_modules/core-js/internals/global.js"),s=n(/*! ../internals/engine-user-agent */"./node_modules/core-js/internals/engine-user-agent.js"),a=o.process,c=o.Deno,u=a&&a.versions||c&&c.version,l=u&&u.v8;l?(r=l.split("."),i=r[0]<4?1:r[0]+r[1]):s&&(r=s.match(/Edge\/(\d+)/),(!r||r[1]>=74)&&(r=s.match(/Chrome\/(\d+)/),r&&(i=r[1]))),t.exports=i&&+i},"./node_modules/core-js/internals/enum-bug-keys.js":
/*!*********************************************************!*\
  !*** ./node_modules/core-js/internals/enum-bug-keys.js ***!
  \*********************************************************/function(t){t.exports=["constructor","hasOwnProperty","isPrototypeOf","propertyIsEnumerable","toLocaleString","toString","valueOf"]},"./node_modules/core-js/internals/export.js":
/*!**************************************************!*\
  !*** ./node_modules/core-js/internals/export.js ***!
  \**************************************************/function(t,e,n){var r=n(/*! ../internals/global */"./node_modules/core-js/internals/global.js"),i=n(/*! ../internals/object-get-own-property-descriptor */"./node_modules/core-js/internals/object-get-own-property-descriptor.js").f,o=n(/*! ../internals/create-non-enumerable-property */"./node_modules/core-js/internals/create-non-enumerable-property.js"),s=n(/*! ../internals/redefine */"./node_modules/core-js/internals/redefine.js"),a=n(/*! ../internals/set-global */"./node_modules/core-js/internals/set-global.js"),c=n(/*! ../internals/copy-constructor-properties */"./node_modules/core-js/internals/copy-constructor-properties.js"),u=n(/*! ../internals/is-forced */"./node_modules/core-js/internals/is-forced.js");t.exports=function(t,e){var n,l,h,f,d,p,m=t.target,g=t.global,v=t.stat;if(l=g?r:v?r[m]||a(m,{}):(r[m]||{}).prototype,l)for(h in e){if(d=e[h],t.noTargetGet?(p=i(l,h),f=p&&p.value):f=l[h],n=u(g?h:m+(v?".":"#")+h,t.forced),!n&&void 0!==f){if(typeof d===typeof f)continue;c(d,f)}(t.sham||f&&f.sham)&&o(d,"sham",!0),s(l,h,d,t)}}},"./node_modules/core-js/internals/fails.js":
/*!*************************************************!*\
  !*** ./node_modules/core-js/internals/fails.js ***!
  \*************************************************/function(t){t.exports=function(t){try{return!!t()}catch(e){return!0}}},"./node_modules/core-js/internals/function-bind-context.js":
/*!*****************************************************************!*\
  !*** ./node_modules/core-js/internals/function-bind-context.js ***!
  \*****************************************************************/function(t,e,n){var r=n(/*! ../internals/a-function */"./node_modules/core-js/internals/a-function.js");t.exports=function(t,e,n){if(r(t),void 0===e)return t;switch(n){case 0:return function(){return t.call(e)};case 1:return function(n){return t.call(e,n)};case 2:return function(n,r){return t.call(e,n,r)};case 3:return function(n,r,i){return t.call(e,n,r,i)}}return function(){return t.apply(e,arguments)}}},"./node_modules/core-js/internals/get-built-in.js":
/*!********************************************************!*\
  !*** ./node_modules/core-js/internals/get-built-in.js ***!
  \********************************************************/function(t,e,n){var r=n(/*! ../internals/global */"./node_modules/core-js/internals/global.js"),i=function(t){return"function"==typeof t?t:void 0};t.exports=function(t,e){return arguments.length<2?i(r[t]):r[t]&&r[t][e]}},"./node_modules/core-js/internals/global.js":
/*!**************************************************!*\
  !*** ./node_modules/core-js/internals/global.js ***!
  \**************************************************/function(t){var n=function(t){return t&&t.Math==Math&&t};t.exports=n("object"==typeof globalThis&&globalThis)||n("object"==typeof window&&window)||n("object"==typeof self&&self)||n("object"==typeof e&&e)||function(){return this}()||Function("return this")()},"./node_modules/core-js/internals/has.js":
/*!***********************************************!*\
  !*** ./node_modules/core-js/internals/has.js ***!
  \***********************************************/function(t,e,n){var r=n(/*! ../internals/to-object */"./node_modules/core-js/internals/to-object.js"),i={}.hasOwnProperty;t.exports=Object.hasOwn||function(t,e){return i.call(r(t),e)}},"./node_modules/core-js/internals/hidden-keys.js":
/*!*******************************************************!*\
  !*** ./node_modules/core-js/internals/hidden-keys.js ***!
  \*******************************************************/function(t){t.exports={}},"./node_modules/core-js/internals/html.js":
/*!************************************************!*\
  !*** ./node_modules/core-js/internals/html.js ***!
  \************************************************/function(t,e,n){var r=n(/*! ../internals/get-built-in */"./node_modules/core-js/internals/get-built-in.js");t.exports=r("document","documentElement")},"./node_modules/core-js/internals/ie8-dom-define.js":
/*!**********************************************************!*\
  !*** ./node_modules/core-js/internals/ie8-dom-define.js ***!
  \**********************************************************/function(t,e,n){var r=n(/*! ../internals/descriptors */"./node_modules/core-js/internals/descriptors.js"),i=n(/*! ../internals/fails */"./node_modules/core-js/internals/fails.js"),o=n(/*! ../internals/document-create-element */"./node_modules/core-js/internals/document-create-element.js");t.exports=!r&&!i((function(){return 7!=Object.defineProperty(o("div"),"a",{get:function(){return 7}}).a}))},"./node_modules/core-js/internals/indexed-object.js":
/*!**********************************************************!*\
  !*** ./node_modules/core-js/internals/indexed-object.js ***!
  \**********************************************************/function(t,e,n){var r=n(/*! ../internals/fails */"./node_modules/core-js/internals/fails.js"),i=n(/*! ../internals/classof-raw */"./node_modules/core-js/internals/classof-raw.js"),o="".split;t.exports=r((function(){return!Object("z").propertyIsEnumerable(0)}))?function(t){return"String"==i(t)?o.call(t,""):Object(t)}:Object},"./node_modules/core-js/internals/inherit-if-required.js":
/*!***************************************************************!*\
  !*** ./node_modules/core-js/internals/inherit-if-required.js ***!
  \***************************************************************/function(t,e,n){var r=n(/*! ../internals/is-object */"./node_modules/core-js/internals/is-object.js"),i=n(/*! ../internals/object-set-prototype-of */"./node_modules/core-js/internals/object-set-prototype-of.js");t.exports=function(t,e,n){var o,s;return i&&"function"==typeof(o=e.constructor)&&o!==n&&r(s=o.prototype)&&s!==n.prototype&&i(t,s),t}},"./node_modules/core-js/internals/inspect-source.js":
/*!**********************************************************!*\
  !*** ./node_modules/core-js/internals/inspect-source.js ***!
  \**********************************************************/function(t,e,n){var r=n(/*! ../internals/shared-store */"./node_modules/core-js/internals/shared-store.js"),i=Function.toString;"function"!=typeof r.inspectSource&&(r.inspectSource=function(t){return i.call(t)}),t.exports=r.inspectSource},"./node_modules/core-js/internals/internal-state.js":
/*!**********************************************************!*\
  !*** ./node_modules/core-js/internals/internal-state.js ***!
  \**********************************************************/function(t,e,n){var r,i,o,s=n(/*! ../internals/native-weak-map */"./node_modules/core-js/internals/native-weak-map.js"),a=n(/*! ../internals/global */"./node_modules/core-js/internals/global.js"),c=n(/*! ../internals/is-object */"./node_modules/core-js/internals/is-object.js"),u=n(/*! ../internals/create-non-enumerable-property */"./node_modules/core-js/internals/create-non-enumerable-property.js"),l=n(/*! ../internals/has */"./node_modules/core-js/internals/has.js"),h=n(/*! ../internals/shared-store */"./node_modules/core-js/internals/shared-store.js"),f=n(/*! ../internals/shared-key */"./node_modules/core-js/internals/shared-key.js"),d=n(/*! ../internals/hidden-keys */"./node_modules/core-js/internals/hidden-keys.js"),p="Object already initialized",m=a.WeakMap,g=function(t){return o(t)?i(t):r(t,{})},v=function(t){return function(e){var n;if(!c(e)||(n=i(e)).type!==t)throw TypeError("Incompatible receiver, "+t+" required");return n}};if(s||h.state){var b=h.state||(h.state=new m),y=b.get,w=b.has,j=b.set;r=function(t,e){if(w.call(b,t))throw new TypeError(p);return e.facade=t,j.call(b,t,e),e},i=function(t){return y.call(b,t)||{}},o=function(t){return w.call(b,t)}}else{var _=f("state");d[_]=!0,r=function(t,e){if(l(t,_))throw new TypeError(p);return e.facade=t,u(t,_,e),e},i=function(t){return l(t,_)?t[_]:{}},o=function(t){return l(t,_)}}t.exports={set:r,get:i,has:o,enforce:g,getterFor:v}},"./node_modules/core-js/internals/is-array.js":
/*!****************************************************!*\
  !*** ./node_modules/core-js/internals/is-array.js ***!
  \****************************************************/function(t,e,n){var r=n(/*! ../internals/classof-raw */"./node_modules/core-js/internals/classof-raw.js");t.exports=Array.isArray||function(t){return"Array"==r(t)}},"./node_modules/core-js/internals/is-forced.js":
/*!*****************************************************!*\
  !*** ./node_modules/core-js/internals/is-forced.js ***!
  \*****************************************************/function(t,e,n){var r=n(/*! ../internals/fails */"./node_modules/core-js/internals/fails.js"),i=/#|\.prototype\./,o=function(t,e){var n=a[s(t)];return n==u||n!=c&&("function"==typeof e?r(e):!!e)},s=o.normalize=function(t){return String(t).replace(i,".").toLowerCase()},a=o.data={},c=o.NATIVE="N",u=o.POLYFILL="P";t.exports=o},"./node_modules/core-js/internals/is-object.js":
/*!*****************************************************!*\
  !*** ./node_modules/core-js/internals/is-object.js ***!
  \*****************************************************/function(t){t.exports=function(t){return"object"===typeof t?null!==t:"function"===typeof t}},"./node_modules/core-js/internals/is-pure.js":
/*!***************************************************!*\
  !*** ./node_modules/core-js/internals/is-pure.js ***!
  \***************************************************/function(t){t.exports=!1},"./node_modules/core-js/internals/is-symbol.js":
/*!*****************************************************!*\
  !*** ./node_modules/core-js/internals/is-symbol.js ***!
  \*****************************************************/function(t,e,n){var r=n(/*! ../internals/get-built-in */"./node_modules/core-js/internals/get-built-in.js"),i=n(/*! ../internals/use-symbol-as-uid */"./node_modules/core-js/internals/use-symbol-as-uid.js");t.exports=i?function(t){return"symbol"==typeof t}:function(t){var e=r("Symbol");return"function"==typeof e&&Object(t)instanceof e}},"./node_modules/core-js/internals/iterators-core.js":
/*!**********************************************************!*\
  !*** ./node_modules/core-js/internals/iterators-core.js ***!
  \**********************************************************/function(t,e,n){"use strict";var r,i,o,s=n(/*! ../internals/fails */"./node_modules/core-js/internals/fails.js"),a=n(/*! ../internals/object-get-prototype-of */"./node_modules/core-js/internals/object-get-prototype-of.js"),c=n(/*! ../internals/create-non-enumerable-property */"./node_modules/core-js/internals/create-non-enumerable-property.js"),u=n(/*! ../internals/has */"./node_modules/core-js/internals/has.js"),l=n(/*! ../internals/well-known-symbol */"./node_modules/core-js/internals/well-known-symbol.js"),h=n(/*! ../internals/is-pure */"./node_modules/core-js/internals/is-pure.js"),f=l("iterator"),d=!1,p=function(){return this};[].keys&&(o=[].keys(),"next"in o?(i=a(a(o)),i!==Object.prototype&&(r=i)):d=!0);var m=void 0==r||s((function(){var t={};return r[f].call(t)!==t}));m&&(r={}),h&&!m||u(r,f)||c(r,f,p),t.exports={IteratorPrototype:r,BUGGY_SAFARI_ITERATORS:d}},"./node_modules/core-js/internals/iterators.js":
/*!*****************************************************!*\
  !*** ./node_modules/core-js/internals/iterators.js ***!
  \*****************************************************/function(t){t.exports={}},"./node_modules/core-js/internals/native-symbol.js":
/*!*********************************************************!*\
  !*** ./node_modules/core-js/internals/native-symbol.js ***!
  \*********************************************************/function(t,e,n){var r=n(/*! ../internals/engine-v8-version */"./node_modules/core-js/internals/engine-v8-version.js"),i=n(/*! ../internals/fails */"./node_modules/core-js/internals/fails.js");t.exports=!!Object.getOwnPropertySymbols&&!i((function(){var t=Symbol();return!String(t)||!(Object(t)instanceof Symbol)||!Symbol.sham&&r&&r<41}))},"./node_modules/core-js/internals/native-weak-map.js":
/*!***********************************************************!*\
  !*** ./node_modules/core-js/internals/native-weak-map.js ***!
  \***********************************************************/function(t,e,n){var r=n(/*! ../internals/global */"./node_modules/core-js/internals/global.js"),i=n(/*! ../internals/inspect-source */"./node_modules/core-js/internals/inspect-source.js"),o=r.WeakMap;t.exports="function"===typeof o&&/native code/.test(i(o))},"./node_modules/core-js/internals/object-assign.js":
/*!*********************************************************!*\
  !*** ./node_modules/core-js/internals/object-assign.js ***!
  \*********************************************************/function(t,e,n){"use strict";var r=n(/*! ../internals/descriptors */"./node_modules/core-js/internals/descriptors.js"),i=n(/*! ../internals/fails */"./node_modules/core-js/internals/fails.js"),o=n(/*! ../internals/object-keys */"./node_modules/core-js/internals/object-keys.js"),s=n(/*! ../internals/object-get-own-property-symbols */"./node_modules/core-js/internals/object-get-own-property-symbols.js"),a=n(/*! ../internals/object-property-is-enumerable */"./node_modules/core-js/internals/object-property-is-enumerable.js"),c=n(/*! ../internals/to-object */"./node_modules/core-js/internals/to-object.js"),u=n(/*! ../internals/indexed-object */"./node_modules/core-js/internals/indexed-object.js"),l=Object.assign,h=Object.defineProperty;t.exports=!l||i((function(){if(r&&1!==l({b:1},l(h({},"a",{enumerable:!0,get:function(){h(this,"b",{value:3,enumerable:!1})}}),{b:2})).b)return!0;var t={},e={},n=Symbol(),i="abcdefghijklmnopqrst";return t[n]=7,i.split("").forEach((function(t){e[t]=t})),7!=l({},t)[n]||o(l({},e)).join("")!=i}))?function(t,e){var n=c(t),i=arguments.length,l=1,h=s.f,f=a.f;while(i>l){var d,p=u(arguments[l++]),m=h?o(p).concat(h(p)):o(p),g=m.length,v=0;while(g>v)d=m[v++],r&&!f.call(p,d)||(n[d]=p[d])}return n}:l},"./node_modules/core-js/internals/object-create.js":
/*!*********************************************************!*\
  !*** ./node_modules/core-js/internals/object-create.js ***!
  \*********************************************************/function(t,e,n){var r,i=n(/*! ../internals/an-object */"./node_modules/core-js/internals/an-object.js"),o=n(/*! ../internals/object-define-properties */"./node_modules/core-js/internals/object-define-properties.js"),s=n(/*! ../internals/enum-bug-keys */"./node_modules/core-js/internals/enum-bug-keys.js"),a=n(/*! ../internals/hidden-keys */"./node_modules/core-js/internals/hidden-keys.js"),c=n(/*! ../internals/html */"./node_modules/core-js/internals/html.js"),u=n(/*! ../internals/document-create-element */"./node_modules/core-js/internals/document-create-element.js"),l=n(/*! ../internals/shared-key */"./node_modules/core-js/internals/shared-key.js"),h=">",f="<",d="prototype",p="script",m=l("IE_PROTO"),g=function(){},v=function(t){return f+p+h+t+f+"/"+p+h},b=function(t){t.write(v("")),t.close();var e=t.parentWindow.Object;return t=null,e},y=function(){var t,e=u("iframe"),n="java"+p+":";if(e.style)return e.style.display="none",c.appendChild(e),e.src=String(n),t=e.contentWindow.document,t.open(),t.write(v("document.F=Object")),t.close(),t.F},w=function(){try{r=new ActiveXObject("htmlfile")}catch(e){}w=document.domain&&r?b(r):y()||b(r);var t=s.length;while(t--)delete w[d][s[t]];return w()};a[m]=!0,t.exports=Object.create||function(t,e){var n;return null!==t?(g[d]=i(t),n=new g,g[d]=null,n[m]=t):n=w(),void 0===e?n:o(n,e)}},"./node_modules/core-js/internals/object-define-properties.js":
/*!********************************************************************!*\
  !*** ./node_modules/core-js/internals/object-define-properties.js ***!
  \********************************************************************/function(t,e,n){var r=n(/*! ../internals/descriptors */"./node_modules/core-js/internals/descriptors.js"),i=n(/*! ../internals/object-define-property */"./node_modules/core-js/internals/object-define-property.js"),o=n(/*! ../internals/an-object */"./node_modules/core-js/internals/an-object.js"),s=n(/*! ../internals/object-keys */"./node_modules/core-js/internals/object-keys.js");t.exports=r?Object.defineProperties:function(t,e){o(t);var n,r=s(e),a=r.length,c=0;while(a>c)i.f(t,n=r[c++],e[n]);return t}},"./node_modules/core-js/internals/object-define-property.js":
/*!******************************************************************!*\
  !*** ./node_modules/core-js/internals/object-define-property.js ***!
  \******************************************************************/function(t,e,n){var r=n(/*! ../internals/descriptors */"./node_modules/core-js/internals/descriptors.js"),i=n(/*! ../internals/ie8-dom-define */"./node_modules/core-js/internals/ie8-dom-define.js"),o=n(/*! ../internals/an-object */"./node_modules/core-js/internals/an-object.js"),s=n(/*! ../internals/to-property-key */"./node_modules/core-js/internals/to-property-key.js"),a=Object.defineProperty;e.f=r?a:function(t,e,n){if(o(t),e=s(e),o(n),i)try{return a(t,e,n)}catch(r){}if("get"in n||"set"in n)throw TypeError("Accessors not supported");return"value"in n&&(t[e]=n.value),t}},"./node_modules/core-js/internals/object-get-own-property-descriptor.js":
/*!******************************************************************************!*\
  !*** ./node_modules/core-js/internals/object-get-own-property-descriptor.js ***!
  \******************************************************************************/function(t,e,n){var r=n(/*! ../internals/descriptors */"./node_modules/core-js/internals/descriptors.js"),i=n(/*! ../internals/object-property-is-enumerable */"./node_modules/core-js/internals/object-property-is-enumerable.js"),o=n(/*! ../internals/create-property-descriptor */"./node_modules/core-js/internals/create-property-descriptor.js"),s=n(/*! ../internals/to-indexed-object */"./node_modules/core-js/internals/to-indexed-object.js"),a=n(/*! ../internals/to-property-key */"./node_modules/core-js/internals/to-property-key.js"),c=n(/*! ../internals/has */"./node_modules/core-js/internals/has.js"),u=n(/*! ../internals/ie8-dom-define */"./node_modules/core-js/internals/ie8-dom-define.js"),l=Object.getOwnPropertyDescriptor;e.f=r?l:function(t,e){if(t=s(t),e=a(e),u)try{return l(t,e)}catch(n){}if(c(t,e))return o(!i.f.call(t,e),t[e])}},"./node_modules/core-js/internals/object-get-own-property-names-external.js":
/*!**********************************************************************************!*\
  !*** ./node_modules/core-js/internals/object-get-own-property-names-external.js ***!
  \**********************************************************************************/function(t,e,n){var r=n(/*! ../internals/to-indexed-object */"./node_modules/core-js/internals/to-indexed-object.js"),i=n(/*! ../internals/object-get-own-property-names */"./node_modules/core-js/internals/object-get-own-property-names.js").f,o={}.toString,s="object"==typeof window&&window&&Object.getOwnPropertyNames?Object.getOwnPropertyNames(window):[],a=function(t){try{return i(t)}catch(e){return s.slice()}};t.exports.f=function(t){return s&&"[object Window]"==o.call(t)?a(t):i(r(t))}},"./node_modules/core-js/internals/object-get-own-property-names.js":
/*!*************************************************************************!*\
  !*** ./node_modules/core-js/internals/object-get-own-property-names.js ***!
  \*************************************************************************/function(t,e,n){var r=n(/*! ../internals/object-keys-internal */"./node_modules/core-js/internals/object-keys-internal.js"),i=n(/*! ../internals/enum-bug-keys */"./node_modules/core-js/internals/enum-bug-keys.js"),o=i.concat("length","prototype");e.f=Object.getOwnPropertyNames||function(t){return r(t,o)}},"./node_modules/core-js/internals/object-get-own-property-symbols.js":
/*!***************************************************************************!*\
  !*** ./node_modules/core-js/internals/object-get-own-property-symbols.js ***!
  \***************************************************************************/function(t,e){e.f=Object.getOwnPropertySymbols},"./node_modules/core-js/internals/object-get-prototype-of.js":
/*!*******************************************************************!*\
  !*** ./node_modules/core-js/internals/object-get-prototype-of.js ***!
  \*******************************************************************/function(t,e,n){var r=n(/*! ../internals/has */"./node_modules/core-js/internals/has.js"),i=n(/*! ../internals/to-object */"./node_modules/core-js/internals/to-object.js"),o=n(/*! ../internals/shared-key */"./node_modules/core-js/internals/shared-key.js"),s=n(/*! ../internals/correct-prototype-getter */"./node_modules/core-js/internals/correct-prototype-getter.js"),a=o("IE_PROTO"),c=Object.prototype;t.exports=s?Object.getPrototypeOf:function(t){return t=i(t),r(t,a)?t[a]:"function"==typeof t.constructor&&t instanceof t.constructor?t.constructor.prototype:t instanceof Object?c:null}},"./node_modules/core-js/internals/object-keys-internal.js":
/*!****************************************************************!*\
  !*** ./node_modules/core-js/internals/object-keys-internal.js ***!
  \****************************************************************/function(t,e,n){var r=n(/*! ../internals/has */"./node_modules/core-js/internals/has.js"),i=n(/*! ../internals/to-indexed-object */"./node_modules/core-js/internals/to-indexed-object.js"),o=n(/*! ../internals/array-includes */"./node_modules/core-js/internals/array-includes.js").indexOf,s=n(/*! ../internals/hidden-keys */"./node_modules/core-js/internals/hidden-keys.js");t.exports=function(t,e){var n,a=i(t),c=0,u=[];for(n in a)!r(s,n)&&r(a,n)&&u.push(n);while(e.length>c)r(a,n=e[c++])&&(~o(u,n)||u.push(n));return u}},"./node_modules/core-js/internals/object-keys.js":
/*!*******************************************************!*\
  !*** ./node_modules/core-js/internals/object-keys.js ***!
  \*******************************************************/function(t,e,n){var r=n(/*! ../internals/object-keys-internal */"./node_modules/core-js/internals/object-keys-internal.js"),i=n(/*! ../internals/enum-bug-keys */"./node_modules/core-js/internals/enum-bug-keys.js");t.exports=Object.keys||function(t){return r(t,i)}},"./node_modules/core-js/internals/object-property-is-enumerable.js":
/*!*************************************************************************!*\
  !*** ./node_modules/core-js/internals/object-property-is-enumerable.js ***!
  \*************************************************************************/function(t,e){"use strict";var n={}.propertyIsEnumerable,r=Object.getOwnPropertyDescriptor,i=r&&!n.call({1:2},1);e.f=i?function(t){var e=r(this,t);return!!e&&e.enumerable}:n},"./node_modules/core-js/internals/object-set-prototype-of.js":
/*!*******************************************************************!*\
  !*** ./node_modules/core-js/internals/object-set-prototype-of.js ***!
  \*******************************************************************/function(t,e,n){var r=n(/*! ../internals/an-object */"./node_modules/core-js/internals/an-object.js"),i=n(/*! ../internals/a-possible-prototype */"./node_modules/core-js/internals/a-possible-prototype.js");t.exports=Object.setPrototypeOf||("__proto__"in{}?function(){var t,e=!1,n={};try{t=Object.getOwnPropertyDescriptor(Object.prototype,"__proto__").set,t.call(n,[]),e=n instanceof Array}catch(o){}return function(n,o){return r(n),i(o),e?t.call(n,o):n.__proto__=o,n}}():void 0)},"./node_modules/core-js/internals/object-to-string.js":
/*!************************************************************!*\
  !*** ./node_modules/core-js/internals/object-to-string.js ***!
  \************************************************************/function(t,e,n){"use strict";var r=n(/*! ../internals/to-string-tag-support */"./node_modules/core-js/internals/to-string-tag-support.js"),i=n(/*! ../internals/classof */"./node_modules/core-js/internals/classof.js");t.exports=r?{}.toString:function(){return"[object "+i(this)+"]"}},"./node_modules/core-js/internals/ordinary-to-primitive.js":
/*!*****************************************************************!*\
  !*** ./node_modules/core-js/internals/ordinary-to-primitive.js ***!
  \*****************************************************************/function(t,e,n){var r=n(/*! ../internals/is-object */"./node_modules/core-js/internals/is-object.js");t.exports=function(t,e){var n,i;if("string"===e&&"function"==typeof(n=t.toString)&&!r(i=n.call(t)))return i;if("function"==typeof(n=t.valueOf)&&!r(i=n.call(t)))return i;if("string"!==e&&"function"==typeof(n=t.toString)&&!r(i=n.call(t)))return i;throw TypeError("Can't convert object to primitive value")}},"./node_modules/core-js/internals/own-keys.js":
/*!****************************************************!*\
  !*** ./node_modules/core-js/internals/own-keys.js ***!
  \****************************************************/function(t,e,n){var r=n(/*! ../internals/get-built-in */"./node_modules/core-js/internals/get-built-in.js"),i=n(/*! ../internals/object-get-own-property-names */"./node_modules/core-js/internals/object-get-own-property-names.js"),o=n(/*! ../internals/object-get-own-property-symbols */"./node_modules/core-js/internals/object-get-own-property-symbols.js"),s=n(/*! ../internals/an-object */"./node_modules/core-js/internals/an-object.js");t.exports=r("Reflect","ownKeys")||function(t){var e=i.f(s(t)),n=o.f;return n?e.concat(n(t)):e}},"./node_modules/core-js/internals/path.js":
/*!************************************************!*\
  !*** ./node_modules/core-js/internals/path.js ***!
  \************************************************/function(t,e,n){var r=n(/*! ../internals/global */"./node_modules/core-js/internals/global.js");t.exports=r},"./node_modules/core-js/internals/redefine.js":
/*!****************************************************!*\
  !*** ./node_modules/core-js/internals/redefine.js ***!
  \****************************************************/function(t,e,n){var r=n(/*! ../internals/global */"./node_modules/core-js/internals/global.js"),i=n(/*! ../internals/create-non-enumerable-property */"./node_modules/core-js/internals/create-non-enumerable-property.js"),o=n(/*! ../internals/has */"./node_modules/core-js/internals/has.js"),s=n(/*! ../internals/set-global */"./node_modules/core-js/internals/set-global.js"),a=n(/*! ../internals/inspect-source */"./node_modules/core-js/internals/inspect-source.js"),c=n(/*! ../internals/internal-state */"./node_modules/core-js/internals/internal-state.js"),u=c.get,l=c.enforce,h=String(String).split("String");(t.exports=function(t,e,n,a){var c,u=!!a&&!!a.unsafe,f=!!a&&!!a.enumerable,d=!!a&&!!a.noTargetGet;"function"==typeof n&&("string"!=typeof e||o(n,"name")||i(n,"name",e),c=l(n),c.source||(c.source=h.join("string"==typeof e?e:""))),t!==r?(u?!d&&t[e]&&(f=!0):delete t[e],f?t[e]=n:i(t,e,n)):f?t[e]=n:s(e,n)})(Function.prototype,"toString",(function(){return"function"==typeof this&&u(this).source||a(this)}))},"./node_modules/core-js/internals/regexp-flags.js":
/*!********************************************************!*\
  !*** ./node_modules/core-js/internals/regexp-flags.js ***!
  \********************************************************/function(t,e,n){"use strict";var r=n(/*! ../internals/an-object */"./node_modules/core-js/internals/an-object.js");t.exports=function(){var t=r(this),e="";return t.global&&(e+="g"),t.ignoreCase&&(e+="i"),t.multiline&&(e+="m"),t.dotAll&&(e+="s"),t.unicode&&(e+="u"),t.sticky&&(e+="y"),e}},"./node_modules/core-js/internals/require-object-coercible.js":
/*!********************************************************************!*\
  !*** ./node_modules/core-js/internals/require-object-coercible.js ***!
  \********************************************************************/function(t){t.exports=function(t){if(void 0==t)throw TypeError("Can't call method on "+t);return t}},"./node_modules/core-js/internals/set-global.js":
/*!******************************************************!*\
  !*** ./node_modules/core-js/internals/set-global.js ***!
  \******************************************************/function(t,e,n){var r=n(/*! ../internals/global */"./node_modules/core-js/internals/global.js");t.exports=function(t,e){try{Object.defineProperty(r,t,{value:e,configurable:!0,writable:!0})}catch(n){r[t]=e}return e}},"./node_modules/core-js/internals/set-to-string-tag.js":
/*!*************************************************************!*\
  !*** ./node_modules/core-js/internals/set-to-string-tag.js ***!
  \*************************************************************/function(t,e,n){var r=n(/*! ../internals/object-define-property */"./node_modules/core-js/internals/object-define-property.js").f,i=n(/*! ../internals/has */"./node_modules/core-js/internals/has.js"),o=n(/*! ../internals/well-known-symbol */"./node_modules/core-js/internals/well-known-symbol.js"),s=o("toStringTag");t.exports=function(t,e,n){t&&!i(t=n?t:t.prototype,s)&&r(t,s,{configurable:!0,value:e})}},"./node_modules/core-js/internals/shared-key.js":
/*!******************************************************!*\
  !*** ./node_modules/core-js/internals/shared-key.js ***!
  \******************************************************/function(t,e,n){var r=n(/*! ../internals/shared */"./node_modules/core-js/internals/shared.js"),i=n(/*! ../internals/uid */"./node_modules/core-js/internals/uid.js"),o=r("keys");t.exports=function(t){return o[t]||(o[t]=i(t))}},"./node_modules/core-js/internals/shared-store.js":
/*!********************************************************!*\
  !*** ./node_modules/core-js/internals/shared-store.js ***!
  \********************************************************/function(t,e,n){var r=n(/*! ../internals/global */"./node_modules/core-js/internals/global.js"),i=n(/*! ../internals/set-global */"./node_modules/core-js/internals/set-global.js"),o="__core-js_shared__",s=r[o]||i(o,{});t.exports=s},"./node_modules/core-js/internals/shared.js":
/*!**************************************************!*\
  !*** ./node_modules/core-js/internals/shared.js ***!
  \**************************************************/function(t,e,n){var r=n(/*! ../internals/is-pure */"./node_modules/core-js/internals/is-pure.js"),i=n(/*! ../internals/shared-store */"./node_modules/core-js/internals/shared-store.js");(t.exports=function(t,e){return i[t]||(i[t]=void 0!==e?e:{})})("versions",[]).push({version:"3.16.0",mode:r?"pure":"global",copyright:"© 2021 Denis Pushkarev (zloirock.ru)"})},"./node_modules/core-js/internals/string-html-forced.js":
/*!**************************************************************!*\
  !*** ./node_modules/core-js/internals/string-html-forced.js ***!
  \**************************************************************/function(t,e,n){var r=n(/*! ../internals/fails */"./node_modules/core-js/internals/fails.js");t.exports=function(t){return r((function(){var e=""[t]('"');return e!==e.toLowerCase()||e.split('"').length>3}))}},"./node_modules/core-js/internals/string-multibyte.js":
/*!************************************************************!*\
  !*** ./node_modules/core-js/internals/string-multibyte.js ***!
  \************************************************************/function(t,e,n){var r=n(/*! ../internals/to-integer */"./node_modules/core-js/internals/to-integer.js"),i=n(/*! ../internals/to-string */"./node_modules/core-js/internals/to-string.js"),o=n(/*! ../internals/require-object-coercible */"./node_modules/core-js/internals/require-object-coercible.js"),s=function(t){return function(e,n){var s,a,c=i(o(e)),u=r(n),l=c.length;return u<0||u>=l?t?"":void 0:(s=c.charCodeAt(u),s<55296||s>56319||u+1===l||(a=c.charCodeAt(u+1))<56320||a>57343?t?c.charAt(u):s:t?c.slice(u,u+2):a-56320+(s-55296<<10)+65536)}};t.exports={codeAt:s(!1),charAt:s(!0)}},"./node_modules/core-js/internals/string-trim.js":
/*!*******************************************************!*\
  !*** ./node_modules/core-js/internals/string-trim.js ***!
  \*******************************************************/function(t,e,n){var r=n(/*! ../internals/require-object-coercible */"./node_modules/core-js/internals/require-object-coercible.js"),i=n(/*! ../internals/to-string */"./node_modules/core-js/internals/to-string.js"),o=n(/*! ../internals/whitespaces */"./node_modules/core-js/internals/whitespaces.js"),s="["+o+"]",a=RegExp("^"+s+s+"*"),c=RegExp(s+s+"*$"),u=function(t){return function(e){var n=i(r(e));return 1&t&&(n=n.replace(a,"")),2&t&&(n=n.replace(c,"")),n}};t.exports={start:u(1),end:u(2),trim:u(3)}},"./node_modules/core-js/internals/to-absolute-index.js":
/*!*************************************************************!*\
  !*** ./node_modules/core-js/internals/to-absolute-index.js ***!
  \*************************************************************/function(t,e,n){var r=n(/*! ../internals/to-integer */"./node_modules/core-js/internals/to-integer.js"),i=Math.max,o=Math.min;t.exports=function(t,e){var n=r(t);return n<0?i(n+e,0):o(n,e)}},"./node_modules/core-js/internals/to-indexed-object.js":
/*!*************************************************************!*\
  !*** ./node_modules/core-js/internals/to-indexed-object.js ***!
  \*************************************************************/function(t,e,n){var r=n(/*! ../internals/indexed-object */"./node_modules/core-js/internals/indexed-object.js"),i=n(/*! ../internals/require-object-coercible */"./node_modules/core-js/internals/require-object-coercible.js");t.exports=function(t){return r(i(t))}},"./node_modules/core-js/internals/to-integer.js":
/*!******************************************************!*\
  !*** ./node_modules/core-js/internals/to-integer.js ***!
  \******************************************************/function(t){var e=Math.ceil,n=Math.floor;t.exports=function(t){return isNaN(t=+t)?0:(t>0?n:e)(t)}},"./node_modules/core-js/internals/to-length.js":
/*!*****************************************************!*\
  !*** ./node_modules/core-js/internals/to-length.js ***!
  \*****************************************************/function(t,e,n){var r=n(/*! ../internals/to-integer */"./node_modules/core-js/internals/to-integer.js"),i=Math.min;t.exports=function(t){return t>0?i(r(t),9007199254740991):0}},"./node_modules/core-js/internals/to-object.js":
/*!*****************************************************!*\
  !*** ./node_modules/core-js/internals/to-object.js ***!
  \*****************************************************/function(t,e,n){var r=n(/*! ../internals/require-object-coercible */"./node_modules/core-js/internals/require-object-coercible.js");t.exports=function(t){return Object(r(t))}},"./node_modules/core-js/internals/to-primitive.js":
/*!********************************************************!*\
  !*** ./node_modules/core-js/internals/to-primitive.js ***!
  \********************************************************/function(t,e,n){var r=n(/*! ../internals/is-object */"./node_modules/core-js/internals/is-object.js"),i=n(/*! ../internals/is-symbol */"./node_modules/core-js/internals/is-symbol.js"),o=n(/*! ../internals/ordinary-to-primitive */"./node_modules/core-js/internals/ordinary-to-primitive.js"),s=n(/*! ../internals/well-known-symbol */"./node_modules/core-js/internals/well-known-symbol.js"),a=s("toPrimitive");t.exports=function(t,e){if(!r(t)||i(t))return t;var n,s=t[a];if(void 0!==s){if(void 0===e&&(e="default"),n=s.call(t,e),!r(n)||i(n))return n;throw TypeError("Can't convert object to primitive value")}return void 0===e&&(e="number"),o(t,e)}},"./node_modules/core-js/internals/to-property-key.js":
/*!***********************************************************!*\
  !*** ./node_modules/core-js/internals/to-property-key.js ***!
  \***********************************************************/function(t,e,n){var r=n(/*! ../internals/to-primitive */"./node_modules/core-js/internals/to-primitive.js"),i=n(/*! ../internals/is-symbol */"./node_modules/core-js/internals/is-symbol.js");t.exports=function(t){var e=r(t,"string");return i(e)?e:String(e)}},"./node_modules/core-js/internals/to-string-tag-support.js":
/*!*****************************************************************!*\
  !*** ./node_modules/core-js/internals/to-string-tag-support.js ***!
  \*****************************************************************/function(t,e,n){var r=n(/*! ../internals/well-known-symbol */"./node_modules/core-js/internals/well-known-symbol.js"),i=r("toStringTag"),o={};o[i]="z",t.exports="[object z]"===String(o)},"./node_modules/core-js/internals/to-string.js":
/*!*****************************************************!*\
  !*** ./node_modules/core-js/internals/to-string.js ***!
  \*****************************************************/function(t,e,n){var r=n(/*! ../internals/is-symbol */"./node_modules/core-js/internals/is-symbol.js");t.exports=function(t){if(r(t))throw TypeError("Cannot convert a Symbol value to a string");return String(t)}},"./node_modules/core-js/internals/uid.js":
/*!***********************************************!*\
  !*** ./node_modules/core-js/internals/uid.js ***!
  \***********************************************/function(t){var e=0,n=Math.random();t.exports=function(t){return"Symbol("+String(void 0===t?"":t)+")_"+(++e+n).toString(36)}},"./node_modules/core-js/internals/use-symbol-as-uid.js":
/*!*************************************************************!*\
  !*** ./node_modules/core-js/internals/use-symbol-as-uid.js ***!
  \*************************************************************/function(t,e,n){var r=n(/*! ../internals/native-symbol */"./node_modules/core-js/internals/native-symbol.js");t.exports=r&&!Symbol.sham&&"symbol"==typeof Symbol.iterator},"./node_modules/core-js/internals/well-known-symbol-wrapped.js":
/*!*********************************************************************!*\
  !*** ./node_modules/core-js/internals/well-known-symbol-wrapped.js ***!
  \*********************************************************************/function(t,e,n){var r=n(/*! ../internals/well-known-symbol */"./node_modules/core-js/internals/well-known-symbol.js");e.f=r},"./node_modules/core-js/internals/well-known-symbol.js":
/*!*************************************************************!*\
  !*** ./node_modules/core-js/internals/well-known-symbol.js ***!
  \*************************************************************/function(t,e,n){var r=n(/*! ../internals/global */"./node_modules/core-js/internals/global.js"),i=n(/*! ../internals/shared */"./node_modules/core-js/internals/shared.js"),o=n(/*! ../internals/has */"./node_modules/core-js/internals/has.js"),s=n(/*! ../internals/uid */"./node_modules/core-js/internals/uid.js"),a=n(/*! ../internals/native-symbol */"./node_modules/core-js/internals/native-symbol.js"),c=n(/*! ../internals/use-symbol-as-uid */"./node_modules/core-js/internals/use-symbol-as-uid.js"),u=i("wks"),l=r.Symbol,h=c?l:l&&l.withoutSetter||s;t.exports=function(t){return o(u,t)&&(a||"string"==typeof u[t])||(a&&o(l,t)?u[t]=l[t]:u[t]=h("Symbol."+t)),u[t]}},"./node_modules/core-js/internals/whitespaces.js":
/*!*******************************************************!*\
  !*** ./node_modules/core-js/internals/whitespaces.js ***!
  \*******************************************************/function(t){t.exports="\t\n\v\f\r                　\u2028\u2029\ufeff"},"./node_modules/core-js/modules/es.array.concat.js":
/*!*********************************************************!*\
  !*** ./node_modules/core-js/modules/es.array.concat.js ***!
  \*********************************************************/function(t,e,n){"use strict";var r=n(/*! ../internals/export */"./node_modules/core-js/internals/export.js"),i=n(/*! ../internals/fails */"./node_modules/core-js/internals/fails.js"),o=n(/*! ../internals/is-array */"./node_modules/core-js/internals/is-array.js"),s=n(/*! ../internals/is-object */"./node_modules/core-js/internals/is-object.js"),a=n(/*! ../internals/to-object */"./node_modules/core-js/internals/to-object.js"),c=n(/*! ../internals/to-length */"./node_modules/core-js/internals/to-length.js"),u=n(/*! ../internals/create-property */"./node_modules/core-js/internals/create-property.js"),l=n(/*! ../internals/array-species-create */"./node_modules/core-js/internals/array-species-create.js"),h=n(/*! ../internals/array-method-has-species-support */"./node_modules/core-js/internals/array-method-has-species-support.js"),f=n(/*! ../internals/well-known-symbol */"./node_modules/core-js/internals/well-known-symbol.js"),d=n(/*! ../internals/engine-v8-version */"./node_modules/core-js/internals/engine-v8-version.js"),p=f("isConcatSpreadable"),m=9007199254740991,g="Maximum allowed index exceeded",v=d>=51||!i((function(){var t=[];return t[p]=!1,t.concat()[0]!==t})),b=h("concat"),y=function(t){if(!s(t))return!1;var e=t[p];return void 0!==e?!!e:o(t)},w=!v||!b;r({target:"Array",proto:!0,forced:w},{concat:function(t){var e,n,r,i,o,s=a(this),h=l(s,0),f=0;for(e=-1,r=arguments.length;e<r;e++)if(o=-1===e?s:arguments[e],y(o)){if(i=c(o.length),f+i>m)throw TypeError(g);for(n=0;n<i;n++,f++)n in o&&u(h,f,o[n])}else{if(f>=m)throw TypeError(g);u(h,f++,o)}return h.length=f,h}})},"./node_modules/core-js/modules/es.array.iterator.js":
/*!***********************************************************!*\
  !*** ./node_modules/core-js/modules/es.array.iterator.js ***!
  \***********************************************************/function(t,e,n){"use strict";var r=n(/*! ../internals/to-indexed-object */"./node_modules/core-js/internals/to-indexed-object.js"),i=n(/*! ../internals/add-to-unscopables */"./node_modules/core-js/internals/add-to-unscopables.js"),o=n(/*! ../internals/iterators */"./node_modules/core-js/internals/iterators.js"),s=n(/*! ../internals/internal-state */"./node_modules/core-js/internals/internal-state.js"),a=n(/*! ../internals/define-iterator */"./node_modules/core-js/internals/define-iterator.js"),c="Array Iterator",u=s.set,l=s.getterFor(c);t.exports=a(Array,"Array",(function(t,e){u(this,{type:c,target:r(t),index:0,kind:e})}),(function(){var t=l(this),e=t.target,n=t.kind,r=t.index++;return!e||r>=e.length?(t.target=void 0,{value:void 0,done:!0}):"keys"==n?{value:r,done:!1}:"values"==n?{value:e[r],done:!1}:{value:[r,e[r]],done:!1}}),"values"),o.Arguments=o.Array,i("keys"),i("values"),i("entries")},"./node_modules/core-js/modules/es.array.join.js":
/*!*******************************************************!*\
  !*** ./node_modules/core-js/modules/es.array.join.js ***!
  \*******************************************************/function(t,e,n){"use strict";var r=n(/*! ../internals/export */"./node_modules/core-js/internals/export.js"),i=n(/*! ../internals/indexed-object */"./node_modules/core-js/internals/indexed-object.js"),o=n(/*! ../internals/to-indexed-object */"./node_modules/core-js/internals/to-indexed-object.js"),s=n(/*! ../internals/array-method-is-strict */"./node_modules/core-js/internals/array-method-is-strict.js"),a=[].join,c=i!=Object,u=s("join",",");r({target:"Array",proto:!0,forced:c||!u},{join:function(t){return a.call(o(this),void 0===t?",":t)}})},"./node_modules/core-js/modules/es.array.map.js":
/*!******************************************************!*\
  !*** ./node_modules/core-js/modules/es.array.map.js ***!
  \******************************************************/function(t,e,n){"use strict";var r=n(/*! ../internals/export */"./node_modules/core-js/internals/export.js"),i=n(/*! ../internals/array-iteration */"./node_modules/core-js/internals/array-iteration.js").map,o=n(/*! ../internals/array-method-has-species-support */"./node_modules/core-js/internals/array-method-has-species-support.js"),s=o("map");r({target:"Array",proto:!0,forced:!s},{map:function(t){return i(this,t,arguments.length>1?arguments[1]:void 0)}})},"./node_modules/core-js/modules/es.array.slice.js":
/*!********************************************************!*\
  !*** ./node_modules/core-js/modules/es.array.slice.js ***!
  \********************************************************/function(t,e,n){"use strict";var r=n(/*! ../internals/export */"./node_modules/core-js/internals/export.js"),i=n(/*! ../internals/is-object */"./node_modules/core-js/internals/is-object.js"),o=n(/*! ../internals/is-array */"./node_modules/core-js/internals/is-array.js"),s=n(/*! ../internals/to-absolute-index */"./node_modules/core-js/internals/to-absolute-index.js"),a=n(/*! ../internals/to-length */"./node_modules/core-js/internals/to-length.js"),c=n(/*! ../internals/to-indexed-object */"./node_modules/core-js/internals/to-indexed-object.js"),u=n(/*! ../internals/create-property */"./node_modules/core-js/internals/create-property.js"),l=n(/*! ../internals/well-known-symbol */"./node_modules/core-js/internals/well-known-symbol.js"),h=n(/*! ../internals/array-method-has-species-support */"./node_modules/core-js/internals/array-method-has-species-support.js"),f=h("slice"),d=l("species"),p=[].slice,m=Math.max;r({target:"Array",proto:!0,forced:!f},{slice:function(t,e){var n,r,l,h=c(this),f=a(h.length),g=s(t,f),v=s(void 0===e?f:e,f);if(o(h)&&(n=h.constructor,"function"!=typeof n||n!==Array&&!o(n.prototype)?i(n)&&(n=n[d],null===n&&(n=void 0)):n=void 0,n===Array||void 0===n))return p.call(h,g,v);for(r=new(void 0===n?Array:n)(m(v-g,0)),l=0;g<v;g++,l++)g in h&&u(r,l,h[g]);return r.length=l,r}})},"./node_modules/core-js/modules/es.function.name.js":
/*!**********************************************************!*\
  !*** ./node_modules/core-js/modules/es.function.name.js ***!
  \**********************************************************/function(t,e,n){var r=n(/*! ../internals/descriptors */"./node_modules/core-js/internals/descriptors.js"),i=n(/*! ../internals/object-define-property */"./node_modules/core-js/internals/object-define-property.js").f,o=Function.prototype,s=o.toString,a=/^\s*function ([^ (]*)/,c="name";r&&!(c in o)&&i(o,c,{configurable:!0,get:function(){try{return s.call(this).match(a)[1]}catch(t){return""}}})},"./node_modules/core-js/modules/es.number.constructor.js":
/*!***************************************************************!*\
  !*** ./node_modules/core-js/modules/es.number.constructor.js ***!
  \***************************************************************/function(t,e,n){"use strict";var r=n(/*! ../internals/descriptors */"./node_modules/core-js/internals/descriptors.js"),i=n(/*! ../internals/global */"./node_modules/core-js/internals/global.js"),o=n(/*! ../internals/is-forced */"./node_modules/core-js/internals/is-forced.js"),s=n(/*! ../internals/redefine */"./node_modules/core-js/internals/redefine.js"),a=n(/*! ../internals/has */"./node_modules/core-js/internals/has.js"),c=n(/*! ../internals/classof-raw */"./node_modules/core-js/internals/classof-raw.js"),u=n(/*! ../internals/inherit-if-required */"./node_modules/core-js/internals/inherit-if-required.js"),l=n(/*! ../internals/is-symbol */"./node_modules/core-js/internals/is-symbol.js"),h=n(/*! ../internals/to-primitive */"./node_modules/core-js/internals/to-primitive.js"),f=n(/*! ../internals/fails */"./node_modules/core-js/internals/fails.js"),d=n(/*! ../internals/object-create */"./node_modules/core-js/internals/object-create.js"),p=n(/*! ../internals/object-get-own-property-names */"./node_modules/core-js/internals/object-get-own-property-names.js").f,m=n(/*! ../internals/object-get-own-property-descriptor */"./node_modules/core-js/internals/object-get-own-property-descriptor.js").f,g=n(/*! ../internals/object-define-property */"./node_modules/core-js/internals/object-define-property.js").f,v=n(/*! ../internals/string-trim */"./node_modules/core-js/internals/string-trim.js").trim,b="Number",y=i[b],w=y.prototype,j=c(d(w))==b,_=function(t){if(l(t))throw TypeError("Cannot convert a Symbol value to a number");var e,n,r,i,o,s,a,c,u=h(t,"number");if("string"==typeof u&&u.length>2)if(u=v(u),e=u.charCodeAt(0),43===e||45===e){if(n=u.charCodeAt(2),88===n||120===n)return NaN}else if(48===e){switch(u.charCodeAt(1)){case 66:case 98:r=2,i=49;break;case 79:case 111:r=8,i=55;break;default:return+u}for(o=u.slice(2),s=o.length,a=0;a<s;a++)if(c=o.charCodeAt(a),c<48||c>i)return NaN;return parseInt(o,r)}return+u};if(o(b,!y(" 0o1")||!y("0b1")||y("+0x1"))){for(var x,N=function(t){var e=arguments.length<1?0:t,n=this;return n instanceof N&&(j?f((function(){w.valueOf.call(n)})):c(n)!=b)?u(new y(_(e)),n,N):_(e)},A=r?p(y):"MAX_VALUE,MIN_VALUE,NaN,NEGATIVE_INFINITY,POSITIVE_INFINITY,EPSILON,isFinite,isInteger,isNaN,isSafeInteger,MAX_SAFE_INTEGER,MIN_SAFE_INTEGER,parseFloat,parseInt,isInteger,fromString,range".split(","),L=0;A.length>L;L++)a(y,x=A[L])&&!a(N,x)&&g(N,x,m(y,x));N.prototype=w,w.constructor=N,s(i,b,N)}},"./node_modules/core-js/modules/es.object.assign.js":
/*!**********************************************************!*\
  !*** ./node_modules/core-js/modules/es.object.assign.js ***!
  \**********************************************************/function(t,e,n){var r=n(/*! ../internals/export */"./node_modules/core-js/internals/export.js"),i=n(/*! ../internals/object-assign */"./node_modules/core-js/internals/object-assign.js");r({target:"Object",stat:!0,forced:Object.assign!==i},{assign:i})},"./node_modules/core-js/modules/es.object.keys.js":
/*!********************************************************!*\
  !*** ./node_modules/core-js/modules/es.object.keys.js ***!
  \********************************************************/function(t,e,n){var r=n(/*! ../internals/export */"./node_modules/core-js/internals/export.js"),i=n(/*! ../internals/to-object */"./node_modules/core-js/internals/to-object.js"),o=n(/*! ../internals/object-keys */"./node_modules/core-js/internals/object-keys.js"),s=n(/*! ../internals/fails */"./node_modules/core-js/internals/fails.js"),a=s((function(){o(1)}));r({target:"Object",stat:!0,forced:a},{keys:function(t){return o(i(t))}})},"./node_modules/core-js/modules/es.object.to-string.js":
/*!*************************************************************!*\
  !*** ./node_modules/core-js/modules/es.object.to-string.js ***!
  \*************************************************************/function(t,e,n){var r=n(/*! ../internals/to-string-tag-support */"./node_modules/core-js/internals/to-string-tag-support.js"),i=n(/*! ../internals/redefine */"./node_modules/core-js/internals/redefine.js"),o=n(/*! ../internals/object-to-string */"./node_modules/core-js/internals/object-to-string.js");r||i(Object.prototype,"toString",o,{unsafe:!0})},"./node_modules/core-js/modules/es.regexp.to-string.js":
/*!*************************************************************!*\
  !*** ./node_modules/core-js/modules/es.regexp.to-string.js ***!
  \*************************************************************/function(t,e,n){"use strict";var r=n(/*! ../internals/redefine */"./node_modules/core-js/internals/redefine.js"),i=n(/*! ../internals/an-object */"./node_modules/core-js/internals/an-object.js"),o=n(/*! ../internals/to-string */"./node_modules/core-js/internals/to-string.js"),s=n(/*! ../internals/fails */"./node_modules/core-js/internals/fails.js"),a=n(/*! ../internals/regexp-flags */"./node_modules/core-js/internals/regexp-flags.js"),c="toString",u=RegExp.prototype,l=u[c],h=s((function(){return"/a/b"!=l.call({source:"a",flags:"b"})})),f=l.name!=c;(h||f)&&r(RegExp.prototype,c,(function(){var t=i(this),e=o(t.source),n=t.flags,r=o(void 0===n&&t instanceof RegExp&&!("flags"in u)?a.call(t):n);return"/"+e+"/"+r}),{unsafe:!0})},"./node_modules/core-js/modules/es.string.iterator.js":
/*!************************************************************!*\
  !*** ./node_modules/core-js/modules/es.string.iterator.js ***!
  \************************************************************/function(t,e,n){"use strict";var r=n(/*! ../internals/string-multibyte */"./node_modules/core-js/internals/string-multibyte.js").charAt,i=n(/*! ../internals/to-string */"./node_modules/core-js/internals/to-string.js"),o=n(/*! ../internals/internal-state */"./node_modules/core-js/internals/internal-state.js"),s=n(/*! ../internals/define-iterator */"./node_modules/core-js/internals/define-iterator.js"),a="String Iterator",c=o.set,u=o.getterFor(a);s(String,"String",(function(t){c(this,{type:a,string:i(t),index:0})}),(function(){var t,e=u(this),n=e.string,i=e.index;return i>=n.length?{value:void 0,done:!0}:(t=r(n,i),e.index+=t.length,{value:t,done:!1})}))},"./node_modules/core-js/modules/es.string.link.js":
/*!********************************************************!*\
  !*** ./node_modules/core-js/modules/es.string.link.js ***!
  \********************************************************/function(t,e,n){"use strict";var r=n(/*! ../internals/export */"./node_modules/core-js/internals/export.js"),i=n(/*! ../internals/create-html */"./node_modules/core-js/internals/create-html.js"),o=n(/*! ../internals/string-html-forced */"./node_modules/core-js/internals/string-html-forced.js");r({target:"String",proto:!0,forced:o("link")},{link:function(t){return i(this,"a","href",t)}})},"./node_modules/core-js/modules/es.symbol.description.js":
/*!***************************************************************!*\
  !*** ./node_modules/core-js/modules/es.symbol.description.js ***!
  \***************************************************************/function(t,e,n){"use strict";var r=n(/*! ../internals/export */"./node_modules/core-js/internals/export.js"),i=n(/*! ../internals/descriptors */"./node_modules/core-js/internals/descriptors.js"),o=n(/*! ../internals/global */"./node_modules/core-js/internals/global.js"),s=n(/*! ../internals/has */"./node_modules/core-js/internals/has.js"),a=n(/*! ../internals/is-object */"./node_modules/core-js/internals/is-object.js"),c=n(/*! ../internals/object-define-property */"./node_modules/core-js/internals/object-define-property.js").f,u=n(/*! ../internals/copy-constructor-properties */"./node_modules/core-js/internals/copy-constructor-properties.js"),l=o.Symbol;if(i&&"function"==typeof l&&(!("description"in l.prototype)||void 0!==l().description)){var h={},f=function(){var t=arguments.length<1||void 0===arguments[0]?void 0:String(arguments[0]),e=this instanceof f?new l(t):void 0===t?l():l(t);return""===t&&(h[e]=!0),e};u(f,l);var d=f.prototype=l.prototype;d.constructor=f;var p=d.toString,m="Symbol(test)"==String(l("test")),g=/^Symbol\((.*)\)[^)]+$/;c(d,"description",{configurable:!0,get:function(){var t=a(this)?this.valueOf():this,e=p.call(t);if(s(h,t))return"";var n=m?e.slice(7,-1):e.replace(g,"$1");return""===n?void 0:n}}),r({global:!0,forced:!0},{Symbol:f})}},"./node_modules/core-js/modules/es.symbol.iterator.js":
/*!************************************************************!*\
  !*** ./node_modules/core-js/modules/es.symbol.iterator.js ***!
  \************************************************************/function(t,e,n){var r=n(/*! ../internals/define-well-known-symbol */"./node_modules/core-js/internals/define-well-known-symbol.js");r("iterator")},"./node_modules/core-js/modules/es.symbol.js":
/*!***************************************************!*\
  !*** ./node_modules/core-js/modules/es.symbol.js ***!
  \***************************************************/function(t,e,n){"use strict";var r=n(/*! ../internals/export */"./node_modules/core-js/internals/export.js"),i=n(/*! ../internals/global */"./node_modules/core-js/internals/global.js"),o=n(/*! ../internals/get-built-in */"./node_modules/core-js/internals/get-built-in.js"),s=n(/*! ../internals/is-pure */"./node_modules/core-js/internals/is-pure.js"),a=n(/*! ../internals/descriptors */"./node_modules/core-js/internals/descriptors.js"),c=n(/*! ../internals/native-symbol */"./node_modules/core-js/internals/native-symbol.js"),u=n(/*! ../internals/fails */"./node_modules/core-js/internals/fails.js"),l=n(/*! ../internals/has */"./node_modules/core-js/internals/has.js"),h=n(/*! ../internals/is-array */"./node_modules/core-js/internals/is-array.js"),f=n(/*! ../internals/is-object */"./node_modules/core-js/internals/is-object.js"),d=n(/*! ../internals/is-symbol */"./node_modules/core-js/internals/is-symbol.js"),p=n(/*! ../internals/an-object */"./node_modules/core-js/internals/an-object.js"),m=n(/*! ../internals/to-object */"./node_modules/core-js/internals/to-object.js"),g=n(/*! ../internals/to-indexed-object */"./node_modules/core-js/internals/to-indexed-object.js"),v=n(/*! ../internals/to-property-key */"./node_modules/core-js/internals/to-property-key.js"),b=n(/*! ../internals/to-string */"./node_modules/core-js/internals/to-string.js"),y=n(/*! ../internals/create-property-descriptor */"./node_modules/core-js/internals/create-property-descriptor.js"),w=n(/*! ../internals/object-create */"./node_modules/core-js/internals/object-create.js"),j=n(/*! ../internals/object-keys */"./node_modules/core-js/internals/object-keys.js"),_=n(/*! ../internals/object-get-own-property-names */"./node_modules/core-js/internals/object-get-own-property-names.js"),x=n(/*! ../internals/object-get-own-property-names-external */"./node_modules/core-js/internals/object-get-own-property-names-external.js"),N=n(/*! ../internals/object-get-own-property-symbols */"./node_modules/core-js/internals/object-get-own-property-symbols.js"),A=n(/*! ../internals/object-get-own-property-descriptor */"./node_modules/core-js/internals/object-get-own-property-descriptor.js"),L=n(/*! ../internals/object-define-property */"./node_modules/core-js/internals/object-define-property.js"),S=n(/*! ../internals/object-property-is-enumerable */"./node_modules/core-js/internals/object-property-is-enumerable.js"),P=n(/*! ../internals/create-non-enumerable-property */"./node_modules/core-js/internals/create-non-enumerable-property.js"),k=n(/*! ../internals/redefine */"./node_modules/core-js/internals/redefine.js"),I=n(/*! ../internals/shared */"./node_modules/core-js/internals/shared.js"),C=n(/*! ../internals/shared-key */"./node_modules/core-js/internals/shared-key.js"),F=n(/*! ../internals/hidden-keys */"./node_modules/core-js/internals/hidden-keys.js"),O=n(/*! ../internals/uid */"./node_modules/core-js/internals/uid.js"),E=n(/*! ../internals/well-known-symbol */"./node_modules/core-js/internals/well-known-symbol.js"),M=n(/*! ../internals/well-known-symbol-wrapped */"./node_modules/core-js/internals/well-known-symbol-wrapped.js"),B=n(/*! ../internals/define-well-known-symbol */"./node_modules/core-js/internals/define-well-known-symbol.js"),T=n(/*! ../internals/set-to-string-tag */"./node_modules/core-js/internals/set-to-string-tag.js"),q=n(/*! ../internals/internal-state */"./node_modules/core-js/internals/internal-state.js"),D=n(/*! ../internals/array-iteration */"./node_modules/core-js/internals/array-iteration.js").forEach,R=C("hidden"),z="Symbol",U="prototype",H=E("toPrimitive"),V=q.set,W=q.getterFor(z),G=Object[U],Y=i.Symbol,J=o("JSON","stringify"),X=A.f,K=L.f,$=x.f,Z=S.f,Q=I("symbols"),tt=I("op-symbols"),et=I("string-to-symbol-registry"),nt=I("symbol-to-string-registry"),rt=I("wks"),it=i.QObject,ot=!it||!it[U]||!it[U].findChild,st=a&&u((function(){return 7!=w(K({},"a",{get:function(){return K(this,"a",{value:7}).a}})).a}))?function(t,e,n){var r=X(G,e);r&&delete G[e],K(t,e,n),r&&t!==G&&K(G,e,r)}:K,at=function(t,e){var n=Q[t]=w(Y[U]);return V(n,{type:z,tag:t,description:e}),a||(n.description=e),n},ct=function(t,e,n){t===G&&ct(tt,e,n),p(t);var r=v(e);return p(n),l(Q,r)?(n.enumerable?(l(t,R)&&t[R][r]&&(t[R][r]=!1),n=w(n,{enumerable:y(0,!1)})):(l(t,R)||K(t,R,y(1,{})),t[R][r]=!0),st(t,r,n)):K(t,r,n)},ut=function(t,e){p(t);var n=g(e),r=j(n).concat(pt(n));return D(r,(function(e){a&&!ht.call(n,e)||ct(t,e,n[e])})),t},lt=function(t,e){return void 0===e?w(t):ut(w(t),e)},ht=function(t){var e=v(t),n=Z.call(this,e);return!(this===G&&l(Q,e)&&!l(tt,e))&&(!(n||!l(this,e)||!l(Q,e)||l(this,R)&&this[R][e])||n)},ft=function(t,e){var n=g(t),r=v(e);if(n!==G||!l(Q,r)||l(tt,r)){var i=X(n,r);return!i||!l(Q,r)||l(n,R)&&n[R][r]||(i.enumerable=!0),i}},dt=function(t){var e=$(g(t)),n=[];return D(e,(function(t){l(Q,t)||l(F,t)||n.push(t)})),n},pt=function(t){var e=t===G,n=$(e?tt:g(t)),r=[];return D(n,(function(t){!l(Q,t)||e&&!l(G,t)||r.push(Q[t])})),r};if(c||(Y=function(){if(this instanceof Y)throw TypeError("Symbol is not a constructor");var t=arguments.length&&void 0!==arguments[0]?b(arguments[0]):void 0,e=O(t),n=function(t){this===G&&n.call(tt,t),l(this,R)&&l(this[R],e)&&(this[R][e]=!1),st(this,e,y(1,t))};return a&&ot&&st(G,e,{configurable:!0,set:n}),at(e,t)},k(Y[U],"toString",(function(){return W(this).tag})),k(Y,"withoutSetter",(function(t){return at(O(t),t)})),S.f=ht,L.f=ct,A.f=ft,_.f=x.f=dt,N.f=pt,M.f=function(t){return at(E(t),t)},a&&(K(Y[U],"description",{configurable:!0,get:function(){return W(this).description}}),s||k(G,"propertyIsEnumerable",ht,{unsafe:!0}))),r({global:!0,wrap:!0,forced:!c,sham:!c},{Symbol:Y}),D(j(rt),(function(t){B(t)})),r({target:z,stat:!0,forced:!c},{for:function(t){var e=b(t);if(l(et,e))return et[e];var n=Y(e);return et[e]=n,nt[n]=e,n},keyFor:function(t){if(!d(t))throw TypeError(t+" is not a symbol");if(l(nt,t))return nt[t]},useSetter:function(){ot=!0},useSimple:function(){ot=!1}}),r({target:"Object",stat:!0,forced:!c,sham:!a},{create:lt,defineProperty:ct,defineProperties:ut,getOwnPropertyDescriptor:ft}),r({target:"Object",stat:!0,forced:!c},{getOwnPropertyNames:dt,getOwnPropertySymbols:pt}),r({target:"Object",stat:!0,forced:u((function(){N.f(1)}))},{getOwnPropertySymbols:function(t){return N.f(m(t))}}),J){var mt=!c||u((function(){var t=Y();return"[null]"!=J([t])||"{}"!=J({a:t})||"{}"!=J(Object(t))}));r({target:"JSON",stat:!0,forced:mt},{stringify:function(t,e,n){var r,i=[t],o=1;while(arguments.length>o)i.push(arguments[o++]);if(r=e,(f(e)||void 0!==t)&&!d(t))return h(e)||(e=function(t,e){if("function"==typeof r&&(e=r.call(this,t,e)),!d(e))return e}),i[1]=e,J.apply(null,i)}})}Y[U][H]||P(Y[U],H,Y[U].valueOf),T(Y,z),F[R]=!0},"./node_modules/core-js/modules/web.dom-collections.for-each.js":
/*!**********************************************************************!*\
  !*** ./node_modules/core-js/modules/web.dom-collections.for-each.js ***!
  \**********************************************************************/function(t,e,n){var r=n(/*! ../internals/global */"./node_modules/core-js/internals/global.js"),i=n(/*! ../internals/dom-iterables */"./node_modules/core-js/internals/dom-iterables.js"),o=n(/*! ../internals/array-for-each */"./node_modules/core-js/internals/array-for-each.js"),s=n(/*! ../internals/create-non-enumerable-property */"./node_modules/core-js/internals/create-non-enumerable-property.js");for(var a in i){var c=r[a],u=c&&c.prototype;if(u&&u.forEach!==o)try{s(u,"forEach",o)}catch(l){u.forEach=o}}},"./node_modules/core-js/modules/web.dom-collections.iterator.js":
/*!**********************************************************************!*\
  !*** ./node_modules/core-js/modules/web.dom-collections.iterator.js ***!
  \**********************************************************************/function(t,e,n){var r=n(/*! ../internals/global */"./node_modules/core-js/internals/global.js"),i=n(/*! ../internals/dom-iterables */"./node_modules/core-js/internals/dom-iterables.js"),o=n(/*! ../modules/es.array.iterator */"./node_modules/core-js/modules/es.array.iterator.js"),s=n(/*! ../internals/create-non-enumerable-property */"./node_modules/core-js/internals/create-non-enumerable-property.js"),a=n(/*! ../internals/well-known-symbol */"./node_modules/core-js/internals/well-known-symbol.js"),c=a("iterator"),u=a("toStringTag"),l=o.values;for(var h in i){var f=r[h],d=f&&f.prototype;if(d){if(d[c]!==l)try{s(d,c,l)}catch(m){d[c]=l}if(d[u]||s(d,u,h),i[h])for(var p in o)if(d[p]!==o[p])try{s(d,p,o[p])}catch(m){d[p]=o[p]}}}},"./node_modules/es6-promise/dist/es6-promise.js":
/*!******************************************************!*\
  !*** ./node_modules/es6-promise/dist/es6-promise.js ***!
  \******************************************************/function(t){
/*!
 * @overview es6-promise - a tiny implementation of Promises/A+.
 * @copyright Copyright (c) 2014 Yehuda Katz, Tom Dale, Stefan Penner and contributors (Conversion to ES6 API by Jake Archibald)
 * @license   Licensed under MIT license
 *            See https://raw.githubusercontent.com/stefanpenner/es6-promise/master/LICENSE
 * @version   v4.2.8+1e68dce6
 */
(function(e,n){t.exports=n()})(0,(function(){"use strict";function t(t){var e=typeof t;return null!==t&&("object"===e||"function"===e)}function n(t){return"function"===typeof t}var i=void 0;i=Array.isArray?Array.isArray:function(t){return"[object Array]"===Object.prototype.toString.call(t)};var o=i,s=0,a=void 0,c=void 0,u=function(t,e){_[s]=t,_[s+1]=e,s+=2,2===s&&(c?c(x):A())};function l(t){c=t}function h(t){u=t}var f="undefined"!==typeof window?window:void 0,d=f||{},p=d.MutationObserver||d.WebKitMutationObserver,m="undefined"===typeof self&&"undefined"!==typeof r&&"[object process]"==={}.toString.call(r),g="undefined"!==typeof Uint8ClampedArray&&"undefined"!==typeof importScripts&&"undefined"!==typeof MessageChannel;function v(){return function(){return r.nextTick(x)}}function b(){return"undefined"!==typeof a?function(){a(x)}:j()}function y(){var t=0,e=new p(x),n=document.createTextNode("");return e.observe(n,{characterData:!0}),function(){n.data=t=++t%2}}function w(){var t=new MessageChannel;return t.port1.onmessage=x,function(){return t.port2.postMessage(0)}}function j(){var t=setTimeout;return function(){return t(x,1)}}var _=new Array(1e3);function x(){for(var t=0;t<s;t+=2){var e=_[t],n=_[t+1];e(n),_[t]=void 0,_[t+1]=void 0}s=0}function N(){try{var t=Function("return this")().require("vertx");return a=t.runOnLoop||t.runOnContext,b()}catch(e){return j()}}var A=void 0;function L(t,e){var n=this,r=new this.constructor(k);void 0===r[P]&&X(r);var i=n._state;if(i){var o=arguments[i-1];u((function(){return W(i,r,o,n._result)}))}else H(n,r,t,e);return r}function S(t){var e=this;if(t&&"object"===typeof t&&t.constructor===e)return t;var n=new e(k);return D(n,t),n}A=m?v():p?y():g?w():void 0===f?N():j();var P=Math.random().toString(36).substring(2);function k(){}var I=void 0,C=1,F=2;function O(){return new TypeError("You cannot resolve a promise with itself")}function E(){return new TypeError("A promises callback cannot return that same promise.")}function M(t,e,n,r){try{t.call(e,n,r)}catch(i){return i}}function B(t,e,n){u((function(t){var r=!1,i=M(n,e,(function(n){r||(r=!0,e!==n?D(t,n):z(t,n))}),(function(e){r||(r=!0,U(t,e))}),"Settle: "+(t._label||" unknown promise"));!r&&i&&(r=!0,U(t,i))}),t)}function T(t,e){e._state===C?z(t,e._result):e._state===F?U(t,e._result):H(e,void 0,(function(e){return D(t,e)}),(function(e){return U(t,e)}))}function q(t,e,r){e.constructor===t.constructor&&r===L&&e.constructor.resolve===S?T(t,e):void 0===r?z(t,e):n(r)?B(t,e,r):z(t,e)}function D(e,n){if(e===n)U(e,O());else if(t(n)){var r=void 0;try{r=n.then}catch(i){return void U(e,i)}q(e,n,r)}else z(e,n)}function R(t){t._onerror&&t._onerror(t._result),V(t)}function z(t,e){t._state===I&&(t._result=e,t._state=C,0!==t._subscribers.length&&u(V,t))}function U(t,e){t._state===I&&(t._state=F,t._result=e,u(R,t))}function H(t,e,n,r){var i=t._subscribers,o=i.length;t._onerror=null,i[o]=e,i[o+C]=n,i[o+F]=r,0===o&&t._state&&u(V,t)}function V(t){var e=t._subscribers,n=t._state;if(0!==e.length){for(var r=void 0,i=void 0,o=t._result,s=0;s<e.length;s+=3)r=e[s],i=e[s+n],r?W(n,r,i,o):i(o);t._subscribers.length=0}}function W(t,e,r,i){var o=n(r),s=void 0,a=void 0,c=!0;if(o){try{s=r(i)}catch(u){c=!1,a=u}if(e===s)return void U(e,E())}else s=i;e._state!==I||(o&&c?D(e,s):!1===c?U(e,a):t===C?z(e,s):t===F&&U(e,s))}function G(t,e){try{e((function(e){D(t,e)}),(function(e){U(t,e)}))}catch(n){U(t,n)}}var Y=0;function J(){return Y++}function X(t){t[P]=Y++,t._state=void 0,t._result=void 0,t._subscribers=[]}function K(){return new Error("Array Methods must be provided an Array")}var $=function(){function t(t,e){this._instanceConstructor=t,this.promise=new t(k),this.promise[P]||X(this.promise),o(e)?(this.length=e.length,this._remaining=e.length,this._result=new Array(this.length),0===this.length?z(this.promise,this._result):(this.length=this.length||0,this._enumerate(e),0===this._remaining&&z(this.promise,this._result))):U(this.promise,K())}return t.prototype._enumerate=function(t){for(var e=0;this._state===I&&e<t.length;e++)this._eachEntry(t[e],e)},t.prototype._eachEntry=function(t,e){var n=this._instanceConstructor,r=n.resolve;if(r===S){var i=void 0,o=void 0,s=!1;try{i=t.then}catch(c){s=!0,o=c}if(i===L&&t._state!==I)this._settledAt(t._state,e,t._result);else if("function"!==typeof i)this._remaining--,this._result[e]=t;else if(n===rt){var a=new n(k);s?U(a,o):q(a,t,i),this._willSettleAt(a,e)}else this._willSettleAt(new n((function(e){return e(t)})),e)}else this._willSettleAt(r(t),e)},t.prototype._settledAt=function(t,e,n){var r=this.promise;r._state===I&&(this._remaining--,t===F?U(r,n):this._result[e]=n),0===this._remaining&&z(r,this._result)},t.prototype._willSettleAt=function(t,e){var n=this;H(t,void 0,(function(t){return n._settledAt(C,e,t)}),(function(t){return n._settledAt(F,e,t)}))},t}();function Z(t){return new $(this,t).promise}function Q(t){var e=this;return o(t)?new e((function(n,r){for(var i=t.length,o=0;o<i;o++)e.resolve(t[o]).then(n,r)})):new e((function(t,e){return e(new TypeError("You must pass an array to race."))}))}function tt(t){var e=this,n=new e(k);return U(n,t),n}function et(){throw new TypeError("You must pass a resolver function as the first argument to the promise constructor")}function nt(){throw new TypeError("Failed to construct 'Promise': Please use the 'new' operator, this object constructor cannot be called as a function.")}var rt=function(){function t(e){this[P]=J(),this._result=this._state=void 0,this._subscribers=[],k!==e&&("function"!==typeof e&&et(),this instanceof t?G(this,e):nt())}return t.prototype.catch=function(t){return this.then(null,t)},t.prototype.finally=function(t){var e=this,r=e.constructor;return n(t)?e.then((function(e){return r.resolve(t()).then((function(){return e}))}),(function(e){return r.resolve(t()).then((function(){throw e}))})):e.then(t,t)},t}();function it(){var t=void 0;if("undefined"!==typeof e)t=e;else if("undefined"!==typeof self)t=self;else try{t=Function("return this")()}catch(i){throw new Error("polyfill failed because global object is unavailable in this environment")}var n=t.Promise;if(n){var r=null;try{r=Object.prototype.toString.call(n.resolve())}catch(i){}if("[object Promise]"===r&&!n.cast)return}t.Promise=rt}return rt.prototype.then=L,rt.all=Z,rt.race=Q,rt.resolve=S,rt.reject=tt,rt._setScheduler=l,rt._setAsap=h,rt._asap=u,rt.polyfill=it,rt.Promise=rt,rt}))},html2canvas:
/*!******************************!*\
  !*** external "html2canvas" ***!
  \******************************/function(t){"use strict";t.exports=n},jspdf:
/*!************************!*\
  !*** external "jspdf" ***!
  \************************/function(e){"use strict";e.exports=t}},o={};function s(t){var e=o[t];if(void 0!==e)return e.exports;var n=o[t]={exports:{}};return i[t].call(n.exports,n,n.exports,s),n.exports}!function(){s.n=function(t){var e=t&&t.__esModule?function(){return t["default"]}:function(){return t};return s.d(e,{a:e}),e}}(),function(){s.d=function(t,e){for(var n in e)s.o(e,n)&&!s.o(t,n)&&Object.defineProperty(t,n,{enumerable:!0,get:e[n]})}}(),function(){s.o=function(t,e){return Object.prototype.hasOwnProperty.call(t,e)}}(),function(){s.r=function(t){"undefined"!==typeof Symbol&&Symbol.toStringTag&&Object.defineProperty(t,Symbol.toStringTag,{value:"Module"}),Object.defineProperty(t,"__esModule",{value:!0})}}();var a={};return function(){"use strict";
/*!**********************!*\
  !*** ./src/index.js ***!
  \**********************/s.r(a);var t=s(/*! ./worker.js */"./src/worker.js"),e=(s(/*! ./plugin/jspdf-plugin.js */"./src/plugin/jspdf-plugin.js"),s(/*! ./plugin/pagebreaks.js */"./src/plugin/pagebreaks.js"),s(/*! ./plugin/hyperlinks.js */"./src/plugin/hyperlinks.js"),function t(e,n){var r=new t.Worker(n);return e?r.from(e).save():r});e.Worker=t.default,a["default"]=e}(),a=a.default,a}()}))}).call(this,n("c8ba"),n("4362"))}}]);