(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-8af99bda"],{"575ed":function(t,e,i){"use strict";i("6f9e")},"6f9e":function(t,e,i){},f5fc:function(t,e,i){"use strict";i.r(e);var n=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",[i("el-button",{attrs:{type:"primary"},on:{click:t.show}},[t._v(" 查看模板json ")]),i("el-dialog",{attrs:{maskClosable:!1,visible:t.visible},on:{cancel:t.hideModal}},[i("div",{directives:[{name:"loading",rawName:"v-loading",value:t.spinning,expression:"spinning"}],staticStyle:{"min-height":"100px"}},[i("el-input",{staticStyle:{width:"100%",height:"100%"},attrs:{type:"textarea"},model:{value:t.jsonOut,callback:function(e){t.jsonOut=e},expression:"jsonOut"}})],1),i("template",{slot:"title"},[i("el-row",{attrs:{gutter:0}},[i("div",{staticStyle:{"margin-right":"20px"}},[t._v("JSON")]),i("el-col",{attrs:{span:12}},[i("el-switch",{attrs:{"active-text":"tid模式","inactive-text":"默认"},on:{change:t.onModeChange},model:{value:t.tidMode,callback:function(e){t.tidMode=e},expression:"tidMode"}})],1),i("el-col",{attrs:{span:12}},[i("el-switch",{attrs:{"active-text":"美化","inactive-textn":"压缩"},on:{change:t.onModeChange},model:{value:t.beautify,callback:function(e){t.beautify=e},expression:"beautify"}})],1)],1)],1),i("template",{slot:"footer"},[i("el-button",{key:"close",attrs:{type:"info"},on:{click:t.hideModal}},[t._v(" 关闭 ")])],1)],2)],1)},a=[],o=(i("e9c4"),{name:"JSONView",props:{template:{type:Object}},data:function(){return{visible:!1,spinning:!0,jsonOut:"",tidMode:!1,beautify:!1}},computed:{},watch:{},created:function(){},mounted:function(){},methods:{hideModal:function(){this.visible=!1},show:function(){var t=this;this.visible=!0,this.spinning=!0,setTimeout((function(){var e=t.tidMode?t.template.getJsonTid():t.template.getJson(),i=t.beautify?2:0;t.jsonOut=JSON.stringify(e,null,i),t.spinning=!1}),500)},onModeChange:function(){this.show()}}}),s=o,l=(i("575ed"),i("2877")),c=Object(l["a"])(s,n,a,!1,null,"acb6af0c",null);e["default"]=c.exports}}]);