{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\document\\op.vue?vue&type=template&id=a26d2e56&scoped=true&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\document\\op.vue", "mtime": 1750732210908}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}