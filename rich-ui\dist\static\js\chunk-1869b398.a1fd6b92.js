(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-1869b398"],{"5eaa":function(e,t,a){"use strict";a.d(t,"d",(function(){return s})),a.d(t,"e",(function(){return n})),a.d(t,"c",(function(){return r})),a.d(t,"a",(function(){return o})),a.d(t,"j",(function(){return i})),a.d(t,"b",(function(){return c})),a.d(t,"h",(function(){return u})),a.d(t,"i",(function(){return p})),a.d(t,"f",(function(){return m})),a.d(t,"g",(function(){return d}));var l=a("b775");function s(e){return Object(l["a"])({url:"/system/booking/list",method:"get",params:e})}function n(e){return Object(l["a"])({url:"/system/booking/psalist",method:"get",params:e})}function r(e){return Object(l["a"])({url:"/system/booking/"+e,method:"get"})}function o(e){return Object(l["a"])({url:"/system/booking",method:"post",data:e})}function i(e){return Object(l["a"])({url:"/system/booking",method:"put",data:e})}function c(e){return Object(l["a"])({url:"/system/booking/"+e,method:"delete"})}function u(e){return Object(l["a"])({url:"/system/booking/saveBookingLogistics",method:"post",data:e})}function p(e){return Object(l["a"])({url:"/system/booking/saveBookingPreCarriage",method:"post",data:e})}function m(e){return Object(l["a"])({url:"/system/booking/saveBookingExportDeclaration",method:"post",data:e})}function d(e){return Object(l["a"])({url:"/system/booking/saveBookingImportClearance",method:"post",data:e})}},b86e:function(e,t,a){"use strict";a.r(t);var l=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:e.showLeft}},[a("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],ref:"queryForm",staticClass:"query",attrs:{inline:!0,model:e.queryParams,size:"mini"}},[a("el-form-item",{attrs:{label:"业务",prop:"salesId"}},[a("treeselect",{attrs:{"disable-branch-nodes":!0,"disabled-fuzzy-matching":!0,"flatten-search-results":!0,normalizer:e.staffNormalizer,options:e.belongList,"show-count":!0,placeholder:"业务员"},on:{input:function(t){void 0==t&&(e.queryParams.salesId=null)},open:e.loadSales,select:function(t){e.queryParams.salesId=t.staffId}},scopedSlots:e._u([{key:"value-label",fn:function(t){var l=t.node;return a("div",{},[e._v(" "+e._s(void 0!=l.raw.staff?l.raw.staff.staffFamilyLocalName+l.raw.staff.staffGivingLocalName+" "+l.raw.staff.staffGivingEnName:"")+" ")])}},{key:"option-label",fn:function(t){var l=t.node,s=t.shouldShowCount,n=t.count,r=t.labelClassName,o=t.countClassName;return a("label",{class:r},[e._v(" "+e._s(-1!=l.label.indexOf(",")?l.label.substring(0,l.label.indexOf(",")):l.label)+" "),s?a("span",{class:o},[e._v("("+e._s(n)+")")]):e._e()])}}]),model:{value:e.salesId,callback:function(t){e.salesId=t},expression:"salesId"}})],1),a("el-form-item",{attrs:{label:"助理",prop:"salesAssistantId"}},[a("treeselect",{attrs:{"disable-branch-nodes":!0,"disabled-fuzzy-matching":!0,"flatten-search-results":!0,normalizer:e.staffNormalizer,options:e.belongList,"show-count":!0,placeholder:"业务助理"},on:{input:function(t){void 0==t&&(e.queryParams.salesAssistantId=null)},open:e.loadSales,select:function(t){e.queryParams.salesAssistantId=t.staffId}},scopedSlots:e._u([{key:"value-label",fn:function(t){var l=t.node;return a("div",{},[e._v(" "+e._s(void 0!=l.raw.staff?l.raw.staff.staffFamilyLocalName+l.raw.staff.staffGivingLocalName+" "+l.raw.staff.staffGivingEnName:"")+" ")])}},{key:"option-label",fn:function(t){var l=t.node,s=t.shouldShowCount,n=t.count,r=t.labelClassName,o=t.countClassName;return a("label",{class:r},[e._v(" "+e._s(-1!=l.label.indexOf(",")?l.label.substring(0,l.label.indexOf(",")):l.label)+" "),s?a("span",{class:o},[e._v("("+e._s(n)+")")]):e._e()])}}]),model:{value:e.salesAssistantId,callback:function(t){e.salesAssistantId=t},expression:"salesAssistantId"}})],1),a("el-form-item",{attrs:{label:"标记",prop:"isPsaVerified"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{clearable:"",placeholder:"商务审核标记"},model:{value:e.queryParams.isPsaVerified,callback:function(t){e.$set(e.queryParams,"isPsaVerified",t)},expression:"queryParams.isPsaVerified"}},[a("el-option",{attrs:{label:"未审",value:"0"}},[e._v("未审")]),a("el-option",{attrs:{label:"已审",value:"1"}},[e._v("已审")])],1)],1),a("el-form-item",{attrs:{label:"商务",prop:"verifyPsaId"}},[a("treeselect",{attrs:{"disable-branch-nodes":!0,"disabled-fuzzy-matching":!0,"flatten-search-results":!0,normalizer:e.staffNormalizer,options:e.businessList,"show-count":!0,placeholder:"商务"},on:{input:function(t){void 0==t&&(e.queryParams.verifyPsaId=null)},open:e.loadBusinesses,select:function(t){e.queryParams.verifyPsaId=t.staffId}},scopedSlots:e._u([{key:"value-label",fn:function(t){var l=t.node;return a("div",{},[e._v(" "+e._s(void 0!=l.raw.staff?l.raw.staff.staffFamilyLocalName+l.raw.staff.staffGivingLocalName+" "+l.raw.staff.staffGivingEnName:"")+" ")])}},{key:"option-label",fn:function(t){var l=t.node,s=t.shouldShowCount,n=t.count,r=t.labelClassName,o=t.countClassName;return a("label",{class:r},[e._v(" "+e._s(-1!=l.label.indexOf(",")?l.label.substring(0,l.label.indexOf(",")):l.label)+" "),s?a("span",{class:o},[e._v("("+e._s(n)+")")]):e._e()])}}]),model:{value:e.verifyPsaId,callback:function(t){e.verifyPsaId=t},expression:"verifyPsaId"}})],1),a("el-form-item",{attrs:{label:"时间",prop:"pasVerifyTime"}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{clearable:"",placeholder:"商务审核时间","default-time":["00:00:00","23:59:59"],type:"daterange","value-format":"yyyy-MM-dd HH:mm:ss"},model:{value:e.queryParams.pasVerifyTime,callback:function(t){e.$set(e.queryParams,"pasVerifyTime",t)},expression:"queryParams.pasVerifyTime"}})],1),a("el-form-item",{attrs:{label:"标记",prop:"opAllocation"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{clearable:"",placeholder:"操作分配标记"},model:{value:e.queryParams.opAllocation,callback:function(t){e.$set(e.queryParams,"opAllocation",t)},expression:"queryParams.opAllocation"}},[a("el-option",{attrs:{label:"未分配",value:"0"}},[e._v("未分配")]),a("el-option",{attrs:{label:"已分配",value:"1"}},[e._v("已分配")])],1)],1),a("el-form-item",{attrs:{label:"操作",prop:"opId"}},[a("treeselect",{attrs:{"disable-branch-nodes":!0,"disabled-fuzzy-matching":!0,"flatten-search-results":!0,normalizer:e.staffNormalizer,options:e.opList.filter((function(e){return"操作员"==e.role.roleLocalName})),"show-count":!0,placeholder:"操作员"},on:{input:function(t){void 0==t&&(e.queryParams.opId=null)},open:e.loadOp,select:function(t){e.queryParams.opId=t.staffId}},scopedSlots:e._u([{key:"value-label",fn:function(t){var l=t.node;return a("div",{},[e._v(" "+e._s(void 0!=l.raw.staff?l.raw.staff.staffFamilyLocalName+l.raw.staff.staffGivingLocalName+" "+l.raw.staff.staffGivingEnName:"")+" ")])}},{key:"option-label",fn:function(t){var l=t.node,s=t.shouldShowCount,n=t.count,r=t.labelClassName,o=t.countClassName;return a("label",{class:r},[e._v(" "+e._s(-1!=l.label.indexOf(",")?l.label.substring(0,l.label.indexOf(",")):l.label)+" "),s?a("span",{class:o},[e._v("("+e._s(n)+")")]):e._e()])}}]),model:{value:e.opId,callback:function(t){e.opId=t},expression:"opId"}})],1),a("el-form-item",{attrs:{label:"单号",prop:"rctNo"}},[a("el-input",{attrs:{placeholder:"操作单号"},model:{value:e.queryParams.rctNo,callback:function(t){e.$set(e.queryParams,"rctNo",t)},expression:"queryParams.rctNo"}})],1),a("el-form-item",{attrs:{label:"日期",prop:"rctOperationDate"}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{clearable:"",placeholder:"操作日期",type:"daterange","default-time":["00:00:00","23:59:59"],"value-format":"yyyy-MM-dd HH:mm:ss"},model:{value:e.queryParams.rctOperationDate,callback:function(t){e.$set(e.queryParams,"rctOperationDate",t)},expression:"queryParams.rctOperationDate"}})],1),a("el-form-item",{attrs:{label:"紧急",prop:"urgencyDegree"}},[a("el-input",{attrs:{placeholder:"紧急程度"},model:{value:e.queryParams.urgencyDegree,callback:function(t){e.$set(e.queryParams,"urgencyDegree",t)},expression:"queryParams.urgencyDegree"}})],1),a("el-form-item",{attrs:{label:"放货",prop:"releaseTypeId"}},[a("tree-select",{attrs:{"d-load":!0,flat:!1,multiple:!1,pass:e.queryParams.releaseTypeId,placeholder:"放货方式",type:"releaseType"},on:{return:function(t){e.queryParams.releaseTypeId=t}}})],1),a("el-form-item",{attrs:{label:"进度",prop:"processStatusId"}},[a("tree-select",{attrs:{"d-load":!0,flat:!1,multiple:!1,pass:e.queryParams.processStatusId,placeholder:"进度状态",type:"processStatus"},on:{return:function(t){e.queryParams.processStatusId=t}}})],1),a("el-form-item",{attrs:{label:"客户",prop:"clientId"}},[a("tree-select",{attrs:{"d-load":!0,flat:!1,multiple:!1,pass:e.queryParams.clientId,placeholder:"委托单位",type:"client"},on:{return:function(t){e.queryParams.clientId=t}}})],1),a("el-form-item",{attrs:{label:"物流",prop:"logisticsTypeId"}},[a("tree-select",{attrs:{"d-load":!0,flat:!1,multiple:!1,pass:e.queryParams.logisticsTypeId,placeholder:"物流类型",type:"serviceType"},on:{return:function(t){e.queryParams.logisticsTypeId=t}}})],1),a("el-form-item",{attrs:{label:"启运",prop:"polIds"}},[a("location-select",{attrs:{multiple:!0,"no-parent":!0,pass:e.queryParams.polIds,placeholder:"启运港"},on:{return:function(t){e.queryParams.polIds=t}}})],1),a("el-form-item",{attrs:{label:"目的",prop:"destinationPortIds"}},[a("location-select",{attrs:{en:!0,multiple:!0,pass:e.queryParams.destinationPortIds,placeholder:"目的港"},on:{return:function(t){e.queryParams.destinationPortIds=t}}})],1),a("el-form-item",{attrs:{label:"航线",prop:"destinationLineIds"}},[a("tree-select",{staticStyle:{width:"100%"},attrs:{"d-load":!0,flat:!1,multiple:!0,pass:e.queryParams.destinationLineIds,placeholder:"目的航线",type:"line"},on:{return:function(t){e.queryParams.destinationLineIds=t}}})],1),a("el-form-item",{attrs:{label:"货量",prop:"revenueTons"}},[a("el-input",{staticStyle:{width:"100%"},attrs:{placeholder:"计费货量"},model:{value:e.queryParams.revenueTons,callback:function(t){e.$set(e.queryParams,"revenueTons",t)},expression:"queryParams.revenueTons"}})],1),a("el-form-item",[a("el-button",{attrs:{icon:"el-icon-search",size:"mini",type:"primary"},on:{click:e.handleQuery}},[e._v("搜索")]),a("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1)],1),a("el-col",{attrs:{span:e.showRight}},[a("el-row",{staticClass:"mb8",attrs:{gutter:10}},[a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:booking:add"],expression:"['system:booking:add']"}],attrs:{icon:"el-icon-plus",plain:"",size:"mini",type:"primary"},on:{click:e.handleAdd}},[e._v("新增 ")])],1),a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:booking:remove"],expression:"['system:booking:remove']"}],attrs:{disabled:e.multiple,icon:"el-icon-delete",plain:"",size:"mini",type:"danger"},on:{click:e.handleDelete}},[e._v("删除 ")])],1),a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:booking:export"],expression:"['system:booking:export']"}],attrs:{icon:"el-icon-download",plain:"",size:"mini",type:"warning"},on:{click:e.handleExport}},[e._v("导出 ")])],1),a("el-col",{attrs:{span:1.5}},["psa"==e.type?a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:booking:psaVerify"],expression:"['system:booking:psaVerify']"}],attrs:{disabled:e.single,icon:"el-icon-download",plain:"",size:"mini",type:"primary"},on:{click:e.handleVerify}},[e._v("商务审核 ")]):e._e()],1),a("right-toolbar",{attrs:{showSearch:e.showSearch},on:{"update:showSearch":function(t){e.showSearch=t},"update:show-search":function(t){e.showSearch=t},queryTable:e.getList}})],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.bookingList},on:{"selection-change":e.handleSelectionChange,"cell-dblclick":e.gotoRct}},[a("el-table-column",{attrs:{align:"center",type:"selection",width:"28"}}),a("el-table-column",{attrs:{align:"center",label:"物流类型",prop:"logisticsTypeName"}}),a("el-table-column",{attrs:{align:"center",label:"服务类型",prop:"serviceTypes","show-overflow-tooltip":""}}),a("el-table-column",{attrs:{align:"center",label:"进度状态",prop:"processStatusName"}}),a("el-table-column",{attrs:{align:"center",label:"委托单位",prop:"clientName"}}),a("el-table-column",{attrs:{align:"center",label:"商务审核",prop:"psaName"}}),a("el-table-column",{attrs:{align:"center",label:"商务审核状态",prop:"isPsaVerified"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(0==t.row.isPsaVerified?"未审核":""))]),a("span",[e._v(e._s(1==t.row.isPsaVerified?"已审核":""))])]}}])}),a("el-table-column",{attrs:{align:"center",label:"商务审核时间",prop:"psaVerifyTime",width:"180"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(e.parseTime(t.row.psaVerifyTime,"{y}-{m}-{d}")))])]}}])}),a("el-table-column",{attrs:{align:"center",label:"操作员",prop:"opName"}}),a("el-table-column",{attrs:{align:"center",label:"业务员",prop:"salesName"}}),a("el-table-column",{attrs:{align:"center",label:"业务助理",prop:"salesAssistantName"}}),a("el-table-column",{attrs:{align:"center",label:"启运港",prop:"pol"}}),a("el-table-column",{attrs:{align:"center",label:"目的港",prop:"destinationPort"}}),a("el-table-column",{attrs:{align:"center",label:"计费货量",prop:"revenueTons","show-overflow-tooltip":""}}),a("el-table-column",{attrs:{align:"center",label:"订舱单号",prop:"newBookingNo","show-overflow-tooltip":""}}),a("el-table-column",{attrs:{align:"center",label:"订舱日期",prop:"newBookingTime",width:"180"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(e.parseTime(t.row.newBookingTime,"{y}-{m}-{d}")))])]}}])}),a("el-table-column",{attrs:{align:"center",label:"报价单号",prop:"quotationNo","show-overflow-tooltip":""}}),a("el-table-column",{attrs:{align:"center",label:"报价日期",prop:"quotationDate",width:"180"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(e.parseTime(t.row.quotationDate,"{y}-{m}-{d}")))])]}}])}),a("el-table-column",{attrs:{align:"center",label:"协助业务",prop:"salesObserverName"}}),a("el-table-column",{attrs:{align:"center",label:"紧急程度",prop:"urgencyDegree"}}),a("el-table-column",{attrs:{align:"center",label:"收付方式",prop:"paymentTypeName"}}),a("el-table-column",{attrs:{align:"center",label:"放货方式",prop:"releaseTypeName"}}),a("el-table-column",{attrs:{align:"center",label:"客户角色",prop:"clientRoleName"}}),a("el-table-column",{attrs:{align:"center",label:"联系人",prop:"clientContactor"}}),a("el-table-column",{attrs:{align:"center",label:"联系人电话",prop:"clientContactorTel"}}),a("el-table-column",{attrs:{align:"center",label:"邮箱",prop:"clientContactorEmail"}}),a("el-table-column",{attrs:{align:"center",label:"关联单位",prop:"relationClientIds"}}),a("el-table-column",{attrs:{align:"center",label:"进出口",prop:"impExpTypeId"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(1==t.row.impExpTypeId?"出口":"")+" "+e._s(2==t.row.impExpTypeId?"进口":"")+" ")]}}])}),a("el-table-column",{attrs:{align:"center",label:"收汇方式",prop:"tradingPaymentChannelName"}}),a("el-table-column",{attrs:{align:"center",label:"贸易条款",prop:"tradingTermsName"}}),a("el-table-column",{attrs:{align:"center",label:"运输条款",prop:"logisticsTermsName"}}),a("el-table-column",{attrs:{align:"center",label:"合同号",prop:"contractNo"}}),a("el-table-column",{attrs:{align:"center",label:"发票号",prop:"invoiceNo"}}),a("el-table-column",{attrs:{align:"center",label:"货名概要",prop:"goodsNameSummary"}}),a("el-table-column",{attrs:{align:"center",label:"件数",prop:"packageQuantity"}}),a("el-table-column",{attrs:{align:"center",label:"毛重",prop:"grossWeight"}}),a("el-table-column",{attrs:{align:"center",label:"重量单位",prop:"weightUnitName"}}),a("el-table-column",{attrs:{align:"center",label:"总体积",prop:"volume"}}),a("el-table-column",{attrs:{align:"center",label:"体积单位",prop:"volumeUnitName"}}),a("el-table-column",{attrs:{align:"center",label:"货物特征",prop:"cargoTypes"}}),a("el-table-column",{attrs:{align:"center",label:"总货值",prop:"goodsValue"}}),a("el-table-column",{attrs:{align:"center",label:"货值币种",prop:"goodsCurrencyName"}}),a("el-table-column",{attrs:{align:"center",label:"货物限重",prop:"maxWeight"}}),a("el-table-column",{attrs:{align:"center",label:"承运人",prop:"carriers"}}),a("el-table-column",{attrs:{align:"center",label:"船期",prop:"schedule"}}),a("el-table-column",{attrs:{align:"center",label:"有效期",prop:"validDate"}}),a("el-table-column",{attrs:{align:"center",label:"主提单",prop:"isMblNeeded"}}),a("el-table-column",{attrs:{align:"center",label:"主提单号",prop:"mblNo"}}),a("el-table-column",{attrs:{align:"center",label:"套约",prop:"isUnderAgreementMbl"}}),a("el-table-column",{attrs:{align:"center",label:"清关中转",prop:"isCustomsIntransitMbl"}}),a("el-table-column",{attrs:{align:"center",label:"转单",prop:"isSwitchMbl"}}),a("el-table-column",{attrs:{align:"center",label:"拆单",prop:"isDividedMbl"}}),a("el-table-column",{attrs:{align:"center",label:"出单方式",prop:"mblIssueTypeName"}}),a("el-table-column",{attrs:{align:"center",label:"取单方式",prop:"mblGetWayName"}}),a("el-table-column",{attrs:{align:"center",label:"交单方式",prop:"mblReleaseWayName"}}),a("el-table-column",{attrs:{align:"center",label:"货代提单",prop:"isHblNeeded"}}),a("el-table-column",{attrs:{align:"center",label:"货代单号",prop:"hblNoList"}}),a("el-table-column",{attrs:{align:"center",label:"套约",prop:"isUnderAgreementHbl"}}),a("el-table-column",{attrs:{align:"center",label:"清关中转",prop:"isCustomsIntransitHbl"}}),a("el-table-column",{attrs:{align:"center",label:"转单",prop:"isSwitchHbl"}}),a("el-table-column",{attrs:{align:"center",label:"拆单",prop:"isDividedHbl"}}),a("el-table-column",{attrs:{align:"center",label:"出单方式",prop:"hblIssueTypeName"}}),a("el-table-column",{attrs:{align:"center",label:"取单方式",prop:"hblGetWayName"}}),a("el-table-column",{attrs:{align:"center",label:"交单方式",prop:"hblReleaseWayName"}}),a("el-table-column",{attrs:{align:"center",label:"业务报价综述",prop:"quotationSummary","show-overflow-tooltip":""}}),a("el-table-column",{attrs:{align:"center",label:"业务订舱备注",prop:"newBookingRemark"}}),a("el-table-column",{attrs:{align:"center",label:"业务须知",prop:"inquiryNotice","show-overflow-tooltip":""}}),a("el-table-column",{attrs:{align:"center",label:"商务备注",prop:"inquiryInnerRemark"}}),a("el-table-column",{attrs:{align:"center",label:"操作主管备注",prop:"opLeaderRemark"}}),a("el-table-column",{attrs:{align:"center",label:"操作备注",prop:"opInnerRemark"}}),a("el-table-column",{attrs:{align:"center",label:"合约类型",prop:"agreementTypeId"}}),a("el-table-column",{attrs:{align:"center",label:"合约号",prop:"agreementNo"}}),a("el-table-column",{attrs:{align:"center","class-name":"small-padding fixed-width",label:"操作",width:"100px"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:booking:edit"],expression:"['system:booking:edit']"}],staticStyle:{"margin-right":"-8px"},attrs:{icon:"el-icon-edit",size:"mini",type:"success"},on:{click:function(a){return e.handleUpdate(t.row)}}},[e._v("修改 ")]),a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:booking:remove"],expression:"['system:booking:remove']"}],staticStyle:{"margin-right":"-8px"},attrs:{icon:"el-icon-delete",size:"mini",type:"danger"},on:{click:function(a){return e.handleDelete(t.row)}}},[e._v("删除 ")])]}}])})],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total>0"}],attrs:{limit:e.queryParams.pageSize,page:e.queryParams.pageNum,total:e.total},on:{"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},pagination:e.getList}})],1)],1)],1)},s=[],n=a("5530"),r=a("c7eb"),o=a("1da1"),i=(a("d81d"),a("5eaa")),c=a("b0b8"),u=a.n(c),p=a("4360"),m=a("ca17"),d=a.n(m),b=(a("6f8d"),{name:"Booking",components:{Treeselect:d.a},props:["type"],data:function(){return{showLeft:3,showRight:21,loading:!0,ids:[],single:!0,multiple:!0,showSearch:!0,total:0,salesId:null,verifyPsaId:null,salesAssistantId:null,opId:null,bookingList:[],belongList:[],opList:[],businessList:[],queryParams:{pageNum:1,pageSize:20},form:{},rules:{}}},watch:{showSearch:function(e){!0===e?(this.showRight=21,this.showLeft=3):(this.showRight=24,this.showLeft=0)},"queryParams.pasVerifyTime":function(e){this.queryParams.pasVerifyFrom=e[0],this.queryParams.pasVerifyTo=e[1]},"queryParams.rctOperationDate":function(e){this.queryParams.rctOperationFrom=e[0],this.queryParams.rctOperationTo=e[1]}},created:function(){var e=this;this.getList().then((function(){e.loadSales(),e.loadOp(),e.loadBusinesses()}))},methods:{loadSales:function(){var e=this;0==this.$store.state.data.salesList.length||this.$store.state.data.redisList.salesList?p["a"].dispatch("getSalesList").then((function(){e.belongList=e.$store.state.data.salesList})):this.belongList=this.$store.state.data.salesList},loadBusinesses:function(){var e=this;0==this.$store.state.data.businessesList.length||this.$store.state.data.redisList.businessesList?p["a"].dispatch("getBusinessesList").then((function(){e.businessList=e.$store.state.data.businessesList})):this.businessList=this.$store.state.data.businessesList},loadOp:function(){var e=this;0==this.$store.state.data.opList.length||this.$store.state.data.redisList.opList?p["a"].dispatch("getOpList").then((function(){e.opList=e.$store.state.data.opList})):this.opList=this.$store.state.data.opList},getList:function(){var e=this;return Object(o["a"])(Object(r["a"])().mark((function t(){return Object(r["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(e.loading=!0,"booking"!=e.type){t.next=4;break}return t.next=4,Object(i["d"])(e.queryParams).then((function(t){e.bookingList=t.rows,e.total=t.total,e.loading=!1}));case 4:if("psa"!=e.type){t.next=7;break}return t.next=7,Object(i["e"])(e.queryParams).then((function(t){e.bookingList=t.rows,e.total=t.total,e.loading=!1}));case 7:case"end":return t.stop()}}),t)})))()},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.resetForm("queryForm"),this.handleQuery()},handleSelectionChange:function(e){this.ids=e.map((function(e){return e.bookingId})),this.single=1!==e.length,this.multiple=!e.length},handleAdd:function(){this.$tab.openPage("订舱申请单","/salesquotation/bookingdetail",{})},handleVerify:function(){this.$tab.openPage("订舱申请单","/salesquotation/bookingdetail",{bId:this.ids,psaVerify:!0})},handleUpdate:function(e){this.$tab.openPage("订舱申请单","/salesquotation/bookingdetail",{bId:e.bookingId})},handleDelete:function(e){var t=this,a=e.bookingId||this.ids;this.$confirm('是否确认删除订舱单列表编号为"'+a+'"的数据项？',"提示",{customClass:"modal-confirm"}).then((function(){return Object(i["b"])(a)})).then((function(){t.getList(),t.$modal.msgSuccess("删除成功")})).catch((function(){}))},handleExport:function(){this.download("system/booking/export",Object(n["a"])({},this.queryParams),"booking_".concat((new Date).getTime(),".xlsx"))},staffNormalizer:function(e){var t;return e.children&&!e.children.length&&delete e.children,e.staff&&(t=null==e.staff.staffFamilyLocalName&&null==e.staff.staffGivingLocalName?null!=e.role.roleLocalName?e.role.roleLocalName+","+u.a.getFullChars(e.role.roleLocalName):e.dept.deptLocalName+","+u.a.getFullChars(e.dept.deptLocalName):e.staff.staffCode+" "+e.staff.staffFamilyLocalName+e.staff.staffGivingLocalName+" "+e.staff.staffGivingEnName+","+u.a.getFullChars(e.staff.staffFamilyLocalName+e.staff.staffGivingLocalName)),e.roleId?{id:e.roleId,label:t,children:e.children,isDisabled:null==e.staffId&&void 0==e.children}:{id:e.deptId,label:t,children:e.children,isDisabled:null==e.staffId&&void 0==e.children}},gotoRct:function(e,t,a,l){null!=e.newBookingNo?this.$tab.openPage("操作单列表","/opprocess/rct",{no:e.newBookingNo}):this.$message.error("请补全订舱申请单号")}}}),f=b,g=a("2877"),h=Object(g["a"])(f,l,s,!1,null,null,null);t["default"]=h.exports}}]);