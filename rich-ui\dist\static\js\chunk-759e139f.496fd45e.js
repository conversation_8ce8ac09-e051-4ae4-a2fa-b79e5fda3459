(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-759e139f"],{"0b95":function(e,t,i){},9429:function(e,t,i){"use strict";i.r(t);var o=function(){var e=this,t=e.$createElement,i=e._self._c||t;return i("div",[i("div",{staticClass:"user-info-head",on:{click:function(t){return e.editCropper()}}},[i("img",{staticClass:"img-circle img-lg",attrs:{title:"点击上传头像",src:e.options.img}})]),i("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"},{name:"dialogDragWidth",rawName:"v-dialogDragWidth"}],attrs:{"close-on-click-modal":!1,"modal-append-to-body":!1,title:e.title,visible:e.open,"append-to-body":"",width:"800px"},on:{"update:visible":function(t){e.open=t},close:e.closeDialog,opened:e.modalOpened}},[i("el-row",[i("el-col",{style:{height:"350px"},attrs:{md:12,xs:24}},[e.visible?i("vue-cropper",{ref:"cropper",attrs:{autoCrop:e.options.autoCrop,autoCropHeight:e.options.autoCropHeight,autoCropWidth:e.options.autoCropWidth,fixedBox:e.options.fixedBox,img:e.options.img,info:!0,outputType:e.options.outputType},on:{realTime:e.realTime}}):e._e()],1),i("el-col",{style:{height:"350px"},attrs:{md:12,xs:24}},[i("div",{staticClass:"avatar-upload-preview"},[i("img",{style:e.previews.img,attrs:{src:e.previews.url}})])])],1),i("br"),i("el-row",[i("el-col",{attrs:{lg:2,sm:3,xs:3}},[i("el-upload",{attrs:{"before-upload":e.beforeUpload,"http-request":e.requestUpload,"show-file-list":!1,action:"#"}},[i("el-button",{attrs:{size:"mini"}},[e._v(" 选择 "),i("i",{staticClass:"el-icon-upload el-icon--right"})])],1)],1),i("el-col",{attrs:{lg:{span:1,offset:2},sm:2,xs:2}},[i("el-button",{attrs:{icon:"el-icon-plus",size:"mini"},on:{click:function(t){return e.changeScale(1)}}})],1),i("el-col",{attrs:{lg:{span:1,offset:2},sm:2,xs:2}},[i("el-button",{attrs:{icon:"el-icon-minus",size:"mini"},on:{click:function(t){return e.changeScale(-1)}}})],1),i("el-col",{attrs:{lg:{span:1,offset:2},sm:2,xs:2}},[i("el-button",{attrs:{icon:"el-icon-refresh-left",size:"mini"},on:{click:function(t){return e.rotateLeft()}}})],1),i("el-col",{attrs:{lg:{span:1,offset:2},sm:2,xs:2}},[i("el-button",{attrs:{icon:"el-icon-refresh-right",size:"mini"},on:{click:function(t){return e.rotateRight()}}})],1),i("el-col",{attrs:{lg:{span:2,offset:6},sm:2,xs:2}},[i("el-button",{attrs:{size:"mini",type:"primary"},on:{click:function(t){return e.uploadImg()}}},[e._v("提 交")])],1)],1)],1)],1)},r=[],s=i("4360"),n=i("7e79"),a=i("c0c7"),l=i("ed08"),c={components:{VueCropper:n["VueCropper"]},props:{user:{type:Object}},data:function(){return{open:!1,visible:!1,title:"修改头像",options:{img:s["a"].getters.avatar,autoCrop:!0,autoCropWidth:200,autoCropHeight:200,fixedBox:!0,outputType:"png"},previews:{},resizeHandler:null}},methods:{editCropper:function(){this.open=!0},modalOpened:function(){var e=this;this.visible=!0,this.resizeHandler||(this.resizeHandler=Object(l["b"])((function(){e.refresh()}),100)),window.addEventListener("resize",this.resizeHandler)},refresh:function(){this.$refs.cropper.refresh()},requestUpload:function(){},rotateLeft:function(){this.$refs.cropper.rotateLeft()},rotateRight:function(){this.$refs.cropper.rotateRight()},changeScale:function(e){e=e||1,this.$refs.cropper.changeScale(e)},beforeUpload:function(e){var t=this;if(-1==e.type.indexOf("image/"))this.$modal.msgError("文件格式错误，请上传图片类型,如：JPG，PNG后缀的文件。");else{var i=new FileReader;i.readAsDataURL(e),i.onload=function(){t.options.img=i.result}}},uploadImg:function(){var e=this;this.$refs.cropper.getCropBlob((function(t){var i=new FormData;i.append("avatarfile",t),Object(a["n"])(i).then((function(t){e.open=!1,e.options.img="/prod-api"+t.imgUrl,s["a"].commit("SET_AVATAR",e.options.img),e.$modal.msgSuccess("修改成功"),e.visible=!1}))}))},realTime:function(e){this.previews=e},closeDialog:function(){this.options.img=s["a"].getters.avatar,this.visible=!1,window.removeEventListener("resize",this.resizeHandler)}}},p=c,u=(i("bed7"),i("2877")),d=Object(u["a"])(p,o,r,!1,null,"129b2dc5",null);t["default"]=d.exports},bed7:function(e,t,i){"use strict";i("0b95")}}]);