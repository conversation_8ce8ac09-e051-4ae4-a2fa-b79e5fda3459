{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\thread-loader\\dist\\cjs.js!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\outboundRecord\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\outboundRecord\\index.vue", "mtime": 1750840424298}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\babel.config.js", "mtime": 1684722674797}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}