(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-3843914b"],{"463b":function(t,o,e){},"7f41":function(t,o,e){"use strict";e("463b")},efbd:function(t,o,e){"use strict";e.r(o);var n=function(){var t=this,o=t.$createElement,e=t._self._c||o;return e("div",[e("el-tooltip",{attrs:{disabled:null==t.scope.row.company||t.scope.row.company.length<3||((null!=t.scope.row.contractType?t.scope.row.contractType:"")+(null!=t.scope.row.contractType&&null!=t.scope.row.contractNo?"：":"")+(null!=t.scope.row.contractNo?t.scope.row.contractNo:"")).length<10,placement:"top"}},[e("div",{attrs:{slot:"content"},slot:"content"},[e("div",{staticStyle:{margin:"0","font-size":"15px","font-weight":"bold"}},[t._v(t._s(t.scope.row.carrierCode)+" "),e("span",{staticClass:"unHighlight-text",staticStyle:{margin:"0","font-weight":"bold","font-size":"12px"}},[t._v(" "+t._s("("+(null!=t.scope.row.contractType?t.scope.row.contractType:"")+")")+" ")])]),e("h6",{staticClass:"unHighlight-text",staticStyle:{margin:"0"}},[t._v(" "+t._s(t.scope.row.company)+" ")])]),e("div",{staticStyle:{margin:"0",overflow:"hidden","text-overflow":"ellipsis","white-space":"nowrap"}},[e("div",{staticStyle:{margin:"0","font-size":"15px","font-weight":"bold"}},[t._v(t._s(t.scope.row.carrierCode)+" "),e("span",{staticClass:"unHighlight-text",staticStyle:{margin:"0","font-weight":"bold","font-size":"12px"}},[t._v(" "+t._s("("+(null!=t.scope.row.contractType?t.scope.row.contractType:"")+")")+" ")])]),e("h6",{staticClass:"unHighlight-text",staticStyle:{margin:"0",overflow:"hidden","text-overflow":"ellipsis","white-space":"nowrap"}},[t._v(" "+t._s(t.scope.row.company)+" ")])])])],1)},s=[],c={name:"carrier",props:["scope"],data:function(){return{size:this.$store.state.app.size||"mini"}}},i=c,r=(e("7f41"),e("2877")),a=Object(r["a"])(i,n,s,!1,null,"36a6b789",null);o["default"]=a.exports}}]);