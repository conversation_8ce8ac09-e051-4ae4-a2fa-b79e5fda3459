(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-6370ac86"],{"258a":function(e,t,o){"use strict";o.r(t);var n=function(){var e=this,t=e.$createElement,o=e._self._c||t;return o("div",{staticClass:"app-container"},[o("el-row",{attrs:{gutter:20}},[o("el-col",{attrs:{span:e.showLeft}},[o("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],ref:"queryForm",staticClass:"query",attrs:{inline:!0,model:e.queryParams,"label-width":"68px",size:"mini"}},[o("el-form-item",{attrs:{label:"出仓单号",prop:"outboundNo"}},[o("el-input",{attrs:{clearable:"",placeholder:"出仓单号"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.outboundNo,callback:function(t){e.$set(e.queryParams,"outboundNo",t)},expression:"queryParams.outboundNo"}})],1),o("el-form-item",{attrs:{label:"客户代码",prop:"clientCode"}},[o("el-input",{attrs:{clearable:"",placeholder:"客户代码"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.clientCode,callback:function(t){e.$set(e.queryParams,"clientCode",t)},expression:"queryParams.clientCode"}})],1),o("el-form-item",{attrs:{label:"客户名称",prop:"clientName"}},[o("el-input",{attrs:{clearable:"",placeholder:"客户名称"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.clientName,callback:function(t){e.$set(e.queryParams,"clientName",t)},expression:"queryParams.clientName"}})],1),o("el-form-item",{attrs:{label:"操作员",prop:"operator"}},[o("el-input",{attrs:{clearable:"",placeholder:"操作员"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.operator,callback:function(t){e.$set(e.queryParams,"operator",t)},expression:"queryParams.operator"}})],1),o("el-form-item",{attrs:{label:"柜号",prop:"containerNo"}},[o("el-input",{attrs:{clearable:"",placeholder:"柜号"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.containerNo,callback:function(t){e.$set(e.queryParams,"containerNo",t)},expression:"queryParams.containerNo"}})],1),o("el-form-item",{attrs:{label:"封号",prop:"sealNo"}},[o("el-input",{attrs:{clearable:"",placeholder:"封号"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.sealNo,callback:function(t){e.$set(e.queryParams,"sealNo",t)},expression:"queryParams.sealNo"}})],1),o("el-form-item",{attrs:{label:"出仓日期",prop:"outboundDate"}},[o("el-date-picker",{attrs:{clearable:"",placeholder:"出仓日期",type:"date","value-format":"yyyy-MM-dd"},model:{value:e.queryParams.outboundDate,callback:function(t){e.$set(e.queryParams,"outboundDate",t)},expression:"queryParams.outboundDate"}})],1),o("el-form-item",{attrs:{label:"仓库报价",prop:"warehouseQuote"}},[o("el-input",{attrs:{clearable:"",placeholder:"仓库报价"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.warehouseQuote,callback:function(t){e.$set(e.queryParams,"warehouseQuote",t)},expression:"queryParams.warehouseQuote"}})],1),o("el-form-item",{attrs:{label:"工人装柜费",prop:"workerLoadingFee"}},[o("el-input",{attrs:{clearable:"",placeholder:"工人装柜费"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.workerLoadingFee,callback:function(t){e.$set(e.queryParams,"workerLoadingFee",t)},expression:"queryParams.workerLoadingFee"}})],1),o("el-form-item",{attrs:{label:"仓管代收",prop:"warehouseCollection"}},[o("el-input",{attrs:{clearable:"",placeholder:"仓管代收"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.warehouseCollection,callback:function(t){e.$set(e.queryParams,"warehouseCollection",t)},expression:"queryParams.warehouseCollection"}})],1),o("el-form-item",{attrs:{label:"总箱数",prop:"totalBoxes"}},[o("el-input",{attrs:{clearable:"",placeholder:"总箱数"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.totalBoxes,callback:function(t){e.$set(e.queryParams,"totalBoxes",t)},expression:"queryParams.totalBoxes"}})],1),o("el-form-item",{attrs:{label:"总毛重",prop:"totalGrossWeight"}},[o("el-input",{attrs:{clearable:"",placeholder:"总毛重"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.totalGrossWeight,callback:function(t){e.$set(e.queryParams,"totalGrossWeight",t)},expression:"queryParams.totalGrossWeight"}})],1),o("el-form-item",{attrs:{label:"总体积",prop:"totalVolume"}},[o("el-input",{attrs:{clearable:"",placeholder:"总体积"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.totalVolume,callback:function(t){e.$set(e.queryParams,"totalVolume",t)},expression:"queryParams.totalVolume"}})],1),o("el-form-item",{attrs:{label:"总行数",prop:"totalRows"}},[o("el-input",{attrs:{clearable:"",placeholder:"总行数"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.totalRows,callback:function(t){e.$set(e.queryParams,"totalRows",t)},expression:"queryParams.totalRows"}})],1),o("el-form-item",{attrs:{label:"已收入仓费",prop:"receivedStorageFee"}},[o("el-input",{attrs:{clearable:"",placeholder:"已收入仓费"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.receivedStorageFee,callback:function(t){e.$set(e.queryParams,"receivedStorageFee",t)},expression:"queryParams.receivedStorageFee"}})],1),o("el-form-item",{attrs:{label:"未收卸货费",prop:"unpaidUnloadingFee"}},[o("el-input",{attrs:{clearable:"",placeholder:"未收卸货费"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.unpaidUnloadingFee,callback:function(t){e.$set(e.queryParams,"unpaidUnloadingFee",t)},expression:"queryParams.unpaidUnloadingFee"}})],1),o("el-form-item",{attrs:{label:"未收打包费",prop:"unpaidPackagingFee"}},[o("el-input",{attrs:{clearable:"",placeholder:"未收打包费"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.unpaidPackagingFee,callback:function(t){e.$set(e.queryParams,"unpaidPackagingFee",t)},expression:"queryParams.unpaidPackagingFee"}})],1),o("el-form-item",{attrs:{label:"物流代垫费",prop:"logisticsAdvanceFee"}},[o("el-input",{attrs:{clearable:"",placeholder:"物流代垫费"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.logisticsAdvanceFee,callback:function(t){e.$set(e.queryParams,"logisticsAdvanceFee",t)},expression:"queryParams.logisticsAdvanceFee"}})],1),o("el-form-item",{attrs:{label:"租金平衡费",prop:"rentalBalanceFee"}},[o("el-input",{attrs:{clearable:"",placeholder:"租金平衡费"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.rentalBalanceFee,callback:function(t){e.$set(e.queryParams,"rentalBalanceFee",t)},expression:"queryParams.rentalBalanceFee"}})],1),o("el-form-item",{attrs:{label:"超期仓租",prop:"overdueRent"}},[o("el-input",{attrs:{clearable:"",placeholder:"超期仓租"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.overdueRent,callback:function(t){e.$set(e.queryParams,"overdueRent",t)},expression:"queryParams.overdueRent"}})],1),o("el-form-item",{attrs:{label:"免堆天数",prop:"freeStackDays"}},[o("el-input",{attrs:{clearable:"",placeholder:"免堆天数"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.freeStackDays,callback:function(t){e.$set(e.queryParams,"freeStackDays",t)},expression:"queryParams.freeStackDays"}})],1),o("el-form-item",{attrs:{label:"超期单价",prop:"overdueUnitPrice"}},[o("el-input",{attrs:{clearable:"",placeholder:"超期单价"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.overdueUnitPrice,callback:function(t){e.$set(e.queryParams,"overdueUnitPrice",t)},expression:"queryParams.overdueUnitPrice"}})],1),o("el-form-item",[o("el-button",{attrs:{icon:"el-icon-search",size:"mini",type:"primary"},on:{click:e.handleQuery}},[e._v("搜索")]),o("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1)],1),o("el-col",{attrs:{span:e.showRight}},[o("el-row",{staticClass:"mb8",attrs:{gutter:10}},[o("right-toolbar",{attrs:{showSearch:e.showSearch},on:{"update:showSearch":function(t){e.showSearch=t},"update:show-search":function(t){e.showSearch=t},queryTable:e.getList}})],1),o("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.outboundrecordList},on:{"selection-change":e.handleSelectionChange,"row-dblclick":e.findOutboundRecord}},[o("el-table-column",{attrs:{align:"center",type:"selection",width:"28"}}),o("el-table-column",{attrs:{align:"center",label:"出仓单号",prop:"outboundNo"}}),o("el-table-column",{attrs:{align:"center",label:"客户代码",prop:"clientCode"}}),o("el-table-column",{attrs:{align:"center",label:"客户名称",prop:"clientName"}}),o("el-table-column",{attrs:{align:"center",label:"操作员",prop:"operator"}}),o("el-table-column",{attrs:{align:"center",label:"柜型",prop:"containerType"}}),o("el-table-column",{attrs:{align:"center",label:"柜号",prop:"containerNo"}}),o("el-table-column",{attrs:{align:"center",label:"封号",prop:"sealNo"}}),o("el-table-column",{attrs:{align:"center",label:"出仓日期",prop:"outboundDate",width:"180"},scopedSlots:e._u([{key:"default",fn:function(t){return[o("span",[e._v(e._s(e.parseTime(t.row.outboundDate,"{y}-{m}-{d}")))])]}}])}),o("el-table-column",{attrs:{align:"center",label:"仓库报价",prop:"warehouseQuote"}}),o("el-table-column",{attrs:{align:"center",label:"工人装柜费",prop:"workerLoadingFee"}}),o("el-table-column",{attrs:{align:"center",label:"仓管代收",prop:"warehouseCollection"}}),o("el-table-column",{attrs:{align:"center",label:"代收备注",prop:"collectionNotes"}}),o("el-table-column",{attrs:{align:"center",label:"总箱数",prop:"totalBoxes"}}),o("el-table-column",{attrs:{align:"center",label:"总毛重",prop:"totalGrossWeight"}}),o("el-table-column",{attrs:{align:"center",label:"总体积",prop:"totalVolume"}}),o("el-table-column",{attrs:{align:"center",label:"总行数",prop:"totalRows"}}),o("el-table-column",{attrs:{align:"center",label:"已收入仓费",prop:"receivedStorageFee"}}),o("el-table-column",{attrs:{align:"center",label:"未收卸货费",prop:"unpaidUnloadingFee"}}),o("el-table-column",{attrs:{align:"center",label:"未收打包费",prop:"unpaidPackagingFee"}}),o("el-table-column",{attrs:{align:"center",label:"物流代垫费",prop:"logisticsAdvanceFee"}}),o("el-table-column",{attrs:{align:"center",label:"租金平衡费",prop:"rentalBalanceFee"}}),o("el-table-column",{attrs:{align:"center",label:"超期仓租",prop:"overdueRentalFee"}}),o("el-table-column",{attrs:{align:"center",label:"免堆天数",prop:"freeStackDays"}}),o("el-table-column",{attrs:{align:"center",label:"超期单价",prop:"overdueUnitPrice"}}),o("el-table-column",{attrs:{align:"center","class-name":"small-padding fixed-width",label:"操作"},scopedSlots:e._u([{key:"default",fn:function(t){return[o("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:outboundrecord:edit"],expression:"['system:outboundrecord:edit']"}],staticStyle:{"margin-right":"-8px"},attrs:{icon:"el-icon-edit",size:"mini",type:"success"},on:{click:function(o){return e.handleUpdate(t.row)}}},[e._v("修改 ")]),o("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:outboundrecord:remove"],expression:"['system:outboundrecord:remove']"}],staticStyle:{"margin-right":"-8px"},attrs:{icon:"el-icon-delete",size:"mini",type:"danger"},on:{click:function(o){return e.handleDelete(t.row)}}},[e._v("删除 ")])]}}])})],1),o("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total>0"}],attrs:{limit:e.queryParams.pageSize,page:e.queryParams.pageNum,total:e.total},on:{"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},pagination:e.getList}})],1)],1),o("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"},{name:"dialogDragWidth",rawName:"v-dialogDragWidth"}],attrs:{"close-on-click-modal":!1,"modal-append-to-body":!1,title:"出库单",visible:e.openOutbound,"append-to-body":"",width:"80%"},on:{"update:visible":function(t){e.openOutbound=t}}},[o("el-form",{ref:"outboundForm",staticClass:"edit",attrs:{model:e.outboundForm,rules:e.rules,"label-width":"80px"}},[o("el-row",{attrs:{gutter:10}},[o("el-col",{attrs:{span:12}},[o("el-row",{attrs:{gutter:10}},[o("el-col",[o("el-col",{attrs:{span:8}},[o("el-form-item",{attrs:{label:"出仓单号",prop:"outboundNo"}},[o("el-input",{attrs:{placeholder:"出仓单号"},model:{value:e.outboundForm.outboundNo,callback:function(t){e.$set(e.outboundForm,"outboundNo",t)},expression:"outboundForm.outboundNo"}})],1)],1),o("el-col",{attrs:{span:8}},[o("el-form-item",{attrs:{label:"计划出仓",prop:"inboundDate"}},[o("el-date-picker",{staticStyle:{width:"100%"},attrs:{clearable:"",placeholder:"计划出仓日期",type:"date","value-format":"yyyy-MM-dd"},model:{value:e.outboundForm.plannedOutboundDate,callback:function(t){e.$set(e.outboundForm,"plannedOutboundDate",t)},expression:"outboundForm.plannedOutboundDate"}})],1)],1),o("el-col",{attrs:{span:8}},[o("el-form-item",{attrs:{label:"操作员",prop:"inboundSerialNo"}},[o("el-input",{attrs:{placeholder:"操作员"},model:{value:e.outboundForm.operator,callback:function(t){e.$set(e.outboundForm,"operator",t)},expression:"outboundForm.operator"}})],1)],1)],1)],1),o("el-row",{attrs:{gutter:10}},[o("el-col",[o("el-col",{attrs:{span:8}},[o("el-form-item",{attrs:{label:"客户信息",prop:"clientCode"}},[o("el-row",[o("el-col",{attrs:{span:12}},[o("tree-select",{attrs:{flat:!1,multiple:!1,pass:e.outboundForm.clientCode,placeholder:"客户代码",type:"warehouseClient"},on:{return:function(t){e.outboundForm.clientCode=t},returnData:function(t){return e.outboundClient(t)}}})],1),o("el-col",{attrs:{span:12}},[o("el-input",{attrs:{placeholder:"客户名称"},model:{value:e.outboundForm.clientName,callback:function(t){e.$set(e.outboundForm,"clientName",t)},expression:"outboundForm.clientName"}})],1)],1)],1)],1),o("el-col",{attrs:{span:16}},[o("el-form-item",{attrs:{label:"柜号",prop:"containerNo"}},[o("el-input",{attrs:{placeholder:"柜号"},model:{value:e.outboundForm.containerNo,callback:function(t){e.$set(e.outboundForm,"containerNo",t)},expression:"outboundForm.containerNo"}})],1)],1)],1)],1),o("el-row",{attrs:{gutter:10}},[o("el-col",[o("el-col",{attrs:{span:8}},[o("el-form-item",{attrs:{label:"出仓方式",prop:"outboundMethod"}},[o("el-input",{attrs:{placeholder:"出仓方式"},model:{value:e.outboundForm.outboundType,callback:function(t){e.$set(e.outboundForm,"outboundType",t)},expression:"outboundForm.outboundType"}})],1)],1),o("el-col",{attrs:{span:8}},[o("el-form-item",{attrs:{label:"柜型",prop:"containerType"}},[o("el-input",{attrs:{placeholder:"柜型"},model:{value:e.outboundForm.containerType,callback:function(t){e.$set(e.outboundForm,"containerType",t)},expression:"outboundForm.containerType"}})],1)],1),o("el-col",{attrs:{span:8}},[o("el-form-item",{attrs:{label:"封号",prop:"sealNo"}},[o("el-input",{attrs:{placeholder:"封号"},model:{value:e.outboundForm.sealNo,callback:function(t){e.$set(e.outboundForm,"sealNo",t)},expression:"outboundForm.sealNo"}})],1)],1)],1)],1),o("el-row",{attrs:{gutter:10}},[o("el-col",[o("el-col",[o("el-form-item",{attrs:{label:"作业要求",prop:"operationRequirement"}},[o("el-input",{attrs:{autosize:{minRows:4},maxlength:"250",placeholder:"内容","show-word-limit":"",type:"textarea"},model:{value:e.outboundForm.operationRequirement,callback:function(t){e.$set(e.outboundForm,"operationRequirement",t)},expression:"outboundForm.operationRequirement"}})],1)],1)],1)],1)],1),o("el-col",{attrs:{span:12}},[o("el-row",{attrs:{gutter:10}},[o("el-col",[o("el-col",{attrs:{span:8}},[o("el-form-item",{attrs:{label:"仓库报价",prop:"warehouseQuote"}},[o("el-input",{attrs:{placeholder:"仓库报价"},model:{value:e.outboundForm.warehouseQuote,callback:function(t){e.$set(e.outboundForm,"warehouseQuote",t)},expression:"outboundForm.warehouseQuote"}})],1)],1),o("el-col",{attrs:{span:8}},[o("el-form-item",{attrs:{label:"仓管代收",prop:"outboundNotes"}},[o("el-input",{attrs:{placeholder:"仓管代收"},model:{value:e.outboundForm.warehouseCollection,callback:function(t){e.$set(e.outboundForm,"warehouseCollection",t)},expression:"outboundForm.warehouseCollection"}})],1)],1),o("el-col",{attrs:{span:8}},[o("el-form-item",{attrs:{label:"工人装柜费",prop:"workerLoadingFee"}},[o("el-input",{attrs:{placeholder:"工人装柜费"},model:{value:e.outboundForm.workerLoadingFee,callback:function(t){e.$set(e.outboundForm,"workerLoadingFee",t)},expression:"outboundForm.workerLoadingFee"}})],1)],1)],1)],1),o("el-row",{attrs:{gutter:10}},[o("el-col",[o("el-col",[o("el-form-item",{attrs:{label:"出仓备注",prop:"outboundNote"}},[o("el-input",{attrs:{autosize:{minRows:4},maxlength:"250",placeholder:"内容","show-word-limit":"",type:"textarea"},model:{value:e.outboundForm.outboundNote,callback:function(t){e.$set(e.outboundForm,"outboundNote",t)},expression:"outboundForm.outboundNote"}})],1)],1)],1)],1),o("el-row",{attrs:{gutter:10}},[o("el-col",[o("el-col",{attrs:{span:8}},[o("el-form-item",{attrs:{label:"出仓日期",prop:"outboundDate"}},[o("el-date-picker",{staticStyle:{width:"100%"},attrs:{clearable:"",placeholder:"出仓日期",type:"date","value-format":"yyyy-MM-dd"},model:{value:e.outboundForm.outboundDate,callback:function(t){e.$set(e.outboundForm,"outboundDate",t)},expression:"outboundForm.outboundDate"}})],1)],1),o("el-col",{attrs:{span:8}},[o("el-form-item",{attrs:{label:"出仓经手人",prop:"outboundHandler"}},[o("el-input",{attrs:{placeholder:"出仓经手人"},model:{value:e.outboundForm.outboundHandler,callback:function(t){e.$set(e.outboundForm,"outboundHandler",t)},expression:"outboundForm.outboundHandler"}})],1)],1)],1)],1)],1),o("el-row",{attrs:{gutter:10}},[o("el-col",[o("el-col",[o("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.preOutboundInventoryListLoading,expression:"preOutboundInventoryListLoading"}],attrs:{data:e.outboundForm.rsInventoryList,"summary-method":e.getSummaries,"show-summary":""},on:{"selection-change":e.handleOutboundSelectionChange}},[o("el-table-column",{attrs:{align:"center",label:"入仓流水号",prop:"inboundSerialNo"}}),o("el-table-column",{attrs:{align:"center",label:"货物明细"},scopedSlots:e._u([{key:"default",fn:function(t){return[o("el-popover",{attrs:{trigger:"click",width:"800"}},[o("el-table",{attrs:{data:t.row.rsCargoDetailsList},on:{"selection-change":function(o){return e.handleOutboundCargoDetailSelectionChange(o,t.row)}}},[o("el-table-column",{attrs:{align:"center",type:"selection",width:"28"}}),o("el-table-column",{attrs:{label:"唛头",prop:"shippingMark",width:"150"}}),o("el-table-column",{attrs:{label:"货名",prop:"itemName",width:"150"}}),o("el-table-column",{attrs:{label:"箱数",prop:"boxCount"}}),o("el-table-column",{attrs:{label:"包装类型",prop:"packageType"}}),o("el-table-column",{attrs:{label:"单件毛重",prop:"unitGrossWeight"}}),o("el-table-column",{attrs:{label:"单件长",prop:"unitLength"}}),o("el-table-column",{attrs:{label:"单件宽",prop:"unitWidth"}}),o("el-table-column",{attrs:{label:"单件高",prop:"unitHeight"}}),o("el-table-column",{attrs:{label:"单件体积",prop:"unitVolume"}}),o("el-table-column",{attrs:{label:"破损标志",prop:"damageStatus"}})],1),o("el-button",{staticStyle:{margin:"0",padding:"5px"},attrs:{slot:"reference"},slot:"reference"},[e._v("查看")])],1)]}}])}),o("el-table-column",{attrs:{align:"center",label:"入仓日期",prop:"actualInboundTime",width:"80"},scopedSlots:e._u([{key:"default",fn:function(t){return[o("span",[e._v(e._s(e.parseTime(t.row.actualInboundTime,"{y}-{m}-{d}")))])]}}])}),o("el-table-column",{attrs:{align:"center",label:"货代单号",prop:"forwarderNo"}}),o("el-table-column",{attrs:{align:"center",label:"客户代码",prop:"clientCode"}}),o("el-table-column",{attrs:{align:"center",label:"箱数",prop:"totalBoxes"}}),o("el-table-column",{attrs:{align:"center",label:"毛重",prop:"totalGrossWeight"}}),o("el-table-column",{attrs:{align:"center",label:"体积",prop:"totalVolume"}}),o("el-table-column",{attrs:{align:"center",label:"免堆期",prop:"freeStackPeriod"}}),o("el-table-column",{attrs:{align:"center",label:"超期租金单价",prop:"overdueRentalUnitPrice"}}),o("el-table-column",{attrs:{align:"center",label:"超期租金"},scopedSlots:e._u([{key:"default",fn:function(t){return[o("span",[e._v(e._s(t.row.midOutboundSettlement.settlementRate))])]}}])}),o("el-table-column",{attrs:{align:"center",label:"唛头",prop:"sqdShippingMark"}}),o("el-table-column",{attrs:{align:"center",label:"包装类型",prop:"packageType"}}),o("el-table-column",{attrs:{align:"center",label:"总货名",prop:"cargoName"}})],1)],1)],1)],1),o("el-row",[o("el-col",{attrs:{span:4}},[o("span",[e._v("结算仓租："+e._s(e.overdueRentalFee))])])],1)],1)],1),o("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[o("el-button",{on:{click:e.generateOutboundBill}},[e._v("生成出仓单")])],1)],1)],1)},a=[],r=o("5530"),l=(o("d3b7"),o("3ca3"),o("ddb0"),o("2b3d"),o("9861"),o("d81d"),o("a9e3"),o("159b"),o("caad"),o("13d5"),o("b64b"),o("82ad")),u=o("fba1"),i=o("5fb3"),s={name:"Outboundrecord",data:function(){return{overdueRentalFee:0,showLeft:0,showRight:24,loading:!0,selectOutboundList:[],ids:[],single:!0,multiple:!0,showSearch:!0,total:0,outboundrecordList:[],title:"",open:!1,queryParams:{pageNum:1,pageSize:20,isRentSettlement:1,outboundNo:null,clientCode:null,clientName:null,operatorId:null,containerType:null,containerNo:null,sealNo:null,outboundDate:null,warehouseQuote:null,workerLoadingFee:null,warehouseCollection:null,collectionNotes:null,totalBoxes:null,totalGrossWeight:null,totalVolume:null,totalRows:null,receivedStorageFee:null,unpaidUnloadingFee:null,unpaidPackagingFee:null,logisticsAdvanceFee:null,rentalBalanceFee:null,overdueRent:null,freeStackDays:null,overdueUnitPrice:null},form:{},outboundType:null,preOutboundInventoryListLoading:!1,rules:{},outboundForm:{},openOutbound:!1,preOutboundInventoryList:[]}},watch:{showSearch:function(e){!0===e?(this.showRight=21,this.showLeft=3):(this.showRight=24,this.showLeft=0)}},created:function(){this.getList()},methods:{generateOutboundBill:function(){var e=this;Object(l["d"])(this.outboundForm).then((function(t){var o=t,n=e.outboundForm.clientCode+"-"+e.outboundForm.operator+"-"+e.outboundForm.outboundNo+".xlsx",a=new Blob([o],{type:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"}),r=document.createElement("a"),l=window.URL.createObjectURL(a);r.href=l,r.download=n,document.body.appendChild(r),r.click(),document.body.removeChild(r),window.URL.revokeObjectURL(l)})).catch((function(e){console.error("文件下载失败:",e)}))},findOutboundRecord:function(e){var t=this;this.outboundReset(),Object(l["g"])(e.outboundRecordId).then((function(e){t.outboundForm=e.data,t.overdueRentalFee=e.data.overdueRentalFee,t.openOutbound=!0}))},outboundConfirm:function(e){var t=this;this.selectOutboundList.map((function(e){e.partialOutboundFlag=Number(e.partialOutboundFlag)})),Object(l["a"])(this.outboundForm).then((function(o){if(o.data){var n=o.data,a=t.selectOutboundList.map((function(o){if("1"!==o.preOutboundFlag)return 0===e&&(o.preOutboundFlag="1"),o.outboundRecordId=n,o.rsCargoDetailsList&&o.rsCargoDetailsList.map((function(t){return t.outboundRecordId=n,0===e&&(t.preOutboundFlag="1"),t})),o;t.$message.warning("勾选记录中有以预出库记录,请重新勾选")}));0===e?Object(i["l"])(a).then((function(e){t.getList(),t.$message.success("预出仓成功"),t.openOutbound=!1})):Object(i["j"])(a).then((function(e){t.getList(),t.$message.success("出仓成功"),t.openOutbound=!1}))}}))},loadPreOutboundInventoryList:function(){var e=this;this.loading=!0;var t={};t.sqdPlannedOutboundDate=this.outboundForm.plannedOutboundDate,t.clientCode=this.outboundForm.clientCode,Object(i["h"])(t).then((function(t){e.preOutboundInventoryList=t.rows,e.total=t.total,e.loading=!1}))},handleOutbound:function(e,t){1===t&&(this.outboundReset(),this.outboundForm=e),this.outboundType=t,this.openOutbound=!0},parseTime:u["f"],handleOutboundCargoDetailSelectionChange:function(e,t){t.rsCargoDetailsList=e},getSummaries:function(e){var t=this,o=e.columns,n=e.data,a=[],r=["totalBoxes","totalGrossWeight","totalVolume","receivedSupplier","receivedStorageFee","unpaidUnloadingFee","unpaidPackagingFee","logisticsAdvanceFee","rentalBalanceFee","overdueRentalFee"],l={};return o.forEach((function(e,t){if(0!==t){var o=n.map((function(t){return Number(t[e.property])}));if(r.includes(e.property)&&!o.every((function(e){return isNaN(e)}))){var u=o.reduce((function(e,t){var o=Number(t);return isNaN(o)?e:e+t}),0);a[t]=u,l[e.property]=u}else a[t]=" "}else a[t]="汇总"})),Object.keys(l).forEach((function(e){t.outboundForm&&(t.outboundForm[e]=l[e])})),a},handleOutboundSelectionChange:function(e){this.selectOutboundList=e},outboundClient:function(e){this.outboundForm.warehouseQuote=e.rateLcl,this.outboundForm.freeStackDays=e.freeStackPeriod,this.outboundForm.overdueRent=e.overdueRent,this.outboundForm.clientName=e.clientName,this.$forceUpdate()},getList:function(){var e=this;this.loading=!0,Object(l["j"])(this.queryParams).then((function(t){e.outboundrecordList=t.rows,e.total=t.total,e.loading=!1}))},cancel:function(){this.open=!1,this.reset()},outboundReset:function(){this.outboundForm={outboundRecordId:null,outboundNo:null,clientCode:null,clientName:null,operatorId:null,containerType:null,containerNo:null,sealNo:null,outboundDate:null,warehouseQuote:null,workerLoadingFee:null,warehouseCollection:null,collectionNotes:null,totalBoxes:null,totalGrossWeight:null,totalVolume:null,overdueRentalFee:null,totalRows:null,receivedStorageFee:null,unpaidUnloadingFee:null,unpaidPackagingFee:null,logisticsAdvanceFee:null,rentalBalanceFee:null,overdueRent:null,freeStackDays:null,overdueUnitPrice:null},this.preOutboundInventoryList=[],this.resetForm("outboundForm")},reset:function(){this.form={outboundRecordId:null,outboundNo:null,clientCode:null,clientName:null,operatorId:null,containerType:null,containerNo:null,sealNo:null,outboundDate:null,warehouseQuote:null,workerLoadingFee:null,warehouseCollection:null,collectionNotes:null,totalBoxes:null,totalGrossWeight:null,overdueRentalFee:null,totalVolume:null,totalRows:null,receivedStorageFee:null,unpaidUnloadingFee:null,unpaidPackagingFee:null,logisticsAdvanceFee:null,rentalBalanceFee:null,overdueRent:null,freeStackDays:null,overdueUnitPrice:null},this.resetForm("form")},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.resetForm("queryForm"),this.handleQuery()},handleStatusChange:function(e){var t=this,o="0"===e.status?"启用":"停用";this.$modal.confirm('确认要"'+o+"吗？").then((function(){return Object(l["b"])(e.outboundRecordId,e.status)})).then((function(){t.$modal.msgSuccess(o+"成功")})).catch((function(){e.status="0"===e.status?"1":"0"}))},handleSelectionChange:function(e){this.ids=e.map((function(e){return e.outboundRecordId})),this.single=1!==e.length,this.multiple=!e.length},handleAdd:function(){this.outboundReset(),this.open=!0,this.title="添加出仓记录"},handleUpdate:function(e){var t=this;this.outboundReset();var o=e.outboundRecordId||this.ids;Object(l["e"])(o).then((function(e){t.form=e.data,t.open=!0,t.title="修改出仓记录"}))},submitForm:function(){var e=this;this.$refs["form"].validate((function(t){t&&(null!=e.form.outboundRecordId?Object(l["k"])(e.form).then((function(t){e.$modal.msgSuccess("修改成功"),e.open=!1,e.getList()})):Object(l["a"])(e.form).then((function(t){e.$modal.msgSuccess("新增成功"),e.open=!1,e.getList()})))}))},handleDelete:function(e){var t=this,o=e.outboundRecordId||this.ids;this.$modal.confirm('是否确认删除出仓记录编号为"'+o+'"的数据项？').then((function(){return Object(l["c"])(o)})).then((function(){t.getList(),t.$modal.msgSuccess("删除成功")})).catch((function(){}))},handleExport:function(){this.download("system/outboundrecord/export",Object(r["a"])({},this.queryParams),"outboundrecord_".concat((new Date).getTime(),".xlsx"))}}},c=s,d=o("2877"),p=Object(d["a"])(c,n,a,!1,null,null,null);t["default"]=p.exports},"5fb3":function(e,t,o){"use strict";o.d(t,"h",(function(){return a})),o.d(t,"g",(function(){return r})),o.d(t,"i",(function(){return l})),o.d(t,"e",(function(){return u})),o.d(t,"f",(function(){return i})),o.d(t,"a",(function(){return s})),o.d(t,"n",(function(){return c})),o.d(t,"d",(function(){return d})),o.d(t,"c",(function(){return p})),o.d(t,"j",(function(){return m})),o.d(t,"m",(function(){return b})),o.d(t,"l",(function(){return y})),o.d(t,"k",(function(){return f})),o.d(t,"b",(function(){return h}));var n=o("b775");function a(e){return Object(n["a"])({url:"/system/inventory/list",method:"get",params:e})}function r(e){return Object(n["a"])({url:"/system/inventory/aggregator",method:"get",params:e})}function l(e){return Object(n["a"])({url:"/system/inventory/lists",method:"get",params:e})}function u(e){return Object(n["a"])({url:"/system/inventory/"+e,method:"get"})}function i(e){return Object(n["a"])({url:"/system/inventory/package",method:"get",params:e})}function s(e){return Object(n["a"])({url:"/system/inventory",method:"post",data:e})}function c(e){return Object(n["a"])({url:"/system/inventory",method:"put",data:e})}function d(e){return Object(n["a"])({url:"/system/inventory/"+e,method:"delete"})}function p(e,t){var o={inventoryId:e,status:t};return Object(n["a"])({url:"/system/inventory/changeStatus",method:"put",data:o})}function m(e){return Object(n["a"])({url:"/system/inventory/outbound",method:"put",data:e})}function b(e){return Object(n["a"])({url:"/system/inventory/settlement",method:"put",data:e})}function y(e){return Object(n["a"])({url:"/system/inventory/preOutbound",method:"put",data:e})}function f(e){return Object(n["a"])({url:"/system/inventory/packUp",method:"put",data:e})}function h(e){return Object(n["a"])({url:"/system/inventory/cancelPkg",method:"put",data:e})}},"82ad":function(e,t,o){"use strict";o.d(t,"h",(function(){return a})),o.d(t,"e",(function(){return r})),o.d(t,"a",(function(){return l})),o.d(t,"k",(function(){return u})),o.d(t,"c",(function(){return i})),o.d(t,"b",(function(){return s})),o.d(t,"i",(function(){return c})),o.d(t,"j",(function(){return d})),o.d(t,"f",(function(){return p})),o.d(t,"g",(function(){return m})),o.d(t,"d",(function(){return b}));var n=o("b775");function a(e){return Object(n["a"])({url:"/system/outboundrecord/list",method:"get",params:e})}function r(e){return Object(n["a"])({url:"/system/outboundrecord/"+e,method:"get"})}function l(e){return Object(n["a"])({url:"/system/outboundrecord",method:"post",data:e})}function u(e){return Object(n["a"])({url:"/system/outboundrecord",method:"put",data:e})}function i(e){return Object(n["a"])({url:"/system/outboundrecord/"+e,method:"delete"})}function s(e,t){var o={outboundRecordId:e,status:t};return Object(n["a"])({url:"/system/outboundrecord/changeStatus",method:"put",data:o})}function c(e){return Object(n["a"])({url:"/system/outboundrecord/outboundrecords",method:"get",params:e})}function d(e){return Object(n["a"])({url:"/system/outboundrecord/listRental",method:"get",params:e})}function p(e){return Object(n["a"])({url:"/system/outboundrecord/outboundrecords/"+e,method:"get"})}function m(e){return Object(n["a"])({url:"/system/outboundrecord/rentals/"+e,method:"get"})}function b(e){return Object(n["a"])({url:"/system/outboundrecord/outboundBill",method:"put",data:e,responseType:"arraybuffer"})}}}]);