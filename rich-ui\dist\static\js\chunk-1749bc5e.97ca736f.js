(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-1749bc5e"],{"2c2f":function(e,t,r){"use strict";r("ff7d")},"4ddb":function(e,t,r){"use strict";r.d(t,"d",(function(){return o})),r.d(t,"e",(function(){return a})),r.d(t,"c",(function(){return s})),r.d(t,"a",(function(){return l})),r.d(t,"f",(function(){return i})),r.d(t,"b",(function(){return c}));var n=r("b775");function o(e){return Object(n["a"])({url:"/system/staffrole/list",method:"get",params:e})}function a(e){return Object(n["a"])({url:"/system/staffrole/menuList",method:"get",params:e})}function s(e){return Object(n["a"])({url:"/system/staffrole/"+e,method:"get"})}function l(e){return Object(n["a"])({url:"/system/staffrole",method:"post",data:e})}function i(e){return Object(n["a"])({url:"/system/staffrole",method:"put",data:e})}function c(e){return Object(n["a"])({url:"/system/staffrole/"+e,method:"delete"})}},"70eb":function(e,t,r){"use strict";r.r(t);var n=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",{staticClass:"app-container"},[r("el-row",{attrs:{gutter:20}},[r("el-col",{attrs:{span:e.showLeft}},[r("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],ref:"queryForm",staticClass:"query",attrs:{inline:!0,model:e.queryParams,size:e.size,"label-width":"35px"}},[r("el-form-item",{attrs:{label:"部门",prop:"companyServiceTypeIds"}},[r("tree-select",{staticStyle:{width:"100%"},attrs:{flat:!1,multiple:!1,pass:e.queryParams.deptId,placeholder:"选择部门",type:"dept"},on:{return:function(t){e.queryParams.deptId=t}}})],1),r("el-form-item",{attrs:{label:"服务",prop:"serviceTypeIds"}},[r("tree-select",{staticStyle:{width:"100%"},attrs:{flat:!1,multiple:!1,pass:e.queryParams.serviceTypeIds,placeholder:"服务类型",type:"serviceType"},on:{return:function(t){e.queryParams.serviceTypeIds=t}}})],1),r("el-form-item",{attrs:{label:"搜索",prop:"roleQuery"}},[r("el-input",{staticStyle:{width:"100%"},attrs:{clearable:"",placeholder:"角色名称"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.roleQuery,callback:function(t){e.$set(e.queryParams,"roleQuery",t)},expression:"queryParams.roleQuery"}})],1),r("el-form-item",{attrs:{label:"关键",prop:"roleKey"}},[r("el-input",{staticStyle:{width:"100%"},attrs:{clearable:"",placeholder:"权限字符"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.roleKey,callback:function(t){e.$set(e.queryParams,"roleKey",t)},expression:"queryParams.roleKey"}})],1),r("el-form-item",[r("el-button",{attrs:{icon:"el-icon-search",size:"mini",type:"primary"},on:{click:e.handleQuery}},[e._v("搜索")]),r("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1)],1),r("el-col",{attrs:{span:e.showRight}},[r("el-row",{staticClass:"mb8",attrs:{gutter:10}},[r("el-col",{attrs:{span:1.5}},[r("el-button",{attrs:{icon:"el-icon-sort",plain:"",size:"mini",type:"info"},on:{click:e.toggleExpandAll}},[e._v("展开/折叠 ")])],1),r("el-col",{attrs:{span:1.5}},[r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:role:export"],expression:"['system:role:export']"}],attrs:{icon:"el-icon-download",plain:"",size:"mini",type:"warning"},on:{click:e.handleExport}},[e._v("导出 ")])],1),r("right-toolbar",{attrs:{showSearch:e.showSearch},on:{"update:showSearch":function(t){e.showSearch=t},"update:show-search":function(t){e.showSearch=t},queryTable:e.getList}})],1),e.refreshTable?r("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.roleList,"default-expand-all":e.isExpandAll,"tree-props":{children:"children",hasChildren:"hasChildren"},"row-key":"roleId"}},[r("el-table-column",{key:"Name",attrs:{align:"left",label:"角色名称",width:"450"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("a",{class:t.row.roleEnName&&-1!==t.row.roleEnName.toString().indexOf("Max")||t.row.roleEnName&&-1!==t.row.roleEnName.toString().indexOf("Basic")?"nonEmphasis":"",staticStyle:{margin:"0","font-weight":"bold","font-size":"small"}},[e._v(e._s(t.row.roleLocalName)+" "+e._s(t.row.roleEnName)+" ")])]}}],null,!1,2457806492)}),r("el-table-column",{key:"roleKey",attrs:{align:"center",label:"关键字",prop:"roleKey",width:"250px"}}),r("el-table-column",{key:"dept.deptLocalName",attrs:{align:"center",label:"所属部门",prop:"dept.deptLocalName",width:"68px"}}),r("el-table-column",{key:"position.positionLocalName",attrs:{align:"center",label:"所属职级",prop:"position.positionLocalName",width:"68px"}}),r("el-table-column",{key:"serviceTypes",attrs:{align:"center",label:"服务类型",prop:"serviceTypes","show-tooltip-when-overflow":"",width:"68px"}}),r("el-table-column",{key:"cargoTypes",attrs:{align:"center",label:"货物特征",prop:"cargoTypes","show-tooltip-when-overflow":"",width:"68px"}}),r("el-table-column",{key:"departure",attrs:{align:"center",label:"启运","show-tooltip-when-overflow":"",width:"68px"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("h6",{staticStyle:{margin:"0"}},[e._v(" "+e._s((null!=t.row.locationDepartures?t.row.locationDepartures:"")+(null!=t.row.locationDepartures&&null!=t.row.lineDepartures?",":"")+(null!=t.row.lineDepartures?t.row.lineDepartures:""))+" ")])]}}],null,!1,3816749506)}),r("el-table-column",{key:"destination",attrs:{align:"center",label:"目的","show-tooltip-when-overflow":""},scopedSlots:e._u([{key:"default",fn:function(t){return[r("h6",{staticStyle:{margin:"0"}},[e._v(" "+e._s((null!=t.row.locationDestinations?t.row.locationDestinations:"")+(null!=t.row.lineDestinations&&null!=t.row.locationDestinations?",":"")+(null!=t.row.lineDestinations?t.row.lineDestinations:""))+" ")])]}}],null,!1,481449666)}),r("el-table-column",{key:"distribute",attrs:{align:"center",label:"角色分配",width:"68px"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("el-popover",{attrs:{placement:"left",trigger:"hover",width:"400"}},[r("el-table",{attrs:{data:t.row.midRsStaffRoles}},[r("el-table-column",{attrs:{align:"center",label:"用户编号",prop:"staff.staffCode"}}),r("el-table-column",{attrs:{align:"center",label:"用户姓名",prop:"staff.staffFamilyLocalName,staff.staffGivingLocalName"},scopedSlots:e._u([{key:"default",fn:function(t){var r=t.row;return[e._v(" "+e._s(r.staff.staffFamilyLocalName)+" "+e._s(r.staff.staffGivingLocalName)+" ")]}}],null,!0)}),r("el-table-column",{attrs:{align:"center",label:"用户英文名",prop:"staff.staffGivingEnName,staff.staffFamilyEnName"},scopedSlots:e._u([{key:"default",fn:function(t){var r=t.row;return[e._v(" "+e._s(r.staff.staffGivingEnName)+" "+e._s(r.staff.staffFamilyEnName)+" ")]}}],null,!0)})],1),r("el-button",{staticStyle:{margin:"0",padding:"5px"},attrs:{slot:"reference"},nativeOn:{mouseenter:function(r){return e.loadStaffRoles(t)}},slot:"reference"},[e._v(" 查看 ")])],1)]}}],null,!1,73784336)}),r("el-table-column",{key:"roleSort",attrs:{align:"center",label:"排序",prop:"roleSort",width:"44px"}}),r("el-table-column",{attrs:{align:"center",label:"状态",width:"68px"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("el-switch",{attrs:{"active-value":"0","inactive-value":"1"},on:{change:function(r){return e.handleStatusChange(t.row)}},model:{value:t.row.status,callback:function(r){e.$set(t.row,"status",r)},expression:"scope.row.status"}})]}}],null,!1,3955094654)}),r("el-table-column",{attrs:{align:"center","class-name":"small-padding fixed-width",label:"操作",width:"170"},scopedSlots:e._u([{key:"default",fn:function(t){return 1!=t.row.roleId?[null===t.row.position.positionId?r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:role:add"],expression:"['system:role:add']"}],staticStyle:{"margin-right":"-8px"},attrs:{icon:"el-icon-plus",size:"mini",type:"primary"},on:{click:function(r){return e.handleAdd(t.row)}}},[e._v("新增 ")]):e._e(),null!==t.row.position.positionId?r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:role:edit"],expression:"['system:role:edit']"}],staticStyle:{"margin-right":"-8px"},attrs:{icon:"el-icon-edit",size:"mini",type:"success"},on:{click:function(r){return e.handleUpdate(t.row)}}},[e._v("修改 ")]):e._e(),0!=t.row.parentId&&1==e.$store.state.user.sid?r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:role:remove"],expression:"['system:role:remove']"}],staticStyle:{"margin-right":"-8px"},attrs:{icon:"el-icon-delete",size:"mini",type:"danger"},on:{click:function(r){return e.handleDelete(t.row)}}},[e._v("删除 ")]):e._e()]:void 0}}],null,!0)})],1):e._e()],1)],1),r("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"},{name:"dialogDragWidth",rawName:"v-dialogDragWidth"}],attrs:{"close-on-click-modal":!1,"modal-append-to-body":!1,title:e.title,visible:e.open,"append-to-body":"",width:"1000px"},on:{"update:visible":function(t){e.open=t}}},[r("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"80px"}},[r("el-row",[r("el-col",{attrs:{span:12}},[r("el-divider",{attrs:{"content-position":"center"}},[e._v("基本信息")]),0!=e.form.parentId?r("el-form-item",{attrs:{label:"上级角色",prop:"parentId"}},[r("tree-select",{attrs:{multiple:!1,pass:e.form.parentId,type:"role",disabled:e.edit&&"admin"!=e.$store.state.user.userRole},on:{return:function(t){e.form.parentId=t}}})],1):e._e(),r("el-form-item",{attrs:{label:"归属部门",prop:"deptId"}},[r("tree-select",{attrs:{multiple:!1,pass:e.form.deptId,placeholder:"归属部门",disabled:e.edit&&"admin"!=e.$store.state.user.userRole,type:"dept"},on:{return:e.getDept}})],1),r("el-form-item",{attrs:{label:"所属职级",prop:"positionId"}},[r("el-select",{staticStyle:{width:"100%"},attrs:{clearable:"",filterable:"",placeholder:"职级",disabled:e.edit&&"admin"!=e.$store.state.user.userRole},model:{value:e.form.positionId,callback:function(t){e.$set(e.form,"positionId",t)},expression:"form.positionId"}},e._l(e.postOptions,(function(e){return r("el-option",{key:e.positionId,attrs:{disabled:1==e.status,label:e.positionLocalName,value:e.positionId}})})),1)],1),r("el-form-item",{attrs:{label:"角色名称",prop:"roleLocalName"}},[r("el-input",{attrs:{placeholder:"角色名称"},model:{value:e.form.roleLocalName,callback:function(t){e.$set(e.form,"roleLocalName",t)},expression:"form.roleLocalName"}})],1),r("el-form-item",{attrs:{label:"英文名称",prop:"roleEnName"}},[r("el-input",{attrs:{placeholder:"英文字符"},model:{value:e.form.roleEnName,callback:function(t){e.$set(e.form,"roleEnName",t)},expression:"form.roleEnName"}})],1),r("el-form-item",{attrs:{label:"关键字",prop:"roleKey"}},[r("el-input",{attrs:{placeholder:"关键词"},model:{value:e.form.roleKey,callback:function(t){e.$set(e.form,"roleKey",t)},expression:"form.roleKey"}})],1),r("el-row",[r("el-col",{attrs:{span:12}},[r("el-form-item",{attrs:{label:"角色顺序",prop:"roleSort"}},[r("el-input-number",{attrs:{controls:!1,min:0,"controls-position":"right"},model:{value:e.form.roleSort,callback:function(t){e.$set(e.form,"roleSort",t)},expression:"form.roleSort"}})],1)],1),r("el-col",{attrs:{span:12}},[r("el-form-item",{attrs:{label:"状态"}},[r("el-radio-group",{model:{value:e.form.status,callback:function(t){e.$set(e.form,"status",t)},expression:"form.status"}},e._l(e.dict.type.sys_normal_disable,(function(t){return r("el-radio",{key:t.value,attrs:{label:t.value}},[e._v(e._s(t.label)+" ")])})),1)],1)],1)],1),r("el-form-item",{attrs:{label:"服务类型"}},[r("tree-select",{attrs:{flat:!1,multiple:!0,pass:e.form.serviceTypeIds,type:"serviceType"},on:{return:function(t){e.form.serviceTypeIds=t}}})],1),r("el-form-item",{attrs:{label:"货物特征"}},[r("tree-select",{attrs:{flat:!1,multiple:!0,pass:e.form.cargoTypeIds,type:"cargoType"},on:{return:function(t){e.form.cargoTypeIds=t}}})],1),r("el-form-item",{attrs:{label:"启运区域"}},[r("location-select",{attrs:{en:!0,"load-options":e.locationOptions,multiple:!0,pass:e.form.locationDepartureIds,type:"location"},on:{return:function(t){e.form.locationDepartureIds=t}}})],1),r("el-form-item",{attrs:{label:"目的区域"}},[r("location-select",{attrs:{en:!0,"load-options":e.locationOptions,multiple:!0,pass:e.form.locationDestinationIds,type:"location"},on:{return:function(t){e.form.locationDestinationIds=t}}})],1),r("el-form-item",{attrs:{label:"目的航线"}},[r("tree-select",{attrs:{flat:!1,multiple:!0,pass:e.form.lineDestinationIds,type:"line"},on:{return:function(t){e.form.lineDestinationIds=t}}})],1),r("el-form-item",{attrs:{label:"备注"}},[r("el-input",{attrs:{autosize:{minRows:10,maxRows:20},maxlength:"150",placeholder:"内容","show-word-limit":"",type:"textarea"},model:{value:e.form.remark,callback:function(t){e.$set(e.form,"remark",t)},expression:"form.remark"}})],1)],1),r("el-col",{attrs:{span:12}},[r("el-divider",{attrs:{"content-position":"center"}},[e._v("类型分配")]),r("el-form-item",{attrs:{label:"菜单权限"}},[r("tree-select",{ref:"menu",attrs:{flat:!1,multiple:!0,pass:e.form.menuIds,type:"menu",limit:3},on:{return:e.getMenuIds}})],1),r("el-form-item",{attrs:{label:"权限范围"}},[r("el-select",{attrs:{filterable:""},on:{change:e.dataScopeSelectChange},model:{value:e.form.dataScope,callback:function(t){e.$set(e.form,"dataScope",t)},expression:"form.dataScope"}},e._l(e.dataScopeOptions,(function(e){return r("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),r("el-form-item",{directives:[{name:"show",rawName:"v-show",value:"2"==e.form.dataScope,expression:"form.dataScope == '2'"}],attrs:{label:"数据权限"}},[r("el-checkbox",{on:{click:function(t){e.form.deptCheckStrictly=!e.form.deptCheckStrictly}},model:{value:e.form.deptCheckStrictly,callback:function(t){e.$set(e.form,"deptCheckStrictly",t)},expression:"form.deptCheckStrictly"}},[e._v(" 父子联动 ")]),r("tree-select",{ref:"dept",attrs:{flat:e.form.deptCheckStrictly,limit:4,multiple:!0,pass:e.form.deptIds,type:"dept"},on:{return:function(t){e.form.deptIds=t}},nativeOn:{click:function(t){return e.getDeptList(t)}}})],1)],1)],1)],1),r("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[r("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("确 定")]),r("el-button",{on:{click:e.cancel}},[e._v("取 消")])],1)],1)],1)},o=[],a=r("5530"),s=r("b85c"),l=(r("d3b7"),r("6062"),r("3ca3"),r("ddb0"),r("d81d"),r("a630"),r("159b"),r("3528")),i=r("a6dc"),c=r("74b1"),u=r("4360"),d=r("4ddb"),m=r("fba1"),p={name:"Role",dicts:["sys_normal_disable"],data:function(){return{showLeft:3,showRight:21,edit:!1,locationOptions:new Set,size:this.$store.state.app.size||"mini",loading:!0,showSearch:!1,roleList:[],title:"",open:!1,refreshTable:!0,isExpandAll:!1,dateRange:[],deptLocalName:void 0,dataScopeOptions:[{value:"1",label:"全部数据权限"},{value:"2",label:"自定数据权限"},{value:"3",label:"本部门数据权限"},{value:"4",label:"本部门及以下数据权限"},{value:"5",label:"仅本人数据权限"}],menuOptions:[],postOptions:[],distributeList:[],queryParams:{roleQuery:null,roleKey:null,deptId:null,serviceTypeId:null},form:{},rules:{roleLocalName:[{required:!0,trigger:"blur"}],roleKey:[{required:!0,trigger:"blur"}],roleSort:[{required:!0,message:"角色顺序不能为空",trigger:"blur"}]},basicMenu:[],limitMenuOptions:[]}},watch:{showSearch:function(e){1==e?(this.showRight=21,this.showLeft=3):(this.showRight=24,this.showLeft=0)}},created:function(){var e=this;this.getList(),Object(c["e"])(this.$store.state.user.sid).then((function(t){e.postOptions=t.rows})),Object(i["e"])().then((function(t){e.menuOptions=t.data}))},methods:{getDeptList:function(){var e=this;if(0==this.$store.state.data.deptList.length||this.$store.state.data.redisList.dept)u["a"].dispatch("getDeptList").then((function(){var t,r=e.$store.state.data.deptList[0].children,n=Object(s["a"])(r[0].children);try{for(n.s();!(t=n.n()).done;){var o=t.value;o.children&&delete o.children}}catch(a){n.e(a)}finally{n.f()}e.$refs.dept.getLoadOptions(r)}));else{var t,r=this.$store.state.data.deptList[0].children,n=Object(s["a"])(r[0].children);try{for(n.s();!(t=n.n()).done;){var o=t.value;o.children&&delete o.children}}catch(a){n.e(a)}finally{n.f()}this.$refs.dept.getLoadOptions(r)}},getList:function(){var e=this;this.loading=!0,Object(l["h"])(this.addDateRange(this.queryParams,this.dateRange)).then((function(t){e.roleList=e.handleTree(t.data,"roleId"),e.loading=!1}))},loadStaffRoles:function(e){if(null==e.row.midRsStaffRoles){var t={roleId:e.row.roleId};Object(d["d"])(t).then((function(t){e.row.midRsStaffRoles=t.rows}))}},handleUpdate:function(e){var t=this;this.reset();var r=e.roleId||this.ids;e&&70!==e.positionId&&0!==e.positionId&&Object(i["f"])(e.deptId,e.positionId).then((function(e){t.limitMenuOptions=e.data})),e&&70===e.positionId&&Object(i["d"])().then((function(e){t.limitMenuOptions=e.data})),e&&0===e.positionId&&Object(i["f"])(e.deptId,69).then((function(e){t.limitMenuOptions=e.data})),Object(l["f"])(r).then((function(r){t.form=r.data,t.form.serviceTypeIds=r.serviceTypeIds,t.form.cargoTypeIds=r.cargoTypeIds,t.form.lineDepartureIds=r.lineDepartureIds,t.form.locationDepartureIds=r.locationDepartureIds,t.form.lineDestinationIds=r.lineDestinationIds,t.form.locationDestinationIds=r.locationDestinationIds,t.form.deptIds=r.deptIds,t.form.menuIds=r.menuIds,t.open=!0,t.title="修改角色",t.locationOptions=r.locationOptions,t.edit=!0,t.$nextTick((function(){t.splitMenu(e)}))}))},splitMenu:function(e){var t=this.limitMenuOptions.length>0?this.limitMenuOptions.map((function(e){return e.menuId})):[],r=this.form.menuIds;if(e&&89===e.positionId){var n=this.handleTree(this.menuOptions,"menuId");return Object(m["j"])(n,r),void this.$refs.menu.getLoadOptions(n)}if(e&&70!==e.positionId&&0!==e.positionId){var o=this.handleTree(this.menuOptions,"menuId");return Object(m["l"])(o,t),Object(m["j"])(o,r),void this.$refs.menu.getLoadOptions(o)}if(e&&0===e.positionId){var a=this.handleTree(this.menuOptions,"menuId");return Object(m["l"])(a,t),Object(m["j"])(a,r),void this.$refs.menu.getLoadOptions(a)}if(e&&70===e.positionId){this.menuOptions=this.menuOptions.map((function(e){return delete e.isDisabled,e}));var s=this.handleTree(this.menuOptions,"menuId");return Object(m["j"])(s,r),void this.$refs.menu.getLoadOptions(s)}},handleStatusChange:function(e){var t=this,r="0"==e.status?"启用":"停用";this.$confirm('确认要"'+r+'""'+e.roleLocalName+'"角色吗？',"提示",{customClass:"modal-confirm"}).then((function(){return Object(l["b"])(e.roleId,e.status)})).then((function(){t.$modal.msgSuccess(r+"成功")})).catch((function(){e.status="0"==e.status?"1":"0"}))},cancel:function(){this.open=!1,this.reset()},reset:function(){this.edit=!1,this.form={roleId:void 0,roleLocalName:void 0,roleKey:void 0,roleSort:0,status:"0",parentId:void 0,deptId:void 0,positionId:void 0,menuIds:[],deptIds:[],menuCheckStrictly:!0,deptCheckStrictly:!0,remark:void 0},this.resetForm("form")},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.dateRange=[],this.resetForm("queryForm"),this.handleQuery()},handleSelectionChange:function(e){this.ids=e.map((function(e){return e.roleId})),this.single=1!=e.length,this.multiple=!e.length},handleAdd:function(e){this.reset(),this.form.parentId=e.roleId,this.form.deptId=e.deptId,this.open=!0,this.title="添加角色"},dataScopeSelectChange:function(e){"2"!==e&&this.$refs.dept.setCheckedKeys([])},submitForm:function(){var e=this;this.$refs["form"].validate((function(t){t&&(e.getSelectedIds(e.$refs.menu.optionsContent,e.form.menuIds),void 0!==e.form.roleId?Object(l["j"])(e.form).then((function(t){e.$modal.msgSuccess("修改成功"),e.open=!1,e.edit=!1,e.getList()})):Object(l["a"])(e.form).then((function(t){e.$modal.msgSuccess("新增成功"),e.open=!1,e.edit=!1,e.getList()})))}))},getSelectedIds:function(e,t){var r=this,n=new Set;t.map((function(t){r.traverseTree(e,t,n)})),n.delete(0),this.form.menuIds=Array.from(n)},traverseTree:function(e,t,r){for(var n=0;n<e.length;n++){var o=e[n];o.menuId===t&&(r.add(o.menuId),o.ancestors.split(",").forEach((function(e){return r.add(parseInt(e))})),o.children&&o.children.length>0&&this.traverseTreeChildren(o.children,r)),o.children&&o.children.length>0&&this.traverseTree(o.children,t,r)}},traverseTreeChildren:function(e,t){for(var r=0;r<e.length;r++){var n=e[r];t.add(n.menuId),n.ancestors.split(",").forEach((function(e){return t.add(parseInt(e))})),n.children&&this.traverseTreeChildren(n.children,t)}},handleDelete:function(e){var t=this,r=e.roleId||this.ids;this.$confirm('是否确认删除角色编号为"'+r+'"的数据项？',"提示",{customClass:"modal-confirm"}).then((function(){return Object(l["c"])(r)})).then((function(){t.getList(),t.$modal.msgSuccess("删除成功")})).catch((function(){}))},handleExport:function(){this.download("system/role/export",Object(a["a"])({},this.queryParams),"role_".concat((new Date).getTime(),".xlsx"))},toggleExpandAll:function(){var e=this;this.refreshTable=!1,this.isExpandAll=!this.isExpandAll,this.$nextTick((function(){e.refreshTable=!0}))},getDept:function(e){this.form.deptId=e},getMenuIds:function(e){this.form.menuIds=e}}},f=p,h=(r("2c2f"),r("2877")),y=Object(h["a"])(f,n,o,!1,null,"4f8906a2",null);t["default"]=y.exports},"74b1":function(e,t,r){"use strict";r.d(t,"d",(function(){return o})),r.d(t,"c",(function(){return a})),r.d(t,"a",(function(){return s})),r.d(t,"g",(function(){return l})),r.d(t,"b",(function(){return i})),r.d(t,"e",(function(){return c})),r.d(t,"f",(function(){return u}));var n=r("b775");function o(e){return Object(n["a"])({url:"/system/post/list",method:"get",params:e})}function a(e){return Object(n["a"])({url:"/system/post/"+e,method:"get"})}function s(e){return Object(n["a"])({url:"/system/post",method:"post",data:e})}function l(e){return Object(n["a"])({url:"/system/post",method:"put",data:e})}function i(e){return Object(n["a"])({url:"/system/post/"+e,method:"delete"})}function c(e){return Object(n["a"])({url:"/system/post/underUser/"+e,method:"get"})}function u(e){return Object(n["a"])({url:"/system/post/user/"+e,method:"get"})}},a6dc:function(e,t,r){"use strict";r.d(t,"e",(function(){return o})),r.d(t,"c",(function(){return a})),r.d(t,"a",(function(){return s})),r.d(t,"g",(function(){return l})),r.d(t,"b",(function(){return i})),r.d(t,"d",(function(){return c})),r.d(t,"f",(function(){return u}));var n=r("b775");function o(e){return Object(n["a"])({url:"/system/menu/list",method:"get",params:e})}function a(e){return Object(n["a"])({url:"/system/menu/"+e,method:"get"})}function s(e){return Object(n["a"])({url:"/system/menu",method:"post",data:e})}function l(e){return Object(n["a"])({url:"/system/menu",method:"put",data:e})}function i(e){return Object(n["a"])({url:"/system/menu/"+e,method:"delete"})}function c(){return Object(n["a"])({url:"/system/menu/limitRoleMenu",method:"get"})}function u(e,t){return Object(n["a"])({url:"/system/menu/listMenuByRole",method:"get",params:{deptId:e,positionId:t}})}},ff7d:function(e,t,r){}}]);