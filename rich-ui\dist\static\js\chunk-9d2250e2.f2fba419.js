(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-9d2250e2"],{"3a13":function(e,t,a){"use strict";a.d(t,"e",(function(){return s})),a.d(t,"d",(function(){return o})),a.d(t,"a",(function(){return n})),a.d(t,"f",(function(){return l})),a.d(t,"c",(function(){return i})),a.d(t,"b",(function(){return c}));var r=a("b775");function s(e){return Object(r["a"])({url:"/system/operationalprocess/list",method:"get",params:e})}function o(e){return Object(r["a"])({url:"/system/operationalprocess/"+e,method:"get"})}function n(e){return Object(r["a"])({url:"/system/operationalprocess",method:"post",data:e})}function l(e){return Object(r["a"])({url:"/system/operationalprocess",method:"put",data:e})}function i(e){return Object(r["a"])({url:"/system/operationalprocess/del",method:"post",data:e})}function c(e,t){var a={operationalProcessId:e,status:t};return Object(r["a"])({url:"/system/operationalprocess/changeStatus",method:"put",data:a})}},"459b":function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:e.showLeft}},[a("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],ref:"queryForm",staticClass:"query",attrs:{model:e.queryParams,size:"mini",inline:!0,"label-width":"68px"}},[a("el-form-item",{attrs:{label:"服务类型",prop:"serviceTypeId"}},[a("el-input",{attrs:{placeholder:"服务类型",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.serviceTypeId,callback:function(t){e.$set(e.queryParams,"serviceTypeId",t)},expression:"queryParams.serviceTypeId"}})],1),a("el-form-item",{attrs:{label:"服务ID",prop:"rctId"}},[a("el-input",{attrs:{placeholder:"服务ID",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.rctId,callback:function(t){e.$set(e.queryParams,"rctId",t)},expression:"queryParams.rctId"}})],1),a("el-form-item",{attrs:{label:"进度名称",prop:"processId"}},[a("el-input",{attrs:{placeholder:"进度名称",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.processId,callback:function(t){e.$set(e.queryParams,"processId",t)},expression:"queryParams.processId"}})],1),a("el-form-item",{attrs:{label:"进度状态",prop:"processStatusId"}},[a("el-input",{attrs:{placeholder:"进度状态",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.processStatusId,callback:function(t){e.$set(e.queryParams,"processStatusId",t)},expression:"queryParams.processStatusId"}})],1),a("el-form-item",[a("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"mini"},on:{click:e.handleQuery}},[e._v("搜索")]),a("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1)],1),a("el-col",{attrs:{span:e.showRight}},[a("el-row",{staticClass:"mb8",attrs:{gutter:10}},[a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:operationalprocess:add"],expression:"['system:operationalprocess:add']"}],attrs:{type:"primary",plain:"",icon:"el-icon-plus",size:"mini"},on:{click:e.handleAdd}},[e._v("新增 ")])],1),a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:operationalprocess:edit"],expression:"['system:operationalprocess:edit']"}],attrs:{type:"success",plain:"",icon:"el-icon-edit",size:"mini",disabled:e.single},on:{click:e.handleUpdate}},[e._v("修改 ")])],1),a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:operationalprocess:remove"],expression:"['system:operationalprocess:remove']"}],attrs:{type:"danger",plain:"",icon:"el-icon-delete",size:"mini",disabled:e.multiple},on:{click:e.handleDelete}},[e._v("删除 ")])],1),a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:operationalprocess:export"],expression:"['system:operationalprocess:export']"}],attrs:{type:"warning",plain:"",icon:"el-icon-download",size:"mini"},on:{click:e.handleExport}},[e._v("导出 ")])],1),a("right-toolbar",{attrs:{showSearch:e.showSearch},on:{"update:showSearch":function(t){e.showSearch=t},"update:show-search":function(t){e.showSearch=t},queryTable:e.getList}})],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.operationalprocessList},on:{"selection-change":e.handleSelectionChange}},[a("el-table-column",{attrs:{type:"selection",width:"28",align:"center"}}),a("el-table-column",{attrs:{align:"center",label:"操作单号",prop:"operationNo","show-tooltip-when-overflow":"",width:"120"}}),a("el-table-column",{attrs:{label:"服务类型",align:"center",prop:"serviceType",width:"68"}}),a("el-table-column",{attrs:{label:"服务",align:"center",prop:"rctId",width:"68"}}),a("el-table-column",{attrs:{label:"进度名称",align:"center",prop:"process",width:"68"}}),a("el-table-column",{attrs:{label:"进度状态",align:"center",prop:"processStatus",width:"68"}}),a("el-table-column",{attrs:{label:"发生时间",align:"center",prop:"happenTime",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(e.parseTime(t.row.happenTime,"{y}-{m}-{d}")))])]}}])}),a("el-table-column",{attrs:{label:"备注",align:"center",prop:"remark"}}),a("el-table-column",{attrs:{align:"center",label:"录入人",prop:"updateBy","show-tooltip-when-overflow":"",width:"78"}}),a("el-table-column",{attrs:{label:"录入时间",align:"center",prop:"updateTime",width:"120"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(e.parseTime(t.row.createTime,"{y}-{m}-{d}")))])]}}])}),a("el-table-column",{attrs:{label:"操作",align:"center","class-name":"small-padding fixed-width",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:operationalprocess:edit"],expression:"['system:operationalprocess:edit']"}],staticStyle:{"margin-right":"-8px"},attrs:{size:"mini",type:"success",icon:"el-icon-edit"},on:{click:function(a){return e.handleUpdate(t.row)}}},[e._v("修改 ")]),a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:operationalprocess:remove"],expression:"['system:operationalprocess:remove']"}],staticStyle:{"margin-right":"-8px"},attrs:{size:"mini",type:"danger",icon:"el-icon-delete"},on:{click:function(a){return e.handleDelete(t.row)}}},[e._v("删除 ")])]}}])})],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total>0"}],attrs:{total:e.total,page:e.queryParams.pageNum,limit:e.queryParams.pageSize},on:{"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},pagination:e.getList}})],1)],1),a("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"},{name:"dialogDragWidth",rawName:"v-dialogDragWidth"}],attrs:{"close-on-click-modal":!1,"modal-append-to-body":!1,title:e.title,visible:e.open,width:"500px","append-to-body":""},on:{"update:visible":function(t){e.open=t}}},[a("el-form",{ref:"form",staticClass:"edit",attrs:{model:e.form,rules:e.rules,"label-width":"80px"}},[a("el-form-item",{attrs:{label:"操作单号",prop:"operationNo"}},[a("el-input",{attrs:{placeholder:"操作单号"},model:{value:e.form.operationNo,callback:function(t){e.$set(e.form,"operationNo",t)},expression:"form.operationNo"}})],1),a("el-form-item",{attrs:{label:"服务类型",prop:"serviceTypeId"}},[a("tree-select",{attrs:{flat:!1,multiple:!1,pass:e.form.serviceTypeId,placeholder:"服务类型",type:"serviceType"},on:{return:e.getServiceTypeId}})],1),a("el-form-item",{attrs:{label:"服务",prop:"rctId"}},[a("el-input",{attrs:{placeholder:"服务ID"},model:{value:e.form.rctId,callback:function(t){e.$set(e.form,"rctId",t)},expression:"form.rctId"}})],1),a("el-form-item",{attrs:{label:"进度名称",prop:"processId"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"进度名称"},model:{value:e.form.processId,callback:function(t){e.$set(e.form,"processId",t)},expression:"form.processId"}},e._l(e.processList,(function(e){return a("el-option",{key:e.processId,attrs:{label:e.serviceType+"/"+e.processLocalName,value:e.processId}})})),1)],1),a("el-form-item",{attrs:{label:"进度状态",prop:"processStatusId"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"进度状态"},model:{value:e.form.processStatusId,callback:function(t){e.$set(e.form,"processStatusId",t)},expression:"form.processStatusId"}},e._l(e.processStatusList,(function(e){return a("el-option",{key:e.processStatusId,attrs:{label:e.processStatusLocalName,value:e.processStatusId}})})),1)],1),a("el-form-item",{attrs:{label:"发生时间",prop:"happenTime"}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{clearable:"",type:"date","value-format":"yyyy-MM-dd",placeholder:"发生时间"},model:{value:e.form.happenTime,callback:function(t){e.$set(e.form,"happenTime",t)},expression:"form.happenTime"}})],1),a("el-form-item",{attrs:{label:"备注",prop:"remark"}},[a("el-input",{attrs:{placeholder:"备注"},model:{value:e.form.remark,callback:function(t){e.$set(e.form,"remark",t)},expression:"form.remark"}})],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("确 定")]),a("el-button",{on:{click:e.cancel}},[e._v("取 消")])],1)],1)],1)},s=[],o=a("5530"),n=(a("d81d"),a("3a13")),l=a("23c0"),i=a("1abc"),c={name:"Operationalprocess",data:function(){return{showLeft:3,showRight:21,loading:!0,ids:[],single:!0,multiple:!0,showSearch:!0,total:0,operationalprocessList:[],title:"",open:!1,queryParams:{pageNum:1,pageSize:20,serviceTypeId:null,rctId:null,processId:null,processStatusId:null},form:{},processList:[],processStatusList:[],rules:{}}},watch:{showSearch:function(e){!0===e?(this.showRight=21,this.showLeft=3):(this.showRight=24,this.showLeft=0)}},created:function(){var e=this;this.getList(),Object(l["e"])({pageNum:1,pageSize:200}).then((function(t){e.processList=t.rows})),Object(i["e"])({pageNum:1,pageSize:100}).then((function(t){e.processStatusList=t.rows}))},methods:{getList:function(){var e=this;this.loading=!0,Object(n["e"])(this.queryParams).then((function(t){e.operationalprocessList=t.rows,e.total=t.total,e.loading=!1}))},cancel:function(){this.open=!1,this.reset()},reset:function(){this.form={operationalProcessId:null,operationNo:null,serviceTypeId:null,rctId:null,processId:null,processStatusId:null,happenTime:null,remark:null,createBy:null,createTime:null,updateBy:null,updateTime:null,deleteBy:null,deleteTime:null,deleteStatus:"0"},this.resetForm("form")},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.resetForm("queryForm"),this.handleQuery()},handleStatusChange:function(e){var t=this,a="0"===e.status?"启用":"停用";this.$confirm('确认要"'+a+"吗？","提示",{customClass:"modal-confirm"}).then((function(){return Object(n["b"])(e.operationalProcessId,e.status)})).then((function(){t.$modal.msgSuccess(a+"成功")})).catch((function(){e.status="0"===e.status?"1":"0"}))},handleSelectionChange:function(e){this.ids=e.map((function(e){return e.operationalProcessId})),this.single=1!==e.length,this.multiple=!e.length},handleAdd:function(){this.reset(),this.open=!0,this.title="添加操作进度"},handleUpdate:function(e){var t=this;this.reset();var a=e.operationalProcessId||this.ids;Object(n["d"])(a).then((function(e){t.form=e.data,t.open=!0,t.title="修改操作进度"}))},submitForm:function(){var e=this;this.$refs["form"].validate((function(t){t&&(null!=e.form.operationalProcessId?Object(n["f"])(e.form).then((function(t){e.$modal.msgSuccess("修改成功"),e.open=!1,e.getList()})):Object(n["a"])(e.form).then((function(t){e.$modal.msgSuccess("新增成功"),e.open=!1,e.getList()})))}))},handleDelete:function(e){var t=this,a=e.operationalProcessId||this.ids;this.$confirm('是否确认删除操作进度编号为"'+a+'"的数据项？',"提示",{customClass:"modal-confirm"}).then((function(){return Object(n["c"])(a)})).then((function(){t.getList(),t.$modal.msgSuccess("删除成功")})).catch((function(){}))},handleExport:function(){this.download("system/operationalprocess/export",Object(o["a"])({},this.queryParams),"operationalprocess_".concat((new Date).getTime(),".xlsx"))},getServiceTypeId:function(e){this.form.serviceTypeId=e}}},p=c,u=a("2877"),d=Object(u["a"])(p,r,s,!1,null,null,null);t["default"]=d.exports}}]);