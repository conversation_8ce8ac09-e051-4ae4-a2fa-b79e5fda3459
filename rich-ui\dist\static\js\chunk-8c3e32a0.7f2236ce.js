(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-8c3e32a0"],{"15b6":function(e,t,n){},"1a30":function(e,t,n){"use strict";n("15b6")},"74b1":function(e,t,n){"use strict";n.d(t,"d",(function(){return o})),n.d(t,"c",(function(){return r})),n.d(t,"a",(function(){return l})),n.d(t,"g",(function(){return a})),n.d(t,"b",(function(){return i})),n.d(t,"e",(function(){return c})),n.d(t,"f",(function(){return u}));var s=n("b775");function o(e){return Object(s["a"])({url:"/system/post/list",method:"get",params:e})}function r(e){return Object(s["a"])({url:"/system/post/"+e,method:"get"})}function l(e){return Object(s["a"])({url:"/system/post",method:"post",data:e})}function a(e){return Object(s["a"])({url:"/system/post",method:"put",data:e})}function i(e){return Object(s["a"])({url:"/system/post/"+e,method:"delete"})}function c(e){return Object(s["a"])({url:"/system/post/underUser/"+e,method:"get"})}function u(e){return Object(s["a"])({url:"/system/post/user/"+e,method:"get"})}},a6dc:function(e,t,n){"use strict";n.d(t,"e",(function(){return o})),n.d(t,"c",(function(){return r})),n.d(t,"a",(function(){return l})),n.d(t,"g",(function(){return a})),n.d(t,"b",(function(){return i})),n.d(t,"d",(function(){return c})),n.d(t,"f",(function(){return u}));var s=n("b775");function o(e){return Object(s["a"])({url:"/system/menu/list",method:"get",params:e})}function r(e){return Object(s["a"])({url:"/system/menu/"+e,method:"get"})}function l(e){return Object(s["a"])({url:"/system/menu",method:"post",data:e})}function a(e){return Object(s["a"])({url:"/system/menu",method:"put",data:e})}function i(e){return Object(s["a"])({url:"/system/menu/"+e,method:"delete"})}function c(){return Object(s["a"])({url:"/system/menu/limitRoleMenu",method:"get"})}function u(e,t){return Object(s["a"])({url:"/system/menu/listMenuByRole",method:"get",params:{deptId:e,positionId:t}})}},f794:function(e,t,n){"use strict";n.r(t);var s=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"app-container"},[n("el-row",{attrs:{gutter:20}},[n("el-col",{attrs:{span:e.showLeft}},[n("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],ref:"queryForm",staticClass:"query",attrs:{inline:!0,model:e.queryParams,size:"mini"}},[n("el-form-item",{attrs:{label:"搜索",prop:"menuQuery"}},[n("el-input",{staticStyle:{width:"100%"},attrs:{clearable:"",placeholder:"菜单名称"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.menuQuery,callback:function(t){e.$set(e.queryParams,"menuQuery",t)},expression:"queryParams.menuQuery"}})],1),n("el-form-item",[n("el-button",{attrs:{icon:"el-icon-search",size:"mini",type:"primary"},on:{click:e.handleQuery}},[e._v("搜索")]),n("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1)],1),n("el-col",{attrs:{span:e.showRight}},[n("el-row",{staticClass:"mb8",attrs:{gutter:10}},[n("el-col",{attrs:{span:1.5}},[n("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:menu:add"],expression:"['system:menu:add']"}],attrs:{icon:"el-icon-plus",plain:"",size:"mini",type:"primary"},on:{click:e.handleAdd}},[e._v("新增 ")])],1),n("el-col",{attrs:{span:1.5}},[n("el-button",{attrs:{icon:"el-icon-sort",plain:"",size:"mini",type:"info"},on:{click:e.toggleExpandAll}},[e._v("展开/折叠 ")])],1),n("right-toolbar",{attrs:{showSearch:e.showSearch},on:{"update:showSearch":function(t){e.showSearch=t},"update:show-search":function(t){e.showSearch=t},queryTable:e.getList}})],1),e.refreshTable?n("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.menuList,"default-expand-all":e.isExpandAll,"tree-props":{children:"children",hasChildren:"hasChildren"},"row-key":"menuId"}},[n("el-table-column",{attrs:{"show-tooltip-when-overflow":!0,label:"菜单名称",prop:"menuName"}}),n("el-table-column",{attrs:{label:"权限标识",prop:"perms",width:"300"}}),n("el-table-column",{attrs:{label:"组件路径",prop:"component",width:"300"}}),n("el-table-column",{key:"orderNum",attrs:{align:"center",label:"排序",prop:"orderNum",width:"48"}}),n("el-table-column",{attrs:{align:"center",label:"图标",prop:"icon",width:"48"},scopedSlots:e._u([{key:"default",fn:function(e){return[n("svg-icon",{attrs:{"icon-class":e.row.icon}})]}}],null,!1,3094025326)}),n("el-table-column",{attrs:{align:"center",label:"状态",prop:"status",width:"68"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("dict-tag",{attrs:{options:e.dict.type.sys_normal_disable,value:t.row.status}})]}}],null,!1,2802338569)}),n("el-table-column",{attrs:{align:"center","class-name":"small-padding fixed-width",label:"操作",width:"170"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:menu:add"],expression:"['system:menu:add']"}],staticStyle:{"margin-right":"-8px"},attrs:{icon:"el-icon-plus",size:"mini",type:"primary"},on:{click:function(n){return e.handleAdd(t.row)}}},[e._v("新增 ")]),n("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:menu:edit"],expression:"['system:menu:edit']"}],staticStyle:{"margin-right":"-8px"},attrs:{icon:"el-icon-edit",size:"mini",type:"success"},on:{click:function(n){return e.handleUpdate(t.row)}}},[e._v("修改 ")]),0!=t.row.parentId?n("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:menu:remove"],expression:"['system:menu:remove']"}],staticStyle:{"margin-right":"-8px"},attrs:{icon:"el-icon-delete",size:"mini",type:"danger"},on:{click:function(n){return e.handleDelete(t.row)}}},[e._v("删除 ")]):e._e()]}}],null,!1,3356956326)})],1):e._e()],1)],1),n("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"},{name:"dialogDragWidth",rawName:"v-dialogDragWidth"}],attrs:{"close-on-click-modal":!1,"modal-append-to-body":!1,title:e.title,visible:e.open,"append-to-body":"",width:"680px"},on:{"update:visible":function(t){e.open=t}}},[n("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"100px"}},[n("el-row",[n("el-col",{attrs:{span:24}},[n("el-form-item",{attrs:{label:"上级菜单"}},[n("treeselect",{attrs:{normalizer:e.normalizer,options:e.menuOptions,"show-count":!0,placeholder:"选择上级菜单"},model:{value:e.form.parentId,callback:function(t){e.$set(e.form,"parentId",t)},expression:"form.parentId"}})],1)],1),n("el-col",{attrs:{span:24}},[n("el-form-item",{attrs:{label:"菜单类型",prop:"menuType"}},[n("el-radio-group",{model:{value:e.form.menuType,callback:function(t){e.$set(e.form,"menuType",t)},expression:"form.menuType"}},[n("el-radio",{attrs:{label:"M"}},[e._v("目录")]),n("el-radio",{attrs:{label:"C"}},[e._v("菜单")]),n("el-radio",{attrs:{label:"F"}},[e._v("按钮")])],1)],1)],1),"F"!=e.form.menuType?n("el-col",{attrs:{span:24}},[n("el-form-item",{attrs:{label:"菜单图标",prop:"icon"}},[n("el-popover",{attrs:{placement:"bottom-start",trigger:"click",width:"460"},on:{show:function(t){return e.$refs["iconSelect"].reset()}}},[n("IconSelect",{ref:"iconSelect",attrs:{"active-icon":e.form.icon},on:{selected:e.selected}}),n("el-input",{attrs:{slot:"reference",placeholder:"点击选择图标",readonly:""},slot:"reference",model:{value:e.form.icon,callback:function(t){e.$set(e.form,"icon",t)},expression:"form.icon"}},[e.form.icon?n("svg-icon",{staticClass:"el-input__icon",staticStyle:{width:"25px"},attrs:{slot:"prefix","icon-class":e.form.icon},slot:"prefix"}):n("i",{staticClass:"el-icon-search el-input__icon",attrs:{slot:"prefix"},slot:"prefix"})],1)],1)],1)],1):e._e(),n("el-col",{attrs:{span:12}},[n("el-form-item",{attrs:{label:"菜单名称",prop:"menuName"}},[n("el-input",{attrs:{placeholder:"菜单名称"},model:{value:e.form.menuName,callback:function(t){e.$set(e.form,"menuName",t)},expression:"form.menuName"}})],1)],1),n("el-col",{attrs:{span:12}},[n("el-form-item",{attrs:{label:"显示排序",prop:"orderNum"}},[n("el-input-number",{attrs:{controls:!1,min:0,"controls-position":"right"},model:{value:e.form.orderNum,callback:function(t){e.$set(e.form,"orderNum",t)},expression:"form.orderNum"}})],1)],1),"F"!=e.form.menuType?n("el-col",{attrs:{span:12}},[n("el-form-item",[n("span",{attrs:{slot:"label"},slot:"label"},[n("el-tooltip",{attrs:{content:"选择是外链则路由地址需要以`http(s)://`开头",placement:"top"}},[n("i",{staticClass:"el-icon-question"})]),e._v(" 是否外链 ")],1),n("el-radio-group",{model:{value:e.form.isFrame,callback:function(t){e.$set(e.form,"isFrame",t)},expression:"form.isFrame"}},[n("el-radio",{attrs:{label:"0"}},[e._v("是")]),n("el-radio",{attrs:{label:"1"}},[e._v("否")])],1)],1)],1):e._e(),"F"!=e.form.menuType?n("el-col",{attrs:{span:12}},[n("el-form-item",{attrs:{prop:"path"}},[n("span",{attrs:{slot:"label"},slot:"label"},[n("el-tooltip",{attrs:{content:"访问的路由地址，如：`user`，如外网地址需内链访问则以`http(s)://`开头",placement:"top"}},[n("i",{staticClass:"el-icon-question"})]),e._v(" 路由地址 ")],1),n("el-input",{attrs:{placeholder:"路由地址"},model:{value:e.form.path,callback:function(t){e.$set(e.form,"path",t)},expression:"form.path"}})],1)],1):e._e(),"C"==e.form.menuType?n("el-col",{attrs:{span:12}},[n("el-form-item",{attrs:{prop:"component"}},[n("span",{attrs:{slot:"label"},slot:"label"},[n("el-tooltip",{attrs:{content:"访问的组件路径，如：`system/user/index`，默认在`views`目录下",placement:"top"}},[n("i",{staticClass:"el-icon-question"})]),e._v(" 组件路径 ")],1),n("el-input",{attrs:{placeholder:"组件路径"},model:{value:e.form.component,callback:function(t){e.$set(e.form,"component",t)},expression:"form.component"}})],1)],1):e._e(),"M"!=e.form.menuType?n("el-col",{attrs:{span:12}},[n("el-form-item",[n("el-input",{attrs:{maxlength:"100",placeholder:"权限标识"},model:{value:e.form.perms,callback:function(t){e.$set(e.form,"perms",t)},expression:"form.perms"}}),n("span",{attrs:{slot:"label"},slot:"label"},[n("el-tooltip",{attrs:{content:"控制器中定义的权限字符，如：@PreAuthorize(`@ss.hasPermi('system:user:list')`)",placement:"top"}},[n("i",{staticClass:"el-icon-question"})]),e._v(" 权限字符 ")],1)],1)],1):e._e(),"C"==e.form.menuType?n("el-col",{attrs:{span:12}},[n("el-form-item",[n("el-input",{attrs:{maxlength:"255",placeholder:"路由参数"},model:{value:e.form.query,callback:function(t){e.$set(e.form,"query",t)},expression:"form.query"}}),n("span",{attrs:{slot:"label"},slot:"label"},[n("el-tooltip",{attrs:{content:'访问路由的默认传递参数，如：`{"id": 1, "name": "ry"}`',placement:"top"}},[n("i",{staticClass:"el-icon-question"})]),e._v(" 路由参数 ")],1)],1)],1):e._e(),"C"==e.form.menuType?n("el-col",{attrs:{span:12}},[n("el-form-item",[n("span",{attrs:{slot:"label"},slot:"label"},[n("el-tooltip",{attrs:{content:"选择是则会被`keep-alive`缓存，需要匹配组件的`name`和地址保持一致",placement:"top"}},[n("i",{staticClass:"el-icon-question"})]),e._v(" 是否缓存 ")],1),n("el-radio-group",{model:{value:e.form.isCache,callback:function(t){e.$set(e.form,"isCache",t)},expression:"form.isCache"}},[n("el-radio",{attrs:{label:"0"}},[e._v("缓存")]),n("el-radio",{attrs:{label:"1"}},[e._v("不缓存")])],1)],1)],1):e._e(),"F"!=e.form.menuType?n("el-col",{attrs:{span:12}},[n("el-form-item",[n("span",{attrs:{slot:"label"},slot:"label"},[n("el-tooltip",{attrs:{content:"选择隐藏则路由将不会出现在侧边栏，但仍然可以访问",placement:"top"}},[n("i",{staticClass:"el-icon-question"})]),e._v(" 显示状态 ")],1),n("el-radio-group",{model:{value:e.form.visible,callback:function(t){e.$set(e.form,"visible",t)},expression:"form.visible"}},e._l(e.dict.type.sys_show_hide,(function(t){return n("el-radio",{key:t.value,attrs:{label:t.value}},[e._v(e._s(t.label)+" ")])})),1)],1)],1):e._e(),"F"!=e.form.menuType?n("el-col",{attrs:{span:12}},[n("el-form-item",[n("span",{attrs:{slot:"label"},slot:"label"},[n("el-tooltip",{attrs:{content:"选择停用则路由将不会出现在侧边栏，也不能被访问",placement:"top"}},[n("i",{staticClass:"el-icon-question"})]),e._v(" 菜单状态 ")],1),n("el-radio-group",{model:{value:e.form.status,callback:function(t){e.$set(e.form,"status",t)},expression:"form.status"}},e._l(e.dict.type.sys_normal_disable,(function(t){return n("el-radio",{key:t.value,attrs:{label:t.value}},[e._v(e._s(t.label)+" ")])})),1)],1)],1):e._e(),"C"==e.form.menuType?n("el-col",{attrs:{span:12}},[n("el-form-item",{attrs:{label:"部门"}},[n("tree-select",{attrs:{pass:e.form.deptList,dbn:!0,multiple:!0,placeholder:"选择部门",type:"dept"},on:{return:e.batchDeptIds,returnData:e.deptList}})],1)],1):e._e(),"C"==e.form.menuType?n("el-col",{attrs:{span:12}},[n("el-form-item",{attrs:{label:"职级"}},[n("el-select",{ref:"post",staticStyle:{width:"100%"},attrs:{clearable:"",filterable:"",placeholder:"选择权限授权的最低职级"},model:{value:e.form.positionId,callback:function(t){e.$set(e.form,"positionId",t)},expression:"form.positionId"}},e._l(e.postOptions,(function(t){return n("el-option",{key:t.positionId,attrs:{disabled:1==t.status,label:t.positionShortName,value:t.positionId}},[n("span",[e._v(e._s(t.positionShortName))]),n("span",[e._v(e._s(t.positionLocalName))])])})),1)],1)],1):e._e()],1)],1),n("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[n("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("确 定")]),n("el-button",{on:{click:e.cancel}},[e._v("取 消")])],1)],1)],1)},o=[],r=(n("14d9"),n("caad"),n("2532"),n("4de4"),n("d3b7"),n("a6dc")),l=n("ca17"),a=n.n(l),i=(n("542c"),function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"icon-body"},[n("el-input",{staticStyle:{position:"relative"},attrs:{clearable:"",placeholder:"图标名称"},on:{clear:e.filterIcons,input:e.filterIcons},model:{value:e.name,callback:function(t){e.name=t},expression:"name"}},[n("i",{staticClass:"el-icon-search el-input__icon",attrs:{slot:"suffix"},slot:"suffix"})]),n("div",{staticClass:"icon-list"},[n("div",{staticClass:"list-container"},e._l(e.iconList,(function(t,s){return n("div",{key:s,staticClass:"icon-item-wrapper",on:{click:function(n){return e.selectedIcon(t)}}},[n("div",{class:["icon-item",{active:e.activeIcon===t}]},[n("svg-icon",{staticStyle:{height:"25px",width:"16px"},attrs:{"icon-class":t,"class-name":"icon"}}),n("span",[e._v(e._s(t))])],1)])})),0)])],1)}),c=[],u=(n("b0c0"),n("ddb0"),n("d81d"),n("ac1f"),n("466d"),n("23f1")),m=function(e){return e.keys()},p=/\.\/(.*)\.svg/,d=m(u).map((function(e){return e.match(p)[1]})),f=d,h={name:"IconSelect",props:{activeIcon:{type:String}},data:function(){return{name:"",iconList:f}},methods:{filterIcons:function(){var e=this;this.iconList=f,this.name&&(this.iconList=this.iconList.filter((function(t){return t.includes(e.name)})))},selectedIcon:function(e){this.$emit("selected",e),document.body.click()},reset:function(){this.name="",this.iconList=f}}},b=h,v=(n("1a30"),n("2877")),y=Object(v["a"])(b,i,c,!1,null,"170ecc66",null),g=y.exports,_=n("74b1"),w={name:"Menu",dicts:["sys_show_hide","sys_normal_disable"],components:{Treeselect:a.a,IconSelect:g},data:function(){return{showLeft:3,showRight:21,loading:!0,showSearch:!1,menuList:[],menuOptions:[],postOptions:[],title:"",open:!1,isExpandAll:!1,refreshTable:!0,queryParams:{menuQuery:null},form:{},rules:{menuName:[{required:!0,trigger:"blur"}],orderNum:[{required:!0,trigger:"blur"}],path:[{required:!0,trigger:"blur"}]}}},watch:{showSearch:function(e){1==e?(this.showRight=21,this.showLeft=3):(this.showRight=24,this.showLeft=0)}},created:function(){var e=this;this.getList(),Object(_["d"])().then((function(t){e.postOptions=t.rows}))},methods:{selected:function(e){this.form.icon=e},getList:function(){var e=this;this.loading=!0,Object(r["e"])(this.queryParams).then((function(t){e.menuList=e.handleTree(t.data,"menuId"),e.loading=!1}))},normalizer:function(e){return e.children&&!e.children.length&&delete e.children,{id:e.menuId,label:e.menuName,children:e.children}},getTreeselect:function(){var e=this;Object(r["e"])().then((function(t){e.menuOptions=[];var n={menuId:0,menuName:"主类目",children:[]};n.children=e.handleTree(t.data,"menuId"),e.menuOptions.push(n)}))},cancel:function(){this.open=!1,this.reset()},reset:function(){this.form={menuId:null,parentId:0,menuName:null,icon:null,menuType:"M",orderNum:null,isFrame:"1",isCache:"0",visible:"0",status:"0"},this.resetForm("form")},handleQuery:function(){this.getList()},resetQuery:function(){this.resetForm("queryForm"),this.handleQuery()},handleAdd:function(e){this.reset(),this.getTreeselect(),null!=e&&null!=e.menuId?this.form.parentId=e.menuId:this.form.parentId=0,this.open=!0,this.title="添加菜单"},toggleExpandAll:function(){var e=this;this.refreshTable=!1,this.isExpandAll=!this.isExpandAll,this.$nextTick((function(){e.refreshTable=!0}))},handleUpdate:function(e){var t=this;this.reset(),this.getTreeselect(),Object(r["c"])(e.menuId).then((function(e){t.form=e.data,t.open=!0,t.title="修改菜单"}))},submitForm:function(){var e=this;this.$refs["form"].validate((function(t){t&&(null!=e.form.menuId?Object(r["g"])(e.form).then((function(t){e.$modal.msgSuccess("修改成功"),e.open=!1,e.getList()})):Object(r["a"])(e.form).then((function(t){e.$modal.msgSuccess("新增成功"),e.open=!1,e.getList()})))}))},handleDelete:function(e){var t=this;this.$confirm('是否确认删除名称为"'+e.menuName+'"的数据项？',"提示",{customClass:"modal-confirm"}).then((function(){return Object(r["b"])(e.menuId)})).then((function(){t.getList(),t.$modal.msgSuccess("删除成功")})).catch((function(){}))},batchDeptIds:function(e){this.distributeRoles.deptIds=e},deptList:function(e){this.form.deptList.includes(e.deptLocalName)?this.form.deptList.push(e.deptLocalName):this.form.deptList=this.form.deptList.filter((function(t){return t!=e.deptLocalName}))}}},x=w,k=Object(v["a"])(x,s,o,!1,null,null,null);t["default"]=k.exports}}]);