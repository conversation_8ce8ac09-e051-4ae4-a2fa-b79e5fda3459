(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-e2ef1232"],{4923:function(e,t,n){"use strict";n.r(t);n("d81d"),n("d9e2");var a=n("b76a"),o=n.n(a),r=n("a85b"),i={itemBtns:function(e,t,n,a){var o=this.$listeners,r=o.copyItem,i=o.deleteItem;return[e("span",{class:"drawing-item-copy",attrs:{title:"复制"},on:{click:function(e){r(t,a),e.stopPropagation()}}},[e("i",{class:"el-icon-copy-document"})]),e("span",{class:"drawing-item-delete",attrs:{title:"删除"},on:{click:function(e){i(n,a),e.stopPropagation()}}},[e("i",{class:"el-icon-delete"})])]}},l={colFormItem:function(e,t,n,a){var o=this,l=this.$listeners.activeItem,s=this.activeId==t.formId?"drawing-item active-from-item":"drawing-item";return this.formConf.unFocusedComponentBorder&&(s+=" unfocus-bordered"),e("el-col",{attrs:{span:t.span},class:s,nativeOn:{click:function(e){l(t),e.stopPropagation()}}},[e("el-form-item",{attrs:{"label-width":t.labelWidth?"".concat(t.labelWidth,"px"):null,label:t.label,required:t.required}},[e(r["a"],{key:t.renderKey,attrs:{conf:t},on:{input:function(e){o.$set(t,"defaultValue",e)}}})]),i.itemBtns.apply(this,arguments)])},rowFormItem:function(e,t,n,a){var r=this.$listeners.activeItem,l=this.activeId==t.formId?"drawing-row-item active-from-item":"drawing-row-item",c=s.apply(this,arguments);return"flex"==t.type&&(c=e("el-row",{attrs:{type:t.type,justify:t.justify,align:t.align}},[c])),e("el-col",{attrs:{span:t.span}},[e("el-row",{attrs:{gutter:t.gutter},class:l,nativeOn:{click:function(e){r(t),e.stopPropagation()}}},[e("span",{class:"component-name"},[t.componentName]),e(o.a,{attrs:{list:t.children,animation:340,group:"componentsGroup"},class:"drag-wrapper"},[c]),i.itemBtns.apply(this,arguments)])])}};function s(e,t,n,a){var o=this;return Array.isArray(t.children)?t.children.map((function(n,a){var r=l[n.layout];return r?r.call(o,e,n,a,t.children):c()})):null}function c(){throw new Error("没有与".concat(this.element.layout,"匹配的layout"))}var p,u,d={components:{render:r["a"],draggable:o.a},props:["element","index","drawingList","activeId","formConf"],render:function(e){var t=l[this.element.layout];return t?t.call(this,e,this.element,this.index,this.drawingList):c()}},m=d,f=n("2877"),h=Object(f["a"])(m,p,u,!1,null,null,null);t["default"]=h.exports},a85b:function(e,t,n){"use strict";n("d3b7"),n("159b"),n("14d9"),n("b64b"),n("e9c4");var a=n("ed08"),o=Object(a["g"])("accept,accept-charset,accesskey,action,align,alt,async,autocomplete,autofocus,autoplay,autosave,bgcolor,border,buffered,challenge,charset,checked,cite,class,code,codebase,color,cols,colspan,content,http-equiv,name,contenteditable,contextmenu,controls,coords,data,datetime,default,defer,dir,dirname,disabled,download,draggable,dropzone,enctype,method,for,form,formaction,headers,height,hidden,high,href,hreflang,http-equiv,icon,id,ismap,itemprop,keytype,kind,label,lang,language,list,loop,low,manifest,max,maxlength,media,method,GET,POST,min,multiple,email,file,muted,name,novalidate,open,optimum,pattern,ping,placeholder,poster,preload,radiogroup,readonly,rel,required,reversed,rows,rowspan,sandbox,scope,scoped,seamless,selected,shape,size,type,text,password,sizes,span,spellcheck,src,srcdoc,srclang,srcset,start,step,style,summary,tabindex,target,title,type,usemap,value,width,wrap");function r(e,t,n){t.props.value=n,t.on.input=function(t){e.$emit("input",t)}}var i={"el-button":{default:function(e,t,n){return t[n]}},"el-input":{prepend:function(e,t,n){return e("template",{slot:"prepend"},[t[n]])},append:function(e,t,n){return e("template",{slot:"append"},[t[n]])}},"el-select":{options:function(e,t,n){var a=[];return t.options.forEach((function(t){a.push(e("el-option",{attrs:{label:t.label,value:t.value,disabled:t.disabled}}))})),a}},"el-radio-group":{options:function(e,t,n){var a=[];return t.options.forEach((function(n){"button"==t.optionType?a.push(e("el-radio-button",{attrs:{label:n.value}},[n.label])):a.push(e("el-radio",{attrs:{label:n.value,border:t.border}},[n.label]))})),a}},"el-checkbox-group":{options:function(e,t,n){var a=[];return t.options.forEach((function(n){"button"==t.optionType?a.push(e("el-checkbox-button",{attrs:{label:n.value}},[n.label])):a.push(e("el-checkbox",{attrs:{label:n.value,border:t.border}},[n.label]))})),a}},"el-upload":{"list-type":function(e,t,n){var a=[];return"picture-card"==t["list-type"]?a.push(e("i",{class:"el-icon-plus"})):a.push(e("el-button",{attrs:{size:"mini",type:"primary",icon:"el-icon-upload"}},[t.buttonText])),t.showTip&&a.push(e("div",{slot:"tip",class:"el-upload__tip"},["只能上传不超过 ",t.fileSize,t.sizeUnit," 的",t.accept,"文件"])),a}}};t["a"]={render:function(e){var t=this,n={attrs:{},props:{},on:{},style:{}},a=JSON.parse(JSON.stringify(this.conf)),l=[],s=i[a.tag];return s&&Object.keys(s).forEach((function(t){var n=s[t];a[t]&&l.push(n(e,a,t))})),Object.keys(a).forEach((function(e){var i=a[e];"vModel"==e?r(t,n,a.defaultValue):n[e]?n[e]=i:o(e)?n.attrs[e]=i:n.props[e]=i})),e(this.conf.tag,n,l)},props:["conf"]}}}]);