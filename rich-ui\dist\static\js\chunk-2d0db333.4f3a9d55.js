(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d0db333"],{"6f9f":function(e,t,s){"use strict";s.r(t);var a=function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("div",{staticClass:"app-container"},[s("el-row",{attrs:{gutter:20}},[s("el-col",{attrs:{span:e.showLeft}},[s("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],ref:"queryForm",staticClass:"query",attrs:{model:e.queryParams,size:"mini",inline:!0,"label-width":"68px"}},[s("el-form-item",{attrs:{label:"简称",prop:"issueTypeShortName"}},[s("el-input",{attrs:{placeholder:"简称",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.issueTypeShortName,callback:function(t){e.$set(e.queryParams,"issueTypeShortName",t)},expression:"queryParams.issueTypeShortName"}})],1),s("el-form-item",{attrs:{label:"中文名",prop:"issueTypeLocalName"}},[s("el-input",{attrs:{placeholder:"中文名",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.issueTypeLocalName,callback:function(t){e.$set(e.queryParams,"issueTypeLocalName",t)},expression:"queryParams.issueTypeLocalName"}})],1),s("el-form-item",{attrs:{label:"英文名",prop:"issueTypeEnName"}},[s("el-input",{attrs:{placeholder:"英文名",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.issueTypeEnName,callback:function(t){e.$set(e.queryParams,"issueTypeEnName",t)},expression:"queryParams.issueTypeEnName"}})],1),s("el-form-item",{attrs:{label:"排序",prop:"orderNum"}},[s("el-input",{attrs:{placeholder:"排序",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.orderNum,callback:function(t){e.$set(e.queryParams,"orderNum",t)},expression:"queryParams.orderNum"}})],1),s("el-form-item",[s("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"mini"},on:{click:e.handleQuery}},[e._v("搜索")]),s("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1)],1),s("el-col",{attrs:{span:e.showRight}},[s("el-row",{staticClass:"mb8",attrs:{gutter:10}},[s("el-col",{attrs:{span:1.5}},[s("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:docissuetype:add"],expression:"['system:docissuetype:add']"}],attrs:{type:"primary",plain:"",icon:"el-icon-plus",size:"mini"},on:{click:e.handleAdd}},[e._v("新增 ")])],1),s("el-col",{attrs:{span:1.5}},[s("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:docissuetype:edit"],expression:"['system:docissuetype:edit']"}],attrs:{type:"success",plain:"",icon:"el-icon-edit",size:"mini",disabled:e.single},on:{click:e.handleUpdate}},[e._v("修改 ")])],1),s("el-col",{attrs:{span:1.5}},[s("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:docissuetype:remove"],expression:"['system:docissuetype:remove']"}],attrs:{type:"danger",plain:"",icon:"el-icon-delete",size:"mini",disabled:e.multiple},on:{click:e.handleDelete}},[e._v("删除 ")])],1),s("el-col",{attrs:{span:1.5}},[s("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:docissuetype:export"],expression:"['system:docissuetype:export']"}],attrs:{type:"warning",plain:"",icon:"el-icon-download",size:"mini"},on:{click:e.handleExport}},[e._v("导出 ")])],1),s("right-toolbar",{attrs:{showSearch:e.showSearch},on:{"update:showSearch":function(t){e.showSearch=t},"update:show-search":function(t){e.showSearch=t},queryTable:e.getList}})],1),s("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.docissuetypeList},on:{"selection-change":e.handleSelectionChange}},[s("el-table-column",{attrs:{type:"selection",width:"28",align:"center"}}),s("el-table-column",{attrs:{label:"简称",align:"center",prop:"issueTypeShortName"}}),s("el-table-column",{attrs:{label:"中文名",align:"center",prop:"issueTypeLocalName"}}),s("el-table-column",{attrs:{label:"英文名",align:"center",prop:"issueTypeEnName"}}),s("el-table-column",{attrs:{label:"排序",align:"center",prop:"orderNum"}}),s("el-table-column",{attrs:{label:"状态",align:"center",prop:"status"},scopedSlots:e._u([{key:"default",fn:function(t){return[s("el-switch",{attrs:{"active-value":"0","inactive-value":"1"},on:{change:function(s){return e.handleStatusChange(t.row)}},model:{value:t.row.status,callback:function(s){e.$set(t.row,"status",s)},expression:"scope.row.status"}})]}}])}),s("el-table-column",{attrs:{label:"备注",align:"center",prop:"remark"}}),s("el-table-column",{attrs:{label:"操作",align:"center","class-name":"small-padding fixed-width"},scopedSlots:e._u([{key:"default",fn:function(t){return[s("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:docissuetype:edit"],expression:"['system:docissuetype:edit']"}],staticStyle:{"margin-right":"-8px"},attrs:{size:"mini",type:"success",icon:"el-icon-edit"},on:{click:function(s){return e.handleUpdate(t.row)}}},[e._v("修改 ")]),s("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:docissuetype:remove"],expression:"['system:docissuetype:remove']"}],staticStyle:{"margin-right":"-8px"},attrs:{size:"mini",type:"danger",icon:"el-icon-delete"},on:{click:function(s){return e.handleDelete(t.row)}}},[e._v("删除 ")])]}}])})],1),s("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total>0"}],attrs:{total:e.total,page:e.queryParams.pageNum,limit:e.queryParams.pageSize},on:{"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},pagination:e.getList}})],1)],1),s("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"},{name:"dialogDragWidth",rawName:"v-dialogDragWidth"}],attrs:{"close-on-click-modal":!1,"modal-append-to-body":!1,title:e.title,visible:e.open,width:"500px","append-to-body":""},on:{"update:visible":function(t){e.open=t}}},[s("el-form",{ref:"form",staticClass:"edit",attrs:{model:e.form,rules:e.rules,"label-width":"80px"}},[s("el-form-item",{attrs:{label:"简称",prop:"issueTypeShortName"}},[s("el-input",{attrs:{placeholder:"简称"},model:{value:e.form.issueTypeShortName,callback:function(t){e.$set(e.form,"issueTypeShortName",t)},expression:"form.issueTypeShortName"}})],1),s("el-form-item",{attrs:{label:"中文名",prop:"issueTypeLocalName"}},[s("el-input",{attrs:{placeholder:"中文名"},model:{value:e.form.issueTypeLocalName,callback:function(t){e.$set(e.form,"issueTypeLocalName",t)},expression:"form.issueTypeLocalName"}})],1),s("el-form-item",{attrs:{label:"英文名",prop:"issueTypeEnName"}},[s("el-input",{attrs:{placeholder:"英文名"},model:{value:e.form.issueTypeEnName,callback:function(t){e.$set(e.form,"issueTypeEnName",t)},expression:"form.issueTypeEnName"}})],1),s("el-form-item",{attrs:{label:"排序",prop:"orderNum"}},[s("el-input-number",{staticStyle:{width:"100%"},attrs:{controls:!1,placeholder:"排序"},model:{value:e.form.orderNum,callback:function(t){e.$set(e.form,"orderNum",t)},expression:"form.orderNum"}})],1),s("el-form-item",{attrs:{label:"备注",prop:"remark"}},[s("el-input",{attrs:{placeholder:"备注"},model:{value:e.form.remark,callback:function(t){e.$set(e.form,"remark",t)},expression:"form.remark"}})],1)],1),s("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[s("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("确 定")]),s("el-button",{on:{click:e.cancel}},[e._v("取 消")])],1)],1)],1)},r=[],i=s("5530"),n=(s("d81d"),s("3f21")),l={name:"Docissuetype",data:function(){return{showLeft:3,showRight:21,loading:!0,ids:[],single:!0,multiple:!0,showSearch:!0,total:0,docissuetypeList:[],title:"",open:!1,queryParams:{pageNum:1,pageSize:20,issueTypeShortName:null,issueTypeLocalName:null,issueTypeEnName:null,orderNum:null,status:null},form:{},rules:{}}},watch:{showSearch:function(e){!0===e?(this.showRight=21,this.showLeft=3):(this.showRight=24,this.showLeft=0)}},created:function(){this.getList()},methods:{getList:function(){var e=this;this.loading=!0,Object(n["e"])(this.queryParams).then((function(t){e.docissuetypeList=t.rows,e.total=t.total,e.loading=!1}))},cancel:function(){this.open=!1,this.reset()},reset:function(){this.form={issueTypeId:null,issueTypeShortName:null,issueTypeLocalName:null,issueTypeEnName:null,orderNum:null,status:"0",remark:null,createBy:null,createTime:null,updateBy:null,updateTime:null,deleteBy:null,deleteTime:null,deleteStatus:"0"},this.resetForm("form")},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.resetForm("queryForm"),this.handleQuery()},handleStatusChange:function(e){var t=this,s="0"===e.status?"启用":"停用";this.$confirm('确认要"'+s+"吗？","提示",{customClass:"modal-confirm"}).then((function(){return Object(n["b"])(e.issueTypeId,e.status)})).then((function(){t.$modal.msgSuccess(s+"成功")})).catch((function(){e.status="0"===e.status?"1":"0"}))},handleSelectionChange:function(e){this.ids=e.map((function(e){return e.issueTypeId})),this.single=1!==e.length,this.multiple=!e.length},handleAdd:function(){this.reset(),this.open=!0,this.title="添加文件出单方式"},handleUpdate:function(e){var t=this;this.reset();var s=e.issueTypeId||this.ids;Object(n["d"])(s).then((function(e){t.form=e.data,t.open=!0,t.title="修改文件出单方式"}))},submitForm:function(){var e=this;this.$refs["form"].validate((function(t){t&&(null!=e.form.issueTypeId?Object(n["g"])(e.form).then((function(t){e.$modal.msgSuccess("修改成功"),e.open=!1,e.getList()})):Object(n["a"])(e.form).then((function(t){e.$modal.msgSuccess("新增成功"),e.open=!1,e.getList()})))}))},handleDelete:function(e){var t=this,s=e.issueTypeId||this.ids;this.$confirm('是否确认删除文件出单方式编号为"'+s+'"的数据项？',"提示",{customClass:"modal-confirm"}).then((function(){return Object(n["c"])(s)})).then((function(){t.getList(),t.$modal.msgSuccess("删除成功")})).catch((function(){}))},handleExport:function(){this.download("system/docissuetype/export",Object(i["a"])({},this.queryParams),"docissuetype_".concat((new Date).getTime(),".xlsx"))}}},o=l,u=s("2877"),c=Object(u["a"])(o,a,r,!1,null,null,null);t["default"]=c.exports}}]);