(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d20fef7"],{b66c:function(t,e,o){"use strict";o.r(e);var n=function(){var t=this,e=t.$createElement,o=t._self._c||e;return o("div",[o("el-tooltip",{attrs:{placement:"top",disabled:t.scope.row.inquiryNo.length<6}},[o("div",{attrs:{slot:"content"},slot:"content"},[o("h6",{staticStyle:{margin:"0"}},[t._v(" "+t._s(t.scope.row.inquiryNo)+" ")])]),o("div",[o("h6",{staticStyle:{margin:"0",overflow:"hidden","text-overflow":"ellipsis","white-space":"nowrap"}},[t._v(" "+t._s(t.scope.row.inquiryNo)+" ")])])])],1)},i=[],s={name:"contract",props:["scope"],data:function(){return{size:this.$store.state.app.size||"mini"}}},r=s,c=o("2877"),a=Object(c["a"])(r,n,i,!1,null,"6d8fc83d",null);e["default"]=a.exports}}]);