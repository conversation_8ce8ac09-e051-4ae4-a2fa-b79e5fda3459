(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-689f2f24"],{"53cf":function(t,e,a){"use strict";a("53f5")},"53f5":function(t,e,a){},"7a64":function(t,e,a){t.exports=a.p+"static/img/pageFoot.png"},b782:function(t,e,a){"use strict";a.r(e);var i=function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{staticClass:"print-content",attrs:{id:"print-content"}},[t._m(0),i("div",{staticClass:"main-content",attrs:{id:"main-content"}},[i("h2",{staticClass:"title"},[t._v("DEBIT NOTE")]),i("el-row",[i("el-col",{attrs:{span:8}},[i("div",{staticClass:"form-item"},[t._v(" TO： ")]),i("div",{staticClass:"form-item"},[t._v(" ATTN： ")]),i("div",{staticClass:"form-item"},[t._v(" REF No： ")]),i("div",{staticClass:"form-item"},[t._v(" S/O No： ")]),i("div",{staticClass:"form-item"},[t._v(" LoadTime： ")])]),i("el-col",{attrs:{span:8}},[i("div",{staticClass:"form-item",staticStyle:{width:"100%",height:"21px"}}),i("div",{staticClass:"form-item"},[t._v(" Contract No： ")]),i("div",{staticClass:"form-item"},[t._v(" POL： ")]),i("div",{staticClass:"form-item"},[t._v(" POD： ")]),i("div",{staticClass:"form-item"},[t._v(" CARRIER： ")])]),i("el-col",{attrs:{span:8}},[i("div",{staticClass:"form-item"},[t._v(" Printing Date： ")]),i("div",{staticClass:"form-item"},[t._v(" Trans Type： ")]),i("div",{staticClass:"form-item"},[t._v(" RevTon： ")]),i("div",{staticClass:"form-item"},[t._v(" CtnrNo： ")])])],1),i("div",{staticStyle:{"font-size":"13px"}},[t._v("本次运杂费明细如下:")]),i("div",{staticStyle:{"font-size":"13px"}},[t._v("(The Rates Details is as following)：")]),i("el-table",{staticStyle:{width:"100%"},attrs:{data:t.tableData}},[i("el-table-column",{attrs:{align:"center",prop:"address"},scopedSlots:t._u([{key:"header",fn:function(e){return[i("div",{staticClass:"table-title"},[i("div",[t._v("收费项目")]),i("div",{staticStyle:{"line-height":"15px"}},[t._v("(FreightName)")])])]}}])}),i("el-table-column",{attrs:{align:"center",prop:"address"},scopedSlots:t._u([{key:"header",fn:function(e){return[i("div",{staticClass:"table-title"},[i("div",[t._v("备注")]),i("div",[t._v("(Remark)")])])]}}])}),i("el-table-column",{attrs:{align:"center",prop:"address"},scopedSlots:t._u([{key:"header",fn:function(e){return[i("div",{staticClass:"table-title"},[i("div",[t._v("币制")]),i("div",[t._v("(CrcSys)")])])]}}])}),i("el-table-column",{attrs:{align:"center",prop:"address"},scopedSlots:t._u([{key:"header",fn:function(e){return[i("div",{staticClass:"table-title"},[i("div",[t._v("单价")]),i("div",[t._v("(UnitPrice)")])])]}}])}),i("el-table-column",{attrs:{align:"center",prop:"address"},scopedSlots:t._u([{key:"header",fn:function(e){return[i("div",{staticClass:"table-title"},[i("div",[t._v("数量")]),i("div",[t._v("(Counter)")])])]}}])}),i("el-table-column",{attrs:{align:"center",prop:"address"},scopedSlots:t._u([{key:"header",fn:function(e){return[i("div",{staticClass:"table-title"},[i("div",[t._v("税率")]),i("div",[t._v("(CrcSys)")])])]}}])}),i("el-table-column",{attrs:{align:"center",prop:"address"},scopedSlots:t._u([{key:"header",fn:function(e){return[i("div",{staticClass:"table-title"},[i("div",[t._v("小计")]),i("div",[t._v("(SubTotal)")])])]}}])})],1),i("el-row",{staticStyle:{"border-top":"solid 2px black"}},[i("el-col",{attrs:{span:15}},[i("div",{staticStyle:{"font-size":"14px",color:"blue"}},[i("div",[t._v("关于汇率：")]),i("div",[t._v("请按本账单约定的币种和汇率对应金额进行结算 ----------\x3e>")]),i("div",[t._v("如需更改结算币种，则需以我司财务部重新确认的汇率为准。")])])]),i("el-col",{attrs:{span:9}},[i("div",{staticStyle:{"font-size":"14px"}},[t._v("总计(Total)：")])])],1),i("el-row",[i("el-col",{attrs:{span:14}},[i("div",{staticStyle:{"font-size":"13px","margin-top":"20px"}},[i("div",[t._v("我司公账如下："),i("span",{staticStyle:{"font-weight":"bold"}},[t._v("(不接受私账付款）")])]),i("div",[t._v("(Our Company Account Details is as following)：")]),i("div",{staticStyle:{"margin-left":"20px"}},[t._v(" 开户行：中国工商银行广州东城支行"),i("br"),t._v(" 户 名：广州瑞旗国际货运代理有限公司"),i("br"),t._v(" 账 号：3602 2015 0910 0052 834（RMB）"),i("br"),t._v(" 开户行：中国工商银行广州东城支行"),i("br"),t._v(" 户 名：广州瑞旗国际货运代理有限公司"),i("br"),t._v(" 账 号：3602 2015 1910 0371 687 (USD) 银行行号: ************"),i("br"),t._v(" 开户行代码（SWIFT CODE）：ICBKCNBJGDG ")])])]),i("el-col",{attrs:{span:10}},[i("img",{staticStyle:{width:"130px"},attrs:{src:a("ca0c")}})])],1)],1),t._m(1)])},s=[function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{attrs:{id:"header"}},[i("img",{staticClass:"image",attrs:{src:a("e881")}})])},function(){var t=this,e=t.$createElement,i=t._self._c||e;return i("div",{attrs:{id:"footer"}},[i("img",{staticClass:"image",attrs:{src:a("7a64")}})])}],l=(a("aae1"),{name:"PrintTemplate",created:function(){this.$route.params},data:function(){return{tableData:[]}}}),r=l,n=(a("53cf"),a("2877")),c=Object(n["a"])(r,i,s,!1,null,"3a08728d",null);e["default"]=c.exports},ca0c:function(t,e,a){t.exports=a.p+"static/img/signet.png"},e881:function(t,e,a){t.exports=a.p+"static/img/pageHead.png"}}]);