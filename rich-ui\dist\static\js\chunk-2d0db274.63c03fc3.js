(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d0db274"],{"6f3a":function(t,n,i){"use strict";i.r(n);i("b0c0"),i("7db0"),i("d3b7"),i("25f0");n["default"]=function(){function t(){this.name="scale"}return t.prototype.css=function(t,n){return t&&t.length&&n?t.css("transform","scale("+n+")"):null},t.prototype.createTarget=function(t,n,i){return this.target=$('<div class="hiprint-option-item">\n        <div class="hiprint-option-item-label">\n        缩放\n        </div>\n        <div class="hiprint-option-item-field">\n        <input type="number" value="1" step="0.1" min="0.1" max="3" class="auto-submit"/>\n        </div>\n    </div>'),this.target},t.prototype.getValue=function(){var t=this.target.find("input").val();if(t)return parseFloat(t.toString())},t.prototype.setValue=function(t){this.target.find("input").val(t)},t.prototype.destroy=function(){this.target.remove()},t}()}}]);