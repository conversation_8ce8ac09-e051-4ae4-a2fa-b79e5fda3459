<template>
  <div class="app-container">
    <el-row :gutter="20">
      <el-col :span="showLeft">
        <el-form v-show="showSearch" ref="queryForm" :inline="true" :model="queryParams" class="query"
                 label-width="68px"
                 size="mini"
        >
          <el-form-item label="出仓单号" prop="outboundNo">
            <el-input
              v-model="queryParams.outboundNo"
              clearable
              placeholder="出仓单号"
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="客户代码" prop="clientCode">
            <el-input
              v-model="queryParams.clientCode"
              clearable
              placeholder="客户代码"
              @keyup.enter.native="handleQuery"
            />
          </el-form-item>
          <el-form-item label="出仓日期" prop="outboundDate">
            <el-date-picker v-model="queryParams.outboundDateRange"
                            clearable style="width: 100%"
                            placeholder="出仓日期"
                            type="daterange"
                            :default-time="['00:00:00', '23:59:59']"
                            value-format="yyyy-MM-dd HH:mm:ss"
            >
            </el-date-picker>
          </el-form-item>
          <el-form-item>
            <el-button icon="el-icon-search" size="mini" type="primary" @click="handleQuery">搜索</el-button>
            <el-button icon="el-icon-refresh" size="mini" @click="resetQuery">重置</el-button>
          </el-form-item>
        </el-form>
      </el-col>
      <el-col :span="showRight">
        <el-row :gutter="10" class="mb8">
          <el-col :span="1.5">
            <el-button
              v-hasPermi="['system:inventory:export']"
              icon="el-icon-download"
              plain
              size="mini"
              type="primary"
              @click="handleExport"
            >导出
            </el-button>
          </el-col>
          <right-toolbar :showSearch.sync="showSearch" @queryTable="getList"></right-toolbar>
        </el-row>

        <!--出仓记录列表(预出仓记录列表)-->
        <el-table v-loading="loading" :data="outboundrecordList" @selection-change="handleSelectionChange"
                  @row-dblclick="findOutboundRecord"
        >
          <el-table-column align="center" type="selection" width="28"/>
          <el-table-column align="center" label="出仓单号" prop="outboundNo"/>
          <el-table-column align="center" label="客户单号" prop="customerOrderNo"/>
          <el-table-column align="center" label="客户代码" prop="clientCode"/>
          <el-table-column align="center" label="客户名称" prop="clientName" show-overflow-tooltip/>
          <el-table-column align="center" label="操作员" prop="operator"/>
          <el-table-column align="center" label="柜型" prop="containerType"/>
          <el-table-column align="center" label="柜号" prop="containerNo" show-overflow-tooltip/>
          <el-table-column align="center" label="封号" prop="sealNo"/>
          <el-table-column align="center" label="出仓日期" prop="outboundDate" width="100">
            <template slot-scope="scope">
              <span>{{ parseTime(scope.row.outboundDate, "{y}-{m}-{d}") }}</span>
            </template>
          </el-table-column>
          <el-table-column align="center" label="仓库报价" prop="warehouseQuote"/>
          <el-table-column align="center" label="工人装柜费" prop="workerLoadingFee"/>
          <el-table-column align="center" label="仓管代收" prop="warehouseCollection"/>
          <el-table-column align="center" label="补收入仓费" prop="additionalStorageFee">
            <template slot-scope="scope">
              <el-popover
                placement="top"
                popper-class="fee-edit-popover"
                trigger="click"
                width="200"
              >
                <el-input-number
                  v-model="scope.row.additionalStorageFee"
                  :controls="false"
                  :precision="2"
                  size="mini"
                  style="width: 100%"
                  @change="handleFeeChange(scope.row, 'additionalStorageFee', $event)"
                />
                <span slot="reference">{{ scope.row.additionalStorageFee || 0 }}</span>
              </el-popover>
            </template>
          </el-table-column>
          <el-table-column align="center" label="已收入仓费" prop="receivedStorageFee">
            <template slot-scope="scope">
              <el-popover
                placement="top"
                popper-class="fee-edit-popover"
                trigger="click"
                width="200"
              >
                <el-input-number
                  v-model="scope.row.receivedStorageFee"
                  :controls="false"
                  :precision="2"
                  size="mini"
                  style="width: 100%"
                  @change="handleFeeChange(scope.row, 'receivedStorageFee', $event)"
                />
                <span slot="reference">{{ scope.row.receivedStorageFee || 0 }}</span>
              </el-popover>
            </template>
          </el-table-column>
          <el-table-column align="center" label="未收卸货费" prop="unpaidUnloadingFee">
            <template slot-scope="scope">
              <el-popover
                placement="top"
                popper-class="fee-edit-popover"
                trigger="click"
                width="200"
              >
                <el-input-number
                  v-model="scope.row.unpaidUnloadingFee"
                  :controls="false"
                  :precision="2"
                  size="mini"
                  style="width: 100%"
                  @change="handleFeeChange(scope.row, 'unpaidUnloadingFee', $event)"
                />
                <span slot="reference">{{ scope.row.unpaidUnloadingFee || 0 }}</span>
              </el-popover>
            </template>
          </el-table-column>
          <el-table-column align="center" label="实付卸货费" prop="receivedUnloadingFee">
            <template slot-scope="scope">
              <el-popover
                placement="top"
                popper-class="fee-edit-popover"
                trigger="click"
                width="200"
              >
                <el-input-number
                  v-model="scope.row.receivedUnloadingFee"
                  :controls="false"
                  :precision="2"
                  size="mini"
                  style="width: 100%"
                  @change="handleFeeChange(scope.row, 'receivedUnloadingFee', $event)"
                />
                <span slot="reference">{{ scope.row.receivedUnloadingFee || 0 }}</span>
              </el-popover>
            </template>
          </el-table-column>
          <el-table-column align="center" label="未收打包费" prop="unpaidPackingFee">
            <template slot-scope="scope">
              <el-popover
                placement="top"
                popper-class="fee-edit-popover"
                trigger="click"
                width="200"
              >
                <el-input-number
                  v-model="scope.row.unpaidPackingFee"
                  :controls="false"
                  :precision="2"
                  size="mini"
                  style="width: 100%"
                  @change="handleFeeChange(scope.row, 'unpaidPackingFee', $event)"
                />
                <span slot="reference">{{ scope.row.unpaidPackingFee || 0 }}</span>
              </el-popover>
            </template>
          </el-table-column>
          <el-table-column align="center" label="实付打包费" prop="receivedPackingFee">
            <template slot-scope="scope">
              <el-popover
                placement="top"
                popper-class="fee-edit-popover"
                trigger="click"
                width="200"
              >
                <el-input-number
                  v-model="scope.row.receivedPackingFee"
                  :controls="false"
                  :precision="2"
                  size="mini"
                  style="width: 100%"
                  @change="handleFeeChange(scope.row, 'receivedPackingFee', $event)"
                />
                <span slot="reference">{{ scope.row.receivedPackingFee || 0 }}</span>
              </el-popover>
            </template>
          </el-table-column>
          <el-table-column align="center" label="物流代垫费" prop="logisticsAdvanceFee">
            <template slot-scope="scope">
              <el-popover
                placement="top"
                popper-class="fee-edit-popover"
                trigger="click"
                width="200"
              >
                <el-input-number
                  v-model="scope.row.logisticsAdvanceFee"
                  :controls="false"
                  :precision="2"
                  size="mini"
                  style="width: 100%"
                  @change="handleFeeChange(scope.row, 'logisticsAdvanceFee', $event)"
                />
                <span slot="reference">{{ scope.row.logisticsAdvanceFee || 0 }}</span>
              </el-popover>
            </template>
          </el-table-column>
          <el-table-column align="center" label="超期租金单价" prop="overdueRentalUnitPrice"/>
          <el-table-column align="center" label="超期租金" prop="overdueRentalFee"/>
          <el-table-column align="center" label="唛头" prop="sqdShippingMark" show-overflow-tooltip/>
          <el-table-column align="center" label="总货名" prop="cargoName" show-overflow-tooltip/>
          <el-table-column align="center" label="免堆期" prop="freeStackPeriod" width="50"/>
          <el-table-column align="center" class-name="small-padding fixed-width" label="操作">
            <template slot-scope="scope">
              <!--<el-button
                v-hasPermi="['system:outboundrecord:edit']"
                icon="el-icon-edit"
                size="mini"
                style="margin-right: -8px"
                type="success"
                @click="handleUpdate(scope.row)"
              >修改
              </el-button>-->
              <el-button
                v-hasPermi="['system:outboundrecord:remove']"
                icon="el-icon-delete"
                size="mini"
                style="margin-right: -8px"
                type="danger"
                @click="handleDelete(scope.row)"
              >删除
              </el-button>
            </template>
          </el-table-column>
        </el-table>
        <pagination
          v-show="total>0"
          :limit.sync="queryParams.pageSize"
          :page.sync="queryParams.pageNum"
          :total="total"
          @pagination="getList"
        />
      </el-col>
    </el-row>

    <!-- 出仓对话框 -->
    <el-dialog
      v-dialogDrag
      v-dialogDragWidth :close-on-click-modal="false" :modal-append-to-body="false" :title="'出库单'"
      :visible.sync="openOutbound"
      append-to-body
      width="80%"
    >
      <el-form ref="outboundForm" :model="outboundForm" :rules="rules" class="edit" label-width="80px">
        <el-row :gutter="10">
          <el-row :gutter="10">
            <el-col>
              <el-col :span="6">
                <el-form-item label="出仓单号" prop="outboundNo">
                  <el-input v-model="outboundForm.outboundNo" class="disable-form" disabled placeholder="出仓单号"/>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="客户单号" prop="outboundNo">
                  <el-input v-model="outboundForm.customerOrderNo" placeholder="客户单号"/>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="客户代码" prop="outboundNo">
                  <tree-select :flat="false" :multiple="false" :pass="outboundForm.clientCode"
                               :placeholder="'客户代码'"
                               :type="'warehouseClient'" @return="outboundForm.clientCode=$event"
                               @returnData="outboundClient($event)"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="客户名称" prop="outboundNo">
                  <el-input v-model="outboundForm.clientName" class="disable-form" disabled
                            placeholder="客户名称"
                  />
                </el-form-item>
              </el-col>
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col>
              <el-col :span="6">
                <el-form-item label="计划出仓" prop="inboundDate">
                  <el-date-picker v-model="outboundForm.plannedOutboundDate"
                                  clearable
                                  placeholder="计划出仓日期"
                                  style="width: 100%"
                                  type="date"
                                  value-format="yyyy-MM-dd"
                  >
                  </el-date-picker>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="出仓方式" prop="outboundType">
                  <el-select v-model="outboundForm.outboundType" placeholder="出仓方式" style="width: 100%">
                    <el-option label="整柜" value="整柜"></el-option>
                    <el-option label="散货" value="散货"></el-option>
                    <el-option label="快递" value="快递"></el-option>
                    <el-option label="其他" value="其他"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="柜型" prop="containerType">
                  <el-select v-model="outboundForm.containerType" style="width: 100%" @change="selectContainerType">
                    <el-option label="20GP" value="20GP"/>
                    <el-option label="20OT" value="20OT"/>
                    <el-option label="20FR" value="20FR"/>
                    <el-option label="TANK" value="TANK"/>
                    <el-option label="40GP" value="40GP"/>
                    <el-option label="40HQ" value="40HQ"/>
                    <el-option label="40NOR" value="40NOR"/>
                    <el-option label="40OT" value="40OT"/>
                    <el-option label="40FR" value="40FR"/>
                    <el-option label="40RH" value="40RH"/>
                    <el-option label="45HQ" value="45HQ"/>
                  </el-select>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="货物类型" prop="cargoType">
                  <el-select v-model="form.cargoType" placeholder="请选择货物类型" style="width: 100%">
                    <el-option label="普货" value="普货"></el-option>
                    <el-option label="大件" value="大件"></el-option>
                    <el-option label="鲜活" value="鲜活"></el-option>
                    <el-option label="危品" value="危品"></el-option>
                    <el-option label="冷冻" value="冷冻"></el-option>
                    <el-option label="标记" value="标记"></el-option>
                  </el-select>
                </el-form-item>
              </el-col>
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col>
              <el-col :span="6">
                <el-form-item label="柜号" prop="containerNo">
                  <el-input v-model="outboundForm.containerNo" placeholder="柜号"/>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="封号" prop="sealNo">
                  <el-input v-model="outboundForm.sealNo" placeholder="封号"/>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="车牌" prop="plateNumber">
                  <el-input v-model="outboundForm.plateNumber" placeholder="车牌"/>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="司机电话" prop="driverPhone">
                  <el-input v-model="outboundForm.driverPhone" placeholder="司机电话"/>
                </el-form-item>
              </el-col>
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col>
              <el-col :span="6">
                <el-form-item label="仓库报价" prop="warehouseQuote">
                  <el-input v-model="outboundForm.warehouseQuote" class="number" placeholder="仓库报价"/>
                  <!--<el-col :span="12">
                    <el-input v-model="outboundForm.difficultyWorkFee" class="number" placeholder="困难作业费"/>
                  </el-col>-->
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="仓管代收" prop="outboundNotes">
                  <el-input v-model="outboundForm.warehouseCollection" class="number" placeholder="仓管代收"/>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="工人装柜费" prop="workerLoadingFee">
                  <el-input v-model="outboundForm.workerLoadingFee" class="number" placeholder="工人装柜费"/>
                  <!--<el-col :span="12">
                    <el-input v-model="outboundForm.warehouseAdvanceOtherFee" class="number"
                              placeholder="仓库代付其他费用"
                    />
                  </el-col>-->
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="仓管代付" prop="outboundNotes">
                  <el-input v-model="outboundForm.warehousePay" class="number" placeholder="仓管代收"/>
                </el-form-item>
              </el-col>
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col>
              <el-col :span="12">
                <el-form-item label="操作要求" prop="operationRequirement">
                  <el-input v-model="outboundForm.operationRequirement" :autosize="{ minRows: 4}" maxlength="250"
                            placeholder="内容"
                            show-word-limit type="textarea"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="12">
                <el-form-item label="仓管指示" prop="outboundNote">
                  <el-input v-model="outboundForm.outboundNote" :autosize="{ minRows: 4}" maxlength="250"
                            placeholder="内容"
                            show-word-limit type="textarea"
                  />
                </el-form-item>
              </el-col>
            </el-col>
          </el-row>
          <el-row :gutter="10">
            <el-col>
              <el-col :span="6">
                <el-form-item label="操作员" prop="operator">
                  <el-input v-model="outboundForm.operator" class="disable-form" disabled placeholder="操作员"/>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="下单日期" prop="orderDate">
                  <el-date-picker v-model="outboundForm.orderDate"
                                  class="disable-form" clearable disabled
                                  placeholder="出仓日期"
                                  style="width: 100%"
                                  type="date"
                                  value-format="yyyy-MM-dd"
                  >
                  </el-date-picker>
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-form-item label="出仓经手人" prop="outboundHandler">
                  <el-input v-model="outboundForm.outboundHandler" class="disable-form" disabled
                            placeholder="出仓经手人"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="6">
                <el-row>
                  <el-col :span="12">
                    <el-button :disabled="outboundForm.clientCode===null" type="primary" @click="warehouseConfirm">
                      {{ "仓管确认" }}
                    </el-button>
                  </el-col>
                  <el-col :span="12">
                    <el-button :disabled="outboundForm.clientCode===null" type="primary"
                               @click="loadPreOutboundInventoryList">
                      {{ "加载待出库" }}
                    </el-button>
                  </el-col>
                </el-row>
              </el-col>
            </el-col>
          </el-row>
        </el-row>
        <!--库存记录列表(未出库)-->
        <el-row :gutter="10">
          <el-col>
            <el-col>
              <el-table ref="table" v-loading="preOutboundInventoryListLoading"
                        :data="outboundForm.rsInventoryList" :load="loadChildInventory" :summary-method="getSummaries"
                        :tree-props="{children: 'children', hasChildren: 'hasChildren'}" lazy
                        max-height="300" row-key="inventoryId" show-summary
                        style="width: 100%;"
                        @selection-change="handleOutboundSelectionChange"
              >
                <el-table-column align="center" label="入仓流水号" prop="inboundSerialNo"/>
                <el-table-column align="center" label="货物明细" width="50">
                  <template slot-scope="scope">
                    <el-popover
                      trigger="click"
                      width="800"
                    >
                      <el-table :data="scope.row.rsCargoDetailsList"
                                @selection-change="(selectedRows) => handleOutboundCargoDetailSelectionChange(selectedRows,scope.row)"
                      >
                        <el-table-column align="center" type="selection" width="28"/>
                        <el-table-column
                          label="唛头"
                          prop="shippingMark"
                          width="150"
                        >
                        </el-table-column>
                        <el-table-column
                          label="货名"
                          prop="itemName"
                          width="150"
                        >
                        </el-table-column>
                        <el-table-column
                          label="箱数"
                          prop="boxCount"
                        >
                        </el-table-column>
                        <el-table-column
                          label="包装类型"
                          prop="packageType"
                        >
                        </el-table-column>
                        <el-table-column
                          label="单件毛重"
                          prop="unitGrossWeight"
                        >
                        </el-table-column>
                        <el-table-column
                          label="单件长"
                          prop="unitLength"
                        >
                        </el-table-column>
                        <el-table-column
                          label="单件宽"
                          prop="unitWidth"
                        >
                        </el-table-column>
                        <el-table-column
                          label="单件高"
                          prop="unitHeight"
                        >
                        </el-table-column>
                        <el-table-column
                          label="单件体积"
                          prop="unitVolume"
                        >
                        </el-table-column>
                        <el-table-column
                          label="破损标志"
                          prop="damageStatus"
                        >
                        </el-table-column>
                      </el-table>
                      <el-button slot="reference" style="margin: 0;padding: 5px">查看</el-button>
                    </el-popover>
                  </template>
                </el-table-column>
                <el-table-column align="center" label="入仓日期" prop="actualInboundTime" width="80">
                  <template slot-scope="scope">
                    <span>{{ parseTime(scope.row.actualInboundTime, "{y}-{m}-{d}") }}</span>
                  </template>
                </el-table-column>
                <el-table-column align="center" label="货代单号" prop="forwarderNo"/>
                <el-table-column align="center" label="客户代码" prop="clientCode"/>
                <el-table-column align="center" label="司机信息" prop="driverInfo"/>
                <el-table-column align="center" label="箱数" prop="totalBoxes" width="50"/>
                <el-table-column align="center" label="毛重" prop="totalGrossWeight" width="80"/>
                <el-table-column align="center" label="体积" prop="totalVolume" width="50"/>
                <el-table-column align="center" label="已收供应商" prop="receivedSupplier">
                  <template slot-scope="scope">
                    <el-popover
                      placement="top"
                      popper-class="fee-edit-popover"
                      trigger="click"
                      width="200"
                    >
                      <el-input-number
                        v-model="scope.row.receivedSupplier"
                        :controls="false"
                        :precision="2"
                        size="mini"
                        style="width: 100%"
                        @change="handleFeeChange(scope.row, 'receivedSupplier', $event)"
                      />
                      <span slot="reference">{{ scope.row.receivedSupplier || 0 }}</span>
                    </el-popover>
                  </template>
                </el-table-column>
                <el-table-column align="center" label="入仓费" prop="inboundFee">
                  <template slot-scope="scope">
                    <el-popover
                      placement="top"
                      popper-class="fee-edit-popover"
                      trigger="click"
                      width="200"
                    >
                      <el-input-number
                        v-model="scope.row.inboundFee"
                        :controls="false"
                        :precision="2"
                        size="mini"
                        style="width: 100%"
                        @change="handleFeeChange(scope.row, 'inboundFee', $event)"
                      />
                      <span slot="reference">{{ scope.row.inboundFee || 0 }}</span>
                    </el-popover>
                  </template>
                </el-table-column>
                <el-table-column align="center" label="已收入仓费" prop="receivedStorageFee">
                  <template slot-scope="scope">
                    <el-popover
                      placement="top"
                      popper-class="fee-edit-popover"
                      trigger="click"
                      width="200"
                    >
                      <el-input-number
                        v-model="scope.row.receivedStorageFee"
                        :controls="false"
                        :precision="2"
                        size="mini"
                        style="width: 100%"
                        @change="handleFeeChange(scope.row, 'receivedStorageFee', $event)"
                      />
                      <span slot="reference">{{ scope.row.receivedStorageFee || 0 }}</span>
                    </el-popover>
                  </template>
                </el-table-column>
                <el-table-column align="center" label="补收入仓费" prop="additionalStorageFee">
                  <template slot-scope="scope">
                    <el-popover
                      placement="top"
                      popper-class="fee-edit-popover"
                      trigger="click"
                      width="200"
                    >
                      <el-input-number
                        v-model="scope.row.additionalStorageFee"
                        :controls="false"
                        :precision="2"
                        size="mini"
                        style="width: 100%"
                        @change="handleFeeChange(scope.row, 'additionalStorageFee', $event)"
                      />
                      <span slot="reference">{{ scope.row.additionalStorageFee || 0 }}</span>
                    </el-popover>
                  </template>
                </el-table-column>
                <el-table-column align="center" label="未收卸货费" prop="unpaidUnloadingFee">
                  <template slot-scope="scope">
                    <el-popover
                      placement="top"
                      popper-class="fee-edit-popover"
                      trigger="click"
                      width="200"
                    >
                      <el-input-number
                        v-model="scope.row.unpaidUnloadingFee"
                        :controls="false"
                        :precision="2"
                        size="mini"
                        style="width: 100%"
                        @change="handleFeeChange(scope.row, 'unpaidUnloadingFee', $event)"
                      />
                      <span slot="reference">{{ scope.row.unpaidUnloadingFee || 0 }}</span>
                    </el-popover>
                  </template>
                </el-table-column>
                <el-table-column align="center" label="实付卸货费" prop="receivedUnloadingFee">
                  <template slot-scope="scope">
                    <el-popover
                      placement="top"
                      popper-class="fee-edit-popover"
                      trigger="click"
                      width="200"
                    >
                      <el-input-number
                        v-model="scope.row.receivedUnloadingFee"
                        :controls="false"
                        :precision="2"
                        size="mini"
                        style="width: 100%"
                        @change="handleFeeChange(scope.row, 'receivedUnloadingFee', $event)"
                      />
                      <span slot="reference">{{ scope.row.receivedUnloadingFee || 0 }}</span>
                    </el-popover>
                  </template>
                </el-table-column>
                  <el-table-column align="center" label="未收打包费" prop="unpaidPackingFee">
                    <template slot-scope="scope">
                      <el-popover
                        placement="top"
                        popper-class="fee-edit-popover"
                        trigger="click"
                        width="200"
                      >
                        <el-input-number
                          v-model="scope.row.unpaidPackingFee"
                          :controls="false"
                          :precision="2"
                          size="mini"
                          style="width: 100%"
                          @change="handleFeeChange(scope.row, 'unpaidPackingFee', $event)"
                        />
                        <span slot="reference">{{ scope.row.unpaidPackagingFee || 0 }}</span>
                        <span slot="reference">{{ scope.row.unpaidPackingFee || 0 }}</span>
                      </el-popover>
                    </template>
                  </el-table-column>
                  <el-table-column align="center" label="实付打包费" prop="receivedPackingFee">
                    <template slot-scope="scope">
                      <el-popover
                        placement="top"
                        popper-class="fee-edit-popover"
                        trigger="click"
                        width="200"
                      >
                        <el-input-number
                          v-model="scope.row.receivedPackingFee"
                          :controls="false"
                          :precision="2"
                          size="mini"
                          style="width: 100%"
                          @change="handleFeeChange(scope.row, 'receivedPackingFee', $event)"
                        />
                        <span slot="reference">{{ scope.row.receivedPackingFee || 0 }}</span>
                      </el-popover>
                    </template>
                  </el-table-column>
                  <el-table-column align="center" label="物流代垫费" prop="logisticsAdvanceFee">
                    <template slot-scope="scope">
                      <el-popover
                        placement="top"
                        popper-class="fee-edit-popover"
                        trigger="click"
                        width="200"
                      >
                        <el-input-number
                          v-model="scope.row.logisticsAdvanceFee"
                          :controls="false"
                          :precision="2"
                          size="mini"
                          style="width: 100%"
                          @change="handleFeeChange(scope.row, 'logisticsAdvanceFee', $event)"
                        />
                        <span slot="reference">{{ scope.row.logisticsAdvanceFee || 0 }}</span>
                      </el-popover>
                    </template>
                  </el-table-column>
                  <el-table-column align="center" label="超期租金" prop="overdueRentalFee">
                    <template slot-scope="scope">
                      <el-popover
                        placement="top"
                        popper-class="fee-edit-popover"
                        trigger="click"
                        width="200"
                      >
                        <el-input-number
                          v-model="scope.row.overdueRentalFee"
                          :controls="false"
                          :precision="2"
                          size="mini"
                          style="width: 100%"
                          @change="handleFeeChange(scope.row, 'overdueRentalFee', $event)"
                        />
                        <span slot="reference">{{ scope.row.overdueRentalFee || 0 }}</span>
                      </el-popover>
                    </template>
                  </el-table-column>
              </el-table>
            </el-col>
          </el-col>
        </el-row>

        <!--汇总费用-->
        <el-row>
          <el-col :span="4">
            <span>未收客户：{{ outboundForm.unreceivedFromCustomer }}</span>
          </el-col>
          <el-col :span="4">
            <span>已收客户：{{ outboundForm.receivedFromCustomer }}</span>
          </el-col>
          <el-col :span="4">
            <span>应收客户余额：{{ outboundForm.customerReceivableBalance }}</span>
          </el-col>
          <el-col :span="4">
            <span>应付工人：{{ outboundForm.payableToWorker }}</span>
          </el-col>
        </el-row>
        <el-row>
          <el-col :span="4">
            <span>本票销售额：{{ outboundForm.promissoryNoteSales }}</span>
          </el-col>
          <el-col :span="4">
            <span>本票成本：{{ outboundForm.promissoryNoteCost }}</span>
          </el-col>
          <el-col :span="4">
            <span>本票结余：{{ outboundForm.promissoryNoteGrossProfit }}</span>
          </el-col>
          <el-col :span="4">
            <span>已收供应商总额：{{ outboundForm.receivedFromSupplier }}</span>
          </el-col>
        </el-row>
      </el-form>
      <span slot="footer" class="dialog-footer">
    <el-button type="primary" @click="submitForm">保存</el-button>
    <el-button @click="generateOutboundBill">生成出仓单</el-button>
        <!--    <el-button v-if="outboundType===0" type="primary" @click="outboundConfirm(0)">确定预出仓</el-button>
            <el-button v-else type="primary" @click="outboundConfirm(1)">出仓</el-button>-->
  </span>
    </el-dialog>
  </div>
</template>

<script>
import {
  addOutboundrecord,
  changeStatus,
  delOutboundrecord, downloadOutboundBill,
  getOutboundrecord, getOutboundrecords,
  listOutboundrecord, listOutboundrecords,
  updateOutboundrecord
} from "@/api/system/outboundrecord"
import {parseTime} from "../../../utils/rich"
import {listInventory, outboundInventory, preOutboundInventory} from "@/api/system/inventory"
import currency from "currency.js"

export default {
  name: "Outboundrecord",
  data() {
    return {
      selectedCargoDetail:[],
      search: null,
      showLeft: 0,
      showRight: 24,
      // 遮罩层
      loading: true,
      selectOutboundList: [],
      // 选中数组
      ids: [],
      // 非单个禁用
      single: true,
      // 非多个禁用
      multiple: true,
      // 显示搜索条件
      showSearch: false,
      // 总条数
      total: 0,
      // 出仓记录表格数据
      outboundrecordList: [],
      // 弹出层标题
      title: "",
      // 是否显示弹出层
      open: false,
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 20,
        isRentSettlement: 0,
        outboundNo: null,
        clientCode: null,
        clientName: null,
        operatorId: null,
        containerType: null,
        containerNo: null,
        sealNo: null,
        outboundDate: null,
        warehouseQuote: null,
        workerLoadingFee: null,
        warehouseCollection: null,
        collectionNotes: null,
        totalBoxes: null,
        totalGrossWeight: null,
        totalVolume: null,
        totalRows: null,
        receivedStorageFee: null,
        unpaidUnloadingFee: null,
        unpaidPackingFee: null,
        logisticsAdvanceFee: null,
        rentalBalanceFee: null,
        overdueRent: null,
        freeStackDays: null,
        overdueUnitPrice: null
      },
      // 表单参数
      form: {},
      outboundType: null,
      preOutboundInventoryListLoading: false,
      // 表单校验
      rules: {},
      outboundForm: {},
      openOutbound: false,
      preOutboundInventoryList: []
    }
  },
  watch: {
    showSearch(n) {
      if (n === true) {
        this.showRight = 21
        this.showLeft = 3
      } else {
        this.showRight = 24
        this.showLeft = 0
      }
    }
  },
  created() {
    this.getList()
  },
  methods: {
    // 判断当前行是否被选中
    isRowSelected(row) {
      return this.selectedCargoDetail.includes(row)
    },
    // 添加搜索并滚动到匹配行的方法
    handleSearchEnter() {
      if (!this.search) return;

      // 查找匹配的行索引
      const index = this.preOutboundInventoryList.findIndex(
        item => {
          // 确保 inboundSerialNo 存在且为字符串
          const serialNo = String(item.inboundSerialNo || '');
          const searchValue = String(this.search);
          // 打印每次比较的值，帮助调试
          return serialNo.includes(searchValue);
        }
      );

      if (index > -1) {
        // 获取表格DOM
        const table = this.$refs.table;

        this.$nextTick(() => {
          // 获取表格的滚动容器
          const scrollWrapper = table.$el.querySelector('.el-table__body-wrapper');
          // 获取所有行
          const rows = scrollWrapper.querySelectorAll('.el-table__row');

          // 遍历所有行，找到匹配的流水号
          let targetIndex = -1;
          rows.forEach((row, idx) => {
            const rowText = row.textContent;
            if (rowText.includes(this.search)) {
              targetIndex = idx;
            }
          });


          if (targetIndex > -1) {
            const targetRow = rows[targetIndex];
            // 计算需要滚动的位置
            const rowTop = targetRow.offsetTop;

            // 使用平滑滚动
            scrollWrapper.scrollTo({
              top: rowTop - scrollWrapper.clientHeight / 2,
              behavior: 'smooth'
            });

            // 高亮显示该行
            targetRow.classList.add('highlight-row');
            // 1秒后移除高亮
            setTimeout(() => {
              targetRow.classList.remove('highlight-row');
            }, 2000);
          }
        });
      } else {
        this.$message.warning('未找到匹配的记录');
      }
    },
    warehouseConfirm() {

    },
    // 加载子节点数据
    loadChildInventory(tree, treeNode, resolve) {
      // 使用packageTo字段查询子节点
      listInventory({packageTo: tree.inventoryId}).then(response => {
        const rows = response.rows

        // 先将数据传递给表格，确保子节点渲染
        resolve(rows)
        tree.children = rows

        // 如果父项被选中，在子节点渲染完成后选中它们
        if (this.ids.includes(tree.inventoryId)) {
          setTimeout(() => {
            rows.forEach(child => {
              if (!this.ids.includes(child.inventoryId)) {
                this.ids.push(child.inventoryId)
                this.selectOutboundList.push(child)
              }
              // 在UI上选中子项
              this.$refs.table.toggleRowSelection(child, true)
            })
          }, 50) // 等待DOM更新
        }
      })
    },
    selectContainerType(type) {
      switch (type) {
        case "20GP":
          this.outboundForm.warehouseQuote = this.clientRow.rate20gp
          break
        case "40HQ":
          this.outboundForm.warehouseQuote = this.clientRow.rate40hq
          break

      }
    },
    countSummary() {
      this.outboundForm.unreceivedFromCustomer = currency(this.outboundForm.warehouseQuote).add(this.outboundForm.additionalStorageFee).add(this.outboundForm.unpaidUnloadingFee).add(this.outboundForm.unpaidPackingFee).add(this.outboundForm.logisticsAdvanceFee).add(this.outboundForm.overdueRentalFee).add(this.outboundForm.difficultyWorkFee).value
      this.outboundForm.receivedFromCustomer = currency(this.outboundForm.warehouseCollection).value
      this.outboundForm.customerReceivableBalance = currency(this.outboundForm.unreceivedFromCustomer).subtract(this.outboundForm.receivedFromCustomer).value
      this.outboundForm.payableToWorker = currency(this.outboundForm.receivedUnloadingFee).add(this.outboundForm.receivedPackingFee).add(this.outboundForm.workerLoadingFee).value
      this.outboundForm.receivedFromSupplier = currency(this.outboundForm.receivedSupplier).add(this.outboundForm.receivedStorageFee).value
      this.outboundForm.promissoryNoteSales = currency(this.outboundForm.unreceivedFromCustomer).add(this.outboundForm.receivedFromSupplier).value
      this.outboundForm.promissoryNoteCost = currency(this.outboundForm.payableToWorker).add(this.outboundForm.logisticsAdvanceFee).add(this.outboundForm.warehouseAdvanceOtherFee).value
      this.outboundForm.promissoryNoteGrossProfit = currency(this.outboundForm.promissoryNoteSales).subtract(this.outboundForm.promissoryNoteCost).value
    },
    generateOutboundBill() {
      downloadOutboundBill(this.outboundForm)
        .then(response => {
          // 获取文件的字节数组 (ArrayBuffer)
          const data = response

          // 获取文件名（如果在后端响应头中包含文件名）
          let fileName = this.outboundForm.clientCode + "-" + this.outboundForm.operator + "-" + this.outboundForm.outboundNo + ".xlsx"  // 默认文件名

          // 创建一个 Blob 对象来存储文件
          const blob = new Blob([data], {
            type: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"  // Excel 文件类型
          })

          // 创建一个临时链接，模拟点击来下载文件
          const link = document.createElement("a")
          const url = window.URL.createObjectURL(blob)  // 创建一个 URL 指向 Blob 对象
          link.href = url
          link.download = fileName  // 设置下载的文件名

          // 模拟点击链接，触发下载
          document.body.appendChild(link)
          link.click()

          // 下载完成后移除链接，并释放 URL 对象
          document.body.removeChild(link)
          window.URL.revokeObjectURL(url)
        })
        .catch(error => {
          console.error("文件下载失败:", error)
        })
    },
    // 查看出仓记录
    findOutboundRecord(row) {
      this.outboundReset()
      getOutboundrecords(row.outboundRecordId).then(response => {
        this.outboundForm = response.data
        this.outboundForm.rsInventoryList = this.outboundForm.rsInventoryList ? this.outboundForm.rsInventoryList.map(item => {
          // 计算补收入仓费
          if (item.includesInboundFee === 0) {
            const receivedFee = Number(item.receivedStorageFee || 0)
            const inboundFee = Number(item.inboundFee || 0)
            const difference = currency(inboundFee).subtract(receivedFee).value

            // 只有当差值大于0时才设置补收费用
            item.additionalStorageFee = difference > 0 ? difference : 0
          } else {
            item.additionalStorageFee = 0
          }

          return item
        }) : []
        this.openOutbound = true
      })
    },
    /**
     *
     * @param type 0:预出仓/1:出仓
     */
    outboundConfirm(type) {
      this.selectOutboundList.map(item => {
        item.partialOutboundFlag = Number(item.partialOutboundFlag)
      })

      addOutboundrecord(this.outboundForm).then(response => {
        if (response.data) {
          const outboundRecordId = response.data

          // 列表克隆一份,打上预出仓标志
          let data = this.selectOutboundList.map(item => {
            if (item.preOutboundFlag === "1") {
              this.$message.warning("勾选记录中有以预出库记录,请重新勾选")
              return
            }
            type === 0 ? item.preOutboundFlag = "1" : null
            item.outboundRecordId = outboundRecordId
            item.rsCargoDetailsList ? item.rsCargoDetailsList.map(item => {
              item.outboundRecordId = outboundRecordId
              type === 0 ? item.preOutboundFlag = "1" : null
              return item
            }) : null
            return item
          })

          if (type === 0) {
            preOutboundInventory(data).then(response => {
              this.getList()
              this.$message.success("预出仓成功")
              this.openOutbound = false
            })
          } else {
            // 出仓
            outboundInventory(data).then(response => {
              this.getList()
              this.$message.success("出仓成功")
              this.openOutbound = false
            })
          }
        }
      })
    },
    // 根据出库信息加载待出库的记录
    loadPreOutboundInventoryList() {
      this.loading = true
      let data = {}
      data.sqdPlannedOutboundDate = this.outboundForm.plannedOutboundDate
      data.clientCode = this.outboundForm.clientCode
      listInventory(data).then(response => {
        console.log(response)
        this.preOutboundInventoryList = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    /**
     *
     * @param type 0:预出仓/1:正式出仓
     */
    handleOutbound(selectedRows, type) {
      // this.outboundList = this.inventoryList.filter(item => this.ids.includes(item.inventoryId))
      if (type === 1) {
        this.outboundReset()
        this.outboundForm = selectedRows
      }
      this.outboundType = type
      this.openOutbound = true
    },
    parseTime,
    handleOutboundCargoDetailSelectionChange(selection, row) {
      row.rsCargoDetailsList = selection
      this.selectedCargoDetail = selection
    },
    getSummaries(param) {
      const {columns, data} = param
      const sums = []
      const statisticalField = [
        "receivedSupplier", "totalBoxes", "unpaidInboundFee", "totalGrossWeight",
        "totalVolume", "receivedStorageFee", "unpaidUnloadingFee", "logisticsAdvanceFee",
        "rentalBalanceFee", "overdueRentalFee", "additionalStorageFee", "unpaidUnloadingFee",
        "unpaidPackingFee", "receivedUnloadingFee", "receivedPackingFee", "inboundFee"
      ]

      // 汇总结果存储对象
      const summaryResults = {}

      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = "汇总"
          return
        }

        const values = data.map(item => Number(item[column.property]))

        if (statisticalField.includes(column.property) && !values.every(value => isNaN(value))) {
          const sumValue = values.reduce((prev, curr) => {
            const value = Number(curr)
            if (!isNaN(value)) {
              return currency(prev).add(curr).value
            } else {
              return prev
            }
          }, 0)
          sums[index] = sumValue

          // 将汇总结果存储在 summaryResults 对象中
          summaryResults[column.property] = sumValue
        } else {
          sums[index] = " "
        }
      })

      // 如果需要将汇总结果赋值到表单字段中，可以在此处操作
      // 假设表单字段的命名与统计字段一致
      Object.keys(summaryResults).forEach(field => {
        // if (this.outboundForm && this.outboundForm.hasOwnProperty(field)) {
        if (this.outboundForm) {
          this.outboundForm[field] = summaryResults[field]  // 将汇总值赋给表单字段
        }
      })

      return sums
    },

    /* getSummaries(param) {
      const {columns, data} = param
      const sums = []
      const statisticalField = ["totalBoxes", "totalGrossWeight", "totalVolume", "receivedStorageFee", "unpaidUnloadingFee", "logisticsAdvanceFee", "rentalBalanceFee", "overdueRentalFee"]
      columns.forEach((column, index) => {
        if (index === 0) {
          sums[index] = "汇总"
          return
        }
        const values = data.map(item => Number(item[column.property]))
        if (statisticalField.includes(column.property) && !values.every(value => isNaN(value))) {
          sums[index] = values.reduce((prev, curr) => {
            const value = Number(curr)
            if (!isNaN(value)) {
              return prev + curr
            } else {
              return prev
            }
          }, 0)
        } else {
          sums[index] = " "
        }
      })
      return sums
    }, */
    handleOutboundSelectionChange(selection) {
      this.selectOutboundList = selection
    },
    outboundClient(row) {
      this.outboundForm.warehouseQuote = row.rateLcl
      this.outboundForm.freeStackDays = row.freeStackPeriod
      this.outboundForm.overdueRent = row.overdueRent
      this.outboundForm.clientName = row.clientName
      // this.outboundForm.workerLoadingFee=row.workerLoadingFee
      this.$forceUpdate()
    },
    /** 查询出仓记录列表 */
    getList() {
      this.loading = true
      listOutboundrecords(this.queryParams).then(response => {
        this.outboundrecordList = response.rows
        this.total = response.total
        this.loading = false
      })
    },
    // 取消按钮
    cancel() {
      this.open = false
      this.reset()
    },
    outboundReset() {
      this.outboundForm = {
        outboundRecordId: null,
        outboundNo: null,
        clientCode: null,
        clientName: null,
        operatorId: null,
        containerType: null,
        containerNo: null,
        sealNo: null,
        outboundDate: null,
        warehouseQuote: null,
        workerLoadingFee: null,
        warehouseCollection: null,
        collectionNotes: null,
        totalBoxes: null,
        totalGrossWeight: null,
        totalVolume: null,
        totalRows: null,
        receivedStorageFee: null,
        unpaidUnloadingFee: null,
        unpaidPackingFee: null,
        logisticsAdvanceFee: null,
        rentalBalanceFee: null,
        overdueRent: null,
        freeStackDays: null,
        overdueUnitPrice: null
      }
      this.preOutboundInventoryList = []
      this.resetForm("outboundForm")
    },
    // 表单重置
    reset() {
      this.form = {
        outboundRecordId: null,
        outboundNo: null,
        clientCode: null,
        clientName: null,
        operatorId: null,
        containerType: null,
        containerNo: null,
        sealNo: null,
        outboundDate: null,
        warehouseQuote: null,
        workerLoadingFee: null,
        warehouseCollection: null,
        collectionNotes: null,
        totalBoxes: null,
        totalGrossWeight: null,
        totalVolume: null,
        totalRows: null,
        receivedStorageFee: null,
        unpaidUnloadingFee: null,
        unpaidPackingFee: null,
        logisticsAdvanceFee: null,
        rentalBalanceFee: null,
        overdueRent: null,
        freeStackDays: null,
        overdueUnitPrice: null
      }
      this.resetForm("form")
    },
    /** 搜索按钮操作 */
    handleQuery() {
      this.queryParams.pageNum = 1
      this.getList()
    },
    /** 重置按钮操作 */
    resetQuery() {
      this.resetForm("queryForm")
      this.handleQuery()
    },
    handleStatusChange(row) {
      let text = row.status === "0" ? "启用" : "停用"
      this.$modal.confirm("确认要\"" + text + "吗？").then(function () {
        return changeStatus(row.outboundRecordId, row.status)
      }).then(() => {
        this.$modal.msgSuccess(text + "成功")
      }).catch(function () {
        row.status = row.status === "0" ? "1" : "0"
      })
    },
    // 多选框选中数据
    handleSelectionChange(selection) {
      this.ids = selection.map(item => item.outboundRecordId)
      this.single = selection.length !== 1
      this.multiple = !selection.length
    },
    /** 新增按钮操作 */
    handleAdd() {
      this.outboundReset()
      this.open = true
      this.title = "添加出仓记录"
    },
    /** 修改按钮操作 */
    handleUpdate(row) {
      this.outboundReset()
      const outboundRecordId = row.outboundRecordId || this.ids
      getOutboundrecord(outboundRecordId).then(response => {
        this.form = response.data
        this.open = true
        this.title = "修改出仓记录"
      })
    },
    /** 提交按钮 */
    submitForm() {
      this.$refs["outboundForm"].validate(valid => {
        if (valid) {
          if (this.outboundForm.outboundRecordId != null) {
            updateOutboundrecord(this.outboundForm).then(response => {
              this.$modal.msgSuccess("修改成功")
              this.open = false
              this.getList()
            })
          }
        }
      })
    },
    /** 删除按钮操作 */
    handleDelete(row) {
      const outboundRecordIds = row.outboundRecordId || this.ids
      this.$modal.confirm("是否确认删除出仓记录编号为\"" + outboundRecordIds + "\"的数据项？").then(function () {
        return delOutboundrecord(outboundRecordIds)
      }).then(() => {
        this.getList()
        this.$modal.msgSuccess("删除成功")
      }).catch(() => {
      })
    },
    /** 导出按钮操作 */
    handleExport() {
      this.download("system/outboundrecord/export", {
        ...this.queryParams
      }, `outboundrecord_${new Date().getTime()}.xlsx`)
    },
    // 处理费用字段变更的通用逻辑
    handleFeeChange(row, field, value) {
      // 确保值为数字
      value = Number(value) || 0

      // 使用$set确保响应式更新
      this.$set(row, field, value)

      // 对特定字段做额外处理
      if (field === "receivedStorageFee" && row.includesInboundFee === 0) {
        const inboundFee = Number(row.inboundFee || 0)
        const difference = currency(inboundFee).subtract(value).value
        this.$set(row, "additionalStorageFee", difference > 0 ? difference : 0)
      }

      if (field === 'inboundFee') {
        const difference = currency(value).subtract(row.receivedStorageFee).value
        this.$set(row, "additionalStorageFee", difference > 0 ? difference : 0)
      }

      // 强制更新表格视图
      this.$forceUpdate()

      // 将修改后的值设置回表格的数据源，确保表格显示最新值
      const index = this.outboundForm.rsInventoryList.findIndex(item => item === row)
      if (index !== -1) {
        this.$set(this.outboundForm.rsInventoryList, index, {...row})
      }

      // 更新表格和汇总
      this.updateTableData()
    },
    // 更新表格数据并重新计算汇总
    updateTableData() {
      // 强制更新视图
      this.$forceUpdate()
      // 重新计算汇总
      this.$nextTick(() => {
        // 调用计算汇总的方法
        if (this.outboundForm.rsInventoryList && this.outboundForm.rsInventoryList.length > 0) {
          this.getSummaries({
            columns: this.$refs.outboundInventoryTable ? this.$refs.outboundInventoryTable.columns : [],
            data: this.outboundForm.rsInventoryList
          })
        }
        this.countSummary()
      })
    }
  }
}
</script>

<style scoped>
/* 数字字段显示样式 */
.el-table .cell span {
  cursor: pointer;
  color: #606266;
  padding: 2px 5px;
  border-radius: 3px;
  transition: background-color 0.3s;
}

.el-table .cell span:hover {
  background-color: #f5f7fa;
}


</style>
