(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d21dbda"],{d31f:function(e,t,r){"use strict";r.r(t);var a=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",{staticClass:"app-container"},[r("el-row",{attrs:{gutter:20}},[r("el-col",{attrs:{span:e.showLeft}},[r("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],ref:"queryForm",staticClass:"query",attrs:{inline:!0,model:e.queryParams,size:"mini"}},[r("el-form-item",{attrs:{label:"搜索",prop:"carrierQuery"}},[r("el-input",{staticStyle:{width:"158px"},attrs:{clearable:"",placeholder:"中英文/简称"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.carrierQuery,callback:function(t){e.$set(e.queryParams,"carrierQuery",t)},expression:"queryParams.carrierQuery"}})],1),r("el-form-item",{attrs:{label:"服务",prop:"serviceTypeIds"}},[r("tree-select",{staticStyle:{width:"100%"},attrs:{flat:!1,multiple:!1,pass:e.queryParams.serviceTypeIds,placeholder:"服务类型",type:"serviceType"},on:{return:e.queryServiceTypeId}})],1),r("el-form-item",[r("el-button",{attrs:{icon:"el-icon-search",size:"mini",type:"primary"},on:{click:e.handleQuery}},[e._v("搜索")]),r("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1)],1),r("el-col",{attrs:{span:e.showRight}},[r("el-row",{staticClass:"mb8",attrs:{gutter:10}},[r("el-col",{attrs:{span:1.5}},[r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:carrier:add"],expression:"['system:carrier:add']"}],attrs:{icon:"el-icon-plus",plain:"",size:"mini",type:"primary"},on:{click:e.handleAdd}},[e._v("新增 ")])],1),r("el-col",{attrs:{span:1.5}},[r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:carrier:remove"],expression:"['system:carrier:remove']"}],attrs:{disabled:e.multiple,icon:"el-icon-delete",plain:"",size:"mini",type:"danger"},on:{click:e.handleDelete}},[e._v("删除 ")])],1),r("el-col",{attrs:{span:1.5}},[r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:carrier:export"],expression:"['system:carrier:export']"}],attrs:{icon:"el-icon-download",plain:"",size:"mini",type:"warning"},on:{click:e.handleExport}},[e._v("导出 ")])],1),r("right-toolbar",{attrs:{showSearch:e.showSearch},on:{"update:showSearch":function(t){e.showSearch=t},"update:show-search":function(t){e.showSearch=t},queryTable:e.getList}})],1),r("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.carrierList},on:{"selection-change":e.handleSelectionChange}},[r("el-table-column",{attrs:{align:"left",type:"selection",width:"39px"}}),r("el-table-column",{attrs:{align:"center",label:"通用编码",prop:"carrierIntlCode",width:"68"}}),r("el-table-column",{attrs:{align:"left",label:"类型名称"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(t.row.carrierShortName)+" "),r("a",{staticStyle:{margin:"0","font-weight":"bold","font-size":"small"}},[e._v(e._s(t.row.carrierLocalName))]),e._v(" "+e._s(t.row.carrierEnName)+" ")]}}])}),r("el-table-column",{key:"serviceType",attrs:{align:"left",label:"服务类型",width:"170"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("el-tooltip",{attrs:{placement:"top"}},[r("div",{attrs:{slot:"content"},slot:"content"},[r("h6",{staticStyle:{margin:"0"}},[e._v(" "+e._s(null!=t.row.serviceType?t.row.serviceType:"")+" ")])]),r("div",[r("h6",{staticStyle:{margin:"0",overflow:"hidden","text-overflow":"ellipsis"}},[e._v(" "+e._s((null!=t.row.serviceType?t.row.serviceType:"").substring(0,33)+((null!=t.row.serviceType?t.row.serviceType:"").length>33?"...":""))+" ")])])])]}}])}),r("el-table-column",{attrs:{align:"center",label:"货物查询追踪网址",prop:"trackingWebsite"}}),r("el-table-column",{attrs:{align:"center",label:"备注",prop:"remark"}}),r("el-table-column",{attrs:{align:"center",label:"区域",prop:"location"}}),r("el-table-column",{attrs:{align:"center",label:"排序",prop:"orderNum"}}),r("el-table-column",{attrs:{align:"center",label:"状态",prop:"status",width:"68"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("el-switch",{attrs:{"active-value":"0","inactive-value":"1"},on:{change:function(r){return e.handleStatusChange(t.row)}},model:{value:t.row.status,callback:function(r){e.$set(t.row,"status",r)},expression:"scope.row.status"}})]}}])}),r("el-table-column",{attrs:{align:"center","class-name":"small-padding fixed-width",label:"操作",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:carrier:edit"],expression:"['system:carrier:edit']"}],staticStyle:{"margin-right":"-8px"},attrs:{icon:"el-icon-edit",size:"mini",type:"success"},on:{click:function(r){return e.handleUpdate(t.row)}}},[e._v("修改 ")]),r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:carrier:remove"],expression:"['system:carrier:remove']"}],staticStyle:{"margin-right":"-8px"},attrs:{icon:"el-icon-delete",size:"mini",type:"danger"},on:{click:function(r){return e.handleDelete(t.row)}}},[e._v("删除 ")])]}}])})],1),r("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total>0"}],attrs:{limit:e.queryParams.pageSize,page:e.queryParams.pageNum,total:e.total},on:{"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},pagination:e.getList}})],1)],1),r("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"},{name:"dialogDragWidth",rawName:"v-dialogDragWidth"}],attrs:{"close-on-click-modal":!1,"modal-append-to-body":!1,title:e.title,visible:e.open,"append-to-body":"",width:"500px"},on:{"update:visible":function(t){e.open=t}}},[r("el-form",{ref:"form",staticClass:"edit",attrs:{model:e.form,rules:e.rules,"label-width":"100px"}},[r("el-form-item",{attrs:{label:"国际通用编码",prop:"carrierIntlCode"}},[r("el-input",{attrs:{placeholder:"主要承运人国际通用编码"},model:{value:e.form.carrierIntlCode,callback:function(t){e.$set(e.form,"carrierIntlCode",t)},expression:"form.carrierIntlCode"}})],1),r("el-form-item",{attrs:{label:"简称",prop:"carrierShortName"}},[r("el-input",{attrs:{placeholder:"承运人简称"},model:{value:e.form.carrierShortName,callback:function(t){e.$set(e.form,"carrierShortName",t)},expression:"form.carrierShortName"}})],1),r("el-form-item",{attrs:{label:"中文名",prop:"carrierLocalName"}},[r("el-input",{attrs:{placeholder:"中文名"},model:{value:e.form.carrierLocalName,callback:function(t){e.$set(e.form,"carrierLocalName",t)},expression:"form.carrierLocalName"}})],1),r("el-form-item",{attrs:{label:"英文名",prop:"carrierEnName"}},[r("el-input",{attrs:{placeholder:"英文名"},model:{value:e.form.carrierEnName,callback:function(t){e.$set(e.form,"carrierEnName",t)},expression:"form.carrierEnName"}})],1),r("el-form-item",{attrs:{label:"支持服务类型","label-width":"100px"}},[r("tree-select",{attrs:{flat:!1,multiple:!0,pass:e.form.serviceTypeIds,type:"serviceType"},on:{return:e.getServiceTypeIds}})],1),r("el-form-item",{attrs:{label:"货物追踪网址",prop:"trackingWebsite"}},[r("el-input",{attrs:{placeholder:"货物查询追踪网址"},model:{value:e.form.trackingWebsite,callback:function(t){e.$set(e.form,"trackingWebsite",t)},expression:"form.trackingWebsite"}})],1),r("el-form-item",{attrs:{label:"区域",prop:"locationId"}},[r("location-select",{ref:"location",attrs:{pass:e.form.locationId,type:"location"},on:{return:e.getLocationId}})],1),r("el-form-item",{attrs:{label:"排序",prop:"orderNum"}},[r("el-input-number",{staticStyle:{width:"100%"},attrs:{controls:!1,placeholder:"排序"},model:{value:e.form.orderNum,callback:function(t){e.$set(e.form,"orderNum",t)},expression:"form.orderNum"}})],1),r("el-form-item",{attrs:{label:"备注",prop:"remark"}},[r("el-input",{attrs:{placeholder:"备注"},model:{value:e.form.remark,callback:function(t){e.$set(e.form,"remark",t)},expression:"form.remark"}})],1)],1),r("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[r("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("确 定")]),r("el-button",{on:{click:e.cancel}},[e._v("取 消")])],1)],1)],1)},i=[],l=r("5530"),o=(r("d81d"),r("daab")),s={name:"Carrier",data:function(){return{showLeft:3,showRight:21,loading:!0,ids:[],single:!0,multiple:!0,showSearch:!0,total:0,carrierList:[],title:"",open:!1,queryParams:{pageNum:1,pageSize:20,carrierQuery:null},form:{},rules:{carrierShortName:[{required:!0,trigger:"blur"}]}}},watch:{showSearch:function(e){1==e?(this.showRight=21,this.showLeft=3):(this.showRight=24,this.showLeft=0)}},created:function(){this.getList()},methods:{getList:function(){var e=this;this.loading=!0,Object(o["f"])(this.queryParams).then((function(t){e.carrierList=t.rows,e.total=t.total,e.loading=!1}))},cancel:function(){this.open=!1,this.reset()},reset:function(){this.form={carrierId:null,carrierIntlCode:null,carrierShortName:null,carrierEnName:null,carrierLocalName:null,trackingWebsite:null,locationId:null,orderNum:null,status:"0",remark:null,createBy:null,createTime:null,updateBy:null,updateTime:null,deleteBy:null,deleteTime:null,deleteStatus:0},this.resetForm("form")},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.resetForm("queryForm"),this.handleQuery()},handleSelectionChange:function(e){this.ids=e.map((function(e){return e.carrierId})),this.single=1!=e.length,this.multiple=!e.length},handleAdd:function(){this.reset(),this.open=!0,this.title="添加船公司"},handleUpdate:function(e){var t=this;this.reset();var r=e.carrierId||this.ids;Object(o["d"])(r).then((function(r){t.form=r.data,t.form.serviceTypeIds=r.serviceTypeIds,t.open=!0,t.title="修改船公司",t.$refs.location.remoteMethod(e.locationLocalName)}))},submitForm:function(){var e=this;this.$refs["form"].validate((function(t){t&&(null!=e.form.carrierId?Object(o["h"])(e.form).then((function(t){e.$modal.msgSuccess("修改成功"),e.open=!1,e.getList()})):Object(o["a"])(e.form).then((function(t){e.$modal.msgSuccess("新增成功"),e.open=!1,e.getList()})))}))},handleDelete:function(e){var t=this,r=e.carrierId||this.ids;this.$confirm('是否确认删除船公司编号为"'+r+'"的数据项？',"提示",{customClass:"modal-confirm"}).then((function(){return Object(o["c"])(r)})).then((function(){t.getList(),t.$modal.msgSuccess("删除成功")})).catch((function(){}))},handleExport:function(){this.download("system/carrier/export",Object(l["a"])({},this.queryParams),"carrier_".concat((new Date).getTime(),".xlsx"))},handleStatusChange:function(e){var t=this,r="0"==e.status?"启用":"停用";this.$confirm("确认要修改状态吗？","提示",{customClass:"modal-confirm"}).then((function(){return Object(o["b"])(e.cargoTypeId,e.status)})).then((function(){t.$modal.msgSuccess(r+"成功")})).catch((function(){e.status="0"==e.status?"1":"0"}))},getServiceTypeIds:function(e){this.form.serviceTypeIds=e},queryServiceTypeId:function(e){this.queryParams.serviceTypeId=e},getLocationId:function(e){this.form.locationId=e}}},n=s,c=r("2877"),m=Object(c["a"])(n,a,i,!1,null,null,null);t["default"]=m.exports}}]);