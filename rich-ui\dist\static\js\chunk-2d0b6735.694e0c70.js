(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d0b6735"],{"1ce2":function(e,t,o){"use strict";o.r(t);var c=function(){var e=this,t=e.$createElement,o=e._self._c||t;return o("div",[o("el-tooltip",{attrs:{disabled:null==e.scope.row.cargoType||e.scope.row.cargoType.length<12,placement:"top"}},[o("div",{attrs:{slot:"content"},slot:"content"},[o("h6",{staticStyle:{margin:"0"}},[e._v(" "+e._s(e.scope.row.cargoType)+" ")])]),o("div",[o("h6",{staticStyle:{margin:"0",overflow:"hidden","text-overflow":"ellipsis"}},[e._v(" "+e._s(e.scope.row.cargoType)+" ")])])])],1)},s=[],n={name:"cargoType",props:["scope"]},r=n,a=o("2877"),l=Object(a["a"])(r,c,s,!1,null,"97e601a4",null);t["default"]=l.exports}}]);