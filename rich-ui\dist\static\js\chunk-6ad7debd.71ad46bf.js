(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-6ad7debd"],{"6ab7":function(t,e,n){},a603:function(t,e,n){"use strict";n.r(e);var s=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("div",[n("el-tooltip",{attrs:{"open-delay":500,disabled:null==t.scope.row.staff,placement:"top"}},[n("div",{attrs:{slot:"content"},slot:"content"}),n("div",{staticClass:"content"},[n("span"),n("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:company:list"],expression:"['system:company:list']"}],staticStyle:{padding:"0",display:"flex"},attrs:{size:t.size,type:"text"},on:{click:function(e){return t.checkConnect(t.scope.row)}}},[n("div",{staticStyle:{width:"50%",height:"50%"}},[t._v(" "+t._s(null!==t.scope.row.mainStaffOfficialName?"["+t.scope.row.mainStaffOfficialName+"]":"[···]")+" ")])])],1)])],1)},a=[],i={name:"contactor",props:["scope"],data:function(){return{size:this.$store.state.app.size||"mini"}},methods:{checkConnect:function(t){this.$emit("return",{key:"contactor",value:t})}}},c=i,o=(n("e757"),n("2877")),l=Object(o["a"])(c,s,a,!1,null,"18826024",null);e["default"]=l.exports},e757:function(t,e,n){"use strict";n("6ab7")}}]);