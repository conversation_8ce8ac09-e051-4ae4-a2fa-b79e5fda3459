(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-commons"],{"061b":function(e,t,a){"use strict";var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],style:"height:"+e.height},[a("iframe",{staticStyle:{width:"100%",height:"100%"},attrs:{src:e.src,frameborder:"no",scrolling:"auto"}})])},l=[],n={props:{src:{type:String,required:!0}},data:function(){return{height:document.documentElement.clientHeight-94.5+"px;",loading:!0,url:this.src}},mounted:function(){var e=this;setTimeout((function(){e.loading=!1}),300);var t=this;window.onresize=function(){t.height=document.documentElement.clientHeight-94.5+"px;"}}},s=n,o=a("2877"),r=Object(o["a"])(s,i,l,!1,null,null,null);t["a"]=r.exports},"1a12":function(e,t,a){},"1dec":function(e,t,a){"use strict";var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("el-select",{attrs:{filterable:"",placeholder:e.placeholder,disabled:e.disable},on:{change:e.handleSelect},model:{value:e.value,callback:function(t){e.value=t},expression:"value"}},e._l(e.options,(function(e){return a("el-option",{key:e.processId,attrs:{label:e.processLocalName,value:e.processId}})})),1)],1)},l=[],n=(a("4de4"),a("d3b7"),a("23c0"),a("4360")),s={name:"ProgressName",props:["pass","placeholder","disable","serviceType","processType"],watch:{pass:function(e){this.value=e}},beforeMount:function(){this.loadProcessList()},data:function(){return{options:[{value:"0",label:"订仓"},{value:"1",label:"入仓"},{value:"2",label:"出仓"},{value:"3",label:"装柜"},{value:"4",label:"订舱"},{value:"5",label:"放舱"},{value:"6",label:"还重"},{value:"7",label:"装船"},{value:"8",label:"运输"},{value:"9",label:"中转"},{value:"10",label:"到港"},{value:"11",label:"入舱"},{value:"12",label:"托书"},{value:"13",label:"下单"},{value:"14",label:"查验"},{value:"15",label:"课税"},{value:"16",label:"放行"},{value:"17",label:"投保"},{value:"18",label:"金额"},{value:"19",label:"保单"},{value:"20",label:"默认"},{value:"21",label:"形式"},{value:"22",label:"补料"},{value:"23",label:"改单"},{value:"24",label:"赎单"},{value:"25",label:"交付"},{value:"26",label:"报价"},{value:"27",label:"账单"},{value:"28",label:"发票"},{value:"29",label:"水单"},{value:"30",label:"付款"},{value:"31",label:"收款"},{value:"32",label:"起飞"},{value:"33",label:"接单"},{value:"34",label:"派车"},{value:"35",label:"提柜"},{value:"36",label:"过磅"},{value:"37",label:"还柜"},{value:"38",label:"申报"},{value:"39",label:"出单"},{value:"40",label:"放货"},{value:"41",label:"放仓"},{value:"42",label:"厂检"},{value:"43",label:"下药"},{value:"44",label:"盖章"}],value:this.pass}},methods:{handleSelect:function(){var e=this;this.$emit("progressName",this.value),this.$emit("returnData",this.options.filter((function(t){return t.processId===e.value}))[0])},loadProcessList:function(){var e=this;0===this.$store.state.data.processList.length||this.$store.state.data.redisList.process?n["a"].dispatch("getProcess").then((function(){e.options=e.serviceType?e.$store.state.data.processList.filter((function(t){return t.serviceTypeId===e.serviceType&&t.processTypeId===e.processType})):e.$store.state.data.processList})):this.options=this.serviceType?this.$store.state.data.processList.filter((function(t){return t.serviceTypeId===e.serviceType&&t.processTypeId===e.processType})):this.$store.state.data.processList}}},o=s,r=(a("f1c6"),a("2877")),c=Object(r["a"])(o,i,l,!1,null,"19359f64",null);t["a"]=c.exports},"37ff":function(e,t,a){"use strict";var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"box-card"},[a("el-tooltip",{staticClass:"item",attrs:{content:e.getConfirmedDate+" 由"+e.getConfirmedName+"审核",disabled:e.confirmed,effect:"dark",placement:"top"}},[e.confirmed?a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:agreementrecord:deptlock"],expression:"['system:agreementrecord:deptlock']"}],attrs:{icon:"el-icon-minus",size:"mini"},on:{click:e.Lock}},[e._v(e._s(e.confirmedName)+"未审 ")]):e._e(),e.confirmed?e._e():a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:agreementrecord:deptunlock"],expression:"['system:agreementrecord:deptunlock']"}],attrs:{icon:"el-icon-check",size:"mini",type:"success"},on:{click:e.Lock}},[e._v(e._s(e.confirmedName)+"已审 ")])],1)],1)},l=[],n=(a("4de4"),a("d3b7"),a("4360")),s=a("c1df"),o=a.n(s),r={name:"Confirmed",dicts:["sys_account_type","sys_is_confirm","sys_is_lock"],props:["type","row","id","confirmed"],data:function(){return{confirm:"sales"===this.type?"salesConfirmed":"acc"===this.type?"accConfirmed":"op"===this.type?"opConfirmed":"psa"===this.type?"psaConfirmed":"",confirmId:"sales"===this.type?"salesConfirmedId":"acc"===this.type?"accConfirmedId":"op"===this.type?"opConfirmedId":"psa"===this.type?"psaConfirmedId":"",confirmDate:"sales"===this.type?"salesConfirmedDate":"acc"===this.type?"accConfirmedDate":"op"===this.type?"opConfirmedDate":"psa"===this.type?"psaConfirmedDate":"",staffList:[],form:this.row,confirmedName:"sales"===this.type?"业务":"acc"===this.type?"财务":"op"===this.type?"操作":"psa"===this.type?"商务":""}},created:function(){this.loadStaffList()},computed:{getConfirmedName:function(){var e=this;if(this.form[this.confirmId])return this.staffList.filter((function(t){return t.staffId===e.form[e.confirmId]}))[0]?this.staffList.filter((function(t){return t.staffId===e.form[e.confirmId]}))[0].staffGivingEnName:""},getConfirmedDate:function(){if(this.form[this.confirmId])return this.form["sales"===this.type?"salesConfirmedDate":"acc"===this.type?"accConfirmedDate":"op"===this.type?"opConfirmedDate":"psa"===this.type?"psaConfirmedDate":""]}},methods:{Lock:function(){if(null!=this.form[this.id]){new Object;0==this.form[this.confirm]?(this.form[this.confirm]=1,this.form[this.confirmId]=this.$store.state.user.sid,this.form[this.confirmDate]=o()().format("yyyy-MM-DD HH:mm:ss")):(this.form[this.confirm]=0,this.form[this.confirmId]=null,this.form[this.confirmDate]=null),this.$emit("lockMethod",this.form)}else this.$modal.msgError("错误操作")},loadStaffList:function(){var e=this;0==this.$store.state.data.allRsStaffList.length||this.$store.state.data.redisList.allRsStaffList?n["a"].dispatch("getAllRsStaffList").then((function(){e.staffList=e.$store.state.data.allRsStaffList})):this.staffList=this.$store.state.data.allRsStaffList}}},c=r,u=a("2877"),d=Object(u["a"])(c,i,l,!1,null,"2f5e6d5c",null);t["a"]=d.exports},"388a":function(e,t,a){"use strict";var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"},{name:"dialogDragWidth",rawName:"v-dialogDragWidth"}],staticClass:"blackList",attrs:{"close-on-click-modal":!1,"modal-append-to-body":!1,visible:e.openContent,title:"加入黑名单",width:"1000px"},on:{"update:visible":function(t){e.openContent=t}}},[a("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-position":"right","label-width":"75px"}},[a("el-form-item",{attrs:{label:"拉黑者"}},[e._v(" "+e._s(this.$store.state.user.name)+" ")]),a("el-form-item",{attrs:{label:"被拉黑者"}},[e._v(" "+e._s(this.company.companyLocalName+" ("+this.company.companyShortName+") "+this.company.companyEnName)+" ")]),a("el-form-item",{attrs:{label:"拉黑原因",prop:"content"}},[a("el-input",{attrs:{maxlength:"200",placeholder:"拉黑原因","show-word-limit":"",type:"textarea"},model:{value:e.form.blacklistContent,callback:function(t){e.$set(e.form,"blacklistContent",t)},expression:"form.blacklistContent"}})],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("确 定")]),a("el-button",{on:{click:e.cancel}},[e._v("取 消")])],1)],1)},l=[],n=a("aff7"),s={name:"BlackList",props:["open","company"],data:function(){return{openContent:!1,form:{},rules:{blacklistContent:[{required:!0,trigger:"blur"}]}}},watch:{open:function(e){this.openContent=e},openContent:function(e){0==e&&this.$emit("openBlackList")}},methods:{submitForm:function(){var e=this;this.$refs["form"].validate((function(t){if(t){var a=e;e.form.companyId=e.company.companyId,e.$confirm('是否确定把公司"'+e.company.companyLocalName+" ("+e.company.companyShortName+") "+e.company.companyEnName+'"拉入黑名单？',"提示",{customClass:"modal-confirm"}).then((function(){return Object(n["e"])(a.form)})).then((function(){e.openContent=!1,e.getList(),e.$modal.msgSuccess("拉黑成功")})).catch((function(){}))}}))},cancel:function(){this.openContent=!1}}},o=s,r=a("2877"),c=Object(r["a"])(o,i,l,!1,null,"2c6d408d",null);t["a"]=c.exports},"4f70":function(e,t,a){"use strict";var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("el-button",{attrs:{icon:"el-icon-search",type:"primary"},on:{click:e.handleButtonClick}},[e._v(e._s(e.configName?e.configName:"筛选")+" ")]),a("el-button",{attrs:{icon:"el-icon-edit",type:"primary"},on:{click:function(t){e.searchDialogVisible=!0}}},[e._v(e._s("修改"))]),a("el-dialog",{attrs:{visible:e.searchDialogVisible,"append-to-body":"",title:"高级搜索",width:"400px"},on:{"update:visible":function(t){e.searchDialogVisible=t}}},[a("div",{staticClass:"dynamic-search"},[a("el-form",[a("el-row",[a("el-col",[a("el-form-item",[a("el-row",{attrs:{gutter:10,align:"middle",type:"flex"}},[a("el-col",{attrs:{span:18}},[a("el-input",{attrs:{placeholder:"请输入速查名称"},model:{value:e.configName,callback:function(t){e.configName=t},expression:"configName"}})],1),a("el-col",{attrs:{span:3}},[a("el-button",{attrs:{size:"small",type:"text"},on:{click:e.saveConfig}},[e._v("[↗]")])],1),a("el-col",{attrs:{span:3}},[a("el-button",{attrs:{size:"small",type:"text"},on:{click:e.loadConfigs}},[e._v("[...]")])],1)],1)],1)],1),a("el-col",[e._l(e.searchConditions,(function(t,i){return a("div",{key:i},[a("el-row",[t.field?[a("el-form-item",{attrs:{label:e.getFieldConfig(t.field).label}},[a("el-row",[a("el-col",{attrs:{span:16}},["input"===e.getFieldConfig(t.field).type?a("el-input",{staticClass:"value-input",attrs:{placeholder:e.getFieldConfig(t.field).placeholder},model:{value:t.value,callback:function(a){e.$set(t,"value",a)},expression:"condition.value"}}):"date"===e.getFieldConfig(t.field).type?a("el-date-picker",{staticClass:"value-input",staticStyle:{width:"100%"},attrs:{"end-placeholder":"结束","range-separator":"至","start-placeholder":"开始",type:"daterange","value-format":"yyyy-MM-dd HH:mm:ss","default-time":["00:00:00","23:59:59"]},model:{value:t.value,callback:function(a){e.$set(t,"value",a)},expression:"condition.value"}}):"select"===e.getFieldConfig(t.field).type?a("el-select",{staticClass:"value-input",attrs:{loading:t.loading,placeholder:e.getFieldConfig(t.field).placeholder,remote:e.getFieldConfig(t.field).remote,"remote-method":function(a){return e.handleRemoteSearch(a,t.field)},clearable:"",filterable:""},model:{value:t.value,callback:function(a){e.$set(t,"value",a)},expression:"condition.value"}},e._l(t.options,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1):"treeselect"===e.getFieldConfig(t.field).type?a("treeselect",{staticClass:"value-input",attrs:{"disable-branch-nodes":e.getFieldConfig(t.field).disableBranchNodes||!0,"disabled-fuzzy-matching":e.getFieldConfig(t.field).disabledFuzzyMatching||!0,"flatten-search-results":e.getFieldConfig(t.field).flattenSearchResults||!0,normalizer:e.getNormalizer(t.field),options:t.treeOptions||[],placeholder:e.getFieldConfig(t.field).placeholder,"show-count":e.getFieldConfig(t.field).showCount||!0},on:{input:function(t){return e.handleTreeselectInput(i,t)},open:function(a){return e.handleTreeselectOpen(i,t.field)},select:function(t){return e.handleTreeselectSelect(i,t)}},scopedSlots:e._u([e.getFieldConfig(t.field).valueSlot?{key:"value-label",fn:function(a){var i=a.node;return[e._v(" "+e._s(e.renderValueSlot(t.field,i))+" ")]}}:null,e.getFieldConfig(t.field).optionSlot?{key:"option-label",fn:function(i){var l=i.node,n=i.shouldShowCount,s=i.count,o=i.labelClassName,r=i.countClassName;return[a("span",{class:o},[e._v(" "+e._s(e.renderOptionSlot(t.field,l))+" "),n?a("span",{class:r},[e._v("("+e._s(s)+")")]):e._e()])]}}:null],null,!0),model:{value:t.treeValue,callback:function(a){e.$set(t,"treeValue",a)},expression:"condition.treeValue"}}):"location"===e.getFieldConfig(t.field).type?a("location-select",{attrs:{en:e.getFieldConfig(t.field).en,multiple:e.getFieldConfig(t.field).multiple,pass:t.value,placeholder:e.getFieldConfig(t.field).placeholder},on:{return:function(t){return e.handleLocationSelect(t,i)}},model:{value:t.value,callback:function(a){e.$set(t,"value",a)},expression:"condition.value"}}):"company"===e.getFieldConfig(t.field).type?a("company-select",{attrs:{multiple:e.getFieldConfig(t.field).multiple,"no-parent":e.getFieldConfig(t.field).noParent,pass:t.value,placeholder:e.getFieldConfig(t.field).placeholder,"role-client":e.getFieldConfig(t.field).roleClient,roleTypeId:e.getFieldConfig(t.field).roleTypeId},on:{return:function(t){return e.handleCompanySelect(t,i)}}}):"tree-select"===e.getFieldConfig(t.field).type?a("tree-select",{attrs:{"d-load":e.getFieldConfig(t.field).dLoad,flat:e.getFieldConfig(t.field).flat||!1,multiple:e.getFieldConfig(t.field).multiple||!1,pass:t.value,placeholder:e.getFieldConfig(t.field).placeholder,type:e.getFieldConfig(t.field).treeType,"type-id":e.getFieldConfig(t.field).typeId},on:{close:e.handleTreeSelectClose,return:function(t){return e.handleTreeSelect(t,i)}},model:{value:t.value,callback:function(a){e.$set(t,"value",a)},expression:"condition.value"}}):e._e()],1),a("el-col",{attrs:{span:1}},[a("el-button",{staticClass:"remove-btn",staticStyle:{color:"red"},attrs:{icon:"el-icon-delete",type:"text"},on:{click:function(t){return e.removeCondition(i)}}})],1)],1)],1)]:[a("el-select",{staticClass:"field-select",attrs:{filterable:"",placeholder:"请选择搜索字段"},on:{change:function(t){return e.handleFieldChange(i)}},model:{value:t.field,callback:function(a){e.$set(t,"field",a)},expression:"condition.field"}},e._l(e.availableFields,(function(e){return a("el-option",{key:e.key,attrs:{label:e.label,value:e.key}})})),1)]],2)],1)})),a("el-button",{attrs:{type:"text"},on:{click:e.addCondition}},[e._v("[+]")]),a("el-row",[a("el-button",{attrs:{type:"primary"},on:{click:e.handleSearch}},[e._v("搜索")]),a("el-button",{on:{click:e.handleReset}},[e._v("重置")])],1)],2)],1)],1)],1)]),a("el-dialog",{attrs:{visible:e.configDialogVisible,"append-to-body":"",title:"加载配置",width:"500px"},on:{"update:visible":function(t){e.configDialogVisible=t}}},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.configLoading,expression:"configLoading"}],staticStyle:{width:"100%"},attrs:{data:e.savedConfigs},on:{"row-click":e.handleConfigSelect}},[a("el-table-column",{attrs:{label:"配置名称",prop:"name"}}),a("el-table-column",{attrs:{label:"创建时间",prop:"createTime"}}),a("el-table-column",{attrs:{width:"120"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{attrs:{type:"text"},on:{click:function(a){return a.stopPropagation(),e.deleteConfig(t.row)}}},[e._v("删除")])]}}])})],1)],1)],1)},l=[],n=a("b85c"),s=a("c7eb"),o=a("1da1"),r=a("3835"),c=(a("d3b7"),a("6062"),a("3ca3"),a("ddb0"),a("d81d"),a("4de4"),a("4fadc"),a("14d9"),a("a434"),a("7db0"),a("d9e2"),a("b64b"),a("b0c0"),a("b775")),u=a("ca17"),d=a.n(u),f=(a("6f8d"),a("4360")),p=a("b0b8"),h=a.n(p),m=a("addf"),v=a("c211"),b={name:"DynamicSearch",components:{Treeselect:d.a,LocationSelect:m["a"]},props:{searchFields:{type:Object,required:!0},configType:{type:String,required:!1}},data:function(){return{searchConditions:[],remoteData:{},treeData:{},belongList:[],businessList:[],opList:[],configName:"",configDialogVisible:!1,configLoading:!1,savedConfigs:[],searchDialogVisible:!1,clickTimer:null}},computed:{availableFields:function(){var e=new Set(this.searchConditions.map((function(e){return e.field})));return Object.entries(this.searchFields).map((function(t){var a=Object(r["a"])(t,2),i=a[0],l=a[1];return{key:i,label:l.label,disabled:e.has(i)}})).filter((function(e){return!e.disabled}))}},methods:{handleTreeSelect:function(e,t){this.searchConditions[t].value=e},handleTreeSelectClose:function(){},handleCompanySelect:function(e,t){this.searchConditions[t].value=e},handleLocationSelect:function(e,t){this.searchConditions[t].value=e},getFieldConfig:function(e){return this.searchFields[e]||{}},addCondition:function(){this.searchConditions.push({field:"",value:"",options:[],loading:!1,treeValue:null,treeOptions:[]})},removeCondition:function(e){this.searchConditions.splice(e,1)},handleFieldChange:function(e){var t=this;return Object(o["a"])(Object(s["a"])().mark((function a(){var i,l;return Object(s["a"])().wrap((function(a){while(1)switch(a.prev=a.next){case 0:if(i=t.searchConditions[e],l=t.getFieldConfig(i.field),"date"===l.type?i.value=[]:"treeselect"===l.type||"treeselect"===l.type||"tree-select"===l.type?(i.treeValue=null,i.treeOptions=[],i.value=null):i.value="",i.options=[],"select"!==l.type){a.next=13;break}if(!l.remote){a.next=12;break}if(t.remoteData[i.field]){a.next=9;break}return a.next=9,t.fetchRemoteOptions(i.field);case 9:i.options=t.remoteData[i.field]||[],a.next=13;break;case 12:i.options=l.options||[];case 13:case"end":return a.stop()}}),a)})))()},handleTreeselectOpen:function(e,t){var a=this,i=this.getFieldConfig(t),l=this.searchConditions[e];if(i.loadMethod&&"string"===typeof i.loadMethod&&"function"===typeof this[i.loadMethod]){var n=this[i.loadMethod]();n&&"function"===typeof n.then?n.then((function(e){e?l.treeOptions=e:i.dataSource&&a.$store.state.data[i.dataSource]&&(l.treeOptions=a.$store.state.data[i.dataSource])})):(l.treeOptions=n,n&&0!==n.length||!i.dataSource||!this.$store.state.data[i.dataSource]||(l.treeOptions=this.$store.state.data[i.dataSource]))}},handleTreeselectSelect:function(e,t){var a=this.searchConditions[e],i=this.getFieldConfig(a.field);i.valueField&&void 0!==t[i.valueField]?a.value=t[i.valueField]:void 0!==t.staffId?a.value=t.staffId:a.value=t.id},handleTreeselectInput:function(e,t){var a=this.searchConditions[e];void 0!==t&&null!==t||(a.value=null)},renderValueSlot:function(e,t){var a=this.getFieldConfig(e);return a.valueRenderer&&"function"===typeof a.valueRenderer?a.valueRenderer(t):t.raw&&t.raw.staff?t.raw.staff.staffFamilyLocalName+t.raw.staff.staffGivingLocalName+" "+t.raw.staff.staffGivingEnName:t.label},renderOptionSlot:function(e,t){var a=this.getFieldConfig(e);return a.optionRenderer&&"function"===typeof a.optionRenderer?a.optionRenderer(t):t.label&&-1!==t.label.indexOf(",")?t.label.substring(0,t.label.indexOf(",")):t.label},handleRemoteSearch:function(e,t){var a=this;return Object(o["a"])(Object(s["a"])().mark((function i(){var l;return Object(s["a"])().wrap((function(i){while(1)switch(i.prev=i.next){case 0:if(l=a.searchConditions.find((function(e){return e.field===t})),l){i.next=3;break}return i.abrupt("return");case 3:return l.loading=!0,i.prev=4,i.next=7,a.fetchRemoteOptions(t,e);case 7:l.options=a.remoteData[t]||[];case 8:return i.prev=8,l.loading=!1,i.finish(8);case 11:case"end":return i.stop()}}),i,null,[[4,,8,11]])})))()},fetchRemoteOptions:function(e){var t=arguments,a=this;return Object(o["a"])(Object(s["a"])().mark((function i(){var l,n,o,r;return Object(s["a"])().wrap((function(i){while(1)switch(i.prev=i.next){case 0:if(l=t.length>1&&void 0!==t[1]?t[1]:"",n=a.getFieldConfig(e),n.remote&&n.options){i.next=4;break}return i.abrupt("return");case 4:if(o=remoteMethodsMap[n.options],o){i.next=7;break}return i.abrupt("return");case 7:return i.prev=7,i.next=10,Object(c["a"])({url:o,method:"get",params:{query:l}});case 10:r=i.sent,200===r.code&&(a.remoteData[e]=r.data),i.next=18;break;case 14:i.prev=14,i.t0=i["catch"](7),console.error("获取远程选项失败:",i.t0),a.$message.error("获取选项数据失败");case 18:case"end":return i.stop()}}),i,null,[[7,14]])})))()},handleSearch:function(){var e,t={},a=Object(n["a"])(this.searchConditions);try{for(a.s();!(e=a.n()).done;){var i=e.value;if(i.field&&""!==i.value&&null!==i.value){this.getFieldConfig(i.field);t[i.field]=i.value}}}catch(l){a.e(l)}finally{a.f()}this.$emit("search",t)},handleReset:function(){this.searchConditions=[],this.configName="",this.$emit("reset")},staffNormalizer:function(e){var t;return e.children&&!e.children.length&&delete e.children,e.staff&&(t=null==e.staff.staffFamilyLocalName&&null==e.staff.staffGivingLocalName?null!=e.role.roleLocalName?e.role.roleLocalName+","+h.a.getFullChars(e.role.roleLocalName):e.dept.deptLocalName+","+h.a.getFullChars(e.dept.deptLocalName):e.staff.staffCode+" "+e.staff.staffFamilyLocalName+e.staff.staffGivingLocalName+" "+e.staff.staffGivingEnName+","+h.a.getFullChars(e.staff.staffFamilyLocalName+e.staff.staffGivingLocalName)),e.roleId?{id:e.roleId,label:t,children:e.children,isDisabled:null==e.staffId&&void 0==e.children}:{id:e.deptId,label:t,children:e.children,isDisabled:null==e.staffId&&void 0==e.children}},loadSales:function(){var e=this;return 0==this.$store.state.data.salesList.length||this.$store.state.data.redisList.salesList?f["a"].dispatch("getSalesList").then((function(){return e.belongList=e.$store.state.data.salesList,e.$store.state.data.salesList})):(this.belongList=this.$store.state.data.salesList,this.$store.state.data.salesList)},loadBusinesses:function(){var e=this;return 0==this.$store.state.data.businessesList.length||this.$store.state.data.redisList.businessesList?f["a"].dispatch("getBusinessesList").then((function(){return e.businessList=e.$store.state.data.businessesList,e.$store.state.data.businessesList})):(this.businessList=this.$store.state.data.businessesList,this.$store.state.data.businessesList)},loadOp:function(){var e=this;return 0==this.$store.state.data.opList.length||this.$store.state.data.redisList.opList?f["a"].dispatch("getOpList").then((function(){return e.opList=e.$store.state.data.opList,e.$store.state.data.opList})):(this.opList=this.$store.state.data.opList,this.$store.state.data.opList)},getNormalizer:function(e){var t=this.getFieldConfig(e);return t.normalizer&&"string"===typeof t.normalizer?this[t.normalizer]:t.normalizer&&"function"===typeof t.normalizer?t.normalizer:this.staffNormalizer},saveConfig:function(){var e=this;return Object(o["a"])(Object(s["a"])().mark((function t(){var a;return Object(s["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(t.prev=0,e.configName){t.next=4;break}return e.$message.warning("请输入速查名称"),t.abrupt("return");case 4:if(e.searchConditions.length){t.next=7;break}return e.$message.warning("请添加至少一个搜索条件"),t.abrupt("return");case 7:return a={name:e.configName,type:e.configType,config:{searchConditions:e.searchConditions.map((function(t){return{field:t.field,value:t.value,type:e.getFieldConfig(t.field).type}}))}},t.next=10,Object(v["c"])(a);case 10:e.$message.success("配置保存成功"),t.next=16;break;case 13:t.prev=13,t.t0=t["catch"](0),"cancel"!==t.t0&&e.$message.error("保存配置失败："+(t.t0.message||"未知错误"));case 16:case"end":return t.stop()}}),t,null,[[0,13]])})))()},loadConfigs:function(){var e=this;return Object(o["a"])(Object(s["a"])().mark((function t(){var a,i;return Object(s["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.configLoading=!0,e.configDialogVisible=!0,t.prev=2,t.next=5,Object(v["b"])({configType:e.configType});case 5:a=t.sent,e.savedConfigs=a.rows,t.next=13;break;case 9:t.prev=9,t.t0=t["catch"](2),console.error("加载配置失败:",t.t0),e.$message.error((null===(i=t.t0.response)||void 0===i||null===(i=i.data)||void 0===i?void 0:i.message)||t.t0.message||"加载配置列表失败，请稍后重试");case 13:return t.prev=13,e.configLoading=!1,t.finish(13);case 16:case"end":return t.stop()}}),t,null,[[2,9,13,16]])})))()},handleConfigSelect:function(e){var t=this;return Object(o["a"])(Object(s["a"])().mark((function a(){var i,l,o,r,c;return Object(s["a"])().wrap((function(a){while(1)switch(a.prev=a.next){case 0:if(a.prev=0,"string"===typeof e.config){a.next=3;break}throw new Error("配置格式错误");case 3:if(i=JSON.parse(e.config),t.configName=e.name,t.searchConditions=[],i&&i.searchConditions&&Array.isArray(i.searchConditions)){a.next=8;break}throw new Error("配置数据格式不正确");case 8:l=Object(n["a"])(i.searchConditions),a.prev=9,l.s();case 11:if((o=l.n()).done){a.next=19;break}if(r=o.value,r&&r.field){a.next=15;break}return a.abrupt("continue",17);case 15:c={field:r.field,value:r.value||"",options:[],loading:!1,treeValue:null,treeOptions:[]},t.searchConditions.push(c);case 17:a.next=11;break;case 19:a.next=24;break;case 21:a.prev=21,a.t0=a["catch"](9),l.e(a.t0);case 24:return a.prev=24,l.f(),a.finish(24);case 27:t.configDialogVisible=!1,t.$message.success("配置加载成功"),t.handleSearch(),a.next=36;break;case 32:a.prev=32,a.t1=a["catch"](0),console.error("加载配置失败:",a.t1),t.$message.error("加载配置失败："+(a.t1.message||"配置数据格式错误"));case 36:case"end":return a.stop()}}),a,null,[[0,32],[9,21,24,27]])})))()},deleteConfig:function(e){var t=this;return Object(o["a"])(Object(s["a"])().mark((function a(){return Object(s["a"])().wrap((function(a){while(1)switch(a.prev=a.next){case 0:return a.prev=0,a.next=3,t.$confirm("确认删除该配置？","提示",{type:"warning"});case 3:return a.next=5,Object(v["a"])(e.id);case 5:t.savedConfigs=t.savedConfigs.filter((function(t){return t.id!==e.id})),t.$message.success("配置删除成功"),a.next=12;break;case 9:a.prev=9,a.t0=a["catch"](0),"cancel"!==a.t0&&t.$message.error("删除配置失败："+a.t0.message);case 12:case"end":return a.stop()}}),a,null,[[0,9]])})))()},handleButtonClick:function(e){var t=this;this.clickTimer?(clearTimeout(this.clickTimer),this.clickTimer=null,this.handleReset()):this.clickTimer=setTimeout((function(){t.clickTimer=null,t.loadConfigs()}),300)},openSearchDialog:function(){this.searchDialogVisible=!0},handleSearchAndClose:function(){this.handleSearch(),this.searchDialogVisible=!1}}},g=b,y=a("2877"),C=Object(y["a"])(g,i,l,!1,null,"16078bfc",null);t["a"]=C.exports},"5bc0":function(e,t,a){"use strict";var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("el-select",{attrs:{filterable:"",placeholder:e.placeholder,disabled:e.disable},on:{change:e.handleSelect},model:{value:e.value,callback:function(t){e.value=t},expression:"value"}},e._l(e.options,(function(e){return a("el-option",{key:e.processStatusId,attrs:{label:e.processStatusShortName,value:e.processStatusId}})})),1)],1)},l=[],n=a("4360"),s={name:"ProgressStatus",props:["pass","placeholder","disable"],watch:{pass:function(e){this.value=e}},created:function(){this.loadProcessStatus()},data:function(){return{options:[{value:0,label:"等待"},{value:1,label:"行进"},{value:2,label:"变更"},{value:3,label:"异常"},{value:4,label:"质押"},{value:5,label:"驳回"},{value:6,label:"确认"},{value:7,label:"完成"},{value:8,label:"取消"},{value:9,label:"回收"}],value:this.pass}},methods:{loadProcessStatus:function(){var e=this;0==this.$store.state.data.processStatusList.length||this.$store.state.data.redisList.processStatusList?n["a"].dispatch("getProcessStatus").then((function(){e.options=e.$store.state.data.processStatusList})):this.options=this.$store.state.data.processStatusList},handleSelect:function(){this.$emit("progressStatus",this.value)}}},o=s,r=(a("aff2"),a("2877")),c=Object(r["a"])(o,i,l,!1,null,"389a115f",null);t["a"]=c.exports},7205:function(e,t,a){"use strict";a("1a12")},"75a1":function(e,t,a){},"811c":function(e,t,a){"use strict";a("a164")},"8d6d":function(e,t,a){},"9f2c":function(e,t,a){"use strict";var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("el-select",{staticClass:"selector",attrs:{filterable:"",placeholder:"订单难度"},on:{change:e.handleSelect},model:{value:e.value,callback:function(t){e.value=t},expression:"value"}},e._l(e.options,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1)},l=[],n={name:"OrderDifficultySelect",props:["pass"],watch:{pass:function(e){this.value=e}},data:function(){return{options:[{value:"0",label:"简易"},{value:"1",label:"标准"},{value:"2",label:"高级"},{value:"3",label:"特别"}],value:this.pass}},computed:{},methods:{handleSelect:function(){this.$emit("difficulty",this.value)}}},s=n,o=(a("811c"),a("2877")),r=Object(o["a"])(s,i,l,!1,null,"1350bb7e",null);t["a"]=r.exports},a164:function(e,t,a){},aff2:function(e,t,a){"use strict";a("75a1")},c6cc:function(e,t,a){"use strict";var i=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("el-select",{attrs:{filterable:"",placeholder:"紧急程度"},on:{change:e.handleSelect},model:{value:e.value,callback:function(t){e.value=t},expression:"value"}},e._l(e.options,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1)},l=[],n={name:"UrgencyDegreeSelect",props:["pass"],watch:{pass:function(e){this.value=e}},data:function(){return{options:[{value:"0",label:"预定"},{value:"1",label:"当天"},{value:"2",label:"常规"},{value:"3",label:"紧急"},{value:"4",label:"立即"}],value:""}},methods:{handleSelect:function(){this.$emit("urgencyDegree",this.value)}}},s=n,o=(a("7205"),a("2877")),r=Object(o["a"])(s,i,l,!1,null,"f70f1a68",null);t["a"]=r.exports},f1c6:function(e,t,a){"use strict";a("8d6d")}}]);