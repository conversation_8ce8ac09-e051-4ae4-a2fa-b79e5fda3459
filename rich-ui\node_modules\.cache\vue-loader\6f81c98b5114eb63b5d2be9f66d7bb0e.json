{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\outboundRecord\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\outboundRecord\\index.vue", "mtime": 1750840424298}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgew0KICBhZGRPdXRib3VuZHJlY29yZCwNCiAgY2hhbmdlU3RhdHVzLA0KICBkZWxPdXRib3VuZHJlY29yZCwgZG93bmxvYWRPdXRib3VuZEJpbGwsDQogIGdldE91dGJvdW5kcmVjb3JkLCBnZXRPdXRib3VuZHJlY29yZHMsDQogIGxpc3RPdXRib3VuZHJlY29yZCwgbGlzdE91dGJvdW5kcmVjb3JkcywNCiAgdXBkYXRlT3V0Ym91bmRyZWNvcmQNCn0gZnJvbSAiQC9hcGkvc3lzdGVtL291dGJvdW5kcmVjb3JkIg0KaW1wb3J0IHtwYXJzZVRpbWV9IGZyb20gIi4uLy4uLy4uL3V0aWxzL3JpY2giDQppbXBvcnQge2xpc3RJbnZlbnRvcnksIG91dGJvdW5kSW52ZW50b3J5LCBwcmVPdXRib3VuZEludmVudG9yeX0gZnJvbSAiQC9hcGkvc3lzdGVtL2ludmVudG9yeSINCmltcG9ydCBjdXJyZW5jeSBmcm9tICJjdXJyZW5jeS5qcyINCg0KZXhwb3J0IGRlZmF1bHQgew0KICBuYW1lOiAiT3V0Ym91bmRyZWNvcmQiLA0KICBkYXRhKCkgew0KICAgIHJldHVybiB7DQogICAgICBzZWxlY3RlZENhcmdvRGV0YWlsOltdLA0KICAgICAgc2VhcmNoOiBudWxsLA0KICAgICAgc2hvd0xlZnQ6IDAsDQogICAgICBzaG93UmlnaHQ6IDI0LA0KICAgICAgLy8g6YGu572p5bGCDQogICAgICBsb2FkaW5nOiB0cnVlLA0KICAgICAgc2VsZWN0T3V0Ym91bmRMaXN0OiBbXSwNCiAgICAgIC8vIOmAieS4reaVsOe7hA0KICAgICAgaWRzOiBbXSwNCiAgICAgIC8vIOmdnuWNleS4quemgeeUqA0KICAgICAgc2luZ2xlOiB0cnVlLA0KICAgICAgLy8g6Z2e5aSa5Liq56aB55SoDQogICAgICBtdWx0aXBsZTogdHJ1ZSwNCiAgICAgIC8vIOaYvuekuuaQnOe0ouadoeS7tg0KICAgICAgc2hvd1NlYXJjaDogZmFsc2UsDQogICAgICAvLyDmgLvmnaHmlbANCiAgICAgIHRvdGFsOiAwLA0KICAgICAgLy8g5Ye65LuT6K6w5b2V6KGo5qC85pWw5o2uDQogICAgICBvdXRib3VuZHJlY29yZExpc3Q6IFtdLA0KICAgICAgLy8g5by55Ye65bGC5qCH6aKYDQogICAgICB0aXRsZTogIiIsDQogICAgICAvLyDmmK/lkKbmmL7npLrlvLnlh7rlsYINCiAgICAgIG9wZW46IGZhbHNlLA0KICAgICAgLy8g5p+l6K+i5Y+C5pWwDQogICAgICBxdWVyeVBhcmFtczogew0KICAgICAgICBwYWdlTnVtOiAxLA0KICAgICAgICBwYWdlU2l6ZTogMjAsDQogICAgICAgIGlzUmVudFNldHRsZW1lbnQ6IDAsDQogICAgICAgIG91dGJvdW5kTm86IG51bGwsDQogICAgICAgIGNsaWVudENvZGU6IG51bGwsDQogICAgICAgIGNsaWVudE5hbWU6IG51bGwsDQogICAgICAgIG9wZXJhdG9ySWQ6IG51bGwsDQogICAgICAgIGNvbnRhaW5lclR5cGU6IG51bGwsDQogICAgICAgIGNvbnRhaW5lck5vOiBudWxsLA0KICAgICAgICBzZWFsTm86IG51bGwsDQogICAgICAgIG91dGJvdW5kRGF0ZTogbnVsbCwNCiAgICAgICAgd2FyZWhvdXNlUXVvdGU6IG51bGwsDQogICAgICAgIHdvcmtlckxvYWRpbmdGZWU6IG51bGwsDQogICAgICAgIHdhcmVob3VzZUNvbGxlY3Rpb246IG51bGwsDQogICAgICAgIGNvbGxlY3Rpb25Ob3RlczogbnVsbCwNCiAgICAgICAgdG90YWxCb3hlczogbnVsbCwNCiAgICAgICAgdG90YWxHcm9zc1dlaWdodDogbnVsbCwNCiAgICAgICAgdG90YWxWb2x1bWU6IG51bGwsDQogICAgICAgIHRvdGFsUm93czogbnVsbCwNCiAgICAgICAgcmVjZWl2ZWRTdG9yYWdlRmVlOiBudWxsLA0KICAgICAgICB1bnBhaWRVbmxvYWRpbmdGZWU6IG51bGwsDQogICAgICAgIHVucGFpZFBhY2tpbmdGZWU6IG51bGwsDQogICAgICAgIGxvZ2lzdGljc0FkdmFuY2VGZWU6IG51bGwsDQogICAgICAgIHJlbnRhbEJhbGFuY2VGZWU6IG51bGwsDQogICAgICAgIG92ZXJkdWVSZW50OiBudWxsLA0KICAgICAgICBmcmVlU3RhY2tEYXlzOiBudWxsLA0KICAgICAgICBvdmVyZHVlVW5pdFByaWNlOiBudWxsDQogICAgICB9LA0KICAgICAgLy8g6KGo5Y2V5Y+C5pWwDQogICAgICBmb3JtOiB7fSwNCiAgICAgIG91dGJvdW5kVHlwZTogbnVsbCwNCiAgICAgIHByZU91dGJvdW5kSW52ZW50b3J5TGlzdExvYWRpbmc6IGZhbHNlLA0KICAgICAgLy8g6KGo5Y2V5qCh6aqMDQogICAgICBydWxlczoge30sDQogICAgICBvdXRib3VuZEZvcm06IHt9LA0KICAgICAgb3Blbk91dGJvdW5kOiBmYWxzZSwNCiAgICAgIHByZU91dGJvdW5kSW52ZW50b3J5TGlzdDogW10NCiAgICB9DQogIH0sDQogIHdhdGNoOiB7DQogICAgc2hvd1NlYXJjaChuKSB7DQogICAgICBpZiAobiA9PT0gdHJ1ZSkgew0KICAgICAgICB0aGlzLnNob3dSaWdodCA9IDIxDQogICAgICAgIHRoaXMuc2hvd0xlZnQgPSAzDQogICAgICB9IGVsc2Ugew0KICAgICAgICB0aGlzLnNob3dSaWdodCA9IDI0DQogICAgICAgIHRoaXMuc2hvd0xlZnQgPSAwDQogICAgICB9DQogICAgfQ0KICB9LA0KICBjcmVhdGVkKCkgew0KICAgIHRoaXMuZ2V0TGlzdCgpDQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICAvLyDliKTmlq3lvZPliY3ooYzmmK/lkKbooqvpgInkuK0NCiAgICBpc1Jvd1NlbGVjdGVkKHJvdykgew0KICAgICAgcmV0dXJuIHRoaXMuc2VsZWN0ZWRDYXJnb0RldGFpbC5pbmNsdWRlcyhyb3cpDQogICAgfSwNCiAgICAvLyDmt7vliqDmkJzntKLlubbmu5rliqjliLDljLnphY3ooYznmoTmlrnms5UNCiAgICBoYW5kbGVTZWFyY2hFbnRlcigpIHsNCiAgICAgIGlmICghdGhpcy5zZWFyY2gpIHJldHVybjsNCg0KICAgICAgLy8g5p+l5om+5Yy56YWN55qE6KGM57Si5byVDQogICAgICBjb25zdCBpbmRleCA9IHRoaXMucHJlT3V0Ym91bmRJbnZlbnRvcnlMaXN0LmZpbmRJbmRleCgNCiAgICAgICAgaXRlbSA9PiB7DQogICAgICAgICAgLy8g56Gu5L+dIGluYm91bmRTZXJpYWxObyDlrZjlnKjkuJTkuLrlrZfnrKbkuLINCiAgICAgICAgICBjb25zdCBzZXJpYWxObyA9IFN0cmluZyhpdGVtLmluYm91bmRTZXJpYWxObyB8fCAnJyk7DQogICAgICAgICAgY29uc3Qgc2VhcmNoVmFsdWUgPSBTdHJpbmcodGhpcy5zZWFyY2gpOw0KICAgICAgICAgIC8vIOaJk+WNsOavj+asoeavlOi+g+eahOWAvO+8jOW4ruWKqeiwg+ivlQ0KICAgICAgICAgIHJldHVybiBzZXJpYWxOby5pbmNsdWRlcyhzZWFyY2hWYWx1ZSk7DQogICAgICAgIH0NCiAgICAgICk7DQoNCiAgICAgIGlmIChpbmRleCA+IC0xKSB7DQogICAgICAgIC8vIOiOt+WPluihqOagvERPTQ0KICAgICAgICBjb25zdCB0YWJsZSA9IHRoaXMuJHJlZnMudGFibGU7DQoNCiAgICAgICAgdGhpcy4kbmV4dFRpY2soKCkgPT4gew0KICAgICAgICAgIC8vIOiOt+WPluihqOagvOeahOa7muWKqOWuueWZqA0KICAgICAgICAgIGNvbnN0IHNjcm9sbFdyYXBwZXIgPSB0YWJsZS4kZWwucXVlcnlTZWxlY3RvcignLmVsLXRhYmxlX19ib2R5LXdyYXBwZXInKTsNCiAgICAgICAgICAvLyDojrflj5bmiYDmnInooYwNCiAgICAgICAgICBjb25zdCByb3dzID0gc2Nyb2xsV3JhcHBlci5xdWVyeVNlbGVjdG9yQWxsKCcuZWwtdGFibGVfX3JvdycpOw0KDQogICAgICAgICAgLy8g6YGN5Y6G5omA5pyJ6KGM77yM5om+5Yiw5Yy56YWN55qE5rWB5rC05Y+3DQogICAgICAgICAgbGV0IHRhcmdldEluZGV4ID0gLTE7DQogICAgICAgICAgcm93cy5mb3JFYWNoKChyb3csIGlkeCkgPT4gew0KICAgICAgICAgICAgY29uc3Qgcm93VGV4dCA9IHJvdy50ZXh0Q29udGVudDsNCiAgICAgICAgICAgIGlmIChyb3dUZXh0LmluY2x1ZGVzKHRoaXMuc2VhcmNoKSkgew0KICAgICAgICAgICAgICB0YXJnZXRJbmRleCA9IGlkeDsNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9KTsNCg0KDQogICAgICAgICAgaWYgKHRhcmdldEluZGV4ID4gLTEpIHsNCiAgICAgICAgICAgIGNvbnN0IHRhcmdldFJvdyA9IHJvd3NbdGFyZ2V0SW5kZXhdOw0KICAgICAgICAgICAgLy8g6K6h566X6ZyA6KaB5rua5Yqo55qE5L2N572uDQogICAgICAgICAgICBjb25zdCByb3dUb3AgPSB0YXJnZXRSb3cub2Zmc2V0VG9wOw0KDQogICAgICAgICAgICAvLyDkvb/nlKjlubPmu5Hmu5rliqgNCiAgICAgICAgICAgIHNjcm9sbFdyYXBwZXIuc2Nyb2xsVG8oew0KICAgICAgICAgICAgICB0b3A6IHJvd1RvcCAtIHNjcm9sbFdyYXBwZXIuY2xpZW50SGVpZ2h0IC8gMiwNCiAgICAgICAgICAgICAgYmVoYXZpb3I6ICdzbW9vdGgnDQogICAgICAgICAgICB9KTsNCg0KICAgICAgICAgICAgLy8g6auY5Lqu5pi+56S66K+l6KGMDQogICAgICAgICAgICB0YXJnZXRSb3cuY2xhc3NMaXN0LmFkZCgnaGlnaGxpZ2h0LXJvdycpOw0KICAgICAgICAgICAgLy8gMeenkuWQjuenu+mZpOmrmOS6rg0KICAgICAgICAgICAgc2V0VGltZW91dCgoKSA9PiB7DQogICAgICAgICAgICAgIHRhcmdldFJvdy5jbGFzc0xpc3QucmVtb3ZlKCdoaWdobGlnaHQtcm93Jyk7DQogICAgICAgICAgICB9LCAyMDAwKTsNCiAgICAgICAgICB9DQogICAgICAgIH0pOw0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCfmnKrmib7liLDljLnphY3nmoTorrDlvZUnKTsNCiAgICAgIH0NCiAgICB9LA0KICAgIHdhcmVob3VzZUNvbmZpcm0oKSB7DQoNCiAgICB9LA0KICAgIC8vIOWKoOi9veWtkOiKgueCueaVsOaNrg0KICAgIGxvYWRDaGlsZEludmVudG9yeSh0cmVlLCB0cmVlTm9kZSwgcmVzb2x2ZSkgew0KICAgICAgLy8g5L2/55SocGFja2FnZVRv5a2X5q615p+l6K+i5a2Q6IqC54K5DQogICAgICBsaXN0SW52ZW50b3J5KHtwYWNrYWdlVG86IHRyZWUuaW52ZW50b3J5SWR9KS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgY29uc3Qgcm93cyA9IHJlc3BvbnNlLnJvd3MNCg0KICAgICAgICAvLyDlhYjlsIbmlbDmja7kvKDpgJLnu5nooajmoLzvvIznoa7kv53lrZDoioLngrnmuLLmn5MNCiAgICAgICAgcmVzb2x2ZShyb3dzKQ0KICAgICAgICB0cmVlLmNoaWxkcmVuID0gcm93cw0KDQogICAgICAgIC8vIOWmguaenOeItumhueiiq+mAieS4re+8jOWcqOWtkOiKgueCuea4suafk+WujOaIkOWQjumAieS4reWug+S7rA0KICAgICAgICBpZiAodGhpcy5pZHMuaW5jbHVkZXModHJlZS5pbnZlbnRvcnlJZCkpIHsNCiAgICAgICAgICBzZXRUaW1lb3V0KCgpID0+IHsNCiAgICAgICAgICAgIHJvd3MuZm9yRWFjaChjaGlsZCA9PiB7DQogICAgICAgICAgICAgIGlmICghdGhpcy5pZHMuaW5jbHVkZXMoY2hpbGQuaW52ZW50b3J5SWQpKSB7DQogICAgICAgICAgICAgICAgdGhpcy5pZHMucHVzaChjaGlsZC5pbnZlbnRvcnlJZCkNCiAgICAgICAgICAgICAgICB0aGlzLnNlbGVjdE91dGJvdW5kTGlzdC5wdXNoKGNoaWxkKQ0KICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgIC8vIOWcqFVJ5LiK6YCJ5Lit5a2Q6aG5DQogICAgICAgICAgICAgIHRoaXMuJHJlZnMudGFibGUudG9nZ2xlUm93U2VsZWN0aW9uKGNoaWxkLCB0cnVlKQ0KICAgICAgICAgICAgfSkNCiAgICAgICAgICB9LCA1MCkgLy8g562J5b6FRE9N5pu05pawDQogICAgICAgIH0NCiAgICAgIH0pDQogICAgfSwNCiAgICBzZWxlY3RDb250YWluZXJUeXBlKHR5cGUpIHsNCiAgICAgIHN3aXRjaCAodHlwZSkgew0KICAgICAgICBjYXNlICIyMEdQIjoNCiAgICAgICAgICB0aGlzLm91dGJvdW5kRm9ybS53YXJlaG91c2VRdW90ZSA9IHRoaXMuY2xpZW50Um93LnJhdGUyMGdwDQogICAgICAgICAgYnJlYWsNCiAgICAgICAgY2FzZSAiNDBIUSI6DQogICAgICAgICAgdGhpcy5vdXRib3VuZEZvcm0ud2FyZWhvdXNlUXVvdGUgPSB0aGlzLmNsaWVudFJvdy5yYXRlNDBocQ0KICAgICAgICAgIGJyZWFrDQoNCiAgICAgIH0NCiAgICB9LA0KICAgIGNvdW50U3VtbWFyeSgpIHsNCiAgICAgIHRoaXMub3V0Ym91bmRGb3JtLnVucmVjZWl2ZWRGcm9tQ3VzdG9tZXIgPSBjdXJyZW5jeSh0aGlzLm91dGJvdW5kRm9ybS53YXJlaG91c2VRdW90ZSkuYWRkKHRoaXMub3V0Ym91bmRGb3JtLmFkZGl0aW9uYWxTdG9yYWdlRmVlKS5hZGQodGhpcy5vdXRib3VuZEZvcm0udW5wYWlkVW5sb2FkaW5nRmVlKS5hZGQodGhpcy5vdXRib3VuZEZvcm0udW5wYWlkUGFja2luZ0ZlZSkuYWRkKHRoaXMub3V0Ym91bmRGb3JtLmxvZ2lzdGljc0FkdmFuY2VGZWUpLmFkZCh0aGlzLm91dGJvdW5kRm9ybS5vdmVyZHVlUmVudGFsRmVlKS5hZGQodGhpcy5vdXRib3VuZEZvcm0uZGlmZmljdWx0eVdvcmtGZWUpLnZhbHVlDQogICAgICB0aGlzLm91dGJvdW5kRm9ybS5yZWNlaXZlZEZyb21DdXN0b21lciA9IGN1cnJlbmN5KHRoaXMub3V0Ym91bmRGb3JtLndhcmVob3VzZUNvbGxlY3Rpb24pLnZhbHVlDQogICAgICB0aGlzLm91dGJvdW5kRm9ybS5jdXN0b21lclJlY2VpdmFibGVCYWxhbmNlID0gY3VycmVuY3kodGhpcy5vdXRib3VuZEZvcm0udW5yZWNlaXZlZEZyb21DdXN0b21lcikuc3VidHJhY3QodGhpcy5vdXRib3VuZEZvcm0ucmVjZWl2ZWRGcm9tQ3VzdG9tZXIpLnZhbHVlDQogICAgICB0aGlzLm91dGJvdW5kRm9ybS5wYXlhYmxlVG9Xb3JrZXIgPSBjdXJyZW5jeSh0aGlzLm91dGJvdW5kRm9ybS5yZWNlaXZlZFVubG9hZGluZ0ZlZSkuYWRkKHRoaXMub3V0Ym91bmRGb3JtLnJlY2VpdmVkUGFja2luZ0ZlZSkuYWRkKHRoaXMub3V0Ym91bmRGb3JtLndvcmtlckxvYWRpbmdGZWUpLnZhbHVlDQogICAgICB0aGlzLm91dGJvdW5kRm9ybS5yZWNlaXZlZEZyb21TdXBwbGllciA9IGN1cnJlbmN5KHRoaXMub3V0Ym91bmRGb3JtLnJlY2VpdmVkU3VwcGxpZXIpLmFkZCh0aGlzLm91dGJvdW5kRm9ybS5yZWNlaXZlZFN0b3JhZ2VGZWUpLnZhbHVlDQogICAgICB0aGlzLm91dGJvdW5kRm9ybS5wcm9taXNzb3J5Tm90ZVNhbGVzID0gY3VycmVuY3kodGhpcy5vdXRib3VuZEZvcm0udW5yZWNlaXZlZEZyb21DdXN0b21lcikuYWRkKHRoaXMub3V0Ym91bmRGb3JtLnJlY2VpdmVkRnJvbVN1cHBsaWVyKS52YWx1ZQ0KICAgICAgdGhpcy5vdXRib3VuZEZvcm0ucHJvbWlzc29yeU5vdGVDb3N0ID0gY3VycmVuY3kodGhpcy5vdXRib3VuZEZvcm0ucGF5YWJsZVRvV29ya2VyKS5hZGQodGhpcy5vdXRib3VuZEZvcm0ubG9naXN0aWNzQWR2YW5jZUZlZSkuYWRkKHRoaXMub3V0Ym91bmRGb3JtLndhcmVob3VzZUFkdmFuY2VPdGhlckZlZSkudmFsdWUNCiAgICAgIHRoaXMub3V0Ym91bmRGb3JtLnByb21pc3NvcnlOb3RlR3Jvc3NQcm9maXQgPSBjdXJyZW5jeSh0aGlzLm91dGJvdW5kRm9ybS5wcm9taXNzb3J5Tm90ZVNhbGVzKS5zdWJ0cmFjdCh0aGlzLm91dGJvdW5kRm9ybS5wcm9taXNzb3J5Tm90ZUNvc3QpLnZhbHVlDQogICAgfSwNCiAgICBnZW5lcmF0ZU91dGJvdW5kQmlsbCgpIHsNCiAgICAgIGRvd25sb2FkT3V0Ym91bmRCaWxsKHRoaXMub3V0Ym91bmRGb3JtKQ0KICAgICAgICAudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgICAgLy8g6I635Y+W5paH5Lu255qE5a2X6IqC5pWw57uEIChBcnJheUJ1ZmZlcikNCiAgICAgICAgICBjb25zdCBkYXRhID0gcmVzcG9uc2UNCg0KICAgICAgICAgIC8vIOiOt+WPluaWh+S7tuWQje+8iOWmguaenOWcqOWQjuerr+WTjeW6lOWktOS4reWMheWQq+aWh+S7tuWQje+8iQ0KICAgICAgICAgIGxldCBmaWxlTmFtZSA9IHRoaXMub3V0Ym91bmRGb3JtLmNsaWVudENvZGUgKyAiLSIgKyB0aGlzLm91dGJvdW5kRm9ybS5vcGVyYXRvciArICItIiArIHRoaXMub3V0Ym91bmRGb3JtLm91dGJvdW5kTm8gKyAiLnhsc3giICAvLyDpu5jorqTmlofku7blkI0NCg0KICAgICAgICAgIC8vIOWIm+W7uuS4gOS4qiBCbG9iIOWvueixoeadpeWtmOWCqOaWh+S7tg0KICAgICAgICAgIGNvbnN0IGJsb2IgPSBuZXcgQmxvYihbZGF0YV0sIHsNCiAgICAgICAgICAgIHR5cGU6ICJhcHBsaWNhdGlvbi92bmQub3BlbnhtbGZvcm1hdHMtb2ZmaWNlZG9jdW1lbnQuc3ByZWFkc2hlZXRtbC5zaGVldCIgIC8vIEV4Y2VsIOaWh+S7tuexu+Weiw0KICAgICAgICAgIH0pDQoNCiAgICAgICAgICAvLyDliJvlu7rkuIDkuKrkuLTml7bpk77mjqXvvIzmqKHmi5/ngrnlh7vmnaXkuIvovb3mlofku7YNCiAgICAgICAgICBjb25zdCBsaW5rID0gZG9jdW1lbnQuY3JlYXRlRWxlbWVudCgiYSIpDQogICAgICAgICAgY29uc3QgdXJsID0gd2luZG93LlVSTC5jcmVhdGVPYmplY3RVUkwoYmxvYikgIC8vIOWIm+W7uuS4gOS4qiBVUkwg5oyH5ZCRIEJsb2Ig5a+56LGhDQogICAgICAgICAgbGluay5ocmVmID0gdXJsDQogICAgICAgICAgbGluay5kb3dubG9hZCA9IGZpbGVOYW1lICAvLyDorr7nva7kuIvovb3nmoTmlofku7blkI0NCg0KICAgICAgICAgIC8vIOaooeaLn+eCueWHu+mTvuaOpe+8jOinpuWPkeS4i+i9vQ0KICAgICAgICAgIGRvY3VtZW50LmJvZHkuYXBwZW5kQ2hpbGQobGluaykNCiAgICAgICAgICBsaW5rLmNsaWNrKCkNCg0KICAgICAgICAgIC8vIOS4i+i9veWujOaIkOWQjuenu+mZpOmTvuaOpe+8jOW5tumHiuaUviBVUkwg5a+56LGhDQogICAgICAgICAgZG9jdW1lbnQuYm9keS5yZW1vdmVDaGlsZChsaW5rKQ0KICAgICAgICAgIHdpbmRvdy5VUkwucmV2b2tlT2JqZWN0VVJMKHVybCkNCiAgICAgICAgfSkNCiAgICAgICAgLmNhdGNoKGVycm9yID0+IHsNCiAgICAgICAgICBjb25zb2xlLmVycm9yKCLmlofku7bkuIvovb3lpLHotKU6IiwgZXJyb3IpDQogICAgICAgIH0pDQogICAgfSwNCiAgICAvLyDmn6XnnIvlh7rku5PorrDlvZUNCiAgICBmaW5kT3V0Ym91bmRSZWNvcmQocm93KSB7DQogICAgICB0aGlzLm91dGJvdW5kUmVzZXQoKQ0KICAgICAgZ2V0T3V0Ym91bmRyZWNvcmRzKHJvdy5vdXRib3VuZFJlY29yZElkKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgdGhpcy5vdXRib3VuZEZvcm0gPSByZXNwb25zZS5kYXRhDQogICAgICAgIHRoaXMub3V0Ym91bmRGb3JtLnJzSW52ZW50b3J5TGlzdCA9IHRoaXMub3V0Ym91bmRGb3JtLnJzSW52ZW50b3J5TGlzdCA/IHRoaXMub3V0Ym91bmRGb3JtLnJzSW52ZW50b3J5TGlzdC5tYXAoaXRlbSA9PiB7DQogICAgICAgICAgLy8g6K6h566X6KGl5pS25YWl5LuT6LS5DQogICAgICAgICAgaWYgKGl0ZW0uaW5jbHVkZXNJbmJvdW5kRmVlID09PSAwKSB7DQogICAgICAgICAgICBjb25zdCByZWNlaXZlZEZlZSA9IE51bWJlcihpdGVtLnJlY2VpdmVkU3RvcmFnZUZlZSB8fCAwKQ0KICAgICAgICAgICAgY29uc3QgaW5ib3VuZEZlZSA9IE51bWJlcihpdGVtLmluYm91bmRGZWUgfHwgMCkNCiAgICAgICAgICAgIGNvbnN0IGRpZmZlcmVuY2UgPSBjdXJyZW5jeShpbmJvdW5kRmVlKS5zdWJ0cmFjdChyZWNlaXZlZEZlZSkudmFsdWUNCg0KICAgICAgICAgICAgLy8g5Y+q5pyJ5b2T5beu5YC85aSn5LqOMOaXtuaJjeiuvue9ruihpeaUtui0ueeUqA0KICAgICAgICAgICAgaXRlbS5hZGRpdGlvbmFsU3RvcmFnZUZlZSA9IGRpZmZlcmVuY2UgPiAwID8gZGlmZmVyZW5jZSA6IDANCiAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgaXRlbS5hZGRpdGlvbmFsU3RvcmFnZUZlZSA9IDANCiAgICAgICAgICB9DQoNCiAgICAgICAgICByZXR1cm4gaXRlbQ0KICAgICAgICB9KSA6IFtdDQogICAgICAgIHRoaXMub3Blbk91dGJvdW5kID0gdHJ1ZQ0KICAgICAgfSkNCiAgICB9LA0KICAgIC8qKg0KICAgICAqDQogICAgICogQHBhcmFtIHR5cGUgMDrpooTlh7rku5MvMTrlh7rku5MNCiAgICAgKi8NCiAgICBvdXRib3VuZENvbmZpcm0odHlwZSkgew0KICAgICAgdGhpcy5zZWxlY3RPdXRib3VuZExpc3QubWFwKGl0ZW0gPT4gew0KICAgICAgICBpdGVtLnBhcnRpYWxPdXRib3VuZEZsYWcgPSBOdW1iZXIoaXRlbS5wYXJ0aWFsT3V0Ym91bmRGbGFnKQ0KICAgICAgfSkNCg0KICAgICAgYWRkT3V0Ym91bmRyZWNvcmQodGhpcy5vdXRib3VuZEZvcm0pLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICBpZiAocmVzcG9uc2UuZGF0YSkgew0KICAgICAgICAgIGNvbnN0IG91dGJvdW5kUmVjb3JkSWQgPSByZXNwb25zZS5kYXRhDQoNCiAgICAgICAgICAvLyDliJfooajlhYvpmobkuIDku70s5omT5LiK6aKE5Ye65LuT5qCH5b+XDQogICAgICAgICAgbGV0IGRhdGEgPSB0aGlzLnNlbGVjdE91dGJvdW5kTGlzdC5tYXAoaXRlbSA9PiB7DQogICAgICAgICAgICBpZiAoaXRlbS5wcmVPdXRib3VuZEZsYWcgPT09ICIxIikgew0KICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoIuWLvumAieiusOW9leS4reacieS7pemihOWHuuW6k+iusOW9lSzor7fph43mlrDli77pgIkiKQ0KICAgICAgICAgICAgICByZXR1cm4NCiAgICAgICAgICAgIH0NCiAgICAgICAgICAgIHR5cGUgPT09IDAgPyBpdGVtLnByZU91dGJvdW5kRmxhZyA9ICIxIiA6IG51bGwNCiAgICAgICAgICAgIGl0ZW0ub3V0Ym91bmRSZWNvcmRJZCA9IG91dGJvdW5kUmVjb3JkSWQNCiAgICAgICAgICAgIGl0ZW0ucnNDYXJnb0RldGFpbHNMaXN0ID8gaXRlbS5yc0NhcmdvRGV0YWlsc0xpc3QubWFwKGl0ZW0gPT4gew0KICAgICAgICAgICAgICBpdGVtLm91dGJvdW5kUmVjb3JkSWQgPSBvdXRib3VuZFJlY29yZElkDQogICAgICAgICAgICAgIHR5cGUgPT09IDAgPyBpdGVtLnByZU91dGJvdW5kRmxhZyA9ICIxIiA6IG51bGwNCiAgICAgICAgICAgICAgcmV0dXJuIGl0ZW0NCiAgICAgICAgICAgIH0pIDogbnVsbA0KICAgICAgICAgICAgcmV0dXJuIGl0ZW0NCiAgICAgICAgICB9KQ0KDQogICAgICAgICAgaWYgKHR5cGUgPT09IDApIHsNCiAgICAgICAgICAgIHByZU91dGJvdW5kSW52ZW50b3J5KGRhdGEpLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICAgICAgICB0aGlzLmdldExpc3QoKQ0KICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoIumihOWHuuS7k+aIkOWKnyIpDQogICAgICAgICAgICAgIHRoaXMub3Blbk91dGJvdW5kID0gZmFsc2UNCiAgICAgICAgICAgIH0pDQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIC8vIOWHuuS7kw0KICAgICAgICAgICAgb3V0Ym91bmRJbnZlbnRvcnkoZGF0YSkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgICAgICAgIHRoaXMuZ2V0TGlzdCgpDQogICAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygi5Ye65LuT5oiQ5YqfIikNCiAgICAgICAgICAgICAgdGhpcy5vcGVuT3V0Ym91bmQgPSBmYWxzZQ0KICAgICAgICAgICAgfSkNCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgIH0pDQogICAgfSwNCiAgICAvLyDmoLnmja7lh7rlupPkv6Hmga/liqDovb3lvoXlh7rlupPnmoTorrDlvZUNCiAgICBsb2FkUHJlT3V0Ym91bmRJbnZlbnRvcnlMaXN0KCkgew0KICAgICAgdGhpcy5sb2FkaW5nID0gdHJ1ZQ0KICAgICAgbGV0IGRhdGEgPSB7fQ0KICAgICAgZGF0YS5zcWRQbGFubmVkT3V0Ym91bmREYXRlID0gdGhpcy5vdXRib3VuZEZvcm0ucGxhbm5lZE91dGJvdW5kRGF0ZQ0KICAgICAgZGF0YS5jbGllbnRDb2RlID0gdGhpcy5vdXRib3VuZEZvcm0uY2xpZW50Q29kZQ0KICAgICAgbGlzdEludmVudG9yeShkYXRhKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgY29uc29sZS5sb2cocmVzcG9uc2UpDQogICAgICAgIHRoaXMucHJlT3V0Ym91bmRJbnZlbnRvcnlMaXN0ID0gcmVzcG9uc2Uucm93cw0KICAgICAgICB0aGlzLnRvdGFsID0gcmVzcG9uc2UudG90YWwNCiAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2UNCiAgICAgIH0pDQogICAgfSwNCiAgICAvKioNCiAgICAgKg0KICAgICAqIEBwYXJhbSB0eXBlIDA66aKE5Ye65LuTLzE65q2j5byP5Ye65LuTDQogICAgICovDQogICAgaGFuZGxlT3V0Ym91bmQoc2VsZWN0ZWRSb3dzLCB0eXBlKSB7DQogICAgICAvLyB0aGlzLm91dGJvdW5kTGlzdCA9IHRoaXMuaW52ZW50b3J5TGlzdC5maWx0ZXIoaXRlbSA9PiB0aGlzLmlkcy5pbmNsdWRlcyhpdGVtLmludmVudG9yeUlkKSkNCiAgICAgIGlmICh0eXBlID09PSAxKSB7DQogICAgICAgIHRoaXMub3V0Ym91bmRSZXNldCgpDQogICAgICAgIHRoaXMub3V0Ym91bmRGb3JtID0gc2VsZWN0ZWRSb3dzDQogICAgICB9DQogICAgICB0aGlzLm91dGJvdW5kVHlwZSA9IHR5cGUNCiAgICAgIHRoaXMub3Blbk91dGJvdW5kID0gdHJ1ZQ0KICAgIH0sDQogICAgcGFyc2VUaW1lLA0KICAgIGhhbmRsZU91dGJvdW5kQ2FyZ29EZXRhaWxTZWxlY3Rpb25DaGFuZ2Uoc2VsZWN0aW9uLCByb3cpIHsNCiAgICAgIHJvdy5yc0NhcmdvRGV0YWlsc0xpc3QgPSBzZWxlY3Rpb24NCiAgICAgIHRoaXMuc2VsZWN0ZWRDYXJnb0RldGFpbCA9IHNlbGVjdGlvbg0KICAgIH0sDQogICAgZ2V0U3VtbWFyaWVzKHBhcmFtKSB7DQogICAgICBjb25zdCB7Y29sdW1ucywgZGF0YX0gPSBwYXJhbQ0KICAgICAgY29uc3Qgc3VtcyA9IFtdDQogICAgICBjb25zdCBzdGF0aXN0aWNhbEZpZWxkID0gWw0KICAgICAgICAicmVjZWl2ZWRTdXBwbGllciIsICJ0b3RhbEJveGVzIiwgInVucGFpZEluYm91bmRGZWUiLCAidG90YWxHcm9zc1dlaWdodCIsDQogICAgICAgICJ0b3RhbFZvbHVtZSIsICJyZWNlaXZlZFN0b3JhZ2VGZWUiLCAidW5wYWlkVW5sb2FkaW5nRmVlIiwgImxvZ2lzdGljc0FkdmFuY2VGZWUiLA0KICAgICAgICAicmVudGFsQmFsYW5jZUZlZSIsICJvdmVyZHVlUmVudGFsRmVlIiwgImFkZGl0aW9uYWxTdG9yYWdlRmVlIiwgInVucGFpZFVubG9hZGluZ0ZlZSIsDQogICAgICAgICJ1bnBhaWRQYWNraW5nRmVlIiwgInJlY2VpdmVkVW5sb2FkaW5nRmVlIiwgInJlY2VpdmVkUGFja2luZ0ZlZSIsICJpbmJvdW5kRmVlIg0KICAgICAgXQ0KDQogICAgICAvLyDmsYfmgLvnu5PmnpzlrZjlgqjlr7nosaENCiAgICAgIGNvbnN0IHN1bW1hcnlSZXN1bHRzID0ge30NCg0KICAgICAgY29sdW1ucy5mb3JFYWNoKChjb2x1bW4sIGluZGV4KSA9PiB7DQogICAgICAgIGlmIChpbmRleCA9PT0gMCkgew0KICAgICAgICAgIHN1bXNbaW5kZXhdID0gIuaxh+aAuyINCiAgICAgICAgICByZXR1cm4NCiAgICAgICAgfQ0KDQogICAgICAgIGNvbnN0IHZhbHVlcyA9IGRhdGEubWFwKGl0ZW0gPT4gTnVtYmVyKGl0ZW1bY29sdW1uLnByb3BlcnR5XSkpDQoNCiAgICAgICAgaWYgKHN0YXRpc3RpY2FsRmllbGQuaW5jbHVkZXMoY29sdW1uLnByb3BlcnR5KSAmJiAhdmFsdWVzLmV2ZXJ5KHZhbHVlID0+IGlzTmFOKHZhbHVlKSkpIHsNCiAgICAgICAgICBjb25zdCBzdW1WYWx1ZSA9IHZhbHVlcy5yZWR1Y2UoKHByZXYsIGN1cnIpID0+IHsNCiAgICAgICAgICAgIGNvbnN0IHZhbHVlID0gTnVtYmVyKGN1cnIpDQogICAgICAgICAgICBpZiAoIWlzTmFOKHZhbHVlKSkgew0KICAgICAgICAgICAgICByZXR1cm4gY3VycmVuY3kocHJldikuYWRkKGN1cnIpLnZhbHVlDQogICAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgICByZXR1cm4gcHJldg0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0sIDApDQogICAgICAgICAgc3Vtc1tpbmRleF0gPSBzdW1WYWx1ZQ0KDQogICAgICAgICAgLy8g5bCG5rGH5oC757uT5p6c5a2Y5YKo5ZyoIHN1bW1hcnlSZXN1bHRzIOWvueixoeS4rQ0KICAgICAgICAgIHN1bW1hcnlSZXN1bHRzW2NvbHVtbi5wcm9wZXJ0eV0gPSBzdW1WYWx1ZQ0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHN1bXNbaW5kZXhdID0gIiAiDQogICAgICAgIH0NCiAgICAgIH0pDQoNCiAgICAgIC8vIOWmguaenOmcgOimgeWwhuaxh+aAu+e7k+aenOi1i+WAvOWIsOihqOWNleWtl+auteS4re+8jOWPr+S7peWcqOatpOWkhOaTjeS9nA0KICAgICAgLy8g5YGH6K6+6KGo5Y2V5a2X5q6155qE5ZG95ZCN5LiO57uf6K6h5a2X5q615LiA6Ie0DQogICAgICBPYmplY3Qua2V5cyhzdW1tYXJ5UmVzdWx0cykuZm9yRWFjaChmaWVsZCA9PiB7DQogICAgICAgIC8vIGlmICh0aGlzLm91dGJvdW5kRm9ybSAmJiB0aGlzLm91dGJvdW5kRm9ybS5oYXNPd25Qcm9wZXJ0eShmaWVsZCkpIHsNCiAgICAgICAgaWYgKHRoaXMub3V0Ym91bmRGb3JtKSB7DQogICAgICAgICAgdGhpcy5vdXRib3VuZEZvcm1bZmllbGRdID0gc3VtbWFyeVJlc3VsdHNbZmllbGRdICAvLyDlsIbmsYfmgLvlgLzotYvnu5nooajljZXlrZfmrrUNCiAgICAgICAgfQ0KICAgICAgfSkNCg0KICAgICAgcmV0dXJuIHN1bXMNCiAgICB9LA0KDQogICAgLyogZ2V0U3VtbWFyaWVzKHBhcmFtKSB7DQogICAgICBjb25zdCB7Y29sdW1ucywgZGF0YX0gPSBwYXJhbQ0KICAgICAgY29uc3Qgc3VtcyA9IFtdDQogICAgICBjb25zdCBzdGF0aXN0aWNhbEZpZWxkID0gWyJ0b3RhbEJveGVzIiwgInRvdGFsR3Jvc3NXZWlnaHQiLCAidG90YWxWb2x1bWUiLCAicmVjZWl2ZWRTdG9yYWdlRmVlIiwgInVucGFpZFVubG9hZGluZ0ZlZSIsICJsb2dpc3RpY3NBZHZhbmNlRmVlIiwgInJlbnRhbEJhbGFuY2VGZWUiLCAib3ZlcmR1ZVJlbnRhbEZlZSJdDQogICAgICBjb2x1bW5zLmZvckVhY2goKGNvbHVtbiwgaW5kZXgpID0+IHsNCiAgICAgICAgaWYgKGluZGV4ID09PSAwKSB7DQogICAgICAgICAgc3Vtc1tpbmRleF0gPSAi5rGH5oC7Ig0KICAgICAgICAgIHJldHVybg0KICAgICAgICB9DQogICAgICAgIGNvbnN0IHZhbHVlcyA9IGRhdGEubWFwKGl0ZW0gPT4gTnVtYmVyKGl0ZW1bY29sdW1uLnByb3BlcnR5XSkpDQogICAgICAgIGlmIChzdGF0aXN0aWNhbEZpZWxkLmluY2x1ZGVzKGNvbHVtbi5wcm9wZXJ0eSkgJiYgIXZhbHVlcy5ldmVyeSh2YWx1ZSA9PiBpc05hTih2YWx1ZSkpKSB7DQogICAgICAgICAgc3Vtc1tpbmRleF0gPSB2YWx1ZXMucmVkdWNlKChwcmV2LCBjdXJyKSA9PiB7DQogICAgICAgICAgICBjb25zdCB2YWx1ZSA9IE51bWJlcihjdXJyKQ0KICAgICAgICAgICAgaWYgKCFpc05hTih2YWx1ZSkpIHsNCiAgICAgICAgICAgICAgcmV0dXJuIHByZXYgKyBjdXJyDQogICAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgICByZXR1cm4gcHJldg0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0sIDApDQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgc3Vtc1tpbmRleF0gPSAiICINCiAgICAgICAgfQ0KICAgICAgfSkNCiAgICAgIHJldHVybiBzdW1zDQogICAgfSwgKi8NCiAgICBoYW5kbGVPdXRib3VuZFNlbGVjdGlvbkNoYW5nZShzZWxlY3Rpb24pIHsNCiAgICAgIHRoaXMuc2VsZWN0T3V0Ym91bmRMaXN0ID0gc2VsZWN0aW9uDQogICAgfSwNCiAgICBvdXRib3VuZENsaWVudChyb3cpIHsNCiAgICAgIHRoaXMub3V0Ym91bmRGb3JtLndhcmVob3VzZVF1b3RlID0gcm93LnJhdGVMY2wNCiAgICAgIHRoaXMub3V0Ym91bmRGb3JtLmZyZWVTdGFja0RheXMgPSByb3cuZnJlZVN0YWNrUGVyaW9kDQogICAgICB0aGlzLm91dGJvdW5kRm9ybS5vdmVyZHVlUmVudCA9IHJvdy5vdmVyZHVlUmVudA0KICAgICAgdGhpcy5vdXRib3VuZEZvcm0uY2xpZW50TmFtZSA9IHJvdy5jbGllbnROYW1lDQogICAgICAvLyB0aGlzLm91dGJvdW5kRm9ybS53b3JrZXJMb2FkaW5nRmVlPXJvdy53b3JrZXJMb2FkaW5nRmVlDQogICAgICB0aGlzLiRmb3JjZVVwZGF0ZSgpDQogICAgfSwNCiAgICAvKiog5p+l6K+i5Ye65LuT6K6w5b2V5YiX6KGoICovDQogICAgZ2V0TGlzdCgpIHsNCiAgICAgIHRoaXMubG9hZGluZyA9IHRydWUNCiAgICAgIGxpc3RPdXRib3VuZHJlY29yZHModGhpcy5xdWVyeVBhcmFtcykudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgIHRoaXMub3V0Ym91bmRyZWNvcmRMaXN0ID0gcmVzcG9uc2Uucm93cw0KICAgICAgICB0aGlzLnRvdGFsID0gcmVzcG9uc2UudG90YWwNCiAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2UNCiAgICAgIH0pDQogICAgfSwNCiAgICAvLyDlj5bmtojmjInpkq4NCiAgICBjYW5jZWwoKSB7DQogICAgICB0aGlzLm9wZW4gPSBmYWxzZQ0KICAgICAgdGhpcy5yZXNldCgpDQogICAgfSwNCiAgICBvdXRib3VuZFJlc2V0KCkgew0KICAgICAgdGhpcy5vdXRib3VuZEZvcm0gPSB7DQogICAgICAgIG91dGJvdW5kUmVjb3JkSWQ6IG51bGwsDQogICAgICAgIG91dGJvdW5kTm86IG51bGwsDQogICAgICAgIGNsaWVudENvZGU6IG51bGwsDQogICAgICAgIGNsaWVudE5hbWU6IG51bGwsDQogICAgICAgIG9wZXJhdG9ySWQ6IG51bGwsDQogICAgICAgIGNvbnRhaW5lclR5cGU6IG51bGwsDQogICAgICAgIGNvbnRhaW5lck5vOiBudWxsLA0KICAgICAgICBzZWFsTm86IG51bGwsDQogICAgICAgIG91dGJvdW5kRGF0ZTogbnVsbCwNCiAgICAgICAgd2FyZWhvdXNlUXVvdGU6IG51bGwsDQogICAgICAgIHdvcmtlckxvYWRpbmdGZWU6IG51bGwsDQogICAgICAgIHdhcmVob3VzZUNvbGxlY3Rpb246IG51bGwsDQogICAgICAgIGNvbGxlY3Rpb25Ob3RlczogbnVsbCwNCiAgICAgICAgdG90YWxCb3hlczogbnVsbCwNCiAgICAgICAgdG90YWxHcm9zc1dlaWdodDogbnVsbCwNCiAgICAgICAgdG90YWxWb2x1bWU6IG51bGwsDQogICAgICAgIHRvdGFsUm93czogbnVsbCwNCiAgICAgICAgcmVjZWl2ZWRTdG9yYWdlRmVlOiBudWxsLA0KICAgICAgICB1bnBhaWRVbmxvYWRpbmdGZWU6IG51bGwsDQogICAgICAgIHVucGFpZFBhY2tpbmdGZWU6IG51bGwsDQogICAgICAgIGxvZ2lzdGljc0FkdmFuY2VGZWU6IG51bGwsDQogICAgICAgIHJlbnRhbEJhbGFuY2VGZWU6IG51bGwsDQogICAgICAgIG92ZXJkdWVSZW50OiBudWxsLA0KICAgICAgICBmcmVlU3RhY2tEYXlzOiBudWxsLA0KICAgICAgICBvdmVyZHVlVW5pdFByaWNlOiBudWxsDQogICAgICB9DQogICAgICB0aGlzLnByZU91dGJvdW5kSW52ZW50b3J5TGlzdCA9IFtdDQogICAgICB0aGlzLnJlc2V0Rm9ybSgib3V0Ym91bmRGb3JtIikNCiAgICB9LA0KICAgIC8vIOihqOWNlemHjee9rg0KICAgIHJlc2V0KCkgew0KICAgICAgdGhpcy5mb3JtID0gew0KICAgICAgICBvdXRib3VuZFJlY29yZElkOiBudWxsLA0KICAgICAgICBvdXRib3VuZE5vOiBudWxsLA0KICAgICAgICBjbGllbnRDb2RlOiBudWxsLA0KICAgICAgICBjbGllbnROYW1lOiBudWxsLA0KICAgICAgICBvcGVyYXRvcklkOiBudWxsLA0KICAgICAgICBjb250YWluZXJUeXBlOiBudWxsLA0KICAgICAgICBjb250YWluZXJObzogbnVsbCwNCiAgICAgICAgc2VhbE5vOiBudWxsLA0KICAgICAgICBvdXRib3VuZERhdGU6IG51bGwsDQogICAgICAgIHdhcmVob3VzZVF1b3RlOiBudWxsLA0KICAgICAgICB3b3JrZXJMb2FkaW5nRmVlOiBudWxsLA0KICAgICAgICB3YXJlaG91c2VDb2xsZWN0aW9uOiBudWxsLA0KICAgICAgICBjb2xsZWN0aW9uTm90ZXM6IG51bGwsDQogICAgICAgIHRvdGFsQm94ZXM6IG51bGwsDQogICAgICAgIHRvdGFsR3Jvc3NXZWlnaHQ6IG51bGwsDQogICAgICAgIHRvdGFsVm9sdW1lOiBudWxsLA0KICAgICAgICB0b3RhbFJvd3M6IG51bGwsDQogICAgICAgIHJlY2VpdmVkU3RvcmFnZUZlZTogbnVsbCwNCiAgICAgICAgdW5wYWlkVW5sb2FkaW5nRmVlOiBudWxsLA0KICAgICAgICB1bnBhaWRQYWNraW5nRmVlOiBudWxsLA0KICAgICAgICBsb2dpc3RpY3NBZHZhbmNlRmVlOiBudWxsLA0KICAgICAgICByZW50YWxCYWxhbmNlRmVlOiBudWxsLA0KICAgICAgICBvdmVyZHVlUmVudDogbnVsbCwNCiAgICAgICAgZnJlZVN0YWNrRGF5czogbnVsbCwNCiAgICAgICAgb3ZlcmR1ZVVuaXRQcmljZTogbnVsbA0KICAgICAgfQ0KICAgICAgdGhpcy5yZXNldEZvcm0oImZvcm0iKQ0KICAgIH0sDQogICAgLyoqIOaQnOe0ouaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZVF1ZXJ5KCkgew0KICAgICAgdGhpcy5xdWVyeVBhcmFtcy5wYWdlTnVtID0gMQ0KICAgICAgdGhpcy5nZXRMaXN0KCkNCiAgICB9LA0KICAgIC8qKiDph43nva7mjInpkq7mk43kvZwgKi8NCiAgICByZXNldFF1ZXJ5KCkgew0KICAgICAgdGhpcy5yZXNldEZvcm0oInF1ZXJ5Rm9ybSIpDQogICAgICB0aGlzLmhhbmRsZVF1ZXJ5KCkNCiAgICB9LA0KICAgIGhhbmRsZVN0YXR1c0NoYW5nZShyb3cpIHsNCiAgICAgIGxldCB0ZXh0ID0gcm93LnN0YXR1cyA9PT0gIjAiID8gIuWQr+eUqCIgOiAi5YGc55SoIg0KICAgICAgdGhpcy4kbW9kYWwuY29uZmlybSgi56Gu6K6k6KaBXCIiICsgdGV4dCArICLlkJfvvJ8iKS50aGVuKGZ1bmN0aW9uICgpIHsNCiAgICAgICAgcmV0dXJuIGNoYW5nZVN0YXR1cyhyb3cub3V0Ym91bmRSZWNvcmRJZCwgcm93LnN0YXR1cykNCiAgICAgIH0pLnRoZW4oKCkgPT4gew0KICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKHRleHQgKyAi5oiQ5YqfIikNCiAgICAgIH0pLmNhdGNoKGZ1bmN0aW9uICgpIHsNCiAgICAgICAgcm93LnN0YXR1cyA9IHJvdy5zdGF0dXMgPT09ICIwIiA/ICIxIiA6ICIwIg0KICAgICAgfSkNCiAgICB9LA0KICAgIC8vIOWkmumAieahhumAieS4reaVsOaNrg0KICAgIGhhbmRsZVNlbGVjdGlvbkNoYW5nZShzZWxlY3Rpb24pIHsNCiAgICAgIHRoaXMuaWRzID0gc2VsZWN0aW9uLm1hcChpdGVtID0+IGl0ZW0ub3V0Ym91bmRSZWNvcmRJZCkNCiAgICAgIHRoaXMuc2luZ2xlID0gc2VsZWN0aW9uLmxlbmd0aCAhPT0gMQ0KICAgICAgdGhpcy5tdWx0aXBsZSA9ICFzZWxlY3Rpb24ubGVuZ3RoDQogICAgfSwNCiAgICAvKiog5paw5aKe5oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlQWRkKCkgew0KICAgICAgdGhpcy5vdXRib3VuZFJlc2V0KCkNCiAgICAgIHRoaXMub3BlbiA9IHRydWUNCiAgICAgIHRoaXMudGl0bGUgPSAi5re75Yqg5Ye65LuT6K6w5b2VIg0KICAgIH0sDQogICAgLyoqIOS/ruaUueaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZVVwZGF0ZShyb3cpIHsNCiAgICAgIHRoaXMub3V0Ym91bmRSZXNldCgpDQogICAgICBjb25zdCBvdXRib3VuZFJlY29yZElkID0gcm93Lm91dGJvdW5kUmVjb3JkSWQgfHwgdGhpcy5pZHMNCiAgICAgIGdldE91dGJvdW5kcmVjb3JkKG91dGJvdW5kUmVjb3JkSWQpLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICB0aGlzLmZvcm0gPSByZXNwb25zZS5kYXRhDQogICAgICAgIHRoaXMub3BlbiA9IHRydWUNCiAgICAgICAgdGhpcy50aXRsZSA9ICLkv67mlLnlh7rku5PorrDlvZUiDQogICAgICB9KQ0KICAgIH0sDQogICAgLyoqIOaPkOS6pOaMiemSriAqLw0KICAgIHN1Ym1pdEZvcm0oKSB7DQogICAgICB0aGlzLiRyZWZzWyJvdXRib3VuZEZvcm0iXS52YWxpZGF0ZSh2YWxpZCA9PiB7DQogICAgICAgIGlmICh2YWxpZCkgew0KICAgICAgICAgIGlmICh0aGlzLm91dGJvdW5kRm9ybS5vdXRib3VuZFJlY29yZElkICE9IG51bGwpIHsNCiAgICAgICAgICAgIHVwZGF0ZU91dGJvdW5kcmVjb3JkKHRoaXMub3V0Ym91bmRGb3JtKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5L+u5pS55oiQ5YqfIikNCiAgICAgICAgICAgICAgdGhpcy5vcGVuID0gZmFsc2UNCiAgICAgICAgICAgICAgdGhpcy5nZXRMaXN0KCkNCiAgICAgICAgICAgIH0pDQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICB9KQ0KICAgIH0sDQogICAgLyoqIOWIoOmZpOaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZURlbGV0ZShyb3cpIHsNCiAgICAgIGNvbnN0IG91dGJvdW5kUmVjb3JkSWRzID0gcm93Lm91dGJvdW5kUmVjb3JkSWQgfHwgdGhpcy5pZHMNCiAgICAgIHRoaXMuJG1vZGFsLmNvbmZpcm0oIuaYr+WQpuehruiupOWIoOmZpOWHuuS7k+iusOW9lee8luWPt+S4ulwiIiArIG91dGJvdW5kUmVjb3JkSWRzICsgIlwi55qE5pWw5o2u6aG577yfIikudGhlbihmdW5jdGlvbiAoKSB7DQogICAgICAgIHJldHVybiBkZWxPdXRib3VuZHJlY29yZChvdXRib3VuZFJlY29yZElkcykNCiAgICAgIH0pLnRoZW4oKCkgPT4gew0KICAgICAgICB0aGlzLmdldExpc3QoKQ0KICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLliKDpmaTmiJDlip8iKQ0KICAgICAgfSkuY2F0Y2goKCkgPT4gew0KICAgICAgfSkNCiAgICB9LA0KICAgIC8qKiDlr7zlh7rmjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVFeHBvcnQoKSB7DQogICAgICB0aGlzLmRvd25sb2FkKCJzeXN0ZW0vb3V0Ym91bmRyZWNvcmQvZXhwb3J0Iiwgew0KICAgICAgICAuLi50aGlzLnF1ZXJ5UGFyYW1zDQogICAgICB9LCBgb3V0Ym91bmRyZWNvcmRfJHtuZXcgRGF0ZSgpLmdldFRpbWUoKX0ueGxzeGApDQogICAgfSwNCiAgICAvLyDlpITnkIbotLnnlKjlrZfmrrXlj5jmm7TnmoTpgJrnlKjpgLvovpENCiAgICBoYW5kbGVGZWVDaGFuZ2Uocm93LCBmaWVsZCwgdmFsdWUpIHsNCiAgICAgIC8vIOehruS/neWAvOS4uuaVsOWtlw0KICAgICAgdmFsdWUgPSBOdW1iZXIodmFsdWUpIHx8IDANCg0KICAgICAgLy8g5L2/55SoJHNldOehruS/neWTjeW6lOW8j+abtOaWsA0KICAgICAgdGhpcy4kc2V0KHJvdywgZmllbGQsIHZhbHVlKQ0KDQogICAgICAvLyDlr7nnibnlrprlrZfmrrXlgZrpop3lpJblpITnkIYNCiAgICAgIGlmIChmaWVsZCA9PT0gInJlY2VpdmVkU3RvcmFnZUZlZSIgJiYgcm93LmluY2x1ZGVzSW5ib3VuZEZlZSA9PT0gMCkgew0KICAgICAgICBjb25zdCBpbmJvdW5kRmVlID0gTnVtYmVyKHJvdy5pbmJvdW5kRmVlIHx8IDApDQogICAgICAgIGNvbnN0IGRpZmZlcmVuY2UgPSBjdXJyZW5jeShpbmJvdW5kRmVlKS5zdWJ0cmFjdCh2YWx1ZSkudmFsdWUNCiAgICAgICAgdGhpcy4kc2V0KHJvdywgImFkZGl0aW9uYWxTdG9yYWdlRmVlIiwgZGlmZmVyZW5jZSA+IDAgPyBkaWZmZXJlbmNlIDogMCkNCiAgICAgIH0NCg0KICAgICAgaWYgKGZpZWxkID09PSAnaW5ib3VuZEZlZScpIHsNCiAgICAgICAgY29uc3QgZGlmZmVyZW5jZSA9IGN1cnJlbmN5KHZhbHVlKS5zdWJ0cmFjdChyb3cucmVjZWl2ZWRTdG9yYWdlRmVlKS52YWx1ZQ0KICAgICAgICB0aGlzLiRzZXQocm93LCAiYWRkaXRpb25hbFN0b3JhZ2VGZWUiLCBkaWZmZXJlbmNlID4gMCA/IGRpZmZlcmVuY2UgOiAwKQ0KICAgICAgfQ0KDQogICAgICAvLyDlvLrliLbmm7TmlrDooajmoLzop4blm74NCiAgICAgIHRoaXMuJGZvcmNlVXBkYXRlKCkNCg0KICAgICAgLy8g5bCG5L+u5pS55ZCO55qE5YC86K6+572u5Zue6KGo5qC855qE5pWw5o2u5rqQ77yM56Gu5L+d6KGo5qC85pi+56S65pyA5paw5YC8DQogICAgICBjb25zdCBpbmRleCA9IHRoaXMub3V0Ym91bmRGb3JtLnJzSW52ZW50b3J5TGlzdC5maW5kSW5kZXgoaXRlbSA9PiBpdGVtID09PSByb3cpDQogICAgICBpZiAoaW5kZXggIT09IC0xKSB7DQogICAgICAgIHRoaXMuJHNldCh0aGlzLm91dGJvdW5kRm9ybS5yc0ludmVudG9yeUxpc3QsIGluZGV4LCB7Li4ucm93fSkNCiAgICAgIH0NCg0KICAgICAgLy8g5pu05paw6KGo5qC85ZKM5rGH5oC7DQogICAgICB0aGlzLnVwZGF0ZVRhYmxlRGF0YSgpDQogICAgfSwNCiAgICAvLyDmm7TmlrDooajmoLzmlbDmja7lubbph43mlrDorqHnrpfmsYfmgLsNCiAgICB1cGRhdGVUYWJsZURhdGEoKSB7DQogICAgICAvLyDlvLrliLbmm7TmlrDop4blm74NCiAgICAgIHRoaXMuJGZvcmNlVXBkYXRlKCkNCiAgICAgIC8vIOmHjeaWsOiuoeeul+axh+aAuw0KICAgICAgdGhpcy4kbmV4dFRpY2soKCkgPT4gew0KICAgICAgICAvLyDosIPnlKjorqHnrpfmsYfmgLvnmoTmlrnms5UNCiAgICAgICAgaWYgKHRoaXMub3V0Ym91bmRGb3JtLnJzSW52ZW50b3J5TGlzdCAmJiB0aGlzLm91dGJvdW5kRm9ybS5yc0ludmVudG9yeUxpc3QubGVuZ3RoID4gMCkgew0KICAgICAgICAgIHRoaXMuZ2V0U3VtbWFyaWVzKHsNCiAgICAgICAgICAgIGNvbHVtbnM6IHRoaXMuJHJlZnMub3V0Ym91bmRJbnZlbnRvcnlUYWJsZSA/IHRoaXMuJHJlZnMub3V0Ym91bmRJbnZlbnRvcnlUYWJsZS5jb2x1bW5zIDogW10sDQogICAgICAgICAgICBkYXRhOiB0aGlzLm91dGJvdW5kRm9ybS5yc0ludmVudG9yeUxpc3QNCiAgICAgICAgICB9KQ0KICAgICAgICB9DQogICAgICAgIHRoaXMuY291bnRTdW1tYXJ5KCkNCiAgICAgIH0pDQogICAgfQ0KICB9DQp9DQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuyBA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;AAGA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/system/outboundRecord", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-row :gutter=\"20\">\r\n      <el-col :span=\"showLeft\">\r\n        <el-form v-show=\"showSearch\" ref=\"queryForm\" :inline=\"true\" :model=\"queryParams\" class=\"query\"\r\n                 label-width=\"68px\"\r\n                 size=\"mini\"\r\n        >\r\n          <el-form-item label=\"出仓单号\" prop=\"outboundNo\">\r\n            <el-input\r\n              v-model=\"queryParams.outboundNo\"\r\n              clearable\r\n              placeholder=\"出仓单号\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"客户代码\" prop=\"clientCode\">\r\n            <el-input\r\n              v-model=\"queryParams.clientCode\"\r\n              clearable\r\n              placeholder=\"客户代码\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"出仓日期\" prop=\"outboundDate\">\r\n            <el-date-picker v-model=\"queryParams.outboundDateRange\"\r\n                            clearable style=\"width: 100%\"\r\n                            placeholder=\"出仓日期\"\r\n                            type=\"daterange\"\r\n                            :default-time=\"['00:00:00', '23:59:59']\"\r\n                            value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n            >\r\n            </el-date-picker>\r\n          </el-form-item>\r\n          <el-form-item>\r\n            <el-button icon=\"el-icon-search\" size=\"mini\" type=\"primary\" @click=\"handleQuery\">搜索</el-button>\r\n            <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n          </el-form-item>\r\n        </el-form>\r\n      </el-col>\r\n      <el-col :span=\"showRight\">\r\n        <el-row :gutter=\"10\" class=\"mb8\">\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              v-hasPermi=\"['system:inventory:export']\"\r\n              icon=\"el-icon-download\"\r\n              plain\r\n              size=\"mini\"\r\n              type=\"primary\"\r\n              @click=\"handleExport\"\r\n            >导出\r\n            </el-button>\r\n          </el-col>\r\n          <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n        </el-row>\r\n\r\n        <!--出仓记录列表(预出仓记录列表)-->\r\n        <el-table v-loading=\"loading\" :data=\"outboundrecordList\" @selection-change=\"handleSelectionChange\"\r\n                  @row-dblclick=\"findOutboundRecord\"\r\n        >\r\n          <el-table-column align=\"center\" type=\"selection\" width=\"28\"/>\r\n          <el-table-column align=\"center\" label=\"出仓单号\" prop=\"outboundNo\"/>\r\n          <el-table-column align=\"center\" label=\"客户单号\" prop=\"customerOrderNo\"/>\r\n          <el-table-column align=\"center\" label=\"客户代码\" prop=\"clientCode\"/>\r\n          <el-table-column align=\"center\" label=\"客户名称\" prop=\"clientName\" show-overflow-tooltip/>\r\n          <el-table-column align=\"center\" label=\"操作员\" prop=\"operator\"/>\r\n          <el-table-column align=\"center\" label=\"柜型\" prop=\"containerType\"/>\r\n          <el-table-column align=\"center\" label=\"柜号\" prop=\"containerNo\" show-overflow-tooltip/>\r\n          <el-table-column align=\"center\" label=\"封号\" prop=\"sealNo\"/>\r\n          <el-table-column align=\"center\" label=\"出仓日期\" prop=\"outboundDate\" width=\"100\">\r\n            <template slot-scope=\"scope\">\r\n              <span>{{ parseTime(scope.row.outboundDate, \"{y}-{m}-{d}\") }}</span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"仓库报价\" prop=\"warehouseQuote\"/>\r\n          <el-table-column align=\"center\" label=\"工人装柜费\" prop=\"workerLoadingFee\"/>\r\n          <el-table-column align=\"center\" label=\"仓管代收\" prop=\"warehouseCollection\"/>\r\n          <el-table-column align=\"center\" label=\"补收入仓费\" prop=\"additionalStorageFee\">\r\n            <template slot-scope=\"scope\">\r\n              <el-popover\r\n                placement=\"top\"\r\n                popper-class=\"fee-edit-popover\"\r\n                trigger=\"click\"\r\n                width=\"200\"\r\n              >\r\n                <el-input-number\r\n                  v-model=\"scope.row.additionalStorageFee\"\r\n                  :controls=\"false\"\r\n                  :precision=\"2\"\r\n                  size=\"mini\"\r\n                  style=\"width: 100%\"\r\n                  @change=\"handleFeeChange(scope.row, 'additionalStorageFee', $event)\"\r\n                />\r\n                <span slot=\"reference\">{{ scope.row.additionalStorageFee || 0 }}</span>\r\n              </el-popover>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"已收入仓费\" prop=\"receivedStorageFee\">\r\n            <template slot-scope=\"scope\">\r\n              <el-popover\r\n                placement=\"top\"\r\n                popper-class=\"fee-edit-popover\"\r\n                trigger=\"click\"\r\n                width=\"200\"\r\n              >\r\n                <el-input-number\r\n                  v-model=\"scope.row.receivedStorageFee\"\r\n                  :controls=\"false\"\r\n                  :precision=\"2\"\r\n                  size=\"mini\"\r\n                  style=\"width: 100%\"\r\n                  @change=\"handleFeeChange(scope.row, 'receivedStorageFee', $event)\"\r\n                />\r\n                <span slot=\"reference\">{{ scope.row.receivedStorageFee || 0 }}</span>\r\n              </el-popover>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"未收卸货费\" prop=\"unpaidUnloadingFee\">\r\n            <template slot-scope=\"scope\">\r\n              <el-popover\r\n                placement=\"top\"\r\n                popper-class=\"fee-edit-popover\"\r\n                trigger=\"click\"\r\n                width=\"200\"\r\n              >\r\n                <el-input-number\r\n                  v-model=\"scope.row.unpaidUnloadingFee\"\r\n                  :controls=\"false\"\r\n                  :precision=\"2\"\r\n                  size=\"mini\"\r\n                  style=\"width: 100%\"\r\n                  @change=\"handleFeeChange(scope.row, 'unpaidUnloadingFee', $event)\"\r\n                />\r\n                <span slot=\"reference\">{{ scope.row.unpaidUnloadingFee || 0 }}</span>\r\n              </el-popover>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"实付卸货费\" prop=\"receivedUnloadingFee\">\r\n            <template slot-scope=\"scope\">\r\n              <el-popover\r\n                placement=\"top\"\r\n                popper-class=\"fee-edit-popover\"\r\n                trigger=\"click\"\r\n                width=\"200\"\r\n              >\r\n                <el-input-number\r\n                  v-model=\"scope.row.receivedUnloadingFee\"\r\n                  :controls=\"false\"\r\n                  :precision=\"2\"\r\n                  size=\"mini\"\r\n                  style=\"width: 100%\"\r\n                  @change=\"handleFeeChange(scope.row, 'receivedUnloadingFee', $event)\"\r\n                />\r\n                <span slot=\"reference\">{{ scope.row.receivedUnloadingFee || 0 }}</span>\r\n              </el-popover>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"未收打包费\" prop=\"unpaidPackingFee\">\r\n            <template slot-scope=\"scope\">\r\n              <el-popover\r\n                placement=\"top\"\r\n                popper-class=\"fee-edit-popover\"\r\n                trigger=\"click\"\r\n                width=\"200\"\r\n              >\r\n                <el-input-number\r\n                  v-model=\"scope.row.unpaidPackingFee\"\r\n                  :controls=\"false\"\r\n                  :precision=\"2\"\r\n                  size=\"mini\"\r\n                  style=\"width: 100%\"\r\n                  @change=\"handleFeeChange(scope.row, 'unpaidPackingFee', $event)\"\r\n                />\r\n                <span slot=\"reference\">{{ scope.row.unpaidPackingFee || 0 }}</span>\r\n              </el-popover>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"实付打包费\" prop=\"receivedPackingFee\">\r\n            <template slot-scope=\"scope\">\r\n              <el-popover\r\n                placement=\"top\"\r\n                popper-class=\"fee-edit-popover\"\r\n                trigger=\"click\"\r\n                width=\"200\"\r\n              >\r\n                <el-input-number\r\n                  v-model=\"scope.row.receivedPackingFee\"\r\n                  :controls=\"false\"\r\n                  :precision=\"2\"\r\n                  size=\"mini\"\r\n                  style=\"width: 100%\"\r\n                  @change=\"handleFeeChange(scope.row, 'receivedPackingFee', $event)\"\r\n                />\r\n                <span slot=\"reference\">{{ scope.row.receivedPackingFee || 0 }}</span>\r\n              </el-popover>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"物流代垫费\" prop=\"logisticsAdvanceFee\">\r\n            <template slot-scope=\"scope\">\r\n              <el-popover\r\n                placement=\"top\"\r\n                popper-class=\"fee-edit-popover\"\r\n                trigger=\"click\"\r\n                width=\"200\"\r\n              >\r\n                <el-input-number\r\n                  v-model=\"scope.row.logisticsAdvanceFee\"\r\n                  :controls=\"false\"\r\n                  :precision=\"2\"\r\n                  size=\"mini\"\r\n                  style=\"width: 100%\"\r\n                  @change=\"handleFeeChange(scope.row, 'logisticsAdvanceFee', $event)\"\r\n                />\r\n                <span slot=\"reference\">{{ scope.row.logisticsAdvanceFee || 0 }}</span>\r\n              </el-popover>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"超期租金单价\" prop=\"overdueRentalUnitPrice\"/>\r\n          <el-table-column align=\"center\" label=\"超期租金\" prop=\"overdueRentalFee\"/>\r\n          <el-table-column align=\"center\" label=\"唛头\" prop=\"sqdShippingMark\" show-overflow-tooltip/>\r\n          <el-table-column align=\"center\" label=\"总货名\" prop=\"cargoName\" show-overflow-tooltip/>\r\n          <el-table-column align=\"center\" label=\"免堆期\" prop=\"freeStackPeriod\" width=\"50\"/>\r\n          <el-table-column align=\"center\" class-name=\"small-padding fixed-width\" label=\"操作\">\r\n            <template slot-scope=\"scope\">\r\n              <!--<el-button\r\n                v-hasPermi=\"['system:outboundrecord:edit']\"\r\n                icon=\"el-icon-edit\"\r\n                size=\"mini\"\r\n                style=\"margin-right: -8px\"\r\n                type=\"success\"\r\n                @click=\"handleUpdate(scope.row)\"\r\n              >修改\r\n              </el-button>-->\r\n              <el-button\r\n                v-hasPermi=\"['system:outboundrecord:remove']\"\r\n                icon=\"el-icon-delete\"\r\n                size=\"mini\"\r\n                style=\"margin-right: -8px\"\r\n                type=\"danger\"\r\n                @click=\"handleDelete(scope.row)\"\r\n              >删除\r\n              </el-button>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n        <pagination\r\n          v-show=\"total>0\"\r\n          :limit.sync=\"queryParams.pageSize\"\r\n          :page.sync=\"queryParams.pageNum\"\r\n          :total=\"total\"\r\n          @pagination=\"getList\"\r\n        />\r\n      </el-col>\r\n    </el-row>\r\n\r\n    <!-- 出仓对话框 -->\r\n    <el-dialog\r\n      v-dialogDrag\r\n      v-dialogDragWidth :close-on-click-modal=\"false\" :modal-append-to-body=\"false\" :title=\"'出库单'\"\r\n      :visible.sync=\"openOutbound\"\r\n      append-to-body\r\n      width=\"80%\"\r\n    >\r\n      <el-form ref=\"outboundForm\" :model=\"outboundForm\" :rules=\"rules\" class=\"edit\" label-width=\"80px\">\r\n        <el-row :gutter=\"10\">\r\n          <el-row :gutter=\"10\">\r\n            <el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"出仓单号\" prop=\"outboundNo\">\r\n                  <el-input v-model=\"outboundForm.outboundNo\" class=\"disable-form\" disabled placeholder=\"出仓单号\"/>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"客户单号\" prop=\"outboundNo\">\r\n                  <el-input v-model=\"outboundForm.customerOrderNo\" placeholder=\"客户单号\"/>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"客户代码\" prop=\"outboundNo\">\r\n                  <tree-select :flat=\"false\" :multiple=\"false\" :pass=\"outboundForm.clientCode\"\r\n                               :placeholder=\"'客户代码'\"\r\n                               :type=\"'warehouseClient'\" @return=\"outboundForm.clientCode=$event\"\r\n                               @returnData=\"outboundClient($event)\"\r\n                  />\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"客户名称\" prop=\"outboundNo\">\r\n                  <el-input v-model=\"outboundForm.clientName\" class=\"disable-form\" disabled\r\n                            placeholder=\"客户名称\"\r\n                  />\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"10\">\r\n            <el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"计划出仓\" prop=\"inboundDate\">\r\n                  <el-date-picker v-model=\"outboundForm.plannedOutboundDate\"\r\n                                  clearable\r\n                                  placeholder=\"计划出仓日期\"\r\n                                  style=\"width: 100%\"\r\n                                  type=\"date\"\r\n                                  value-format=\"yyyy-MM-dd\"\r\n                  >\r\n                  </el-date-picker>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"出仓方式\" prop=\"outboundType\">\r\n                  <el-select v-model=\"outboundForm.outboundType\" placeholder=\"出仓方式\" style=\"width: 100%\">\r\n                    <el-option label=\"整柜\" value=\"整柜\"></el-option>\r\n                    <el-option label=\"散货\" value=\"散货\"></el-option>\r\n                    <el-option label=\"快递\" value=\"快递\"></el-option>\r\n                    <el-option label=\"其他\" value=\"其他\"></el-option>\r\n                  </el-select>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"柜型\" prop=\"containerType\">\r\n                  <el-select v-model=\"outboundForm.containerType\" style=\"width: 100%\" @change=\"selectContainerType\">\r\n                    <el-option label=\"20GP\" value=\"20GP\"/>\r\n                    <el-option label=\"20OT\" value=\"20OT\"/>\r\n                    <el-option label=\"20FR\" value=\"20FR\"/>\r\n                    <el-option label=\"TANK\" value=\"TANK\"/>\r\n                    <el-option label=\"40GP\" value=\"40GP\"/>\r\n                    <el-option label=\"40HQ\" value=\"40HQ\"/>\r\n                    <el-option label=\"40NOR\" value=\"40NOR\"/>\r\n                    <el-option label=\"40OT\" value=\"40OT\"/>\r\n                    <el-option label=\"40FR\" value=\"40FR\"/>\r\n                    <el-option label=\"40RH\" value=\"40RH\"/>\r\n                    <el-option label=\"45HQ\" value=\"45HQ\"/>\r\n                  </el-select>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"货物类型\" prop=\"cargoType\">\r\n                  <el-select v-model=\"form.cargoType\" placeholder=\"请选择货物类型\" style=\"width: 100%\">\r\n                    <el-option label=\"普货\" value=\"普货\"></el-option>\r\n                    <el-option label=\"大件\" value=\"大件\"></el-option>\r\n                    <el-option label=\"鲜活\" value=\"鲜活\"></el-option>\r\n                    <el-option label=\"危品\" value=\"危品\"></el-option>\r\n                    <el-option label=\"冷冻\" value=\"冷冻\"></el-option>\r\n                    <el-option label=\"标记\" value=\"标记\"></el-option>\r\n                  </el-select>\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"10\">\r\n            <el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"柜号\" prop=\"containerNo\">\r\n                  <el-input v-model=\"outboundForm.containerNo\" placeholder=\"柜号\"/>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"封号\" prop=\"sealNo\">\r\n                  <el-input v-model=\"outboundForm.sealNo\" placeholder=\"封号\"/>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"车牌\" prop=\"plateNumber\">\r\n                  <el-input v-model=\"outboundForm.plateNumber\" placeholder=\"车牌\"/>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"司机电话\" prop=\"driverPhone\">\r\n                  <el-input v-model=\"outboundForm.driverPhone\" placeholder=\"司机电话\"/>\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"10\">\r\n            <el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"仓库报价\" prop=\"warehouseQuote\">\r\n                  <el-input v-model=\"outboundForm.warehouseQuote\" class=\"number\" placeholder=\"仓库报价\"/>\r\n                  <!--<el-col :span=\"12\">\r\n                    <el-input v-model=\"outboundForm.difficultyWorkFee\" class=\"number\" placeholder=\"困难作业费\"/>\r\n                  </el-col>-->\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"仓管代收\" prop=\"outboundNotes\">\r\n                  <el-input v-model=\"outboundForm.warehouseCollection\" class=\"number\" placeholder=\"仓管代收\"/>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"工人装柜费\" prop=\"workerLoadingFee\">\r\n                  <el-input v-model=\"outboundForm.workerLoadingFee\" class=\"number\" placeholder=\"工人装柜费\"/>\r\n                  <!--<el-col :span=\"12\">\r\n                    <el-input v-model=\"outboundForm.warehouseAdvanceOtherFee\" class=\"number\"\r\n                              placeholder=\"仓库代付其他费用\"\r\n                    />\r\n                  </el-col>-->\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"仓管代付\" prop=\"outboundNotes\">\r\n                  <el-input v-model=\"outboundForm.warehousePay\" class=\"number\" placeholder=\"仓管代收\"/>\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"10\">\r\n            <el-col>\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"操作要求\" prop=\"operationRequirement\">\r\n                  <el-input v-model=\"outboundForm.operationRequirement\" :autosize=\"{ minRows: 4}\" maxlength=\"250\"\r\n                            placeholder=\"内容\"\r\n                            show-word-limit type=\"textarea\"\r\n                  />\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"仓管指示\" prop=\"outboundNote\">\r\n                  <el-input v-model=\"outboundForm.outboundNote\" :autosize=\"{ minRows: 4}\" maxlength=\"250\"\r\n                            placeholder=\"内容\"\r\n                            show-word-limit type=\"textarea\"\r\n                  />\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"10\">\r\n            <el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"操作员\" prop=\"operator\">\r\n                  <el-input v-model=\"outboundForm.operator\" class=\"disable-form\" disabled placeholder=\"操作员\"/>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"下单日期\" prop=\"orderDate\">\r\n                  <el-date-picker v-model=\"outboundForm.orderDate\"\r\n                                  class=\"disable-form\" clearable disabled\r\n                                  placeholder=\"出仓日期\"\r\n                                  style=\"width: 100%\"\r\n                                  type=\"date\"\r\n                                  value-format=\"yyyy-MM-dd\"\r\n                  >\r\n                  </el-date-picker>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"出仓经手人\" prop=\"outboundHandler\">\r\n                  <el-input v-model=\"outboundForm.outboundHandler\" class=\"disable-form\" disabled\r\n                            placeholder=\"出仓经手人\"\r\n                  />\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"6\">\r\n                <el-row>\r\n                  <el-col :span=\"12\">\r\n                    <el-button :disabled=\"outboundForm.clientCode===null\" type=\"primary\" @click=\"warehouseConfirm\">\r\n                      {{ \"仓管确认\" }}\r\n                    </el-button>\r\n                  </el-col>\r\n                  <el-col :span=\"12\">\r\n                    <el-button :disabled=\"outboundForm.clientCode===null\" type=\"primary\"\r\n                               @click=\"loadPreOutboundInventoryList\">\r\n                      {{ \"加载待出库\" }}\r\n                    </el-button>\r\n                  </el-col>\r\n                </el-row>\r\n              </el-col>\r\n            </el-col>\r\n          </el-row>\r\n        </el-row>\r\n        <!--库存记录列表(未出库)-->\r\n        <el-row :gutter=\"10\">\r\n          <el-col>\r\n            <el-col>\r\n              <el-table ref=\"table\" v-loading=\"preOutboundInventoryListLoading\"\r\n                        :data=\"outboundForm.rsInventoryList\" :load=\"loadChildInventory\" :summary-method=\"getSummaries\"\r\n                        :tree-props=\"{children: 'children', hasChildren: 'hasChildren'}\" lazy\r\n                        max-height=\"300\" row-key=\"inventoryId\" show-summary\r\n                        style=\"width: 100%;\"\r\n                        @selection-change=\"handleOutboundSelectionChange\"\r\n              >\r\n                <el-table-column align=\"center\" label=\"入仓流水号\" prop=\"inboundSerialNo\"/>\r\n                <el-table-column align=\"center\" label=\"货物明细\" width=\"50\">\r\n                  <template slot-scope=\"scope\">\r\n                    <el-popover\r\n                      trigger=\"click\"\r\n                      width=\"800\"\r\n                    >\r\n                      <el-table :data=\"scope.row.rsCargoDetailsList\"\r\n                                @selection-change=\"(selectedRows) => handleOutboundCargoDetailSelectionChange(selectedRows,scope.row)\"\r\n                      >\r\n                        <el-table-column align=\"center\" type=\"selection\" width=\"28\"/>\r\n                        <el-table-column\r\n                          label=\"唛头\"\r\n                          prop=\"shippingMark\"\r\n                          width=\"150\"\r\n                        >\r\n                        </el-table-column>\r\n                        <el-table-column\r\n                          label=\"货名\"\r\n                          prop=\"itemName\"\r\n                          width=\"150\"\r\n                        >\r\n                        </el-table-column>\r\n                        <el-table-column\r\n                          label=\"箱数\"\r\n                          prop=\"boxCount\"\r\n                        >\r\n                        </el-table-column>\r\n                        <el-table-column\r\n                          label=\"包装类型\"\r\n                          prop=\"packageType\"\r\n                        >\r\n                        </el-table-column>\r\n                        <el-table-column\r\n                          label=\"单件毛重\"\r\n                          prop=\"unitGrossWeight\"\r\n                        >\r\n                        </el-table-column>\r\n                        <el-table-column\r\n                          label=\"单件长\"\r\n                          prop=\"unitLength\"\r\n                        >\r\n                        </el-table-column>\r\n                        <el-table-column\r\n                          label=\"单件宽\"\r\n                          prop=\"unitWidth\"\r\n                        >\r\n                        </el-table-column>\r\n                        <el-table-column\r\n                          label=\"单件高\"\r\n                          prop=\"unitHeight\"\r\n                        >\r\n                        </el-table-column>\r\n                        <el-table-column\r\n                          label=\"单件体积\"\r\n                          prop=\"unitVolume\"\r\n                        >\r\n                        </el-table-column>\r\n                        <el-table-column\r\n                          label=\"破损标志\"\r\n                          prop=\"damageStatus\"\r\n                        >\r\n                        </el-table-column>\r\n                      </el-table>\r\n                      <el-button slot=\"reference\" style=\"margin: 0;padding: 5px\">查看</el-button>\r\n                    </el-popover>\r\n                  </template>\r\n                </el-table-column>\r\n                <el-table-column align=\"center\" label=\"入仓日期\" prop=\"actualInboundTime\" width=\"80\">\r\n                  <template slot-scope=\"scope\">\r\n                    <span>{{ parseTime(scope.row.actualInboundTime, \"{y}-{m}-{d}\") }}</span>\r\n                  </template>\r\n                </el-table-column>\r\n                <el-table-column align=\"center\" label=\"货代单号\" prop=\"forwarderNo\"/>\r\n                <el-table-column align=\"center\" label=\"客户代码\" prop=\"clientCode\"/>\r\n                <el-table-column align=\"center\" label=\"司机信息\" prop=\"driverInfo\"/>\r\n                <el-table-column align=\"center\" label=\"箱数\" prop=\"totalBoxes\" width=\"50\"/>\r\n                <el-table-column align=\"center\" label=\"毛重\" prop=\"totalGrossWeight\" width=\"80\"/>\r\n                <el-table-column align=\"center\" label=\"体积\" prop=\"totalVolume\" width=\"50\"/>\r\n                <el-table-column align=\"center\" label=\"已收供应商\" prop=\"receivedSupplier\">\r\n                  <template slot-scope=\"scope\">\r\n                    <el-popover\r\n                      placement=\"top\"\r\n                      popper-class=\"fee-edit-popover\"\r\n                      trigger=\"click\"\r\n                      width=\"200\"\r\n                    >\r\n                      <el-input-number\r\n                        v-model=\"scope.row.receivedSupplier\"\r\n                        :controls=\"false\"\r\n                        :precision=\"2\"\r\n                        size=\"mini\"\r\n                        style=\"width: 100%\"\r\n                        @change=\"handleFeeChange(scope.row, 'receivedSupplier', $event)\"\r\n                      />\r\n                      <span slot=\"reference\">{{ scope.row.receivedSupplier || 0 }}</span>\r\n                    </el-popover>\r\n                  </template>\r\n                </el-table-column>\r\n                <el-table-column align=\"center\" label=\"入仓费\" prop=\"inboundFee\">\r\n                  <template slot-scope=\"scope\">\r\n                    <el-popover\r\n                      placement=\"top\"\r\n                      popper-class=\"fee-edit-popover\"\r\n                      trigger=\"click\"\r\n                      width=\"200\"\r\n                    >\r\n                      <el-input-number\r\n                        v-model=\"scope.row.inboundFee\"\r\n                        :controls=\"false\"\r\n                        :precision=\"2\"\r\n                        size=\"mini\"\r\n                        style=\"width: 100%\"\r\n                        @change=\"handleFeeChange(scope.row, 'inboundFee', $event)\"\r\n                      />\r\n                      <span slot=\"reference\">{{ scope.row.inboundFee || 0 }}</span>\r\n                    </el-popover>\r\n                  </template>\r\n                </el-table-column>\r\n                <el-table-column align=\"center\" label=\"已收入仓费\" prop=\"receivedStorageFee\">\r\n                  <template slot-scope=\"scope\">\r\n                    <el-popover\r\n                      placement=\"top\"\r\n                      popper-class=\"fee-edit-popover\"\r\n                      trigger=\"click\"\r\n                      width=\"200\"\r\n                    >\r\n                      <el-input-number\r\n                        v-model=\"scope.row.receivedStorageFee\"\r\n                        :controls=\"false\"\r\n                        :precision=\"2\"\r\n                        size=\"mini\"\r\n                        style=\"width: 100%\"\r\n                        @change=\"handleFeeChange(scope.row, 'receivedStorageFee', $event)\"\r\n                      />\r\n                      <span slot=\"reference\">{{ scope.row.receivedStorageFee || 0 }}</span>\r\n                    </el-popover>\r\n                  </template>\r\n                </el-table-column>\r\n                <el-table-column align=\"center\" label=\"补收入仓费\" prop=\"additionalStorageFee\">\r\n                  <template slot-scope=\"scope\">\r\n                    <el-popover\r\n                      placement=\"top\"\r\n                      popper-class=\"fee-edit-popover\"\r\n                      trigger=\"click\"\r\n                      width=\"200\"\r\n                    >\r\n                      <el-input-number\r\n                        v-model=\"scope.row.additionalStorageFee\"\r\n                        :controls=\"false\"\r\n                        :precision=\"2\"\r\n                        size=\"mini\"\r\n                        style=\"width: 100%\"\r\n                        @change=\"handleFeeChange(scope.row, 'additionalStorageFee', $event)\"\r\n                      />\r\n                      <span slot=\"reference\">{{ scope.row.additionalStorageFee || 0 }}</span>\r\n                    </el-popover>\r\n                  </template>\r\n                </el-table-column>\r\n                <el-table-column align=\"center\" label=\"未收卸货费\" prop=\"unpaidUnloadingFee\">\r\n                  <template slot-scope=\"scope\">\r\n                    <el-popover\r\n                      placement=\"top\"\r\n                      popper-class=\"fee-edit-popover\"\r\n                      trigger=\"click\"\r\n                      width=\"200\"\r\n                    >\r\n                      <el-input-number\r\n                        v-model=\"scope.row.unpaidUnloadingFee\"\r\n                        :controls=\"false\"\r\n                        :precision=\"2\"\r\n                        size=\"mini\"\r\n                        style=\"width: 100%\"\r\n                        @change=\"handleFeeChange(scope.row, 'unpaidUnloadingFee', $event)\"\r\n                      />\r\n                      <span slot=\"reference\">{{ scope.row.unpaidUnloadingFee || 0 }}</span>\r\n                    </el-popover>\r\n                  </template>\r\n                </el-table-column>\r\n                <el-table-column align=\"center\" label=\"实付卸货费\" prop=\"receivedUnloadingFee\">\r\n                  <template slot-scope=\"scope\">\r\n                    <el-popover\r\n                      placement=\"top\"\r\n                      popper-class=\"fee-edit-popover\"\r\n                      trigger=\"click\"\r\n                      width=\"200\"\r\n                    >\r\n                      <el-input-number\r\n                        v-model=\"scope.row.receivedUnloadingFee\"\r\n                        :controls=\"false\"\r\n                        :precision=\"2\"\r\n                        size=\"mini\"\r\n                        style=\"width: 100%\"\r\n                        @change=\"handleFeeChange(scope.row, 'receivedUnloadingFee', $event)\"\r\n                      />\r\n                      <span slot=\"reference\">{{ scope.row.receivedUnloadingFee || 0 }}</span>\r\n                    </el-popover>\r\n                  </template>\r\n                </el-table-column>\r\n                  <el-table-column align=\"center\" label=\"未收打包费\" prop=\"unpaidPackingFee\">\r\n                    <template slot-scope=\"scope\">\r\n                      <el-popover\r\n                        placement=\"top\"\r\n                        popper-class=\"fee-edit-popover\"\r\n                        trigger=\"click\"\r\n                        width=\"200\"\r\n                      >\r\n                        <el-input-number\r\n                          v-model=\"scope.row.unpaidPackingFee\"\r\n                          :controls=\"false\"\r\n                          :precision=\"2\"\r\n                          size=\"mini\"\r\n                          style=\"width: 100%\"\r\n                          @change=\"handleFeeChange(scope.row, 'unpaidPackingFee', $event)\"\r\n                        />\r\n                        <span slot=\"reference\">{{ scope.row.unpaidPackagingFee || 0 }}</span>\r\n                        <span slot=\"reference\">{{ scope.row.unpaidPackingFee || 0 }}</span>\r\n                      </el-popover>\r\n                    </template>\r\n                  </el-table-column>\r\n                  <el-table-column align=\"center\" label=\"实付打包费\" prop=\"receivedPackingFee\">\r\n                    <template slot-scope=\"scope\">\r\n                      <el-popover\r\n                        placement=\"top\"\r\n                        popper-class=\"fee-edit-popover\"\r\n                        trigger=\"click\"\r\n                        width=\"200\"\r\n                      >\r\n                        <el-input-number\r\n                          v-model=\"scope.row.receivedPackingFee\"\r\n                          :controls=\"false\"\r\n                          :precision=\"2\"\r\n                          size=\"mini\"\r\n                          style=\"width: 100%\"\r\n                          @change=\"handleFeeChange(scope.row, 'receivedPackingFee', $event)\"\r\n                        />\r\n                        <span slot=\"reference\">{{ scope.row.receivedPackingFee || 0 }}</span>\r\n                      </el-popover>\r\n                    </template>\r\n                  </el-table-column>\r\n                  <el-table-column align=\"center\" label=\"物流代垫费\" prop=\"logisticsAdvanceFee\">\r\n                    <template slot-scope=\"scope\">\r\n                      <el-popover\r\n                        placement=\"top\"\r\n                        popper-class=\"fee-edit-popover\"\r\n                        trigger=\"click\"\r\n                        width=\"200\"\r\n                      >\r\n                        <el-input-number\r\n                          v-model=\"scope.row.logisticsAdvanceFee\"\r\n                          :controls=\"false\"\r\n                          :precision=\"2\"\r\n                          size=\"mini\"\r\n                          style=\"width: 100%\"\r\n                          @change=\"handleFeeChange(scope.row, 'logisticsAdvanceFee', $event)\"\r\n                        />\r\n                        <span slot=\"reference\">{{ scope.row.logisticsAdvanceFee || 0 }}</span>\r\n                      </el-popover>\r\n                    </template>\r\n                  </el-table-column>\r\n                  <el-table-column align=\"center\" label=\"超期租金\" prop=\"overdueRentalFee\">\r\n                    <template slot-scope=\"scope\">\r\n                      <el-popover\r\n                        placement=\"top\"\r\n                        popper-class=\"fee-edit-popover\"\r\n                        trigger=\"click\"\r\n                        width=\"200\"\r\n                      >\r\n                        <el-input-number\r\n                          v-model=\"scope.row.overdueRentalFee\"\r\n                          :controls=\"false\"\r\n                          :precision=\"2\"\r\n                          size=\"mini\"\r\n                          style=\"width: 100%\"\r\n                          @change=\"handleFeeChange(scope.row, 'overdueRentalFee', $event)\"\r\n                        />\r\n                        <span slot=\"reference\">{{ scope.row.overdueRentalFee || 0 }}</span>\r\n                      </el-popover>\r\n                    </template>\r\n                  </el-table-column>\r\n              </el-table>\r\n            </el-col>\r\n          </el-col>\r\n        </el-row>\r\n\r\n        <!--汇总费用-->\r\n        <el-row>\r\n          <el-col :span=\"4\">\r\n            <span>未收客户：{{ outboundForm.unreceivedFromCustomer }}</span>\r\n          </el-col>\r\n          <el-col :span=\"4\">\r\n            <span>已收客户：{{ outboundForm.receivedFromCustomer }}</span>\r\n          </el-col>\r\n          <el-col :span=\"4\">\r\n            <span>应收客户余额：{{ outboundForm.customerReceivableBalance }}</span>\r\n          </el-col>\r\n          <el-col :span=\"4\">\r\n            <span>应付工人：{{ outboundForm.payableToWorker }}</span>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"4\">\r\n            <span>本票销售额：{{ outboundForm.promissoryNoteSales }}</span>\r\n          </el-col>\r\n          <el-col :span=\"4\">\r\n            <span>本票成本：{{ outboundForm.promissoryNoteCost }}</span>\r\n          </el-col>\r\n          <el-col :span=\"4\">\r\n            <span>本票结余：{{ outboundForm.promissoryNoteGrossProfit }}</span>\r\n          </el-col>\r\n          <el-col :span=\"4\">\r\n            <span>已收供应商总额：{{ outboundForm.receivedFromSupplier }}</span>\r\n          </el-col>\r\n        </el-row>\r\n      </el-form>\r\n      <span slot=\"footer\" class=\"dialog-footer\">\r\n    <el-button type=\"primary\" @click=\"submitForm\">保存</el-button>\r\n    <el-button @click=\"generateOutboundBill\">生成出仓单</el-button>\r\n        <!--    <el-button v-if=\"outboundType===0\" type=\"primary\" @click=\"outboundConfirm(0)\">确定预出仓</el-button>\r\n            <el-button v-else type=\"primary\" @click=\"outboundConfirm(1)\">出仓</el-button>-->\r\n  </span>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  addOutboundrecord,\r\n  changeStatus,\r\n  delOutboundrecord, downloadOutboundBill,\r\n  getOutboundrecord, getOutboundrecords,\r\n  listOutboundrecord, listOutboundrecords,\r\n  updateOutboundrecord\r\n} from \"@/api/system/outboundrecord\"\r\nimport {parseTime} from \"../../../utils/rich\"\r\nimport {listInventory, outboundInventory, preOutboundInventory} from \"@/api/system/inventory\"\r\nimport currency from \"currency.js\"\r\n\r\nexport default {\r\n  name: \"Outboundrecord\",\r\n  data() {\r\n    return {\r\n      selectedCargoDetail:[],\r\n      search: null,\r\n      showLeft: 0,\r\n      showRight: 24,\r\n      // 遮罩层\r\n      loading: true,\r\n      selectOutboundList: [],\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: false,\r\n      // 总条数\r\n      total: 0,\r\n      // 出仓记录表格数据\r\n      outboundrecordList: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 20,\r\n        isRentSettlement: 0,\r\n        outboundNo: null,\r\n        clientCode: null,\r\n        clientName: null,\r\n        operatorId: null,\r\n        containerType: null,\r\n        containerNo: null,\r\n        sealNo: null,\r\n        outboundDate: null,\r\n        warehouseQuote: null,\r\n        workerLoadingFee: null,\r\n        warehouseCollection: null,\r\n        collectionNotes: null,\r\n        totalBoxes: null,\r\n        totalGrossWeight: null,\r\n        totalVolume: null,\r\n        totalRows: null,\r\n        receivedStorageFee: null,\r\n        unpaidUnloadingFee: null,\r\n        unpaidPackingFee: null,\r\n        logisticsAdvanceFee: null,\r\n        rentalBalanceFee: null,\r\n        overdueRent: null,\r\n        freeStackDays: null,\r\n        overdueUnitPrice: null\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      outboundType: null,\r\n      preOutboundInventoryListLoading: false,\r\n      // 表单校验\r\n      rules: {},\r\n      outboundForm: {},\r\n      openOutbound: false,\r\n      preOutboundInventoryList: []\r\n    }\r\n  },\r\n  watch: {\r\n    showSearch(n) {\r\n      if (n === true) {\r\n        this.showRight = 21\r\n        this.showLeft = 3\r\n      } else {\r\n        this.showRight = 24\r\n        this.showLeft = 0\r\n      }\r\n    }\r\n  },\r\n  created() {\r\n    this.getList()\r\n  },\r\n  methods: {\r\n    // 判断当前行是否被选中\r\n    isRowSelected(row) {\r\n      return this.selectedCargoDetail.includes(row)\r\n    },\r\n    // 添加搜索并滚动到匹配行的方法\r\n    handleSearchEnter() {\r\n      if (!this.search) return;\r\n\r\n      // 查找匹配的行索引\r\n      const index = this.preOutboundInventoryList.findIndex(\r\n        item => {\r\n          // 确保 inboundSerialNo 存在且为字符串\r\n          const serialNo = String(item.inboundSerialNo || '');\r\n          const searchValue = String(this.search);\r\n          // 打印每次比较的值，帮助调试\r\n          return serialNo.includes(searchValue);\r\n        }\r\n      );\r\n\r\n      if (index > -1) {\r\n        // 获取表格DOM\r\n        const table = this.$refs.table;\r\n\r\n        this.$nextTick(() => {\r\n          // 获取表格的滚动容器\r\n          const scrollWrapper = table.$el.querySelector('.el-table__body-wrapper');\r\n          // 获取所有行\r\n          const rows = scrollWrapper.querySelectorAll('.el-table__row');\r\n\r\n          // 遍历所有行，找到匹配的流水号\r\n          let targetIndex = -1;\r\n          rows.forEach((row, idx) => {\r\n            const rowText = row.textContent;\r\n            if (rowText.includes(this.search)) {\r\n              targetIndex = idx;\r\n            }\r\n          });\r\n\r\n\r\n          if (targetIndex > -1) {\r\n            const targetRow = rows[targetIndex];\r\n            // 计算需要滚动的位置\r\n            const rowTop = targetRow.offsetTop;\r\n\r\n            // 使用平滑滚动\r\n            scrollWrapper.scrollTo({\r\n              top: rowTop - scrollWrapper.clientHeight / 2,\r\n              behavior: 'smooth'\r\n            });\r\n\r\n            // 高亮显示该行\r\n            targetRow.classList.add('highlight-row');\r\n            // 1秒后移除高亮\r\n            setTimeout(() => {\r\n              targetRow.classList.remove('highlight-row');\r\n            }, 2000);\r\n          }\r\n        });\r\n      } else {\r\n        this.$message.warning('未找到匹配的记录');\r\n      }\r\n    },\r\n    warehouseConfirm() {\r\n\r\n    },\r\n    // 加载子节点数据\r\n    loadChildInventory(tree, treeNode, resolve) {\r\n      // 使用packageTo字段查询子节点\r\n      listInventory({packageTo: tree.inventoryId}).then(response => {\r\n        const rows = response.rows\r\n\r\n        // 先将数据传递给表格，确保子节点渲染\r\n        resolve(rows)\r\n        tree.children = rows\r\n\r\n        // 如果父项被选中，在子节点渲染完成后选中它们\r\n        if (this.ids.includes(tree.inventoryId)) {\r\n          setTimeout(() => {\r\n            rows.forEach(child => {\r\n              if (!this.ids.includes(child.inventoryId)) {\r\n                this.ids.push(child.inventoryId)\r\n                this.selectOutboundList.push(child)\r\n              }\r\n              // 在UI上选中子项\r\n              this.$refs.table.toggleRowSelection(child, true)\r\n            })\r\n          }, 50) // 等待DOM更新\r\n        }\r\n      })\r\n    },\r\n    selectContainerType(type) {\r\n      switch (type) {\r\n        case \"20GP\":\r\n          this.outboundForm.warehouseQuote = this.clientRow.rate20gp\r\n          break\r\n        case \"40HQ\":\r\n          this.outboundForm.warehouseQuote = this.clientRow.rate40hq\r\n          break\r\n\r\n      }\r\n    },\r\n    countSummary() {\r\n      this.outboundForm.unreceivedFromCustomer = currency(this.outboundForm.warehouseQuote).add(this.outboundForm.additionalStorageFee).add(this.outboundForm.unpaidUnloadingFee).add(this.outboundForm.unpaidPackingFee).add(this.outboundForm.logisticsAdvanceFee).add(this.outboundForm.overdueRentalFee).add(this.outboundForm.difficultyWorkFee).value\r\n      this.outboundForm.receivedFromCustomer = currency(this.outboundForm.warehouseCollection).value\r\n      this.outboundForm.customerReceivableBalance = currency(this.outboundForm.unreceivedFromCustomer).subtract(this.outboundForm.receivedFromCustomer).value\r\n      this.outboundForm.payableToWorker = currency(this.outboundForm.receivedUnloadingFee).add(this.outboundForm.receivedPackingFee).add(this.outboundForm.workerLoadingFee).value\r\n      this.outboundForm.receivedFromSupplier = currency(this.outboundForm.receivedSupplier).add(this.outboundForm.receivedStorageFee).value\r\n      this.outboundForm.promissoryNoteSales = currency(this.outboundForm.unreceivedFromCustomer).add(this.outboundForm.receivedFromSupplier).value\r\n      this.outboundForm.promissoryNoteCost = currency(this.outboundForm.payableToWorker).add(this.outboundForm.logisticsAdvanceFee).add(this.outboundForm.warehouseAdvanceOtherFee).value\r\n      this.outboundForm.promissoryNoteGrossProfit = currency(this.outboundForm.promissoryNoteSales).subtract(this.outboundForm.promissoryNoteCost).value\r\n    },\r\n    generateOutboundBill() {\r\n      downloadOutboundBill(this.outboundForm)\r\n        .then(response => {\r\n          // 获取文件的字节数组 (ArrayBuffer)\r\n          const data = response\r\n\r\n          // 获取文件名（如果在后端响应头中包含文件名）\r\n          let fileName = this.outboundForm.clientCode + \"-\" + this.outboundForm.operator + \"-\" + this.outboundForm.outboundNo + \".xlsx\"  // 默认文件名\r\n\r\n          // 创建一个 Blob 对象来存储文件\r\n          const blob = new Blob([data], {\r\n            type: \"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet\"  // Excel 文件类型\r\n          })\r\n\r\n          // 创建一个临时链接，模拟点击来下载文件\r\n          const link = document.createElement(\"a\")\r\n          const url = window.URL.createObjectURL(blob)  // 创建一个 URL 指向 Blob 对象\r\n          link.href = url\r\n          link.download = fileName  // 设置下载的文件名\r\n\r\n          // 模拟点击链接，触发下载\r\n          document.body.appendChild(link)\r\n          link.click()\r\n\r\n          // 下载完成后移除链接，并释放 URL 对象\r\n          document.body.removeChild(link)\r\n          window.URL.revokeObjectURL(url)\r\n        })\r\n        .catch(error => {\r\n          console.error(\"文件下载失败:\", error)\r\n        })\r\n    },\r\n    // 查看出仓记录\r\n    findOutboundRecord(row) {\r\n      this.outboundReset()\r\n      getOutboundrecords(row.outboundRecordId).then(response => {\r\n        this.outboundForm = response.data\r\n        this.outboundForm.rsInventoryList = this.outboundForm.rsInventoryList ? this.outboundForm.rsInventoryList.map(item => {\r\n          // 计算补收入仓费\r\n          if (item.includesInboundFee === 0) {\r\n            const receivedFee = Number(item.receivedStorageFee || 0)\r\n            const inboundFee = Number(item.inboundFee || 0)\r\n            const difference = currency(inboundFee).subtract(receivedFee).value\r\n\r\n            // 只有当差值大于0时才设置补收费用\r\n            item.additionalStorageFee = difference > 0 ? difference : 0\r\n          } else {\r\n            item.additionalStorageFee = 0\r\n          }\r\n\r\n          return item\r\n        }) : []\r\n        this.openOutbound = true\r\n      })\r\n    },\r\n    /**\r\n     *\r\n     * @param type 0:预出仓/1:出仓\r\n     */\r\n    outboundConfirm(type) {\r\n      this.selectOutboundList.map(item => {\r\n        item.partialOutboundFlag = Number(item.partialOutboundFlag)\r\n      })\r\n\r\n      addOutboundrecord(this.outboundForm).then(response => {\r\n        if (response.data) {\r\n          const outboundRecordId = response.data\r\n\r\n          // 列表克隆一份,打上预出仓标志\r\n          let data = this.selectOutboundList.map(item => {\r\n            if (item.preOutboundFlag === \"1\") {\r\n              this.$message.warning(\"勾选记录中有以预出库记录,请重新勾选\")\r\n              return\r\n            }\r\n            type === 0 ? item.preOutboundFlag = \"1\" : null\r\n            item.outboundRecordId = outboundRecordId\r\n            item.rsCargoDetailsList ? item.rsCargoDetailsList.map(item => {\r\n              item.outboundRecordId = outboundRecordId\r\n              type === 0 ? item.preOutboundFlag = \"1\" : null\r\n              return item\r\n            }) : null\r\n            return item\r\n          })\r\n\r\n          if (type === 0) {\r\n            preOutboundInventory(data).then(response => {\r\n              this.getList()\r\n              this.$message.success(\"预出仓成功\")\r\n              this.openOutbound = false\r\n            })\r\n          } else {\r\n            // 出仓\r\n            outboundInventory(data).then(response => {\r\n              this.getList()\r\n              this.$message.success(\"出仓成功\")\r\n              this.openOutbound = false\r\n            })\r\n          }\r\n        }\r\n      })\r\n    },\r\n    // 根据出库信息加载待出库的记录\r\n    loadPreOutboundInventoryList() {\r\n      this.loading = true\r\n      let data = {}\r\n      data.sqdPlannedOutboundDate = this.outboundForm.plannedOutboundDate\r\n      data.clientCode = this.outboundForm.clientCode\r\n      listInventory(data).then(response => {\r\n        console.log(response)\r\n        this.preOutboundInventoryList = response.rows\r\n        this.total = response.total\r\n        this.loading = false\r\n      })\r\n    },\r\n    /**\r\n     *\r\n     * @param type 0:预出仓/1:正式出仓\r\n     */\r\n    handleOutbound(selectedRows, type) {\r\n      // this.outboundList = this.inventoryList.filter(item => this.ids.includes(item.inventoryId))\r\n      if (type === 1) {\r\n        this.outboundReset()\r\n        this.outboundForm = selectedRows\r\n      }\r\n      this.outboundType = type\r\n      this.openOutbound = true\r\n    },\r\n    parseTime,\r\n    handleOutboundCargoDetailSelectionChange(selection, row) {\r\n      row.rsCargoDetailsList = selection\r\n      this.selectedCargoDetail = selection\r\n    },\r\n    getSummaries(param) {\r\n      const {columns, data} = param\r\n      const sums = []\r\n      const statisticalField = [\r\n        \"receivedSupplier\", \"totalBoxes\", \"unpaidInboundFee\", \"totalGrossWeight\",\r\n        \"totalVolume\", \"receivedStorageFee\", \"unpaidUnloadingFee\", \"logisticsAdvanceFee\",\r\n        \"rentalBalanceFee\", \"overdueRentalFee\", \"additionalStorageFee\", \"unpaidUnloadingFee\",\r\n        \"unpaidPackingFee\", \"receivedUnloadingFee\", \"receivedPackingFee\", \"inboundFee\"\r\n      ]\r\n\r\n      // 汇总结果存储对象\r\n      const summaryResults = {}\r\n\r\n      columns.forEach((column, index) => {\r\n        if (index === 0) {\r\n          sums[index] = \"汇总\"\r\n          return\r\n        }\r\n\r\n        const values = data.map(item => Number(item[column.property]))\r\n\r\n        if (statisticalField.includes(column.property) && !values.every(value => isNaN(value))) {\r\n          const sumValue = values.reduce((prev, curr) => {\r\n            const value = Number(curr)\r\n            if (!isNaN(value)) {\r\n              return currency(prev).add(curr).value\r\n            } else {\r\n              return prev\r\n            }\r\n          }, 0)\r\n          sums[index] = sumValue\r\n\r\n          // 将汇总结果存储在 summaryResults 对象中\r\n          summaryResults[column.property] = sumValue\r\n        } else {\r\n          sums[index] = \" \"\r\n        }\r\n      })\r\n\r\n      // 如果需要将汇总结果赋值到表单字段中，可以在此处操作\r\n      // 假设表单字段的命名与统计字段一致\r\n      Object.keys(summaryResults).forEach(field => {\r\n        // if (this.outboundForm && this.outboundForm.hasOwnProperty(field)) {\r\n        if (this.outboundForm) {\r\n          this.outboundForm[field] = summaryResults[field]  // 将汇总值赋给表单字段\r\n        }\r\n      })\r\n\r\n      return sums\r\n    },\r\n\r\n    /* getSummaries(param) {\r\n      const {columns, data} = param\r\n      const sums = []\r\n      const statisticalField = [\"totalBoxes\", \"totalGrossWeight\", \"totalVolume\", \"receivedStorageFee\", \"unpaidUnloadingFee\", \"logisticsAdvanceFee\", \"rentalBalanceFee\", \"overdueRentalFee\"]\r\n      columns.forEach((column, index) => {\r\n        if (index === 0) {\r\n          sums[index] = \"汇总\"\r\n          return\r\n        }\r\n        const values = data.map(item => Number(item[column.property]))\r\n        if (statisticalField.includes(column.property) && !values.every(value => isNaN(value))) {\r\n          sums[index] = values.reduce((prev, curr) => {\r\n            const value = Number(curr)\r\n            if (!isNaN(value)) {\r\n              return prev + curr\r\n            } else {\r\n              return prev\r\n            }\r\n          }, 0)\r\n        } else {\r\n          sums[index] = \" \"\r\n        }\r\n      })\r\n      return sums\r\n    }, */\r\n    handleOutboundSelectionChange(selection) {\r\n      this.selectOutboundList = selection\r\n    },\r\n    outboundClient(row) {\r\n      this.outboundForm.warehouseQuote = row.rateLcl\r\n      this.outboundForm.freeStackDays = row.freeStackPeriod\r\n      this.outboundForm.overdueRent = row.overdueRent\r\n      this.outboundForm.clientName = row.clientName\r\n      // this.outboundForm.workerLoadingFee=row.workerLoadingFee\r\n      this.$forceUpdate()\r\n    },\r\n    /** 查询出仓记录列表 */\r\n    getList() {\r\n      this.loading = true\r\n      listOutboundrecords(this.queryParams).then(response => {\r\n        this.outboundrecordList = response.rows\r\n        this.total = response.total\r\n        this.loading = false\r\n      })\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false\r\n      this.reset()\r\n    },\r\n    outboundReset() {\r\n      this.outboundForm = {\r\n        outboundRecordId: null,\r\n        outboundNo: null,\r\n        clientCode: null,\r\n        clientName: null,\r\n        operatorId: null,\r\n        containerType: null,\r\n        containerNo: null,\r\n        sealNo: null,\r\n        outboundDate: null,\r\n        warehouseQuote: null,\r\n        workerLoadingFee: null,\r\n        warehouseCollection: null,\r\n        collectionNotes: null,\r\n        totalBoxes: null,\r\n        totalGrossWeight: null,\r\n        totalVolume: null,\r\n        totalRows: null,\r\n        receivedStorageFee: null,\r\n        unpaidUnloadingFee: null,\r\n        unpaidPackingFee: null,\r\n        logisticsAdvanceFee: null,\r\n        rentalBalanceFee: null,\r\n        overdueRent: null,\r\n        freeStackDays: null,\r\n        overdueUnitPrice: null\r\n      }\r\n      this.preOutboundInventoryList = []\r\n      this.resetForm(\"outboundForm\")\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        outboundRecordId: null,\r\n        outboundNo: null,\r\n        clientCode: null,\r\n        clientName: null,\r\n        operatorId: null,\r\n        containerType: null,\r\n        containerNo: null,\r\n        sealNo: null,\r\n        outboundDate: null,\r\n        warehouseQuote: null,\r\n        workerLoadingFee: null,\r\n        warehouseCollection: null,\r\n        collectionNotes: null,\r\n        totalBoxes: null,\r\n        totalGrossWeight: null,\r\n        totalVolume: null,\r\n        totalRows: null,\r\n        receivedStorageFee: null,\r\n        unpaidUnloadingFee: null,\r\n        unpaidPackingFee: null,\r\n        logisticsAdvanceFee: null,\r\n        rentalBalanceFee: null,\r\n        overdueRent: null,\r\n        freeStackDays: null,\r\n        overdueUnitPrice: null\r\n      }\r\n      this.resetForm(\"form\")\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1\r\n      this.getList()\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\")\r\n      this.handleQuery()\r\n    },\r\n    handleStatusChange(row) {\r\n      let text = row.status === \"0\" ? \"启用\" : \"停用\"\r\n      this.$modal.confirm(\"确认要\\\"\" + text + \"吗？\").then(function () {\r\n        return changeStatus(row.outboundRecordId, row.status)\r\n      }).then(() => {\r\n        this.$modal.msgSuccess(text + \"成功\")\r\n      }).catch(function () {\r\n        row.status = row.status === \"0\" ? \"1\" : \"0\"\r\n      })\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.outboundRecordId)\r\n      this.single = selection.length !== 1\r\n      this.multiple = !selection.length\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.outboundReset()\r\n      this.open = true\r\n      this.title = \"添加出仓记录\"\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.outboundReset()\r\n      const outboundRecordId = row.outboundRecordId || this.ids\r\n      getOutboundrecord(outboundRecordId).then(response => {\r\n        this.form = response.data\r\n        this.open = true\r\n        this.title = \"修改出仓记录\"\r\n      })\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      this.$refs[\"outboundForm\"].validate(valid => {\r\n        if (valid) {\r\n          if (this.outboundForm.outboundRecordId != null) {\r\n            updateOutboundrecord(this.outboundForm).then(response => {\r\n              this.$modal.msgSuccess(\"修改成功\")\r\n              this.open = false\r\n              this.getList()\r\n            })\r\n          }\r\n        }\r\n      })\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const outboundRecordIds = row.outboundRecordId || this.ids\r\n      this.$modal.confirm(\"是否确认删除出仓记录编号为\\\"\" + outboundRecordIds + \"\\\"的数据项？\").then(function () {\r\n        return delOutboundrecord(outboundRecordIds)\r\n      }).then(() => {\r\n        this.getList()\r\n        this.$modal.msgSuccess(\"删除成功\")\r\n      }).catch(() => {\r\n      })\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.download(\"system/outboundrecord/export\", {\r\n        ...this.queryParams\r\n      }, `outboundrecord_${new Date().getTime()}.xlsx`)\r\n    },\r\n    // 处理费用字段变更的通用逻辑\r\n    handleFeeChange(row, field, value) {\r\n      // 确保值为数字\r\n      value = Number(value) || 0\r\n\r\n      // 使用$set确保响应式更新\r\n      this.$set(row, field, value)\r\n\r\n      // 对特定字段做额外处理\r\n      if (field === \"receivedStorageFee\" && row.includesInboundFee === 0) {\r\n        const inboundFee = Number(row.inboundFee || 0)\r\n        const difference = currency(inboundFee).subtract(value).value\r\n        this.$set(row, \"additionalStorageFee\", difference > 0 ? difference : 0)\r\n      }\r\n\r\n      if (field === 'inboundFee') {\r\n        const difference = currency(value).subtract(row.receivedStorageFee).value\r\n        this.$set(row, \"additionalStorageFee\", difference > 0 ? difference : 0)\r\n      }\r\n\r\n      // 强制更新表格视图\r\n      this.$forceUpdate()\r\n\r\n      // 将修改后的值设置回表格的数据源，确保表格显示最新值\r\n      const index = this.outboundForm.rsInventoryList.findIndex(item => item === row)\r\n      if (index !== -1) {\r\n        this.$set(this.outboundForm.rsInventoryList, index, {...row})\r\n      }\r\n\r\n      // 更新表格和汇总\r\n      this.updateTableData()\r\n    },\r\n    // 更新表格数据并重新计算汇总\r\n    updateTableData() {\r\n      // 强制更新视图\r\n      this.$forceUpdate()\r\n      // 重新计算汇总\r\n      this.$nextTick(() => {\r\n        // 调用计算汇总的方法\r\n        if (this.outboundForm.rsInventoryList && this.outboundForm.rsInventoryList.length > 0) {\r\n          this.getSummaries({\r\n            columns: this.$refs.outboundInventoryTable ? this.$refs.outboundInventoryTable.columns : [],\r\n            data: this.outboundForm.rsInventoryList\r\n          })\r\n        }\r\n        this.countSummary()\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n/* 数字字段显示样式 */\r\n.el-table .cell span {\r\n  cursor: pointer;\r\n  color: #606266;\r\n  padding: 2px 5px;\r\n  border-radius: 3px;\r\n  transition: background-color 0.3s;\r\n}\r\n\r\n.el-table .cell span:hover {\r\n  background-color: #f5f7fa;\r\n}\r\n\r\n\r\n</style>\r\n"]}]}