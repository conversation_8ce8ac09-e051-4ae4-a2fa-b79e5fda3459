(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d0a385b"],{"0313":function(e,o,t){"use strict";t.r(o);var l=function(){var e=this,o=e.$createElement,t=e._self._c||o;return t("div",[t("el-tooltip",{attrs:{disabled:null==e.scope.row.role||e.scope.row.role.length<15,placement:"top"}},[t("div",{attrs:{slot:"content"},slot:"content"},[t("h6",{staticStyle:{margin:"0"}},[e._v(" "+e._s(e.scope.row.role)+" ")])]),t("div",[t("h6",{staticStyle:{margin:"0",overflow:"hidden","text-overflow":"ellipsis"}},[e._v(" "+e._s(null!=e.scope.row.role?e.scope.row.role.length>15?e.scope.row.role.substring(0,15)+"...":e.scope.row.role:"")+" ")])])])],1)},r=[],s={name:"role",props:["scope"]},n=s,c=t("2877"),p=Object(c["a"])(n,l,r,!1,null,"4f8f6690",null);o["default"]=p.exports}}]);