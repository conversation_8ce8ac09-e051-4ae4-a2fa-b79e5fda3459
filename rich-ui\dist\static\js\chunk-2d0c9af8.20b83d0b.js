(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d0c9af8"],{"59ca":function(t,e,a){"use strict";a.r(e);var r=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{staticClass:"app-container"},[a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:t.showLeft}},[a("el-form",{directives:[{name:"show",rawName:"v-show",value:t.showSearch,expression:"showSearch"}],ref:"queryForm",staticClass:"query",attrs:{model:t.queryParams,size:"mini",inline:!0,"label-width":"68px"}},[a("el-form-item",{attrs:{label:"简称",prop:"transportationShortName"}},[a("el-input",{attrs:{placeholder:"简称",clearable:""},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.handleQuery(e)}},model:{value:t.queryParams.transportationShortName,callback:function(e){t.$set(t.queryParams,"transportationShortName",e)},expression:"queryParams.transportationShortName"}})],1),a("el-form-item",{attrs:{label:"中文名",prop:"transportationLocalName"}},[a("el-input",{attrs:{placeholder:"中文名",clearable:""},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.handleQuery(e)}},model:{value:t.queryParams.transportationLocalName,callback:function(e){t.$set(t.queryParams,"transportationLocalName",e)},expression:"queryParams.transportationLocalName"}})],1),a("el-form-item",{attrs:{label:"英文名",prop:"transportationEnName"}},[a("el-input",{attrs:{placeholder:"英文名",clearable:""},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.handleQuery(e)}},model:{value:t.queryParams.transportationEnName,callback:function(e){t.$set(t.queryParams,"transportationEnName",e)},expression:"queryParams.transportationEnName"}})],1),a("el-form-item",{attrs:{label:"排序",prop:"orderNum"}},[a("el-input",{attrs:{placeholder:"排序",clearable:""},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.handleQuery(e)}},model:{value:t.queryParams.orderNum,callback:function(e){t.$set(t.queryParams,"orderNum",e)},expression:"queryParams.orderNum"}})],1),a("el-form-item",[a("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"mini"},on:{click:t.handleQuery}},[t._v("搜索")]),a("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:t.resetQuery}},[t._v("重置")])],1)],1)],1),a("el-col",{attrs:{span:t.showRight}},[a("el-row",{staticClass:"mb8",attrs:{gutter:10}},[a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:transportationterms:add"],expression:"['system:transportationterms:add']"}],attrs:{type:"primary",plain:"",icon:"el-icon-plus",size:"mini"},on:{click:t.handleAdd}},[t._v("新增 ")])],1),a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:transportationterms:edit"],expression:"['system:transportationterms:edit']"}],attrs:{type:"success",plain:"",icon:"el-icon-edit",size:"mini",disabled:t.single},on:{click:t.handleUpdate}},[t._v("修改 ")])],1),a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:transportationterms:remove"],expression:"['system:transportationterms:remove']"}],attrs:{type:"danger",plain:"",icon:"el-icon-delete",size:"mini",disabled:t.multiple},on:{click:t.handleDelete}},[t._v("删除 ")])],1),a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:transportationterms:export"],expression:"['system:transportationterms:export']"}],attrs:{type:"warning",plain:"",icon:"el-icon-download",size:"mini"},on:{click:t.handleExport}},[t._v("导出 ")])],1),a("right-toolbar",{attrs:{showSearch:t.showSearch},on:{"update:showSearch":function(e){t.showSearch=e},"update:show-search":function(e){t.showSearch=e},queryTable:t.getList}})],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],attrs:{data:t.transportationtermsList},on:{"selection-change":t.handleSelectionChange}},[a("el-table-column",{attrs:{type:"selection",width:"28",align:"center"}}),a("el-table-column",{attrs:{label:"运输条款",align:"center",prop:"transportationTermsId"}}),a("el-table-column",{attrs:{label:"简称",align:"center",prop:"transportationShortName"}}),a("el-table-column",{attrs:{label:"中文名",align:"center",prop:"transportationLocalName"}}),a("el-table-column",{attrs:{label:"英文名",align:"center",prop:"transportationEnName"}}),a("el-table-column",{attrs:{label:"排序",align:"center",prop:"orderNum"}}),a("el-table-column",{attrs:{label:"状态",align:"center",prop:"status"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-switch",{attrs:{"active-value":"0","inactive-value":"1"},on:{change:function(a){return t.handleStatusChange(e.row)}},model:{value:e.row.status,callback:function(a){t.$set(e.row,"status",a)},expression:"scope.row.status"}})]}}])}),a("el-table-column",{attrs:{label:"备注",align:"center",prop:"remark"}}),a("el-table-column",{attrs:{label:"操作",align:"center","class-name":"small-padding fixed-width"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:transportationterms:edit"],expression:"['system:transportationterms:edit']"}],staticStyle:{"margin-right":"-8px"},attrs:{size:"mini",type:"success",icon:"el-icon-edit"},on:{click:function(a){return t.handleUpdate(e.row)}}},[t._v("修改 ")]),a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:transportationterms:remove"],expression:"['system:transportationterms:remove']"}],staticStyle:{"margin-right":"-8px"},attrs:{size:"mini",type:"danger",icon:"el-icon-delete"},on:{click:function(a){return t.handleDelete(e.row)}}},[t._v("删除 ")])]}}])})],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total>0"}],attrs:{total:t.total,page:t.queryParams.pageNum,limit:t.queryParams.pageSize},on:{"update:page":function(e){return t.$set(t.queryParams,"pageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"pageSize",e)},pagination:t.getList}})],1)],1),a("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"},{name:"dialogDragWidth",rawName:"v-dialogDragWidth"}],attrs:{"close-on-click-modal":!1,"modal-append-to-body":!1,title:t.title,visible:t.open,width:"500px","append-to-body":""},on:{"update:visible":function(e){t.open=e}}},[a("el-form",{ref:"form",staticClass:"edit",attrs:{model:t.form,rules:t.rules,"label-width":"80px"}},[a("el-form-item",{attrs:{label:"简称",prop:"transportationShortName"}},[a("el-input",{attrs:{placeholder:"简称"},model:{value:t.form.transportationShortName,callback:function(e){t.$set(t.form,"transportationShortName",e)},expression:"form.transportationShortName"}})],1),a("el-form-item",{attrs:{label:"中文名",prop:"transportationLocalName"}},[a("el-input",{attrs:{placeholder:"中文名"},model:{value:t.form.transportationLocalName,callback:function(e){t.$set(t.form,"transportationLocalName",e)},expression:"form.transportationLocalName"}})],1),a("el-form-item",{attrs:{label:"英文名",prop:"transportationEnName"}},[a("el-input",{attrs:{placeholder:"英文名"},model:{value:t.form.transportationEnName,callback:function(e){t.$set(t.form,"transportationEnName",e)},expression:"form.transportationEnName"}})],1),a("el-form-item",{attrs:{label:"排序",prop:"orderNum"}},[a("el-input-number",{staticStyle:{width:"100%"},attrs:{controls:!1,placeholder:"排序"},model:{value:t.form.orderNum,callback:function(e){t.$set(t.form,"orderNum",e)},expression:"form.orderNum"}})],1),a("el-form-item",{attrs:{label:"备注",prop:"remark"}},[a("el-input",{attrs:{placeholder:"备注"},model:{value:t.form.remark,callback:function(e){t.$set(t.form,"remark",e)},expression:"form.remark"}})],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:t.submitForm}},[t._v("确 定")]),a("el-button",{on:{click:t.cancel}},[t._v("取 消")])],1)],1)],1)},n=[],o=a("5530"),s=(a("d81d"),a("8162")),i={name:"Transportationterms",data:function(){return{showLeft:3,showRight:21,loading:!0,ids:[],single:!0,multiple:!0,showSearch:!0,total:0,transportationtermsList:[],title:"",open:!1,queryParams:{pageNum:1,pageSize:20,transportationShortName:null,transportationLocalName:null,transportationEnName:null,orderNum:null,status:null},form:{},rules:{}}},watch:{showSearch:function(t){!0===t?(this.showRight=21,this.showLeft=3):(this.showRight=24,this.showLeft=0)}},created:function(){this.getList()},methods:{getList:function(){var t=this;this.loading=!0,Object(s["e"])(this.queryParams).then((function(e){t.transportationtermsList=e.rows,t.total=e.total,t.loading=!1}))},cancel:function(){this.open=!1,this.reset()},reset:function(){this.form={transportationTermsId:null,transportationShortName:null,transportationLocalName:null,transportationEnName:null,orderNum:null,status:"0",remark:null,createBy:null,createTime:null,updateBy:null,updateTime:null,deleteBy:null,deleteTime:null,deleteStatus:"0"},this.resetForm("form")},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.resetForm("queryForm"),this.handleQuery()},handleStatusChange:function(t){var e=this,a="0"===t.status?"启用":"停用";this.$confirm('确认要"'+a+"吗？","提示",{customClass:"modal-confirm"}).then((function(){return Object(s["b"])(t.transportationTermsId,t.status)})).then((function(){e.$modal.msgSuccess(a+"成功")})).catch((function(){t.status="0"===t.status?"1":"0"}))},handleSelectionChange:function(t){this.ids=t.map((function(t){return t.transportationTermsId})),this.single=1!==t.length,this.multiple=!t.length},handleAdd:function(){this.reset(),this.open=!0,this.title="添加运输条款"},handleUpdate:function(t){var e=this;this.reset();var a=t.transportationTermsId||this.ids;Object(s["d"])(a).then((function(t){e.form=t.data,e.open=!0,e.title="修改运输条款"}))},submitForm:function(){var t=this;this.$refs["form"].validate((function(e){e&&(null!=t.form.transportationTermsId?Object(s["g"])(t.form).then((function(e){t.$modal.msgSuccess("修改成功"),t.open=!1,t.getList()})):Object(s["a"])(t.form).then((function(e){t.$modal.msgSuccess("新增成功"),t.open=!1,t.getList()})))}))},handleDelete:function(t){var e=this,a=t.transportationTermsId||this.ids;this.$confirm('是否确认删除运输条款编号为"'+a+'"的数据项？',"提示",{customClass:"modal-confirm"}).then((function(){return Object(s["c"])(a)})).then((function(){e.getList(),e.$modal.msgSuccess("删除成功")})).catch((function(){}))},handleExport:function(){this.download("system/transportationterms/export",Object(o["a"])({},this.queryParams),"transportationterms_".concat((new Date).getTime(),".xlsx"))}}},l=i,m=a("2877"),c=Object(m["a"])(l,r,n,!1,null,null,null);e["default"]=c.exports}}]);