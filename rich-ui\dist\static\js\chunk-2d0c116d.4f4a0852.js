(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d0c116d"],{"450f":function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:e.showLeft}},[a("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],ref:"queryForm",staticClass:"query",attrs:{inline:!0,model:e.queryParams,size:"mini"}},[a("el-form-item",{attrs:{label:"搜索",prop:"roleQuery"}},[a("el-input",{staticStyle:{width:"100%"},attrs:{clearable:"",placeholder:"中英文，简称"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.roleQuery,callback:function(t){e.$set(e.queryParams,"roleQuery",t)},expression:"queryParams.roleQuery"}})],1),a("el-form-item",[a("el-button",{attrs:{icon:"el-icon-search",size:"mini",type:"primary"},on:{click:e.handleQuery}},[e._v("搜索")]),a("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1)],1),a("el-col",{attrs:{span:e.showRight}},[a("el-row",{staticClass:"mb8",attrs:{gutter:10}},[a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:companyrole:add"],expression:"['system:companyrole:add']"}],attrs:{icon:"el-icon-plus",plain:"",size:"mini",type:"primary"},on:{click:e.handleAdd}},[e._v("新增 ")])],1),a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:companyrole:remove"],expression:"['system:companyrole:remove']"}],attrs:{disabled:e.multiple,icon:"el-icon-delete",plain:"",size:"mini",type:"danger"},on:{click:e.handleDelete}},[e._v("删除 ")])],1),a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:companyrole:export"],expression:"['system:companyrole:export']"}],attrs:{icon:"el-icon-download",plain:"",size:"mini",type:"warning"},on:{click:e.handleExport}},[e._v("导出 ")])],1),a("right-toolbar",{attrs:{showSearch:e.showSearch},on:{"update:showSearch":function(t){e.showSearch=t},"update:show-search":function(t){e.showSearch=t},queryTable:e.getList}})],1),e.refreshTable?a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.companyroleList}},[a("el-table-column",{attrs:{align:"left",label:"角色名称"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(t.row.roleShortName)+" "),a("a",{staticStyle:{margin:"0","font-weight":"bold","font-size":"small"}},[e._v(e._s(t.row.roleLocalName))]),e._v(" "+e._s(t.row.roleEnName)+" ")]}}],null,!1,3273281054)}),a("el-table-column",{attrs:{align:"center",label:"备注",prop:"remark"}}),a("el-table-column",{attrs:{align:"center",label:"排序",prop:"orderNum",width:"48"}}),a("el-table-column",{attrs:{align:"center",label:"状态",prop:"status",width:"68"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-switch",{attrs:{"active-value":"0","inactive-value":"1"},on:{change:function(a){return e.handleStatusChange(t.row)}},model:{value:t.row.status,callback:function(a){e.$set(t.row,"status",a)},expression:"scope.row.status"}})]}}],null,!1,3955094654)}),a("el-table-column",{attrs:{align:"center","class-name":"small-padding fixed-width",label:"操作",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:companyrole:edit"],expression:"['system:companyrole:edit']"}],staticStyle:{"margin-right":"-8px"},attrs:{icon:"el-icon-edit",size:"mini",type:"success"},on:{click:function(a){return e.handleUpdate(t.row)}}},[e._v("修改 ")]),0!=t.row.parentId?a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:companyrole:remove"],expression:"['system:companyrole:remove']"}],staticStyle:{"margin-right":"-8px"},attrs:{icon:"el-icon-delete",size:"mini",type:"danger"},on:{click:function(a){return e.handleDelete(t.row)}}},[e._v("删除 ")]):e._e()]}}],null,!1,2921935024)})],1):e._e()],1)],1),a("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"},{name:"dialogDragWidth",rawName:"v-dialogDragWidth"}],attrs:{"close-on-click-modal":!1,"modal-append-to-body":!1,title:e.title,visible:e.open,"append-to-body":"",width:"500px"},on:{"update:visible":function(t){e.open=t}}},[a("el-form",{ref:"form",staticClass:"edit",attrs:{model:e.form,rules:e.rules,"label-width":"58px"}},[a("el-form-item",{attrs:{label:"简称",prop:"roleShortName"}},[a("el-input",{attrs:{placeholder:"简称"},model:{value:e.form.roleShortName,callback:function(t){e.$set(e.form,"roleShortName",t)},expression:"form.roleShortName"}})],1),a("el-form-item",{attrs:{label:"中文名",prop:"roleLocalName"}},[a("el-input",{attrs:{placeholder:"母语名称"},model:{value:e.form.roleLocalName,callback:function(t){e.$set(e.form,"roleLocalName",t)},expression:"form.roleLocalName"}})],1),a("el-form-item",{attrs:{label:"英文名",prop:"roleEnName"}},[a("el-input",{attrs:{placeholder:"英文名"},model:{value:e.form.roleEnName,callback:function(t){e.$set(e.form,"roleEnName",t)},expression:"form.roleEnName"}})],1),a("el-form-item",{attrs:{label:"排序",prop:"orderNum"}},[a("el-input-number",{staticStyle:{width:"100%"},attrs:{controls:!1,placeholder:"横向优先级sort"},model:{value:e.form.orderNum,callback:function(t){e.$set(e.form,"orderNum",t)},expression:"form.orderNum"}})],1),a("el-form-item",{attrs:{label:"备注",prop:"remark"}},[a("el-input",{attrs:{placeholder:"备注"},model:{value:e.form.remark,callback:function(t){e.$set(e.form,"remark",t)},expression:"form.remark"}})],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("确 定")]),a("el-button",{on:{click:e.cancel}},[e._v("取 消")])],1)],1)],1)},o=[],l=a("5530"),n=a("0ffa"),s={name:"Companyrole",data:function(){return{showLeft:3,showRight:21,loading:!0,ids:[],single:!0,multiple:!0,showSearch:!0,companyroleList:[],title:"",open:!1,refreshTable:!0,queryParams:{roleQuery:null},form:{},rules:{}}},watch:{showSearch:function(e){1==e?(this.showRight=21,this.showLeft=3):(this.showRight=24,this.showLeft=0)}},created:function(){this.getList()},methods:{getList:function(){var e=this;this.loading=!0,Object(n["d"])(this.queryParams).then((function(t){e.companyroleList=t.data,e.loading=!1}))},handleStatusChange:function(e){var t=this,a="0"==e.status?"启用":"停用";this.$confirm('确认要"'+a+'""'+e.roleLocalName+'"吗？',"提示",{customClass:"modal-confirm"}).then((function(){return changeStatus(e.roleId,e.status)})).then((function(){t.$modal.msgSuccess(a+"成功")})).catch((function(){e.status="0"==e.status?"1":"0"}))},cancel:function(){this.open=!1,this.reset()},reset:function(){this.form={roleId:null,roleShortName:null,roleLocalName:null,roleEnName:null,orderNum:null,remark:null,createBy:null,createTime:null,updateBy:null,updateTime:null,deleteBy:null,deleteTime:null,deleteStatus:0},this.resetForm("form")},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.resetForm("queryForm"),this.handleQuery()},handleAdd:function(){this.reset(),this.open=!0,this.title="添加公司角色"},handleUpdate:function(e){var t=this;this.reset();var a=e.roleId||this.ids;Object(n["c"])(a).then((function(e){t.form=e.data,t.open=!0,t.title="修改公司角色"}))},submitForm:function(){var e=this;this.$refs["form"].validate((function(t){t&&(null!=e.form.roleId?Object(n["f"])(e.form).then((function(t){e.$modal.msgSuccess("修改成功"),e.open=!1,e.getList()})):Object(n["a"])(e.form).then((function(t){e.$modal.msgSuccess("新增成功"),e.open=!1,e.getList()})))}))},handleDelete:function(e){var t=this,a=e.roleId||this.ids;this.$confirm('是否确认删除公司角色编号为"'+a+'"的数据项？',"提示",{customClass:"modal-confirm"}).then((function(){return Object(n["b"])(a)})).then((function(){t.getList(),t.$modal.msgSuccess("删除成功")})).catch((function(){}))},handleExport:function(){this.download("system/companyrole/export",Object(l["a"])({},this.queryParams),"companyrole_".concat((new Date).getTime(),".xlsx"))}}},i=s,c=a("2877"),m=Object(c["a"])(i,r,o,!1,null,null,null);t["default"]=m.exports}}]);