(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d0ba824"],{3809:function(o,t,c){"use strict";c.r(t);var e=function(){var o=this,t=o.$createElement,c=o._self._c||t;return c("div",[c("el-tooltip",{attrs:{placement:"top",disabled:null==o.scope.row.companyGrade||o.scope.row.companyGrade.length<3||((null!=o.scope.row.contractType?o.scope.row.contractType:"")+(null!=o.scope.row.contractType&&null!=o.scope.row.contractNo?"：":"")+(null!=o.scope.row.contractNo?o.scope.row.contractNo:"")).length<10}},[c("div",{attrs:{slot:"content"},slot:"content"},[c("h6",{staticStyle:{margin:"0"}},[o._v(" "+o._s(o.scope.row.companyGrade)+" ")]),c("h6",{staticStyle:{margin:"0","font-weight":"bold","font-size":"small"}},[o._v(" "+o._s((null!=o.scope.row.contractType?o.scope.row.contractType:"")+(null!=o.scope.row.contractType&&null!=o.scope.row.contractNo&&""!=o.scope.row.contractNo?"：":"")+(null!=o.scope.row.contractNo?o.scope.row.contractNo:""))+" ")])]),c("div",[c("h6",{staticStyle:{margin:"0",overflow:"hidden","text-overflow":"ellipsis","white-space":"nowrap"}},[o._v(" "+o._s(o.scope.row.companyGrade)+" ")]),c("h6",{staticStyle:{margin:"0",overflow:"hidden","text-overflow":"ellipsis","white-space":"nowrap"}},[o._v(" "+o._s((null!=o.scope.row.contractType?o.scope.row.contractType:"")+(null!=o.scope.row.contractType&&null!=o.scope.row.contractNo&&""!=o.scope.row.contractNo?"：":"")+(null!=o.scope.row.contractNo?o.scope.row.contractNo:""))+" ")])])])],1)},n=[],r={name:"company",props:["scope"],data:function(){return{size:this.$store.state.app.size||"mini"}}},a=r,s=c("2877"),p=Object(s["a"])(a,e,n,!1,null,"5ca2675e",null);t["default"]=p.exports}}]);