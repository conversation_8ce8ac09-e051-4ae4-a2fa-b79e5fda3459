(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-02603c8c","chunk-2d0d69a4"],{"151c":function(e,t,o){"use strict";o("9da1")},"5fb3":function(e,t,o){"use strict";o.d(t,"h",(function(){return r})),o.d(t,"g",(function(){return a})),o.d(t,"i",(function(){return l})),o.d(t,"e",(function(){return u})),o.d(t,"f",(function(){return i})),o.d(t,"a",(function(){return s})),o.d(t,"n",(function(){return c})),o.d(t,"d",(function(){return d})),o.d(t,"c",(function(){return p})),o.d(t,"j",(function(){return m})),o.d(t,"m",(function(){return b})),o.d(t,"l",(function(){return h})),o.d(t,"k",(function(){return f})),o.d(t,"b",(function(){return g}));var n=o("b775");function r(e){return Object(n["a"])({url:"/system/inventory/list",method:"get",params:e})}function a(e){return Object(n["a"])({url:"/system/inventory/aggregator",method:"get",params:e})}function l(e){return Object(n["a"])({url:"/system/inventory/lists",method:"get",params:e})}function u(e){return Object(n["a"])({url:"/system/inventory/"+e,method:"get"})}function i(e){return Object(n["a"])({url:"/system/inventory/package",method:"get",params:e})}function s(e){return Object(n["a"])({url:"/system/inventory",method:"post",data:e})}function c(e){return Object(n["a"])({url:"/system/inventory",method:"put",data:e})}function d(e){return Object(n["a"])({url:"/system/inventory/"+e,method:"delete"})}function p(e,t){var o={inventoryId:e,status:t};return Object(n["a"])({url:"/system/inventory/changeStatus",method:"put",data:o})}function m(e){return Object(n["a"])({url:"/system/inventory/outbound",method:"put",data:e})}function b(e){return Object(n["a"])({url:"/system/inventory/settlement",method:"put",data:e})}function h(e){return Object(n["a"])({url:"/system/inventory/preOutbound",method:"put",data:e})}function f(e){return Object(n["a"])({url:"/system/inventory/packUp",method:"put",data:e})}function g(e){return Object(n["a"])({url:"/system/inventory/cancelPkg",method:"put",data:e})}},"6f35":function(e,t,o){"use strict";o.r(t);var n=function(){var e=this,t=e.$createElement,o=e._self._c||t;return o("div",{staticClass:"app-container"},[o("el-row",{attrs:{gutter:20}},[o("el-col",{attrs:{span:e.showLeft}},[o("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],ref:"queryForm",staticClass:"query",attrs:{inline:!0,model:e.queryParams,"label-width":"68px",size:"mini"}},[o("el-form-item",{attrs:{label:"出仓单号",prop:"outboundNo"}},[o("el-input",{attrs:{clearable:"",placeholder:"出仓单号"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.outboundNo,callback:function(t){e.$set(e.queryParams,"outboundNo",t)},expression:"queryParams.outboundNo"}})],1),o("el-form-item",{attrs:{label:"客户代码",prop:"clientCode"}},[o("el-input",{attrs:{clearable:"",placeholder:"客户代码"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.clientCode,callback:function(t){e.$set(e.queryParams,"clientCode",t)},expression:"queryParams.clientCode"}})],1),o("el-form-item",{attrs:{label:"出仓日期",prop:"outboundDate"}},[o("el-date-picker",{staticStyle:{width:"100%"},attrs:{clearable:"",placeholder:"出仓日期",type:"daterange","default-time":["00:00:00","23:59:59"],"value-format":"yyyy-MM-dd HH:mm:ss"},model:{value:e.queryParams.outboundDateRange,callback:function(t){e.$set(e.queryParams,"outboundDateRange",t)},expression:"queryParams.outboundDateRange"}})],1),o("el-form-item",[o("el-button",{attrs:{icon:"el-icon-search",size:"mini",type:"primary"},on:{click:e.handleQuery}},[e._v("搜索")]),o("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1)],1),o("el-col",{attrs:{span:e.showRight}},[o("el-row",{staticClass:"mb8",attrs:{gutter:10}},[o("el-col",{attrs:{span:1.5}},[o("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:inventory:export"],expression:"['system:inventory:export']"}],attrs:{icon:"el-icon-download",plain:"",size:"mini",type:"primary"},on:{click:e.handleExport}},[e._v("导出 ")])],1),o("right-toolbar",{attrs:{showSearch:e.showSearch},on:{"update:showSearch":function(t){e.showSearch=t},"update:show-search":function(t){e.showSearch=t},queryTable:e.getList}})],1),o("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.outboundrecordList},on:{"selection-change":e.handleSelectionChange,"row-dblclick":e.findOutboundRecord}},[o("el-table-column",{attrs:{align:"center",type:"selection",width:"28"}}),o("el-table-column",{attrs:{align:"center",label:"出仓单号",prop:"outboundNo"}}),o("el-table-column",{attrs:{align:"center",label:"客户单号",prop:"customerOrderNo"}}),o("el-table-column",{attrs:{align:"center",label:"客户代码",prop:"clientCode"}}),o("el-table-column",{attrs:{align:"center",label:"客户名称",prop:"clientName","show-overflow-tooltip":""}}),o("el-table-column",{attrs:{align:"center",label:"操作员",prop:"operator"}}),o("el-table-column",{attrs:{align:"center",label:"柜型",prop:"containerType"}}),o("el-table-column",{attrs:{align:"center",label:"柜号",prop:"containerNo","show-overflow-tooltip":""}}),o("el-table-column",{attrs:{align:"center",label:"封号",prop:"sealNo"}}),o("el-table-column",{attrs:{align:"center",label:"出仓日期",prop:"outboundDate",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[o("span",[e._v(e._s(e.parseTime(t.row.outboundDate,"{y}-{m}-{d}")))])]}}])}),o("el-table-column",{attrs:{align:"center",label:"仓库报价",prop:"warehouseQuote"}}),o("el-table-column",{attrs:{align:"center",label:"工人装柜费",prop:"workerLoadingFee"}}),o("el-table-column",{attrs:{align:"center",label:"仓管代收",prop:"warehouseCollection"}}),o("el-table-column",{attrs:{align:"center",label:"补收入仓费",prop:"additionalStorageFee"},scopedSlots:e._u([{key:"default",fn:function(t){return[o("el-popover",{attrs:{placement:"top","popper-class":"fee-edit-popover",trigger:"click",width:"200"}},[o("el-input-number",{staticStyle:{width:"100%"},attrs:{controls:!1,precision:2,size:"mini"},on:{change:function(o){return e.handleFeeChange(t.row,"additionalStorageFee",o)}},model:{value:t.row.additionalStorageFee,callback:function(o){e.$set(t.row,"additionalStorageFee",o)},expression:"scope.row.additionalStorageFee"}}),o("span",{attrs:{slot:"reference"},slot:"reference"},[e._v(e._s(t.row.additionalStorageFee||0))])],1)]}}])}),o("el-table-column",{attrs:{align:"center",label:"已收入仓费",prop:"receivedStorageFee"},scopedSlots:e._u([{key:"default",fn:function(t){return[o("el-popover",{attrs:{placement:"top","popper-class":"fee-edit-popover",trigger:"click",width:"200"}},[o("el-input-number",{staticStyle:{width:"100%"},attrs:{controls:!1,precision:2,size:"mini"},on:{change:function(o){return e.handleFeeChange(t.row,"receivedStorageFee",o)}},model:{value:t.row.receivedStorageFee,callback:function(o){e.$set(t.row,"receivedStorageFee",o)},expression:"scope.row.receivedStorageFee"}}),o("span",{attrs:{slot:"reference"},slot:"reference"},[e._v(e._s(t.row.receivedStorageFee||0))])],1)]}}])}),o("el-table-column",{attrs:{align:"center",label:"未收卸货费",prop:"unpaidUnloadingFee"},scopedSlots:e._u([{key:"default",fn:function(t){return[o("el-popover",{attrs:{placement:"top","popper-class":"fee-edit-popover",trigger:"click",width:"200"}},[o("el-input-number",{staticStyle:{width:"100%"},attrs:{controls:!1,precision:2,size:"mini"},on:{change:function(o){return e.handleFeeChange(t.row,"unpaidUnloadingFee",o)}},model:{value:t.row.unpaidUnloadingFee,callback:function(o){e.$set(t.row,"unpaidUnloadingFee",o)},expression:"scope.row.unpaidUnloadingFee"}}),o("span",{attrs:{slot:"reference"},slot:"reference"},[e._v(e._s(t.row.unpaidUnloadingFee||0))])],1)]}}])}),o("el-table-column",{attrs:{align:"center",label:"实付卸货费",prop:"receivedUnloadingFee"},scopedSlots:e._u([{key:"default",fn:function(t){return[o("el-popover",{attrs:{placement:"top","popper-class":"fee-edit-popover",trigger:"click",width:"200"}},[o("el-input-number",{staticStyle:{width:"100%"},attrs:{controls:!1,precision:2,size:"mini"},on:{change:function(o){return e.handleFeeChange(t.row,"receivedUnloadingFee",o)}},model:{value:t.row.receivedUnloadingFee,callback:function(o){e.$set(t.row,"receivedUnloadingFee",o)},expression:"scope.row.receivedUnloadingFee"}}),o("span",{attrs:{slot:"reference"},slot:"reference"},[e._v(e._s(t.row.receivedUnloadingFee||0))])],1)]}}])}),o("el-table-column",{attrs:{align:"center",label:"未收打包费",prop:"unpaidPackingFee"},scopedSlots:e._u([{key:"default",fn:function(t){return[o("el-popover",{attrs:{placement:"top","popper-class":"fee-edit-popover",trigger:"click",width:"200"}},[o("el-input-number",{staticStyle:{width:"100%"},attrs:{controls:!1,precision:2,size:"mini"},on:{change:function(o){return e.handleFeeChange(t.row,"unpaidPackingFee",o)}},model:{value:t.row.unpaidPackingFee,callback:function(o){e.$set(t.row,"unpaidPackingFee",o)},expression:"scope.row.unpaidPackingFee"}}),o("span",{attrs:{slot:"reference"},slot:"reference"},[e._v(e._s(t.row.unpaidPackingFee||0))])],1)]}}])}),o("el-table-column",{attrs:{align:"center",label:"实付打包费",prop:"receivedPackingFee"},scopedSlots:e._u([{key:"default",fn:function(t){return[o("el-popover",{attrs:{placement:"top","popper-class":"fee-edit-popover",trigger:"click",width:"200"}},[o("el-input-number",{staticStyle:{width:"100%"},attrs:{controls:!1,precision:2,size:"mini"},on:{change:function(o){return e.handleFeeChange(t.row,"receivedPackingFee",o)}},model:{value:t.row.receivedPackingFee,callback:function(o){e.$set(t.row,"receivedPackingFee",o)},expression:"scope.row.receivedPackingFee"}}),o("span",{attrs:{slot:"reference"},slot:"reference"},[e._v(e._s(t.row.receivedPackingFee||0))])],1)]}}])}),o("el-table-column",{attrs:{align:"center",label:"物流代垫费",prop:"logisticsAdvanceFee"},scopedSlots:e._u([{key:"default",fn:function(t){return[o("el-popover",{attrs:{placement:"top","popper-class":"fee-edit-popover",trigger:"click",width:"200"}},[o("el-input-number",{staticStyle:{width:"100%"},attrs:{controls:!1,precision:2,size:"mini"},on:{change:function(o){return e.handleFeeChange(t.row,"logisticsAdvanceFee",o)}},model:{value:t.row.logisticsAdvanceFee,callback:function(o){e.$set(t.row,"logisticsAdvanceFee",o)},expression:"scope.row.logisticsAdvanceFee"}}),o("span",{attrs:{slot:"reference"},slot:"reference"},[e._v(e._s(t.row.logisticsAdvanceFee||0))])],1)]}}])}),o("el-table-column",{attrs:{align:"center",label:"超期租金单价",prop:"overdueRentalUnitPrice"}}),o("el-table-column",{attrs:{align:"center",label:"超期租金",prop:"overdueRentalFee"}}),o("el-table-column",{attrs:{align:"center",label:"唛头",prop:"sqdShippingMark","show-overflow-tooltip":""}}),o("el-table-column",{attrs:{align:"center",label:"总货名",prop:"cargoName","show-overflow-tooltip":""}}),o("el-table-column",{attrs:{align:"center",label:"免堆期",prop:"freeStackPeriod",width:"50"}}),o("el-table-column",{attrs:{align:"center","class-name":"small-padding fixed-width",label:"操作"},scopedSlots:e._u([{key:"default",fn:function(t){return[o("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:outboundrecord:remove"],expression:"['system:outboundrecord:remove']"}],staticStyle:{"margin-right":"-8px"},attrs:{icon:"el-icon-delete",size:"mini",type:"danger"},on:{click:function(o){return e.handleDelete(t.row)}}},[e._v("删除 ")])]}}])})],1),o("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total>0"}],attrs:{limit:e.queryParams.pageSize,page:e.queryParams.pageNum,total:e.total},on:{"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},pagination:e.getList}})],1)],1),o("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"},{name:"dialogDragWidth",rawName:"v-dialogDragWidth"}],attrs:{"close-on-click-modal":!1,"modal-append-to-body":!1,title:"出库单",visible:e.openOutbound,"append-to-body":"",width:"80%"},on:{"update:visible":function(t){e.openOutbound=t}}},[o("el-form",{ref:"outboundForm",staticClass:"edit",attrs:{model:e.outboundForm,rules:e.rules,"label-width":"80px"}},[o("el-row",{attrs:{gutter:10}},[o("el-row",{attrs:{gutter:10}},[o("el-col",[o("el-col",{attrs:{span:6}},[o("el-form-item",{attrs:{label:"出仓单号",prop:"outboundNo"}},[o("el-input",{staticClass:"disable-form",attrs:{disabled:"",placeholder:"出仓单号"},model:{value:e.outboundForm.outboundNo,callback:function(t){e.$set(e.outboundForm,"outboundNo",t)},expression:"outboundForm.outboundNo"}})],1)],1),o("el-col",{attrs:{span:6}},[o("el-form-item",{attrs:{label:"客户单号",prop:"outboundNo"}},[o("el-input",{attrs:{placeholder:"客户单号"},model:{value:e.outboundForm.customerOrderNo,callback:function(t){e.$set(e.outboundForm,"customerOrderNo",t)},expression:"outboundForm.customerOrderNo"}})],1)],1),o("el-col",{attrs:{span:6}},[o("el-form-item",{attrs:{label:"客户代码",prop:"outboundNo"}},[o("tree-select",{attrs:{flat:!1,multiple:!1,pass:e.outboundForm.clientCode,placeholder:"客户代码",type:"warehouseClient"},on:{return:function(t){e.outboundForm.clientCode=t},returnData:function(t){return e.outboundClient(t)}}})],1)],1),o("el-col",{attrs:{span:6}},[o("el-form-item",{attrs:{label:"客户名称",prop:"outboundNo"}},[o("el-input",{staticClass:"disable-form",attrs:{disabled:"",placeholder:"客户名称"},model:{value:e.outboundForm.clientName,callback:function(t){e.$set(e.outboundForm,"clientName",t)},expression:"outboundForm.clientName"}})],1)],1)],1)],1),o("el-row",{attrs:{gutter:10}},[o("el-col",[o("el-col",{attrs:{span:6}},[o("el-form-item",{attrs:{label:"计划出仓",prop:"inboundDate"}},[o("el-date-picker",{staticStyle:{width:"100%"},attrs:{clearable:"",placeholder:"计划出仓日期",type:"date","value-format":"yyyy-MM-dd"},model:{value:e.outboundForm.plannedOutboundDate,callback:function(t){e.$set(e.outboundForm,"plannedOutboundDate",t)},expression:"outboundForm.plannedOutboundDate"}})],1)],1),o("el-col",{attrs:{span:6}},[o("el-form-item",{attrs:{label:"出仓方式",prop:"outboundType"}},[o("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"出仓方式"},model:{value:e.outboundForm.outboundType,callback:function(t){e.$set(e.outboundForm,"outboundType",t)},expression:"outboundForm.outboundType"}},[o("el-option",{attrs:{label:"整柜",value:"整柜"}}),o("el-option",{attrs:{label:"散货",value:"散货"}}),o("el-option",{attrs:{label:"快递",value:"快递"}}),o("el-option",{attrs:{label:"其他",value:"其他"}})],1)],1)],1),o("el-col",{attrs:{span:6}},[o("el-form-item",{attrs:{label:"柜型",prop:"containerType"}},[o("el-select",{staticStyle:{width:"100%"},on:{change:e.selectContainerType},model:{value:e.outboundForm.containerType,callback:function(t){e.$set(e.outboundForm,"containerType",t)},expression:"outboundForm.containerType"}},[o("el-option",{attrs:{label:"20GP",value:"20GP"}}),o("el-option",{attrs:{label:"20OT",value:"20OT"}}),o("el-option",{attrs:{label:"20FR",value:"20FR"}}),o("el-option",{attrs:{label:"TANK",value:"TANK"}}),o("el-option",{attrs:{label:"40GP",value:"40GP"}}),o("el-option",{attrs:{label:"40HQ",value:"40HQ"}}),o("el-option",{attrs:{label:"40NOR",value:"40NOR"}}),o("el-option",{attrs:{label:"40OT",value:"40OT"}}),o("el-option",{attrs:{label:"40FR",value:"40FR"}}),o("el-option",{attrs:{label:"40RH",value:"40RH"}}),o("el-option",{attrs:{label:"45HQ",value:"45HQ"}})],1)],1)],1),o("el-col",{attrs:{span:6}},[o("el-form-item",{attrs:{label:"货物类型",prop:"cargoType"}},[o("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择货物类型"},model:{value:e.form.cargoType,callback:function(t){e.$set(e.form,"cargoType",t)},expression:"form.cargoType"}},[o("el-option",{attrs:{label:"普货",value:"普货"}}),o("el-option",{attrs:{label:"大件",value:"大件"}}),o("el-option",{attrs:{label:"鲜活",value:"鲜活"}}),o("el-option",{attrs:{label:"危品",value:"危品"}}),o("el-option",{attrs:{label:"冷冻",value:"冷冻"}}),o("el-option",{attrs:{label:"标记",value:"标记"}})],1)],1)],1)],1)],1),o("el-row",{attrs:{gutter:10}},[o("el-col",[o("el-col",{attrs:{span:6}},[o("el-form-item",{attrs:{label:"柜号",prop:"containerNo"}},[o("el-input",{attrs:{placeholder:"柜号"},model:{value:e.outboundForm.containerNo,callback:function(t){e.$set(e.outboundForm,"containerNo",t)},expression:"outboundForm.containerNo"}})],1)],1),o("el-col",{attrs:{span:6}},[o("el-form-item",{attrs:{label:"封号",prop:"sealNo"}},[o("el-input",{attrs:{placeholder:"封号"},model:{value:e.outboundForm.sealNo,callback:function(t){e.$set(e.outboundForm,"sealNo",t)},expression:"outboundForm.sealNo"}})],1)],1),o("el-col",{attrs:{span:6}},[o("el-form-item",{attrs:{label:"车牌",prop:"plateNumber"}},[o("el-input",{attrs:{placeholder:"车牌"},model:{value:e.outboundForm.plateNumber,callback:function(t){e.$set(e.outboundForm,"plateNumber",t)},expression:"outboundForm.plateNumber"}})],1)],1),o("el-col",{attrs:{span:6}},[o("el-form-item",{attrs:{label:"司机电话",prop:"driverPhone"}},[o("el-input",{attrs:{placeholder:"司机电话"},model:{value:e.outboundForm.driverPhone,callback:function(t){e.$set(e.outboundForm,"driverPhone",t)},expression:"outboundForm.driverPhone"}})],1)],1)],1)],1),o("el-row",{attrs:{gutter:10}},[o("el-col",[o("el-col",{attrs:{span:6}},[o("el-form-item",{attrs:{label:"仓库报价",prop:"warehouseQuote"}},[o("el-input",{staticClass:"number",attrs:{placeholder:"仓库报价"},model:{value:e.outboundForm.warehouseQuote,callback:function(t){e.$set(e.outboundForm,"warehouseQuote",t)},expression:"outboundForm.warehouseQuote"}})],1)],1),o("el-col",{attrs:{span:6}},[o("el-form-item",{attrs:{label:"仓管代收",prop:"outboundNotes"}},[o("el-input",{staticClass:"number",attrs:{placeholder:"仓管代收"},model:{value:e.outboundForm.warehouseCollection,callback:function(t){e.$set(e.outboundForm,"warehouseCollection",t)},expression:"outboundForm.warehouseCollection"}})],1)],1),o("el-col",{attrs:{span:6}},[o("el-form-item",{attrs:{label:"工人装柜费",prop:"workerLoadingFee"}},[o("el-input",{staticClass:"number",attrs:{placeholder:"工人装柜费"},model:{value:e.outboundForm.workerLoadingFee,callback:function(t){e.$set(e.outboundForm,"workerLoadingFee",t)},expression:"outboundForm.workerLoadingFee"}})],1)],1),o("el-col",{attrs:{span:6}},[o("el-form-item",{attrs:{label:"仓管代付",prop:"outboundNotes"}},[o("el-input",{staticClass:"number",attrs:{placeholder:"仓管代收"},model:{value:e.outboundForm.warehousePay,callback:function(t){e.$set(e.outboundForm,"warehousePay",t)},expression:"outboundForm.warehousePay"}})],1)],1)],1)],1),o("el-row",{attrs:{gutter:10}},[o("el-col",[o("el-col",{attrs:{span:12}},[o("el-form-item",{attrs:{label:"操作要求",prop:"operationRequirement"}},[o("el-input",{attrs:{autosize:{minRows:4},maxlength:"250",placeholder:"内容","show-word-limit":"",type:"textarea"},model:{value:e.outboundForm.operationRequirement,callback:function(t){e.$set(e.outboundForm,"operationRequirement",t)},expression:"outboundForm.operationRequirement"}})],1)],1),o("el-col",{attrs:{span:12}},[o("el-form-item",{attrs:{label:"仓管指示",prop:"outboundNote"}},[o("el-input",{attrs:{autosize:{minRows:4},maxlength:"250",placeholder:"内容","show-word-limit":"",type:"textarea"},model:{value:e.outboundForm.outboundNote,callback:function(t){e.$set(e.outboundForm,"outboundNote",t)},expression:"outboundForm.outboundNote"}})],1)],1)],1)],1),o("el-row",{attrs:{gutter:10}},[o("el-col",[o("el-col",{attrs:{span:6}},[o("el-form-item",{attrs:{label:"操作员",prop:"operator"}},[o("el-input",{staticClass:"disable-form",attrs:{disabled:"",placeholder:"操作员"},model:{value:e.outboundForm.operator,callback:function(t){e.$set(e.outboundForm,"operator",t)},expression:"outboundForm.operator"}})],1)],1),o("el-col",{attrs:{span:6}},[o("el-form-item",{attrs:{label:"下单日期",prop:"orderDate"}},[o("el-date-picker",{staticClass:"disable-form",staticStyle:{width:"100%"},attrs:{clearable:"",disabled:"",placeholder:"出仓日期",type:"date","value-format":"yyyy-MM-dd"},model:{value:e.outboundForm.orderDate,callback:function(t){e.$set(e.outboundForm,"orderDate",t)},expression:"outboundForm.orderDate"}})],1)],1),o("el-col",{attrs:{span:6}},[o("el-form-item",{attrs:{label:"出仓经手人",prop:"outboundHandler"}},[o("el-input",{staticClass:"disable-form",attrs:{disabled:"",placeholder:"出仓经手人"},model:{value:e.outboundForm.outboundHandler,callback:function(t){e.$set(e.outboundForm,"outboundHandler",t)},expression:"outboundForm.outboundHandler"}})],1)],1),o("el-col",{attrs:{span:6}},[o("el-row",[o("el-col",{attrs:{span:12}},[o("el-button",{attrs:{disabled:null===e.outboundForm.clientCode,type:"primary"},on:{click:e.warehouseConfirm}},[e._v(" "+e._s("仓管确认")+" ")])],1),o("el-col",{attrs:{span:12}},[o("el-button",{attrs:{disabled:null===e.outboundForm.clientCode,type:"primary"},on:{click:e.loadPreOutboundInventoryList}},[e._v(" "+e._s("加载待出库")+" ")])],1)],1)],1)],1)],1)],1),o("el-row",{attrs:{gutter:10}},[o("el-col",[o("el-col",[o("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.preOutboundInventoryListLoading,expression:"preOutboundInventoryListLoading"}],ref:"table",staticStyle:{width:"100%"},attrs:{data:e.outboundForm.rsInventoryList,load:e.loadChildInventory,"summary-method":e.getSummaries,"tree-props":{children:"children",hasChildren:"hasChildren"},lazy:"","max-height":"300","row-key":"inventoryId","show-summary":""},on:{"selection-change":e.handleOutboundSelectionChange}},[o("el-table-column",{attrs:{align:"center",label:"入仓流水号",prop:"inboundSerialNo"}}),o("el-table-column",{attrs:{align:"center",label:"货物明细",width:"50"},scopedSlots:e._u([{key:"default",fn:function(t){return[o("el-popover",{attrs:{trigger:"click",width:"800"}},[o("el-table",{attrs:{data:t.row.rsCargoDetailsList},on:{"selection-change":function(o){return e.handleOutboundCargoDetailSelectionChange(o,t.row)}}},[o("el-table-column",{attrs:{align:"center",type:"selection",width:"28"}}),o("el-table-column",{attrs:{label:"唛头",prop:"shippingMark",width:"150"}}),o("el-table-column",{attrs:{label:"货名",prop:"itemName",width:"150"}}),o("el-table-column",{attrs:{label:"箱数",prop:"boxCount"}}),o("el-table-column",{attrs:{label:"包装类型",prop:"packageType"}}),o("el-table-column",{attrs:{label:"单件毛重",prop:"unitGrossWeight"}}),o("el-table-column",{attrs:{label:"单件长",prop:"unitLength"}}),o("el-table-column",{attrs:{label:"单件宽",prop:"unitWidth"}}),o("el-table-column",{attrs:{label:"单件高",prop:"unitHeight"}}),o("el-table-column",{attrs:{label:"单件体积",prop:"unitVolume"}}),o("el-table-column",{attrs:{label:"破损标志",prop:"damageStatus"}})],1),o("el-button",{staticStyle:{margin:"0",padding:"5px"},attrs:{slot:"reference"},slot:"reference"},[e._v("查看")])],1)]}}])}),o("el-table-column",{attrs:{align:"center",label:"入仓日期",prop:"actualInboundTime",width:"80"},scopedSlots:e._u([{key:"default",fn:function(t){return[o("span",[e._v(e._s(e.parseTime(t.row.actualInboundTime,"{y}-{m}-{d}")))])]}}])}),o("el-table-column",{attrs:{align:"center",label:"货代单号",prop:"forwarderNo"}}),o("el-table-column",{attrs:{align:"center",label:"客户代码",prop:"clientCode"}}),o("el-table-column",{attrs:{align:"center",label:"司机信息",prop:"driverInfo"}}),o("el-table-column",{attrs:{align:"center",label:"箱数",prop:"totalBoxes",width:"50"}}),o("el-table-column",{attrs:{align:"center",label:"毛重",prop:"totalGrossWeight",width:"80"}}),o("el-table-column",{attrs:{align:"center",label:"体积",prop:"totalVolume",width:"50"}}),o("el-table-column",{attrs:{align:"center",label:"已收供应商",prop:"receivedSupplier"},scopedSlots:e._u([{key:"default",fn:function(t){return[o("el-popover",{attrs:{placement:"top","popper-class":"fee-edit-popover",trigger:"click",width:"200"}},[o("el-input-number",{staticStyle:{width:"100%"},attrs:{controls:!1,precision:2,size:"mini"},on:{change:function(o){return e.handleFeeChange(t.row,"receivedSupplier",o)}},model:{value:t.row.receivedSupplier,callback:function(o){e.$set(t.row,"receivedSupplier",o)},expression:"scope.row.receivedSupplier"}}),o("span",{attrs:{slot:"reference"},slot:"reference"},[e._v(e._s(t.row.receivedSupplier||0))])],1)]}}])}),o("el-table-column",{attrs:{align:"center",label:"入仓费",prop:"inboundFee"},scopedSlots:e._u([{key:"default",fn:function(t){return[o("el-popover",{attrs:{placement:"top","popper-class":"fee-edit-popover",trigger:"click",width:"200"}},[o("el-input-number",{staticStyle:{width:"100%"},attrs:{controls:!1,precision:2,size:"mini"},on:{change:function(o){return e.handleFeeChange(t.row,"inboundFee",o)}},model:{value:t.row.inboundFee,callback:function(o){e.$set(t.row,"inboundFee",o)},expression:"scope.row.inboundFee"}}),o("span",{attrs:{slot:"reference"},slot:"reference"},[e._v(e._s(t.row.inboundFee||0))])],1)]}}])}),o("el-table-column",{attrs:{align:"center",label:"已收入仓费",prop:"receivedStorageFee"},scopedSlots:e._u([{key:"default",fn:function(t){return[o("el-popover",{attrs:{placement:"top","popper-class":"fee-edit-popover",trigger:"click",width:"200"}},[o("el-input-number",{staticStyle:{width:"100%"},attrs:{controls:!1,precision:2,size:"mini"},on:{change:function(o){return e.handleFeeChange(t.row,"receivedStorageFee",o)}},model:{value:t.row.receivedStorageFee,callback:function(o){e.$set(t.row,"receivedStorageFee",o)},expression:"scope.row.receivedStorageFee"}}),o("span",{attrs:{slot:"reference"},slot:"reference"},[e._v(e._s(t.row.receivedStorageFee||0))])],1)]}}])}),o("el-table-column",{attrs:{align:"center",label:"补收入仓费",prop:"additionalStorageFee"},scopedSlots:e._u([{key:"default",fn:function(t){return[o("el-popover",{attrs:{placement:"top","popper-class":"fee-edit-popover",trigger:"click",width:"200"}},[o("el-input-number",{staticStyle:{width:"100%"},attrs:{controls:!1,precision:2,size:"mini"},on:{change:function(o){return e.handleFeeChange(t.row,"additionalStorageFee",o)}},model:{value:t.row.additionalStorageFee,callback:function(o){e.$set(t.row,"additionalStorageFee",o)},expression:"scope.row.additionalStorageFee"}}),o("span",{attrs:{slot:"reference"},slot:"reference"},[e._v(e._s(t.row.additionalStorageFee||0))])],1)]}}])}),o("el-table-column",{attrs:{align:"center",label:"未收卸货费",prop:"unpaidUnloadingFee"},scopedSlots:e._u([{key:"default",fn:function(t){return[o("el-popover",{attrs:{placement:"top","popper-class":"fee-edit-popover",trigger:"click",width:"200"}},[o("el-input-number",{staticStyle:{width:"100%"},attrs:{controls:!1,precision:2,size:"mini"},on:{change:function(o){return e.handleFeeChange(t.row,"unpaidUnloadingFee",o)}},model:{value:t.row.unpaidUnloadingFee,callback:function(o){e.$set(t.row,"unpaidUnloadingFee",o)},expression:"scope.row.unpaidUnloadingFee"}}),o("span",{attrs:{slot:"reference"},slot:"reference"},[e._v(e._s(t.row.unpaidUnloadingFee||0))])],1)]}}])}),o("el-table-column",{attrs:{align:"center",label:"实付卸货费",prop:"receivedUnloadingFee"},scopedSlots:e._u([{key:"default",fn:function(t){return[o("el-popover",{attrs:{placement:"top","popper-class":"fee-edit-popover",trigger:"click",width:"200"}},[o("el-input-number",{staticStyle:{width:"100%"},attrs:{controls:!1,precision:2,size:"mini"},on:{change:function(o){return e.handleFeeChange(t.row,"receivedUnloadingFee",o)}},model:{value:t.row.receivedUnloadingFee,callback:function(o){e.$set(t.row,"receivedUnloadingFee",o)},expression:"scope.row.receivedUnloadingFee"}}),o("span",{attrs:{slot:"reference"},slot:"reference"},[e._v(e._s(t.row.receivedUnloadingFee||0))])],1)]}}])}),o("el-table-column",{attrs:{align:"center",label:"未收打包费",prop:"unpaidPackingFee"},scopedSlots:e._u([{key:"default",fn:function(t){return[o("el-popover",{attrs:{placement:"top","popper-class":"fee-edit-popover",trigger:"click",width:"200"}},[o("el-input-number",{staticStyle:{width:"100%"},attrs:{controls:!1,precision:2,size:"mini"},on:{change:function(o){return e.handleFeeChange(t.row,"unpaidPackingFee",o)}},model:{value:t.row.unpaidPackingFee,callback:function(o){e.$set(t.row,"unpaidPackingFee",o)},expression:"scope.row.unpaidPackingFee"}}),o("span",{attrs:{slot:"reference"},slot:"reference"},[e._v(e._s(t.row.unpaidPackagingFee||0))]),o("span",{attrs:{slot:"reference"},slot:"reference"},[e._v(e._s(t.row.unpaidPackingFee||0))])],1)]}}])}),o("el-table-column",{attrs:{align:"center",label:"实付打包费",prop:"receivedPackingFee"},scopedSlots:e._u([{key:"default",fn:function(t){return[o("el-popover",{attrs:{placement:"top","popper-class":"fee-edit-popover",trigger:"click",width:"200"}},[o("el-input-number",{staticStyle:{width:"100%"},attrs:{controls:!1,precision:2,size:"mini"},on:{change:function(o){return e.handleFeeChange(t.row,"receivedPackingFee",o)}},model:{value:t.row.receivedPackingFee,callback:function(o){e.$set(t.row,"receivedPackingFee",o)},expression:"scope.row.receivedPackingFee"}}),o("span",{attrs:{slot:"reference"},slot:"reference"},[e._v(e._s(t.row.receivedPackingFee||0))])],1)]}}])}),o("el-table-column",{attrs:{align:"center",label:"物流代垫费",prop:"logisticsAdvanceFee"},scopedSlots:e._u([{key:"default",fn:function(t){return[o("el-popover",{attrs:{placement:"top","popper-class":"fee-edit-popover",trigger:"click",width:"200"}},[o("el-input-number",{staticStyle:{width:"100%"},attrs:{controls:!1,precision:2,size:"mini"},on:{change:function(o){return e.handleFeeChange(t.row,"logisticsAdvanceFee",o)}},model:{value:t.row.logisticsAdvanceFee,callback:function(o){e.$set(t.row,"logisticsAdvanceFee",o)},expression:"scope.row.logisticsAdvanceFee"}}),o("span",{attrs:{slot:"reference"},slot:"reference"},[e._v(e._s(t.row.logisticsAdvanceFee||0))])],1)]}}])}),o("el-table-column",{attrs:{align:"center",label:"超期租金",prop:"overdueRentalFee"},scopedSlots:e._u([{key:"default",fn:function(t){return[o("el-popover",{attrs:{placement:"top","popper-class":"fee-edit-popover",trigger:"click",width:"200"}},[o("el-input-number",{staticStyle:{width:"100%"},attrs:{controls:!1,precision:2,size:"mini"},on:{change:function(o){return e.handleFeeChange(t.row,"overdueRentalFee",o)}},model:{value:t.row.overdueRentalFee,callback:function(o){e.$set(t.row,"overdueRentalFee",o)},expression:"scope.row.overdueRentalFee"}}),o("span",{attrs:{slot:"reference"},slot:"reference"},[e._v(e._s(t.row.overdueRentalFee||0))])],1)]}}])})],1)],1)],1)],1),o("el-row",[o("el-col",{attrs:{span:4}},[o("span",[e._v("未收客户："+e._s(e.outboundForm.unreceivedFromCustomer))])]),o("el-col",{attrs:{span:4}},[o("span",[e._v("已收客户："+e._s(e.outboundForm.receivedFromCustomer))])]),o("el-col",{attrs:{span:4}},[o("span",[e._v("应收客户余额："+e._s(e.outboundForm.customerReceivableBalance))])]),o("el-col",{attrs:{span:4}},[o("span",[e._v("应付工人："+e._s(e.outboundForm.payableToWorker))])])],1),o("el-row",[o("el-col",{attrs:{span:4}},[o("span",[e._v("本票销售额："+e._s(e.outboundForm.promissoryNoteSales))])]),o("el-col",{attrs:{span:4}},[o("span",[e._v("本票成本："+e._s(e.outboundForm.promissoryNoteCost))])]),o("el-col",{attrs:{span:4}},[o("span",[e._v("本票结余："+e._s(e.outboundForm.promissoryNoteGrossProfit))])]),o("el-col",{attrs:{span:4}},[o("span",[e._v("已收供应商总额："+e._s(e.outboundForm.receivedFromSupplier))])])],1)],1),o("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[o("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("保存")]),o("el-button",{on:{click:e.generateOutboundBill}},[e._v("生成出仓单")])],1)],1)],1)},r=[],a=o("5530"),l=(o("caad"),o("2532"),o("ac1f"),o("841c"),o("c740"),o("d3b7"),o("159b"),o("14d9"),o("3ca3"),o("ddb0"),o("2b3d"),o("9861"),o("d81d"),o("a9e3"),o("13d5"),o("b64b"),o("82ad")),u=o("fba1"),i=o("5fb3"),s=o("72f9"),c=o.n(s),d={name:"Outboundrecord",data:function(){return{selectedCargoDetail:[],search:null,showLeft:0,showRight:24,loading:!0,selectOutboundList:[],ids:[],single:!0,multiple:!0,showSearch:!1,total:0,outboundrecordList:[],title:"",open:!1,queryParams:{pageNum:1,pageSize:20,isRentSettlement:0,outboundNo:null,clientCode:null,clientName:null,operatorId:null,containerType:null,containerNo:null,sealNo:null,outboundDate:null,warehouseQuote:null,workerLoadingFee:null,warehouseCollection:null,collectionNotes:null,totalBoxes:null,totalGrossWeight:null,totalVolume:null,totalRows:null,receivedStorageFee:null,unpaidUnloadingFee:null,unpaidPackingFee:null,logisticsAdvanceFee:null,rentalBalanceFee:null,overdueRent:null,freeStackDays:null,overdueUnitPrice:null},form:{},outboundType:null,preOutboundInventoryListLoading:!1,rules:{},outboundForm:{},openOutbound:!1,preOutboundInventoryList:[]}},watch:{showSearch:function(e){!0===e?(this.showRight=21,this.showLeft=3):(this.showRight=24,this.showLeft=0)}},created:function(){this.getList()},methods:{isRowSelected:function(e){return this.selectedCargoDetail.includes(e)},handleSearchEnter:function(){var e=this;if(this.search){var t=this.preOutboundInventoryList.findIndex((function(t){var o=String(t.inboundSerialNo||""),n=String(e.search);return o.includes(n)}));if(t>-1){var o=this.$refs.table;this.$nextTick((function(){var t=o.$el.querySelector(".el-table__body-wrapper"),n=t.querySelectorAll(".el-table__row"),r=-1;if(n.forEach((function(t,o){var n=t.textContent;n.includes(e.search)&&(r=o)})),r>-1){var a=n[r],l=a.offsetTop;t.scrollTo({top:l-t.clientHeight/2,behavior:"smooth"}),a.classList.add("highlight-row"),setTimeout((function(){a.classList.remove("highlight-row")}),2e3)}}))}else this.$message.warning("未找到匹配的记录")}},warehouseConfirm:function(){},loadChildInventory:function(e,t,o){var n=this;Object(i["h"])({packageTo:e.inventoryId}).then((function(t){var r=t.rows;o(r),e.children=r,n.ids.includes(e.inventoryId)&&setTimeout((function(){r.forEach((function(e){n.ids.includes(e.inventoryId)||(n.ids.push(e.inventoryId),n.selectOutboundList.push(e)),n.$refs.table.toggleRowSelection(e,!0)}))}),50)}))},selectContainerType:function(e){switch(e){case"20GP":this.outboundForm.warehouseQuote=this.clientRow.rate20gp;break;case"40HQ":this.outboundForm.warehouseQuote=this.clientRow.rate40hq;break}},countSummary:function(){this.outboundForm.unreceivedFromCustomer=c()(this.outboundForm.warehouseQuote).add(this.outboundForm.additionalStorageFee).add(this.outboundForm.unpaidUnloadingFee).add(this.outboundForm.unpaidPackingFee).add(this.outboundForm.logisticsAdvanceFee).add(this.outboundForm.overdueRentalFee).add(this.outboundForm.difficultyWorkFee).value,this.outboundForm.receivedFromCustomer=c()(this.outboundForm.warehouseCollection).value,this.outboundForm.customerReceivableBalance=c()(this.outboundForm.unreceivedFromCustomer).subtract(this.outboundForm.receivedFromCustomer).value,this.outboundForm.payableToWorker=c()(this.outboundForm.receivedUnloadingFee).add(this.outboundForm.receivedPackingFee).add(this.outboundForm.workerLoadingFee).value,this.outboundForm.receivedFromSupplier=c()(this.outboundForm.receivedSupplier).add(this.outboundForm.receivedStorageFee).value,this.outboundForm.promissoryNoteSales=c()(this.outboundForm.unreceivedFromCustomer).add(this.outboundForm.receivedFromSupplier).value,this.outboundForm.promissoryNoteCost=c()(this.outboundForm.payableToWorker).add(this.outboundForm.logisticsAdvanceFee).add(this.outboundForm.warehouseAdvanceOtherFee).value,this.outboundForm.promissoryNoteGrossProfit=c()(this.outboundForm.promissoryNoteSales).subtract(this.outboundForm.promissoryNoteCost).value},generateOutboundBill:function(){var e=this;Object(l["d"])(this.outboundForm).then((function(t){var o=t,n=e.outboundForm.clientCode+"-"+e.outboundForm.operator+"-"+e.outboundForm.outboundNo+".xlsx",r=new Blob([o],{type:"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"}),a=document.createElement("a"),l=window.URL.createObjectURL(r);a.href=l,a.download=n,document.body.appendChild(a),a.click(),document.body.removeChild(a),window.URL.revokeObjectURL(l)})).catch((function(e){console.error("文件下载失败:",e)}))},findOutboundRecord:function(e){var t=this;this.outboundReset(),Object(l["f"])(e.outboundRecordId).then((function(e){t.outboundForm=e.data,t.outboundForm.rsInventoryList=t.outboundForm.rsInventoryList?t.outboundForm.rsInventoryList.map((function(e){if(0===e.includesInboundFee){var t=Number(e.receivedStorageFee||0),o=Number(e.inboundFee||0),n=c()(o).subtract(t).value;e.additionalStorageFee=n>0?n:0}else e.additionalStorageFee=0;return e})):[],t.openOutbound=!0}))},outboundConfirm:function(e){var t=this;this.selectOutboundList.map((function(e){e.partialOutboundFlag=Number(e.partialOutboundFlag)})),Object(l["a"])(this.outboundForm).then((function(o){if(o.data){var n=o.data,r=t.selectOutboundList.map((function(o){if("1"!==o.preOutboundFlag)return 0===e&&(o.preOutboundFlag="1"),o.outboundRecordId=n,o.rsCargoDetailsList&&o.rsCargoDetailsList.map((function(t){return t.outboundRecordId=n,0===e&&(t.preOutboundFlag="1"),t})),o;t.$message.warning("勾选记录中有以预出库记录,请重新勾选")}));0===e?Object(i["l"])(r).then((function(e){t.getList(),t.$message.success("预出仓成功"),t.openOutbound=!1})):Object(i["j"])(r).then((function(e){t.getList(),t.$message.success("出仓成功"),t.openOutbound=!1}))}}))},loadPreOutboundInventoryList:function(){var e=this;this.loading=!0;var t={};t.sqdPlannedOutboundDate=this.outboundForm.plannedOutboundDate,t.clientCode=this.outboundForm.clientCode,Object(i["h"])(t).then((function(t){console.log(t),e.preOutboundInventoryList=t.rows,e.total=t.total,e.loading=!1}))},handleOutbound:function(e,t){1===t&&(this.outboundReset(),this.outboundForm=e),this.outboundType=t,this.openOutbound=!0},parseTime:u["f"],handleOutboundCargoDetailSelectionChange:function(e,t){t.rsCargoDetailsList=e,this.selectedCargoDetail=e},getSummaries:function(e){var t=this,o=e.columns,n=e.data,r=[],a=["receivedSupplier","totalBoxes","unpaidInboundFee","totalGrossWeight","totalVolume","receivedStorageFee","unpaidUnloadingFee","logisticsAdvanceFee","rentalBalanceFee","overdueRentalFee","additionalStorageFee","unpaidUnloadingFee","unpaidPackingFee","receivedUnloadingFee","receivedPackingFee","inboundFee"],l={};return o.forEach((function(e,t){if(0!==t){var o=n.map((function(t){return Number(t[e.property])}));if(a.includes(e.property)&&!o.every((function(e){return isNaN(e)}))){var u=o.reduce((function(e,t){var o=Number(t);return isNaN(o)?e:c()(e).add(t).value}),0);r[t]=u,l[e.property]=u}else r[t]=" "}else r[t]="汇总"})),Object.keys(l).forEach((function(e){t.outboundForm&&(t.outboundForm[e]=l[e])})),r},handleOutboundSelectionChange:function(e){this.selectOutboundList=e},outboundClient:function(e){this.outboundForm.warehouseQuote=e.rateLcl,this.outboundForm.freeStackDays=e.freeStackPeriod,this.outboundForm.overdueRent=e.overdueRent,this.outboundForm.clientName=e.clientName,this.$forceUpdate()},getList:function(){var e=this;this.loading=!0,Object(l["i"])(this.queryParams).then((function(t){e.outboundrecordList=t.rows,e.total=t.total,e.loading=!1}))},cancel:function(){this.open=!1,this.reset()},outboundReset:function(){this.outboundForm={outboundRecordId:null,outboundNo:null,clientCode:null,clientName:null,operatorId:null,containerType:null,containerNo:null,sealNo:null,outboundDate:null,warehouseQuote:null,workerLoadingFee:null,warehouseCollection:null,collectionNotes:null,totalBoxes:null,totalGrossWeight:null,totalVolume:null,totalRows:null,receivedStorageFee:null,unpaidUnloadingFee:null,unpaidPackingFee:null,logisticsAdvanceFee:null,rentalBalanceFee:null,overdueRent:null,freeStackDays:null,overdueUnitPrice:null},this.preOutboundInventoryList=[],this.resetForm("outboundForm")},reset:function(){this.form={outboundRecordId:null,outboundNo:null,clientCode:null,clientName:null,operatorId:null,containerType:null,containerNo:null,sealNo:null,outboundDate:null,warehouseQuote:null,workerLoadingFee:null,warehouseCollection:null,collectionNotes:null,totalBoxes:null,totalGrossWeight:null,totalVolume:null,totalRows:null,receivedStorageFee:null,unpaidUnloadingFee:null,unpaidPackingFee:null,logisticsAdvanceFee:null,rentalBalanceFee:null,overdueRent:null,freeStackDays:null,overdueUnitPrice:null},this.resetForm("form")},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.resetForm("queryForm"),this.handleQuery()},handleStatusChange:function(e){var t=this,o="0"===e.status?"启用":"停用";this.$modal.confirm('确认要"'+o+"吗？").then((function(){return Object(l["b"])(e.outboundRecordId,e.status)})).then((function(){t.$modal.msgSuccess(o+"成功")})).catch((function(){e.status="0"===e.status?"1":"0"}))},handleSelectionChange:function(e){this.ids=e.map((function(e){return e.outboundRecordId})),this.single=1!==e.length,this.multiple=!e.length},handleAdd:function(){this.outboundReset(),this.open=!0,this.title="添加出仓记录"},handleUpdate:function(e){var t=this;this.outboundReset();var o=e.outboundRecordId||this.ids;Object(l["e"])(o).then((function(e){t.form=e.data,t.open=!0,t.title="修改出仓记录"}))},submitForm:function(){var e=this;this.$refs["outboundForm"].validate((function(t){t&&null!=e.outboundForm.outboundRecordId&&Object(l["k"])(e.outboundForm).then((function(t){e.$modal.msgSuccess("修改成功"),e.open=!1,e.getList()}))}))},handleDelete:function(e){var t=this,o=e.outboundRecordId||this.ids;this.$modal.confirm('是否确认删除出仓记录编号为"'+o+'"的数据项？').then((function(){return Object(l["c"])(o)})).then((function(){t.getList(),t.$modal.msgSuccess("删除成功")})).catch((function(){}))},handleExport:function(){this.download("system/outboundrecord/export",Object(a["a"])({},this.queryParams),"outboundrecord_".concat((new Date).getTime(),".xlsx"))},handleFeeChange:function(e,t,o){if(o=Number(o)||0,this.$set(e,t,o),"receivedStorageFee"===t&&0===e.includesInboundFee){var n=Number(e.inboundFee||0),r=c()(n).subtract(o).value;this.$set(e,"additionalStorageFee",r>0?r:0)}if("inboundFee"===t){var l=c()(o).subtract(e.receivedStorageFee).value;this.$set(e,"additionalStorageFee",l>0?l:0)}this.$forceUpdate();var u=this.outboundForm.rsInventoryList.findIndex((function(t){return t===e}));-1!==u&&this.$set(this.outboundForm.rsInventoryList,u,Object(a["a"])({},e)),this.updateTableData()},updateTableData:function(){var e=this;this.$forceUpdate(),this.$nextTick((function(){e.outboundForm.rsInventoryList&&e.outboundForm.rsInventoryList.length>0&&e.getSummaries({columns:e.$refs.outboundInventoryTable?e.$refs.outboundInventoryTable.columns:[],data:e.outboundForm.rsInventoryList}),e.countSummary()}))}}},p=d,m=(o("151c"),o("2877")),b=Object(m["a"])(p,n,r,!1,null,"4af16491",null);t["default"]=b.exports},"72f9":function(e,t,o){(function(t,o){e.exports=o()})(0,(function(){function e(a,l){if(!(this instanceof e))return new e(a,l);l=Object.assign({},o,l);var u=Math.pow(10,l.precision);this.intValue=a=t(a,l),this.value=a/u,l.increment=l.increment||1/u,l.groups=l.useVedic?r:n,this.s=l,this.p=u}function t(t,o){var n=!(2<arguments.length&&void 0!==arguments[2])||arguments[2],r=o.decimal,a=o.errorOnInvalid,l=o.fromCents,u=Math.pow(10,o.precision),i=t instanceof e;if(i&&l)return t.intValue;if("number"===typeof t||i)r=i?t.value:t;else if("string"===typeof t)a=new RegExp("[^-\\d"+r+"]","g"),r=new RegExp("\\"+r,"g"),r=(r=t.replace(/\((.*)\)/,"-$1").replace(a,"").replace(r,"."))||0;else{if(a)throw Error("Invalid Input");r=0}return l||(r=(r*u).toFixed(4)),n?Math.round(r):r}var o={symbol:"$",separator:",",decimal:".",errorOnInvalid:!1,precision:2,pattern:"!#",negativePattern:"-!#",format:function(e,t){var o=t.pattern,n=t.negativePattern,r=t.symbol,a=t.separator,l=t.decimal;t=t.groups;var u=(""+e).replace(/^-/,"").split("."),i=u[0];return u=u[1],(0<=e.value?o:n).replace("!",r).replace("#",i.replace(t,"$1"+a)+(u?l+u:""))},fromCents:!1},n=/(\d)(?=(\d{3})+\b)/g,r=/(\d)(?=(\d\d)+\d\b)/g;return e.prototype={add:function(o){var n=this.s,r=this.p;return e((this.intValue+t(o,n))/(n.fromCents?1:r),n)},subtract:function(o){var n=this.s,r=this.p;return e((this.intValue-t(o,n))/(n.fromCents?1:r),n)},multiply:function(t){var o=this.s;return e(this.intValue*t/(o.fromCents?1:Math.pow(10,o.precision)),o)},divide:function(o){var n=this.s;return e(this.intValue/t(o,n,!1),n)},distribute:function(t){var o=this.intValue,n=this.p,r=this.s,a=[],l=Math[0<=o?"floor":"ceil"](o/t),u=Math.abs(o-l*t);for(n=r.fromCents?1:n;0!==t;t--){var i=e(l/n,r);0<u--&&(i=i[0<=o?"add":"subtract"](1/n)),a.push(i)}return a},dollars:function(){return~~this.value},cents:function(){return~~(this.intValue%this.p)},format:function(e){var t=this.s;return"function"===typeof e?e(this,t):t.format(this,Object.assign({},t,e))},toString:function(){var e=this.s,t=e.increment;return(Math.round(this.intValue/this.p/t)*t).toFixed(e.precision)},toJSON:function(){return this.value}},e}))},"82ad":function(e,t,o){"use strict";o.d(t,"h",(function(){return r})),o.d(t,"e",(function(){return a})),o.d(t,"a",(function(){return l})),o.d(t,"k",(function(){return u})),o.d(t,"c",(function(){return i})),o.d(t,"b",(function(){return s})),o.d(t,"i",(function(){return c})),o.d(t,"j",(function(){return d})),o.d(t,"f",(function(){return p})),o.d(t,"g",(function(){return m})),o.d(t,"d",(function(){return b}));var n=o("b775");function r(e){return Object(n["a"])({url:"/system/outboundrecord/list",method:"get",params:e})}function a(e){return Object(n["a"])({url:"/system/outboundrecord/"+e,method:"get"})}function l(e){return Object(n["a"])({url:"/system/outboundrecord",method:"post",data:e})}function u(e){return Object(n["a"])({url:"/system/outboundrecord",method:"put",data:e})}function i(e){return Object(n["a"])({url:"/system/outboundrecord/"+e,method:"delete"})}function s(e,t){var o={outboundRecordId:e,status:t};return Object(n["a"])({url:"/system/outboundrecord/changeStatus",method:"put",data:o})}function c(e){return Object(n["a"])({url:"/system/outboundrecord/outboundrecords",method:"get",params:e})}function d(e){return Object(n["a"])({url:"/system/outboundrecord/listRental",method:"get",params:e})}function p(e){return Object(n["a"])({url:"/system/outboundrecord/outboundrecords/"+e,method:"get"})}function m(e){return Object(n["a"])({url:"/system/outboundrecord/rentals/"+e,method:"get"})}function b(e){return Object(n["a"])({url:"/system/outboundrecord/outboundBill",method:"put",data:e,responseType:"arraybuffer"})}},"9da1":function(e,t,o){}}]);