
7c6daedba92ef0ace61368e2eef9b5e0352fc9bf	{"key":"make-fetch-happen:request-cache:https://registry.npmmirror.com/@anthropic-ai%2fclaude-code","integrity":"sha512-GEw31vYvrl9GcVuXorf749ouPkOFz2RcNV9AxRGzRc0IIX2oiSGftwfhhOAd97E1NTZnVstrkry5KoMCj+UKLg==","time":1750840551185,"size":339996,"metadata":{"time":1750840551185,"url":"https://registry.npmmirror.com/@anthropic-ai%2fclaude-code","reqHeaders":{"accept":"application/json"},"resHeaders":{"cache-control":"public, max-age=300","date":"Wed, 25 Jun 2025 08:31:25 GMT","etag":"W/\"461c036e1d33ecdd17cdaf92711aa0c382e827b5\"","vary":"Origin, Accept, Accept-Encoding","content-encoding":"gzip","content-type":"application/json; charset=utf-8"},"options":{"compress":true}}}
fb420956fb984cbbe69b791d5ce89529b2637c50	{"key":"make-fetch-happen:request-cache:https://registry.npmmirror.com/@anthropic-ai%2fclaude-code","integrity":"sha512-GEw31vYvrl9GcVuXorf749ouPkOFz2RcNV9AxRGzRc0IIX2oiSGftwfhhOAd97E1NTZnVstrkry5KoMCj+UKLg==","time":1750842315095,"size":339996,"metadata":{"time":1750842315094,"url":"https://registry.npmmirror.com/@anthropic-ai%2fclaude-code","reqHeaders":{"accept":"application/json"},"resHeaders":{"cache-control":"public, max-age=300","date":"Wed, 25 Jun 2025 09:00:24 GMT","etag":"W/\"461c036e1d33ecdd17cdaf92711aa0c382e827b5\"","vary":"Origin, Accept, Accept-Encoding","content-encoding":"gzip","content-type":"application/json; charset=utf-8"},"options":{"compress":true}}}