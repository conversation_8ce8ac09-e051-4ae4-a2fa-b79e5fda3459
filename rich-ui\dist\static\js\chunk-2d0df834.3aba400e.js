(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d0df834"],{"89da":function(e,r,t){"use strict";t.r(r);var o=function(){var e=this,r=e.$createElement,t=e._self._c||r;return t("div",[t("el-tooltip",{attrs:{disabled:null==e.scope.row.carrier||e.scope.row.carrier.length<20,placement:"top"}},[t("div",{attrs:{slot:"content"},slot:"content"},[t("h6",{staticStyle:{margin:"0"}},[e._v(" "+e._s(e.scope.row.carrier)+" ")])]),t("div",[t("h6",{staticStyle:{margin:"0",overflow:"hidden","text-overflow":"ellipsis"}},[e._v(" "+e._s(null!=e.scope.row.carrier?e.scope.row.carrier.substring(0,20):"")+" ")])])])],1)},s=[],c={name:"carrier",props:["scope"]},n=c,i=t("2877"),a=Object(i["a"])(n,o,s,!1,null,"c01579fa",null);r["default"]=a.exports}}]);