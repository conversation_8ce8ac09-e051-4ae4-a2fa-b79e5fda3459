(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-59cce740","chunk-2d21f29c"],{"20f5":function(e,t,r){"use strict";r.d(t,"f",(function(){return s})),r.d(t,"c",(function(){return o})),r.d(t,"d",(function(){return l})),r.d(t,"a",(function(){return i})),r.d(t,"k",(function(){return n})),r.d(t,"b",(function(){return c})),r.d(t,"i",(function(){return d})),r.d(t,"j",(function(){return f})),r.d(t,"g",(function(){return u})),r.d(t,"h",(function(){return p})),r.d(t,"e",(function(){return m}));var a=r("b775");function s(e){return Object(a["a"])({url:"/system/rct/list",method:"get",params:e})}function o(e){return Object(a["a"])({url:"/system/rct/"+e,method:"get"})}function l(){return Object(a["a"])({url:"/system/rctold/mon",method:"get"})}function i(e){return Object(a["a"])({url:"/system/rct",method:"post",data:e})}function n(e){return Object(a["a"])({url:"/system/rct",method:"put",data:e})}function c(e){return Object(a["a"])({url:"/system/rct/"+e,method:"delete"})}function d(e){return Object(a["a"])({url:"/system/rctold/saveRctLogistics",method:"post",data:e})}function f(e){return Object(a["a"])({url:"/system/rctold/saveRctPreCarriage",method:"post",data:e})}function u(e){return Object(a["a"])({url:"/system/rctold/saveRctExportDeclaration",method:"post",data:e})}function p(e){return Object(a["a"])({url:"/system/rctold/saveRctImportClearance",method:"post",data:e})}function m(){return Object(a["a"])({url:"system/rctold/getRctStatistics",method:"get"})}},"74b1":function(e,t,r){"use strict";r.d(t,"d",(function(){return s})),r.d(t,"c",(function(){return o})),r.d(t,"a",(function(){return l})),r.d(t,"g",(function(){return i})),r.d(t,"b",(function(){return n})),r.d(t,"e",(function(){return c})),r.d(t,"f",(function(){return d}));var a=r("b775");function s(e){return Object(a["a"])({url:"/system/post/list",method:"get",params:e})}function o(e){return Object(a["a"])({url:"/system/post/"+e,method:"get"})}function l(e){return Object(a["a"])({url:"/system/post",method:"post",data:e})}function i(e){return Object(a["a"])({url:"/system/post",method:"put",data:e})}function n(e){return Object(a["a"])({url:"/system/post/"+e,method:"delete"})}function c(e){return Object(a["a"])({url:"/system/post/underUser/"+e,method:"get"})}function d(e){return Object(a["a"])({url:"/system/post/user/"+e,method:"get"})}},d93d:function(e,t,r){"use strict";r.r(t);var a=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("company",{attrs:{"role-supplier":"1"}})},s=[],o=r("1231"),l={name:"Supplier",components:{company:o["default"]}},i=l,n=r("2877"),c=Object(n["a"])(i,a,s,!1,null,null,null);t["default"]=c.exports},e11a:function(e,t,r){"use strict";r.r(t);var a=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",{staticClass:"app-container"},[r("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"},{name:"dialogDragWidth",rawName:"v-dialogDragWidth"}],attrs:{"close-on-click-modal":!1,"modal-append-to-body":!1,"show-close":!1,title:e.title,visible:this.openCompany,width:"800px"},on:{close:e.closeDialog,open:e.openDialog}},[r("el-form",{ref:"form",staticClass:"edit",attrs:{model:e.form,rules:e.rules,"label-width":"68px"}},[r("el-row",[r("el-divider",{attrs:{"content-position":"left"}},[e._v("新增操作单")]),r("el-row",{attrs:{gutter:12}},[r("el-col",{attrs:{span:8}},[r("el-form-item",{attrs:{label:"操作单号",prop:"rctNo"}},[r("el-input",{attrs:{placeholder:"操作单号"},on:{focus:function(t){return e.generateRct(!1)}},model:{value:e.form.rctNo,callback:function(t){e.$set(e.form,"rctNo",t)},expression:"form.rctNo"}}),r("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"},{name:"dialogDragWidth",rawName:"v-dialogDragWidth"}],attrs:{"close-on-click-modal":!1,"modal-append-to-body":!1,visible:e.openGenerateRct,"append-to-body":"",title:"新增操作单号",width:"350px"},on:{"update:visible":function(t){e.openGenerateRct=t}}},[r("el-form",{ref:"form",staticClass:"edit",attrs:{model:e.rct,rules:e.rules,"label-width":"65px"}},[r("el-form-item",{attrs:{label:"单号规则"}},[r("el-input",{attrs:{disabled:"",placeholder:"前导字符+2位年份+2位月份+4位序列"},model:{value:e.rct.rules,callback:function(t){e.$set(e.rct,"rules",t)},expression:"rct.rules"}})],1),r("el-form-item",{attrs:{label:"前导字符",prop:"leadingCharacter"}},[r("el-input",{attrs:{disabled:"",placeholder:"前导字符"},model:{value:e.rct.leadingCharacter,callback:function(t){e.$set(e.rct,"leadingCharacter",t)},expression:"rct.leadingCharacter"}})],1),r("el-form-item",{attrs:{label:"所属月份"}},[r("el-radio-group",{model:{value:e.rct.month,callback:function(t){e.$set(e.rct,"month",t)},expression:"rct.month"}},[r("el-radio-button",{attrs:{label:"1"}},[e._v("本月单号")]),r("el-radio-button",{attrs:{label:"2"}},[e._v("显示下月")])],1)],1),r("el-form-item",{attrs:{label:"单号序列"}},[r("el-radio-group",{model:{value:e.rct.noNum,callback:function(t){e.$set(e.rct,"noNum",t)},expression:"rct.noNum"}},[r("el-radio-button",{attrs:{label:"1"}},[e._v("自然序列")]),r("el-radio-button",{attrs:{label:"2"}},[e._v("手动分配")])],1)],1),r("el-form-item",{attrs:{label:"单号预览"}},[r("div",{staticStyle:{display:"flex"}},[r("el-input",{attrs:{disabled:"1"==e.rct.noNum},model:{value:e.rct.rctNo,callback:function(t){e.$set(e.rct,"rctNo",t)},expression:"rct.rctNo"}}),r("el-button",{attrs:{size:"mini",type:"success"},on:{click:function(t){return e.generateRct(!0)}}},[e._v(" "+e._s("1"==e.rct.noNum?"生成":"")+" "+e._s("2"==e.rct.noNum?"校验":"")+" ")])],1)])],1),r("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[r("el-button",{attrs:{size:"mini",type:"primary"},on:{click:e.confirmRct}},[e._v("确 定")]),r("el-button",{attrs:{size:"mini"},on:{click:e.createCancel}},[e._v("取 消")])],1)],1)],1)],1),r("el-col",{attrs:{span:8}},[r("el-form-item",{attrs:{label:"操作日期",prop:"rctOpDate"}},[r("el-date-picker",{staticStyle:{width:"100%"},attrs:{clearable:"",placeholder:"操作日期",type:"date","value-format":"yyyy-MM-dd"},model:{value:e.form.rctOpDate,callback:function(t){e.$set(e.form,"rctOpDate",t)},expression:"form.rctOpDate"}})],1)],1),r("el-col",{attrs:{span:8}},[r("el-form-item",{attrs:{label:"订单进度",prop:"processStatusId"}},[r("tree-select",{attrs:{flat:!1,multiple:!1,pass:e.form.processStatusId,placeholder:"进度状态",type:"processStatus"},on:{return:function(t){e.form.processStatusId=t}}})],1)],1),r("el-col",{attrs:{span:8}},[r("el-form-item",{attrs:{label:"业务",prop:"salesId"}},[r("treeselect",{attrs:{"disable-branch-nodes":!0,disabled:e.psaVerify,"disabled-fuzzy-matching":!0,"flatten-search-results":!0,normalizer:e.staffNormalizer,options:e.belongList,"show-count":!0,placeholder:"业务员"},on:{input:function(t){void 0==t&&(e.form.salesId=null)},open:e.loadSales,select:function(t){e.form.salesId=t.staffId}},scopedSlots:e._u([{key:"value-label",fn:function(t){var a=t.node;return r("div",{},[e._v(" "+e._s(void 0!=a.raw.staff?a.raw.staff.staffFamilyLocalName+a.raw.staff.staffGivingLocalName+" "+a.raw.staff.staffGivingEnName:"")+" ")])}},{key:"option-label",fn:function(t){var a=t.node,s=t.shouldShowCount,o=t.count,l=t.labelClassName,i=t.countClassName;return r("label",{class:l},[e._v(" "+e._s(-1!=a.label.indexOf(",")?a.label.substring(0,a.label.indexOf(",")):a.label)+" "),s?r("span",{class:i},[e._v("("+e._s(o)+")")]):e._e()])}}]),model:{value:e.salesId,callback:function(t){e.salesId=t},expression:"salesId"}})],1)],1),r("el-col",{attrs:{span:8}},[r("el-form-item",{attrs:{label:"业务助理",prop:"salesAssistantId"}},[r("treeselect",{attrs:{"disable-branch-nodes":!0,disabled:e.psaVerify,"disabled-fuzzy-matching":!0,"flatten-search-results":!0,normalizer:e.staffNormalizer,options:e.belongList,"show-count":!0,placeholder:"业务助理"},on:{input:function(t){void 0==t&&(e.form.salesAssistantId=null)},open:e.loadSales,select:function(t){e.form.salesAssistantId=t.staffId}},scopedSlots:e._u([{key:"value-label",fn:function(t){var a=t.node;return r("div",{},[e._v(" "+e._s(void 0!=a.raw.staff?a.raw.staff.staffFamilyLocalName+a.raw.staff.staffGivingLocalName+" "+a.raw.staff.staffGivingEnName:"")+" ")])}},{key:"option-label",fn:function(t){var a=t.node,s=t.shouldShowCount,o=t.count,l=t.labelClassName,i=t.countClassName;return r("label",{class:l},[e._v(" "+e._s(-1!=a.label.indexOf(",")?a.label.substring(0,a.label.indexOf(",")):a.label)+" "),s?r("span",{class:i},[e._v("("+e._s(o)+")")]):e._e()])}}]),model:{value:e.salesAssistantId,callback:function(t){e.salesAssistantId=t},expression:"salesAssistantId"}})],1)],1),r("el-col",{attrs:{span:8}},[r("el-form-item",{attrs:{label:"订单难度",prop:"orderDifficulty"}},[r("order-difficulty-select",{attrs:{pass:e.form.difficultyLevel},on:{difficulty:e.selectedDifficulty}})],1)],1),r("el-col",{attrs:{span:8}},[r("el-form-item",{attrs:{label:"商务",prop:"verifyPsaId"}},[r("treeselect",{attrs:{"disable-branch-nodes":!0,"disabled-fuzzy-matching":!0,"flatten-search-results":!0,normalizer:e.staffNormalizer,options:e.businessList,"show-count":!0,placeholder:"商务"},on:{input:function(t){void 0==t&&(e.form.verifyPsaId=null)},open:e.loadBusinesses,select:function(t){e.form.verifyPsaId=t.staffId}},scopedSlots:e._u([{key:"value-label",fn:function(t){var a=t.node;return r("div",{},[e._v(" "+e._s(void 0!=a.raw.staff?a.raw.staff.staffFamilyLocalName+a.raw.staff.staffGivingLocalName+" "+a.raw.staff.staffGivingEnName:"")+" ")])}},{key:"option-label",fn:function(t){var a=t.node,s=t.shouldShowCount,o=t.count,l=t.labelClassName,i=t.countClassName;return r("label",{class:l},[e._v(" "+e._s(-1!=a.label.indexOf(",")?a.label.substring(0,a.label.indexOf(",")):a.label)+" "),s?r("span",{class:i},[e._v("("+e._s(o)+")")]):e._e()])}}]),model:{value:e.verifyPsaId,callback:function(t){e.verifyPsaId=t},expression:"verifyPsaId"}})],1)],1),r("el-col",{attrs:{span:8}},[r("el-form-item",{attrs:{label:"审核日期",prop:"psaVerifyTime"}},[r("el-date-picker",{staticStyle:{width:"100%"},attrs:{clearable:"",placeholder:"商务审核日期",type:"date","value-format":"yyyy-MM-dd"},model:{value:e.form.psaVerifyTime,callback:function(t){e.$set(e.form,"psaVerifyTime",t)},expression:"form.psaVerifyTime"}})],1)],1),r("el-col",{attrs:{span:8}},[r("el-form-item",{attrs:{label:"紧急程度",prop:"emergencyLevel"}},[r("urgency-degree-select",{attrs:{pass:e.form.emergencyLevel},on:{urgencyDegree:e.selectedUrgencyDegree}})],1)],1),r("el-col",{attrs:{span:8}},[r("el-form-item",{class:e.psaVerify?"booking":"",attrs:{label:"操作",prop:"opId"}},[r("treeselect",{attrs:{"disable-branch-nodes":!0,disabled:e.psaVerify,"disabled-fuzzy-matching":!0,"flatten-search-results":!0,normalizer:e.staffNormalizer,options:e.opList.filter((function(e){return"操作员"==e.role.roleLocalName})),"show-count":!0,placeholder:"操作员"},on:{input:function(t){void 0==t&&(e.form.opId=null)},open:e.loadOp,select:function(t){e.form.opId=t.staffId}},scopedSlots:e._u([{key:"value-label",fn:function(t){var a=t.node;return r("div",{},[e._v(" "+e._s(void 0!=a.raw.staff?a.raw.staff.staffFamilyLocalName+a.raw.staff.staffGivingLocalName+" "+a.raw.staff.staffGivingEnName:"")+" ")])}},{key:"option-label",fn:function(t){var a=t.node,s=t.shouldShowCount,o=t.count,l=t.labelClassName,i=t.countClassName;return r("label",{class:l},[e._v(" "+e._s(-1!=a.label.indexOf(",")?a.label.substring(0,a.label.indexOf(",")):a.label)+" "),s?r("span",{class:i},[e._v("("+e._s(o)+")")]):e._e()])}}]),model:{value:e.opId,callback:function(t){e.opId=t},expression:"opId"}})],1)],1),r("el-col",{attrs:{span:8}},[r("el-form-item",{attrs:{label:"协助操作",prop:"opObserverId"}},[r("treeselect",{attrs:{"disable-branch-nodes":!0,"disabled-fuzzy-matching":!0,"flatten-search-results":!0,normalizer:e.staffNormalizer,options:e.opList,"show-count":!0,placeholder:"协助操作"},on:{input:function(t){void 0==t&&(e.form.opObserverId=null)},open:e.loadOp,select:function(t){e.form.opObserverId=t.staffId}},scopedSlots:e._u([{key:"value-label",fn:function(t){var a=t.node;return r("div",{},[e._v(" "+e._s(void 0!=a.raw.staff?a.raw.staff.staffFamilyLocalName+a.raw.staff.staffGivingLocalName+" "+a.raw.staff.staffGivingEnName:"")+" ")])}},{key:"option-label",fn:function(t){var a=t.node,s=t.shouldShowCount,o=t.count,l=t.labelClassName,i=t.countClassName;return r("label",{class:l},[e._v(" "+e._s(-1!=a.label.indexOf(",")?a.label.substring(0,a.label.indexOf(",")):a.label)+" "),s?r("span",{class:i},[e._v("("+e._s(o)+")")]):e._e()])}}]),model:{value:e.opObserverId,callback:function(t){e.opObserverId=t},expression:"opObserverId"}})],1)],1),r("el-col",{attrs:{span:8}},[r("el-form-item",{attrs:{label:"收付方式",prop:"paymentTypeId"}},[r("tree-select",{attrs:{disabled:e.psaVerify,flat:!1,multiple:!1,pass:e.form.logisticsPaymentTermsCode,placeholder:"收付方式",type:"paymentType"},on:{return:function(t){e.form.logisticsPaymentTermsCode=t}}})],1)],1),r("el-col",{attrs:{span:16}},[r("el-form-item",{attrs:{label:"委托单位",prop:"clientId"}},[r("company-select",{attrs:{disabled:e.psaVerify,"load-options":this.companyList,multiple:!1,"no-parent":!0,pass:e.form.clientId,placeholder:"委托单位",roleTypeId:1},on:{return:function(t){e.form.clientId=t}}})],1)],1),r("el-col",{attrs:{span:8}},[r("el-form-item",{attrs:{label:"放货方式",prop:"releaseTypeId"}},[r("tree-select",{attrs:{disabled:e.psaVerify,flat:!1,multiple:!1,pass:e.form.releaseType,placeholder:"放货方式",type:"releaseType"},on:{return:function(t){e.form.releaseType=t}}})],1)],1),r("el-col",{attrs:{span:8}},[r("el-form-item",{attrs:{label:"物流类型",prop:"logisticsTypeId"}},[r("tree-select",{attrs:{disabled:e.psaVerify,flat:!1,main:!0,multiple:!1,pass:e.form.logisticsTypeId,placeholder:"物流类型",type:"serviceType"},on:{return:function(t){e.form.logisticsTypeId=t}}})],1)],1),r("el-col",{attrs:{span:8}},[r("el-form-item",{attrs:{label:"进出口",prop:"impExpTypeId"}},[r("el-select",{staticStyle:{width:"100%"},attrs:{disabled:e.psaVerify,clearable:"",filterable:"",placeholder:"进出口"},model:{value:e.form.impExpType,callback:function(t){e.$set(e.form,"impExpType",t)},expression:"form.impExpType"}},[r("el-option",{attrs:{label:"出口",value:"1"}},[e._v("出口")]),r("el-option",{attrs:{label:"进口",value:"2"}},[e._v("进口")])],1)],1)],1),r("el-col",{attrs:{span:8}},[r("el-form-item",{attrs:{label:"货名"}},[r("el-input",{attrs:{disabled:e.psaVerify,placeholder:"货名概要"},model:{value:e.form.goodsNameSummary,callback:function(t){e.$set(e.form,"goodsNameSummary",t)},expression:"form.goodsNameSummary"}})],1)],1),r("el-col",{attrs:{span:8}},[r("el-form-item",{attrs:{label:"启运港",prop:"polId"}},[r("location-select",{attrs:{"check-port":e.logisticsType,disabled:e.psaVerify,"load-options":e.locationOptions,"is-filter":!0,multiple:!1,"no-parent":!0,pass:e.form.polId,placeholder:"启运港"},on:{return:function(t){e.form.polId=t}}})],1)],1),r("el-col",{attrs:{span:8}}),r("el-col",{attrs:{span:8}},[r("el-form-item",{attrs:{label:"目的港",prop:"destinationPortId"}},[r("location-select",{attrs:{"check-port":e.logisticsType,disabled:e.psaVerify,en:!0,"load-options":e.locationOptions,multiple:!1,pass:e.form.destinationPortId,placeholder:"目的港","is-filter":!0},on:{return:function(t){e.form.destinationPortId=t}}})],1)],1),r("el-col",{attrs:{span:8}},[r("el-form-item",{attrs:{label:"货量"}},[r("el-input",{staticStyle:{width:"100%"},attrs:{disabled:e.psaVerify,placeholder:"计费货量"},on:{focus:e.editRevenueTon},model:{value:e.form.revenueTon,callback:function(t){e.$set(e.form,"revenueTon",t)},expression:"form.revenueTon"}}),r("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"},{name:"dialogDragWidth",rawName:"v-dialogDragWidth"}],attrs:{"close-on-click-modal":!1,"modal-append-to-body":!1,visible:e.openGenerateRevenueTons,"append-to-body":"",title:"修改计费货量",width:"350px"},on:{"update:visible":function(t){e.openGenerateRevenueTons=t}}},[r("el-row",[r("el-col",{attrs:{span:23}},[r("el-col",{attrs:{span:12}},[r("el-input-number",{staticStyle:{width:"100%"},attrs:{controls:!1,min:1,placeholder:"数量"},model:{value:e.form.countA,callback:function(t){e.$set(e.form,"countA",t)},expression:"form.countA"}}),r("el-input-number",{staticStyle:{width:"100%"},attrs:{controls:!1,min:1,placeholder:"数量"},model:{value:e.form.countB,callback:function(t){e.$set(e.form,"countB",t)},expression:"form.countB"}}),r("el-input-number",{staticStyle:{width:"100%"},attrs:{controls:!1,min:1,placeholder:"数量"},model:{value:e.form.countC,callback:function(t){e.$set(e.form,"countC",t)},expression:"form.countC"}})],1),r("el-col",{attrs:{span:12}},[r("tree-select",{attrs:{pass:e.form.unitCodeA,type:"unit",placeholder:"选择柜型"},on:{returnData:function(t){e.form.unitCodeA=t.unitCode}}}),r("tree-select",{attrs:{pass:e.form.unitCodeB,type:"unit",placeholder:"选择柜型"},on:{returnData:function(t){e.form.unitCodeB=t.unitCode}}}),r("tree-select",{attrs:{pass:e.form.unitCodeC,type:"unit",placeholder:"选择柜型"},on:{returnData:function(t){e.form.unitCodeC=t.unitCode}}})],1)],1)],1),r("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[r("el-button",{attrs:{type:"primary"},on:{click:e.revenueTonConfirm}},[e._v("确 定")])],1)],1)],1)],1),r("el-col",{attrs:{span:8}},[r("el-form-item",{attrs:{label:"承运人",prop:"carrier"}},[r("treeselect",{attrs:{"disable-branch-nodes":!0,"disable-fuzzy-matching":!0,"flatten-search-results":!0,multiple:!1,normalizer:e.carrierNormalizer,options:e.filterCarrierList,"show-count":!0,placeholder:"承运人"},on:{input:e.deselectCarrierId,select:e.handleSelectCarrierId},scopedSlots:e._u([{key:"value-label",fn:function(t){var a=t.node;return r("div",{},[e._v(" "+e._s(a.raw.carrier?a.raw.carrier.carrierIntlCode:"")+" "+e._s(null==(a.raw.carrier?a.raw.carrier.carrierIntlCode:null)&&a.raw.carrier?a.raw.carrier.carrierShortName:"")+" ")])}},{key:"option-label",fn:function(t){var a=t.node,s=t.shouldShowCount,o=t.count,l=t.labelClassName,i=t.countClassName;return r("label",{class:l},[e._v(" "+e._s(-1!=a.label.indexOf(",")?a.label.substring(0,a.label.indexOf(",")):a.label)+" "),s?r("span",{class:i},[e._v("("+e._s(o)+")")]):e._e()])}}]),model:{value:e.carrier,callback:function(t){e.carrier=t},expression:"carrier"}})],1)],1),r("el-col",{attrs:{span:8}},[r("el-form-item",{attrs:{label:"订舱口",prop:"companyId"}},[r("company-select",{attrs:{disabled:e.psaVerify,"load-options":this.companyList,multiple:!1,"no-parent":!0,pass:e.form.companyId,pslaceholder:"订舱口",roleTypeId:2},on:{return:function(t){e.form.companyId=t}}})],1)],1),r("el-col",{attrs:{span:8}},[r("el-form-item",{attrs:{label:"SO号"}},[r("el-input",{attrs:{placeholder:"SO号"},model:{value:e.form.sqdSoNoSum,callback:function(t){e.$set(e.form,"sqdSoNoSum",t)},expression:"form.sqdSoNoSum"}})],1)],1),r("el-col",{attrs:{span:24}},[r("el-form-item",{attrs:{label:"商务备注"}},[r("el-input",{attrs:{placeholder:"商务备注"},model:{value:e.form.inquiryInnerRemarkSum,callback:function(t){e.$set(e.form,"inquiryInnerRemarkSum",t)},expression:"form.inquiryInnerRemarkSum"}})],1)],1)],1)],1)],1),r("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},["add"===this.type||e.hasPermitSubmit?r("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("确 定")]):e._e(),r("el-button",{on:{click:e.cancel}},[e._v("取 消")])],1)],1)],1)},s=[],o=r("b85c"),l=(r("14d9"),r("a15b"),r("d81d"),r("a9e3"),r("d3b7"),r("25f0"),r("4de4"),r("ca17")),i=r.n(l),n=r("b0b8"),c=r.n(n),d=r("4360"),f=r("20f5"),u=r("d93d"),p=r("9f2c"),m=r("c6cc"),h=r("6e71"),b=(r("74b1"),{name:"RctDialog",computed:{supplier:function(){return u["default"]}},components:{CompanySelect:h["a"],UrgencyDegreeSelect:m["a"],OrderDifficultySelect:p["a"],Treeselect:i.a},props:["title","openCompany","row","type","hasPermitSubmit"],created:function(){this.open=this.openCompany},beforeMount:function(){this.loadSelections()},data:function(){return{form:{rctId:this.row.rctId,companyId:null,impExpTypeId:"1",rctNo:null,rctOpDate:null,processStatusId:null,salesId:null,salesAssistantId:null,orderDifficulty:null,verifyPsaId:null,psaVerifyTime:null,urgencyDegree:null,opId:null,opObserverId:null,paymentTypeId:null,clientId:null,logisticsTypeId:null,polId:null,goodsNameSummary:null,destinationPortId:null,revenueTons:null,carrier:null,bookingAgent:null,soNo:"",inquiryInnerRemark:null,releaseTypeId:null},rules:{},belongList:[],psaVerify:!1,salesAssistantId:null,openGenerateRct:!1,rct:{leadingCharacter:"RCT",month:1,noNum:1,rctNo:null},open:this.openCompany,opId:null,opList:[],opObserverId:null,carrier:null,carrierList:[],filterCarrierList:[],carrierId:null,locationOptions:[],logisticsType:"1",bookingAgentId:null,bookingAgentList:[],verifyPsaId:null,businessList:[],salesId:null,companyList:[],openGenerateRevenueTons:!1}},watch:{"form.logisticsTypeId":function(e){var t=this;0==this.$store.state.data.serviceTypeList.length||this.$store.state.data.redisList.serviceType?d["a"].dispatch("getServiceTypeList").then((function(){t.getType(e)})):this.getType(e)}},methods:{revenueTonConfirm:function(){var e=[];this.form.unitCodeA&&(this.form.countA?e.push(this.form.countA+"x"+this.form.unitCodeA):e.push("1x"+this.form.unitCodeA)),this.form.unitCodeB&&(this.form.countB?e.push(this.form.countB+"x"+this.form.unitCodeB):e.push("1x"+this.form.unitCodeB)),this.form.unitCodeC&&(this.form.countC?e.push(this.form.countC+"x"+this.form.unitCodeC):e.push("1x"+this.form.unitCodeC)),this.form.revenueTon=e.join("+"),this.form.unitCodeA=null,this.form.unitCodeB=null,this.form.unitCodeC=null,this.form.conutA=null,this.form.conutB=null,this.form.conutC=null,this.openGenerateRevenueTons=!1},editRevenueTon:function(){var e=this;this.form.revenueTon&&this.form.revenueTon.split("+").length>0&&this.form.revenueTon.split("+").map((function(t,r){t.split("x").length>0&&0===r&&(e.form.countA=Number(t.split("x")[0]),e.form.unitCodeA=t.split("x")[1],console.log(e.form.countA)),t.split("x").length>0&&1===r&&(e.form.countB=Number(t.split("x")[0]),e.form.unitCodeB=t.split("x")[1]),t.split("x").length>0&&2===r&&(e.form.countC=Number(t.split("x")[0]),e.form.unitCodeC=t.split("x")[1])})),this.openGenerateRevenueTons=!0},selectedUrgencyDegree:function(e){this.form.emergencyLevel=e},selectedDifficulty:function(e){console.log(e),this.form.difficultyLevel=e},getbookingAgentId:function(e){this.form.bookingAgent=e},handleSelectCarrierId:function(e){this.form.carrier=e.carrier.carrierId},deselectCarrierId:function(e){void 0==e&&(this.form.carrier=null)},openDialog:function(){this.reset(),"edit"===this.type?this.editOp():"add"===this.type&&this.addOp()},editOp:function(){var e=this;Object(f["c"])(this.row.rctId).then((function(t){if(e.form=t.data,e.form.rctId=t.data.rctId,e.form.rctNo=t.data.rctNo,e.form.rctOpDate=t.data.rctOpDate,e.form.processStatusId=t.data.processStatusId,e.form.salesId=t.data.salesId,e.form.salesAssistantId=t.data.salesAssistantId,e.form.orderDifficulty=t.data.orderDifficulty,e.form.verifyPsaId=t.data.verifyPsaId,e.form.psaVerifyTime=t.data.psaVerifyTime,e.form.urgencyDegree=t.data.urgencyDegree,e.form.opId=t.data.opId,e.form.opObserverId=t.data.opObserverId,e.form.paymentTypeId=t.data.paymentTypeId,e.form.clientId=t.data.clientId,e.form.logisticsTypeId=t.data.logisticsTypeId,e.form.polId=t.data.polId,e.form.impExpType=t.data.impExpType,e.form.goodsNameSummary=t.data.goodsNameSummary,e.form.destinationPortId=t.data.destinationPortId,e.form.revenueTons=t.data.revenueTons,e.form.carrier=t.data.carrier,e.form.companyId=t.data.bookingAgent,e.form.inquiryInnerRemark=t.data.inquiryInnerRemark,e.form.releaseTypeId=t.data.releaseTypeId,e.locationOptions=t.locationOptions,e.companyList=t.companyList,e.bookingAgentId=t.data.bookingAgent,void 0!=e.belongList){var r,a=Object(o["a"])(e.belongList);try{for(a.s();!(r=a.n()).done;){var s=r.value;if(void 0!=s.children){var l,i=Object(o["a"])(s.children);try{for(i.s();!(l=i.n()).done;){var n=l.value;if(void 0!=n.children){var c,d=Object(o["a"])(n.children);try{for(d.s();!(c=d.n()).done;){var f=c.value;f.staffId==t.data.salesId&&(e.salesId=f.deptId),f.staffId==t.data.salesAssistantId&&(e.salesAssistantId=f.deptId),f.staffId==t.data.salesObserverId&&(e.salesObserverId=f.deptId)}}catch(A){d.e(A)}finally{d.f()}}}}catch(A){i.e(A)}finally{i.f()}}}}catch(A){a.e(A)}finally{a.f()}}if(void 0!=e.businessList){var u,p=Object(o["a"])(e.businessList);try{for(p.s();!(u=p.n()).done;){var m=u.value;if(void 0!=m.children){var h,b=Object(o["a"])(m.children);try{for(b.s();!(h=b.n()).done;){var v=h.value;v.staffId==t.data.verifyPsaId&&(e.verifyPsaId=v.roleId)}}catch(A){b.e(A)}finally{b.f()}}}}catch(A){p.e(A)}finally{p.f()}}if(e.carrierList.length>0){var y,g=Object(o["a"])(e.carrierList);try{for(g.s();!(y=g.n()).done;){var I=y.value;if(null!=I.carrier&&null!=I.carrier.carrierId&&void 0!=I.carrier.carrierId&&null!=t.data.carrier&&void 0!=t.data.carrier&&I.carrier.carrierId==t.data.carrierId&&(e.carrier=I.serviceTypeId),void 0!=I.children&&I.children.length>0){var L,C=Object(o["a"])(I.children);try{for(C.s();!(L=C.n()).done;){var N=L.value;if(null!=N.carrier&&null!=N.carrier.carrierId&&void 0!=N.carrier.carrierId&&null!=t.data.carrier&&void 0!=t.data.carrier&&N.carrier.carrierId==t.data.carrierId&&(e.carrier=I.serviceTypeId),void 0!=N.children&&N.children.length>0){var O,T=Object(o["a"])(N.children);try{for(T.s();!(O=T.n()).done;){var k=O.value;null!=k.carrier&&null!=k.carrier.carrierId&&void 0!=k.carrier.carrierId&&null!=t.data.carrier&&void 0!=t.data.carrier&&k.carrier.carrierId==t.data.carrier&&(e.carrier=k.serviceTypeId)}}catch(A){T.e(A)}finally{T.f()}}}}catch(A){C.e(A)}finally{C.f()}}}}catch(A){g.e(A)}finally{g.f()}}if(void 0!=e.opList){var S,w=Object(o["a"])(e.opList);try{for(w.s();!(S=w.n()).done;){var _=S.value;if(void 0!=_.children){var x,D=Object(o["a"])(_.children);try{for(D.s();!(x=D.n()).done;){var $=x.value;"操作员"==_.role.roleLocalName&&$.staffId==t.data.opId&&(e.opId=$.roleId),"订舱员"==_.role.roleLocalName&&$.staffId==t.data.bookingOpId&&(e.bookingOpId=$.roleId),"单证员"==_.role.roleLocalName&&$.staffId==t.data.docOpId&&(e.docOpId=$.roleId),$.staffId==t.data.opObserverId&&(e.opObserverId=$.roleId)}}catch(A){D.e(A)}finally{D.f()}}}}catch(A){w.e(A)}finally{w.f()}}})),this.open=this.openCompany},addOp:function(){if(void 0!==this.businessList){var e,t=Object(o["a"])(this.businessList);try{for(t.s();!(e=t.n()).done;){var r=e.value;if(void 0!==r.children){var a,s=Object(o["a"])(r.children);try{for(s.s();!(a=s.n()).done;){var l=a.value;l.staffId===this.$store.state.user.sid&&(this.verifyPsaId=l.roleId)}}catch(i){s.e(i)}finally{s.f()}}}}catch(i){t.e(i)}finally{t.f()}}this.form.rctOpDate=new Date,this.form.psaVerifyTime=new Date,this.form.logisticsTypeId=1,this.form.impExpTypeId=1},getBookingAgent:function(e){this.form.bookingAgent=e},loadSelections:function(){this.loadSales(),this.loadOp(),this.loadBookingAgent(),this.loadBusinesses(),this.loadCarrier()},reset:function(){this.form.rctId=null,this.form.rctNo=null,this.form.rctOpDate=null,this.form.processStatusId=null,this.form.orderDifficulty=null,this.form.psaVerifyTime=null,this.form.urgencyDegree=null,this.form.paymentTypeId=null,this.form.clientId=null,this.form.releaseTypeId=null,this.form.logisticsTypeId=null,this.form.polId=null,this.form.impExpTypeId=null,this.form.goodsNameSummary=null,this.form.destinationPortId=null,this.form.revenueTons=null,this.form.bookingAgent=null,this.form.soNo=null,this.form.inquiryInnerRemark=null,this.form.companyId=null,this.opObserverId=null,this.salesId=null,this.opId=null,this.carrier=null,this.verifyPsaId=null,this.salesAssistantId=null},staffNormalizer:function(e){var t;return e.children&&!e.children.length&&delete e.children,e.staff&&(t=null==e.staff.staffFamilyLocalName&&null==e.staff.staffGivingLocalName?null!=e.role.roleLocalName?e.role.roleLocalName+","+c.a.getFullChars(e.role.roleLocalName):e.dept.deptLocalName+","+c.a.getFullChars(e.dept.deptLocalName):e.staff.staffCode+" "+e.staff.staffFamilyLocalName+e.staff.staffGivingLocalName+" "+e.staff.staffGivingEnName+","+c.a.getFullChars(e.staff.staffFamilyLocalName+e.staff.staffGivingLocalName)),e.roleId?{id:e.roleId,label:t,children:e.children,isDisabled:null==e.staffId&&void 0==e.children}:{id:e.deptId,label:t,children:e.children,isDisabled:null==e.staffId&&void 0==e.children}},loadSales:function(){var e=this;0==this.$store.state.data.salesList.length||this.$store.state.data.redisList.salesList?d["a"].dispatch("getSalesList").then((function(){e.belongList=e.$store.state.data.salesList})):this.belongList=this.$store.state.data.salesList},generateRct:function(e){var t=this;e?Object(f["d"])().then((function(e){var r=e.data;if(r.toString().length<3)for(var a=3-r.toString().length,s=0;s<a;s++)r="0"+r;var o=new Date,l=(o.getMonth()+Number(t.rct.month)).toString(),i=(o.getFullYear()+(l/12>1?1:0)).toString().substring(2,4);t.rct.rctNo=t.rct.leadingCharacter+i+(1==l.length?"0"+l:l)+r.toString()})):this.openGenerateRct=!0},confirmRct:function(){this.form.rctNo=this.rct.rctNo,this.openGenerateRct=!1},cancel:function(){this.reset(),this.resetForm("form"),this.$emit("closeDialog")},loadOp:function(){var e=this;0==this.$store.state.data.opList.length||this.$store.state.data.redisList.opList?d["a"].dispatch("getOpList").then((function(){e.opList=e.$store.state.data.opList})):this.opList=this.$store.state.data.opList},carrierNormalizer:function(e){var t;return e.children&&!e.children.length&&delete e.children,t=!e.carrier||null==e.carrier.carrierLocalName&&null==e.carrier.carrierEnName?e.serviceLocalName+" "+e.serviceEnName+","+c.a.getFullChars(void 0!=e.serviceLocalName?e.serviceLocalName:""):(null!=e.carrier.carrierIntlCode?e.carrier.carrierIntlCode:"")+" "+(null!=e.carrier.carrierEnName?e.carrier.carrierEnName:"")+" "+(null!=e.carrier.carrierLocalName?e.carrier.carrierLocalName:"")+","+c.a.getFullChars(null!=e.carrier.carrierLocalName?e.carrier.carrierLocalName:""),{id:e.serviceTypeId,label:t,children:e.children}},loadCarrier:function(){var e=this;0==this.$store.state.data.serviceTypeCarriers.length||this.$store.state.data.redisList.serviceTypeCarriers?d["a"].dispatch("getServiceTypeCarriersList").then((function(){e.carrierList=e.$store.state.data.serviceTypeCarriers})):this.carrierList=this.$store.state.data.serviceTypeCarriers,this.filterCarrierList=this.$store.state.data.serviceTypeCarriers},handleDeselectCarrierIds:function(e){this.form.carrierIds=this.form.carrierIds.filter((function(t){return t!=e.carrier.carrierId}))},handleSelectCarrierIds:function(e){this.form.carrier=e.carrier.carrierId},loadBookingAgent:function(){var e=this;0==this.$store.state.data.supplierList.length||this.$store.state.data.redisList.supplierList?d["a"].dispatch("getSupplierList").then((function(){e.bookingAgentList=e.$store.state.data.supplierList})):this.bookingAgentList=this.$store.state.data.supplierList},loadBusinesses:function(){var e=this;0==this.$store.state.data.businessesList.length||this.$store.state.data.redisList.businessesList?d["a"].dispatch("getBusinessesList").then((function(){e.businessList=e.$store.state.data.businessesList})):this.businessList=this.$store.state.data.businessesList},handleSelectBookingAgent:function(e){this.bookingAgentId=e.bookingAgent.bookingAgentId},handleDeselectBookingAgent:function(e){console.log(e)},submitForm:function(){var e=this;this.$refs["form"].validate((function(t){t&&(null!=e.form.rctId?Object(f["k"])(e.form).then((function(t){e.$modal.msgSuccess("修改成功"),e.$emit("closeDialog"),e.$emit("getRctList")})):Object(f["a"])(e.form).then((function(t){e.$modal.msgSuccess("新增成功"),e.$emit("closeDialog"),e.$emit("getRctList")})),e.$emit("updateStatistics"))}))},createCancel:function(){this.openGenerateRct=!1},closeDialog:function(){this.$emit("closeDialog")},getType:function(e){var t,r=Object(o["a"])(this.$store.state.data.serviceTypeList);try{for(r.s();!(t=r.n()).done;){var a=t.value;if(a.children)for(var s in a.children)s.serviceTypeId==e&&(this.logisticsType=s.typeId,this.changeCarrierList(s.serviceEnName));a.serviceTypeId==e&&(this.logisticsType=a.typeId,this.changeCarrierList(a.serviceEnName))}}catch(l){r.e(l)}finally{r.f()}},changeCarrierList:function(e){switch(e){case"1":this.filterCarrierList=this.carrierList.filter((function(e){return"1"===e.typeId}));break;case"2":this.filterCarrierList=this.carrierList.filter((function(e){return"2"===e.typeId}));break;case"3":this.filterCarrierList=this.carrierList.filter((function(e){return"3"===e.typeId}));break}}}}),v=b,y=r("2877"),g=Object(y["a"])(v,a,s,!1,null,"f567cdee",null);t["default"]=g.exports}}]);