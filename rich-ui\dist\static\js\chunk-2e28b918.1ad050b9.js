(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2e28b918"],{"34bb":function(e,t,o){"use strict";o.r(t);var r=function(){var e=this,t=e.$createElement,o=e._self._c||t;return o("el-col",{style:{display:e.openOpHistory?"":"none"},attrs:{span:16}},[o("div",{staticClass:"titleStyle"},[o("div",{staticClass:"titleText"},[e._v("操作历史记录（文件管理）")])]),o("div",{class:{inactive:0==e.openOpHistory,active:e.openOpHistory}},[o("el-table",{staticClass:"pd0",attrs:{data:e.opHistory,border:""}},[o("el-table-column",{attrs:{align:"center",label:"操作进度流水",prop:"operationalProcessId","show-tooltip-when-overflow":"",width:"80px"}}),o("el-table-column",{attrs:{align:"center",label:"进度名称",prop:"processId",width:"68px"},scopedSlots:e._u([{key:"default",fn:function(e){return[o("tree-select",{attrs:{flat:!1,multiple:!1,pass:e.row.processId,placeholder:"进度状态",type:"process"},on:{return:function(t){e.row.processId=t}}})]}}])}),o("el-table-column",{attrs:{align:"center",label:"进度状态",prop:"processStatusId",width:"68px"},scopedSlots:e._u([{key:"default",fn:function(e){return[o("tree-select",{attrs:{flat:!1,multiple:!1,pass:e.row.processStatusId,placeholder:"进度状态",type:"processStatus"},on:{return:function(t){e.row.processStatusId=t}}})]}}])}),o("el-table-column",{attrs:{align:"center",label:"发送方",prop:"senderId",width:"68px"},scopedSlots:e._u([{key:"default",fn:function(t){return[o("el-select",{attrs:{placeholder:"发送方",filterable:""},on:{change:function(o){return e.getSender(o,t.row)}},model:{value:t.row.senderId,callback:function(o){e.$set(t.row,"senderId",o)},expression:"scope.row.senderId"}},e._l(e.$store.state.data.companyList,(function(e){return o("el-option",{key:e.companyId,attrs:{label:e.companyShortName,value:e.companyId}})})),1)]}}])}),o("el-table-column",{attrs:{align:"center",label:"接收方",prop:"receiverId",width:"68px"},scopedSlots:e._u([{key:"default",fn:function(t){return[o("el-select",{attrs:{filterable:"",placeholder:"接收方"},on:{change:function(o){return e.getReceiver(o,t.row)}},model:{value:t.row.receiverId,callback:function(o){e.$set(t.row,"receiverId",o)},expression:"scope.row.receiverId"}},e._l(e.$store.state.data.companyList,(function(e){return o("el-option",{key:e.companyId,attrs:{label:e.companyShortName,value:e.companyId}})})),1)]}}])}),o("el-table-column",{attrs:{label:"随附文件列表"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._l(t.row.docList,(function(r){return o("el-button",{key:r.docDetailId,staticStyle:{padding:"0"},attrs:{size:"small",type:"text"},on:{click:function(o){return e.checkDoc(r,t.row)}}},[e._v(" "+e._s("["+r.flowNo+"]")+" ")])})),o("el-button",{staticStyle:{padding:"0"},attrs:{size:"mini",type:"text"},on:{click:function(o){return e.handleAddDocList(t.row)}}},[e._v(" [＋] ")])]}}])}),o("el-table-column",{attrs:{align:"center",label:"交互方式",prop:"releaseWayId",width:"68px"},scopedSlots:e._u([{key:"default",fn:function(e){return[o("tree-select",{attrs:{flat:!1,multiple:!1,pass:e.row.releaseWayId,placeholder:"交互方式",type:"docReleaseWay"},on:{return:function(t){e.row.releaseWayId=t}}})]}}])}),o("el-table-column",{attrs:{align:"center",label:"发生时间",prop:"processStatusTime",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[o("el-date-picker",{staticStyle:{width:"100%"},attrs:{clearable:"",placeholder:"进度发生时间",type:"date","value-format":"yyyy-MM-dd"},model:{value:t.row.processStatusTime,callback:function(o){e.$set(t.row,"processStatusTime",o)},expression:"scope.row.processStatusTime"}})]}}])}),o("el-table-column",{attrs:{align:"center",label:"备注",prop:"remark"},scopedSlots:e._u([{key:"default",fn:function(t){return[o("el-input",{attrs:{placeholder:"备注"},model:{value:t.row.remark,callback:function(o){e.$set(t.row,"remark",o)},expression:"scope.row.remark"}})]}}])}),o("el-table-column",{attrs:{align:"center",label:"发送",width:"50px"},scopedSlots:e._u([{key:"default",fn:function(t){return[o("el-button",{staticStyle:{padding:"0"},attrs:{icon:"el-icon-s-promotion",size:"mini",type:"primary"},on:{click:e.handleSend}},[e._v("Send ")])]}}])}),o("el-table-column",{attrs:{align:"center",label:"操作员",prop:"opName",width:"50px"}}),o("el-table-column",{attrs:{align:"center",label:"录入时间",prop:"createTime",width:"80"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(e.parseTime(t.row.createTime,"{y}/{m}/{d}"))+" ")]}}])}),o("el-table-column",{attrs:{align:"center","class-name":"small-padding fixed-width",label:"操作",width:"50"},scopedSlots:e._u([{key:"default",fn:function(t){return[o("el-button",{attrs:{icon:"el-icon-delete",size:"mini",type:"danger"},on:{click:function(o){return e.delOpHistory(t.row)}}},[e._v("删除 ")])]}}])})],1),o("el-button",{staticStyle:{padding:"0"},attrs:{type:"text"},on:{click:e.addOpHistory}},[e._v("[＋] ")]),o("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"},{name:"dialogDragWidth",rawName:"v-dialogDragWidth"}],attrs:{"close-on-click-modal":!1,"modal-append-to-body":!1,visible:e.open,"append-to-body":"",title:"新增操作历史记录",width:"500px"},on:{"update:visible":function(t){e.open=t}}},[o("el-form",{ref:"docList",attrs:{model:e.doc,border:"","label-width":"68px"}},[o("el-form-item",{attrs:{label:"流向编号",prop:"flowNo"}},[o("el-input",{attrs:{placeholder:"文件名/流向编号"},model:{value:e.doc.flowNo,callback:function(t){e.$set(e.doc,"flowNo",t)},expression:"doc.flowNo"}})],1),o("el-form-item",{attrs:{label:"文件类型",prop:"docId"}},[o("tree-select",{attrs:{flat:!1,multiple:!1,pass:e.doc.docId,placeholder:"文件类型",type:"doc"},on:{return:function(t){e.doc.docId=t}}})],1),o("el-form-item",{attrs:{label:"文件流向",prop:"docFlowDirectionId"}},[o("tree-select",{attrs:{flat:!1,multiple:!1,pass:e.doc.docFlowDirectionId,placeholder:"文件流向",type:"docFlowDirection"},on:{return:function(t){e.doc.docFlowDirectionId=t}}})],1),o("el-form-item",{attrs:{label:"文件形式",prop:"issueTypeId"}},[o("tree-select",{attrs:{flat:!1,multiple:!1,pass:e.doc.issueTypeId,placeholder:"文件形式",type:"docIssueType"},on:{return:function(t){e.doc.issueTypeId=t}}})],1),o("el-form-item",{attrs:{label:"创建时间",prop:"createTime"}},[e._v(" "+e._s(e.parseTime(e.doc.createTime,"{y}/{m}/{d}"))+" ")]),o("el-form-item",{attrs:{label:"备注",prop:"remark"}},[o("el-input",{attrs:{autosize:{minRows:1},maxlength:"300",placeholder:"备注","show-word-limit":"",type:"textarea"},model:{value:e.doc.remark,callback:function(t){e.$set(e.doc,"remark",t)},expression:"doc.remark"}})],1),o("el-form-item",{attrs:{label:"创建人",prop:"createByName"}},[e._v(" "+e._s(e.doc.createByName)+" ")]),o("el-form-item",{attrs:{label:"录入时间",prop:"updateTime"}},[e._v(" "+e._s(e.parseTime(e.doc.updateTime,"{y}/{m}/{d}"))+" ")]),o("file-upload",{attrs:{"file-type":["xlsx","xls","docx","doc","pdf"],limit:3,value:e.doc.fileList},on:{input:function(t){e.doc.fileList=t}}})],1),o("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[o("el-button",{attrs:{type:"primary"},on:{click:e.submitDoc}},[e._v("确 定")]),o("el-button",{attrs:{type:"danger"},on:{click:e.delDoc}},[e._v("删 除")]),o("el-button",{on:{click:e.cancel}},[e._v("取 消")])],1)],1)],1)])},s=[],n=(o("b0c0"),o("14d9"),o("4de4"),o("d3b7"),o("fba1")),a=o("3a13"),l=o("b775");function c(e){return Object(l["a"])({url:"/system/docdetail/"+e,method:"get"})}function i(e){return Object(l["a"])({url:"/system/docdetail",method:"post",data:e})}function d(e){return Object(l["a"])({url:"/system/docdetail",method:"put",data:e})}function u(e){return Object(l["a"])({url:"/system/docdetail/"+e,method:"delete"})}var p={name:"opHistory",props:["opHistory","openOpHistory","typeId","rctId","basicInfoId"],data:function(){return{open:!1,doc:{},dList:{},receive:!1,send:!1}},watch:{opHistory:function(e){this.$emit("return",e)}},methods:{handleAddDocList:function(e){this.open=!0,this.doc={operationalProcessId:e.operationalProcessId,docId:null,flowNo:null,docFlowDirectionId:null,issueTypeId:null,fileList:null,createBy:this.$store.state.user.sid,createByName:this.$store.state.user.name.split(" ")[1],createTime:Object(n["f"])(new Date),remark:null,updateTime:Object(n["f"])(new Date)},this.dList=e},checkDoc:function(e,t){var o=this;c(e.docDetailId).then((function(e){o.doc=e.data,o.open=!0})),this.dList=t},getSender:function(e,t){this.send=!0,this.receive=!1,this.receive||(t.senderId=e,t.receiverId=1)},getReceiver:function(e,t){this.receive=!0,this.send=!1,this.send||(t.receiverId=e,t.senderId=1)},handleSend:function(){},addOpHistory:function(){var e=this,t={docList:[],typeId:this.typeId,rctId:this.rctId,basicInfoId:this.basicInfoId,processId:null,processStatusId:null,senderId:null,receiverId:null,releaseWayId:null,processStatusTime:Object(n["f"])(new Date),remark:null,opId:this.$store.state.user.sid,opName:this.$store.state.user.name.split(" ")[1],createTime:Object(n["f"])(new Date)};Object(a["a"])(t).then((function(o){e.opHistory.push(t)}))},delOpHistory:function(e){var t=this,o={operationalProcessId:e.operationalProcessId,typeId:e.typeId,rctId:e.rctId,basicInfoId:e.basicInfoId};Object(a["c"])(o).then((function(o){t.$message.success(o.msg),t.opHistory=t.opHistory.filter((function(t){return t.operationalProcessId!=e.operationalProcessId}))}))},submitDoc:function(){var e=this;null==this.doc.docDetailId?i(this.doc).then((function(t){null==e.dList.docList&&(e.dList.docList=[]),e.dList.docList.push(e.doc),console.log(e.doc)})):d(this.doc).then((function(t){e.$message.success("修改成功"),console.log(e.doc)})),this.open=!1},delDoc:function(){var e=this;null!=this.doc.docDetailId&&u(this.doc.docDetailId).then((function(t){e.$message.success("删除成功"),e.dList.docList=e.dList.docList.filter((function(t){return t.docDetailId!=e.doc.docDetailId})),e.doc={}}))},cancel:function(){this.open=!1}}},f=p,m=o("2877"),y=Object(m["a"])(f,r,s,!1,null,"5f398ee3",null);t["default"]=y.exports},"3a13":function(e,t,o){"use strict";o.d(t,"e",(function(){return s})),o.d(t,"d",(function(){return n})),o.d(t,"a",(function(){return a})),o.d(t,"f",(function(){return l})),o.d(t,"c",(function(){return c})),o.d(t,"b",(function(){return i}));var r=o("b775");function s(e){return Object(r["a"])({url:"/system/operationalprocess/list",method:"get",params:e})}function n(e){return Object(r["a"])({url:"/system/operationalprocess/"+e,method:"get"})}function a(e){return Object(r["a"])({url:"/system/operationalprocess",method:"post",data:e})}function l(e){return Object(r["a"])({url:"/system/operationalprocess",method:"put",data:e})}function c(e){return Object(r["a"])({url:"/system/operationalprocess/del",method:"post",data:e})}function i(e,t){var o={operationalProcessId:e,status:t};return Object(r["a"])({url:"/system/operationalprocess/changeStatus",method:"put",data:o})}}}]);