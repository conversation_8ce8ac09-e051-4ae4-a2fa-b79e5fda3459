(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d21ad05"],{bcb0:function(t,e,o){"use strict";o.r(e);var l=function(){var t=this,e=t.$createElement,o=t._self._c||e;return o("div",{staticClass:"app-container"},[o("el-row",{attrs:{gutter:20}},[o("el-col",{attrs:{span:t.showLeft}},[o("el-form",{directives:[{name:"show",rawName:"v-show",value:t.showSearch,expression:"showSearch"}],ref:"queryForm",staticClass:"query",attrs:{inline:!0,model:t.queryParams,size:"mini"}},[o("el-form-item",{attrs:{label:"搜索",prop:"locationQuery"}},[o("el-input",{staticStyle:{width:"158px"},on:{change:t.handleQuery},model:{value:t.queryParams.locationQuery,callback:function(e){t.$set(t.queryParams,"locationQuery",e)},expression:"queryParams.locationQuery"}}),o("el-input",{staticStyle:{display:"none"}})],1),o("el-form-item",[o("el-button",{attrs:{icon:"el-icon-search",size:"mini",type:"primary"},on:{click:t.handleQuery}},[t._v("搜索")]),o("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:t.resetQuery}},[t._v("重置")])],1)],1)],1),o("el-col",{attrs:{span:t.showRight}},[o("el-row",{staticClass:"mb8",attrs:{gutter:10}},[o("el-col",{attrs:{span:1.5}},[o("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:location:export"],expression:"['system:location:export']"}],attrs:{icon:"el-icon-download",plain:"",size:"mini",type:"warning"},on:{click:t.handleExport}},[t._v("导出 ")])],1),o("right-toolbar",{attrs:{showSearch:t.showSearch},on:{"update:showSearch":function(e){t.showSearch=e},"update:show-search":function(e){t.showSearch=e},queryTable:t.getList}})],1),o("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],attrs:{data:t.locationList,load:t.loadingLocation,"tree-props":{children:"children",hasChildren:"hasChildren"},lazy:"","row-key":"locationId"}},[o("el-table-column",{attrs:{label:"地区名称",width:"400px"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(e.row.locationShortName)+" "),o("a",{staticStyle:{margin:"0","font-weight":"bold","font-size":"small"}},[t._v(t._s(e.row.locationLocalName))]),t._v(" "+t._s(e.row.locationEnName)+" ")]}}])}),o("el-table-column",{attrs:{align:"center",label:"上级区域",prop:"parentName"}}),o("el-table-column",{attrs:{align:"center",label:"国区代码",prop:"nationCode"}}),o("el-table-column",{attrs:{align:"center",label:"地区代码",prop:"locationCode"}}),o("el-table-column",{attrs:{align:"center",label:"电话区号",prop:"locationPhoneCode"}}),o("el-table-column",{key:"portType",attrs:{align:"center",label:"海港类型"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(null==e.row.portTypeId?"":e.row.portType.portTypeLocalName)+" ")]}}])}),o("el-table-column",{key:"line",attrs:{align:"center",label:"海运航线"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(null==e.row.lineId?"":e.row.line.lineLocalName)+" ")]}}])}),o("el-table-column",{attrs:{align:"center",label:"海运代码",prop:"portCode"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s("Y"==e.row.isPort?"√":"")+" "+t._s(e.row.portCode)+" ")]}}])}),o("el-table-column",{attrs:{align:"center",label:"空运代码",prop:"portIataCode"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s("Y"==e.row.isIataPort?"√":"")+" "+t._s(e.row.portIataCode)+" ")]}}])}),o("el-table-column",{attrs:{align:"center",label:"铁路代码",prop:"portRailCode"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s("Y"==e.row.isRailPort?"√":"")+" "+t._s(e.row.portRailCode)+" ")]}}])}),o("el-table-column",{attrs:{align:"center",label:"地区级别",prop:"locationLevel"}}),o("el-table-column",{attrs:{align:"center",label:"横/纵优先级",prop:"locationPopularity"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(e.row.locationPopularity+"/"+e.row.locationShowpriority)+" ")]}}])}),o("el-table-column",{attrs:{align:"center",label:"状态",prop:"status",width:"50"},scopedSlots:t._u([{key:"default",fn:function(e){return[o("el-switch",{attrs:{"active-value":"0","inactive-value":"1"},on:{change:function(o){return t.handleStatusChange(e.row)}},model:{value:e.row.status,callback:function(o){t.$set(e.row,"status",o)},expression:"scope.row.status"}})]}}])}),o("el-table-column",{attrs:{align:"center","class-name":"small-padding fixed-width",label:"操作",width:"150px"},scopedSlots:t._u([{key:"default",fn:function(e){return[o("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:location:add"],expression:"['system:location:add']"}],staticStyle:{"margin-right":"-8px"},attrs:{icon:"el-icon-plus",size:"mini",type:"primary"},on:{click:function(o){return t.handleAdd(e.row)}}},[t._v("新增 ")]),o("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:location:edit"],expression:"['system:location:edit']"}],staticStyle:{"margin-right":"-8px"},attrs:{icon:"el-icon-edit",size:"mini",type:"success"},on:{click:function(o){return t.handleUpdate(e.row)}}},[t._v("修改 ")]),0!=e.row.parentId?o("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:location:remove"],expression:"['system:location:remove']"}],staticStyle:{"margin-right":"-8px"},attrs:{icon:"el-icon-delete",size:"mini",type:"danger"},on:{click:function(o){return t.handleDelete(e.row)}}},[t._v("删除 ")]):t._e()]}}])})],1)],1)],1),o("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"},{name:"dialogDragWidth",rawName:"v-dialogDragWidth"}],attrs:{"close-on-click-modal":!1,"modal-append-to-body":!1,title:t.title,visible:t.open,"append-to-body":"",width:"600px"},on:{"update:visible":function(e){t.open=e}}},[o("el-form",{ref:"form",staticClass:"edit",attrs:{model:t.form,rules:t.rules,"label-width":"80px"}},[o("el-divider",{attrs:{"content-position":"left"}},[t._v(" 添加地区 ")]),o("el-row",{attrs:{gutter:30}},[o("el-col",{attrs:{span:12}},[o("el-form-item",{attrs:{label:"地区ID"}},[t._v(" "+t._s(t.form.locationId)+" ")])],1),o("el-col",{attrs:{span:12}},[o("el-form-item",{attrs:{label:"上级区域",prop:"parentId"}},[o("location-select",{ref:"location",attrs:{"load-options":t.loadOptions,multiple:!1,pass:t.form.parentId,type:"location"},on:{return:t.ParentId}})],1)],1),o("el-col",{attrs:{span:12}},[o("el-form-item",{attrs:{label:"中文简称",prop:"locationShortName"}},[o("el-input",{attrs:{placeholder:"简称"},model:{value:t.form.locationShortName,callback:function(e){t.$set(t.form,"locationShortName",e)},expression:"form.locationShortName"}})],1)],1),o("el-col",{attrs:{span:12}},[o("el-form-item",{attrs:{label:"中文全称",prop:"locationLocalName"}},[o("el-input",{ref:"input",attrs:{focus:"",placeholder:"地区名"},model:{value:t.form.locationLocalName,callback:function(e){t.$set(t.form,"locationLocalName",e)},expression:"form.locationLocalName"}})],1)],1),o("el-col",{attrs:{span:12}},[o("el-form-item",{attrs:{label:"英文简称",prop:"locationShortName"}},[o("el-input",{attrs:{placeholder:"简称"},model:{value:t.form.locationEnShortName,callback:function(e){t.$set(t.form,"locationEnShortName",e)},expression:"form.locationEnShortName"}})],1)],1),o("el-col",{attrs:{span:12}},[o("el-form-item",{attrs:{label:"英文全称",prop:"locationEnName"}},[o("el-input",{attrs:{placeholder:"英文名"},model:{value:t.form.locationEnName,callback:function(e){t.$set(t.form,"locationEnName",e)},expression:"form.locationEnName"}})],1)],1),o("el-col",{attrs:{span:12}},[o("el-form-item",{attrs:{label:"国区代码",prop:"nationCode"}},[o("el-input",{attrs:{placeholder:"国区代码"},model:{value:t.form.nationCode,callback:function(e){t.$set(t.form,"nationCode",e)},expression:"form.nationCode"}})],1)],1),o("el-col",{attrs:{span:12}},[o("el-form-item",{attrs:{label:"地区代码",prop:"locationCode"}},[o("el-input",{attrs:{placeholder:"地区代码"},model:{value:t.form.locationCode,callback:function(e){t.$set(t.form,"locationCode",e)},expression:"form.locationCode"}})],1)],1),o("el-col",{attrs:{span:12}},[o("el-form-item",{attrs:{label:"电话区号",prop:"locationPhoneCode"}},[o("el-input",{attrs:{placeholder:"电话区号"},model:{value:t.form.locationPhoneCode,callback:function(e){t.$set(t.form,"locationPhoneCode",e)},expression:"form.locationPhoneCode"}})],1)],1),o("el-col",{attrs:{span:12}},[o("el-form-item",{attrs:{label:"地区级别",prop:"locationLevel"}},[o("el-input",{attrs:{placeholder:"地区级别"},model:{value:t.form.locationLevel,callback:function(e){t.$set(t.form,"locationLevel",e)},expression:"form.locationLevel"}})],1)],1),o("el-col",{attrs:{span:12}},[o("el-form-item",{attrs:{label:"横向优先级",prop:"locationPopularity"}},[o("el-input-number",{staticStyle:{width:"100%"},attrs:{controls:!1,placeholder:"地区知名度"},model:{value:t.form.locationPopularity,callback:function(e){t.$set(t.form,"locationPopularity",e)},expression:"form.locationPopularity"}})],1)],1),o("el-col",{attrs:{span:12}},[o("el-form-item",{attrs:{label:"纵向优先级",prop:"locationShowpriority"}},[o("el-input-number",{staticStyle:{width:"100%"},attrs:{controls:!1,placeholder:"地区展示优先级"},model:{value:t.form.locationShowpriority,callback:function(e){t.$set(t.form,"locationShowpriority",e)},expression:"form.locationShowpriority"}})],1)],1)],1),o("el-divider",{attrs:{"content-position":"left"}},[t._v(" 地区性质 ")]),o("el-row",[o("el-col",{attrs:{span:24}},[o("el-form-item",{attrs:{label:"是否港口"}},[o("el-checkbox-button",{staticStyle:{width:"33.3%"},attrs:{label:"Y"==t.form.isPort?"√ 海运港口":"海运港口","true-label":"Y","false-label":"N"},model:{value:t.form.isPort,callback:function(e){t.$set(t.form,"isPort",e)},expression:"form.isPort"}}),o("el-checkbox-button",{staticStyle:{width:"33.3%"},attrs:{label:"Y"==t.form.isIataPort?"√ 空运港口":"空运港口","false-label":"N","true-label":"Y"},model:{value:t.form.isIataPort,callback:function(e){t.$set(t.form,"isIataPort",e)},expression:"form.isIataPort"}}),o("el-checkbox-button",{staticStyle:{width:"33.3%"},attrs:{label:"Y"==t.form.isRailPort?"√ 铁路港口":"铁路港口","false-label":"N","true-label":"Y"},model:{value:t.form.isRailPort,callback:function(e){t.$set(t.form,"isRailPort",e)},expression:"form.isRailPort"}})],1)],1),o("el-col",{attrs:{span:24}},[o("el-form-item",{attrs:{label:"港口代码",prop:"portCode"}},[o("div",{staticStyle:{display:"flex"}},[o("el-input",{attrs:{placeholder:"海运港口代码"},model:{value:t.form.portCode,callback:function(e){t.$set(t.form,"portCode",e)},expression:"form.portCode"}}),o("el-input",{attrs:{placeholder:"空运三字代码"},model:{value:t.form.portIataCode,callback:function(e){t.$set(t.form,"portIataCode",e)},expression:"form.portIataCode"}}),o("el-input",{attrs:{placeholder:"铁路车站代码"},model:{value:t.form.portRailCode,callback:function(e){t.$set(t.form,"portRailCode",e)},expression:"form.portRailCode"}})],1)])],1),o("el-col",{attrs:{span:10}},[o("el-form-item",{attrs:{label:"海运航线",prop:"lineId"}},[o("tree-select",{attrs:{multiple:!1,pass:t.form.lineId,type:"line"},on:{return:t.lineId}})],1)],1),o("el-col",{attrs:{span:20}}),o("el-col",{attrs:{span:10}},[o("el-form-item",{attrs:{label:"海港类型",prop:"portTypeId"}},[o("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"基港/内陆点/未知"},model:{value:t.form.portTypeId,callback:function(e){t.$set(t.form,"portTypeId",e)},expression:"form.portTypeId"}},[o("el-option",{attrs:{label:"基础港口",value:"1"}},[t._v("基础港口")]),o("el-option",{attrs:{label:"内陆点",value:"2"}},[t._v("内陆点")]),o("el-option",{attrs:{label:"未知",value:"3"}},[t._v("未知")])],1)],1)],1),o("el-col",{attrs:{span:24}},[o("el-form-item",{attrs:{label:"备注",prop:"remark"}},[o("el-input",{attrs:{placeholder:"备注"},model:{value:t.form.remark,callback:function(e){t.$set(t.form,"remark",e)},expression:"form.remark"}})],1)],1)],1)],1),o("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[o("el-button",{attrs:{type:"primary"},on:{click:t.submitForm}},[t._v("确 定")]),o("el-button",{on:{click:t.cancel}},[t._v("取 消")])],1)],1)],1)},a=[],n=o("5530"),r=o("c7eb"),i=o("1da1"),s=(o("4ec9"),o("d3b7"),o("3ca3"),o("ddb0"),o("d81d"),o("b1cd")),c=(o("b0b8"),{name:"Location",dicts:["sys_yes_no"],data:function(){return{showLeft:3,showRight:21,loading:!0,ids:[],single:!0,multiple:!0,showSearch:!0,locationList:[],title:"",open:!1,map:new Map,loadOptions:[],queryParams:{locationQuery:null},form:{},rules:{}}},watch:{showSearch:function(t){1==t?(this.showRight=21,this.showLeft=3):(this.showRight=24,this.showLeft=0)}},created:function(){this.getList()},methods:{getList:function(){var t=this;if(this.loading=!0,null==this.queryParams.locationQuery)if(this.map.has("location")){var e=this.map.get("location"),o=e.tree,l=e.treeNode,a=e.resolve;this.loadingLocation(o,l,a).then((function(){t.$nextTick((function(){return t.loading=!1}))}))}else Object(s["e"])(this.queryParams).then((function(e){t.locationList=e.data,t.loading=!1}));else Object(s["f"])(this.queryParams).then((function(e){t.locationList=e.data,t.loading=!1}))},handleStatusChange:function(t){var e=this,o="0"==t.status?"启用":"停用";this.$confirm('确认要"'+o+'""'+t.locationLocalName+'"吗？',"提示",{customClass:"modal-confirm"}).then((function(){return Object(s["b"])(t.locationId,t.status)})).then((function(){e.$modal.msgSuccess(o+"成功")})).catch((function(){t.status="0"==t.status?"1":"0"}))},loadingLocation:function(t,e,o){var l=this;return Object(i["a"])(Object(r["a"])().mark((function a(){return Object(r["a"])().wrap((function(a){while(1)switch(a.prev=a.next){case 0:l.map.set("location",{tree:t,treeNode:e,resolve:o}),Object(s["e"])({locationId:t.locationId}).then((function(t){o(t.data)}));case 2:case"end":return a.stop()}}),a)})))()},ParentId:function(t){this.form.parentId=t},lineId:function(t){this.form.lineId=t},cancel:function(){this.open=!1,this.reset()},reset:function(){this.form={locationId:null,parentId:null,ancestors:null,locationShortName:null,locationEnShortName:null,locationLocalName:null,locationEnName:null,locationCode:null,nationCode:null,locationPhoneCode:null,locationLevel:null,locationPopularity:null,locationShowpriority:null,isPort:"Y",isIataPort:null,isRailPort:null,portTypeId:null,portCode:null,portIataCode:null,portRailCode:null,lineId:null,orderNum:null,remark:null},this.resetForm("form")},handleQuery:function(){this.getList()},resetQuery:function(){this.resetForm("queryForm"),this.handleQuery()},handleAdd:function(t){var e=this;this.reset(),void 0!=t&&(this.form.parentId=t.locationId,this.$nextTick((function(){e.$refs.location.remoteMethod(t.locationLocalName),e.$refs.input.$el.querySelector("input").focus()}))),this.open=!0,this.title="添加地区"},handleUpdate:function(t){var e=this;this.reset();var o=t.locationId||this.ids;Object(s["d"])(o).then((function(t){e.form=t.data,e.open=!0,e.title="修改地区",e.loadOptions=t.locationOptions}))},submitForm:function(){var t=this;this.$refs["form"].validate((function(e){e&&(null!=t.form.locationId?Object(s["h"])(t.form).then((function(e){t.$modal.msgSuccess("修改成功"),t.open=!1,t.getList()})):Object(s["a"])(t.form).then((function(e){t.$modal.msgSuccess("新增成功"),t.getList()})))}))},handleDelete:function(t){var e=this,o=t.locationId||this.ids;this.$confirm('是否确认删除地址编号为"'+o+'"的数据项？',"提示",{customClass:"modal-confirm"}).then((function(){return Object(s["c"])(o)})).then((function(){e.getList(),e.$modal.msgSuccess("删除成功")})).catch((function(){}))},handleExport:function(){this.download("system/location/export",Object(n["a"])({},this.queryParams),"location_".concat((new Date).getTime(),".xlsx"))}}}),u=c,m=o("2877"),p=Object(m["a"])(u,l,a,!1,null,null,null);e["default"]=p.exports}}]);