(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-87504660","chunk-87504660","chunk-bef801f8","chunk-4d660a62","chunk-7c45f1d2","chunk-786477e0","chunk-6ad7debd","chunk-6c498dba","chunk-2e1863d5","chunk-2d0f0472","chunk-2d0dd811","chunk-2d213368","chunk-2d0b6735","chunk-2d0df834","chunk-2d22498d","chunk-2d0be2f9","chunk-2d0ac3fc","chunk-2d0aa9f4","chunk-2d226c7f","chunk-2d0ac5e8","chunk-2d0a385b","chunk-2d0e57d3"],{"0313":function(e,t,a){"use strict";a.r(t);var o=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("el-tooltip",{attrs:{disabled:null==e.scope.row.role||e.scope.row.role.length<15,placement:"top"}},[a("div",{attrs:{slot:"content"},slot:"content"},[a("h6",{staticStyle:{margin:"0"}},[e._v(" "+e._s(e.scope.row.role)+" ")])]),a("div",[a("h6",{staticStyle:{margin:"0",overflow:"hidden","text-overflow":"ellipsis"}},[e._v(" "+e._s(null!=e.scope.row.role?e.scope.row.role.length>15?e.scope.row.role.substring(0,15)+"...":e.scope.row.role:"")+" ")])])])],1)},r=[],n={name:"role",props:["scope"]},s=n,l=a("2877"),i=Object(l["a"])(s,o,r,!1,null,"4f8f6690",null);t["default"]=i.exports},1228:function(e,t,a){"use strict";a("283b")},1231:function(e,t,a){"use strict";a.r(t);var o=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("el-row",[a("el-col",{attrs:{span:e.showLeft}},[e.showSearch?a("el-form",{ref:"queryForm",staticClass:"query",attrs:{inline:!0,model:e.queryParams,size:e.size,"label-width":"35px"}},["1"===e.roleClient?a("el-form-item",{attrs:{label:"所属",prop:"queryStaffId"}},[a("treeselect",{staticStyle:{width:"100%"},attrs:{"disable-branch-nodes":!0,"disable-fuzzy-matching":!0,"flatten-search-results":!0,normalizer:e.staffNormalizer,options:e.belongList,"show-count":!0,placeholder:"所属人"},on:{input:e.cleanBFStaffId,open:e.loadSales,select:e.handleSelectBFStaffId},scopedSlots:e._u([{key:"value-label",fn:function(t){var o=t.node;return a("div",{},[e._v(" "+e._s(void 0!=o.raw.staff?o.raw.staff.staffFamilyLocalName+o.raw.staff.staffGivingLocalName+" "+o.raw.staff.staffGivingEnName:"")+" ")])}},{key:"option-label",fn:function(t){var o=t.node,r=t.shouldShowCount,n=t.count,s=t.labelClassName,l=t.countClassName;return a("label",{class:s},[e._v(" "+e._s(-1!=o.label.indexOf(",")?o.label.substring(0,o.label.indexOf(",")):o.label)+" "),r?a("span",{class:l},[e._v("("+e._s(n)+")")]):e._e()])}}],null,!1,*********),model:{value:e.queryBFStaffId,callback:function(t){e.queryBFStaffId=t},expression:"queryBFStaffId"}})],1):e._e(),"2"===e.roleTypeId?a("el-form-item",{attrs:{label:"所属",prop:"queryStaffId"}},[a("treeselect",{staticStyle:{width:"100%"},attrs:{"disable-branch-nodes":!0,"disable-fuzzy-matching":!0,"flatten-search-results":!0,normalizer:e.staffNormalizer,options:e.businessList,"show-count":!0,placeholder:"所属人"},on:{input:e.cleanBStaffId,open:e.loadBusinesses,select:e.handleSelectBStaffId},scopedSlots:e._u([{key:"value-label",fn:function(t){var o=t.node;return a("div",{},[e._v(" "+e._s(void 0!=o.raw.staff?o.raw.staff.staffFamilyLocalName+o.raw.staff.staffGivingLocalName+" "+o.raw.staff.staffGivingEnName:"")+" ")])}},{key:"option-label",fn:function(t){var o=t.node,r=t.shouldShowCount,n=t.count,s=t.labelClassName,l=t.countClassName;return a("label",{class:s},[e._v(" "+e._s(-1!=o.label.indexOf(",")?o.label.substring(0,o.label.indexOf(",")):o.label)+" "),r?a("span",{class:l},[e._v("("+e._s(n)+")")]):e._e()])}}],null,!1,*********),model:{value:e.queryBStaffId,callback:function(t){e.queryBStaffId=t},expression:"queryBStaffId"}})],1):e._e(),a("el-form-item",{attrs:{label:"客户",prop:"companyQuery"}},[a("el-input",{staticStyle:{width:"100%"},attrs:{clearable:"",placeholder:"客户名称/代码"},on:{change:e.handleQuery},model:{value:e.queryParams.companyQuery,callback:function(t){e.$set(e.queryParams,"companyQuery",t)},expression:"queryParams.companyQuery"}})],1),a("el-form-item",{attrs:{label:"地址",prop:"locationId"}},[a("location-select",{staticStyle:{width:"100%"},attrs:{multiple:!1,pass:e.queryParams.locationId,"load-options":e.locationOptions},on:{return:e.queryLocationId}})],1),a("el-form-item",{attrs:{label:"信用",prop:"creditLevel"}},[a("tree-select",{staticStyle:{width:"100%"},attrs:{multiple:!1,pass:e.queryParams.creditLevel,type:"creditLevel"},on:{return:e.handleQuery}})],1),a("el-form-item",{attrs:{label:"服务",prop:"serviceTypeIds"}},[a("tree-select",{staticStyle:{width:"100%"},attrs:{flat:!1,multiple:!0,pass:e.queryParams.serviceTypeIds,placeholder:"服务类型",type:"serviceType","d-load":!0},on:{return:e.queryServiceTypeIds}})],1),a("el-form-item",{attrs:{label:"启运",prop:"locationDepartureIds"}},[a("location-select",{staticStyle:{width:"100%"},attrs:{multiple:!0,pass:e.queryParams.locationDepartureIds,"load-options":e.locationOptions},on:{return:e.queryLocationDepartureIds}})],1),a("el-form-item",{attrs:{label:"目的",prop:"locationDestinationIds"}},[a("location-select",{staticStyle:{width:"100%"},attrs:{multiple:!0,pass:e.queryParams.locationDestinationIds,en:!0,"load-options":e.locationOptions},on:{return:e.queryLocationDestinationIds}})],1),a("el-form-item",{attrs:{label:"角色",prop:"locationDestinationIds"}},[a("tree-select",{attrs:{flat:!1,multiple:!0,pass:e.queryParams.roleIds,type:"companyRole"},on:{return:e.queryCompanyRoleIds}})],1),a("el-form-item",{attrs:{label:"航线",prop:"lineDestinationIds"}},[a("tree-select",{staticStyle:{width:"100%"},attrs:{flat:!1,multiple:!0,pass:e.queryParams.lineDestinationIds,placeholder:"目的航线",type:"line","d-load":!0},on:{return:e.queryLineDestinationIds}})],1),a("el-form-item",{attrs:{label:"货物",prop:"cargoTypeIds"}},[a("tree-select",{staticStyle:{width:"100%"},attrs:{flat:!1,multiple:!0,pass:e.queryParams.cargoTypeIds,placeholder:"货物特征","d-load":!0,type:"cargoType"},on:{return:e.queryCargoTypeIds}})],1),a("el-form-item",{attrs:{label:"承运",prop:"carrierIds"}},[a("treeselect",{staticStyle:{width:"100%"},attrs:{"disable-fuzzy-matching":!0,"disable-branch-nodes":!0,flat:!0,"flatten-search-results":!0,multiple:!0,normalizer:e.carrierNormalizer,options:e.carrierList,"show-count":!0,placeholder:"优选承运人"},on:{open:e.loadCarrier,deselect:e.handleDeselectQueryCarrierIds,select:e.handleSelectQueryCarrierIds},scopedSlots:e._u([{key:"value-label",fn:function(t){var o=t.node;return a("div",{},[e._v(" "+e._s(o.raw.carrier.carrierIntlCode)+" "+e._s(null==o.raw.carrier.carrierIntlCode?o.raw.carrier.carrierShortName:"")+" ")])}},{key:"option-label",fn:function(t){var o=t.node,r=t.shouldShowCount,n=t.count,s=t.labelClassName,l=t.countClassName;return a("label",{class:s},[e._v(" "+e._s(-1!=o.label.indexOf(",")?o.label.substring(0,o.label.indexOf(",")):o.label)+" "),r?a("span",{class:l},[e._v("("+e._s(n)+")")]):e._e()])}}],null,!1,4006291921),model:{value:e.queryCarrierIds,callback:function(t){e.queryCarrierIds=t},expression:"queryCarrierIds"}})],1),a("el-form-item",{attrs:{label:"评级",prop:"creditLevel"}},[a("el-input",{staticStyle:{width:"100%"},attrs:{placeholder:"A~E"},on:{change:e.handleQuery},model:{value:e.queryParams.creditLevel,callback:function(t){e.$set(e.queryParams,"creditLevel",t)},expression:"queryParams.creditLevel"}})],1),a("el-form-item",[a("el-button",{attrs:{size:e.size,icon:"el-icon-search",type:"primary"},on:{click:e.handleQuery}},[e._v("搜索")]),a("el-button",{attrs:{size:e.size,icon:"el-icon-refresh"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1):e._e()],1),a("el-col",{attrs:{span:e.showRight}},[a("el-row",{staticClass:"mb8",attrs:{gutter:10}},[a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:company:add"],expression:"['system:company:add']"}],attrs:{size:e.size,icon:"el-icon-plus",plain:"",type:"primary"},on:{click:e.handleAdd}},[e._v("新增 ")])],1),a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:company:export"],expression:"['system:company:export']"}],attrs:{size:e.size,icon:"el-icon-download",plain:"",type:"warning"},on:{click:e.handleExport}},[e._v("导出 ")])],1),a("el-col",{attrs:{span:1.5}},[a("el-button",{attrs:{disabled:e.single,size:e.size,icon:"el-icon-user-solid",type:"info"},on:{click:e.handleBlackList}},[e._v(" 拉黑 ")])],1),a("el-col",{attrs:{span:1.5}},[a("el-button",{attrs:{disabled:e.selectTwo,size:e.size,icon:"el-icon-connection"},on:{click:e.handleMergeCompany}},[e._v(" 合并 ")])],1),a("right-toolbar",{attrs:{columns:e.columns,showSearch:e.showSearch,types:"2"==this.roleTypeId?"supplier":"1"==this.roleTypeId?"client":""},on:{"update:showSearch":function(t){e.showSearch=t},"update:show-search":function(t){e.showSearch=t},queryTable:e.getList,refreshColumns:e.refreshColumns}})],1),a("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"},{name:"dialogDragWidth",rawName:"v-dialogDragWidth"}],attrs:{"close-on-click-modal":!1,"modal-append-to-body":!1,title:"选择保留公司",visible:e.merge,width:"800px"},on:{"update:visible":function(t){e.merge=t}}},[e.mergeList.length>0?a("el-row",{attrs:{gutter:5}},[a("el-col",{attrs:{span:12}},[a("el-descriptions",{attrs:{title:"公司1",direction:"vertical",border:""}},[a("el-descriptions-item",{attrs:{label:"简称"}},[e._v(e._s(e.mergeList[0].companyShortName))]),a("el-descriptions-item",{attrs:{label:"中文名"}},[e._v(e._s(e.mergeList[0].companyLocalName))]),a("el-descriptions-item",{attrs:{label:"英文名"}},[e._v(e._s(e.mergeList[0].companyEnName))]),a("el-descriptions-item",{attrs:{label:"选择"}},[a("el-button",{on:{click:function(t){return e.handleMerge(e.mergeList[0].companyId,e.mergeList[1].companyId)}}},[e._v(" 留下"+e._s(e.mergeList[0].companyShortName)+" ")])],1)],1)],1),a("el-col",{attrs:{span:12}},[a("el-descriptions",{attrs:{title:"公司2",direction:"vertical",border:""}},[a("el-descriptions-item",{attrs:{label:"简称"}},[e._v(e._s(e.mergeList[1].companyShortName))]),a("el-descriptions-item",{attrs:{label:"中文名"}},[e._v(e._s(e.mergeList[1].companyLocalName))]),a("el-descriptions-item",{attrs:{label:"英文名"}},[e._v(e._s(e.mergeList[1].companyEnName))]),a("el-descriptions-item",{attrs:{label:"选择"}},[a("el-button",{on:{click:function(t){return e.handleMerge(e.mergeList[1].companyId,e.mergeList[0].companyId)}}},[e._v(" 留下"+e._s(e.mergeList[1].companyShortName)+" ")])],1)],1)],1)],1):e._e()],1),e.refreshTable?a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.companyList,border:""},on:{"selection-change":e.handleSelectionChange}},[a("el-table-column",{attrs:{type:"selection",width:"28",align:"center"}}),e._l(e.columns,(function(t){return t.visible?a("el-table-column",{key:t.key,attrs:{align:t.align,label:t.label,width:t.width,"show-overflow-tooltip":t.tooltip},scopedSlots:e._u([{key:"default",fn:function(o){return[a(t.prop,{tag:"component",attrs:{scope:o},on:{return:e.getReturn}})]}}],null,!0)}):e._e()})),a("el-table-column",{attrs:{align:"center",label:"结款方式",width:"70"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("h6",{staticStyle:{margin:"0"}},[e._v(e._s())])]}}],null,!1,*********)}),a("el-table-column",{attrs:{align:"center",label:"额度",width:"70"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",{staticStyle:{margin:"0"}},[e._v(e._s(1==e.roleClient||1==e.roleRich?t.row.receiveCurrencyCode:t.row.payCurrencyCode))]),a("span",{staticStyle:{margin:"0"}},[e._v(e._s(e.formatDisplayCreditLimit(1==e.roleClient||1==e.roleRich?t.row.receiveCreditLimit:t.row.payCreditLimit)))])]}}],null,!1,1157879669)}),a("el-table-column",{attrs:{align:"center",label:"录入人","show-overflow-tooltip":"",width:"70"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("div",[a("h6",{staticClass:"text-display",staticStyle:{margin:"0"}},[e._v(e._s(t.row.updateByName))]),a("h6",{staticClass:"text-display",staticStyle:{margin:"0"}},[e._v(e._s(e.parseTime(t.row.updateTime,"{y}.{m}.{d}")))])])]}}],null,!1,2957631882)}),a("el-table-column",{attrs:{align:"center","class-name":"small-padding fixed-width",label:"操作",width:"90px"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:company:edit"],expression:"['system:company:edit']"}],staticStyle:{margin:"0 3px"},attrs:{size:e.size,icon:"el-icon-edit",type:"success"},on:{click:function(a){return e.handleUpdate(t.row)}}},[e._v("修改 ")]),a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:company:remove"],expression:"['system:company:remove']"}],staticStyle:{margin:"0"},attrs:{size:e.size,icon:"el-icon-delete",type:"danger"},on:{click:function(a){return e.handleDelete(t.row)}}},[e._v("删除 ")])]}}],null,!1,1664289417)})],2):e._e(),a("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total>0"}],attrs:{limit:e.queryParams.pageSize,page:e.queryParams.pageNum,total:e.total},on:{"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},pagination:e.getList}})],1)],1),a("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"},{name:"dialogDragWidth",rawName:"v-dialogDragWidth"}],attrs:{"close-on-click-modal":!1,"append-to-body":"",title:e.title,visible:e.openCompany,width:"55%"},on:{"update:visible":function(t){e.openCompany=t}}},[a("el-form",{ref:"form",staticClass:"edit",attrs:{model:e.form,rules:e.rules,"label-width":"68px"}},[a("el-row",{attrs:{gutter:5}},[a("el-col",{attrs:{span:12}},["1"===e.roleClient?a("el-row",{attrs:{gutter:5}},[a("el-divider",{attrs:{"content-position":"left"}},[e._v("从属信息")]),a("el-row",{attrs:{gutter:5}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"业务员"}},[a("treeselect",{staticClass:"sss",class:e.isLock?"disable-form":"",attrs:{"disable-branch-nodes":!0,"disable-fuzzy-matching":!0,disabled:e.isLock,"flatten-search-results":!0,normalizer:e.staffNormalizer,options:e.belongList,"show-count":!0,placeholder:"选择所属人"},on:{input:e.handleDeselectBelongTo,open:e.loadSales,select:e.handleSelectBelongTo},scopedSlots:e._u([{key:"value-label",fn:function(t){var o=t.node;return a("div",{},[e._v(" "+e._s(void 0!=o.raw.staff?o.raw.staff.staffFamilyLocalName+o.raw.staff.staffGivingLocalName+" "+o.raw.staff.staffGivingEnName:"")+" ")])}},{key:"option-label",fn:function(t){var o=t.node,r=t.shouldShowCount,n=t.count,s=t.labelClassName,l=t.countClassName;return a("label",{class:s},[e._v(" "+e._s(-1!=o.label.indexOf(",")?o.label.substring(0,o.label.indexOf(",")):o.label)+" "),r?a("span",{class:l},[e._v("("+e._s(n)+")")]):e._e()])}}],null,!1,*********),model:{value:e.belongTo,callback:function(t){e.belongTo=t},expression:"belongTo"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"业务助理"}},[a("treeselect",{staticClass:"sss",class:e.isLock?"disable-form":"",attrs:{"disable-branch-nodes":!0,"disable-fuzzy-matching":!0,"flatten-search-results":!0,normalizer:e.staffNormalizer,options:e.belongList,"show-count":!0,disabled:e.isLock,placeholder:"业务员自己跟进的情况无须填写"},on:{input:e.handleDeselectFollowUp,open:e.loadSales,select:e.handleSelectFollowUp},scopedSlots:e._u([{key:"value-label",fn:function(t){var o=t.node;return a("div",{},[e._v(" "+e._s(void 0!=o.raw.staff?o.raw.staff.staffFamilyLocalName+o.raw.staff.staffGivingLocalName+" "+o.raw.staff.staffGivingEnName:"")+" ")])}},{key:"option-label",fn:function(t){var o=t.node,r=t.shouldShowCount,n=t.count,s=t.labelClassName,l=t.countClassName;return a("label",{class:s},[e._v(" "+e._s(-1!=o.label.indexOf(",")?o.label.substring(0,o.label.indexOf(",")):o.label)+" "),r?a("span",{class:l},[e._v("("+e._s(n)+")")]):e._e()])}}],null,!1,*********),model:{value:e.followUp,callback:function(t){e.followUp=t},expression:"followUp"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"所属公司"}},[a("tree-select",{class:e.isLock?"disable-form":"",attrs:{pass:e.form.companyBelongTo,placeholder:"收付路径",disabled:e.isLock,type:"rsPaymentTitle"},on:{return:function(t){e.form.companyBelongTo=t}}})],1)],1)],1)],1):e._e(),a("el-row",[a("el-divider",{attrs:{"content-position":"left"}},[e._v("基础资料")]),a("el-row",[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"公司代码",prop:"companyTaxCode"}},[a("el-input",{staticClass:" disable-form",class:"sss",attrs:{disabled:"",placeholder:"国际通用简称"},model:{value:e.form.companyTaxCode,callback:function(t){e.$set(e.form,"companyTaxCode",t)},expression:"form.companyTaxCode"}})],1)],1),a("el-col",{attrs:{span:16}},[a("el-form-item",{attrs:{label:"税号",prop:"companyTaxCode"}},[a("el-input",{class:e.basicLock||e.commonLock?"disable-form":"",attrs:{disabled:e.basicLock||e.commonLock,placeholder:"公司税号/统一社会信用代码"},model:{value:e.form.taxNo,callback:function(t){e.$set(e.form,"taxNo",t)},expression:"form.taxNo"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{rules:[{required:!0,message:"简称不能为空",trigger:"blur"},{validator:e.validateCompanyShortName,trigger:"blur"}],label:"公司简称",prop:"companyShortName"}},[a("el-input",{staticClass:"sss",class:e.basicLock||e.commonLock?"disable-form":"",attrs:{disabled:e.basicLock||e.commonLock,placeholder:"公司简称"},model:{value:e.form.companyShortName,callback:function(t){e.$set(e.form,"companyShortName",t)},expression:"form.companyShortName"}})],1)],1),a("el-col",{attrs:{span:16}},[a("el-form-item",{attrs:{rules:[{required:!0,message:"抬头不能为空",trigger:"blur"}],label:"发票抬头",prop:"companyLocalName"}},[a("el-input",{class:e.basicLock||e.commonLock?"disable-form":"",attrs:{disabled:e.basicLock||e.commonLock,placeholder:"公司发票抬头"},model:{value:e.form.companyLocalName,callback:function(t){e.$set(e.form,"companyLocalName",t)},expression:"form.companyLocalName"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"行政区域",prop:"locationId"}},[a("location-select",{staticClass:"sss",attrs:{"load-options":e.locationOptions,multiple:!1,pass:e.form.locationId},on:{return:e.getLocationId}})],1)],1),a("el-col",{attrs:{span:16}},[a("el-form-item",{attrs:{label:"详细地址",prop:"locationDetail"}},[a("el-input",{attrs:{placeholder:"详细地址信息"},model:{value:e.form.locationDetail,callback:function(t){e.$set(e.form,"locationDetail",t)},expression:"form.locationDetail"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"主联系人",prop:"mainStaffOfficialName"}},[a("el-input",{staticClass:"sss",attrs:{placeholder:"联系人名称"},model:{value:e.form.mainStaffOfficialName,callback:function(t){e.$set(e.form,"mainStaffOfficialName",t)},expression:"form.mainStaffOfficialName"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"微信",prop:"staffWechat"}},[a("el-input",{staticClass:"sss",attrs:{placeholder:"联系人微信"},model:{value:e.form.staffWechat,callback:function(t){e.$set(e.form,"staffWechat",t)},expression:"form.staffWechat"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"QQ",prop:"staffQq"}},[a("el-input",{attrs:{placeholder:"联系人QQ"},model:{value:e.form.staffQq,callback:function(t){e.$set(e.form,"staffQq",t)},expression:"form.staffQq"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"手机",prop:"staffMobile"}},[a("el-input",{staticClass:"sss",attrs:{placeholder:"手机"},model:{value:e.form.staffMobile,callback:function(t){e.$set(e.form,"staffMobile",t)},expression:"form.staffMobile"}})],1)],1),a("el-col",{attrs:{span:16}},[a("el-form-item",{attrs:{label:"邮箱",prop:"staffEmail"}},[a("el-input",{attrs:{placeholder:"联系人邮箱"},model:{value:e.form.staffEmail,callback:function(t){e.$set(e.form,"staffEmail",t)},expression:"form.staffEmail"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"Whatsapp",prop:"staffWhatsapp"}},[a("el-input",{staticClass:"sss",attrs:{placeholder:"联系人Whatsapp"},model:{value:e.form.staffWhatsapp,callback:function(t){e.$set(e.form,"staffWhatsapp",t)},expression:"form.staffWhatsapp"}})],1)],1),a("el-col",{attrs:{span:16}},[a("el-form-item",{attrs:{label:"联系方式",prop:"staffOtherContact"}},[a("el-input",{attrs:{placeholder:"员工其他联系方式"},model:{value:e.form.staffOtherContact,callback:function(t){e.$set(e.form,"staffOtherContact",t)},expression:"form.staffOtherContact"}})],1)],1)],1)],1),a("el-row",[a("el-divider",{attrs:{"content-position":"left"}},[e._v("协议信息")]),a("el-row",[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"协议号",prop:"agreementNumber"}},[a("el-input",{staticClass:"sss",class:e.agreementLock||e.commonLock?"disable-form":"",attrs:{disabled:e.agreementLock||e.commonLock,placeholder:"协议号"},model:{value:e.form.agreementNumber,callback:function(t){e.$set(e.form,"agreementNumber",t)},expression:"form.agreementNumber"}})],1)],1),a("el-col",{attrs:{span:16}},[a("el-form-item",{attrs:{label:"起讫日期",prop:"agreementStartDate"}},[a("el-date-picker",{staticClass:"sss",class:e.agreementLock||e.commonLock?"disable-form":"",staticStyle:{width:"100%"},attrs:{disabled:e.agreementLock||e.commonLock,"end-placeholder":"结束日期","range-separator":"至","start-placeholder":"开始日期","default-time":["00:00:00","23:59:59"],type:"daterange","value-format":"yyyy-MM-dd HH:mm:ss"},on:{input:e.changeDate},model:{value:e.form.agreementDateRange,callback:function(t){e.$set(e.form,"agreementDateRange",t)},expression:"form.agreementDateRange"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"信用等级",prop:"creditLevel"}},[a("tree-select",{staticClass:"sss",class:e.agreementLock||e.commonLock?"disable-form":"",attrs:{disabled:e.agreementLock||e.commonLock,pass:e.form.creditLevel,type:"creditLevel"},on:{return:e.getcreditLevel}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:1==e.roleClient||1==e.roleRich?"收款方式":1==e.roleSupplier||1==e.roleSupport?"付款方式":"",prop:"creditLevel"}},[1==e.roleClient||1==e.roleRich?a("tree-select",{staticClass:"sss",class:e.agreementLock||e.commonLock?"disable-form":"",attrs:{disabled:e.agreementLock||e.commonLock,multiple:!1,pass:e.form.receiveWay,placeholder:"收款方式",flat:!1,type:"releaseTypeCode"},on:{returnData:function(t){e.form.receiveWay=t.releaseTypeShortName}}}):e._e(),1==e.roleSupplier||1==e.roleSupport?a("tree-select",{staticClass:"sss",class:e.agreementLock||e.commonLock?"disable-form":"",attrs:{flat:!1,multiple:!1,disabled:e.agreementLock||e.commonLock,pass:e.form.payWay,placeholder:"付款方式",type:"releaseTypeCode"},on:{returnData:function(t){e.form.payWay=t.releaseTypeShortName}}}):e._e()],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:1==e.roleClient||1==e.roleRich?"收款信用":1==e.roleSupplier||1==e.roleSupport?"付款信用":"",prop:"creditLevel"}},[1==e.roleClient||1==e.roleRich?a("el-col",[a("el-col",{attrs:{span:9}},[a("tree-select",{staticClass:" currency",class:e.agreementLock||e.commonLock?"disable-form":"",attrs:{disabled:e.agreementLock||e.commonLock,pass:e.form.receiveCurrencyCode,type:"currency"},on:{return:function(t){e.form.receiveCurrencyCode=t}}})],1),a("el-col",{attrs:{span:15}},[a("el-input",{staticClass:" limit",class:e.agreementLock||e.commonLock?"disable-form":"",attrs:{disabled:e.agreementLock||e.commonLock,placeholder:"信用额度(填入整数)"},on:{change:e.formatCreditLimit},model:{value:e.form.receiveCreditLimit,callback:function(t){e.$set(e.form,"receiveCreditLimit",t)},expression:"form.receiveCreditLimit"}})],1)],1):e._e(),1==e.roleSupplier||1==e.roleSupport?a("el-col",[a("el-col",{attrs:{span:9}},[a("tree-select",{staticClass:" currency",class:e.agreementLock||e.commonLock?"disable-form":"",attrs:{disabled:e.agreementLock||e.commonLock,pass:e.form.payCurrencyCode,type:"currency"},on:{return:function(t){e.form.payCurrencyCode=t}}})],1),a("el-col",{attrs:{span:15}},[a("el-input",{staticClass:" limit",class:e.agreementLock||e.commonLock?"disable-form":"",attrs:{disabled:e.agreementLock||e.commonLock,placeholder:"信用额度(填入整数)"},on:{change:e.formatCreditLimit},model:{value:e.form.payCreditLimit,callback:function(t){e.$set(e.form,"payCreditLimit",t)},expression:"form.payCreditLimit"}})],1)],1):e._e()],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"账单基准",prop:"creditLevel"}},[1==e.roleClient||1==e.roleRich?a("tree-select",{staticClass:"sss",class:e.agreementLock||e.commonLock?"disable-form":"",attrs:{disabled:e.agreementLock||e.commonLock,flat:!1,multiple:!1,pass:e.form.receiveStandard,placeholder:"收款账单基准",type:"commonInfoOfTime"},on:{return:function(t){e.form.receiveStandard=t},returnData:function(t){e.localStandard=t.infoLocalName}}}):e._e(),1==e.roleSupplier||1==e.roleSupport?a("tree-select",{staticClass:"sss",class:e.agreementLock||e.commonLock?"disable-form":"",attrs:{disabled:e.agreementLock||e.commonLock,flat:!1,multiple:!1,pass:e.form.payStandard,placeholder:"付款账单基准",type:"commonInfoOfTime"},on:{returnData:function(t){e.form.payStandard=t.infoShortName}}}):e._e()],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{class:e.agreementLock||e.commonLock?"disable-form":"",attrs:{label:1==e.roleClient||1==e.roleRich?"收款期限":1==e.roleSupplier||1==e.roleSupport?"付款期限":"",disabled:e.agreementLock||e.commonLock,prop:"agreementNumber"}},[1==e.roleClient||1==e.roleRich?a("el-input",{staticClass:"sss",class:e.agreementLock||e.commonLock?"disable-form":"",attrs:{disabled:e.agreementLock||e.commonLock,placeholder:"收款期限(填写±n)"},on:{change:e.handleTermChange},model:{value:e.form.receiveTerm,callback:function(t){e.$set(e.form,"receiveTerm",t)},expression:"form.receiveTerm"}}):e._e(),1==e.roleSupplier||1==e.roleSupport?a("el-input",{staticClass:"sss",class:e.agreementLock||e.commonLock?"disable-form":"",attrs:{disabled:e.agreementLock||e.commonLock,placeholder:"付款期限(填写±n)"},on:{change:e.handleTermChange},model:{value:e.form.payTerm,callback:function(t){e.$set(e.form,"payTerm",t)},expression:"form.payTerm"}}):e._e()],1)],1),a("el-col",{attrs:{span:8}},[a("el-input",{staticClass:"disable-form",attrs:{value:e.description,disabled:""}})],1)],1),a("el-row",[a("el-form-item",{attrs:{label:"协议备注",prop:"agreementRemark"}},[a("el-input",{class:e.agreementLock||e.commonLock?"disable-form":"",attrs:{disabled:e.agreementLock||e.commonLock,placeholder:"协议备注"},model:{value:e.form.agreementRemark,callback:function(t){e.$set(e.form,"agreementRemark",t)},expression:"form.agreementRemark"}})],1)],1)],1),e.showConfirm?a("el-row",[a("el-divider",{attrs:{"content-position":"left"}},[e._v(" 审核意见 ")]),a("el-row",[1==this.roleClient?a("el-col",{attrs:{span:4}},[a("confirmed",{attrs:{id:"companyId",confirmed:0==this.form.salesConfirmed,row:e.form,type:"sales"},on:{lockMethod:e.updateCompany}})],1):e._e(),1==this.roleSupplier?a("el-col",{attrs:{span:4}},[a("confirmed",{attrs:{id:"companyId",confirmed:0==this.form.psaConfirmed,row:e.form,type:"psa"},on:{lockMethod:e.updateCompany}})],1):e._e(),a("el-col",{attrs:{span:4}},[a("confirmed",{attrs:{id:"companyId",confirmed:0==this.form.opConfirmed,row:e.form,type:"op"},on:{lockMethod:e.updateCompany}})],1),a("el-col",{attrs:{span:4}},[a("confirmed",{attrs:{id:"companyId",confirmed:0==this.form.accConfirmed,row:e.form,type:"acc"},on:{lockMethod:e.updateCompany}})],1)],1)],1):e._e()],1),a("el-col",{attrs:{span:12}},[1==e.roleClient?a("el-row",[a("el-divider",{attrs:{"content-position":"left"}},[e._v(" 开发分类 ")]),a("el-row",[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"客户来源"}},[a("tree-select",{staticClass:"sss",attrs:{pass:e.form.sourceId,type:"source"},on:{returnData:e.getSourceId}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"开发权重"}},[a("el-input",{staticClass:"sss",attrs:{placeholder:"重要程度"},model:{value:e.form.developmentWeight,callback:function(t){e.$set(e.form,"developmentWeight",t)},expression:"form.developmentWeight"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"业务标记",prop:"salesRemark"}},[a("el-input",{staticClass:"sss",attrs:{placeholder:"重要程度"},model:{value:e.form.salesRemark,callback:function(t){e.$set(e.form,"salesRemark",t)},expression:"form.salesRemark"}})],1)],1)],1)],1):e._e(),a("el-row",[a("el-divider",{attrs:{"content-position":"left"}},[e._v(" 物流分类 ")]),a("el-row",[a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"公司角色",prop:"roleIds"}},[a("tree-select",{staticClass:"sss",attrs:{flat:!1,multiple:!0,pass:e.form.roleIds,type:"companyRole"},on:{return:e.getCompanyRoleIds}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"所属组织",prop:"organizationIds"}},[a("tree-select",{staticClass:"sss",attrs:{multiple:!0,pass:e.form.organizationIds,type:"organization"},on:{return:e.getOrganizationIds}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"服务类型",prop:"serviceTypeIds"}},[a("tree-select",{staticClass:"sss",attrs:{flat:!1,multiple:!0,pass:e.form.serviceTypeIds,type:"serviceType"},on:{return:e.getServiceTypeIds}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"货物类型",prop:"cargoTypeIds"}},[a("tree-select",{staticClass:"sss",attrs:{flat:!1,multiple:!0,pass:e.form.cargoTypeIds,type:"cargoType"},on:{return:e.getCargoTypeIds}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"启运区域",prop:"locationDepartureIds"}},[a("location-select",{staticClass:"sss",attrs:{"load-options":e.locationOptions,multiple:!0,pass:e.form.locationDepartureIds},on:{return:e.getLocationDepartureIds}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"优选承运",prop:"carrierIds"}},[a("treeselect",{staticClass:"sss",attrs:{"disable-branch-nodes":!0,"disable-fuzzy-matching":!0,flat:!0,"flatten-search-results":!0,multiple:!0,normalizer:e.carrierNormalizer,options:e.temCarrierList,"show-count":!0,placeholder:"选择承运人"},on:{deselect:e.handleDeselectCarrierIds,open:e.loadCarrier,select:e.handleSelectCarrierIds},scopedSlots:e._u([{key:"value-label",fn:function(t){var o=t.node;return a("div",{},[e._v(" "+e._s(o.raw.carrier.carrierIntlCode)+" "+e._s(null==o.raw.carrier.carrierIntlCode?o.raw.carrier.carrierShortName:"")+" ")])}},{key:"option-label",fn:function(t){var o=t.node,r=t.shouldShowCount,n=t.count,s=t.labelClassName,l=t.countClassName;return a("label",{class:s},[e._v(" "+e._s(-1!=o.label.indexOf(",")?o.label.substring(0,o.label.indexOf(",")):o.label)+" "),r?a("span",{class:l},[e._v("("+e._s(n)+")")]):e._e()])}}]),model:{value:e.carrierIds,callback:function(t){e.carrierIds=t},expression:"carrierIds"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"目的区域",prop:"locationDestinationIds"}},[a("location-select",{staticClass:"sss",attrs:{en:!0,"load-options":e.locationOptions,multiple:!0,pass:e.form.locationDestinationIds},on:{return:e.getLocationDestinationIds}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"目的航线",prop:"lineDestinationIds"}},[a("tree-select",{staticClass:"sss",attrs:{flat:!1,multiple:!0,pass:e.form.lineDestinationIds,type:"line"},on:{return:e.getLineDestinationIds}})],1)],1)],1),a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"备注信息",prop:"remark"}},[a("el-input",{staticClass:"sss",attrs:{placeholder:"备注信息"},model:{value:e.form.remark,callback:function(t){e.$set(e.form,"remark",t)},expression:"form.remark"}})],1)],1)],1)],1),a("el-row",[a("el-divider",{attrs:{"content-position":"left"}},[e._v(" 客户习惯 ")]),a("el-col",{attrs:{span:24}},[a("el-input",{attrs:{autosize:{minRows:15,maxRows:10},maxlength:"150",placeholder:"备注","show-word-limit":"",type:"textarea"},model:{value:e.form.partnerHabit,callback:function(t){e.$set(e.form,"partnerHabit",t)},expression:"form.partnerHabit"}})],1)],1)],1)],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("确 定")]),e.edit?e._e():a("el-button",{attrs:{size:e.size,type:"primary"},on:{click:e.querySame}},[e._v("查重")]),a("el-button",{on:{click:e.cancel}},[e._v("取 消")])],1)],1),a("div",[a("staff-info",{attrs:{company:e.companyInfo,"load-options":e.staffList,open:e.openStaff},on:{openStaffs:e.cancel}}),a("account-info",{attrs:{company:e.companyInfo,"is-lock":e.isLock,"load-options":e.accountList,open:e.openAccount,type:"company"},on:{openAccounts:e.accountCancel}}),a("communications",{attrs:{company:e.companyInfo,"load-options":e.communicationList,open:e.openCommunication,totle:e.ctotle},on:{openCommunications:e.cancel}}),a("agreement-record",{attrs:{company:e.companyInfo,"load-options":e.agreementList,open:e.openAgreement,totle:e.atotle},on:{openCommunications:e.cancel}}),a("BlackList",{attrs:{company:e.companyInfo,open:e.openBlackList},on:{openBlackList:e.cancel}})],1)],1)},r=[],n=a("c7eb"),s=a("5530"),l=a("1da1"),i=a("b85c"),c=(a("caad"),a("2532"),a("14d9"),a("a4d3"),a("e01a"),a("d3b7"),a("25f0"),a("b0c0"),a("d9e2"),a("d81d"),a("ac1f"),a("5319"),a("4de4"),a("aff7")),m=a("3528"),d=a("0503"),f=a("998b"),u=a("3964"),p=a("b0b8"),h=a.n(p),y=a("4360"),b=a("ca17"),g=a.n(b),v=(a("6f8d"),a("388a")),w=a("59e5"),C=a("e0aa"),k=a("8f75"),I=a("e18c"),S=a("a603"),L=a("e9b7"),_=a("0313"),x=a("9567"),N=a("2ed4"),D=a("1975"),j=a("1ce2"),T=a("89da"),$=a("9c4f"),O=a("ac5e"),P=a("c06f"),z=a("1296"),q=a("8256"),E=a("18f1"),R=a("c747"),A=a("dce4"),B=a("fba1"),F=a("5c96"),W=a("37ff"),Q=a("1401"),U=a("9dd5"),M=a("e350"),G={name:"Company",dicts:["sys_is_idle"],components:{AccountInfo:U["default"],Confirmed:W["a"],Treeselect:g.a,communication:P["default"],communications:w["default"],BlackList:v["a"],belong:R["default"],company:I["default"],contactor:S["default"],staffInfo:k["default"],location:L["default"],role:_["default"],serviceType:x["default"],departure:N["default"],destination:D["default"],cargoType:j["default"],carrier:T["default"],account:$["default"],agreement:O["default"],agreementRecord:C["default"],grade:z["default"],achievement:q["default"],remark:E["default"],rsPaymentTitle:Q["default"]},props:["roleTypeId","roleRich","roleClient","roleSupplier","roleSupport"],data:function(){return{loading:!0,showLeft:3,showRight:21,single:!0,multiple:!0,ids:[],showSearch:!0,total:0,add:!1,selectTwo:!0,size:this.$store.state.app.size||"mini",mergeList:[],companyList:[],staffList:[],accountList:[],communicationList:[],agreementList:[],belongList:[],carrierList:[],businessList:[],temCarrierList:[],locationOptions:[],carrierIds:[],companyInfo:{},queryCarrierIds:[],title:"",merge:!1,openCompany:!1,openStaff:!1,openAccount:!1,openCommunication:!1,openAgreement:!1,openBlackList:!1,edit:!1,belongTo:null,followUp:null,queryBFStaffId:null,queryBStaffId:null,refreshTable:!0,ctotle:null,atotle:null,queryParams:{pageNum:1,pageSize:20,roleRich:this.roleRich?this.roleRich:null,roleClient:this.roleClient?this.roleClient:null,roleSupplier:this.roleSupplier?this.roleSupplier:null,roleSupport:this.roleSupport?this.roleSupport:null,companyQuery:null,locationId:null,idleStatus:null,queryStaffId:null,showPriority:null,serviceTypeIds:[],cargoTypeIds:[],locationDepartureIds:[],lineDepartureIds:[],locationDestinationIds:[],lineDestinationIds:[],roleIds:[],carrierIds:[]},form:{agreementStartDate:null,agreementEndDate:null,settlementDate:null},rules:{},companyRow:null,isLock:!0,showConfirm:!1,localStandard:null,description:null}},computed:{columns:{get:function(){return"2"==this.roleTypeId?this.$store.state.listSettings.supplierSetting:"1"==this.roleTypeId||this.roleClient?this.$store.state.listSettings.clientSetting:this.roleSupplier||this.roleRich||this.roleSupport?this.$store.state.listSettings.supplierSetting:void 0}},commonLock:function(){return 1==this.form.psaConfirmed||1==this.form.salesConfirmed},basicLock:function(){return 1==this.form.opConfirmed},agreementLock:function(){return 1==this.form.accConfirmed}},watch:{showSearch:function(e){1==e?(this.showRight=21,this.showLeft=3):(this.showRight=24,this.showLeft=0)},queryStaffId:function(){this.queryParams.queryStaffId=this.queryStaffId},"form.belongTo":function(){this.form.belongTo==this.form.followUp&&(this.form.followUp=0,this.followUp=null)},"form.serviceTypeIds":function(e){this.loadCarrier();var t=[];if(void 0!=this.carrierList&&null!=e&&e.includes(-1)){this.temCarrierList=this.carrierList;var a,o=Object(i["a"])(this.carrierList);try{for(o.s();!(a=o.n()).done;){var r=a.value;if(void 0!=r.children&&r.children.length>0){var n,s=Object(i["a"])(r.children);try{for(s.s();!(n=s.n()).done;){var l=n.value;if(void 0!=l.children&&l.children.length>0){var c,m=Object(i["a"])(l.children);try{for(m.s();!(c=m.n()).done;){var d=c.value;null!=this.form.carrierIds&&this.form.carrierIds.includes(d.carrier.carrierId)&&!this.carrierIds.includes(d.serviceTypeId)&&this.carrierIds.push(d.serviceTypeId)}}catch(x){m.e(x)}finally{m.f()}}}}catch(x){s.e(x)}finally{s.f()}}}}catch(x){o.e(x)}finally{o.f()}}if(void 0!=this.carrierList&&null!=e&&!e.includes(-1)){var f,u=Object(i["a"])(this.carrierList);try{for(u.s();!(f=u.n()).done;){var p=f.value;if(null!=e&&void 0!=e){var h,y=Object(i["a"])(e);try{for(y.s();!(h=y.n()).done;){var b=h.value;if(p.serviceTypeId==b&&t.push(p),void 0!=p.children&&p.children.length>0){var g,v=Object(i["a"])(p.children);try{for(v.s();!(g=v.n()).done;){var w=g.value;w.serviceTypeId==b&&t.push(w)}}catch(x){v.e(x)}finally{v.f()}}}}catch(x){y.e(x)}finally{y.f()}}}}catch(x){u.e(x)}finally{u.f()}if(this.temCarrierList=t,this.temCarrierList.length>0){var C,k=Object(i["a"])(this.temCarrierList);try{for(k.s();!(C=k.n()).done;){var I=C.value;if(void 0!=I.children&&I.children.length>0){var S,L=Object(i["a"])(I.children);try{for(L.s();!(S=L.n()).done;){var _=S.value;null!=this.form.carrierIds&&this.form.carrierIds.includes(_.carrier.carrierId)&&!this.carrierIds.includes(_.serviceTypeId)&&this.carrierIds.push(_.serviceTypeId)}}catch(x){L.e(x)}finally{L.f()}}}}catch(x){k.e(x)}finally{k.f()}}}},form:function(){1==this.form.salesConfirmed||1==this.form.accConfirmed||1==this.form.psaConfirmed||1==this.form.opConfirmed?this.isLock=!0:this.isLock=!1},localStandard:function(e){this.handleTermChange()},"form.receiveTerm":function(e){this.handleTermChange()},"form.payTerm":function(e){this.handleTermChange()},"form.receiveWay":function(e){this.handleTermChange()},"form.payWay":function(e){this.handleTermChange()}},created:function(){var e=this;this.getList().then((function(){e.loadBusinesses(),e.loadCarrier(),e.loadSales()}))},methods:{parseTime:B["f"],handleTermChange:function(e){"月结"===this.form.receiveWay||"月结"===this.form.payWay?1==this.roleClient||1==this.roleRich?this.description=(this.form.receiveStandard?this.form.receiveStandard:"")+(this.form.receiveTerm?"-"===this.form.receiveTerm.substring(0,1)?"的下个月"+this.form.receiveTerm.substring(1,3)+"号之后":"的下个月"+this.form.receiveTerm.substring(1,3)+"号之前":"前"):this.description=(this.form.payStandard?this.form.payStandard:"")+(this.form.payTerm?"-"===this.form.payTerm.substring(0,1)?"的下个月"+this.form.payTerm.substring(1,3)+"号之后":"的下个月"+this.form.payTerm.substring(1,3)+"号之前":"前"):1==this.roleClient||1==this.roleRich?this.description=(this.form.receiveStandard?this.form.receiveStandard:"")+(this.form.receiveTerm?"-"===this.form.receiveTerm.substring(0,1)?this.form.receiveTerm.substring(1,3)+"天前":"后"+this.form.receiveTerm.substring(1,3)+"天内":"前"):this.description=(this.form.payStandard?this.form.payStandard:"")+(this.form.payTerm?"-"===this.form.payTerm.substring(0,1)?this.form.payTerm.substring(1,3)+"天前":"后"+this.form.payTerm.substring(1,3)+"天内":"前")},loadSales:function(){var e=this;0==this.$store.state.data.salesList.length||this.$store.state.data.redisList.salesList?Object(M["b"])(["Operator"])?y["a"].dispatch("getSalesList").then((function(){e.belongList=e.$store.state.data.salesList})):y["a"].dispatch("getSalesListC").then((function(){e.belongList=e.$store.state.data.salesList})):this.belongList=this.$store.state.data.salesList},loadBusinesses:function(){var e=this;0==this.$store.state.data.businessesList.length||this.$store.state.data.redisList.businessesList?y["a"].dispatch("getBusinessesList").then((function(){e.businessList=e.$store.state.data.businessesList})):this.businessList=this.$store.state.data.businessesList},loadCarrier:function(){var e=this;0==this.$store.state.data.serviceTypeCarriers.length||this.$store.state.data.redisList.serviceTypeCarriers?y["a"].dispatch("getServiceTypeCarriersList").then((function(){e.carrierList=e.$store.state.data.serviceTypeCarriers})):this.carrierList=this.$store.state.data.serviceTypeCarriers},querySame:function(){var e=this,t={cargoTypeIds:[],locationDepartureIds:[],locationDestinationIds:[],lineDepartureIds:[],lineDestinationIds:[],companyShortName:this.form.companyShortName,companyLocalName:this.form.companyLocalName,serviceTypeIds:this.form.serviceTypeIds,roleTypeId:this.roleTypeId,roleRich:this.roleRich?this.roleRich:null,roleClient:this.roleClient?this.roleClient:null,roleSupplier:this.roleSupplier?this.roleSupplier:null,roleSupport:this.roleSupport?this.roleSupport:null,belongTo:this.form.belongTo,followUp:this.form.followUp,deleteStatus:1};if(Object(m["d"])(this.$store.state.user.sid).then((function(e){t.cargoTypeIds=e.cargoTypeIds,t.locationDepartureIds=e.locationDepartureIds,t.locationDestinationIds=e.locationDestinationIds,t.lineDepartureIds=e.lineDepartureIds,t.lineDestinationIds=e.lineDestinationIds})),null==t.belongTo&&void 0!=this.belongList){var a,o=Object(i["a"])(this.belongList);try{for(o.s();!(a=o.n()).done;){var r=a.value;if(void 0!=r.children){var n,s=Object(i["a"])(r.children);try{for(s.s();!(n=s.n()).done;){var l=n.value;if(void 0!=l.children){var f,u=Object(i["a"])(l.children);try{for(u.s();!(f=u.n()).done;){var p=f.value;p.staffId==this.$store.state.user.sid&&(t.belongTo=this.$store.state.user.sid)}}catch(h){u.e(h)}finally{u.f()}}}}catch(h){s.e(h)}finally{s.f()}}}}catch(h){o.e(h)}finally{o.f()}}this.$refs["form"].validate((function(a){a&&Object(c["k"])(t).then((function(a){var o=a.data;if(void 0!=o){var r=e.roleRich?"瑞旗分支":e.roleClient?"客户":e.roleSupplier?"供应商":e.roleSupport?"支持":"",n=1==o.roleRich?"瑞旗分支":1==o.roleClient?"客户":1==o.roleSupplier?"供应商":1==o.roleSupport?"支持":"";e.$confirm(0==o.deleteStatus?"此公司已存在，角色为"+n+"，新增角色为"+r+",是否确认新增":"存在重复数据，但已删除，是否重新读取","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning",customClass:"modal-confirm"}).then((function(){if(o.deleteStatus=0,e.form=o,e.form.roleTypeId=e.roleTypeId,void 0!=e.belongList){var t,r=Object(i["a"])(e.belongList);try{for(r.s();!(t=r.n()).done;){var n=t.value;if(void 0!=n.children){var s,l=Object(i["a"])(n.children);try{for(l.s();!(s=l.n()).done;){var c=s.value;if(void 0!=c.children){var m,d=Object(i["a"])(c.children);try{for(d.s();!(m=d.n()).done;){var f=m.value;f.staffId==a.data.belongTo&&(e.belongTo=f.deptId),f.staffId==a.data.followUp&&(e.followUp=f.deptId)}}catch(h){d.e(h)}finally{d.f()}}}}catch(h){l.e(h)}finally{l.f()}}}}catch(h){r.e(h)}finally{r.f()}}e.form.roleIds=a.roleIds,e.form.serviceTypeIds=a.serviceTypeIds,e.form.cargoTypeIds=a.cargoTypeIds,e.form.lineDepartureIds=a.lineDepartureIds,e.form.locationDepartureIds=a.locationDepartureIds,e.form.lineDestinationIds=a.lineDestinationIds,e.form.locationDestinationIds=a.locationDestinationIds,e.form.carrierIds=a.carrierIds,e.form.organizationIds=a.organizationIds,e.locationOptions=a.locationOptions,e.openCompany=!0,e.title="修改公司信息",e.loading=!1}))}a.msg.toString().indexOf("Error")>-1&&e.$confirm(a.msg,"提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning",customClass:"modal-confirm"}).then((function(){var t={messageOwner:1,messageType:3,messageFrom:null,messageTitle:e.$store.state.user.name.split(" ")[1]+"请求更新公司",messageContent:a.msg};Object(d["a"])(t).then((function(t){e.$message({type:"success",message:"已发送请求!"})}))})),a.msg.toString().indexOf("Success")>-1&&e.$confirm("不存在重复的公司简称，是否确定新增客户？","提示",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning",customClass:"modal-confirm"}).then((function(){t.deleteStatus=0,Object(c["k"])(t).then((function(t){if(t.data){if(e.$message.success("添加成功"),e.form=t.data,e.form.roleTypeId=e.roleTypeId,void 0!=e.belongList){var a,o=Object(i["a"])(e.belongList);try{for(o.s();!(a=o.n()).done;){var r=a.value;if(void 0!=r.children){var n,s=Object(i["a"])(r.children);try{for(s.s();!(n=s.n()).done;){var l=n.value;if(void 0!=l.children){var c,m=Object(i["a"])(l.children);try{for(m.s();!(c=m.n()).done;){var d=c.value;d.staffId==t.data.belongTo&&(e.belongTo=d.deptId),d.staffId==t.data.followUp&&(e.followUp=d.deptId)}}catch(h){m.e(h)}finally{m.f()}}}}catch(h){s.e(h)}finally{s.f()}}}}catch(h){o.e(h)}finally{o.f()}}e.form.roleIds=t.roleIds,e.form.serviceTypeIds=t.serviceTypeIds,e.form.cargoTypeIds=t.cargoTypeIds,e.form.lineDepartureIds=t.lineDepartureIds,e.form.locationDepartureIds=t.locationDepartureIds,e.form.lineDestinationIds=t.lineDestinationIds,e.form.locationDestinationIds=t.locationDestinationIds,e.form.carrierIds=t.carrierIds,e.form.organizationIds=t.organizationIds,e.locationOptions=t.locationOptions,e.openCompany=!0,e.title="修改公司信息",e.loading=!1}}))}))}))}))},getList:function(){var e=this;return Object(l["a"])(Object(n["a"])().mark((function t(){return Object(n["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.loading=!0,t.next=3,Object(c["f"])(Object(s["a"])(Object(s["a"])({},e.queryParams),{},{permissionLevel:e.$store.state.user.permissionLevelList.C})).then((function(t){e.companyList=t.rows,isNaN(t.total)||(e.total=t.total),e.loading=!1}));case 3:case"end":return t.stop()}}),t)})))()},staffNormalizer:function(e){var t;return e.children&&!e.children.length&&delete e.children,e.staff&&(t=null==e.staff.staffFamilyLocalName&&null==e.staff.staffGivingLocalName?null!=e.role.roleLocalName?e.role.roleLocalName+","+h.a.getFullChars(e.role.roleLocalName):e.dept.deptLocalName+","+h.a.getFullChars(e.dept.deptLocalName):e.staff.staffCode+" "+e.staff.staffFamilyLocalName+e.staff.staffGivingLocalName+" "+e.staff.staffGivingEnName+","+h.a.getFullChars(e.staff.staffFamilyLocalName+e.staff.staffGivingLocalName)),e.roleId?{id:e.roleId,label:t,children:e.children,isDisabled:null==e.staffId&&void 0==e.children}:{id:e.deptId,label:t,children:e.children,isDisabled:null==e.staffId&&void 0==e.children}},validateCompanyShortName:function(e,t,a){if(t||"string"===typeof t){var o=t.split("-").length-1;if(o>1)return a(new Error("只能包含一个中横线"))}a()},carrierNormalizer:function(e){var t;return e.children&&!e.children.length&&delete e.children,t=!e.carrier||null==e.carrier.carrierLocalName&&null==e.carrier.carrierEnName?e.serviceLocalName+" "+e.serviceEnName+","+h.a.getFullChars(e.serviceLocalName):(null!=e.carrier.carrierIntlCode?e.carrier.carrierIntlCode:"")+" "+(null!=e.carrier.carrierEnName?e.carrier.carrierEnName:"")+" "+(null!=e.carrier.carrierLocalName?e.carrier.carrierLocalName:"")+","+h.a.getFullChars(null!=e.carrier.carrierLocalName?e.carrier.carrierLocalName:""),{id:e.serviceTypeId,label:t,children:e.children}},cancel:function(){this.openCompany=!1,this.openAccount=!1,this.openStaff=!1,this.openCommunication=!1,this.openAgreement=!1,this.openBlackList=!1,this.add=!1,this.merge=!1,this.edit=!1,this.reset(),this.belongTo=null,this.followUp=null},accountCancel:function(){this.openAccount=!1},reset:function(){this.belongTo=null,this.followUp=null,this.carrierIds=[],this.form={belongTo:null,followUp:null,carrierIds:null,locationDetail:null,companyId:null,companyIntlCode:null,companyShortName:null,companyEnShortName:null,companyLocalName:null,companyEnName:null,companyTaxCode:null,roleIds:null,serviceTypeIds:null,cargoTypeIds:null,locationDepartureIds:null,lineDepartureIds:null,locationDestinationIds:null,lineDestinationIds:null,organizationIds:null,companyPortIds:null,roleTypeId:this.roleTypeId,roleRich:this.roleRich?this.roleRich:null,roleClient:this.roleClient?this.roleClient:null,roleSupplier:this.roleSupplier?this.roleSupplier:null,roleSupport:this.roleSupport?this.roleSupport:null,locationId:null,salesConfirmed:0,salesConfirmedId:null,salesConfirmedName:null,salesConfirmedDate:null,psaConfirmed:0,psaConfirmedId:null,psaConfirmedName:null,psaConfirmedDate:null,accConfirmed:0,accConfirmedId:null,accConfirmedName:null,accConfirmedDate:null,opConfirmed:0,opConfirmedId:null,opConfirmedName:null,opConfirmedDate:null,remark:null,rsPaymentTitles:[]},this.carrierIds=[],this.companyRow=null,this.resetForm("form")},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.resetForm("queryForm"),this.queryBStaffId=null,this.queryParams.locationId=null,this.queryParams.serviceTypeIds=null,this.queryParams.cargoTypeIds=null,this.queryParams.locationDepartureIds=null,this.queryParams.lineDepartureIds=null,this.queryParams.locationDestinationIds=null,this.queryParams.lineDestinationIds=null,this.queryParams.organizationIds=null,this.handleQuery()},handleSelectionChange:function(e){this.ids=e.map((function(e){return e.companyId})),this.single=1!=e.length,this.multiple=!e.length,this.selectTwo=2!=e.length,1==e.length&&this.setCompanyInfo(e[0]),2==e.length&&(this.mergeList=e)},handleAdd:function(){if(this.reset(),this.edit=!1,this.form.belongTo=null,this.openCompany=!0,this.title="新增公司信息",this.form.serviceTypeIds=[],this.temCarrierList=this.carrierList,void 0!=this.temCarrierList&&null!=this.form.serviceTypeIds){var e,t=Object(i["a"])(this.temCarrierList);try{for(t.s();!(e=t.n()).done;)e.value}catch(f){t.e(f)}finally{t.f()}}if(this.add=!0,void 0!=this.belongList){var a,o=Object(i["a"])(this.belongList);try{for(o.s();!(a=o.n()).done;){var r=a.value;if(void 0!=r.children){var n,s=Object(i["a"])(r.children);try{for(s.s();!(n=s.n()).done;){var l=n.value;if(void 0!=l.children){var c,m=Object(i["a"])(l.children);try{for(m.s();!(c=m.n()).done;){var d=c.value;d.staffId==this.$store.state.user.sid&&(this.belongTo=d.deptId)}}catch(f){m.e(f)}finally{m.f()}}}}catch(f){s.e(f)}finally{s.f()}}}}catch(f){o.e(f)}finally{o.f()}}this.form.agreementCurrencyCode="RMB",this.showConfirm=!1},getReturn:function(e){var t=this;"contactor"==e.key&&(this.setCompanyInfo(e.value),Object(c["d"])(e.value.companyId).then((function(e){t.staffList=e.staffList,t.openStaff=!0}))),"communication"==e.key&&(this.setCompanyInfo(e.value),Object(f["d"])({sqdCompanyId:e.value.companyId}).then((function(e){t.communicationList=e.rows,t.ctotle=e.totle,t.openCommunication=!0}))),"agreement"==e.key&&(this.setCompanyInfo(e.value),Object(u["d"])({sqdCompanyId:e.value.companyId}).then((function(e){t.agreementList=e.rows,t.atotle=e.totle,t.openAgreement=!0}))),"account"==e.key&&(this.setCompanyInfo(e.value),Object(c["b"])(e.value.companyId).then((function(e){t.accountList=e.accountList,t.openAccount=!0})))},setCompanyInfo:function(e){this.companyInfo={companyId:null!=e.companyId?e.companyId:"",companyTaxCode:null!=e.companyTaxCode?e.companyTaxCode:"",companyShortName:null!=e.companyShortName?e.companyShortName:"",companyEnShortName:null!=e.companyEnShortName?e.companyEnShortName:"",companyLocalName:null!=e.companyLocalName?e.companyLocalName:"",companyEnName:null!=e.companyEnName?e.companyEnName:"",companyLocation:null!=e.locationId?e.locationId:"",companyIntlCode:null!=e.companyIntlCode?e.companyIntlCode:"",mainStaffId:null!=e.staff?e.staff.staffId:""}},handleUpdate:function(e){var t=this;this.reset(),this.loading=!0,this.edit=!0,this.companyRow=e,this.add=A["a"].hasPermi("system:client:distribute");var a=e.companyId||this.ids;this.showConfirm=!0,Object(c["c"])(a).then((function(e){if(t.form=e.data,void 0!=t.belongList){var a,o=Object(i["a"])(t.belongList);try{for(o.s();!(a=o.n()).done;){var r=a.value;if(void 0!=r.children){var n,s=Object(i["a"])(r.children);try{for(s.s();!(n=s.n()).done;){var l=n.value;if(void 0!=l.children){var c,m=Object(i["a"])(l.children);try{for(m.s();!(c=m.n()).done;){var d=c.value;d.staffId==e.data.belongTo&&(t.belongTo=d.deptId),d.staffId==e.data.followUp&&(t.followUp=d.deptId)}}catch(u){m.e(u)}finally{m.f()}}}}catch(u){s.e(u)}finally{s.f()}}}}catch(u){o.e(u)}finally{o.f()}}t.form.roleIds=e.roleIds,t.form.serviceTypeIds=e.serviceTypeIds,t.form.cargoTypeIds=e.cargoTypeIds,t.form.lineDepartureIds=e.lineDepartureIds,t.form.locationDepartureIds=e.locationDepartureIds,t.form.lineDestinationIds=e.lineDestinationIds,t.form.locationDestinationIds=e.locationDestinationIds,t.form.carrierIds=e.carrierIds,t.form.organizationIds=e.organizationIds,t.locationOptions=e.locationOptions,t.openCompany=!0,t.title="修改公司信息",t.loading=!1,null!==e.data.agreementStartDate&&null!==e.data.agreementEndDate&&(t.form.agreementDateRange=[],t.form.agreementDateRange.push(e.data.agreementStartDate),t.form.agreementDateRange.push(e.data.agreementEndDate));var f=new Intl.NumberFormat("en-US",{style:"decimal",minimumFractionDigits:2,maximumFractionDigits:2});1!=t.roleClient&&1!=t.roleRich||null===e.data.receiveCreditLimit||(t.form.receiveCreditLimit=e.data.receiveCreditLimit.toLocaleString("en-US"),t.form.receiveCreditLimit=t.form.receiveCreditLimit.replace(/,/g,""),t.form.receiveCreditLimit=f.format(t.form.receiveCreditLimit)),1!=t.roleSupplier&&1!=t.roleSupport||null===e.data.payCreditLimit||(t.form.payCreditLimit=e.data.payCreditLimit.toLocaleString("en-US"),t.form.payCreditLimit=t.form.payCreditLimit.replace(/,/g,""),t.form.payCreditLimit=f.format(t.form.payCreditLimit))}))},submitForm:function(){var e=this;this.$refs["form"].validate((function(t){1!=e.roleClient&&1!=e.roleRich||(e.form.sqdReceiveTermsSummary=e.description),1!=e.roleClient&&1!=e.roleRich||(e.form.sqdPayTermsSummary=e.description),e.form.roleRich=e.roleRich?e.roleRich:null,e.form.roleClient=e.roleClient?e.roleClient:null,e.form.roleSupplier=e.roleSupplier?e.roleSupplier:null,e.form.roleSupport=e.roleSupport?e.roleSupport:null,e.form.receiveCreditLimit=e.form.receiveCreditLimit?e.form.receiveCreditLimit.replace(/,/g,""):0,e.form.payCreditLimit=e.form.payCreditLimit?e.form.payCreditLimit.replace(/,/g,""):0,e.form.agreementDateRange&&e.form.agreementDateRange.length>0&&(e.form.agreementStartDate=e.form.agreementDateRange[0],e.form.agreementEndDate=e.form.agreementDateRange[1]);var a=new Date(e.form.agreementStartDate),o=new Date(e.form.agreementEndDate);a>o?Object(F["Message"])({message:"协议开始时间不能大于结束时间",type:"error"}):null==e.form.creditCycleMonth||e.form.creditCycleMonth%1==0?t&&(null!=e.form.companyId?(Object(c["m"])(e.form).then((function(t){e.$modal.msgSuccess("修改成功"),e.openCompany=!1,e.getList()})),e.reset()):e.$message.info("未查重，先对简称查重吧")):Object(F["Message"])({message:"信用周期必须为整数",type:"error"})}))},handleDelete:function(e){var t=this;e.roleRich=this.roleRich?this.roleRich:null,e.roleClient=this.roleClient?this.roleClient:null,e.roleSupplier=this.roleSupplier?this.roleSupplier:null,e.roleSupport=this.roleSupport?this.roleSupport:null;var a=e.companyId||this.ids;this.$confirm('是否确认删除公司编号为"'+a+'"的数据项？',"提示",{customClass:"modal-confirm"}).then((function(){return Object(c["a"])(e)})).then((function(){t.getList(),t.$modal.msgSuccess("删除成功")})).catch((function(){}))},handleBlackList:function(){this.openBlackList=!0},handleExport:function(){this.download("system/company/export",Object(s["a"])({},this.queryParams),"company_".concat((new Date).getTime(),".xlsx"))},queryLocationId:function(e){this.queryParams.locationId=e,this.handleQuery()},getLocationId:function(e){this.form.locationId=e},getSourceId:function(e){this.form.sourceId=e.sourceShortName},getOrganizationIds:function(e){this.form.organizationIds=e},getServiceTypeIds:function(e){this.form.serviceTypeIds=e,void 0==e&&(this.carrierIds=null,this.form.carrierIds=null)},queryServiceTypeIds:function(e){this.queryParams.serviceTypeIds=e,this.handleQuery()},getCargoTypeIds:function(e){this.form.cargoTypeIds=e},queryCargoTypeIds:function(e){this.queryParams.cargoTypeIds=e,this.handleQuery()},getCompanyRoleIds:function(e){this.form.roleIds=e},queryCompanyRoleIds:function(e){this.queryParams.roleIds=e,this.handleQuery()},queryLocationDepartureIds:function(e){this.queryParams.locationDepartureIds=e,this.handleQuery()},getLineDepartureIds:function(e){this.form.lineDepartureIds=e},getLocationDestinationIds:function(e){this.form.locationDestinationIds=e},queryLineDepartureIds:function(e){this.queryParams.lineDepartureIds=e,this.handleQuery()},getLocationDepartureIds:function(e){this.form.locationDepartureIds=e},queryLocationDestinationIds:function(e){this.queryParams.locationDestinationIds=e,this.handleQuery()},getLineDestinationIds:function(e){this.form.lineDestinationIds=e},queryLineDestinationIds:function(e){this.queryParams.lineDestinationIds=e,this.handleQuery()},handleSelectBelongTo:function(e){this.form.belongTo=e.staffId},handleDeselectBelongTo:function(e){void 0==e&&(this.form.belongTo=0,this.belongTo=null)},handleSelectFollowUp:function(e){this.form.followUp=e.staffId},handleDeselectFollowUp:function(e){void 0==e&&(this.form.followUp=0,this.followUp=null)},handleSelectBFStaffId:function(e){this.queryParams.queryBFStaffId=e.staffId,this.handleQuery()},cleanBFStaffId:function(e){void 0==e&&(this.queryParams.queryBFStaffId=null,this.handleQuery())},cleanBStaffId:function(e){void 0==e&&(this.queryParams.queryBStaffId=null,this.handleQuery())},handleSelectBStaffId:function(e){var t=this;this.queryParams.queryBStaffId=e.staffId,Object(m["d"])(e.staffId).then((function(e){t.queryParams.cargoTypeIds=e.cargoTypeIds,t.queryParams.serviceTypeIds=e.serviceTypeIds,t.queryParams.locationDepartureIds=e.locationDepartureIds,t.queryParams.lineDepartureIds=e.lineDepartureIds,t.queryParams.locationDestinationIds=e.locationDestinationIds,t.queryParams.lineDestinationIds=e.lineDestinationIds,t.locationOptions=e.locationOptions,t.handleQuery()}))},handleSelectCarrierIds:function(e){this.form.carrierIds.push(e.carrier.carrierId)},handleSelectQueryCarrierIds:function(e){this.queryParams.carrierIds.push(e.carrier.carrierId),this.handleQuery()},handleDeselectCarrierIds:function(e){this.form.carrierIds=this.form.carrierIds.filter((function(t){return t!=e.carrier.carrierId}))},handleDeselectQueryCarrierIds:function(e){this.queryParams.carrierIds=this.queryParams.carrierIds.filter((function(t){return t!=e.carrier.carrierId})),this.handleQuery()},refreshColumns:function(){var e=this;this.refreshTable=!1,this.$nextTick((function(){e.refreshTable=!0}))},handleMergeCompany:function(){this.merge=!0},handleMerge:function(e,t){var a=this;Object(c["i"])(e,t).then((function(e){a.$message.success(e.msg),a.merge=!1,a.getList()}))},deptLock:function(){var e=this;null!=this.form.companyId?(this.form.deptConfirmed=0==this.form.deptConfirmed?1:0,this.form.deptConfirmedId=this.$store.state.user.sid,this.form.deptConfirmedName=this.$store.state.user.name.split(" ")[1],this.form.deptConfirmedDate=Object(B["f"])(new Date),Object(c["m"])(this.form).then((function(t){e.$modal.msgSuccess("修改成功")}))):this.$modal.msgError("错误操作")},financeLock:function(){var e=this;null!=this.form.companyId?(this.form.financeConfirmed=0==this.form.financeConfirmed?1:0,this.form.financeConfirmedId=this.$store.state.user.sid,this.form.financeConfirmedName=this.$store.state.user.name.split(" ")[1],this.form.financeConfirmedDate=Object(B["f"])(new Date),Object(c["m"])(this.form).then((function(t){e.$modal.msgSuccess("修改成功")}))):this.$modal.msgError("错误操作")},psaLock:function(){var e=this;null!=this.form.companyId?(this.form.psaConfirmed=0==this.form.psaConfirmed?1:0,this.form.psaConfirmedId=this.$store.state.user.sid,this.form.psaConfirmedName=this.$store.state.user.name.split(" ")[1],this.form.psaConfirmedDate=Object(B["f"])(new Date),Object(c["m"])(this.form).then((function(t){e.$modal.msgSuccess("修改成功")}))):this.$modal.msgError("错误操作")},opLock:function(){var e=this;null!=this.form.companyId?(this.form.opConfirmed=0===this.form.opConfirmed?1:0,this.form.opConfirmedId=this.$store.state.user.sid,this.form.opConfirmedName=this.$store.state.user.name.split(" ")[1],this.form.opConfirmedDate=Object(B["f"])(new Date),Object(c["m"])(this.form).then((function(t){e.$modal.msgSuccess("修改成功")}))):this.$modal.msgError("错误操作")},getCurrencyCode:function(e){this.form.agreementCurrencyCode=e},getcreditLevel:function(e){this.form.creditLevel=e},getRsPaymentTitle:function(e){this.form.rsPaymentTitle=e},updateCompany:function(e){var t=this;return Object(l["a"])(Object(n["a"])().mark((function a(){var o,r,s,l,m,d,f,u,p,h,y;return Object(n["a"])().wrap((function(a){while(1)switch(a.prev=a.next){case 0:return 1!=t.roleClient&&1!=t.roleRich||(t.form.receiveCreditLimit=t.form.receiveCreditLimit?t.form.receiveCreditLimit.replace(/,/g,""):0),1!=t.roleSupplier&&1!=t.roleSupport||(t.form.payCreditLimit=t.form.payCreditLimit?t.form.payCreditLimit.replace(/,/g,""):0),t.form.agreementDateRange&&t.form.agreementDateRange.length>0&&(t.form.agreementStartDate=t.form.agreementDateRange[0],t.form.agreementEndDate=t.form.agreementDateRange[1]),a.next=5,Object(c["m"])(e);case 5:return t.$modal.msgSuccess("修改成功"),a.next=8,Object(c["c"])(e.companyId);case 8:if(o=a.sent,t.form=o.data,void 0!=t.belongList){r=Object(i["a"])(t.belongList);try{for(r.s();!(s=r.n()).done;)if(l=s.value,void 0!=l.children){m=Object(i["a"])(l.children);try{for(m.s();!(d=m.n()).done;)if(f=d.value,void 0!=f.children){u=Object(i["a"])(f.children);try{for(u.s();!(p=u.n()).done;)h=p.value,h.staffId==o.data.belongTo&&(t.belongTo=h.deptId),h.staffId==o.data.followUp&&(t.followUp=h.deptId)}catch(n){u.e(n)}finally{u.f()}}}catch(n){m.e(n)}finally{m.f()}}}catch(n){r.e(n)}finally{r.f()}}t.form.roleIds=o.roleIds,t.form.serviceTypeIds=o.serviceTypeIds,t.form.cargoTypeIds=o.cargoTypeIds,t.form.lineDepartureIds=o.lineDepartureIds,t.form.locationDepartureIds=o.locationDepartureIds,t.form.lineDestinationIds=o.lineDestinationIds,t.form.locationDestinationIds=o.locationDestinationIds,t.form.carrierIds=o.carrierIds,t.form.organizationIds=o.organizationIds,t.locationOptions=o.locationOptions,t.openCompany=!0,t.title="修改公司信息",t.loading=!1,y=new Intl.NumberFormat("en-US",{style:"decimal",minimumFractionDigits:2,maximumFractionDigits:2}),1!=t.roleClient&&1!=t.roleRich||(t.form.receiveCreditLimit=o.data.receiveCreditLimit.toLocaleString("en-US"),t.form.receiveCreditLimit=t.form.receiveCreditLimit.replace(/,/g,""),t.form.receiveCreditLimit=y.format(t.form.receiveCreditLimit)),1!=t.roleSupplier&&1!=t.roleSupport||(t.form.payCreditLimit=o.data.payCreditLimit.toLocaleString("en-US"),t.form.payCreditLimit=t.form.payCreditLimit.replace(/,/g,""),t.form.payCreditLimit=y.format(t.form.payCreditLimit)),t.form.agreementDateRange=null!=o.data.agreementStartDate&&null!=o.data.agreementEndDate?[o.data.agreementStartDate,o.data.agreementEndDate]:[],t.form.salesConfirmed=o.data.salesConfirmed,t.form.psaConfirmed=o.data.psaConfirmed,t.form.opConfirmed=o.data.opConfirmed,t.form.accConfirmed=o.data.accConfirmed;case 32:case"end":return a.stop()}}),a)})))()},formatCreditLimit:function(){if(null!=this.form.receiveCreditLimit||null!=this.form.payCreditLimit){var e=new Intl.NumberFormat("en-US",{style:"decimal",minimumFractionDigits:2,maximumFractionDigits:2});1==this.roleClient&&(this.form.receiveCreditLimit=this.form.receiveCreditLimit.replace(/,/g,""),this.form.receiveCreditLimit=e.format(this.form.receiveCreditLimit)),1==this.roleSupplier&&(this.form.payCreditLimit=this.form.payCreditLimit.replace(/,/g,""),this.form.payCreditLimit=e.format(this.form.payCreditLimit))}},formatDisplayCreditLimit:function(e){var t=new Intl.NumberFormat("en-US",{notation:"compact"});return t.format(e)},changeDate:function(){this.$forceUpdate()}}},J=G,H=(a("7aab"),a("2877")),Y=Object(H["a"])(J,o,r,!1,null,"897b0dee",null);t["default"]=Y.exports},1296:function(e,t,a){"use strict";a.r(t);var o=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("h6",{staticStyle:{margin:"0"}},[e._v(" "+e._s(e.scope.row.showPriority)+" ")]),a("h6",{staticStyle:{margin:"0"}},[e._v(" "+e._s(null!=e.scope.row.score?e.scope.row.score.substring(0,4):"")+" ")])])},r=[],n={name:"grade",props:["scope"]},s=n,l=a("2877"),i=Object(l["a"])(s,o,r,!1,null,"ddd6a916",null);t["default"]=i.exports},1401:function(e,t,a){"use strict";a.r(t);var o=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("el-tooltip",{attrs:{"open-delay":500,disabled:(null!=e.scope.row.companyBelongTo?e.scope.row.companyBelongTo:"").length<5,placement:"top"}},[a("div",{attrs:{slot:"content"},slot:"content"},[a("h6",{staticStyle:{margin:"0"}},[e._v(" "+e._s(null!=e.scope.row.companyBelongTo?e.scope.row.companyBelongTo:"")+" ")])]),a("div",{staticClass:"text-box"},[a("h6",{staticStyle:{margin:"0",overflow:"hidden","text-overflow":"ellipsis"}},[e._v(" "+e._s(null!=e.scope.row.companyBelongTo?e.scope.row.companyBelongTo:"")+" ")])])])],1)},r=[],n={name:"rsPaymentTitle",props:["scope"]},s=n,l=(a("f4d2"),a("2877")),i=Object(l["a"])(s,o,r,!1,null,"d51090d8",null);t["default"]=i.exports},"18f1":function(e,t,a){"use strict";a.r(t);var o=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("el-tooltip",{attrs:{disabled:null==e.scope.row.remark||e.scope.row.remark.length<22,placement:"top"}},[a("div",{attrs:{slot:"content"},slot:"content"},[a("h6",{staticStyle:{margin:"0"}},[e._v(" "+e._s(e.scope.row.remark)+" ")])]),a("div",[a("h6",{staticStyle:{margin:"0",overflow:"hidden","text-overflow":"ellipsis"}},[e._v(" "+e._s(e.scope.row.remark)+" ")])])])],1)},r=[],n={name:"remark",props:["scope"]},s=n,l=a("2877"),i=Object(l["a"])(s,o,r,!1,null,"7e8a6be1",null);t["default"]=i.exports},1975:function(e,t,a){"use strict";a.r(t);var o=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("el-tooltip",{attrs:{"open-delay":500,disabled:((null!=e.scope.row.locationDestination?e.scope.row.locationDestination:"")+(null!=e.scope.row.lineDestination&&null!=e.scope.row.locationDestination?",":"")+(null!=e.scope.row.lineDestination?e.scope.row.lineDestination:"")).length<33,placement:"top"}},[a("div",{attrs:{slot:"content"},slot:"content"},[a("h6",{staticStyle:{margin:"0"}},[e._v(" "+e._s((null!=e.scope.row.locationDestination?e.scope.row.locationDestination:"")+(null!=e.scope.row.lineDestination&&null!=e.scope.row.locationDestination?",":"")+(null!=e.scope.row.lineDestination?e.scope.row.lineDestination:""))+" ")])]),a("div",[a("h6",{staticStyle:{margin:"0",overflow:"hidden","text-overflow":"ellipsis"}},[e._v(" "+e._s(((null!=e.scope.row.locationDestination?e.scope.row.locationDestination:"")+(null!=e.scope.row.lineDestination&&null!=e.scope.row.locationDestination?",":"")+(null!=e.scope.row.lineDestination?e.scope.row.lineDestination:"")).substring(0,33)+(((null!=e.scope.row.locationDestination?e.scope.row.locationDestination:"")+(null!=e.scope.row.lineDestination&&null!=e.scope.row.locationDestination?",":"")+(null!=e.scope.row.lineDestination?e.scope.row.lineDestination:"")).length>33?"...":""))+" ")])])])],1)},r=[],n={name:"destination",props:["scope"]},s=n,l=a("2877"),i=Object(l["a"])(s,o,r,!1,null,"029bb674",null);t["default"]=i.exports},"1ce2":function(e,t,a){"use strict";a.r(t);var o=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("el-tooltip",{attrs:{disabled:null==e.scope.row.cargoType||e.scope.row.cargoType.length<12,placement:"top"}},[a("div",{attrs:{slot:"content"},slot:"content"},[a("h6",{staticStyle:{margin:"0"}},[e._v(" "+e._s(e.scope.row.cargoType)+" ")])]),a("div",[a("h6",{staticStyle:{margin:"0",overflow:"hidden","text-overflow":"ellipsis"}},[e._v(" "+e._s(e.scope.row.cargoType)+" ")])])])],1)},r=[],n={name:"cargoType",props:["scope"]},s=n,l=a("2877"),i=Object(l["a"])(s,o,r,!1,null,"97e601a4",null);t["default"]=i.exports},"283b":function(e,t,a){},"2ed4":function(e,t,a){"use strict";a.r(t);var o=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("el-tooltip",{attrs:{"open-delay":500,disabled:((null!=e.scope.row.locationDeparture?e.scope.row.locationDeparture:"")+(null!=e.scope.row.lineDeparture?e.scope.row.lineDeparture:"")).length<11,placement:"top"}},[a("div",{attrs:{slot:"content"},slot:"content"},[a("h6",{staticStyle:{margin:"0"}},[e._v(" "+e._s((null!=e.scope.row.locationDeparture?e.scope.row.locationDeparture:"")+(null!=e.scope.row.locationDeparture&&null!=e.scope.row.lineDeparture?",":"")+(null!=e.scope.row.lineDeparture?e.scope.row.lineDeparture:""))+" ")])]),a("div",[a("h6",{staticStyle:{margin:"0",overflow:"hidden","text-overflow":"ellipsis"}},[e._v(" "+e._s(((null!=e.scope.row.locationDeparture?e.scope.row.locationDeparture:"")+(null!=e.scope.row.locationDeparture&&null!=e.scope.row.lineDeparture?",":"")+(null!=e.scope.row.lineDeparture?e.scope.row.lineDeparture:"")).substring(0,11)+(((null!=e.scope.row.locationDeparture?e.scope.row.locationDeparture:"")+(null!=e.scope.row.lineDeparture?e.scope.row.lineDeparture:"")).length>11?"...":""))+" ")])])])],1)},r=[],n={name:"departure",props:["scope"]},s=n,l=a("2877"),i=Object(l["a"])(s,o,r,!1,null,"8d1372e4",null);t["default"]=i.exports},3964:function(e,t,a){"use strict";a.d(t,"d",(function(){return r})),a.d(t,"c",(function(){return n})),a.d(t,"a",(function(){return s})),a.d(t,"e",(function(){return l})),a.d(t,"b",(function(){return i}));var o=a("b775");function r(e){return Object(o["a"])({url:"/system/agreementrecord/list",method:"get",params:e})}function n(e){return Object(o["a"])({url:"/system/agreementrecord/"+e,method:"get"})}function s(e){return Object(o["a"])({url:"/system/agreementrecord",method:"post",data:e})}function l(e){return Object(o["a"])({url:"/system/agreementrecord",method:"put",data:e})}function i(e){return Object(o["a"])({url:"/system/agreementrecord/"+e,method:"delete"})}},3977:function(e,t,a){},4678:function(e,t,a){var o={"./af":"2bfb","./af.js":"2bfb","./ar":"8e73","./ar-dz":"a356","./ar-dz.js":"a356","./ar-kw":"423e","./ar-kw.js":"423e","./ar-ly":"1cfd","./ar-ly.js":"1cfd","./ar-ma":"0a84","./ar-ma.js":"0a84","./ar-ps":"4c98","./ar-ps.js":"4c98","./ar-sa":"8230","./ar-sa.js":"8230","./ar-tn":"6d83","./ar-tn.js":"6d83","./ar.js":"8e73","./az":"485c","./az.js":"485c","./be":"1fc1","./be.js":"1fc1","./bg":"84aa","./bg.js":"84aa","./bm":"a7fa","./bm.js":"a7fa","./bn":"9043","./bn-bd":"9686","./bn-bd.js":"9686","./bn.js":"9043","./bo":"d26a","./bo.js":"d26a","./br":"6887","./br.js":"6887","./bs":"25548","./bs.js":"25548","./ca":"d716","./ca.js":"d716","./cs":"3c0d","./cs.js":"3c0d","./cv":"03ec","./cv.js":"03ec","./cy":"9797","./cy.js":"9797","./da":"0f14","./da.js":"0f14","./de":"b469","./de-at":"b3eb","./de-at.js":"b3eb","./de-ch":"bb71","./de-ch.js":"bb71","./de.js":"b469","./dv":"598a","./dv.js":"598a","./el":"8d47","./el.js":"8d47","./en-au":"0e6b","./en-au.js":"0e6b","./en-ca":"3886","./en-ca.js":"3886","./en-gb":"39a6","./en-gb.js":"39a6","./en-ie":"e1d3","./en-ie.js":"e1d3","./en-il":"7333","./en-il.js":"7333","./en-in":"ec2e","./en-in.js":"ec2e","./en-nz":"6f50","./en-nz.js":"6f50","./en-sg":"b7e9","./en-sg.js":"b7e9","./eo":"65db","./eo.js":"65db","./es":"898b","./es-do":"0a3c","./es-do.js":"0a3c","./es-mx":"b5b7","./es-mx.js":"b5b7","./es-us":"55c9","./es-us.js":"55c9","./es.js":"898b","./et":"ec18","./et.js":"ec18","./eu":"0ff2","./eu.js":"0ff2","./fa":"8df48","./fa.js":"8df48","./fi":"81e9","./fi.js":"81e9","./fil":"d69a","./fil.js":"d69a","./fo":"0721","./fo.js":"0721","./fr":"9f26","./fr-ca":"d9f8","./fr-ca.js":"d9f8","./fr-ch":"0e49","./fr-ch.js":"0e49","./fr.js":"9f26","./fy":"7118","./fy.js":"7118","./ga":"5120","./ga.js":"5120","./gd":"f6b46","./gd.js":"f6b46","./gl":"8840","./gl.js":"8840","./gom-deva":"aaf2","./gom-deva.js":"aaf2","./gom-latn":"0caa","./gom-latn.js":"0caa","./gu":"e0c5","./gu.js":"e0c5","./he":"c7aa","./he.js":"c7aa","./hi":"dc4d","./hi.js":"dc4d","./hr":"4ba9","./hr.js":"4ba9","./hu":"5b14","./hu.js":"5b14","./hy-am":"d6b6","./hy-am.js":"d6b6","./id":"5038","./id.js":"5038","./is":"0558","./is.js":"0558","./it":"6e98","./it-ch":"6f12","./it-ch.js":"6f12","./it.js":"6e98","./ja":"079e","./ja.js":"079e","./jv":"b540","./jv.js":"b540","./ka":"201b","./ka.js":"201b","./kk":"6d79","./kk.js":"6d79","./km":"e81d","./km.js":"e81d","./kn":"3e92","./kn.js":"3e92","./ko":"22f8","./ko.js":"22f8","./ku":"2421","./ku-kmr":"7558","./ku-kmr.js":"7558","./ku.js":"2421","./ky":"9609","./ky.js":"9609","./lb":"440c","./lb.js":"440c","./lo":"b29d","./lo.js":"b29d","./lt":"26f9","./lt.js":"26f9","./lv":"b97c","./lv.js":"b97c","./me":"293c","./me.js":"293c","./mi":"688b","./mi.js":"688b","./mk":"6909","./mk.js":"6909","./ml":"02fb","./ml.js":"02fb","./mn":"958b","./mn.js":"958b","./mr":"39bd","./mr.js":"39bd","./ms":"ebe4","./ms-my":"6403","./ms-my.js":"6403","./ms.js":"ebe4","./mt":"1b45","./mt.js":"1b45","./my":"8689","./my.js":"8689","./nb":"6ce3","./nb.js":"6ce3","./ne":"3a39","./ne.js":"3a39","./nl":"facd","./nl-be":"db29","./nl-be.js":"db29","./nl.js":"facd","./nn":"b84c","./nn.js":"b84c","./oc-lnc":"167b","./oc-lnc.js":"167b","./pa-in":"f3ff","./pa-in.js":"f3ff","./pl":"8d57","./pl.js":"8d57","./pt":"f260","./pt-br":"d2d4","./pt-br.js":"d2d4","./pt.js":"f260","./ro":"972c","./ro.js":"972c","./ru":"957c","./ru.js":"957c","./sd":"6784","./sd.js":"6784","./se":"ffff","./se.js":"ffff","./si":"eda5","./si.js":"eda5","./sk":"7be6","./sk.js":"7be6","./sl":"8155","./sl.js":"8155","./sq":"c8f3","./sq.js":"c8f3","./sr":"cf1e","./sr-cyrl":"13e9","./sr-cyrl.js":"13e9","./sr.js":"cf1e","./ss":"52bd","./ss.js":"52bd","./sv":"5fbd","./sv.js":"5fbd","./sw":"74dc","./sw.js":"74dc","./ta":"3de5","./ta.js":"3de5","./te":"5cbb","./te.js":"5cbb","./tet":"576c","./tet.js":"576c","./tg":"3b1b","./tg.js":"3b1b","./th":"10e8","./th.js":"10e8","./tk":"5aff","./tk.js":"5aff","./tl-ph":"0f38","./tl-ph.js":"0f38","./tlh":"cf75","./tlh.js":"cf75","./tr":"0e81","./tr.js":"0e81","./tzl":"cf51","./tzl.js":"cf51","./tzm":"c109","./tzm-latn":"b53d","./tzm-latn.js":"b53d","./tzm.js":"c109","./ug-cn":"6117","./ug-cn.js":"6117","./uk":"ada2","./uk.js":"ada2","./ur":"5294","./ur.js":"5294","./uz":"2e8c","./uz-latn":"010e","./uz-latn.js":"010e","./uz.js":"2e8c","./vi":"2921","./vi.js":"2921","./x-pseudo":"fd7e","./x-pseudo.js":"fd7e","./yo":"7f33","./yo.js":"7f33","./zh-cn":"5c3a","./zh-cn.js":"5c3a","./zh-hk":"49ab","./zh-hk.js":"49ab","./zh-mo":"3a6c","./zh-mo.js":"3a6c","./zh-tw":"90ea","./zh-tw.js":"90ea"};function r(e){var t=n(e);return a(t)}function n(e){if(!a.o(o,e)){var t=new Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}return o[e]}r.keys=function(){return Object.keys(o)},r.resolve=n,e.exports=r,r.id="4678"},"59e5":function(e,t,a){"use strict";a.r(t);var o=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"},{name:"dialogDragWidth",rawName:"v-dialogDragWidth"}],attrs:{"close-on-click-modal":!1,"append-to-body":!0,visible:e.openContent,title:"沟通记录",width:"1500px"},on:{"update:visible":function(t){e.openContent=t}}},[a("el-row",{staticClass:"mb8",attrs:{gutter:10}},[a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:communication:add"],expression:"['system:communication:add']"}],attrs:{icon:"el-icon-plus",plain:"",size:"mini",type:"primary"},on:{click:e.handleAdd}},[e._v("新增 ")])],1),a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:communication:edit"],expression:"['system:communication:edit']"}],attrs:{disabled:e.single,icon:"el-icon-edit",plain:"",size:"mini",type:"success"},on:{click:e.handleUpdate}},[e._v("修改 ")])],1),a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:communication:remove"],expression:"['system:communication:remove']"}],attrs:{disabled:e.multiple,icon:"el-icon-delete",plain:"",size:"mini",type:"danger"},on:{click:e.handleDelete}},[e._v("删除 ")])],1)],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.communicationList,border:"",stripe:""},on:{"selection-change":e.handleSelectionChange}},[a("el-table-column",{attrs:{type:"selection",width:"28",align:"center"}}),a("el-table-column",{key:"shortName",attrs:{label:"员工信息","show-tooltip-when-overflow":"",width:"100px"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(e.company.companyShortName)+" "+e._s(null!=t.row.extStaff?(null!=t.row.extStaff.staffLocalName?t.row.extStaff.staffLocalName:"")+" "+(null!=t.row.extStaff.staffEnName?t.row.extStaff.staffEnName:""):"")+" ")]}}])}),a("el-table-column",{attrs:{align:"center",label:"物流节点",prop:"stage.stageLocalName",width:"68px"}}),a("el-table-column",{attrs:{align:"center",label:"问题",prop:"issue.issueLocalName",width:"88px"}}),a("el-table-column",{attrs:{align:"center",label:"沟通详细",prop:"content","show-tooltip-when-overflow":""}}),a("el-table-column",{attrs:{align:"center",label:"评分",prop:"score",width:"48px"}}),a("el-table-column",{attrs:{align:"center",label:"优先度",prop:"orderNum",width:"58px"}}),a("el-table-column",{attrs:{align:"center",label:"备注",prop:"remark"}}),a("el-table-column",{key:"rsStaff.staffLocalName",attrs:{align:"center",label:"跟进人",width:"120px"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(null!=t.row.rsStaff?(null!=t.row.rsStaff.staffLocalName?t.row.rsStaff.staffLocalName:"")+" "+(null!=t.row.rsStaff.staffEnName?t.row.rsStaff.staffEnName:""):"")+" ")]}}])}),a("el-table-column",{attrs:{align:"center",label:"沟通时间",prop:"communicationDatetime",width:"135px"}}),a("el-table-column",{attrs:{align:"center","class-name":"small-padding fixed-width",label:"操作",width:"48px"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("div",{staticStyle:{height:"15px",padding:"0",margin:"0"}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:communication:edit"],expression:"['system:communication:edit']"}],staticStyle:{display:"flex"},attrs:{size:e.size,icon:"el-icon-edit",type:"success"},on:{click:function(a){return e.handleUpdate(t.row)}}},[e._v("修改 ")])],1),a("div",{staticStyle:{height:"15px",padding:"0",margin:"0"}},[e.id==t.row.staffId||1==e.id?a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:communication:remove"],expression:"['system:communication:remove']"}],staticStyle:{display:"flex"},attrs:{size:e.size,icon:"el-icon-delete",type:"danger"},on:{click:function(a){return e.handleDelete(t.row)}}},[e._v(" 删除 ")]):e._e()],1)]}}])})],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total>0"}],attrs:{limit:e.queryParams.pageSize,page:e.queryParams.pageNum,total:e.total},on:{"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},pagination:e.getList}})],1),a("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"},{name:"dialogDragWidth",rawName:"v-dialogDragWidth"}],attrs:{"close-on-click-modal":!1,"modal-append-to-body":!1,title:e.title,visible:e.oopen,"append-to-body":"",width:"800px"},on:{"update:visible":function(t){e.oopen=t}}},[a("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"100px"}},[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"公司",prop:"companyId"}},[a("a",{staticStyle:{margin:"0","font-weight":"bold","font-size":"small"}},[e._v(e._s(this.company.companyShortName))])])],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"沟通时间"}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{format:"yyyy 年 MM 月 dd 日 HH 时 mm 分 ss 秒",placeholder:"选择日期",type:"datetime","value-format":"yyyy-MM-dd HH:mm:ss"},model:{value:e.form.communicationDatetime,callback:function(t){e.$set(e.form,"communicationDatetime",t)},expression:"form.communicationDatetime"}})],1)],1),a("el-form-item",{attrs:{label:"沟通员工",prop:"extStaffId"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{filterable:"",placeholder:""},model:{value:e.form.extStaffId,callback:function(t){e.$set(e.form,"extStaffId",t)},expression:"form.extStaffId"}},e._l(e.extStaffOptions,(function(e){return a("el-option",{key:e.staffId,attrs:{label:(null!=e.staffLocalName?e.staffLocalName:"")+" "+(null!=e.staffEnName?e.staffEnName:""),value:e.staffId}})})),1)],1),a("el-form-item",{attrs:{label:"物流节点",prop:"stageId"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"进度分类"},model:{value:e.form.processTypeId,callback:function(t){e.$set(e.form,"processTypeId",t)},expression:"form.processTypeId"}},e._l(e.processtypeList,(function(e){return a("el-option",{key:e.processTypeId,attrs:{label:e.processTypeLocalName,value:e.processTypeId}})})),1)],1),a("el-form-item",{attrs:{label:"问题",prop:"issueId"}},[a("tree-select",{attrs:{pass:e.form.issueId,placeholder:"",type:"issue"},on:{return:e.getIssueId}})],1),a("el-form-item",{attrs:{label:"沟通详细"}},[a("el-input",{attrs:{autosize:{minRows:10,maxRows:20},maxlength:"300",placeholder:"沟通详细","show-word-limit":"",type:"textarea"},model:{value:e.form.content,callback:function(t){e.$set(e.form,"content",t)},expression:"form.content"}})],1),a("el-form-item",{attrs:{label:"备注",prop:"remark"}},[a("el-input",{attrs:{rows:3,maxlength:"150",placeholder:"备注","show-word-limit":"",type:"textarea"},model:{value:e.form.remark,callback:function(t){e.$set(e.form,"remark",t)},expression:"form.remark"}})],1),a("el-form-item",{attrs:{label:"评分1~10",prop:"score"}},[a("el-input-number",{staticStyle:{width:"100%"},attrs:{controls:!1,max:10,min:1,placeholder:"评分1~10"},model:{value:e.form.score,callback:function(t){e.$set(e.form,"score",t)},expression:"form.score"}})],1),a("el-form-item",{attrs:{label:"优先度",prop:"orderNum"}},[a("el-input-number",{staticStyle:{width:"100%"},attrs:{controls:!1,placeholder:"优先度"},model:{value:e.form.orderNum,callback:function(t){e.$set(e.form,"orderNum",t)},expression:"form.orderNum"}})],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("确 定")]),a("el-button",{on:{click:e.cancel}},[e._v("取 消")])],1)],1)],1)},r=[],n=a("5530"),s=(a("d81d"),a("998b")),l=a("b857"),i=a("ed08"),c=a("f0c6"),m={name:"communications",props:["type","open","loadOptions","company"],data:function(){return{extStaffOptions:[],processtypeList:[],id:this.$store.state.user.sid,openContent:!1,mainStaffId:null,oopen:!1,size:this.$store.state.app.size||"mini",loading:!0,ids:[],single:!0,multiple:!0,showSearch:!0,total:0,communicationList:[],title:"",queryParams:{pageNum:1,pageSize:20,sqdCompanyId:null,staffId:null,extStaffId:null,stageId:null,issueId:null,content:null,score:null},form:{},rules:{}}},watch:{loadOptions:function(){this.communicationList=this.loadOptions,this.loading=!1},open:function(e){this.openContent=e},openContent:function(e){0==e&&this.$emit("openCommunications")}},created:function(){var e=this;Object(c["e"])({pageNum:1,pageSize:100}).then((function(t){e.processtypeList=t.rows}))},methods:{getList:function(){var e=this;this.loading=!0,this.queryParams.sqdCompanyId=this.company.companyId,Object(s["d"])(this.queryParams).then((function(t){e.communicationList=t.rows,e.total=t.total,e.loading=!1}))},cancel:function(){this.oopen=!1,this.reset()},reset:function(){this.form={communicationId:null,staffId:this.$store.state.user.id,extStaffId:this.company.mainStaffId,stageId:null,issueId:null,content:null,score:null,orderNum:null,remark:null,createBy:null,createTime:null,updateBy:null,updateTime:null,deleteBy:null,deleteTime:null,deleteStatus:0},this.resetForm("form")},resetQuery:function(){this.resetForm("queryForm"),this.handleQuery()},handleSelectionChange:function(e){this.ids=e.map((function(e){return e.communicationId})),this.single=1!=e.length,this.multiple=!e.length},handleAdd:function(){var e=this;this.reset(),this.oopen=!0,this.title="添加记录",Object(l["e"])({sqdCompanyId:this.company.companyId}).then((function(t){e.extStaffOptions=t.data,e.form.extStaffId=e.company.mainStaffId})),this.form.communicationDatetime=Object(i["d"])(new Date)},handleUpdate:function(e){var t=this;this.reset();var a=e.communicationId||this.ids;Object(s["c"])(a).then((function(e){t.form=e.data,t.oopen=!0,t.title="修改交流",t.getList()}))},submitForm:function(){var e=this;this.$refs["form"].validate((function(t){t&&(e.form.sqdCompanyId=e.company.companyId,null!=e.form.communicationId?Object(s["e"])(e.form).then((function(t){e.$modal.msgSuccess("修改成功"),e.oopen=!1,e.getList()})):Object(s["a"])(e.form).then((function(t){e.$modal.msgSuccess("新增成功"),e.oopen=!1,e.getList()})))}))},handleDelete:function(e){var t=this,a=e.communicationId||this.ids;this.$confirm('是否确认删除交流编号为"'+a+'"的数据项？',"提示",{customClass:"modal-confirm"}).then((function(){return Object(s["b"])(a)})).then((function(){t.getList(),t.$modal.msgSuccess("删除成功")})).catch((function(){}))},handleExport:function(){this.download("system/communication/export",Object(n["a"])({},this.queryParams),"communication_".concat((new Date).getTime(),".xlsx"))},getStageId:function(e){this.form.stageId=e},getIssueId:function(e){this.form.issueId=e}}},d=m,f=a("2877"),u=Object(f["a"])(d,o,r,!1,null,null,null);t["default"]=u.exports},"5a72":function(e,t,a){"use strict";a("82c0")},6300:function(e,t,a){},"6ab7":function(e,t,a){},"7aab":function(e,t,a){"use strict";a("6300")},8256:function(e,t,a){"use strict";a.r(t);var o=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div")},r=[],n={name:"achievement",props:["scope"]},s=n,l=a("2877"),i=Object(l["a"])(s,o,r,!1,null,"35abf27c",null);t["default"]=i.exports},"82c0":function(e,t,a){},"89da":function(e,t,a){"use strict";a.r(t);var o=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("el-tooltip",{attrs:{disabled:null==e.scope.row.carrier||e.scope.row.carrier.length<20,placement:"top"}},[a("div",{attrs:{slot:"content"},slot:"content"},[a("h6",{staticStyle:{margin:"0"}},[e._v(" "+e._s(e.scope.row.carrier)+" ")])]),a("div",[a("h6",{staticStyle:{margin:"0",overflow:"hidden","text-overflow":"ellipsis"}},[e._v(" "+e._s(null!=e.scope.row.carrier?e.scope.row.carrier.substring(0,20):"")+" ")])])])],1)},r=[],n={name:"carrier",props:["scope"]},s=n,l=a("2877"),i=Object(l["a"])(s,o,r,!1,null,"c01579fa",null);t["default"]=i.exports},"8f75":function(e,t,a){"use strict";a.r(t);var o=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"},{name:"dialogDragWidth",rawName:"v-dialogDragWidth"}],staticClass:"staffInfo",attrs:{"close-on-click-modal":!1,"modal-append-to-body":!1,"append-to-body":!0,visible:e.openContent,title:"员工信息",width:"1800px"},on:{"update:visible":function(t){e.openContent=t},close:e.handleClose}},[a("el-row",{staticClass:"mb8",attrs:{gutter:10}},[a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:extstaff:add"],expression:"['system:extstaff:add']"}],attrs:{size:e.size,icon:"el-icon-plus",plain:"",type:"primary"},on:{click:e.handleAdd}},[e._v("新增 ")])],1),a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:extstaff:edit"],expression:"['system:extstaff:edit']"}],attrs:{disabled:e.single,size:e.size,icon:"el-icon-edit",plain:"",type:"success"},on:{click:e.handleUpdate}},[e._v("修改 ")])],1),a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:extstaff:remove"],expression:"['system:extstaff:remove']"}],attrs:{disabled:e.multiple,size:e.size,icon:"el-icon-delete",plain:"",type:"danger"},on:{click:e.handleDelete}},[e._v("删除 ")])],1)],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.extStaffList,border:"",stripe:""}},[a("el-table-column",{key:"staffShortName",attrs:{align:"left",label:"员工",prop:"staffShortName","show-tooltip-when-overflow":"",width:"180"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-row",[a("el-col",{attrs:{span:5}},["Y"==t.row.isMain?a("el-tag",{attrs:{size:e.size,type:"primary"}},[e._v("主")]):e._e()],1),a("el-col",{attrs:{span:19}},[a("h6",{staticStyle:{margin:"5px",overflow:"hidden","text-overflow":"ellipsis","white-space":"nowrap"}},[e._v(" "+e._s(null!=e.company.companyShortName?e.company.companyShortName:"")+" "+e._s(null!=t.row.staffLocalName?t.row.staffLocalName:"")+" "+e._s(null!=t.row.staffEnName?t.row.staffEnName:"")+" "+e._s(null!=t.row.staffShortName?t.row.staffShortName:"")+" "+e._s(null!=t.row.deptName?t.row.deptName:"")+" "+e._s(null!=t.row.deptPositionName?t.row.deptPositionName:"")+" ")])])],1)]}}])}),a("el-table-column",{attrs:{align:"center",label:"职位","show-tooltip-when-overflow":"",width:"60px"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("h6",{staticStyle:{margin:"5px",overflow:"hidden","text-overflow":"ellipsis","white-space":"nowrap"}},[e._v(" "+e._s(null!=t.row.deptName?t.row.deptName+"/":"")+e._s(t.row.deptPositionName)+" ")])]}}])}),a("el-table-column",{attrs:{align:"center",label:"微信",prop:"staffWechat","show-tooltip-when-overflow":"",width:"100px"}}),a("el-table-column",{attrs:{align:"center",label:"QQ",prop:"staffQq","show-tooltip-when-overflow":"",width:"80px"}}),a("el-table-column",{attrs:{align:"center",label:"WhatsApp",prop:"staffWhatsapp","show-tooltip-when-overflow":"",width:"80px"}}),a("el-table-column",{attrs:{align:"center",label:"其他联系人方式",prop:"staffOtherContact","show-tooltip-when-overflow":"",width:"80px"}}),a("el-table-column",{key:"staffTelNum",attrs:{align:"center",label:"个人邮箱",prop:"staffTelNum","show-tooltip-when-overflow":"",width:"150px"}}),a("el-table-column",{key:"staffEmailEnterprise",attrs:{align:"center",label:"企业邮箱",prop:"staffEmailEnterprise","show-tooltip-when-overflow":"",width:"150px"}}),a("el-table-column",{key:"staffGender",attrs:{align:"center",label:"性别",prop:"staffGender",width:"38px"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("dict-tag",{attrs:{options:e.dict.type.sys_user_sex,value:t.row.staffGender}})]}}])}),a("el-table-column",{key:"staffPhoneNum",attrs:{align:"center",label:"电话号码",prop:"staffPhoneNum","show-tooltip-when-overflow":"",width:"100"}}),a("el-table-column",{key:"staffAddress",attrs:{align:"center",label:"详细住址",prop:"staffAddress","show-tooltip-when-overflow":"",width:"100px"}}),a("el-table-column",{key:"staffBirthday",attrs:{align:"center",label:"生日",prop:"staffBirthday",width:"78px"}}),a("el-table-column",{key:"staffLanguage",attrs:{align:"center",label:"母语",prop:"staffLanguage",width:"48px"}}),a("el-table-column",{key:"staffCountry",attrs:{align:"center",label:"国籍",prop:"staffCountry",width:"48px"}}),a("el-table-column",{key:"staffNation",attrs:{align:"center",label:"民族",prop:"staffNation",width:"48px"}}),a("el-table-column",{key:"staffReligion",attrs:{align:"center",label:"宗教",prop:"staffReligion",width:"48px"}}),a("el-table-column",{key:"remark",attrs:{align:"center",label:"员工评价",prop:"remark"}}),a("el-table-column",{key:"staffJobStatus",attrs:{align:"center",label:"状态",width:"58px"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-switch",{attrs:{"active-value":"0","inactive-value":"1"},on:{change:function(a){return e.handleStatusChange(t.row)}},model:{value:t.row.staffJobStatus,callback:function(a){e.$set(t.row,"staffJobStatus",a)},expression:"scope.row.staffJobStatus"}})]}}])}),a("el-table-column",{attrs:{align:"center","class-name":"small-padding fixed-width",label:"操作",width:"100px"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:extstaff:edit"],expression:"['system:extstaff:edit']"}],staticStyle:{margin:"2px"},attrs:{size:e.size,icon:"el-icon-edit",type:"success"},on:{click:function(a){return e.handleUpdate(t.row)}}},[e._v("修改 ")]),a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:extstaff:remove"],expression:"['system:extstaff:remove']"}],staticStyle:{margin:"2px"},attrs:{size:e.size,icon:"el-icon-delete",type:"danger"},on:{click:function(a){return e.handleDelete(t.row)}}},[e._v("删除 ")])]}}])})],1)],1),a("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"},{name:"dialogDragWidth",rawName:"v-dialogDragWidth"}],attrs:{"close-on-click-modal":!1,"append-to-body":!0,"modal-append-to-body":!1,title:e.title,visible:e.oopen,width:"800px"},on:{"update:visible":function(t){e.oopen=t}}},[a("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"88px"}},[a("el-form-item",{attrs:{label:"所属公司："}},[a("h4",{staticStyle:{margin:"0","font-weight":"bold"}},[e._v(e._s(e.company.companyTaxCode)+" "),a("span",{staticStyle:{"font-size":"large"}},[e._v(e._s(e.company.companyShortName))])]),e._v(" "+e._s(e.company.companyLocalName)+" ")]),a("el-form-item",{attrs:{label:"姓名称谓："}},[a("el-row",[a("el-col",{attrs:{span:8}},[a("el-input",{attrs:{placeholder:"正式称谓"},model:{value:e.form.staffShortName,callback:function(t){e.$set(e.form,"staffShortName",t)},expression:"form.staffShortName"}})],1),a("el-col",{attrs:{span:8}},[a("el-input",{attrs:{placeholder:"中文姓名"},model:{value:e.form.staffLocalName,callback:function(t){e.$set(e.form,"staffLocalName",t)},expression:"form.staffLocalName"}})],1),a("el-col",{attrs:{span:8}},[a("el-input",{attrs:{placeholder:"英文姓名"},model:{value:e.form.staffEnName,callback:function(t){e.$set(e.form,"staffEnName",t)},expression:"form.staffEnName"}})],1)],1)],1),a("el-form-item",{attrs:{label:"部门职位："}},[a("el-row",[a("el-col",{attrs:{span:8}},[a("el-input",{attrs:{placeholder:"部门名称，手动输入"},model:{value:e.form.deptName,callback:function(t){e.$set(e.form,"deptName",t)},expression:"form.deptName"}})],1),a("el-col",{attrs:{span:8}},[a("el-input",{attrs:{placeholder:"职位名称，手动输入"},model:{value:e.form.deptPositionName,callback:function(t){e.$set(e.form,"deptPositionName",t)},expression:"form.deptPositionName"}})],1),a("el-col",{attrs:{span:8}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"是否主要联系人"},model:{value:e.form.isMain,callback:function(t){e.$set(e.form,"isMain",t)},expression:"form.isMain"}},e._l(e.dict.type.sys_yes_no,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1)],1)],1),a("el-form-item",{attrs:{label:"联系方式："}},[a("el-row",[a("el-col",{attrs:{span:8}},[a("el-input",{attrs:{placeholder:"手机号码"},model:{value:e.form.staffPhoneNum,callback:function(t){e.$set(e.form,"staffPhoneNum",t)},expression:"form.staffPhoneNum"}})],1),a("el-col",{attrs:{span:8}},[a("el-input",{attrs:{placeholder:"微信"},model:{value:e.form.staffWechat,callback:function(t){e.$set(e.form,"staffWechat",t)},expression:"form.staffWechat"}})],1),a("el-col",{attrs:{span:8}},[a("el-input",{attrs:{placeholder:"QQ"},model:{value:e.form.staffQq,callback:function(t){e.$set(e.form,"staffQq",t)},expression:"form.staffQq"}})],1),a("el-col",{attrs:{span:8}},[a("el-input",{attrs:{placeholder:"Whatsapp"},model:{value:e.form.staffWhatsapp,callback:function(t){e.$set(e.form,"staffWhatsapp",t)},expression:"form.staffWhatsapp"}})],1),a("el-col",{attrs:{span:8}},[a("el-input",{attrs:{placeholder:"其他联系方式"},model:{value:e.form.staffOtherContact,callback:function(t){e.$set(e.form,"staffOtherContact",t)},expression:"form.staffOtherContact"}})],1),a("el-col",{attrs:{span:8}},[a("el-input",{attrs:{placeholder:"固定电话"},model:{value:e.form.staffTelNum,callback:function(t){e.$set(e.form,"staffTelNum",t)},expression:"form.staffTelNum"}})],1),a("el-col",{attrs:{span:16}},[a("el-input",{attrs:{placeholder:"企业邮箱"},model:{value:e.form.staffEmailEnterprise,callback:function(t){e.$set(e.form,"staffEmailEnterprise",t)},expression:"form.staffEmailEnterprise"}})],1)],1)],1),a("el-form-item",{attrs:{label:"个人信息："}},[a("el-row",[a("el-col",{attrs:{span:8}},[a("el-select",{staticStyle:{width:"100%"},model:{value:e.form.staffGender,callback:function(t){e.$set(e.form,"staffGender",t)},expression:"form.staffGender"}},[a("el-option",{attrs:{label:"0"}},[e._v("男")]),a("el-option",{attrs:{label:"1"}},[e._v("女")])],1)],1),a("el-col",{attrs:{span:8}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{clearable:"",placeholder:"生日",type:"date","value-format":"yyyy-MM-dd"},model:{value:e.form.staffBirthday,callback:function(t){e.$set(e.form,"staffBirthday",t)},expression:"form.staffBirthday"}})],1),a("el-col",{attrs:{span:8}},[a("el-input",{attrs:{placeholder:"擅长语种"},model:{value:e.form.staffLanguage,callback:function(t){e.$set(e.form,"staffLanguage",t)},expression:"form.staffLanguage"}})],1),a("el-col",{attrs:{span:8}},[a("el-input",{attrs:{placeholder:"国籍"},model:{value:e.form.staffCountry,callback:function(t){e.$set(e.form,"staffCountry",t)},expression:"form.staffCountry"}})],1),a("el-col",{attrs:{span:8}},[a("el-input",{attrs:{placeholder:"民族"},model:{value:e.form.staffNation,callback:function(t){e.$set(e.form,"staffNation",t)},expression:"form.staffNation"}})],1),a("el-col",{attrs:{span:8}},[a("el-input",{attrs:{placeholder:"宗教信仰"},model:{value:e.form.staffReligion,callback:function(t){e.$set(e.form,"staffReligion",t)},expression:"form.staffReligion"}})],1),a("el-col",{attrs:{span:8}},[a("location-select",{ref:"location",attrs:{pass:e.form.locationId,type:"location"},on:{return:e.getLocationId}})],1),a("el-col",{attrs:{span:16}},[a("el-input",{attrs:{placeholder:"详细住址"},model:{value:e.form.staffAddress,callback:function(t){e.$set(e.form,"staffAddress",t)},expression:"form.staffAddress"}})],1)],1)],1),a("el-form-item",{attrs:{label:"备注信息：",prop:"remark"}},[a("el-input",{attrs:{maxlength:"200",placeholder:"内容","show-word-limit":"",type:"textarea"},model:{value:e.form.remark,callback:function(t){e.$set(e.form,"remark",t)},expression:"form.remark"}})],1),a("el-form-item",{attrs:{label:"员工状态：",prop:"staffJobStatus"}},[a("el-select",{model:{value:e.form.staffJobStatus,callback:function(t){e.$set(e.form,"staffJobStatus",t)},expression:"form.staffJobStatus"}},e._l(e.dict.type.sys_normal_disable,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("确 定")]),a("el-button",{on:{click:e.cancel}},[e._v("取 消")])],1)],1)],1)},r=[],n=a("b857"),s={name:"staffInfo",dicts:["sys_user_sex","sys_normal_disable","sys_yes_no"],props:["loadOptions","open","company"],data:function(){return{size:this.$store.state.app.size||"mini",single:!0,multiple:!0,openContent:!1,loading:!0,oopen:!1,title:null,extStaffList:[],form:{},queryParams:{sqdCompanyId:null,staffShortName:null,staffLocalName:null,staffEnName:null,staffTelNum:null,staffEmailEnterprise:null,staffPhoneNum:null,staffBirthday:null,staffGender:null,staffWechat:null,staffQq:null,staffCountry:null,staffNation:null,staffReligion:null,locationId:null,staffAddress:null,staffLanguage:null,deptName:null,deptPositionName:null,staffJobStatus:null},rules:{}}},watch:{loadOptions:function(){this.extStaffList=this.loadOptions,this.loading=!1},open:function(e){this.openContent=e},openContent:function(e){0==e&&this.$emit("openStaffs")}},methods:{getList:function(){var e=this;this.loading=!0,this.queryParams.sqdCompanyId=this.company.companyId,Object(n["e"])(this.queryParams).then((function(t){e.extStaffList=t.data,e.loading=!1}))},cancel:function(){this.oopen=!1,this.reset()},reset:function(){this.form={staffId:null,sqdCompanyId:null,staffShortName:null,staffLocalName:null,staffEnName:null,staffTelNum:null,staffEmailEnterprise:null,credentialTypeId:null,staffPhoneNum:null,staffBirthday:null,staffGender:null,locationId:null,staffAddress:null,staffLanguage:null,deptName:null,deptPositionName:null,staffJobStatus:0,isMain:"N"},this.resetForm("form")},handleAdd:function(){this.reset(),this.oopen=!0,this.title="添加外部员工"},handleUpdate:function(e){var t=this;this.reset();var a=e.staffId||this.ids;Object(n["d"])(a).then((function(a){t.form=a.data,t.oopen=!0,t.title="修改外部员工",t.$refs.location.remoteMethod(e.locationLocalName)}))},submitForm:function(){var e=this;this.$refs["form"].validate((function(t){t&&(e.form.sqdCompanyId=e.company.companyId,null!=e.form.staffId?Object(n["f"])(e.form).then((function(t){e.$modal.msgSuccess("修改成功"),e.oopen=!1,e.getList()})):Object(n["a"])(e.form).then((function(t){e.$modal.msgSuccess("新增成功"),e.oopen=!1,e.getList()})))}))},handleDelete:function(e){var t=this,a=e.staffId||this.ids;this.$confirm('是否确认删除外部员工编号为"'+a+'"的数据项？',"提示",{customClass:"modal-confirm"}).then((function(){return Object(n["c"])(a)})).then((function(){t.getList(),t.$modal.msgSuccess("删除成功")})).catch((function(){}))},handleStatusChange:function(e){var t=this,a="0"==e.staffJobStatus?"启用":"停用",o=(null!=e.staffShortName?e.staffShortName:"")+(null!=e.staffLocalName?e.staffLocalName:"")+(null!=e.staffEnName?e.staffEnName:"");this.$confirm('确认要"'+a+'""'+o+'"用户吗？',"提示",{customClass:"modal-confirm"}).then((function(){return Object(n["b"])(e.staffId,e.staffJobStatus)})).then((function(){t.$modal.msgSuccess(a+"成功")})).catch((function(){e.staffJobStatus="0"==e.staffJobStatus?"1":"0"}))},getLocationId:function(e){this.form.locationId=e},handleClose:function(){}}},l=s,i=a("2877"),c=Object(i["a"])(l,o,r,!1,null,null,null);t["default"]=c.exports},9567:function(e,t,a){"use strict";a.r(t);var o=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("el-tooltip",{attrs:{disabled:null==e.scope.row.serviceType||e.scope.row.serviceType.length<15,placement:"top"}},[a("div",{attrs:{slot:"content"},slot:"content"},[a("h6",{staticStyle:{margin:"0"}},[e._v(" "+e._s(e.scope.row.serviceType)+" ")])]),a("div",[a("h6",{staticStyle:{margin:"0",overflow:"hidden","text-overflow":"ellipsis"}},[e._v(" "+e._s(null!=e.scope.row.serviceType?e.scope.row.serviceType.length>15?e.scope.row.serviceType.substring(0,15)+"...":e.scope.row.serviceType:"")+" ")])])])],1)},r=[],n={name:"serviceType",props:["scope"]},s=n,l=a("2877"),i=Object(l["a"])(s,o,r,!1,null,"56054c56",null);t["default"]=i.exports},"998b":function(e,t,a){"use strict";a.d(t,"d",(function(){return r})),a.d(t,"c",(function(){return n})),a.d(t,"a",(function(){return s})),a.d(t,"e",(function(){return l})),a.d(t,"b",(function(){return i}));var o=a("b775");function r(e){return Object(o["a"])({url:"/system/communication/list",method:"get",params:e})}function n(e){return Object(o["a"])({url:"/system/communication/"+e,method:"get"})}function s(e){return Object(o["a"])({url:"/system/communication",method:"post",data:e})}function l(e){return Object(o["a"])({url:"/system/communication",method:"put",data:e})}function i(e){return Object(o["a"])({url:"/system/communication/"+e,method:"delete"})}},"9c4f":function(e,t,a){"use strict";a.r(t);var o=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:company:list"],expression:"['system:company:list']"}],attrs:{size:"mini",type:"text"},on:{click:function(t){return e.checkBank(e.scope.row)}}},[e._v(" "+e._s("[···]")+" ")])],1)},r=[],n={name:"account",props:["scope"],data:function(){return{size:this.$store.state.app.size||"mini"}},methods:{checkBank:function(e){this.$emit("return",{key:"account",value:e})}}},s=n,l=a("2877"),i=Object(l["a"])(s,o,r,!1,null,"0e993626",null);t["default"]=i.exports},"9dd5":function(e,t,a){"use strict";a.r(t);var o=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"},{name:"dialogDragWidth",rawName:"v-dialogDragWidth"}],staticClass:"accountInfo",attrs:{"close-on-click-modal":!1,"close-on-click":!1,visible:e.openContent,appear:"fadeIn",title:"银行信息","append-to-body":"",width:"1800px"},on:{"update:visible":function(t){e.openContent=t}}},[a("el-row",{staticClass:"mb8",attrs:{gutter:10}},[a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:account:add"],expression:"['system:account:add']"}],attrs:{size:e.size,icon:"el-icon-plus",plain:"",type:"primary"},on:{click:e.handleAdd}},[e._v("新增 ")])],1)],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.accountList,border:"",stripe:""}},[a("el-table-column",{attrs:{align:"center",label:"序号",prop:"bankAccId",width:"100px"}}),a("el-table-column",{attrs:{align:"center",label:"账号代码",prop:"bankAccCode",width:"100px"}}),a("el-table-column",{attrs:{align:"center",label:"账号归属",prop:"defaultCurrencyCode",width:"150px"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("h6",{staticClass:"column-text"},[e._v(" "+e._s(t.row.benefitLocalName)+" ")]),a("h6",{staticClass:"column-text"},[e._v(" "+e._s(t.row.benefitTax)+" ")])]}}])}),a("el-table-column",{attrs:{align:"center",label:"户名",prop:"benefitLocalName",width:"200px"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("h6",{staticClass:"column-text"},[e._v(" "+e._s(t.row.benefitLocalName)+" ")]),a("h6",{staticClass:"column-text"},[e._v(" "+e._s(t.row.benefitEnName)+" ")])]}}])}),a("el-table-column",{attrs:{align:"center",label:"币种账户",prop:"benefitEnName",width:"200px"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("h6",{staticClass:"column-text"},[e._v(" "+e._s((t.row.defaultCurrencyCode?t.row.defaultCurrencyCode:"")+(0==t.row.isOfficialAcc?" 公":" 私")+"户")+" ")]),a("h6",{staticClass:"column-text"},[e._v(e._s(t.row.bankAccount))])]}}])}),a("el-table-column",{attrs:{align:"center",label:"开户行名址",prop:"bankName",width:"200px"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("h6",{staticClass:"column-text"},[e._v(e._s(t.row.bankName?t.row.bankName+":":""+t.row.bankBranchName)+" ")]),a("h6",{staticClass:"column-text"},[e._v(" "+e._s((t.row.branchLocation?t.row.branchLocation+":":"")+t.row.branchAddress)+" ")])]}}])}),a("el-table-column",{attrs:{align:"center",label:"汇款码",prop:"bankBranchName",width:"200px"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("h6",{staticClass:"column-text"},[e._v(e._s(t.row.swiftCode))]),a("h6",{staticClass:"column-text"},[e._v(e._s(t.row.intlExchangeCode))])]}}])}),a("el-table-column",{attrs:{align:"center",label:"账户状态",prop:"benefitTax",width:"100px"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(e.confirmState(t.row))+" ")]}}])}),a("el-table-column",{attrs:{align:"center",label:"账户概要",prop:"bankAccSummary",width:"100px"}}),a("el-table-column",{attrs:{align:"center",label:"录入人",prop:"branchAddress"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("h6",{staticClass:"column-text"},[e._v(e._s(t.row.updateBy?e.getStaff(t.row.updateBy):""))]),a("h6",{staticClass:"column-text"},[e._v(e._s(t.row.updateTime))])]}}])}),a("el-table-column",{attrs:{align:"center",label:"确认状态",prop:"isOfficialAcc"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("h6",{staticClass:"column-text"},[e._v(e._s(e.returnConfirmDept(t.row)))])]}}])}),a("el-table-column",{attrs:{align:"center",label:"操作",width:"58px"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("div",{staticStyle:{height:"15px"}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:account:edit"],expression:"['system:account:edit']"}],staticStyle:{display:"flex",padding:"0",margin:"0"},attrs:{size:e.size,icon:"el-icon-edit",type:"success"},on:{click:function(a){return e.handleUpdate(t.row)}}},[e._v("修改 ")])],1),a("div",{staticStyle:{height:"15px"}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:account:remove"],expression:"['system:account:remove']"}],staticStyle:{display:"flex",padding:"0",margin:"0"},attrs:{size:e.size,icon:"el-icon-delete",type:"danger"},on:{click:function(a){return e.handleDelete(t.row)}}},[e._v("删除 ")])],1)]}}])})],1),a("span",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:function(t){e.openContent=!1}}},[e._v("确 定")]),a("el-button",{on:{click:function(t){e.openContent=!1}}},[e._v("取 消")])],1)],1),a("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"},{name:"dialogDragWidth",rawName:"v-dialogDragWidth"}],attrs:{"append-to-body":!0,"close-on-click-modal":!1,title:e.title,visible:e.oopen,width:"500px"},on:{"update:visible":function(t){e.oopen=t}}},[a("el-form",{ref:"form",staticClass:"edit",attrs:{disabled:1==this.form.salesConfirmed||1==this.form.accConfirmed||1==this.form.psaConfirmed||1==this.form.opConfirmed,model:e.form,rules:e.rules,"label-width":"100px"}},[a("el-form-item",{attrs:{label:"账户代码",prop:"bankAccCode"}},[a("el-input",{attrs:{placeholder:"银行账户代码"},model:{value:e.form.bankAccCode,callback:function(t){e.$set(e.form,"bankAccCode",t)},expression:"form.bankAccCode"}})],1),a("el-form-item",{attrs:{label:"银行账号",prop:"bankAccount"}},[a("el-input",{attrs:{placeholder:"银行账户号码"},model:{value:e.form.bankAccount,callback:function(t){e.$set(e.form,"bankAccount",t)},expression:"form.bankAccount"}})],1),a("el-form-item",{attrs:{label:"账户归属",prop:"accBelongsTo"}},[e._v(" "+e._s(e.company.companyLocalName?e.company.companyLocalName:e.company.companyShortName+"   "+e.company.companyEnName)+" ")]),a("el-form-item",{attrs:{label:"中文户名",prop:"benefitLocalName"}},[a("el-input",{attrs:{placeholder:"中文户名"},model:{value:e.form.benefitLocalName,callback:function(t){e.$set(e.form,"benefitLocalName",t)},expression:"form.benefitLocalName"}})],1),a("el-form-item",{attrs:{label:"英文户名",prop:"benefitEnName"}},[a("el-input",{attrs:{placeholder:"中文户名"},model:{value:e.form.benefitEnName,callback:function(t){e.$set(e.form,"benefitEnName",t)},expression:"form.benefitEnName"}})],1),a("el-form-item",{attrs:{label:"开户总行",prop:"bankName"}},[a("el-input",{attrs:{placeholder:"开户总行"},model:{value:e.form.bankName,callback:function(t){e.$set(e.form,"bankName",t)},expression:"form.bankName"}})],1),a("el-form-item",{attrs:{label:"开户支行",prop:"bankBranchName"}},[a("el-input",{attrs:{placeholder:"开户支行"},model:{value:e.form.bankBranchName,callback:function(t){e.$set(e.form,"bankBranchName",t)},expression:"form.bankBranchName"}})],1),a("el-form-item",{attrs:{label:"支行地区"}},[a("el-input",{attrs:{placeholder:"开户支行地区"},model:{value:e.form.bankBranchLocation,callback:function(t){e.$set(e.form,"bankBranchLocation",t)},expression:"form.bankBranchLocation"}})],1),a("el-form-item",{attrs:{label:"支行详址",prop:"bankBranchAddress"}},[a("el-input",{attrs:{placeholder:"开户详址"},model:{value:e.form.bankBranchAddress,callback:function(t){e.$set(e.form,"bankBranchAddress",t)},expression:"form.bankBranchAddress"}})],1),a("el-form-item",{attrs:{label:"英文地址",prop:"bankBranchAddressEnName"}},[a("el-input",{attrs:{placeholder:"支行英文地址"},model:{value:e.form.bankBranchAddressEnName,callback:function(t){e.$set(e.form,"bankBranchAddressEnName",t)},expression:"form.bankBranchAddressEnName"}})],1),a("el-form-item",{attrs:{label:"公司税号",prop:"benefitTax"}},[a("el-input",{attrs:{placeholder:"公司税号"},model:{value:e.form.benefitTax,callback:function(t){e.$set(e.form,"benefitTax",t)},expression:"form.benefitTax"}})],1),a("el-form-item",{attrs:{label:"SWIFT码",prop:"swiftCode"}},[a("el-input",{attrs:{placeholder:"SWIFT编码"},model:{value:e.form.swiftCode,callback:function(t){e.$set(e.form,"swiftCode",t)},expression:"form.swiftCode"}})],1),a("el-form-item",{attrs:{label:"币种"}},[a("tree-select",{attrs:{disabled:1==this.form.salesConfirmed||1==this.form.accConfirmed||1==this.form.psaConfirmed||1==this.form.opConfirmed,pass:e.form.defaultCurrencyCode,type:"currency"},on:{return:e.getCurrencyId}})],1),a("el-form-item",{attrs:{label:"是否公账",prop:"isOfficialAcc"}},[a("el-switch",{attrs:{"active-text":"公账","inactive-text":"私账"},model:{value:e.isOfficialAcc,callback:function(t){e.isOfficialAcc=t},expression:"isOfficialAcc"}})],1),a("el-form-item",{attrs:{label:"账户排序",prop:"accountPriority"}},[a("el-input",{attrs:{placeholder:"银行卡优先级"},model:{value:e.form.accountPriority,callback:function(t){e.$set(e.form,"accountPriority",t)},expression:"form.accountPriority"}})],1),a("el-form-item",{attrs:{label:"备注",prop:"remark"}},[a("el-input",{attrs:{placeholder:"备注"},model:{value:e.form.remark,callback:function(t){e.$set(e.form,"remark",t)},expression:"form.remark"}})],1),a("el-form-item",{attrs:{label:"账户概要",prop:"remark"}},[a("el-input",{attrs:{placeholder:"账户概要"},model:{value:e.form.bankAccSummary,callback:function(t){e.$set(e.form,"bankAccSummary",t)},expression:"form.bankAccSummary"}})],1),e.add?e._e():a("el-row",[a("el-form-item",{attrs:{label:"是否已锁定",prop:"isLocked"}},[a("dict-tag",{attrs:{options:e.dict.type.sys_is_lock,value:e.form.isLocked}})],1)],1)],1),a("div",{staticClass:"confirmed-list"},[a("confirmed",{attrs:{id:"bankAccId",confirmed:0==this.form.salesConfirmed,row:e.form,type:"sales"},on:{lockMethod:e.updateAccount}}),a("confirmed",{attrs:{id:"bankAccId",confirmed:0==this.form.psaConfirmed,row:e.form,type:"psa"},on:{lockMethod:e.updateAccount}}),a("confirmed",{attrs:{id:"bankAccId",confirmed:0==this.form.opConfirmed,row:e.form,type:"op"},on:{lockMethod:e.updateAccount}}),a("confirmed",{attrs:{id:"bankAccId",confirmed:0==this.form.accConfirmed,row:e.form,type:"acc"},on:{lockMethod:e.updateAccount}})],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("确 定")]),a("el-button",{on:{click:e.cancel}},[e._v("取 消")])],1)],1)],1)},r=[],n=(a("4de4"),a("d3b7"),a("a287")),s=(a("1231"),a("37ff")),l=a("4360"),i={name:"accountInfo",computed:{confirmed:function(){return s["a"]}},components:{Confirmed:s["a"]},dicts:["sys_account_type","sys_is_confirm","sys_is_lock"],props:["type","open","loadOptions","company","isLock"],data:function(){return{size:this.$store.state.app.size||"mini",loading:!0,openContent:!1,oopen:!1,title:null,accountList:[],form:{defaultCurrencyCode:"RMB"},belongTo:"company"==this.type?1:"staff"==this.type?0:"",belongToCompany:this.company.companyId,queryParams:{pageNum:1,pageSize:20,bankAccId:this.bankAccId,belongTo:this.belongTo},rules:{},isOfficialAcc:!0,add:!0,staffList:[]}},watch:{loadOptions:function(){this.accountList=this.loadOptions,this.loading=!1},open:function(e){this.openContent=e},openContent:function(e){0==e&&this.$emit("openAccounts")}},created:function(){this.loadAllStaffList()},methods:{getList:function(){var e=this;this.loading=!0,this.queryParams.accountOwnerId=this.accountOwnerId,this.queryParams.belongTo=this.belongTo,1===this.belongTo?this.queryParams.belongToCompany=this.company.companyId:this.queryParams.belongToStaff=this.company.companyId,Object(n["e"])(this.queryParams).then((function(t){e.accountList=t.rows,e.total=t.total,e.loading=!1}))},cancel:function(){this.oopen=!1,this.reset()},handleUpdate:function(e){var t=this;this.reset();var a=e.bankAccId;this.add=!1,Object(n["d"])(a).then((function(e){t.form=e.data,t.isOfficialAcc=0==e.data.isOfficialAcc,t.oopen=!0,t.title="修改银行账户"}))},submitForm:function(){var e=this;this.$refs["form"].validate((function(t){t&&(e.form.belongToCompany=e.company.companyId,e.isOfficialAcc?e.form.isOfficialAcc=0:e.form.isOfficialAcc=1,e.form.sqdBelongToCompanyCode=e.company.companyIntlCode,null!=e.form.bankAccId?Object(n["f"])(e.form).then((function(t){e.$modal.msgSuccess("修改成功"),e.oopen=!1,e.getList()})):Object(n["a"])(e.form).then((function(t){e.$modal.msgSuccess("新增成功"),e.oopen=!1,e.getList()})))}))},handleAdd:function(){this.reset(),this.oopen=!0,this.form.isOfficialAcc=!0,this.title="添加银行账户",this.add=!0},handleDelete:function(e){var t=this,a=e.bankAccId||this.ids;this.$confirm('是否确认删除银行账户编号为"'+a+'"的数据项？',"提示",{customClass:"modal-confirm"}).then((function(){return Object(n["c"])(a)})).then((function(){t.getList(),t.$modal.msgSuccess("删除成功")})).catch((function(){}))},reset:function(){this.form={bankAccId:null,bankAccount:null,accountOwnerid:null,currencyId:null,accountLocalName:null,accountAddressLocalName:null,accountEnName:null,accountAddressEnName:null,accountTaxcode:null,accountSwiftcode:null,accountIntlcode:null,accountIsOfficial:null,accountPriority:null,remark:null,salesConfirmed:0,salesConfirmedId:null,salesConfirmedName:null,salesConfirmedDate:null,psaConfirmed:0,psaConfirmedId:null,psaConfirmedName:null,psaConfirmedDate:null,accConfirmed:0,accConfirmedId:null,accConfirmedName:null,accConfirmedDate:null,opConfirmed:0,opConfirmedId:null,opConfirmedName:null,opConfirmedDate:null},this.resetForm("form")},getLocationId:function(e){this.form.branchLocation=e},getCurrencyId:function(e){this.form.defaultCurrencyCode=e},lock:function(){return!this.isLock},handleClose:function(e){this.$confirm("确认关闭？","提示",{customClass:"modal-confirm"}).then((function(t){e()})).catch((function(e){}))},updateAccount:function(e){var t=this;console.log(e),Object(n["f"])(e).then((function(e){t.$modal.msgSuccess("修改成功"),Object(n["d"])(t.form.bankAccId).then((function(e){t.form=e.data}))}))},confirmState:function(e){return 1==e.salesConfirmed&&1==e.accConfirmed&&1==e.psaConfirmed&&1==e.opConfirmed?"审核完成":1==e.salesConfirmed||1==e.accConfirmed||1==e.psaConfirmed||1==e.opConfirmed?"审核中":"待审核"},loadAllStaffList:function(){var e=this;0==this.$store.state.data.allRsStaffList.length||this.$store.state.data.redisList.allRsStaffList?l["a"].dispatch("getAllRsStaffList").then((function(){e.staffList=e.$store.state.data.allRsStaffList})):this.staffList=this.$store.state.data.allRsStaffList},getStaff:function(e){return this.staffList.filter((function(t){return t.staffId===e}))[0]?this.staffList.filter((function(t){return t.staffId===e}))[0].staffGivingEnName:""},returnConfirmDept:function(e){var t="";return 1==e.salesConfirmed&&(t&&(t+="/"),t+="业务"),1==e.accConfirmed&&(t&&(t+="/"),t+="财务"),1==e.psaConfirmed&&(t&&(t+="/"),t+="商务"),1==e.opConfirmed&&(t&&(t+="/"),t+="操作"),t},deptLock:function(){var e=this;null!=this.form.bankAccId?(this.form.salesConfirmed=0==this.form.salesConfirmed?1:0,this.form.salesConfirmedId=this.$store.state.user.sid,Object(n["f"])(this.form).then((function(t){e.$modal.msgSuccess("修改成功"),e.oopen=!1,e.getList()}))):this.$modal.msgError("错误操作")},financeLock:function(){var e=this;null!=this.form.bankAccId?(this.form.accConfirmed=0==this.form.accConfirmed?1:0,this.form.accConfirmedId=this.$store.state.user.sid,Object(n["f"])(this.form).then((function(t){e.$modal.msgSuccess("修改成功"),e.oopen=!1,e.getList()}))):this.$modal.msgError("错误操作")},opLock:function(){var e=this;null!=this.form.bankAccId?(this.form.accConfirmed=0==this.form.accConfirmed?1:0,this.form.accConfirmedId=this.$store.state.user.sid,Object(n["f"])(this.form).then((function(t){e.$modal.msgSuccess("修改成功"),e.oopen=!1,e.getList()}))):this.$modal.msgError("错误操作")},psaLock:function(){var e=this;null!=this.form.bankAccId?(this.form.accConfirmed=0==this.form.accConfirmed?1:0,this.form.accConfirmedId=this.$store.state.user.sid,Object(n["f"])(this.form).then((function(t){e.$modal.msgSuccess("修改成功"),e.oopen=!1,e.getList()}))):this.$modal.msgError("错误操作")}}},c=i,m=(a("1228"),a("2877")),d=Object(m["a"])(c,o,r,!1,null,"ad20e10a",null);t["default"]=d.exports},a603:function(e,t,a){"use strict";a.r(t);var o=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("el-tooltip",{attrs:{"open-delay":500,disabled:null==e.scope.row.staff,placement:"top"}},[a("div",{attrs:{slot:"content"},slot:"content"}),a("div",{staticClass:"content"},[a("span"),a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:company:list"],expression:"['system:company:list']"}],staticStyle:{padding:"0",display:"flex"},attrs:{size:e.size,type:"text"},on:{click:function(t){return e.checkConnect(e.scope.row)}}},[a("div",{staticStyle:{width:"50%",height:"50%"}},[e._v(" "+e._s(null!==e.scope.row.mainStaffOfficialName?"["+e.scope.row.mainStaffOfficialName+"]":"[···]")+" ")])])],1)])],1)},r=[],n={name:"contactor",props:["scope"],data:function(){return{size:this.$store.state.app.size||"mini"}},methods:{checkConnect:function(e){this.$emit("return",{key:"contactor",value:e})}}},s=n,l=(a("e757"),a("2877")),i=Object(l["a"])(s,o,r,!1,null,"18826024",null);t["default"]=i.exports},ac5e:function(e,t,a){"use strict";a.r(t);var o=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("el-row",[a("el-col",{attrs:{span:18}},[a("h6",{staticStyle:{margin:"0",float:"left"}},[e._v(" "+e._s(null!=e.scope.row.agreementRecord?(null!=e.scope.row.agreementRecord.creditDays?e.scope.row.agreementRecord.creditDays:"")+"月":" ")+" ")])]),a("el-col",{attrs:{span:6}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:company:list"],expression:"['system:company:list']"}],staticStyle:{padding:"0",display:"flex",margin:"auto",float:"right"},attrs:{size:e.size,type:"text"},on:{click:function(t){return e.checkAgreement(e.scope.row)}}},[e._v(" "+e._s("[···]")+" ")])],1)],1),a("h6",{staticStyle:{margin:"0","font-weight":"bold"}},[e._v(" "+e._s(null!=e.scope.row.agreementRecord?(null!=e.scope.row.agreementRecord.currencyCode?e.scope.row.agreementRecord.currencyCode:"")+" "+(null!=e.scope.row.agreementRecord.creditLimit?e.scope.row.agreementRecord.creditLimit+"W":" "):" ")+" ")]),null!=e.scope.row.agreementRecord&&(new Date(e.scope.row.agreementRecord.agreementEndDate)-new Date(Date.now()))/1e3/60/60/24<30?a("h6",{staticStyle:{margin:"0",color:"red"}},[e._v(" "+e._s(null!=e.scope.row.agreementRecord?e.scope.row.agreementRecord.agreementEndDate:" ")+" ")]):a("h6",{staticStyle:{margin:"0",color:"darkgray"}},[e._v(" "+e._s(null!=e.scope.row.agreementRecord?e.scope.row.agreementRecord.agreementEndDate:" ")+" ")])],1)},r=[],n={name:"agreement",props:["scope"],data:function(){return{size:this.$store.state.app.size||"mini"}},methods:{checkAgreement:function(e){this.$emit("return",{key:"agreement",value:e})}}},s=n,l=a("2877"),i=Object(l["a"])(s,o,r,!1,null,"09b251b0",null);t["default"]=i.exports},b857:function(e,t,a){"use strict";a.d(t,"e",(function(){return r})),a.d(t,"d",(function(){return n})),a.d(t,"a",(function(){return s})),a.d(t,"f",(function(){return l})),a.d(t,"c",(function(){return i})),a.d(t,"b",(function(){return c}));var o=a("b775");function r(e){return Object(o["a"])({url:"/system/extStaff/list",method:"get",params:e})}function n(e){return Object(o["a"])({url:"/system/extStaff/"+e,method:"get"})}function s(e){return Object(o["a"])({url:"/system/extStaff",method:"post",data:e})}function l(e){return Object(o["a"])({url:"/system/extStaff",method:"put",data:e})}function i(e){return Object(o["a"])({url:"/system/extStaff/"+e,method:"delete"})}function c(e,t){var a={staffId:e,staffJobStatus:t};return Object(o["a"])({url:"/system/extStaff",method:"put",data:a})}},c06f:function(e,t,a){"use strict";a.r(t);var o=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"container"},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:communication:list"],expression:"['system:communication:list']"}],staticStyle:{padding:"0",display:"flex",margin:"auto",float:"right"},attrs:{size:e.size,type:"text"},on:{click:function(t){return e.checkCommunicateRecord(e.scope.row)}}},[e._v(" "+e._s("[···]")+" ")])],1)},r=[],n={name:"communication",props:["scope"],data:function(){return{size:this.$store.state.app.size||"mini"}},methods:{checkCommunicateRecord:function(e){this.$emit("return",{key:"communication",value:e})}}},s=n,l=(a("d7cb"),a("2877")),i=Object(l["a"])(s,o,r,!1,null,"4a101624",null);t["default"]=i.exports},c747:function(e,t,a){"use strict";a.r(t);var o=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("el-tooltip",{attrs:{placement:"top"}},[a("div",{attrs:{slot:"content"},slot:"content"},[a("h6",{staticStyle:{margin:"0"}},[e._v(" "+e._s((null!=e.scope.row.belongLocalName?e.scope.row.belongLocalName:"")+" "+(null!=e.scope.row.belongEnName?e.scope.row.belongEnName:""))+" ")]),a("h6",{staticClass:"column-text",staticStyle:{margin:"0"}},[e._v(" "+e._s((null!=e.scope.row.followLocalName?e.scope.row.followLocalName:"")+" "+(null!=e.scope.row.followEnName?e.scope.row.followEnName:""))+" ")])]),a("div",[a("dict-tag",{directives:[{name:"show",rawName:"v-show",value:null==e.scope.row.belongTo||0==e.scope.row.belongTo,expression:"scope.row.belongTo==null||scope.row.belongTo==0"}],attrs:{options:e.dict.type.sys_is_idle,value:"Y"}}),a("h6",{staticStyle:{margin:"0",overflow:"hidden","text-overflow":"ellipsis","white-space":"nowrap"}},[e._v(" "+e._s((null!=e.scope.row.belongLocalName?e.scope.row.belongLocalName:"")+" "+(null!=e.scope.row.belongEnName?e.scope.row.belongEnName:""))+" ")]),a("h6",{staticClass:"column-text",staticStyle:{margin:"0",overflow:"hidden","text-overflow":"ellipsis","white-space":"nowrap"}},[e._v(" "+e._s((null!=e.scope.row.followLocalName?e.scope.row.followLocalName:"")+" "+(null!=e.scope.row.followEnName?e.scope.row.followEnName:""))+" ")])],1)])],1)},r=[],n={name:"index",dicts:["sys_is_idle"],props:["scope"]},s=n,l=(a("5a72"),a("2877")),i=Object(l["a"])(s,o,r,!1,null,"80dfbb6e",null);t["default"]=i.exports},d7cb:function(e,t,a){"use strict";a("3977")},e0aa:function(e,t,a){"use strict";a.r(t);var o=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"},{name:"dialogDragWidth",rawName:"v-dialogDragWidth"}],attrs:{"close-on-click-modal":!1,"modal-append-to-body":!1,visible:e.openContent,"append-to-body":"",title:"协议列表",width:"1600px"},on:{"update:visible":function(t){e.openContent=t}}},[a("el-row",{staticClass:"mb8",attrs:{gutter:10}},[a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:agreementrecord:add"],expression:"['system:agreementrecord:add']"}],attrs:{icon:"el-icon-plus",plain:"",size:"mini",type:"primary"},on:{click:e.handleAdd}},[e._v("新增 ")])],1),a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:agreementrecord:remove"],expression:"['system:agreementrecord:remove']"}],attrs:{disabled:e.multiple,icon:"el-icon-delete",plain:"",size:"mini",type:"danger"},on:{click:e.handleDelete}},[e._v("删除 ")])],1),a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:agreementrecord:export"],expression:"['system:agreementrecord:export']"}],attrs:{icon:"el-icon-download",plain:"",size:"mini",type:"warning"},on:{click:e.handleExport}},[e._v("导出 ")])],1),a("right-toolbar",{attrs:{showSearch:e.showSearch},on:{"update:showSearch":function(t){e.showSearch=t},"update:show-search":function(t){e.showSearch=t},queryTable:e.getList}})],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.agreementrecordList,border:"",stripe:""},on:{"selection-change":e.handleSelectionChange}},[a("el-table-column",{attrs:{align:"left",type:"selection",width:"39px"}}),a("el-table-column",{attrs:{align:"center",label:"公司",prop:"sqdCompanyId",width:"69px"}},[a("div",[e._v(e._s(this.company.companyShortName))])]),a("el-table-column",{attrs:{align:"center",label:"协议号",prop:"agreementNumber",width:"69px"}}),a("el-table-column",{attrs:{align:"center",label:"协议类型",prop:"agreementTypeId","show-tooltip-when-overflow":"",width:"69px"}}),a("el-table-column",{attrs:{align:"center",label:"负责员工",prop:"staffName",width:"69px"}}),a("el-table-column",{attrs:{align:"center",label:"协议起始日",prop:"agreementStartDate",width:"85px"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(e.parseTime(t.row.agreementStartDate,"{y}-{m}-{d}")))])]}}])}),a("el-table-column",{attrs:{align:"center",label:"协议终止日",prop:"agreementEndDate",width:"85px"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(e.parseTime(t.row.agreementEndDate,"{y}-{m}-{d}")))])]}}])}),a("el-table-column",{attrs:{align:"center",label:"是否有效",prop:"isAvailable",width:"69px"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("dict-tag",{attrs:{options:e.dict.type.sys_yes_no,value:t.row.isAvailable}})]}}])}),a("el-table-column",{key:"creditLimit",attrs:{align:"center",label:"信用额度",width:"69px"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(t.row.creditLimit)+"万 ")]}}])}),a("el-table-column",{attrs:{align:"center",label:"协议币种",prop:"currencyId",width:"69px"}}),a("el-table-column",{attrs:{align:"center",label:"支付凭据",prop:"paymentDateNodeId",width:"69px"}}),a("el-table-column",{key:"creditDays",attrs:{align:"center",label:"信用周期",width:"69px"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(t.row.creditDays)+"月 ")]}}])}),a("el-table-column",{attrs:{align:"center",label:"是否工作日",prop:"isWorkingDay"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("dict-tag",{attrs:{options:e.dict.type.sys_yes_no,value:t.row.isWorkingDay}})]}}])}),a("el-table-column",{attrs:{align:"center",label:"信用评级",prop:"creditLevel",width:"69px"}}),a("el-table-column",{attrs:{align:"center",label:"是否锁定",prop:"isLocked",width:"69px"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("dict-tag",{attrs:{options:e.dict.type.sys_is_lock,value:t.row.isLocked}})]}}])}),a("el-table-column",{attrs:{align:"center",label:"经理确认时间",prop:"deptConfirmedDate",width:"100px"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(e.parseTime(t.row.deptConfirmedDate,"{y}-{m}-{d}")))])]}}])}),a("el-table-column",{attrs:{align:"center",label:"财务确认时间",prop:"financeConfirmedDate",width:"100px"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(e.parseTime(t.row.financeConfirmedDate,"{y}-{m}-{d}")))])]}}])}),a("el-table-column",{attrs:{align:"center",label:"结款日",prop:"settlementDate",width:"58px"}}),a("el-table-column",{attrs:{align:"center",label:"备注",prop:"remark",width:"48px"}}),a("el-table-column",{attrs:{align:"center","class-name":"small-padding fixed-width",label:"操作",width:"48px"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("div",{staticStyle:{height:"15px",padding:"0",margin:"0"}},["1"!=t.row.isLocked?a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:agreementrecord:edit"],expression:"['system:agreementrecord:edit']"}],staticStyle:{display:"flex"},attrs:{size:e.size,icon:"el-icon-edit",type:"success"},on:{click:function(a){return e.handleUpdate(t.row)}}},[e._v("修改 ")]):e._e()],1),a("div",{staticStyle:{height:"15px",padding:"0",margin:"0"}},["1"!=t.row.isLocked?a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:agreementrecord:remove"],expression:"['system:agreementrecord:remove']"}],staticStyle:{display:"flex"},attrs:{size:e.size,icon:"el-icon-delete",type:"danger"},on:{click:function(a){return e.handleDelete(t.row)}}},[e._v("删除 ")]):e._e()],1)]}}])})],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total>0"}],attrs:{limit:e.queryParams.pageSize,page:e.queryParams.pageNum,total:e.total},on:{"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},pagination:e.getList}})],1),a("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"},{name:"dialogDragWidth",rawName:"v-dialogDragWidth"}],attrs:{"append-to-body":!0,"close-on-click-modal":!1,"modal-append-to-body":!1,title:e.title,visible:e.oopen,width:"800px"},on:{"update:visible":function(t){e.oopen=t}}},[a("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"100px"}},[a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"合作公司",prop:"sqdCompanyId"}},[a("a",{staticStyle:{margin:"0","font-weight":"bold","font-size":"small"}},[e._v(e._s(this.company.companyShortName))])])],1)],1),a("el-row",[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"是否有效",prop:"isAvailable"}},[a("el-select",{attrs:{placeholder:"是否有效"},model:{value:e.form.isAvailable,callback:function(t){e.$set(e.form,"isAvailable",t)},expression:"form.isAvailable"}},e._l(e.dict.type.sys_yes_no,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"协议号",prop:"agreementNumber"}},[a("el-input",{attrs:{placeholder:"与合作公司签订的协议号"},model:{value:e.form.agreementNumber,callback:function(t){e.$set(e.form,"agreementNumber",t)},expression:"form.agreementNumber"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"协议类型",prop:"agreementTypeId"}},[a("el-input",{attrs:{placeholder:"协议类型"},model:{value:e.form.agreementTypeId,callback:function(t){e.$set(e.form,"agreementTypeId",t)},expression:"form.agreementTypeId"}})],1)],1)],1),a("el-row",[a("el-form-item",{attrs:{label:"负责员工",prop:"staffName"}},[e._v(" "+e._s(this.form.staffName)+" ")])],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"协议起始日",prop:"agreementStartDate"}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{clearable:"",placeholder:"协议有效期从",type:"date","value-format":"yyyy-MM-dd"},model:{value:e.form.agreementStartDate,callback:function(t){e.$set(e.form,"agreementStartDate",t)},expression:"form.agreementStartDate"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"协议终止日",prop:"agreementEndDate"}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{clearable:"",placeholder:"协议有效期至",type:"date","value-format":"yyyy-MM-dd"},model:{value:e.form.agreementEndDate,callback:function(t){e.$set(e.form,"agreementEndDate",t)},expression:"form.agreementEndDate"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"协议币种",prop:"currencyId"}},[a("tree-select",{attrs:{pass:e.form.currencyId,type:"currency"},on:{return:e.getCurrencyId}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"信用额度",prop:"creditLimit"}},[a("el-input-number",{attrs:{controls:!1,precision:2,step:.01,placeholder:"信用额度，默认0"},model:{value:e.form.creditLimit,callback:function(t){e.$set(e.form,"creditLimit",t)},expression:"form.creditLimit"}}),e._v(" 万 ")],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"支付凭据",prop:"paymentDateNodeId"}},[a("el-input",{attrs:{placeholder:"支付时间节点凭据"},model:{value:e.form.paymentDateNodeId,callback:function(t){e.$set(e.form,"paymentDateNodeId",t)},expression:"form.paymentDateNodeId"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"信用周期",prop:"creditDays"}},[a("el-input-number",{attrs:{controls:!1,max:12,precision:2,step:.01,placeholder:"信用周期"},model:{value:e.form.creditDays,callback:function(t){e.$set(e.form,"creditDays",t)},expression:"form.creditDays"}}),e._v(" 月 ")],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"信用评级",prop:"creditLevel"}},[a("el-input",{attrs:{placeholder:"信用评级，待定"},model:{value:e.form.creditLevel,callback:function(t){e.$set(e.form,"creditLevel",t)},expression:"form.creditLevel"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"是否工作日",prop:"isWorkingDay"}},[a("el-select",{attrs:{placeholder:"是否工作日"},model:{value:e.form.isWorkingDay,callback:function(t){e.$set(e.form,"isWorkingDay",t)},expression:"form.isWorkingDay"}},e._l(e.dict.type.sys_yes_no,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1)],1)],1),a("el-row",[a("el-form-item",{attrs:{label:"是否已锁定",prop:"isLocked"}},[a("dict-tag",{attrs:{options:e.dict.type.sys_is_lock,value:e.form.isLocked}})],1)],1),a("el-form-item",{attrs:{label:"结款日",prop:"settlementDate"}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{clearable:"",placeholder:"结款日",type:"date","value-format":"yyyy-MM-dd"},model:{value:e.form.settlementDate,callback:function(t){e.$set(e.form,"settlementDate",t)},expression:"form.settlementDate"}})],1),a("el-form-item",{attrs:{label:"备注",prop:"remark"}},[a("el-input",{attrs:{autosize:{minRows:10,maxRows:20},maxlength:"150",placeholder:"备注","show-word-limit":"",type:"textarea"},model:{value:e.form.remark,callback:function(t){e.$set(e.form,"remark",t)},expression:"form.remark"}})],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"部门确认",prop:"deptConfirmed"}},[a("el-row",[a("el-col",{attrs:{span:4}},[a("dict-tag",{attrs:{options:e.dict.type.sys_is_confirm,value:e.form.deptConfirmed}})],1),a("el-col",{attrs:{span:20}},[0==e.form.deptConfirmed?a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:agreementrecord:deptlock"],expression:"['system:agreementrecord:deptlock']"}],attrs:{icon:"el-icon-lock",plain:"",size:"mini",type:"primary"},on:{click:e.deptLock}},[e._v("锁定 ")]):e._e(),1==e.form.deptConfirmed?a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:agreementrecord:deptunlock"],expression:"['system:agreementrecord:deptunlock']"}],attrs:{icon:"el-icon-unlock",plain:"",size:"mini",type:"primary"},on:{click:e.deptLock}},[e._v("解锁 ")]):e._e()],1)],1)],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"部门确认时间",prop:"deptConfirmedDate"}},[e._v(" "+e._s(e.form.deptConfirmedDate)+" ")])],1)],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"财务确认",prop:"financeConfirmed"}},[a("el-row",[a("el-col",{attrs:{span:4}},[a("dict-tag",{attrs:{options:e.dict.type.sys_is_confirm,value:e.form.financeConfirmed}})],1),a("el-col",{attrs:{span:20}},[0==e.form.financeConfirmed?a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:agreementrecord:financelock"],expression:"['system:agreementrecord:financelock']"}],attrs:{icon:"el-icon-lock",plain:"",size:"mini",type:"primary"},on:{click:e.financeLock}},[e._v("锁定 ")]):e._e(),1==e.form.financeConfirmed?a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:agreementrecord:financeunlock"],expression:"['system:agreementrecord:financeunlock']"}],attrs:{icon:"el-icon-unlock",plain:"",size:"mini",type:"primary"},on:{click:e.financeLock}},[e._v("解锁 ")]):e._e()],1)],1)],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"财务确认时间",prop:"financeConfirmedDate"}},[e._v(" "+e._s(e.form.financeConfirmedDate)+" ")])],1)],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("确 定")]),a("el-button",{on:{click:e.cancel}},[e._v("取 消")])],1)],1)],1)},r=[],n=a("5530"),s=(a("d81d"),a("b0c0"),a("3964")),l={name:"agreementRecord",dicts:["sys_yes_no","sys_is_lock","sys_is_confirm"],props:["type","open","loadOptions","company"],data:function(){return{size:this.$store.state.app.size||"mini",openContent:!1,loading:!0,ids:[],single:!0,multiple:!0,showSearch:!0,total:0,agreementrecordList:[],title:"",oopen:!1,queryParams:{pageNum:1,pageSize:20,sqdCompanyId:this.company.companyId,agreementNumber:null,staffId:null},form:{},rules:{}}},watch:{loadOptions:function(){this.agreementrecordList=this.loadOptions,this.loading=!1},open:function(e){this.openContent=e},openContent:function(e){0==e&&this.$emit("openCommunications")}},methods:{getList:function(){var e=this;this.loading=!0,this.queryParams.sqdCompanyId=this.company.companyId,Object(s["d"])(this.queryParams).then((function(t){e.agreementrecordList=t.rows,e.total=t.total,e.loading=!1}))},cancel:function(){this.oopen=!1,this.reset()},reset:function(){this.form={agreementId:null,sqdCompanyId:null,agreementNumber:null,agreementTypeId:null,agreementPrice:null,staffId:null,agreementStartDate:null,agreementEndDate:null,isAvailable:null,creditLimit:null,currencyId:null,paymentDateNodeId:null,creditDays:null,isWorkingDay:null,creditLevel:null,remark:null,isLocked:"0",deptConfirmed:"0",deptConfirmedDate:null,financeConfirmed:"0",financeConfirmedDate:null,orderNum:null,status:"0"},this.resetForm("form")},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.resetForm("queryForm"),this.handleQuery()},handleSelectionChange:function(e){this.ids=e.map((function(e){return e.agreementId})),this.single=1!=e.length,this.multiple=!e.length},handleAdd:function(){this.reset(),this.oopen=!0,this.title="添加协议记录",this.form.staffName=this.$store.state.user.name},handleUpdate:function(e){var t=this;this.reset();var a=e.agreementId||this.ids;Object(s["c"])(a).then((function(e){t.form=e.data,t.oopen=!0,t.title="修改协议记录"}))},submitForm:function(){var e=this;this.$refs["form"].validate((function(t){t&&(e.form.sqdCompanyId=e.company.companyId,null!=e.form.agreementId?Object(s["e"])(e.form).then((function(t){e.$modal.msgSuccess("修改成功"),e.oopen=!1,e.getList()})):(e.form.sqdCompanyId=e.company.companyId,Object(s["a"])(e.form).then((function(t){e.$modal.msgSuccess("新增成功"),e.oopen=!1,e.getList()}))))}))},handleDelete:function(e){var t=this,a=e.agreementId||this.ids;this.$confirm('是否确认删除协议记录编号为"'+a+'"的数据项？',"提示",{customClass:"modal-confirm"}).then((function(){return Object(s["b"])(a)})).then((function(){t.getList(),t.$modal.msgSuccess("删除成功")})).catch((function(){}))},handleExport:function(){this.download("system/agreementrecord/export",Object(n["a"])({},this.queryParams),"agreementrecord_".concat((new Date).getTime(),".xlsx"))},deptLock:function(){var e=this;null!=this.form.agreementId?(this.form.deptConfirmed=0==this.form.deptConfirmed?1:0,this.form.deptConfirmedId=this.$store.state.user.sid,Object(s["e"])(this.form).then((function(t){e.$modal.msgSuccess("修改成功"),e.oopen=!1,e.getList()}))):this.$modal.msgError("错误操作")},financeLock:function(){var e=this;null!=this.form.agreementId?(this.form.financeConfirmed=0==this.form.financeConfirmed?1:0,this.form.financeConfirmedId=this.$store.state.user.sid,Object(s["e"])(this.form).then((function(t){e.$modal.msgSuccess("修改成功"),e.oopen=!1,e.getList()}))):this.$modal.msgError("错误操作")},getCurrencyId:function(e){this.form.currencyId=e}}},i=l,c=a("2877"),m=Object(c["a"])(i,o,r,!1,null,null,null);t["default"]=m.exports},e18c:function(e,t,a){"use strict";a.r(t);var o=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"container"},["1"==e.scope.row.isBlacklist?a("el-popover",{attrs:{placement:"right",trigger:"click",width:"400"}},[a("div",[e._v(" "+e._s("拉黑人："+e.scope.row.blacklistStaffLocalName+" "+e.scope.row.blacklistStaffEnName)+" ")]),a("div",[e._v(" "+e._s("拉黑原因："+e.scope.row.blacklistContent)+" ")]),a("el-button",{staticStyle:{padding:"2px","background-color":"black",color:"white"},attrs:{slot:"reference"},slot:"reference"},[e._v("黑名单")])],1):e._e(),a("div",[a("el-row",{staticStyle:{display:"flex"}},[a("el-col",{attrs:{span:11}},[a("h3",{staticStyle:{margin:"0","padding-right":"3px",color:"darkgray"}},[e._v(" ("+e._s(null!=e.scope.row.companyTaxCode?e.scope.row.companyTaxCode:"")+") ")])]),a("el-col",{attrs:{span:13}},[a("h3",{staticStyle:{margin:"0","font-weight":"bold","font-size":"small",overflow:"hidden","text-overflow":"ellipsis","white-space":"nowrap"}},[e._v(" "+e._s(e.scope.row.companyEnShortName?e.scope.row.companyEnShortName:e.scope.row.companyShortName)+" ")])])],1)],1)],1)},r=[],n={name:"company",props:["scope"]},s=n,l=a("2877"),i=Object(l["a"])(s,o,r,!1,null,"041bf838",null);t["default"]=i.exports},e350:function(e,t,a){"use strict";a.d(t,"a",(function(){return r})),a.d(t,"b",(function(){return n}));a("d3b7"),a("caad"),a("2532");var o=a("4360");function r(e){if(e&&e instanceof Array&&e.length>0){var t=o["a"].getters&&o["a"].getters.permissions,a=e,r="*:*:*",n=t.some((function(e){return r==e||a.includes(e)}));return!!n}return console.error("need roles! Like checkPermi=\"['system:user:add','system:user:edit']\""),!1}function n(e){if(e&&e instanceof Array&&e.length>0){var t=o["a"].getters&&o["a"].getters.roles,a=e,r="admin",n=t.some((function(e){return r==e||a.includes(e)}));return!!n}return console.error("need roles! Like checkRole=\"['admin','editor']\""),!1}},e757:function(e,t,a){"use strict";a("6ab7")},e9b7:function(e,t,a){"use strict";a.r(t);var o=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("el-tooltip",{attrs:{placement:"top"}},[a("div",{attrs:{slot:"content"},slot:"content"},[a("h6",{staticStyle:{margin:"0"}},[e._v(" "+e._s(null!=e.scope.row.locationName?e.scope.row.locationName.includes("全部")?e.scope.row.locationName.replace("全部/",""):e.scope.row.locationName:""))]),a("h6",{staticStyle:{margin:"0"}},[e._v(e._s(e.scope.row.locationDetail))])]),a("div",[a("h6",{staticStyle:{margin:"0",overflow:"hidden","text-overflow":"ellipsis","white-space":"nowrap"}},[e._v(" "+e._s(null!=e.scope.row.locationName?e.scope.row.locationName.includes("全部")?e.scope.row.locationName.replace("全部/",""):e.scope.row.locationName:""))]),a("h6",{staticStyle:{margin:"0",overflow:"hidden","text-overflow":"ellipsis","white-space":"nowrap"}},[e._v(" "+e._s(e.scope.row.locationDetail))])])])],1)},r=[],n={name:"location",props:["scope"]},s=n,l=a("2877"),i=Object(l["a"])(s,o,r,!1,null,"6ef7a6f2",null);t["default"]=i.exports},ea1f:function(e,t,a){},f0c6:function(e,t,a){"use strict";a.d(t,"e",(function(){return r})),a.d(t,"d",(function(){return n})),a.d(t,"a",(function(){return s})),a.d(t,"f",(function(){return l})),a.d(t,"c",(function(){return i})),a.d(t,"b",(function(){return c}));var o=a("b775");function r(e){return Object(o["a"])({url:"/system/processtype/list",method:"get",params:e})}function n(e){return Object(o["a"])({url:"/system/processtype/"+e,method:"get"})}function s(e){return Object(o["a"])({url:"/system/processtype",method:"post",data:e})}function l(e){return Object(o["a"])({url:"/system/processtype",method:"put",data:e})}function i(e){return Object(o["a"])({url:"/system/processtype/"+e,method:"delete"})}function c(e,t){var a={processTypeId:e,status:t};return Object(o["a"])({url:"/system/processtype/changeStatus",method:"put",data:a})}},f4d2:function(e,t,a){"use strict";a("ea1f")}}]);