(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d2295d7"],{dcac:function(e,a,t){"use strict";t.r(a);var r=function(){var e=this,a=e.$createElement,t=e._self._c||a;return t("div",{staticClass:"app-container"},[t("el-row",{attrs:{gutter:20}},[t("el-col",{attrs:{span:e.showLeft}},[t("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],ref:"queryForm",staticClass:"query",attrs:{model:e.queryParams,size:"mini",inline:!0,"label-width":"68px"}},[t("el-form-item",{attrs:{label:"简称",prop:"releaseWayShortName"}},[t("el-input",{attrs:{placeholder:"简称",clearable:""},nativeOn:{keyup:function(a){return!a.type.indexOf("key")&&e._k(a.keyCode,"enter",13,a.key,"Enter")?null:e.handleQuery(a)}},model:{value:e.queryParams.releaseWayShortName,callback:function(a){e.$set(e.queryParams,"releaseWayShortName",a)},expression:"queryParams.releaseWayShortName"}})],1),t("el-form-item",{attrs:{label:"中文名",prop:"releaseWayLocalName"}},[t("el-input",{attrs:{placeholder:"中文名",clearable:""},nativeOn:{keyup:function(a){return!a.type.indexOf("key")&&e._k(a.keyCode,"enter",13,a.key,"Enter")?null:e.handleQuery(a)}},model:{value:e.queryParams.releaseWayLocalName,callback:function(a){e.$set(e.queryParams,"releaseWayLocalName",a)},expression:"queryParams.releaseWayLocalName"}})],1),t("el-form-item",{attrs:{label:"英文名",prop:"releaseWayEnName"}},[t("el-input",{attrs:{placeholder:"英文名",clearable:""},nativeOn:{keyup:function(a){return!a.type.indexOf("key")&&e._k(a.keyCode,"enter",13,a.key,"Enter")?null:e.handleQuery(a)}},model:{value:e.queryParams.releaseWayEnName,callback:function(a){e.$set(e.queryParams,"releaseWayEnName",a)},expression:"queryParams.releaseWayEnName"}})],1),t("el-form-item",{attrs:{label:"排序",prop:"orderNum"}},[t("el-input",{attrs:{placeholder:"排序",clearable:""},nativeOn:{keyup:function(a){return!a.type.indexOf("key")&&e._k(a.keyCode,"enter",13,a.key,"Enter")?null:e.handleQuery(a)}},model:{value:e.queryParams.orderNum,callback:function(a){e.$set(e.queryParams,"orderNum",a)},expression:"queryParams.orderNum"}})],1),t("el-form-item",[t("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"mini"},on:{click:e.handleQuery}},[e._v("搜索")]),t("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1)],1),t("el-col",{attrs:{span:e.showRight}},[t("el-row",{staticClass:"mb8",attrs:{gutter:10}},[t("el-col",{attrs:{span:1.5}},[t("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:docreleaseway:add"],expression:"['system:docreleaseway:add']"}],attrs:{type:"primary",plain:"",icon:"el-icon-plus",size:"mini"},on:{click:e.handleAdd}},[e._v("新增 ")])],1),t("el-col",{attrs:{span:1.5}},[t("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:docreleaseway:edit"],expression:"['system:docreleaseway:edit']"}],attrs:{type:"success",plain:"",icon:"el-icon-edit",size:"mini",disabled:e.single},on:{click:e.handleUpdate}},[e._v("修改 ")])],1),t("el-col",{attrs:{span:1.5}},[t("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:docreleaseway:remove"],expression:"['system:docreleaseway:remove']"}],attrs:{type:"danger",plain:"",icon:"el-icon-delete",size:"mini",disabled:e.multiple},on:{click:e.handleDelete}},[e._v("删除 ")])],1),t("el-col",{attrs:{span:1.5}},[t("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:docreleaseway:export"],expression:"['system:docreleaseway:export']"}],attrs:{type:"warning",plain:"",icon:"el-icon-download",size:"mini"},on:{click:e.handleExport}},[e._v("导出 ")])],1),t("right-toolbar",{attrs:{showSearch:e.showSearch},on:{"update:showSearch":function(a){e.showSearch=a},"update:show-search":function(a){e.showSearch=a},queryTable:e.getList}})],1),t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.docreleasewayList},on:{"selection-change":e.handleSelectionChange}},[t("el-table-column",{attrs:{type:"selection",width:"28",align:"center"}}),t("el-table-column",{attrs:{label:"交单方式",align:"center",prop:"issueType"}}),t("el-table-column",{attrs:{label:"简称",align:"center",prop:"releaseWayShortName"}}),t("el-table-column",{attrs:{label:"中文名",align:"center",prop:"releaseWayLocalName"}}),t("el-table-column",{attrs:{label:"英文名",align:"center",prop:"releaseWayEnName"}}),t("el-table-column",{attrs:{label:"排序",align:"center",prop:"orderNum"}}),t("el-table-column",{attrs:{label:"状态",align:"center",prop:"status"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("el-switch",{attrs:{"active-value":"0","inactive-value":"1"},on:{change:function(t){return e.handleStatusChange(a.row)}},model:{value:a.row.status,callback:function(t){e.$set(a.row,"status",t)},expression:"scope.row.status"}})]}}])}),t("el-table-column",{attrs:{label:"备注",align:"center",prop:"remark"}}),t("el-table-column",{attrs:{label:"操作",align:"center","class-name":"small-padding fixed-width"},scopedSlots:e._u([{key:"default",fn:function(a){return[t("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:docreleaseway:edit"],expression:"['system:docreleaseway:edit']"}],staticStyle:{"margin-right":"-8px"},attrs:{size:"mini",type:"success",icon:"el-icon-edit"},on:{click:function(t){return e.handleUpdate(a.row)}}},[e._v("修改 ")]),t("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:docreleaseway:remove"],expression:"['system:docreleaseway:remove']"}],staticStyle:{"margin-right":"-8px"},attrs:{size:"mini",type:"danger",icon:"el-icon-delete"},on:{click:function(t){return e.handleDelete(a.row)}}},[e._v("删除 ")])]}}])})],1),t("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total>0"}],attrs:{total:e.total,page:e.queryParams.pageNum,limit:e.queryParams.pageSize},on:{"update:page":function(a){return e.$set(e.queryParams,"pageNum",a)},"update:limit":function(a){return e.$set(e.queryParams,"pageSize",a)},pagination:e.getList}})],1)],1),t("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"},{name:"dialogDragWidth",rawName:"v-dialogDragWidth"}],attrs:{"close-on-click-modal":!1,"modal-append-to-body":!1,title:e.title,visible:e.open,width:"500px","append-to-body":""},on:{"update:visible":function(a){e.open=a}}},[t("el-form",{ref:"form",staticClass:"edit",attrs:{model:e.form,rules:e.rules,"label-width":"80px"}},[t("el-form-item",{attrs:{label:"出单方式",prop:"issueTypeId"}},[t("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"主单出单方式"},model:{value:e.form.issueTypeId,callback:function(a){e.$set(e.form,"issueTypeId",a)},expression:"form.issueTypeId"}},e._l(e.docissuetypeList,(function(e){return t("el-option",{key:e.issueTypeId,attrs:{label:e.issueTypeLocalName,value:e.issueTypeId}})})),1)],1),t("el-form-item",{attrs:{label:"简称",prop:"releaseWayShortName"}},[t("el-input",{attrs:{placeholder:"简称"},model:{value:e.form.releaseWayShortName,callback:function(a){e.$set(e.form,"releaseWayShortName",a)},expression:"form.releaseWayShortName"}})],1),t("el-form-item",{attrs:{label:"中文名",prop:"releaseWayLocalName"}},[t("el-input",{attrs:{placeholder:"中文名"},model:{value:e.form.releaseWayLocalName,callback:function(a){e.$set(e.form,"releaseWayLocalName",a)},expression:"form.releaseWayLocalName"}})],1),t("el-form-item",{attrs:{label:"英文名",prop:"releaseWayEnName"}},[t("el-input",{attrs:{placeholder:"英文名"},model:{value:e.form.releaseWayEnName,callback:function(a){e.$set(e.form,"releaseWayEnName",a)},expression:"form.releaseWayEnName"}})],1),t("el-form-item",{attrs:{label:"排序",prop:"orderNum"}},[t("el-input-number",{staticStyle:{width:"100%"},attrs:{controls:!1,placeholder:"排序"},model:{value:e.form.orderNum,callback:function(a){e.$set(e.form,"orderNum",a)},expression:"form.orderNum"}})],1),t("el-form-item",{attrs:{label:"备注",prop:"remark"}},[t("el-input",{attrs:{placeholder:"备注"},model:{value:e.form.remark,callback:function(a){e.$set(e.form,"remark",a)},expression:"form.remark"}})],1)],1),t("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("确 定")]),t("el-button",{on:{click:e.cancel}},[e._v("取 消")])],1)],1)],1)},l=[],s=t("5530"),n=(t("d81d"),t("a48f")),o=t("3f21"),i={name:"Docreleaseway",data:function(){return{showLeft:3,showRight:21,docissuetypeList:[],loading:!0,ids:[],single:!0,multiple:!0,showSearch:!0,total:0,docreleasewayList:[],title:"",open:!1,queryParams:{pageNum:1,pageSize:20,releaseWayShortName:null,releaseWayLocalName:null,releaseWayEnName:null,orderNum:null,status:null},form:{},rules:{}}},watch:{showSearch:function(e){!0===e?(this.showRight=21,this.showLeft=3):(this.showRight=24,this.showLeft=0)}},created:function(){var e=this;this.getList(),Object(o["e"])({pageNum:1,pageSize:100}).then((function(a){e.docissuetypeList=a.rows}))},methods:{getList:function(){var e=this;this.loading=!0,Object(n["e"])(this.queryParams).then((function(a){e.docreleasewayList=a.rows,e.total=a.total,e.loading=!1}))},cancel:function(){this.open=!1,this.reset()},reset:function(){this.form={releaseWayId:null,releaseWayShortName:null,releaseWayLocalName:null,releaseWayEnName:null,orderNum:null,status:"0",remark:null,createBy:null,createTime:null,updateBy:null,updateTime:null,deleteBy:null,deleteTime:null,deleteStatus:"0"},this.resetForm("form")},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.resetForm("queryForm"),this.handleQuery()},handleStatusChange:function(e){var a=this,t="0"===e.status?"启用":"停用";this.$confirm('确认要"'+t+"吗？","提示",{customClass:"modal-confirm"}).then((function(){return Object(n["b"])(e.releaseWayId,e.status)})).then((function(){a.$modal.msgSuccess(t+"成功")})).catch((function(){e.status="0"===e.status?"1":"0"}))},handleSelectionChange:function(e){this.ids=e.map((function(e){return e.releaseWayId})),this.single=1!==e.length,this.multiple=!e.length},handleAdd:function(){this.reset(),this.open=!0,this.title="添加交单方式"},handleUpdate:function(e){var a=this;this.reset();var t=e.releaseWayId||this.ids;Object(n["d"])(t).then((function(e){a.form=e.data,a.open=!0,a.title="修改交单方式"}))},submitForm:function(){var e=this;this.$refs["form"].validate((function(a){a&&(null!=e.form.releaseWayId?Object(n["g"])(e.form).then((function(a){e.$modal.msgSuccess("修改成功"),e.open=!1,e.getList()})):Object(n["a"])(e.form).then((function(a){e.$modal.msgSuccess("新增成功"),e.open=!1,e.getList()})))}))},handleDelete:function(e){var a=this,t=e.releaseWayId||this.ids;this.$confirm('是否确认删除交单方式编号为"'+t+'"的数据项？',"提示",{customClass:"modal-confirm"}).then((function(){return Object(n["c"])(t)})).then((function(){a.getList(),a.$modal.msgSuccess("删除成功")})).catch((function(){}))},handleExport:function(){this.download("system/docreleaseway/export",Object(s["a"])({},this.queryParams),"docreleaseway_".concat((new Date).getTime(),".xlsx"))}}},c=i,u=t("2877"),m=Object(u["a"])(c,r,l,!1,null,null,null);a["default"]=m.exports}}]);