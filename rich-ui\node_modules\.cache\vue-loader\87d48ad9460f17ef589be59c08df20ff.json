{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\outboundRecord\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\outboundRecord\\index.vue", "mtime": 1750840424298}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgew0KICBhZGRPdXRib3VuZHJlY29yZCwNCiAgY2hhbmdlU3RhdHVzLA0KICBkZWxPdXRib3VuZHJlY29yZCwgZG93bmxvYWRPdXRib3VuZEJpbGwsDQogIGdldE91dGJvdW5kcmVjb3JkLCBnZXRPdXRib3VuZHJlY29yZHMsDQogIGxpc3RPdXRib3VuZHJlY29yZCwgbGlzdE91dGJvdW5kcmVjb3JkcywNCiAgdXBkYXRlT3V0Ym91bmRyZWNvcmQNCn0gZnJvbSAiQC9hcGkvc3lzdGVtL291dGJvdW5kcmVjb3JkIg0KaW1wb3J0IHtwYXJzZVRpbWV9IGZyb20gIi4uLy4uLy4uL3V0aWxzL3JpY2giDQppbXBvcnQge2xpc3RJbnZlbnRvcnksIG91dGJvdW5kSW52ZW50b3J5LCBwcmVPdXRib3VuZEludmVudG9yeX0gZnJvbSAiQC9hcGkvc3lzdGVtL2ludmVudG9yeSINCmltcG9ydCBjdXJyZW5jeSBmcm9tICJjdXJyZW5jeS5qcyINCg0KZXhwb3J0IGRlZmF1bHQgew0KICBuYW1lOiAiT3V0Ym91bmRyZWNvcmQiLA0KICBkYXRhKCkgew0KICAgIHJldHVybiB7DQogICAgICBzZWxlY3RlZENhcmdvRGV0YWlsOltdLA0KICAgICAgc2VhcmNoOiBudWxsLA0KICAgICAgc2hvd0xlZnQ6IDAsDQogICAgICBzaG93UmlnaHQ6IDI0LA0KICAgICAgLy8g6YGu572p5bGCDQogICAgICBsb2FkaW5nOiB0cnVlLA0KICAgICAgc2VsZWN0T3V0Ym91bmRMaXN0OiBbXSwNCiAgICAgIC8vIOmAieS4reaVsOe7hA0KICAgICAgaWRzOiBbXSwNCiAgICAgIC8vIOmdnuWNleS4quemgeeUqA0KICAgICAgc2luZ2xlOiB0cnVlLA0KICAgICAgLy8g6Z2e5aSa5Liq56aB55SoDQogICAgICBtdWx0aXBsZTogdHJ1ZSwNCiAgICAgIC8vIOaYvuekuuaQnOe0ouadoeS7tg0KICAgICAgc2hvd1NlYXJjaDogZmFsc2UsDQogICAgICAvLyDmgLvmnaHmlbANCiAgICAgIHRvdGFsOiAwLA0KICAgICAgLy8g5Ye65LuT6K6w5b2V6KGo5qC85pWw5o2uDQogICAgICBvdXRib3VuZHJlY29yZExpc3Q6IFtdLA0KICAgICAgLy8g5by55Ye65bGC5qCH6aKYDQogICAgICB0aXRsZTogIiIsDQogICAgICAvLyDmmK/lkKbmmL7npLrlvLnlh7rlsYINCiAgICAgIG9wZW46IGZhbHNlLA0KICAgICAgLy8g5p+l6K+i5Y+C5pWwDQogICAgICBxdWVyeVBhcmFtczogew0KICAgICAgICBwYWdlTnVtOiAxLA0KICAgICAgICBwYWdlU2l6ZTogMjAsDQogICAgICAgIGlzUmVudFNldHRsZW1lbnQ6IDAsDQogICAgICAgIG91dGJvdW5kTm86IG51bGwsDQogICAgICAgIGNsaWVudENvZGU6IG51bGwsDQogICAgICAgIGNsaWVudE5hbWU6IG51bGwsDQogICAgICAgIG9wZXJhdG9ySWQ6IG51bGwsDQogICAgICAgIGNvbnRhaW5lclR5cGU6IG51bGwsDQogICAgICAgIGNvbnRhaW5lck5vOiBudWxsLA0KICAgICAgICBzZWFsTm86IG51bGwsDQogICAgICAgIG91dGJvdW5kRGF0ZTogbnVsbCwNCiAgICAgICAgd2FyZWhvdXNlUXVvdGU6IG51bGwsDQogICAgICAgIHdvcmtlckxvYWRpbmdGZWU6IG51bGwsDQogICAgICAgIHdhcmVob3VzZUNvbGxlY3Rpb246IG51bGwsDQogICAgICAgIGNvbGxlY3Rpb25Ob3RlczogbnVsbCwNCiAgICAgICAgdG90YWxCb3hlczogbnVsbCwNCiAgICAgICAgdG90YWxHcm9zc1dlaWdodDogbnVsbCwNCiAgICAgICAgdG90YWxWb2x1bWU6IG51bGwsDQogICAgICAgIHRvdGFsUm93czogbnVsbCwNCiAgICAgICAgcmVjZWl2ZWRTdG9yYWdlRmVlOiBudWxsLA0KICAgICAgICB1bnBhaWRVbmxvYWRpbmdGZWU6IG51bGwsDQogICAgICAgIHVucGFpZFBhY2tpbmdGZWU6IG51bGwsDQogICAgICAgIGxvZ2lzdGljc0FkdmFuY2VGZWU6IG51bGwsDQogICAgICAgIHJlbnRhbEJhbGFuY2VGZWU6IG51bGwsDQogICAgICAgIG92ZXJkdWVSZW50OiBudWxsLA0KICAgICAgICBmcmVlU3RhY2tEYXlzOiBudWxsLA0KICAgICAgICBvdmVyZHVlVW5pdFByaWNlOiBudWxsDQogICAgICB9LA0KICAgICAgLy8g6KGo5Y2V5Y+C5pWwDQogICAgICBmb3JtOiB7fSwNCiAgICAgIG91dGJvdW5kVHlwZTogbnVsbCwNCiAgICAgIHByZU91dGJvdW5kSW52ZW50b3J5TGlzdExvYWRpbmc6IGZhbHNlLA0KICAgICAgLy8g6KGo5Y2V5qCh6aqMDQogICAgICBydWxlczoge30sDQogICAgICBvdXRib3VuZEZvcm06IHt9LA0KICAgICAgb3Blbk91dGJvdW5kOiBmYWxzZSwNCiAgICAgIHByZU91dGJvdW5kSW52ZW50b3J5TGlzdDogW10NCiAgICB9DQogIH0sDQogIHdhdGNoOiB7DQogICAgc2hvd1NlYXJjaChuKSB7DQogICAgICBpZiAobiA9PT0gdHJ1ZSkgew0KICAgICAgICB0aGlzLnNob3dSaWdodCA9IDIxDQogICAgICAgIHRoaXMuc2hvd0xlZnQgPSAzDQogICAgICB9IGVsc2Ugew0KICAgICAgICB0aGlzLnNob3dSaWdodCA9IDI0DQogICAgICAgIHRoaXMuc2hvd0xlZnQgPSAwDQogICAgICB9DQogICAgfQ0KICB9LA0KICBjcmVhdGVkKCkgew0KICAgIHRoaXMuZ2V0TGlzdCgpDQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICAvLyDliKTmlq3lvZPliY3ooYzmmK/lkKbooqvpgInkuK0NCiAgICBpc1Jvd1NlbGVjdGVkKHJvdykgew0KICAgICAgcmV0dXJuIHRoaXMuc2VsZWN0ZWRDYXJnb0RldGFpbC5pbmNsdWRlcyhyb3cpDQogICAgfSwNCiAgICAvLyDmt7vliqDmkJzntKLlubbmu5rliqjliLDljLnphY3ooYznmoTmlrnms5UNCiAgICBoYW5kbGVTZWFyY2hFbnRlcigpIHsNCiAgICAgIGlmICghdGhpcy5zZWFyY2gpIHJldHVybjsNCg0KICAgICAgLy8g5p+l5om+5Yy56YWN55qE6KGM57Si5byVDQogICAgICBjb25zdCBpbmRleCA9IHRoaXMucHJlT3V0Ym91bmRJbnZlbnRvcnlMaXN0LmZpbmRJbmRleCgNCiAgICAgICAgaXRlbSA9PiB7DQogICAgICAgICAgLy8g56Gu5L+dIGluYm91bmRTZXJpYWxObyDlrZjlnKjkuJTkuLrlrZfnrKbkuLINCiAgICAgICAgICBjb25zdCBzZXJpYWxObyA9IFN0cmluZyhpdGVtLmluYm91bmRTZXJpYWxObyB8fCAnJyk7DQogICAgICAgICAgY29uc3Qgc2VhcmNoVmFsdWUgPSBTdHJpbmcodGhpcy5zZWFyY2gpOw0KICAgICAgICAgIC8vIOaJk+WNsOavj+asoeavlOi+g+eahOWAvO+8jOW4ruWKqeiwg+ivlQ0KICAgICAgICAgIHJldHVybiBzZXJpYWxOby5pbmNsdWRlcyhzZWFyY2hWYWx1ZSk7DQogICAgICAgIH0NCiAgICAgICk7DQoNCiAgICAgIGlmIChpbmRleCA+IC0xKSB7DQogICAgICAgIC8vIOiOt+WPluihqOagvERPTQ0KICAgICAgICBjb25zdCB0YWJsZSA9IHRoaXMuJHJlZnMudGFibGU7DQoNCiAgICAgICAgdGhpcy4kbmV4dFRpY2soKCkgPT4gew0KICAgICAgICAgIC8vIOiOt+WPluihqOagvOeahOa7muWKqOWuueWZqA0KICAgICAgICAgIGNvbnN0IHNjcm9sbFdyYXBwZXIgPSB0YWJsZS4kZWwucXVlcnlTZWxlY3RvcignLmVsLXRhYmxlX19ib2R5LXdyYXBwZXInKTsNCiAgICAgICAgICAvLyDojrflj5bmiYDmnInooYwNCiAgICAgICAgICBjb25zdCByb3dzID0gc2Nyb2xsV3JhcHBlci5xdWVyeVNlbGVjdG9yQWxsKCcuZWwtdGFibGVfX3JvdycpOw0KDQogICAgICAgICAgLy8g6YGN5Y6G5omA5pyJ6KGM77yM5om+5Yiw5Yy56YWN55qE5rWB5rC05Y+3DQogICAgICAgICAgbGV0IHRhcmdldEluZGV4ID0gLTE7DQogICAgICAgICAgcm93cy5mb3JFYWNoKChyb3csIGlkeCkgPT4gew0KICAgICAgICAgICAgY29uc3Qgcm93VGV4dCA9IHJvdy50ZXh0Q29udGVudDsNCiAgICAgICAgICAgIGlmIChyb3dUZXh0LmluY2x1ZGVzKHRoaXMuc2VhcmNoKSkgew0KICAgICAgICAgICAgICB0YXJnZXRJbmRleCA9IGlkeDsNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9KTsNCg0KDQogICAgICAgICAgaWYgKHRhcmdldEluZGV4ID4gLTEpIHsNCiAgICAgICAgICAgIGNvbnN0IHRhcmdldFJvdyA9IHJvd3NbdGFyZ2V0SW5kZXhdOw0KICAgICAgICAgICAgLy8g6K6h566X6ZyA6KaB5rua5Yqo55qE5L2N572uDQogICAgICAgICAgICBjb25zdCByb3dUb3AgPSB0YXJnZXRSb3cub2Zmc2V0VG9wOw0KDQogICAgICAgICAgICAvLyDkvb/nlKjlubPmu5Hmu5rliqgNCiAgICAgICAgICAgIHNjcm9sbFdyYXBwZXIuc2Nyb2xsVG8oew0KICAgICAgICAgICAgICB0b3A6IHJvd1RvcCAtIHNjcm9sbFdyYXBwZXIuY2xpZW50SGVpZ2h0IC8gMiwNCiAgICAgICAgICAgICAgYmVoYXZpb3I6ICdzbW9vdGgnDQogICAgICAgICAgICB9KTsNCg0KICAgICAgICAgICAgLy8g6auY5Lqu5pi+56S66K+l6KGMDQogICAgICAgICAgICB0YXJnZXRSb3cuY2xhc3NMaXN0LmFkZCgnaGlnaGxpZ2h0LXJvdycpOw0KICAgICAgICAgICAgLy8gMeenkuWQjuenu+mZpOmrmOS6rg0KICAgICAgICAgICAgc2V0VGltZW91dCgoKSA9PiB7DQogICAgICAgICAgICAgIHRhcmdldFJvdy5jbGFzc0xpc3QucmVtb3ZlKCdoaWdobGlnaHQtcm93Jyk7DQogICAgICAgICAgICB9LCAyMDAwKTsNCiAgICAgICAgICB9DQogICAgICAgIH0pOw0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCfmnKrmib7liLDljLnphY3nmoTorrDlvZUnKTsNCiAgICAgIH0NCiAgICB9LA0KICAgIHdhcmVob3VzZUNvbmZpcm0oKSB7DQoNCiAgICB9LA0KICAgIC8vIOWKoOi9veWtkOiKgueCueaVsOaNrg0KICAgIGxvYWRDaGlsZEludmVudG9yeSh0cmVlLCB0cmVlTm9kZSwgcmVzb2x2ZSkgew0KICAgICAgLy8g5L2/55SocGFja2FnZVRv5a2X5q615p+l6K+i5a2Q6IqC54K5DQogICAgICBsaXN0SW52ZW50b3J5KHtwYWNrYWdlVG86IHRyZWUuaW52ZW50b3J5SWR9KS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgY29uc3Qgcm93cyA9IHJlc3BvbnNlLnJvd3MNCg0KICAgICAgICAvLyDlhYjlsIbmlbDmja7kvKDpgJLnu5nooajmoLzvvIznoa7kv53lrZDoioLngrnmuLLmn5MNCiAgICAgICAgcmVzb2x2ZShyb3dzKQ0KICAgICAgICB0cmVlLmNoaWxkcmVuID0gcm93cw0KDQogICAgICAgIC8vIOWmguaenOeItumhueiiq+mAieS4re+8jOWcqOWtkOiKgueCuea4suafk+WujOaIkOWQjumAieS4reWug+S7rA0KICAgICAgICBpZiAodGhpcy5pZHMuaW5jbHVkZXModHJlZS5pbnZlbnRvcnlJZCkpIHsNCiAgICAgICAgICBzZXRUaW1lb3V0KCgpID0+IHsNCiAgICAgICAgICAgIHJvd3MuZm9yRWFjaChjaGlsZCA9PiB7DQogICAgICAgICAgICAgIGlmICghdGhpcy5pZHMuaW5jbHVkZXMoY2hpbGQuaW52ZW50b3J5SWQpKSB7DQogICAgICAgICAgICAgICAgdGhpcy5pZHMucHVzaChjaGlsZC5pbnZlbnRvcnlJZCkNCiAgICAgICAgICAgICAgICB0aGlzLnNlbGVjdE91dGJvdW5kTGlzdC5wdXNoKGNoaWxkKQ0KICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgIC8vIOWcqFVJ5LiK6YCJ5Lit5a2Q6aG5DQogICAgICAgICAgICAgIHRoaXMuJHJlZnMudGFibGUudG9nZ2xlUm93U2VsZWN0aW9uKGNoaWxkLCB0cnVlKQ0KICAgICAgICAgICAgfSkNCiAgICAgICAgICB9LCA1MCkgLy8g562J5b6FRE9N5pu05pawDQogICAgICAgIH0NCiAgICAgIH0pDQogICAgfSwNCiAgICBzZWxlY3RDb250YWluZXJUeXBlKHR5cGUpIHsNCiAgICAgIHN3aXRjaCAodHlwZSkgew0KICAgICAgICBjYXNlICIyMEdQIjoNCiAgICAgICAgICB0aGlzLm91dGJvdW5kRm9ybS53YXJlaG91c2VRdW90ZSA9IHRoaXMuY2xpZW50Um93LnJhdGUyMGdwDQogICAgICAgICAgYnJlYWsNCiAgICAgICAgY2FzZSAiNDBIUSI6DQogICAgICAgICAgdGhpcy5vdXRib3VuZEZvcm0ud2FyZWhvdXNlUXVvdGUgPSB0aGlzLmNsaWVudFJvdy5yYXRlNDBocQ0KICAgICAgICAgIGJyZWFrDQoNCiAgICAgIH0NCiAgICB9LA0KICAgIGNvdW50U3VtbWFyeSgpIHsNCiAgICAgIHRoaXMub3V0Ym91bmRGb3JtLnVucmVjZWl2ZWRGcm9tQ3VzdG9tZXIgPSBjdXJyZW5jeSh0aGlzLm91dGJvdW5kRm9ybS53YXJlaG91c2VRdW90ZSkuYWRkKHRoaXMub3V0Ym91bmRGb3JtLmFkZGl0aW9uYWxTdG9yYWdlRmVlKS5hZGQodGhpcy5vdXRib3VuZEZvcm0udW5wYWlkVW5sb2FkaW5nRmVlKS5hZGQodGhpcy5vdXRib3VuZEZvcm0udW5wYWlkUGFja2luZ0ZlZSkuYWRkKHRoaXMub3V0Ym91bmRGb3JtLmxvZ2lzdGljc0FkdmFuY2VGZWUpLmFkZCh0aGlzLm91dGJvdW5kRm9ybS5vdmVyZHVlUmVudGFsRmVlKS5hZGQodGhpcy5vdXRib3VuZEZvcm0uZGlmZmljdWx0eVdvcmtGZWUpLnZhbHVlDQogICAgICB0aGlzLm91dGJvdW5kRm9ybS5yZWNlaXZlZEZyb21DdXN0b21lciA9IGN1cnJlbmN5KHRoaXMub3V0Ym91bmRGb3JtLndhcmVob3VzZUNvbGxlY3Rpb24pLnZhbHVlDQogICAgICB0aGlzLm91dGJvdW5kRm9ybS5jdXN0b21lclJlY2VpdmFibGVCYWxhbmNlID0gY3VycmVuY3kodGhpcy5vdXRib3VuZEZvcm0udW5yZWNlaXZlZEZyb21DdXN0b21lcikuc3VidHJhY3QodGhpcy5vdXRib3VuZEZvcm0ucmVjZWl2ZWRGcm9tQ3VzdG9tZXIpLnZhbHVlDQogICAgICB0aGlzLm91dGJvdW5kRm9ybS5wYXlhYmxlVG9Xb3JrZXIgPSBjdXJyZW5jeSh0aGlzLm91dGJvdW5kRm9ybS5yZWNlaXZlZFVubG9hZGluZ0ZlZSkuYWRkKHRoaXMub3V0Ym91bmRGb3JtLnJlY2VpdmVkUGFja2luZ0ZlZSkuYWRkKHRoaXMub3V0Ym91bmRGb3JtLndvcmtlckxvYWRpbmdGZWUpLnZhbHVlDQogICAgICB0aGlzLm91dGJvdW5kRm9ybS5yZWNlaXZlZEZyb21TdXBwbGllciA9IGN1cnJlbmN5KHRoaXMub3V0Ym91bmRGb3JtLnJlY2VpdmVkU3VwcGxpZXIpLmFkZCh0aGlzLm91dGJvdW5kRm9ybS5yZWNlaXZlZFN0b3JhZ2VGZWUpLnZhbHVlDQogICAgICB0aGlzLm91dGJvdW5kRm9ybS5wcm9taXNzb3J5Tm90ZVNhbGVzID0gY3VycmVuY3kodGhpcy5vdXRib3VuZEZvcm0udW5yZWNlaXZlZEZyb21DdXN0b21lcikuYWRkKHRoaXMub3V0Ym91bmRGb3JtLnJlY2VpdmVkRnJvbVN1cHBsaWVyKS52YWx1ZQ0KICAgICAgdGhpcy5vdXRib3VuZEZvcm0ucHJvbWlzc29yeU5vdGVDb3N0ID0gY3VycmVuY3kodGhpcy5vdXRib3VuZEZvcm0ucGF5YWJsZVRvV29ya2VyKS5hZGQodGhpcy5vdXRib3VuZEZvcm0ubG9naXN0aWNzQWR2YW5jZUZlZSkuYWRkKHRoaXMub3V0Ym91bmRGb3JtLndhcmVob3VzZUFkdmFuY2VPdGhlckZlZSkudmFsdWUNCiAgICAgIHRoaXMub3V0Ym91bmRGb3JtLnByb21pc3NvcnlOb3RlR3Jvc3NQcm9maXQgPSBjdXJyZW5jeSh0aGlzLm91dGJvdW5kRm9ybS5wcm9taXNzb3J5Tm90ZVNhbGVzKS5zdWJ0cmFjdCh0aGlzLm91dGJvdW5kRm9ybS5wcm9taXNzb3J5Tm90ZUNvc3QpLnZhbHVlDQogICAgfSwNCiAgICBnZW5lcmF0ZU91dGJvdW5kQmlsbCgpIHsNCiAgICAgIGRvd25sb2FkT3V0Ym91bmRCaWxsKHRoaXMub3V0Ym91bmRGb3JtKQ0KICAgICAgICAudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgICAgLy8g6I635Y+W5paH5Lu255qE5a2X6IqC5pWw57uEIChBcnJheUJ1ZmZlcikNCiAgICAgICAgICBjb25zdCBkYXRhID0gcmVzcG9uc2UNCg0KICAgICAgICAgIC8vIOiOt+WPluaWh+S7tuWQje+8iOWmguaenOWcqOWQjuerr+WTjeW6lOWktOS4reWMheWQq+aWh+S7tuWQje+8iQ0KICAgICAgICAgIGxldCBmaWxlTmFtZSA9IHRoaXMub3V0Ym91bmRGb3JtLmNsaWVudENvZGUgKyAiLSIgKyB0aGlzLm91dGJvdW5kRm9ybS5vcGVyYXRvciArICItIiArIHRoaXMub3V0Ym91bmRGb3JtLm91dGJvdW5kTm8gKyAiLnhsc3giICAvLyDpu5jorqTmlofku7blkI0NCg0KICAgICAgICAgIC8vIOWIm+W7uuS4gOS4qiBCbG9iIOWvueixoeadpeWtmOWCqOaWh+S7tg0KICAgICAgICAgIGNvbnN0IGJsb2IgPSBuZXcgQmxvYihbZGF0YV0sIHsNCiAgICAgICAgICAgIHR5cGU6ICJhcHBsaWNhdGlvbi92bmQub3BlbnhtbGZvcm1hdHMtb2ZmaWNlZG9jdW1lbnQuc3ByZWFkc2hlZXRtbC5zaGVldCIgIC8vIEV4Y2VsIOaWh+S7tuexu+Weiw0KICAgICAgICAgIH0pDQoNCiAgICAgICAgICAvLyDliJvlu7rkuIDkuKrkuLTml7bpk77mjqXvvIzmqKHmi5/ngrnlh7vmnaXkuIvovb3mlofku7YNCiAgICAgICAgICBjb25zdCBsaW5rID0gZG9jdW1lbnQuY3JlYXRlRWxlbWVudCgiYSIpDQogICAgICAgICAgY29uc3QgdXJsID0gd2luZG93LlVSTC5jcmVhdGVPYmplY3RVUkwoYmxvYikgIC8vIOWIm+W7uuS4gOS4qiBVUkwg5oyH5ZCRIEJsb2Ig5a+56LGhDQogICAgICAgICAgbGluay5ocmVmID0gdXJsDQogICAgICAgICAgbGluay5kb3dubG9hZCA9IGZpbGVOYW1lICAvLyDorr7nva7kuIvovb3nmoTmlofku7blkI0NCg0KICAgICAgICAgIC8vIOaooeaLn+eCueWHu+mTvuaOpe+8jOinpuWPkeS4i+i9vQ0KICAgICAgICAgIGRvY3VtZW50LmJvZHkuYXBwZW5kQ2hpbGQobGluaykNCiAgICAgICAgICBsaW5rLmNsaWNrKCkNCg0KICAgICAgICAgIC8vIOS4i+i9veWujOaIkOWQjuenu+mZpOmTvuaOpe+8jOW5tumHiuaUviBVUkwg5a+56LGhDQogICAgICAgICAgZG9jdW1lbnQuYm9keS5yZW1vdmVDaGlsZChsaW5rKQ0KICAgICAgICAgIHdpbmRvdy5VUkwucmV2b2tlT2JqZWN0VVJMKHVybCkNCiAgICAgICAgfSkNCiAgICAgICAgLmNhdGNoKGVycm9yID0+IHsNCiAgICAgICAgICBjb25zb2xlLmVycm9yKCLmlofku7bkuIvovb3lpLHotKU6IiwgZXJyb3IpDQogICAgICAgIH0pDQogICAgfSwNCiAgICAvLyDmn6XnnIvlh7rku5PorrDlvZUNCiAgICBmaW5kT3V0Ym91bmRSZWNvcmQocm93KSB7DQogICAgICB0aGlzLm91dGJvdW5kUmVzZXQoKQ0KICAgICAgZ2V0T3V0Ym91bmRyZWNvcmRzKHJvdy5vdXRib3VuZFJlY29yZElkKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgdGhpcy5vdXRib3VuZEZvcm0gPSByZXNwb25zZS5kYXRhDQogICAgICAgIHRoaXMub3V0Ym91bmRGb3JtLnJzSW52ZW50b3J5TGlzdCA9IHRoaXMub3V0Ym91bmRGb3JtLnJzSW52ZW50b3J5TGlzdCA/IHRoaXMub3V0Ym91bmRGb3JtLnJzSW52ZW50b3J5TGlzdC5tYXAoaXRlbSA9PiB7DQogICAgICAgICAgLy8g6K6h566X6KGl5pS25YWl5LuT6LS5DQogICAgICAgICAgaWYgKGl0ZW0uaW5jbHVkZXNJbmJvdW5kRmVlID09PSAwKSB7DQogICAgICAgICAgICBjb25zdCByZWNlaXZlZEZlZSA9IE51bWJlcihpdGVtLnJlY2VpdmVkU3RvcmFnZUZlZSB8fCAwKQ0KICAgICAgICAgICAgY29uc3QgaW5ib3VuZEZlZSA9IE51bWJlcihpdGVtLmluYm91bmRGZWUgfHwgMCkNCiAgICAgICAgICAgIGNvbnN0IGRpZmZlcmVuY2UgPSBjdXJyZW5jeShpbmJvdW5kRmVlKS5zdWJ0cmFjdChyZWNlaXZlZEZlZSkudmFsdWUNCg0KICAgICAgICAgICAgLy8g5Y+q5pyJ5b2T5beu5YC85aSn5LqOMOaXtuaJjeiuvue9ruihpeaUtui0ueeUqA0KICAgICAgICAgICAgaXRlbS5hZGRpdGlvbmFsU3RvcmFnZUZlZSA9IGRpZmZlcmVuY2UgPiAwID8gZGlmZmVyZW5jZSA6IDANCiAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgaXRlbS5hZGRpdGlvbmFsU3RvcmFnZUZlZSA9IDANCiAgICAgICAgICB9DQoNCiAgICAgICAgICByZXR1cm4gaXRlbQ0KICAgICAgICB9KSA6IFtdDQogICAgICAgIHRoaXMub3Blbk91dGJvdW5kID0gdHJ1ZQ0KICAgICAgfSkNCiAgICB9LA0KICAgIC8qKg0KICAgICAqDQogICAgICogQHBhcmFtIHR5cGUgMDrpooTlh7rku5MvMTrlh7rku5MNCiAgICAgKi8NCiAgICBvdXRib3VuZENvbmZpcm0odHlwZSkgew0KICAgICAgdGhpcy5zZWxlY3RPdXRib3VuZExpc3QubWFwKGl0ZW0gPT4gew0KICAgICAgICBpdGVtLnBhcnRpYWxPdXRib3VuZEZsYWcgPSBOdW1iZXIoaXRlbS5wYXJ0aWFsT3V0Ym91bmRGbGFnKQ0KICAgICAgfSkNCg0KICAgICAgYWRkT3V0Ym91bmRyZWNvcmQodGhpcy5vdXRib3VuZEZvcm0pLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICBpZiAocmVzcG9uc2UuZGF0YSkgew0KICAgICAgICAgIGNvbnN0IG91dGJvdW5kUmVjb3JkSWQgPSByZXNwb25zZS5kYXRhDQoNCiAgICAgICAgICAvLyDliJfooajlhYvpmobkuIDku70s5omT5LiK6aKE5Ye65LuT5qCH5b+XDQogICAgICAgICAgbGV0IGRhdGEgPSB0aGlzLnNlbGVjdE91dGJvdW5kTGlzdC5tYXAoaXRlbSA9PiB7DQogICAgICAgICAgICBpZiAoaXRlbS5wcmVPdXRib3VuZEZsYWcgPT09ICIxIikgew0KICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoIuWLvumAieiusOW9leS4reacieS7pemihOWHuuW6k+iusOW9lSzor7fph43mlrDli77pgIkiKQ0KICAgICAgICAgICAgICByZXR1cm4NCiAgICAgICAgICAgIH0NCiAgICAgICAgICAgIHR5cGUgPT09IDAgPyBpdGVtLnByZU91dGJvdW5kRmxhZyA9ICIxIiA6IG51bGwNCiAgICAgICAgICAgIGl0ZW0ub3V0Ym91bmRSZWNvcmRJZCA9IG91dGJvdW5kUmVjb3JkSWQNCiAgICAgICAgICAgIGl0ZW0ucnNDYXJnb0RldGFpbHNMaXN0ID8gaXRlbS5yc0NhcmdvRGV0YWlsc0xpc3QubWFwKGl0ZW0gPT4gew0KICAgICAgICAgICAgICBpdGVtLm91dGJvdW5kUmVjb3JkSWQgPSBvdXRib3VuZFJlY29yZElkDQogICAgICAgICAgICAgIHR5cGUgPT09IDAgPyBpdGVtLnByZU91dGJvdW5kRmxhZyA9ICIxIiA6IG51bGwNCiAgICAgICAgICAgICAgcmV0dXJuIGl0ZW0NCiAgICAgICAgICAgIH0pIDogbnVsbA0KICAgICAgICAgICAgcmV0dXJuIGl0ZW0NCiAgICAgICAgICB9KQ0KDQogICAgICAgICAgaWYgKHR5cGUgPT09IDApIHsNCiAgICAgICAgICAgIHByZU91dGJvdW5kSW52ZW50b3J5KGRhdGEpLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICAgICAgICB0aGlzLmdldExpc3QoKQ0KICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoIumihOWHuuS7k+aIkOWKnyIpDQogICAgICAgICAgICAgIHRoaXMub3Blbk91dGJvdW5kID0gZmFsc2UNCiAgICAgICAgICAgIH0pDQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIC8vIOWHuuS7kw0KICAgICAgICAgICAgb3V0Ym91bmRJbnZlbnRvcnkoZGF0YSkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgICAgICAgIHRoaXMuZ2V0TGlzdCgpDQogICAgICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygi5Ye65LuT5oiQ5YqfIikNCiAgICAgICAgICAgICAgdGhpcy5vcGVuT3V0Ym91bmQgPSBmYWxzZQ0KICAgICAgICAgICAgfSkNCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgIH0pDQogICAgfSwNCiAgICAvLyDmoLnmja7lh7rlupPkv6Hmga/liqDovb3lvoXlh7rlupPnmoTorrDlvZUNCiAgICBsb2FkUHJlT3V0Ym91bmRJbnZlbnRvcnlMaXN0KCkgew0KICAgICAgdGhpcy5sb2FkaW5nID0gdHJ1ZQ0KICAgICAgbGV0IGRhdGEgPSB7fQ0KICAgICAgZGF0YS5zcWRQbGFubmVkT3V0Ym91bmREYXRlID0gdGhpcy5vdXRib3VuZEZvcm0ucGxhbm5lZE91dGJvdW5kRGF0ZQ0KICAgICAgZGF0YS5jbGllbnRDb2RlID0gdGhpcy5vdXRib3VuZEZvcm0uY2xpZW50Q29kZQ0KICAgICAgbGlzdEludmVudG9yeShkYXRhKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgY29uc29sZS5sb2cocmVzcG9uc2UpDQogICAgICAgIHRoaXMucHJlT3V0Ym91bmRJbnZlbnRvcnlMaXN0ID0gcmVzcG9uc2Uucm93cw0KICAgICAgICB0aGlzLnRvdGFsID0gcmVzcG9uc2UudG90YWwNCiAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2UNCiAgICAgIH0pDQogICAgfSwNCiAgICAvKioNCiAgICAgKg0KICAgICAqIEBwYXJhbSB0eXBlIDA66aKE5Ye65LuTLzE65q2j5byP5Ye65LuTDQogICAgICovDQogICAgaGFuZGxlT3V0Ym91bmQoc2VsZWN0ZWRSb3dzLCB0eXBlKSB7DQogICAgICAvLyB0aGlzLm91dGJvdW5kTGlzdCA9IHRoaXMuaW52ZW50b3J5TGlzdC5maWx0ZXIoaXRlbSA9PiB0aGlzLmlkcy5pbmNsdWRlcyhpdGVtLmludmVudG9yeUlkKSkNCiAgICAgIGlmICh0eXBlID09PSAxKSB7DQogICAgICAgIHRoaXMub3V0Ym91bmRSZXNldCgpDQogICAgICAgIHRoaXMub3V0Ym91bmRGb3JtID0gc2VsZWN0ZWRSb3dzDQogICAgICB9DQogICAgICB0aGlzLm91dGJvdW5kVHlwZSA9IHR5cGUNCiAgICAgIHRoaXMub3Blbk91dGJvdW5kID0gdHJ1ZQ0KICAgIH0sDQogICAgcGFyc2VUaW1lLA0KICAgIGhhbmRsZU91dGJvdW5kQ2FyZ29EZXRhaWxTZWxlY3Rpb25DaGFuZ2Uoc2VsZWN0aW9uLCByb3cpIHsNCiAgICAgIHJvdy5yc0NhcmdvRGV0YWlsc0xpc3QgPSBzZWxlY3Rpb24NCiAgICAgIHRoaXMuc2VsZWN0ZWRDYXJnb0RldGFpbCA9IHNlbGVjdGlvbg0KICAgIH0sDQogICAgZ2V0U3VtbWFyaWVzKHBhcmFtKSB7DQogICAgICBjb25zdCB7Y29sdW1ucywgZGF0YX0gPSBwYXJhbQ0KICAgICAgY29uc3Qgc3VtcyA9IFtdDQogICAgICBjb25zdCBzdGF0aXN0aWNhbEZpZWxkID0gWw0KICAgICAgICAicmVjZWl2ZWRTdXBwbGllciIsICJ0b3RhbEJveGVzIiwgInVucGFpZEluYm91bmRGZWUiLCAidG90YWxHcm9zc1dlaWdodCIsDQogICAgICAgICJ0b3RhbFZvbHVtZSIsICJyZWNlaXZlZFN0b3JhZ2VGZWUiLCAidW5wYWlkVW5sb2FkaW5nRmVlIiwgImxvZ2lzdGljc0FkdmFuY2VGZWUiLA0KICAgICAgICAicmVudGFsQmFsYW5jZUZlZSIsICJvdmVyZHVlUmVudGFsRmVlIiwgImFkZGl0aW9uYWxTdG9yYWdlRmVlIiwgInVucGFpZFVubG9hZGluZ0ZlZSIsDQogICAgICAgICJ1bnBhaWRQYWNraW5nRmVlIiwgInJlY2VpdmVkVW5sb2FkaW5nRmVlIiwgInJlY2VpdmVkUGFja2luZ0ZlZSIsICJpbmJvdW5kRmVlIg0KICAgICAgXQ0KDQogICAgICAvLyDmsYfmgLvnu5PmnpzlrZjlgqjlr7nosaENCiAgICAgIGNvbnN0IHN1bW1hcnlSZXN1bHRzID0ge30NCg0KICAgICAgY29sdW1ucy5mb3JFYWNoKChjb2x1bW4sIGluZGV4KSA9PiB7DQogICAgICAgIGlmIChpbmRleCA9PT0gMCkgew0KICAgICAgICAgIHN1bXNbaW5kZXhdID0gIuaxh+aAuyINCiAgICAgICAgICByZXR1cm4NCiAgICAgICAgfQ0KDQogICAgICAgIGNvbnN0IHZhbHVlcyA9IGRhdGEubWFwKGl0ZW0gPT4gTnVtYmVyKGl0ZW1bY29sdW1uLnByb3BlcnR5XSkpDQoNCiAgICAgICAgaWYgKHN0YXRpc3RpY2FsRmllbGQuaW5jbHVkZXMoY29sdW1uLnByb3BlcnR5KSAmJiAhdmFsdWVzLmV2ZXJ5KHZhbHVlID0+IGlzTmFOKHZhbHVlKSkpIHsNCiAgICAgICAgICBjb25zdCBzdW1WYWx1ZSA9IHZhbHVlcy5yZWR1Y2UoKHByZXYsIGN1cnIpID0+IHsNCiAgICAgICAgICAgIGNvbnN0IHZhbHVlID0gTnVtYmVyKGN1cnIpDQogICAgICAgICAgICBpZiAoIWlzTmFOKHZhbHVlKSkgew0KICAgICAgICAgICAgICByZXR1cm4gY3VycmVuY3kocHJldikuYWRkKGN1cnIpLnZhbHVlDQogICAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgICByZXR1cm4gcHJldg0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0sIDApDQogICAgICAgICAgc3Vtc1tpbmRleF0gPSBzdW1WYWx1ZQ0KDQogICAgICAgICAgLy8g5bCG5rGH5oC757uT5p6c5a2Y5YKo5ZyoIHN1bW1hcnlSZXN1bHRzIOWvueixoeS4rQ0KICAgICAgICAgIHN1bW1hcnlSZXN1bHRzW2NvbHVtbi5wcm9wZXJ0eV0gPSBzdW1WYWx1ZQ0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHN1bXNbaW5kZXhdID0gIiAiDQogICAgICAgIH0NCiAgICAgIH0pDQoNCiAgICAgIC8vIOWmguaenOmcgOimgeWwhuaxh+aAu+e7k+aenOi1i+WAvOWIsOihqOWNleWtl+auteS4re+8jOWPr+S7peWcqOatpOWkhOaTjeS9nA0KICAgICAgLy8g5YGH6K6+6KGo5Y2V5a2X5q6155qE5ZG95ZCN5LiO57uf6K6h5a2X5q615LiA6Ie0DQogICAgICBPYmplY3Qua2V5cyhzdW1tYXJ5UmVzdWx0cykuZm9yRWFjaChmaWVsZCA9PiB7DQogICAgICAgIC8vIGlmICh0aGlzLm91dGJvdW5kRm9ybSAmJiB0aGlzLm91dGJvdW5kRm9ybS5oYXNPd25Qcm9wZXJ0eShmaWVsZCkpIHsNCiAgICAgICAgaWYgKHRoaXMub3V0Ym91bmRGb3JtKSB7DQogICAgICAgICAgdGhpcy5vdXRib3VuZEZvcm1bZmllbGRdID0gc3VtbWFyeVJlc3VsdHNbZmllbGRdICAvLyDlsIbmsYfmgLvlgLzotYvnu5nooajljZXlrZfmrrUNCiAgICAgICAgfQ0KICAgICAgfSkNCg0KICAgICAgcmV0dXJuIHN1bXMNCiAgICB9LA0KDQogICAgLyogZ2V0U3VtbWFyaWVzKHBhcmFtKSB7DQogICAgICBjb25zdCB7Y29sdW1ucywgZGF0YX0gPSBwYXJhbQ0KICAgICAgY29uc3Qgc3VtcyA9IFtdDQogICAgICBjb25zdCBzdGF0aXN0aWNhbEZpZWxkID0gWyJ0b3RhbEJveGVzIiwgInRvdGFsR3Jvc3NXZWlnaHQiLCAidG90YWxWb2x1bWUiLCAicmVjZWl2ZWRTdG9yYWdlRmVlIiwgInVucGFpZFVubG9hZGluZ0ZlZSIsICJsb2dpc3RpY3NBZHZhbmNlRmVlIiwgInJlbnRhbEJhbGFuY2VGZWUiLCAib3ZlcmR1ZVJlbnRhbEZlZSJdDQogICAgICBjb2x1bW5zLmZvckVhY2goKGNvbHVtbiwgaW5kZXgpID0+IHsNCiAgICAgICAgaWYgKGluZGV4ID09PSAwKSB7DQogICAgICAgICAgc3Vtc1tpbmRleF0gPSAi5rGH5oC7Ig0KICAgICAgICAgIHJldHVybg0KICAgICAgICB9DQogICAgICAgIGNvbnN0IHZhbHVlcyA9IGRhdGEubWFwKGl0ZW0gPT4gTnVtYmVyKGl0ZW1bY29sdW1uLnByb3BlcnR5XSkpDQogICAgICAgIGlmIChzdGF0aXN0aWNhbEZpZWxkLmluY2x1ZGVzKGNvbHVtbi5wcm9wZXJ0eSkgJiYgIXZhbHVlcy5ldmVyeSh2YWx1ZSA9PiBpc05hTih2YWx1ZSkpKSB7DQogICAgICAgICAgc3Vtc1tpbmRleF0gPSB2YWx1ZXMucmVkdWNlKChwcmV2LCBjdXJyKSA9PiB7DQogICAgICAgICAgICBjb25zdCB2YWx1ZSA9IE51bWJlcihjdXJyKQ0KICAgICAgICAgICAgaWYgKCFpc05hTih2YWx1ZSkpIHsNCiAgICAgICAgICAgICAgcmV0dXJuIHByZXYgKyBjdXJyDQogICAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgICByZXR1cm4gcHJldg0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0sIDApDQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgc3Vtc1tpbmRleF0gPSAiICINCiAgICAgICAgfQ0KICAgICAgfSkNCiAgICAgIHJldHVybiBzdW1zDQogICAgfSwgKi8NCiAgICBoYW5kbGVPdXRib3VuZFNlbGVjdGlvbkNoYW5nZShzZWxlY3Rpb24pIHsNCiAgICAgIHRoaXMuc2VsZWN0T3V0Ym91bmRMaXN0ID0gc2VsZWN0aW9uDQogICAgfSwNCiAgICBvdXRib3VuZENsaWVudChyb3cpIHsNCiAgICAgIHRoaXMub3V0Ym91bmRGb3JtLndhcmVob3VzZVF1b3RlID0gcm93LnJhdGVMY2wNCiAgICAgIHRoaXMub3V0Ym91bmRGb3JtLmZyZWVTdGFja0RheXMgPSByb3cuZnJlZVN0YWNrUGVyaW9kDQogICAgICB0aGlzLm91dGJvdW5kRm9ybS5vdmVyZHVlUmVudCA9IHJvdy5vdmVyZHVlUmVudA0KICAgICAgdGhpcy5vdXRib3VuZEZvcm0uY2xpZW50TmFtZSA9IHJvdy5jbGllbnROYW1lDQogICAgICAvLyB0aGlzLm91dGJvdW5kRm9ybS53b3JrZXJMb2FkaW5nRmVlPXJvdy53b3JrZXJMb2FkaW5nRmVlDQogICAgICB0aGlzLiRmb3JjZVVwZGF0ZSgpDQogICAgfSwNCiAgICAvKiog5p+l6K+i5Ye65LuT6K6w5b2V5YiX6KGoICovDQogICAgZ2V0TGlzdCgpIHsNCiAgICAgIHRoaXMubG9hZGluZyA9IHRydWUNCiAgICAgIGxpc3RPdXRib3VuZHJlY29yZHModGhpcy5xdWVyeVBhcmFtcykudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgIHRoaXMub3V0Ym91bmRyZWNvcmRMaXN0ID0gcmVzcG9uc2Uucm93cw0KICAgICAgICB0aGlzLnRvdGFsID0gcmVzcG9uc2UudG90YWwNCiAgICAgICAgdGhpcy5sb2FkaW5nID0gZmFsc2UNCiAgICAgIH0pDQogICAgfSwNCiAgICAvLyDlj5bmtojmjInpkq4NCiAgICBjYW5jZWwoKSB7DQogICAgICB0aGlzLm9wZW4gPSBmYWxzZQ0KICAgICAgdGhpcy5yZXNldCgpDQogICAgfSwNCiAgICBvdXRib3VuZFJlc2V0KCkgew0KICAgICAgdGhpcy5vdXRib3VuZEZvcm0gPSB7DQogICAgICAgIG91dGJvdW5kUmVjb3JkSWQ6IG51bGwsDQogICAgICAgIG91dGJvdW5kTm86IG51bGwsDQogICAgICAgIGNsaWVudENvZGU6IG51bGwsDQogICAgICAgIGNsaWVudE5hbWU6IG51bGwsDQogICAgICAgIG9wZXJhdG9ySWQ6IG51bGwsDQogICAgICAgIGNvbnRhaW5lclR5cGU6IG51bGwsDQogICAgICAgIGNvbnRhaW5lck5vOiBudWxsLA0KICAgICAgICBzZWFsTm86IG51bGwsDQogICAgICAgIG91dGJvdW5kRGF0ZTogbnVsbCwNCiAgICAgICAgd2FyZWhvdXNlUXVvdGU6IG51bGwsDQogICAgICAgIHdvcmtlckxvYWRpbmdGZWU6IG51bGwsDQogICAgICAgIHdhcmVob3VzZUNvbGxlY3Rpb246IG51bGwsDQogICAgICAgIGNvbGxlY3Rpb25Ob3RlczogbnVsbCwNCiAgICAgICAgdG90YWxCb3hlczogbnVsbCwNCiAgICAgICAgdG90YWxHcm9zc1dlaWdodDogbnVsbCwNCiAgICAgICAgdG90YWxWb2x1bWU6IG51bGwsDQogICAgICAgIHRvdGFsUm93czogbnVsbCwNCiAgICAgICAgcmVjZWl2ZWRTdG9yYWdlRmVlOiBudWxsLA0KICAgICAgICB1bnBhaWRVbmxvYWRpbmdGZWU6IG51bGwsDQogICAgICAgIHVucGFpZFBhY2tpbmdGZWU6IG51bGwsDQogICAgICAgIGxvZ2lzdGljc0FkdmFuY2VGZWU6IG51bGwsDQogICAgICAgIHJlbnRhbEJhbGFuY2VGZWU6IG51bGwsDQogICAgICAgIG92ZXJkdWVSZW50OiBudWxsLA0KICAgICAgICBmcmVlU3RhY2tEYXlzOiBudWxsLA0KICAgICAgICBvdmVyZHVlVW5pdFByaWNlOiBudWxsDQogICAgICB9DQogICAgICB0aGlzLnByZU91dGJvdW5kSW52ZW50b3J5TGlzdCA9IFtdDQogICAgICB0aGlzLnJlc2V0Rm9ybSgib3V0Ym91bmRGb3JtIikNCiAgICB9LA0KICAgIC8vIOihqOWNlemHjee9rg0KICAgIHJlc2V0KCkgew0KICAgICAgdGhpcy5mb3JtID0gew0KICAgICAgICBvdXRib3VuZFJlY29yZElkOiBudWxsLA0KICAgICAgICBvdXRib3VuZE5vOiBudWxsLA0KICAgICAgICBjbGllbnRDb2RlOiBudWxsLA0KICAgICAgICBjbGllbnROYW1lOiBudWxsLA0KICAgICAgICBvcGVyYXRvcklkOiBudWxsLA0KICAgICAgICBjb250YWluZXJUeXBlOiBudWxsLA0KICAgICAgICBjb250YWluZXJObzogbnVsbCwNCiAgICAgICAgc2VhbE5vOiBudWxsLA0KICAgICAgICBvdXRib3VuZERhdGU6IG51bGwsDQogICAgICAgIHdhcmVob3VzZVF1b3RlOiBudWxsLA0KICAgICAgICB3b3JrZXJMb2FkaW5nRmVlOiBudWxsLA0KICAgICAgICB3YXJlaG91c2VDb2xsZWN0aW9uOiBudWxsLA0KICAgICAgICBjb2xsZWN0aW9uTm90ZXM6IG51bGwsDQogICAgICAgIHRvdGFsQm94ZXM6IG51bGwsDQogICAgICAgIHRvdGFsR3Jvc3NXZWlnaHQ6IG51bGwsDQogICAgICAgIHRvdGFsVm9sdW1lOiBudWxsLA0KICAgICAgICB0b3RhbFJvd3M6IG51bGwsDQogICAgICAgIHJlY2VpdmVkU3RvcmFnZUZlZTogbnVsbCwNCiAgICAgICAgdW5wYWlkVW5sb2FkaW5nRmVlOiBudWxsLA0KICAgICAgICB1bnBhaWRQYWNraW5nRmVlOiBudWxsLA0KICAgICAgICBsb2dpc3RpY3NBZHZhbmNlRmVlOiBudWxsLA0KICAgICAgICByZW50YWxCYWxhbmNlRmVlOiBudWxsLA0KICAgICAgICBvdmVyZHVlUmVudDogbnVsbCwNCiAgICAgICAgZnJlZVN0YWNrRGF5czogbnVsbCwNCiAgICAgICAgb3ZlcmR1ZVVuaXRQcmljZTogbnVsbA0KICAgICAgfQ0KICAgICAgdGhpcy5yZXNldEZvcm0oImZvcm0iKQ0KICAgIH0sDQogICAgLyoqIOaQnOe0ouaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZVF1ZXJ5KCkgew0KICAgICAgdGhpcy5xdWVyeVBhcmFtcy5wYWdlTnVtID0gMQ0KICAgICAgdGhpcy5nZXRMaXN0KCkNCiAgICB9LA0KICAgIC8qKiDph43nva7mjInpkq7mk43kvZwgKi8NCiAgICByZXNldFF1ZXJ5KCkgew0KICAgICAgdGhpcy5yZXNldEZvcm0oInF1ZXJ5Rm9ybSIpDQogICAgICB0aGlzLmhhbmRsZVF1ZXJ5KCkNCiAgICB9LA0KICAgIGhhbmRsZVN0YXR1c0NoYW5nZShyb3cpIHsNCiAgICAgIGxldCB0ZXh0ID0gcm93LnN0YXR1cyA9PT0gIjAiID8gIuWQr+eUqCIgOiAi5YGc55SoIg0KICAgICAgdGhpcy4kbW9kYWwuY29uZmlybSgi56Gu6K6k6KaBXCIiICsgdGV4dCArICLlkJfvvJ8iKS50aGVuKGZ1bmN0aW9uICgpIHsNCiAgICAgICAgcmV0dXJuIGNoYW5nZVN0YXR1cyhyb3cub3V0Ym91bmRSZWNvcmRJZCwgcm93LnN0YXR1cykNCiAgICAgIH0pLnRoZW4oKCkgPT4gew0KICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKHRleHQgKyAi5oiQ5YqfIikNCiAgICAgIH0pLmNhdGNoKGZ1bmN0aW9uICgpIHsNCiAgICAgICAgcm93LnN0YXR1cyA9IHJvdy5zdGF0dXMgPT09ICIwIiA/ICIxIiA6ICIwIg0KICAgICAgfSkNCiAgICB9LA0KICAgIC8vIOWkmumAieahhumAieS4reaVsOaNrg0KICAgIGhhbmRsZVNlbGVjdGlvbkNoYW5nZShzZWxlY3Rpb24pIHsNCiAgICAgIHRoaXMuaWRzID0gc2VsZWN0aW9uLm1hcChpdGVtID0+IGl0ZW0ub3V0Ym91bmRSZWNvcmRJZCkNCiAgICAgIHRoaXMuc2luZ2xlID0gc2VsZWN0aW9uLmxlbmd0aCAhPT0gMQ0KICAgICAgdGhpcy5tdWx0aXBsZSA9ICFzZWxlY3Rpb24ubGVuZ3RoDQogICAgfSwNCiAgICAvKiog5paw5aKe5oyJ6ZKu5pON5L2cICovDQogICAgaGFuZGxlQWRkKCkgew0KICAgICAgdGhpcy5vdXRib3VuZFJlc2V0KCkNCiAgICAgIHRoaXMub3BlbiA9IHRydWUNCiAgICAgIHRoaXMudGl0bGUgPSAi5re75Yqg5Ye65LuT6K6w5b2VIg0KICAgIH0sDQogICAgLyoqIOS/ruaUueaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZVVwZGF0ZShyb3cpIHsNCiAgICAgIHRoaXMub3V0Ym91bmRSZXNldCgpDQogICAgICBjb25zdCBvdXRib3VuZFJlY29yZElkID0gcm93Lm91dGJvdW5kUmVjb3JkSWQgfHwgdGhpcy5pZHMNCiAgICAgIGdldE91dGJvdW5kcmVjb3JkKG91dGJvdW5kUmVjb3JkSWQpLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICB0aGlzLmZvcm0gPSByZXNwb25zZS5kYXRhDQogICAgICAgIHRoaXMub3BlbiA9IHRydWUNCiAgICAgICAgdGhpcy50aXRsZSA9ICLkv67mlLnlh7rku5PorrDlvZUiDQogICAgICB9KQ0KICAgIH0sDQogICAgLyoqIOaPkOS6pOaMiemSriAqLw0KICAgIHN1Ym1pdEZvcm0oKSB7DQogICAgICB0aGlzLiRyZWZzWyJvdXRib3VuZEZvcm0iXS52YWxpZGF0ZSh2YWxpZCA9PiB7DQogICAgICAgIGlmICh2YWxpZCkgew0KICAgICAgICAgIGlmICh0aGlzLm91dGJvdW5kRm9ybS5vdXRib3VuZFJlY29yZElkICE9IG51bGwpIHsNCiAgICAgICAgICAgIHVwZGF0ZU91dGJvdW5kcmVjb3JkKHRoaXMub3V0Ym91bmRGb3JtKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5L+u5pS55oiQ5YqfIikNCiAgICAgICAgICAgICAgdGhpcy5vcGVuID0gZmFsc2UNCiAgICAgICAgICAgICAgdGhpcy5nZXRMaXN0KCkNCiAgICAgICAgICAgIH0pDQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICB9KQ0KICAgIH0sDQogICAgLyoqIOWIoOmZpOaMiemSruaTjeS9nCAqLw0KICAgIGhhbmRsZURlbGV0ZShyb3cpIHsNCiAgICAgIGNvbnN0IG91dGJvdW5kUmVjb3JkSWRzID0gcm93Lm91dGJvdW5kUmVjb3JkSWQgfHwgdGhpcy5pZHMNCiAgICAgIHRoaXMuJG1vZGFsLmNvbmZpcm0oIuaYr+WQpuehruiupOWIoOmZpOWHuuS7k+iusOW9lee8luWPt+S4ulwiIiArIG91dGJvdW5kUmVjb3JkSWRzICsgIlwi55qE5pWw5o2u6aG577yfIikudGhlbihmdW5jdGlvbiAoKSB7DQogICAgICAgIHJldHVybiBkZWxPdXRib3VuZHJlY29yZChvdXRib3VuZFJlY29yZElkcykNCiAgICAgIH0pLnRoZW4oKCkgPT4gew0KICAgICAgICB0aGlzLmdldExpc3QoKQ0KICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLliKDpmaTmiJDlip8iKQ0KICAgICAgfSkuY2F0Y2goKCkgPT4gew0KICAgICAgfSkNCiAgICB9LA0KICAgIC8qKiDlr7zlh7rmjInpkq7mk43kvZwgKi8NCiAgICBoYW5kbGVFeHBvcnQoKSB7DQogICAgICB0aGlzLmRvd25sb2FkKCJzeXN0ZW0vb3V0Ym91bmRyZWNvcmQvZXhwb3J0Iiwgew0KICAgICAgICAuLi50aGlzLnF1ZXJ5UGFyYW1zDQogICAgICB9LCBgb3V0Ym91bmRyZWNvcmRfJHtuZXcgRGF0ZSgpLmdldFRpbWUoKX0ueGxzeGApDQogICAgfSwNCiAgICAvLyDlpITnkIbotLnnlKjlrZfmrrXlj5jmm7TnmoTpgJrnlKjpgLvovpENCiAgICBoYW5kbGVGZWVDaGFuZ2Uocm93LCBmaWVsZCwgdmFsdWUpIHsNCiAgICAgIC8vIOehruS/neWAvOS4uuaVsOWtlw0KICAgICAgdmFsdWUgPSBOdW1iZXIodmFsdWUpIHx8IDANCg0KICAgICAgLy8g5L2/55SoJHNldOehruS/neWTjeW6lOW8j+abtOaWsA0KICAgICAgdGhpcy4kc2V0KHJvdywgZmllbGQsIHZhbHVlKQ0KDQogICAgICAvLyDlr7nnibnlrprlrZfmrrXlgZrpop3lpJblpITnkIYNCiAgICAgIGlmIChmaWVsZCA9PT0gInJlY2VpdmVkU3RvcmFnZUZlZSIgJiYgcm93LmluY2x1ZGVzSW5ib3VuZEZlZSA9PT0gMCkgew0KICAgICAgICBjb25zdCBpbmJvdW5kRmVlID0gTnVtYmVyKHJvdy5pbmJvdW5kRmVlIHx8IDApDQogICAgICAgIGNvbnN0IGRpZmZlcmVuY2UgPSBjdXJyZW5jeShpbmJvdW5kRmVlKS5zdWJ0cmFjdCh2YWx1ZSkudmFsdWUNCiAgICAgICAgdGhpcy4kc2V0KHJvdywgImFkZGl0aW9uYWxTdG9yYWdlRmVlIiwgZGlmZmVyZW5jZSA+IDAgPyBkaWZmZXJlbmNlIDogMCkNCiAgICAgIH0NCg0KICAgICAgaWYgKGZpZWxkID09PSAnaW5ib3VuZEZlZScpIHsNCiAgICAgICAgY29uc3QgZGlmZmVyZW5jZSA9IGN1cnJlbmN5KHZhbHVlKS5zdWJ0cmFjdChyb3cucmVjZWl2ZWRTdG9yYWdlRmVlKS52YWx1ZQ0KICAgICAgICB0aGlzLiRzZXQocm93LCAiYWRkaXRpb25hbFN0b3JhZ2VGZWUiLCBkaWZmZXJlbmNlID4gMCA/IGRpZmZlcmVuY2UgOiAwKQ0KICAgICAgfQ0KDQogICAgICAvLyDlvLrliLbmm7TmlrDooajmoLzop4blm74NCiAgICAgIHRoaXMuJGZvcmNlVXBkYXRlKCkNCg0KICAgICAgLy8g5bCG5L+u5pS55ZCO55qE5YC86K6+572u5Zue6KGo5qC855qE5pWw5o2u5rqQ77yM56Gu5L+d6KGo5qC85pi+56S65pyA5paw5YC8DQogICAgICBjb25zdCBpbmRleCA9IHRoaXMub3V0Ym91bmRGb3JtLnJzSW52ZW50b3J5TGlzdC5maW5kSW5kZXgoaXRlbSA9PiBpdGVtID09PSByb3cpDQogICAgICBpZiAoaW5kZXggIT09IC0xKSB7DQogICAgICAgIHRoaXMuJHNldCh0aGlzLm91dGJvdW5kRm9ybS5yc0ludmVudG9yeUxpc3QsIGluZGV4LCB7Li4ucm93fSkNCiAgICAgIH0NCg0KICAgICAgLy8g5pu05paw6KGo5qC85ZKM5rGH5oC7DQogICAgICB0aGlzLnVwZGF0ZVRhYmxlRGF0YSgpDQogICAgfSwNCiAgICAvLyDmm7TmlrDooajmoLzmlbDmja7lubbph43mlrDorqHnrpfmsYfmgLsNCiAgICB1cGRhdGVUYWJsZURhdGEoKSB7DQogICAgICAvLyDlvLrliLbmm7TmlrDop4blm74NCiAgICAgIHRoaXMuJGZvcmNlVXBkYXRlKCkNCiAgICAgIC8vIOmHjeaWsOiuoeeul+axh+aAuw0KICAgICAgdGhpcy4kbmV4dFRpY2soKCkgPT4gew0KICAgICAgICAvLyDosIPnlKjorqHnrpfmsYfmgLvnmoTmlrnms5UNCiAgICAgICAgaWYgKHRoaXMub3V0Ym91bmRGb3JtLnJzSW52ZW50b3J5TGlzdCAmJiB0aGlzLm91dGJvdW5kRm9ybS5yc0ludmVudG9yeUxpc3QubGVuZ3RoID4gMCkgew0KICAgICAgICAgIHRoaXMuZ2V0U3VtbWFyaWVzKHsNCiAgICAgICAgICAgIGNvbHVtbnM6IHRoaXMuJHJlZnMub3V0Ym91bmRJbnZlbnRvcnlUYWJsZSA/IHRoaXMuJHJlZnMub3V0Ym91bmRJbnZlbnRvcnlUYWJsZS5jb2x1bW5zIDogW10sDQogICAgICAgICAgICBkYXRhOiB0aGlzLm91dGJvdW5kRm9ybS5yc0ludmVudG9yeUxpc3QNCiAgICAgICAgICB9KQ0KICAgICAgICB9DQogICAgICAgIHRoaXMuY291bnRTdW1tYXJ5KCkNCiAgICAgIH0pDQogICAgfQ0KICB9DQp9DQo="}, null]}