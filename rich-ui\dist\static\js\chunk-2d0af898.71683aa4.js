(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d0af898"],{"0f6b":function(t,e,s){"use strict";s.r(e);var n=function(){var t=this,e=t.$createElement,s=t._self._c||e;return s("div",[s("h6",{staticStyle:{margin:"0"}},[t._v(t._s(t.scope.row.currencyCode))])])},r=[],a=s("4360"),i={name:"currency",props:["scope"],created:function(){var t=this;0==this.$store.state.data.currencyList.length||this.$store.state.data.redisList.currency?a["a"].dispatch("getCurrencyList").then((function(){t.options=t.$store.state.data.currencyList})):this.options=this.$store.state.data.currencyList},data:function(){return{options:[],size:this.$store.state.app.size||"mini"}}},c=i,o=s("2877"),u=Object(o["a"])(c,n,r,!1,null,"05a81096",null);e["default"]=u.exports}}]);