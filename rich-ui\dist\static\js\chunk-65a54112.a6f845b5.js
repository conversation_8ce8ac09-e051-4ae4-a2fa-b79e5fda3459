(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-65a54112","chunk-68702101","chunk-46f65df6","chunk-2d0ba824"],{"0062":function(e,t,r){"use strict";r.r(t);var a=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",{staticClass:"app-container"},[r("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],ref:"queryForm",staticClass:"query",attrs:{inline:!0,model:e.queryParams,"label-width":"68px",size:"mini"}},[r("el-form-item",{attrs:{label:"任务名称",prop:"jobName"}},[r("el-input",{staticStyle:{width:"240px"},attrs:{clearable:"",placeholder:"任务名称"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.jobName,callback:function(t){e.$set(e.queryParams,"jobName",t)},expression:"queryParams.jobName"}})],1),r("el-form-item",{attrs:{label:"任务组名",prop:"jobGroup"}},[r("el-select",{staticStyle:{width:"240px"},attrs:{clearable:"",placeholder:"任务组名"},model:{value:e.queryParams.jobGroup,callback:function(t){e.$set(e.queryParams,"jobGroup",t)},expression:"queryParams.jobGroup"}},e._l(e.dict.type.sys_job_group,(function(e){return r("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),r("el-form-item",{attrs:{label:"执行状态",prop:"status"}},[r("el-select",{staticStyle:{width:"240px"},attrs:{clearable:"",placeholder:"执行状态"},model:{value:e.queryParams.status,callback:function(t){e.$set(e.queryParams,"status",t)},expression:"queryParams.status"}},e._l(e.dict.type.sys_common_status,(function(e){return r("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),r("el-form-item",{attrs:{label:"执行时间"}},[r("el-date-picker",{staticStyle:{width:"240px"},attrs:{"end-placeholder":"结束日期","range-separator":"-","start-placeholder":"开始日期",type:"daterange","default-time":["00:00:00","23:59:59"],"value-format":"yyyy-MM-dd HH:mm:ss"},model:{value:e.dateRange,callback:function(t){e.dateRange=t},expression:"dateRange"}})],1),r("el-form-item",[r("el-button",{attrs:{icon:"el-icon-search",size:"mini",type:"primary"},on:{click:e.handleQuery}},[e._v("搜索")]),r("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1),r("el-row",{staticClass:"mb8",attrs:{gutter:10}},[r("el-col",{attrs:{span:1.5}},[r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["monitor:job:remove"],expression:"['monitor:job:remove']"}],attrs:{disabled:e.multiple,icon:"el-icon-delete",plain:"",size:"mini",type:"danger"},on:{click:e.handleDelete}},[e._v("删除 ")])],1),r("el-col",{attrs:{span:1.5}},[r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["monitor:job:remove"],expression:"['monitor:job:remove']"}],attrs:{icon:"el-icon-delete",plain:"",size:"mini",type:"danger"},on:{click:e.handleClean}},[e._v("清空 ")])],1),r("el-col",{attrs:{span:1.5}},[r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["monitor:job:export"],expression:"['monitor:job:export']"}],attrs:{icon:"el-icon-download",plain:"",size:"mini",type:"warning"},on:{click:e.handleExport}},[e._v("导出 ")])],1),r("el-col",{attrs:{span:1.5}},[r("el-button",{attrs:{icon:"el-icon-close",plain:"",size:"mini",type:"warning"},on:{click:e.handleClose}},[e._v("关闭 ")])],1),r("right-toolbar",{attrs:{showSearch:e.showSearch},on:{"update:showSearch":function(t){e.showSearch=t},"update:show-search":function(t){e.showSearch=t},queryTable:e.getList}})],1),r("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.jobLogList},on:{"selection-change":e.handleSelectionChange}},[r("el-table-column",{attrs:{type:"selection",width:"28",align:"center"}}),r("el-table-column",{attrs:{align:"center",label:"日志编号",prop:"jobLogId",width:"80"}}),r("el-table-column",{attrs:{"show-tooltip-when-overflow":!0,align:"center",label:"任务名称",prop:"jobName"}}),r("el-table-column",{attrs:{"show-tooltip-when-overflow":!0,align:"center",label:"任务组名",prop:"jobGroup"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("dict-tag",{attrs:{options:e.dict.type.sys_job_group,value:t.row.jobGroup}})]}}])}),r("el-table-column",{attrs:{"show-tooltip-when-overflow":!0,align:"center",label:"调用目标字符串",prop:"invokeTarget"}}),r("el-table-column",{attrs:{"show-tooltip-when-overflow":!0,align:"center",label:"日志信息",prop:"jobMessage"}}),r("el-table-column",{attrs:{align:"center",label:"执行状态",prop:"status"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("dict-tag",{attrs:{options:e.dict.type.sys_common_status,value:t.row.status}})]}}])}),r("el-table-column",{attrs:{align:"center",label:"执行时间",prop:"createTime",width:"180"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("span",[e._v(e._s(e.parseTime(t.row.createTime)))])]}}])}),r("el-table-column",{attrs:{align:"center","class-name":"small-padding fixed-width",label:"操作"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["monitor:job:query"],expression:"['monitor:job:query']"}],attrs:{icon:"el-icon-view",size:"mini",type:"text"},on:{click:function(r){return e.handleView(t.row)}}},[e._v("详细 ")])]}}])})],1),r("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total>0"}],attrs:{limit:e.queryParams.pageSize,page:e.queryParams.pageNum,total:e.total},on:{"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},pagination:e.getList}}),r("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"},{name:"dialogDragWidth",rawName:"v-dialogDragWidth"}],attrs:{"close-on-click-modal":!1,"modal-append-to-body":!1,visible:e.open,"append-to-body":"",title:"调度日志详细",width:"700px"},on:{"update:visible":function(t){e.open=t}}},[r("el-form",{ref:"form",attrs:{model:e.form,"label-width":"100px",size:"mini"}},[r("el-row",[r("el-col",{attrs:{span:12}},[r("el-form-item",{attrs:{label:"日志序号："}},[e._v(e._s(e.form.jobLogId))]),r("el-form-item",{attrs:{label:"任务名称："}},[e._v(e._s(e.form.jobName))])],1),r("el-col",{attrs:{span:12}},[r("el-form-item",{attrs:{label:"任务分组："}},[e._v(e._s(e.form.jobGroup))]),r("el-form-item",{attrs:{label:"执行时间："}},[e._v(e._s(e.form.createTime))])],1),r("el-col",{attrs:{span:24}},[r("el-form-item",{attrs:{label:"调用方法："}},[e._v(e._s(e.form.invokeTarget))])],1),r("el-col",{attrs:{span:24}},[r("el-form-item",{attrs:{label:"日志信息："}},[e._v(e._s(e.form.jobMessage))])],1),r("el-col",{attrs:{span:24}},[r("el-form-item",{attrs:{label:"执行状态："}},[0==e.form.status?r("div",[e._v("正常")]):1==e.form.status?r("div",[e._v("失败")]):e._e()])],1),r("el-col",{attrs:{span:24}},[1==e.form.status?r("el-form-item",{attrs:{label:"异常信息："}},[e._v(e._s(e.form.exceptionInfo))]):e._e()],1)],1)],1),r("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[r("el-button",{on:{click:function(t){e.open=!1}}},[e._v("关 闭")])],1)],1)],1)},n=[],i=r("5530"),o=(r("d81d"),r("a159")),s=r("b775");function l(e){return Object(s["a"])({url:"/monitor/jobLog/list",method:"get",params:e})}function c(e){return Object(s["a"])({url:"/monitor/jobLog/"+e,method:"delete"})}function u(){return Object(s["a"])({url:"/monitor/jobLog/clean",method:"delete"})}var d={name:"JobLog",dicts:["sys_common_status","sys_job_group"],data:function(){return{loading:!0,ids:[],multiple:!0,showSearch:!0,total:0,jobLogList:[],open:!1,dateRange:[],form:{},queryParams:{pageNum:1,pageSize:10,jobName:void 0,jobGroup:void 0,status:void 0}}},created:function(){var e=this,t=this.$route.params&&this.$route.params.jobId;void 0!=t&&0!=t?Object(o["d"])(t).then((function(t){e.queryParams.jobName=t.data.jobName,e.queryParams.jobGroup=t.data.jobGroup,e.getList()})):this.getList()},methods:{getList:function(){var e=this;this.loading=!0,l(this.addDateRange(this.queryParams,this.dateRange)).then((function(t){e.jobLogList=t.rows,e.total=t.total,e.loading=!1}))},handleClose:function(){var e={path:"/monitor/job"};this.$tab.closeOpenPage(e)},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.dateRange=[],this.resetForm("queryForm"),this.handleQuery()},handleSelectionChange:function(e){this.ids=e.map((function(e){return e.jobLogId})),this.multiple=!e.length},handleView:function(e){this.open=!0,this.form=e},handleDelete:function(e){var t=this,r=this.ids;this.$confirm('是否确认删除调度日志编号为"'+r+'"的数据项？',"提示",{customClass:"modal-confirm"}).then((function(){return c(r)})).then((function(){t.getList(),t.$modal.msgSuccess("删除成功")})).catch((function(){}))},handleClean:function(){var e=this;this.$confirm("是否确认清空所有调度日志数据项？","提示",{customClass:"modal-confirm"}).then((function(){return u()})).then((function(){e.getList(),e.$modal.msgSuccess("清空成功")})).catch((function(){}))},handleExport:function(){this.download("/monitor/jobLog/export",Object(i["a"])({},this.queryParams),"log_".concat((new Date).getTime(),".xlsx"))}}},h=d,p=r("2877"),f=Object(p["a"])(h,a,n,!1,null,null,null);t["default"]=f.exports},3809:function(e,t,r){"use strict";r.r(t);var a=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",[r("el-tooltip",{attrs:{placement:"top",disabled:null==e.scope.row.companyGrade||e.scope.row.companyGrade.length<3||((null!=e.scope.row.contractType?e.scope.row.contractType:"")+(null!=e.scope.row.contractType&&null!=e.scope.row.contractNo?"：":"")+(null!=e.scope.row.contractNo?e.scope.row.contractNo:"")).length<10}},[r("div",{attrs:{slot:"content"},slot:"content"},[r("h6",{staticStyle:{margin:"0"}},[e._v(" "+e._s(e.scope.row.companyGrade)+" ")]),r("h6",{staticStyle:{margin:"0","font-weight":"bold","font-size":"small"}},[e._v(" "+e._s((null!=e.scope.row.contractType?e.scope.row.contractType:"")+(null!=e.scope.row.contractType&&null!=e.scope.row.contractNo&&""!=e.scope.row.contractNo?"：":"")+(null!=e.scope.row.contractNo?e.scope.row.contractNo:""))+" ")])]),r("div",[r("h6",{staticStyle:{margin:"0",overflow:"hidden","text-overflow":"ellipsis","white-space":"nowrap"}},[e._v(" "+e._s(e.scope.row.companyGrade)+" ")]),r("h6",{staticStyle:{margin:"0",overflow:"hidden","text-overflow":"ellipsis","white-space":"nowrap"}},[e._v(" "+e._s((null!=e.scope.row.contractType?e.scope.row.contractType:"")+(null!=e.scope.row.contractType&&null!=e.scope.row.contractNo&&""!=e.scope.row.contractNo?"：":"")+(null!=e.scope.row.contractNo?e.scope.row.contractNo:""))+" ")])])])],1)},n=[],i={name:"company",props:["scope"],data:function(){return{size:this.$store.state.app.size||"mini"}}},o=i,s=r("2877"),l=Object(s["a"])(o,a,n,!1,null,"5ca2675e",null);t["default"]=l.exports},5666:function(e,t,r){"use strict";r.r(t);var a=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",{staticClass:"app-container"},[r("el-row",[r("el-col",{attrs:{span:e.showLeft}},[r("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],ref:"queryForm",staticClass:"query",attrs:{model:e.queryParams,size:"mini",inline:!0}},[r("el-form-item",{attrs:{label:"单号",prop:"richNo"}},[r("el-input",{staticStyle:{width:"100%"},attrs:{placeholder:"报价单号",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.richNo,callback:function(t){e.$set(e.queryParams,"richNo",t)},expression:"queryParams.richNo"}})],1),r("el-form-item",{attrs:{label:"业务",prop:"queryStaffId"}},[r("treeselect",{staticStyle:{width:"100%"},attrs:{"disable-branch-nodes":!0,"disable-fuzzy-matching":!0,"flatten-search-results":!0,normalizer:e.staffNormalizer,options:e.belongList,"show-count":!0,placeholder:"业务员"},on:{input:e.cleanStaffId,open:e.loadSales,select:e.handleSelectStaffId},scopedSlots:e._u([{key:"value-label",fn:function(t){var a=t.node;return r("div",{},[e._v(" "+e._s(void 0!=a.raw.staff?a.raw.staff.staffFamilyLocalName+a.raw.staff.staffGivingLocalName+" "+a.raw.staff.staffGivingEnName:"")+" ")])}},{key:"option-label",fn:function(t){var a=t.node,n=t.shouldShowCount,i=t.count,o=t.labelClassName,s=t.countClassName;return r("label",{class:o},[e._v(" "+e._s(-1!=a.label.indexOf(",")?a.label.substring(0,a.label.indexOf(",")):a.label)+" "),n?r("span",{class:s},[e._v("("+e._s(i)+")")]):e._e()])}}]),model:{value:e.queryStaffId,callback:function(t){e.queryStaffId=t},expression:"queryStaffId"}})],1),r("el-form-item",{attrs:{label:"客户",prop:"companyId"}},[r("company-select",{attrs:{"load-options":this.companyList,multiple:!1,"role-client":"1","no-parent":!0,pass:e.queryParams.companyId,placeholder:"客户","role-control":!0,roleTypeId:1},on:{return:function(t){e.queryParams.companyId=t}}})],1),r("el-form-item",{attrs:{label:"物流",prop:"logisticsTypeId"}},[r("tree-select",{staticStyle:{width:"100%"},attrs:{flat:!1,multiple:!1,pass:e.queryParams.logisticsTypeId,"d-load":!0,placeholder:"物流类型",type:"mainServiceType"},on:{returnData:e.queryLogisticsType}})],1),r("el-form-item",{attrs:{label:"服务",prop:"serviceTypeId"}},[r("tree-select",{staticStyle:{width:"100%"},attrs:{flat:!1,multiple:!1,pass:e.queryParams.serviceTypeId,"d-load":!0,placeholder:"服务项目",type:"serviceType"},on:{returnData:e.queryServiceType}})],1),r("el-form-item",{attrs:{label:"货型",prop:"cargoTypeIds"}},[r("tree-select",{staticStyle:{width:"100%"},attrs:{multiple:!0,pass:e.queryParams.cargoTypeIds,placeholder:"货物特征","d-load":!0,type:"cargoType"},on:{return:e.queryCargoTypeIds}})],1),r("el-form-item",{attrs:{label:"限重",prop:"grossWeight"}},[r("el-input-number",{staticStyle:{width:"100%"},attrs:{controls:!1,placeholder:"货物限重",precision:2,step:.01},model:{value:e.queryParams.grossWeight,callback:function(t){e.$set(e.queryParams,"grossWeight",t)},expression:"queryParams.grossWeight"}})],1),r("el-form-item",{attrs:{label:"启运",prop:"departureId"}},[r("location-select",{ref:"departure",staticStyle:{width:"100%"},attrs:{"check-port":e.checkPort,multiple:!1,pass:e.queryParams.departureId},on:{return:e.queryDepartureId}})],1),r("el-form-item",{attrs:{label:"目的",prop:"destinationId"}},[r("location-select",{staticStyle:{width:"100%"},attrs:{"check-port":e.checkPort,en:!0,multiple:!1,pass:e.queryParams.destinationId},on:{return:e.queryDestinationId}})],1),r("el-form-item",{attrs:{label:"柜型",prop:"unitId"}},[r("tree-select",{staticStyle:{width:"100%"},attrs:{pass:e.queryParams.unitId,placeholder:"单位",type:"unit","d-load":!0},on:{return:e.queryUnitId}})],1),r("el-form-item",{attrs:{label:"承运",prop:"queryCarrierIds"}},[r("treeselect",{staticStyle:{width:"100%"},attrs:{"disable-branch-nodes":!0,"disable-fuzzy-matching":!0,"flatten-search-results":!0,multiple:!0,normalizer:e.carrierNormalizer,options:e.carrierList,"show-count":!0,placeholder:"承运人"},on:{open:e.loadCarrier,deselect:e.handleDeselectQueryCarrierIds,input:e.deselectAllQueryCarrierIds,select:e.handleSelectQueryCarrierIds},scopedSlots:e._u([{key:"value-label",fn:function(t){var a=t.node;return r("div",{},[e._v(" "+e._s(a.raw.carrier.carrierIntlCode)+" "+e._s(null==a.raw.carrier.carrierIntlCode?a.raw.carrier.carrierShortName:"")+" ")])}},{key:"option-label",fn:function(t){var a=t.node,n=t.shouldShowCount,i=t.count,o=t.labelClassName,s=t.countClassName;return r("label",{class:o},[e._v(" "+e._s(-1!=a.label.indexOf(",")?a.label.substring(0,a.label.indexOf(",")):a.label)+" "),n?r("span",{class:s},[e._v("("+e._s(i)+")")]):e._e()])}}]),model:{value:e.queryCarrierIds,callback:function(t){e.queryCarrierIds=t},expression:"queryCarrierIds"}})],1),r("el-form-item",{attrs:{label:"评级",prop:"showPriority"}},[r("el-input",{staticStyle:{width:"100%"},attrs:{placeholder:"供应商评级"},on:{change:e.handleQuery},model:{value:e.queryParams.showPriority,callback:function(t){e.$set(e.queryParams,"showPriority",t)},expression:"queryParams.showPriority"}})],1),r("el-form-item",{attrs:{label:"订舱",prop:"orderMark"}},[r("el-input",{staticStyle:{width:"100%"},attrs:{placeholder:"订舱标记"},on:{change:e.handleQuery},model:{value:e.queryParams.orderMark,callback:function(t){e.$set(e.queryParams,"orderMark",t)},expression:"queryParams.orderMark"}})],1),r("el-form-item",[r("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"mini"},on:{click:e.handleQuery}},[e._v("搜索")]),r("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1)],1),r("el-col",{attrs:{span:e.showRight}},[r("div",{staticClass:"declare"},[r("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.quotationList,border:"","row-class-name":e.tableRowClassName}},[r("el-table-column",{scopedSlots:e._u([{key:"header",fn:function(t){return[r("h2",{staticStyle:{"font-weight":"bold",margin:"5px",display:"inline-block"}},[e._v(" 客户报价列表： ")]),r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:quotation:add"],expression:"['system:quotation:add']"}],staticStyle:{padding:"4px"},attrs:{type:"primary",plain:"",icon:"el-icon-plus",size:"mini"},on:{click:e.handleAdd}},[e._v("新增 ")]),r("right-toolbar",{attrs:{showSearch:e.showSearch},on:{"update:showSearch":function(t){e.showSearch=t},"update:show-search":function(t){e.showSearch=t},queryTable:e.getList}})]}}])},[r("el-table-column",{attrs:{width:"25px"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("el-button",{staticStyle:{padding:"0"},attrs:{size:"mini"},on:{click:function(r){return e.searchFreight(t.row)}}},[e._v("选定 ")])]}}])}),r("el-table-column",{attrs:{align:"center",label:"报价单号",prop:"richNo","show-tooltip-when-overflow":"",width:"68px"}}),r("el-table-column",{attrs:{align:"center",label:"瑞旗业务员",prop:"staffName","show-tooltip-when-overflow":"",width:"88px"}}),r("el-table-column",{attrs:{align:"center",label:"客户",prop:"company","show-tooltip-when-overflow":"",width:"80px"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(t.row.company)+" "+e._s(t.row.extStaffName)+" ")]}}])}),r("el-table-column",{attrs:{align:"center",label:"物流类型",prop:"logisticsType","show-tooltip-when-overflow":"",width:"58px"}}),r("el-table-column",{attrs:{align:"center",label:"货物名称",prop:"cargoName","show-tooltip-when-overflow":"",width:"58px"}}),r("el-table-column",{attrs:{align:"center",label:"货物特征",prop:"cargoType","show-tooltip-when-overflow":"",width:"88px"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("span",[e._v(e._s(t.row.cargoType))]),r("span",[e._v(" "+e._s(null!=t.row.grossWeight&&0!=t.row.grossWeight||null!=t.row.cargoPrice&&0!=t.row.cargoPrice?",":"")+" ")]),r("span",[e._v(" "+e._s(null!=t.row.grossWeight&&0!=t.row.grossWeight?t.row.grossWeight+(null!=t.row.cargoUnit?t.row.cargoUnit+",":""):""))]),r("span",[e._v(" "+e._s(null!=t.row.cargoPrice&&0!=t.row.cargoPrice?t.row.cargoPrice+(null!=t.row.cargoCurrency?t.row.cargoCurrency:""):"")+" ")])]}}])}),r("el-table-column",{attrs:{align:"center",label:"装运区域",prop:"loading","show-tooltip-when-overflow":"",width:"58px"}}),r("el-table-column",{attrs:{align:"center",label:"启运港",prop:"departure","show-tooltip-when-overflow":"",width:"58px"}}),r("el-table-column",{attrs:{align:"center",label:"目的港",prop:"destination","show-tooltip-when-overflow":"",width:"58px"}}),r("el-table-column",{attrs:{align:"center",label:"货量",prop:"revenueTons","show-tooltip-when-overflow":"",width:"100px"}}),r("el-table-column",{attrs:{align:"center",label:"优选承运人",prop:"carrier","show-tooltip-when-overflow":"",width:"68px"}}),r("el-table-column",{attrs:{label:"货好时间",align:"center",prop:"goodsTime",width:"68px"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("span",[e._v(e._s(e.parseTime(t.row.goodsTime,"{y}-{m}-{d}")))])]}}])}),r("el-table-column",{attrs:{align:"center",label:"需求备注",prop:"remark","show-overflow-tooltip":""}}),r("el-table-column",{attrs:{align:"center",label:"成本总计",prop:"inquiryRate",width:"58px"}}),r("el-table-column",{attrs:{align:"center",label:"报价总计",prop:"quotationRate",width:"58px"}}),r("el-table-column",{attrs:{label:"利润总计",align:"center",prop:"profit",width:"58px"}}),r("el-table-column",{attrs:{label:"订舱标记",align:"center",prop:"orderMark",width:"58px"}}),r("el-table-column",{attrs:{align:"center","class-name":"small-padding fixed-width",label:"操作",width:"120px"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:quotation:edit"],expression:"['system:quotation:edit']"}],staticStyle:{margin:"2px"},attrs:{size:"mini",type:"success",icon:"el-icon-edit"},on:{click:function(r){return e.handleUpdate(t.row)}}},[e._v("修改 ")]),r("el-button",{staticStyle:{margin:"2px"},attrs:{type:"success"},on:{click:function(r){return e.handleBooking(t.row)}}},[e._v(" 订舱 ")]),r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:quotation:remove"],expression:"['system:quotation:remove']"}],staticStyle:{margin:"2px"},attrs:{size:"mini",type:"danger",icon:"el-icon-delete"},on:{click:function(r){return e.handleDelete(t.row)}}},[e._v("删除 ")])]}}])})],1)],1),r("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total>0"}],attrs:{total:e.total,page:e.queryParams.pageNum,limit:e.queryParams.pageSize,"auto-scroll":!1},on:{"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},pagination:e.getList}})],1),r("div",{staticClass:"freight"},[r("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.freightList,border:"","row-class-name":e.tableRowClassName}},[r("el-table-column",[r("template",{slot:"header"},[r("div",{staticStyle:{display:"flex"}},[r("h2",{staticStyle:{"font-weight":"bold",margin:"5px"}},[e._v(" 查询结果列表： ")]),r("tree-select",{ref:"serviceType",staticStyle:{width:"158px"},attrs:{"d-load":!0,dbn:!0,flat:!1,multiple:!1,placeholder:"服务项目",type:"serviceType"},on:{returnData:e.getType}}),r("el-button",{attrs:{type:"primary"},on:{click:e.confirmRequire}},[e._v(" 请求更新 ")])],1)]),e._l(e.columns,(function(t){return t.visible&&"商务备注"!=t.label?r("el-table-column",{key:t.key,attrs:{align:t.align,label:t.label,width:t.width},scopedSlots:e._u([{key:"default",fn:function(e){return[r(t.prop,{tag:"component",attrs:{scope:e}})]}}],null,!0)}):e._e()})),r("el-table-column",{attrs:{align:"center",label:"操作","class-name":"small-padding fixed-width",width:"120"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("el-dropdown",{attrs:{trigger:"click"},on:{command:e.handleSelect}},[r("el-button",{staticStyle:{margin:"2px"},attrs:{size:"mini",type:"success"},on:{click:function(r){e.select=t.row}}},[e._v("确认选择 ")]),r("el-dropdown-menu",{attrs:{slot:"dropdown"},slot:"dropdown"},e._l(e.quotation.midRevenueTonsList,(function(t){return r("el-dropdown-item",{key:t.unitId,attrs:{command:t.unit}},[e._v(" "+e._s(t.count+"x"+t.unit)+" ")])})),1)],1),r("el-dropdown",{attrs:{trigger:"click"},on:{command:e.replaceSelect}},[r("el-button",{staticStyle:{margin:"2px"},attrs:{size:"mini",type:"warning"},on:{click:function(r){e.select=t.row}}},[e._v("替换已选 ")]),r("el-dropdown-menu",{attrs:{slot:"dropdown"},slot:"dropdown"},e._l(e.quotation.midRevenueTonsList,(function(t){return r("el-dropdown-item",{key:t.unitId,attrs:{command:t.unit}},[e._v(" "+e._s(t.count+"x"+t.unit)+" ")])})),1)],1)]}}])})],2)],1),r("pagination",{directives:[{name:"show",rawName:"v-show",value:e.ftotal>0,expression:"ftotal>0"}],attrs:{limit:e.freight.pageSize,page:e.freight.pageNum,total:e.ftotal,"auto-scroll":!1},on:{"update:limit":function(t){return e.$set(e.freight,"pageSize",t)},"update:page":function(t){return e.$set(e.freight,"pageNum",t)},pagination:e.getFreightList}})],1)])],1),r("h2",{staticStyle:{display:"contents","font-weight":"bold",margin:"5px"}},[e._v(" 物流服务费用清单： ")]),r("el-row",[r("el-col",{attrs:{span:4}},[r("el-checkbox-group",{model:{value:e.changeShow,callback:function(t){e.changeShow=t},expression:"changeShow"}},[r("el-checkbox-button",{attrs:{label:"1"}},[e._v("中英文")]),r("el-checkbox-button",{attrs:{label:"2"}},[e._v("含数量")]),r("el-checkbox-button",{attrs:{label:"3"}},[e._v("含行注")]),r("el-checkbox-button",{attrs:{label:"4"}},[e._v("含总计")])],1),r("h4",{ref:"inputValue1",staticStyle:{margin:"5px","font-weight":"bold"}},[e.changeShow.includes("1")?r("span",[e._v(e._s(e.quotation.departureEn)+e._s(null!=e.quotation.departureEn&&null!=e.quotation.destination?"-":"")+e._s(e.quotation.destination)+" ")]):r("span",[e._v(e._s(e.quotation.departure)+e._s(null!=e.quotation.departure&&null!=e.quotation.destination?"-":"")+e._s(e.quotation.destination)+" ")]),r("br"),e.changeShow.includes("1")?r("span",[e._v(e._s(e.quotation.cargoTypeEn))]):r("span",[e._v(e._s(e.quotation.cargoType))]),r("span",[e._v(" "+e._s(0!=e.quotation.grossWeight&&null!=e.quotation.grossWeight?e.quotation.grossWeight+(null!=e.quotation.cargoUnit?e.quotation.cargoUnit:""):"")+" "+e._s(0!=e.quotation.cargoPrice&&null!=e.quotation.cargoPrice?e.quotation.cargoPrice+(null!=e.quotation.cargoCurrency?e.quotation.cargoCurrency:""):"")+" ")]),r("br"),e._v(" "+e._s(e.quotation.revenueTons)+" ")]),r("div",{ref:"inputValue2",staticStyle:{padding:"5px"}},[e._l(e.costList,(function(t){return r("div",e._l(t.freight,(function(t){return null==t.strategyId?r("h5",{staticStyle:{margin:"1px"}},[e.changeShow.includes("1")?r("span",[e._v(" "+e._s(null!=t.chargeEn?t.chargeEn+":":"")+" ")]):r("span",[e._v(" "+e._s(null!=t.charge?t.charge+":":"")+" ")]),e._v(" "+e._s((null!=t.quotationCurrency?t.quotationCurrency.toLowerCase():"")+Number(t.quotationRate)+(null!=t.unit?"/"+t.unit:""))+" "),e.changeShow.includes("2")?r("span",[e._v(e._s("x"+t.quotationAmount))]):e._e(),e.changeShow.includes("3")?r("span",[e.changeShow.includes("1")?r("span",[e._v(e._s(null!=t.locationEn?"("+t.locationEn+")":""))]):r("span",[e._v(" "+e._s(null!=t.location?"("+t.location+")":"")+" ")])]):e._e()]):e._e()})),0)})),e.changeShow.includes("4")?r("div",[e.changeShow.includes("1")?r("span",[e._v("Total:")]):r("span",[e._v("合计：")]),e.changeShow.includes("2")?r("span",[e._v("￥"+e._s(e.quotationAmountTotal))]):r("span",[e._v("￥"+e._s(e.quotationTotalPrice))])]):e._e()],2),r("h5",{ref:"inputValue3",staticStyle:{margin:"5px"}},[e._v(" 1, "),e.changeShow.includes("1")?r("span",[e._v("Quotation included:")]):r("span",[e._v("报价已含：")]),e._l(e.costList,(function(t){return r("span",e._l(t.freight,(function(t){return 1==t.strategyId?r("span",[e.changeShow.includes("1")?r("span",[e._v(e._s(null!=t.chargeEn?t.chargeEn+",":""))]):r("span",[e._v(e._s(null!=t.charge?t.charge+",":""))])]):e._e()})),0)})),r("br"),e._v(" 2, "),e.changeShow.includes("1")?r("span",[e._v("Quotation excluded:")]):r("span",[e._v("报价未含：")]),e._l(e.costList,(function(t){return r("span",e._l(t.freight,(function(t){return 0==t.strategyId?r("span",[e.changeShow.includes("1")?r("span",[e._v(e._s(null!=t.chargeEn?t.chargeEn+",":""))]):r("span",[e._v(e._s(null!=t.charge?t.charge+",":""))])]):e._e()})),0)})),r("br"),e._v(" 3, "),e.changeShow.includes("1")?r("span",[e._v("Shipping Date:")]):r("span",[e._v("船期：")]),e._v(" "+e._s(null!=e.select.shippingDateDetail&&""!=e.select.shippingDateDetail?e.select.shippingDateDetail+",":"")+" "),r("br"),e.changeShow.includes("1")?r("span",[e._v("Valid Date:")]):r("span",[e._v("有效期："+e._s(e.parseTime(e.select.validFrom,"{m}.{d}"))+e._s(null!=e.select.validFrom&&null!=e.select.validTo?"-":"")+e._s(e.parseTime(e.select.validTo,"{m}.{d}"))+" ")])],2),e._l(e.temSelectCharacteristicsList,(function(t){return r("h5",{ref:"inputValue4",refInFor:!0,staticStyle:{margin:"5px"}},[e.changeShow.includes("1")?r("span",{staticStyle:{margin:"0"}},[e._v(" "+e._s(t.serviceTypeEn)+" "+e._s(null!=t.cargoTypeEn&&-1==t.cargoTypeEn.indexOf("All")?t.cargoTypeEn:"")+" "+e._s(t.companyEn)+" "+e._s(null!=t.locationDepartureEn&&-1==t.locationDepartureEn.indexOf("All")?"from "+t.locationDepartureEn+" started":"")+" "+e._s(null!=t.locationDestinationEn&&-1==t.locationDestinationEn.indexOf("All")?"to "+t.locationDestinationEn:"")+" "+e._s(t.infoEn)+" ")]):r("span",{staticStyle:{margin:"0"}},[e._v(" "+e._s(t.serviceType)+" "+e._s(null!=t.cargoType&&-1==t.cargoType.indexOf("全部")?t.cargoType:"")+" "+e._s(t.company)+" "+e._s(null!=t.locationDeparture&&-1==t.locationDeparture.indexOf("全部")?"从"+t.locationDeparture+"起步":"")+" "+e._s(null!=t.locationDestination&&-1==t.locationDestination.indexOf("全部")?"至"+t.locationDestination:"")+" "+e._s(t.info)+" ")]),null!=t.essentialDetail?r("div",e._l(t.essentialDetail.split("\n"),(function(t){return r("div",[r("span",{staticStyle:{margin:"0"}},[e._v(e._s(t))])])})),0):e._e()])})),r("div",[r("textarea",{directives:[{name:"model",rawName:"v-model",value:e.remark,expression:"remark"}],staticStyle:{width:"100%",height:"300px"},attrs:{placeholder:"其他备注"},domProps:{value:e.remark},on:{input:function(t){t.target.composing||(e.remark=t.target.value)}}})])],2),r("el-col",{attrs:{span:20}},[r("el-row",[r("el-table",{attrs:{data:e.costList,"row-class-name":e.rowIndex,border:"","show-summary":"","summary-method":e.getTotalPrice}},[r("el-table-column",{attrs:{label:"服务项目",align:"center",width:"69",prop:"title"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(t.row.title)+" "),r("el-button",{staticStyle:{margin:"0",padding:"0"},attrs:{size:"mini",type:"danger",icon:"el-icon-delete"},on:{click:function(r){return e.handleDeleteFreight(t.row.id)}}})]}}])}),r("el-table-column",{scopedSlots:e._u([{key:"header",fn:function(t){return[r("el-button",{staticStyle:{padding:"4px"},attrs:{plain:"",type:"primary"},on:{click:e.saveQuotation}},[e._v("保存已选费用")])]}},{key:"default",fn:function(t){return[r("el-table",{ref:"freight",staticClass:"pd0",attrs:{data:t.row.freight,"row-key":"id","default-expand-all":"","tree-props":{children:"children",hasChildren:"hasChildren"},"show-header":0==t.$index}},[r("el-table-column",{attrs:{label:"报价设计"}},[r("el-table-column",{attrs:{label:"收费名称",align:"center",prop:"charge",width:"80"},scopedSlots:e._u([{key:"default",fn:function(a){return[a.row.showCharge?r("tree-select",{attrs:{flat:!1,multiple:!1,pass:a.row.chargeId,placeholder:"运费",type:"charge",dbn:!0},on:{returnData:function(r){return e.getChargeId(r,a.row,t.row.freightId)}}}):r("div",{on:{click:function(e){a.row.showCharge=!0}}},[e._v(" "+e._s(a.row.charge)+" ")])]}}],null,!0)}),r("el-table-column",{attrs:{label:"策略",align:"center",prop:"strategyId",width:"58"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("el-select",{attrs:{clearable:""},on:{change:function(r){return e.strategy(t.row)}},model:{value:t.row.strategyId,callback:function(r){e.$set(t.row,"strategyId",r)},expression:"scope.row.strategyId"}},[r("el-option",{attrs:{label:"已含",value:"1"}},[e._v("已含")]),r("el-option",{attrs:{label:"未含",value:"0"}},[e._v("未含")])],1)]}}],null,!0)}),r("el-table-column",{attrs:{align:"center",label:"币种",prop:"quotationCurrencyCode",width:"70"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.showCurrency?r("tree-select",{attrs:{pass:t.row.quotationCurrencyCode,type:"currency"},on:{returnData:function(r){return e.getCurrencyId(r,t.row)}}}):r("div",{on:{click:function(e){t.row.showCurrency=!0}}},[e._v(" "+e._s(t.row.quotationCurrency)+" ")])]}}],null,!0)}),r("el-table-column",{attrs:{align:"center",label:"报价",prop:"quotationRate",width:"68"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("el-input-number",{staticStyle:{display:"flex",width:"100%"},attrs:{controls:!1,min:0},on:{change:function(r){return e.countProfit(t.row)}},model:{value:t.row.quotationRate,callback:function(r){e.$set(t.row,"quotationRate",r)},expression:"scope.row.quotationRate"}})]}}],null,!0)}),r("el-table-column",{attrs:{label:"单位",align:"center",prop:"unitId",width:"70"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.showUnit?r("tree-select",{attrs:{pass:t.row.unitId,type:"unit"},on:{returnData:function(r){return e.getUnitId(r,t.row)}}}):r("div",{staticStyle:{width:"50px",height:"20px"},on:{click:function(e){t.row.showUnit=!0}}},[e._v(e._s(t.row.unitCode)+" ")])]}}],null,!0)}),r("el-table-column",{attrs:{label:"数量",align:"center",prop:"quotationAmount",width:"48"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("el-input-number",{staticStyle:{display:"flex",width:"100%"},attrs:{controls:!1,min:1,placeholder:"数量"},on:{change:function(r){return e.countProfit(t.row)}},model:{value:t.row.quotationAmount,callback:function(r){e.$set(t.row,"quotationAmount",r)},expression:"scope.row.quotationAmount"}})]}}],null,!0)}),r("el-table-column",{attrs:{label:"汇率",align:"center",prop:"exchangeRate",width:"60"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("el-input-number",{staticStyle:{width:"100%"},attrs:{controls:!1,min:1,precision:4,step:1e-4},on:{change:function(r){return e.countProfit(t.row)}},model:{value:t.row.exchangeRate,callback:function(r){e.$set(t.row,"exchangeRate",r)},expression:"scope.row.exchangeRate"}})]}}],null,!0)}),r("el-table-column",{attrs:{label:"税率",align:"center",prop:"taxRate",width:"68"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("el-input-number",{staticStyle:{width:"75%"},attrs:{controls:!1,min:0},on:{change:function(r){return e.countProfit(t.row)}},model:{value:t.row.taxRate,callback:function(r){e.$set(t.row,"taxRate",r)},expression:"scope.row.taxRate"}}),e._v(" % ")]}}],null,!0)}),r("el-table-column",{attrs:{label:"删除",align:"center",prop:"del",width:"35"},scopedSlots:e._u([{key:"default",fn:function(a){return[r("el-button",{staticStyle:{margin:"0",padding:"0"},attrs:{size:"mini",type:"danger",icon:"el-icon-delete"},on:{click:function(r){return e.handleDeleteCost(t.row,a.row)}}})]}}],null,!0)})],1),r("el-table-column",{attrs:{width:"20px","class-name":"showBorder"}}),r("el-table-column",{attrs:{label:"参考成本清单"}},[r("el-table-column",{attrs:{align:"left",label:"成本列表",prop:"inquiryRate",width:"170"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s((null!=t.row.charge?t.row.charge+"：":"")+(null!=t.row.costCurrency?t.row.costCurrency:"")+(null!=t.row.inquiryRate?t.row.inquiryRate+"/":"")+(null!=t.row.unit?t.row.unit:""))+" ")]}}],null,!0)}),r("el-table-column",{attrs:{label:"计费单位",align:"center",prop:"count",width:"50"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("el-input-number",{staticStyle:{display:"flex",width:"100%"},attrs:{controls:!1,min:1,placeholder:"数量"},on:{change:function(r){return e.countProfit(t.row)}},model:{value:t.row.inquiryAmount,callback:function(r){e.$set(t.row,"inquiryAmount",r)},expression:"scope.row.inquiryAmount"}})]}}],null,!0)})],1),r("el-table-column",{attrs:{width:"20px","class-name":"showBorder"}}),r("el-table-column",{attrs:{label:"利润预计"}},[r("el-table-column",{attrs:{label:"选定方案利润",align:"center",prop:"profit"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(t.row.quotationCurrency)+" "+e._s(t.row.profit)+" ")]}}],null,!0)}),r("el-table-column",{attrs:{label:"后备方案最低",align:"center",prop:"backupMin"}}),r("el-table-column",{attrs:{label:"后备方案最高",align:"center",prop:"backupMax"}})],1),r("el-table-column",{attrs:{align:"center",label:"内部号码",prop:"richNo","show-tooltip-when-overflow":""}}),r("template",{slot:"append"},[r("el-button",{staticStyle:{"margin-left":"5px",padding:"0"},attrs:{type:"primary",icon:"el-icon-plus",size:"mini"},on:{click:function(r){return e.handleAddLocal(t.row)}}})],1)],2)]}}])})],1)],1),r("el-row",[r("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],ref:"characteristics",attrs:{data:e.temCharacteristicsList},on:{"selection-change":e.handleSelectionChange}},[r("el-table-column",{attrs:{label:"相关注意事项"}},[r("el-table-column",{attrs:{type:"selection",width:"28",align:"center"}}),r("el-table-column",{attrs:{align:"left",label:"详情","show-tooltip-when-overflow":""},scopedSlots:e._u([{key:"default",fn:function(t){return[r("span",{staticStyle:{margin:"0"}},[e._v(e._s(t.row.serviceType)+" "+e._s(null!=t.row.cargoType&&-1==t.row.cargoType.indexOf("全部")?t.row.cargoType:"")+" "+e._s(t.row.company)+" "+e._s(null!=t.row.locationDeparture&&-1==t.row.locationDeparture.indexOf("全部")?"从"+t.row.locationDeparture+"起步":"")+" "+e._s(null!=t.row.locationDestination&&-1==t.row.locationDestination.indexOf("全部")?"至"+t.row.locationDestination:"")+" "+e._s(t.row.info))]),null!=t.row.essentialDetail?r("div",e._l(t.row.essentialDetail.split("\n"),(function(t){return r("div",[r("span",{staticStyle:{margin:"0"}},[e._v(e._s(t))])])})),0):e._e()]}}])})],1)],1),r("pagination",{directives:[{name:"show",rawName:"v-show",value:e.ctotal>0,expression:"ctotal>0"}],attrs:{limit:e.characteristics.pageSize,page:e.characteristics.pageNum,total:e.ctotal,"auto-scroll":!1},on:{"update:limit":function(t){return e.$set(e.characteristics,"pageSize",t)},"update:page":function(t){return e.$set(e.characteristics,"pageNum",t)},pagination:e.getCharacteristicsList}})],1)],1)],1),r("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"},{name:"dialogDragWidth",rawName:"v-dialogDragWidth"}],attrs:{"close-on-click-modal":!1,"modal-append-to-body":!1,title:e.title,visible:e.open,width:"600px","append-to-body":""},on:{"update:visible":function(t){e.open=t}}},[r("el-form",{ref:"form",staticClass:"edit",attrs:{model:e.form,rules:e.rules,"label-width":"68px"}},[r("el-form-item",{attrs:{label:"报价单号",prop:"richNo"}},[e._v(" "+e._s(e.form.richNo)+" ")]),r("el-row",[r("el-col",{attrs:{span:14}},[r("el-form-item",{attrs:{label:"客户",prop:"companyId"}},[r("company-select",{attrs:{"load-options":this.companyList,multiple:!1,"role-client":"1","no-parent":!0,pass:e.form.companyId,placeholder:"客户","role-control":!0,roleTypeId:1},on:{return:function(t){return e.handleSelectCompany(t)}}})],1)],1),r("el-col",{attrs:{span:10}},[r("el-form-item",{attrs:{prop:"extStaffId","label-width":"0"}},[r("el-select",{staticStyle:{width:"100%"},attrs:{clearable:"",filterable:"",placeholder:"联系人"},model:{value:e.form.extStaffId,callback:function(t){e.$set(e.form,"extStaffId",t)},expression:"form.extStaffId"}},e._l(e.extStaffList,(function(e){return r("el-option",{key:e.staffId,attrs:{label:(null!=e.staffLocalName?e.staffLocalName:"")+" "+(null!=e.staffEnName?e.staffEnName:""),value:e.staffId}})})),1)],1)],1)],1),r("el-row",[r("el-col",{attrs:{span:14}},[r("el-form-item",{attrs:{label:"物流类型",prop:"logisticsTypeId"}},[r("tree-select",{attrs:{flat:!1,multiple:!1,pass:e.form.logisticsTypeId,placeholder:"物流类型",dbn:!0,main:!0,type:"serviceType"},on:{return:e.getLogisticsType}})],1)],1),r("el-col",{attrs:{span:10}},[r("el-form-item",{attrs:{prop:"transportationTermsId","label-width":"0"}},[r("tree-select",{attrs:{flat:!1,multiple:!1,pass:e.form.transportationTermsCode,placeholder:"运输条款",main:!0,type:"transportationTerms"},on:{return:e.getTransportationTerms}})],1)],1)],1),r("el-row",[r("el-col",{attrs:{span:18}},[r("el-form-item",{attrs:{label:"服务项目",prop:"serviceTypeIds"}},[r("tree-select",{attrs:{flat:!1,multiple:!0,pass:e.form.serviceTypeIds,placeholder:"服务项目",type:"serviceType",dbn:!0},on:{return:e.getServiceType}})],1)],1),r("el-col",{attrs:{span:6}},[r("el-form-item",{attrs:{prop:"imExPort","label-width":"0"}},[r("el-select",{staticStyle:{width:"100%"},attrs:{clearable:"",placeholder:"进出口"},model:{value:e.form.imExPort,callback:function(t){e.$set(e.form,"imExPort",t)},expression:"form.imExPort"}},[r("el-option",{attrs:{value:"1",label:"出口"}},[e._v("出口")]),r("el-option",{attrs:{value:"2",label:"进口"}},[e._v("进口")])],1)],1)],1)],1),r("el-form-item",{attrs:{label:"货物特征",prop:"cargoTypeIds"}},[r("tree-select",{attrs:{flat:!1,multiple:!0,pass:e.form.cargoTypeIds,type:"cargoType"},on:{return:e.getCargoTypeIds}})],1),r("el-form-item",{attrs:{label:"货物名称",prop:"cargoName"}},[r("el-input",{attrs:{placeholder:"货物名称"},model:{value:e.form.cargoName,callback:function(t){e.$set(e.form,"cargoName",t)},expression:"form.cargoName"}})],1),r("el-row",[r("el-col",{attrs:{span:9}},[r("el-form-item",{attrs:{label:"货物限重",prop:"grossWeight"}},[r("el-input-number",{staticStyle:{width:"100%"},attrs:{controls:!1,placeholder:"货物限重",precision:2,step:.01},model:{value:e.form.grossWeight,callback:function(t){e.$set(e.form,"grossWeight",t)},expression:"form.grossWeight"}})],1)],1),r("el-col",{attrs:{span:4}},[r("el-form-item",{attrs:{prop:"cargoUnitId","label-width":"0"}},[r("tree-select",{attrs:{pass:e.form.cargoUnitId,type:"unit"},on:{return:e.getCargoUnitId}})],1)],1),r("el-col",{attrs:{span:7}},[r("el-form-item",{attrs:{prop:"cargoPrice","label-width":"0"}},[r("el-input-number",{staticStyle:{width:"100%"},attrs:{controls:!1,precision:2,step:.01,placeholder:"货值"},model:{value:e.form.cargoPrice,callback:function(t){e.$set(e.form,"cargoPrice",t)},expression:"form.cargoPrice"}})],1)],1),r("el-col",{attrs:{span:4}},[r("el-form-item",{attrs:{prop:"cargoCurrencyId","label-width":"0"}},[r("tree-select",{attrs:{pass:e.form.cargoCurrencyId,type:"currency"},on:{return:e.getCargoCurrencyId}})],1)],1)],1),r("el-form-item",{attrs:{label:"装运区域",prop:"loadingIds"}},[r("location-select",{attrs:{"load-options":e.locationOptions,multiple:!1,pass:e.form.loadingIds},on:{return:e.getLoading}})],1),r("el-form-item",{attrs:{label:"启运港",prop:"departureId"}},[r("location-select",{attrs:{"check-port":e.checkPort,"load-options":e.locationOptions,multiple:!1,pass:e.form.departureId},on:{return:e.getDeparture}})],1),r("el-form-item",{attrs:{label:"目的港",prop:"destinationId"}},[r("location-select",{attrs:{multiple:!1,pass:e.form.destinationId,"load-options":e.locationOptions,"check-port":e.checkPort,en:!0},on:{return:e.getDestination}})],1),r("el-form-item",{attrs:{label:"货量",prop:"goodsTime"}},[r("el-row",[r("el-col",{attrs:{span:23}},[r("el-table",{ref:"midRevenueTons",attrs:{data:e.form.midRevenueTonsList,"row-class-name":e.rowIndex}},[r("el-table-column",{attrs:{label:"序号",align:"center",prop:"index",width:"50"}}),r("el-table-column",{attrs:{label:"数量",prop:"count",align:"center"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("el-input-number",{attrs:{controls:!1,min:1,placeholder:"数量"},model:{value:t.row.count,callback:function(r){e.$set(t.row,"count",r)},expression:"scope.row.count"}})]}}])}),r("el-table-column",{attrs:{label:"计费类型",prop:"unitId",align:"center"},scopedSlots:e._u([{key:"default",fn:function(e){return[r("tree-select",{attrs:{pass:e.row.unit,type:"unit"},on:{returnData:function(t){e.row.unitId=t.unitId}}})]}}])}),r("el-table-column",{attrs:{label:"操作",align:"center","class-name":"small-padding fixed-width",width:"50"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("el-button",{attrs:{type:"danger",icon:"el-icon-delete",size:"mini"},on:{click:function(r){return e.handleDeleteMidRevenueTons(t.row)}}})]}}])})],1)],1),r("el-col",{attrs:{span:1}},[r("el-button",{staticStyle:{padding:"0"},attrs:{type:"primary",icon:"el-icon-plus",size:"mini"},on:{click:e.handleAddMidRevenueTons}})],1)],1)],1),r("el-form-item",{attrs:{label:"承运人",prop:"carrierIds"}},[r("treeselect",{attrs:{"disable-fuzzy-matching":!0,"flatten-search-results":!0,multiple:!0,"disable-branch-nodes":!0,normalizer:e.carrierNormalizer,options:e.carrierList,placeholder:"承运人","show-count":!0},on:{open:e.loadCarrier,input:e.deselectAllCarrierIds,deselect:e.handleDeselectCarrierIds,select:e.handleSelectCarrierIds},scopedSlots:e._u([{key:"value-label",fn:function(t){var a=t.node;return r("div",{},[e._v(" "+e._s(a.raw.carrier.carrierIntlCode)+" "+e._s(null==a.raw.carrier.carrierIntlCode?a.raw.carrier.carrierShortName:"")+" ")])}},{key:"option-label",fn:function(t){var a=t.node,n=t.shouldShowCount,i=t.count,o=t.labelClassName,s=t.countClassName;return r("label",{class:o},[e._v(" "+e._s(-1!=a.label.indexOf(",")?a.label.substring(0,a.label.indexOf(",")):a.label)+" "),n?r("span",{class:s},[e._v("("+e._s(i)+")")]):e._e()])}}]),model:{value:e.carrierIds,callback:function(t){e.carrierIds=t},expression:"carrierIds"}})],1),r("el-form-item",{attrs:{label:"货好时间",prop:"goodsTime"}},[r("el-date-picker",{staticStyle:{width:"100%"},attrs:{clearable:"",type:"date","value-format":"yyyy-MM-dd",placeholder:"货好时间"},model:{value:e.form.goodsTime,callback:function(t){e.$set(e.form,"goodsTime",t)},expression:"form.goodsTime"}})],1),r("el-form-item",{attrs:{label:"需求备注",prop:"remark"}},[r("el-input",{attrs:{type:"textarea",autosize:{minRows:6},placeholder:"需求备注"},model:{value:e.form.remark,callback:function(t){e.$set(e.form,"remark",t)},expression:"form.remark"}})],1),r("el-form-item",{attrs:{label:"录入人",prop:"updateBy"}},[e._v(" "+e._s(e.$store.state.user.name)+" ")])],1),r("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[r("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v(e._s(e.edit?"确认修改":"确 认"))]),e.edit?r("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.submitForm("true")}}},[e._v("另存为")]):e._e(),r("el-button",{on:{click:e.cancel}},[e._v("取 消")])],1)],1)],1)},n=[],i=r("5530"),o=r("c7eb"),s=r("1da1"),l=r("b85c"),c=(r("d3b7"),r("6062"),r("3ca3"),r("ddb0"),r("caad"),r("2532"),r("14d9"),r("d81d"),r("159b"),r("4e82"),r("4de4"),r("25f0"),r("b680"),r("a9e3"),r("06ee")),u=r("b0b8"),d=r.n(u),h=r("ca17"),p=r.n(h),f=(r("6f8d"),r("4360")),m=r("b857"),g=r("6b1f"),y=r("98dc"),b=r("3809"),v=r("b66c"),w=r("0f6b"),I=r("87df"),q=r("42dd"),_=r("bfbe"),T=r("de8e"),x=r("8eee"),S=r("0c23"),C=r("27cd"),k=r("86a4"),L=r("363f"),N=r("2947"),j=r("46aa"),O=r("2a3a"),P=r("e996"),R=r("2a23"),F=r("efbd"),$=r("8eca"),D=r("fba1"),E=r("d6c9"),z=r("6e71"),Q=r("aff7"),A=r("c1b9"),G=(r("0062"),r("1122")),M=r("e8bd"),U=r("4af4"),W=r("86ea"),V={name:"Quotation",components:{departure:_["default"],destination:T["default"],CompanySelect:z["a"],Treeselect:p.a,serviceType:P["default"],logisticsType:R["default"],cargoPrice:g["default"],cargoType:y["default"],company:b["default"],contract:v["default"],currency:w["default"],demurrage:I["default"],departureTodestination:q["default"],freeStorage:x["default"],loading:S["default"],loadingLocation:G["default"],destinationLocation:M["default"],price:C["default"],recorder:k["default"],salesRemark:L["default"],shippingDate:N["default"],unit:j["default"],validTime:O["default"],carrier:F["default"],carrierNoSupplier:W["default"],local:$["default"],weight:U["default"]},data:function(){return{showLeft:3,showRight:21,queryStaffId:null,serviceTypeId:null,changeShow:[],highLight:null,edit:!1,typeId:null,quotationTitle:null,quotation:{},select:{},columns:this.$store.state.listSettings.seafreightSetting2,freightList:[],characteristicsList:[],belongList:[],carrierList:[],extStaffList:[],carrierIds:[],queryCarrierIds:[],temCarrierList:[],serviceTypeList:[],temCharacteristicsList:[],temSelectCharacteristicsList:[],locationOptions:new Set,quotationTotalPrice:0,quotationAmountTotal:0,costList:[],loading:!0,single:!0,multiple:!0,showSearch:!0,total:0,quotationList:[],title:"",open:!1,queryParams:{pageNum:1,pageSize:10,richNo:null,staffId:null,companyId:null,logisticsTypeId:null,serviceTypeIds:null,cargoTypeIds:[],departureId:null,destinationId:null,grossWeight:null,carrierIds:[],showPriority:null,orderMark:null,permissionLevel:this.$store.state.user.permissionLevelList.C},form:{},quotationFreight:{},rules:{},freight:{pageSize:10,pageNum:1},ftotal:0,characteristics:{pageSize:10,pageNum:1},ctotal:0,companyList:[],rsCharge:{},inputValue1:null,inputValue2:null,inputValue3:null,inputValue4:null,remark:"",selectRow:null,checkPort:null}},watch:{typeId:function(e){switch(e){case"1":this.quotationTitle="海运",this.columns=this.$store.state.listSettings.seafreightSetting2;break;case"3":this.quotationTitle="铁路",this.columns=this.$store.state.listSettings.seafreightSetting2;break;case"2":this.quotationTitle="空运",this.columns=this.$store.state.listSettings.airfreightSetting;break;case"4":this.quotationTitle="快递",this.columns=this.$store.state.listSettings.expressdeliverySetting;break;case"5":this.quotationTitle="拖车",this.columns=this.$store.state.listSettings.trailerSetting;break;case"6":this.quotationTitle="报关",this.columns=this.$store.state.listSettings.declareSetting;break;case"6":this.quotationTitle="检验",this.columns=this.$store.state.listSettings.benchmarkSetting;break;case"7":this.quotationTitle="保险",this.columns=this.$store.state.listSettings.insuranceSetting;break;case"8":this.quotationTitle="仓储",this.columns=this.$store.state.listSettings.warehouseSetting;break;default:break}},showSearch:function(e){!0===e?(this.showRight=21,this.showLeft=3):(this.showRight=24,this.showLeft=0)},"form.serviceTypeIds":function(e){this.loadCarrier();var t=[];if(void 0!=this.carrierList&&null!=e&&e.includes(-1)){this.temCarrierList=this.carrierList;var r,a=Object(l["a"])(this.carrierList);try{for(a.s();!(r=a.n()).done;){var n=r.value;if(void 0!=n.children&&n.children.length>0){var i,o=Object(l["a"])(n.children);try{for(o.s();!(i=o.n()).done;){var s=i.value;if(void 0!=s.children&&s.children.length>0){var c,u=Object(l["a"])(s.children);try{for(u.s();!(c=u.n()).done;){var d=c.value;null!=this.form.carrierIds&&this.form.carrierIds.includes(d.carrier.carrierId)&&!this.carrierIds.includes(d.serviceTypeId)&&this.carrierIds.push(d.serviceTypeId)}}catch(C){u.e(C)}finally{u.f()}}}}catch(C){o.e(C)}finally{o.f()}}}}catch(C){a.e(C)}finally{a.f()}}if(void 0!=this.carrierList&&null!=e&&!e.includes(-1)){var h,p=Object(l["a"])(this.carrierList);try{for(p.s();!(h=p.n()).done;){var f=h.value;if(null!=e&&void 0!=e){var m,g=Object(l["a"])(e);try{for(g.s();!(m=g.n()).done;){var y=m.value;if(f.serviceTypeId==y&&t.push(f),void 0!=f.children&&f.children.length>0){var b,v=Object(l["a"])(f.children);try{for(v.s();!(b=v.n()).done;){var w=b.value;w.serviceTypeId==y&&t.push(w)}}catch(C){v.e(C)}finally{v.f()}}}}catch(C){g.e(C)}finally{g.f()}}}}catch(C){p.e(C)}finally{p.f()}if(this.temCarrierList=t,this.temCarrierList.length>0){var I,q=Object(l["a"])(this.temCarrierList);try{for(q.s();!(I=q.n()).done;){var _=I.value;if(void 0!=_.children&&_.children.length>0){var T,x=Object(l["a"])(_.children);try{for(x.s();!(T=x.n()).done;){var S=T.value;null!=this.form.carrierIds&&this.form.carrierIds.includes(S.carrier.carrierId)&&!this.carrierIds.includes(S.serviceTypeId)&&this.carrierIds.push(S.serviceTypeId)}}catch(C){x.e(C)}finally{x.f()}}}}catch(C){q.e(C)}finally{q.f()}}}},"form.logisticsTypeId":function(e){var t=this;if(0==this.$store.state.data.serviceTypeList.length||this.$store.state.data.redisList.serviceType)f["a"].dispatch("getServiceTypeList").then((function(){if(t.serviceTypeList){var r,a=Object(l["a"])(t.serviceTypeList);try{for(a.s();!(r=a.n()).done;){var n=r.value;if(n.children){var i,o=Object(l["a"])(n.children);try{for(o.s();!(i=o.n()).done;){var s=i.value;s.serviceTypeId==e&&(t.checkPort=s.typeId)}}catch(c){o.e(c)}finally{o.f()}}n.serviceTypeId==e&&(t.checkPort=n.typeId)}}catch(c){a.e(c)}finally{a.f()}}}));else if(this.serviceTypeList){var r,a=Object(l["a"])(this.serviceTypeList);try{for(a.s();!(r=a.n()).done;){var n=r.value;if(n.children){var i,o=Object(l["a"])(n.children);try{for(o.s();!(i=o.n()).done;){var s=i.value;s.serviceTypeId==e&&(this.checkPort=s.typeId)}}catch(c){o.e(c)}finally{o.f()}}n.serviceTypeId==e&&(this.checkPort=n.typeId)}}catch(c){a.e(c)}finally{a.f()}}}},created:function(){var e=this;this.getList().then((function(){e.loadCarrier(),e.loadSales(),e.getServiceTypeList(),e.getExchangeRate(),e.loadCompany()}))},methods:{getRemark:function(){var e="";return e+=(this.$refs.inputValue1?this.$refs.inputValue1.innerText:"")+"\n",e+=(this.$refs.inputValue2?this.$refs.inputValue2.innerText:"")+"\n",e+=(this.$refs.inputValue3?this.$refs.inputValue3.innerText:"")+"\n",this.$refs.inputValue4&&this.$refs.inputValue4.length>0&&this.$refs.inputValue4.map((function(t){e+=t.innerText+"\n"})),e+=this.remark,e},searchFreight:function(e){var t=this;if(this.selectRow=e,this.highLight=e.quotationId,this.serviceTypeList){var r,a=Object(l["a"])(this.serviceTypeList);try{for(a.s();!(r=a.n()).done;){var n=r.value;if(n.children){var i,o=Object(l["a"])(n.children);try{for(o.s();!(i=o.n()).done;){var s=i.value;s.serviceTypeId===e.logisticsTypeId&&(this.serviceTypeId=s.serviceTypeId,this.typeId=s.typeId)}}catch(h){o.e(h)}finally{o.f()}}n.serviceTypeId===e.logisticsTypeId&&(this.serviceTypeId=n.serviceTypeId,this.typeId=n.typeId)}}catch(h){a.e(h)}finally{a.f()}}var u=[],d=[];Object(c["c"])(e.quotationId).then((function(e){t.reset(),t.quotation=e.data,t.quotation.midRevenueTonsList=e.midRevenueTonsList,t.quotation.serviceTypeIds=e.serviceTypeIds,e.serviceTypeIds.includes(-1)?d=t.serviceTypeList:t.serviceTypeList.forEach((function(t){e.serviceTypeIds.includes(t.serviceTypeId)&&d.push(t),void 0!=t.children&&t.children.forEach((function(t){e.serviceTypeIds.includes(t.serviceTypeId)&&d.push(t)}))})),t.$refs.serviceType.getLoadOptions(d),t.$refs.serviceType.content=t.serviceTypeId,t.quotation.cargoTypeIds=e.cargoTypeIds,t.quotation.carrierIds=e.carrierIds,t.quotation.loadingIds=e.locationLoadingIds[0]?[e.locationLoadingIds[0]]:[],t.quotation.characteristicsIds=e.quotationCharacteristics,e.quotationFreight.length>0&&e.quotationFreight.forEach((function(e){t.resetQuotationFreight(),e.showCharge=""==e.charge,e.showCurrency=""==e.currency,e.showUnit=""==e.unit,t.quotationFreight=e,t.handleInsertCost(),null!=e.companyId&&u.push(e.companyId)})),t.costList.sort((function(e,t){return e.typeId-t.typeId})).forEach((function(e){return e.freight.sort((function(e,t){return e.chargeTypeOrderNum==t.chargeTypeOrderNum?e.chargeOrderNum-t.chargeOrderNum:e.chargeTypeOrderNum-t.chargeTypeOrderNum}))})),t.getFreightList(),t.getCharacteristicsList(u)}))},getFreightList:function(){var e=this;this.quotation.pageSize=this.freight.pageSize,this.quotation.pageNum=this.freight.pageNum,this.quotation.typeId=this.typeId,this.quotation.serviceTypeId=this.serviceTypeId,Object(c["f"])(this.quotation).then((function(t){e.freightList=t.rows,e.ftotal=t.total}))},getCharacteristicsList:function(e){var t=this;this.characteristics.serviceTypeIds=this.quotation.serviceTypeIds,this.characteristics.locationDepartureIds=null==this.quotation.departureId?[]:[this.quotation.departureId],this.characteristics.locationDestinationIds=null==this.quotation.destinationId?[]:[this.quotation.destinationId],this.characteristics.cargoTypeIds=this.quotation.cargoTypeIds,this.characteristics.carrierIds=this.quotation.carrierIds,Object(c["e"])(this.characteristics).then((function(r){t.characteristicsList=r.rows,t.temCharacteristicsList=r.rows,t.ctotal=r.total,t.selectCharacteristics(e)}))},selectCharacteristics:function(e){var t=this,r=this.temCharacteristicsList,a=this.characteristicsList;this.temCharacteristicsList=r.length==a.length?a.filter((function(r){return e&&e.length>0?null==r.companyId||e.includes(r.companyId):null==t.select.companyId||(null==r.companyId||r.companyId==t.select.companyId)})):r.filter((function(r){return e&&e.length>0?null==r.companyId||e.includes(r.companyId):null==t.select.companyId||(null==r.companyId||r.companyId==t.select.companyId)})),this.$nextTick((function(){t.temCharacteristicsList.forEach((function(e){t.quotation.characteristicsIds.includes(e.characteristicsId)&&t.$refs.characteristics.toggleRowSelection(e,!0)})),t.$forceUpdate()}))},getType:function(e){if(this.serviceTypeId=e.serviceTypeId,this.serviceTypeList){var t,r=Object(l["a"])(this.serviceTypeList);try{for(r.s();!(t=r.n()).done;){var a=t.value;if(a.serviceTypeId==e.serviceTypeId&&(this.typeId=a.typeId),a.children){var n,i=Object(l["a"])(a.children);try{for(i.s();!(n=i.n()).done;){var o=n.value;o.serviceTypeId==e.serviceTypeId&&(this.typeId=o.typeId)}}catch(s){i.e(s)}finally{i.f()}}}}catch(s){r.e(s)}finally{r.f()}}this.getFreightList()},getList:function(){var e=this;return Object(s["a"])(Object(o["a"])().mark((function t(){return Object(o["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.loading=!0,t.next=3,Object(c["d"])(e.queryParams).then((function(t){e.quotationList=t.rows,e.total=t.total?t.total:0,e.loading=!1,e.freightList=[],e.characteristicsList=[]}));case 3:case"end":return t.stop()}}),t)})))()},cancel:function(){this.open=!1,this.edit=!1,this.reset()},reset:function(){this.form={quotationId:null,richNo:null,companyId:null,staffId:this.$store.state.user.sid,extStaffId:null,transportationTermsId:null,logisticsTypeId:null,serviceTypeIds:[],imExPort:null,cargoTypeIds:[],cargoName:null,grossWeight:null,cargoUnitId:5,cargoPrice:null,cargoCurrencyId:10,loadingIds:[],departureId:null,destinationId:null,carrierIds:[],goodsTime:null,remark:null,midRevenueTonsList:[]},this.select=[],this.costList=[],this.carrierIds=[],this.resetForm("form")},resetQuotationFreight:function(){this.quotationFreight={quotationId:null,showCharge:!1,showCurrency:!1,showUnit:!1,typeId:null,freightId:null,localId:null,chargeId:null,strategyId:null,quotationRate:null,quotationCurrencyCode:null,quotationAmount:null,inquiryRate:null,costCurrencyId:null,inquiryAmount:null,unitId:null,unitCode:null,baseCurrencyRate:null,exchangeRate:null,isTaxIncluded:null,taxRate:null,profit:null}},getServiceTypeList:function(){var e=this;0==this.$store.state.data.serviceTypeList.length||this.$store.state.data.redisList.serviceType?f["a"].dispatch("getServiceTypeList").then((function(){return e.serviceTypeList=e.$store.state.data.serviceTypeList})):this.serviceTypeList=this.$store.state.data.serviceTypeList},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.resetForm("queryForm"),this.handleQuery(),this.queryStaffId=null},handleAdd:function(){this.reset(),this.open=!0,this.title="添加报价列表",this.form.imExPort="1",this.handleAddMidRevenueTons()},handleUpdate:function(e){var t=this;this.reset(),this.edit=!0,e.company&&this.getCompany(e.companyId),Object(c["c"])(e.quotationId).then((function(e){t.form=e.data,t.form.midRevenueTonsList=e.midRevenueTonsList,t.form.serviceTypeIds=e.serviceTypeIds,t.form.cargoTypeIds=e.cargoTypeIds,t.form.carrierIds=e.carrierIds,t.form.loadingIds=e.locationLoadingIds?e.locationLoadingIds[0]:[],t.locationOptions=e.locationOptions,t.open=!0,t.title="修改报价列表"}))},submitForm:function(e){var t=this;this.form.loadingIds=[this.form.loadingIds],this.$refs["form"].validate((function(r){r&&(null!=t.form.quotationId&&"true"!=e?Object(c["h"])(t.form).then((function(e){t.$modal.msgSuccess("修改成功"),t.open=!1,t.edit=!1,t.getList()})):(t.form.richNo=null,Object(c["a"])(t.form).then((function(e){t.$modal.msgSuccess("新增成功"),t.open=!1,t.getList()}))))}))},handleDelete:function(e){var t=this,r=e.quotationId;this.$confirm('是否确认删除报价列表编号为"'+r+'"的数据项？',"提示",{customClass:"modal-confirm"}).then((function(){return Object(c["b"])(r)})).then((function(){t.getList(),t.$modal.msgSuccess("删除成功")})).catch((function(){}))},rowIndex:function(e){var t=e.row,r=e.rowIndex;t.index=r+1},handleAddMidRevenueTons:function(){var e={count:1,unitId:null,unitCode:null};this.form.midRevenueTonsList.length<3?this.form.midRevenueTonsList.push(e):this.$message.warning("最多可录入三个不同的柜型")},handleDeleteMidRevenueTons:function(e){this.form.midRevenueTonsList=this.form.midRevenueTonsList.filter((function(t){return t.index!=e.index}))},handleExport:function(){this.download("system/quotation/export",Object(i["a"])({},this.queryParams),"quotation_".concat((new Date).getTime(),".xlsx"))},loadSales:function(){var e=this;0==this.$store.state.data.salesList.length||this.$store.state.data.redisList.salesList?f["a"].dispatch("getSalesList").then((function(){e.belongList=e.$store.state.data.salesList})):this.belongList=this.$store.state.data.salesList},getExchangeRate:function(e,t){var r;Object(A["f"])().then((function(a){if(e){var n,i=Object(l["a"])(a.data);try{for(i.s();!(n=i.n()).done;){var o=n.value;"RMB"==o.basicCurrency&&e.quotationCurrencyCode==o.currency&&Object(D["f"])(o.validFrom)<=Object(D["f"])(e.createTime)&&Object(D["f"])(e.createTime)<=Object(D["f"])(o.validTo)&&(r=o.sellRate/o.base)}}catch(s){i.e(s)}finally{i.f()}t(r)}}))},getExchangeRateDirect:function(e){return Object(s["a"])(Object(o["a"])().mark((function t(){var r,a,n,i,s;return Object(o["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,Object(A["f"])();case 2:if(a=t.sent,e){n=Object(l["a"])(a.data);try{for(n.s();!(i=n.n()).done;)s=i.value,"RMB"==s.basicCurrency&&e.quotationCurrencyCode==s.currency&&Object(D["f"])(s.validFrom)<=Object(D["f"])(e.createTime)&&Object(D["f"])(e.createTime)<=Object(D["f"])(s.validTo)&&(r=s.sellRate/s.base)}catch(o){n.e(o)}finally{n.f()}}return t.abrupt("return",r);case 5:case"end":return t.stop()}}),t)})))()},staffNormalizer:function(e){var t;return e.children&&!e.children.length&&delete e.children,e.staff&&(t=null==e.staff.staffFamilyLocalName&&null==e.staff.staffGivingLocalName?null!=e.role.roleLocalName?e.role.roleLocalName+","+d.a.getFullChars(e.role.roleLocalName):e.dept.deptLocalName+","+d.a.getFullChars(e.dept.deptLocalName):e.staff.staffCode+" "+e.staff.staffFamilyLocalName+e.staff.staffGivingLocalName+" "+e.staff.staffGivingEnName+","+d.a.getFullChars(e.staff.staffFamilyLocalName+e.staff.staffGivingLocalName)),e.roleId?{id:e.roleId,label:t,children:e.children,isDisabled:null==e.staffId&&void 0==e.children}:{id:e.deptId,label:t,children:e.children,isDisabled:null==e.staffId&&void 0==e.children}},loadCarrier:function(){var e=this;0==this.$store.state.data.serviceTypeCarriers.length||this.$store.state.data.redisList.serviceTypeCarriers?f["a"].dispatch("getServiceTypeCarriersList").then((function(){e.carrierList=e.$store.state.data.serviceTypeCarriers})):this.carrierList=this.$store.state.data.serviceTypeCarriers},carrierNormalizer:function(e){var t;return e.children&&!e.children.length&&delete e.children,t=!e.carrier||null==e.carrier.carrierLocalName&&null==e.carrier.carrierEnName?e.serviceLocalName+" "+e.serviceEnName+","+d.a.getFullChars(e.serviceLocalName):(null!=e.carrier.carrierIntlCode?e.carrier.carrierIntlCode:"")+" "+(null!=e.carrier.carrierLocalName?e.carrier.carrierLocalName:"")+" "+(null!=e.carrier.carrierEnName?e.carrier.carrierEnName:"")+","+d.a.getFullChars(null!=e.carrier.carrierLocalName?e.carrier.carrierLocalName:""),{id:e.serviceTypeId,label:t,children:e.children}},handleSelectStaffId:function(e){this.queryParams.staffId=e.staffId,this.handleQuery()},cleanStaffId:function(e){void 0==e&&(this.queryParams.staffId=null,this.handleQuery())},handleSelectQueryCarrierIds:function(e){this.queryParams.carrierIds.push(e.carrier.carrierId),this.handleQuery()},handleDeselectQueryCarrierIds:function(e){this.queryParams.carrierIds=this.queryParams.carrierIds.filter((function(t){return t!=e.carrier.carrierId})),this.handleQuery()},deselectAllQueryCarrierIds:function(e){0==e.length&&(this.queryParams.carrierIds=[],this.handleQuery())},queryCompany:function(e){this.queryParams.companyId=e.companyId,this.handleQuery()},queryLogisticsType:function(e){this.queryParams.logisticsTypeId=e.serviceTypeId,this.handleQuery()},queryServiceType:function(e){this.queryParams.serviceTypeId=e.serviceTypeId,this.handleQuery()},queryCargoTypeIds:function(e){this.queryParams.cargoTypeIds=e,this.handleQuery()},queryDepartureId:function(e){this.queryParams.departureId=e,this.handleQuery()},queryDestinationId:function(e){this.queryParams.destinationId=e,this.handleQuery()},queryUnitId:function(e){this.queryParams.unitCode=e,this.handleQuery()},getCompany:function(e){var t=this;this.form.companyId=e,void 0!=e&&Object(m["e"])({sqdCompanyId:e}).then((function(e){t.extStaffList=e.data}))},getTransportationTerms:function(e){this.form.transportationTermsCode=e},getLogisticsType:function(e){this.form.logisticsTypeId=e},getServiceType:function(e){this.form.serviceTypeIds=e},requireServiceType:function(e){this.requires.serviceTypeIds=e},getLoading:function(e){this.form.loadingIds=e},getDeparture:function(e){this.form.departureId=e},getDestination:function(e){this.form.destinationId=e},handleSelectCarrierIds:function(e){this.form.carrierIds.push(e.carrier.carrierId)},handleDeselectCarrierIds:function(e){this.form.carrierIds=this.form.carrierIds.filter((function(t){return t!=e.carrier.carrierId}))},deselectAllCarrierIds:function(e){0==e.length&&(this.form.carrierIds=[])},getCargoTypeIds:function(e){this.form.cargoTypeIds=e},getCargoCurrencyId:function(e){this.form.cargoCurrencyCode=e},getCurrencyId:function(e,t){var r=this;t.quotationCurrencyCode=e.currencyCode,t.quotationCurrency=e.currencyCode,this.getExchangeRate(t,(function(e){null==e?r.$message.error("不存在当天的汇率,请通知财务更新汇率"):t.exchangeRate=e}))},getUnitId:function(e,t){t.unit=e.unitCode},getCargoUnitId:function(e){this.form.cargoUnitCode=e},getLocalList:function(e){var t=e.locals;if(null==t||0==t.length)return new Promise((function(t,r){Object(c["g"])(e).then((function(r){e.locals=r.data,t(e)}))}))},handleInsertCost:function(){var e=this,t=!1;if(this.costList.length>0){var r,a=Object(l["a"])(this.costList);try{var n=function(){var a=r.value;if(a.typeId==e.quotationFreight.typeId&&e.quotationFreight.freightId==a.freightId){t=!0,e.quotationFreight.id=Math.random();var n=!1;a.freight.forEach((function(t){t.chargeId==e.quotationFreight.chargeId&&t.unitCode==e.quotationFreight.unitCode&&(n=!0)})),n||a.freight.push(e.quotationFreight)}};for(a.s();!(r=a.n()).done;)n()}catch(i){a.e(i)}finally{a.f()}}t&&0!=this.costList.length||this.insertCost()},insertCost:function(){var e;switch(null!=this.quotationFreight.typeId?this.quotationFreight.typeId.toString():""){case"1":e="海运";break;case"2":e="空运";break;case"3":e="铁路";break;case"4":e="快递";break;case"5":e="拖车";break;case"6":e="报关";break;case"7":e="清关";break;case"8":e="仓储";break;case"9":e="拓展";break;default:break}var t={title:e,id:Math.random(),typeId:this.quotationFreight.typeId,freightId:this.quotationFreight.freightId,freight:[]};this.quotationFreight.id=Math.random(),t.freight.push(this.quotationFreight),this.costList.push(t)},handleSelect:function(e){var t=this;return Object(s["a"])(Object(o["a"])().mark((function r(){var a,n,i,s,c,u;return Object(o["a"])().wrap((function(r){while(1)switch(r.prev=r.next){case 0:return r.next=2,t.getLocalList(t.select);case 2:if(!(t.quotation.midRevenueTonsList&&t.quotation.midRevenueTonsList.length>0)){r.next=40;break}a=Object(l["a"])(t.quotation.midRevenueTonsList),r.prev=4,a.s();case 6:if((n=a.n()).done){r.next=32;break}if(i=n.value,i.unit!=e){r.next=30;break}return r.next=11,t.addQuotationFreight(i,t.select,t.select.freightId);case 11:s=Object(l["a"])(t.select.locals),r.prev=12,s.s();case 14:if((c=s.n()).done){r.next=20;break}return u=c.value,r.next=18,t.addQuotationFreight(i,u,t.select.freightId);case 18:r.next=14;break;case 20:r.next=25;break;case 22:r.prev=22,r.t0=r["catch"](12),s.e(r.t0);case 25:return r.prev=25,s.f(),r.finish(25);case 28:return t.costList.sort((function(e,t){return e.typeId-t.typeId})).forEach((function(e){return e.freight.sort((function(e,t){return e.chargeTypeOrderNum==t.chargeTypeOrderNum?e.chargeOrderNum-t.chargeOrderNum:e.chargeTypeOrderNum-t.chargeTypeOrderNum}))})),r.abrupt("break",32);case 30:r.next=6;break;case 32:r.next=37;break;case 34:r.prev=34,r.t1=r["catch"](4),a.e(r.t1);case 37:return r.prev=37,a.f(),r.finish(37);case 40:t.selectCharacteristics();case 41:case"end":return r.stop()}}),r,null,[[4,34,37,40],[12,22,25,28]])})))()},saveQuotation:function(){var e=this,t=[];this.costList.forEach((function(r){r.freight.forEach((function(r){r.quotationId=e.quotation.quotationId,t.push(r)}))}));var r={quotationId:this.quotation.quotationId,rsQuotationFreightList:t,characteristicsIds:this.temSelectCharacteristicsList.map((function(e){return e.characteristicsId}))};Object(c["j"])({quotationId:this.quotation.quotationId,quotationSketch:this.getRemark()}),Object(c["i"])(r).then((function(t){e.$message.success("成功处理"+t.data+"条数据")}))},handleDeleteCost:function(e,t){var r=e.freight.filter((function(e){return e.id!=t.id}));0==r.length&&(this.costList=this.costList.filter((function(t){return t.id!=e.id}))),r.length>0&&(e.freight=r)},handleAddLocal:function(e){this.resetQuotationFreight(),this.quotationFreight.id=Math.random(),this.quotationFreight.typeId=e.typeId,this.quotationFreight.showCharge=!0,this.quotationFreight.showCurrency=!0,this.quotationFreight.showUnit=!0,e.freight.push(this.quotationFreight)},getChargeId:function(e,t,r){var a=this;t.chargeId=e.chargeId,t.charge=e.chargeLocalName,t.chargeEn=e.chargeEnName,t.quotationCurrency=null,t.quotationCurrencyCode=null,t.quotationRate=null,t.quotationAmount=null,t.exchangeRate=null,t.taxRate=null,t.unit=null,t.unitCode=null,t.costCurrency=null,t.costCurrencyId=null,t.inquiryRate=null,t.inquiryAmount=null,t.profit=null,this.getLocalList({freightId:r,query:!0}).then(function(){var n=Object(s["a"])(Object(o["a"])().mark((function n(i){var s,c,u;return Object(o["a"])().wrap((function(n){while(1)switch(n.prev=n.next){case 0:s=Object(l["a"])(i.locals),n.prev=1,s.s();case 3:if((c=s.n()).done){n.next=23;break}if(u=c.value,u.chargeId!=e.chargeId){n.next=21;break}return n.next=8,a.addQuotationFreight(null,u,r,!0);case 8:t.quotationCurrency=a.quotationFreight.quotationCurrency,t.quotationCurrencyCode=a.quotationFreight.quotationCurrencyCode,t.quotationRate=a.quotationFreight.quotationRate,t.quotationAmount=a.quotationFreight.quotationAmount,t.exchangeRate=a.quotationFreight.exchangeRate,t.taxRate=a.quotationFreight.taxRate,t.unit=a.quotationFreight.unit,t.unitCode=a.quotationFreight.unitCode,t.costCurrency=a.quotationFreight.costCurrency,t.costCurrencyId=a.quotationFreight.costCurrencyId,t.inquiryRate=a.quotationFreight.inquiryRate,t.inquiryAmount=a.quotationFreight.inquiryAmount,t.profit=a.quotationFreight.profit;case 21:n.next=3;break;case 23:n.next=28;break;case 25:n.prev=25,n.t0=n["catch"](1),s.e(n.t0);case 28:return n.prev=28,s.f(),n.finish(28);case 31:case"end":return n.stop()}}),n,null,[[1,25,28,31]])})));return function(e){return n.apply(this,arguments)}}()),this.$forceUpdate()},addQuotationFreight:function(e,t,r,a){var n=this;return Object(s["a"])(Object(o["a"])().mark((function i(){var s;return Object(o["a"])().wrap((function(i){while(1)switch(i.prev=i.next){case 0:return n.resetQuotationFreight(),n.quotationFreight.chargeOrderNum=t.chargeOrderNum,n.quotationFreight.chargeTypeOrderNum=t.chargeTypeOrderNum,n.quotationFreight.typeId=n.typeId,n.quotationFreight.serviceTypeId=null!==t.serviceTypeId?t.serviceTypeId:t.logisticsTypeId,n.quotationFreight.revenueTonsId=null!=e?e.revenueTonsId:null,n.quotationFreight.chargeId=t.chargeId,"BL"==t.unit?(n.quotationFreight.inquiryAmount=1,n.quotationFreight.quotationAmount=1):(n.quotationFreight.inquiryAmount=null!=e?e.count:1,n.quotationFreight.quotationAmount=null!=e?e.count:1),n.quotationFreight.unit="Ctnr"!=t.unit?t.unitCode:null!=e?e.unit:"",n.quotationFreight.unitCode="Ctnr"!=t.unit?t.unitCode:null!=e?e.unit:null,null!=e&&("20GP"===e.unit?n.quotationFreight.inquiryRate=t.priceB:"40GP"===e.unit?n.quotationFreight.inquiryRate=t.priceC:"40HQ"===e.unit&&(n.quotationFreight.inquiryRate=t.priceD)),null==n.quotationFreight.inquiryRate&&(n.quotationFreight.inquiryRate=t.priceA),n.quotationFreight.costCurrencyCode=t.currency?t.currency:t.currencyCode,n.quotationFreight.costCurrency=t.currency?t.currency:t.currencyCode,n.quotationFreight.quotationRate=n.quotationFreight.inquiryRate,null!=t.charge&&t.charge.includes("/")?n.quotationFreight.charge=t.charge.split("/")[1]:n.quotationFreight.charge=t.charge,n.quotationFreight.chargeEn=t.chargeEn,n.quotationFreight.freightId=r,n.quotationFreight.localChargeId=t.localChargeId,n.quotationFreight.profit=0,n.quotationFreight.taxRate=0,n.quotationFreight.quotationCurrencyCode=t.currency?t.currency:t.currencyCode,n.quotationFreight.quotationCurrency=t.currency?t.currency:t.currencyCode,n.quotationFreight.createTime=Object(D["f"])(new Date),n.quotationFreight.createBy=n.$store.state.user.sid,n.quotationFreight.company=t.company,n.quotationFreight.richNo=t.richNo,i.next=29,n.getExchangeRateDirect(n.quotationFreight);case 29:s=i.sent,n.quotationFreight.exchangeRate=null==s?1:s,null!=n.quotationFreight.inquiryRate&&n.quotationFreight.chargeTypeOrderNum<4&&!a&&n.handleInsertCost();case 32:case"end":return i.stop()}}),i)})))()},handleDeleteFreight:function(e){this.costList=this.costList.filter((function(t){return t.id!=e})),this.selectCharacteristics()},strategy:function(e){1==e.strategyId&&(e.quotationRate=0),this.countProfit(e)},countProfit:function(e){e.profit=(Number(e.quotationRate)*Number(e.quotationAmount)-Number(e.inquiryRate)*Number(e.inquiryAmount)).toFixed(2)},getTotalPrice:function(e){var t=this,r=e.columns,a=e.data,n=[];return r.forEach((function(e,r){if(0===r&&(n[r]="合计"),1===r){var i=0,o=0,s=0,l=0,c=0,u=0,d=0;a.forEach((function(e){e.freight.forEach((function(e){d+=Number(e.quotationRate)*Number(e.exchangeRate),i+=Number(e.quotationRate)*Number(e.exchangeRate)*Number(e.quotationAmount),o+=Number(e.quotationRate)*Number(e.exchangeRate)*Number(e.quotationAmount)*(1+Number(e.taxRate)/100),s+=Number(e.inquiryRate)*Number(e.exchangeRate)*Number(e.inquiryAmount),l+=Number(e.inquiryRate)*Number(e.exchangeRate)*Number(e.inquiryAmount)*(1+Number(e.taxRate)/100),c+=Number(e.profit)*Number(e.exchangeRate)*Number(e.quotationAmount),u+=Number(e.profit)*(1+Number(e.taxRate)/100)*Number(e.exchangeRate)*Number(e.quotationAmount)}))})),t.quotationTotalPrice=d,t.quotationAmountTotal=i,n[r]="　　　　　　　　　　　　报价：￥"+i.toFixed(2)+"/含税：￥"+o.toFixed(2)+"　　　　　　　　　　　　　　　　　　　　成本：￥"+s.toFixed(2)+"/含税：￥"+l.toFixed(2)+"　　　利润：￥"+c.toFixed(2)+"/含税：￥"+u.toFixed(2)}})),n},confirmRequire:function(){var e=this;console.log(this.quotation.typeId),this.quotation!={}&&(this.quotation.serviceTypeId=this.serviceTypeId,this.quotation.logisticsTypeId=this.selectRow.logisticsTypeId,this.quotation.isSalesRequired=1,this.quotation.isReplied=0,this.quotation.requireSalesId=this.$store.state.user.sid,"1"===this.quotation.typeId&&(this.quotation.chargeId=1),"2"===this.quotation.typeId&&(this.quotation.chargeId=7),"3"===this.quotation.typeId&&(this.quotation.chargeId=52),"4"===this.quotation.typeId&&(this.quotation.chargeId=43),"5"===this.quotation.typeId&&(this.quotation.chargeId=8),"6"===this.quotation.typeId&&(this.quotation.chargeId=42),"7"===this.quotation.typeId&&(this.quotation.chargeId=53),"8"===this.quotation.typeId&&(this.quotation.chargeId=46),"9"===this.quotation.typeId&&(this.quotation.chargeId=51),"4"!==this.quotation.typeId&&"5"!==this.quotation.typeId&&"8"!==this.quotation.typeId&&"9"!==this.quotation.typeId||(this.quotation.precarriageRegionId=this.selectRow.loadingId),this.quotation.polId=this.quotation.departureId,this.quotation.destinationPortId=this.quotation.destinationId,null!=this.quotation.departureId&&null!=this.quotation.destinationId&&Object(E["a"])(this.quotation).then((function(t){e.$message.success(t.msg)})),null==this.quotation.departureId?this.$message.warning("启运港为空"):null==this.quotation.destinationId&&this.$message.warning("目的港为空"))},handleBooking:function(e){this.$tab.openPage("订舱单","/salesquotation/bookingDetail",{id:e.quotationId,booking:!0})},replaceSelect:function(e){var t=this;return Object(s["a"])(Object(o["a"])().mark((function r(){return Object(o["a"])().wrap((function(r){while(1)switch(r.prev=r.next){case 0:return r.next=2,t.getLocalList(t.select);case 2:t.costList.forEach((function(r){r.typeId==t.typeId&&r.freight.forEach((function(r){var a;r.chargeId==t.select.chargeId&&r.unitCode==e&&("Ctnr"==t.select.unitCode?"20GP"===r.unit?r.inquiryRate=t.select.priceB:"40GP"===r.unit?r.inquiryRate=t.select.priceC:"40HQ"===r.unit&&(r.inquiryRate=t.select.priceD):r.inquiryRate=t.select.priceA,r.costCurrencyCode=t.select.currencyCode,r.costCurrency=t.select.currency,r.createTime=Object(D["f"])(new Date),r.createBy=t.$store.state.user.sid,r.company=t.select.company,r.richNo=t.select.richNo,t.countProfit(r),t.getExchangeRate(r,(function(e){a=e})),r.exchangeRate=null==a?1:a);t.select.locals.forEach((function(a){var n;r.chargeId==a.chargeId&&e==a.unitCode&&("Ctnr"==a.unitCode?"20GP"===r.unit?r.inquiryRate=a.priceB:"40GP"===r.unit?r.inquiryRate=a.priceC:"40HQ"===r.unit&&(r.inquiryRate=a.priceD):r.inquiryRate=a.priceA,r.costCurrencyCode=a.currencyCode,r.costCurrency=a.currency,r.createTime=Object(D["f"])(new Date),r.createBy=t.$store.state.user.sid,r.company=a.company,r.richNo=a.richNo,t.countProfit(r),t.getExchangeRate(r,(function(e){n=e})),r.exchangeRate=null==n?1:n)}))}))})),t.selectCharacteristics();case 4:case"end":return r.stop()}}),r)})))()},handleSelectionChange:function(e){this.temSelectCharacteristicsList=e},tableRowClassName:function(e){var t=e.row;if(t.quotationId==this.highLight)return"valid-before";var r=Object(D["f"])(new Date,"{y}-{m}-{d}"),a=Object(D["f"])(t.validFrom,"{y}-{m}-{d}"),n=Object(D["f"])(t.validTo,"{y}-{m}-{d}");return a<r<n?"":n<r?"valid-row":a>r?"valid-before":""},handleSelectCompany:function(e){this.form.companyId=e,this.getCompany(e)},loadCompany:function(){var e=this;Object(Q["h"])({roleClient:"1",permissionLevel:this.$store.state.user.permissionLevelList.C}).then((function(t){e.companyList=t.rows}))}}},B=V,H=r("2877"),J=Object(H["a"])(B,a,n,!1,null,null,null);t["default"]=J.exports},5768:function(e,t,r){},"86ea":function(e,t,r){"use strict";r.r(t);var a=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",[r("el-tooltip",{attrs:{disabled:null==e.scope.row.company||e.scope.row.company.length<3||((null!=e.scope.row.contractType?e.scope.row.contractType:"")+(null!=e.scope.row.contractType&&null!=e.scope.row.contractNo?"：":"")+(null!=e.scope.row.contractNo?e.scope.row.contractNo:"")).length<10,placement:"top"}},[r("div",{attrs:{slot:"content"},slot:"content"},[r("div",{staticStyle:{margin:"0","font-size":"15px","font-weight":"bold"}},[e._v(e._s(e.scope.row.carrierCode)+" "),r("span",{staticClass:"unHighlight-text",staticStyle:{margin:"0","font-weight":"bold","font-size":"12px"}},[e._v(" "+e._s("("+(null!=e.scope.row.contractType?e.scope.row.contractType:"")+")")+" ")])])]),r("div",{staticStyle:{margin:"0",overflow:"hidden","text-overflow":"ellipsis","white-space":"nowrap"}},[r("div",{staticStyle:{margin:"0","font-size":"15px","font-weight":"bold"}},[e._v(e._s(e.scope.row.carrierCode)+" "),r("span",{staticClass:"unHighlight-text",staticStyle:{margin:"0","font-weight":"bold","font-size":"12px"}},[e._v(" "+e._s("("+(null!=e.scope.row.contractType?e.scope.row.contractType:"")+")")+" ")])])])])],1)},n=[],i={name:"carrierNoSupplier",props:["scope"],data:function(){return{size:this.$store.state.app.size||"mini"}}},o=i,s=(r("efc0"),r("2877")),l=Object(s["a"])(o,a,n,!1,null,"36cb6b05",null);t["default"]=l.exports},a159:function(e,t,r){"use strict";r.d(t,"e",(function(){return n})),r.d(t,"d",(function(){return i})),r.d(t,"a",(function(){return o})),r.d(t,"g",(function(){return s})),r.d(t,"c",(function(){return l})),r.d(t,"b",(function(){return c})),r.d(t,"f",(function(){return u}));var a=r("b775");function n(e){return Object(a["a"])({url:"/monitor/job/list",method:"get",params:e})}function i(e){return Object(a["a"])({url:"/monitor/job/"+e,method:"get"})}function o(e){return Object(a["a"])({url:"/monitor/job",method:"post",data:e})}function s(e){return Object(a["a"])({url:"/monitor/job",method:"put",data:e})}function l(e){return Object(a["a"])({url:"/monitor/job/"+e,method:"delete"})}function c(e,t){var r={jobId:e,status:t};return Object(a["a"])({url:"/monitor/job/changeStatus",method:"put",data:r})}function u(e,t){var r={jobId:e,jobGroup:t};return Object(a["a"])({url:"/monitor/job/run",method:"put",data:r})}},b857:function(e,t,r){"use strict";r.d(t,"e",(function(){return n})),r.d(t,"d",(function(){return i})),r.d(t,"a",(function(){return o})),r.d(t,"f",(function(){return s})),r.d(t,"c",(function(){return l})),r.d(t,"b",(function(){return c}));var a=r("b775");function n(e){return Object(a["a"])({url:"/system/extStaff/list",method:"get",params:e})}function i(e){return Object(a["a"])({url:"/system/extStaff/"+e,method:"get"})}function o(e){return Object(a["a"])({url:"/system/extStaff",method:"post",data:e})}function s(e){return Object(a["a"])({url:"/system/extStaff",method:"put",data:e})}function l(e){return Object(a["a"])({url:"/system/extStaff/"+e,method:"delete"})}function c(e,t){var r={staffId:e,staffJobStatus:t};return Object(a["a"])({url:"/system/extStaff",method:"put",data:r})}},efc0:function(e,t,r){"use strict";r("5768")}}]);