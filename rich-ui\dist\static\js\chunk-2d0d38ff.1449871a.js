(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d0d38ff"],{"5cfa":function(e,t,r){"use strict";r.r(t);var a=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",{staticClass:"app-container"},[r("el-row",{attrs:{gutter:20}},[r("el-col",{attrs:{span:e.showLeft}},[r("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],ref:"queryForm",staticClass:"query",attrs:{inline:!0,model:e.queryParams,size:"mini"}},[r("el-form-item",{attrs:{label:"搜索",prop:"deptQuery"}},[r("el-input",{staticStyle:{width:"158px"},attrs:{clearable:"",placeholder:"部门名称"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.deptQuery,callback:function(t){e.$set(e.queryParams,"deptQuery",t)},expression:"queryParams.deptQuery"}})],1),r("el-form-item",[r("el-button",{attrs:{icon:"el-icon-search",size:"mini",type:"primary"},on:{click:e.handleQuery}},[e._v("搜索")]),r("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1)],1),r("el-col",{attrs:{span:e.showRight}},[r("el-row",{staticClass:"mb8",attrs:{gutter:10}},[r("el-col",{attrs:{span:1.5}},[r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:dept:add"],expression:"['system:dept:add']"}],attrs:{icon:"el-icon-plus",plain:"",size:"mini",type:"primary"},on:{click:e.handleAdd}},[e._v("新增 ")])],1),r("el-col",{attrs:{span:1.5}},[r("el-button",{attrs:{icon:"el-icon-sort",plain:"",size:"mini",type:"info"},on:{click:e.toggleExpandAll}},[e._v("展开/折叠 ")])],1),r("right-toolbar",{attrs:{showSearch:e.showSearch},on:{"update:showSearch":function(t){e.showSearch=t},"update:show-search":function(t){e.showSearch=t},queryTable:e.getList}})],1),e.refreshTable?r("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.deptList,"default-expand-all":e.isExpandAll,"tree-props":{children:"children",hasChildren:"hasChildren"},"row-key":"deptId"}},[r("el-table-column",{attrs:{label:"部门名称",width:"350"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(t.row.deptShortName)+" "),r("a",{staticStyle:{margin:"0","font-weight":"bold","font-size":"small"}},[e._v(e._s(t.row.deptLocalName))]),e._v(" "+e._s(t.row.deptEnName)+" ")]}}],null,!1,3609239727)}),r("el-table-column",{attrs:{label:"备注",prop:"remark"}}),r("el-table-column",{attrs:{align:"center",label:"排序",prop:"orderNum",width:"48"}}),r("el-table-column",{attrs:{align:"center",label:"状态",prop:"status",width:"58"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("dict-tag",{attrs:{options:e.dict.type.sys_normal_disable,value:t.row.status}})]}}],null,!1,2802338569)}),r("el-table-column",{attrs:{align:"center","class-name":"small-padding fixed-width",label:"操作",width:"170"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:dept:add"],expression:"['system:dept:add']"}],staticStyle:{"margin-right":"-8px"},attrs:{icon:"el-icon-plus",size:"mini",type:"primary"},on:{click:function(r){return e.handleAdd(t.row)}}},[e._v("新增 ")]),r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:dept:edit"],expression:"['system:dept:edit']"}],staticStyle:{"margin-right":"-8px"},attrs:{icon:"el-icon-edit",size:"mini",type:"success"},on:{click:function(r){return e.handleUpdate(t.row)}}},[e._v("修改 ")]),0!=t.row.parentId?r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:dept:remove"],expression:"['system:dept:remove']"}],staticStyle:{"margin-right":"-8px"},attrs:{icon:"el-icon-delete",size:"mini",type:"danger"},on:{click:function(r){return e.handleDelete(t.row)}}},[e._v("删除 ")]):e._e()]}}],null,!1,1981734694)})],1):e._e()],1)],1),r("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"},{name:"dialogDragWidth",rawName:"v-dialogDragWidth"}],attrs:{"close-on-click-modal":!1,"modal-append-to-body":!1,title:e.title,visible:e.open,"append-to-body":"",width:"600px"},on:{"update:visible":function(t){e.open=t}}},[r("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"80px"}},[r("el-row",[0!=e.form.parentId?r("el-col",{attrs:{span:24}},[0!=e.form.parentId?r("el-form-item",{attrs:{label:"上级部门",prop:"parentId"}},[r("tree-select",{attrs:{multiple:!1,pass:e.form.parentId,type:"dept"},on:{return:e.getParentId}})],1):e._e()],1):e._e()],1),r("el-row",[r("el-col",{attrs:{span:14}},[r("el-form-item",{attrs:{label:"部门名称",prop:"deptLocalName"}},[r("el-input",{attrs:{placeholder:"部门名称"},model:{value:e.form.deptLocalName,callback:function(t){e.$set(e.form,"deptLocalName",t)},expression:"form.deptLocalName"}})],1)],1),r("el-col",{attrs:{span:10}},[r("el-form-item",{attrs:{label:"显示排序",prop:"orderNum"}},[r("el-input-number",{attrs:{controls:!1,min:0,"controls-position":"right"},model:{value:e.form.orderNum,callback:function(t){e.$set(e.form,"orderNum",t)},expression:"form.orderNum"}})],1)],1)],1),r("el-row",[r("el-col",{attrs:{span:12}},[r("el-form-item",{attrs:{label:"英文名",prop:"deptEnName"}},[r("el-input",{attrs:{placeholder:"部门英文名"},model:{value:e.form.deptEnName,callback:function(t){e.$set(e.form,"deptEnName",t)},expression:"form.deptEnName"}})],1)],1),r("el-col",{attrs:{span:12}},[r("el-form-item",{attrs:{label:"简称",prop:"deptShortName"}},[r("el-input",{attrs:{placeholder:"部门简称"},model:{value:e.form.deptShortName,callback:function(t){e.$set(e.form,"deptShortName",t)},expression:"form.deptShortName"}})],1)],1)],1),r("el-form-item",{attrs:{label:"备注",prop:"remark"}},[r("el-input",{attrs:{autosize:{minRows:10,maxRows:20},maxlength:"150",placeholder:"备注","show-word-limit":"",type:"textarea"},model:{value:e.form.remark,callback:function(t){e.$set(e.form,"remark",t)},expression:"form.remark"}})],1),r("el-row",[r("el-col",{attrs:{span:12}},[r("el-form-item",{attrs:{label:"部门状态"}},[r("el-radio-group",{model:{value:e.form.status,callback:function(t){e.$set(e.form,"status",t)},expression:"form.status"}},e._l(e.dict.type.sys_normal_disable,(function(t){return r("el-radio",{key:t.value,attrs:{label:t.value}},[e._v(e._s(t.label)+" ")])})),1)],1)],1)],1)],1),r("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[r("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("确 定")]),r("el-button",{on:{click:e.cancel}},[e._v("取 消")])],1)],1)],1)},o=[],l=r("fcb7"),s={name:"Dept",dicts:["sys_normal_disable"],data:function(){return{showLeft:3,showRight:21,loading:!0,showSearch:!1,deptList:[],title:"",open:!1,isExpandAll:!0,refreshTable:!0,queryParams:{deptQuery:null},form:{},rules:{parentId:[{required:!0,trigger:"blur"}],deptLocalName:[{required:!0,trigger:"blur"}],orderNum:[{required:!0,trigger:"blur"}],email:[{type:"email",message:"正确的邮箱地址",trigger:["blur","change"]}],phone:[{pattern:/^1[3|4|5|6|7|8|9][0-9]\d{8}$/,message:"正确的手机号码",trigger:"blur"}]}}},watch:{showSearch:function(e){1==e?(this.showRight=21,this.showLeft=3):(this.showRight=24,this.showLeft=0)}},created:function(){this.getList()},methods:{getList:function(){var e=this;this.loading=!0,Object(l["d"])(this.queryParams).then((function(t){e.deptList=e.handleTree(t.data,"deptId"),e.loading=!1}))},getParentId:function(e){this.form.parentId=e},cancel:function(){this.open=!1,this.reset()},reset:function(){this.form={deptId:void 0,parentId:void 0,deptLocalName:void 0,orderNum:void 0,leaderName:void 0,phone:void 0,email:void 0,status:"0"},this.resetForm("form")},handleQuery:function(){this.getList()},resetQuery:function(){this.resetForm("queryForm"),this.handleQuery()},handleAdd:function(e){this.reset(),void 0!=e&&(this.form.parentId=e.deptId),this.open=!0,this.title="添加部门"},toggleExpandAll:function(){var e=this;this.refreshTable=!1,this.isExpandAll=!this.isExpandAll,this.$nextTick((function(){e.refreshTable=!0}))},handleUpdate:function(e){var t=this;this.reset(),Object(l["c"])(e.deptId).then((function(e){t.form=e.data,t.open=!0,t.title="修改部门"})),Object(l["e"])(e.deptId).then((function(e){t.deptOptions=t.handleTree(e.data,"deptId")}))},submitForm:function(){var e=this;this.$refs["form"].validate((function(t){t&&(void 0!=e.form.deptId?Object(l["g"])(e.form).then((function(t){e.$modal.msgSuccess("修改成功"),e.open=!1,e.getList()})):Object(l["a"])(e.form).then((function(t){e.$modal.msgSuccess("新增成功"),e.open=!1,e.getList()})))}))},handleDelete:function(e){var t=this;this.$confirm('是否确认删除名称为"'+e.deptLocalName+'"的数据项？',"提示",{customClass:"modal-confirm"}).then((function(){return Object(l["b"])(e.deptId)})).then((function(){t.getList(),t.$modal.msgSuccess("删除成功")})).catch((function(){}))}}},i=s,n=r("2877"),d=Object(n["a"])(i,a,o,!1,null,null,null);t["default"]=d.exports}}]);