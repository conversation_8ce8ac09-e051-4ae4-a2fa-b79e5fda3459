(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d0e8fb1"],{"8c1a":function(e,t,a){"use strict";a.r(t);var o=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:e.showLeft}},[a("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],ref:"queryForm",staticClass:"query",attrs:{inline:!0,model:e.queryParams,"label-width":"68px",size:"mini"}},[a("el-form-item",{attrs:{label:"中文名",prop:"docTypeLocalName"}},[a("el-input",{attrs:{clearable:"",placeholder:"中文名"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.docTypeLocalName,callback:function(t){e.$set(e.queryParams,"docTypeLocalName",t)},expression:"queryParams.docTypeLocalName"}})],1),a("el-form-item",{attrs:{label:"英文名",prop:"docTypeEnName"}},[a("el-input",{attrs:{clearable:"",placeholder:"英文名"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.docTypeEnName,callback:function(t){e.$set(e.queryParams,"docTypeEnName",t)},expression:"queryParams.docTypeEnName"}})],1),a("el-form-item",{attrs:{label:"所属服务",prop:"belongsServiceId"}},[a("tree-select",{staticClass:"sss",attrs:{flat:!1,multiple:!1,pass:e.form.belongsServiceId,type:"serviceType"},on:{return:function(t){e.form.belongsServiceId=t}}})],1),a("el-form-item",[a("el-button",{attrs:{icon:"el-icon-search",size:"mini",type:"primary"},on:{click:e.handleQuery}},[e._v("搜索")]),a("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1)],1),a("el-col",{attrs:{span:e.showRight}},[a("el-row",{staticClass:"mb8",attrs:{gutter:10}},[a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:doctype:add"],expression:"['system:doctype:add']"}],attrs:{icon:"el-icon-plus",plain:"",size:"mini",type:"primary"},on:{click:e.handleAdd}},[e._v("新增 ")])],1),a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:doctype:edit"],expression:"['system:doctype:edit']"}],attrs:{disabled:e.single,icon:"el-icon-edit",plain:"",size:"mini",type:"success"},on:{click:e.handleUpdate}},[e._v("修改 ")])],1),a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:doctype:remove"],expression:"['system:doctype:remove']"}],attrs:{disabled:e.multiple,icon:"el-icon-delete",plain:"",size:"mini",type:"danger"},on:{click:e.handleDelete}},[e._v("删除 ")])],1),a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:doctype:export"],expression:"['system:doctype:export']"}],attrs:{icon:"el-icon-download",plain:"",size:"mini",type:"warning"},on:{click:e.handleExport}},[e._v("导出 ")])],1),a("right-toolbar",{attrs:{showSearch:e.showSearch},on:{"update:showSearch":function(t){e.showSearch=t},"update:show-search":function(t){e.showSearch=t},queryTable:e.getList}})],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.doctypeList},on:{"selection-change":e.handleSelectionChange}},[a("el-table-column",{attrs:{align:"center",type:"selection",width:"28"}}),a("el-table-column",{attrs:{align:"center",label:"文件类型",prop:"docTypeCode"}}),a("el-table-column",{attrs:{align:"center",label:"中文名",prop:"docTypeLocalName"}}),a("el-table-column",{attrs:{align:"center",label:"英文名",prop:"docTypeEnName"}}),a("el-table-column",{attrs:{align:"center",label:"所属服务",prop:"serviceName"}}),a("el-table-column",{attrs:{align:"center",label:"排序",prop:"orderNum"}}),a("el-table-column",{attrs:{align:"center",label:"状态",prop:"status"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-switch",{attrs:{"active-value":"0","inactive-value":"1"},on:{change:function(a){return e.handleStatusChange(t.row)}},model:{value:t.row.status,callback:function(a){e.$set(t.row,"status",a)},expression:"scope.row.status"}})]}}])}),a("el-table-column",{attrs:{align:"left",label:"备注",prop:"remark"}}),a("el-table-column",{attrs:{align:"center","class-name":"small-padding fixed-width",label:"操作"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:doctype:edit"],expression:"['system:doctype:edit']"}],staticStyle:{"margin-right":"-8px"},attrs:{icon:"el-icon-edit",size:"mini",type:"success"},on:{click:function(a){return e.handleUpdate(t.row)}}},[e._v("修改 ")]),a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:doctype:remove"],expression:"['system:doctype:remove']"}],staticStyle:{"margin-right":"-8px"},attrs:{icon:"el-icon-delete",size:"mini",type:"danger"},on:{click:function(a){return e.handleDelete(t.row)}}},[e._v("删除 ")])]}}])})],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total>0"}],attrs:{limit:e.queryParams.pageSize,page:e.queryParams.pageNum,total:e.total},on:{"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},pagination:e.getList}})],1)],1),a("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"},{name:"dialogDragWidth",rawName:"v-dialogDragWidth"}],attrs:{"close-on-click-modal":!1,"modal-append-to-body":!1,title:e.title,visible:e.open,"append-to-body":"",width:"500px"},on:{"update:visible":function(t){e.open=t}}},[a("el-form",{ref:"form",staticClass:"edit",attrs:{model:e.form,rules:e.rules,"label-width":"80px"}},[a("el-form-item",{attrs:{label:"文件类型",prop:"docTypeLocalName"}},[a("el-input",{attrs:{placeholder:"文件类型代码"},model:{value:e.form.docTypeCode,callback:function(t){e.$set(e.form,"docTypeCode",t)},expression:"form.docTypeCode"}})],1),a("el-form-item",{attrs:{label:"中文名",prop:"docTypeLocalName"}},[a("el-input",{attrs:{placeholder:"中文名"},model:{value:e.form.docTypeLocalName,callback:function(t){e.$set(e.form,"docTypeLocalName",t)},expression:"form.docTypeLocalName"}})],1),a("el-form-item",{attrs:{label:"英文名",prop:"docTypeEnName"}},[a("el-input",{attrs:{placeholder:"英文名"},model:{value:e.form.docTypeEnName,callback:function(t){e.$set(e.form,"docTypeEnName",t)},expression:"form.docTypeEnName"}})],1),a("el-form-item",{attrs:{label:"所属服务",prop:"belongsServiceId"}},[a("tree-select",{staticClass:"sss",attrs:{flat:!1,multiple:!1,pass:e.form.belongsServiceId,type:"serviceType"},on:{return:function(t){e.form.belongsServiceId=t}}})],1),a("el-form-item",{attrs:{label:"排序",prop:"docTypeEnName"}},[a("el-input",{attrs:{placeholder:"排序"},model:{value:e.form.orderNum,callback:function(t){e.$set(e.form,"orderNum",t)},expression:"form.orderNum"}})],1),a("el-form-item",{attrs:{label:"备注",prop:"remark"}},[a("el-input",{attrs:{placeholder:"备注"},model:{value:e.form.remark,callback:function(t){e.$set(e.form,"remark",t)},expression:"form.remark"}})],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("确 定")]),a("el-button",{on:{click:e.cancel}},[e._v("取 消")])],1)],1)],1)},l=[],n=a("5530"),s=(a("d81d"),a("b775"));function r(e){return Object(s["a"])({url:"/system/doctype/list",method:"get",params:e})}function i(e){return Object(s["a"])({url:"/system/doctype/"+e,method:"get"})}function c(e){return Object(s["a"])({url:"/system/doctype",method:"post",data:e})}function d(e){return Object(s["a"])({url:"/system/doctype",method:"put",data:e})}function m(e){return Object(s["a"])({url:"/system/doctype/"+e,method:"delete"})}function u(e,t){var a={docTypeCode:e,status:t};return Object(s["a"])({url:"/system/doctype/changeStatus",method:"put",data:a})}var p={name:"Doctype",data:function(){return{showLeft:0,showRight:24,loading:!0,ids:[],single:!0,multiple:!0,showSearch:!0,total:0,doctypeList:[],title:"",open:!1,queryParams:{pageNum:1,pageSize:20,docTypeLocalName:null,docTypeEnName:null,belongsServiceId:null,status:null,deleteTime:null,deleteStatus:null,deleteBy:null},form:{},rules:{},add:!1}},watch:{showSearch:function(e){!0===e?(this.showRight=21,this.showLeft=3):(this.showRight=24,this.showLeft=0)}},created:function(){this.getList()},methods:{getList:function(){var e=this;this.loading=!0,r(this.queryParams).then((function(t){e.doctypeList=t.rows,e.total=t.total,e.loading=!1}))},cancel:function(){this.open=!1,this.reset()},reset:function(){this.form={docTypeCode:null,docTypeLocalName:null,docTypeEnName:null,belongsServiceId:null,status:"0",remark:null,createTime:null,updateTime:null,deleteTime:null,deleteStatus:"0",deleteBy:null,updateBy:null,createBy:null},this.resetForm("form")},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.resetForm("queryForm"),this.handleQuery()},handleStatusChange:function(e){var t=this,a="0"===e.status?"启用":"停用";this.$confirm('确认要"'+a+"吗？","提示",{customClass:"modal-confirm"}).then((function(){return u(e.docTypeCode,e.status)})).then((function(){t.$modal.msgSuccess(a+"成功")})).catch((function(){e.status="0"===e.status?"1":"0"}))},handleSelectionChange:function(e){this.ids=e.map((function(e){return e.docTypeCode})),this.single=1!==e.length,this.multiple=!e.length},handleAdd:function(){this.reset(),this.open=!0,this.add=!0,this.title="新增文件类型"},handleUpdate:function(e){var t=this;this.reset();var a=e.docTypeCode||this.ids;i(a).then((function(e){t.form=e.data,t.open=!0,t.title="修改文件类型"}))},submitForm:function(){var e=this;this.$refs["form"].validate((function(t){t&&(e.add?(c(e.form).then((function(t){e.$modal.msgSuccess("新增成功"),e.open=!1,e.getList()})),e.add=!1):d(e.form).then((function(t){e.$modal.msgSuccess("修改成功"),e.open=!1,e.getList()})))}))},handleDelete:function(e){var t=this,a=e.docTypeCode||this.ids;this.$confirm('是否确认删除【请填写功能名称】编号为"'+a+'"的数据项？',"提示",{customClass:"modal-confirm"}).then((function(){return m(a)})).then((function(){t.getList(),t.$modal.msgSuccess("删除成功")})).catch((function(){}))},handleExport:function(){this.download("system/doctype/export",Object(n["a"])({},this.queryParams),"doctype_".concat((new Date).getTime(),".xlsx"))}}},h=p,y=a("2877"),f=Object(y["a"])(h,o,l,!1,null,null,null);t["default"]=f.exports}}]);