<template>
  <view class="inventory-container">
    <!-- 搜索区域 -->
    <view class="search-box">
      <view class="search-item">
        <input v-model="queryParams.search" class="search-input" placeholder="Please enter the express tracking number"
               @confirm="handleSearch"/>
      </view>
      <view class="search-btn" @click="handleSearch">
        <uni-icons color="#fff" size="18" type="search"></uni-icons>
      </view>
    </view>

    <!-- 列表区域 -->
    <scroll-view :style="{ height: scrollHeight + 'px' }" class="inventory-list" scroll-y @scrolltolower="loadMoreData">
      <view v-if="inventoryList.length === 0" class="empty-data">
        <text class="empty-text">暂无库存数据</text>
      </view>

      <view v-for="(item, index) in inventoryList" :key="index" class="inventory-item" @click="navToDetail(item)">
        <view class="item-header">
          <text class="item-name">{{ item.clientCode + '-' + item.consigneeCode + '-' + item.consigneeName }}</text>
          <!--          <text :class="['item-quantity', getQuantityClass(item.quantity)]" class="item-quantity">-->
          <!--            {{ item.totalGrossWeight + ' KGS/' }} {{ item.totalBoxes + ' PKG/' }} {{ item.totalVolume + ' CBM' }}-->
          <!--          </text>-->
        </view>
        <view class="item-info">
          <text class="info-label">预录单号：</text>
          <text class="info-value">{{ (item.preEntrySerialNo + '-' + item.inboundSerialNo || '暂无') }}</text>
        </view>
        <view class="item-info">
          <text class="info-label">快递单号：</text>
          <text class="info-value">{{ item.logisticsInfo || '暂无' }}-{{ item.subOrderNo }}</text>
        </view>

        <view class="item-footer">
          <text v-if="item.logisticsInfo === queryParams.search && item.clientCode === null" class="claimed">可认领
          </text>
          <text class="update-time">创建时间：{{ formatDate(item.createdAt) }}</text>
        </view>
      </view>

      <!-- 加载更多 -->
      <view v-if="loading && inventoryList.length > 0" class="loading-more">
        <text class="loading-text">正在加载更多...</text>
      </view>
      <view v-if="!hasMore && inventoryList.length > 0" class="no-more">
        <text class="no-more-text">-- 没有更多数据了 --</text>
      </view>
    </scroll-view>

    <!-- 悬浮按钮 -->
    <view v-if="checkRole(['warehouse','client'])" class="float-btn" @click="handleAdd">
      <uni-icons color="#fff" size="22" type="plus"></uni-icons>
    </view>
  </view>
</template>

<script>
import uniIcons from '../components/uni-icons/uni-icons.vue';
import {getInventoryList} from '@/api/system/invenory';
import {checkRole} from '@/utils/permission'

export default {
  components: {
    uniIcons
  },
  data() {
    return {
      // 查询参数
      queryParams: {
        pageNum: 1,
        pageSize: 10,
        productName: '',
        status: null, // 0: 全部, 1: 库存紧张, 2: 库存充足
        deliveryStatus: null // null: 全部, 1: 已完善, 2: 待完善
      },
      // 库存列表数据
      inventoryList: [],
      // 当前激活的选项卡
      activeTab: 0,
      // 当前激活的送货单选项卡
      activeDeliveryTab: 0,
      // 是否正在加载
      loading: false,
      // 是否还有更多数据
      hasMore: true,
      // 滚动区域高度
      scrollHeight: 0,
      displayFilter: false,
      claim: false
    };
  },
  onLoad(options) {
    // 获取系统信息设置滚动区域高度
    const systemInfo = uni.getSystemInfoSync();
    // 减去搜索框和筛选栏的高度，预留底部安全距离
    this.scrollHeight = systemInfo.windowHeight - 180;
    if (options.claim) {
      this.claim = true
      this.displayFilter = false;
    } else if (options.inTransit) {
      this.queryParams.cargoStatus = 'Pre-entry';
      this.displayFilter = false;
      this.getList();
    } else if (options.uncompleted) {
      this.queryParams.recordStatus = 3;
      this.displayFilter = false;
      this.getList();
    } else if (options.unconfirmed) {
      this.queryParams.cargoStatus = 'In-warehouse';
      this.displayFilter = false;
      this.queryParams.recordStatus = 4;
      this.getList();
    } else if (options.confirmed) {
      this.queryParams.recordStatus = 5;
      this.displayFilter = false;
      this.getList();
    } else {
      // 首次加载数据
      this.displayFilter = true;
      this.getList();
    }

  },
  methods: {
    checkRole,
    // 修改方法名和返回值
    getQuantityClass(quantity) {
      if (quantity <= 10) {
        return 'quantity-low';
      } else if (quantity <= 50) {
        return 'quantity-medium';
      } else {
        return 'quantity-high';
      }
    },
    // 获取库存列表
    async getList(loadMore = false) {
      if (this.loading) return;

      this.loading = true;

      // 如果不是加载更多，重置页码和列表
      if (!loadMore) {
        this.queryParams.pageNum = 1;
        this.inventoryList = [];
        this.hasMore = true;
      }

      try {
        if (checkRole(['client'])) {
          this.queryParams.clientCode = this.$store.state.user.mpWarehouseClient.clientCode;
          if (!this.queryParams.clientCode) {
            uni.showToast({
              title: '获取数据失败',
              icon: 'none'
            });
            this.loading = false;
            return;
          }
        }
        if (checkRole(['user'])) {
          this.queryParams.createdBy = this.$store.state.user.userId
        }
        console.log(this.queryParams)
        const res = await getInventoryList(this.queryParams);

        if (res.code === 200) {
          const {rows, total} = res;

          // 追加数据
          // this.inventoryList = loadMore ? [...this.inventoryList, ...rows] : rows;
          if (loadMore) {
            // 假设this.inventoryList和rows是包含对象的数组，每个对象都有id属性
            const mergedArray = [...this.inventoryList, ...rows];
            const uniqueArray = Array.from(
                new Map(mergedArray.map(item => [item.inventoryId, item])).values()
            );
            this.inventoryList = uniqueArray
          } else {
            this.inventoryList = rows
          }


          // 判断是否还有更多数据
          this.hasMore = this.inventoryList.length < total;

          // 如果没有数据，显示空数据提示
          if (this.inventoryList.length === 0) {
            uni.showToast({
              title: '暂无数据',
              icon: 'none'
            });
          }
        }
      } catch (error) {
        uni.showToast({
          title: '获取数据失败',
          icon: 'none'
        });
      } finally {
        this.loading = false;

        // 停止下拉刷新
        uni.stopPullDownRefresh();
      }
    },

    // 切换送货单选项卡,2待完善
    switchDeliveryTab(tabIndex) {
      if (this.activeDeliveryTab === tabIndex) return;

      this.activeDeliveryTab = tabIndex;

      // 根据选项卡设置查询状态
      switch (tabIndex) {
        case 0: // 全部
          this.queryParams.recordStatus = 5;
          break;
        case 1: // 已完善
          this.queryParams.deliveryStatus = 1;
          break;
        case 2: // 待完善
          this.queryParams.recordStatus = null;
          break;
      }

      // 重新获取数据
      this.getList();
    },

    // 切换选项卡
    switchTab(tabIndex) {
      if (this.activeTab === tabIndex) return;

      this.activeTab = tabIndex;

      // 根据选项卡设置查询状态
      switch (tabIndex) {
        case 0: // 全部
          this.queryParams.status = null;
          break;
        case 1: // 库存紧张
          this.queryParams.status = 1;
          break;
        case 2: // 库存充足
          this.queryParams.status = 2;
          break;
      }

      // 重新获取数据
      this.getList();
    },

    // 处理搜索
    handleSearch() {
      this.getList();
    },

    // 加载更多数据
    loadMoreData() {
      if (!this.hasMore || this.loading) return;

      // 页码加1
      this.queryParams.pageNum++;

      // 加载更多
      this.getList(true);
    },

    // 跳转到详情页
    navToDetail(item) {
      uni.navigateTo({
        url: (item.logisticsInfo === this.queryParams.search && item.clientCode === null) ? `/packageA/inventory/edit?inventoryId=${item.inventoryId}&claimed=1` : `/packageA/inventory/edit?inventoryId=${item.inventoryId}`
      });
    },

    // 添加库存
    handleAdd() {
      uni.navigateTo({
        url: '/packageA/inventory/index?add=true'
      });
    },

    // 获取库存状态的样式类
    getStatusClass(quantity) {
      // 这里根据实际业务需求判断库存状态
      if (quantity <= 10) {
        return 'quantity-low';
      } else if (quantity <= 50) {
        return 'quantity-medium';
      } else {
        return 'quantity-high';
      }
    },

    // 格式化日期
    formatDate(dateString) {
      if (!dateString) return '暂无记录';

      const date = new Date(dateString);
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, '0');
      const day = String(date.getDate()).padStart(2, '0');

      return `${year}-${month}-${day}`;
    }
  },
  // 下拉刷新
  onPullDownRefresh() {
    this.getList();
  }
};
</script>

<style lang="scss">
.inventory-container {
  min-height: 100vh;
  background-color: #f5f5f5;
  position: relative;
}

.search-box {
  padding: 20rpx;
  background-color: #ffffff;
  display: flex;
  align-items: center;
}

.search-item {
  flex: 1;
  background-color: #f0f0f0;
  border-radius: 36rpx;
  padding: 0 20rpx;
  height: 72rpx;
  display: flex;
  align-items: center;
}

.search-input {
  width: 100%;
  height: 100%;
  font-size: 28rpx;
}

.search-btn {
  width: 72rpx;
  height: 72rpx;
  background-color: #1890ff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 20rpx;
}

.filter-box {
  display: flex;
  background-color: #ffffff;
  padding: 20rpx 0;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.filter-item {
  flex: 1;
  text-align: center;
  font-size: 28rpx;
  color: #333333;
  position: relative;
  padding: 10rpx 0;
}

.filter-item.active {
  color: #1890ff;
  font-weight: bold;
}

.filter-item.active::after {
  content: '';
  position: absolute;
  bottom: -10rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 60rpx;
  height: 6rpx;
  background-color: #1890ff;
  border-radius: 3rpx;
}

.inventory-list {
  width: 100%;
}

.inventory-item {
  margin: 20rpx;
  padding: 30rpx;
  background-color: #ffffff;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #eee;
  margin-bottom: 20rpx;
}

.item-name {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
}

.item-quantity {
  font-size: 32rpx;
  font-weight: bold;
}

.quantity-low {
  color: #ff4d4f;
}

.quantity-medium {
  color: #faad14;
}

.quantity-high {
  color: #52c41a;
}

.item-info {
  display: flex;
  font-size: 28rpx;
  margin-bottom: 16rpx;
  color: #666666;
}

.info-label {
  min-width: 160rpx;
  color: #999999;
}

.info-value {
  flex: 1;
}

.item-footer {
  display: flex;
  justify-content: space-between;
  margin-top: 20rpx;
  padding-top: 20rpx;
  border-top: 1rpx solid #eee;
}

.claimed {
  font-size: 26rpx;
  font-weight: bold;
  color: #ffffff;
  background-color: #ff0000;
  padding: 8rpx 20rpx;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 8rpx rgba(255, 0, 0, 0.5);
  animation: pulse 1.2s infinite;
  position: relative;
  display: inline-block;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    box-shadow: 0 4rpx 8rpx rgba(255, 0, 0, 0.5);
  }
  50% {
    transform: scale(1.1);
    box-shadow: 0 4rpx 12rpx rgba(255, 0, 0, 0.7);
  }
  100% {
    transform: scale(1);
    box-shadow: 0 4rpx 8rpx rgba(255, 0, 0, 0.5);
  }
}

.update-time {
  font-size: 24rpx;
  color: #999999;
}

.empty-data {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 100rpx 0;
}

.empty-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 20rpx;
}

.empty-text {
  font-size: 28rpx;
  color: #999999;
}

.loading-more,
.no-more {
  text-align: center;
  padding: 30rpx;
}

.loading-text,
.no-more-text {
  font-size: 24rpx;
  color: #999999;
}

.float-btn {
  position: fixed;
  right: 30rpx;
  bottom: 100rpx;
  width: 100rpx;
  height: 100rpx;
  background-color: #1890ff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  box-shadow: 0 4rpx 12rpx rgba(24, 144, 255, 0.4);
  z-index: 10;
}
</style>