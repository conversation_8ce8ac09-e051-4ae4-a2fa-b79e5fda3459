(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d0e892a"],{"8a9d":function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:e.showLeft}},[a("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],ref:"queryForm",staticClass:"query",attrs:{inline:!0,model:e.queryParams,size:"mini"}},[a("el-form-item",{attrs:{label:"搜索",prop:"cargoTypeQuery"}},[a("el-input",{staticStyle:{width:"158px"},attrs:{clearable:"",placeholder:"中英文/简称"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.cargoTypeQuery,callback:function(t){e.$set(e.queryParams,"cargoTypeQuery",t)},expression:"queryParams.cargoTypeQuery"}})],1),a("el-form-item",[a("el-button",{attrs:{icon:"el-icon-search",size:"mini",type:"primary"},on:{click:e.handleQuery}},[e._v("搜索")]),a("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1)],1),a("el-col",{attrs:{span:e.showRight}},[a("el-row",{staticClass:"mb8",attrs:{gutter:10}},[a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:cargotype:add"],expression:"['system:cargotype:add']"}],attrs:{icon:"el-icon-plus",plain:"",size:"mini",type:"primary"},on:{click:e.handleAdd}},[e._v("新增 ")])],1),a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:cargotype:remove"],expression:"['system:cargotype:remove']"}],attrs:{disabled:e.multiple,icon:"el-icon-delete",plain:"",size:"mini",type:"danger"},on:{click:e.handleDelete}},[e._v("删除 ")])],1),a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:cargotype:export"],expression:"['system:cargotype:export']"}],attrs:{icon:"el-icon-download",plain:"",size:"mini",type:"warning"},on:{click:e.handleExport}},[e._v("导出 ")])],1),a("right-toolbar",{attrs:{showSearch:e.showSearch},on:{"update:showSearch":function(t){e.showSearch=t},"update:show-search":function(t){e.showSearch=t},queryTable:e.getList}})],1),e.refreshTable?a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.typeList,"default-expand-all":e.isExpandAll,"tree-props":{children:"children",hasChildren:"hasChildren"},"row-key":"cargoTypeId"}},[a("el-table-column",{attrs:{align:"left",label:"货物特征名称"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(t.row.cargoTypeShortName)+" "),a("a",{staticStyle:{margin:"0","font-weight":"bold","font-size":"small"}},[e._v(e._s(t.row.cargoTypeLocalName))]),e._v(" "+e._s(t.row.cargoTypeEnName)+" ")]}}],null,!1,5697098)}),a("el-table-column",{attrs:{align:"left",label:"备注",prop:"remark"}}),a("el-table-column",{attrs:{align:"center",label:"等级",prop:"cargoTypeLevel",width:"48"}}),a("el-table-column",{attrs:{align:"center",label:"上下排序",prop:"verticalSort",width:"68"}}),a("el-table-column",{attrs:{align:"center",label:"排序",prop:"orderNum",width:"48"}}),a("el-table-column",{attrs:{align:"center",label:"是否上锁",prop:"isLocked",width:"68"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-switch",{attrs:{"active-value":"0","inactive-value":"1"},on:{change:function(a){return e.handleLockedChange(t.row)}},model:{value:t.row.isLocked,callback:function(a){e.$set(t.row,"isLocked",a)},expression:"scope.row.isLocked"}})]}}],null,!1,2097829220)}),a("el-table-column",{attrs:{align:"center",label:"状态",prop:"status",width:"68"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-switch",{attrs:{"active-value":"0","inactive-value":"1"},on:{change:function(a){return e.handleStatusChange(t.row)}},model:{value:t.row.status,callback:function(a){e.$set(t.row,"status",a)},expression:"scope.row.status"}})]}}],null,!1,3955094654)}),a("el-table-column",{attrs:{align:"center","class-name":"small-padding fixed-width",label:"操作",width:"170"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:cargotype:add"],expression:"['system:cargotype:add']"}],staticStyle:{"margin-right":"-8px"},attrs:{icon:"el-icon-plus",size:"mini",type:"primary"},on:{click:function(a){return e.handleAdd(t.row)}}},[e._v("新增 ")]),a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:cargotype:edit"],expression:"['system:cargotype:edit']"}],staticStyle:{"margin-right":"-8px"},attrs:{icon:"el-icon-edit",size:"mini",type:"success"},on:{click:function(a){return e.handleUpdate(t.row)}}},[e._v("修改 ")]),0!=t.row.parentId?a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:cargotype:remove"],expression:"['system:cargotype:remove']"}],staticStyle:{"margin-right":"-8px"},attrs:{icon:"el-icon-delete",size:"mini",type:"danger"},on:{click:function(a){return e.handleDelete(t.row)}}},[e._v("删除 ")]):e._e()]}}],null,!1,3769135270)})],1):e._e()],1)],1),a("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"},{name:"dialogDragWidth",rawName:"v-dialogDragWidth"}],attrs:{"close-on-click-modal":!1,"modal-append-to-body":!1,title:e.title,visible:e.open,"append-to-body":"",width:"500px"},on:{"update:visible":function(t){e.open=t}}},[a("el-form",{ref:"form",staticClass:"edit",attrs:{model:e.form,rules:e.rules,"label-width":"80px"}},[0!=e.form.parentId?a("el-form-item",{attrs:{label:"上级",prop:"cargoTypeId"}},[a("tree-select",{attrs:{multiple:!1,pass:e.form.parentId,placeholder:"上级类名",type:"cargoType"},on:{return:e.getParentId}})],1):e._e(),a("el-form-item",{attrs:{label:"简称",prop:"cargoTypeShortName"}},[a("el-input",{attrs:{placeholder:"货物特征名缩写"},model:{value:e.form.cargoTypeShortName,callback:function(t){e.$set(e.form,"cargoTypeShortName",t)},expression:"form.cargoTypeShortName"}})],1),a("el-form-item",{attrs:{label:"中文名",prop:"cargoTypeLocalName"}},[a("el-input",{attrs:{placeholder:"货物特征中文名"},model:{value:e.form.cargoTypeLocalName,callback:function(t){e.$set(e.form,"cargoTypeLocalName",t)},expression:"form.cargoTypeLocalName"}})],1),a("el-form-item",{attrs:{label:"英文名",prop:"cargoTypeEnName"}},[a("el-input",{attrs:{placeholder:"货物特征英文名"},model:{value:e.form.cargoTypeEnName,callback:function(t){e.$set(e.form,"cargoTypeEnName",t)},expression:"form.cargoTypeEnName"}})],1),a("el-form-item",{attrs:{label:"等级",prop:"cargoTypeLevel"}},[a("el-input",{attrs:{placeholder:"货物特征等级"},model:{value:e.form.cargoTypeLevel,callback:function(t){e.$set(e.form,"cargoTypeLevel",t)},expression:"form.cargoTypeLevel"}})],1),a("el-form-item",{attrs:{label:"是否上锁",prop:"isLocked"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"是否上锁"},model:{value:e.form.isLocked,callback:function(t){e.$set(e.form,"isLocked",t)},expression:"form.isLocked"}},e._l(e.dict.type.sys_yes_no,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),a("el-form-item",{attrs:{label:"纵向优先级",prop:"verticalSort"}},[a("el-input-number",{staticStyle:{width:"100%"},attrs:{controls:!1,min:"0",placeholder:"纵向优先级"},model:{value:e.form.verticalSort,callback:function(t){e.$set(e.form,"verticalSort",t)},expression:"form.verticalSort"}})],1),a("el-form-item",{attrs:{label:"横向优先级",prop:"orderNum"}},[a("el-input-number",{staticStyle:{width:"100%"},attrs:{controls:!1,min:"0",placeholder:"横向优先级"},model:{value:e.form.orderNum,callback:function(t){e.$set(e.form,"orderNum",t)},expression:"form.orderNum"}})],1),a("el-form-item",{attrs:{label:"备注",prop:"remark"}},[a("el-input",{attrs:{autosize:{minRows:10,maxRows:20},maxlength:"150",placeholder:"备注","show-word-limit":"",type:"textarea"},model:{value:e.form.remark,callback:function(t){e.$set(e.form,"remark",t)},expression:"form.remark"}})],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("确 定")]),a("el-button",{on:{click:e.cancel}},[e._v("取 消")])],1)],1)],1)},o=[],l=a("5530"),n=(a("d81d"),a("cc121")),s={name:"CargoType",dicts:["sys_yes_no"],data:function(){return{showLeft:3,showRight:21,defaultProps:{children:"children",label:"label"},refreshTable:!0,isExpandAll:!1,loading:!0,ids:[],single:!0,multiple:!0,showSearch:!0,typeList:[],title:"",open:!1,queryParams:{cargoTypeQuery:null},form:{},rules:{}}},watch:{showSearch:function(e){1==e?(this.showRight=21,this.showLeft=3):(this.showRight=24,this.showLeft=0)}},created:function(){this.getList()},methods:{getList:function(){var e=this;this.loading=!0,Object(n["f"])(this.queryParams).then((function(t){e.typeList=e.handleTree(t.data,"cargoTypeId"),e.loading=!1}))},cancel:function(){this.open=!1,this.reset()},reset:function(){this.form={cargoTypeId:null,parentId:null,cargoTypeShortName:null,cargoTypeLocalName:null,cargoTypeEnName:null,cargoTypeLevel:null,isLocked:null,verticalSort:null,orderNum:null,status:0,remark:null,createBy:null,createTime:null,updateBy:null,updateTime:null,deleteBy:null,deleteTime:null,deleteStatus:0},this.resetForm("form")},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.resetForm("queryForm"),this.handleQuery()},handleSelectionChange:function(e){this.ids=e.map((function(e){return e.cargoTypeId})),this.single=1!=e.length,this.multiple=!e.length},handleAdd:function(e){this.reset(),this.open=!0,this.title="添加货物特征",e&&(this.form.parentId=e.cargoTypeId)},handleUpdate:function(e){var t=this;this.reset();var a=e.cargoTypeId||this.ids;Object(n["e"])(a).then((function(e){t.form=e.data,t.open=!0,t.title="修改货物特征"}))},submitForm:function(){var e=this;this.$refs["form"].validate((function(t){t&&(null!=e.form.cargoTypeId?Object(n["h"])(e.form).then((function(t){e.$modal.msgSuccess("修改成功"),e.open=!1,e.getList()})):Object(n["a"])(e.form).then((function(t){e.$modal.msgSuccess("新增成功"),e.open=!1,e.getList()})))}))},handleLockedChange:function(e){var t=this,a="0"==e.isLocked?"上锁":"解锁";this.$confirm("确认要"+a+"吗？","提示",{customClass:"modal-confirm"}).then((function(){return Object(n["b"])(e.cargoTypeId,e.isLocked)})).then((function(){t.$modal.msgSuccess(a+"成功")})).catch((function(){e.isLocked="0"==e.isLocked?"1":"0"}))},handleStatusChange:function(e){var t=this,a="0"==e.status?"启用":"停用";this.$confirm("确认要修改状态吗？","提示",{customClass:"modal-confirm"}).then((function(){return Object(n["c"])(e.cargoTypeId,e.status)})).then((function(){t.$modal.msgSuccess(a+"成功")})).catch((function(){e.status="0"==e.status?"1":"0"}))},handleDelete:function(e){var t=this,a=e.cargoTypeId||this.ids;this.$confirm('是否确认删除货物特征编号为"'+a+'"的数据项？',"提示",{customClass:"modal-confirm"}).then((function(){return Object(n["d"])(a)})).then((function(){t.getList(),t.$modal.msgSuccess("删除成功")})).catch((function(){}))},handleExport:function(){this.download("system/type/export",Object(l["a"])({},this.queryParams),"type_".concat((new Date).getTime(),".xlsx"))},getParentId:function(e){this.form.parentId=e}}},i=s,c=a("2877"),m=Object(c["a"])(i,r,o,!1,null,null,null);t["default"]=m.exports}}]);