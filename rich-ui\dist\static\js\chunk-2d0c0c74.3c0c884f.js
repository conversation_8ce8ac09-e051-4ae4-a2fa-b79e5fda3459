(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d0c0c74"],{"42dd":function(e,t,o){"use strict";o.r(t);var s=function(){var e=this,t=e.$createElement,o=e._self._c||t;return o("div",[2!=e.typeId?o("el-tooltip",{attrs:{disabled:null==e.scope.row.departure||null==e.scope.row.destination||(e.scope.row.departure+e.scope.row.destination).length+3<25,placement:"top"}},[o("div",{attrs:{slot:"content"},slot:"content"},[o("h6",{staticStyle:{margin:"0","font-weight":"bold","font-size":"small"}},[e._v(" "+e._s(e.scope.row.departure)+" "+e._s(null!=e.scope.row.departure&&null!=e.scope.row.destination?"-":"")+e._s(e.scope.row.destination)+" ")])]),o("div",[o("h6",{staticStyle:{margin:"0","font-weight":"bold","font-size":"small",overflow:"hidden","text-overflow":"ellipsis","white-space":"nowrap"}},[e._v(" "+e._s(e.scope.row.departure)+" "+e._s(null!=e.scope.row.departure&&null!=e.scope.row.destination?"-":"")+" "+e._s(e.scope.row.destination)+" ")])])]):o("el-tooltip",{attrs:{disabled:null==e.scope.row.departure||null==e.scope.row.destination||(e.scope.row.departure+e.scope.row.destination).length+3<25,placement:"top"}},[o("div",{attrs:{slot:"content"},slot:"content"},[o("h6",{staticStyle:{margin:"0","font-weight":"bold","font-size":"small"}},[e._v(" "+e._s(e.scope.row.departureIata)+" "+e._s(null!=e.scope.row.departureIata&&null!=e.scope.row.destinationIata?"-":"")+" "+e._s(e.scope.row.destinationIata)+" ")]),o("h6",{staticStyle:{margin:"0","font-weight":"bold","font-size":"small"}},[e._v(" ("+e._s(e.scope.row.departure)+" "+e._s(null!=e.scope.row.departure&&null!=e.scope.row.destination?"-":"")+e._s(e.scope.row.destination)+") ")])]),o("div",[o("h6",{staticStyle:{margin:"0","font-weight":"bold","font-size":"small",overflow:"hidden","text-overflow":"ellipsis","white-space":"nowrap"}},[e._v(" "+e._s(e.scope.row.departureIata)+" "+e._s(null!=e.scope.row.departureIata&&null!=e.scope.row.destinationIata?"-":"")+" "+e._s(e.scope.row.destinationIata)+" ")]),o("h6",{staticStyle:{margin:"0","font-weight":"bold","font-size":"small",overflow:"hidden","text-overflow":"ellipsis","white-space":"nowrap"}},[e._v(" ("+e._s(e.scope.row.departure)+" "+e._s(null!=e.scope.row.departure&&null!=e.scope.row.destination?"-":"")+" "+e._s(e.scope.row.destination)+") ")])])])],1)},n=[],r={name:"departureToDestination",props:["scope","typeId"],data:function(){return{size:this.$store.state.app.size||"mini"}}},a=r,i=o("2877"),l=Object(i["a"])(a,s,n,!1,null,"fbc0b9c8",null);t["default"]=l.exports}}]);