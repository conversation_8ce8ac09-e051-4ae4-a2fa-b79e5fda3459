(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-58202c56"],{"2d11":function(e,t,n){"use strict";n("3630")},3630:function(e,t,n){},c18a:function(e,t,n){"use strict";n("fc8a")},dd7b:function(e,t,n){"use strict";n.r(t);var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"login"},[n("el-form",{directives:[{name:"loading",rawName:"v-loading",value:e.mac,expression:"mac"}],ref:"loginForm",staticClass:"login-form",attrs:{model:e.loginForm,rules:e.loginRules,"show-message":!1,"status-icon":""}},[n("div",{staticClass:"title"},[n("h3",{staticStyle:{margin:"auto",width:"fit-content"},on:{dblclick:e.toggleDebugMode}},[e._v("瑞旗系统")])]),n("el-form-item",{attrs:{prop:"username"}},[n("el-input",{attrs:{"auto-complete":"off",placeholder:"账号",type:"text"},model:{value:e.loginForm.username,callback:function(t){e.$set(e.loginForm,"username",t)},expression:"loginForm.username"}})],1),n("el-form-item",{attrs:{prop:"password"}},[n("el-input",{attrs:{"auto-complete":"off",placeholder:"密码",type:"password"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleLogin(t)}},model:{value:e.loginForm.password,callback:function(t){e.$set(e.loginForm,"password",t)},expression:"loginForm.password"}})],1),e.captchaEnabled?n("el-form-item",{attrs:{prop:"code"}},[n("el-input",{staticStyle:{width:"63%"},attrs:{"auto-complete":"off",placeholder:"验证码"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleLogin(t)}},model:{value:e.loginForm.code,callback:function(t){e.$set(e.loginForm,"code",t)},expression:"loginForm.code"}}),n("div",{staticClass:"login-code"},[n("img",{staticClass:"login-code-img",attrs:{src:e.codeUrl},on:{click:e.getCode}})])],1):e._e(),n("el-checkbox",{staticStyle:{margin:"0px 0px 25px 0px"},model:{value:e.loginForm.rememberMe,callback:function(t){e.$set(e.loginForm,"rememberMe",t)},expression:"loginForm.rememberMe"}},[e._v("记住密码")]),n("el-form-item",{staticStyle:{width:"100%"}},[n("el-button",{staticStyle:{width:"100%"},attrs:{loading:e.loading,size:"medium",type:"primary"},on:{click:e.handleLogin}},[e.loading?n("span",[e._v("登 录 中...")]):n("span",[e._v("登 录")])]),e.register?n("div",{staticStyle:{float:"right"}},[n("router-link",{staticClass:"link-type",attrs:{to:"/register"}},[e._v("立即注册")])],1):e._e()],1)],1),n("div",{staticClass:"el-login-footer"},[n("span",{on:{click:e.incrementDebugCounter}},[e._v("Copyright © 2009-2024 RichShipping All Rights Reserved.")])]),e.showWechatScan?n("wechat-scan",{attrs:{visible:e.showWechatScan,username:e.loginForm.username},on:{"update:visible":function(t){e.showWechatScan=t},"scan-success":e.handleWechatScanSuccess}}):e._e()],1)},r=[],i=n("c7eb"),s=n("1da1"),o=(n("d3b7"),n("25f0"),n("d81d"),n("a15b"),n("14d9"),n("7ded")),c=n("852e"),l=n.n(c),u=n("24e5"),d=n.n(u),h="MIICIjANBgkqhkiG9w0BAQEFAAOCAg8AMIICCgKCAgEAyvMKuOxIfxASSqyB0fOy\nuOSSc5peeUvBclxCaCEejuMQ7HVUDatpuyJV1KtVo1oyUe/LVQnA38bZn3RoLk0z\neoyRAzC8IaTGzrRlq6ilO5+8R+YP6PysDhY0vIkNNsE6AnWVkwOnJzisawhc3Nwt\nSXpasu8r2FQdw77Ha/25LveQKlg3FTil8MT4CaOO0qar4ngj8hbWmB6nRtF8Q86D\n7IUyObdW/FKObPL8Ej+0U6X/zhk3qkwxBslUPmcNDlOZhCVcjzOpoGorQI7qFU6D\nAmYYRyTi/iA80Uxlk7DMVNZpvArpqjVHS5nYvD6tG99Sfs5b+LeJmlNiBSh2nvOL\nKS5CUO2eqvPnIrBd3+Nv0ESbe0ztyFhMzmcHzQT6kITBzciHQPpHQ62kycNoSWg+\niMTmmr3BEMk0oknv8C9g37NKOH3neg+mg0P67Qpzx1ZEGHolsCCgOx6CmamvKAxd\nRJVBQF22S9wZ93/6PP6bCKsG+R0U6RBAR5EtTeL9AOEqHYjziS8kmmNr3a3gmPc9\nNWXbKLrRwUWn+JTU4gdWOUEeK1Xo3SnvFil/Ewbee77eQBs7WsINqgs6tetu+ZKW\n0YglS4jmWydMUmxHmZPyKy6ze36Ze40R4FSOv+LqWVccqnzUZrkAaGrg/Xzyk/Hr\nnno+fRJoDYchY8GexB4TTxMCAwEAAQ==",g="MIIJQgIBADANBgkqhkiG9w0BAQEFAASCCSwwggkoAgEAAoICAQDK8wq47Eh/EBJK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\nAgx8YX42Mw1yUrsmEVPRWaDk3dmfNQ==";function m(e){var t=new d.a;return t.setPublicKey(h),t.encrypt(e)}function p(e){var t=new d.a;return t.setPrivateKey(g),t.decrypt(e)}var f=n("e813"),v=n.n(f),b=n("a5bb"),w=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("el-dialog",{attrs:{title:"微信扫码登录",visible:e.dialogVisible,width:"350px","close-on-click-modal":!1,"close-on-press-escape":!1,"show-close":!1},on:{"update:visible":function(t){e.dialogVisible=t}}},[n("div",{staticClass:"qrcode-container"},["loading"===e.status?n("div",{staticClass:"loading"},[n("i",{staticClass:"el-icon-loading"}),n("p",[e._v("正在加载二维码...")])]):"expired"===e.status?n("div",{staticClass:"expired"},[n("i",{staticClass:"el-icon-warning"}),n("p",[e._v("二维码已过期")]),n("el-button",{attrs:{type:"primary"},on:{click:e.refreshQrCode}},[e._v("刷新二维码")])],1):"confirmed"===e.status?n("div",{staticClass:"success"},[n("i",{staticClass:"el-icon-success"}),n("p",[e._v("验证成功")]),n("p",[e._v("正在登录...")])]):n("div",[n("div",{staticClass:"qrcode"},[n("div",{ref:"qrCodeContainer",staticClass:"qrcode-img",attrs:{id:"login_container"}})]),n("p",{staticClass:"tip"},[e._v("请使用微信扫一扫")]),e.isWechatBound?e._e():n("p",{staticClass:"tip-bind"},[e._v("首次扫码将绑定您的微信账号")]),n("p",{staticClass:"tip-warn"},[e._v("扫描后请在微信中确认")])])])])},C=[],x=(n("3ca3"),n("ddb0"),n("2b3d"),n("9861"),{name:"WechatScan",props:{visible:{type:Boolean,default:!1},username:{type:String,required:!0}},data:function(){return{status:"loading",scanId:"",qrCodeUrl:"",checkInterval:null,wxLoginInstance:null,isWechatBound:!1}},computed:{dialogVisible:{get:function(){return this.visible},set:function(e){this.$emit("update:visible",e)}}},watch:{visible:{immediate:!0,handler:function(e){e?this.loadWxLoginScript():this.clearInterval()}}},methods:{loadWxLoginScript:function(){var e=this;if(window.WxLogin)this.checkWechatBindStatus();else{var t=document.createElement("script");t.src="https://res.wx.qq.com/connect/zh_CN/htmledition/js/wxLogin.js",t.onload=function(){e.checkWechatBindStatus()},t.onerror=function(){e.$message.error("加载微信登录脚本失败"),e.status="expired"},document.head.appendChild(t)}},checkWechatBindStatus:function(){var e=this;return Object(s["a"])(Object(i["a"])().mark((function t(){var n;return Object(i["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,Object(o["a"])(e.username);case 3:if(n=t.sent,200!==n.code){t.next=10;break}return e.isWechatBound=n.data.isWechatBound,t.next=8,e.getQrCode();case 8:t.next=12;break;case 10:e.$message.error(n.msg||"获取微信绑定状态失败"),e.status="expired";case 12:t.next=18;break;case 14:t.prev=14,t.t0=t["catch"](0),console.error("检查微信绑定状态失败",t.t0),e.$message.error("检查微信绑定状态失败");case 18:case"end":return t.stop()}}),t,null,[[0,14]])})))()},getQrCode:function(){var e=this;return Object(s["a"])(Object(i["a"])().mark((function t(){var n;return Object(i["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.status="loading",t.prev=1,t.next=4,Object(o["f"])(e.username);case 4:n=t.sent,200===n.code?(e.scanId=n.data.scanId,e.qrCodeUrl=n.data.qrCodeUrl,e.status="waiting",e.$nextTick((function(){e.generateWxQrCode(n.data)})),e.startCheckStatus()):(e.$message.error(n.msg||"获取二维码失败"),e.status="expired"),t.next=13;break;case 8:t.prev=8,t.t0=t["catch"](1),console.error("获取二维码失败",t.t0),e.$message.error("获取二维码失败"),e.status="expired";case 13:case"end":return t.stop()}}),t,null,[[1,8]])})))()},generateWxQrCode:function(e){if(this.$refs.qrCodeContainer){this.$refs.qrCodeContainer.innerHTML="";var t=new URL(e.qrCodeUrl),n=t.searchParams.get("appid"),a=t.searchParams.get("redirect_uri");a=decodeURIComponent(a),this.wxLoginInstance=new WxLogin({id:"login_container",appid:n,scope:"snsapi_login",redirect_uri:a,state:this.scanId,self_redirect:!0,style:"",href:""})}},startCheckStatus:function(){this.clearInterval(),this.checkInterval=setInterval(this.checkStatus,2e3)},checkStatus:function(){var e=this;return Object(s["a"])(Object(i["a"])().mark((function t(){var n,a;return Object(i["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,t.next=3,Object(o["b"])(e.scanId);case 3:n=t.sent,200===n.code?(a=n.data.status,"CONFIRMED"===a&&(e.status="confirmed",e.clearInterval(),setTimeout((function(){e.$emit("scan-success")}),1500))):(e.status="expired",e.clearInterval()),t.next=12;break;case 7:t.prev=7,t.t0=t["catch"](0),console.error("检查扫码状态失败",t.t0),e.status="expired",e.clearInterval();case 12:case"end":return t.stop()}}),t,null,[[0,7]])})))()},clearInterval:function(e){function t(){return e.apply(this,arguments)}return t.toString=function(){return e.toString()},t}((function(){this.checkInterval&&(clearInterval(this.checkInterval),this.checkInterval=null)})),refreshQrCode:function(){this.getQrCode()}},beforeDestroy:function(){this.clearInterval()}}),y=x,k=(n("c18a"),n("2877")),S=Object(k["a"])(y,w,C,!1,null,"70715d10",null),O=S.exports,I={name:"Login",components:{WechatScan:O},data:function(){return{mac:!1,codeUrl:"",loginForm:{username:"",password:"",rememberMe:!1,code:"",uuid:"",unid:""},loginRules:{username:[{required:!0,trigger:"blur",message:"您的账号"}],password:[{required:!0,trigger:"blur",message:"您的密码"}],code:[{required:!0,trigger:"change",message:"验证码"}]},loading:!1,captchaEnabled:!0,register:!1,redirect:void 0,showWechatScan:!1,showDebugOptions:!1,debugClickCount:0,enableWechatVerify:!0}},watch:{$route:{handler:function(e){this.redirect=e.query&&e.query.redirect},immediate:!0},enableWechatVerify:{handler:function(e){localStorage.setItem("debug_enable_wechat_verify",e.toString())}}},created:function(){this.getCode(),this.getCookie();var e=localStorage.getItem("debug_enable_wechat_verify");this.showDebugOptions&&null!==e?this.enableWechatVerify="true"===e:(this.enableWechatVerify=!0,localStorage.setItem("debug_enable_wechat_verify",this.enableWechatVerify.toString()))},methods:{toggleDebugMode:function(){this.showDebugOptions=!this.showDebugOptions,this.showDebugOptions&&this.$message.success("已"+(this.enableWechatVerify?"启用":"关闭")+"微信验证")},incrementDebugCounter:function(){this.debugClickCount++,this.debugClickCount>=5&&(this.showDebugOptions=!0,this.debugClickCount=0,this.$message.success("已"+(this.enableWechatVerify?"启用":"关闭")+"微信验证"))},getMac:function(){var e=this;this.mac=!0,this.getFingerPrint((function(t){e.$alert(t,"",{callback:function(n){"confirm"==n&&(e.$message.success("已复制"),e.mac=!1,navigator.clipboard.writeText(t)),"cancel"==n&&(e.mac=!1)}})}))},getFingerPrint:function(e){return Object(s["a"])(Object(i["a"])().mark((function t(){var n;return Object(i["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:n=v.a.Options={excludes:{webdriver:!0,userAgent:!0,language:!0,colorDepth:!0,deviceMemory:!0,pixelRatio:!0,hardwareConcurrency:!0,screenResolution:!0,availableScreenResolution:!0,timezoneOffset:!0,timezone:!0,sessionStorage:!0,localStorage:!0,indexedDb:!0,addBehavior:!0,openDatabase:!0,cpuClass:!0,platform:!0,doNotTrack:!0,plugins:!0,canvas:!0,webgl:!1,webglVendorAndRenderer:!1,adBlock:!0,hasLiedLanguages:!0,hasLiedResolution:!0,hasLiedOs:!0,hasLiedBrowser:!0,touchSupport:!0,fonts:!0,fontsFlash:!0,audio:!1,enumerateDevices:!0}},v.a.get(n,function(){var t=Object(s["a"])(Object(i["a"])().mark((function t(n){var a,r,s;return Object(i["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return a=n.map((function(e,t){return e.value})),v.a.x64hash128(a.join(""),31),t.next=4,b["a"].load();case 4:return r=t.sent,t.next=7,r.get();case 7:s=t.sent,e(s.visitorId);case 9:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}());case 2:case"end":return t.stop()}}),t)})))()},logCode:function(){return Object(s["a"])(Object(i["a"])().mark((function e(){var t,n;return Object(i["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return e.next=2,b["a"].load();case 2:return t=e.sent,e.next=5,t.get();case 5:return n=e.sent,console.log("Browser fingerprint:",n.visitorId),e.abrupt("return",n.visitorId);case 8:case"end":return e.stop()}}),e)})))()},getCode:function(){var e=this;Object(o["c"])().then((function(t){e.captchaEnabled=void 0==t.captchaEnabled||t.captchaEnabled,e.captchaEnabled&&(e.codeUrl="data:image/gif;base64,"+t.img,e.loginForm.uuid=t.uuid)}))},getCookie:function(){var e=l.a.get("username"),t=l.a.get("password"),n=l.a.get("rememberMe");this.loginForm={username:void 0==e?this.loginForm.username:e,password:void 0==t?this.loginForm.password:p(t),rememberMe:void 0!=n&&Boolean(n)}},handleLogin:function(){var e=this;this.$refs.loginForm.validate((function(t){t&&(e.loading=!0,e.loginForm.rememberMe?(l.a.set("username",e.loginForm.username,{expires:30}),l.a.set("password",m(e.loginForm.password),{expires:30}),l.a.set("rememberMe",e.loginForm.rememberMe,{expires:30})):(l.a.remove("username"),l.a.remove("password"),l.a.remove("rememberMe")),e.getFingerPrint((function(t){e.loginForm.unid=t,e.checkNeedWechatScan()})))}))},checkNeedWechatScan:function(){var e=this;return Object(s["a"])(Object(i["a"])().mark((function t(){var n,a,r;return Object(i["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(e.enableWechatVerify){t.next=4;break}return console.log("微信验证已关闭，直接登录"),e.doLogin(),t.abrupt("return");case 4:return t.prev=4,t.next=7,Object(o["a"])(e.loginForm.username);case 7:n=t.sent,200===n.code?(a=n.data.needScan,r=n.data.isWechatBound,a?(e.showWechatScan=!0,e.loading=!1,r||e.$message.info("首次登录需要绑定微信账号进行验证")):e.doLogin()):e.doLogin(),t.next=15;break;case 11:t.prev=11,t.t0=t["catch"](4),console.error("检查微信扫码验证失败",t.t0),e.doLogin();case 15:case"end":return t.stop()}}),t,null,[[4,11]])})))()},doLogin:function(){var e=this;this.$store.dispatch("Login",this.loginForm).then((function(){e.$router.push({path:e.redirect||"/"}).catch((function(){}))})).catch((function(){e.loading=!1,e.captchaEnabled&&e.getCode()}))},handleWechatScanSuccess:function(){this.showWechatScan=!1,this.doLogin()}}},F=I,W=(n("2d11"),Object(k["a"])(F,a,r,!1,null,null,null));t["default"]=W.exports},fc8a:function(e,t,n){}}]);