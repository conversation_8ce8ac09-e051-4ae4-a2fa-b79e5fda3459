(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d0be2f9"],{"2ed4":function(e,o,r){"use strict";r.r(o);var t=function(){var e=this,o=e.$createElement,r=e._self._c||o;return r("div",[r("el-tooltip",{attrs:{"open-delay":500,disabled:((null!=e.scope.row.locationDeparture?e.scope.row.locationDeparture:"")+(null!=e.scope.row.lineDeparture?e.scope.row.lineDeparture:"")).length<11,placement:"top"}},[r("div",{attrs:{slot:"content"},slot:"content"},[r("h6",{staticStyle:{margin:"0"}},[e._v(" "+e._s((null!=e.scope.row.locationDeparture?e.scope.row.locationDeparture:"")+(null!=e.scope.row.locationDeparture&&null!=e.scope.row.lineDeparture?",":"")+(null!=e.scope.row.lineDeparture?e.scope.row.lineDeparture:""))+" ")])]),r("div",[r("h6",{staticStyle:{margin:"0",overflow:"hidden","text-overflow":"ellipsis"}},[e._v(" "+e._s(((null!=e.scope.row.locationDeparture?e.scope.row.locationDeparture:"")+(null!=e.scope.row.locationDeparture&&null!=e.scope.row.lineDeparture?",":"")+(null!=e.scope.row.lineDeparture?e.scope.row.lineDeparture:"")).substring(0,11)+(((null!=e.scope.row.locationDeparture?e.scope.row.locationDeparture:"")+(null!=e.scope.row.lineDeparture?e.scope.row.lineDeparture:"")).length>11?"...":""))+" ")])])])],1)},l=[],n={name:"departure",props:["scope"]},p=n,a=r("2877"),c=Object(a["a"])(p,t,l,!1,null,"8d1372e4",null);o["default"]=c.exports}}]);