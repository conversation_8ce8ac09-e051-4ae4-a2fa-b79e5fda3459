(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-aafadac4","chunk-90e79546"],{"07ac":function(e,t,a){var n=a("23e7"),i=a("6f53").values;n({target:"Object",stat:!0},{values:function(e){return i(e)}})},"2ce2":function(e,t,a){},4678:function(e,t,a){var n={"./af":"2bfb","./af.js":"2bfb","./ar":"8e73","./ar-dz":"a356","./ar-dz.js":"a356","./ar-kw":"423e","./ar-kw.js":"423e","./ar-ly":"1cfd","./ar-ly.js":"1cfd","./ar-ma":"0a84","./ar-ma.js":"0a84","./ar-ps":"4c98","./ar-ps.js":"4c98","./ar-sa":"8230","./ar-sa.js":"8230","./ar-tn":"6d83","./ar-tn.js":"6d83","./ar.js":"8e73","./az":"485c","./az.js":"485c","./be":"1fc1","./be.js":"1fc1","./bg":"84aa","./bg.js":"84aa","./bm":"a7fa","./bm.js":"a7fa","./bn":"9043","./bn-bd":"9686","./bn-bd.js":"9686","./bn.js":"9043","./bo":"d26a","./bo.js":"d26a","./br":"6887","./br.js":"6887","./bs":"25548","./bs.js":"25548","./ca":"d716","./ca.js":"d716","./cs":"3c0d","./cs.js":"3c0d","./cv":"03ec","./cv.js":"03ec","./cy":"9797","./cy.js":"9797","./da":"0f14","./da.js":"0f14","./de":"b469","./de-at":"b3eb","./de-at.js":"b3eb","./de-ch":"bb71","./de-ch.js":"bb71","./de.js":"b469","./dv":"598a","./dv.js":"598a","./el":"8d47","./el.js":"8d47","./en-au":"0e6b","./en-au.js":"0e6b","./en-ca":"3886","./en-ca.js":"3886","./en-gb":"39a6","./en-gb.js":"39a6","./en-ie":"e1d3","./en-ie.js":"e1d3","./en-il":"7333","./en-il.js":"7333","./en-in":"ec2e","./en-in.js":"ec2e","./en-nz":"6f50","./en-nz.js":"6f50","./en-sg":"b7e9","./en-sg.js":"b7e9","./eo":"65db","./eo.js":"65db","./es":"898b","./es-do":"0a3c","./es-do.js":"0a3c","./es-mx":"b5b7","./es-mx.js":"b5b7","./es-us":"55c9","./es-us.js":"55c9","./es.js":"898b","./et":"ec18","./et.js":"ec18","./eu":"0ff2","./eu.js":"0ff2","./fa":"8df48","./fa.js":"8df48","./fi":"81e9","./fi.js":"81e9","./fil":"d69a","./fil.js":"d69a","./fo":"0721","./fo.js":"0721","./fr":"9f26","./fr-ca":"d9f8","./fr-ca.js":"d9f8","./fr-ch":"0e49","./fr-ch.js":"0e49","./fr.js":"9f26","./fy":"7118","./fy.js":"7118","./ga":"5120","./ga.js":"5120","./gd":"f6b46","./gd.js":"f6b46","./gl":"8840","./gl.js":"8840","./gom-deva":"aaf2","./gom-deva.js":"aaf2","./gom-latn":"0caa","./gom-latn.js":"0caa","./gu":"e0c5","./gu.js":"e0c5","./he":"c7aa","./he.js":"c7aa","./hi":"dc4d","./hi.js":"dc4d","./hr":"4ba9","./hr.js":"4ba9","./hu":"5b14","./hu.js":"5b14","./hy-am":"d6b6","./hy-am.js":"d6b6","./id":"5038","./id.js":"5038","./is":"0558","./is.js":"0558","./it":"6e98","./it-ch":"6f12","./it-ch.js":"6f12","./it.js":"6e98","./ja":"079e","./ja.js":"079e","./jv":"b540","./jv.js":"b540","./ka":"201b","./ka.js":"201b","./kk":"6d79","./kk.js":"6d79","./km":"e81d","./km.js":"e81d","./kn":"3e92","./kn.js":"3e92","./ko":"22f8","./ko.js":"22f8","./ku":"2421","./ku-kmr":"7558","./ku-kmr.js":"7558","./ku.js":"2421","./ky":"9609","./ky.js":"9609","./lb":"440c","./lb.js":"440c","./lo":"b29d","./lo.js":"b29d","./lt":"26f9","./lt.js":"26f9","./lv":"b97c","./lv.js":"b97c","./me":"293c","./me.js":"293c","./mi":"688b","./mi.js":"688b","./mk":"6909","./mk.js":"6909","./ml":"02fb","./ml.js":"02fb","./mn":"958b","./mn.js":"958b","./mr":"39bd","./mr.js":"39bd","./ms":"ebe4","./ms-my":"6403","./ms-my.js":"6403","./ms.js":"ebe4","./mt":"1b45","./mt.js":"1b45","./my":"8689","./my.js":"8689","./nb":"6ce3","./nb.js":"6ce3","./ne":"3a39","./ne.js":"3a39","./nl":"facd","./nl-be":"db29","./nl-be.js":"db29","./nl.js":"facd","./nn":"b84c","./nn.js":"b84c","./oc-lnc":"167b","./oc-lnc.js":"167b","./pa-in":"f3ff","./pa-in.js":"f3ff","./pl":"8d57","./pl.js":"8d57","./pt":"f260","./pt-br":"d2d4","./pt-br.js":"d2d4","./pt.js":"f260","./ro":"972c","./ro.js":"972c","./ru":"957c","./ru.js":"957c","./sd":"6784","./sd.js":"6784","./se":"ffff","./se.js":"ffff","./si":"eda5","./si.js":"eda5","./sk":"7be6","./sk.js":"7be6","./sl":"8155","./sl.js":"8155","./sq":"c8f3","./sq.js":"c8f3","./sr":"cf1e","./sr-cyrl":"13e9","./sr-cyrl.js":"13e9","./sr.js":"cf1e","./ss":"52bd","./ss.js":"52bd","./sv":"5fbd","./sv.js":"5fbd","./sw":"74dc","./sw.js":"74dc","./ta":"3de5","./ta.js":"3de5","./te":"5cbb","./te.js":"5cbb","./tet":"576c","./tet.js":"576c","./tg":"3b1b","./tg.js":"3b1b","./th":"10e8","./th.js":"10e8","./tk":"5aff","./tk.js":"5aff","./tl-ph":"0f38","./tl-ph.js":"0f38","./tlh":"cf75","./tlh.js":"cf75","./tr":"0e81","./tr.js":"0e81","./tzl":"cf51","./tzl.js":"cf51","./tzm":"c109","./tzm-latn":"b53d","./tzm-latn.js":"b53d","./tzm.js":"c109","./ug-cn":"6117","./ug-cn.js":"6117","./uk":"ada2","./uk.js":"ada2","./ur":"5294","./ur.js":"5294","./uz":"2e8c","./uz-latn":"010e","./uz-latn.js":"010e","./uz.js":"2e8c","./vi":"2921","./vi.js":"2921","./x-pseudo":"fd7e","./x-pseudo.js":"fd7e","./yo":"7f33","./yo.js":"7f33","./zh-cn":"5c3a","./zh-cn.js":"5c3a","./zh-hk":"49ab","./zh-hk.js":"49ab","./zh-mo":"3a6c","./zh-mo.js":"3a6c","./zh-tw":"90ea","./zh-tw.js":"90ea"};function i(e){var t=r(e);return a(t)}function r(e){if(!a.o(n,e)){var t=new Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}return n[e]}i.keys=function(){return Object.keys(n)},i.resolve=r,e.exports=i,i.id="4678"},"6f53":function(e,t,a){var n=a("83ab"),i=a("e330"),r=a("df75"),l=a("fc6a"),s=a("d1e7").f,o=i(s),c=i([].push),d=function(e){return function(t){var a,i=l(t),s=r(i),d=s.length,f=0,u=[];while(d>f)a=s[f++],n&&!o(i,a)||c(u,e?[a,i[a]]:i[a]);return u}};e.exports={entries:d(!0),values:d(!1)}},c211:function(e,t,a){"use strict";a.d(t,"c",(function(){return i})),a.d(t,"b",(function(){return r})),a.d(t,"a",(function(){return l}));var n=a("b775");function i(e){return Object(n["a"])({url:"/system/aggregatorconfigs",method:"post",data:e})}function r(e){return Object(n["a"])({url:"/system/aggregatorconfigs/list",method:"get",params:e})}function l(e){return Object(n["a"])({url:"/system/aggregatorconfigs/".concat(e),method:"delete"})}},d3da:function(e,t,a){"use strict";a("2ce2")},de7d:function(e,t,a){"use strict";a.r(t);var n=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"data-aggregator"},[a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:(e.showResult,10)}},[a("el-card",{staticClass:"config-card",scopedSlots:e._u([{key:"header",fn:function(){return[a("div",{staticClass:"header-with-operations"},[a("span",[e._v("汇总配置")])])]},proxy:!0}])},[a("el-form",{staticClass:"edit",attrs:{"label-width":"80px"}},[a("el-form-item",{attrs:{label:"速查名称",required:""}},[a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:18}},[a("el-input",{attrs:{placeholder:"请输入速查名称"},model:{value:e.config.name,callback:function(t){e.$set(e.config,"name",t)},expression:"config.name"}})],1),a("el-col",{attrs:{span:5}},[a("el-button",{attrs:{size:"small",type:"text"},on:{click:e.saveConfig}},[e._v("[↗]")]),a("el-button",{attrs:{size:"small",type:"text"},on:{click:e.loadConfigs}},[e._v("[...]")])],1)],1)],1),a("el-form-item",{attrs:{label:"分组依据",required:""}},[a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:8}},[a("el-select",{attrs:{clearable:"",filterable:"",placeholder:"操作单号"},model:{value:e.config.primaryField,callback:function(t){e.$set(e.config,"primaryField",t)},expression:"config.primaryField"}},e._l(e.availableFields,(function(t){return a("el-option",{key:t,attrs:{label:e.getFieldLabel(t),value:t}})})),1)],1),a("el-col",{attrs:{span:15}},[a("el-checkbox",{model:{value:e.config.matchOptions.exact,callback:function(t){e.$set(e.config.matchOptions,"exact",t)},expression:"config.matchOptions.exact"}},[e._v("精确匹配")]),a("el-checkbox",{model:{value:e.config.matchOptions.caseSensitive,callback:function(t){e.$set(e.config.matchOptions,"caseSensitive",t)},expression:"config.matchOptions.caseSensitive"}},[e._v("区分大小写")])],1)],1)],1),a("el-form-item",{attrs:{label:"分组日期"}},[a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:8}},[a("el-select",{attrs:{clearable:"",filterable:"",placeholder:"分组日期"},model:{value:e.config.dateField,callback:function(t){e.$set(e.config,"dateField",t)},expression:"config.dateField"}},e._l(e.dateFields,(function(t){return a("el-option",{key:t,attrs:{label:e.getFieldLabel(t),value:t}})})),1)],1),a("el-col",{attrs:{span:15}},[a("el-checkbox",{model:{value:e.config.dateOptions.convertToNumber,callback:function(t){e.$set(e.config.dateOptions,"convertToNumber",t)},expression:"config.dateOptions.convertToNumber"}},[e._v("转换为数字")]),a("el-radio-group",{staticStyle:{display:"flex","line-height":"26px"},model:{value:e.config.dateOptions.formatType,callback:function(t){e.$set(e.config.dateOptions,"formatType",t)},expression:"config.dateOptions.formatType"}},[a("el-radio",{attrs:{label:"year"}},[e._v("按年")]),a("el-radio",{attrs:{label:"month"}},[e._v("按月")]),a("el-radio",{attrs:{label:"day"}},[e._v("按天")])],1)],1)],1)],1),a("el-form-item",{attrs:{label:"显示方式"}},[a("el-checkbox",{staticStyle:{"padding-left":"5px"},model:{value:e.config.showDetails,callback:function(t){e.$set(e.config,"showDetails",t)},expression:"config.showDetails"}},[e._v("含明细")]),a("el-switch",{attrs:{"active-text":"区分币种"},model:{value:e.config.splitByCurrency,callback:function(t){e.$set(e.config,"splitByCurrency",t)},expression:"config.splitByCurrency"}})],1),a("el-table",{staticStyle:{width:"100%"},attrs:{data:e.config.fields,border:""}},[a("el-table-column",{attrs:{align:"center",label:"序号",type:"index",width:"60"}}),a("el-table-column",{attrs:{label:"表头名称","min-width":"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-select",{staticStyle:{width:"100%"},attrs:{filterable:"",placeholder:"选择字段"},on:{change:function(a){return e.handleFieldSelect(t.$index)}},model:{value:t.row.fieldKey,callback:function(a){e.$set(t.row,"fieldKey",a)},expression:"scope.row.fieldKey"}},e._l(e.fieldLabelMap,(function(e,t){return a("el-option",{key:t,attrs:{label:e.name,value:t}})})),1)]}}])}),a("el-table-column",{attrs:{label:"排序",width:"80"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-select",{staticStyle:{width:"100%"},model:{value:t.row.sort,callback:function(a){e.$set(t.row,"sort",a)},expression:"scope.row.sort"}},[a("el-option",{attrs:{label:"-",value:"none"}}),a("el-option",{attrs:{label:"∧",value:"asc"}}),a("el-option",{attrs:{label:"∨ ",value:"desc"}})],1)]}}])}),a("el-table-column",{attrs:{label:"汇总方式",width:"80"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-select",{staticStyle:{width:"100%"},attrs:{disabled:!e.isAggregatable(t.row.fieldKey)},model:{value:t.row.aggregation,callback:function(a){e.$set(t.row,"aggregation",a)},expression:"scope.row.aggregation"}},[a("el-option",{attrs:{label:"-",value:"none"}}),a("el-option",{attrs:{label:"求和",value:"sum"}}),a("el-option",{attrs:{label:"平均值",value:"avg"}}),a("el-option",{attrs:{label:"最大值",value:"max"}}),a("el-option",{attrs:{label:"最小值",value:"min"}}),a("el-option",{attrs:{label:"方差",value:"variance"}})],1)]}}])}),a("el-table-column",{attrs:{label:"显示格式",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-select",{staticStyle:{width:"100%"},model:{value:t.row.format,callback:function(a){e.$set(t.row,"format",a)},expression:"scope.row.format"}},[a("el-option",{attrs:{label:"-",value:"none"}}),"date"===e.getFieldDisplay(t.row.fieldKey)?[a("el-option",{attrs:{label:"YYYYMM",value:"YYYYMM"}}),a("el-option",{attrs:{label:"MM-DD",value:"MM-DD"}}),a("el-option",{attrs:{label:"YYYY-MM-DD",value:"YYYY-MM-DD"}})]:e._e(),"number"===e.getFieldDisplay(t.row.fieldKey)?[a("el-option",{attrs:{label:"0.00",value:"decimal"}}),a("el-option",{attrs:{label:"0.00%",value:"percent"}}),a("el-option",{attrs:{label:"¥0.00",value:"currency"}}),a("el-option",{attrs:{label:"$0.00",value:"usd"}})]:e._e()],2)]}}])}),a("el-table-column",{attrs:{align:"center",label:"操作",width:"120"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button-group",[a("el-button",{attrs:{disabled:0===t.$index,size:"mini",type:"text"},on:{click:function(a){return e.moveField(t.$index,"up")}}},[e._v("[∧] ")]),a("el-button",{attrs:{disabled:t.$index===e.config.fields.length-1,size:"mini",type:"text"},on:{click:function(a){return e.moveField(t.$index,"down")}}},[e._v("[∨] ")]),a("el-button",{staticStyle:{color:"red"},attrs:{icon:"el-icon-delete",size:"mini",type:"text"},on:{click:function(a){return e.removeField(t.$index)}}})],1)]}}])})],1),a("div",{staticStyle:{"margin-top":"10px"}},[a("el-button",{attrs:{plain:"",type:"text"},on:{click:e.addField}},[e._v("[ + ]")])],1),a("el-form-item",[a("el-button",{attrs:{type:"primary"},on:{click:e.handleAggregate}},[e._v("分类汇总")]),a("el-button",{on:{click:e.resetConfig}},[e._v("重置")])],1)],1)],1)],1),e.showResult?a("el-col",{attrs:{span:14}},[a("el-card",{staticClass:"result-card",scopedSlots:e._u([{key:"header",fn:function(){return[a("div",{staticClass:"header-with-operations"},[a("span",[e._v("汇总结果")]),a("div",{staticClass:"operations"},[a("el-switch",{staticStyle:{"margin-right":"15px"},attrs:{"active-text":"横向","inactive-text":"纵向"},model:{value:e.isLandscape,callback:function(t){e.isLandscape=t},expression:"isLandscape"}}),a("el-button",{attrs:{size:"small"},on:{click:e.printTable}},[e._v("打印")]),a("el-button",{attrs:{size:"small",type:"primary"},on:{click:e.exportToPDF}},[e._v("导出PDF")])],1)])]},proxy:!0}],null,!1,1080603383)},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],ref:"resultTable",staticStyle:{width:"100%"},attrs:{data:e.processedData,border:"","summary-method":e.getSummary,"show-summary":""}},[a("el-table-column",{attrs:{align:e.config.primaryField?e.fieldLabelMap[e.config.primaryField].align:"left",label:e.groupFieldName,width:e.config.primaryField?e.fieldLabelMap[e.config.primaryField].width:""},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(e.formatGroupKey(t.row.groupKey))+" ")]}}],null,!1,2877943199)}),e._l(e.config.fields,(function(t){return[t.fieldKey?a("el-table-column",{key:t.fieldKey,attrs:{align:e.fieldLabelMap[t.fieldKey].align,label:e.getResultLabel(t),width:e.fieldLabelMap[t.fieldKey].width},scopedSlots:e._u([{key:"default",fn:function(a){return[e._v(" "+e._s(e.formatCellValue(a.row[e.getResultProp(t)],t))+" ")]}}],null,!0)}):e._e()]}))],2)],1)],1):e._e()],1),a("el-dialog",{attrs:{visible:e.configDialogVisible,"append-to-body":"",title:"加载配置",width:"500px"},on:{"update:visible":function(t){e.configDialogVisible=t}}},[a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.configLoading,expression:"configLoading"}],staticStyle:{width:"100%"},attrs:{data:e.savedConfigs},on:{"row-click":e.handleConfigSelect}},[a("el-table-column",{attrs:{label:"配置名称",prop:"name"}}),a("el-table-column",{attrs:{label:"创建时间",prop:"createTime"}}),a("el-table-column",{attrs:{width:"120"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{attrs:{type:"text"},on:{click:function(a){return a.stopPropagation(),e.deleteConfig(t.row)}}},[e._v("删除")])]}}])})],1)],1)],1)},i=[],r=a("5530"),l=a("c7eb"),s=a("1da1"),o=a("2909"),c=a("53ca"),d=(a("4de4"),a("d3b7"),a("b64b"),a("99af"),a("159b"),a("d81d"),a("a9e3"),a("13d5"),a("b680"),a("b0c0"),a("498a"),a("25f0"),a("14d9"),a("07ac"),a("7db0"),a("4e82"),a("d9e2"),a("ac1f"),a("5319"),a("a434"),a("c1df")),f=a.n(d),u=(a("72f9"),a("c211")),g=a("d67e"),b=a.n(g),p={name:"DataAggregator",props:{dataSource:{type:Array,required:!0},fieldLabelMap:{type:Object,required:!0,default:function(){return{}}}},data:function(){return{configName:"",config:{name:"",primaryField:"",matchOptions:{exact:!0,caseSensitive:!1},dateField:"",dateOptions:{convertToNumber:!1,formatType:"day"},showDetails:!1,fields:[],splitByCurrency:!1},dateOptions:[{label:"按年",value:"year"},{label:"按月",value:"month"},{label:"按周",value:"week"},{label:"按日",value:"day"},{label:"按时",value:"hour"},{label:"按分",value:"minute"}],aggregationOptions:[{label:"计数",value:"count"},{label:"求和",value:"sum"},{label:"平均值",value:"avg"},{label:"方差",value:"variance"},{label:"最大值",value:"max"},{label:"最小值",value:"min"}],loading:!1,configDialogVisible:!1,savedConfigs:[],configLoading:!1,isLandscape:!1,showResult:!1,processedData:[]}},computed:{availableFields:function(){var e=this;return 0===this.dataSource.length?[]:Object.keys(this.dataSource[0]).filter((function(t){return t in e.fieldLabelMap}))},numericFields:function(){var e=this;return this.availableFields.filter((function(t){return"number"===typeof e.dataSource[0][t]}))},currentFieldType:function(){if(!this.config.primaryField||!this.dataSource.length)return null;var e=this.dataSource[0][this.config.primaryField];return f()(e,f.a.ISO_8601,!0).isValid()?"date":Object(c["a"])(e)},groupFieldName:function(){return this.config.primaryField&&this.config.dateField?"".concat(this.getFieldLabel(this.config.dateField),"+").concat(this.getFieldLabel(this.config.primaryField)):this.getFieldLabel(this.config.primaryField)},dateFields:function(){var e=this;return this.availableFields.filter((function(t){if(e.fieldLabelMap[t]&&"date"===e.fieldLabelMap[t].display)return!0}))}},methods:{getSummary:function(e){var t=this,a=e.columns,n=e.data,i=[];return a.forEach((function(e,a){if(0!==a){var r=t.config.fields[a-1];if(r&&r.fieldKey)if(r.aggregation&&"none"!==r.aggregation){var l=t.fieldLabelMap[r.fieldKey];if(l&&"number"===l.display){var s=n.map((function(e){var a=t.getResultProp(r);return Number(e[a])})).filter((function(e){return!isNaN(e)}));if(0!==s.length){var c=0;switch(r.aggregation){case"sum":c=s.reduce((function(e,t){return e+t}),0);break;case"avg":c=s.reduce((function(e,t){return e+t}),0)/s.length;break;case"max":c=Math.max.apply(Math,Object(o["a"])(s));break;case"min":c=Math.min.apply(Math,Object(o["a"])(s));break;case"variance":var d=s.reduce((function(e,t){return e+t}),0)/s.length;c=s.reduce((function(e,t){return e+Math.pow(t-d,2)}),0)/s.length;break;default:c=s.reduce((function(e,t){return e+t}),0)}"decimal"===r.format?i[a]=c.toFixed(2):"percent"===r.format?i[a]=(100*c).toFixed(2)+"%":"currency"===r.format?i[a]="¥"+c.toFixed(2):"usd"===r.format?i[a]="$"+c.toFixed(2):i[a]=c}else i[a]=""}else i[a]=""}else i[a]="";else i[a]=""}else i[a]="合计"})),i},getName:function(e){if(null!==e){var t=this.$store.state.data.allRsStaffList.filter((function(t){return t.staffId==e}))[0];if(t&&void 0!==t)return t.staffShortName+t.staffFamilyEnName}return""},getFieldLabel:function(e){var t;return(null===(t=this.fieldLabelMap[e])||void 0===t?void 0:t.name)||e},groupData:function(){var e=this,t={};return this.dataSource.forEach((function(a){if(!e.config.dateField||a[e.config.dateField]){var n=a[e.config.primaryField];"string"===typeof n&&(e.config.matchOptions.caseSensitive||(n=n.toLowerCase()),e.config.matchOptions.exact&&(n=n.trim()));var i=n;if(e.config.dateField&&a[e.config.dateField]){var r,l=f()(a[e.config.dateField]);r=e.config.dateOptions.formatType?l.format(e.getDateFormat()):e.config.dateOptions.convertToNumber?l.valueOf():l.format("YYYY-MM-DD"),i={primary:n,date:r,toString:function(){return"".concat(this.date,"_").concat(this.primary)}}}var s=i.toString?i.toString():i;t[s]||(t[s]={items:[],groupKey:i}),t[s].items.push(a)}})),t},calculateAggregations:function(e){var t=this;return Object.values(e).map((function(e){var a=e.items||[],n=e.groupKey,i={groupKey:n};return t.config.fields.forEach((function(e){if(e.fieldKey){var n=t.fieldLabelMap[e.fieldKey];if(n){var r=a.map((function(t){return t[e.fieldKey]})),l=t.getResultProp(e),s=n.display&&"function"===typeof t[n.display];if(s)i[l]=r[0];else switch(n.display){case"number":var c=r.filter((function(e){return null!=e})).map((function(e){return Number(e)})).filter((function(e){return!isNaN(e)}));if(0===c.length)return void(i[l]=null);switch(e.aggregation){case"sum":i[l]=c.reduce((function(e,t){return Number((e+t).toFixed(2))}),0);break;case"avg":i[l]=Number((c.reduce((function(e,t){return e+t}),0)/c.length).toFixed(2));break;case"max":i[l]=Math.max.apply(Math,Object(o["a"])(c));break;case"min":i[l]=Math.min.apply(Math,Object(o["a"])(c));break;case"variance":if(c.length>0){var d=c.reduce((function(e,t){return e+t}),0)/c.length;i[l]=Number((c.reduce((function(e,t){return e+Math.pow(t-d,2)}),0)/c.length).toFixed(2))}else i[l]=0;break;case"none":default:i[l]=r[0]}break;case"date":case"text":case"boolean":default:i[l]=r[0]}}}})),i}))},handleAggregate:function(){if(this.config.primaryField)if(this.config.fields.length)try{this.loading=!0;var e=this.groupData();this.processedData=this.calculateAggregations(e),this.applySorting(),this.showResult=!0}catch(t){this.$message.error("汇总处理失败："+t.message)}finally{this.loading=!1}else this.$message.warning("请添加要汇总的字段");else this.$message.warning("请选择分组依据字段")},applySorting:function(){var e=this.config.fields.find((function(e){return"none"!==e.sort}));if(e){var t=this.getResultProp(e),a="asc"===e.sort,n=this.fieldLabelMap[e.fieldKey];n&&this.processedData.sort((function(e,i){var r=e[t],l=i[t];switch(n.display){case"number":r=Number(r)||0,l=Number(l)||0;break;case"date":r=r?new Date(r).getTime():0,l=l?new Date(l).getTime():0;break;case"boolean":r=r?1:0,l=l?1:0;break;default:r=String(r||""),l=String(l||"")}return a?r>l?1:r<l?-1:0:r<l?1:r>l?-1:0}))}},handleFieldChange:function(){this.config.selectedFields=[],this.config.displayFields=[]},saveConfig:function(){var e=this;return Object(s["a"])(Object(l["a"])().mark((function t(){var a,n;return Object(l["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(t.prev=0,e.config.name){t.next=4;break}return e.$message.warning("请输入速查名称"),t.abrupt("return");case 4:if(e.config.primaryField){t.next=7;break}return e.$message.warning("请选择分组依据字段"),t.abrupt("return");case 7:if(e.config.fields.length){t.next=10;break}return e.$message.warning("请添加至少一个字段"),t.abrupt("return");case 10:if(a=e.config.fields.find((function(e){return!e.fieldKey})),!a){t.next=14;break}return e.$message.warning("请完成所有字段的配置"),t.abrupt("return");case 14:return n={name:e.config.name,type:"Aggregator",config:{primaryField:e.config.primaryField,matchOptions:e.config.matchOptions,dateField:e.config.dateField,dateOptions:e.config.dateOptions,showDetails:e.config.showDetails,fields:e.config.fields}},t.next=17,Object(u["c"])(n);case 17:e.$message.success("配置保存成功"),t.next=23;break;case 20:t.prev=20,t.t0=t["catch"](0),"cancel"!==t.t0&&e.$message.error("保存配置失败："+(t.t0.message||"未知错误"));case 23:case"end":return t.stop()}}),t,null,[[0,20]])})))()},loadConfigs:function(){var e=this;return Object(s["a"])(Object(l["a"])().mark((function t(){var a,n,i;return Object(l["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.configLoading=!0,e.configDialogVisible=!0,t.prev=2,t.next=5,Object(u["b"])({configType:"Aggregator"});case 5:if(a=t.sent,n=a.rows,Array.isArray(n)){t.next=9;break}throw new Error("返回数据格式错误");case 9:e.savedConfigs=n.map((function(e){return{id:e.id,name:e.name,createTime:e.createTime,config:e.config||{primaryField:"",secondaryField:"",textMatchMode:"exact",caseSensitive:!1,dateGranularity:"day",aggregationMethods:["count","sum"],showDetails:!1,selectedFields:[],displayFields:[]}}})),e.configDialogVisible=!0,t.next=17;break;case 13:t.prev=13,t.t0=t["catch"](2),console.error("加载配置失败:",t.t0),e.$message.error((null===(i=t.t0.response)||void 0===i||null===(i=i.data)||void 0===i?void 0:i.message)||t.t0.message||"加载配置列表失败，请稍后重试");case 17:return t.prev=17,e.configLoading=!1,t.finish(17);case 20:case"end":return t.stop()}}),t,null,[[2,13,17,20]])})))()},handleConfigSelect:function(e){var t=this;return Object(s["a"])(Object(l["a"])().mark((function a(){var n;return Object(l["a"])().wrap((function(a){while(1)switch(a.prev=a.next){case 0:try{n={primaryField:"",secondaryField:"",textMatchMode:"exact",caseSensitive:!1,dateGranularity:"day",aggregationMethods:["count","sum"],showDetails:!1,selectedFields:[],displayFields:[]},t.config=Object(r["a"])(Object(r["a"])(Object(r["a"])({},n),JSON.parse(e.config)),{},{name:e.name}),t.configDialogVisible=!1,t.$message.success("配置加载成功"),t.$nextTick((function(){console.log("配置已加载:",t.config)}))}catch(i){console.error("加载配置失败:",i),t.$message.error("加载配置失败："+i.message)}case 1:case"end":return a.stop()}}),a)})))()},deleteConfig:function(e){var t=this;return Object(s["a"])(Object(l["a"])().mark((function a(){return Object(l["a"])().wrap((function(a){while(1)switch(a.prev=a.next){case 0:return a.prev=0,a.next=3,t.$confirm("确认删除该配置？","提示",{type:"warning"});case 3:return a.next=5,Object(u["a"])(e.id);case 5:t.savedConfigs=t.savedConfigs.filter((function(t){return t.id!==e.id})),t.$message.success("配置删除成功"),a.next=12;break;case 9:a.prev=9,a.t0=a["catch"](0),"cancel"!==a.t0&&t.$message.error("删除配置失败："+a.t0.message);case 12:case"end":return a.stop()}}),a,null,[[0,9]])})))()},printTable:function(){var e=window.open("","_blank"),t=this.$refs.resultTable.$el.cloneNode(!0),a="",n=((new Date).toLocaleDateString(),'\n        <div class="company-header">\n          <div class="company-logo">\n            <img src="/logo.png" alt="Rich Shipping Logo" />\n            <div class="company-name">\n              <div class="company-name-cn">广州瑞旗国际货运代理有限公司</div>\n              <div class="company-name-en">GUANGZHOU RICH SHIPPING INT\'L CO.,LTD.</div>\n            </div>\n          </div>\n          <div class="document-title">\n            <div class="title-cn">对账单汇总</div>\n            <div class="title-en">[DEBIT NOTE]</div>\n          </div>\n        </div>\n      ');e.document.write('\n        <html lang="">\n          <head>\n            <title>'.concat(a,"</title>\n            <style>\n          /* 基础样式 */\n          body {\n            margin: 0;\n            padding: 0;\n            font-family: Arial, sans-serif;\n          }\n\n          /* 打印样式 - 必须放在这里才能生效 */\n          @media print {\n            @page {\n              size: ").concat(this.isLandscape?"landscape":"portrait",';\n              margin: 1.5cm 1cm 1cm 1cm;\n            }\n\n            /* 重要：使用重复表头技术 */\n            thead {\n              display: table-header-group;\n            }\n\n            /* 页眉作为表格的一部分，放在thead中 */\n            .page-header {\n              display: table-header-group;\n            }\n\n            /* 内容部分 */\n            .page-content {\n              display: table-row-group;\n            }\n\n            /* 页脚 */\n            tfoot {\n              display: table-footer-group;\n            }\n\n            /* 避免元素内部分页 */\n            .company-header, .header-content {\n              page-break-inside: avoid;\n            }\n\n            /* 表格样式 */\n            table.main-table {\n              width: 100%;\n              border-collapse: collapse;\n              border: none;\n            }\n\n            /* 确保表头在每页都显示 */\n            table.data-table thead {\n              display: table-header-group;\n            }\n\n            /* 避免行内分页 */\n            table.data-table tr {\n              page-break-inside: avoid;\n            }\n          }\n\n          /* 表格样式 */\n          table.data-table {\n            border-collapse: collapse;\n            width: 100%;\n            margin-top: 20px;\n          }\n\n          table.data-table th, table.data-table td {\n            border: 1px solid #ddd;\n            padding: 8px;\n            text-align: left;\n            font-size: 12px;\n          }\n\n          table.data-table th {\n            background-color: #f2f2f2;\n          }\n\n          /* Element UI 表格样式模拟 */\n          .el-table {\n            border-collapse: collapse;\n            width: 100%;\n          }\n\n          .el-table th, .el-table td {\n            border: 1px solid #ddd;\n            padding: 8px;\n            text-align: left;\n            font-size: 12px;\n          }\n\n          .el-table th {\n            background-color: #f2f2f2;\n            font-weight: bold;\n          }\n\n          .el-table__footer {\n            background-color: #f8f8f9;\n            font-weight: bold;\n          }\n\n          .el-table__footer td {\n            border: 1px solid #ddd;\n            padding: 8px;\n          }\n\n          /* 公司标题和标志样式 */\n          .company-header {\n            display: flex;\n            justify-content: space-between;\n            align-items: center;\n            border-bottom: 2px solid #000;\n            padding-bottom: 10px;\n            width: 100%;\n          }\n\n          .company-logo {\n            display: flex;\n            align-items: center;\n          }\n\n          .company-logo img {\n            height: 50px;\n            margin-right: 10px;\n          }\n\n          .company-name {\n            display: flex;\n            flex-direction: column;\n          }\n\n          .company-name-cn {\n            font-size: 18px;\n            font-weight: bold;\n            color: #ff0000;\n          }\n\n          .company-name-en {\n            font-size: 14px;\n          }\n\n          .document-title {\n            text-align: right;\n          }\n\n          .title-cn {\n            font-size: 18px;\n            font-weight: bold;\n          }\n\n          .title-en {\n            font-size: 16px;\n            font-weight: bold;\n          }\n\n          /* 清除表格边框 */\n          table.main-table, table.main-table td {\n            border: none;\n          }\n\n          /* 页眉容器 */\n          .header-container {\n            width: 100%;\n            margin-bottom: 20px;\n          }\n\n          /* 日期信息 */\n          .date-info {\n            text-align: right;\n            margin-top: 10px;\n            margin-bottom: 20px;\n          }\n        </style>\n          </head>\n          <body>\n            \x3c!-- 使用表格布局确保页眉在每页重复 --\x3e\n            <table class="main-table">\n              <thead class="page-header">\n                <tr>\n                  <td>\n                    <div class="header-container">\n                      ').concat(n,'\n                    </div>\n                  </td>\n                </tr>\n              </thead>\n              <tbody class="page-content">\n                <tr>\n                  <td>\n                    \x3c!-- 保留原始表格的类名并添加data-table类 --\x3e\n                    ').concat(t.outerHTML.replace("<table",'<table class="el-table data-table"'),"\n                  </td>\n                </tr>\n              </tbody>\n              <tfoot>\n                <tr>\n                  <td></td>\n                </tr>\n              </tfoot>\n            </table>\n          </body>\n        </html>\n      ")),e.document.close(),setTimeout((function(){try{e.focus(),e.print()}catch(t){console.error("打印过程中发生错误:",t)}}),1e3)},exportToPDF:function(){var e=this;return Object(s["a"])(Object(l["a"])().mark((function t(){var a,n;return Object(l["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.prev=0,e.loading=!0,a=e.$refs.resultTable.$el,n={margin:[.8,.8,.8,.8],filename:"汇总数据.pdf",image:{type:"jpeg",quality:.98},html2canvas:{scale:2},jsPDF:{unit:"in",format:"a3",orientation:e.isLandscape?"landscape":"portrait"},pagebreak:{mode:["avoid-all","css","legacy"]},header:[{text:"汇总数据",style:"headerStyle"},{text:(new Date).toLocaleDateString(),style:"headerStyle",alignment:"right"}],footer:{height:"20px",contents:{default:'<span style="float:right">{{page}}/{{pages}}</span>'}}},t.next=6,b()().set(n).from(a).save();case 6:e.$message.success("PDF导出成功"),t.next=12;break;case 9:t.prev=9,t.t0=t["catch"](0),e.$message.error("PDF导出失败："+t.t0.message);case 12:return t.prev=12,e.loading=!1,t.finish(12);case 15:case"end":return t.stop()}}),t,null,[[0,9,12,15]])})))()},addField:function(){this.config.fields.push({fieldKey:"",aggregation:"none",format:"none",sort:"none"})},removeField:function(e){this.config.fields.splice(e,1)},moveField:function(e,t){var a=Object(o["a"])(this.config.fields);if("up"===t&&e>0){var n=[a[e-1],a[e]];a[e]=n[0],a[e-1]=n[1]}else if("down"===t&&e<a.length-1){var i=[a[e+1],a[e]];a[e]=i[0],a[e+1]=i[1]}this.$set(this.config,"fields",a)},handleFieldSelect:function(e){var t=this.config.fields[e],a=this.fieldLabelMap[t.fieldKey];a&&(t.format=this.getDefaultFormat(a.display),t.aggregation=a.aggregated?"sum":"none",t.sort="none")},getFieldDisplay:function(e){var t=this.fieldLabelMap[e];return t?t.display&&"function"===typeof this[t.display]?"custom":t.display||"text":"text"},isAggregatable:function(e){var t=this.fieldLabelMap[e];return(null===t||void 0===t?void 0:t.aggregated)||!1},getDefaultFormat:function(e){switch(e){case"date":return"YYYY-MM-DD";case"number":return"decimal";default:return"none"}},resetConfig:function(){this.config={name:"",primaryField:"",matchOptions:{exact:!0,caseSensitive:!1},dateField:"",dateOptions:{convertToNumber:!1,formatType:"day"},showDetails:!1,fields:[]},this.showResult=!1},getResultProp:function(e){return e.aggregation&&"none"!==e.aggregation?"".concat(e.fieldKey,"_").concat(e.aggregation):e.fieldKey},getResultLabel:function(e){var t=this.getFieldLabel(e.fieldKey);if(e.aggregation&&"none"!==e.aggregation){var a,n=(null===(a=this.aggregationOptions.find((function(t){return t.value===e.aggregation})))||void 0===a?void 0:a.label)||e.aggregation;return"".concat(t,"(").concat(n,")")}return t},formatCellValue:function(e,t){if(null==e)return"-";var a=this.fieldLabelMap[t.fieldKey];if(!a)return e;if(a.display&&"function"===typeof this[a.display])return this[a.display](e);switch(a.display){case"number":var n=Number(e);if(isNaN(n))return"-";if(t.aggregation&&"none"!==t.aggregation&&("avg"===t.aggregation||"variance"===t.aggregation))return"percent"===t.format?(100*n).toFixed(2)+"%":n.toFixed(2);switch(t.format){case"decimal":return n.toFixed(2);case"percent":return(100*n).toFixed(2)+"%";case"currency":return"¥"+n.toFixed(2);case"usd":return"$"+n.toFixed(2);default:return n.toFixed(2)}case"date":return f()(e).format(t.format||"YYYY-MM-DD");case"boolean":return"avg"===t.aggregation?(100*Number(e)).toFixed(2)+"%":e?"是":"否";default:return e}},getDateFormat:function(){switch(this.config.dateOptions.formatType){case"year":return"YYYY";case"month":return"YYYY-MM";case"day":default:return"YYYY-MM-DD"}},formatGroupKey:function(e){if("object"===Object(c["a"])(e)&&null!==e&&void 0!==e.primary&&void 0!==e.date){var t=this.fieldLabelMap[this.config.primaryField],a=e.primary;return t&&t.display&&"function"===typeof this[t.display]&&(a=this[t.display](a)),"".concat(e.date).concat(a)}if(this.config.primaryField){var n=this.fieldLabelMap[this.config.primaryField];if(n&&n.display&&"function"===typeof this[n.display])return this[n.display](e)}return String(e||"")}}},m=p,h=(a("d3da"),a("2877")),v=Object(h["a"])(m,n,i,!1,null,"fd8b3134",null);t["default"]=v.exports}}]);