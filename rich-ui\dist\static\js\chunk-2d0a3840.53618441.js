(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d0a3840"],{"0307":function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:e.showLeft}},[a("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],ref:"queryForm",staticClass:"query",attrs:{inline:!0,model:e.queryParams,size:"mini"}},[a("el-form-item",{attrs:{label:"查询",prop:"chargeTypeQuery"}},[a("el-input",{staticStyle:{width:"100%"},attrs:{clearable:"",placeholder:"中英文，简称"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.chargeTypeQuery,callback:function(t){e.$set(e.queryParams,"chargeTypeQuery",t)},expression:"queryParams.chargeTypeQuery"}})],1),a("el-form-item",[a("el-button",{attrs:{icon:"el-icon-search",size:"mini",type:"primary"},on:{click:e.handleQuery}},[e._v("搜索")]),a("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1)],1),a("el-col",{attrs:{span:e.showRight}},[a("el-row",{staticClass:"mb8",attrs:{gutter:10}},[a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:chargetype:remove"],expression:"['system:chargetype:remove']"}],attrs:{disabled:e.multiple,icon:"el-icon-delete",plain:"",size:"mini",type:"danger"},on:{click:e.handleDelete}},[e._v("删除 ")])],1),a("right-toolbar",{attrs:{showSearch:e.showSearch},on:{"update:showSearch":function(t){e.showSearch=t},"update:show-search":function(t){e.showSearch=t},queryTable:e.getList}})],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.typeList,"tree-props":{children:"children",hasChildren:"hasChildren"},"row-key":"chargeTypeId"},on:{"selection-change":e.handleSelectionChange}},[a("el-table-column",{attrs:{type:"selection",width:"28",align:"center"}}),a("el-table-column",{key:"chargeTypeName",attrs:{align:"left",label:"费用类型名称",width:"350"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(t.row.chargeTypeShortName)+" "),a("a",{staticStyle:{margin:"0","font-weight":"bold","font-size":"small"}},[e._v(e._s(t.row.chargeTypeLocalName))]),e._v(" "+e._s(t.row.chargeTypeEnName)+" ")]}}])}),a("el-table-column",{key:"enterDepts",attrs:{align:"left",label:"录入部门",prop:"enterDepts","show-tooltip-when-overflow":"",width:"170"}}),a("el-table-column",{key:"checkDepts",attrs:{align:"left",label:"可显部门",prop:"checkDepts","show-tooltip-when-overflow":"",width:"170"}}),a("el-table-column",{key:"remark",attrs:{align:"left",label:"备注",prop:"remark","show-tooltip-when-overflow":""}}),a("el-table-column",{key:"orderNum",attrs:{align:"center",label:"优先级",prop:"orderNum",width:"58"}}),a("el-table-column",{key:"status",attrs:{align:"center",label:"状态",prop:"status",width:"68"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-switch",{attrs:{"active-value":"0","inactive-value":"1"},on:{change:function(a){return e.handleStatusChange(t.row)}},model:{value:t.row.status,callback:function(a){e.$set(t.row,"status",a)},expression:"scope.row.status"}})]}}])}),a("el-table-column",{attrs:{align:"center","class-name":"small-padding fixed-width",label:"操作",width:"170"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:chargetype:add"],expression:"['system:chargetype:add']"}],staticStyle:{"margin-right":"-8px"},attrs:{icon:"el-icon-plus",size:"mini",type:"primary"},on:{click:function(a){return e.handleAdd(t.row)}}},[e._v("新增 ")]),a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:chargetype:edit"],expression:"['system:chargetype:edit']"}],staticStyle:{"margin-right":"-8px"},attrs:{icon:"el-icon-edit",size:"mini",type:"success"},on:{click:function(a){return e.handleUpdate(t.row)}}},[e._v("修改 ")]),0!=t.row.parentId?a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:chargetype:remove"],expression:"['system:chargetype:remove']"}],staticStyle:{"margin-right":"-8px"},attrs:{icon:"el-icon-delete",size:"mini",type:"danger"},on:{click:function(a){return e.handleDelete(t.row)}}},[e._v("删除 ")]):e._e()]}}])})],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total>0"}],attrs:{limit:e.queryParams.pageSize,page:e.queryParams.pageNum,total:e.total},on:{"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},pagination:e.getList}})],1)],1),a("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"},{name:"dialogDragWidth",rawName:"v-dialogDragWidth"}],attrs:{"close-on-click-modal":!1,"modal-append-to-body":!1,title:e.title,visible:e.open,"append-to-body":"",width:"500px"},on:{"update:visible":function(t){e.open=t}}},[a("el-form",{ref:"form",staticClass:"edit",attrs:{model:e.form,rules:e.rules,"label-width":"80px"}},[0!=e.form.parentId?a("el-form-item",{attrs:{label:"上级类型",prop:"parentId"}},[a("tree-select",{attrs:{multiple:!1,pass:e.form.parentId,placeholder:"上级类名",type:"chargeType",dbn:!1},on:{return:e.ParentId}})],1):e._e(),a("el-form-item",{attrs:{label:"简称",prop:"chargeTypeShortName"}},[a("el-input",{attrs:{placeholder:"简称"},model:{value:e.form.chargeTypeShortName,callback:function(t){e.$set(e.form,"chargeTypeShortName",t)},expression:"form.chargeTypeShortName"}})],1),a("el-form-item",{attrs:{label:"英文名",prop:"chargeTypeEnName"}},[a("el-input",{attrs:{placeholder:"英文名"},model:{value:e.form.chargeTypeEnName,callback:function(t){e.$set(e.form,"chargeTypeEnName",t)},expression:"form.chargeTypeEnName"}})],1),a("el-form-item",{attrs:{label:"中文名",prop:"chargeTypeLocalName"}},[a("el-input",{attrs:{placeholder:"中文名"},model:{value:e.form.chargeTypeLocalName,callback:function(t){e.$set(e.form,"chargeTypeLocalName",t)},expression:"form.chargeTypeLocalName"}})],1),a("el-form-item",{attrs:{label:"录入部门",prop:"enterDeptIds"}},[a("tree-select",{attrs:{multiple:!0,pass:e.form.enterDeptIds,placeholder:"适用的部门",type:"dept"},on:{return:e.getEnterDeptIds}})],1),a("el-form-item",{attrs:{label:"可显部门",prop:"checkDeptIds"}},[a("tree-select",{attrs:{multiple:!0,pass:e.form.checkDeptIds,placeholder:"适用的部门",type:"dept"},on:{return:e.getCheckDeptIds}})],1),a("el-form-item",{attrs:{label:"排序",prop:"orderNum"}},[a("el-input-number",{staticStyle:{width:"100%"},attrs:{controls:!1,placeholder:"排序"},model:{value:e.form.orderNum,callback:function(t){e.$set(e.form,"orderNum",t)},expression:"form.orderNum"}})],1),a("el-form-item",{attrs:{label:"备注",prop:"remark"}},[a("el-input",{attrs:{autosize:{minRows:5,maxRows:20},maxlength:"150",placeholder:"备注","show-word-limit":"",type:"textarea"},model:{value:e.form.remark,callback:function(t){e.$set(e.form,"remark",t)},expression:"form.remark"}})],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("确 定")]),a("el-button",{on:{click:e.cancel}},[e._v("取 消")])],1)],1)],1)},n=[],s=a("5530"),o=(a("d81d"),a("caad"),a("2532"),a("4de4"),a("d3b7"),a("7b06")),l={name:"Type",data:function(){return{showLeft:3,showRight:21,loading:!0,ids:[],single:!0,multiple:!0,showSearch:!0,total:0,typeList:[],title:"",open:!1,queryParams:{pageNum:1,pageSize:20,chargeTypeQuery:null},form:{},rules:{}}},watch:{showSearch:function(e){1==e?(this.showRight=21,this.showLeft=3):(this.showRight=24,this.showLeft=0)}},created:function(){this.getList()},methods:{getList:function(){var e=this;this.loading=!0,Object(o["e"])(this.queryParams).then((function(t){e.typeList=e.handleTree(t.data,"chargeTypeId"),e.loading=!1}))},cancel:function(){this.open=!1,this.reset()},reset:function(){this.form={chargeTypeId:null,chargeTypeShortName:null,chargeTypeEnName:null,chargeTypeLocalName:null,orderNum:null,remark:null},this.resetForm("form")},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.resetForm("queryForm"),this.handleQuery()},handleSelectionChange:function(e){this.ids=e.map((function(e){return e.chargeTypeId})),this.single=1!=e.length,this.multiple=!e.length},handleAdd:function(e){this.reset(),this.open=!0,void 0!=e&&(this.form.parentId=e.chargeTypeId),this.title="添加费用类型"},handleUpdate:function(e){var t=this;this.reset();var a=e.chargeTypeId||this.ids;Object(o["d"])(a).then((function(e){t.form=e.data,t.form.checkDeptIds=e.check,t.form.enterDeptIds=e.enter,t.open=!0,t.title="修改费用类型"}))},submitForm:function(){var e=this;this.$refs["form"].validate((function(t){t&&(null!=e.form.chargeTypeId?Object(o["h"])(e.form).then((function(t){e.$modal.msgSuccess("修改成功"),e.open=!1,e.getList()})):Object(o["a"])(e.form).then((function(t){e.$modal.msgSuccess("新增成功"),e.open=!1,e.getList()})))}))},handleDelete:function(e){var t=this,a=e.chargeTypeId||this.ids;this.$confirm('是否确认删除费用类型编号为"'+a+'"的数据项？',"提示",{customClass:"modal-confirm"}).then((function(){return Object(o["c"])(a)})).then((function(){t.getList(),t.$modal.msgSuccess("删除成功")})).catch((function(){}))},handleStatusChange:function(e){var t=this,a="0"==e.status?"启用":"停用";this.$confirm('确认要"'+a+'""'+e.chargeTypeLocalName+'"吗？',"提示",{customClass:"modal-confirm"}).then((function(){return Object(o["b"])(e.chargeTypeId,e.status)})).then((function(){t.$modal.msgSuccess(a+"成功")})).catch((function(){e.status="0"==e.status?"1":"0"}))},handleExport:function(){this.download("system/type/export",Object(s["a"])({},this.queryParams),"type_".concat((new Date).getTime(),".xlsx"))},ParentId:function(e){this.form.parentId=e},getCheckDeptIds:function(e){this.form.enterDeptIds.includes(e[e.length-1])?(this.$message.warning("录入部门已存在此部门，已可显"),this.form.checkDeptIds=e.filter((function(t){return t!=e[e.length-1]}))):this.form.checkDeptIds=e},getEnterDeptIds:function(e){this.form.enterDeptIds=e}}},i=l,c=a("2877"),h=Object(c["a"])(i,r,n,!1,null,null,null);t["default"]=h.exports}}]);