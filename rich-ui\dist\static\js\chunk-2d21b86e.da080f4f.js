(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d21b86e"],{bfc7:function(e,t,r){"use strict";r.r(t);var a=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",{staticClass:"app-container"},[r("el-row",{attrs:{gutter:20}},[r("el-col",{attrs:{span:e.showLeft}},[e.showSearch?r("el-form",{ref:"queryForm",staticClass:"query",attrs:{model:e.queryParams,size:"mini",inline:!0}},[r("el-form-item",{attrs:{label:"筛选"}},[r("el-select",{staticStyle:{width:"100%"},on:{change:e.handleQuery},model:{value:e.queryParams.accurate,callback:function(t){e.$set(e.queryParams,"accurate",t)},expression:"queryParams.accurate"}},[r("el-option",{attrs:{value:1,label:"相关所有"}},[e._v("相关所有")]),r("el-option",{attrs:{value:2,label:"向上所属"}},[e._v("向上所属")]),r("el-option",{attrs:{value:3,label:"向下包含"}},[e._v("向下包含")]),r("el-option",{attrs:{value:4,label:"精确匹配"}},[e._v("精确匹配")])],1)],1),r("el-form-item",{attrs:{label:"服务",prop:"serviceTypeIds"}},[r("tree-select",{staticStyle:{width:"100%"},attrs:{flat:!1,multiple:!1,pass:e.queryParams.serviceTypeId,placeholder:"服务类型",type:"serviceType"},on:{return:e.queryServiceTypeId}})],1),r("el-form-item",{attrs:{label:"货物",prop:"cargoTypeIds"}},[r("tree-select",{staticStyle:{width:"100%"},attrs:{flat:!1,multiple:!0,pass:e.queryParams.cargoTypeIds,placeholder:"货物特征",type:"cargoType"},on:{return:e.queryCargoTypeIds}})],1),r("el-form-item",{attrs:{label:"承运",prop:"carrierIds"}},[r("treeselect",{staticStyle:{width:"100%"},attrs:{"disable-fuzzy-matching":!0,"disable-branch-nodes":!0,flat:!0,"flatten-search-results":!0,multiple:!0,normalizer:e.carrierNormalizer,options:e.carrierList,"show-count":!0,placeholder:"承运人"},on:{input:e.deselectAllQueryCarrierIds,open:e.loadCarrier,deselect:e.handleDeselectQueryCarrierIds,select:e.handleSelectQueryCarrierIds},scopedSlots:e._u([{key:"value-label",fn:function(t){var a=t.node;return r("div",{},[e._v(" "+e._s(a.raw.carrier.carrierIntlCode)+" "+e._s(null==a.raw.carrier.carrierIntlCode?a.raw.carrier.carrierShortName:"")+" ")])}},{key:"option-label",fn:function(t){var a=t.node,i=t.shouldShowCount,l=t.count,s=t.labelClassName,o=t.countClassName;return r("label",{class:s},[e._v(" "+e._s(-1!=a.label.indexOf(",")?a.label.substring(0,a.label.indexOf(",")):a.label)+" "),i?r("span",{class:o},[e._v("("+e._s(l)+")")]):e._e()])}}],null,!1,*********),model:{value:e.queryCarrierIds,callback:function(t){e.queryCarrierIds=t},expression:"queryCarrierIds"}})],1),r("el-form-item",{attrs:{label:"订舱",prop:"companyId"}},[r("tree-select",{staticStyle:{width:"100%"},attrs:{flat:!1,multiple:!1,pass:e.queryParams.companyId,placeholder:"订舱口",type:"supplier"},on:{return:e.queryCompanyId}})],1),r("el-form-item",{attrs:{label:"启运",prop:"locationDepartureIds"}},[r("location-select",{staticStyle:{width:"100%"},attrs:{multiple:!0,pass:e.queryParams.locationDepartureIds,"load-options":e.locationOptions},on:{return:e.queryLocationDepartureIds}})],1),r("el-form-item",{attrs:{label:"目的",prop:"locationDestinationIds"}},[r("location-select",{staticStyle:{width:"100%"},attrs:{multiple:!0,pass:e.queryParams.locationDestinationIds,en:!0,"load-options":e.locationOptions},on:{return:e.queryLocationDestinationIds}})],1),r("el-form-item",{attrs:{label:"航线",prop:"lineDestinationIds"}},[r("tree-select",{staticStyle:{width:"100%"},attrs:{flat:!1,multiple:!0,pass:e.queryParams.lineDestinationIds,placeholder:"目的航线",type:"line"},on:{return:e.queryLineDestinationIds}})],1),r("el-form-item",{attrs:{label:"要素",prop:"infoId"}},[r("tree-select",{staticStyle:{width:"100%"},attrs:{flat:!1,multiple:!1,pass:e.queryParams.infoId,type:"commonInfo",placeholder:"物流要素"},on:{return:e.queryInfoId}})],1),r("el-form-item",{attrs:{label:"详细",prop:"essentialDetail"}},[r("el-input",{staticStyle:{width:"100%"},attrs:{placeholder:"要素详细"},nativeOn:{focusout:function(t){return e.handleQuery(t)}},model:{value:e.queryParams.essentialDetail,callback:function(t){e.$set(e.queryParams,"essentialDetail",t)},expression:"queryParams.essentialDetail"}})],1),r("el-form-item",{attrs:{label:"有效",prop:"isValid"}},[r("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"是否有效"},on:{change:e.handleQuery},model:{value:e.queryParams.isValid,callback:function(t){e.$set(e.queryParams,"isValid",t)},expression:"queryParams.isValid"}},e._l(e.dict.type.sys_yes_no,(function(e){return r("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),r("el-form-item",{attrs:{label:"模式",prop:"showMode"}},[r("el-select",{staticStyle:{width:"100%"},attrs:{clearable:"",placeholder:"显示模式"},on:{change:e.handleQuery},model:{value:e.queryParams.showMode,callback:function(t){e.$set(e.queryParams,"showMode",t)},expression:"queryParams.showMode"}},[r("el-option",{attrs:{value:"0",label:"小白模式"}}),r("el-option",{attrs:{value:"1",label:"专业模式"}})],1)],1),r("el-form-item",{attrs:{label:"员工",prop:"updateBy"}},[r("el-select",{staticStyle:{width:"100%"},attrs:{filterable:"",placeholder:"选择员工",clearable:""},on:{change:e.handleQuery},model:{value:e.queryParams.updateBy,callback:function(t){e.$set(e.queryParams,"updateBy",t)},expression:"queryParams.updateBy"}},e._l(e.userList,(function(e){return r("el-option",{key:e.staffId,attrs:{label:e.staffCode+" "+e.staffFamilyLocalName+e.staffGivingLocalName+" "+e.staffGivingEnName,value:e.staffId}})})),1)],1),r("el-form-item",{attrs:{label:"时间",prop:"updateTime"}},[r("el-date-picker",{staticStyle:{width:"100%"},attrs:{clearable:"",placeholder:"录入起始时间",type:"date","value-format":"yyyy-MM-dd"},on:{change:e.handleQuery},model:{value:e.queryParams.updateTime,callback:function(t){e.$set(e.queryParams,"updateTime",t)},expression:"queryParams.updateTime"}})],1),r("el-form-item",[r("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"mini"},on:{click:e.handleQuery}},[e._v("搜索")]),r("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1):e._e()],1),r("el-col",{attrs:{span:e.showRight}},[r("el-row",{staticClass:"mb8",attrs:{gutter:10}},[r("el-col",{attrs:{span:1.5}},[r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:characteristics:add"],expression:"['system:characteristics:add']"}],attrs:{type:"primary",plain:"",icon:"el-icon-plus",size:"mini"},on:{click:e.handleAdd}},[e._v("新增 ")])],1),r("el-col",{attrs:{span:1.5}},[r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:characteristics:edit"],expression:"['system:characteristics:edit']"}],attrs:{type:"success",plain:"",icon:"el-icon-edit",size:"mini",disabled:e.single},on:{click:e.handleUpdate}},[e._v("修改 ")])],1),r("el-col",{attrs:{span:1.5}},[r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:characteristics:remove"],expression:"['system:characteristics:remove']"}],attrs:{type:"danger",plain:"",icon:"el-icon-delete",size:"mini",disabled:e.multiple},on:{click:e.handleDelete}},[e._v("删除 ")])],1),r("el-col",{attrs:{span:1.5}},[r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:characteristics:export"],expression:"['system:characteristics:export']"}],attrs:{type:"warning",plain:"",icon:"el-icon-download",size:"mini"},on:{click:e.handleExport}},[e._v("导出 ")])],1),r("right-toolbar",{attrs:{showSearch:e.showSearch},on:{"update:showSearch":function(t){e.showSearch=t},"update:show-search":function(t){e.showSearch=t},queryTable:e.getList}})],1),r("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.characteristicsList,"row-class-name":e.tableRowClassName,border:""},on:{"selection-change":e.handleSelectionChange}},[r("el-table-column",{attrs:{type:"selection",width:"28",align:"center"}}),r("el-table-column",{attrs:{align:"center",label:"内部序号",prop:"richNo","show-tooltip-when-overflow":"",width:"80"}}),r("el-table-column",{attrs:{align:"left",label:"服务类型",prop:"serviceType",width:"58px"}}),r("el-table-column",{key:"cargoType",attrs:{align:"left",label:"货物特征",prop:"cargoType",width:"68px","show-tooltip-when-overflow":""}}),r("el-table-column",{key:"carrier",attrs:{align:"left",label:"承运人",width:"58px","show-tooltip-when-overflow":""},scopedSlots:e._u([{key:"default",fn:function(t){return[r("div",{staticStyle:{padding:"0",overflow:"hidden","text-overflow":"ellipsis","white-space":"nowrap"}},[e._v(" "+e._s(null==t.row.carrier?"全部":t.row.carrier)+" ")])]}}])}),r("el-table-column",{attrs:{align:"center",label:"订舱口",prop:"company","show-tooltip-when-overflow":"",width:"58px"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("div",{staticStyle:{padding:"0",overflow:"hidden","text-overflow":"ellipsis","white-space":"nowrap"}},[e._v(" "+e._s(null==t.row.companyId?"全部":t.row.company)+" ")])]}}])}),r("el-table-column",{attrs:{align:"left",label:"启运区域","show-tooltip-when-overflow":"",width:"69px"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("div",{staticStyle:{padding:"0",overflow:"hidden","text-overflow":"ellipsis","white-space":"nowrap"}},[e._v(" "+e._s((null!=t.row.locationDeparture?t.row.locationDeparture:"")+(null!=t.row.locationDeparture&&null!=t.row.lineDeparture?",":"")+(null!=t.row.lineDeparture?t.row.lineDeparture:""))+" ")])]}}])}),r("el-table-column",{attrs:{align:"left",label:"目的区域","show-tooltip-when-overflow":"",width:"170"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("div",{staticStyle:{padding:"0",overflow:"hidden","text-overflow":"ellipsis","white-space":"nowrap"}},[e._v(" "+e._s((null!=t.row.locationDestinationEn?t.row.locationDestinationEn:"")+(null!=t.row.lineDestination&&null!=t.row.locationDestinationEn?",":"")+(null!=t.row.lineDestination?t.row.lineDestination:""))+" ")])]}}])}),r("el-table-column",{attrs:{align:"center",label:"物流要素",prop:"info",width:"68"}}),r("el-table-column",{attrs:{align:"left",label:"要素详细",prop:"essentialDetail"},scopedSlots:e._u([{key:"default",fn:function(t){return[null!=t.row.essentialDetail&&t.row.essentialDetail.length>13?r("el-tooltip",{attrs:{placement:"top"}},[r("div",{attrs:{slot:"content"},slot:"content"},e._l(t.row.essentialDetail.split("\n"),(function(t){return r("div",[r("h6",{staticStyle:{margin:"0"}},[e._v(" "+e._s(t)+" ")])])})),0),r("div",[r("h6",{staticStyle:{margin:"0",overflow:"hidden","text-overflow":"ellipsis","white-space":"nowrap"}},[e._v(" "+e._s(t.row.essentialDetail)+" ")])])]):r("div",[r("h6",{staticStyle:{margin:"0"}},[e._v(" "+e._s(t.row.essentialDetail)+" ")])])]}}])}),r("el-table-column",{attrs:{align:"center",label:"有效时间",prop:"validTime",width:"130"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("span",[e._v(e._s(e.parseTime(t.row.validFrom,"{y}.{m}.{d}"))+"-"+e._s(e.parseTime(t.row.validTo,"{y}.{m}.{d}")))])]}}])}),r("el-table-column",{attrs:{align:"center",label:"有效",prop:"isValid",width:"38"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("dict-tag",{attrs:{options:e.dict.type.sys_yes_no,value:t.row.isValid}})]}}])}),r("el-table-column",{attrs:{label:"显示模式",align:"center",prop:"showMode",width:"68"},scopedSlots:e._u([{key:"default",fn:function(t){return["0"==t.row.showMode?r("el-tag",{attrs:{type:"primary"}},[e._v("小白模式")]):e._e(),"1"==t.row.showMode?r("el-tag",{attrs:{type:"primary"}},[e._v("专业模式")]):e._e()]}}])}),r("el-table-column",{attrs:{align:"left",label:"备注",prop:"remark","show-tooltip-when-overflow":""}}),r("el-table-column",{attrs:{align:"center",label:"重要",prop:"orderNum",width:"33"}}),r("el-table-column",{attrs:{align:"center",label:"录入时间",prop:"updateTime",width:"80"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("h6",{staticStyle:{margin:"0"}},[e._v(e._s(t.row.updateByName))]),r("h6",{staticStyle:{margin:"0"}},[e._v(e._s(e.parseTime(t.row.updateTime,"{y}-{m}-{d}")))])]}}])}),r("el-table-column",{attrs:{label:"操作",align:"center","class-name":"small-padding fixed-width",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:characteristics:edit"],expression:"['system:characteristics:edit']"}],staticStyle:{"margin-right":"-8px"},attrs:{size:"mini",type:"success",icon:"el-icon-edit"},on:{click:function(r){return e.handleUpdate(t.row)}}},[e._v("修改 ")]),r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:characteristics:remove"],expression:"['system:characteristics:remove']"}],staticStyle:{"margin-right":"-8px"},attrs:{size:"mini",type:"danger",icon:"el-icon-delete"},on:{click:function(r){return e.handleDelete(t.row)}}},[e._v("删除 ")])]}}])})],1),r("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total>0"}],attrs:{total:e.total,page:e.queryParams.pageNum,limit:e.queryParams.pageSize},on:{"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},pagination:e.getList}})],1)],1),r("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"},{name:"dialogDragWidth",rawName:"v-dialogDragWidth"}],attrs:{"close-on-click-modal":!1,"modal-append-to-body":!1,title:e.title,visible:e.open,width:"500px","append-to-body":""},on:{"update:visible":function(t){e.open=t}}},[r("el-form",{ref:"form",staticClass:"edit",attrs:{model:e.form,rules:e.rules,"label-width":"78px"}},[r("el-form-item",{attrs:{label:"服务类型",prop:"serviceTypeId"}},[r("tree-select",{attrs:{flat:!1,multiple:!1,pass:e.form.serviceTypeId,type:"serviceType"},on:{return:e.getServiceTypeId}})],1),r("el-form-item",{attrs:{label:"货物特征",prop:"cargoTypeIds"}},[r("tree-select",{attrs:{flat:!1,multiple:!0,pass:e.form.cargoTypeIds,type:"cargoType"},on:{return:e.getCargoTypeIds}})],1),r("el-form-item",{attrs:{label:"承运人",prop:"carrierIds"}},[r("treeselect",{attrs:{"disable-fuzzy-matching":!0,flat:!0,"flatten-search-results":!0,"disable-branch-nodes":!0,multiple:!0,"disable-branch-nodes":"",normalizer:e.carrierNormalizer,placeholder:"选择承运人",options:e.temCarrierList,"show-count":!0},on:{open:e.loadCarrier,input:e.deselectAllCarrrierIds,deselect:e.handleDeselectCarrierIds,select:e.handleSelectCarrierIds},scopedSlots:e._u([{key:"value-label",fn:function(t){var a=t.node;return r("div",{},[e._v(" "+e._s(a.raw.carrier.carrierIntlCode)+" "+e._s(null==a.raw.carrier.carrierIntlCode?a.raw.carrier.carrierShortName:"")+" ")])}},{key:"option-label",fn:function(t){var a=t.node,i=t.shouldShowCount,l=t.count,s=t.labelClassName,o=t.countClassName;return r("label",{class:s},[e._v(" "+e._s(-1!=a.label.indexOf(",")?a.label.substring(0,a.label.indexOf(",")):a.label)+" "),i?r("span",{class:o},[e._v("("+e._s(l)+")")]):e._e()])}}]),model:{value:e.carrierIds,callback:function(t){e.carrierIds=t},expression:"carrierIds"}})],1),r("el-form-item",{attrs:{label:"订舱口",prop:"companyId"}},[r("tree-select",{attrs:{flat:!1,multiple:!1,pass:e.form.companyId,placeholder:"订舱口",type:"supplier"},on:{return:e.getCompanyId}})],1),r("el-row",[r("el-form-item",{attrs:{label:"启运区域",prop:"locationDepartureIds"}},[r("location-select",{ref:"location",attrs:{multiple:!0,pass:e.form.locationDepartureIds,"load-options":e.locationOptions},on:{return:e.getLocationDepartureIds}})],1)],1),r("el-row",[r("el-form-item",{attrs:{label:"目的区域",prop:"locationDestinationIds"}},[r("location-select",{attrs:{multiple:!0,pass:e.form.locationDestinationIds,"load-options":e.locationOptions,en:!0},on:{return:e.getLocationDestinationIds}})],1)],1),r("el-row",[r("el-form-item",{attrs:{label:"目的航线",prop:"lineDestinationIds"}},[r("tree-select",{attrs:{flat:!1,multiple:!0,pass:e.form.lineDestinationIds,type:"line"},on:{return:e.getLineDestinationIds}})],1)],1),r("el-form-item",{attrs:{label:"物流要素",prop:"infoId"}},[r("tree-select",{attrs:{flat:!1,multiple:!1,pass:e.form.infoId,type:"commonInfo"},on:{return:e.getInfoId}})],1),r("el-form-item",{attrs:{label:"要素详细",prop:"essentialDetail"}},[r("el-input",{attrs:{type:"textarea",autosize:{minRows:5,maxRows:20},maxlength:"300",placeholder:"内容","show-word-limit":""},model:{value:e.form.essentialDetail,callback:function(t){e.$set(e.form,"essentialDetail",t)},expression:"form.essentialDetail"}})],1),r("el-row",{attrs:{gutter:5}},[r("el-col",{attrs:{span:15}},[r("el-form-item",{attrs:{label:"有效时间",prop:"validTime"}},[r("el-date-picker",{staticStyle:{width:"100%"},attrs:{clearable:"",placeholder:"有效时间","default-time":"['00:00:00', '23:59:59']","value-format":"yyyy-MM-dd",type:"daterange"},on:{change:e.changeTime},model:{value:e.validTime,callback:function(t){e.validTime=t},expression:"validTime"}})],1)],1),r("el-col",{attrs:{span:9}},[r("el-form-item",{attrs:{label:"录入时间",prop:"updateTime"}},[r("el-date-picker",{staticStyle:{width:"100%"},attrs:{clearable:"",placeholder:"录入时间",disabled:"",type:"date","value-format":"yyyy-MM-dd"},model:{value:e.form.updateTime,callback:function(t){e.$set(e.form,"updateTime",t)},expression:"form.updateTime"}})],1)],1)],1),r("el-row",{attrs:{gutter:5}},[r("el-col",{attrs:{span:12}},[r("el-form-item",{attrs:{label:"是否有效",prop:"isValid"}},[r("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"是否有效"},model:{value:e.form.isValid,callback:function(t){e.$set(e.form,"isValid",t)},expression:"form.isValid"}},e._l(e.dict.type.sys_yes_no,(function(e){return r("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1)],1),r("el-col",{attrs:{span:12}},[r("el-form-item",{attrs:{label:"状态"}},[r("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"状态"},model:{value:e.form.status,callback:function(t){e.$set(e.form,"status",t)},expression:"form.status"}},e._l(e.dict.type.sys_normal_disable,(function(e){return r("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1)],1)],1),r("el-form-item",{attrs:{label:"显示模式",prop:"showMode"}},[r("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"显示模式"},model:{value:e.form.showMode,callback:function(t){e.$set(e.form,"showMode",t)},expression:"form.showMode"}},[r("el-option",{attrs:{value:"0",label:"小白模式"}}),r("el-option",{attrs:{value:"1",label:"专业模式"}})],1)],1),r("el-form-item",{attrs:{label:"重要程度",prop:"orderNum"}},[r("el-input-number",{staticStyle:{width:"100%"},attrs:{controls:!1,min:0,placeholder:"排序"},model:{value:e.form.orderNum,callback:function(t){e.$set(e.form,"orderNum",t)},expression:"form.orderNum"}})],1),r("el-form-item",{attrs:{label:"备注",prop:"remark"}},[r("el-input",{attrs:{type:"textarea",placeholder:"备注"},model:{value:e.form.remark,callback:function(t){e.$set(e.form,"remark",t)},expression:"form.remark"}})],1)],1),r("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[r("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("确 定")]),r("el-button",{on:{click:e.cancel}},[e._v("取 消")])],1)],1)],1)},i=[],l=r("5530"),s=r("b85c"),o=(r("14d9"),r("d81d"),r("caad"),r("2532"),r("4de4"),r("d3b7"),r("b775"));function n(e){return Object(o["a"])({url:"/system/characteristics/list",method:"get",params:e})}function c(e){return Object(o["a"])({url:"/system/characteristics/"+e,method:"get"})}function d(e){return Object(o["a"])({url:"/system/characteristics",method:"post",data:e})}function u(e){return Object(o["a"])({url:"/system/characteristics",method:"put",data:e})}function p(e){return Object(o["a"])({url:"/system/characteristics/"+e,method:"delete"})}var m=r("ca17"),h=r.n(m),f=(r("6f8d"),r("b0b8")),y=r.n(f),v=r("4360"),I=r("fba1"),w=r("c0c7"),b={name:"Characteristics",dicts:["sys_normal_disable","sys_yes_no"],components:{Treeselect:h.a},data:function(){return{showLeft:3,showRight:21,validTime:[],carrierList:[],carrierIds:[],queryCarrierIds:[],temCarrierList:[],locationOptions:[],userList:[],loading:!0,ids:[],single:!0,multiple:!0,showSearch:!0,total:0,characteristicsList:[],title:"",open:!1,queryParams:{accurate:1,pageNum:1,pageSize:20,infoId:null,showMode:null,serviceTypeId:null,cargoTypeIds:[],locationDepartureIds:[],lineDepartureIds:[],locationDestinationIds:[],lineDestinationIds:[],carrierIds:[],companyId:null,essentialDetail:null,isValid:"Y",updateBy:null,updateTime:null},form:{},rules:{cargoTypeIds:{required:!0,trigger:"blur"}}}},watch:{showSearch:function(e){1==e?(this.showRight=21,this.showLeft=3):(this.showRight=24,this.showLeft=0)},"form.serviceTypeId":function(e){this.loadCarrier();var t=[];if(void 0!=this.carrierList&&null!=e&&(this.temCarrierList=this.carrierList),void 0!=this.carrierList&&null!=e){var r,a=Object(s["a"])(this.carrierList);try{for(a.s();!(r=a.n()).done;){var i=r.value;if(null!=e&&void 0!=e&&(i.serviceTypeId==e&&t.push(i),void 0!=i.children&&i.children.length>0)){var l,o=Object(s["a"])(i.children);try{for(o.s();!(l=o.n()).done;){var n=l.value;n.serviceTypeId==e&&t.push(n)}}catch(c){o.e(c)}finally{o.f()}}}}catch(c){a.e(c)}finally{a.f()}this.temCarrierList=t,this.temCarrierList.length}}},created:function(){var e=this;this.getList(),this.loadCarrier(),Object(w["j"])().then((function(t){e.userList=t.data}))},methods:{changeTime:function(e){void 0==e&&(this.form.validFrom=null,this.form.validTo=null),this.form.validFrom=e[0],this.form.validTo=e[1]},loadCarrier:function(){var e=this;0==this.$store.state.data.serviceTypeCarriers.length||this.$store.state.data.redisList.serviceTypeCarriers?v["a"].dispatch("getServiceTypeCarriersList").then((function(){e.carrierList=e.$store.state.data.serviceTypeCarriers})):this.carrierList=this.$store.state.data.serviceTypeCarriers},carrierNormalizer:function(e){var t;return e.children&&!e.children.length&&delete e.children,t=!e.carrier||null==e.carrier.carrierLocalName&&null==e.carrier.carrierEnName?e.serviceLocalName+" "+e.serviceEnName+","+y.a.getFullChars(e.serviceLocalName):(null!=e.carrier.carrierIntlCode?e.carrier.carrierIntlCode:"")+" "+(null!=e.carrier.carrierEnName?e.carrier.carrierEnName:"")+" "+(null!=e.carrier.carrierLocalName?e.carrier.carrierLocalName:"")+","+y.a.getFullChars(null!=e.carrier.carrierLocalName?e.carrier.carrierLocalName:""),{id:e.serviceTypeId,label:t,children:e.children}},tableRowClassName:function(e){var t=e.row,r=Object(I["f"])(new Date,"{y}-{m}-{d}"),a=Object(I["f"])(t.validFrom,"{y}-{m}-{d}"),i=Object(I["f"])(t.validTo,"{y}-{m}-{d}");return a<r<i?"":i<r?"valid-row":a>r?"valid-before":""},getList:function(){var e=this;this.loading=!0,n(this.queryParams).then((function(t){e.characteristicsList=t.rows,e.total=t.total,e.loading=!1}))},cancel:function(){this.open=!1,this.reset()},reset:function(){this.form={characteristicsId:null,serviceTypeId:void 0!=this.form.serviceTypeId?this.form.serviceTypeId:21,infoId:null,showMode:"0",essentialDetail:null,updateTime:Object(I["f"])(new Date),validFrom:null,validTo:null,isValid:"Y",status:"0",remark:null,createBy:null,createTime:null,updateBy:null,deleteBy:null,deleteTime:null,deleteStatus:"0",cargoTypeIds:void 0!=this.form.cargoTypeIds?this.form.cargoTypeIds:[-1],locationDepartureIds:void 0!=this.form.locationDepartureIds?this.form.locationDepartureIds:[13716],lineDepartureIds:[],locationDestinationIds:[],lineDestinationIds:[],carrierIds:[]},this.validTime=[],this.carrierIds=[],this.resetForm("form")},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.resetForm("queryForm"),this.handleQuery()},handleSelectionChange:function(e){this.ids=e.map((function(e){return e.characteristicsId})),this.single=1!=e.length,this.multiple=!e.length},handleAdd:function(){var e=this;this.reset(),this.open=!0,this.title="添加物流注意事项",setTimeout((function(){e.$refs.location.remoteMethod("广东")}),0)},handleUpdate:function(e){var t=this;this.reset(),this.loading=!0;var r=e.characteristicsId||this.ids;c(r).then((function(e){t.form=e.data,t.form.cargoTypeIds=e.cargoTypeIds,t.form.lineDepartureIds=e.lineDepartureIds,t.form.locationDepartureIds=e.locationDepartureIds,t.form.lineDestinationIds=e.lineDestinationIds,t.form.locationDestinationIds=e.locationDestinationIds,t.form.carrierIds=e.carrierIds,t.loadCarrier();var r=[];if(void 0!=t.carrierList&&null!=t.form.serviceTypeId){t.temCarrierList=t.carrierList;var a,i=Object(s["a"])(t.carrierList);try{for(i.s();!(a=i.n()).done;){var l=a.value;if(void 0!=l.children&&l.children.length>0){var o,n=Object(s["a"])(l.children);try{for(n.s();!(o=n.n()).done;){var c=o.value;null!=e.carrierIds&&e.carrierIds.includes(c.carrier.carrierId)&&!e.carrierIds.includes(c.serviceTypeId)&&t.carrierIds.push(c.serviceTypeId)}}catch(T){n.e(T)}finally{n.f()}}}}catch(T){i.e(T)}finally{i.f()}}if(void 0!=t.carrierList&&null!=t.form.serviceTypeId){var d,u=Object(s["a"])(t.carrierList);try{for(u.s();!(d=u.n()).done;){var p=d.value;if(null!=t.form.serviceTypeId&&void 0!=t.form.serviceTypeId&&(p.serviceTypeId==t.form.serviceTypeId&&r.push(p),void 0!=p.children&&p.children.length>0)){var m,h=Object(s["a"])(p.children);try{for(h.s();!(m=h.n()).done;){var f=m.value;f.serviceTypeId==t.form.serviceTypeId&&r.push(f)}}catch(T){h.e(T)}finally{h.f()}}}}catch(T){u.e(T)}finally{u.f()}if(t.temCarrierList=r,null==t.form.serviceTypeId&&t.temCarrierList.length>0){var y,v=Object(s["a"])(t.temCarrierList);try{for(v.s();!(y=v.n()).done;){var I=y.value;if(void 0!=I.children&&I.children.length>0){var w,b=Object(s["a"])(I.children);try{for(b.s();!(w=b.n()).done;){var g=w.value;null!=e.carrierIds&&e.carrierIds.includes(g.carrier.carrierId)&&!e.carrierIds.includes(g.serviceTypeId)&&t.carrierIds.push(g.serviceTypeId)}}catch(T){b.e(T)}finally{b.f()}}}}catch(T){v.e(T)}finally{v.f()}}}e.data.validFrom&&t.validTime.push(e.data.validFrom),e.data.validTo&&t.validTime.push(e.data.validTo),t.locationOptions=e.locationOptions,t.open=!0,t.title="修改物流注意事项",t.loading=!1}))},submitForm:function(){var e=this;this.$refs["form"].validate((function(t){t&&(null!=e.form.characteristicsId?u(e.form).then((function(t){e.$modal.msgSuccess("修改成功"),e.open=!1,e.getList()})):d(e.form).then((function(t){e.$modal.msgSuccess("新增成功"),e.open=!1,e.getList()})))}))},handleDelete:function(e){var t=this,r=e.characteristicsId||this.ids;this.$confirm('是否确认删除物流注意事项编号为"'+r+'"的数据项？',"提示",{customClass:"modal-confirm"}).then((function(){return p(r)})).then((function(){t.getList(),t.$modal.msgSuccess("删除成功")})).catch((function(){}))},handleExport:function(){this.download("system/characteristics/export",Object(l["a"])({},this.queryParams),"characteristics_".concat((new Date).getTime(),".xlsx"))},queryServiceTypeId:function(e){this.queryParams.serviceTypeId=e,this.handleQuery()},queryCargoTypeIds:function(e){this.queryParams.cargoTypeIds=e,this.handleQuery()},queryLocationDepartureIds:function(e){this.queryParams.locationDepartureIds=e,this.handleQuery()},queryLineDepartureIds:function(e){this.queryParams.lineDepartureIds=e,this.handleQuery()},queryLocationDestinationIds:function(e){this.queryParams.locationDestinationIds=e,this.handleQuery()},queryLineDestinationIds:function(e){this.queryParams.lineDestinationIds=e,this.handleQuery()},queryInfoId:function(e){this.queryParams.infoId=e,this.handleQuery()},getServiceTypeId:function(e){void 0==e?(this.form.serviceTypeId=null,this.carrierIds=null,this.form.carrierIds=null):this.form.serviceTypeId=e},getCurrencyId:function(e){this.form.currencyId=void 0==e?null:e},getUnitId:function(e){this.form.unitId=void 0==e?null:e},getCargoTypeIds:function(e){this.form.cargoTypeIds=e},getCompanyId:function(e){this.form.companyId=e},getLocationDepartureIds:function(e){this.form.locationDepartureIds=e},getLineDepartureIds:function(e){this.form.lineDepartureIds=e},getLocationDestinationIds:function(e){this.form.locationDestinationIds=e},getLineDestinationIds:function(e){this.form.lineDestinationIds=e},getInfoId:function(e){this.form.infoId=e},handleSelectCarrierIds:function(e){this.form.carrierIds.push(e.carrier.carrierId)},handleSelectQueryCarrierIds:function(e){this.queryParams.carrierIds.push(e.carrier.carrierId),this.handleQuery()},handleDeselectCarrierIds:function(e){this.form.carrierIds=this.form.carrierIds.filter((function(t){return t!=e.carrier.carrierId}))},handleDeselectQueryCarrierIds:function(e){this.queryParams.carrierIds=this.queryParams.carrierIds.filter((function(t){return t!=e.carrier.carrierId})),this.handleQuery()},deselectAllCarrrierIds:function(e){0==e.length&&(this.form.carrierIds=[])},deselectAllQueryCarrierIds:function(e){0==e.length&&(this.queryParams.carrierIds=[],this.handleQuery())},queryCompanyId:function(e){this.queryParams.companyId=e,this.handleQuery()}}},g=b,T=r("2877"),D=Object(T["a"])(g,a,i,!1,null,null,null);t["default"]=D.exports}}]);