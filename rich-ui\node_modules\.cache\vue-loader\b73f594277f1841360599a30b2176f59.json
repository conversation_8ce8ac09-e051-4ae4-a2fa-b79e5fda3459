{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\inventory\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\inventory\\index.vue", "mtime": 1750818094548}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7CiAgYWRkSW52ZW50b3J5LCBjYW5jZWxQa2csCiAgY2hhbmdlU3RhdHVzLAogIGRlbEludmVudG9yeSwKICBnZXRJbnZlbnRvcnksIGdldFBhY2thZ2UsIGxpc3RBZ2dyZWdhdG9yUnNJbnZlbnRvcnksCiAgbGlzdEludmVudG9yeSwgcGFja1VwLAogIHVwZGF0ZUludmVudG9yeQp9IGZyb20gIkAvYXBpL3N5c3RlbS9pbnZlbnRvcnkiCmltcG9ydCB7ZGVmYXVsdEVsZW1lbnRUeXBlUHJvdmlkZXIsIGhpcHJpbnR9IGZyb20gIkAiCmltcG9ydCB3YXJlaG91c2VSZWNlaXB0IGZyb20gIkAvcHJpbnQtdGVtcGxhdGUvd2FyZWhvdXNlUmVjZWlwdCIKaW1wb3J0IHdhcmVob3VzZVJlY2VpcHROZXcgZnJvbSAiQC9wcmludC10ZW1wbGF0ZS93YXJlaG91c2VSZWNlaXB0TmV3IgppbXBvcnQgcHJpbnRQcmV2aWV3IGZyb20gIkAvdmlld3MvcHJpbnQvZGVtby9kZXNpZ24vcHJldmlldy52dWUiCmltcG9ydCBQcmludFRlbXBsYXRlIGZyb20gIkAvdmlld3Mvc3lzdGVtL3ByaW50L1ByaW50VGVtcGxhdGUudnVlIgppbXBvcnQgY3VycmVuY3kgZnJvbSAiY3VycmVuY3kuanMiCmltcG9ydCBsb2cgZnJvbSAiQC92aWV3cy9tb25pdG9yL2pvYi9sb2cudnVlIgppbXBvcnQgbW9tZW50IGZyb20gIm1vbWVudC9tb21lbnQiCmltcG9ydCBzdG9yZSBmcm9tICJAL3N0b3JlIgppbXBvcnQge3BhcnNlVGltZX0gZnJvbSAiLi4vLi4vLi4vdXRpbHMvcmljaCIKaW1wb3J0IERhdGFBZ2dyZWdhdG9yQmFja0dyb3VuZCBmcm9tICJAL3ZpZXdzL3N5c3RlbS9EYXRhQWdncmVnYXRvckJhY2tHcm91bmQvaW5kZXgudnVlIgppbXBvcnQge3JjdEZpZWxkTGFiZWxNYXB9IGZyb20gIkAvY29uZmlnL3JjdEZpZWxkTGFiZWxNYXAiCmltcG9ydCB7cnNJbnZlbnRvcnlGaWVsZExhYmVsTWFwfSBmcm9tICJAL2NvbmZpZy9yc0ludmVudG9yeUZpZWxkTGFiZWxNYXAiCmltcG9ydCB7bGlzdEFnZ3JlZ2F0b3JSY3R9IGZyb20gIkAvYXBpL3N5c3RlbS9yY3QiCmltcG9ydCB7Z2V0VG9rZW59IGZyb20gIkAvdXRpbHMvYXV0aCIKCmxldCBoaXByaW50VGVtcGxhdGUKZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICJJbnZlbnRvcnkiLAogIGRhdGEoKSB7CiAgICBjb25zdCB2YWxpZGF0ZUluYm91bmRTZXJpYWxObyA9IChydWxlLCB2YWx1ZSwgY2FsbGJhY2spID0+IHsKICAgICAgY29uc29sZS5sb2codmFsdWUpCiAgICAgIC8qIGlmICh2YWx1ZSA9PT0gJycpIHsKICAgICAgICBjYWxsYmFjayhuZXcgRXJyb3IoJ+ivt+i+k+WFpea1geawtOWPtycpKTsKICAgICAgfSBlbHNlIHsKICAgICAgICBpZiAodGhpcy5mb3JtLmluYm91bmRTZXJpYWxOb1N1YiAhPT0gJycpIHsKICAgICAgICAgIHRoaXMuJHJlZnMuZm9ybS52YWxpZGF0ZUZpZWxkKCdpbmJvdW5kU2VyaWFsTm8nKTsKICAgICAgICB9CiAgICAgICAgY2FsbGJhY2soKTsKICAgICAgfSAqLwogICAgfQogICAgcmV0dXJuIHsKICAgICAgY2FyZ29EZXRhaWxPcGVuOiBmYWxzZSwKICAgICAgY2FyZ29EZXRhaWxSb3c6IHsKICAgICAgICBjYXJnb0RldGFpbHNJZDogbnVsbCwKICAgICAgICBpbmJvdW5kU2VyaWFsTm86IG51bGwsCiAgICAgICAgaW5ib3VuZFNlcmlhbFNwbGl0OiBudWxsLAogICAgICAgIGNsaWVudENvZGU6IG51bGwsCiAgICAgICAgc2hpcHBpbmdNYXJrOiBudWxsLAogICAgICAgIGl0ZW1OYW1lOiBudWxsLAogICAgICAgIGJveENvdW50OiBudWxsLAogICAgICAgIGJveEl0ZW1Db3VudDogbnVsbCwKICAgICAgICBzdWJ0b3RhbEl0ZW1Db3VudDogbnVsbCwKICAgICAgICBleHByZXNzRGF0ZTogbW9tZW50KCkuZm9ybWF0KCJ5eXl5LU1NLUREIiksCiAgICAgICAgYWRkaXRpb25hbEZlZTogbnVsbCwKICAgICAgICBwYWNrYWdlVHlwZTogIue6uOeusSIsCiAgICAgICAgdW5pdEdyb3NzV2VpZ2h0OiBudWxsLAogICAgICAgIHVuaXRMZW5ndGg6IG51bGwsCiAgICAgICAgdW5pdFdpZHRoOiBudWxsLAogICAgICAgIHVuaXRIZWlnaHQ6IG51bGwsCiAgICAgICAgdW5pdFZvbHVtZTogbnVsbCwKICAgICAgICBkYW1hZ2VTdGF0dXM6ICIwIiwKICAgICAgICBiYXJjb2RlOiBudWxsCiAgICAgIH0sCiAgICAgIHNob3dMZWZ0OiAwLAogICAgICBzaG93UmlnaHQ6IDI0LAogICAgICAvLyDpga7nvanlsYIKICAgICAgbG9hZGluZzogdHJ1ZSwKICAgICAgLy8g6YCJ5Lit5pWw57uECiAgICAgIGlkczogW10sCiAgICAgIGFnZ3JlZ2F0b3JSY3RMaXN0OiBbXSwKICAgICAgZmllbGRMYWJlbE1hcDogcnNJbnZlbnRvcnlGaWVsZExhYmVsTWFwLAogICAgICAvLyDpnZ7ljZXkuKrnpoHnlKgKICAgICAgc2luZ2xlOiB0cnVlLAogICAgICBvcGVuQWdncmVnYXRvcjogZmFsc2UsCiAgICAgIC8vIOmdnuWkmuS4quemgeeUqAogICAgICBtdWx0aXBsZTogdHJ1ZSwKICAgICAgc2VsZWN0ZWRJbnZlbnRvcnlMaXN0OiBbXSwKICAgICAgc2VsZWN0ZWRQa2dMaXN0OiBbXSwKICAgICAgLy8g5pi+56S65pCc57Si5p2h5Lu2CiAgICAgIHNob3dTZWFyY2g6IGZhbHNlLAogICAgICAvLyDmgLvmnaHmlbAKICAgICAgdG90YWw6IDAsCiAgICAgIC8vIOW6k+WtmOihqOagvOaVsOaNrgogICAgICBpbnZlbnRvcnlMaXN0OiBbXSwKICAgICAgb3V0Ym91bmRMaXN0OiBbXSwKICAgICAgLy8g5by55Ye65bGC5qCH6aKYCiAgICAgIHRpdGxlOiAiIiwKICAgICAgb3BlblBrZ1RvOiBmYWxzZSwKCiAgICAgIG91dGJvdW5kVHlwZTogbnVsbCwKCiAgICAgIC8vIOaYr+WQpuaYvuekuuW8ueWHuuWxggogICAgICBvcGVuOiBmYWxzZSwKICAgICAgb3BlblBrZzogZmFsc2UsCiAgICAgIHBrZ0xpc3Q6IFtdLAogICAgICAvLyDnlKjmiLflr7zlhaXlj4LmlbAKICAgICAgdXBsb2FkOiB7CiAgICAgICAgLy8g5piv5ZCm5pi+56S65by55Ye65bGC77yI55So5oi35a+85YWl77yJCiAgICAgICAgb3BlbjogZmFsc2UsCiAgICAgICAgLy8g5by55Ye65bGC5qCH6aKY77yI55So5oi35a+85YWl77yJCiAgICAgICAgdGl0bGU6ICIiLAogICAgICAgIC8vIOaYr+WQpuemgeeUqOS4iuS8oAogICAgICAgIGlzVXBsb2FkaW5nOiBmYWxzZSwKICAgICAgICAvLyDmmK/lkKbmm7TmlrDlt7Lnu4/lrZjlnKjnmoTnlKjmiLfmlbDmja4KICAgICAgICB1cGRhdGVTdXBwb3J0OiB0cnVlLAogICAgICAgIC8vIOiuvue9ruS4iuS8oOeahOivt+axguWktOmDqAogICAgICAgIGhlYWRlcnM6IHtBdXRob3JpemF0aW9uOiAiQmVhcmVyICIgKyBnZXRUb2tlbigpfSwKICAgICAgICAvLyDkuIrkvKDnmoTlnLDlnYAKICAgICAgICB1cmw6IHByb2Nlc3MuZW52LlZVRV9BUFBfQkFTRV9BUEkgKyAiL3N5c3RlbS9pbnZlbnRvcnkvaW1wb3J0RGF0YSIKICAgICAgfSwKICAgICAgLy8g5p+l6K+i5Y+C5pWwCiAgICAgIHF1ZXJ5UGFyYW1zOiB7CiAgICAgICAgcGFnZU51bTogMSwKICAgICAgICBwYWdlU2l6ZTogMjAsCiAgICAgICAgaW52ZW50b3J5U3RhdHVzOiBudWxsLAogICAgICAgIGluYm91bmRTZXJpYWxObzogbnVsbCwKICAgICAgICBpbmJvdW5kU2VyaWFsU3BsaXQ6IG51bGwsCiAgICAgICAgaW5ib3VuZERhdGU6IG51bGwsCiAgICAgICAgb3V0Ym91bmRObzogbnVsbCwKICAgICAgICBmb3J3YXJkZXJObzogbnVsbCwKICAgICAgICByZW50YWxTZXR0bGVtZW50RGF0ZTogbnVsbCwKICAgICAgICBvdXRib3VuZERhdGU6IG51bGwsCiAgICAgICAgY2xpZW50Q29kZTogbnVsbCwKICAgICAgICBzdWJPcmRlck5vOiBudWxsLAogICAgICAgIHN1cHBsaWVyOiBudWxsLAogICAgICAgIGRyaXZlckluZm86IG51bGwsCiAgICAgICAgc3FkU2hpcHBpbmdNYXJrOiBudWxsLAogICAgICAgIGNhcmdvTmFtZTogbnVsbCwKICAgICAgICB0b3RhbEJveGVzOiBudWxsLAogICAgICAgIHBhY2thZ2VUeXBlOiBudWxsLAogICAgICAgIHRvdGFsR3Jvc3NXZWlnaHQ6IG51bGwsCiAgICAgICAgdG90YWxWb2x1bWU6IG51bGwsCiAgICAgICAgZGFtYWdlU3RhdHVzOiBudWxsLAogICAgICAgIHN0b3JhZ2VMb2NhdGlvbjE6IG51bGwsCiAgICAgICAgc3RvcmFnZUxvY2F0aW9uMjogbnVsbCwKICAgICAgICBzdG9yYWdlTG9jYXRpb24zOiBudWxsLAogICAgICAgIHJlY2VpdmVkU3RvcmFnZUZlZTogbnVsbCwKICAgICAgICB1bnBhaWRVbmxvYWRpbmdGZWU6IG51bGwsCiAgICAgICAgbG9naXN0aWNzQWR2YW5jZUZlZTogbnVsbCwKICAgICAgICByZW50YWxCYWxhbmNlRmVlOiBudWxsLAogICAgICAgIGZyZWVTdGFja1BlcmlvZDogbnVsbCwKICAgICAgICBvdmVyZHVlUmVudGFsVW5pdFByaWNlOiBudWxsLAogICAgICAgIG92ZXJkdWVSZW50YWxGZWU6IG51bGwsCiAgICAgICAgbm90ZXM6IG51bGwsCiAgICAgICAgaXNUb3BMZXZlbDogdHJ1ZSAvLyDpu5jorqTlj6rmn6Xor6LpobblsYLmlbDmja4KICAgICAgfSwKICAgICAgLy8g6KGo5Y2V5Y+C5pWwCiAgICAgIGZvcm06IHt9LAogICAgICBwa2dEZXRhaWxzTGlzdDogW10sCiAgICAgIC8vIOihqOWNleagoemqjAogICAgICBydWxlczogewogICAgICAgIC8vIGNhcmdvTmFtZTogWwogICAgICAgIC8vICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuivt+i+k+WFpei0p+eJqeaPj+i/sCIsIHRyaWdnZXI6ICJibHVyIiB9CiAgICAgICAgLy8gXSwKICAgICAgICAvLyBjb25zaWduZWVOYW1lOiBbCiAgICAgICAgLy8gICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi6K+36L6T5YWl5pS26LSn5Lq65ZCN56ewIiwgdHJpZ2dlcjogImJsdXIiIH0KICAgICAgICAvLyBdLAogICAgICAgIC8vIGNvbnNpZ25lZVRlbDogWwogICAgICAgIC8vICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuivt+i+k+WFpeaUtui0p+S6uueUteivnSIsIHRyaWdnZXI6ICJibHVyIiB9CiAgICAgICAgLy8gXSwKICAgICAgICBzdWJPcmRlck5vOiBbCiAgICAgICAgICB7CiAgICAgICAgICAgIHZhbGlkYXRvcjogKHJ1bGUsIHZhbHVlLCBjYWxsYmFjaykgPT4gewogICAgICAgICAgICAgIGlmICghdmFsdWUpIHsKICAgICAgICAgICAgICAgIGNhbGxiYWNrKCkKICAgICAgICAgICAgICAgIHJldHVybgogICAgICAgICAgICAgIH0KICAgICAgICAgICAgICBpZiAoIXRoaXMuZm9ybS5jbGllbnRDb2RlKSB7CiAgICAgICAgICAgICAgICBjYWxsYmFjayhuZXcgRXJyb3IoIuivt+WFiOmAieaLqeWuouaIt+S7o+eggSIpKQogICAgICAgICAgICAgICAgcmV0dXJuCiAgICAgICAgICAgICAgfQogICAgICAgICAgICAgIGNvbnN0IHByZWZpeCA9IGAke3RoaXMuZm9ybS5jbGllbnRDb2RlfS1gCiAgICAgICAgICAgICAgaWYgKCF2YWx1ZS5zdGFydHNXaXRoKHByZWZpeCkpIHsKICAgICAgICAgICAgICAgIGNhbGxiYWNrKG5ldyBFcnJvcihg5YiG5Y2V5Y+35b+F6aG75LulICR7cHJlZml4fSDlvIDlpLRgKSkKICAgICAgICAgICAgICAgIHJldHVybgogICAgICAgICAgICAgIH0KICAgICAgICAgICAgICBjYWxsYmFjaygpCiAgICAgICAgICAgIH0sCiAgICAgICAgICAgIHRyaWdnZXI6ICJibHVyIgogICAgICAgICAgfQogICAgICAgIF0KICAgICAgfSwKICAgICAgc2VsZWN0ZWRDbGllbnQ6IG51bGwKICAgIH0KICB9LAogIGNvbXBvbmVudHM6IHsKICAgIERhdGFBZ2dyZWdhdG9yQmFja0dyb3VuZCwKICAgIHByaW50UHJldmlldywKICAgIFByaW50VGVtcGxhdGUKICB9LAogIHdhdGNoOiB7CiAgICBzaG93U2VhcmNoKG4pIHsKICAgICAgaWYgKG4gPT09IHRydWUpIHsKICAgICAgICB0aGlzLnNob3dSaWdodCA9IDIxCiAgICAgICAgdGhpcy5zaG93TGVmdCA9IDMKICAgICAgfSBlbHNlIHsKICAgICAgICB0aGlzLnNob3dSaWdodCA9IDI0CiAgICAgICAgdGhpcy5zaG93TGVmdCA9IDAKICAgICAgfQogICAgfQogIH0sCiAgbW91bnRlZCgpIHsKICAgIHRoaXMuaW5pdFByaW50KCkKICB9LAogIGNyZWF0ZWQoKSB7CiAgICB0aGlzLmdldExpc3QoKQogIH0sCiAgbWV0aG9kczogewogICAgLy8g5o+Q5Lqk5LiK5Lyg5paH5Lu2CiAgICBzdWJtaXRGaWxlRm9ybSgpIHsKICAgICAgdGhpcy4kcmVmcy51cGxvYWQuc3VibWl0KCkKICAgIH0sCiAgICAvLyDmlofku7bkuIrkvKDkuK3lpITnkIYKICAgIGhhbmRsZUZpbGVVcGxvYWRQcm9ncmVzcyhldmVudCwgZmlsZSwgZmlsZUxpc3QpIHsKICAgICAgdGhpcy51cGxvYWQuaXNVcGxvYWRpbmcgPSB0cnVlCiAgICB9LAogICAgLy8g5paH5Lu25LiK5Lyg5oiQ5Yqf5aSE55CGCiAgICBoYW5kbGVGaWxlU3VjY2VzcyhyZXNwb25zZSwgZmlsZSwgZmlsZUxpc3QpIHsKICAgICAgdGhpcy51cGxvYWQub3BlbiA9IGZhbHNlCiAgICAgIHRoaXMudXBsb2FkLmlzVXBsb2FkaW5nID0gZmFsc2UKICAgICAgdGhpcy4kcmVmcy51cGxvYWQuY2xlYXJGaWxlcygpCiAgICAgIHRoaXMuJG1lc3NhZ2UuaW5mbyhyZXNwb25zZS5tc2cpCiAgICAgIGlmIChyZXNwb25zZS5tc2cgIT0gIuWFqOmDqOS4iuS8oOaIkOWKnyIpIHsKICAgICAgICB0aGlzLmRvd25sb2FkKCJzeXN0ZW0vaW52ZW50b3J5L2ZhaWxMaXN0Iiwge30sIGDkuIrkvKDlpLHotKXliJfooagueGxzeGApCiAgICAgIH0KICAgICAgdGhpcy5nZXRMaXN0KCkKICAgIH0sCiAgICBsb2FkUGtnRGV0YWlsKCkgewogICAgICBpZiAodGhpcy5mb3JtLmNsaWVudENvZGUpIHsKICAgICAgICBsaXN0SW52ZW50b3J5KHtwYWNrYWdlVG86IHRoaXMuZm9ybS5pbnZlbnRvcnlJZH0pLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgICAgdGhpcy5wa2dEZXRhaWxzTGlzdCA9IHJlc3BvbnNlLnJvd3MKICAgICAgICB9KQogICAgICB9CiAgICB9LAogICAgLy8g5Yqg6L295a2Q6IqC54K55pWw5o2uCiAgICBsb2FkQ2hpbGRJbnZlbnRvcnkodHJlZSwgdHJlZU5vZGUsIHJlc29sdmUpIHsKICAgICAgLy8g5L2/55SocGFja2FnZVRv5a2X5q615p+l6K+i5a2Q6IqC54K5CiAgICAgIGxpc3RJbnZlbnRvcnkoe3BhY2thZ2VUbzogdHJlZS5pbnZlbnRvcnlJZH0pLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgIGNvbnN0IHJvd3MgPSByZXNwb25zZS5yb3dzCgogICAgICAgIC8vIOWFiOWwhuaVsOaNruS8oOmAkue7meihqOagvO+8jOehruS/neWtkOiKgueCuea4suafkwogICAgICAgIHJlc29sdmUocm93cykKICAgICAgICB0cmVlLmNoaWxkcmVuID0gcm93cwoKICAgICAgICAvLyDlpoLmnpzniLbpobnooqvpgInkuK3vvIzlnKjlrZDoioLngrnmuLLmn5PlrozmiJDlkI7pgInkuK3lroPku6wKICAgICAgICBpZiAodGhpcy5pZHMuaW5jbHVkZXModHJlZS5pbnZlbnRvcnlJZCkpIHsKICAgICAgICAgIHNldFRpbWVvdXQoKCkgPT4gewogICAgICAgICAgICByb3dzLmZvckVhY2goY2hpbGQgPT4gewogICAgICAgICAgICAgIGlmICghdGhpcy5pZHMuaW5jbHVkZXMoY2hpbGQuaW52ZW50b3J5SWQpKSB7CiAgICAgICAgICAgICAgICB0aGlzLmlkcy5wdXNoKGNoaWxkLmludmVudG9yeUlkKQogICAgICAgICAgICAgICAgdGhpcy5zZWxlY3RlZEludmVudG9yeUxpc3QucHVzaChjaGlsZCkKICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgLy8g5ZyoVUnkuIrpgInkuK3lrZDpobkKICAgICAgICAgICAgICB0aGlzLiRyZWZzLmludmVudG9yeVRhYmxlLnRvZ2dsZVJvd1NlbGVjdGlvbihjaGlsZCwgdHJ1ZSkKICAgICAgICAgICAgfSkKICAgICAgICAgIH0sIDUwKSAvLyDnrYnlvoVET03mm7TmlrAKICAgICAgICB9CiAgICAgIH0pCiAgICB9LAogICAgaGFuZGxlQWRkUGtnKCkgewogICAgICB0aGlzLnJlc2V0KCkKICAgICAgdGhpcy5vcGVuUGtnID0gdHJ1ZQogICAgfSwKICAgIGhhbmRsZVBhY2tpbmdUbygpIHsKICAgICAgLy8g5qOA5p+l5piv5ZCm6YCJ5oup5LqG6LSn54mpCiAgICAgIGlmICh0aGlzLmlkcy5sZW5ndGggPT09IDApIHsKICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoIuivt+WFiOmAieaLqemcgOimgeaJk+WMheeahOi0p+eJqSIpCiAgICAgICAgcmV0dXJuCiAgICAgIH0KCiAgICAgIC8vIOajgOafpeaYr+WQpuS4uuWQjOS4gOWuouaIt+eahOi0p+eJqQogICAgICBjb25zdCBmaXJzdENsaWVudENvZGUgPSB0aGlzLnNlbGVjdGVkSW52ZW50b3J5TGlzdFswXS5jbGllbnRDb2RlCiAgICAgIGNvbnN0IGlzU2FtZUNsaWVudCA9IHRoaXMuc2VsZWN0ZWRJbnZlbnRvcnlMaXN0LmV2ZXJ5KGl0ZW0gPT4gaXRlbS5jbGllbnRDb2RlID09PSBmaXJzdENsaWVudENvZGUpCiAgICAgIGNvbnN0IGlzUGFja2FnZWQgPSB0aGlzLnNlbGVjdGVkSW52ZW50b3J5TGlzdC5ldmVyeShpdGVtID0+IGl0ZW0ucGFja2FnZUludG9ObyA9PSBudWxsKQoKICAgICAgaWYgKCFpc1NhbWVDbGllbnQpIHsKICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoIuWPquiDveaJk+WMheWQjOS4gOS4quWuouaIt+eahOi0p+eJqSIpCiAgICAgICAgcmV0dXJuCiAgICAgIH0KICAgICAgaWYgKCFpc1BhY2thZ2VkKSB7CiAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCLmnInotKfnianlt7LooqvmiZPljIUiKQogICAgICAgIHJldHVybgogICAgICB9CgogICAgICAvLyDmiZPlvIDmiZPljIXnrrHpgInmi6nlr7nor53moYYKICAgICAgdGhpcy5yZXNldCgpCiAgICAgIHRoaXMuZm9ybS5jbGllbnRDb2RlID0gZmlyc3RDbGllbnRDb2RlCiAgICAgIHRoaXMuZm9ybS5jbGllbnROYW1lID0gdGhpcy5zZWxlY3RlZEludmVudG9yeUxpc3RbMF0uY2xpZW50TmFtZQogICAgICB0aGlzLmZvcm0ucmVwYWNraW5nU3RhdHVzID0gIuaJk+WMheS4rSIKCiAgICAgIC8vIOS9v+eUqOWHhuehrueahOW9k+WJjeaXtumXtO+8jOS4jei/m+ihjOWPluaVtAogICAgICBjb25zdCBub3cgPSBuZXcgRGF0ZSgpCiAgICAgIHRoaXMuZm9ybS5hY3R1YWxJbmJvdW5kVGltZSA9IG5vdwoKICAgICAgdGhpcy5mb3JtLmludmVudG9yeVN0YXR1cyA9ICIwIgogICAgICB0aGlzLmZvcm0uc3ViT3JkZXJObyA9IGZpcnN0Q2xpZW50Q29kZSArICItIgoKICAgICAgLy8g6K6w5b2V6KKr5omT5YyF55qE6LSn54mpSUQKICAgICAgdGhpcy5mb3JtLnBhY2tpbmdTb3VyY2VJZHMgPSB0aGlzLmlkcwoKICAgICAgdGhpcy50aXRsZSA9ICLmiZPljIXoo4XnrrHoh7MiCiAgICAgIHRoaXMub3BlblBrZ1RvID0gdHJ1ZQogICAgfSwKICAgIHBrZ0NhbmNlbCgpIHsKICAgICAgY2FuY2VsUGtnKHRoaXMuc2VsZWN0ZWRQa2dMaXN0KS50aGVuKHJlc3BvbnNlID0+IHsKICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoIuenu+WHuuaIkOWKnyIpCiAgICAgICAgdGhpcy5vcGVuUGtnID0gZmFsc2UKICAgICAgICB0aGlzLmdldExpc3QoKQogICAgICB9KQogICAgfSwKICAgIGxvYWRQa2dUb0xpc3QoKSB7CiAgICAgIGdldFBhY2thZ2Uoe2NsaWVudENvZGU6IHRoaXMuZm9ybS5jbGllbnRDb2RlLCByZXBhY2tpbmdTdGF0dXM6ICLmiZPljIXkuK0ifSkudGhlbihyZXNwb25zZSA9PiB7CiAgICAgICAgdGhpcy5wa2dMaXN0ID0gcmVzcG9uc2UuZGF0YQogICAgICB9KQogICAgfSwKICAgIHBhY2tpbmdUbygpIHsKICAgICAgdGhpcy5zZWxlY3RlZEludmVudG9yeUxpc3QuZm9yRWFjaChpdGVtID0+IHsKICAgICAgICBpdGVtLnBhY2thZ2VUbyA9IHRoaXMuZm9ybS5wYWNrYWdlVG8KICAgICAgICBpdGVtLnJlcGFja2luZ1N0YXR1cyA9ICLooqvmiZPljIUiCiAgICAgIH0pCgogICAgICBwYWNrVXAodGhpcy5zZWxlY3RlZEludmVudG9yeUxpc3QpLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygi5omT5YyF5oiQ5YqfIikKICAgICAgICB0aGlzLm9wZW5Qa2dUbyA9IGZhbHNlCiAgICAgICAgdGhpcy5nZXRMaXN0KCkKICAgICAgfSkKICAgIH0sCiAgICBwYXJzZVRpbWUsCiAgICBoYW5kbGVCbHVyKCkgewogICAgICAvLyDliKTmlq3plb/luqbmmK/lkKblsI/kuo405L2N77yM6Iul5piv5YiZ6KGl6b2QMAogICAgICB0aGlzLmZvcm0uaW5ib3VuZFNlcmlhbE5vU3ViID0gdGhpcy5mb3JtLmluYm91bmRTZXJpYWxOb1N1Yi5wYWRTdGFydCg0LCAiMCIpCiAgICB9LAogICAgc2VsZWN0SW5ib3VuZEZlZSgpIHsKICAgICAgc3dpdGNoICh0aGlzLmZvcm0ucmVjb3JkVHlwZSkgewogICAgICAgIGNhc2UgIuagh+WHhiI6CiAgICAgICAgICB0aGlzLmZvcm0uaW5ib3VuZEZlZSA9IHRoaXMuc2VsZWN0ZWRDbGllbnQuc3RhbmRhcmRJbmJvdW5kRmVlCiAgICAgICAgICBicmVhawogICAgICAgIGNhc2UgIueyvuehriI6CiAgICAgICAgICB0aGlzLmZvcm0uaW5ib3VuZEZlZSA9IHRoaXMuc2VsZWN0ZWRDbGllbnQucHJlY2lzZUluYm91bmRGZWUKICAgICAgICAgIGJyZWFrCiAgICAgICAgY2FzZSAi5b+r6YCSIjoKICAgICAgICAgIHRoaXMuZm9ybS5pbmJvdW5kRmVlID0gdGhpcy5zZWxlY3RlZENsaWVudC5leHByZXNzSW5ib3VuZEZlZQogICAgICAgICAgYnJlYWsKICAgICAgfQogICAgfSwKICAgIGNtVG9DYm0oY21Wb2x1bWUpIHsKICAgICAgcmV0dXJuIGN1cnJlbmN5KGNtVm9sdW1lKS5kaXZpZGUoMV8wMDBfMDAwKS52YWx1ZSAvLyAxIENCTSA9IDEsMDAwLDAwMCBjbcKzCiAgICB9LAogICAgLy8g5qC85byP5YyW5Li65bim6YCX5Y+35ZKM5bCP5pWw55qE5a2X56ym5LiyCiAgICBmb3JtYXROdW1iZXIodmFsdWUpIHsKICAgICAgcmV0dXJuIGN1cnJlbmN5KHZhbHVlLCB7c3ltYm9sOiAiIiwgcHJlY2lzaW9uOiAyfSkuZm9ybWF0KCkgLy8gZWc6IDEyMzQuNTYgPT4gIjEsMjM0LjU2IgogICAgfSwKICAgIC8vIOW9k+eUqOaIt+i+k+WFpeaXtuWunuaXtuagvOW8j+WMlu+8jOS9huS/neeVmeWFieagh+S9jee9rgogICAgZm9ybWF0SW5wdXQoZSkgewogICAgICBjb25zdCByYXdWYWx1ZSA9IGUucmVwbGFjZSgvLC9nLCAiIikgLy8g5Y676Zmk6YCX5Y+3CiAgICAgIGlmICghaXNOYU4ocmF3VmFsdWUpKSB7CiAgICAgICAgdGhpcy5hbW91bnQgPSBwYXJzZUZsb2F0KHJhd1ZhbHVlKSAvLyDmm7TmlrDljp/lp4vlgLwKICAgICAgICByZXR1cm4gdGhpcy5mb3JtYXROdW1iZXIocmF3VmFsdWUpCiAgICAgIH0KICAgIH0sCiAgICBsb2FkQ2FyZ29EZXRhaWwoKSB7CiAgICAgIHRoaXMuZm9ybS5yc0NhcmdvRGV0YWlsc0xpc3QubWFwKGl0ZW0gPT4gewogICAgICAgIHRoaXMudXBkYXRlRm9ybWF0dGVyKGl0ZW0pCiAgICAgIH0pCgogICAgICB0aGlzLiRmb3JjZVVwZGF0ZSgpCiAgICB9LAogICAgdXBkYXRlRm9ybWF0dGVyKHJvdykgewogICAgICByb3cuc2luZ2xlUGllY2VXZWlnaHRGb3JtYXR0ZXIgPSB0aGlzLmZvcm1hdE51bWJlcihyb3cuc2luZ2xlUGllY2VXZWlnaHQpCiAgICAgIHJvdy51bml0TGVuZ3RoRm9ybWF0dGVyID0gdGhpcy5mb3JtYXROdW1iZXIocm93LnVuaXRMZW5ndGgpCiAgICAgIHJvdy51bml0SGVpZ2h0Rm9ybWF0dGVyID0gdGhpcy5mb3JtYXROdW1iZXIocm93LnVuaXRIZWlnaHQpCiAgICAgIHJvdy5zaW5nbGVQaWVjZVZvbHVtZUZvcm1hdHRlciA9IHRoaXMuZm9ybWF0TnVtYmVyKHJvdy5zaW5nbGVQaWVjZVZvbHVtZSkKICAgICAgcm93LnVuaXRHcm9zc1dlaWdodEZvcm1hdHRlciA9IHRoaXMuZm9ybWF0TnVtYmVyKHJvdy51bml0R3Jvc3NXZWlnaHQpCiAgICAgIHJvdy51bml0Vm9sdW1lRm9ybWF0dGVyID0gdGhpcy5mb3JtYXROdW1iZXIocm93LnVuaXRWb2x1bWUpCiAgICAgIHJvdy51bml0V2lkdGhGb3JtYXR0ZXIgPSB0aGlzLmZvcm1hdE51bWJlcihyb3cudW5pdFdpZHRoKQogICAgICByb3cuYm94SXRlbUNvdW50Rm9ybWF0dGVyID0gTnVtYmVyKHJvdy5ib3hJdGVtQ291bnQpID8gTnVtYmVyKHJvdy5ib3hJdGVtQ291bnQpIDogMAogICAgICByb3cuc3VidG90YWxJdGVtQ291bnRGb3JtYXR0ZXIgPSBOdW1iZXIocm93LnN1YnRvdGFsSXRlbUNvdW50KSA/IE51bWJlcihyb3cuc3VidG90YWxJdGVtQ291bnQpIDogMAogICAgfSwKICAgIC8vIOi+k+WFpeWkseeEpuaXtuehruS/neagvOW8j+ato+ehrgogICAgcGFyc2VJbnB1dChyb3csIHR5cGUpIHsKICAgICAgaWYgKCFyb3cgfHwgdHlwZW9mIHJvdyAhPT0gIm9iamVjdCIpIHJldHVybiAvLyDnqbrlgLzmoKHpqozvvJpyb3cg5LiN5a2Y5Zyo5oiW6Z2e5a+56LGh55u05o6l6L+U5ZueCiAgICAgIHN3aXRjaCAodHlwZSkgewogICAgICAgIGNhc2UgInNpbmdsZVBpZWNlV2VpZ2h0IjoKICAgICAgICAgIGNvbnN0IHNpbmdsZVBpZWNlV2VpZ2h0Rm9ybWF0dGVyVmFsdWUgPSBTdHJpbmcocm93LnNpbmdsZVBpZWNlV2VpZ2h0Rm9ybWF0dGVyIHx8ICIiKS5yZXBsYWNlKC8sL2csICIiKSAvLyDnqbrlgLzlronlhajlpITnkIYKICAgICAgICAgIGlmICghaXNOYU4oc2luZ2xlUGllY2VXZWlnaHRGb3JtYXR0ZXJWYWx1ZSkgJiYgc2luZ2xlUGllY2VXZWlnaHRGb3JtYXR0ZXJWYWx1ZSAhPT0gIiIpIHsKICAgICAgICAgICAgcm93LnNpbmdsZVBpZWNlV2VpZ2h0Rm9ybWF0dGVyID0gdGhpcy5mb3JtYXROdW1iZXIoc2luZ2xlUGllY2VXZWlnaHRGb3JtYXR0ZXJWYWx1ZSkKICAgICAgICAgIH0KICAgICAgICAgIGJyZWFrCiAgICAgICAgY2FzZSAidW5pdExlbmd0aCI6CiAgICAgICAgICBjb25zdCB1bml0TGVuZ3RoVmFsdWUgPSBTdHJpbmcocm93LnVuaXRMZW5ndGhGb3JtYXR0ZXIgfHwgIiIpLnJlcGxhY2UoLywvZywgIiIpIC8vIOepuuWAvOWuieWFqOWkhOeQhgogICAgICAgICAgaWYgKCFpc05hTih1bml0TGVuZ3RoVmFsdWUpICYmIHVuaXRMZW5ndGhWYWx1ZSAhPT0gIiIpIHsKICAgICAgICAgICAgcm93LnVuaXRMZW5ndGhGb3JtYXR0ZXIgPSB0aGlzLmZvcm1hdE51bWJlcih1bml0TGVuZ3RoVmFsdWUpCiAgICAgICAgICB9CiAgICAgICAgICBicmVhawogICAgICAgIGNhc2UgInVuaXRXaWR0aCI6CiAgICAgICAgICBjb25zdCB1bml0V2lkdGhWYWx1ZSA9IFN0cmluZyhyb3cudW5pdFdpZHRoRm9ybWF0dGVyIHx8ICIiKS5yZXBsYWNlKC8sL2csICIiKSAvLyDnqbrlgLzlronlhajlpITnkIYKICAgICAgICAgIGlmICghaXNOYU4odW5pdFdpZHRoVmFsdWUpICYmIHVuaXRXaWR0aFZhbHVlICE9PSAiIikgewogICAgICAgICAgICByb3cudW5pdFdpZHRoRm9ybWF0dGVyID0gdGhpcy5mb3JtYXROdW1iZXIodW5pdFdpZHRoVmFsdWUpCiAgICAgICAgICB9CiAgICAgICAgICBicmVhawogICAgICAgIGNhc2UgInVuaXRIZWlnaHQiOgogICAgICAgICAgY29uc3QgdW5pdEhlaWdodEZvcm1hdHRlclZhbHVlID0gU3RyaW5nKHJvdy51bml0SGVpZ2h0Rm9ybWF0dGVyIHx8ICIiKS5yZXBsYWNlKC8sL2csICIiKSAvLyDnqbrlgLzlronlhajlpITnkIYKICAgICAgICAgIGlmICghaXNOYU4odW5pdEhlaWdodEZvcm1hdHRlclZhbHVlKSAmJiB1bml0SGVpZ2h0Rm9ybWF0dGVyVmFsdWUgIT09ICIiKSB7CiAgICAgICAgICAgIHJvdy51bml0SGVpZ2h0Rm9ybWF0dGVyID0gdGhpcy5mb3JtYXROdW1iZXIodW5pdEhlaWdodEZvcm1hdHRlclZhbHVlKQogICAgICAgICAgfQogICAgICAgICAgYnJlYWsKICAgICAgICBjYXNlICJzaW5nbGVQaWVjZVZvbHVtZSI6CiAgICAgICAgICBjb25zdCBzaW5nbGVQaWVjZVZvbHVtZUZvcm1hdHRlclZhbHVlID0gU3RyaW5nKHJvdy5zaW5nbGVQaWVjZVZvbHVtZUZvcm1hdHRlciB8fCAiIikucmVwbGFjZSgvLC9nLCAiIikgLy8g56m65YC85a6J5YWo5aSE55CGCiAgICAgICAgICBpZiAoIWlzTmFOKHNpbmdsZVBpZWNlVm9sdW1lRm9ybWF0dGVyVmFsdWUpICYmIHNpbmdsZVBpZWNlVm9sdW1lRm9ybWF0dGVyVmFsdWUgIT09ICIiKSB7CiAgICAgICAgICAgIHJvdy5zaW5nbGVQaWVjZVZvbHVtZUZvcm1hdHRlciA9IHRoaXMuZm9ybWF0TnVtYmVyKHNpbmdsZVBpZWNlVm9sdW1lRm9ybWF0dGVyVmFsdWUpCiAgICAgICAgICB9CiAgICAgICAgICBicmVhawogICAgICAgIGNhc2UgInVuaXRHcm9zc1dlaWdodCI6CiAgICAgICAgICBjb25zdCB1bml0R3Jvc3NXZWlnaHRGb3JtYXR0ZXJWYWx1ZSA9IFN0cmluZyhyb3cudW5pdEdyb3NzV2VpZ2h0Rm9ybWF0dGVyIHx8ICIiKS5yZXBsYWNlKC8sL2csICIiKSAvLyDnqbrlgLzlronlhajlpITnkIYKICAgICAgICAgIGlmICghaXNOYU4odW5pdEdyb3NzV2VpZ2h0Rm9ybWF0dGVyVmFsdWUpICYmIHVuaXRHcm9zc1dlaWdodEZvcm1hdHRlclZhbHVlICE9PSAiIikgewogICAgICAgICAgICByb3cudW5pdEdyb3NzV2VpZ2h0Rm9ybWF0dGVyID0gdGhpcy5mb3JtYXROdW1iZXIodW5pdEdyb3NzV2VpZ2h0Rm9ybWF0dGVyVmFsdWUpCiAgICAgICAgICB9CiAgICAgICAgICBicmVhawogICAgICAgIGNhc2UgInVuaXRWb2x1bWUiOgogICAgICAgICAgY29uc3QgdW5pdFZvbHVtZUZvcm1hdHRlclZhbHVlID0gU3RyaW5nKHJvdy51bml0Vm9sdW1lRm9ybWF0dGVyIHx8ICIiKS5yZXBsYWNlKC8sL2csICIiKSAvLyDnqbrlgLzlronlhajlpITnkIYKICAgICAgICAgIGlmICghaXNOYU4odW5pdFZvbHVtZUZvcm1hdHRlclZhbHVlKSAmJiB1bml0Vm9sdW1lRm9ybWF0dGVyVmFsdWUgIT09ICIiKSB7CiAgICAgICAgICAgIHJvdy51bml0Vm9sdW1lRm9ybWF0dGVyID0gdGhpcy5mb3JtYXROdW1iZXIodW5pdFZvbHVtZUZvcm1hdHRlclZhbHVlKQogICAgICAgICAgfQogICAgICAgICAgYnJlYWsKICAgICAgfQogICAgfSwKICAgIGNvdW50Q2FyZ29tZWFzdXJlKHJvdywgdHlwZSkgewogICAgICBpZiAoIXJvdyB8fCB0eXBlb2Ygcm93ICE9PSAib2JqZWN0IikgcmV0dXJuIC8vIOepuuWAvOagoemqjO+8mnJvdyDkuI3lrZjlnKjmiJbpnZ7lr7nosaHnm7TmjqXov5Tlm54KICAgICAgc3dpdGNoICh0eXBlKSB7CiAgICAgICAgY2FzZSAic2luZ2xlUGllY2VXZWlnaHQiOgogICAgICAgICAgY29uc3Qgc2luZ2xlUGllY2VXZWlnaHRGb3JtYXR0ZXJWYWx1ZSA9IFN0cmluZyhyb3cuc2luZ2xlUGllY2VXZWlnaHRGb3JtYXR0ZXIgfHwgIiIpLnJlcGxhY2UoLywvZywgIiIpIC8vIOepuuWAvOWuieWFqOWkhOeQhgogICAgICAgICAgaWYgKCFpc05hTihzaW5nbGVQaWVjZVdlaWdodEZvcm1hdHRlclZhbHVlKSAmJiBzaW5nbGVQaWVjZVdlaWdodEZvcm1hdHRlclZhbHVlICE9PSAiIikgewogICAgICAgICAgICByb3cuc2luZ2xlUGllY2VXZWlnaHQgPSBwYXJzZUZsb2F0KHNpbmdsZVBpZWNlV2VpZ2h0Rm9ybWF0dGVyVmFsdWUpIC8vIOabtOaWsOWOn+Wni+WAvAogICAgICAgICAgfQogICAgICAgICAgYnJlYWsKICAgICAgICBjYXNlICJ1bml0TGVuZ3RoIjoKICAgICAgICAgIGNvbnN0IHVuaXRMZW5ndGhWYWx1ZSA9IFN0cmluZyhyb3cudW5pdExlbmd0aEZvcm1hdHRlciB8fCAiIikucmVwbGFjZSgvLC9nLCAiIikgLy8g56m65YC85a6J5YWo5aSE55CGCiAgICAgICAgICBpZiAoIWlzTmFOKHVuaXRMZW5ndGhWYWx1ZSkgJiYgdW5pdExlbmd0aFZhbHVlICE9PSAiIikgewogICAgICAgICAgICByb3cudW5pdExlbmd0aCA9IHBhcnNlRmxvYXQodW5pdExlbmd0aFZhbHVlKSAvLyDmm7TmlrDljp/lp4vlgLwKICAgICAgICAgIH0KICAgICAgICAgIGJyZWFrCiAgICAgICAgY2FzZSAidW5pdFdpZHRoIjoKICAgICAgICAgIGNvbnN0IHVuaXRXaWR0aFZhbHVlID0gU3RyaW5nKHJvdy51bml0V2lkdGhGb3JtYXR0ZXIgfHwgIiIpLnJlcGxhY2UoLywvZywgIiIpIC8vIOepuuWAvOWuieWFqOWkhOeQhgogICAgICAgICAgaWYgKCFpc05hTih1bml0V2lkdGhWYWx1ZSkgJiYgdW5pdFdpZHRoVmFsdWUgIT09ICIiKSB7CiAgICAgICAgICAgIHJvdy51bml0V2lkdGggPSBwYXJzZUZsb2F0KHVuaXRXaWR0aFZhbHVlKSAvLyDmm7TmlrDljp/lp4vlgLwKICAgICAgICAgIH0KICAgICAgICAgIGJyZWFrCiAgICAgICAgY2FzZSAidW5pdEhlaWdodCI6CiAgICAgICAgICBjb25zdCB1bml0SGVpZ2h0Rm9ybWF0dGVyVmFsdWUgPSBTdHJpbmcocm93LnVuaXRIZWlnaHRGb3JtYXR0ZXIgfHwgIiIpLnJlcGxhY2UoLywvZywgIiIpIC8vIOepuuWAvOWuieWFqOWkhOeQhgogICAgICAgICAgaWYgKCFpc05hTih1bml0SGVpZ2h0Rm9ybWF0dGVyVmFsdWUpICYmIHVuaXRIZWlnaHRGb3JtYXR0ZXJWYWx1ZSAhPT0gIiIpIHsKICAgICAgICAgICAgcm93LnVuaXRIZWlnaHQgPSBwYXJzZUZsb2F0KHVuaXRIZWlnaHRGb3JtYXR0ZXJWYWx1ZSkgLy8g5pu05paw5Y6f5aeL5YC8CiAgICAgICAgICB9CiAgICAgICAgICBicmVhawogICAgICAgIGNhc2UgInNpbmdsZVBpZWNlVm9sdW1lIjoKICAgICAgICAgIGNvbnN0IHNpbmdsZVBpZWNlVm9sdW1lRm9ybWF0dGVyVmFsdWUgPSBTdHJpbmcocm93LnNpbmdsZVBpZWNlVm9sdW1lRm9ybWF0dGVyIHx8ICIiKS5yZXBsYWNlKC8sL2csICIiKSAvLyDnqbrlgLzlronlhajlpITnkIYKICAgICAgICAgIGlmICghaXNOYU4oc2luZ2xlUGllY2VWb2x1bWVGb3JtYXR0ZXJWYWx1ZSkgJiYgc2luZ2xlUGllY2VWb2x1bWVGb3JtYXR0ZXJWYWx1ZSAhPT0gIiIpIHsKICAgICAgICAgICAgcm93LnNpbmdsZVBpZWNlVm9sdW1lID0gcGFyc2VGbG9hdChzaW5nbGVQaWVjZVZvbHVtZUZvcm1hdHRlclZhbHVlKSAvLyDmm7TmlrDljp/lp4vlgLwKICAgICAgICAgIH0KICAgICAgICAgIGJyZWFrCiAgICAgICAgY2FzZSAidW5pdEdyb3NzV2VpZ2h0IjoKICAgICAgICAgIGNvbnN0IHVuaXRHcm9zc1dlaWdodEZvcm1hdHRlclZhbHVlID0gU3RyaW5nKHJvdy51bml0R3Jvc3NXZWlnaHRGb3JtYXR0ZXIgfHwgIiIpLnJlcGxhY2UoLywvZywgIiIpIC8vIOepuuWAvOWuieWFqOWkhOeQhgogICAgICAgICAgaWYgKCFpc05hTih1bml0R3Jvc3NXZWlnaHRGb3JtYXR0ZXJWYWx1ZSkgJiYgdW5pdEdyb3NzV2VpZ2h0Rm9ybWF0dGVyVmFsdWUgIT09ICIiKSB7CiAgICAgICAgICAgIHJvdy51bml0R3Jvc3NXZWlnaHQgPSBwYXJzZUZsb2F0KHVuaXRHcm9zc1dlaWdodEZvcm1hdHRlclZhbHVlKSAvLyDmm7TmlrDljp/lp4vlgLwKICAgICAgICAgIH0KICAgICAgICAgIGJyZWFrCiAgICAgICAgY2FzZSAidW5pdFZvbHVtZSI6CiAgICAgICAgICBjb25zdCB1bml0Vm9sdW1lRm9ybWF0dGVyVmFsdWUgPSBTdHJpbmcocm93LnVuaXRWb2x1bWVGb3JtYXR0ZXIgfHwgIiIpLnJlcGxhY2UoLywvZywgIiIpIC8vIOepuuWAvOWuieWFqOWkhOeQhgogICAgICAgICAgaWYgKCFpc05hTih1bml0Vm9sdW1lRm9ybWF0dGVyVmFsdWUpICYmIHVuaXRWb2x1bWVGb3JtYXR0ZXJWYWx1ZSAhPT0gIiIpIHsKICAgICAgICAgICAgcm93LnVuaXRWb2x1bWUgPSBwYXJzZUZsb2F0KHVuaXRWb2x1bWVGb3JtYXR0ZXJWYWx1ZSkgLy8g5pu05paw5Y6f5aeL5YC8CiAgICAgICAgICB9CiAgICAgICAgICBicmVhawogICAgICB9CgogICAgICAvLyDmm7TmlrDnm7jlhbPlrZfmrrUKICAgICAgdGhpcy5mb3JtLnJzQ2FyZ29EZXRhaWxzTGlzdCA9IHRoaXMuZm9ybS5yc0NhcmdvRGV0YWlsc0xpc3QubWFwKGl0ZW0gPT4gewogICAgICAgIGlmIChyb3cgPT09IGl0ZW0pIHsKICAgICAgICAgIGlmIChpdGVtLnVuaXRMZW5ndGggJiYgaXRlbS51bml0V2lkdGggJiYgaXRlbS51bml0SGVpZ2h0KSB7CiAgICAgICAgICAgIGNvbnN0IGNtID0gY3VycmVuY3koaXRlbS51bml0TGVuZ3RoKS5tdWx0aXBseShpdGVtLnVuaXRXaWR0aCkubXVsdGlwbHkoaXRlbS51bml0SGVpZ2h0KS52YWx1ZQogICAgICAgICAgICBpdGVtLnNpbmdsZVBpZWNlVm9sdW1lID0gdGhpcy5jbVRvQ2JtKGNtKQogICAgICAgICAgICBpdGVtLnNpbmdsZVBpZWNlVm9sdW1lRm9ybWF0dGVyID0gdGhpcy5mb3JtYXROdW1iZXIoaXRlbS5zaW5nbGVQaWVjZVZvbHVtZSkKICAgICAgICAgIH0KICAgICAgICAgIGlmIChpdGVtLnNpbmdsZVBpZWNlVm9sdW1lICYmIGl0ZW0uYm94Q291bnQpIHsKICAgICAgICAgICAgaXRlbS51bml0Vm9sdW1lID0gY3VycmVuY3koaXRlbS5zaW5nbGVQaWVjZVZvbHVtZSkubXVsdGlwbHkoaXRlbS5ib3hDb3VudCkudmFsdWUKICAgICAgICAgICAgaXRlbS51bml0Vm9sdW1lRm9ybWF0dGVyID0gdGhpcy5mb3JtYXROdW1iZXIoaXRlbS51bml0Vm9sdW1lKQogICAgICAgICAgfQogICAgICAgICAgaWYgKGl0ZW0uc2luZ2xlUGllY2VXZWlnaHQgJiYgaXRlbS5ib3hDb3VudCkgewogICAgICAgICAgICBpdGVtLnVuaXRHcm9zc1dlaWdodCA9IGN1cnJlbmN5KGl0ZW0uc2luZ2xlUGllY2VXZWlnaHQpLm11bHRpcGx5KGl0ZW0uYm94Q291bnQpLnZhbHVlCiAgICAgICAgICAgIGl0ZW0udW5pdEdyb3NzV2VpZ2h0Rm9ybWF0dGVyID0gdGhpcy5mb3JtYXROdW1iZXIoaXRlbS51bml0R3Jvc3NXZWlnaHQpCiAgICAgICAgICB9CiAgICAgICAgICAvLyDku7bmlbDlsI/orqEKICAgICAgICAgIGlmIChpdGVtLmJveEl0ZW1Db3VudCkgewogICAgICAgICAgICBpZiAoaXRlbS5ib3hDb3VudCkgewogICAgICAgICAgICAgIGl0ZW0uc3VidG90YWxJdGVtQ291bnQgPSBjdXJyZW5jeShpdGVtLmJveEl0ZW1Db3VudCkubXVsdGlwbHkoaXRlbS5ib3hDb3VudCkudmFsdWUKICAgICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgICBpdGVtLnN1YnRvdGFsSXRlbUNvdW50ID0gY3VycmVuY3koaXRlbS5ib3hJdGVtQ291bnQpLnZhbHVlCiAgICAgICAgICAgIH0KICAgICAgICAgIH0KICAgICAgICB9CiAgICAgICAgcmV0dXJuIGl0ZW0KICAgICAgfSkKCiAgICAgIC8vIHRoaXMuJGZvcmNlVXBkYXRlKCkKICAgIH0sCiAgICBzZWxlY3RXYXJlaG91c2VDbGllbnQocm93KSB7CiAgICAgIHRoaXMuZm9ybS5jbGllbnRDb2RlID0gcm93LmNsaWVudENvZGUKICAgICAgdGhpcy5mb3JtLmNsaWVudE5hbWUgPSByb3cuY2xpZW50TmFtZQogICAgICB0aGlzLmZvcm0ub3ZlcmR1ZVJlbnRhbFVuaXRQcmljZSA9IHJvdy5vdmVyZHVlUmVudAogICAgICB0aGlzLmZvcm0uZnJlZVN0YWNrUGVyaW9kID0gcm93LmZyZWVTdGFja1BlcmlvZAogICAgICB0aGlzLnNlbGVjdGVkQ2xpZW50ID0gcm93CiAgICAgIC8qIGlmIChyb3cuY2xpZW50VHlwZSA9PT0gIuebtOWuoiIpIHsKICAgICAgICB0aGlzLmZvcm0uaW5ib3VuZFNlcmlhbE5vUHJlID0gIk5vLjkwIgogICAgICB9IGVsc2UgewogICAgICAgIHRoaXMuZm9ybS5pbmJvdW5kU2VyaWFsTm9QcmUgPSAiTm8uODAiCiAgICAgIH0gKi8KICAgICAgaWYgKHJvdy5pbmNsdWRlc1VubG9hZGluZ0ZlZSAhPSBudWxsKSB7CiAgICAgICAgdGhpcy5mb3JtLmluY2x1ZGVzVW5sb2FkaW5nRmVlID0gcm93LmluY2x1ZGVzVW5sb2FkaW5nRmVlCiAgICAgICAgLyogaWYgKHJvdy5pbmNsdWRlc1VubG9hZGluZ0ZlZSA9PSAxKSB7CiAgICAgICAgICB0aGlzLmZvcm0udW5wYWlkVW5sb2FkaW5nRmVlID0gMAogICAgICAgIH0gKi8KICAgICAgfQogICAgICBpZiAocm93LmluY2x1ZGVzUGFja2luZ0ZlZSAhPSBudWxsKSB7CiAgICAgICAgdGhpcy5mb3JtLmluY2x1ZGVzUGFja2luZ0ZlZSA9IHJvdy5pbmNsdWRlc1BhY2tpbmdGZWUKICAgICAgICBpZiAocm93LmluY2x1ZGVzUGFja2luZ0ZlZSA9PSAxKSB7CiAgICAgICAgICB0aGlzLmZvcm0udW5wYWlkUGFja2luZ0ZlZSA9IDAKICAgICAgICB9CiAgICAgIH0KICAgICAgaWYgKHJvdy5pbmNsdWRlc0luYm91bmRGZWUgIT0gbnVsbCkgewogICAgICAgIHRoaXMuZm9ybS5pbmNsdWRlc0luYm91bmRGZWUgPSByb3cuaW5jbHVkZXNJbmJvdW5kRmVlCiAgICAgICAgaWYgKHJvdy5pbmNsdWRlc0luYm91bmRGZWUgPT0gMSkgewogICAgICAgICAgdGhpcy5mb3JtLmluYm91bmRGZWUgPSAwCiAgICAgICAgfQogICAgICB9CiAgICAgIGlmIChyb3cuaW1tZWRpYXRlUGF5bWVudEZlZSkgewogICAgICAgIHRoaXMuZm9ybS5pbW1lZGlhdGVQYXltZW50RmVlID0gcm93LmltbWVkaWF0ZVBheW1lbnRGZWUKICAgICAgfQogICAgICAvLyDmoLnmja7orrDlvZXmlrnlvI/pgInmi6nlhaXku5PmoIflh4botLkKICAgICAgaWYgKHRoaXMuZm9ybS5yZWNvcmRUeXBlICYmIHJvdy5pbmNsdWRlc0luYm91bmRGZWUgPT0gMCkgewogICAgICAgIHN3aXRjaCAodGhpcy5mb3JtLnJlY29yZFR5cGUpIHsKICAgICAgICAgIGNhc2UgIuagh+WHhiI6CiAgICAgICAgICAgIHRoaXMuZm9ybS5pbmJvdW5kRmVlID0gcm93LnN0YW5kYXJkSW5ib3VuZEZlZQogICAgICAgICAgICBicmVhawogICAgICAgICAgY2FzZSAi57K+56GuIjoKICAgICAgICAgICAgdGhpcy5mb3JtLmluYm91bmRGZWUgPSByb3cucHJlY2lzZUluYm91bmRGZWUKICAgICAgICAgICAgYnJlYWsKICAgICAgICAgIGNhc2UgIuW/q+mAkiI6CiAgICAgICAgICAgIHRoaXMuZm9ybS5pbmJvdW5kRmVlID0gcm93LmV4cHJlc3NJbmJvdW5kRmVlCiAgICAgICAgICAgIGJyZWFrCiAgICAgICAgfQogICAgICB9CgogICAgICAvLyDlvZPlrqLmiLfku6PnoIHmlLnlj5jml7Ys5riF56m65YiG5Y2V5Y+3CiAgICAgIHRoaXMuZm9ybS5zdWJPcmRlck5vID0gIiIKICAgICAgdGhpcy5mb3JtLnN1Yk9yZGVyTm8gPSByb3cuY2xpZW50Q29kZSArICItIgogICAgfSwKICAgIGdldFN1bW1hcmllc0ludmVudG9yeShwYXJhbSkgewogICAgICBjb25zdCB7Y29sdW1ucywgZGF0YX0gPSBwYXJhbQogICAgICBjb25zdCBzdW1zID0gW10KICAgICAgY29uc3Qgc3RhdGlzdGljYWxGaWVsZCA9IFsidG90YWxCb3hlcyIsICJ0b3RhbEdyb3NzV2VpZ2h0IiwgInRvdGFsVm9sdW1lIiwKICAgICAgICAiaW5ib3VuZEZlZSIsICJyZWNlaXZlZFN0b3JhZ2VGZWUiLCAidW5wYWlkVW5sb2FkaW5nRmVlIiwgInJlY2VpdmVkVW5sb2FkaW5nRmVlIiwKICAgICAgICAidW5wYWlkUGFja2luZ0ZlZSIsICJyZWNlaXZlZFBhY2tpbmdGZWUiLCAibG9naXN0aWNzQWR2YW5jZUZlZSJdCgogICAgICBjb2x1bW5zLmZvckVhY2goKGNvbHVtbiwgaW5kZXgpID0+IHsKICAgICAgICBpZiAoaW5kZXggPT09IDApIHsKICAgICAgICAgIHN1bXNbaW5kZXhdID0gIuaAu+iuoToiCiAgICAgICAgICByZXR1cm4KICAgICAgICB9CgogICAgICAgIC8vIOetm+mAieWHuuiiq+e7n+iuoeeahOWtl+autQogICAgICAgIGlmIChzdGF0aXN0aWNhbEZpZWxkLmluY2x1ZGVzKGNvbHVtbi5wcm9wZXJ0eSkpIHsKICAgICAgICAgIC8vIOWwhuaVsOaNrui9rOS4uuacieaViOaVsOWtlwogICAgICAgICAgY29uc3QgdmFsdWVzID0gZGF0YQogICAgICAgICAgICAubWFwKGl0ZW0gPT4gTnVtYmVyKGl0ZW1bY29sdW1uLnByb3BlcnR5XSkpCiAgICAgICAgICAgIC5maWx0ZXIodmFsdWUgPT4gIWlzTmFOKHZhbHVlKSkgLy8g5o6S6Zmk6Z2e5pWw5a2XCgogICAgICAgICAgaWYgKHZhbHVlcy5sZW5ndGggPiAwKSB7CiAgICAgICAgICAgIC8vIOiuoeeul+aAu+WSjOW5tuagvOW8j+WMlgogICAgICAgICAgICBjb25zdCB0b3RhbCA9IHZhbHVlcy5yZWR1Y2UoKHByZXYsIGN1cnIpID0+IHByZXYgKyBjdXJyLCAwKQogICAgICAgICAgICBzdW1zW2luZGV4XSA9CiAgICAgICAgICAgICAgdGhpcy5mb3JtYXROdW1iZXJGaXhlZCh0b3RhbCkgKwogICAgICAgICAgICAgIChjb2x1bW4ucHJvcGVydHkgPT09ICJ0b3RhbFZvbHVtZSIKICAgICAgICAgICAgICAgID8gIiBDQk0iCiAgICAgICAgICAgICAgICA6IGNvbHVtbi5wcm9wZXJ0eSA9PT0gInRvdGFsR3Jvc3NXZWlnaHQiCiAgICAgICAgICAgICAgICAgID8gIiBLR1MiCiAgICAgICAgICAgICAgICAgIDogY29sdW1uLnByb3BlcnR5ID09PSAidG90YWxCb3hlcyIKICAgICAgICAgICAgICAgICAgICA/ICIg5Lu2IgogICAgICAgICAgICAgICAgICAgIDogIiIpCiAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICBzdW1zW2luZGV4XSA9ICIgIgogICAgICAgICAgfQogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICBzdW1zW2luZGV4XSA9ICIgIgogICAgICAgIH0KICAgICAgfSkKCiAgICAgIHJldHVybiBzdW1zCiAgICB9LAogICAgZ2V0U3VtbWFyaWVzKHBhcmFtKSB7CiAgICAgIGNvbnN0IHtjb2x1bW5zLCBkYXRhfSA9IHBhcmFtCiAgICAgIGNvbnN0IHN1bXMgPSBbXQogICAgICBjb25zdCBzdGF0aXN0aWNhbEZpZWxkID0gWyJ1bml0Vm9sdW1lIiwgInVuaXRHcm9zc1dlaWdodCIsICJib3hDb3VudCJdCgogICAgICBjb2x1bW5zLmZvckVhY2goKGNvbHVtbiwgaW5kZXgpID0+IHsKICAgICAgICBpZiAoaW5kZXggPT09IDApIHsKICAgICAgICAgIHN1bXNbaW5kZXhdID0gIuaAu+iuoToiCiAgICAgICAgICByZXR1cm4KICAgICAgICB9CgogICAgICAgIC8vIOetm+mAieWHuuiiq+e7n+iuoeeahOWtl+autQogICAgICAgIGlmIChzdGF0aXN0aWNhbEZpZWxkLmluY2x1ZGVzKGNvbHVtbi5wcm9wZXJ0eSkpIHsKICAgICAgICAgIC8vIOWwhuaVsOaNrui9rOS4uuacieaViOaVsOWtlwogICAgICAgICAgY29uc3QgdmFsdWVzID0gZGF0YQogICAgICAgICAgICAubWFwKGl0ZW0gPT4gTnVtYmVyKGl0ZW1bY29sdW1uLnByb3BlcnR5XSkpCiAgICAgICAgICAgIC5maWx0ZXIodmFsdWUgPT4gIWlzTmFOKHZhbHVlKSkgLy8g5o6S6Zmk6Z2e5pWw5a2XCgogICAgICAgICAgaWYgKHZhbHVlcy5sZW5ndGggPiAwKSB7CiAgICAgICAgICAgIC8vIOiuoeeul+aAu+WSjOW5tuagvOW8j+WMlgogICAgICAgICAgICBjb25zdCB0b3RhbCA9IHZhbHVlcy5yZWR1Y2UoKHByZXYsIGN1cnIpID0+IHByZXYgKyBjdXJyLCAwKQogICAgICAgICAgICBzdW1zW2luZGV4XSA9CiAgICAgICAgICAgICAgdGhpcy5mb3JtYXROdW1iZXJGaXhlZCh0b3RhbCkgKwogICAgICAgICAgICAgIChjb2x1bW4ucHJvcGVydHkgPT09ICJ1bml0Vm9sdW1lIgogICAgICAgICAgICAgICAgPyAiIENCTSIKICAgICAgICAgICAgICAgIDogY29sdW1uLnByb3BlcnR5ID09PSAidW5pdEdyb3NzV2VpZ2h0IgogICAgICAgICAgICAgICAgICA/ICIgS0dTIgogICAgICAgICAgICAgICAgICA6IGNvbHVtbi5wcm9wZXJ0eSA9PT0gImJveENvdW50IgogICAgICAgICAgICAgICAgICAgID8gIiDku7YiCiAgICAgICAgICAgICAgICAgICAgOiAiIikKICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgIHN1bXNbaW5kZXhdID0gIiAiCiAgICAgICAgICB9CiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIHN1bXNbaW5kZXhdID0gIiAiCiAgICAgICAgfQogICAgICB9KQoKICAgICAgcmV0dXJuIHN1bXMKICAgIH0sCiAgICBmb3JtYXROdW1iZXJGaXhlZCh2YWx1ZSkgewogICAgICByZXR1cm4gTnVtYmVyKHZhbHVlKS50b0ZpeGVkKDIpIC8vIOS/neeVmeS4pOS9jeWwj+aVsAogICAgfSwKICAgIGhhbmRsZU9wT3V0Ym91bmQoKSB7CiAgICAgIHRoaXMub3Blbk91dGJvdW5kID0gdHJ1ZQogICAgfSwKICAgIGluaXRQcmludCgpIHsKICAgICAgaGlwcmludC5pbml0KHsKICAgICAgICBwcm92aWRlcnM6IFtuZXcgZGVmYXVsdEVsZW1lbnRUeXBlUHJvdmlkZXIoKV0KICAgICAgfSkKICAgIH0sCiAgICBwcmludEluYm91bmRCaWxsKHR5cGUpIHsKICAgICAgbGV0IGRhdGEgPSB7fQogICAgICBkYXRhLmNsaWVudENvZGUgPSB0aGlzLmZvcm0uY2xpZW50Q29kZQogICAgICBkYXRhLnN1Yk9yZGVyTm8gPSB0aGlzLmZvcm0uc3ViT3JkZXJObwogICAgICBkYXRhLmluYm91bmRTZXJpYWxObyA9IHRoaXMuZm9ybS5pbmJvdW5kU2VyaWFsTm8KICAgICAgZGF0YS5mb3J3YXJkZXJObyA9IHRoaXMuZm9ybS5mb3J3YXJkZXJObwogICAgICBkYXRhLmFjdHVhbEluYm91bmRUaW1lID0gbW9tZW50KHRoaXMuZm9ybS5hY3R1YWxJbmJvdW5kVGltZSkuZm9ybWF0KCJ5eXl5LU1NLUREIEhIOm1tOnNzIikKICAgICAgZGF0YS5zcWRTaGlwcGluZ01hcmsgPSB0aGlzLmZvcm0uc3FkU2hpcHBpbmdNYXJrCiAgICAgIGRhdGEuY2FyZ29OYW1lID0gdGhpcy5mb3JtLmNhcmdvTmFtZQogICAgICBkYXRhLnRvdGFsQm94ZXMgPSB0aGlzLmZvcm0udG90YWxCb3hlcwogICAgICBkYXRhLnRvdGFsR3Jvc3NXZWlnaHQgPSB0aGlzLmZvcm0udG90YWxHcm9zc1dlaWdodAogICAgICBkYXRhLnRvdGFsVm9sdW1lID0gdGhpcy5mb3JtLnRvdGFsVm9sdW1lCiAgICAgIGRhdGEubm90ZXMgPSB0aGlzLmZvcm0ubm90ZXMKICAgICAgZGF0YS5zdXBwbGllciA9IHRoaXMuZm9ybS5zdXBwbGllcgogICAgICBkYXRhLmRyaXZlckluZm8gPSB0aGlzLmZvcm0uZHJpdmVySW5mbwogICAgICBkYXRhLmluYm91bmRBZGRyID0gdGhpcy5mb3JtLmluYm91bmRBZGRyCiAgICAgIGRhdGEuY29uc2lnbmVlTmFtZSA9IHRoaXMuZm9ybS5jb25zaWduZWVOYW1lCiAgICAgIGRhdGEuaW5ib3VuZFNlcmlhbE5vID0gdGhpcy5mb3JtLmluYm91bmRTZXJpYWxObwoKICAgICAgZGF0YS5leHByZXNzTm8gPSB0aGlzLmZvcm0uZHJpdmVySW5mbwoKICAgICAgaWYgKHR5cGUgPT09ICLml6fmqKHmnb8iKSB7CiAgICAgICAgaGlwcmludFRlbXBsYXRlID0gbmV3IGhpcHJpbnQuUHJpbnRUZW1wbGF0ZSh7dGVtcGxhdGU6IHdhcmVob3VzZVJlY2VpcHR9KQogICAgICB9IGVsc2UgewogICAgICAgIGhpcHJpbnRUZW1wbGF0ZSA9IG5ldyBoaXByaW50LlByaW50VGVtcGxhdGUoe3RlbXBsYXRlOiB3YXJlaG91c2VSZWNlaXB0TmV3fSkKICAgICAgfQogICAgICAvLyDmiZPlvIDpooTop4jnu4Tku7YKICAgICAgLy8gdGhpcy4kcmVmcy5wcmVWaWV3LnNob3coaGlwcmludFRlbXBsYXRlLCBkYXRhKQogICAgICAvLyDnm7TmjqXmiZPljbAKICAgICAgdGhpcy4kcmVmcy5wcmVWaWV3LnByaW50KGhpcHJpbnRUZW1wbGF0ZSwgZGF0YSkKICAgIH0sCiAgICBkZWxldGVDYXJnb0RldGFpbChyb3cpIHsKICAgICAgdGhpcy5mb3JtLnJzQ2FyZ29EZXRhaWxzTGlzdCA9IHRoaXMuZm9ybS5yc0NhcmdvRGV0YWlsc0xpc3QuZmlsdGVyKGl0ZW0gPT4gaXRlbSAhPT0gcm93KQogICAgICB0aGlzLiRmb3JjZVVwZGF0ZSgpCiAgICB9LAogICAgaGFuZGxlVXBkYXRlQ2FyZ29EZXRhaWwocm93KSB7CiAgICAgIHRoaXMuY2FyZ29EZXRhaWxPcGVuID0gdHJ1ZQogICAgICB0aGlzLmNhcmdvRGV0YWlsUm93ID0gcm93CiAgICB9LAogICAgaGFuZGxlUGtnU2VsZWN0aW9uQ2hhbmdlKHNlbGVjdGlvbikgewogICAgICB0aGlzLnNlbGVjdGVkUGtnTGlzdCA9IHNlbGVjdGlvbgogICAgfSwKICAgIHBrZ0ZpbmlzaCgpIHsKICAgICAgaWYgKHRoaXMuZm9ybS5yZXBhY2tpbmdTdGF0dXMgPT09ICLmiZPljIXlrowiKSB7CiAgICAgICAgdGhpcy5mb3JtLnJlcGFja2luZ1N0YXR1cyA9ICLmiZPljIXkuK0iCiAgICAgIH0gZWxzZSB7CiAgICAgICAgdGhpcy5mb3JtLnJlcGFja2luZ1N0YXR1cyA9ICLmiZPljIXlrowiCiAgICAgIH0KCiAgICAgIHVwZGF0ZUludmVudG9yeSh0aGlzLmZvcm0pLnRoZW4ocmVzID0+IHsKICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoIuS/ruaUueaIkOWKnyIpCiAgICAgIH0pCiAgICB9LAogICAgYWRkQ2FyZ29EZXRhaWxSb3cocm93KSB7CgogICAgfSwKICAgIGFkZENhcmdvRGV0YWlsKCkgewogICAgICB0aGlzLmZvcm0ucnNDYXJnb0RldGFpbHNMaXN0LnB1c2godGhpcy5fLmNsb25lRGVlcCh0aGlzLmNhcmdvRGV0YWlsUm93KSkKICAgICAgdGhpcy5jYXJnb0RldGFpbE9wZW4gPSB0cnVlCiAgICB9LAogICAgbGlzdEFnZ3JlZ2F0b3JSc0ludmVudG9yeShwYXJhbXMpIHsKICAgICAgcGFyYW1zLmNvbmZpZyA9IEpTT04uc3RyaW5naWZ5KHBhcmFtcy5jb25maWcpCiAgICAgIHRoaXMucXVlcnlQYXJhbXMucGFyYW1zID0gcGFyYW1zCiAgICAgIHJldHVybiBsaXN0QWdncmVnYXRvclJzSW52ZW50b3J5KHRoaXMucXVlcnlQYXJhbXMpCiAgICB9LAogICAgLyoqIOafpeivouW6k+WtmOWIl+ihqCAqLwogICAgZ2V0TGlzdCgpIHsKICAgICAgdGhpcy5sb2FkaW5nID0gdHJ1ZQogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnBlcm1pc3Npb25MZXZlbCA9IHRoaXMuJHN0b3JlLnN0YXRlLnVzZXIucGVybWlzc2lvbkxldmVsTGlzdC5DCiAgICAgIC8vIOa3u+WKoOadoeS7tu+8jOWPquafpeivoumhtuWxguaVsOaNru+8iOayoeacieeItue6p+eahOaVsOaNru+8iQogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLmlzVG9wTGV2ZWwgPSB0cnVlCiAgICAgIGxpc3RJbnZlbnRvcnkodGhpcy5xdWVyeVBhcmFtcykudGhlbihyZXNwb25zZSA9PiB7CiAgICAgICAgLy8g5aSE55CG5pWw5o2u77yM5qCH6K6w5pyJ5a2Q6IqC54K555qE5pWw5o2uCiAgICAgICAgbGV0IHJvd3MgPSByZXNwb25zZS5yb3dzCgogICAgICAgIC8vIOWmguaenOWQjuerr+S4jeaUr+aMgWlzVG9wTGV2ZWzlj4LmlbDvvIzlnKjliY3nq6/ov5vooYzov4fmu6QKICAgICAgICAvLyDku4XlvZPlv6vpgJLljZXlj7fmnKrloavlhpnml7bmiY3ov4fmu6RwYWNrYWdlVG/vvIzkv53or4Hlv6vpgJLmn6Xor6Lml7bog73mmL7npLrmiYDmnInljLnphY3nmoTorrDlvZUKICAgICAgICBpZiAodGhpcy5xdWVyeVBhcmFtcy5pc1RvcExldmVsICYmICF0aGlzLnF1ZXJ5UGFyYW1zLmRyaXZlckluZm8pIHsKICAgICAgICAgIHJvd3MgPSByb3dzLmZpbHRlcihpdGVtID0+ICFpdGVtLnBhY2thZ2VUbykKICAgICAgICB9CgogICAgICAgIHRoaXMuaW52ZW50b3J5TGlzdCA9IHJvd3MubWFwKGl0ZW0gPT4gewogICAgICAgICAgLy8g5aaC5p6c5piv5omT5YyF566x77yM5qCH6K6w5Li65pyJ5a2Q6IqC54K5CiAgICAgICAgICBpZiAoaXRlbS5wYWNrYWdlUmVjb3JkID09PSAiMSIpIHsKICAgICAgICAgICAgaXRlbS5oYXNDaGlsZHJlbiA9IHRydWUKICAgICAgICAgIH0KICAgICAgICAgIHJldHVybiBpdGVtCiAgICAgICAgfSkKICAgICAgICB0aGlzLnRvdGFsID0gcmVzcG9uc2UudG90YWwKICAgICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZQogICAgICB9KQogICAgfSwKICAgIC8vIOWPlua2iOaMiemSrgogICAgY2FuY2VsKCkgewogICAgICB0aGlzLm9wZW4gPSBmYWxzZQogICAgICB0aGlzLnJlc2V0KCkKICAgIH0sCiAgICAvLyDooajljZXph43nva4KICAgIHJlc2V0KCkgewogICAgICB0aGlzLnBrZ0RldGFpbHNMaXN0ID0gW10KICAgICAgdGhpcy5mb3JtID0gewogICAgICAgIHJlcGFja2luZ1N0YXR1czogIi0iLAogICAgICAgIGludmVudG9yeUlkOiBudWxsLAogICAgICAgIGludmVudG9yeVN0YXR1czogIjAiLAogICAgICAgIGluYm91bmRTZXJpYWxObzogbnVsbCwKICAgICAgICBpbmJvdW5kU2VyaWFsTm9QcmU6IG51bGwsCiAgICAgICAgaW5ib3VuZFNlcmlhbFNwbGl0OiBudWxsLAogICAgICAgIGluYm91bmREYXRlOiBudWxsLAogICAgICAgIG91dGJvdW5kTm86IG51bGwsCiAgICAgICAgZm9yd2FyZGVyTm86IG51bGwsCiAgICAgICAgcmVudGFsU2V0dGxlbWVudERhdGU6IG51bGwsCiAgICAgICAgb3V0Ym91bmREYXRlOiBudWxsLAogICAgICAgIGNsaWVudENvZGU6IG51bGwsCiAgICAgICAgc3ViT3JkZXJObzogbnVsbCwKICAgICAgICBzdXBwbGllcjogbnVsbCwKICAgICAgICBkcml2ZXJJbmZvOiBudWxsLAogICAgICAgIHNxZFNoaXBwaW5nTWFyazogbnVsbCwKICAgICAgICBjYXJnb05hbWU6IG51bGwsCiAgICAgICAgdG90YWxCb3hlczogbnVsbCwKICAgICAgICBwYWNrYWdlVHlwZTogbnVsbCwKICAgICAgICB0b3RhbEdyb3NzV2VpZ2h0OiBudWxsLAogICAgICAgIHRvdGFsVm9sdW1lOiBudWxsLAogICAgICAgIGRhbWFnZVN0YXR1czogIjAiLAogICAgICAgIHN0b3JhZ2VMb2NhdGlvbjE6IG51bGwsCiAgICAgICAgc3RvcmFnZUxvY2F0aW9uMjogbnVsbCwKICAgICAgICBzdG9yYWdlTG9jYXRpb24zOiBudWxsLAogICAgICAgIHJlY2VpdmVkU3RvcmFnZUZlZTogbnVsbCwKICAgICAgICB1bnBhaWRVbmxvYWRpbmdGZWU6IG51bGwsCiAgICAgICAgbG9naXN0aWNzQWR2YW5jZUZlZTogbnVsbCwKICAgICAgICByZW50YWxCYWxhbmNlRmVlOiBudWxsLAogICAgICAgIGZyZWVTdGFja1BlcmlvZDogbnVsbCwKICAgICAgICBvdmVyZHVlUmVudGFsVW5pdFByaWNlOiBudWxsLAogICAgICAgIG92ZXJkdWVSZW50YWxGZWU6IG51bGwsCiAgICAgICAgbm90ZXM6IG51bGwsCiAgICAgICAgd2FyZWhvdXNlQ29kZTogbnVsbCwKICAgICAgICByZWNvcmRUeXBlOiBudWxsLAogICAgICAgIGluYm91bmRUeXBlOiBudWxsLAogICAgICAgIGNhcmdvTmF0dXJlOiBudWxsLAogICAgICAgIGNyZWF0ZWRBdDogbnVsbCwKICAgICAgICBwcmVPdXRib3VuZEZsYWc6IG51bGwsCiAgICAgICAgb3V0Ym91bmRSZXF1ZXN0RmxhZzogbnVsbCwKICAgICAgICBzcWRQbGFubmVkT3V0Ym91bmREYXRlOiBudWxsLAogICAgICAgIGNvbmZpcm1JbmJvdW5kUmVxdWVzdEZsYWc6IG51bGwsCiAgICAgICAgY29uZmlybU91dGJvdW5kUmVxdWVzdEZsYWc6IG51bGwsCiAgICAgICAgc3FkSW5ib3VuZEhhbmRsZXI6IG51bGwsCiAgICAgICAgcGFydGlhbE91dGJvdW5kRmxhZzogbnVsbCwKICAgICAgICBvdXRib3VuZFJlY29yZElkOiBudWxsLAogICAgICAgIGFjdHVhbEluYm91bmRUaW1lOiBtb21lbnQoKS5mb3JtYXQoInl5eXktTU0tREQgSEg6bW06c3MiKSwKICAgICAgICBhY3R1YWxPdXRib3VuZFRpbWU6IG51bGwsCiAgICAgICAgY2FyZ29EZXRhaWxSb3dzOiBudWxsLAogICAgICAgIGluY2x1ZGVzVW5sb2FkaW5nRmVlOiBudWxsLAogICAgICAgIHVucGFpZFBhY2tpbmdGZWU6IG51bGwsCiAgICAgICAgaW5ib3VuZEZlZTogbnVsbCwKICAgICAgICBzaW5nbGVQaWVjZVdlaWdodEZvcm1hdHRlcjogbnVsbCwKICAgICAgICB1bml0TGVuZ3RoRm9ybWF0dGVyOiBudWxsLAogICAgICAgIHVuaXRXaWR0aEZvcm1hdHRlcjogbnVsbCwKICAgICAgICB1bml0SGVpZ2h0Rm9ybWF0dGVyOiBudWxsLAogICAgICAgIHNpbmdsZVBpZWNlVm9sdW1lRm9ybWF0dGVyOiBudWxsLAogICAgICAgIHVuaXRHcm9zc1dlaWdodEZvcm1hdHRlcjogbnVsbCwKICAgICAgICB1bml0Vm9sdW1lRm9ybWF0dGVyOiBudWxsLAogICAgICAgIHJzQ2FyZ29EZXRhaWxzTGlzdDogWwogICAgICAgICAgewogICAgICAgICAgICBzaGlwcGluZ01hcms6ICIiLAogICAgICAgICAgICBpdGVtTmFtZTogIiIsCiAgICAgICAgICAgIGJveENvdW50OiAwLAogICAgICAgICAgICBwYWNrYWdlVHlwZTogIue6uOeusSIsCiAgICAgICAgICAgIHNpbmdsZVBpZWNlV2VpZ2h0Rm9ybWF0dGVyOiAiIiwKICAgICAgICAgICAgdW5pdExlbmd0aEZvcm1hdHRlcjogIiIsCiAgICAgICAgICAgIHVuaXRXaWR0aEZvcm1hdHRlcjogIiIsCiAgICAgICAgICAgIHVuaXRIZWlnaHRGb3JtYXR0ZXI6ICIiLAogICAgICAgICAgICBzaW5nbGVQaWVjZVZvbHVtZUZvcm1hdHRlcjogIiIsCiAgICAgICAgICAgIHVuaXRHcm9zc1dlaWdodEZvcm1hdHRlcjogIiIsCiAgICAgICAgICAgIHVuaXRWb2x1bWVGb3JtYXR0ZXI6ICIiLAogICAgICAgICAgICBkYW1hZ2VTdGF0dXM6ICIiLAogICAgICAgICAgICBib3hJdGVtQ291bnQ6IDAsCiAgICAgICAgICAgIHN1YnRvdGFsSXRlbUNvdW50OiAwLAogICAgICAgICAgICBleHByZXNzRGF0ZTogbW9tZW50KCkuZm9ybWF0KCJ5eXl5LU1NLUREIiksCiAgICAgICAgICAgIGFkZGl0aW9uYWxGZWU6IDAKICAgICAgICAgIH0KICAgICAgICBdCiAgICAgIH0KICAgICAgdGhpcy5yZXNldEZvcm0oImZvcm0iKQogICAgfSwKICAgIC8qKiDmkJzntKLmjInpkq7mk43kvZwgKi8KICAgIGhhbmRsZVF1ZXJ5KCkgewogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnBhZ2VOdW0gPSAxCiAgICAgIHRoaXMuZ2V0TGlzdCgpCiAgICB9LAogICAgLyoqIOmHjee9ruaMiemSruaTjeS9nCAqLwogICAgcmVzZXRRdWVyeSgpIHsKICAgICAgdGhpcy5yZXNldEZvcm0oInF1ZXJ5Rm9ybSIpCiAgICAgIC8vIOS/neeVmWlzVG9wTGV2ZWzlj4LmlbDvvIznoa7kv53ph43nva7lkI7ku43nhLblj6rmn6Xor6LpobblsYLmlbDmja4KICAgICAgdGhpcy5xdWVyeVBhcmFtcy5pc1RvcExldmVsID0gdHJ1ZQogICAgICB0aGlzLmhhbmRsZVF1ZXJ5KCkKICAgIH0sCiAgICBoYW5kbGVTdGF0dXNDaGFuZ2Uocm93KSB7CiAgICAgIGxldCB0ZXh0ID0gcm93LnN0YXR1cyA9PT0gIjAiID8gIuWQr+eUqCIgOiAi5YGc55SoIgogICAgICB0aGlzLiRtb2RhbC5jb25maXJtKCLnoa7orqTopoFcIiIgKyB0ZXh0ICsgIuWQl++8nyIpLnRoZW4oZnVuY3Rpb24gKCkgewogICAgICAgIHJldHVybiBjaGFuZ2VTdGF0dXMocm93LmludmVudG9yeUlkLCByb3cuc3RhdHVzKQogICAgICB9KS50aGVuKCgpID0+IHsKICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKHRleHQgKyAi5oiQ5YqfIikKICAgICAgfSkuY2F0Y2goZnVuY3Rpb24gKCkgewogICAgICAgIHJvdy5zdGF0dXMgPSByb3cuc3RhdHVzID09PSAiMCIgPyAiMSIgOiAiMCIKICAgICAgfSkKICAgIH0sCiAgICAvLyDlpJrpgInmoYbpgInkuK3mlbDmja4KICAgIGhhbmRsZVNlbGVjdGlvbkNoYW5nZShzZWxlY3Rpb24pIHsKICAgICAgLy8g5q2j56Gu6I635Y+W6KGo5qC85pWw5o2uIC0g6YCa6L+HZGF0YeWxnuaApwogICAgICBjb25zdCB0cmVlRGF0YSA9IHRoaXMuJHJlZnMuaW52ZW50b3J5VGFibGUuc3RvcmUuc3RhdGVzLmRhdGEKCiAgICAgIC8vIOiOt+WPluS5i+WJjeeahOmAieaLqeeKtuaAge+8jOeUqOS6juavlOi+g+WPmOWMlgogICAgICBjb25zdCBwcmV2aW91c0lkcyA9IFsuLi50aGlzLmlkc10KCiAgICAgIC8vIOa4heepuuW9k+WJjemAieaLqQogICAgICB0aGlzLmlkcyA9IFtdCiAgICAgIHRoaXMuc2VsZWN0ZWRJbnZlbnRvcnlMaXN0ID0gW10KCiAgICAgIC8vIOmHjeaWsOWhq+WFhemAieaLqeaVsOaNrgogICAgICB0aGlzLmlkcyA9IHNlbGVjdGlvbi5tYXAoaXRlbSA9PiBpdGVtLmludmVudG9yeUlkKQogICAgICB0aGlzLnNpbmdsZSA9IHNlbGVjdGlvbi5sZW5ndGggIT09IDEKICAgICAgdGhpcy5tdWx0aXBsZSA9ICFzZWxlY3Rpb24ubGVuZ3RoCiAgICAgIHRoaXMuc2VsZWN0ZWRJbnZlbnRvcnlMaXN0ID0gc2VsZWN0aW9uCgogICAgICAvLyDmib7lh7rmlrDpgInkuK3lkozlj5bmtojpgInkuK3nmoTpobkKICAgICAgY29uc3QgbmV3bHlTZWxlY3RlZCA9IHRoaXMuaWRzLmZpbHRlcihpZCA9PiAhcHJldmlvdXNJZHMuaW5jbHVkZXMoaWQpKQogICAgICBjb25zdCBuZXdseURlc2VsZWN0ZWQgPSBwcmV2aW91c0lkcy5maWx0ZXIoaWQgPT4gIXRoaXMuaWRzLmluY2x1ZGVzKGlkKSkKCiAgICAgIC8vIOWkhOeQhuaWsOmAieS4reeahOaJk+WMheeuse+8muiHquWKqOmAieS4reWFtuWtkOmhuQogICAgICBzZWxlY3Rpb24uZm9yRWFjaChpdGVtID0+IHsKICAgICAgICBpZiAoaXRlbS5wYWNrYWdlUmVjb3JkID09PSAiMSIgJiYgbmV3bHlTZWxlY3RlZC5pbmNsdWRlcyhpdGVtLmludmVudG9yeUlkKSkgewogICAgICAgICAgLy8g5aaC5p6c5piv5paw6YCJ5Lit55qE5omT5YyF566x6IqC54K5CgogICAgICAgICAgLy8g5Zyo5qCR5b2i6KGo5qC85pWw5o2u5Lit5om+5Yiw5a+55bqU55qE6IqC54K5CiAgICAgICAgICBjb25zdCBwYXJlbnROb2RlID0gdHJlZURhdGEuZmluZChub2RlID0+IG5vZGUuaW52ZW50b3J5SWQgPT09IGl0ZW0uaW52ZW50b3J5SWQpCgogICAgICAgICAgLy8g5qOA5p+l6IqC54K55piv5ZCm5bey5bGV5byAKOW3suaciWNoaWxkcmVu5bGe5oCn5LiU5pyJ5YaF5a65KQogICAgICAgICAgaWYgKHBhcmVudE5vZGUgJiYgcGFyZW50Tm9kZS5jaGlsZHJlbiAmJiBwYXJlbnROb2RlLmNoaWxkcmVuLmxlbmd0aCA+IDApIHsKICAgICAgICAgICAgLy8g5aaC5p6c6IqC54K55bey5bGV5byA77yM55u05o6l6YCJ5Lit5YW25omA5pyJ5a2Q6aG5CiAgICAgICAgICAgIHNldFRpbWVvdXQoKCkgPT4gewogICAgICAgICAgICAgIHBhcmVudE5vZGUuY2hpbGRyZW4uZm9yRWFjaChjaGlsZCA9PiB7CiAgICAgICAgICAgICAgICBpZiAoIXRoaXMuaWRzLmluY2x1ZGVzKGNoaWxkLmludmVudG9yeUlkKSkgewogICAgICAgICAgICAgICAgICB0aGlzLmlkcy5wdXNoKGNoaWxkLmludmVudG9yeUlkKQogICAgICAgICAgICAgICAgICB0aGlzLnNlbGVjdGVkSW52ZW50b3J5TGlzdC5wdXNoKGNoaWxkKQogICAgICAgICAgICAgICAgICB0aGlzLiRyZWZzLmludmVudG9yeVRhYmxlLnRvZ2dsZVJvd1NlbGVjdGlvbihjaGlsZCwgdHJ1ZSkKICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICB9KQogICAgICAgICAgICB9LCA1MCkgLy8g57uZ5LiA54K55pe26Ze06K6pVUnmm7TmlrAKICAgICAgICAgIH0gZWxzZSBpZiAocGFyZW50Tm9kZSAmJiAhcGFyZW50Tm9kZS5jaGlsZHJlbkxvYWRlZCAmJiBwYXJlbnROb2RlLmhhc0NoaWxkcmVuKSB7CiAgICAgICAgICAgIC8vIOWmguaenOiKgueCueacquWxleW8gOS4lOacquWKoOi9vei/h+S9huacieWtkOiKgueCueagh+iusAogICAgICAgICAgICBwYXJlbnROb2RlLmNoaWxkcmVuTG9hZGVkID0gdHJ1ZQoKICAgICAgICAgICAgLy8g5omL5Yqo5bGV5byA6KGM77yM6Kem5Y+R5oeS5Yqg6L29CiAgICAgICAgICAgIHRoaXMuJHJlZnMuaW52ZW50b3J5VGFibGUudG9nZ2xlUm93RXhwYW5zaW9uKHBhcmVudE5vZGUsIHRydWUpCgogICAgICAgICAgICAvLyDnm5HlkKzlrZDoioLngrnliqDovb3lrozmiJDlkI7lho3pgInkuK3lroPku6wKICAgICAgICAgICAgLy8g6L+Z6YeM5Yip55So5LqGbG9hZENoaWxkSW52ZW50b3J55pa55rOV5Lit55qE6YC76L6R77yM5a6D5Lya5Zyo5a2Q6IqC54K55Yqg6L295ZCO5aSE55CG6YCJ5Lit54q25oCBCiAgICAgICAgICB9CiAgICAgICAgfQogICAgICB9KQoKICAgICAgLy8g5aSE55CG5Y+W5raI6YCJ5Lit55qE5omT5YyF566x77ya5Y+W5raI6YCJ5Lit5YW25a2Q6aG5CiAgICAgIG5ld2x5RGVzZWxlY3RlZC5mb3JFYWNoKHBhcmVudElkID0+IHsKICAgICAgICAvLyDmib7lh7rlr7nlupTnmoTniLboioLngrkKICAgICAgICBjb25zdCBwYXJlbnROb2RlID0gdHJlZURhdGEuZmluZChub2RlID0+CiAgICAgICAgICBub2RlLmludmVudG9yeUlkID09PSBwYXJlbnRJZCAmJiBub2RlLnBhY2thZ2VSZWNvcmQgPT09ICIxIgogICAgICAgICkKCiAgICAgICAgaWYgKHBhcmVudE5vZGUgJiYgcGFyZW50Tm9kZS5jaGlsZHJlbiAmJiBwYXJlbnROb2RlLmNoaWxkcmVuLmxlbmd0aCA+IDApIHsKICAgICAgICAgIC8vIOWPlua2iOmAieS4reaJgOacieWtkOmhuQogICAgICAgICAgcGFyZW50Tm9kZS5jaGlsZHJlbi5mb3JFYWNoKGNoaWxkID0+IHsKICAgICAgICAgICAgY29uc3QgY2hpbGRJbmRleCA9IHRoaXMuaWRzLmluZGV4T2YoY2hpbGQuaW52ZW50b3J5SWQpCiAgICAgICAgICAgIGlmIChjaGlsZEluZGV4ID4gLTEpIHsKICAgICAgICAgICAgICAvLyDku47pgInkuK3liJfooajkuK3np7vpmaQKICAgICAgICAgICAgICB0aGlzLmlkcy5zcGxpY2UoY2hpbGRJbmRleCwgMSkKICAgICAgICAgICAgICBjb25zdCBpdGVtSW5kZXggPSB0aGlzLnNlbGVjdGVkSW52ZW50b3J5TGlzdC5maW5kSW5kZXgoCiAgICAgICAgICAgICAgICBpdGVtID0+IGl0ZW0uaW52ZW50b3J5SWQgPT09IGNoaWxkLmludmVudG9yeUlkCiAgICAgICAgICAgICAgKQogICAgICAgICAgICAgIGlmIChpdGVtSW5kZXggPiAtMSkgewogICAgICAgICAgICAgICAgdGhpcy5zZWxlY3RlZEludmVudG9yeUxpc3Quc3BsaWNlKGl0ZW1JbmRleCwgMSkKICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgLy8g5ZyoVUnkuIrlj5bmtojpgInkuK0KICAgICAgICAgICAgICB0aGlzLiRyZWZzLmludmVudG9yeVRhYmxlLnRvZ2dsZVJvd1NlbGVjdGlvbihjaGlsZCwgZmFsc2UpCiAgICAgICAgICAgIH0KICAgICAgICAgIH0pCiAgICAgICAgfQogICAgICB9KQogICAgfSwKICAgIGhhbmRsZU9wZW5BZ2dyZWdhdG9yKCkgewogICAgICAvLyBsaXN0QWdncmVnYXRvclJjdCh0aGlzLnF1ZXJ5UGFyYW1zKS50aGVuKHJlc3BvbnNlID0+IHsKICAgICAgLy8gICB0aGlzLmFnZ3JlZ2F0b3JSY3RMaXN0ID0gcmVzcG9uc2UKICAgICAgLy8gfSkKCiAgICAgIHRoaXMub3BlbkFnZ3JlZ2F0b3IgPSB0cnVlCiAgICB9LAogICAgLyoqIOaWsOWinuaMiemSruaTjeS9nCAqLwogICAgaGFuZGxlQWRkKCkgewogICAgICB0aGlzLnJlc2V0KCkKICAgICAgdGhpcy5vcGVuID0gdHJ1ZQogICAgICB0aGlzLnRpdGxlID0gIuWFpeS7kyIKICAgICAgdGhpcy5mb3JtLnNxZEluYm91bmRIYW5kbGVyID0gdGhpcy4kc3RvcmUuc3RhdGUudXNlci5uYW1lLnNwbGl0KCIgIilbMV0KICAgICAgdGhpcy5mb3JtLmFjdHVhbEluYm91bmRUaW1lID0gbmV3IERhdGUoKQogICAgICB0aGlzLmZvcm0uaW52ZW50b3J5U3RhdHVzID0gIjAiCiAgICAgIC8vIHRoaXMuZm9ybS5yc0NhcmdvRGV0YWlsc0xpc3QgPyBudWxsIDogdGhpcy5mb3JtLnJzQ2FyZ29EZXRhaWxzTGlzdCA9IFtdCiAgICAgIC8vIHRoaXMuZm9ybS5yc0NhcmdvRGV0YWlsc0xpc3QucHVzaCh0aGlzLl8uY2xvbmVEZWVwKHRoaXMuY2FyZ29EZXRhaWxSb3cpKQogICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7CiAgICAgICAgdGhpcy5mb3JtLmluYm91bmRTZXJpYWxOb1ByZSA9ICJSUy45MSIKICAgICAgICB0aGlzLmZvcm0uY2FyZ29OYXR1cmUgPSAi5pmu6LSnIgogICAgICAgIHRoaXMuZm9ybS5yZWNvcmRUeXBlID0gIuagh+WHhiIKICAgICAgICB0aGlzLmZvcm0uaW5ib3VuZFR5cGUgPSAi5YWl5LuTIgogICAgICB9KQoKICAgIH0sCiAgICAvKiog5L+u5pS55oyJ6ZKu5pON5L2cICovCiAgICBoYW5kbGVVcGRhdGUocm93KSB7CiAgICAgIHRoaXMucmVzZXQoKQogICAgICBjb25zdCBpbnZlbnRvcnlJZCA9IHJvdy5pbnZlbnRvcnlJZCB8fCB0aGlzLmlkcwogICAgICBnZXRJbnZlbnRvcnkoaW52ZW50b3J5SWQpLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgIHRoaXMuZm9ybSA9IHJlc3BvbnNlLmRhdGEKICAgICAgICB0aGlzLmZvcm0ucnNDYXJnb0RldGFpbHNMaXN0ID0gcmVzcG9uc2UuZGF0YS5yc0NhcmdvRGV0YWlsc0xpc3QubWFwKGl0ZW0gPT4gKHsKICAgICAgICAgIC4uLml0ZW0sCiAgICAgICAgICBzaGlwcGluZ01hcms6IGl0ZW0uc2hpcHBpbmdNYXJrIHx8ICIiLAogICAgICAgICAgaXRlbU5hbWU6IGl0ZW0uaXRlbU5hbWUgfHwgIiIsCiAgICAgICAgICBib3hDb3VudDogaXRlbS5ib3hDb3VudCB8fCAwLAogICAgICAgICAgcGFja2FnZVR5cGU6IGl0ZW0ucGFja2FnZVR5cGUgfHwgIiIsCiAgICAgICAgICBzaW5nbGVQaWVjZVdlaWdodDogaXRlbS5zaW5nbGVQaWVjZVdlaWdodCB8fCAwLAogICAgICAgICAgc2luZ2xlUGllY2VXZWlnaHRGb3JtYXR0ZXI6IHRoaXMuZm9ybWF0TnVtYmVyKGl0ZW0uc2luZ2xlUGllY2VXZWlnaHQgfHwgMCksCiAgICAgICAgICB1bml0TGVuZ3RoOiBpdGVtLnVuaXRMZW5ndGggfHwgMCwKICAgICAgICAgIHVuaXRMZW5ndGhGb3JtYXR0ZXI6IHRoaXMuZm9ybWF0TnVtYmVyKGl0ZW0udW5pdExlbmd0aCB8fCAwKSwKICAgICAgICAgIHVuaXRXaWR0aDogaXRlbS51bml0V2lkdGggfHwgMCwKICAgICAgICAgIHVuaXRXaWR0aEZvcm1hdHRlcjogdGhpcy5mb3JtYXROdW1iZXIoaXRlbS51bml0V2lkdGggfHwgMCksCiAgICAgICAgICB1bml0SGVpZ2h0OiBpdGVtLnVuaXRIZWlnaHQgfHwgMCwKICAgICAgICAgIHVuaXRIZWlnaHRGb3JtYXR0ZXI6IHRoaXMuZm9ybWF0TnVtYmVyKGl0ZW0udW5pdEhlaWdodCB8fCAwKSwKICAgICAgICAgIHNpbmdsZVBpZWNlVm9sdW1lOiBpdGVtLnNpbmdsZVBpZWNlVm9sdW1lIHx8IDAsCiAgICAgICAgICBzaW5nbGVQaWVjZVZvbHVtZUZvcm1hdHRlcjogdGhpcy5mb3JtYXROdW1iZXIoaXRlbS5zaW5nbGVQaWVjZVZvbHVtZSB8fCAwKSwKICAgICAgICAgIHVuaXRHcm9zc1dlaWdodDogaXRlbS51bml0R3Jvc3NXZWlnaHQgfHwgMCwKICAgICAgICAgIHVuaXRHcm9zc1dlaWdodEZvcm1hdHRlcjogdGhpcy5mb3JtYXROdW1iZXIoaXRlbS51bml0R3Jvc3NXZWlnaHQgfHwgMCksCiAgICAgICAgICB1bml0Vm9sdW1lOiBpdGVtLnVuaXRWb2x1bWUgfHwgMCwKICAgICAgICAgIHVuaXRWb2x1bWVGb3JtYXR0ZXI6IHRoaXMuZm9ybWF0TnVtYmVyKGl0ZW0udW5pdFZvbHVtZSB8fCAwKSwKICAgICAgICAgIGRhbWFnZVN0YXR1czogaXRlbS5kYW1hZ2VTdGF0dXMgfHwgIiIKICAgICAgICB9KSkKICAgICAgICBpZiAocmVzcG9uc2UuZGF0YS5wYWNrYWdlUmVjb3JkID09PSAiMCIpIHsKICAgICAgICAgIHRoaXMub3BlbiA9IHRydWUKICAgICAgICB9IGVsc2UgewogICAgICAgICAgdGhpcy5vcGVuUGtnID0gdHJ1ZQogICAgICAgIH0KCiAgICAgICAgdGhpcy50aXRsZSA9ICLkv67mlLnlupPlrZgiCiAgICAgIH0pCiAgICB9LAogICAgLyoqIOaPkOS6pOaMiemSriAqLwogICAgc3VibWl0Rm9ybSh0eXBlKSB7CiAgICAgIHRoaXMuJHJlZnNbImZvcm0iXS52YWxpZGF0ZSh2YWxpZCA9PiB7CiAgICAgICAgaWYgKHZhbGlkKSB7CiAgICAgICAgICAvLyDmo4Dmn6XliIbljZXlj7fmoLzlvI8KICAgICAgICAgIGlmICh0aGlzLmZvcm0uc3ViT3JkZXJObyAmJiAhdGhpcy5mb3JtLnN1Yk9yZGVyTm8uc3RhcnRzV2l0aChgJHt0aGlzLmZvcm0uY2xpZW50Q29kZX0tYCkpIHsKICAgICAgICAgICAgdGhpcy4kbW9kYWwubXNnRXJyb3IoYOWIhuWNleWPt+W/hemhu+S7pSAke3RoaXMuZm9ybS5jbGllbnRDb2RlfS0g5byA5aS0YCkKICAgICAgICAgICAgcmV0dXJuCiAgICAgICAgICB9CgogICAgICAgICAgdGhpcy5mb3JtLmluYm91bmREYXRlID0gdGhpcy5mb3JtLmluYm91bmREYXRlID8gbW9tZW50KHRoaXMuZm9ybS5pbmJvdW5kRGF0ZSkuZm9ybWF0KCJ5eXl5LU1NLUREIEhIOm1tOnNzIikgOiBudWxsCiAgICAgICAgICB0aGlzLmZvcm0uYWN0dWFsSW5ib3VuZFRpbWUgPSB0aGlzLmZvcm0uYWN0dWFsSW5ib3VuZFRpbWUgPyBtb21lbnQodGhpcy5mb3JtLmFjdHVhbEluYm91bmRUaW1lKS5mb3JtYXQoInl5eXktTU0tREQgSEg6bW06c3MiKSA6IG51bGwKCiAgICAgICAgICAvLyB0aGlzLmZvcm0uaW5ib3VuZFNlcmlhbE5vID0gdGhpcy5mb3JtLmluYm91bmRTZXJpYWxOb1ByZSArIHRoaXMuZm9ybS5pbmJvdW5kU2VyaWFsTm9TdWIucGFkU3RhcnQoMywgIjAiKQogICAgICAgICAgdGhpcy5mb3JtLmFjdHVhbEluYm91bmRUaW1lID0gbW9tZW50KHRoaXMuZm9ybS5hY3R1YWxJbmJvdW5kVGltZSkuZm9ybWF0KCJ5eXl5LU1NLUREIEhIOm1tOnNzIikKICAgICAgICAgIGlmICh0eXBlICE9PSAicGtnIikgewogICAgICAgICAgICB0aGlzLmZvcm0udG90YWxCb3hlcyA9IDAKICAgICAgICAgICAgdGhpcy5mb3JtLnRvdGFsR3Jvc3NXZWlnaHQgPSAwCiAgICAgICAgICAgIHRoaXMuZm9ybS50b3RhbFZvbHVtZSA9IDAKICAgICAgICAgICAgdGhpcy5mb3JtLnJzQ2FyZ29EZXRhaWxzTGlzdC5mb3JFYWNoKChpdGVtLCBpbmRleCkgPT4gewogICAgICAgICAgICAgIC8vIOaxh+aAu+iuoeeulwogICAgICAgICAgICAgIHRoaXMuZm9ybS50b3RhbEJveGVzID0gY3VycmVuY3koaXRlbS5ib3hDb3VudCB8fCAwKS5hZGQodGhpcy5mb3JtLnRvdGFsQm94ZXMpLnZhbHVlCiAgICAgICAgICAgICAgdGhpcy5mb3JtLnRvdGFsR3Jvc3NXZWlnaHQgPSBjdXJyZW5jeShpdGVtLnVuaXRHcm9zc1dlaWdodCB8fCAwKS5hZGQodGhpcy5mb3JtLnRvdGFsR3Jvc3NXZWlnaHQpLnZhbHVlCiAgICAgICAgICAgICAgdGhpcy5mb3JtLnRvdGFsVm9sdW1lID0gY3VycmVuY3koaXRlbS51bml0Vm9sdW1lIHx8IDApLmFkZCh0aGlzLmZvcm0udG90YWxWb2x1bWUpLnZhbHVlCiAgICAgICAgICAgIH0pCiAgICAgICAgICB9CgogICAgICAgICAgLy8g57uf6K6h5ZSb5aS0CiAgICAgICAgICBsZXQgbWFyayA9IFtdCiAgICAgICAgICB0aGlzLmZvcm0ucnNDYXJnb0RldGFpbHNMaXN0Lm1hcChpdGVtID0+IHsKICAgICAgICAgICAgbWFyay5wdXNoKGl0ZW0uc2hpcHBpbmdNYXJrKQogICAgICAgICAgfSkKICAgICAgICAgIHRoaXMuZm9ybS5zcWRTaGlwcGluZ01hcmsgPSBtYXJrLmpvaW4oIiwiKQoKICAgICAgICAgIGlmICh0aGlzLmZvcm0uaW52ZW50b3J5SWQgIT0gbnVsbCkgewogICAgICAgICAgICB0aGlzLmZvcm0ucmVudGFsU2V0dGxlbWVudERhdGUgPyBudWxsIDogdGhpcy5mb3JtLnJlbnRhbFNldHRsZW1lbnREYXRlID0gdGhpcy5mb3JtLmFjdHVhbEluYm91bmRUaW1lCiAgICAgICAgICAgIHVwZGF0ZUludmVudG9yeSh0aGlzLmZvcm0pLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuS/ruaUueaIkOWKnyIpCiAgICAgICAgICAgICAgdGhpcy5nZXRMaXN0KCkKICAgICAgICAgICAgfSkKICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgIGlmICh0eXBlID09PSAicGtnIikgewogICAgICAgICAgICAgIHRoaXMuZm9ybS5wYWNrYWdlUmVjb3JkID0gIjEiCiAgICAgICAgICAgICAgdGhpcy5mb3JtLnJlcGFja2luZ1N0YXR1cyA9ICLmiZPljIXkuK0iCgogICAgICAgICAgICB9CiAgICAgICAgICAgIHRoaXMuZm9ybS5yZW50YWxTZXR0bGVtZW50RGF0ZSA9IHRoaXMuZm9ybS5hY3R1YWxJbmJvdW5kVGltZQogICAgICAgICAgICBhZGRJbnZlbnRvcnkodGhpcy5mb3JtKS50aGVuKHJlc3BvbnNlID0+IHsKICAgICAgICAgICAgICB0aGlzLmZvcm0gPSByZXNwb25zZS5kYXRhCiAgICAgICAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5paw5aKe5oiQ5YqfIikKICAgICAgICAgICAgICB0aGlzLmdldExpc3QoKQogICAgICAgICAgICB9KQogICAgICAgICAgfQogICAgICAgIH0KICAgICAgfSkKICAgIH0sCiAgICAvKiog5Yig6Zmk5oyJ6ZKu5pON5L2cICovCiAgICBoYW5kbGVEZWxldGUocm93KSB7CiAgICAgIGNvbnN0IGludmVudG9yeUlkcyA9IHJvdy5pbnZlbnRvcnlJZCB8fCB0aGlzLmlkcwogICAgICB0aGlzLiRtb2RhbC5jb25maXJtKCLmmK/lkKbnoa7orqTliKDpmaTlupPlrZjnvJblj7fkuLpcIiIgKyBpbnZlbnRvcnlJZHMgKyAiXCLnmoTmlbDmja7pobnvvJ8iKS50aGVuKGZ1bmN0aW9uICgpIHsKICAgICAgICByZXR1cm4gZGVsSW52ZW50b3J5KGludmVudG9yeUlkcykKICAgICAgfSkudGhlbigoKSA9PiB7CiAgICAgICAgdGhpcy5nZXRMaXN0KCkKICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLliKDpmaTmiJDlip8iKQogICAgICB9KS5jYXRjaCgoKSA9PiB7CiAgICAgIH0pCiAgICB9LAogICAgLyoqIOWvvOWFpeaMiemSruaTjeS9nCAqLwogICAgaGFuZGxlSW1wb3J0KCkgewogICAgICB0aGlzLnVwbG9hZC50aXRsZSA9ICLnlKjmiLflr7zlhaUiCiAgICAgIHRoaXMudXBsb2FkLm9wZW4gPSB0cnVlCiAgICB9LAogICAgLyoqIOWvvOWHuuaMiemSruaTjeS9nCAqLwogICAgaGFuZGxlRXhwb3J0KCkgewoKICAgICAgdGhpcy5kb3dubG9hZCgic3lzdGVtL2ludmVudG9yeS9leHBvcnQiLCB7CiAgICAgICAgLi4udGhpcy5xdWVyeVBhcmFtcywgcGFnZVNpemU6IDk5OQogICAgICB9LCBgaW52ZW50b3J5XyR7bmV3IERhdGUoKS5nZXRUaW1lKCl9Lnhsc3hgKQogICAgfQogIH0KfQo="}, null]}