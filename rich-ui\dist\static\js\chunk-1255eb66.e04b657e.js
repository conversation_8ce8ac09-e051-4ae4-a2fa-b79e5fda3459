(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-1255eb66"],{"063e":function(t,e,o){"use strict";o("629f")},"629f":function(t,e,o){},c9a3:function(t,e,o){"use strict";o.r(e);var n=function(){var t=this,e=t.$createElement,o=t._self._c||e;return o("div",[o("el-tooltip",{attrs:{disabled:null==t.scope.row.company||t.scope.row.company.length<3||((null!=t.scope.row.contractType?t.scope.row.contractType:"")+(null!=t.scope.row.contractType&&null!=t.scope.row.contractNo?"：":"")+(null!=t.scope.row.contractNo?t.scope.row.contractNo:"")).length<10,placement:"top"}},[o("div",{attrs:{slot:"content"},slot:"content"},[o("div",{staticStyle:{margin:"0","font-size":"15px","font-weight":"bold"}},[t._v(t._s(t.scope.row.carrierCode)+" "),o("span",{staticClass:"unHighlight-text",staticStyle:{margin:"0","font-weight":"bold","font-size":"12px"}},[t._v(" "+t._s("("+(null!=t.scope.row.contractType?t.scope.row.contractType:"")+")")+" ")])]),t.checkRole(["Operator"])?o("h6",{staticClass:"unHighlight-text",staticStyle:{margin:"0"}},[t._v(" "+t._s(t.scope.row.company)+" ")]):t._e()]),o("div",{staticStyle:{margin:"0",overflow:"hidden","text-overflow":"ellipsis","white-space":"nowrap"}},[o("div",{staticStyle:{margin:"0","font-size":"15px","font-weight":"bold"}},[t._v(t._s(t.scope.row.carrierCode)+" "),o("span",{staticClass:"unHighlight-text",staticStyle:{margin:"0","font-weight":"bold","font-size":"12px"}},[t._v(" "+t._s("("+(null!=t.scope.row.contractType?t.scope.row.contractType:"")+")")+" ")])]),t.checkRole(["Operator"])?o("h6",{staticClass:"unHighlight-text",staticStyle:{margin:"0",overflow:"hidden","text-overflow":"ellipsis","white-space":"nowrap"}},[t._v(" "+t._s(t.scope.row.company)+" ")]):t._e()])])],1)},r=[],c=o("e350"),s={name:"carrier",methods:{checkRole:c["b"]},props:["scope"],data:function(){return{size:this.$store.state.app.size||"mini"}},computed:{contractType:function(){}}},i=s,a=(o("063e"),o("2877")),l=Object(a["a"])(i,n,r,!1,null,"2d15028d",null);e["default"]=l.exports},e350:function(t,e,o){"use strict";o.d(e,"a",(function(){return r})),o.d(e,"b",(function(){return c}));o("d3b7"),o("caad"),o("2532");var n=o("4360");function r(t){if(t&&t instanceof Array&&t.length>0){var e=n["a"].getters&&n["a"].getters.permissions,o=t,r="*:*:*",c=e.some((function(t){return r==t||o.includes(t)}));return!!c}return console.error("need roles! Like checkPermi=\"['system:user:add','system:user:edit']\""),!1}function c(t){if(t&&t instanceof Array&&t.length>0){var e=n["a"].getters&&n["a"].getters.roles,o=t,r="admin",c=e.some((function(t){return r==t||o.includes(t)}));return!!c}return console.error("need roles! Like checkRole=\"['admin','editor']\""),!1}}}]);