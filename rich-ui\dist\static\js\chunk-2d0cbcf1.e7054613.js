(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d0cbcf1"],{"4af4":function(e,t,s){"use strict";s.r(t);var c=function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("div",[s("h6",{staticStyle:{margin:"0"}},[e._v(e._s(0!=e.scope.row.grossWeight?"+"+e.scope.row.grossWeight+e.scope.row.cargoUnit:""))])])},n=[],o={name:"weight",props:["scope"],data:function(){return{size:this.$store.state.app.size||"mini"}}},i=o,r=s("2877"),a=Object(r["a"])(i,c,n,!1,null,"de015dcc",null);t["default"]=a.exports}}]);