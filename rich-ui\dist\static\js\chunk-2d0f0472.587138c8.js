(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d0f0472"],{"9c4f":function(e,t,n){"use strict";n.r(t);var s=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",[n("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:company:list"],expression:"['system:company:list']"}],attrs:{size:"mini",type:"text"},on:{click:function(t){return e.checkBank(e.scope.row)}}},[e._v(" "+e._s("[···]")+" ")])],1)},c=[],i={name:"account",props:["scope"],data:function(){return{size:this.$store.state.app.size||"mini"}},methods:{checkBank:function(e){this.$emit("return",{key:"account",value:e})}}},a=i,o=n("2877"),r=Object(o["a"])(a,s,c,!1,null,"0e993626",null);t["default"]=r.exports}}]);