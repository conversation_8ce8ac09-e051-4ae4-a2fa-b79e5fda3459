package com.rich.system.service.impl;

import com.rich.common.core.domain.AjaxResult;
import com.rich.common.core.domain.entity.*;
import com.rich.common.utils.DateUtils;
import com.rich.common.utils.SecurityUtils;
import com.rich.common.utils.StringUtils;
import com.rich.system.mapper.*;
import com.rich.system.service.MpInventoryService;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 库存Service业务层处理
 *
 * <AUTHOR>
 * @date 2025-04-30
 */
@Service
public class MpInventoryServiceImpl implements MpInventoryService {
    @Autowired
    private MpInventoryMapper mpInventoryMapper;

    @Autowired
    private MpCargoDetailsMapper mpCargoDetailsMapper;

    @Autowired
    private RsInventoryMapper rsInventoryMapper;

    @Autowired
    private RsCargoDetailsMapper rsCargoDetailsMapper;

    @Autowired
    private RsWarehouseClientMapper rsWarehouseClientMapper;

    @Autowired
    private MpUserMapper mpUserMapper;

    /**
     * 查询库存
     *
     * @param inventoryId 库存主键
     * @return 库存
     */
    @Override
    public MpInventory selectMpInventoryByInventoryId(Long inventoryId) {
        return mpInventoryMapper.selectMpInventoryByInventoryId(inventoryId);
    }

    /**
     * 查询库存列表
     *
     * @param mpInventory 库存
     * @return 库存
     */
    @Override
    public List<MpInventory> selectMpInventoryList(MpInventory mpInventory) {
        return mpInventoryMapper.selectMpInventoryList(mpInventory);
    }

    /**
     * 新增库存
     *
     * @param mpInventory 库存
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public MpInventory insertMpInventory(MpInventory mpInventory) {
        //新增库存前先根据快递单号进行查重
        if (StringUtils.isNotEmpty(mpInventory.getLogisticsInfo())) {
            MpInventory existInventory = mpInventoryMapper.selectMpInventoryByExpressNo(mpInventory.getLogisticsInfo());
            if (existInventory != null) {
                // 设置标记，表示库存已存在，前端可根据此标记进行提示并跳转
                existInventory.setExist(true);
                return existInventory;
            }
        }

        String prefix = "PRE.";
        int no = mpInventoryMapper.getInboundNo();
        String dateStr = DateUtils.dateTimeNow("yyMM");
        String padlNo = StringUtils.padl(no + 1, 4);
        mpInventory.setPreEntrySerialNo(prefix + dateStr + padlNo);
        int i = mpInventoryMapper.insertMpInventory(mpInventory);
        mpInventory.getMpCargoDetails().forEach(mpCargoDetail -> {
            mpCargoDetail.setInventoryId(mpInventory.getInventoryId());
            mpCargoDetailsMapper.insertMpCargoDetails(mpCargoDetail);
        });
        return mpInventory;
    }

    /**
     * 修改库存
     *
     * @param mpInventory 库存
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int updateMpInventory(MpInventory mpInventory) {
        if (mpInventory.getRecordStatus().equals("1") && mpInventory.getClientCode() != null) {
            mpInventory.setRecordStatus("4");
        }

        int i = mpInventoryMapper.updateMpInventory(mpInventory);
        mpInventory.getMpCargoDetails().forEach(mpCargoDetail -> {
            if (mpCargoDetail.getInventoryId() != null && mpCargoDetail.getCargoDetailsId() != null) {
                mpCargoDetailsMapper.updateMpCargoDetails(mpCargoDetail);
            } else {
                mpCargoDetailsMapper.insertMpCargoDetails(mpCargoDetail);
            }
        });
        return i;
    }

    /**
     * 修改库存状态
     *
     * @param mpInventory 库存
     * @return 库存
     */
    @Override
    public int changeStatus(MpInventory mpInventory) {
        return mpInventoryMapper.updateMpInventory(mpInventory);
    }

    @Override
    public MpInventory selectMpInventoryByExpressNo(String expressNo) {
        return mpInventoryMapper.selectMpInventoryByExpressNo(expressNo);
    }

    /**
     * 根据收货人代码查询库存
     *
     * @param consigneeCode 收货人代码
     * @return 库存信息
     */
    @Override
    public MpInventory selectMpInventoryByConsigneeCode(String consigneeCode) {
        return mpInventoryMapper.selectMpInventoryByConsigneeCode(consigneeCode);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public MpInventory enter(MpInventory mpInventory) {

        // 参数校验
        validateInventoryParams(mpInventory);

        // 查询客户信息
        RsWarehouseClient rsWarehouseClient = rsWarehouseClientMapper.selectRsWarehouseClientByClientCode(mpInventory.getClientCode());
        if (rsWarehouseClient == null) {
            throw new RuntimeException("客户代码不存在：" + mpInventory.getClientCode());
        }

        // 根据是否已入仓处理不同逻辑
        if (mpInventory.getRsInventoryId() != null) {
            // 已入仓记录，执行更新操作
            updateExistingInventory(mpInventory, rsWarehouseClient);
        } else {
            // 新入仓记录，执行新增操作
            createNewInventory(mpInventory, rsWarehouseClient);
        }

        // 更新送货单状态
        int updateCount = mpInventoryMapper.updateMpInventory(mpInventory);
        if (updateCount <= 0) {
            throw new RuntimeException("送货单状态更新失败，ID: " + mpInventory.getInventoryId());
        }

        return mpInventory;
    }

    @Override
    public Map<String, Object> getStatusNumber(MpInventory mpInventory) {
        return mpInventoryMapper.getStatusNumber(mpInventory);
    }

    /**
     * 验证入仓参数
     *
     * @param mpInventory 库存对象
     */
    private void validateInventoryParams(MpInventory mpInventory) {
        if (mpInventory == null) {
            throw new IllegalArgumentException("入参 mpInventory 不能为空");
        }
        if (mpInventory.getClientCode() == null || mpInventory.getClientCode().trim().isEmpty()) {
            throw new IllegalArgumentException("客户代码不能为空");
        }
        if (mpInventory.getMpCargoDetails() == null || mpInventory.getMpCargoDetails().isEmpty()) {
            throw new IllegalArgumentException("货物明细不能为空");
        }
    }

    /**
     * 更新已存在的库存记录
     *
     * @param mpInventory       库存对象
     * @param rsWarehouseClient 仓库客户信息
     */
    private void updateExistingInventory(MpInventory mpInventory, RsWarehouseClient rsWarehouseClient) {
        // 构建库存主表对象
        RsInventory rsInventory = new RsInventory();
        rsInventory.setInventoryId(mpInventory.getRsInventoryId());

        // 设置库存基本信息
        populateInventoryBasicInfo(rsInventory, mpInventory, rsWarehouseClient);

        // 更新库存主表
        rsInventoryMapper.updateRsInventory(rsInventory);

        // 处理货物明细
        updateCargoDetails(mpInventory, rsInventory);

        mpInventory.setRecordStatus("5"); // 已核实
    }

    /**
     * 创建新的库存记录
     *
     * @param mpInventory       库存对象
     * @param rsWarehouseClient 仓库客户信息
     */
    private void createNewInventory(MpInventory mpInventory, RsWarehouseClient rsWarehouseClient) {
        // 生成入仓流水号
        String inboundSerialNo = generateInboundSerialNo();

        // 构建库存主表对象
        RsInventory rsInventory = new RsInventory();
        rsInventory.setInboundSerialNo(inboundSerialNo);

        // 设置库存基本信息
        populateInventoryBasicInfo(rsInventory, mpInventory, rsWarehouseClient);

        // 处理入仓费用
        calculateInboundFee(rsInventory, mpInventory, rsWarehouseClient);

        rsInventory.setRepackingStatus(rsInventory.getPackageTo() != null ? "被打包" : "-");
        rsInventory.setCreateBy(SecurityUtils.getUserId());
        // 插入库存主表
        rsInventoryMapper.insertRsInventory(rsInventory);

        // 插入货物明细
        insertCargoDetails(mpInventory, rsInventory);

        // 更新送货单状态信息
        mpInventory.setInboundSerialNo(inboundSerialNo);
        mpInventory.setRecordStatus("5"); // 已核实
        mpInventory.setCargoStatus("In-warehouse");
        mpInventory.setInboundDate(new Date());
        mpInventory.setRsInventoryId(rsInventory.getInventoryId());
    }

    /**
     * 填充库存基本信息
     *
     * @param rsInventory       库存对象
     * @param mpInventory       送货单对象
     * @param rsWarehouseClient 仓库客户信息
     */
    private void populateInventoryBasicInfo(RsInventory rsInventory, MpInventory mpInventory, RsWarehouseClient rsWarehouseClient) {
        rsInventory.setClientCode(rsWarehouseClient.getClientCode());
        rsInventory.setClientName(rsWarehouseClient.getClientName());
        rsInventory.setActualInboundTime(new Date());
        rsInventory.setConsigneeName(mpInventory.getConsigneeName());
        rsInventory.setConsigneeTel(mpInventory.getConsigneeTel());
        rsInventory.setSupplier(mpInventory.getSupplier());
        rsInventory.setTotalBoxes(mpInventory.getTotalBoxes());
        rsInventory.setTotalGrossWeight(mpInventory.getTotalGrossWeight());
        rsInventory.setTotalVolume(mpInventory.getTotalVolume());
        rsInventory.setFreeStackPeriod(rsWarehouseClient.getFreeStackPeriod());
        rsInventory.setDriverInfo(mpInventory.getLogisticsInfo());
        rsInventory.setOverdueRentalUnitPrice(rsWarehouseClient.getOverdueRent());
        rsInventory.setInventoryStatus("0");
        rsInventory.setCargoName(mpInventory.getMpCargoDetails().stream()
                .map(MpCargoDetails::getItemName)
                .filter(StringUtils::isNotEmpty)
                .collect(Collectors.joining("/")));
        rsInventory.setConsigneeName(mpInventory.getConsigneeName());
        rsInventory.setConsigneeTel(mpInventory.getConsigneeTel());
        rsInventory.setPackageTo(mpInventory.getPackageTo());
        rsInventory.setRepackedInto(mpInventory.getPackageInto());
        rsInventory.setSubOrderNo(mpInventory.getClientCode() + "-" + mpInventory.getConsigneeCode());
        rsInventory.setNotes(mpInventory.getRemarks());
    }

    /**
     * 计算入仓费用
     *
     * @param rsInventory       库存对象
     * @param mpInventory       送货单对象
     * @param rsWarehouseClient 仓库客户信息
     */
    private void calculateInboundFee(RsInventory rsInventory, MpInventory mpInventory, RsWarehouseClient rsWarehouseClient) {
        String deliveryType = mpInventory.getDeliveryType();
        if ("国内快递".equals(deliveryType)) {
            rsInventory.setInboundFee(BigDecimal.valueOf(5));
        } else if (rsWarehouseClient.getIncludesInboundFee() == 0) {
            rsInventory.setInboundFee(rsWarehouseClient.getInboundFee() != null ?
                    rsWarehouseClient.getInboundFee() : BigDecimal.ZERO);
        } else {
            rsInventory.setInboundFee(BigDecimal.ZERO);
        }
    }

    /**
     * 生成入仓流水号
     *
     * @return 生成的入仓流水号
     */
    private String generateInboundSerialNo() {
        String prefix = "RS.";
        int no = rsInventoryMapper.getInboundNo();
        String dateStr = DateUtils.dateTimeNow("yyMM");
        String padlNo = StringUtils.padl(no + 1, 4);
        return prefix + dateStr + padlNo;
    }

    /**
     * 更新货物明细
     *
     * @param mpInventory 送货单对象
     * @param rsInventory 库存对象
     */
    private void updateCargoDetails(MpInventory mpInventory, RsInventory rsInventory) {
        rsCargoDetailsMapper.deleteRsCargoDetailsByInventoryId(mpInventory.getRsInventoryId());

        for (MpCargoDetails mpCargoDetail : mpInventory.getMpCargoDetails()) {
            if (mpCargoDetail == null) continue;

            // 构建货物明细对象
            RsCargoDetails rsCargoDetails = createCargoDetailFromMpDetail(mpCargoDetail, rsInventory);

            rsCargoDetailsMapper.insertRsCargoDetails(rsCargoDetails);
        }
    }

    /**
     * 插入货物明细
     *
     * @param mpInventory 送货单对象
     * @param rsInventory 库存对象
     */
    private void insertCargoDetails(MpInventory mpInventory, RsInventory rsInventory) {
        for (MpCargoDetails mpCargoDetail : mpInventory.getMpCargoDetails()) {
            if (mpCargoDetail == null) continue;

            // 构建货物明细对象
            RsCargoDetails rsCargoDetails = createCargoDetailFromMpDetail(mpCargoDetail, rsInventory);

            // 插入货物明细
            rsCargoDetailsMapper.insertRsCargoDetails(rsCargoDetails);
        }
    }

    /**
     * 根据送货单明细创建库存明细对象
     *
     * @param mpCargoDetail 送货单明细
     * @param rsInventory   库存对象
     * @return 库存明细对象
     */
    private RsCargoDetails createCargoDetailFromMpDetail(MpCargoDetails mpCargoDetail, RsInventory rsInventory) {
        RsCargoDetails rsCargoDetails = new RsCargoDetails();
        rsCargoDetails.setInventoryId(rsInventory.getInventoryId());
        rsCargoDetails.setClientCode(rsInventory.getClientCode());
        rsCargoDetails.setShippingMark(mpCargoDetail.getShippingMark());
        rsCargoDetails.setItemName(mpCargoDetail.getItemName());
        rsCargoDetails.setItemEnName(mpCargoDetail.getItemEnName());
        rsCargoDetails.setBoxCount(mpCargoDetail.getBoxCount());
        rsCargoDetails.setSinglePieceWeight(mpCargoDetail.getSinglePieceWeight());
        rsCargoDetails.setUnitWidth(mpCargoDetail.getUnitWidth());
        rsCargoDetails.setUnitHeight(mpCargoDetail.getUnitHeight());
        rsCargoDetails.setUnitLength(mpCargoDetail.getUnitLength());
        rsCargoDetails.setSinglePieceVolume(mpCargoDetail.getSinglePieceVolume());
        rsCargoDetails.setUnitGrossWeight(mpCargoDetail.getUnitGrossWeight());
        rsCargoDetails.setUnitVolume(mpCargoDetail.getUnitVolume());
        rsCargoDetails.setSubtotalItemCount(Math.toIntExact(mpCargoDetail.getBoxCount()));
        rsCargoDetails.setExpressNo(rsInventory.getDriverInfo());
        return rsCargoDetails;
    }

    /**
     * 批量删除库存
     *
     * @param inventoryIds 需要删除的库存主键
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int deleteMpInventoryByInventoryIds(Long[] inventoryIds) {
        return mpInventoryMapper.deleteMpInventoryByInventoryIds(inventoryIds);
    }

    /**
     * 删除库存信息
     *
     * @param inventoryId 库存主键
     * @return 结果
     */
    @Override
    public int deleteMpInventoryByInventoryId(Long inventoryId) {
        return mpInventoryMapper.deleteMpInventoryByInventoryId(inventoryId);
    }

    /**
     * 检查快递状态（针对客户角色）
     *
     * @param expressNo 快递单号
     * @return 状态信息
     */
    @Override
    public AjaxResult checkExpressStatusForClient(String expressNo) {
        try {
            // 参数校验
            if (StringUtils.isEmpty(expressNo)) {
                return AjaxResult.error("快递单号不能为空");
            }

            // 获取当前登录用户的客户代码
            String currentClientCode = getCurrentUserClientCode();
            if (StringUtils.isEmpty(currentClientCode)) {
                return AjaxResult.error("无法获取当前用户的客户信息或未绑定客户信息，请联系管理员");
            }

            // 根据快递单号查询库存
            MpInventory inventory = mpInventoryMapper.selectMpInventoryByExpressNo(expressNo);

            Map<String, Object> data = new HashMap<>();

            if (inventory == null) {
                // 1. 不存在：此快递不存在，是否添加？
                data.put("type", "NOT_EXIST");
                data.put("message", "此快递不存在，是否添加？");
            } else {
                String inventoryClientCode = inventory.getClientCode();

                if (StringUtils.isEmpty(inventoryClientCode)) {
                    // 2. 存在但属于未知归属：此快递已到仓库但无人认领，是否认领？
                    data.put("type", "UNKNOWN_OWNER");
                    data.put("message", "此快递已到仓库但无人认领，是否认领？");
                    data.put("inventoryId", inventory.getInventoryId());
                } else if (!inventoryClientCode.equals(currentClientCode)) {
                    // 3. 存在但不属于自己：此快递属于其他人，请您重新核对单号
                    data.put("type", "BELONGS_TO_OTHERS");
                    data.put("message", "此快递属于其他人，请您重新核对单号。");
                } else {
                    // 属于当前用户，检查具体状态
                    if (isInventoryInfoIncomplete(inventory)) {
                        // 4. 存在且属于自己但信息未完善：直接打开详情页，可编辑
                        data.put("type", "INCOMPLETE_INFO");
                        data.put("message", "信息未完善，请完善货物信息");
                        data.put("inventoryId", inventory.getInventoryId());
                    } else if ("已出仓".equals(inventory.getInventoryStatus()) || "已出库".equals(inventory.getInventoryStatus())) {
                        // 6. 存在且属于自己但已出仓：货物已出仓，请联系瑞旗公司获取详情
                        data.put("type", "SHIPPED_OUT");
                        data.put("message", "货物已出仓，请联系瑞旗公司获取详情。");
                    } else {
                        // 5. 存在且属于自己且已入仓：直接打开详情页，仅查看
                        data.put("type", "IN_WAREHOUSE");
                        data.put("message", "货物已入仓");
                        data.put("inventoryId", inventory.getInventoryId());
                    }
                }
            }

            return AjaxResult.success("查询成功", data);
        } catch (Exception e) {
            // 记录异常日志
            // log.error("检查快递状态失败，快递单号：{}", expressNo, e);
            return AjaxResult.error("系统异常，请稍后重试");
        }
    }

    /**
     * 客户认领快递
     *
     * @param inventoryId 库存ID
     * @param clientCode  客户代码
     * @return 结果
     */
    @Override
    @Transactional(rollbackFor = Exception.class)
    public int claimExpressByClient(Long inventoryId, String clientCode) {
        // 参数校验
        if (inventoryId == null) {
            throw new IllegalArgumentException("库存ID不能为空");
        }
        if (StringUtils.isEmpty(clientCode)) {
            throw new IllegalArgumentException("客户代码不能为空");
        }

        // 验证库存是否存在
        MpInventory inventory = mpInventoryMapper.selectMpInventoryByInventoryId(inventoryId);
        if (inventory == null) {
            throw new RuntimeException("库存记录不存在");
        }

        // 验证是否为未知归属状态
        if (StringUtils.isNotEmpty(inventory.getClientCode())) {
            throw new RuntimeException("该快递已有归属，无法认领");
        }

        // 验证客户代码的有效性（可选：检查客户是否存在）
        RsWarehouseClient warehouseClient = rsWarehouseClientMapper.selectRsWarehouseClientByClientCode(clientCode);
        if (warehouseClient == null) {
            throw new RuntimeException("客户代码无效");
        }

        // 更新客户代码和相关信息
        MpInventory updateInventory = new MpInventory();
        updateInventory.setInventoryId(inventoryId);
        updateInventory.setClientCode(clientCode);
        updateInventory.setClientName(warehouseClient.getClientName());
        updateInventory.setRecordStatus("2"); // 设置为信息未完善状态
        updateInventory.setClaimTime(new Date()); // 设置认领时间

        int result = mpInventoryMapper.updateMpInventory(updateInventory);
        if (result <= 0) {
            throw new RuntimeException("认领失败，请重试");
        }

        return result;
    }

    /**
     * 获取当前登录用户的客户代码
     *
     * @return 客户代码，如果获取失败或用户未关联客户则返回null
     */
    private String getCurrentUserClientCode() {
        try {
            // 从SecurityUtils获取当前用户信息
            Long userId = SecurityUtils.getUserId();
            if (userId == null) {
                return null;
            }

            // 查询用户信息
            MpUser mpUser = mpUserMapper.selectMpUserByUserId(userId);
            if (mpUser == null) {
                return null;
            }

            // 获取用户关联的仓库客户信息
            MpWarehouseClient mpWarehouseClient = mpUser.getMpWarehouseClient();
            if (mpWarehouseClient == null) {
                return null;
            }

            // 获取客户代码
            String clientCode = mpWarehouseClient.getClientCode();
            if (StringUtils.isEmpty(clientCode)) {
                return null;
            }

            return clientCode;
        } catch (Exception e) {
            // 记录异常日志（如果需要的话）
            // log.error("获取当前用户客户代码失败", e);
            return null;
        }
    }

    /**
     * 检查库存信息是否不完整
     */
    private boolean isInventoryInfoIncomplete(MpInventory inventory) {
        // 检查关键信息是否缺失
        return StringUtils.isEmpty(inventory.getConsigneeName()) ||
                StringUtils.isEmpty(inventory.getConsigneeTel()) ||
                StringUtils.isEmpty(inventory.getCargoName()) ||
                inventory.getTotalBoxes() == null ||
                inventory.getTotalGrossWeight() == null;
    }
}
