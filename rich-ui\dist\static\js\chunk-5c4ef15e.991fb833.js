(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-5c4ef15e"],{"083c":function(t,e,n){"use strict";n.r(e);var r=function(){var t=this,e=t.$createElement,n=t._self._c||e;return n("el-table",{staticStyle:{width:"100%"},attrs:{data:t.tableData,border:""}},[n("el-table-column",{attrs:{label:"操作员",prop:"opName",width:"180"}}),n("el-table-column",{attrs:{label:"上月累计",prop:"lastMonth",width:"180"}}),n("el-table-column",{attrs:{label:"本月累计",prop:"currentMonth"}}),n("el-table-column",{attrs:{label:"昨天累计",prop:"lastDay"}}),n("el-table-column",{attrs:{label:"当天累计",prop:"currentDay"}})],1)},a=[],c=n("5530"),o=(n("d81d"),n("20f5")),u={name:"OpStatistics",data:function(){return{tableData:[]}},mounted:function(){this.loadData(),this.$on("updateStatistics",(function(){this.loadData()}))},methods:{loadData:function(){var t=this;Object(o["e"])().then((function(e){e.data&&(t.tableData=e.data.map((function(t){return null==t.opId?Object(c["a"])(Object(c["a"])({},t),{},{opName:"合计"}):t})))}))}}},l=u,s=n("2877"),d=Object(s["a"])(l,r,a,!1,null,"1770f753",null);e["default"]=d.exports},"20f5":function(t,e,n){"use strict";n.d(e,"f",(function(){return a})),n.d(e,"c",(function(){return c})),n.d(e,"d",(function(){return o})),n.d(e,"a",(function(){return u})),n.d(e,"k",(function(){return l})),n.d(e,"b",(function(){return s})),n.d(e,"i",(function(){return d})),n.d(e,"j",(function(){return i})),n.d(e,"g",(function(){return f})),n.d(e,"h",(function(){return m})),n.d(e,"e",(function(){return b}));var r=n("b775");function a(t){return Object(r["a"])({url:"/system/rct/list",method:"get",params:t})}function c(t){return Object(r["a"])({url:"/system/rct/"+t,method:"get"})}function o(){return Object(r["a"])({url:"/system/rctold/mon",method:"get"})}function u(t){return Object(r["a"])({url:"/system/rct",method:"post",data:t})}function l(t){return Object(r["a"])({url:"/system/rct",method:"put",data:t})}function s(t){return Object(r["a"])({url:"/system/rct/"+t,method:"delete"})}function d(t){return Object(r["a"])({url:"/system/rctold/saveRctLogistics",method:"post",data:t})}function i(t){return Object(r["a"])({url:"/system/rctold/saveRctPreCarriage",method:"post",data:t})}function f(t){return Object(r["a"])({url:"/system/rctold/saveRctExportDeclaration",method:"post",data:t})}function m(t){return Object(r["a"])({url:"/system/rctold/saveRctImportClearance",method:"post",data:t})}function b(){return Object(r["a"])({url:"system/rctold/getRctStatistics",method:"get"})}}}]);