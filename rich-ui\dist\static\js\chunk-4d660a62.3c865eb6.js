(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-4d660a62"],{3964:function(e,t,a){"use strict";a.d(t,"d",(function(){return n})),a.d(t,"c",(function(){return o})),a.d(t,"a",(function(){return l})),a.d(t,"e",(function(){return i})),a.d(t,"b",(function(){return s}));var r=a("b775");function n(e){return Object(r["a"])({url:"/system/agreementrecord/list",method:"get",params:e})}function o(e){return Object(r["a"])({url:"/system/agreementrecord/"+e,method:"get"})}function l(e){return Object(r["a"])({url:"/system/agreementrecord",method:"post",data:e})}function i(e){return Object(r["a"])({url:"/system/agreementrecord",method:"put",data:e})}function s(e){return Object(r["a"])({url:"/system/agreementrecord/"+e,method:"delete"})}},e0aa:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[a("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"},{name:"dialogDragWidth",rawName:"v-dialogDragWidth"}],attrs:{"close-on-click-modal":!1,"modal-append-to-body":!1,visible:e.openContent,"append-to-body":"",title:"协议列表",width:"1600px"},on:{"update:visible":function(t){e.openContent=t}}},[a("el-row",{staticClass:"mb8",attrs:{gutter:10}},[a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:agreementrecord:add"],expression:"['system:agreementrecord:add']"}],attrs:{icon:"el-icon-plus",plain:"",size:"mini",type:"primary"},on:{click:e.handleAdd}},[e._v("新增 ")])],1),a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:agreementrecord:remove"],expression:"['system:agreementrecord:remove']"}],attrs:{disabled:e.multiple,icon:"el-icon-delete",plain:"",size:"mini",type:"danger"},on:{click:e.handleDelete}},[e._v("删除 ")])],1),a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:agreementrecord:export"],expression:"['system:agreementrecord:export']"}],attrs:{icon:"el-icon-download",plain:"",size:"mini",type:"warning"},on:{click:e.handleExport}},[e._v("导出 ")])],1),a("right-toolbar",{attrs:{showSearch:e.showSearch},on:{"update:showSearch":function(t){e.showSearch=t},"update:show-search":function(t){e.showSearch=t},queryTable:e.getList}})],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.agreementrecordList,border:"",stripe:""},on:{"selection-change":e.handleSelectionChange}},[a("el-table-column",{attrs:{align:"left",type:"selection",width:"39px"}}),a("el-table-column",{attrs:{align:"center",label:"公司",prop:"sqdCompanyId",width:"69px"}},[a("div",[e._v(e._s(this.company.companyShortName))])]),a("el-table-column",{attrs:{align:"center",label:"协议号",prop:"agreementNumber",width:"69px"}}),a("el-table-column",{attrs:{align:"center",label:"协议类型",prop:"agreementTypeId","show-tooltip-when-overflow":"",width:"69px"}}),a("el-table-column",{attrs:{align:"center",label:"负责员工",prop:"staffName",width:"69px"}}),a("el-table-column",{attrs:{align:"center",label:"协议起始日",prop:"agreementStartDate",width:"85px"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(e.parseTime(t.row.agreementStartDate,"{y}-{m}-{d}")))])]}}])}),a("el-table-column",{attrs:{align:"center",label:"协议终止日",prop:"agreementEndDate",width:"85px"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(e.parseTime(t.row.agreementEndDate,"{y}-{m}-{d}")))])]}}])}),a("el-table-column",{attrs:{align:"center",label:"是否有效",prop:"isAvailable",width:"69px"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("dict-tag",{attrs:{options:e.dict.type.sys_yes_no,value:t.row.isAvailable}})]}}])}),a("el-table-column",{key:"creditLimit",attrs:{align:"center",label:"信用额度",width:"69px"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(t.row.creditLimit)+"万 ")]}}])}),a("el-table-column",{attrs:{align:"center",label:"协议币种",prop:"currencyId",width:"69px"}}),a("el-table-column",{attrs:{align:"center",label:"支付凭据",prop:"paymentDateNodeId",width:"69px"}}),a("el-table-column",{key:"creditDays",attrs:{align:"center",label:"信用周期",width:"69px"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(t.row.creditDays)+"月 ")]}}])}),a("el-table-column",{attrs:{align:"center",label:"是否工作日",prop:"isWorkingDay"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("dict-tag",{attrs:{options:e.dict.type.sys_yes_no,value:t.row.isWorkingDay}})]}}])}),a("el-table-column",{attrs:{align:"center",label:"信用评级",prop:"creditLevel",width:"69px"}}),a("el-table-column",{attrs:{align:"center",label:"是否锁定",prop:"isLocked",width:"69px"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("dict-tag",{attrs:{options:e.dict.type.sys_is_lock,value:t.row.isLocked}})]}}])}),a("el-table-column",{attrs:{align:"center",label:"经理确认时间",prop:"deptConfirmedDate",width:"100px"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(e.parseTime(t.row.deptConfirmedDate,"{y}-{m}-{d}")))])]}}])}),a("el-table-column",{attrs:{align:"center",label:"财务确认时间",prop:"financeConfirmedDate",width:"100px"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(e.parseTime(t.row.financeConfirmedDate,"{y}-{m}-{d}")))])]}}])}),a("el-table-column",{attrs:{align:"center",label:"结款日",prop:"settlementDate",width:"58px"}}),a("el-table-column",{attrs:{align:"center",label:"备注",prop:"remark",width:"48px"}}),a("el-table-column",{attrs:{align:"center","class-name":"small-padding fixed-width",label:"操作",width:"48px"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("div",{staticStyle:{height:"15px",padding:"0",margin:"0"}},["1"!=t.row.isLocked?a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:agreementrecord:edit"],expression:"['system:agreementrecord:edit']"}],staticStyle:{display:"flex"},attrs:{size:e.size,icon:"el-icon-edit",type:"success"},on:{click:function(a){return e.handleUpdate(t.row)}}},[e._v("修改 ")]):e._e()],1),a("div",{staticStyle:{height:"15px",padding:"0",margin:"0"}},["1"!=t.row.isLocked?a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:agreementrecord:remove"],expression:"['system:agreementrecord:remove']"}],staticStyle:{display:"flex"},attrs:{size:e.size,icon:"el-icon-delete",type:"danger"},on:{click:function(a){return e.handleDelete(t.row)}}},[e._v("删除 ")]):e._e()],1)]}}])})],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total>0"}],attrs:{limit:e.queryParams.pageSize,page:e.queryParams.pageNum,total:e.total},on:{"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},pagination:e.getList}})],1),a("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"},{name:"dialogDragWidth",rawName:"v-dialogDragWidth"}],attrs:{"append-to-body":!0,"close-on-click-modal":!1,"modal-append-to-body":!1,title:e.title,visible:e.oopen,width:"800px"},on:{"update:visible":function(t){e.oopen=t}}},[a("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"100px"}},[a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"合作公司",prop:"sqdCompanyId"}},[a("a",{staticStyle:{margin:"0","font-weight":"bold","font-size":"small"}},[e._v(e._s(this.company.companyShortName))])])],1)],1),a("el-row",[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"是否有效",prop:"isAvailable"}},[a("el-select",{attrs:{placeholder:"是否有效"},model:{value:e.form.isAvailable,callback:function(t){e.$set(e.form,"isAvailable",t)},expression:"form.isAvailable"}},e._l(e.dict.type.sys_yes_no,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"协议号",prop:"agreementNumber"}},[a("el-input",{attrs:{placeholder:"与合作公司签订的协议号"},model:{value:e.form.agreementNumber,callback:function(t){e.$set(e.form,"agreementNumber",t)},expression:"form.agreementNumber"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"协议类型",prop:"agreementTypeId"}},[a("el-input",{attrs:{placeholder:"协议类型"},model:{value:e.form.agreementTypeId,callback:function(t){e.$set(e.form,"agreementTypeId",t)},expression:"form.agreementTypeId"}})],1)],1)],1),a("el-row",[a("el-form-item",{attrs:{label:"负责员工",prop:"staffName"}},[e._v(" "+e._s(this.form.staffName)+" ")])],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"协议起始日",prop:"agreementStartDate"}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{clearable:"",placeholder:"协议有效期从",type:"date","value-format":"yyyy-MM-dd"},model:{value:e.form.agreementStartDate,callback:function(t){e.$set(e.form,"agreementStartDate",t)},expression:"form.agreementStartDate"}})],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"协议终止日",prop:"agreementEndDate"}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{clearable:"",placeholder:"协议有效期至",type:"date","value-format":"yyyy-MM-dd"},model:{value:e.form.agreementEndDate,callback:function(t){e.$set(e.form,"agreementEndDate",t)},expression:"form.agreementEndDate"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"协议币种",prop:"currencyId"}},[a("tree-select",{attrs:{pass:e.form.currencyId,type:"currency"},on:{return:e.getCurrencyId}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"信用额度",prop:"creditLimit"}},[a("el-input-number",{attrs:{controls:!1,precision:2,step:.01,placeholder:"信用额度，默认0"},model:{value:e.form.creditLimit,callback:function(t){e.$set(e.form,"creditLimit",t)},expression:"form.creditLimit"}}),e._v(" 万 ")],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"支付凭据",prop:"paymentDateNodeId"}},[a("el-input",{attrs:{placeholder:"支付时间节点凭据"},model:{value:e.form.paymentDateNodeId,callback:function(t){e.$set(e.form,"paymentDateNodeId",t)},expression:"form.paymentDateNodeId"}})],1)],1)],1),a("el-row",[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"信用周期",prop:"creditDays"}},[a("el-input-number",{attrs:{controls:!1,max:12,precision:2,step:.01,placeholder:"信用周期"},model:{value:e.form.creditDays,callback:function(t){e.$set(e.form,"creditDays",t)},expression:"form.creditDays"}}),e._v(" 月 ")],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"信用评级",prop:"creditLevel"}},[a("el-input",{attrs:{placeholder:"信用评级，待定"},model:{value:e.form.creditLevel,callback:function(t){e.$set(e.form,"creditLevel",t)},expression:"form.creditLevel"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"是否工作日",prop:"isWorkingDay"}},[a("el-select",{attrs:{placeholder:"是否工作日"},model:{value:e.form.isWorkingDay,callback:function(t){e.$set(e.form,"isWorkingDay",t)},expression:"form.isWorkingDay"}},e._l(e.dict.type.sys_yes_no,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1)],1)],1),a("el-row",[a("el-form-item",{attrs:{label:"是否已锁定",prop:"isLocked"}},[a("dict-tag",{attrs:{options:e.dict.type.sys_is_lock,value:e.form.isLocked}})],1)],1),a("el-form-item",{attrs:{label:"结款日",prop:"settlementDate"}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{clearable:"",placeholder:"结款日",type:"date","value-format":"yyyy-MM-dd"},model:{value:e.form.settlementDate,callback:function(t){e.$set(e.form,"settlementDate",t)},expression:"form.settlementDate"}})],1),a("el-form-item",{attrs:{label:"备注",prop:"remark"}},[a("el-input",{attrs:{autosize:{minRows:10,maxRows:20},maxlength:"150",placeholder:"备注","show-word-limit":"",type:"textarea"},model:{value:e.form.remark,callback:function(t){e.$set(e.form,"remark",t)},expression:"form.remark"}})],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"部门确认",prop:"deptConfirmed"}},[a("el-row",[a("el-col",{attrs:{span:4}},[a("dict-tag",{attrs:{options:e.dict.type.sys_is_confirm,value:e.form.deptConfirmed}})],1),a("el-col",{attrs:{span:20}},[0==e.form.deptConfirmed?a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:agreementrecord:deptlock"],expression:"['system:agreementrecord:deptlock']"}],attrs:{icon:"el-icon-lock",plain:"",size:"mini",type:"primary"},on:{click:e.deptLock}},[e._v("锁定 ")]):e._e(),1==e.form.deptConfirmed?a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:agreementrecord:deptunlock"],expression:"['system:agreementrecord:deptunlock']"}],attrs:{icon:"el-icon-unlock",plain:"",size:"mini",type:"primary"},on:{click:e.deptLock}},[e._v("解锁 ")]):e._e()],1)],1)],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"部门确认时间",prop:"deptConfirmedDate"}},[e._v(" "+e._s(e.form.deptConfirmedDate)+" ")])],1)],1),a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"财务确认",prop:"financeConfirmed"}},[a("el-row",[a("el-col",{attrs:{span:4}},[a("dict-tag",{attrs:{options:e.dict.type.sys_is_confirm,value:e.form.financeConfirmed}})],1),a("el-col",{attrs:{span:20}},[0==e.form.financeConfirmed?a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:agreementrecord:financelock"],expression:"['system:agreementrecord:financelock']"}],attrs:{icon:"el-icon-lock",plain:"",size:"mini",type:"primary"},on:{click:e.financeLock}},[e._v("锁定 ")]):e._e(),1==e.form.financeConfirmed?a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:agreementrecord:financeunlock"],expression:"['system:agreementrecord:financeunlock']"}],attrs:{icon:"el-icon-unlock",plain:"",size:"mini",type:"primary"},on:{click:e.financeLock}},[e._v("解锁 ")]):e._e()],1)],1)],1)],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"财务确认时间",prop:"financeConfirmedDate"}},[e._v(" "+e._s(e.form.financeConfirmedDate)+" ")])],1)],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("确 定")]),a("el-button",{on:{click:e.cancel}},[e._v("取 消")])],1)],1)],1)},n=[],o=a("5530"),l=(a("d81d"),a("b0c0"),a("3964")),i={name:"agreementRecord",dicts:["sys_yes_no","sys_is_lock","sys_is_confirm"],props:["type","open","loadOptions","company"],data:function(){return{size:this.$store.state.app.size||"mini",openContent:!1,loading:!0,ids:[],single:!0,multiple:!0,showSearch:!0,total:0,agreementrecordList:[],title:"",oopen:!1,queryParams:{pageNum:1,pageSize:20,sqdCompanyId:this.company.companyId,agreementNumber:null,staffId:null},form:{},rules:{}}},watch:{loadOptions:function(){this.agreementrecordList=this.loadOptions,this.loading=!1},open:function(e){this.openContent=e},openContent:function(e){0==e&&this.$emit("openCommunications")}},methods:{getList:function(){var e=this;this.loading=!0,this.queryParams.sqdCompanyId=this.company.companyId,Object(l["d"])(this.queryParams).then((function(t){e.agreementrecordList=t.rows,e.total=t.total,e.loading=!1}))},cancel:function(){this.oopen=!1,this.reset()},reset:function(){this.form={agreementId:null,sqdCompanyId:null,agreementNumber:null,agreementTypeId:null,agreementPrice:null,staffId:null,agreementStartDate:null,agreementEndDate:null,isAvailable:null,creditLimit:null,currencyId:null,paymentDateNodeId:null,creditDays:null,isWorkingDay:null,creditLevel:null,remark:null,isLocked:"0",deptConfirmed:"0",deptConfirmedDate:null,financeConfirmed:"0",financeConfirmedDate:null,orderNum:null,status:"0"},this.resetForm("form")},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.resetForm("queryForm"),this.handleQuery()},handleSelectionChange:function(e){this.ids=e.map((function(e){return e.agreementId})),this.single=1!=e.length,this.multiple=!e.length},handleAdd:function(){this.reset(),this.oopen=!0,this.title="添加协议记录",this.form.staffName=this.$store.state.user.name},handleUpdate:function(e){var t=this;this.reset();var a=e.agreementId||this.ids;Object(l["c"])(a).then((function(e){t.form=e.data,t.oopen=!0,t.title="修改协议记录"}))},submitForm:function(){var e=this;this.$refs["form"].validate((function(t){t&&(e.form.sqdCompanyId=e.company.companyId,null!=e.form.agreementId?Object(l["e"])(e.form).then((function(t){e.$modal.msgSuccess("修改成功"),e.oopen=!1,e.getList()})):(e.form.sqdCompanyId=e.company.companyId,Object(l["a"])(e.form).then((function(t){e.$modal.msgSuccess("新增成功"),e.oopen=!1,e.getList()}))))}))},handleDelete:function(e){var t=this,a=e.agreementId||this.ids;this.$confirm('是否确认删除协议记录编号为"'+a+'"的数据项？',"提示",{customClass:"modal-confirm"}).then((function(){return Object(l["b"])(a)})).then((function(){t.getList(),t.$modal.msgSuccess("删除成功")})).catch((function(){}))},handleExport:function(){this.download("system/agreementrecord/export",Object(o["a"])({},this.queryParams),"agreementrecord_".concat((new Date).getTime(),".xlsx"))},deptLock:function(){var e=this;null!=this.form.agreementId?(this.form.deptConfirmed=0==this.form.deptConfirmed?1:0,this.form.deptConfirmedId=this.$store.state.user.sid,Object(l["e"])(this.form).then((function(t){e.$modal.msgSuccess("修改成功"),e.oopen=!1,e.getList()}))):this.$modal.msgError("错误操作")},financeLock:function(){var e=this;null!=this.form.agreementId?(this.form.financeConfirmed=0==this.form.financeConfirmed?1:0,this.form.financeConfirmedId=this.$store.state.user.sid,Object(l["e"])(this.form).then((function(t){e.$modal.msgSuccess("修改成功"),e.oopen=!1,e.getList()}))):this.$modal.msgError("错误操作")},getCurrencyId:function(e){this.form.currencyId=e}}},s=i,m=a("2877"),c=Object(m["a"])(s,r,n,!1,null,null,null);t["default"]=c.exports}}]);