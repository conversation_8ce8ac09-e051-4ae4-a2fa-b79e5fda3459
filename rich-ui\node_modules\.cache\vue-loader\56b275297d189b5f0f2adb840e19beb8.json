{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\document\\op.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\document\\op.vue", "mtime": 1750732210908}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KDQppbXBvcnQgZG9jdW1lbnQgZnJvbSAiQC92aWV3cy9zeXN0ZW0vZG9jdW1lbnQvaW5kZXgiDQppbXBvcnQgT3JkZXJEaWZmaWN1bHR5U2VsZWN0IGZyb20gIkAvY29tcG9uZW50cy9PcmRlckRpZmZpY3VsdHlTZWxlY3QvaW5kZXgudnVlIg0KaW1wb3J0IENvbXBhbnlTZWxlY3QgZnJvbSAiQC9jb21wb25lbnRzL0NvbXBhbnlTZWxlY3QvaW5kZXgudnVlIg0KaW1wb3J0IHN0b3JlIGZyb20gIkAvc3RvcmUiDQppbXBvcnQgVHJlZXNlbGVjdCBmcm9tICJAcmlvcGhhZS92dWUtdHJlZXNlbGVjdCINCmltcG9ydCBwaW55aW4gZnJvbSAianMtcGlueWluIg0KaW1wb3J0IERvY0xpc3QgZnJvbSAiQC92aWV3cy9zeXN0ZW0vZG9jdW1lbnQvZG9jTGlzdC52dWUiDQppbXBvcnQgQ2hhcmdlTGlzdCBmcm9tICJAL3ZpZXdzL3N5c3RlbS9kb2N1bWVudC9jaGFyZ2VMaXN0LnZ1ZSINCmltcG9ydCBMb2dpc3RpY3NQcm9ncmVzcyBmcm9tICJAL3ZpZXdzL3N5c3RlbS9kb2N1bWVudC9sb2dpc3RpY3NQcm9ncmVzcy52dWUiDQppbXBvcnQgQXVkaXQgZnJvbSAiQC92aWV3cy9zeXN0ZW0vZG9jdW1lbnQvYXVkaXQudnVlIg0KaW1wb3J0IHtwYXJzZVRpbWV9IGZyb20gIkAvdXRpbHMvcmljaCINCmltcG9ydCBMb2dpc3RpY3NOb0luZm8gZnJvbSAiQC92aWV3cy9zeXN0ZW0vZG9jdW1lbnQvbG9naXN0aWNzTm9JbmZvLnZ1ZSINCmltcG9ydCBvcEhpc3RvcnkgZnJvbSAiQC92aWV3cy9zeXN0ZW0vZG9jdW1lbnQvb3BIaXN0b3J5LnZ1ZSINCmltcG9ydCByZWNlaXZhYmxlUGF5YWJsZSBmcm9tICJAL3ZpZXdzL3N5c3RlbS9kb2N1bWVudC9yZWNlaXZhYmxlUGF5YWJsZS52dWUiDQppbXBvcnQgew0KICBhZGRCYXNpY0xvZ2lzdGljcywNCiAgYWRkQ2xpZW50TWVzc2FnZSwNCiAgYWRkRXhwb3J0RGVjbGFyYXRpb24sDQogIGFkZEltcG9ydENsZWFyYW5jZSwNCiAgYWRkUHJlQ2FycmlhZ2UsDQogIGFkZFJjdCwNCiAgZ2V0UmN0LA0KICBnZXRSY3RDRk1vbiwNCiAgZ2V0UmN0TW9uLCBnZXRSY3RSU1dITW9uLA0KICBzYXZlQWxsU2VydmljZSwgc2F2ZUFzQWxsU2VydmljZSwgc2F2ZUFzUmN0LA0KICB1cGRhdGVSY3QNCn0gZnJvbSAiQC9hcGkvc3lzdGVtL3JjdCINCmltcG9ydCBVcmdlbmN5RGVncmVlU2VsZWN0IGZyb20gIkAvY29tcG9uZW50cy9VcmdlbmN5RGVncmVlU2VsZWN0L2luZGV4LnZ1ZSINCmltcG9ydCBQcm9ncmVzc1N0YXR1cyBmcm9tICJAL2NvbXBvbmVudHMvUHJvZ3Jlc3NTdGF0dXMvaW5kZXgudnVlIg0KaW1wb3J0IHtjaGVja1Blcm1pLCBjaGVja1JvbGV9IGZyb20gIkAvdXRpbHMvcGVybWlzc2lvbiINCmltcG9ydCBjdXJyZW5jeSBmcm9tICJjdXJyZW5jeS5qcyINCmltcG9ydCBGcmVpZ2h0U2VsZWN0IGZyb20gIkAvdmlld3Mvc3lzdGVtL2ZyZWlnaHQvZnJlaWdodFNlbGVjdC52dWUiDQppbXBvcnQge2dldFF1b3RhdGlvbiwgcXVlcnlMb2NhbH0gZnJvbSAiQC9hcGkvc3lzdGVtL3F1b3RhdGlvbiINCmltcG9ydCBTZWxlY3RDb21wYW55IGZyb20gIkAvdmlld3Mvc3lzdGVtL2NvbXBhbnkvc2VsZWN0Q29tcGFueS52dWUiDQppbXBvcnQge2dldEJvb2tpbmd9IGZyb20gIkAvYXBpL3N5c3RlbS9ib29raW5nIg0KaW1wb3J0IFByb2dyZXNzTmFtZSBmcm9tICJAL2NvbXBvbmVudHMvUHJvZ3Jlc3NOYW1lL2luZGV4LnZ1ZSINCmltcG9ydCBQcmludFRlbXBsYXRlIGZyb20gIkAvdmlld3Mvc3lzdGVtL3ByaW50L1ByaW50VGVtcGxhdGUudnVlIg0KaW1wb3J0IHtzZWxlY3RMaXN0RXhjaGFuZ2VyYXRlfSBmcm9tICJAL2FwaS9zeXN0ZW0vZXhjaGFuZ2VyYXRlIg0KaW1wb3J0IHtkZWZhdWx0RWxlbWVudFR5cGVQcm92aWRlciwgaGlwcmludH0gZnJvbSAiLi4vLi4vLi4vaW5kZXgiDQppbXBvcnQgcHJpbnRQcmV2aWV3IGZyb20gIkAvdmlld3MvcHJpbnQvZGVtby9kZXNpZ24vcHJldmlldy52dWUiDQppbXBvcnQgdHJ1Y2tpbmdPcmRlciBmcm9tICIuLi8uLi8uLi9wcmludC10ZW1wbGF0ZS90cnVja2luZ09yZGVyIg0KaW1wb3J0IGRpc3BhdGNoQmlsbCBmcm9tICIuLi8uLi8uLi9wcmludC10ZW1wbGF0ZS9kaXNwYXRjaEJpbGwiDQppbXBvcnQgYm9va2luZyBmcm9tICIuLi8uLi8uLi9wcmludC10ZW1wbGF0ZS9ib29raW5nIg0KaW1wb3J0IENGTEJvb2tpbmcgZnJvbSAiLi4vLi4vLi4vcHJpbnQtdGVtcGxhdGUvQ0ZMQmJvb2tpbmciDQppbXBvcnQgZGViaXROb2RlIGZyb20gIi4uLy4uLy4uL3ByaW50LXRlbXBsYXRlL2RlYml0Tm9kZSINCmltcG9ydCBkZWJpdE5vZGVFbiBmcm9tICIuLi8uLi8uLi9wcmludC10ZW1wbGF0ZS9kZWJpdE5vZGVFbiINCmltcG9ydCBkZWJpdE5vZGVFbkhLUk1CVG9VU0QgZnJvbSAiLi4vLi4vLi4vcHJpbnQtdGVtcGxhdGUvZGViaXROb2RlRW5IS1JNQlRvVVNEIg0KaW1wb3J0IGRlYml0Tm9kZVVTRFRvUk1CIGZyb20gIi4uLy4uLy4uL3ByaW50LXRlbXBsYXRlL2RlYml0Tm9kZVVTRFRvUk1CIg0KaW1wb3J0IGRlYml0Tm9kZVpTVVNEIGZyb20gIi4uLy4uLy4uL3ByaW50LXRlbXBsYXRlL2RlYml0Tm9kZVpTVVNEIg0KaW1wb3J0IGRlYml0Tm9kZUNGTCBmcm9tICIuLi8uLi8uLi9wcmludC10ZW1wbGF0ZS9kZWJpdE5vZGVDRkwiDQppbXBvcnQgZGViaXROb2RlQ0ZMVG9STUIgZnJvbSAiLi4vLi4vLi4vcHJpbnQtdGVtcGxhdGUvZGViaXROb2RlQ0ZMVG9STUIiDQppbXBvcnQgaW52b2ljZSBmcm9tICIuLi8uLi8uLi9wcmludC10ZW1wbGF0ZS9pbnZvaWNlIg0KaW1wb3J0IEZDTEJpbGwgZnJvbSAiLi4vLi4vLi4vcHJpbnQtdGVtcGxhdGUvRkNMQmlsbCINCmltcG9ydCBBaXJCaWxsIGZyb20gIi4uLy4uLy4uL3ByaW50LXRlbXBsYXRlL0FpckJpbGwiDQppbXBvcnQgbW9tZW50IGZyb20gIm1vbWVudCINCmltcG9ydCB7YWRkUHNhcmN0LCB1cGRhdGVQc2FyY3R9IGZyb20gIkAvYXBpL3N5c3RlbS9wc2FyY3QiDQppbXBvcnQgUHNhQm9va2luZ0xpc3RTZWxlY3QgZnJvbSAiQC92aWV3cy9zeXN0ZW0vYm9va2luZy9wc2FCb29raW5nTGlzdFNlbGVjdC52dWUiDQppbXBvcnQge2FkZENsaWVudHNpbmZvfSBmcm9tICJAL2FwaS9zeXN0ZW0vY2xpZW50c2luZm8iDQppbXBvcnQgQ29tbW9uVXNlZFNlbGVjdCBmcm9tICJAL3ZpZXdzL3N5c3RlbS9jb21tb251c2VkL2NvbW1vblVzZWRTZWxlY3QudnVlIg0KaW1wb3J0IHtsb2NhdGlvbk9wdGlvbnN9IGZyb20gIkAvYXBpL3N5c3RlbS9sb2NhdGlvbiINCmltcG9ydCBiaWxsT2ZMYWRpbmcgZnJvbSAiQC9wcmludC10ZW1wbGF0ZS9iaWxsT2ZMYWRpbmciDQppbXBvcnQgYmlsbE9mTGFkaW5nUmVsZWFzZSBmcm9tICJAL3ByaW50LXRlbXBsYXRlL2JpbGxPZkxhZGluZ1JlbGVhc2UiDQppbXBvcnQge3VwZGF0ZUNoYXJnZX0gZnJvbSAiQC9hcGkvc3lzdGVtL3JzQ2hhcmdlIg0KaW1wb3J0IHt1cGRhdGVTZXJ2aWNlaW5zdGFuY2VzfSBmcm9tICJAL2FwaS9zeXN0ZW0vc2VydmljZWluc3RhbmNlcyINCmltcG9ydCB0b1dvcmRzIGZyb20gIm51bS13b3JkcyINCmltcG9ydCBDaGFyZ2VTZWxlY3QgZnJvbSAiQC92aWV3cy9zeXN0ZW0vZG9jdW1lbnQvY2hhcmdlU2VsZWN0LnZ1ZSINCmltcG9ydCBEYXRlUGlja2VySXRlbSBmcm9tICJAL3ZpZXdzL3N5c3RlbS9EYXRlUGlja2VySXRlbS9pbmRleC52dWUiDQppbXBvcnQgT3V0Ym91bmRQbGFuIGZyb20gIkAvdmlld3Mvc3lzdGVtL2RvY3VtZW50L291dGJvdW5kUGxhbi52dWUiDQoNCmxldCBoaXByaW50VGVtcGxhdGUNCg0KZXhwb3J0IGRlZmF1bHQgew0KICBuYW1lOiAib3AiLA0KICBwcm9wczogWyJ0eXBlIl0sDQogIGNvbXBvbmVudHM6IHsNCiAgICBPdXRib3VuZFBsYW4sDQogICAgRGF0ZVBpY2tlckl0ZW0sDQogICAgQ2hhcmdlU2VsZWN0LA0KICAgIENvbW1vblVzZWRTZWxlY3QsDQogICAgUHNhQm9va2luZ0xpc3RTZWxlY3QsDQogICAgcHJpbnRQcmV2aWV3LA0KICAgIFByaW50VGVtcGxhdGUsDQogICAgUHJvZ3Jlc3NOYW1lLA0KICAgIFNlbGVjdENvbXBhbnksDQogICAgRnJlaWdodFNlbGVjdCwNCiAgICBQcm9ncmVzc1N0YXR1cywNCiAgICBVcmdlbmN5RGVncmVlU2VsZWN0LA0KICAgIHJlY2VpdmFibGVQYXlhYmxlLCBvcEhpc3RvcnksIExvZ2lzdGljc05vSW5mbywNCiAgICBBdWRpdCwNCiAgICBMb2dpc3RpY3NQcm9ncmVzcywNCiAgICBDaGFyZ2VMaXN0LA0KICAgIERvY0xpc3QsDQogICAgVHJlZXNlbGVjdCwNCiAgICBDb21wYW55U2VsZWN0LA0KICAgIE9yZGVyRGlmZmljdWx0eVNlbGVjdCwNCiAgICBkb2N1bWVudA0KICB9LA0KICBkYXRhKCkgew0KICAgIHZhciBjaGVja09yZGVyQmVsb25nc1RvID0gKHJ1bGUsIHZhbHVlLCBjYWxsYmFjaykgPT4gew0KICAgICAgaWYgKCF2YWx1ZSkgew0KICAgICAgICBjYWxsYmFjayhuZXcgRXJyb3IoIuivt+i+k+WFpeaJgOWxnuWFrOWPuCIpKQ0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgLyogaWYgKHRoaXMuZm9ybS5vcmRlckJlbG9uZ3NUbyAhPT0gJycpIHsNCiAgICAgICAgICB0aGlzLiRyZWZzLmZvcm0udmFsaWRhdGVGaWVsZCgnb3JkZXJCZWxvbmdzVG8nKTsNCiAgICAgICAgfSAqLw0KICAgICAgICBjYWxsYmFjaygpDQogICAgICB9DQogICAgfQ0KICAgIHJldHVybiB7DQogICAgICBvdXRib3VuZEZvcm06IG51bGwsDQogICAgICBvcGVuT3V0Ym91bmQ6IGZhbHNlLA0KICAgICAgb3V0Ym91bmREYXRhOiB7fSwNCiAgICAgIGZvcm1UeXBlOiBudWxsLA0KICAgICAgc2VydmljZUxpc3Q6IG5ldyBTZXQoKSwNCiAgICAgIGZvcm06IHsNCiAgICAgICAgbm9UcmFuc2ZlckFsbG93ZWQ6IGZhbHNlLA0KICAgICAgICBub0RpdmlkZWRBbGxvd2VkOiBmYWxzZSwNCiAgICAgICAgbm9BZ3JlZW1lbnRTaG93ZWQ6IGZhbHNlLA0KICAgICAgICBpc0N1c3RvbXNJbnRyYW5zaXRTaG93ZWQ6IGZhbHNlLA0KICAgICAgICByc09wU2VhRmNsTGlzdDogW10sDQogICAgICAgIHJzT3BTZWFMY2xMaXN0OiBbXSwNCiAgICAgICAgcnNPcEFpckxpc3Q6IFtdLA0KICAgICAgICByc09wQ3RuclRydWNrTGlzdDogW10sDQogICAgICAgIHJzT3BCdWxrVHJ1Y2tMaXN0OiBbXSwNCiAgICAgICAgcnNPcERvY0RlY2xhcmVMaXN0OiBbXSwNCiAgICAgICAgcnNPcEZyZWVEZWNsYXJlTGlzdDogW10NCiAgICAgIH0sDQogICAgICBzdGF0dXNGb3JtOiB7fSwNCiAgICAgIHJ1bGVzOiB7DQogICAgICAgIGNsaWVudElkOiBbe3JlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi6K+36YCJ5oup5a6i5oi3IiwgdHJpZ2dlcjogImJsdXIifV0sDQogICAgICAgIGdvb2RzTmFtZVN1bW1hcnk6IFt7cmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLor7floavlhpnotKflkI0iLCB0cmlnZ2VyOiAiYmx1ciJ9XSwNCiAgICAgICAgZ3Jvc3NXZWlnaHQ6IFt7cmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLor7floavlhpnotKflkI0iLCB0cmlnZ2VyOiAiYmx1ciJ9XSwNCiAgICAgICAgbG9naXN0aWNzVHlwZUlkOiBbe3JlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi6K+36YCJ5oup54mp5rWB57G75Z6LIiwgdHJpZ2dlcjogImJsdXIifV0sDQogICAgICAgIHBvbElkOiBbe3JlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi6K+36YCJ5oup5ZCv6L+Q5rivIiwgdHJpZ2dlcjogImJsdXIifV0sDQogICAgICAgIGRlc3RpbmF0aW9uUG9ydElkOiBbe3JlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi6K+36YCJ5oup55uu55qE5rivIiwgdHJpZ2dlcjogImJsdXIifV0sDQogICAgICAgIHNlcnZpY2VUeXBlSWRzOiBbe3JlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi6K+36YCJ5oup5pyN5Yqh5YiX6KGoIiwgdHJpZ2dlcjogImJsdXIifV0sDQogICAgICAgIGJsRm9ybUNvZGU6IFt7cmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLor7fpgInmi6nlh7rljZXmlrnlvI8iLCB0cmlnZ2VyOiAiYmx1ciJ9XSwNCiAgICAgICAgcmV2ZW51ZVRvbjogW3tyZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuivt+mAieaLqeiuoei0uei0p+mHjyIsIHRyaWdnZXI6ICJibHVyIn1dLA0KICAgICAgICBzYWxlc0lkOiBbe3JlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi6K+36YCJ5oup6K6h6LS56LSn6YePIiwgdHJpZ2dlcjogImJsdXIifV0sDQogICAgICAgIC8vIGdyb3NzV2VpZ2h0OiBbe3JlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi6K+36L6T5YWl5q+b6YeNIiwgdHJpZ2dlcjogImJsdXIifV0sDQogICAgICAgIC8vIGltcEV4cFR5cGU6IFt7cmVxdWlyZWQ6IHRydWUsIG1lc3NhZ2U6ICLor7fpgInmi6nov5vlh7rlj6PnsbvlnosiLCB0cmlnZ2VyOiAiYmx1ciJ9XSwNCiAgICAgICAgb3JkZXJCZWxvbmdzVG86IFt7dmFsaWRhdG9yOiBjaGVja09yZGVyQmVsb25nc1RvLCB0cmlnZ2VyOiAiYmx1ciJ9XQ0KICAgICAgfSwNCiAgICAgIHJjdDogew0KICAgICAgICBsZWFkaW5nQ2hhcmFjdGVyOiAiUkNUIiwNCiAgICAgICAgR1pDRkxlYWRpbmdDaGFyYWN0ZXI6ICJDRkwiLA0KICAgICAgICBSU1dITGVhZGluZ0NoYXJhY3RlcjogIlJTSCIsDQogICAgICAgIG1vbnRoOiAxLA0KICAgICAgICBub051bTogMSwNCiAgICAgICAgcmN0Tm86IG51bGwNCiAgICAgIH0sDQogICAgICBvcGVuR2VuZXJhdGVSY3Q6IGZhbHNlLA0KICAgICAgcHNhVmVyaWZ5OiBmYWxzZSwNCiAgICAgIG9wOiBmYWxzZSwNCiAgICAgIGZpbmFuY2U6IGZhbHNlLA0KICAgICAgYm9va2luZzogZmFsc2UsDQogICAgICBjb21wYW55TGlzdDogW10sDQogICAgICBncm9zc1dlaWdodDogbnVsbCwNCiAgICAgIGdvb2RzVmFsdWU6IG51bGwsDQogICAgICBsb2NhdGlvbk9wdGlvbnM6IFtdLA0KICAgICAgbG9naXN0aWNzVHlwZTogbnVsbCwNCiAgICAgIGNhcnJpZXJJZHM6IFtdLA0KICAgICAgY2Fycmllckxpc3Q6IFtdLA0KICAgICAgY2xpZW50RG9jTGlzdDogW10sDQogICAgICBvcGVuRG9jTGlzdDogZmFsc2UsDQogICAgICBsb2dpc3RpY3NQcm9ncmVzc0xpc3Q6IFtdLA0KICAgICAgYmVsb25nTGlzdDogW10sDQogICAgICBidXNpbmVzc0xpc3Q6IFtdLA0KICAgICAgb3BMaXN0OiBbXSwNCiAgICAgIC8vIOWuouaIt+S/oeaBr+aVsOaNrg0KICAgICAgcnNDbGllbnRNZXNzYWdlOiB7DQogICAgICAgIHJzQ2hhcmdlTGlzdDogW10sDQogICAgICAgIHJzT3BMb2dMaXN0OiBbXSwNCiAgICAgICAgcnNEb2NMaXN0OiBbXQ0KICAgICAgfSwNCiAgICAgIC8vIG5ldyDlrZDmnI3liqHmlbDmja4NCiAgICAgIHJzT3BTZWFGY2w6IHsNCiAgICAgICAgcnNTZXJ2aWNlSW5zdGFuY2VzOiB7DQogICAgICAgICAgYWNjb3VudENvbmZpcm1UaW1lOiBudWxsLA0KICAgICAgICAgIGFncmVlbWVudE5vOiAiIiwNCiAgICAgICAgICBhZ3JlZW1lbnRUeXBlQ29kZTogbnVsbCwNCiAgICAgICAgICBjbGllbnRDb25maXJtZWRUaW1lOiBudWxsLA0KICAgICAgICAgIGNvbmZpcm1BY2NvdW50SWQ6IG51bGwsDQogICAgICAgICAgY3JlYXRlQnk6IG51bGwsDQogICAgICAgICAgY3JlYXRlQnlOYW1lOiBudWxsLA0KICAgICAgICAgIGNyZWF0ZVRpbWU6IG51bGwsDQogICAgICAgICAgZGVsZXRlQnk6IG51bGwsDQogICAgICAgICAgZGVsZXRlQnlOYW1lOiBudWxsLA0KICAgICAgICAgIGRlbGV0ZVN0YXR1czogbnVsbCwNCiAgICAgICAgICBkZWxldGVUaW1lOiBudWxsLA0KICAgICAgICAgIGlucXVpcnlJbm5lclJlbWFyazogIiIsDQogICAgICAgICAgaW5xdWlyeUxlYXRlc3RVcGRhdGVkVGltZTogbnVsbCwNCiAgICAgICAgICBpbnF1aXJ5Tm86IG51bGwsDQogICAgICAgICAgaW5xdWlyeU5vdGljZTogbnVsbCwNCiAgICAgICAgICBpbnF1aXJ5UHNhSWQ6IG51bGwsDQogICAgICAgICAgaXNBY2NvdW50Q29uZmlybWVkOiBudWxsLA0KICAgICAgICAgIGlzRG5DbGllbnRDb25maXJtZWQ6IG51bGwsDQogICAgICAgICAgaXNEbk9wQ29uZmlybWVkOiBudWxsLA0KICAgICAgICAgIGlzRG5Qc2FDb25maXJtZWQ6IG51bGwsDQogICAgICAgICAgaXNEblNhbGVzQ29uZmlybWVkOiBudWxsLA0KICAgICAgICAgIGlzRG5TdXBwbGllckNvbmZpcm1lZDogbnVsbCwNCiAgICAgICAgICBsb2dpc3RpY3NQYXltZW50VGVybXNDb2RlOiBudWxsLA0KICAgICAgICAgIG1heFdlaWdodDogbnVsbCwNCiAgICAgICAgICBvcENvbmZpcm1lZFRpbWU6IG51bGwsDQogICAgICAgICAgcGF5bWVudFRpdGxlQ29kZTogbnVsbCwNCiAgICAgICAgICBwZXJtaXNzaW9uTGV2ZWw6IG51bGwsDQogICAgICAgICAgcHNhQ29uZmlybWVkVGltZTogbnVsbCwNCiAgICAgICAgICByY3RJZDogbnVsbCwNCiAgICAgICAgICByY3RObzogbnVsbCwNCiAgICAgICAgICByZW1hcms6IG51bGwsDQogICAgICAgICAgc2FsZXNDb25maXJtZWRUaW1lOiBudWxsLA0KICAgICAgICAgIHNlcnZpY2VCZWxvbmdUbzogbnVsbCwNCiAgICAgICAgICBzZXJ2aWNlSWQ6IG51bGwsDQogICAgICAgICAgc2VydmljZVR5cGVJZDogbnVsbCwNCiAgICAgICAgICBzdXBwbGllckNvbmZpcm1lZFRpbWU6IG51bGwsDQogICAgICAgICAgc3VwcGxpZXJDb250YWN0OiBudWxsLA0KICAgICAgICAgIHN1cHBsaWVySWQ6IG51bGwsDQogICAgICAgICAgc3VwcGxpZXJOYW1lOiBudWxsLA0KICAgICAgICAgIHN1cHBsaWVyU3VtbWFyeTogbnVsbCwNCiAgICAgICAgICBzdXBwbGllclRlbDogbnVsbCwNCiAgICAgICAgICB1cGRhdGVCeTogbnVsbCwNCiAgICAgICAgICB1cGRhdGVCeU5hbWU6IG51bGwsDQogICAgICAgICAgdXBkYXRlVGltZTogbnVsbA0KICAgICAgICB9LA0KICAgICAgICByc0NoYXJnZUxpc3Q6IFtdLA0KICAgICAgICByc09wTG9nTGlzdDogW10sDQogICAgICAgIHJzRG9jTGlzdDogW10NCiAgICAgIH0sDQogICAgICByc09wU2VhTGNsOiB7DQogICAgICAgIHJzU2VydmljZUluc3RhbmNlczogew0KICAgICAgICAgIGFjY291bnRDb25maXJtVGltZTogbnVsbCwNCiAgICAgICAgICBhZ3JlZW1lbnRObzogIiIsDQogICAgICAgICAgYWdyZWVtZW50VHlwZUNvZGU6IG51bGwsDQogICAgICAgICAgY2xpZW50Q29uZmlybWVkVGltZTogbnVsbCwNCiAgICAgICAgICBjb25maXJtQWNjb3VudElkOiBudWxsLA0KICAgICAgICAgIGNyZWF0ZUJ5OiBudWxsLA0KICAgICAgICAgIGNyZWF0ZUJ5TmFtZTogbnVsbCwNCiAgICAgICAgICBjcmVhdGVUaW1lOiBudWxsLA0KICAgICAgICAgIGRlbGV0ZUJ5OiBudWxsLA0KICAgICAgICAgIGRlbGV0ZUJ5TmFtZTogbnVsbCwNCiAgICAgICAgICBkZWxldGVTdGF0dXM6IG51bGwsDQogICAgICAgICAgZGVsZXRlVGltZTogbnVsbCwNCiAgICAgICAgICBpbnF1aXJ5SW5uZXJSZW1hcms6ICIiLA0KICAgICAgICAgIGlucXVpcnlMZWF0ZXN0VXBkYXRlZFRpbWU6IG51bGwsDQogICAgICAgICAgaW5xdWlyeU5vOiBudWxsLA0KICAgICAgICAgIGlucXVpcnlOb3RpY2U6IG51bGwsDQogICAgICAgICAgaW5xdWlyeVBzYUlkOiBudWxsLA0KICAgICAgICAgIGlzQWNjb3VudENvbmZpcm1lZDogbnVsbCwNCiAgICAgICAgICBpc0RuQ2xpZW50Q29uZmlybWVkOiBudWxsLA0KICAgICAgICAgIGlzRG5PcENvbmZpcm1lZDogbnVsbCwNCiAgICAgICAgICBpc0RuUHNhQ29uZmlybWVkOiBudWxsLA0KICAgICAgICAgIGlzRG5TYWxlc0NvbmZpcm1lZDogbnVsbCwNCiAgICAgICAgICBpc0RuU3VwcGxpZXJDb25maXJtZWQ6IG51bGwsDQogICAgICAgICAgbG9naXN0aWNzUGF5bWVudFRlcm1zQ29kZTogbnVsbCwNCiAgICAgICAgICBtYXhXZWlnaHQ6IG51bGwsDQogICAgICAgICAgb3BDb25maXJtZWRUaW1lOiBudWxsLA0KICAgICAgICAgIHBheW1lbnRUaXRsZUNvZGU6IG51bGwsDQogICAgICAgICAgcGVybWlzc2lvbkxldmVsOiBudWxsLA0KICAgICAgICAgIHBzYUNvbmZpcm1lZFRpbWU6IG51bGwsDQogICAgICAgICAgcmN0SWQ6IG51bGwsDQogICAgICAgICAgcmN0Tm86IG51bGwsDQogICAgICAgICAgcmVtYXJrOiBudWxsLA0KICAgICAgICAgIHNhbGVzQ29uZmlybWVkVGltZTogbnVsbCwNCiAgICAgICAgICBzZXJ2aWNlQmVsb25nVG86IG51bGwsDQogICAgICAgICAgc2VydmljZUlkOiBudWxsLA0KICAgICAgICAgIHNlcnZpY2VUeXBlSWQ6IG51bGwsDQogICAgICAgICAgc3VwcGxpZXJDb25maXJtZWRUaW1lOiBudWxsLA0KICAgICAgICAgIHN1cHBsaWVyQ29udGFjdDogbnVsbCwNCiAgICAgICAgICBzdXBwbGllcklkOiBudWxsLA0KICAgICAgICAgIHN1cHBsaWVyTmFtZTogbnVsbCwNCiAgICAgICAgICBzdXBwbGllclN1bW1hcnk6IG51bGwsDQogICAgICAgICAgc3VwcGxpZXJUZWw6IG51bGwsDQogICAgICAgICAgdXBkYXRlQnk6IG51bGwsDQogICAgICAgICAgdXBkYXRlQnlOYW1lOiBudWxsLA0KICAgICAgICAgIHVwZGF0ZVRpbWU6IG51bGwNCiAgICAgICAgfSwNCiAgICAgICAgcnNDaGFyZ2VMaXN0OiBbXSwNCiAgICAgICAgcnNPcExvZ0xpc3Q6IFtdLA0KICAgICAgICByc0RvY0xpc3Q6IFtdDQogICAgICB9LA0KICAgICAgcnNPcEFpcjogew0KICAgICAgICByc1NlcnZpY2VJbnN0YW5jZXM6IHsNCiAgICAgICAgICBhY2NvdW50Q29uZmlybVRpbWU6IG51bGwsDQogICAgICAgICAgYWdyZWVtZW50Tm86ICIiLA0KICAgICAgICAgIGFncmVlbWVudFR5cGVDb2RlOiBudWxsLA0KICAgICAgICAgIGNsaWVudENvbmZpcm1lZFRpbWU6IG51bGwsDQogICAgICAgICAgY29uZmlybUFjY291bnRJZDogbnVsbCwNCiAgICAgICAgICBjcmVhdGVCeTogbnVsbCwNCiAgICAgICAgICBjcmVhdGVCeU5hbWU6IG51bGwsDQogICAgICAgICAgY3JlYXRlVGltZTogbnVsbCwNCiAgICAgICAgICBkZWxldGVCeTogbnVsbCwNCiAgICAgICAgICBkZWxldGVCeU5hbWU6IG51bGwsDQogICAgICAgICAgZGVsZXRlU3RhdHVzOiBudWxsLA0KICAgICAgICAgIGRlbGV0ZVRpbWU6IG51bGwsDQogICAgICAgICAgaW5xdWlyeUlubmVyUmVtYXJrOiAiIiwNCiAgICAgICAgICBpbnF1aXJ5TGVhdGVzdFVwZGF0ZWRUaW1lOiBudWxsLA0KICAgICAgICAgIGlucXVpcnlObzogbnVsbCwNCiAgICAgICAgICBpbnF1aXJ5Tm90aWNlOiBudWxsLA0KICAgICAgICAgIGlucXVpcnlQc2FJZDogbnVsbCwNCiAgICAgICAgICBpc0FjY291bnRDb25maXJtZWQ6IG51bGwsDQogICAgICAgICAgaXNEbkNsaWVudENvbmZpcm1lZDogbnVsbCwNCiAgICAgICAgICBpc0RuT3BDb25maXJtZWQ6IG51bGwsDQogICAgICAgICAgaXNEblBzYUNvbmZpcm1lZDogbnVsbCwNCiAgICAgICAgICBpc0RuU2FsZXNDb25maXJtZWQ6IG51bGwsDQogICAgICAgICAgaXNEblN1cHBsaWVyQ29uZmlybWVkOiBudWxsLA0KICAgICAgICAgIGxvZ2lzdGljc1BheW1lbnRUZXJtc0NvZGU6IG51bGwsDQogICAgICAgICAgbWF4V2VpZ2h0OiBudWxsLA0KICAgICAgICAgIG9wQ29uZmlybWVkVGltZTogbnVsbCwNCiAgICAgICAgICBwYXltZW50VGl0bGVDb2RlOiBudWxsLA0KICAgICAgICAgIHBlcm1pc3Npb25MZXZlbDogbnVsbCwNCiAgICAgICAgICBwc2FDb25maXJtZWRUaW1lOiBudWxsLA0KICAgICAgICAgIHJjdElkOiBudWxsLA0KICAgICAgICAgIHJjdE5vOiBudWxsLA0KICAgICAgICAgIHJlbWFyazogbnVsbCwNCiAgICAgICAgICBzYWxlc0NvbmZpcm1lZFRpbWU6IG51bGwsDQogICAgICAgICAgc2VydmljZUJlbG9uZ1RvOiBudWxsLA0KICAgICAgICAgIHNlcnZpY2VJZDogbnVsbCwNCiAgICAgICAgICBzZXJ2aWNlVHlwZUlkOiBudWxsLA0KICAgICAgICAgIHN1cHBsaWVyQ29uZmlybWVkVGltZTogbnVsbCwNCiAgICAgICAgICBzdXBwbGllckNvbnRhY3Q6IG51bGwsDQogICAgICAgICAgc3VwcGxpZXJJZDogbnVsbCwNCiAgICAgICAgICBzdXBwbGllck5hbWU6IG51bGwsDQogICAgICAgICAgc3VwcGxpZXJTdW1tYXJ5OiBudWxsLA0KICAgICAgICAgIHN1cHBsaWVyVGVsOiBudWxsLA0KICAgICAgICAgIHVwZGF0ZUJ5OiBudWxsLA0KICAgICAgICAgIHVwZGF0ZUJ5TmFtZTogbnVsbCwNCiAgICAgICAgICB1cGRhdGVUaW1lOiBudWxsDQogICAgICAgIH0sDQogICAgICAgIHJzQ2hhcmdlTGlzdDogW10sDQogICAgICAgIHJzT3BMb2dMaXN0OiBbXSwNCiAgICAgICAgcnNEb2NMaXN0OiBbXQ0KICAgICAgfSwNCiAgICAgIHJzT3BSYWlsRkNMOiB7DQogICAgICAgIHJzQ2hhcmdlTGlzdDogW10sDQogICAgICAgIHJzT3BMb2dMaXN0OiBbXSwNCiAgICAgICAgcnNEb2NMaXN0OiBbXQ0KICAgICAgfSwNCiAgICAgIHJzT3BSYWlsTENMOiB7DQogICAgICAgIHJzQ2hhcmdlTGlzdDogW10sDQogICAgICAgIHJzT3BMb2dMaXN0OiBbXSwNCiAgICAgICAgcnNEb2NMaXN0OiBbXQ0KICAgICAgfSwNCiAgICAgIHJzT3BSYWlsOiB7DQogICAgICAgIHJzQ2hhcmdlTGlzdDogW10sDQogICAgICAgIHJzT3BMb2dMaXN0OiBbXSwNCiAgICAgICAgcnNEb2NMaXN0OiBbXQ0KICAgICAgfSwNCiAgICAgIHJzT3BFeHByZXNzOiB7DQogICAgICAgIHJzQ2hhcmdlTGlzdDogW10sDQogICAgICAgIHJzT3BMb2dMaXN0OiBbXSwNCiAgICAgICAgcnNEb2NMaXN0OiBbXQ0KICAgICAgfSwNCiAgICAgIHJzT3BUcnVjazogew0KICAgICAgICByc0NoYXJnZUxpc3Q6IFtdLA0KICAgICAgICByc09wTG9nTGlzdDogW10sDQogICAgICAgIHJzRG9jTGlzdDogW10NCiAgICAgIH0sDQogICAgICByc09wQ3RuclRydWNrOiB7DQogICAgICAgIHJzU2VydmljZUluc3RhbmNlczogew0KICAgICAgICAgIGFjY291bnRDb25maXJtVGltZTogbnVsbCwNCiAgICAgICAgICBhZ3JlZW1lbnRObzogIiIsDQogICAgICAgICAgYWdyZWVtZW50VHlwZUNvZGU6IG51bGwsDQogICAgICAgICAgY2xpZW50Q29uZmlybWVkVGltZTogbnVsbCwNCiAgICAgICAgICBjb25maXJtQWNjb3VudElkOiBudWxsLA0KICAgICAgICAgIGNyZWF0ZUJ5OiBudWxsLA0KICAgICAgICAgIGNyZWF0ZUJ5TmFtZTogbnVsbCwNCiAgICAgICAgICBjcmVhdGVUaW1lOiBudWxsLA0KICAgICAgICAgIGRlbGV0ZUJ5OiBudWxsLA0KICAgICAgICAgIGRlbGV0ZUJ5TmFtZTogbnVsbCwNCiAgICAgICAgICBkZWxldGVTdGF0dXM6IG51bGwsDQogICAgICAgICAgZGVsZXRlVGltZTogbnVsbCwNCiAgICAgICAgICBpbnF1aXJ5SW5uZXJSZW1hcms6ICIiLA0KICAgICAgICAgIGlucXVpcnlMZWF0ZXN0VXBkYXRlZFRpbWU6IG51bGwsDQogICAgICAgICAgaW5xdWlyeU5vOiBudWxsLA0KICAgICAgICAgIGlucXVpcnlOb3RpY2U6IG51bGwsDQogICAgICAgICAgaW5xdWlyeVBzYUlkOiBudWxsLA0KICAgICAgICAgIGlzQWNjb3VudENvbmZpcm1lZDogbnVsbCwNCiAgICAgICAgICBpc0RuQ2xpZW50Q29uZmlybWVkOiBudWxsLA0KICAgICAgICAgIGlzRG5PcENvbmZpcm1lZDogbnVsbCwNCiAgICAgICAgICBpc0RuUHNhQ29uZmlybWVkOiBudWxsLA0KICAgICAgICAgIGlzRG5TYWxlc0NvbmZpcm1lZDogbnVsbCwNCiAgICAgICAgICBpc0RuU3VwcGxpZXJDb25maXJtZWQ6IG51bGwsDQogICAgICAgICAgbG9naXN0aWNzUGF5bWVudFRlcm1zQ29kZTogbnVsbCwNCiAgICAgICAgICBtYXhXZWlnaHQ6IG51bGwsDQogICAgICAgICAgb3BDb25maXJtZWRUaW1lOiBudWxsLA0KICAgICAgICAgIHBheW1lbnRUaXRsZUNvZGU6IG51bGwsDQogICAgICAgICAgcGVybWlzc2lvbkxldmVsOiBudWxsLA0KICAgICAgICAgIHBzYUNvbmZpcm1lZFRpbWU6IG51bGwsDQogICAgICAgICAgcmN0SWQ6IG51bGwsDQogICAgICAgICAgcmN0Tm86IG51bGwsDQogICAgICAgICAgcmVtYXJrOiBudWxsLA0KICAgICAgICAgIHNhbGVzQ29uZmlybWVkVGltZTogbnVsbCwNCiAgICAgICAgICBzZXJ2aWNlQmVsb25nVG86IG51bGwsDQogICAgICAgICAgc2VydmljZUlkOiBudWxsLA0KICAgICAgICAgIHNlcnZpY2VUeXBlSWQ6IG51bGwsDQogICAgICAgICAgc3VwcGxpZXJDb25maXJtZWRUaW1lOiBudWxsLA0KICAgICAgICAgIHN1cHBsaWVyQ29udGFjdDogbnVsbCwNCiAgICAgICAgICBzdXBwbGllcklkOiBudWxsLA0KICAgICAgICAgIHN1cHBsaWVyTmFtZTogbnVsbCwNCiAgICAgICAgICBzdXBwbGllclN1bW1hcnk6IG51bGwsDQogICAgICAgICAgc3VwcGxpZXJUZWw6IG51bGwsDQogICAgICAgICAgdXBkYXRlQnk6IG51bGwsDQogICAgICAgICAgdXBkYXRlQnlOYW1lOiBudWxsLA0KICAgICAgICAgIHVwZGF0ZVRpbWU6IG51bGwNCiAgICAgICAgfSwNCiAgICAgICAgcnNDaGFyZ2VMaXN0OiBbXSwNCiAgICAgICAgcnNPcExvZ0xpc3Q6IFtdLA0KICAgICAgICByc0RvY0xpc3Q6IFtdLA0KICAgICAgICByc09wVHJ1Y2tMaXN0OiBbXQ0KICAgICAgfSwNCiAgICAgIHJzT3BCdWxrVHJ1Y2s6IHsNCiAgICAgICAgcnNTZXJ2aWNlSW5zdGFuY2VzOiB7DQogICAgICAgICAgYWNjb3VudENvbmZpcm1UaW1lOiBudWxsLA0KICAgICAgICAgIGFncmVlbWVudE5vOiAiIiwNCiAgICAgICAgICBhZ3JlZW1lbnRUeXBlQ29kZTogbnVsbCwNCiAgICAgICAgICBjbGllbnRDb25maXJtZWRUaW1lOiBudWxsLA0KICAgICAgICAgIGNvbmZpcm1BY2NvdW50SWQ6IG51bGwsDQogICAgICAgICAgY3JlYXRlQnk6IG51bGwsDQogICAgICAgICAgY3JlYXRlQnlOYW1lOiBudWxsLA0KICAgICAgICAgIGNyZWF0ZVRpbWU6IG51bGwsDQogICAgICAgICAgZGVsZXRlQnk6IG51bGwsDQogICAgICAgICAgZGVsZXRlQnlOYW1lOiBudWxsLA0KICAgICAgICAgIGRlbGV0ZVN0YXR1czogbnVsbCwNCiAgICAgICAgICBkZWxldGVUaW1lOiBudWxsLA0KICAgICAgICAgIGlucXVpcnlJbm5lclJlbWFyazogIiIsDQogICAgICAgICAgaW5xdWlyeUxlYXRlc3RVcGRhdGVkVGltZTogbnVsbCwNCiAgICAgICAgICBpbnF1aXJ5Tm86IG51bGwsDQogICAgICAgICAgaW5xdWlyeU5vdGljZTogbnVsbCwNCiAgICAgICAgICBpbnF1aXJ5UHNhSWQ6IG51bGwsDQogICAgICAgICAgaXNBY2NvdW50Q29uZmlybWVkOiBudWxsLA0KICAgICAgICAgIGlzRG5DbGllbnRDb25maXJtZWQ6IG51bGwsDQogICAgICAgICAgaXNEbk9wQ29uZmlybWVkOiBudWxsLA0KICAgICAgICAgIGlzRG5Qc2FDb25maXJtZWQ6IG51bGwsDQogICAgICAgICAgaXNEblNhbGVzQ29uZmlybWVkOiBudWxsLA0KICAgICAgICAgIGlzRG5TdXBwbGllckNvbmZpcm1lZDogbnVsbCwNCiAgICAgICAgICBsb2dpc3RpY3NQYXltZW50VGVybXNDb2RlOiBudWxsLA0KICAgICAgICAgIG1heFdlaWdodDogbnVsbCwNCiAgICAgICAgICBvcENvbmZpcm1lZFRpbWU6IG51bGwsDQogICAgICAgICAgcGF5bWVudFRpdGxlQ29kZTogbnVsbCwNCiAgICAgICAgICBwZXJtaXNzaW9uTGV2ZWw6IG51bGwsDQogICAgICAgICAgcHNhQ29uZmlybWVkVGltZTogbnVsbCwNCiAgICAgICAgICByY3RJZDogbnVsbCwNCiAgICAgICAgICByY3RObzogbnVsbCwNCiAgICAgICAgICByZW1hcms6IG51bGwsDQogICAgICAgICAgc2FsZXNDb25maXJtZWRUaW1lOiBudWxsLA0KICAgICAgICAgIHNlcnZpY2VCZWxvbmdUbzogbnVsbCwNCiAgICAgICAgICBzZXJ2aWNlSWQ6IG51bGwsDQogICAgICAgICAgc2VydmljZVR5cGVJZDogbnVsbCwNCiAgICAgICAgICBzdXBwbGllckNvbmZpcm1lZFRpbWU6IG51bGwsDQogICAgICAgICAgc3VwcGxpZXJDb250YWN0OiBudWxsLA0KICAgICAgICAgIHN1cHBsaWVySWQ6IG51bGwsDQogICAgICAgICAgc3VwcGxpZXJOYW1lOiBudWxsLA0KICAgICAgICAgIHN1cHBsaWVyU3VtbWFyeTogbnVsbCwNCiAgICAgICAgICBzdXBwbGllclRlbDogbnVsbCwNCiAgICAgICAgICB1cGRhdGVCeTogbnVsbCwNCiAgICAgICAgICB1cGRhdGVCeU5hbWU6IG51bGwsDQogICAgICAgICAgdXBkYXRlVGltZTogbnVsbA0KICAgICAgICB9LA0KICAgICAgICByc0NoYXJnZUxpc3Q6IFtdLA0KICAgICAgICByc09wTG9nTGlzdDogW10sDQogICAgICAgIHJzRG9jTGlzdDogW10sDQogICAgICAgIHJzT3BUcnVja0xpc3Q6IFtdDQogICAgICB9LA0KICAgICAgLy/mraPljZXmiqXlhbMNCiAgICAgIHJzT3BEb2NEZWNsYXJlOiB7DQogICAgICAgIHJzU2VydmljZUluc3RhbmNlczogew0KICAgICAgICAgIGFjY291bnRDb25maXJtVGltZTogbnVsbCwNCiAgICAgICAgICBhZ3JlZW1lbnRObzogIiIsDQogICAgICAgICAgYWdyZWVtZW50VHlwZUNvZGU6IG51bGwsDQogICAgICAgICAgY2xpZW50Q29uZmlybWVkVGltZTogbnVsbCwNCiAgICAgICAgICBjb25maXJtQWNjb3VudElkOiBudWxsLA0KICAgICAgICAgIGNyZWF0ZUJ5OiBudWxsLA0KICAgICAgICAgIGNyZWF0ZUJ5TmFtZTogbnVsbCwNCiAgICAgICAgICBjcmVhdGVUaW1lOiBudWxsLA0KICAgICAgICAgIGRlbGV0ZUJ5OiBudWxsLA0KICAgICAgICAgIGRlbGV0ZUJ5TmFtZTogbnVsbCwNCiAgICAgICAgICBkZWxldGVTdGF0dXM6IG51bGwsDQogICAgICAgICAgZGVsZXRlVGltZTogbnVsbCwNCiAgICAgICAgICBpbnF1aXJ5SW5uZXJSZW1hcms6ICIiLA0KICAgICAgICAgIGlucXVpcnlMZWF0ZXN0VXBkYXRlZFRpbWU6IG51bGwsDQogICAgICAgICAgaW5xdWlyeU5vOiBudWxsLA0KICAgICAgICAgIGlucXVpcnlOb3RpY2U6IG51bGwsDQogICAgICAgICAgaW5xdWlyeVBzYUlkOiBudWxsLA0KICAgICAgICAgIGlzQWNjb3VudENvbmZpcm1lZDogbnVsbCwNCiAgICAgICAgICBpc0RuQ2xpZW50Q29uZmlybWVkOiBudWxsLA0KICAgICAgICAgIGlzRG5PcENvbmZpcm1lZDogbnVsbCwNCiAgICAgICAgICBpc0RuUHNhQ29uZmlybWVkOiBudWxsLA0KICAgICAgICAgIGlzRG5TYWxlc0NvbmZpcm1lZDogbnVsbCwNCiAgICAgICAgICBpc0RuU3VwcGxpZXJDb25maXJtZWQ6IG51bGwsDQogICAgICAgICAgbG9naXN0aWNzUGF5bWVudFRlcm1zQ29kZTogbnVsbCwNCiAgICAgICAgICBtYXhXZWlnaHQ6IG51bGwsDQogICAgICAgICAgb3BDb25maXJtZWRUaW1lOiBudWxsLA0KICAgICAgICAgIHBheW1lbnRUaXRsZUNvZGU6IG51bGwsDQogICAgICAgICAgcGVybWlzc2lvbkxldmVsOiBudWxsLA0KICAgICAgICAgIHBzYUNvbmZpcm1lZFRpbWU6IG51bGwsDQogICAgICAgICAgcmN0SWQ6IG51bGwsDQogICAgICAgICAgcmN0Tm86IG51bGwsDQogICAgICAgICAgcmVtYXJrOiBudWxsLA0KICAgICAgICAgIHNhbGVzQ29uZmlybWVkVGltZTogbnVsbCwNCiAgICAgICAgICBzZXJ2aWNlQmVsb25nVG86IG51bGwsDQogICAgICAgICAgc2VydmljZUlkOiBudWxsLA0KICAgICAgICAgIHNlcnZpY2VUeXBlSWQ6IG51bGwsDQogICAgICAgICAgc3VwcGxpZXJDb25maXJtZWRUaW1lOiBudWxsLA0KICAgICAgICAgIHN1cHBsaWVyQ29udGFjdDogbnVsbCwNCiAgICAgICAgICBzdXBwbGllcklkOiBudWxsLA0KICAgICAgICAgIHN1cHBsaWVyTmFtZTogbnVsbCwNCiAgICAgICAgICBzdXBwbGllclN1bW1hcnk6IG51bGwsDQogICAgICAgICAgc3VwcGxpZXJUZWw6IG51bGwsDQogICAgICAgICAgdXBkYXRlQnk6IG51bGwsDQogICAgICAgICAgdXBkYXRlQnlOYW1lOiBudWxsLA0KICAgICAgICAgIHVwZGF0ZVRpbWU6IG51bGwNCiAgICAgICAgfSwNCiAgICAgICAgcnNDaGFyZ2VMaXN0OiBbXSwNCiAgICAgICAgcnNPcExvZ0xpc3Q6IFtdLA0KICAgICAgICByc0RvY0xpc3Q6IFtdDQogICAgICB9LA0KICAgICAgLy8g5YWo5YyF5oql5YWzDQogICAgICByc09wRnJlZURlY2xhcmU6IHsNCiAgICAgICAgcnNTZXJ2aWNlSW5zdGFuY2VzOiB7DQogICAgICAgICAgYWNjb3VudENvbmZpcm1UaW1lOiBudWxsLA0KICAgICAgICAgIGFncmVlbWVudE5vOiAiIiwNCiAgICAgICAgICBhZ3JlZW1lbnRUeXBlQ29kZTogbnVsbCwNCiAgICAgICAgICBjbGllbnRDb25maXJtZWRUaW1lOiBudWxsLA0KICAgICAgICAgIGNvbmZpcm1BY2NvdW50SWQ6IG51bGwsDQogICAgICAgICAgY3JlYXRlQnk6IG51bGwsDQogICAgICAgICAgY3JlYXRlQnlOYW1lOiBudWxsLA0KICAgICAgICAgIGNyZWF0ZVRpbWU6IG51bGwsDQogICAgICAgICAgZGVsZXRlQnk6IG51bGwsDQogICAgICAgICAgZGVsZXRlQnlOYW1lOiBudWxsLA0KICAgICAgICAgIGRlbGV0ZVN0YXR1czogbnVsbCwNCiAgICAgICAgICBkZWxldGVUaW1lOiBudWxsLA0KICAgICAgICAgIGlucXVpcnlJbm5lclJlbWFyazogIiIsDQogICAgICAgICAgaW5xdWlyeUxlYXRlc3RVcGRhdGVkVGltZTogbnVsbCwNCiAgICAgICAgICBpbnF1aXJ5Tm86IG51bGwsDQogICAgICAgICAgaW5xdWlyeU5vdGljZTogbnVsbCwNCiAgICAgICAgICBpbnF1aXJ5UHNhSWQ6IG51bGwsDQogICAgICAgICAgaXNBY2NvdW50Q29uZmlybWVkOiBudWxsLA0KICAgICAgICAgIGlzRG5DbGllbnRDb25maXJtZWQ6IG51bGwsDQogICAgICAgICAgaXNEbk9wQ29uZmlybWVkOiBudWxsLA0KICAgICAgICAgIGlzRG5Qc2FDb25maXJtZWQ6IG51bGwsDQogICAgICAgICAgaXNEblNhbGVzQ29uZmlybWVkOiBudWxsLA0KICAgICAgICAgIGlzRG5TdXBwbGllckNvbmZpcm1lZDogbnVsbCwNCiAgICAgICAgICBsb2dpc3RpY3NQYXltZW50VGVybXNDb2RlOiBudWxsLA0KICAgICAgICAgIG1heFdlaWdodDogbnVsbCwNCiAgICAgICAgICBvcENvbmZpcm1lZFRpbWU6IG51bGwsDQogICAgICAgICAgcGF5bWVudFRpdGxlQ29kZTogbnVsbCwNCiAgICAgICAgICBwZXJtaXNzaW9uTGV2ZWw6IG51bGwsDQogICAgICAgICAgcHNhQ29uZmlybWVkVGltZTogbnVsbCwNCiAgICAgICAgICByY3RJZDogbnVsbCwNCiAgICAgICAgICByY3RObzogbnVsbCwNCiAgICAgICAgICByZW1hcms6IG51bGwsDQogICAgICAgICAgc2FsZXNDb25maXJtZWRUaW1lOiBudWxsLA0KICAgICAgICAgIHNlcnZpY2VCZWxvbmdUbzogbnVsbCwNCiAgICAgICAgICBzZXJ2aWNlSWQ6IG51bGwsDQogICAgICAgICAgc2VydmljZVR5cGVJZDogbnVsbCwNCiAgICAgICAgICBzdXBwbGllckNvbmZpcm1lZFRpbWU6IG51bGwsDQogICAgICAgICAgc3VwcGxpZXJDb250YWN0OiBudWxsLA0KICAgICAgICAgIHN1cHBsaWVySWQ6IG51bGwsDQogICAgICAgICAgc3VwcGxpZXJOYW1lOiBudWxsLA0KICAgICAgICAgIHN1cHBsaWVyU3VtbWFyeTogbnVsbCwNCiAgICAgICAgICBzdXBwbGllclRlbDogbnVsbCwNCiAgICAgICAgICB1cGRhdGVCeTogbnVsbCwNCiAgICAgICAgICB1cGRhdGVCeU5hbWU6IG51bGwsDQogICAgICAgICAgdXBkYXRlVGltZTogbnVsbA0KICAgICAgICB9LA0KICAgICAgICByc0NoYXJnZUxpc3Q6IFtdLA0KICAgICAgICByc09wTG9nTGlzdDogW10sDQogICAgICAgIHJzRG9jTGlzdDogW10NCiAgICAgIH0sDQogICAgICByc09wSW1wb3J0RGlzcGF0Y2hUcnVjazogew0KICAgICAgICByc0NoYXJnZUxpc3Q6IFtdLA0KICAgICAgICByc09wTG9nTGlzdDogW10sDQogICAgICAgIHJzRG9jTGlzdDogW10NCiAgICAgIH0sDQogICAgICAvLyDku6PnkIbmlL7ljZUNCiAgICAgIHJzT3BET0FnZW50OiB7DQogICAgICAgIHJzQ2hhcmdlTGlzdDogW10sDQogICAgICAgIHJzT3BMb2dMaXN0OiBbXSwNCiAgICAgICAgcnNEb2NMaXN0OiBbXQ0KICAgICAgfSwNCiAgICAgIHJzT3BDbGVhckFnZW50OiB7DQogICAgICAgIHJzQ2hhcmdlTGlzdDogW10sDQogICAgICAgIHJzT3BMb2dMaXN0OiBbXSwNCiAgICAgICAgcnNEb2NMaXN0OiBbXQ0KICAgICAgfSwNCiAgICAgIHJzT3BXSFM6IHsNCiAgICAgICAgcnNDaGFyZ2VMaXN0OiBbXSwNCiAgICAgICAgcnNPcExvZ0xpc3Q6IFtdLA0KICAgICAgICByc0RvY0xpc3Q6IFtdDQogICAgICB9LA0KICAgICAgcnNPcFdhcmVob3VzZTogew0KICAgICAgICByc0NoYXJnZUxpc3Q6IFtdLA0KICAgICAgICByc09wTG9nTGlzdDogW10sDQogICAgICAgIHJzRG9jTGlzdDogW10NCiAgICAgIH0sDQogICAgICByc09wSW5zcGVjdGlvbkFuZENlcnRpZmljYXRlOiB7DQogICAgICAgIHJzQ2hhcmdlTGlzdDogW10sDQogICAgICAgIHJzT3BMb2dMaXN0OiBbXSwNCiAgICAgICAgcnNEb2NMaXN0OiBbXQ0KICAgICAgfSwNCiAgICAgIHJzT3BJbnN1cmFuY2U6IHsNCiAgICAgICAgcnNDaGFyZ2VMaXN0OiBbXSwNCiAgICAgICAgcnNPcExvZ0xpc3Q6IFtdLA0KICAgICAgICByc0RvY0xpc3Q6IFtdDQogICAgICB9LA0KICAgICAgcnNPcEV4cGFuZFNlcnZpY2U6IHsNCiAgICAgICAgcnNDaGFyZ2VMaXN0OiBbXSwNCiAgICAgICAgcnNPcExvZ0xpc3Q6IFtdLA0KICAgICAgICByc0RvY0xpc3Q6IFtdDQogICAgICB9LA0KICAgICAgcnNPcDNyZENlcnQ6IHsNCiAgICAgICAgcnNDaGFyZ2VMaXN0OiBbXSwNCiAgICAgICAgcnNPcExvZ0xpc3Q6IFtdLA0KICAgICAgICByc0RvY0xpc3Q6IFtdDQogICAgICB9LA0KICAgICAgcnNPcElOUzogew0KICAgICAgICByc0NoYXJnZUxpc3Q6IFtdLA0KICAgICAgICByc09wTG9nTGlzdDogW10sDQogICAgICAgIHJzRG9jTGlzdDogW10NCiAgICAgIH0sDQogICAgICByc09wVHJhZGluZzogew0KICAgICAgICByc0NoYXJnZUxpc3Q6IFtdLA0KICAgICAgICByc09wTG9nTGlzdDogW10sDQogICAgICAgIHJzRG9jTGlzdDogW10NCiAgICAgIH0sDQogICAgICByc09wRnVtaWdhdGlvbjogew0KICAgICAgICByc0NoYXJnZUxpc3Q6IFtdLA0KICAgICAgICByc09wTG9nTGlzdDogW10sDQogICAgICAgIHJzRG9jTGlzdDogW10NCiAgICAgIH0sDQogICAgICByc09wQ086IHsNCiAgICAgICAgcnNDaGFyZ2VMaXN0OiBbXSwNCiAgICAgICAgcnNPcExvZ0xpc3Q6IFtdLA0KICAgICAgICByc0RvY0xpc3Q6IFtdDQogICAgICB9LA0KICAgICAgcnNPcE90aGVyOiB7DQogICAgICAgIHJzQ2hhcmdlTGlzdDogW10sDQogICAgICAgIHJzT3BMb2dMaXN0OiBbXSwNCiAgICAgICAgcnNEb2NMaXN0OiBbXQ0KICAgICAgfSwNCiAgICAgIC8vIG5ldyDmnI3liqHlrp7kvosNCiAgICAgIHJzQ2xpZW50U2VydmljZUluc3RhbmNlOiB7DQogICAgICAgIHNlcnZpY2VJZDogbnVsbCwNCiAgICAgICAgc2VydmljZVR5cGVJZDogbnVsbCwNCiAgICAgICAgcmN0SWQ6IG51bGwsDQogICAgICAgIHJjdE5vOiBudWxsLA0KICAgICAgICBzdXBwbGllcklkOiBudWxsLA0KICAgICAgICBzdXBwbGllclN1bW1hcnk6IG51bGwsDQogICAgICAgIHN1cHBsaWVyQ29udGFjdDogbnVsbCwNCiAgICAgICAgc3VwcGxpZXJUZWw6IG51bGwsDQogICAgICAgIHBheW1lbnRUaXRsZUNvZGU6IG51bGwsDQogICAgICAgIGxvZ2lzdGljc1BheW1lbnRUZXJtc0NvZGU6IG51bGwsDQogICAgICAgIGlucXVpcnlObzogbnVsbCwNCiAgICAgICAgYWdyZWVtZW50VHlwZUNvZGU6IG51bGwsDQogICAgICAgIGFncmVlbWVudE5vOiBudWxsLA0KICAgICAgICBtYXhXZWlnaHQ6IG51bGwsDQogICAgICAgIGlucXVpcnlOb3RpY2U6IG51bGwsDQogICAgICAgIGlucXVpcnlJbm5lclJlbWFyazogbnVsbCwNCiAgICAgICAgaW5xdWlyeVBzYUlkOiBudWxsLA0KICAgICAgICBpbnF1aXJ5TGVhdGVzdFVwZGF0ZWRUaW1lOiBudWxsLA0KICAgICAgICBzZXJ2aWNlQmVsb25nVG86IG51bGwsDQogICAgICAgIGlzRG5TYWxlc0NvbmZpcm1lZDogbnVsbCwNCiAgICAgICAgaXNEbkNsaWVudENvbmZpcm1lZDogbnVsbCwNCiAgICAgICAgaXNEbk9wQ29uZmlybWVkOiBudWxsLA0KICAgICAgICBpc0RuUHNhQ29uZmlybWVkOiBudWxsLA0KICAgICAgICBpc0RuU3VwcGxpZXJDb25maXJtZWQ6IG51bGwsDQogICAgICAgIGlzQWNjb3VudENvbmZpcm1lZDogbnVsbCwNCiAgICAgICAgY29uZmlybUFjY291bnRJZDogbnVsbCwNCiAgICAgICAgYWNjb3VudENvbmZpcm1UaW1lOiBudWxsLA0KICAgICAgICBzYWxlc0NvbmZpcm1lZFRpbWU6IG51bGwsDQogICAgICAgIGNsaWVudENvbmZpcm1lZFRpbWU6IG51bGwsDQogICAgICAgIG9wQ29uZmlybWVkVGltZTogbnVsbCwNCiAgICAgICAgcHNhQ29uZmlybWVkVGltZTogbnVsbCwNCiAgICAgICAgc3VwcGxpZXJDb25maXJtZWRUaW1lOiBudWxsDQogICAgICB9LA0KICAgICAgcnNPcFNlYUZjbFNlcnZpY2VJbnN0YW5jZTogew0KICAgICAgICBhY2NvdW50Q29uZmlybVRpbWU6IG51bGwsDQogICAgICAgIGFncmVlbWVudE5vOiAiIiwNCiAgICAgICAgYWdyZWVtZW50VHlwZUNvZGU6IG51bGwsDQogICAgICAgIGNsaWVudENvbmZpcm1lZFRpbWU6IG51bGwsDQogICAgICAgIGNvbmZpcm1BY2NvdW50SWQ6IG51bGwsDQogICAgICAgIGNyZWF0ZUJ5OiBudWxsLA0KICAgICAgICBjcmVhdGVCeU5hbWU6IG51bGwsDQogICAgICAgIGNyZWF0ZVRpbWU6IG51bGwsDQogICAgICAgIGRlbGV0ZUJ5OiBudWxsLA0KICAgICAgICBkZWxldGVCeU5hbWU6IG51bGwsDQogICAgICAgIGRlbGV0ZVN0YXR1czogbnVsbCwNCiAgICAgICAgZGVsZXRlVGltZTogbnVsbCwNCiAgICAgICAgaW5xdWlyeUlubmVyUmVtYXJrOiAiIiwNCiAgICAgICAgaW5xdWlyeUxlYXRlc3RVcGRhdGVkVGltZTogbnVsbCwNCiAgICAgICAgaW5xdWlyeU5vOiBudWxsLA0KICAgICAgICBpbnF1aXJ5Tm90aWNlOiBudWxsLA0KICAgICAgICBpbnF1aXJ5UHNhSWQ6IG51bGwsDQogICAgICAgIGlzQWNjb3VudENvbmZpcm1lZDogbnVsbCwNCiAgICAgICAgaXNEbkNsaWVudENvbmZpcm1lZDogbnVsbCwNCiAgICAgICAgaXNEbk9wQ29uZmlybWVkOiBudWxsLA0KICAgICAgICBpc0RuUHNhQ29uZmlybWVkOiBudWxsLA0KICAgICAgICBpc0RuU2FsZXNDb25maXJtZWQ6IG51bGwsDQogICAgICAgIGlzRG5TdXBwbGllckNvbmZpcm1lZDogbnVsbCwNCiAgICAgICAgbG9naXN0aWNzUGF5bWVudFRlcm1zQ29kZTogbnVsbCwNCiAgICAgICAgbWF4V2VpZ2h0OiBudWxsLA0KICAgICAgICBvcENvbmZpcm1lZFRpbWU6IG51bGwsDQogICAgICAgIHBheW1lbnRUaXRsZUNvZGU6IG51bGwsDQogICAgICAgIHBlcm1pc3Npb25MZXZlbDogbnVsbCwNCiAgICAgICAgcHNhQ29uZmlybWVkVGltZTogbnVsbCwNCiAgICAgICAgcmN0SWQ6IG51bGwsDQogICAgICAgIHJjdE5vOiBudWxsLA0KICAgICAgICByZW1hcms6IG51bGwsDQogICAgICAgIHNhbGVzQ29uZmlybWVkVGltZTogbnVsbCwNCiAgICAgICAgc2VydmljZUJlbG9uZ1RvOiBudWxsLA0KICAgICAgICBzZXJ2aWNlSWQ6IG51bGwsDQogICAgICAgIHNlcnZpY2VUeXBlSWQ6IG51bGwsDQogICAgICAgIHN1cHBsaWVyQ29uZmlybWVkVGltZTogbnVsbCwNCiAgICAgICAgc3VwcGxpZXJDb250YWN0OiBudWxsLA0KICAgICAgICBzdXBwbGllcklkOiBudWxsLA0KICAgICAgICBzdXBwbGllck5hbWU6IG51bGwsDQogICAgICAgIHN1cHBsaWVyU3VtbWFyeTogbnVsbCwNCiAgICAgICAgc3VwcGxpZXJUZWw6IG51bGwsDQogICAgICAgIHVwZGF0ZUJ5OiBudWxsLA0KICAgICAgICB1cGRhdGVCeU5hbWU6IG51bGwsDQogICAgICAgIHVwZGF0ZVRpbWU6IG51bGwNCiAgICAgIH0sDQogICAgICByc09wU2VhTGNsU2VydmljZUluc3RhbmNlOiB7DQogICAgICAgIHNlcnZpY2VJZDogbnVsbCwNCiAgICAgICAgc2VydmljZVR5cGVJZDogbnVsbCwNCiAgICAgICAgcmN0SWQ6IG51bGwsDQogICAgICAgIHJjdE5vOiBudWxsLA0KICAgICAgICBzdXBwbGllcklkOiBudWxsLA0KICAgICAgICBzdXBwbGllclN1bW1hcnk6IG51bGwsDQogICAgICAgIHN1cHBsaWVyQ29udGFjdDogbnVsbCwNCiAgICAgICAgc3VwcGxpZXJUZWw6IG51bGwsDQogICAgICAgIHBheW1lbnRUaXRsZUNvZGU6IG51bGwsDQogICAgICAgIGxvZ2lzdGljc1BheW1lbnRUZXJtc0NvZGU6IG51bGwsDQogICAgICAgIGlucXVpcnlObzogbnVsbCwNCiAgICAgICAgYWdyZWVtZW50VHlwZUNvZGU6IG51bGwsDQogICAgICAgIGFncmVlbWVudE5vOiBudWxsLA0KICAgICAgICBtYXhXZWlnaHQ6IG51bGwsDQogICAgICAgIGlucXVpcnlOb3RpY2U6IG51bGwsDQogICAgICAgIGlucXVpcnlJbm5lclJlbWFyazogbnVsbCwNCiAgICAgICAgaW5xdWlyeVBzYUlkOiBudWxsLA0KICAgICAgICBpbnF1aXJ5TGVhdGVzdFVwZGF0ZWRUaW1lOiBudWxsLA0KICAgICAgICBzZXJ2aWNlQmVsb25nVG86IG51bGwsDQogICAgICAgIGlzRG5TYWxlc0NvbmZpcm1lZDogbnVsbCwNCiAgICAgICAgaXNEbkNsaWVudENvbmZpcm1lZDogbnVsbCwNCiAgICAgICAgaXNEbk9wQ29uZmlybWVkOiBudWxsLA0KICAgICAgICBpc0RuUHNhQ29uZmlybWVkOiBudWxsLA0KICAgICAgICBpc0RuU3VwcGxpZXJDb25maXJtZWQ6IG51bGwsDQogICAgICAgIGlzQWNjb3VudENvbmZpcm1lZDogbnVsbCwNCiAgICAgICAgY29uZmlybUFjY291bnRJZDogbnVsbCwNCiAgICAgICAgYWNjb3VudENvbmZpcm1UaW1lOiBudWxsLA0KICAgICAgICBzYWxlc0NvbmZpcm1lZFRpbWU6IG51bGwsDQogICAgICAgIGNsaWVudENvbmZpcm1lZFRpbWU6IG51bGwsDQogICAgICAgIG9wQ29uZmlybWVkVGltZTogbnVsbCwNCiAgICAgICAgcHNhQ29uZmlybWVkVGltZTogbnVsbCwNCiAgICAgICAgc3VwcGxpZXJDb25maXJtZWRUaW1lOiBudWxsDQogICAgICB9LA0KICAgICAgcnNPcEFpclNlcnZpY2VJbnN0YW5jZTogew0KICAgICAgICBzZXJ2aWNlSWQ6IG51bGwsDQogICAgICAgIHNlcnZpY2VUeXBlSWQ6IG51bGwsDQogICAgICAgIHJjdElkOiBudWxsLA0KICAgICAgICByY3RObzogbnVsbCwNCiAgICAgICAgc3VwcGxpZXJJZDogbnVsbCwNCiAgICAgICAgc3VwcGxpZXJTdW1tYXJ5OiBudWxsLA0KICAgICAgICBzdXBwbGllckNvbnRhY3Q6IG51bGwsDQogICAgICAgIHN1cHBsaWVyVGVsOiBudWxsLA0KICAgICAgICBwYXltZW50VGl0bGVDb2RlOiBudWxsLA0KICAgICAgICBsb2dpc3RpY3NQYXltZW50VGVybXNDb2RlOiBudWxsLA0KICAgICAgICBpbnF1aXJ5Tm86IG51bGwsDQogICAgICAgIGFncmVlbWVudFR5cGVDb2RlOiBudWxsLA0KICAgICAgICBhZ3JlZW1lbnRObzogbnVsbCwNCiAgICAgICAgbWF4V2VpZ2h0OiBudWxsLA0KICAgICAgICBpbnF1aXJ5Tm90aWNlOiBudWxsLA0KICAgICAgICBpbnF1aXJ5SW5uZXJSZW1hcms6IG51bGwsDQogICAgICAgIGlucXVpcnlQc2FJZDogbnVsbCwNCiAgICAgICAgaW5xdWlyeUxlYXRlc3RVcGRhdGVkVGltZTogbnVsbCwNCiAgICAgICAgc2VydmljZUJlbG9uZ1RvOiBudWxsLA0KICAgICAgICBpc0RuU2FsZXNDb25maXJtZWQ6IG51bGwsDQogICAgICAgIGlzRG5DbGllbnRDb25maXJtZWQ6IG51bGwsDQogICAgICAgIGlzRG5PcENvbmZpcm1lZDogbnVsbCwNCiAgICAgICAgaXNEblBzYUNvbmZpcm1lZDogbnVsbCwNCiAgICAgICAgaXNEblN1cHBsaWVyQ29uZmlybWVkOiBudWxsLA0KICAgICAgICBpc0FjY291bnRDb25maXJtZWQ6IG51bGwsDQogICAgICAgIGNvbmZpcm1BY2NvdW50SWQ6IG51bGwsDQogICAgICAgIGFjY291bnRDb25maXJtVGltZTogbnVsbCwNCiAgICAgICAgc2FsZXNDb25maXJtZWRUaW1lOiBudWxsLA0KICAgICAgICBjbGllbnRDb25maXJtZWRUaW1lOiBudWxsLA0KICAgICAgICBvcENvbmZpcm1lZFRpbWU6IG51bGwsDQogICAgICAgIHBzYUNvbmZpcm1lZFRpbWU6IG51bGwsDQogICAgICAgIHN1cHBsaWVyQ29uZmlybWVkVGltZTogbnVsbA0KICAgICAgfSwNCiAgICAgIHJzT3BSYWlsU2VydmljZUluc3RhbmNlOiB7DQogICAgICAgIHNlcnZpY2VJZDogbnVsbCwNCiAgICAgICAgc2VydmljZVR5cGVJZDogbnVsbCwNCiAgICAgICAgcmN0SWQ6IG51bGwsDQogICAgICAgIHJjdE5vOiBudWxsLA0KICAgICAgICBzdXBwbGllcklkOiBudWxsLA0KICAgICAgICBzdXBwbGllclN1bW1hcnk6IG51bGwsDQogICAgICAgIHN1cHBsaWVyQ29udGFjdDogbnVsbCwNCiAgICAgICAgc3VwcGxpZXJUZWw6IG51bGwsDQogICAgICAgIHBheW1lbnRUaXRsZUNvZGU6IG51bGwsDQogICAgICAgIGxvZ2lzdGljc1BheW1lbnRUZXJtc0NvZGU6IG51bGwsDQogICAgICAgIGlucXVpcnlObzogbnVsbCwNCiAgICAgICAgYWdyZWVtZW50VHlwZUNvZGU6IG51bGwsDQogICAgICAgIGFncmVlbWVudE5vOiBudWxsLA0KICAgICAgICBtYXhXZWlnaHQ6IG51bGwsDQogICAgICAgIGlucXVpcnlOb3RpY2U6IG51bGwsDQogICAgICAgIGlucXVpcnlJbm5lclJlbWFyazogbnVsbCwNCiAgICAgICAgaW5xdWlyeVBzYUlkOiBudWxsLA0KICAgICAgICBpbnF1aXJ5TGVhdGVzdFVwZGF0ZWRUaW1lOiBudWxsLA0KICAgICAgICBzZXJ2aWNlQmVsb25nVG86IG51bGwsDQogICAgICAgIGlzRG5TYWxlc0NvbmZpcm1lZDogbnVsbCwNCiAgICAgICAgaXNEbkNsaWVudENvbmZpcm1lZDogbnVsbCwNCiAgICAgICAgaXNEbk9wQ29uZmlybWVkOiBudWxsLA0KICAgICAgICBpc0RuUHNhQ29uZmlybWVkOiBudWxsLA0KICAgICAgICBpc0RuU3VwcGxpZXJDb25maXJtZWQ6IG51bGwsDQogICAgICAgIGlzQWNjb3VudENvbmZpcm1lZDogbnVsbCwNCiAgICAgICAgY29uZmlybUFjY291bnRJZDogbnVsbCwNCiAgICAgICAgYWNjb3VudENvbmZpcm1UaW1lOiBudWxsLA0KICAgICAgICBzYWxlc0NvbmZpcm1lZFRpbWU6IG51bGwsDQogICAgICAgIGNsaWVudENvbmZpcm1lZFRpbWU6IG51bGwsDQogICAgICAgIG9wQ29uZmlybWVkVGltZTogbnVsbCwNCiAgICAgICAgcHNhQ29uZmlybWVkVGltZTogbnVsbCwNCiAgICAgICAgc3VwcGxpZXJDb25maXJtZWRUaW1lOiBudWxsDQogICAgICB9LA0KICAgICAgcnNPcFJhaWxGY2xTZXJ2aWNlSW5zdGFuY2U6IHsNCiAgICAgICAgc2VydmljZUlkOiBudWxsLA0KICAgICAgICBzZXJ2aWNlVHlwZUlkOiBudWxsLA0KICAgICAgICByY3RJZDogbnVsbCwNCiAgICAgICAgcmN0Tm86IG51bGwsDQogICAgICAgIHN1cHBsaWVySWQ6IG51bGwsDQogICAgICAgIHN1cHBsaWVyU3VtbWFyeTogbnVsbCwNCiAgICAgICAgc3VwcGxpZXJDb250YWN0OiBudWxsLA0KICAgICAgICBzdXBwbGllclRlbDogbnVsbCwNCiAgICAgICAgcGF5bWVudFRpdGxlQ29kZTogbnVsbCwNCiAgICAgICAgbG9naXN0aWNzUGF5bWVudFRlcm1zQ29kZTogbnVsbCwNCiAgICAgICAgaW5xdWlyeU5vOiBudWxsLA0KICAgICAgICBhZ3JlZW1lbnRUeXBlQ29kZTogbnVsbCwNCiAgICAgICAgYWdyZWVtZW50Tm86IG51bGwsDQogICAgICAgIG1heFdlaWdodDogbnVsbCwNCiAgICAgICAgaW5xdWlyeU5vdGljZTogbnVsbCwNCiAgICAgICAgaW5xdWlyeUlubmVyUmVtYXJrOiBudWxsLA0KICAgICAgICBpbnF1aXJ5UHNhSWQ6IG51bGwsDQogICAgICAgIGlucXVpcnlMZWF0ZXN0VXBkYXRlZFRpbWU6IG51bGwsDQogICAgICAgIHNlcnZpY2VCZWxvbmdUbzogbnVsbCwNCiAgICAgICAgaXNEblNhbGVzQ29uZmlybWVkOiBudWxsLA0KICAgICAgICBpc0RuQ2xpZW50Q29uZmlybWVkOiBudWxsLA0KICAgICAgICBpc0RuT3BDb25maXJtZWQ6IG51bGwsDQogICAgICAgIGlzRG5Qc2FDb25maXJtZWQ6IG51bGwsDQogICAgICAgIGlzRG5TdXBwbGllckNvbmZpcm1lZDogbnVsbCwNCiAgICAgICAgaXNBY2NvdW50Q29uZmlybWVkOiBudWxsLA0KICAgICAgICBjb25maXJtQWNjb3VudElkOiBudWxsLA0KICAgICAgICBhY2NvdW50Q29uZmlybVRpbWU6IG51bGwsDQogICAgICAgIHNhbGVzQ29uZmlybWVkVGltZTogbnVsbCwNCiAgICAgICAgY2xpZW50Q29uZmlybWVkVGltZTogbnVsbCwNCiAgICAgICAgb3BDb25maXJtZWRUaW1lOiBudWxsLA0KICAgICAgICBwc2FDb25maXJtZWRUaW1lOiBudWxsLA0KICAgICAgICBzdXBwbGllckNvbmZpcm1lZFRpbWU6IG51bGwNCiAgICAgIH0sDQogICAgICByc09wUmFpbExjbFNlcnZpY2VJbnN0YW5jZTogew0KICAgICAgICBzZXJ2aWNlSWQ6IG51bGwsDQogICAgICAgIHNlcnZpY2VUeXBlSWQ6IG51bGwsDQogICAgICAgIHJjdElkOiBudWxsLA0KICAgICAgICByY3RObzogbnVsbCwNCiAgICAgICAgc3VwcGxpZXJJZDogbnVsbCwNCiAgICAgICAgc3VwcGxpZXJTdW1tYXJ5OiBudWxsLA0KICAgICAgICBzdXBwbGllckNvbnRhY3Q6IG51bGwsDQogICAgICAgIHN1cHBsaWVyVGVsOiBudWxsLA0KICAgICAgICBwYXltZW50VGl0bGVDb2RlOiBudWxsLA0KICAgICAgICBsb2dpc3RpY3NQYXltZW50VGVybXNDb2RlOiBudWxsLA0KICAgICAgICBpbnF1aXJ5Tm86IG51bGwsDQogICAgICAgIGFncmVlbWVudFR5cGVDb2RlOiBudWxsLA0KICAgICAgICBhZ3JlZW1lbnRObzogbnVsbCwNCiAgICAgICAgbWF4V2VpZ2h0OiBudWxsLA0KICAgICAgICBpbnF1aXJ5Tm90aWNlOiBudWxsLA0KICAgICAgICBpbnF1aXJ5SW5uZXJSZW1hcms6IG51bGwsDQogICAgICAgIGlucXVpcnlQc2FJZDogbnVsbCwNCiAgICAgICAgaW5xdWlyeUxlYXRlc3RVcGRhdGVkVGltZTogbnVsbCwNCiAgICAgICAgc2VydmljZUJlbG9uZ1RvOiBudWxsLA0KICAgICAgICBpc0RuU2FsZXNDb25maXJtZWQ6IG51bGwsDQogICAgICAgIGlzRG5DbGllbnRDb25maXJtZWQ6IG51bGwsDQogICAgICAgIGlzRG5PcENvbmZpcm1lZDogbnVsbCwNCiAgICAgICAgaXNEblBzYUNvbmZpcm1lZDogbnVsbCwNCiAgICAgICAgaXNEblN1cHBsaWVyQ29uZmlybWVkOiBudWxsLA0KICAgICAgICBpc0FjY291bnRDb25maXJtZWQ6IG51bGwsDQogICAgICAgIGNvbmZpcm1BY2NvdW50SWQ6IG51bGwsDQogICAgICAgIGFjY291bnRDb25maXJtVGltZTogbnVsbCwNCiAgICAgICAgc2FsZXNDb25maXJtZWRUaW1lOiBudWxsLA0KICAgICAgICBjbGllbnRDb25maXJtZWRUaW1lOiBudWxsLA0KICAgICAgICBvcENvbmZpcm1lZFRpbWU6IG51bGwsDQogICAgICAgIHBzYUNvbmZpcm1lZFRpbWU6IG51bGwsDQogICAgICAgIHN1cHBsaWVyQ29uZmlybWVkVGltZTogbnVsbA0KICAgICAgfSwNCiAgICAgIHJzT3BFeHByZXNzU2VydmljZUluc3RhbmNlOiB7DQogICAgICAgIHNlcnZpY2VJZDogbnVsbCwNCiAgICAgICAgc2VydmljZVR5cGVJZDogbnVsbCwNCiAgICAgICAgcmN0SWQ6IG51bGwsDQogICAgICAgIHJjdE5vOiBudWxsLA0KICAgICAgICBzdXBwbGllcklkOiBudWxsLA0KICAgICAgICBzdXBwbGllclN1bW1hcnk6IG51bGwsDQogICAgICAgIHN1cHBsaWVyQ29udGFjdDogbnVsbCwNCiAgICAgICAgc3VwcGxpZXJUZWw6IG51bGwsDQogICAgICAgIHBheW1lbnRUaXRsZUNvZGU6IG51bGwsDQogICAgICAgIGxvZ2lzdGljc1BheW1lbnRUZXJtc0NvZGU6IG51bGwsDQogICAgICAgIGlucXVpcnlObzogbnVsbCwNCiAgICAgICAgYWdyZWVtZW50VHlwZUNvZGU6IG51bGwsDQogICAgICAgIGFncmVlbWVudE5vOiBudWxsLA0KICAgICAgICBtYXhXZWlnaHQ6IG51bGwsDQogICAgICAgIGlucXVpcnlOb3RpY2U6IG51bGwsDQogICAgICAgIGlucXVpcnlJbm5lclJlbWFyazogbnVsbCwNCiAgICAgICAgaW5xdWlyeVBzYUlkOiBudWxsLA0KICAgICAgICBpbnF1aXJ5TGVhdGVzdFVwZGF0ZWRUaW1lOiBudWxsLA0KICAgICAgICBzZXJ2aWNlQmVsb25nVG86IG51bGwsDQogICAgICAgIGlzRG5TYWxlc0NvbmZpcm1lZDogbnVsbCwNCiAgICAgICAgaXNEbkNsaWVudENvbmZpcm1lZDogbnVsbCwNCiAgICAgICAgaXNEbk9wQ29uZmlybWVkOiBudWxsLA0KICAgICAgICBpc0RuUHNhQ29uZmlybWVkOiBudWxsLA0KICAgICAgICBpc0RuU3VwcGxpZXJDb25maXJtZWQ6IG51bGwsDQogICAgICAgIGlzQWNjb3VudENvbmZpcm1lZDogbnVsbCwNCiAgICAgICAgY29uZmlybUFjY291bnRJZDogbnVsbCwNCiAgICAgICAgYWNjb3VudENvbmZpcm1UaW1lOiBudWxsLA0KICAgICAgICBzYWxlc0NvbmZpcm1lZFRpbWU6IG51bGwsDQogICAgICAgIGNsaWVudENvbmZpcm1lZFRpbWU6IG51bGwsDQogICAgICAgIG9wQ29uZmlybWVkVGltZTogbnVsbCwNCiAgICAgICAgcHNhQ29uZmlybWVkVGltZTogbnVsbCwNCiAgICAgICAgc3VwcGxpZXJDb25maXJtZWRUaW1lOiBudWxsDQogICAgICB9LA0KICAgICAgcnNPcFRydWNrU2VydmljZUluc3RhbmNlOiB7DQogICAgICAgIHNlcnZpY2VJZDogbnVsbCwNCiAgICAgICAgc2VydmljZVR5cGVJZDogbnVsbCwNCiAgICAgICAgcmN0SWQ6IG51bGwsDQogICAgICAgIHJjdE5vOiBudWxsLA0KICAgICAgICBzdXBwbGllcklkOiBudWxsLA0KICAgICAgICBzdXBwbGllclN1bW1hcnk6IG51bGwsDQogICAgICAgIHN1cHBsaWVyQ29udGFjdDogbnVsbCwNCiAgICAgICAgc3VwcGxpZXJUZWw6IG51bGwsDQogICAgICAgIHBheW1lbnRUaXRsZUNvZGU6IG51bGwsDQogICAgICAgIGxvZ2lzdGljc1BheW1lbnRUZXJtc0NvZGU6IG51bGwsDQogICAgICAgIGlucXVpcnlObzogbnVsbCwNCiAgICAgICAgYWdyZWVtZW50VHlwZUNvZGU6IG51bGwsDQogICAgICAgIGFncmVlbWVudE5vOiBudWxsLA0KICAgICAgICBtYXhXZWlnaHQ6IG51bGwsDQogICAgICAgIGlucXVpcnlOb3RpY2U6IG51bGwsDQogICAgICAgIGlucXVpcnlJbm5lclJlbWFyazogbnVsbCwNCiAgICAgICAgaW5xdWlyeVBzYUlkOiBudWxsLA0KICAgICAgICBpbnF1aXJ5TGVhdGVzdFVwZGF0ZWRUaW1lOiBudWxsLA0KICAgICAgICBzZXJ2aWNlQmVsb25nVG86IG51bGwsDQogICAgICAgIGlzRG5TYWxlc0NvbmZpcm1lZDogbnVsbCwNCiAgICAgICAgaXNEbkNsaWVudENvbmZpcm1lZDogbnVsbCwNCiAgICAgICAgaXNEbk9wQ29uZmlybWVkOiBudWxsLA0KICAgICAgICBpc0RuUHNhQ29uZmlybWVkOiBudWxsLA0KICAgICAgICBpc0RuU3VwcGxpZXJDb25maXJtZWQ6IG51bGwsDQogICAgICAgIGlzQWNjb3VudENvbmZpcm1lZDogbnVsbCwNCiAgICAgICAgY29uZmlybUFjY291bnRJZDogbnVsbCwNCiAgICAgICAgYWNjb3VudENvbmZpcm1UaW1lOiBudWxsLA0KICAgICAgICBzYWxlc0NvbmZpcm1lZFRpbWU6IG51bGwsDQogICAgICAgIGNsaWVudENvbmZpcm1lZFRpbWU6IG51bGwsDQogICAgICAgIG9wQ29uZmlybWVkVGltZTogbnVsbCwNCiAgICAgICAgcHNhQ29uZmlybWVkVGltZTogbnVsbCwNCiAgICAgICAgc3VwcGxpZXJDb25maXJtZWRUaW1lOiBudWxsDQogICAgICB9LA0KICAgICAgcnNPcEN0bnJUcnVja1NlcnZpY2VJbnN0YW5jZTogew0KICAgICAgICBzZXJ2aWNlSWQ6IG51bGwsDQogICAgICAgIHNlcnZpY2VUeXBlSWQ6IG51bGwsDQogICAgICAgIHJjdElkOiBudWxsLA0KICAgICAgICByY3RObzogbnVsbCwNCiAgICAgICAgc3VwcGxpZXJJZDogbnVsbCwNCiAgICAgICAgc3VwcGxpZXJTdW1tYXJ5OiBudWxsLA0KICAgICAgICBzdXBwbGllckNvbnRhY3Q6IG51bGwsDQogICAgICAgIHN1cHBsaWVyVGVsOiBudWxsLA0KICAgICAgICBwYXltZW50VGl0bGVDb2RlOiBudWxsLA0KICAgICAgICBsb2dpc3RpY3NQYXltZW50VGVybXNDb2RlOiBudWxsLA0KICAgICAgICBpbnF1aXJ5Tm86IG51bGwsDQogICAgICAgIGFncmVlbWVudFR5cGVDb2RlOiBudWxsLA0KICAgICAgICBhZ3JlZW1lbnRObzogbnVsbCwNCiAgICAgICAgbWF4V2VpZ2h0OiBudWxsLA0KICAgICAgICBpbnF1aXJ5Tm90aWNlOiBudWxsLA0KICAgICAgICBpbnF1aXJ5SW5uZXJSZW1hcms6IG51bGwsDQogICAgICAgIGlucXVpcnlQc2FJZDogbnVsbCwNCiAgICAgICAgaW5xdWlyeUxlYXRlc3RVcGRhdGVkVGltZTogbnVsbCwNCiAgICAgICAgc2VydmljZUJlbG9uZ1RvOiBudWxsLA0KICAgICAgICBpc0RuU2FsZXNDb25maXJtZWQ6IG51bGwsDQogICAgICAgIGlzRG5DbGllbnRDb25maXJtZWQ6IG51bGwsDQogICAgICAgIGlzRG5PcENvbmZpcm1lZDogbnVsbCwNCiAgICAgICAgaXNEblBzYUNvbmZpcm1lZDogbnVsbCwNCiAgICAgICAgaXNEblN1cHBsaWVyQ29uZmlybWVkOiBudWxsLA0KICAgICAgICBpc0FjY291bnRDb25maXJtZWQ6IG51bGwsDQogICAgICAgIGNvbmZpcm1BY2NvdW50SWQ6IG51bGwsDQogICAgICAgIGFjY291bnRDb25maXJtVGltZTogbnVsbCwNCiAgICAgICAgc2FsZXNDb25maXJtZWRUaW1lOiBudWxsLA0KICAgICAgICBjbGllbnRDb25maXJtZWRUaW1lOiBudWxsLA0KICAgICAgICBvcENvbmZpcm1lZFRpbWU6IG51bGwsDQogICAgICAgIHBzYUNvbmZpcm1lZFRpbWU6IG51bGwsDQogICAgICAgIHN1cHBsaWVyQ29uZmlybWVkVGltZTogbnVsbA0KICAgICAgfSwNCiAgICAgIHJzT3BCdWxrVHJ1Y2tTZXJ2aWNlSW5zdGFuY2U6IHsNCiAgICAgICAgc2VydmljZUlkOiBudWxsLA0KICAgICAgICBzZXJ2aWNlVHlwZUlkOiBudWxsLA0KICAgICAgICByY3RJZDogbnVsbCwNCiAgICAgICAgcmN0Tm86IG51bGwsDQogICAgICAgIHN1cHBsaWVySWQ6IG51bGwsDQogICAgICAgIHN1cHBsaWVyU3VtbWFyeTogbnVsbCwNCiAgICAgICAgc3VwcGxpZXJDb250YWN0OiBudWxsLA0KICAgICAgICBzdXBwbGllclRlbDogbnVsbCwNCiAgICAgICAgcGF5bWVudFRpdGxlQ29kZTogbnVsbCwNCiAgICAgICAgbG9naXN0aWNzUGF5bWVudFRlcm1zQ29kZTogbnVsbCwNCiAgICAgICAgaW5xdWlyeU5vOiBudWxsLA0KICAgICAgICBhZ3JlZW1lbnRUeXBlQ29kZTogbnVsbCwNCiAgICAgICAgYWdyZWVtZW50Tm86IG51bGwsDQogICAgICAgIG1heFdlaWdodDogbnVsbCwNCiAgICAgICAgaW5xdWlyeU5vdGljZTogbnVsbCwNCiAgICAgICAgaW5xdWlyeUlubmVyUmVtYXJrOiBudWxsLA0KICAgICAgICBpbnF1aXJ5UHNhSWQ6IG51bGwsDQogICAgICAgIGlucXVpcnlMZWF0ZXN0VXBkYXRlZFRpbWU6IG51bGwsDQogICAgICAgIHNlcnZpY2VCZWxvbmdUbzogbnVsbCwNCiAgICAgICAgaXNEblNhbGVzQ29uZmlybWVkOiBudWxsLA0KICAgICAgICBpc0RuQ2xpZW50Q29uZmlybWVkOiBudWxsLA0KICAgICAgICBpc0RuT3BDb25maXJtZWQ6IG51bGwsDQogICAgICAgIGlzRG5Qc2FDb25maXJtZWQ6IG51bGwsDQogICAgICAgIGlzRG5TdXBwbGllckNvbmZpcm1lZDogbnVsbCwNCiAgICAgICAgaXNBY2NvdW50Q29uZmlybWVkOiBudWxsLA0KICAgICAgICBjb25maXJtQWNjb3VudElkOiBudWxsLA0KICAgICAgICBhY2NvdW50Q29uZmlybVRpbWU6IG51bGwsDQogICAgICAgIHNhbGVzQ29uZmlybWVkVGltZTogbnVsbCwNCiAgICAgICAgY2xpZW50Q29uZmlybWVkVGltZTogbnVsbCwNCiAgICAgICAgb3BDb25maXJtZWRUaW1lOiBudWxsLA0KICAgICAgICBwc2FDb25maXJtZWRUaW1lOiBudWxsLA0KICAgICAgICBzdXBwbGllckNvbmZpcm1lZFRpbWU6IG51bGwNCiAgICAgIH0sDQogICAgICByc09wRXhwb3J0Q3VzdG9tc0NsZWFyYW5jZVNlcnZpY2VJbnN0YW5jZTogew0KICAgICAgICBzZXJ2aWNlSWQ6IG51bGwsDQogICAgICAgIHNlcnZpY2VUeXBlSWQ6IG51bGwsDQogICAgICAgIHJjdElkOiBudWxsLA0KICAgICAgICByY3RObzogbnVsbCwNCiAgICAgICAgc3VwcGxpZXJJZDogbnVsbCwNCiAgICAgICAgc3VwcGxpZXJTdW1tYXJ5OiBudWxsLA0KICAgICAgICBzdXBwbGllckNvbnRhY3Q6IG51bGwsDQogICAgICAgIHN1cHBsaWVyVGVsOiBudWxsLA0KICAgICAgICBwYXltZW50VGl0bGVDb2RlOiBudWxsLA0KICAgICAgICBsb2dpc3RpY3NQYXltZW50VGVybXNDb2RlOiBudWxsLA0KICAgICAgICBpbnF1aXJ5Tm86IG51bGwsDQogICAgICAgIGFncmVlbWVudFR5cGVDb2RlOiBudWxsLA0KICAgICAgICBhZ3JlZW1lbnRObzogbnVsbCwNCiAgICAgICAgbWF4V2VpZ2h0OiBudWxsLA0KICAgICAgICBpbnF1aXJ5Tm90aWNlOiBudWxsLA0KICAgICAgICBpbnF1aXJ5SW5uZXJSZW1hcms6IG51bGwsDQogICAgICAgIGlucXVpcnlQc2FJZDogbnVsbCwNCiAgICAgICAgaW5xdWlyeUxlYXRlc3RVcGRhdGVkVGltZTogbnVsbCwNCiAgICAgICAgc2VydmljZUJlbG9uZ1RvOiBudWxsLA0KICAgICAgICBpc0RuU2FsZXNDb25maXJtZWQ6IG51bGwsDQogICAgICAgIGlzRG5DbGllbnRDb25maXJtZWQ6IG51bGwsDQogICAgICAgIGlzRG5PcENvbmZpcm1lZDogbnVsbCwNCiAgICAgICAgaXNEblBzYUNvbmZpcm1lZDogbnVsbCwNCiAgICAgICAgaXNEblN1cHBsaWVyQ29uZmlybWVkOiBudWxsLA0KICAgICAgICBpc0FjY291bnRDb25maXJtZWQ6IG51bGwsDQogICAgICAgIGNvbmZpcm1BY2NvdW50SWQ6IG51bGwsDQogICAgICAgIGFjY291bnRDb25maXJtVGltZTogbnVsbCwNCiAgICAgICAgc2FsZXNDb25maXJtZWRUaW1lOiBudWxsLA0KICAgICAgICBjbGllbnRDb25maXJtZWRUaW1lOiBudWxsLA0KICAgICAgICBvcENvbmZpcm1lZFRpbWU6IG51bGwsDQogICAgICAgIHBzYUNvbmZpcm1lZFRpbWU6IG51bGwsDQogICAgICAgIHN1cHBsaWVyQ29uZmlybWVkVGltZTogbnVsbA0KICAgICAgfSwNCiAgICAgIHJzT3BJbXBvcnRDdXN0b21zQ2xlYXJhbmNlU2VydmljZUluc3RhbmNlOiB7DQogICAgICAgIHNlcnZpY2VJZDogbnVsbCwNCiAgICAgICAgc2VydmljZVR5cGVJZDogbnVsbCwNCiAgICAgICAgcmN0SWQ6IG51bGwsDQogICAgICAgIHJjdE5vOiBudWxsLA0KICAgICAgICBzdXBwbGllcklkOiBudWxsLA0KICAgICAgICBzdXBwbGllclN1bW1hcnk6IG51bGwsDQogICAgICAgIHN1cHBsaWVyQ29udGFjdDogbnVsbCwNCiAgICAgICAgc3VwcGxpZXJUZWw6IG51bGwsDQogICAgICAgIHBheW1lbnRUaXRsZUNvZGU6IG51bGwsDQogICAgICAgIGxvZ2lzdGljc1BheW1lbnRUZXJtc0NvZGU6IG51bGwsDQogICAgICAgIGlucXVpcnlObzogbnVsbCwNCiAgICAgICAgYWdyZWVtZW50VHlwZUNvZGU6IG51bGwsDQogICAgICAgIGFncmVlbWVudE5vOiBudWxsLA0KICAgICAgICBtYXhXZWlnaHQ6IG51bGwsDQogICAgICAgIGlucXVpcnlOb3RpY2U6IG51bGwsDQogICAgICAgIGlucXVpcnlJbm5lclJlbWFyazogbnVsbCwNCiAgICAgICAgaW5xdWlyeVBzYUlkOiBudWxsLA0KICAgICAgICBpbnF1aXJ5TGVhdGVzdFVwZGF0ZWRUaW1lOiBudWxsLA0KICAgICAgICBzZXJ2aWNlQmVsb25nVG86IG51bGwsDQogICAgICAgIGlzRG5TYWxlc0NvbmZpcm1lZDogbnVsbCwNCiAgICAgICAgaXNEbkNsaWVudENvbmZpcm1lZDogbnVsbCwNCiAgICAgICAgaXNEbk9wQ29uZmlybWVkOiBudWxsLA0KICAgICAgICBpc0RuUHNhQ29uZmlybWVkOiBudWxsLA0KICAgICAgICBpc0RuU3VwcGxpZXJDb25maXJtZWQ6IG51bGwsDQogICAgICAgIGlzQWNjb3VudENvbmZpcm1lZDogbnVsbCwNCiAgICAgICAgY29uZmlybUFjY291bnRJZDogbnVsbCwNCiAgICAgICAgYWNjb3VudENvbmZpcm1UaW1lOiBudWxsLA0KICAgICAgICBzYWxlc0NvbmZpcm1lZFRpbWU6IG51bGwsDQogICAgICAgIGNsaWVudENvbmZpcm1lZFRpbWU6IG51bGwsDQogICAgICAgIG9wQ29uZmlybWVkVGltZTogbnVsbCwNCiAgICAgICAgcHNhQ29uZmlybWVkVGltZTogbnVsbCwNCiAgICAgICAgc3VwcGxpZXJDb25maXJtZWRUaW1lOiBudWxsDQogICAgICB9LA0KICAgICAgcnNPcERvY0RlY2xhcmVTZXJ2aWNlSW5zdGFuY2U6IHsNCiAgICAgICAgc2VydmljZUlkOiBudWxsLA0KICAgICAgICBzZXJ2aWNlVHlwZUlkOiBudWxsLA0KICAgICAgICByY3RJZDogbnVsbCwNCiAgICAgICAgcmN0Tm86IG51bGwsDQogICAgICAgIHN1cHBsaWVySWQ6IG51bGwsDQogICAgICAgIHN1cHBsaWVyU3VtbWFyeTogbnVsbCwNCiAgICAgICAgc3VwcGxpZXJDb250YWN0OiBudWxsLA0KICAgICAgICBzdXBwbGllclRlbDogbnVsbCwNCiAgICAgICAgcGF5bWVudFRpdGxlQ29kZTogbnVsbCwNCiAgICAgICAgbG9naXN0aWNzUGF5bWVudFRlcm1zQ29kZTogbnVsbCwNCiAgICAgICAgaW5xdWlyeU5vOiBudWxsLA0KICAgICAgICBhZ3JlZW1lbnRUeXBlQ29kZTogbnVsbCwNCiAgICAgICAgYWdyZWVtZW50Tm86IG51bGwsDQogICAgICAgIG1heFdlaWdodDogbnVsbCwNCiAgICAgICAgaW5xdWlyeU5vdGljZTogbnVsbCwNCiAgICAgICAgaW5xdWlyeUlubmVyUmVtYXJrOiBudWxsLA0KICAgICAgICBpbnF1aXJ5UHNhSWQ6IG51bGwsDQogICAgICAgIGlucXVpcnlMZWF0ZXN0VXBkYXRlZFRpbWU6IG51bGwsDQogICAgICAgIHNlcnZpY2VCZWxvbmdUbzogbnVsbCwNCiAgICAgICAgaXNEblNhbGVzQ29uZmlybWVkOiBudWxsLA0KICAgICAgICBpc0RuQ2xpZW50Q29uZmlybWVkOiBudWxsLA0KICAgICAgICBpc0RuT3BDb25maXJtZWQ6IG51bGwsDQogICAgICAgIGlzRG5Qc2FDb25maXJtZWQ6IG51bGwsDQogICAgICAgIGlzRG5TdXBwbGllckNvbmZpcm1lZDogbnVsbCwNCiAgICAgICAgaXNBY2NvdW50Q29uZmlybWVkOiBudWxsLA0KICAgICAgICBjb25maXJtQWNjb3VudElkOiBudWxsLA0KICAgICAgICBhY2NvdW50Q29uZmlybVRpbWU6IG51bGwsDQogICAgICAgIHNhbGVzQ29uZmlybWVkVGltZTogbnVsbCwNCiAgICAgICAgY2xpZW50Q29uZmlybWVkVGltZTogbnVsbCwNCiAgICAgICAgb3BDb25maXJtZWRUaW1lOiBudWxsLA0KICAgICAgICBwc2FDb25maXJtZWRUaW1lOiBudWxsLA0KICAgICAgICBzdXBwbGllckNvbmZpcm1lZFRpbWU6IG51bGwNCiAgICAgIH0sDQogICAgICByc09wRnJlZURlY2xhcmVTZXJ2aWNlSW5zdGFuY2U6IHsNCiAgICAgICAgc2VydmljZUlkOiBudWxsLA0KICAgICAgICBzZXJ2aWNlVHlwZUlkOiBudWxsLA0KICAgICAgICByY3RJZDogbnVsbCwNCiAgICAgICAgcmN0Tm86IG51bGwsDQogICAgICAgIHN1cHBsaWVySWQ6IG51bGwsDQogICAgICAgIHN1cHBsaWVyU3VtbWFyeTogbnVsbCwNCiAgICAgICAgc3VwcGxpZXJDb250YWN0OiBudWxsLA0KICAgICAgICBzdXBwbGllclRlbDogbnVsbCwNCiAgICAgICAgcGF5bWVudFRpdGxlQ29kZTogbnVsbCwNCiAgICAgICAgbG9naXN0aWNzUGF5bWVudFRlcm1zQ29kZTogbnVsbCwNCiAgICAgICAgaW5xdWlyeU5vOiBudWxsLA0KICAgICAgICBhZ3JlZW1lbnRUeXBlQ29kZTogbnVsbCwNCiAgICAgICAgYWdyZWVtZW50Tm86IG51bGwsDQogICAgICAgIG1heFdlaWdodDogbnVsbCwNCiAgICAgICAgaW5xdWlyeU5vdGljZTogbnVsbCwNCiAgICAgICAgaW5xdWlyeUlubmVyUmVtYXJrOiBudWxsLA0KICAgICAgICBpbnF1aXJ5UHNhSWQ6IG51bGwsDQogICAgICAgIGlucXVpcnlMZWF0ZXN0VXBkYXRlZFRpbWU6IG51bGwsDQogICAgICAgIHNlcnZpY2VCZWxvbmdUbzogbnVsbCwNCiAgICAgICAgaXNEblNhbGVzQ29uZmlybWVkOiBudWxsLA0KICAgICAgICBpc0RuQ2xpZW50Q29uZmlybWVkOiBudWxsLA0KICAgICAgICBpc0RuT3BDb25maXJtZWQ6IG51bGwsDQogICAgICAgIGlzRG5Qc2FDb25maXJtZWQ6IG51bGwsDQogICAgICAgIGlzRG5TdXBwbGllckNvbmZpcm1lZDogbnVsbCwNCiAgICAgICAgaXNBY2NvdW50Q29uZmlybWVkOiBudWxsLA0KICAgICAgICBjb25maXJtQWNjb3VudElkOiBudWxsLA0KICAgICAgICBhY2NvdW50Q29uZmlybVRpbWU6IG51bGwsDQogICAgICAgIHNhbGVzQ29uZmlybWVkVGltZTogbnVsbCwNCiAgICAgICAgY2xpZW50Q29uZmlybWVkVGltZTogbnVsbCwNCiAgICAgICAgb3BDb25maXJtZWRUaW1lOiBudWxsLA0KICAgICAgICBwc2FDb25maXJtZWRUaW1lOiBudWxsLA0KICAgICAgICBzdXBwbGllckNvbmZpcm1lZFRpbWU6IG51bGwNCiAgICAgIH0sDQogICAgICByc09wRE9BZ2VudFNlcnZpY2VJbnN0YW5jZTogew0KICAgICAgICBzZXJ2aWNlSWQ6IG51bGwsDQogICAgICAgIHNlcnZpY2VUeXBlSWQ6IG51bGwsDQogICAgICAgIHJjdElkOiBudWxsLA0KICAgICAgICByY3RObzogbnVsbCwNCiAgICAgICAgc3VwcGxpZXJJZDogbnVsbCwNCiAgICAgICAgc3VwcGxpZXJTdW1tYXJ5OiBudWxsLA0KICAgICAgICBzdXBwbGllckNvbnRhY3Q6IG51bGwsDQogICAgICAgIHN1cHBsaWVyVGVsOiBudWxsLA0KICAgICAgICBwYXltZW50VGl0bGVDb2RlOiBudWxsLA0KICAgICAgICBsb2dpc3RpY3NQYXltZW50VGVybXNDb2RlOiBudWxsLA0KICAgICAgICBpbnF1aXJ5Tm86IG51bGwsDQogICAgICAgIGFncmVlbWVudFR5cGVDb2RlOiBudWxsLA0KICAgICAgICBhZ3JlZW1lbnRObzogbnVsbCwNCiAgICAgICAgbWF4V2VpZ2h0OiBudWxsLA0KICAgICAgICBpbnF1aXJ5Tm90aWNlOiBudWxsLA0KICAgICAgICBpbnF1aXJ5SW5uZXJSZW1hcms6IG51bGwsDQogICAgICAgIGlucXVpcnlQc2FJZDogbnVsbCwNCiAgICAgICAgaW5xdWlyeUxlYXRlc3RVcGRhdGVkVGltZTogbnVsbCwNCiAgICAgICAgc2VydmljZUJlbG9uZ1RvOiBudWxsLA0KICAgICAgICBpc0RuU2FsZXNDb25maXJtZWQ6IG51bGwsDQogICAgICAgIGlzRG5DbGllbnRDb25maXJtZWQ6IG51bGwsDQogICAgICAgIGlzRG5PcENvbmZpcm1lZDogbnVsbCwNCiAgICAgICAgaXNEblBzYUNvbmZpcm1lZDogbnVsbCwNCiAgICAgICAgaXNEblN1cHBsaWVyQ29uZmlybWVkOiBudWxsLA0KICAgICAgICBpc0FjY291bnRDb25maXJtZWQ6IG51bGwsDQogICAgICAgIGNvbmZpcm1BY2NvdW50SWQ6IG51bGwsDQogICAgICAgIGFjY291bnRDb25maXJtVGltZTogbnVsbCwNCiAgICAgICAgc2FsZXNDb25maXJtZWRUaW1lOiBudWxsLA0KICAgICAgICBjbGllbnRDb25maXJtZWRUaW1lOiBudWxsLA0KICAgICAgICBvcENvbmZpcm1lZFRpbWU6IG51bGwsDQogICAgICAgIHBzYUNvbmZpcm1lZFRpbWU6IG51bGwsDQogICAgICAgIHN1cHBsaWVyQ29uZmlybWVkVGltZTogbnVsbA0KICAgICAgfSwNCiAgICAgIHJzT3BDbGVhckFnZW50U2VydmljZUluc3RhbmNlOiB7DQogICAgICAgIHNlcnZpY2VJZDogbnVsbCwNCiAgICAgICAgc2VydmljZVR5cGVJZDogbnVsbCwNCiAgICAgICAgcmN0SWQ6IG51bGwsDQogICAgICAgIHJjdE5vOiBudWxsLA0KICAgICAgICBzdXBwbGllcklkOiBudWxsLA0KICAgICAgICBzdXBwbGllclN1bW1hcnk6IG51bGwsDQogICAgICAgIHN1cHBsaWVyQ29udGFjdDogbnVsbCwNCiAgICAgICAgc3VwcGxpZXJUZWw6IG51bGwsDQogICAgICAgIHBheW1lbnRUaXRsZUNvZGU6IG51bGwsDQogICAgICAgIGxvZ2lzdGljc1BheW1lbnRUZXJtc0NvZGU6IG51bGwsDQogICAgICAgIGlucXVpcnlObzogbnVsbCwNCiAgICAgICAgYWdyZWVtZW50VHlwZUNvZGU6IG51bGwsDQogICAgICAgIGFncmVlbWVudE5vOiBudWxsLA0KICAgICAgICBtYXhXZWlnaHQ6IG51bGwsDQogICAgICAgIGlucXVpcnlOb3RpY2U6IG51bGwsDQogICAgICAgIGlucXVpcnlJbm5lclJlbWFyazogbnVsbCwNCiAgICAgICAgaW5xdWlyeVBzYUlkOiBudWxsLA0KICAgICAgICBpbnF1aXJ5TGVhdGVzdFVwZGF0ZWRUaW1lOiBudWxsLA0KICAgICAgICBzZXJ2aWNlQmVsb25nVG86IG51bGwsDQogICAgICAgIGlzRG5TYWxlc0NvbmZpcm1lZDogbnVsbCwNCiAgICAgICAgaXNEbkNsaWVudENvbmZpcm1lZDogbnVsbCwNCiAgICAgICAgaXNEbk9wQ29uZmlybWVkOiBudWxsLA0KICAgICAgICBpc0RuUHNhQ29uZmlybWVkOiBudWxsLA0KICAgICAgICBpc0RuU3VwcGxpZXJDb25maXJtZWQ6IG51bGwsDQogICAgICAgIGlzQWNjb3VudENvbmZpcm1lZDogbnVsbCwNCiAgICAgICAgY29uZmlybUFjY291bnRJZDogbnVsbCwNCiAgICAgICAgYWNjb3VudENvbmZpcm1UaW1lOiBudWxsLA0KICAgICAgICBzYWxlc0NvbmZpcm1lZFRpbWU6IG51bGwsDQogICAgICAgIGNsaWVudENvbmZpcm1lZFRpbWU6IG51bGwsDQogICAgICAgIG9wQ29uZmlybWVkVGltZTogbnVsbCwNCiAgICAgICAgcHNhQ29uZmlybWVkVGltZTogbnVsbCwNCiAgICAgICAgc3VwcGxpZXJDb25maXJtZWRUaW1lOiBudWxsDQogICAgICB9LA0KICAgICAgcnNPcEltcG9ydERpc3BhdGNoVHJ1Y2tTZXJ2aWNlSW5zdGFuY2U6IHsNCiAgICAgICAgc2VydmljZUlkOiBudWxsLA0KICAgICAgICBzZXJ2aWNlVHlwZUlkOiBudWxsLA0KICAgICAgICByY3RJZDogbnVsbCwNCiAgICAgICAgcmN0Tm86IG51bGwsDQogICAgICAgIHN1cHBsaWVySWQ6IG51bGwsDQogICAgICAgIHN1cHBsaWVyU3VtbWFyeTogbnVsbCwNCiAgICAgICAgc3VwcGxpZXJDb250YWN0OiBudWxsLA0KICAgICAgICBzdXBwbGllclRlbDogbnVsbCwNCiAgICAgICAgcGF5bWVudFRpdGxlQ29kZTogbnVsbCwNCiAgICAgICAgbG9naXN0aWNzUGF5bWVudFRlcm1zQ29kZTogbnVsbCwNCiAgICAgICAgaW5xdWlyeU5vOiBudWxsLA0KICAgICAgICBhZ3JlZW1lbnRUeXBlQ29kZTogbnVsbCwNCiAgICAgICAgYWdyZWVtZW50Tm86IG51bGwsDQogICAgICAgIG1heFdlaWdodDogbnVsbCwNCiAgICAgICAgaW5xdWlyeU5vdGljZTogbnVsbCwNCiAgICAgICAgaW5xdWlyeUlubmVyUmVtYXJrOiBudWxsLA0KICAgICAgICBpbnF1aXJ5UHNhSWQ6IG51bGwsDQogICAgICAgIGlucXVpcnlMZWF0ZXN0VXBkYXRlZFRpbWU6IG51bGwsDQogICAgICAgIHNlcnZpY2VCZWxvbmdUbzogbnVsbCwNCiAgICAgICAgaXNEblNhbGVzQ29uZmlybWVkOiBudWxsLA0KICAgICAgICBpc0RuQ2xpZW50Q29uZmlybWVkOiBudWxsLA0KICAgICAgICBpc0RuT3BDb25maXJtZWQ6IG51bGwsDQogICAgICAgIGlzRG5Qc2FDb25maXJtZWQ6IG51bGwsDQogICAgICAgIGlzRG5TdXBwbGllckNvbmZpcm1lZDogbnVsbCwNCiAgICAgICAgaXNBY2NvdW50Q29uZmlybWVkOiBudWxsLA0KICAgICAgICBjb25maXJtQWNjb3VudElkOiBudWxsLA0KICAgICAgICBhY2NvdW50Q29uZmlybVRpbWU6IG51bGwsDQogICAgICAgIHNhbGVzQ29uZmlybWVkVGltZTogbnVsbCwNCiAgICAgICAgY2xpZW50Q29uZmlybWVkVGltZTogbnVsbCwNCiAgICAgICAgb3BDb25maXJtZWRUaW1lOiBudWxsLA0KICAgICAgICBwc2FDb25maXJtZWRUaW1lOiBudWxsLA0KICAgICAgICBzdXBwbGllckNvbmZpcm1lZFRpbWU6IG51bGwNCiAgICAgIH0sDQogICAgICByc09wV2FyZWhvdXNlU2VydmljZUluc3RhbmNlOiB7DQogICAgICAgIHNlcnZpY2VJZDogbnVsbCwNCiAgICAgICAgc2VydmljZVR5cGVJZDogbnVsbCwNCiAgICAgICAgcmN0SWQ6IG51bGwsDQogICAgICAgIHJjdE5vOiBudWxsLA0KICAgICAgICBzdXBwbGllcklkOiBudWxsLA0KICAgICAgICBzdXBwbGllclN1bW1hcnk6IG51bGwsDQogICAgICAgIHN1cHBsaWVyQ29udGFjdDogbnVsbCwNCiAgICAgICAgc3VwcGxpZXJUZWw6IG51bGwsDQogICAgICAgIHBheW1lbnRUaXRsZUNvZGU6IG51bGwsDQogICAgICAgIGxvZ2lzdGljc1BheW1lbnRUZXJtc0NvZGU6IG51bGwsDQogICAgICAgIGlucXVpcnlObzogbnVsbCwNCiAgICAgICAgYWdyZWVtZW50VHlwZUNvZGU6IG51bGwsDQogICAgICAgIGFncmVlbWVudE5vOiBudWxsLA0KICAgICAgICBtYXhXZWlnaHQ6IG51bGwsDQogICAgICAgIGlucXVpcnlOb3RpY2U6IG51bGwsDQogICAgICAgIGlucXVpcnlJbm5lclJlbWFyazogbnVsbCwNCiAgICAgICAgaW5xdWlyeVBzYUlkOiBudWxsLA0KICAgICAgICBpbnF1aXJ5TGVhdGVzdFVwZGF0ZWRUaW1lOiBudWxsLA0KICAgICAgICBzZXJ2aWNlQmVsb25nVG86IG51bGwsDQogICAgICAgIGlzRG5TYWxlc0NvbmZpcm1lZDogbnVsbCwNCiAgICAgICAgaXNEbkNsaWVudENvbmZpcm1lZDogbnVsbCwNCiAgICAgICAgaXNEbk9wQ29uZmlybWVkOiBudWxsLA0KICAgICAgICBpc0RuUHNhQ29uZmlybWVkOiBudWxsLA0KICAgICAgICBpc0RuU3VwcGxpZXJDb25maXJtZWQ6IG51bGwsDQogICAgICAgIGlzQWNjb3VudENvbmZpcm1lZDogbnVsbCwNCiAgICAgICAgY29uZmlybUFjY291bnRJZDogbnVsbCwNCiAgICAgICAgYWNjb3VudENvbmZpcm1UaW1lOiBudWxsLA0KICAgICAgICBzYWxlc0NvbmZpcm1lZFRpbWU6IG51bGwsDQogICAgICAgIGNsaWVudENvbmZpcm1lZFRpbWU6IG51bGwsDQogICAgICAgIG9wQ29uZmlybWVkVGltZTogbnVsbCwNCiAgICAgICAgcHNhQ29uZmlybWVkVGltZTogbnVsbCwNCiAgICAgICAgc3VwcGxpZXJDb25maXJtZWRUaW1lOiBudWxsDQogICAgICB9LA0KICAgICAgcnNPcEluc3BlY3Rpb25BbmRDZXJ0aWZpY2F0ZVNlcnZpY2VJbnN0YW5jZTogew0KICAgICAgICBzZXJ2aWNlSWQ6IG51bGwsDQogICAgICAgIHNlcnZpY2VUeXBlSWQ6IG51bGwsDQogICAgICAgIHJjdElkOiBudWxsLA0KICAgICAgICByY3RObzogbnVsbCwNCiAgICAgICAgc3VwcGxpZXJJZDogbnVsbCwNCiAgICAgICAgc3VwcGxpZXJTdW1tYXJ5OiBudWxsLA0KICAgICAgICBzdXBwbGllckNvbnRhY3Q6IG51bGwsDQogICAgICAgIHN1cHBsaWVyVGVsOiBudWxsLA0KICAgICAgICBwYXltZW50VGl0bGVDb2RlOiBudWxsLA0KICAgICAgICBsb2dpc3RpY3NQYXltZW50VGVybXNDb2RlOiBudWxsLA0KICAgICAgICBpbnF1aXJ5Tm86IG51bGwsDQogICAgICAgIGFncmVlbWVudFR5cGVDb2RlOiBudWxsLA0KICAgICAgICBhZ3JlZW1lbnRObzogbnVsbCwNCiAgICAgICAgbWF4V2VpZ2h0OiBudWxsLA0KICAgICAgICBpbnF1aXJ5Tm90aWNlOiBudWxsLA0KICAgICAgICBpbnF1aXJ5SW5uZXJSZW1hcms6IG51bGwsDQogICAgICAgIGlucXVpcnlQc2FJZDogbnVsbCwNCiAgICAgICAgaW5xdWlyeUxlYXRlc3RVcGRhdGVkVGltZTogbnVsbCwNCiAgICAgICAgc2VydmljZUJlbG9uZ1RvOiBudWxsLA0KICAgICAgICBpc0RuU2FsZXNDb25maXJtZWQ6IG51bGwsDQogICAgICAgIGlzRG5DbGllbnRDb25maXJtZWQ6IG51bGwsDQogICAgICAgIGlzRG5PcENvbmZpcm1lZDogbnVsbCwNCiAgICAgICAgaXNEblBzYUNvbmZpcm1lZDogbnVsbCwNCiAgICAgICAgaXNEblN1cHBsaWVyQ29uZmlybWVkOiBudWxsLA0KICAgICAgICBpc0FjY291bnRDb25maXJtZWQ6IG51bGwsDQogICAgICAgIGNvbmZpcm1BY2NvdW50SWQ6IG51bGwsDQogICAgICAgIGFjY291bnRDb25maXJtVGltZTogbnVsbCwNCiAgICAgICAgc2FsZXNDb25maXJtZWRUaW1lOiBudWxsLA0KICAgICAgICBjbGllbnRDb25maXJtZWRUaW1lOiBudWxsLA0KICAgICAgICBvcENvbmZpcm1lZFRpbWU6IG51bGwsDQogICAgICAgIHBzYUNvbmZpcm1lZFRpbWU6IG51bGwsDQogICAgICAgIHN1cHBsaWVyQ29uZmlybWVkVGltZTogbnVsbA0KICAgICAgfSwNCiAgICAgIHJzT3BMYW5kU2VydmljZUluc3RhbmNlOiB7DQogICAgICAgIHNlcnZpY2VJZDogbnVsbCwNCiAgICAgICAgc2VydmljZVR5cGVJZDogbnVsbCwNCiAgICAgICAgcmN0SWQ6IG51bGwsDQogICAgICAgIHJjdE5vOiBudWxsLA0KICAgICAgICBzdXBwbGllcklkOiBudWxsLA0KICAgICAgICBzdXBwbGllclN1bW1hcnk6IG51bGwsDQogICAgICAgIHN1cHBsaWVyQ29udGFjdDogbnVsbCwNCiAgICAgICAgc3VwcGxpZXJUZWw6IG51bGwsDQogICAgICAgIHBheW1lbnRUaXRsZUNvZGU6IG51bGwsDQogICAgICAgIGxvZ2lzdGljc1BheW1lbnRUZXJtc0NvZGU6IG51bGwsDQogICAgICAgIGlucXVpcnlObzogbnVsbCwNCiAgICAgICAgYWdyZWVtZW50VHlwZUNvZGU6IG51bGwsDQogICAgICAgIGFncmVlbWVudE5vOiBudWxsLA0KICAgICAgICBtYXhXZWlnaHQ6IG51bGwsDQogICAgICAgIGlucXVpcnlOb3RpY2U6IG51bGwsDQogICAgICAgIGlucXVpcnlJbm5lclJlbWFyazogbnVsbCwNCiAgICAgICAgaW5xdWlyeVBzYUlkOiBudWxsLA0KICAgICAgICBpbnF1aXJ5TGVhdGVzdFVwZGF0ZWRUaW1lOiBudWxsLA0KICAgICAgICBzZXJ2aWNlQmVsb25nVG86IG51bGwsDQogICAgICAgIGlzRG5TYWxlc0NvbmZpcm1lZDogbnVsbCwNCiAgICAgICAgaXNEbkNsaWVudENvbmZpcm1lZDogbnVsbCwNCiAgICAgICAgaXNEbk9wQ29uZmlybWVkOiBudWxsLA0KICAgICAgICBpc0RuUHNhQ29uZmlybWVkOiBudWxsLA0KICAgICAgICBpc0RuU3VwcGxpZXJDb25maXJtZWQ6IG51bGwsDQogICAgICAgIGlzQWNjb3VudENvbmZpcm1lZDogbnVsbCwNCiAgICAgICAgY29uZmlybUFjY291bnRJZDogbnVsbCwNCiAgICAgICAgYWNjb3VudENvbmZpcm1UaW1lOiBudWxsLA0KICAgICAgICBzYWxlc0NvbmZpcm1lZFRpbWU6IG51bGwsDQogICAgICAgIGNsaWVudENvbmZpcm1lZFRpbWU6IG51bGwsDQogICAgICAgIG9wQ29uZmlybWVkVGltZTogbnVsbCwNCiAgICAgICAgcHNhQ29uZmlybWVkVGltZTogbnVsbCwNCiAgICAgICAgc3VwcGxpZXJDb25maXJtZWRUaW1lOiBudWxsDQogICAgICB9LA0KICAgICAgcnNPcEluc3VyYW5jZVNlcnZpY2VJbnN0YW5jZTogew0KICAgICAgICBzZXJ2aWNlSWQ6IG51bGwsDQogICAgICAgIHNlcnZpY2VUeXBlSWQ6IG51bGwsDQogICAgICAgIHJjdElkOiBudWxsLA0KICAgICAgICByY3RObzogbnVsbCwNCiAgICAgICAgc3VwcGxpZXJJZDogbnVsbCwNCiAgICAgICAgc3VwcGxpZXJTdW1tYXJ5OiBudWxsLA0KICAgICAgICBzdXBwbGllckNvbnRhY3Q6IG51bGwsDQogICAgICAgIHN1cHBsaWVyVGVsOiBudWxsLA0KICAgICAgICBwYXltZW50VGl0bGVDb2RlOiBudWxsLA0KICAgICAgICBsb2dpc3RpY3NQYXltZW50VGVybXNDb2RlOiBudWxsLA0KICAgICAgICBpbnF1aXJ5Tm86IG51bGwsDQogICAgICAgIGFncmVlbWVudFR5cGVDb2RlOiBudWxsLA0KICAgICAgICBhZ3JlZW1lbnRObzogbnVsbCwNCiAgICAgICAgbWF4V2VpZ2h0OiBudWxsLA0KICAgICAgICBpbnF1aXJ5Tm90aWNlOiBudWxsLA0KICAgICAgICBpbnF1aXJ5SW5uZXJSZW1hcms6IG51bGwsDQogICAgICAgIGlucXVpcnlQc2FJZDogbnVsbCwNCiAgICAgICAgaW5xdWlyeUxlYXRlc3RVcGRhdGVkVGltZTogbnVsbCwNCiAgICAgICAgc2VydmljZUJlbG9uZ1RvOiBudWxsLA0KICAgICAgICBpc0RuU2FsZXNDb25maXJtZWQ6IG51bGwsDQogICAgICAgIGlzRG5DbGllbnRDb25maXJtZWQ6IG51bGwsDQogICAgICAgIGlzRG5PcENvbmZpcm1lZDogbnVsbCwNCiAgICAgICAgaXNEblBzYUNvbmZpcm1lZDogbnVsbCwNCiAgICAgICAgaXNEblN1cHBsaWVyQ29uZmlybWVkOiBudWxsLA0KICAgICAgICBpc0FjY291bnRDb25maXJtZWQ6IG51bGwsDQogICAgICAgIGNvbmZpcm1BY2NvdW50SWQ6IG51bGwsDQogICAgICAgIGFjY291bnRDb25maXJtVGltZTogbnVsbCwNCiAgICAgICAgc2FsZXNDb25maXJtZWRUaW1lOiBudWxsLA0KICAgICAgICBjbGllbnRDb25maXJtZWRUaW1lOiBudWxsLA0KICAgICAgICBvcENvbmZpcm1lZFRpbWU6IG51bGwsDQogICAgICAgIHBzYUNvbmZpcm1lZFRpbWU6IG51bGwsDQogICAgICAgIHN1cHBsaWVyQ29uZmlybWVkVGltZTogbnVsbA0KICAgICAgfSwNCiAgICAgIHJzT3BFeHBhbmRTZXJ2aWNlU2VydmljZUluc3RhbmNlOiB7DQogICAgICAgIHNlcnZpY2VJZDogbnVsbCwNCiAgICAgICAgc2VydmljZVR5cGVJZDogbnVsbCwNCiAgICAgICAgcmN0SWQ6IG51bGwsDQogICAgICAgIHJjdE5vOiBudWxsLA0KICAgICAgICBzdXBwbGllcklkOiBudWxsLA0KICAgICAgICBzdXBwbGllclN1bW1hcnk6IG51bGwsDQogICAgICAgIHN1cHBsaWVyQ29udGFjdDogbnVsbCwNCiAgICAgICAgc3VwcGxpZXJUZWw6IG51bGwsDQogICAgICAgIHBheW1lbnRUaXRsZUNvZGU6IG51bGwsDQogICAgICAgIGxvZ2lzdGljc1BheW1lbnRUZXJtc0NvZGU6IG51bGwsDQogICAgICAgIGlucXVpcnlObzogbnVsbCwNCiAgICAgICAgYWdyZWVtZW50VHlwZUNvZGU6IG51bGwsDQogICAgICAgIGFncmVlbWVudE5vOiBudWxsLA0KICAgICAgICBtYXhXZWlnaHQ6IG51bGwsDQogICAgICAgIGlucXVpcnlOb3RpY2U6IG51bGwsDQogICAgICAgIGlucXVpcnlJbm5lclJlbWFyazogbnVsbCwNCiAgICAgICAgaW5xdWlyeVBzYUlkOiBudWxsLA0KICAgICAgICBpbnF1aXJ5TGVhdGVzdFVwZGF0ZWRUaW1lOiBudWxsLA0KICAgICAgICBzZXJ2aWNlQmVsb25nVG86IG51bGwsDQogICAgICAgIGlzRG5TYWxlc0NvbmZpcm1lZDogbnVsbCwNCiAgICAgICAgaXNEbkNsaWVudENvbmZpcm1lZDogbnVsbCwNCiAgICAgICAgaXNEbk9wQ29uZmlybWVkOiBudWxsLA0KICAgICAgICBpc0RuUHNhQ29uZmlybWVkOiBudWxsLA0KICAgICAgICBpc0RuU3VwcGxpZXJDb25maXJtZWQ6IG51bGwsDQogICAgICAgIGlzQWNjb3VudENvbmZpcm1lZDogbnVsbCwNCiAgICAgICAgY29uZmlybUFjY291bnRJZDogbnVsbCwNCiAgICAgICAgYWNjb3VudENvbmZpcm1UaW1lOiBudWxsLA0KICAgICAgICBzYWxlc0NvbmZpcm1lZFRpbWU6IG51bGwsDQogICAgICAgIGNsaWVudENvbmZpcm1lZFRpbWU6IG51bGwsDQogICAgICAgIG9wQ29uZmlybWVkVGltZTogbnVsbCwNCiAgICAgICAgcHNhQ29uZmlybWVkVGltZTogbnVsbCwNCiAgICAgICAgc3VwcGxpZXJDb25maXJtZWRUaW1lOiBudWxsDQogICAgICB9LA0KICAgICAgcnNPcFdIU1NlcnZpY2VJbnN0YW5jZTogew0KICAgICAgICBzZXJ2aWNlSWQ6IG51bGwsDQogICAgICAgIHNlcnZpY2VUeXBlSWQ6IG51bGwsDQogICAgICAgIHJjdElkOiBudWxsLA0KICAgICAgICByY3RObzogbnVsbCwNCiAgICAgICAgc3VwcGxpZXJJZDogbnVsbCwNCiAgICAgICAgc3VwcGxpZXJTdW1tYXJ5OiBudWxsLA0KICAgICAgICBzdXBwbGllckNvbnRhY3Q6IG51bGwsDQogICAgICAgIHN1cHBsaWVyVGVsOiBudWxsLA0KICAgICAgICBwYXltZW50VGl0bGVDb2RlOiBudWxsLA0KICAgICAgICBsb2dpc3RpY3NQYXltZW50VGVybXNDb2RlOiBudWxsLA0KICAgICAgICBpbnF1aXJ5Tm86IG51bGwsDQogICAgICAgIGFncmVlbWVudFR5cGVDb2RlOiBudWxsLA0KICAgICAgICBhZ3JlZW1lbnRObzogbnVsbCwNCiAgICAgICAgbWF4V2VpZ2h0OiBudWxsLA0KICAgICAgICBpbnF1aXJ5Tm90aWNlOiBudWxsLA0KICAgICAgICBpbnF1aXJ5SW5uZXJSZW1hcms6IG51bGwsDQogICAgICAgIGlucXVpcnlQc2FJZDogbnVsbCwNCiAgICAgICAgaW5xdWlyeUxlYXRlc3RVcGRhdGVkVGltZTogbnVsbCwNCiAgICAgICAgc2VydmljZUJlbG9uZ1RvOiBudWxsLA0KICAgICAgICBpc0RuU2FsZXNDb25maXJtZWQ6IG51bGwsDQogICAgICAgIGlzRG5DbGllbnRDb25maXJtZWQ6IG51bGwsDQogICAgICAgIGlzRG5PcENvbmZpcm1lZDogbnVsbCwNCiAgICAgICAgaXNEblBzYUNvbmZpcm1lZDogbnVsbCwNCiAgICAgICAgaXNEblN1cHBsaWVyQ29uZmlybWVkOiBudWxsLA0KICAgICAgICBpc0FjY291bnRDb25maXJtZWQ6IG51bGwsDQogICAgICAgIGNvbmZpcm1BY2NvdW50SWQ6IG51bGwsDQogICAgICAgIGFjY291bnRDb25maXJtVGltZTogbnVsbCwNCiAgICAgICAgc2FsZXNDb25maXJtZWRUaW1lOiBudWxsLA0KICAgICAgICBjbGllbnRDb25maXJtZWRUaW1lOiBudWxsLA0KICAgICAgICBvcENvbmZpcm1lZFRpbWU6IG51bGwsDQogICAgICAgIHBzYUNvbmZpcm1lZFRpbWU6IG51bGwsDQogICAgICAgIHN1cHBsaWVyQ29uZmlybWVkVGltZTogbnVsbA0KICAgICAgfSwNCiAgICAgIHJzT3AzcmRDZXJ0U2VydmljZUluc3RhbmNlOiB7DQogICAgICAgIHNlcnZpY2VJZDogbnVsbCwNCiAgICAgICAgc2VydmljZVR5cGVJZDogbnVsbCwNCiAgICAgICAgcmN0SWQ6IG51bGwsDQogICAgICAgIHJjdE5vOiBudWxsLA0KICAgICAgICBzdXBwbGllcklkOiBudWxsLA0KICAgICAgICBzdXBwbGllclN1bW1hcnk6IG51bGwsDQogICAgICAgIHN1cHBsaWVyQ29udGFjdDogbnVsbCwNCiAgICAgICAgc3VwcGxpZXJUZWw6IG51bGwsDQogICAgICAgIHBheW1lbnRUaXRsZUNvZGU6IG51bGwsDQogICAgICAgIGxvZ2lzdGljc1BheW1lbnRUZXJtc0NvZGU6IG51bGwsDQogICAgICAgIGlucXVpcnlObzogbnVsbCwNCiAgICAgICAgYWdyZWVtZW50VHlwZUNvZGU6IG51bGwsDQogICAgICAgIGFncmVlbWVudE5vOiBudWxsLA0KICAgICAgICBtYXhXZWlnaHQ6IG51bGwsDQogICAgICAgIGlucXVpcnlOb3RpY2U6IG51bGwsDQogICAgICAgIGlucXVpcnlJbm5lclJlbWFyazogbnVsbCwNCiAgICAgICAgaW5xdWlyeVBzYUlkOiBudWxsLA0KICAgICAgICBpbnF1aXJ5TGVhdGVzdFVwZGF0ZWRUaW1lOiBudWxsLA0KICAgICAgICBzZXJ2aWNlQmVsb25nVG86IG51bGwsDQogICAgICAgIGlzRG5TYWxlc0NvbmZpcm1lZDogbnVsbCwNCiAgICAgICAgaXNEbkNsaWVudENvbmZpcm1lZDogbnVsbCwNCiAgICAgICAgaXNEbk9wQ29uZmlybWVkOiBudWxsLA0KICAgICAgICBpc0RuUHNhQ29uZmlybWVkOiBudWxsLA0KICAgICAgICBpc0RuU3VwcGxpZXJDb25maXJtZWQ6IG51bGwsDQogICAgICAgIGlzQWNjb3VudENvbmZpcm1lZDogbnVsbCwNCiAgICAgICAgY29uZmlybUFjY291bnRJZDogbnVsbCwNCiAgICAgICAgYWNjb3VudENvbmZpcm1UaW1lOiBudWxsLA0KICAgICAgICBzYWxlc0NvbmZpcm1lZFRpbWU6IG51bGwsDQogICAgICAgIGNsaWVudENvbmZpcm1lZFRpbWU6IG51bGwsDQogICAgICAgIG9wQ29uZmlybWVkVGltZTogbnVsbCwNCiAgICAgICAgcHNhQ29uZmlybWVkVGltZTogbnVsbCwNCiAgICAgICAgc3VwcGxpZXJDb25maXJtZWRUaW1lOiBudWxsDQogICAgICB9LA0KICAgICAgcnNPcElOU1NlcnZpY2VJbnN0YW5jZTogew0KICAgICAgICBzZXJ2aWNlSWQ6IG51bGwsDQogICAgICAgIHNlcnZpY2VUeXBlSWQ6IG51bGwsDQogICAgICAgIHJjdElkOiBudWxsLA0KICAgICAgICByY3RObzogbnVsbCwNCiAgICAgICAgc3VwcGxpZXJJZDogbnVsbCwNCiAgICAgICAgc3VwcGxpZXJTdW1tYXJ5OiBudWxsLA0KICAgICAgICBzdXBwbGllckNvbnRhY3Q6IG51bGwsDQogICAgICAgIHN1cHBsaWVyVGVsOiBudWxsLA0KICAgICAgICBwYXltZW50VGl0bGVDb2RlOiBudWxsLA0KICAgICAgICBsb2dpc3RpY3NQYXltZW50VGVybXNDb2RlOiBudWxsLA0KICAgICAgICBpbnF1aXJ5Tm86IG51bGwsDQogICAgICAgIGFncmVlbWVudFR5cGVDb2RlOiBudWxsLA0KICAgICAgICBhZ3JlZW1lbnRObzogbnVsbCwNCiAgICAgICAgbWF4V2VpZ2h0OiBudWxsLA0KICAgICAgICBpbnF1aXJ5Tm90aWNlOiBudWxsLA0KICAgICAgICBpbnF1aXJ5SW5uZXJSZW1hcms6IG51bGwsDQogICAgICAgIGlucXVpcnlQc2FJZDogbnVsbCwNCiAgICAgICAgaW5xdWlyeUxlYXRlc3RVcGRhdGVkVGltZTogbnVsbCwNCiAgICAgICAgc2VydmljZUJlbG9uZ1RvOiBudWxsLA0KICAgICAgICBpc0RuU2FsZXNDb25maXJtZWQ6IG51bGwsDQogICAgICAgIGlzRG5DbGllbnRDb25maXJtZWQ6IG51bGwsDQogICAgICAgIGlzRG5PcENvbmZpcm1lZDogbnVsbCwNCiAgICAgICAgaXNEblBzYUNvbmZpcm1lZDogbnVsbCwNCiAgICAgICAgaXNEblN1cHBsaWVyQ29uZmlybWVkOiBudWxsLA0KICAgICAgICBpc0FjY291bnRDb25maXJtZWQ6IG51bGwsDQogICAgICAgIGNvbmZpcm1BY2NvdW50SWQ6IG51bGwsDQogICAgICAgIGFjY291bnRDb25maXJtVGltZTogbnVsbCwNCiAgICAgICAgc2FsZXNDb25maXJtZWRUaW1lOiBudWxsLA0KICAgICAgICBjbGllbnRDb25maXJtZWRUaW1lOiBudWxsLA0KICAgICAgICBvcENvbmZpcm1lZFRpbWU6IG51bGwsDQogICAgICAgIHBzYUNvbmZpcm1lZFRpbWU6IG51bGwsDQogICAgICAgIHN1cHBsaWVyQ29uZmlybWVkVGltZTogbnVsbA0KICAgICAgfSwNCiAgICAgIHJzT3BUcmFkaW5nU2VydmljZUluc3RhbmNlOiB7DQogICAgICAgIHNlcnZpY2VJZDogbnVsbCwNCiAgICAgICAgc2VydmljZVR5cGVJZDogbnVsbCwNCiAgICAgICAgcmN0SWQ6IG51bGwsDQogICAgICAgIHJjdE5vOiBudWxsLA0KICAgICAgICBzdXBwbGllcklkOiBudWxsLA0KICAgICAgICBzdXBwbGllclN1bW1hcnk6IG51bGwsDQogICAgICAgIHN1cHBsaWVyQ29udGFjdDogbnVsbCwNCiAgICAgICAgc3VwcGxpZXJUZWw6IG51bGwsDQogICAgICAgIHBheW1lbnRUaXRsZUNvZGU6IG51bGwsDQogICAgICAgIGxvZ2lzdGljc1BheW1lbnRUZXJtc0NvZGU6IG51bGwsDQogICAgICAgIGlucXVpcnlObzogbnVsbCwNCiAgICAgICAgYWdyZWVtZW50VHlwZUNvZGU6IG51bGwsDQogICAgICAgIGFncmVlbWVudE5vOiBudWxsLA0KICAgICAgICBtYXhXZWlnaHQ6IG51bGwsDQogICAgICAgIGlucXVpcnlOb3RpY2U6IG51bGwsDQogICAgICAgIGlucXVpcnlJbm5lclJlbWFyazogbnVsbCwNCiAgICAgICAgaW5xdWlyeVBzYUlkOiBudWxsLA0KICAgICAgICBpbnF1aXJ5TGVhdGVzdFVwZGF0ZWRUaW1lOiBudWxsLA0KICAgICAgICBzZXJ2aWNlQmVsb25nVG86IG51bGwsDQogICAgICAgIGlzRG5TYWxlc0NvbmZpcm1lZDogbnVsbCwNCiAgICAgICAgaXNEbkNsaWVudENvbmZpcm1lZDogbnVsbCwNCiAgICAgICAgaXNEbk9wQ29uZmlybWVkOiBudWxsLA0KICAgICAgICBpc0RuUHNhQ29uZmlybWVkOiBudWxsLA0KICAgICAgICBpc0RuU3VwcGxpZXJDb25maXJtZWQ6IG51bGwsDQogICAgICAgIGlzQWNjb3VudENvbmZpcm1lZDogbnVsbCwNCiAgICAgICAgY29uZmlybUFjY291bnRJZDogbnVsbCwNCiAgICAgICAgYWNjb3VudENvbmZpcm1UaW1lOiBudWxsLA0KICAgICAgICBzYWxlc0NvbmZpcm1lZFRpbWU6IG51bGwsDQogICAgICAgIGNsaWVudENvbmZpcm1lZFRpbWU6IG51bGwsDQogICAgICAgIG9wQ29uZmlybWVkVGltZTogbnVsbCwNCiAgICAgICAgcHNhQ29uZmlybWVkVGltZTogbnVsbCwNCiAgICAgICAgc3VwcGxpZXJDb25maXJtZWRUaW1lOiBudWxsDQogICAgICB9LA0KICAgICAgcnNPcEZ1bWlnYXRpb25TZXJ2aWNlSW5zdGFuY2U6IHsNCiAgICAgICAgc2VydmljZUlkOiBudWxsLA0KICAgICAgICBzZXJ2aWNlVHlwZUlkOiBudWxsLA0KICAgICAgICByY3RJZDogbnVsbCwNCiAgICAgICAgcmN0Tm86IG51bGwsDQogICAgICAgIHN1cHBsaWVySWQ6IG51bGwsDQogICAgICAgIHN1cHBsaWVyU3VtbWFyeTogbnVsbCwNCiAgICAgICAgc3VwcGxpZXJDb250YWN0OiBudWxsLA0KICAgICAgICBzdXBwbGllclRlbDogbnVsbCwNCiAgICAgICAgcGF5bWVudFRpdGxlQ29kZTogbnVsbCwNCiAgICAgICAgbG9naXN0aWNzUGF5bWVudFRlcm1zQ29kZTogbnVsbCwNCiAgICAgICAgaW5xdWlyeU5vOiBudWxsLA0KICAgICAgICBhZ3JlZW1lbnRUeXBlQ29kZTogbnVsbCwNCiAgICAgICAgYWdyZWVtZW50Tm86IG51bGwsDQogICAgICAgIG1heFdlaWdodDogbnVsbCwNCiAgICAgICAgaW5xdWlyeU5vdGljZTogbnVsbCwNCiAgICAgICAgaW5xdWlyeUlubmVyUmVtYXJrOiBudWxsLA0KICAgICAgICBpbnF1aXJ5UHNhSWQ6IG51bGwsDQogICAgICAgIGlucXVpcnlMZWF0ZXN0VXBkYXRlZFRpbWU6IG51bGwsDQogICAgICAgIHNlcnZpY2VCZWxvbmdUbzogbnVsbCwNCiAgICAgICAgaXNEblNhbGVzQ29uZmlybWVkOiBudWxsLA0KICAgICAgICBpc0RuQ2xpZW50Q29uZmlybWVkOiBudWxsLA0KICAgICAgICBpc0RuT3BDb25maXJtZWQ6IG51bGwsDQogICAgICAgIGlzRG5Qc2FDb25maXJtZWQ6IG51bGwsDQogICAgICAgIGlzRG5TdXBwbGllckNvbmZpcm1lZDogbnVsbCwNCiAgICAgICAgaXNBY2NvdW50Q29uZmlybWVkOiBudWxsLA0KICAgICAgICBjb25maXJtQWNjb3VudElkOiBudWxsLA0KICAgICAgICBhY2NvdW50Q29uZmlybVRpbWU6IG51bGwsDQogICAgICAgIHNhbGVzQ29uZmlybWVkVGltZTogbnVsbCwNCiAgICAgICAgY2xpZW50Q29uZmlybWVkVGltZTogbnVsbCwNCiAgICAgICAgb3BDb25maXJtZWRUaW1lOiBudWxsLA0KICAgICAgICBwc2FDb25maXJtZWRUaW1lOiBudWxsLA0KICAgICAgICBzdXBwbGllckNvbmZpcm1lZFRpbWU6IG51bGwNCiAgICAgIH0sDQogICAgICByc09wQ09TZXJ2aWNlSW5zdGFuY2U6IHsNCiAgICAgICAgc2VydmljZUlkOiBudWxsLA0KICAgICAgICBzZXJ2aWNlVHlwZUlkOiBudWxsLA0KICAgICAgICByY3RJZDogbnVsbCwNCiAgICAgICAgcmN0Tm86IG51bGwsDQogICAgICAgIHN1cHBsaWVySWQ6IG51bGwsDQogICAgICAgIHN1cHBsaWVyU3VtbWFyeTogbnVsbCwNCiAgICAgICAgc3VwcGxpZXJDb250YWN0OiBudWxsLA0KICAgICAgICBzdXBwbGllclRlbDogbnVsbCwNCiAgICAgICAgcGF5bWVudFRpdGxlQ29kZTogbnVsbCwNCiAgICAgICAgbG9naXN0aWNzUGF5bWVudFRlcm1zQ29kZTogbnVsbCwNCiAgICAgICAgaW5xdWlyeU5vOiBudWxsLA0KICAgICAgICBhZ3JlZW1lbnRUeXBlQ29kZTogbnVsbCwNCiAgICAgICAgYWdyZWVtZW50Tm86IG51bGwsDQogICAgICAgIG1heFdlaWdodDogbnVsbCwNCiAgICAgICAgaW5xdWlyeU5vdGljZTogbnVsbCwNCiAgICAgICAgaW5xdWlyeUlubmVyUmVtYXJrOiBudWxsLA0KICAgICAgICBpbnF1aXJ5UHNhSWQ6IG51bGwsDQogICAgICAgIGlucXVpcnlMZWF0ZXN0VXBkYXRlZFRpbWU6IG51bGwsDQogICAgICAgIHNlcnZpY2VCZWxvbmdUbzogbnVsbCwNCiAgICAgICAgaXNEblNhbGVzQ29uZmlybWVkOiBudWxsLA0KICAgICAgICBpc0RuQ2xpZW50Q29uZmlybWVkOiBudWxsLA0KICAgICAgICBpc0RuT3BDb25maXJtZWQ6IG51bGwsDQogICAgICAgIGlzRG5Qc2FDb25maXJtZWQ6IG51bGwsDQogICAgICAgIGlzRG5TdXBwbGllckNvbmZpcm1lZDogbnVsbCwNCiAgICAgICAgaXNBY2NvdW50Q29uZmlybWVkOiBudWxsLA0KICAgICAgICBjb25maXJtQWNjb3VudElkOiBudWxsLA0KICAgICAgICBhY2NvdW50Q29uZmlybVRpbWU6IG51bGwsDQogICAgICAgIHNhbGVzQ29uZmlybWVkVGltZTogbnVsbCwNCiAgICAgICAgY2xpZW50Q29uZmlybWVkVGltZTogbnVsbCwNCiAgICAgICAgb3BDb25maXJtZWRUaW1lOiBudWxsLA0KICAgICAgICBwc2FDb25maXJtZWRUaW1lOiBudWxsLA0KICAgICAgICBzdXBwbGllckNvbmZpcm1lZFRpbWU6IG51bGwNCiAgICAgIH0sDQogICAgICByc09wT3RoZXJTZXJ2aWNlSW5zdGFuY2U6IHsNCiAgICAgICAgc2VydmljZUlkOiBudWxsLA0KICAgICAgICBzZXJ2aWNlVHlwZUlkOiBudWxsLA0KICAgICAgICByY3RJZDogbnVsbCwNCiAgICAgICAgcmN0Tm86IG51bGwsDQogICAgICAgIHN1cHBsaWVySWQ6IG51bGwsDQogICAgICAgIHN1cHBsaWVyU3VtbWFyeTogbnVsbCwNCiAgICAgICAgc3VwcGxpZXJDb250YWN0OiBudWxsLA0KICAgICAgICBzdXBwbGllclRlbDogbnVsbCwNCiAgICAgICAgcGF5bWVudFRpdGxlQ29kZTogbnVsbCwNCiAgICAgICAgbG9naXN0aWNzUGF5bWVudFRlcm1zQ29kZTogbnVsbCwNCiAgICAgICAgaW5xdWlyeU5vOiBudWxsLA0KICAgICAgICBhZ3JlZW1lbnRUeXBlQ29kZTogbnVsbCwNCiAgICAgICAgYWdyZWVtZW50Tm86IG51bGwsDQogICAgICAgIG1heFdlaWdodDogbnVsbCwNCiAgICAgICAgaW5xdWlyeU5vdGljZTogbnVsbCwNCiAgICAgICAgaW5xdWlyeUlubmVyUmVtYXJrOiBudWxsLA0KICAgICAgICBpbnF1aXJ5UHNhSWQ6IG51bGwsDQogICAgICAgIGlucXVpcnlMZWF0ZXN0VXBkYXRlZFRpbWU6IG51bGwsDQogICAgICAgIHNlcnZpY2VCZWxvbmdUbzogbnVsbCwNCiAgICAgICAgaXNEblNhbGVzQ29uZmlybWVkOiBudWxsLA0KICAgICAgICBpc0RuQ2xpZW50Q29uZmlybWVkOiBudWxsLA0KICAgICAgICBpc0RuT3BDb25maXJtZWQ6IG51bGwsDQogICAgICAgIGlzRG5Qc2FDb25maXJtZWQ6IG51bGwsDQogICAgICAgIGlzRG5TdXBwbGllckNvbmZpcm1lZDogbnVsbCwNCiAgICAgICAgaXNBY2NvdW50Q29uZmlybWVkOiBudWxsLA0KICAgICAgICBjb25maXJtQWNjb3VudElkOiBudWxsLA0KICAgICAgICBhY2NvdW50Q29uZmlybVRpbWU6IG51bGwsDQogICAgICAgIHNhbGVzQ29uZmlybWVkVGltZTogbnVsbCwNCiAgICAgICAgY2xpZW50Q29uZmlybWVkVGltZTogbnVsbCwNCiAgICAgICAgb3BDb25maXJtZWRUaW1lOiBudWxsLA0KICAgICAgICBwc2FDb25maXJtZWRUaW1lOiBudWxsLA0KICAgICAgICBzdXBwbGllckNvbmZpcm1lZFRpbWU6IG51bGwNCiAgICAgIH0sDQogICAgICBsaXN0OiBuZXcgU2V0KCksDQogICAgICAvLyDpgLvovpHmlbDmja7vvIzmjqfliLZ1aSjmjqfliLbmipjlj6ApDQogICAgICBjbGllbnRNZXNzYWdlOiB0cnVlLA0KICAgICAgYmFzaWNMb2dpc3RpY3M6IGZhbHNlLA0KICAgICAgcHJlQ2FycmlhZ2U6IGZhbHNlLA0KICAgICAgZXhwb3J0RGVjbGFyYXRpb246IGZhbHNlLA0KICAgICAgaW1wb3J0Q2xlYXJhbmNlOiBmYWxzZSwNCiAgICAgIC8vIFVJ5oqY5Y+g5o6n5Yi277yIbmV377yJDQogICAgICByc09wU2VhbEZjbEZvbGQ6IGZhbHNlLA0KICAgICAgb3RoZXJGb2xkOiB0cnVlLA0KICAgICAgcnNPcFNlYWxMY2xGb2xkOiBmYWxzZSwNCiAgICAgIHJzT3BCdWxrU2hpcEZvbGQ6IGZhbHNlLA0KICAgICAgcnNPcFJvcm9TaGlwRm9sZDogZmFsc2UsDQogICAgICByc09wQWlyRm9sZDogZmFsc2UsDQogICAgICByc09wUmFpbEZvbGQ6IGZhbHNlLA0KICAgICAgcnNPcFJhaWxGY2xGb2xkOiBmYWxzZSwNCiAgICAgIHJzT3BSYWlsTGNsRm9sZDogZmFsc2UsDQogICAgICByc09wRXhwcmVzc0ZvbGQ6IGZhbHNlLA0KICAgICAgcnNPcFBvcnRTZXJ2aWNlRm9sZDogZmFsc2UsDQogICAgICByc09wRXhwb3J0VHJ1Y2tGb2xkOiBmYWxzZSwNCiAgICAgIHJzT3BDdG5yVHJ1Y2tGb2xkOiBmYWxzZSwNCiAgICAgIHJzT3BCdWxrVHJ1Y2tGb2xkOiBmYWxzZSwNCiAgICAgIHJzT3BFeHBvcnRDdXN0b21zQ2xlYXJhbmNlRm9sZDogZmFsc2UsDQogICAgICByc09wRG9jRGVjbGFyZUZvbGQ6IGZhbHNlLA0KICAgICAgcnNPcERPQWdlbnRGb2xkOiBmYWxzZSwNCiAgICAgIHJzT3BDbGVhckFnZW50Rm9sZDogZmFsc2UsDQogICAgICByc09wRnJlZURlY2xhcmVGb2xkOiBmYWxzZSwNCiAgICAgIHJzT3BJbXBvcnRDdXN0b21zQ2xlYXJhbmNlRm9sZDogZmFsc2UsDQogICAgICByc09wSW1wb3J0RGlzcGF0Y2hUcnVja0ZvbGQ6IGZhbHNlLA0KICAgICAgcnNPcFdhcmVob3VzZUZvbGQ6IGZhbHNlLA0KICAgICAgcnNPcEluc3BlY3Rpb25BbmRDZXJ0aWZpY2F0ZUZvbGQ6IGZhbHNlLA0KICAgICAgcnNPcExhbmRGb2xkOiBmYWxzZSwNCiAgICAgIHJzT3BJbnN1cmFuY2VGb2xkOiBmYWxzZSwNCiAgICAgIHJzT3BFeHBhbmRTZXJ2aWNlRm9sZDogZmFsc2UsDQogICAgICByc09wV0hTRm9sZDogZmFsc2UsDQogICAgICByc09wM3JkQ2VydEZvbGQ6IGZhbHNlLA0KICAgICAgcnNPcElOU0ZvbGQ6IGZhbHNlLA0KICAgICAgcnNPcFRyYWRpbmdGb2xkOiBmYWxzZSwNCiAgICAgIHJzT3BGdW1pZ2F0aW9uRm9sZDogZmFsc2UsDQogICAgICByc09wQ09Gb2xkOiBmYWxzZSwNCiAgICAgIHJzT3BPdGhlckZvbGQ6IGZhbHNlLA0KDQogICAgICAvL+aYr+WQpuWxleekug0KICAgICAgYmFzaWNJbmZvOiB0cnVlLA0KICAgICAgb3JkZXJJbmZvOiB0cnVlLA0KICAgICAgYnJhbmNoSW5mbzogdHJ1ZSwNCiAgICAgIHNlcnZpY2VJbmZvOiBmYWxzZSwNCiAgICAgIGxvZ2lzdGljc0luZm86IGZhbHNlLA0KICAgICAgZG9jSW5mbzogZmFsc2UsDQogICAgICBjaGFyZ2VJbmZvOiBmYWxzZSwNCiAgICAgIGF1ZGl0SW5mbzogZmFsc2UsDQogICAgICBjaGFyZ2VPcGVuOiBmYWxzZSwNCiAgICAgIGNoYXJnZVNlYXJjaERhdGE6IHt9LA0KICAgICAgY2hhcmdlU2VsZWN0SXRlbTogbnVsbCwNCiAgICAgIC8vIOWQhOS4quW6lOaUtuOAgeW6lOS7mOOAgeWIqea2puetiQ0KICAgICAgcnNDbGllbnRNZXNzYWdlUmVjZWl2YWJsZTogMCwNCiAgICAgIHJzQ2xpZW50TWVzc2FnZVJlY2VpdmFibGVSTUI6IDAsDQogICAgICByc0NsaWVudE1lc3NhZ2VSZWNlaXZhYmxlVVNEOiAwLA0KICAgICAgcnNDbGllbnRNZXNzYWdlUGF5YWJsZTogMCwNCiAgICAgIHJzQ2xpZW50TWVzc2FnZUNoYXJnZURhdGE6IHt9LA0KICAgICAgcnNDbGllbnRNZXNzYWdlUGF5YWJsZVJNQjogMCwNCiAgICAgIHJzQ2xpZW50TWVzc2FnZVBheWFibGVVU0Q6IDAsDQogICAgICByc0NsaWVudE1lc3NhZ2VQYXlhYmxlVGF4Uk1COiAwLA0KICAgICAgcnNDbGllbnRNZXNzYWdlUGF5YWJsZVRheFVTRDogMCwNCiAgICAgIHNxZFVucmVjZWl2ZWRSbWJTdW06IDAsDQogICAgICBzcWRVbnJlY2VpdmVkVXNkU3VtOiAwLA0KICAgICAgc3FkVW5wYWlkUm1iU3VtOiAwLA0KICAgICAgc3FkVW5wYWlkVXNkU3VtOiAwLA0KICAgICAgcnNDbGllbnRNZXNzYWdlUHJvZml0Uk1COiAwLA0KICAgICAgcnNDbGllbnRNZXNzYWdlUHJvZml0VVNEOiAwLA0KICAgICAgcnNCYXNpY0xvZ2lzdGljc1BheWFibGU6IDAsDQogICAgICByc1ByZWNhcnJpYWdlUGF5YWJsZTogMCwNCiAgICAgIHJzRXhwb3J0Q3VzdG9tc1BheWFibGU6IDAsDQogICAgICByc0ltcG9ydEN1c3RvbXNQYXlhYmxlOiAwLA0KICAgICAgcnNDbGllbnRNZXNzYWdlUHJvZml0VGF4Uk1COiAwLA0KICAgICAgcnNDbGllbnRNZXNzYWdlUHJvZml0VGF4VVNEOiAwLA0KDQogICAgICByc09wU2VhRmNsUGF5YWJsZTogMCwNCiAgICAgIHJzT3BTZWFMY2xQYXlhYmxlOiAwLA0KICAgICAgcnNPcE90aGVyUGF5YWJsZVVTRDogMCwNCiAgICAgIHJzT3BPdGhlclBheWFibGVSTUI6IDAsDQogICAgICByc09wQnVsa1NoaXBQYXlhYmxlOiAwLA0KICAgICAgcnNPcFJvcm9TaGlwUGF5YWJsZTogMCwNCiAgICAgIHJzT3BBaXJQYXlhYmxlUk1COiAwLA0KICAgICAgcnNPcEFpclBheWFibGVVU0Q6IDAsDQogICAgICByc09wUmFpbFBheWFibGU6IDAsDQogICAgICByc09wUmFpbEZjbFBheWFibGVSTUI6IDAsDQogICAgICByc09wUmFpbEZjbFBheWFibGVVU0Q6IDAsDQogICAgICByc09wUmFpbExjbFBheWFibGVSTUI6IDAsDQogICAgICByc09wUmFpbExjbFBheWFibGVVU0Q6IDAsDQogICAgICByc09wRXhwcmVzc1BheWFibGVSTUI6IDAsDQogICAgICByc09wRXhwcmVzc1BheWFibGVVU0Q6IDAsDQogICAgICByc09wUG9ydFNlcnZpY2VQYXlhYmxlOiAwLA0KICAgICAgcnNPcEV4cG9ydFRydWNrUGF5YWJsZTogMCwNCiAgICAgIHJzT3BFeHBvcnRDdXN0b21zQ2xlYXJhbmNlUGF5YWJsZTogMCwNCiAgICAgIHJzT3BJbXBvcnRDdXN0b21zQ2xlYXJhbmNlUGF5YWJsZTogMCwNCiAgICAgIHJzT3BJbXBvcnREaXNwYXRjaFRydWNrUGF5YWJsZTogMCwNCiAgICAgIHJzT3BXYXJlaG91c2VQYXlhYmxlOiAwLA0KICAgICAgcnNPcEluc3BlY3Rpb25BbmRDZXJ0aWZpY2F0ZVBheWFibGU6IDAsDQogICAgICByc09wTGFuZFBheWFibGU6IDAsDQogICAgICByc09wSW5zdXJhbmNlUGF5YWJsZTogMCwNCiAgICAgIHJzT3BDdG5yVHJ1Y2tQYXlhYmxlUk1COiAwLA0KICAgICAgcnNPcEN0bnJUcnVja1BheWFibGVVU0Q6IDAsDQogICAgICByc09wQnVsa1RydWNrUGF5YWJsZVJNQjogMCwNCiAgICAgIHJzT3BCdWxrVHJ1Y2tQYXlhYmxlVVNEOiAwLA0KICAgICAgcnNPcERvY0RlY2xhcmVQYXlhYmxlOiAwLA0KICAgICAgcnNPcEZyZWVEZWNsYXJlUGF5YWJsZTogMCwNCiAgICAgIHJzT3BET0FnZW50UGF5YWJsZVJNQjogMCwNCiAgICAgIHJzT3BET0FnZW50UGF5YWJsZVVTRDogMCwNCiAgICAgIHJzT3BDbGVhckFnZW50UGF5YWJsZVJNQjogMCwNCiAgICAgIHJzT3BDbGVhckFnZW50UGF5YWJsZVVTRDogMCwNCiAgICAgIHJzT3BXSFNQYXlhYmxlUk1COiAwLA0KICAgICAgcnNPcFdIU1BheWFibGVVU0Q6IDAsDQogICAgICByc09wM3JkQ2VydFBheWFibGVSTUI6IDAsDQogICAgICByc09wM3JkQ2VydFBheWFibGVVU0Q6IDAsDQogICAgICByc09wSU5TUGF5YWJsZVJNQjogMCwNCiAgICAgIHJzT3BJTlNQYXlhYmxlVVNEOiAwLA0KICAgICAgcnNPcFRyYWRpbmdQYXlhYmxlUk1COiAwLA0KICAgICAgcnNPcFRyYWRpbmdQYXlhYmxlVVNEOiAwLA0KICAgICAgcnNPcEZ1bWlnYXRpb25QYXlhYmxlUk1COiAwLA0KICAgICAgcnNPcEZ1bWlnYXRpb25QYXlhYmxlVVNEOiAwLA0KICAgICAgcnNPcENPUGF5YWJsZVJNQjogMCwNCiAgICAgIHJzT3BDT1BheWFibGVVU0Q6IDAsDQogICAgICAvLyDlupTku5jvvIhuZXfvvIkNCg0KICAgICAgLy8g5a6i5oi35L+h5oGv5Lit55qE5a6h5qC45L+h5oGvDQogICAgICBvcENvbmZpcm1lZE5hbWU6IG51bGwsDQogICAgICBvcENvbmZpcm1lZERhdGU6IG51bGwsDQogICAgICBhY2NvdW50Q29uZmlybWVkTmFtZTogbnVsbCwNCiAgICAgIGFjY291bnRDb25maXJtZWREYXRlOiBudWxsLA0KICAgICAgcHNhQ29uZmlybWVkTmFtZTogbnVsbCwNCiAgICAgIHBzYUNvbmZpcm1lZERhdGU6IG51bGwsDQogICAgICBzYWxlc0NvbmZpcm1lZE5hbWU6IG51bGwsDQogICAgICBzYWxlc0NvbmZpcm1lZERhdGU6IG51bGwsDQogICAgICBjbGllbnRDb25maXJtZWROYW1lOiBudWxsLA0KICAgICAgY2xpZW50Q29uZmlybWVkRGF0ZTogbnVsbCwNCiAgICAgIFJlbGF0aW9uQ2xpZW50SWRMaXN0OiBbXSwNCiAgICAgIFBheW1lbnRUaXRsZUNvZGU6IG51bGwsDQogICAgICBzYWxlc0lkOiBudWxsLA0KICAgICAgc2FsZXNBc3Npc3RhbnRJZDogbnVsbCwNCiAgICAgIHNhbGVzT2JzZXJ2ZXJJZDogbnVsbCwNCiAgICAgIHZlcmlmeVBzYUlkOiBudWxsLA0KICAgICAgYm9va2luZ09wSWQ6IG51bGwsDQogICAgICBvcElkOiBudWxsLA0KICAgICAgZG9jT3BJZDogbnVsbCwNCiAgICAgIG9wT2JzZXJ2ZXJJZDogbnVsbCwNCiAgICAgIGNhcnJpZXJJZDogbnVsbCwNCiAgICAgIGJhc2ljU2VydmljZU5hbWU6IFtdLA0KICAgICAgUmVsYXRpb25DbGllbnRMaXN0OiBbXSwNCiAgICAgIHJlbGF0aW9uQ2xpZW50TGlzdHM6IFtdLA0KICAgICAgYmFzaWNTZXJ2aWNlSWQ6IFtdLA0KICAgICAgb3BlbkZyZWlnaHRTZWxlY3Q6IGZhbHNlLA0KICAgICAgb3BlbkNvbXBhbnlTZWxlY3Q6IGZhbHNlLA0KICAgICAgb3BlbkNvbW1vblVzZWRTZWxlY3Q6IGZhbHNlLA0KICAgICAgb3BlblBzYUJvb2tpbmdTZWxlY3Q6IGZhbHNlLA0KICAgICAgZnJlaWdodFNlbGVjdERhdGE6IHt9LA0KICAgICAgY3VyRnJlaWdodFNlbGVjdFJvdzoge30sDQogICAgICBwc2FCb29raW5nU2VsZWN0RGF0YToge30sDQogICAgICBjb21tb25Vc2VkU2VsZWN0RGF0YToge30sDQogICAgICBjb21tb25Vc2VkVHlwZTogbnVsbCwNCiAgICAgIFNFQTogbmV3IFNldCgpLA0KICAgICAgU0VBRkNMOiBuZXcgU2V0KCksDQogICAgICBTRUFMQ0w6IG5ldyBTZXQoKSwNCiAgICAgIEFJUjogbmV3IFNldCgpLA0KICAgICAgUkFJTDogbmV3IFNldCgpLA0KICAgICAgRVhQUkVTUzogbmV3IFNldCgpLA0KICAgICAgVFJVQ0s6IG5ldyBTZXQoKSwNCiAgICAgIENVU1RPTTogbmV3IFNldCgpLA0KICAgICAgQ0xFQVI6IG5ldyBTZXQoKSwNCiAgICAgIFdIUzogbmV3IFNldCgpLA0KICAgICAgRVhURU5EOiBuZXcgU2V0KCksDQogICAgICBvcGVuR2VuZXJhdGVSZXZlbnVlVG9uczogZmFsc2UsDQogICAgICBvcGVuQm9va2luZ01lc3NhZ2U6IGZhbHNlLA0KICAgICAgYm9va2luZ01lc3NhZ2VUaXRsZTogbnVsbCwNCiAgICAgIHN1cHBsaWVyTGlzdDogW10sDQogICAgICBib29raW5nTWVzc2FnZUxpc3Q6IFtdLA0KICAgICAgYm9va2luZ01lc3NhZ2VGb3JtOiB7fSwNCiAgICAgIGJvb2tpbmdCaWxsUHJpbnRSb3c6IG51bGwsDQogICAgICBib29raW5nTWVzc2FnZVN0YXR1czogbnVsbCwNCiAgICAgIHNlbGVjdGVkUHJpbnRDaGFyZ2VzOiBbXSwNCiAgICAgIGN0bnJUeXBlQ29kZUlkczogW10sDQogICAgICBjYXJnb1R5cGVDb2RlczogW10sDQogICAgICBzaG93UHNhUmN0OiBmYWxzZSwNCiAgICAgIHJzU2VydmljZUluc3RhbmNlczogew0KICAgICAgICBhY2NvdW50Q29uZmlybVRpbWU6IG51bGwsDQogICAgICAgIGFncmVlbWVudE5vOiAiIiwNCiAgICAgICAgYWdyZWVtZW50VHlwZUNvZGU6IG51bGwsDQogICAgICAgIGNsaWVudENvbmZpcm1lZFRpbWU6IG51bGwsDQogICAgICAgIGNvbmZpcm1BY2NvdW50SWQ6IG51bGwsDQogICAgICAgIGNyZWF0ZUJ5OiBudWxsLA0KICAgICAgICBjcmVhdGVCeU5hbWU6IG51bGwsDQogICAgICAgIGNyZWF0ZVRpbWU6IG51bGwsDQogICAgICAgIGRlbGV0ZUJ5OiBudWxsLA0KICAgICAgICBkZWxldGVCeU5hbWU6IG51bGwsDQogICAgICAgIGRlbGV0ZVN0YXR1czogbnVsbCwNCiAgICAgICAgZGVsZXRlVGltZTogbnVsbCwNCiAgICAgICAgaW5xdWlyeUlubmVyUmVtYXJrOiAiIiwNCiAgICAgICAgaW5xdWlyeUxlYXRlc3RVcGRhdGVkVGltZTogbnVsbCwNCiAgICAgICAgaW5xdWlyeU5vOiBudWxsLA0KICAgICAgICBpbnF1aXJ5Tm90aWNlOiBudWxsLA0KICAgICAgICBpbnF1aXJ5UHNhSWQ6IG51bGwsDQogICAgICAgIGlzQWNjb3VudENvbmZpcm1lZDogbnVsbCwNCiAgICAgICAgaXNEbkNsaWVudENvbmZpcm1lZDogbnVsbCwNCiAgICAgICAgaXNEbk9wQ29uZmlybWVkOiBudWxsLA0KICAgICAgICBpc0RuUHNhQ29uZmlybWVkOiBudWxsLA0KICAgICAgICBpc0RuU2FsZXNDb25maXJtZWQ6IG51bGwsDQogICAgICAgIGlzRG5TdXBwbGllckNvbmZpcm1lZDogbnVsbCwNCiAgICAgICAgbG9naXN0aWNzUGF5bWVudFRlcm1zQ29kZTogbnVsbCwNCiAgICAgICAgbWF4V2VpZ2h0OiBudWxsLA0KICAgICAgICBvcENvbmZpcm1lZFRpbWU6IG51bGwsDQogICAgICAgIHBheW1lbnRUaXRsZUNvZGU6IG51bGwsDQogICAgICAgIHBlcm1pc3Npb25MZXZlbDogbnVsbCwNCiAgICAgICAgcHNhQ29uZmlybWVkVGltZTogbnVsbCwNCiAgICAgICAgcmN0SWQ6IG51bGwsDQogICAgICAgIHJjdE5vOiBudWxsLA0KICAgICAgICByZW1hcms6IG51bGwsDQogICAgICAgIHNhbGVzQ29uZmlybWVkVGltZTogbnVsbCwNCiAgICAgICAgc2VydmljZUJlbG9uZ1RvOiBudWxsLA0KICAgICAgICBzZXJ2aWNlSWQ6IG51bGwsDQogICAgICAgIHNlcnZpY2VUeXBlSWQ6IG51bGwsDQogICAgICAgIHN1cHBsaWVyQ29uZmlybWVkVGltZTogbnVsbCwNCiAgICAgICAgc3VwcGxpZXJDb250YWN0OiBudWxsLA0KICAgICAgICBzdXBwbGllcklkOiBudWxsLA0KICAgICAgICBzdXBwbGllck5hbWU6IG51bGwsDQogICAgICAgIHN1cHBsaWVyU3VtbWFyeTogbnVsbCwNCiAgICAgICAgc3VwcGxpZXJUZWw6IG51bGwsDQogICAgICAgIHVwZGF0ZUJ5OiBudWxsLA0KICAgICAgICB1cGRhdGVCeU5hbWU6IG51bGwsDQogICAgICAgIHVwZGF0ZVRpbWU6IG51bGwsDQogICAgICAgIHNlcnZpY2VGb2xkOiBmYWxzZQ0KICAgICAgfSwNCiAgICAgIGN1clBzYVJvdzogbnVsbA0KICAgIH0NCiAgfSwNCiAgd2F0Y2g6IHsNCiAgICAiZm9ybS5sb2dpc3RpY3NUeXBlSWQiKG4pIHsNCiAgICAgIGlmICh0aGlzLiRzdG9yZS5zdGF0ZS5kYXRhLnNlcnZpY2VUeXBlTGlzdC5sZW5ndGggPT0gMCB8fCB0aGlzLiRzdG9yZS5zdGF0ZS5kYXRhLnJlZGlzTGlzdC5zZXJ2aWNlVHlwZSkgew0KICAgICAgICBzdG9yZS5kaXNwYXRjaCgiZ2V0U2VydmljZVR5cGVMaXN0IikudGhlbigoKSA9PiB7DQogICAgICAgICAgdGhpcy5nZXRUeXBlKG4pDQogICAgICAgIH0pDQogICAgICB9IGVsc2Ugew0KICAgICAgICB0aGlzLmdldFR5cGUobikNCiAgICAgIH0NCiAgICB9LA0KICAgICJyc0NsaWVudE1lc3NhZ2UucnNDaGFyZ2VMaXN0IihuLCByc09wU2VydmljZSkgew0KICAgICAgLy8g6K6h566X5a6i5oi35L+h5oGv5Lit55qE5bqU5pS2DQogICAgICBsZXQgcmVjZWl2YWJsZVJNQiA9IDANCiAgICAgIGxldCByZWNlaXZhYmxlVVNEID0gMA0KICAgICAgbGV0IHJlY2VpdmFibGVUYXhSTUIgPSAwDQogICAgICBsZXQgcmVjZWl2YWJsZVRheFVTRCA9IDANCg0KICAgICAgLy8g6K6h566X5a6i5oi35L+h5oGv5Lit55qE5Yip5ramDQogICAgICBsZXQgcGF5YWJsZVJNQiA9IDANCiAgICAgIGxldCBwYXlhYmxlVVNEID0gMA0KICAgICAgbGV0IHBheWFibGVUYXhSTUIgPSAwDQogICAgICBsZXQgcGF5YWJsZVRheFVTRCA9IDANCg0KICAgICAgLy8g6K6h566X5a6i5oi35L+h5oGv5Lit55qE5pyq5pS2L+acquS7mA0KICAgICAgbGV0IHVucmVjZWl2ZWRSbWJTdW0gPSAwDQogICAgICBsZXQgdW5yZWNlaXZlZFVzZFN1bSA9IDANCiAgICAgIGxldCB1bnBhaWRSbWJTdW0gPSAwDQogICAgICBsZXQgdW5wYWlkVXNkU3VtID0gMA0KICAgICAgbi5tYXAocnNDaGFyZ2UgPT4gew0KICAgICAgICBpZiAocnNDaGFyZ2Uuc3VidG90YWwpIHsNCiAgICAgICAgICBpZiAocnNDaGFyZ2UuZG5DdXJyZW5jeUNvZGUgPT09ICJVU0QiKSB7DQogICAgICAgICAgICByZWNlaXZhYmxlVVNEID0gY3VycmVuY3kocmVjZWl2YWJsZVVTRCkuYWRkKGN1cnJlbmN5KHJzQ2hhcmdlLnN1YnRvdGFsKS5kaXZpZGUoY3VycmVuY3koMSkuYWRkKGN1cnJlbmN5KHJzQ2hhcmdlLmR1dHlSYXRlKS5kaXZpZGUoMTAwKSkpKS52YWx1ZQ0KICAgICAgICAgICAgcmVjZWl2YWJsZVRheFVTRCA9IGN1cnJlbmN5KHJlY2VpdmFibGVUYXhVU0QpLmFkZChyc0NoYXJnZS5zdWJ0b3RhbCkudmFsdWUNCiAgICAgICAgICAgIHVucmVjZWl2ZWRVc2RTdW0gPSBjdXJyZW5jeSh1bnJlY2VpdmVkVXNkU3VtKS5hZGQocnNDaGFyZ2Uuc3FkRG5DdXJyZW5jeUJhbGFuY2UpLnZhbHVlDQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIHJlY2VpdmFibGVSTUIgPSBjdXJyZW5jeShyZWNlaXZhYmxlUk1CKS5hZGQoY3VycmVuY3kocnNDaGFyZ2Uuc3VidG90YWwpLmRpdmlkZShjdXJyZW5jeSgxKS5hZGQoY3VycmVuY3kocnNDaGFyZ2UuZHV0eVJhdGUpLmRpdmlkZSgxMDApKSkpLnZhbHVlDQogICAgICAgICAgICByZWNlaXZhYmxlVGF4Uk1CID0gY3VycmVuY3kocmVjZWl2YWJsZVRheFJNQikuYWRkKHJzQ2hhcmdlLnN1YnRvdGFsKS52YWx1ZQ0KICAgICAgICAgICAgdW5yZWNlaXZlZFJtYlN1bSA9IGN1cnJlbmN5KHVucmVjZWl2ZWRSbWJTdW0pLmFkZChyc0NoYXJnZS5zcWREbkN1cnJlbmN5QmFsYW5jZSkudmFsdWUNCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgIH0pDQogICAgICB0aGlzLnJzQ2xpZW50TWVzc2FnZVJlY2VpdmFibGVSTUIgPSByZWNlaXZhYmxlUk1CDQogICAgICB0aGlzLnJzQ2xpZW50TWVzc2FnZVJlY2VpdmFibGVVU0QgPSByZWNlaXZhYmxlVVNEDQogICAgICB0aGlzLnJzQ2xpZW50TWVzc2FnZVJlY2VpdmFibGVUYXhSTUIgPSByZWNlaXZhYmxlVGF4Uk1CDQogICAgICB0aGlzLnJzQ2xpZW50TWVzc2FnZVJlY2VpdmFibGVUYXhVU0QgPSByZWNlaXZhYmxlVGF4VVNEDQogICAgICB0aGlzLnNxZFVucmVjZWl2ZWRSbWJTdW0gPSB1bnJlY2VpdmVkUm1iU3VtDQogICAgICB0aGlzLnNxZFVucmVjZWl2ZWRVc2RTdW0gPSB1bnJlY2VpdmVkVXNkU3VtDQoNCiAgICAgIC8vIOaVtOafnOa1t+i/kOi0ueeUqA0KICAgICAgdGhpcy5mb3JtLnJzT3BTZWFGY2xMaXN0ID8gdGhpcy5mb3JtLnJzT3BTZWFGY2xMaXN0Lm1hcChpdGVtID0+IHsNCiAgICAgICAgaXRlbS5yc0NoYXJnZUxpc3QubWFwKHJzQ2hhcmdlID0+IHsNCiAgICAgICAgICBpZiAocnNDaGFyZ2Uuc3VidG90YWwpIHsNCiAgICAgICAgICAgIGlmIChyc0NoYXJnZS5kbkN1cnJlbmN5Q29kZSA9PT0gIlVTRCIpIHsNCiAgICAgICAgICAgICAgcGF5YWJsZVVTRCA9IGN1cnJlbmN5KHBheWFibGVVU0QpLmFkZChjdXJyZW5jeShyc0NoYXJnZS5zdWJ0b3RhbCkuZGl2aWRlKGN1cnJlbmN5KDEpLmFkZChjdXJyZW5jeShyc0NoYXJnZS5kdXR5UmF0ZSkuZGl2aWRlKDEwMCkpKSkudmFsdWUNCiAgICAgICAgICAgICAgcGF5YWJsZVRheFVTRCA9IGN1cnJlbmN5KHBheWFibGVUYXhVU0QpLmFkZChyc0NoYXJnZS5zdWJ0b3RhbCkudmFsdWUNCiAgICAgICAgICAgICAgdW5wYWlkVXNkU3VtID0gY3VycmVuY3kodW5wYWlkVXNkU3VtKS5hZGQocnNDaGFyZ2Uuc3FkRG5DdXJyZW5jeUJhbGFuY2UpLnZhbHVlDQogICAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgICBwYXlhYmxlUk1CID0gY3VycmVuY3kocGF5YWJsZVJNQikuYWRkKGN1cnJlbmN5KHJzQ2hhcmdlLnN1YnRvdGFsKS5kaXZpZGUoY3VycmVuY3koMSkuYWRkKGN1cnJlbmN5KHJzQ2hhcmdlLmR1dHlSYXRlKS5kaXZpZGUoMTAwKSkpKS52YWx1ZQ0KICAgICAgICAgICAgICBwYXlhYmxlVGF4Uk1CID0gY3VycmVuY3kocGF5YWJsZVRheFJNQikuYWRkKHJzQ2hhcmdlLnN1YnRvdGFsKS52YWx1ZQ0KICAgICAgICAgICAgICB1bnBhaWRSbWJTdW0gPSBjdXJyZW5jeSh1bnBhaWRSbWJTdW0pLmFkZChyc0NoYXJnZS5zcWREbkN1cnJlbmN5QmFsYW5jZSkudmFsdWUNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9DQogICAgICAgIH0pDQogICAgICB9KSA6IG51bGwNCiAgICAgIC8vIOaLvOafnOa1t+i/kOi0ueeUqA0KICAgICAgdGhpcy5mb3JtLnJzT3BTZWFMY2xMaXN0ID8gdGhpcy5mb3JtLnJzT3BTZWFMY2xMaXN0Lm1hcChpdGVtID0+IHsNCiAgICAgICAgaXRlbS5yc0NoYXJnZUxpc3QubWFwKHJzQ2hhcmdlID0+IHsNCiAgICAgICAgICBpZiAocnNDaGFyZ2Uuc3VidG90YWwpIHsNCiAgICAgICAgICAgIGlmIChyc0NoYXJnZS5kbkN1cnJlbmN5Q29kZSA9PT0gIlVTRCIpIHsNCiAgICAgICAgICAgICAgcGF5YWJsZVVTRCA9IGN1cnJlbmN5KHBheWFibGVVU0QpLmFkZChjdXJyZW5jeShyc0NoYXJnZS5zdWJ0b3RhbCkuZGl2aWRlKGN1cnJlbmN5KDEpLmFkZChjdXJyZW5jeShyc0NoYXJnZS5kdXR5UmF0ZSkuZGl2aWRlKDEwMCkpKSkudmFsdWUNCiAgICAgICAgICAgICAgcGF5YWJsZVRheFVTRCA9IGN1cnJlbmN5KHBheWFibGVUYXhVU0QpLmFkZChyc0NoYXJnZS5zdWJ0b3RhbCkudmFsdWUNCiAgICAgICAgICAgICAgdW5wYWlkVXNkU3VtID0gY3VycmVuY3kodW5wYWlkVXNkU3VtKS5hZGQocnNDaGFyZ2Uuc3FkRG5DdXJyZW5jeUJhbGFuY2UpLnZhbHVlDQogICAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgICBwYXlhYmxlUk1CID0gY3VycmVuY3kocGF5YWJsZVJNQikuYWRkKGN1cnJlbmN5KHJzQ2hhcmdlLnN1YnRvdGFsKS5kaXZpZGUoY3VycmVuY3koMSkuYWRkKGN1cnJlbmN5KHJzQ2hhcmdlLmR1dHlSYXRlKS5kaXZpZGUoMTAwKSkpKS52YWx1ZQ0KICAgICAgICAgICAgICBwYXlhYmxlVGF4Uk1CID0gY3VycmVuY3kocGF5YWJsZVRheFJNQikuYWRkKHJzQ2hhcmdlLnN1YnRvdGFsKS52YWx1ZQ0KICAgICAgICAgICAgICB1bnBhaWRSbWJTdW0gPSBjdXJyZW5jeSh1bnBhaWRSbWJTdW0pLmFkZChyc0NoYXJnZS5zcWREbkN1cnJlbmN5QmFsYW5jZSkudmFsdWUNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9DQogICAgICAgIH0pDQogICAgICB9KSA6IG51bGwNCiAgICAgIC8vIOepuui/kOi0ueeUqA0KICAgICAgdGhpcy5mb3JtLnJzT3BBaXJMaXN0ID8gdGhpcy5mb3JtLnJzT3BBaXJMaXN0Lm1hcChpdGVtID0+IHsNCiAgICAgICAgaXRlbS5yc0NoYXJnZUxpc3QubWFwKHJzQ2hhcmdlID0+IHsNCiAgICAgICAgICBpZiAocnNDaGFyZ2Uuc3VidG90YWwpIHsNCiAgICAgICAgICAgIGlmIChyc0NoYXJnZS5kbkN1cnJlbmN5Q29kZSA9PT0gIlVTRCIpIHsNCiAgICAgICAgICAgICAgcGF5YWJsZVVTRCA9IGN1cnJlbmN5KHBheWFibGVVU0QpLmFkZChjdXJyZW5jeShyc0NoYXJnZS5zdWJ0b3RhbCkuZGl2aWRlKGN1cnJlbmN5KDEpLmFkZChjdXJyZW5jeShyc0NoYXJnZS5kdXR5UmF0ZSkuZGl2aWRlKDEwMCkpKSkudmFsdWUNCiAgICAgICAgICAgICAgcGF5YWJsZVRheFVTRCA9IGN1cnJlbmN5KHBheWFibGVUYXhVU0QpLmFkZChyc0NoYXJnZS5zdWJ0b3RhbCkudmFsdWUNCiAgICAgICAgICAgICAgdW5wYWlkVXNkU3VtID0gY3VycmVuY3kodW5wYWlkVXNkU3VtKS5hZGQocnNDaGFyZ2Uuc3FkRG5DdXJyZW5jeUJhbGFuY2UpLnZhbHVlDQogICAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgICBwYXlhYmxlUk1CID0gY3VycmVuY3kocGF5YWJsZVJNQikuYWRkKGN1cnJlbmN5KHJzQ2hhcmdlLnN1YnRvdGFsKS5kaXZpZGUoY3VycmVuY3koMSkuYWRkKGN1cnJlbmN5KHJzQ2hhcmdlLmR1dHlSYXRlKS5kaXZpZGUoMTAwKSkpKS52YWx1ZQ0KICAgICAgICAgICAgICBwYXlhYmxlVGF4Uk1CID0gY3VycmVuY3kocGF5YWJsZVRheFJNQikuYWRkKHJzQ2hhcmdlLnN1YnRvdGFsKS52YWx1ZQ0KICAgICAgICAgICAgICB1bnBhaWRSbWJTdW0gPSBjdXJyZW5jeSh1bnBhaWRSbWJTdW0pLmFkZChyc0NoYXJnZS5zcWREbkN1cnJlbmN5QmFsYW5jZSkudmFsdWUNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9DQogICAgICAgIH0pDQogICAgICB9KSA6IG51bGwNCiAgICAgIC8vIOaVtOafnOaLlui9pui0ueeUqA0KICAgICAgdGhpcy5mb3JtLnJzT3BDdG5yVHJ1Y2tMaXN0ID8gdGhpcy5mb3JtLnJzT3BDdG5yVHJ1Y2tMaXN0Lm1hcChpdGVtID0+IHsNCiAgICAgICAgaXRlbS5yc0NoYXJnZUxpc3QubWFwKHJzQ2hhcmdlID0+IHsNCiAgICAgICAgICBpZiAocnNDaGFyZ2Uuc3VidG90YWwpIHsNCiAgICAgICAgICAgIGlmIChyc0NoYXJnZS5kbkN1cnJlbmN5Q29kZSA9PT0gIlVTRCIpIHsNCiAgICAgICAgICAgICAgcGF5YWJsZVVTRCA9IGN1cnJlbmN5KHBheWFibGVVU0QpLmFkZChjdXJyZW5jeShyc0NoYXJnZS5zdWJ0b3RhbCkuZGl2aWRlKGN1cnJlbmN5KDEpLmFkZChjdXJyZW5jeShyc0NoYXJnZS5kdXR5UmF0ZSkuZGl2aWRlKDEwMCkpKSkudmFsdWUNCiAgICAgICAgICAgICAgcGF5YWJsZVRheFVTRCA9IGN1cnJlbmN5KHBheWFibGVUYXhVU0QpLmFkZChyc0NoYXJnZS5zdWJ0b3RhbCkudmFsdWUNCiAgICAgICAgICAgICAgdW5wYWlkVXNkU3VtID0gY3VycmVuY3kodW5wYWlkVXNkU3VtKS5hZGQocnNDaGFyZ2Uuc3FkRG5DdXJyZW5jeUJhbGFuY2UpLnZhbHVlDQogICAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgICBwYXlhYmxlUk1CID0gY3VycmVuY3kocGF5YWJsZVJNQikuYWRkKGN1cnJlbmN5KHJzQ2hhcmdlLnN1YnRvdGFsKS5kaXZpZGUoY3VycmVuY3koMSkuYWRkKGN1cnJlbmN5KHJzQ2hhcmdlLmR1dHlSYXRlKS5kaXZpZGUoMTAwKSkpKS52YWx1ZQ0KICAgICAgICAgICAgICBwYXlhYmxlVGF4Uk1CID0gY3VycmVuY3kocGF5YWJsZVRheFJNQikuYWRkKHJzQ2hhcmdlLnN1YnRvdGFsKS52YWx1ZQ0KICAgICAgICAgICAgICB1bnBhaWRSbWJTdW0gPSBjdXJyZW5jeSh1bnBhaWRSbWJTdW0pLmFkZChyc0NoYXJnZS5zcWREbkN1cnJlbmN5QmFsYW5jZSkudmFsdWUNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9DQogICAgICAgIH0pDQogICAgICB9KSA6IG51bGwNCiAgICAgIC8vIOaLvOafnOaLlui9pui0ueeUqA0KICAgICAgdGhpcy5mb3JtLnJzT3BCdWxrVHJ1Y2tMaXN0ID8gdGhpcy5mb3JtLnJzT3BCdWxrVHJ1Y2tMaXN0Lm1hcChpdGVtID0+IHsNCiAgICAgICAgaXRlbS5yc0NoYXJnZUxpc3QubWFwKHJzQ2hhcmdlID0+IHsNCiAgICAgICAgICBpZiAocnNDaGFyZ2Uuc3VidG90YWwpIHsNCiAgICAgICAgICAgIGlmIChyc0NoYXJnZS5kbkN1cnJlbmN5Q29kZSA9PT0gIlVTRCIpIHsNCiAgICAgICAgICAgICAgcGF5YWJsZVVTRCA9IGN1cnJlbmN5KHBheWFibGVVU0QpLmFkZChjdXJyZW5jeShyc0NoYXJnZS5zdWJ0b3RhbCkuZGl2aWRlKGN1cnJlbmN5KDEpLmFkZChjdXJyZW5jeShyc0NoYXJnZS5kdXR5UmF0ZSkuZGl2aWRlKDEwMCkpKSkudmFsdWUNCiAgICAgICAgICAgICAgcGF5YWJsZVRheFVTRCA9IGN1cnJlbmN5KHBheWFibGVUYXhVU0QpLmFkZChyc0NoYXJnZS5zdWJ0b3RhbCkudmFsdWUNCiAgICAgICAgICAgICAgdW5wYWlkVXNkU3VtID0gY3VycmVuY3kodW5wYWlkVXNkU3VtKS5hZGQocnNDaGFyZ2Uuc3FkRG5DdXJyZW5jeUJhbGFuY2UpLnZhbHVlDQogICAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgICBwYXlhYmxlUk1CID0gY3VycmVuY3kocGF5YWJsZVJNQikuYWRkKGN1cnJlbmN5KHJzQ2hhcmdlLnN1YnRvdGFsKS5kaXZpZGUoY3VycmVuY3koMSkuYWRkKGN1cnJlbmN5KHJzQ2hhcmdlLmR1dHlSYXRlKS5kaXZpZGUoMTAwKSkpKS52YWx1ZQ0KICAgICAgICAgICAgICBwYXlhYmxlVGF4Uk1CID0gY3VycmVuY3kocGF5YWJsZVRheFJNQikuYWRkKHJzQ2hhcmdlLnN1YnRvdGFsKS52YWx1ZQ0KICAgICAgICAgICAgICB1bnBhaWRSbWJTdW0gPSBjdXJyZW5jeSh1bnBhaWRSbWJTdW0pLmFkZChyc0NoYXJnZS5zcWREbkN1cnJlbmN5QmFsYW5jZSkudmFsdWUNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9DQogICAgICAgIH0pDQogICAgICB9KSA6IG51bGwNCiAgICAgIC8vIOWNleivgeaKpeWFs+i0ueeUqA0KICAgICAgdGhpcy5mb3JtLnJzT3BEb2NEZWNsYXJlTGlzdCA/IHRoaXMuZm9ybS5yc09wRG9jRGVjbGFyZUxpc3QubWFwKGl0ZW0gPT4gew0KICAgICAgICBpdGVtLnJzQ2hhcmdlTGlzdC5tYXAocnNDaGFyZ2UgPT4gew0KICAgICAgICAgIGlmIChyc0NoYXJnZS5zdWJ0b3RhbCkgew0KICAgICAgICAgICAgaWYgKHJzQ2hhcmdlLmRuQ3VycmVuY3lDb2RlID09PSAiVVNEIikgew0KICAgICAgICAgICAgICBwYXlhYmxlVVNEID0gY3VycmVuY3kocGF5YWJsZVVTRCkuYWRkKGN1cnJlbmN5KHJzQ2hhcmdlLnN1YnRvdGFsKS5kaXZpZGUoY3VycmVuY3koMSkuYWRkKGN1cnJlbmN5KHJzQ2hhcmdlLmR1dHlSYXRlKS5kaXZpZGUoMTAwKSkpKS52YWx1ZQ0KICAgICAgICAgICAgICBwYXlhYmxlVGF4VVNEID0gY3VycmVuY3kocGF5YWJsZVRheFVTRCkuYWRkKHJzQ2hhcmdlLnN1YnRvdGFsKS52YWx1ZQ0KICAgICAgICAgICAgICB1bnBhaWRVc2RTdW0gPSBjdXJyZW5jeSh1bnBhaWRVc2RTdW0pLmFkZChyc0NoYXJnZS5zcWREbkN1cnJlbmN5QmFsYW5jZSkudmFsdWUNCiAgICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICAgIHBheWFibGVSTUIgPSBjdXJyZW5jeShwYXlhYmxlUk1CKS5hZGQoY3VycmVuY3kocnNDaGFyZ2Uuc3VidG90YWwpLmRpdmlkZShjdXJyZW5jeSgxKS5hZGQoY3VycmVuY3kocnNDaGFyZ2UuZHV0eVJhdGUpLmRpdmlkZSgxMDApKSkpLnZhbHVlDQogICAgICAgICAgICAgIHBheWFibGVUYXhSTUIgPSBjdXJyZW5jeShwYXlhYmxlVGF4Uk1CKS5hZGQocnNDaGFyZ2Uuc3VidG90YWwpLnZhbHVlDQogICAgICAgICAgICAgIHVucGFpZFJtYlN1bSA9IGN1cnJlbmN5KHVucGFpZFJtYlN1bSkuYWRkKHJzQ2hhcmdlLnNxZERuQ3VycmVuY3lCYWxhbmNlKS52YWx1ZQ0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0NCiAgICAgICAgfSkNCiAgICAgIH0pIDogbnVsbA0KICAgICAgLy8g5YWo5YyF5oql5YWz6LS555SoDQogICAgICB0aGlzLmZvcm0ucnNPcEZyZWVEZWNsYXJlTGlzdCA/IHRoaXMuZm9ybS5yc09wRnJlZURlY2xhcmVMaXN0Lm1hcChpdGVtID0+IHsNCiAgICAgICAgaXRlbS5yc0NoYXJnZUxpc3QubWFwKHJzQ2hhcmdlID0+IHsNCiAgICAgICAgICBpZiAocnNDaGFyZ2Uuc3VidG90YWwpIHsNCiAgICAgICAgICAgIGlmIChyc0NoYXJnZS5kbkN1cnJlbmN5Q29kZSA9PT0gIlVTRCIpIHsNCiAgICAgICAgICAgICAgcGF5YWJsZVVTRCA9IGN1cnJlbmN5KHBheWFibGVVU0QpLmFkZChjdXJyZW5jeShyc0NoYXJnZS5zdWJ0b3RhbCkuZGl2aWRlKGN1cnJlbmN5KDEpLmFkZChjdXJyZW5jeShyc0NoYXJnZS5kdXR5UmF0ZSkuZGl2aWRlKDEwMCkpKSkudmFsdWUNCiAgICAgICAgICAgICAgcGF5YWJsZVRheFVTRCA9IGN1cnJlbmN5KHBheWFibGVUYXhVU0QpLmFkZChyc0NoYXJnZS5zdWJ0b3RhbCkudmFsdWUNCiAgICAgICAgICAgICAgdW5wYWlkVXNkU3VtID0gY3VycmVuY3kodW5wYWlkVXNkU3VtKS5hZGQocnNDaGFyZ2Uuc3FkRG5DdXJyZW5jeUJhbGFuY2UpLnZhbHVlDQogICAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgICBwYXlhYmxlUk1CID0gY3VycmVuY3kocGF5YWJsZVJNQikuYWRkKGN1cnJlbmN5KHJzQ2hhcmdlLnN1YnRvdGFsKS5kaXZpZGUoY3VycmVuY3koMSkuYWRkKGN1cnJlbmN5KHJzQ2hhcmdlLmR1dHlSYXRlKS5kaXZpZGUoMTAwKSkpKS52YWx1ZQ0KICAgICAgICAgICAgICBwYXlhYmxlVGF4Uk1CID0gY3VycmVuY3kocGF5YWJsZVRheFJNQikuYWRkKHJzQ2hhcmdlLnN1YnRvdGFsKS52YWx1ZQ0KICAgICAgICAgICAgICB1bnBhaWRSbWJTdW0gPSBjdXJyZW5jeSh1bnBhaWRSbWJTdW0pLmFkZChyc0NoYXJnZS5zcWREbkN1cnJlbmN5QmFsYW5jZSkudmFsdWUNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9DQogICAgICAgIH0pDQogICAgICB9KSA6IG51bGwNCg0KICAgICAgdGhpcy5yc09wUmFpbEZDTCA/IHRoaXMucnNPcFJhaWxGQ0wucnNDaGFyZ2VMaXN0Lm1hcChyc0NoYXJnZSA9PiB7DQogICAgICAgIGlmIChyc0NoYXJnZS5zdWJ0b3RhbCkgew0KICAgICAgICAgIGlmIChyc0NoYXJnZS5kbkN1cnJlbmN5Q29kZSA9PT0gIlVTRCIpIHsNCiAgICAgICAgICAgIHBheWFibGVVU0QgPSBjdXJyZW5jeShwYXlhYmxlVVNEKS5hZGQoY3VycmVuY3kocnNDaGFyZ2Uuc3VidG90YWwpLmRpdmlkZShjdXJyZW5jeSgxKS5hZGQoY3VycmVuY3kocnNDaGFyZ2UuZHV0eVJhdGUpLmRpdmlkZSgxMDApKSkpLnZhbHVlDQogICAgICAgICAgICBwYXlhYmxlVGF4VVNEID0gY3VycmVuY3kocGF5YWJsZVRheFVTRCkuYWRkKHJzQ2hhcmdlLnN1YnRvdGFsKS52YWx1ZQ0KICAgICAgICAgICAgdW5wYWlkVXNkU3VtID0gY3VycmVuY3kodW5wYWlkVXNkU3VtKS5hZGQocnNDaGFyZ2Uuc3FkRG5DdXJyZW5jeUJhbGFuY2UpLnZhbHVlDQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIHBheWFibGVSTUIgPSBjdXJyZW5jeShwYXlhYmxlUk1CKS5hZGQoY3VycmVuY3kocnNDaGFyZ2Uuc3VidG90YWwpLmRpdmlkZShjdXJyZW5jeSgxKS5hZGQoY3VycmVuY3kocnNDaGFyZ2UuZHV0eVJhdGUpLmRpdmlkZSgxMDApKSkpLnZhbHVlDQogICAgICAgICAgICBwYXlhYmxlVGF4Uk1CID0gY3VycmVuY3kocGF5YWJsZVRheFJNQikuYWRkKHJzQ2hhcmdlLnN1YnRvdGFsKS52YWx1ZQ0KICAgICAgICAgICAgdW5wYWlkUm1iU3VtID0gY3VycmVuY3kodW5wYWlkUm1iU3VtKS5hZGQocnNDaGFyZ2Uuc3FkRG5DdXJyZW5jeUJhbGFuY2UpLnZhbHVlDQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICB9KSA6IG51bGwNCiAgICAgIHRoaXMucnNPcFJhaWxMQ0wgPyB0aGlzLnJzT3BSYWlsTENMLnJzQ2hhcmdlTGlzdC5tYXAocnNDaGFyZ2UgPT4gew0KICAgICAgICBpZiAocnNDaGFyZ2Uuc3VidG90YWwpIHsNCiAgICAgICAgICBpZiAocnNDaGFyZ2UuZG5DdXJyZW5jeUNvZGUgPT09ICJVU0QiKSB7DQogICAgICAgICAgICBwYXlhYmxlVVNEID0gY3VycmVuY3kocGF5YWJsZVVTRCkuYWRkKGN1cnJlbmN5KHJzQ2hhcmdlLnN1YnRvdGFsKS5kaXZpZGUoY3VycmVuY3koMSkuYWRkKGN1cnJlbmN5KHJzQ2hhcmdlLmR1dHlSYXRlKS5kaXZpZGUoMTAwKSkpKS52YWx1ZQ0KICAgICAgICAgICAgcGF5YWJsZVRheFVTRCA9IGN1cnJlbmN5KHBheWFibGVUYXhVU0QpLmFkZChyc0NoYXJnZS5zdWJ0b3RhbCkudmFsdWUNCiAgICAgICAgICAgIHVucGFpZFVzZFN1bSA9IGN1cnJlbmN5KHVucGFpZFVzZFN1bSkuYWRkKHJzQ2hhcmdlLnNxZERuQ3VycmVuY3lCYWxhbmNlKS52YWx1ZQ0KICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICBwYXlhYmxlUk1CID0gY3VycmVuY3kocGF5YWJsZVJNQikuYWRkKGN1cnJlbmN5KHJzQ2hhcmdlLnN1YnRvdGFsKS5kaXZpZGUoY3VycmVuY3koMSkuYWRkKGN1cnJlbmN5KHJzQ2hhcmdlLmR1dHlSYXRlKS5kaXZpZGUoMTAwKSkpKS52YWx1ZQ0KICAgICAgICAgICAgcGF5YWJsZVRheFJNQiA9IGN1cnJlbmN5KHBheWFibGVUYXhSTUIpLmFkZChyc0NoYXJnZS5zdWJ0b3RhbCkudmFsdWUNCiAgICAgICAgICAgIHVucGFpZFJtYlN1bSA9IGN1cnJlbmN5KHVucGFpZFJtYlN1bSkuYWRkKHJzQ2hhcmdlLnNxZERuQ3VycmVuY3lCYWxhbmNlKS52YWx1ZQ0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgfSkgOiBudWxsDQogICAgICB0aGlzLnJzT3BFeHByZXNzID8gdGhpcy5yc09wRXhwcmVzcy5yc0NoYXJnZUxpc3QubWFwKHJzQ2hhcmdlID0+IHsNCiAgICAgICAgaWYgKHJzQ2hhcmdlLnN1YnRvdGFsKSB7DQogICAgICAgICAgaWYgKHJzQ2hhcmdlLmRuQ3VycmVuY3lDb2RlID09PSAiVVNEIikgew0KICAgICAgICAgICAgcGF5YWJsZVVTRCA9IGN1cnJlbmN5KHBheWFibGVVU0QpLmFkZChjdXJyZW5jeShyc0NoYXJnZS5zdWJ0b3RhbCkuZGl2aWRlKGN1cnJlbmN5KDEpLmFkZChjdXJyZW5jeShyc0NoYXJnZS5kdXR5UmF0ZSkuZGl2aWRlKDEwMCkpKSkudmFsdWUNCiAgICAgICAgICAgIHBheWFibGVUYXhVU0QgPSBjdXJyZW5jeShwYXlhYmxlVGF4VVNEKS5hZGQocnNDaGFyZ2Uuc3VidG90YWwpLnZhbHVlDQogICAgICAgICAgICB1bnBhaWRVc2RTdW0gPSBjdXJyZW5jeSh1bnBhaWRVc2RTdW0pLmFkZChyc0NoYXJnZS5zcWREbkN1cnJlbmN5QmFsYW5jZSkudmFsdWUNCiAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgcGF5YWJsZVJNQiA9IGN1cnJlbmN5KHBheWFibGVSTUIpLmFkZChjdXJyZW5jeShyc0NoYXJnZS5zdWJ0b3RhbCkuZGl2aWRlKGN1cnJlbmN5KDEpLmFkZChjdXJyZW5jeShyc0NoYXJnZS5kdXR5UmF0ZSkuZGl2aWRlKDEwMCkpKSkudmFsdWUNCiAgICAgICAgICAgIHBheWFibGVUYXhSTUIgPSBjdXJyZW5jeShwYXlhYmxlVGF4Uk1CKS5hZGQocnNDaGFyZ2Uuc3VidG90YWwpLnZhbHVlDQogICAgICAgICAgICB1bnBhaWRSbWJTdW0gPSBjdXJyZW5jeSh1bnBhaWRSbWJTdW0pLmFkZChyc0NoYXJnZS5zcWREbkN1cnJlbmN5QmFsYW5jZSkudmFsdWUNCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgIH0pIDogbnVsbA0KICAgICAgdGhpcy5yc09wRE9BZ2VudCA/IHRoaXMucnNPcERPQWdlbnQucnNDaGFyZ2VMaXN0Lm1hcChyc0NoYXJnZSA9PiB7DQogICAgICAgIGlmIChyc0NoYXJnZS5zdWJ0b3RhbCkgew0KICAgICAgICAgIGlmIChyc0NoYXJnZS5kbkN1cnJlbmN5Q29kZSA9PT0gIlVTRCIpIHsNCiAgICAgICAgICAgIHBheWFibGVVU0QgPSBjdXJyZW5jeShwYXlhYmxlVVNEKS5hZGQoY3VycmVuY3kocnNDaGFyZ2Uuc3VidG90YWwpLmRpdmlkZShjdXJyZW5jeSgxKS5hZGQoY3VycmVuY3kocnNDaGFyZ2UuZHV0eVJhdGUpLmRpdmlkZSgxMDApKSkpLnZhbHVlDQogICAgICAgICAgICBwYXlhYmxlVGF4VVNEID0gY3VycmVuY3kocGF5YWJsZVRheFVTRCkuYWRkKHJzQ2hhcmdlLnN1YnRvdGFsKS52YWx1ZQ0KICAgICAgICAgICAgdW5wYWlkVXNkU3VtID0gY3VycmVuY3kodW5wYWlkVXNkU3VtKS5hZGQocnNDaGFyZ2Uuc3FkRG5DdXJyZW5jeUJhbGFuY2UpLnZhbHVlDQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIHBheWFibGVSTUIgPSBjdXJyZW5jeShwYXlhYmxlUk1CKS5hZGQoY3VycmVuY3kocnNDaGFyZ2Uuc3VidG90YWwpLmRpdmlkZShjdXJyZW5jeSgxKS5hZGQoY3VycmVuY3kocnNDaGFyZ2UuZHV0eVJhdGUpLmRpdmlkZSgxMDApKSkpLnZhbHVlDQogICAgICAgICAgICBwYXlhYmxlVGF4Uk1CID0gY3VycmVuY3kocGF5YWJsZVRheFJNQikuYWRkKHJzQ2hhcmdlLnN1YnRvdGFsKS52YWx1ZQ0KICAgICAgICAgICAgdW5wYWlkUm1iU3VtID0gY3VycmVuY3kodW5wYWlkUm1iU3VtKS5hZGQocnNDaGFyZ2Uuc3FkRG5DdXJyZW5jeUJhbGFuY2UpLnZhbHVlDQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICB9KSA6IG51bGwNCiAgICAgIHRoaXMucnNPcENsZWFyQWdlbnQgPyB0aGlzLnJzT3BDbGVhckFnZW50LnJzQ2hhcmdlTGlzdC5tYXAocnNDaGFyZ2UgPT4gew0KICAgICAgICBpZiAocnNDaGFyZ2Uuc3VidG90YWwpIHsNCiAgICAgICAgICBpZiAocnNDaGFyZ2UuZG5DdXJyZW5jeUNvZGUgPT09ICJVU0QiKSB7DQogICAgICAgICAgICBwYXlhYmxlVVNEID0gY3VycmVuY3kocGF5YWJsZVVTRCkuYWRkKGN1cnJlbmN5KHJzQ2hhcmdlLnN1YnRvdGFsKS5kaXZpZGUoY3VycmVuY3koMSkuYWRkKGN1cnJlbmN5KHJzQ2hhcmdlLmR1dHlSYXRlKS5kaXZpZGUoMTAwKSkpKS52YWx1ZQ0KICAgICAgICAgICAgcGF5YWJsZVRheFVTRCA9IGN1cnJlbmN5KHBheWFibGVUYXhVU0QpLmFkZChyc0NoYXJnZS5zdWJ0b3RhbCkudmFsdWUNCiAgICAgICAgICAgIHVucGFpZFVzZFN1bSA9IGN1cnJlbmN5KHVucGFpZFVzZFN1bSkuYWRkKHJzQ2hhcmdlLnNxZERuQ3VycmVuY3lCYWxhbmNlKS52YWx1ZQ0KICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICBwYXlhYmxlUk1CID0gY3VycmVuY3kocGF5YWJsZVJNQikuYWRkKGN1cnJlbmN5KHJzQ2hhcmdlLnN1YnRvdGFsKS5kaXZpZGUoY3VycmVuY3koMSkuYWRkKGN1cnJlbmN5KHJzQ2hhcmdlLmR1dHlSYXRlKS5kaXZpZGUoMTAwKSkpKS52YWx1ZQ0KICAgICAgICAgICAgcGF5YWJsZVRheFJNQiA9IGN1cnJlbmN5KHBheWFibGVUYXhSTUIpLmFkZChyc0NoYXJnZS5zdWJ0b3RhbCkudmFsdWUNCiAgICAgICAgICAgIHVucGFpZFJtYlN1bSA9IGN1cnJlbmN5KHVucGFpZFJtYlN1bSkuYWRkKHJzQ2hhcmdlLnNxZERuQ3VycmVuY3lCYWxhbmNlKS52YWx1ZQ0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgfSkgOiBudWxsDQogICAgICB0aGlzLnJzT3BXSFMgPyB0aGlzLnJzT3BXSFMucnNDaGFyZ2VMaXN0Lm1hcChyc0NoYXJnZSA9PiB7DQogICAgICAgIGlmIChyc0NoYXJnZS5zdWJ0b3RhbCkgew0KICAgICAgICAgIGlmIChyc0NoYXJnZS5kbkN1cnJlbmN5Q29kZSA9PT0gIlVTRCIpIHsNCiAgICAgICAgICAgIHBheWFibGVVU0QgPSBjdXJyZW5jeShwYXlhYmxlVVNEKS5hZGQoY3VycmVuY3kocnNDaGFyZ2Uuc3VidG90YWwpLmRpdmlkZShjdXJyZW5jeSgxKS5hZGQoY3VycmVuY3kocnNDaGFyZ2UuZHV0eVJhdGUpLmRpdmlkZSgxMDApKSkpLnZhbHVlDQogICAgICAgICAgICBwYXlhYmxlVGF4VVNEID0gY3VycmVuY3kocGF5YWJsZVRheFVTRCkuYWRkKHJzQ2hhcmdlLnN1YnRvdGFsKS52YWx1ZQ0KICAgICAgICAgICAgdW5wYWlkVXNkU3VtID0gY3VycmVuY3kodW5wYWlkVXNkU3VtKS5hZGQocnNDaGFyZ2Uuc3FkRG5DdXJyZW5jeUJhbGFuY2UpLnZhbHVlDQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIHBheWFibGVSTUIgPSBjdXJyZW5jeShwYXlhYmxlUk1CKS5hZGQoY3VycmVuY3kocnNDaGFyZ2Uuc3VidG90YWwpLmRpdmlkZShjdXJyZW5jeSgxKS5hZGQoY3VycmVuY3kocnNDaGFyZ2UuZHV0eVJhdGUpLmRpdmlkZSgxMDApKSkpLnZhbHVlDQogICAgICAgICAgICBwYXlhYmxlVGF4Uk1CID0gY3VycmVuY3kocGF5YWJsZVRheFJNQikuYWRkKHJzQ2hhcmdlLnN1YnRvdGFsKS52YWx1ZQ0KICAgICAgICAgICAgdW5wYWlkUm1iU3VtID0gY3VycmVuY3kodW5wYWlkUm1iU3VtKS5hZGQocnNDaGFyZ2Uuc3FkRG5DdXJyZW5jeUJhbGFuY2UpLnZhbHVlDQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICB9KSA6IG51bGwNCiAgICAgIHRoaXMucnNPcDNyZENlcnQgPyB0aGlzLnJzT3AzcmRDZXJ0LnJzQ2hhcmdlTGlzdC5tYXAocnNDaGFyZ2UgPT4gew0KICAgICAgICBpZiAocnNDaGFyZ2Uuc3VidG90YWwpIHsNCiAgICAgICAgICBpZiAocnNDaGFyZ2UuZG5DdXJyZW5jeUNvZGUgPT09ICJVU0QiKSB7DQogICAgICAgICAgICBwYXlhYmxlVVNEID0gY3VycmVuY3kocGF5YWJsZVVTRCkuYWRkKGN1cnJlbmN5KHJzQ2hhcmdlLnN1YnRvdGFsKS5kaXZpZGUoY3VycmVuY3koMSkuYWRkKGN1cnJlbmN5KHJzQ2hhcmdlLmR1dHlSYXRlKS5kaXZpZGUoMTAwKSkpKS52YWx1ZQ0KICAgICAgICAgICAgcGF5YWJsZVRheFVTRCA9IGN1cnJlbmN5KHBheWFibGVUYXhVU0QpLmFkZChyc0NoYXJnZS5zdWJ0b3RhbCkudmFsdWUNCiAgICAgICAgICAgIHVucGFpZFVzZFN1bSA9IGN1cnJlbmN5KHVucGFpZFVzZFN1bSkuYWRkKHJzQ2hhcmdlLnNxZERuQ3VycmVuY3lCYWxhbmNlKS52YWx1ZQ0KICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICBwYXlhYmxlUk1CID0gY3VycmVuY3kocGF5YWJsZVJNQikuYWRkKGN1cnJlbmN5KHJzQ2hhcmdlLnN1YnRvdGFsKS5kaXZpZGUoY3VycmVuY3koMSkuYWRkKGN1cnJlbmN5KHJzQ2hhcmdlLmR1dHlSYXRlKS5kaXZpZGUoMTAwKSkpKS52YWx1ZQ0KICAgICAgICAgICAgcGF5YWJsZVRheFJNQiA9IGN1cnJlbmN5KHBheWFibGVUYXhSTUIpLmFkZChyc0NoYXJnZS5zdWJ0b3RhbCkudmFsdWUNCiAgICAgICAgICAgIHVucGFpZFJtYlN1bSA9IGN1cnJlbmN5KHVucGFpZFJtYlN1bSkuYWRkKHJzQ2hhcmdlLnNxZERuQ3VycmVuY3lCYWxhbmNlKS52YWx1ZQ0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgfSkgOiBudWxsDQogICAgICB0aGlzLnJzT3BJTlMgPyB0aGlzLnJzT3BJTlMucnNDaGFyZ2VMaXN0Lm1hcChyc0NoYXJnZSA9PiB7DQogICAgICAgIGlmIChyc0NoYXJnZS5zdWJ0b3RhbCkgew0KICAgICAgICAgIGlmIChyc0NoYXJnZS5kbkN1cnJlbmN5Q29kZSA9PT0gIlVTRCIpIHsNCiAgICAgICAgICAgIHBheWFibGVVU0QgPSBjdXJyZW5jeShwYXlhYmxlVVNEKS5hZGQoY3VycmVuY3kocnNDaGFyZ2Uuc3VidG90YWwpLmRpdmlkZShjdXJyZW5jeSgxKS5hZGQoY3VycmVuY3kocnNDaGFyZ2UuZHV0eVJhdGUpLmRpdmlkZSgxMDApKSkpLnZhbHVlDQogICAgICAgICAgICBwYXlhYmxlVGF4VVNEID0gY3VycmVuY3kocGF5YWJsZVRheFVTRCkuYWRkKHJzQ2hhcmdlLnN1YnRvdGFsKS52YWx1ZQ0KICAgICAgICAgICAgdW5wYWlkVXNkU3VtID0gY3VycmVuY3kodW5wYWlkVXNkU3VtKS5hZGQocnNDaGFyZ2Uuc3FkRG5DdXJyZW5jeUJhbGFuY2UpLnZhbHVlDQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIHBheWFibGVSTUIgPSBjdXJyZW5jeShwYXlhYmxlUk1CKS5hZGQoY3VycmVuY3kocnNDaGFyZ2Uuc3VidG90YWwpLmRpdmlkZShjdXJyZW5jeSgxKS5hZGQoY3VycmVuY3kocnNDaGFyZ2UuZHV0eVJhdGUpLmRpdmlkZSgxMDApKSkpLnZhbHVlDQogICAgICAgICAgICBwYXlhYmxlVGF4Uk1CID0gY3VycmVuY3kocGF5YWJsZVRheFJNQikuYWRkKHJzQ2hhcmdlLnN1YnRvdGFsKS52YWx1ZQ0KICAgICAgICAgICAgdW5wYWlkUm1iU3VtID0gY3VycmVuY3kodW5wYWlkUm1iU3VtKS5hZGQocnNDaGFyZ2Uuc3FkRG5DdXJyZW5jeUJhbGFuY2UpLnZhbHVlDQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICB9KSA6IG51bGwNCiAgICAgIHRoaXMucnNPcFRyYWRpbmcgPyB0aGlzLnJzT3BUcmFkaW5nLnJzQ2hhcmdlTGlzdC5tYXAocnNDaGFyZ2UgPT4gew0KICAgICAgICBpZiAocnNDaGFyZ2Uuc3VidG90YWwpIHsNCiAgICAgICAgICBpZiAocnNDaGFyZ2UuZG5DdXJyZW5jeUNvZGUgPT09ICJVU0QiKSB7DQogICAgICAgICAgICBwYXlhYmxlVVNEID0gY3VycmVuY3kocGF5YWJsZVVTRCkuYWRkKGN1cnJlbmN5KHJzQ2hhcmdlLnN1YnRvdGFsKS5kaXZpZGUoY3VycmVuY3koMSkuYWRkKGN1cnJlbmN5KHJzQ2hhcmdlLmR1dHlSYXRlKS5kaXZpZGUoMTAwKSkpKS52YWx1ZQ0KICAgICAgICAgICAgcGF5YWJsZVRheFVTRCA9IGN1cnJlbmN5KHBheWFibGVUYXhVU0QpLmFkZChyc0NoYXJnZS5zdWJ0b3RhbCkudmFsdWUNCiAgICAgICAgICAgIHVucGFpZFVzZFN1bSA9IGN1cnJlbmN5KHVucGFpZFVzZFN1bSkuYWRkKHJzQ2hhcmdlLnNxZERuQ3VycmVuY3lCYWxhbmNlKS52YWx1ZQ0KICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICBwYXlhYmxlUk1CID0gY3VycmVuY3kocGF5YWJsZVJNQikuYWRkKGN1cnJlbmN5KHJzQ2hhcmdlLnN1YnRvdGFsKS5kaXZpZGUoY3VycmVuY3koMSkuYWRkKGN1cnJlbmN5KHJzQ2hhcmdlLmR1dHlSYXRlKS5kaXZpZGUoMTAwKSkpKS52YWx1ZQ0KICAgICAgICAgICAgcGF5YWJsZVRheFJNQiA9IGN1cnJlbmN5KHBheWFibGVUYXhSTUIpLmFkZChyc0NoYXJnZS5zdWJ0b3RhbCkudmFsdWUNCiAgICAgICAgICAgIHVucGFpZFJtYlN1bSA9IGN1cnJlbmN5KHVucGFpZFJtYlN1bSkuYWRkKHJzQ2hhcmdlLnNxZERuQ3VycmVuY3lCYWxhbmNlKS52YWx1ZQ0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgfSkgOiBudWxsDQogICAgICB0aGlzLnJzT3BGdW1pZ2F0aW9uID8gdGhpcy5yc09wRnVtaWdhdGlvbi5yc0NoYXJnZUxpc3QubWFwKHJzQ2hhcmdlID0+IHsNCiAgICAgICAgaWYgKHJzQ2hhcmdlLnN1YnRvdGFsKSB7DQogICAgICAgICAgaWYgKHJzQ2hhcmdlLmRuQ3VycmVuY3lDb2RlID09PSAiVVNEIikgew0KICAgICAgICAgICAgcGF5YWJsZVVTRCA9IGN1cnJlbmN5KHBheWFibGVVU0QpLmFkZChjdXJyZW5jeShyc0NoYXJnZS5zdWJ0b3RhbCkuZGl2aWRlKGN1cnJlbmN5KDEpLmFkZChjdXJyZW5jeShyc0NoYXJnZS5kdXR5UmF0ZSkuZGl2aWRlKDEwMCkpKSkudmFsdWUNCiAgICAgICAgICAgIHBheWFibGVUYXhVU0QgPSBjdXJyZW5jeShwYXlhYmxlVGF4VVNEKS5hZGQocnNDaGFyZ2Uuc3VidG90YWwpLnZhbHVlDQogICAgICAgICAgICB1bnBhaWRVc2RTdW0gPSBjdXJyZW5jeSh1bnBhaWRVc2RTdW0pLmFkZChyc0NoYXJnZS5zcWREbkN1cnJlbmN5QmFsYW5jZSkudmFsdWUNCiAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgcGF5YWJsZVJNQiA9IGN1cnJlbmN5KHBheWFibGVSTUIpLmFkZChjdXJyZW5jeShyc0NoYXJnZS5zdWJ0b3RhbCkuZGl2aWRlKGN1cnJlbmN5KDEpLmFkZChjdXJyZW5jeShyc0NoYXJnZS5kdXR5UmF0ZSkuZGl2aWRlKDEwMCkpKSkudmFsdWUNCiAgICAgICAgICAgIHBheWFibGVUYXhSTUIgPSBjdXJyZW5jeShwYXlhYmxlVGF4Uk1CKS5hZGQocnNDaGFyZ2Uuc3VidG90YWwpLnZhbHVlDQogICAgICAgICAgICB1bnBhaWRSbWJTdW0gPSBjdXJyZW5jeSh1bnBhaWRSbWJTdW0pLmFkZChyc0NoYXJnZS5zcWREbkN1cnJlbmN5QmFsYW5jZSkudmFsdWUNCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgIH0pIDogbnVsbA0KICAgICAgdGhpcy5yc09wQ08gPyB0aGlzLnJzT3BDTy5yc0NoYXJnZUxpc3QubWFwKHJzQ2hhcmdlID0+IHsNCiAgICAgICAgaWYgKHJzQ2hhcmdlLnN1YnRvdGFsKSB7DQogICAgICAgICAgaWYgKHJzQ2hhcmdlLmRuQ3VycmVuY3lDb2RlID09PSAiVVNEIikgew0KICAgICAgICAgICAgcGF5YWJsZVVTRCA9IGN1cnJlbmN5KHBheWFibGVVU0QpLmFkZChjdXJyZW5jeShyc0NoYXJnZS5zdWJ0b3RhbCkuZGl2aWRlKGN1cnJlbmN5KDEpLmFkZChjdXJyZW5jeShyc0NoYXJnZS5kdXR5UmF0ZSkuZGl2aWRlKDEwMCkpKSkudmFsdWUNCiAgICAgICAgICAgIHBheWFibGVUYXhVU0QgPSBjdXJyZW5jeShwYXlhYmxlVGF4VVNEKS5hZGQocnNDaGFyZ2Uuc3VidG90YWwpLnZhbHVlDQogICAgICAgICAgICB1bnBhaWRVc2RTdW0gPSBjdXJyZW5jeSh1bnBhaWRVc2RTdW0pLmFkZChyc0NoYXJnZS5zcWREbkN1cnJlbmN5QmFsYW5jZSkudmFsdWUNCiAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgcGF5YWJsZVJNQiA9IGN1cnJlbmN5KHBheWFibGVSTUIpLmFkZChjdXJyZW5jeShyc0NoYXJnZS5zdWJ0b3RhbCkuZGl2aWRlKGN1cnJlbmN5KDEpLmFkZChjdXJyZW5jeShyc0NoYXJnZS5kdXR5UmF0ZSkuZGl2aWRlKDEwMCkpKSkudmFsdWUNCiAgICAgICAgICAgIHBheWFibGVUYXhSTUIgPSBjdXJyZW5jeShwYXlhYmxlVGF4Uk1CKS5hZGQocnNDaGFyZ2Uuc3VidG90YWwpLnZhbHVlDQogICAgICAgICAgICB1bnBhaWRSbWJTdW0gPSBjdXJyZW5jeSh1bnBhaWRSbWJTdW0pLmFkZChyc0NoYXJnZS5zcWREbkN1cnJlbmN5QmFsYW5jZSkudmFsdWUNCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgIH0pIDogbnVsbA0KICAgICAgdGhpcy5yc09wT3RoZXIgPyB0aGlzLnJzT3BPdGhlci5yc0NoYXJnZUxpc3QubWFwKHJzQ2hhcmdlID0+IHsNCiAgICAgICAgaWYgKHJzQ2hhcmdlLnN1YnRvdGFsKSB7DQogICAgICAgICAgaWYgKHJzQ2hhcmdlLmRuQ3VycmVuY3lDb2RlID09PSAiVVNEIikgew0KICAgICAgICAgICAgcGF5YWJsZVVTRCA9IGN1cnJlbmN5KHBheWFibGVVU0QpLmFkZChjdXJyZW5jeShyc0NoYXJnZS5zdWJ0b3RhbCkuZGl2aWRlKGN1cnJlbmN5KDEpLmFkZChjdXJyZW5jeShyc0NoYXJnZS5kdXR5UmF0ZSkuZGl2aWRlKDEwMCkpKSkudmFsdWUNCiAgICAgICAgICAgIHBheWFibGVUYXhVU0QgPSBjdXJyZW5jeShwYXlhYmxlVGF4VVNEKS5hZGQocnNDaGFyZ2Uuc3VidG90YWwpLnZhbHVlDQogICAgICAgICAgICB1bnBhaWRVc2RTdW0gPSBjdXJyZW5jeSh1bnBhaWRVc2RTdW0pLmFkZChyc0NoYXJnZS5zcWREbkN1cnJlbmN5QmFsYW5jZSkudmFsdWUNCiAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgcGF5YWJsZVJNQiA9IGN1cnJlbmN5KHBheWFibGVSTUIpLmFkZChjdXJyZW5jeShyc0NoYXJnZS5zdWJ0b3RhbCkuZGl2aWRlKGN1cnJlbmN5KDEpLmFkZChjdXJyZW5jeShyc0NoYXJnZS5kdXR5UmF0ZSkuZGl2aWRlKDEwMCkpKSkudmFsdWUNCiAgICAgICAgICAgIHBheWFibGVUYXhSTUIgPSBjdXJyZW5jeShwYXlhYmxlVGF4Uk1CKS5hZGQocnNDaGFyZ2Uuc3VidG90YWwpLnZhbHVlDQogICAgICAgICAgICB1bnBhaWRSbWJTdW0gPSBjdXJyZW5jeSh1bnBhaWRSbWJTdW0pLmFkZChyc0NoYXJnZS5zcWREbkN1cnJlbmN5QmFsYW5jZSkudmFsdWUNCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgIH0pIDogbnVsbA0KDQogICAgICB0aGlzLnJzQ2xpZW50TWVzc2FnZVBheWFibGVSTUIgPSBwYXlhYmxlUk1CDQogICAgICB0aGlzLnJzQ2xpZW50TWVzc2FnZVBheWFibGVVU0QgPSBwYXlhYmxlVVNEDQogICAgICB0aGlzLnNxZFVucGFpZFJtYlN1bSA9IHVucGFpZFJtYlN1bQ0KICAgICAgdGhpcy5zcWRVbnBhaWRVc2RTdW0gPSB1bnBhaWRVc2RTdW0NCiAgICAgIC8vIOS4jeWQq+eojuWIqea2pg0KICAgICAgdGhpcy5yc0NsaWVudE1lc3NhZ2VQcm9maXRSTUIgPSBjdXJyZW5jeShyZWNlaXZhYmxlUk1CKS5zdWJ0cmFjdChwYXlhYmxlUk1CKS52YWx1ZQ0KICAgICAgdGhpcy5yc0NsaWVudE1lc3NhZ2VQcm9maXRVU0QgPSBjdXJyZW5jeShyZWNlaXZhYmxlVVNEKS5zdWJ0cmFjdChwYXlhYmxlVVNEKS52YWx1ZQ0KICAgICAgaWYgKG4ubGVuZ3RoID4gMCkgew0KICAgICAgICAvLyDlkKvnqI7liKnmtqYNCiAgICAgICAgdGhpcy5yc0NsaWVudE1lc3NhZ2VQcm9maXRUYXhSTUIgPSBjdXJyZW5jeShyZWNlaXZhYmxlVGF4Uk1CKS5zdWJ0cmFjdChwYXlhYmxlVGF4Uk1CKS52YWx1ZQ0KICAgICAgICB0aGlzLnJzQ2xpZW50TWVzc2FnZVByb2ZpdFRheFVTRCA9IGN1cnJlbmN5KHJlY2VpdmFibGVUYXhVU0QpLnN1YnRyYWN0KHBheWFibGVUYXhVU0QpLnZhbHVlDQogICAgICB9IGVsc2Ugew0KICAgICAgICB0aGlzLnJzQ2xpZW50TWVzc2FnZVByb2ZpdFRheFJNQiA9IGN1cnJlbmN5KHJlY2VpdmFibGVSTUIpLnN1YnRyYWN0KHBheWFibGVUYXhSTUIpLnZhbHVlDQogICAgICAgIHRoaXMucnNDbGllbnRNZXNzYWdlUHJvZml0VGF4VVNEID0gY3VycmVuY3kocmVjZWl2YWJsZVVTRCkuc3VidHJhY3QocGF5YWJsZVRheFVTRCkudmFsdWUNCiAgICAgIH0NCiAgICB9LA0KICAgICJmb3JtLnJzT3BTZWFGY2xMaXN0IihuKSB7DQogICAgICBpZiAobiAmJiBuLmxlbmd0aCA+IDApIHsNCiAgICAgICAgbi5tYXAoaXRlbSA9PiB7DQogICAgICAgICAgbGV0IHBheWFibGVSTUIgPSAwDQogICAgICAgICAgbGV0IHBheWFibGVVU0QgPSAwDQogICAgICAgICAgaXRlbS5yc0NoYXJnZUxpc3QubWFwKHJzQ2hhcmdlID0+IHsNCiAgICAgICAgICAgIGlmIChyc0NoYXJnZS5zdWJ0b3RhbCkgew0KICAgICAgICAgICAgICBpZiAocnNDaGFyZ2UuZG5DdXJyZW5jeUNvZGUgPT09ICJVU0QiKSB7DQogICAgICAgICAgICAgICAgcGF5YWJsZVVTRCA9IGN1cnJlbmN5KHBheWFibGVVU0QpLmFkZChyc0NoYXJnZS5zdWJ0b3RhbCkudmFsdWUNCiAgICAgICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgICAgICBwYXlhYmxlUk1CID0gY3VycmVuY3kocGF5YWJsZVJNQikuYWRkKHJzQ2hhcmdlLnN1YnRvdGFsKS52YWx1ZQ0KICAgICAgICAgICAgICB9DQogICAgICAgICAgICB9DQogICAgICAgICAgfSkNCiAgICAgICAgICBpdGVtLnBheWFibGVSTUIgPSBwYXlhYmxlUk1CDQogICAgICAgICAgaXRlbS5wYXlhYmxlVVNEID0gcGF5YWJsZVVTRA0KICAgICAgICB9KQ0KICAgICAgfQ0KICAgIH0sDQogICAgImZvcm0ucnNPcFNlYUxjbExpc3QiKG4pIHsNCiAgICAgIGlmIChuICYmIG4ubGVuZ3RoID4gMCkgew0KICAgICAgICBuLm1hcChpdGVtID0+IHsNCiAgICAgICAgICBsZXQgcGF5YWJsZVJNQiA9IDANCiAgICAgICAgICBsZXQgcGF5YWJsZVVTRCA9IDANCiAgICAgICAgICBpdGVtLnJzQ2hhcmdlTGlzdC5tYXAocnNDaGFyZ2UgPT4gew0KICAgICAgICAgICAgaWYgKHJzQ2hhcmdlLnN1YnRvdGFsKSB7DQogICAgICAgICAgICAgIGlmIChyc0NoYXJnZS5kbkN1cnJlbmN5Q29kZSA9PT0gIlVTRCIpIHsNCiAgICAgICAgICAgICAgICBwYXlhYmxlVVNEID0gY3VycmVuY3kocGF5YWJsZVVTRCkuYWRkKHJzQ2hhcmdlLnN1YnRvdGFsKS52YWx1ZQ0KICAgICAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgICAgIHBheWFibGVSTUIgPSBjdXJyZW5jeShwYXlhYmxlUk1CKS5hZGQocnNDaGFyZ2Uuc3VidG90YWwpLnZhbHVlDQogICAgICAgICAgICAgIH0NCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9KQ0KICAgICAgICAgIGl0ZW0ucGF5YWJsZVJNQiA9IHBheWFibGVSTUINCiAgICAgICAgICBpdGVtLnBheWFibGVVU0QgPSBwYXlhYmxlVVNEDQogICAgICAgIH0pDQogICAgICB9DQogICAgfSwNCiAgICAiZm9ybS5yc09wT3RoZXIucnNDaGFyZ2VMaXN0IihuKSB7DQogICAgICBpZiAobiAmJiBuLmxlbmd0aCA+IDApIHsNCiAgICAgICAgbGV0IHBheWFibGVSTUIgPSAwDQogICAgICAgIGxldCBwYXlhYmxlVVNEID0gMA0KICAgICAgICBuLm1hcChyc0NoYXJnZSA9PiB7DQogICAgICAgICAgaWYgKHJzQ2hhcmdlLmRuQ3VycmVuY3lDb2RlID09PSAiVVNEIikgew0KICAgICAgICAgICAgcGF5YWJsZVVTRCA9IGN1cnJlbmN5KHBheWFibGVVU0QpLmFkZChyc0NoYXJnZS5zdWJ0b3RhbCkudmFsdWUNCiAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgcGF5YWJsZVJNQiA9IGN1cnJlbmN5KHBheWFibGVSTUIpLmFkZChyc0NoYXJnZS5zdWJ0b3RhbCkudmFsdWUNCiAgICAgICAgICB9DQogICAgICAgIH0pDQogICAgICAgIHRoaXMucnNPcE90aGVyUGF5YWJsZVJNQiA9IHBheWFibGVSTUINCiAgICAgICAgdGhpcy5yc09wT3RoZXJQYXlhYmxlVVNEID0gcGF5YWJsZVVTRA0KICAgICAgfQ0KICAgIH0sDQogICAgImZvcm0ucnNPcEFpckxpc3QiKG4pIHsNCiAgICAgIGlmIChuICYmIG4ubGVuZ3RoID4gMCkgew0KICAgICAgICBuLm1hcChpdGVtID0+IHsNCiAgICAgICAgICBsZXQgcGF5YWJsZVJNQiA9IDANCiAgICAgICAgICBsZXQgcGF5YWJsZVVTRCA9IDANCiAgICAgICAgICBpdGVtLnJzQ2hhcmdlTGlzdC5tYXAocnNDaGFyZ2UgPT4gew0KICAgICAgICAgICAgaWYgKHJzQ2hhcmdlLnN1YnRvdGFsKSB7DQogICAgICAgICAgICAgIGlmIChyc0NoYXJnZS5kbkN1cnJlbmN5Q29kZSA9PT0gIlVTRCIpIHsNCiAgICAgICAgICAgICAgICBwYXlhYmxlVVNEID0gY3VycmVuY3kocGF5YWJsZVVTRCkuYWRkKHJzQ2hhcmdlLnN1YnRvdGFsKS52YWx1ZQ0KICAgICAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgICAgIHBheWFibGVSTUIgPSBjdXJyZW5jeShwYXlhYmxlUk1CKS5hZGQocnNDaGFyZ2Uuc3VidG90YWwpLnZhbHVlDQogICAgICAgICAgICAgIH0NCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9KQ0KICAgICAgICAgIGl0ZW0ucGF5YWJsZVJNQiA9IHBheWFibGVSTUINCiAgICAgICAgICBpdGVtLnBheWFibGVVU0QgPSBwYXlhYmxlVVNEDQogICAgICAgIH0pDQogICAgICB9DQogICAgfSwNCiAgICAicnNPcFJhaWxGQ0wucnNDaGFyZ2VMaXN0IihuKSB7DQogICAgICBpZiAobiAmJiBuLmxlbmd0aCA+IDApIHsNCiAgICAgICAgbGV0IHBheWFibGVSTUIgPSAwDQogICAgICAgIGxldCBwYXlhYmxlVVNEID0gMA0KICAgICAgICBuLm1hcChyc0NoYXJnZSA9PiB7DQogICAgICAgICAgaWYgKHJzQ2hhcmdlLnN1YnRvdGFsKSB7DQogICAgICAgICAgICBpZiAocnNDaGFyZ2UuZG5DdXJyZW5jeUNvZGUgPT09ICJVU0QiKSB7DQogICAgICAgICAgICAgIHBheWFibGVVU0QgPSBjdXJyZW5jeShwYXlhYmxlVVNEKS5hZGQocnNDaGFyZ2Uuc3VidG90YWwpLnZhbHVlDQogICAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgICBwYXlhYmxlUk1CID0gY3VycmVuY3kocGF5YWJsZVJNQikuYWRkKHJzQ2hhcmdlLnN1YnRvdGFsKS52YWx1ZQ0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0NCiAgICAgICAgfSkNCiAgICAgICAgdGhpcy5yc09wUmFpbEZjbFBheWFibGVSTUIgPSBwYXlhYmxlUk1CDQogICAgICAgIHRoaXMucnNPcFJhaWxGY2xQYXlhYmxlVVNEID0gcGF5YWJsZVVTRA0KICAgICAgfQ0KICAgIH0sDQogICAgInJzT3BSYWlsTENMLnJzQ2hhcmdlTGlzdCIobikgew0KICAgICAgaWYgKG4gJiYgbi5sZW5ndGggPiAwKSB7DQogICAgICAgIGxldCBwYXlhYmxlUk1CID0gMA0KICAgICAgICBsZXQgcGF5YWJsZVVTRCA9IDANCiAgICAgICAgbi5tYXAocnNDaGFyZ2UgPT4gew0KICAgICAgICAgIGlmIChyc0NoYXJnZS5zdWJ0b3RhbCkgew0KICAgICAgICAgICAgaWYgKHJzQ2hhcmdlLmRuQ3VycmVuY3lDb2RlID09PSAiVVNEIikgew0KICAgICAgICAgICAgICBwYXlhYmxlVVNEID0gY3VycmVuY3kocGF5YWJsZVVTRCkuYWRkKHJzQ2hhcmdlLnN1YnRvdGFsKS52YWx1ZQ0KICAgICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgICAgcGF5YWJsZVJNQiA9IGN1cnJlbmN5KHBheWFibGVSTUIpLmFkZChyc0NoYXJnZS5zdWJ0b3RhbCkudmFsdWUNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9DQogICAgICAgIH0pDQogICAgICAgIHRoaXMucnNPcFJhaWxMY2xQYXlhYmxlUk1CID0gcGF5YWJsZVJNQg0KICAgICAgICB0aGlzLnJzT3BSYWlsTGNsUGF5YWJsZVVTRCA9IHBheWFibGVVU0QNCiAgICAgIH0NCiAgICB9LA0KICAgICJyc09wRXhwcmVzcy5yc0NoYXJnZUxpc3QiKG4pIHsNCiAgICAgIGlmIChuICYmIG4ubGVuZ3RoID4gMCkgew0KICAgICAgICBsZXQgcGF5YWJsZVJNQiA9IDANCiAgICAgICAgbGV0IHBheWFibGVVU0QgPSAwDQogICAgICAgIG4ubWFwKHJzQ2hhcmdlID0+IHsNCiAgICAgICAgICBpZiAocnNDaGFyZ2Uuc3VidG90YWwpIHsNCiAgICAgICAgICAgIGlmIChyc0NoYXJnZS5kbkN1cnJlbmN5Q29kZSA9PT0gIlVTRCIpIHsNCiAgICAgICAgICAgICAgcGF5YWJsZVVTRCA9IGN1cnJlbmN5KHBheWFibGVVU0QpLmFkZChyc0NoYXJnZS5zdWJ0b3RhbCkudmFsdWUNCiAgICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICAgIHBheWFibGVSTUIgPSBjdXJyZW5jeShwYXlhYmxlUk1CKS5hZGQocnNDaGFyZ2Uuc3VidG90YWwpLnZhbHVlDQogICAgICAgICAgICB9DQogICAgICAgICAgfQ0KICAgICAgICB9KQ0KICAgICAgICB0aGlzLnJzT3BFeHByZXNzUGF5YWJsZVJNQiA9IHBheWFibGVSTUINCiAgICAgICAgdGhpcy5yc09wRXhwcmVzc1BheWFibGVVU0QgPSBwYXlhYmxlVVNEDQogICAgICB9DQogICAgfSwNCiAgICAiZm9ybS5yc09wQ3RuclRydWNrTGlzdCIobikgew0KICAgICAgaWYgKG4gJiYgbi5sZW5ndGggPiAwKSB7DQogICAgICAgIG4ubWFwKGl0ZW0gPT4gew0KICAgICAgICAgIGxldCBwYXlhYmxlUk1CID0gMA0KICAgICAgICAgIGxldCBwYXlhYmxlVVNEID0gMA0KICAgICAgICAgIGl0ZW0ucnNDaGFyZ2VMaXN0Lm1hcChyc0NoYXJnZSA9PiB7DQogICAgICAgICAgICBpZiAocnNDaGFyZ2Uuc3VidG90YWwpIHsNCiAgICAgICAgICAgICAgaWYgKHJzQ2hhcmdlLmRuQ3VycmVuY3lDb2RlID09PSAiVVNEIikgew0KICAgICAgICAgICAgICAgIHBheWFibGVVU0QgPSBjdXJyZW5jeShwYXlhYmxlVVNEKS5hZGQocnNDaGFyZ2Uuc3VidG90YWwpLnZhbHVlDQogICAgICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICAgICAgcGF5YWJsZVJNQiA9IGN1cnJlbmN5KHBheWFibGVSTUIpLmFkZChyc0NoYXJnZS5zdWJ0b3RhbCkudmFsdWUNCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0pDQogICAgICAgICAgaXRlbS5wYXlhYmxlUk1CID0gcGF5YWJsZVJNQg0KICAgICAgICAgIGl0ZW0ucGF5YWJsZVVTRCA9IHBheWFibGVVU0QNCiAgICAgICAgfSkNCiAgICAgIH0NCiAgICB9LA0KICAgICJmb3JtLnJzT3BCdWxrVHJ1Y2tMaXN0IihuKSB7DQogICAgICBpZiAobiAmJiBuLmxlbmd0aCA+IDApIHsNCiAgICAgICAgbi5tYXAoaXRlbSA9PiB7DQogICAgICAgICAgbGV0IHBheWFibGVSTUIgPSAwDQogICAgICAgICAgbGV0IHBheWFibGVVU0QgPSAwDQogICAgICAgICAgaXRlbS5yc0NoYXJnZUxpc3QubWFwKHJzQ2hhcmdlID0+IHsNCiAgICAgICAgICAgIGlmIChyc0NoYXJnZS5zdWJ0b3RhbCkgew0KICAgICAgICAgICAgICBpZiAocnNDaGFyZ2UuZG5DdXJyZW5jeUNvZGUgPT09ICJVU0QiKSB7DQogICAgICAgICAgICAgICAgcGF5YWJsZVVTRCA9IGN1cnJlbmN5KHBheWFibGVVU0QpLmFkZChyc0NoYXJnZS5zdWJ0b3RhbCkudmFsdWUNCiAgICAgICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgICAgICBwYXlhYmxlUk1CID0gY3VycmVuY3kocGF5YWJsZVJNQikuYWRkKHJzQ2hhcmdlLnN1YnRvdGFsKS52YWx1ZQ0KICAgICAgICAgICAgICB9DQogICAgICAgICAgICB9DQogICAgICAgICAgfSkNCiAgICAgICAgICBpdGVtLnBheWFibGVSTUIgPSBwYXlhYmxlUk1CDQogICAgICAgICAgaXRlbS5wYXlhYmxlVVNEID0gcGF5YWJsZVVTRA0KICAgICAgICB9KQ0KICAgICAgfQ0KICAgIH0sDQogICAgImZvcm0ucnNPcERvY0RlY2xhcmVMaXN0IihuKSB7DQogICAgICBpZiAobiAmJiBuLmxlbmd0aCA+IDApIHsNCiAgICAgICAgbi5tYXAoaXRlbSA9PiB7DQogICAgICAgICAgbGV0IHBheWFibGVSTUIgPSAwDQogICAgICAgICAgbGV0IHBheWFibGVVU0QgPSAwDQogICAgICAgICAgaXRlbS5yc0NoYXJnZUxpc3QubWFwKHJzQ2hhcmdlID0+IHsNCiAgICAgICAgICAgIGlmIChyc0NoYXJnZS5zdWJ0b3RhbCkgew0KICAgICAgICAgICAgICBpZiAocnNDaGFyZ2UuZG5DdXJyZW5jeUNvZGUgPT09ICJVU0QiKSB7DQogICAgICAgICAgICAgICAgcGF5YWJsZVVTRCA9IGN1cnJlbmN5KHBheWFibGVVU0QpLmFkZChyc0NoYXJnZS5zdWJ0b3RhbCkudmFsdWUNCiAgICAgICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgICAgICBwYXlhYmxlUk1CID0gY3VycmVuY3kocGF5YWJsZVJNQikuYWRkKHJzQ2hhcmdlLnN1YnRvdGFsKS52YWx1ZQ0KICAgICAgICAgICAgICB9DQogICAgICAgICAgICB9DQogICAgICAgICAgfSkNCiAgICAgICAgICBpdGVtLnBheWFibGVSTUIgPSBwYXlhYmxlUk1CDQogICAgICAgICAgaXRlbS5wYXlhYmxlVVNEID0gcGF5YWJsZVVTRA0KICAgICAgICB9KQ0KICAgICAgfQ0KICAgIH0sDQogICAgImZvcm0ucnNPcEZyZWVEZWNsYXJlTGlzdCIobikgew0KICAgICAgaWYgKG4gJiYgbi5sZW5ndGggPiAwKSB7DQogICAgICAgIG4ubWFwKGl0ZW0gPT4gew0KICAgICAgICAgIGxldCBwYXlhYmxlUk1CID0gMA0KICAgICAgICAgIGxldCBwYXlhYmxlVVNEID0gMA0KICAgICAgICAgIGl0ZW0ucnNDaGFyZ2VMaXN0Lm1hcChyc0NoYXJnZSA9PiB7DQogICAgICAgICAgICBpZiAocnNDaGFyZ2Uuc3VidG90YWwpIHsNCiAgICAgICAgICAgICAgaWYgKHJzQ2hhcmdlLmRuQ3VycmVuY3lDb2RlID09PSAiVVNEIikgew0KICAgICAgICAgICAgICAgIHBheWFibGVVU0QgPSBjdXJyZW5jeShwYXlhYmxlVVNEKS5hZGQocnNDaGFyZ2Uuc3VidG90YWwpLnZhbHVlDQogICAgICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICAgICAgcGF5YWJsZVJNQiA9IGN1cnJlbmN5KHBheWFibGVSTUIpLmFkZChyc0NoYXJnZS5zdWJ0b3RhbCkudmFsdWUNCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0pDQogICAgICAgICAgaXRlbS5wYXlhYmxlUk1CID0gcGF5YWJsZVJNQg0KICAgICAgICAgIGl0ZW0ucGF5YWJsZVVTRCA9IHBheWFibGVVU0QNCiAgICAgICAgfSkNCiAgICAgIH0NCiAgICB9LA0KICAgICJyc09wRE9BZ2VudC5yc0NoYXJnZUxpc3QiKG4pIHsNCiAgICAgIGlmIChuICYmIG4ubGVuZ3RoID4gMCkgew0KICAgICAgICBsZXQgcGF5YWJsZVJNQiA9IDANCiAgICAgICAgbGV0IHBheWFibGVVU0QgPSAwDQogICAgICAgIG4ubWFwKHJzQ2hhcmdlID0+IHsNCiAgICAgICAgICBpZiAocnNDaGFyZ2Uuc3VidG90YWwpIHsNCiAgICAgICAgICAgIGlmIChyc0NoYXJnZS5kbkN1cnJlbmN5Q29kZSA9PT0gIlVTRCIpIHsNCiAgICAgICAgICAgICAgcGF5YWJsZVVTRCA9IGN1cnJlbmN5KHBheWFibGVVU0QpLmFkZChyc0NoYXJnZS5zdWJ0b3RhbCkudmFsdWUNCiAgICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICAgIHBheWFibGVSTUIgPSBjdXJyZW5jeShwYXlhYmxlUk1CKS5hZGQocnNDaGFyZ2Uuc3VidG90YWwpLnZhbHVlDQogICAgICAgICAgICB9DQogICAgICAgICAgfQ0KICAgICAgICB9KQ0KICAgICAgICB0aGlzLnJzT3BET0FnZW50UGF5YWJsZVJNQiA9IHBheWFibGVSTUINCiAgICAgICAgdGhpcy5yc09wRE9BZ2VudFBheWFibGVVU0QgPSBwYXlhYmxlVVNEDQogICAgICB9DQogICAgfSwNCiAgICAicnNPcENsZWFyQWdlbnQucnNDaGFyZ2VMaXN0IihuKSB7DQogICAgICBpZiAobiAmJiBuLmxlbmd0aCA+IDApIHsNCiAgICAgICAgbGV0IHBheWFibGVSTUIgPSAwDQogICAgICAgIGxldCBwYXlhYmxlVVNEID0gMA0KICAgICAgICBuLm1hcChyc0NoYXJnZSA9PiB7DQogICAgICAgICAgaWYgKHJzQ2hhcmdlLnN1YnRvdGFsKSB7DQogICAgICAgICAgICBpZiAocnNDaGFyZ2UuZG5DdXJyZW5jeUNvZGUgPT09ICJVU0QiKSB7DQogICAgICAgICAgICAgIHBheWFibGVVU0QgPSBjdXJyZW5jeShwYXlhYmxlVVNEKS5hZGQocnNDaGFyZ2Uuc3VidG90YWwpLnZhbHVlDQogICAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgICBwYXlhYmxlUk1CID0gY3VycmVuY3kocGF5YWJsZVJNQikuYWRkKHJzQ2hhcmdlLnN1YnRvdGFsKS52YWx1ZQ0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0NCiAgICAgICAgfSkNCiAgICAgICAgdGhpcy5yc09wQ2xlYXJBZ2VudFBheWFibGVSTUIgPSBwYXlhYmxlUk1CDQogICAgICAgIHRoaXMucnNPcENsZWFyQWdlbnRQYXlhYmxlVVNEID0gcGF5YWJsZVVTRA0KICAgICAgfQ0KICAgIH0sDQogICAgInJzT3BXSFMucnNDaGFyZ2VMaXN0IihuKSB7DQogICAgICBpZiAobiAmJiBuLmxlbmd0aCA+IDApIHsNCiAgICAgICAgbGV0IHBheWFibGVSTUIgPSAwDQogICAgICAgIGxldCBwYXlhYmxlVVNEID0gMA0KICAgICAgICBuLm1hcChyc0NoYXJnZSA9PiB7DQogICAgICAgICAgaWYgKHJzQ2hhcmdlLnN1YnRvdGFsKSB7DQogICAgICAgICAgICBpZiAocnNDaGFyZ2UuZG5DdXJyZW5jeUNvZGUgPT09ICJVU0QiKSB7DQogICAgICAgICAgICAgIHBheWFibGVVU0QgPSBjdXJyZW5jeShwYXlhYmxlVVNEKS5hZGQocnNDaGFyZ2Uuc3VidG90YWwpLnZhbHVlDQogICAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgICBwYXlhYmxlUk1CID0gY3VycmVuY3kocGF5YWJsZVJNQikuYWRkKHJzQ2hhcmdlLnN1YnRvdGFsKS52YWx1ZQ0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0NCiAgICAgICAgfSkNCiAgICAgICAgdGhpcy5yc09wV0hTUGF5YWJsZVJNQiA9IHBheWFibGVSTUINCiAgICAgICAgdGhpcy5yc09wV0hTUGF5YWJsZVVTRCA9IHBheWFibGVVU0QNCiAgICAgIH0NCiAgICB9LA0KICAgICJyc09wM3JkQ2VydC5yc0NoYXJnZUxpc3QiKG4pIHsNCiAgICAgIGlmIChuICYmIG4ubGVuZ3RoID4gMCkgew0KICAgICAgICBsZXQgcGF5YWJsZVJNQiA9IDANCiAgICAgICAgbGV0IHBheWFibGVVU0QgPSAwDQogICAgICAgIG4ubWFwKHJzQ2hhcmdlID0+IHsNCiAgICAgICAgICBpZiAocnNDaGFyZ2Uuc3VidG90YWwpIHsNCiAgICAgICAgICAgIGlmIChyc0NoYXJnZS5kbkN1cnJlbmN5Q29kZSA9PT0gIlVTRCIpIHsNCiAgICAgICAgICAgICAgcGF5YWJsZVVTRCA9IGN1cnJlbmN5KHBheWFibGVVU0QpLmFkZChyc0NoYXJnZS5zdWJ0b3RhbCkudmFsdWUNCiAgICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICAgIHBheWFibGVSTUIgPSBjdXJyZW5jeShwYXlhYmxlUk1CKS5hZGQocnNDaGFyZ2Uuc3VidG90YWwpLnZhbHVlDQogICAgICAgICAgICB9DQogICAgICAgICAgfQ0KICAgICAgICB9KQ0KICAgICAgICB0aGlzLnJzT3AzcmRDZXJ0UGF5YWJsZVJNQiA9IHBheWFibGVSTUINCiAgICAgICAgdGhpcy5yc09wM3JkQ2VydFBheWFibGVVU0QgPSBwYXlhYmxlVVNEDQogICAgICB9DQogICAgfSwNCiAgICAicnNPcElOUy5yc0NoYXJnZUxpc3QiKG4pIHsNCiAgICAgIGlmIChuICYmIG4ubGVuZ3RoID4gMCkgew0KICAgICAgICBsZXQgcGF5YWJsZVJNQiA9IDANCiAgICAgICAgbGV0IHBheWFibGVVU0QgPSAwDQogICAgICAgIG4ubWFwKHJzQ2hhcmdlID0+IHsNCiAgICAgICAgICBpZiAocnNDaGFyZ2Uuc3VidG90YWwpIHsNCiAgICAgICAgICAgIGlmIChyc0NoYXJnZS5kbkN1cnJlbmN5Q29kZSA9PT0gIlVTRCIpIHsNCiAgICAgICAgICAgICAgcGF5YWJsZVVTRCA9IGN1cnJlbmN5KHBheWFibGVVU0QpLmFkZChyc0NoYXJnZS5zdWJ0b3RhbCkudmFsdWUNCiAgICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICAgIHBheWFibGVSTUIgPSBjdXJyZW5jeShwYXlhYmxlUk1CKS5hZGQocnNDaGFyZ2Uuc3VidG90YWwpLnZhbHVlDQogICAgICAgICAgICB9DQogICAgICAgICAgfQ0KICAgICAgICB9KQ0KICAgICAgICB0aGlzLnJzT3BJTlNQYXlhYmxlUk1CID0gcGF5YWJsZVJNQg0KICAgICAgICB0aGlzLnJzT3BJTlNQYXlhYmxlVVNEID0gcGF5YWJsZVVTRA0KICAgICAgfQ0KICAgIH0sDQogICAgInJzT3BUcmFkaW5nLnJzQ2hhcmdlTGlzdCIobikgew0KICAgICAgaWYgKG4gJiYgbi5sZW5ndGggPiAwKSB7DQogICAgICAgIGxldCBwYXlhYmxlUk1CID0gMA0KICAgICAgICBsZXQgcGF5YWJsZVVTRCA9IDANCiAgICAgICAgbi5tYXAocnNDaGFyZ2UgPT4gew0KICAgICAgICAgIGlmIChyc0NoYXJnZS5zdWJ0b3RhbCkgew0KICAgICAgICAgICAgaWYgKHJzQ2hhcmdlLmRuQ3VycmVuY3lDb2RlID09PSAiVVNEIikgew0KICAgICAgICAgICAgICBwYXlhYmxlVVNEID0gY3VycmVuY3kocGF5YWJsZVVTRCkuYWRkKHJzQ2hhcmdlLnN1YnRvdGFsKS52YWx1ZQ0KICAgICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgICAgcGF5YWJsZVJNQiA9IGN1cnJlbmN5KHBheWFibGVSTUIpLmFkZChyc0NoYXJnZS5zdWJ0b3RhbCkudmFsdWUNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9DQogICAgICAgIH0pDQogICAgICAgIHRoaXMucnNPcFRyYWRpbmdQYXlhYmxlUk1CID0gcGF5YWJsZVJNQg0KICAgICAgICB0aGlzLnJzT3BUcmFkaW5nUGF5YWJsZVVTRCA9IHBheWFibGVVU0QNCiAgICAgIH0NCiAgICB9LA0KICAgICJyc09wRnVtaWdhdGlvbi5yc0NoYXJnZUxpc3QiKG4pIHsNCiAgICAgIGlmIChuICYmIG4ubGVuZ3RoID4gMCkgew0KICAgICAgICBsZXQgcGF5YWJsZVJNQiA9IDANCiAgICAgICAgbGV0IHBheWFibGVVU0QgPSAwDQogICAgICAgIG4ubWFwKHJzQ2hhcmdlID0+IHsNCiAgICAgICAgICBpZiAocnNDaGFyZ2Uuc3VidG90YWwpIHsNCiAgICAgICAgICAgIGlmIChyc0NoYXJnZS5kbkN1cnJlbmN5Q29kZSA9PT0gIlVTRCIpIHsNCiAgICAgICAgICAgICAgcGF5YWJsZVVTRCA9IGN1cnJlbmN5KHBheWFibGVVU0QpLmFkZChyc0NoYXJnZS5zdWJ0b3RhbCkudmFsdWUNCiAgICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICAgIHBheWFibGVSTUIgPSBjdXJyZW5jeShwYXlhYmxlUk1CKS5hZGQocnNDaGFyZ2Uuc3VidG90YWwpLnZhbHVlDQogICAgICAgICAgICB9DQogICAgICAgICAgfQ0KICAgICAgICB9KQ0KICAgICAgICB0aGlzLnJzT3BGdW1pZ2F0aW9uUGF5YWJsZVJNQiA9IHBheWFibGVSTUINCiAgICAgICAgdGhpcy5yc09wRnVtaWdhdGlvblBheWFibGVVU0QgPSBwYXlhYmxlVVNEDQogICAgICB9DQogICAgfSwNCiAgICAicnNPcENPLnJzQ2hhcmdlTGlzdCIobikgew0KICAgICAgaWYgKG4gJiYgbi5sZW5ndGggPiAwKSB7DQogICAgICAgIGxldCBwYXlhYmxlUk1CID0gMA0KICAgICAgICBsZXQgcGF5YWJsZVVTRCA9IDANCiAgICAgICAgbi5tYXAocnNDaGFyZ2UgPT4gew0KICAgICAgICAgIGlmIChyc0NoYXJnZS5zdWJ0b3RhbCkgew0KICAgICAgICAgICAgaWYgKHJzQ2hhcmdlLmRuQ3VycmVuY3lDb2RlID09PSAiVVNEIikgew0KICAgICAgICAgICAgICBwYXlhYmxlVVNEID0gY3VycmVuY3kocGF5YWJsZVVTRCkuYWRkKHJzQ2hhcmdlLnN1YnRvdGFsKS52YWx1ZQ0KICAgICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgICAgcGF5YWJsZVJNQiA9IGN1cnJlbmN5KHBheWFibGVSTUIpLmFkZChyc0NoYXJnZS5zdWJ0b3RhbCkudmFsdWUNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9DQogICAgICAgIH0pDQogICAgICAgIHRoaXMucnNPcENPUGF5YWJsZVJNQiA9IHBheWFibGVSTUINCiAgICAgICAgdGhpcy5yc09wQ09QYXlhYmxlVVNEID0gcGF5YWJsZVVTRA0KICAgICAgfQ0KICAgIH0NCiAgfSwNCiAgY29tcHV0ZWQ6IHsNCg0KICAgIHJzT3BTZWFsRmNsRm9ybURpc2FibGUoKSB7DQogICAgICByZXR1cm4gISEodGhpcy5yc09wU2VhRmNsU2VydmljZUluc3RhbmNlLmlzRG5PcENvbmZpcm1lZCB8fCB0aGlzLnJzT3BTZWFGY2xTZXJ2aWNlSW5zdGFuY2UuaXNEblBzYUNvbmZpcm1lZCB8fCB0aGlzLnJzT3BTZWFGY2xTZXJ2aWNlSW5zdGFuY2UuaXNEblN1cHBsaWVyQ29uZmlybWVkIHx8IHRoaXMucnNPcFNlYUZjbFNlcnZpY2VJbnN0YW5jZS5pc0FjY291bnRDb25maXJtZWQgfHwgdGhpcy5yc09wU2VhRmNsU2VydmljZUluc3RhbmNlLmlzRG5DbGllbnRDb25maXJtZWQgfHwgdGhpcy5yc09wU2VhRmNsU2VydmljZUluc3RhbmNlLmlzRG5TYWxlc0NvbmZpcm1lZCkNCiAgICB9LA0KICAgIHJzT3BTZWFsTGNsRm9ybURpc2FibGUoKSB7DQogICAgICByZXR1cm4gISEodGhpcy5yc09wU2VhTGNsU2VydmljZUluc3RhbmNlLmlzRG5PcENvbmZpcm1lZCB8fCB0aGlzLnJzT3BTZWFMY2xTZXJ2aWNlSW5zdGFuY2UuaXNEblBzYUNvbmZpcm1lZCB8fCB0aGlzLnJzT3BTZWFMY2xTZXJ2aWNlSW5zdGFuY2UuaXNEblN1cHBsaWVyQ29uZmlybWVkIHx8IHRoaXMucnNPcFNlYUxjbFNlcnZpY2VJbnN0YW5jZS5pc0FjY291bnRDb25maXJtZWQgfHwgdGhpcy5yc09wU2VhRmNsU2VydmljZUluc3RhbmNlLmlzRG5DbGllbnRDb25maXJtZWQgfHwgdGhpcy5yc09wU2VhRmNsU2VydmljZUluc3RhbmNlLmlzRG5TYWxlc0NvbmZpcm1lZCkNCiAgICB9LA0KICAgIHJzT3BBaXJGb3JtRGlzYWJsZSgpIHsNCiAgICAgIHJldHVybiAhISh0aGlzLnJzT3BBaXJTZXJ2aWNlSW5zdGFuY2UuaXNEbk9wQ29uZmlybWVkIHx8IHRoaXMucnNPcEFpclNlcnZpY2VJbnN0YW5jZS5pc0RuUHNhQ29uZmlybWVkIHx8IHRoaXMucnNPcEFpclNlcnZpY2VJbnN0YW5jZS5pc0RuU3VwcGxpZXJDb25maXJtZWQgfHwgdGhpcy5yc09wQWlyU2VydmljZUluc3RhbmNlLmlzQWNjb3VudENvbmZpcm1lZCB8fCB0aGlzLnJzT3BBaXJTZXJ2aWNlSW5zdGFuY2UuaXNEbkNsaWVudENvbmZpcm1lZCB8fCB0aGlzLnJzT3BBaXJTZXJ2aWNlSW5zdGFuY2UuaXNEblNhbGVzQ29uZmlybWVkKQ0KICAgIH0sDQogICAgcnNPcFJhaWxGY2xGb3JtRGlzYWJsZSgpIHsNCiAgICAgIHJldHVybiAhISh0aGlzLnJzT3BSYWlsRmNsU2VydmljZUluc3RhbmNlLmlzRG5PcENvbmZpcm1lZCB8fCB0aGlzLnJzT3BSYWlsRmNsU2VydmljZUluc3RhbmNlLmlzRG5Qc2FDb25maXJtZWQgfHwgdGhpcy5yc09wUmFpbEZjbFNlcnZpY2VJbnN0YW5jZS5pc0RuU3VwcGxpZXJDb25maXJtZWQgfHwgdGhpcy5yc09wUmFpbEZjbFNlcnZpY2VJbnN0YW5jZS5pc0FjY291bnRDb25maXJtZWQgfHwgdGhpcy5yc09wUmFpbEZjbFNlcnZpY2VJbnN0YW5jZS5pc0RuQ2xpZW50Q29uZmlybWVkIHx8IHRoaXMucnNPcFJhaWxGY2xTZXJ2aWNlSW5zdGFuY2UuaXNEblNhbGVzQ29uZmlybWVkKQ0KICAgIH0sDQogICAgcnNPcFJhaWxMY2xGb3JtRGlzYWJsZSgpIHsNCiAgICAgIHJldHVybiAhISh0aGlzLnJzT3BSYWlsTGNsU2VydmljZUluc3RhbmNlLmlzRG5PcENvbmZpcm1lZCB8fCB0aGlzLnJzT3BSYWlsTGNsU2VydmljZUluc3RhbmNlLmlzRG5Qc2FDb25maXJtZWQgfHwgdGhpcy5yc09wUmFpbExjbFNlcnZpY2VJbnN0YW5jZS5pc0RuU3VwcGxpZXJDb25maXJtZWQgfHwgdGhpcy5yc09wUmFpbExjbFNlcnZpY2VJbnN0YW5jZS5pc0FjY291bnRDb25maXJtZWQgfHwgdGhpcy5yc09wUmFpbExjbFNlcnZpY2VJbnN0YW5jZS5pc0RuQ2xpZW50Q29uZmlybWVkIHx8IHRoaXMucnNPcFJhaWxMY2xTZXJ2aWNlSW5zdGFuY2UuaXNEblNhbGVzQ29uZmlybWVkKQ0KICAgIH0sDQogICAgcnNPcEV4cHJlc3NGb3JtRGlzYWJsZSgpIHsNCiAgICAgIHJldHVybiAhISh0aGlzLnJzT3BFeHByZXNzU2VydmljZUluc3RhbmNlLmlzRG5PcENvbmZpcm1lZCB8fCB0aGlzLnJzT3BFeHByZXNzU2VydmljZUluc3RhbmNlLmlzRG5Qc2FDb25maXJtZWQgfHwgdGhpcy5yc09wRXhwcmVzc1NlcnZpY2VJbnN0YW5jZS5pc0RuU3VwcGxpZXJDb25maXJtZWQgfHwgdGhpcy5yc09wRXhwcmVzc1NlcnZpY2VJbnN0YW5jZS5pc0FjY291bnRDb25maXJtZWQgfHwgdGhpcy5yc09wRXhwcmVzc1NlcnZpY2VJbnN0YW5jZS5pc0RuQ2xpZW50Q29uZmlybWVkIHx8IHRoaXMucnNPcEV4cHJlc3NTZXJ2aWNlSW5zdGFuY2UuaXNEblNhbGVzQ29uZmlybWVkKQ0KICAgIH0sDQogICAgcnNPcEN0bnJUcnVja0Zvcm1EaXNhYmxlKCkgew0KICAgICAgcmV0dXJuICEhKHRoaXMucnNPcEN0bnJUcnVja1NlcnZpY2VJbnN0YW5jZS5pc0RuT3BDb25maXJtZWQgfHwgdGhpcy5yc09wQ3RuclRydWNrU2VydmljZUluc3RhbmNlLmlzRG5Qc2FDb25maXJtZWQgfHwgdGhpcy5yc09wQ3RuclRydWNrU2VydmljZUluc3RhbmNlLmlzRG5TdXBwbGllckNvbmZpcm1lZCB8fCB0aGlzLnJzT3BDdG5yVHJ1Y2tTZXJ2aWNlSW5zdGFuY2UuaXNBY2NvdW50Q29uZmlybWVkIHx8IHRoaXMucnNPcEN0bnJUcnVja1NlcnZpY2VJbnN0YW5jZS5pc0RuQ2xpZW50Q29uZmlybWVkIHx8IHRoaXMucnNPcEN0bnJUcnVja1NlcnZpY2VJbnN0YW5jZS5pc0RuU2FsZXNDb25maXJtZWQpDQogICAgfSwNCiAgICByc09wQnVsa1RydWNrRm9ybURpc2FibGUoKSB7DQogICAgICByZXR1cm4gISEodGhpcy5yc09wQnVsa1RydWNrU2VydmljZUluc3RhbmNlLmlzRG5PcENvbmZpcm1lZCB8fCB0aGlzLnJzT3BCdWxrVHJ1Y2tTZXJ2aWNlSW5zdGFuY2UuaXNEblBzYUNvbmZpcm1lZCB8fCB0aGlzLnJzT3BCdWxrVHJ1Y2tTZXJ2aWNlSW5zdGFuY2UuaXNEblN1cHBsaWVyQ29uZmlybWVkIHx8IHRoaXMucnNPcEJ1bGtUcnVja1NlcnZpY2VJbnN0YW5jZS5pc0FjY291bnRDb25maXJtZWQgfHwgdGhpcy5yc09wQnVsa1RydWNrU2VydmljZUluc3RhbmNlLmlzRG5DbGllbnRDb25maXJtZWQgfHwgdGhpcy5yc09wQnVsa1RydWNrU2VydmljZUluc3RhbmNlLmlzRG5TYWxlc0NvbmZpcm1lZCkNCiAgICB9LA0KICAgIHJzT3BEb2NEZWNsYXJlRm9ybURpc2FibGUoKSB7DQogICAgICByZXR1cm4gISEodGhpcy5yc09wSW1wb3J0Q3VzdG9tc0NsZWFyYW5jZVNlcnZpY2VJbnN0YW5jZS5pc0RuT3BDb25maXJtZWQgfHwgdGhpcy5yc09wSW1wb3J0Q3VzdG9tc0NsZWFyYW5jZVNlcnZpY2VJbnN0YW5jZS5pc0RuUHNhQ29uZmlybWVkIHx8IHRoaXMucnNPcEltcG9ydEN1c3RvbXNDbGVhcmFuY2VTZXJ2aWNlSW5zdGFuY2UuaXNEblN1cHBsaWVyQ29uZmlybWVkIHx8IHRoaXMucnNPcEltcG9ydEN1c3RvbXNDbGVhcmFuY2VTZXJ2aWNlSW5zdGFuY2UuaXNBY2NvdW50Q29uZmlybWVkIHx8IHRoaXMucnNPcEltcG9ydEN1c3RvbXNDbGVhcmFuY2VTZXJ2aWNlSW5zdGFuY2UuaXNEbkNsaWVudENvbmZpcm1lZCB8fCB0aGlzLnJzT3BJbXBvcnRDdXN0b21zQ2xlYXJhbmNlU2VydmljZUluc3RhbmNlLmlzRG5TYWxlc0NvbmZpcm1lZCkNCiAgICB9LA0KICAgIHJzT3BGcmVlRGVjbGFyZUZvcm1EaXNhYmxlKCkgew0KICAgICAgcmV0dXJuICEhKHRoaXMucnNPcEltcG9ydEN1c3RvbXNDbGVhcmFuY2VTZXJ2aWNlSW5zdGFuY2UuaXNEbk9wQ29uZmlybWVkIHx8IHRoaXMucnNPcEV4cG9ydEN1c3RvbXNDbGVhcmFuY2VTZXJ2aWNlSW5zdGFuY2UuaXNEblBzYUNvbmZpcm1lZCB8fCB0aGlzLnJzT3BFeHBvcnRDdXN0b21zQ2xlYXJhbmNlU2VydmljZUluc3RhbmNlLmlzRG5TdXBwbGllckNvbmZpcm1lZCB8fCB0aGlzLnJzT3BFeHBvcnRDdXN0b21zQ2xlYXJhbmNlU2VydmljZUluc3RhbmNlLmlzQWNjb3VudENvbmZpcm1lZCB8fCB0aGlzLnJzT3BFeHBvcnRDdXN0b21zQ2xlYXJhbmNlU2VydmljZUluc3RhbmNlLmlzRG5DbGllbnRDb25maXJtZWQgfHwgdGhpcy5yc09wRXhwb3J0Q3VzdG9tc0NsZWFyYW5jZVNlcnZpY2VJbnN0YW5jZS5pc0RuU2FsZXNDb25maXJtZWQpDQogICAgfSwNCiAgICByc09wRE9BZ2VudEZvcm1EaXNhYmxlKCkgew0KICAgICAgcmV0dXJuICEhKHRoaXMucnNPcEltcG9ydEN1c3RvbXNDbGVhcmFuY2VTZXJ2aWNlSW5zdGFuY2UuaXNEbk9wQ29uZmlybWVkIHx8IHRoaXMucnNPcEltcG9ydEN1c3RvbXNDbGVhcmFuY2VTZXJ2aWNlSW5zdGFuY2UuaXNEblBzYUNvbmZpcm1lZCB8fCB0aGlzLnJzT3BJbXBvcnRDdXN0b21zQ2xlYXJhbmNlU2VydmljZUluc3RhbmNlLmlzRG5TdXBwbGllckNvbmZpcm1lZCB8fCB0aGlzLnJzT3BJbXBvcnRDdXN0b21zQ2xlYXJhbmNlU2VydmljZUluc3RhbmNlLmlzQWNjb3VudENvbmZpcm1lZCB8fCB0aGlzLnJzT3BJbXBvcnRDdXN0b21zQ2xlYXJhbmNlU2VydmljZUluc3RhbmNlLmlzRG5DbGllbnRDb25maXJtZWQgfHwgdGhpcy5yc09wSW1wb3J0Q3VzdG9tc0NsZWFyYW5jZVNlcnZpY2VJbnN0YW5jZS5pc0RuU2FsZXNDb25maXJtZWQpDQogICAgfSwNCiAgICByc09wQ2xlYXJBZ2VudEZvcm1EaXNhYmxlKCkgew0KICAgICAgcmV0dXJuICEhKHRoaXMucnNPcENsZWFyQWdlbnRTZXJ2aWNlSW5zdGFuY2UuaXNEbk9wQ29uZmlybWVkIHx8IHRoaXMucnNPcENsZWFyQWdlbnRTZXJ2aWNlSW5zdGFuY2UuaXNEblBzYUNvbmZpcm1lZCB8fCB0aGlzLnJzT3BDbGVhckFnZW50U2VydmljZUluc3RhbmNlLmlzRG5TdXBwbGllckNvbmZpcm1lZCB8fCB0aGlzLnJzT3BDbGVhckFnZW50U2VydmljZUluc3RhbmNlLmlzQWNjb3VudENvbmZpcm1lZCB8fCB0aGlzLnJzT3BDbGVhckFnZW50U2VydmljZUluc3RhbmNlLmlzRG5DbGllbnRDb25maXJtZWQgfHwgdGhpcy5yc09wQ2xlYXJBZ2VudFNlcnZpY2VJbnN0YW5jZS5pc0RuU2FsZXNDb25maXJtZWQpDQogICAgfSwNCiAgICByc09wV0hTRm9ybURpc2FibGUoKSB7DQogICAgICByZXR1cm4gISEodGhpcy5yc09wV2FyZWhvdXNlU2VydmljZUluc3RhbmNlLmlzRG5PcENvbmZpcm1lZCB8fCB0aGlzLnJzT3BXYXJlaG91c2VTZXJ2aWNlSW5zdGFuY2UuaXNEblBzYUNvbmZpcm1lZCB8fCB0aGlzLnJzT3BXYXJlaG91c2VTZXJ2aWNlSW5zdGFuY2UuaXNEblN1cHBsaWVyQ29uZmlybWVkIHx8IHRoaXMucnNPcFdhcmVob3VzZVNlcnZpY2VJbnN0YW5jZS5pc0FjY291bnRDb25maXJtZWQgfHwgdGhpcy5yc09wV2FyZWhvdXNlU2VydmljZUluc3RhbmNlLmlzRG5DbGllbnRDb25maXJtZWQgfHwgdGhpcy5yc09wV2FyZWhvdXNlU2VydmljZUluc3RhbmNlLmlzRG5TYWxlc0NvbmZpcm1lZCkNCiAgICB9LA0KICAgIHJzQ2xpZW50TWVzc2FnZUZvcm1EaXNhYmxlKCkgew0KICAgICAgcmV0dXJuICEhKHRoaXMucnNDbGllbnRTZXJ2aWNlSW5zdGFuY2UuaXNEbk9wQ29uZmlybWVkIHx8IHRoaXMucnNDbGllbnRTZXJ2aWNlSW5zdGFuY2UuaXNEblBzYUNvbmZpcm1lZCB8fCB0aGlzLnJzQ2xpZW50U2VydmljZUluc3RhbmNlLmlzRG5TdXBwbGllckNvbmZpcm1lZCB8fCB0aGlzLnJzQ2xpZW50U2VydmljZUluc3RhbmNlLmlzQWNjb3VudENvbmZpcm1lZCB8fCB0aGlzLnJzQ2xpZW50U2VydmljZUluc3RhbmNlLmlzRG5DbGllbnRDb25maXJtZWQgfHwgdGhpcy5yc0NsaWVudFNlcnZpY2VJbnN0YW5jZS5pc0RuU2FsZXNDb25maXJtZWQpDQogICAgfSwNCiAgICByY3ROb0Rpc2FibGUoKSB7DQogICAgICByZXR1cm4gISEodGhpcy5mb3JtLnJjdE5vICYmIHRoaXMuZm9ybS5jcmVhdGVUaW1lID8gdGhpcy5mb3JtLmNyZWF0ZVRpbWUuc3BsaXQoIiAiKVswXSAhPT0gbmV3IERhdGUoKS5nZXRGdWxsWWVhcigpICsgIi0iICsgKG5ldyBEYXRlKCkuZ2V0TW9udGgoKSA+IDEwID8gKG5ldyBEYXRlKCkuZ2V0TW9udGgoKSArIDEpIDogIjAiICsgKG5ldyBEYXRlKCkuZ2V0TW9udGgoKSArIDEpKSArICItIiArIG5ldyBEYXRlKCkuZ2V0RGF0ZSgpIDogIiIpDQogICAgfSwNCiAgICByc09wM3JkQ2VydEZvcm1EaXNhYmxlKCkgew0KICAgICAgcmV0dXJuICEhKHRoaXMucnNPcDNyZENlcnRTZXJ2aWNlSW5zdGFuY2UuaXNEbk9wQ29uZmlybWVkIHx8IHRoaXMucnNPcDNyZENlcnRTZXJ2aWNlSW5zdGFuY2UuaXNEblBzYUNvbmZpcm1lZCB8fCB0aGlzLnJzT3AzcmRDZXJ0U2VydmljZUluc3RhbmNlLmlzRG5TdXBwbGllckNvbmZpcm1lZCB8fCB0aGlzLnJzT3AzcmRDZXJ0U2VydmljZUluc3RhbmNlLmlzQWNjb3VudENvbmZpcm1lZCB8fCB0aGlzLnJzT3AzcmRDZXJ0U2VydmljZUluc3RhbmNlLmlzRG5DbGllbnRDb25maXJtZWQgfHwgdGhpcy5yc09wM3JkQ2VydFNlcnZpY2VJbnN0YW5jZS5pc0RuU2FsZXNDb25maXJtZWQpDQogICAgfSwNCiAgICByc09wSU5TRm9ybURpc2FibGUoKSB7DQogICAgICByZXR1cm4gISEodGhpcy5yc09wSU5TU2VydmljZUluc3RhbmNlLmlzRG5PcENvbmZpcm1lZCB8fCB0aGlzLnJzT3BJTlNTZXJ2aWNlSW5zdGFuY2UuaXNEblBzYUNvbmZpcm1lZCB8fCB0aGlzLnJzT3BJTlNTZXJ2aWNlSW5zdGFuY2UuaXNEblN1cHBsaWVyQ29uZmlybWVkIHx8IHRoaXMucnNPcElOU1NlcnZpY2VJbnN0YW5jZS5pc0FjY291bnRDb25maXJtZWQgfHwgdGhpcy5yc09wSU5TU2VydmljZUluc3RhbmNlLmlzRG5DbGllbnRDb25maXJtZWQgfHwgdGhpcy5yc09wSU5TU2VydmljZUluc3RhbmNlLmlzRG5TYWxlc0NvbmZpcm1lZCkNCiAgICB9LA0KICAgIHJzT3BUcmFkaW5nRm9ybURpc2FibGUoKSB7DQogICAgICByZXR1cm4gISEodGhpcy5yc09wVHJhZGluZ1NlcnZpY2VJbnN0YW5jZS5pc0RuT3BDb25maXJtZWQgfHwgdGhpcy5yc09wVHJhZGluZ1NlcnZpY2VJbnN0YW5jZS5pc0RuUHNhQ29uZmlybWVkIHx8IHRoaXMucnNPcFRyYWRpbmdTZXJ2aWNlSW5zdGFuY2UuaXNEblN1cHBsaWVyQ29uZmlybWVkIHx8IHRoaXMucnNPcFRyYWRpbmdTZXJ2aWNlSW5zdGFuY2UuaXNBY2NvdW50Q29uZmlybWVkIHx8IHRoaXMucnNPcFRyYWRpbmdTZXJ2aWNlSW5zdGFuY2UuaXNEbkNsaWVudENvbmZpcm1lZCB8fCB0aGlzLnJzT3BUcmFkaW5nU2VydmljZUluc3RhbmNlLmlzRG5TYWxlc0NvbmZpcm1lZCkNCiAgICB9LA0KICAgIHJzT3BDT0Zvcm1EaXNhYmxlKCkgew0KICAgICAgcmV0dXJuICEhKHRoaXMucnNPcENPU2VydmljZUluc3RhbmNlLmlzRG5PcENvbmZpcm1lZCB8fCB0aGlzLnJzT3BDT1NlcnZpY2VJbnN0YW5jZS5pc0RuUHNhQ29uZmlybWVkIHx8IHRoaXMucnNPcENPU2VydmljZUluc3RhbmNlLmlzRG5TdXBwbGllckNvbmZpcm1lZCB8fCB0aGlzLnJzT3BDT1NlcnZpY2VJbnN0YW5jZS5pc0FjY291bnRDb25maXJtZWQgfHwgdGhpcy5yc09wQ09TZXJ2aWNlSW5zdGFuY2UuaXNEbkNsaWVudENvbmZpcm1lZCB8fCB0aGlzLnJzT3BDT1NlcnZpY2VJbnN0YW5jZS5pc0RuU2FsZXNDb25maXJtZWQpDQogICAgfSwNCiAgICBkaXNhYmxlZCgpIHsNCiAgICAgIGlmICh0aGlzLmZvcm0ub3BBY2NlcHQgPT0gMCAmJiB0aGlzLm9wKSB7DQogICAgICAgIHJldHVybiB0cnVlDQogICAgICB9DQogICAgICBpZiAodGhpcy5ib29raW5nICYmIHRoaXMuZm9ybS5zcWRTaGlwcGluZ0Jvb2tpbmdTdGF0dXMgPT0gMSkgew0KICAgICAgICByZXR1cm4gdHJ1ZQ0KICAgICAgfQ0KICAgICAgcmV0dXJuIGZhbHNlDQogICAgfQ0KICB9LA0KICBiZWZvcmVNb3VudCgpIHsNCiAgICBpZiAodGhpcy4kc3RvcmUuc3RhdGUuZGF0YS5leGNoYW5nZVJhdGVMaXN0Lmxlbmd0aCA9PSAwIHx8IHRoaXMuJHN0b3JlLnN0YXRlLmRhdGEucmVkaXNMaXN0LmV4Y2hhbmdlUmF0ZUxpc3QpIHsNCiAgICAgIHN0b3JlLmRpc3BhdGNoKCJnZXRFeGNoYW5nZVJhdGUiKQ0KICAgIH0NCg0KICAgIGlmICh0aGlzLiRyb3V0ZS5xdWVyeS50eXBlID09PSAib3AiKSB7DQogICAgICB0aGlzLm9wID0gdHJ1ZQ0KICAgIH0NCiAgICBpZiAodGhpcy4kc3RvcmUuc3RhdGUudXNlci5kZXB0TnVtLmhhcyg0KSkgew0KICAgICAgdGhpcy5maW5hbmNlID0gdHJ1ZQ0KICAgIH0NCiAgICBpZiAodGhpcy4kcm91dGUucXVlcnkucHNhVmVyaWZ5KSB7DQogICAgICB0aGlzLnBzYVZlcmlmeSA9IHRydWUNCiAgICB9DQogICAgaWYgKHRoaXMuJHJvdXRlLnF1ZXJ5LmJvb2tpbmcpIHsNCiAgICAgIHRoaXMuYm9va2luZyA9IHRydWUNCiAgICB9DQoNCiAgICB0aGlzLmxvYWRTZWxlY3Rpb24oKQ0KICAgIHRoaXMucmVzZXQoKQ0KDQogICAgdGhpcy5mb3JtVHlwZSA9ICJyY3QiDQogICAgLy8g5aaC5p6c5piv5p2l6Ieq5LqO5pON5L2c5Y2V5YiX6KGo5L+u5pS5LOWImeS8mumAmui/h+i3r+eUseS8oOmAkuaTjeS9nOWNlWlkLS1ySWQNCiAgICBpZiAodGhpcy4kcm91dGUucXVlcnkucklkKSB7DQogICAgICB0aGlzLmZvcm1UeXBlID0gInJjdCINCiAgICAgIHRoaXMuZ2V0UmN0RGV0YWlsKHRoaXMuJHJvdXRlLnF1ZXJ5LnJJZCkudGhlbigoKSA9PiB7DQogICAgICB9KQ0KICAgIH0gZWxzZSBpZiAodGhpcy4kcm91dGUucXVlcnkuYklkKSB7DQogICAgICAvLyDorqLoiLHljZUNCiAgICAgIHRoaXMuZm9ybVR5cGUgPSAiYm9va2luZyINCiAgICAgIHRoaXMuZ2V0Qm9va2luZ0RldGFpbCh0aGlzLiRyb3V0ZS5xdWVyeS5iSWQpLnRoZW4oKCkgPT4gew0KICAgICAgfSkNCiAgICB9IGVsc2UgaWYgKHRoaXMuJHJvdXRlLnF1ZXJ5LmlkKSB7DQogICAgICAvLyDlpoLmnpzmmK/mnaXoh6rmiqXku7fliJfooajnmoTorqLoiLHnlLPor7cNCiAgICAgIHRoaXMuZm9ybVR5cGUgPSAiYm9va2luZyINCiAgICAgIHRoaXMuZ2V0UXVvdGF0aW9uKHRoaXMuJHJvdXRlLnF1ZXJ5LmlkKQ0KICAgIH0NCiAgfSwNCiAgbW91bnRlZCgpIHsNCiAgICAvKioNCiAgICAgKiDnm5HlkKzmgqzmta7mi5bmi73ljLrln58NCiAgICAgKi8NCiAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7DQogICAgICAvLyDojrflj5ZET03lhYPntKANCiAgICAgIGxldCBkcmFnQXJlYSA9IHRoaXMuJHJlZnMuZHJhZ0FyZWENCiAgICAgIC8vIOe8k+WtmCBjbGllbnRYIGNsaWVudFkg55qE5a+56LGhOiDnlKjkuo7liKTmlq3mmK/ngrnlh7vkuovku7bov5jmmK/np7vliqjkuovku7YNCiAgICAgIGxldCBjbGllbnRPZmZzZXQgPSB7fQ0KICAgICAgLy8g57uR5a6a6byg5qCH5oyJ5LiL5LqL5Lu2DQogICAgICBkcmFnQXJlYS5hZGRFdmVudExpc3RlbmVyKCJtb3VzZWRvd24iLCAoZXZlbnQpID0+IHsNCiAgICAgICAgbGV0IG9mZnNldFggPSBkcmFnQXJlYS5nZXRCb3VuZGluZ0NsaWVudFJlY3QoKS5sZWZ0IC8vIOiOt+WPluW9k+WJjeeahHjovbTot53nprsNCiAgICAgICAgbGV0IG9mZnNldFkgPSBkcmFnQXJlYS5nZXRCb3VuZGluZ0NsaWVudFJlY3QoKS50b3AgLy8g6I635Y+W5b2T5YmN55qEeei9tOi3neemuw0KICAgICAgICBsZXQgaW5uZXJYID0gZXZlbnQuY2xpZW50WCAtIG9mZnNldFggLy8g6I635Y+W6byg5qCH5Zyo5pa55Z2X5YaF55qEeOi9tOi3nQ0KICAgICAgICBsZXQgaW5uZXJZID0gZXZlbnQuY2xpZW50WSAtIG9mZnNldFkgLy8g6I635Y+W6byg5qCH5Zyo5pa55Z2X5YaF55qEeei9tOi3nQ0KICAgICAgICAvLyBjb25zb2xlLmxvZyhvZmZzZXRYLCBvZmZzZXRZLCBpbm5lclgsIGlubmVyWSk7DQogICAgICAgIC8vIOe8k+WtmCBjbGllbnRYIGNsaWVudFkNCiAgICAgICAgY2xpZW50T2Zmc2V0LmNsaWVudFggPSBldmVudC5jbGllbnRYDQogICAgICAgIGNsaWVudE9mZnNldC5jbGllbnRZID0gZXZlbnQuY2xpZW50WQ0KICAgICAgICAvLyDpvKDmoIfnp7vliqjnmoTml7blgJnkuI3lgZznmoTkv67mlLlkaXbnmoRsZWZ05ZKMdG9w5YC8DQogICAgICAgIGRvY3VtZW50Lm9ubW91c2Vtb3ZlID0gZnVuY3Rpb24oZXZlbnQpIHsNCiAgICAgICAgICBkcmFnQXJlYS5zdHlsZS5sZWZ0ID0gZXZlbnQuY2xpZW50WCAtIGlubmVyWCArICJweCINCiAgICAgICAgICBkcmFnQXJlYS5zdHlsZS50b3AgPSBldmVudC5jbGllbnRZIC0gaW5uZXJZICsgInB4Ig0KICAgICAgICAgIC8vIGRyYWdBcmVhIOi3neemu+mhtumDqOeahOi3neemuw0KICAgICAgICAgIGxldCBkcmFnQXJlYVRvcCA9IHdpbmRvdy5pbm5lckhlaWdodCAtIGRyYWdBcmVhLmdldEJvdW5kaW5nQ2xpZW50UmVjdCgpLmhlaWdodA0KICAgICAgICAgIC8vIGRyYWdBcmVhIOi3neemu+W3pumDqOeahOi3neemuw0KICAgICAgICAgIGxldCBkcmFnQXJlYUxlZnQgPSB3aW5kb3cuaW5uZXJXaWR0aCAtIGRyYWdBcmVhLmdldEJvdW5kaW5nQ2xpZW50UmVjdCgpLndpZHRoDQogICAgICAgICAgLy8g6L6555WM5Yik5pat5aSE55CGDQogICAgICAgICAgLy8gMeOAgeiuvue9ruW3puWPs+S4jeiDveWKqA0KICAgICAgICAgIC8vIGRyYWdBcmVhLnN0eWxlLmxlZnQgPSBkcmFnQXJlYUxlZnQgKyAicHgiOw0KDQogICAgICAgICAgLy8gMS7orr7nva7lt6bkvqfovrnnlYwNCiAgICAgICAgICBpZiAoZHJhZ0FyZWEuZ2V0Qm91bmRpbmdDbGllbnRSZWN0KCkubGVmdCA8PSAwKSB7DQogICAgICAgICAgICBkcmFnQXJlYS5zdHlsZS5sZWZ0ID0gIjBweCINCiAgICAgICAgICB9DQogICAgICAgICAgLy8gMi7orr7nva7lj7PkvqfovrnnlYwNCiAgICAgICAgICBpZiAoZHJhZ0FyZWEuZ2V0Qm91bmRpbmdDbGllbnRSZWN0KCkubGVmdCA+PSBkcmFnQXJlYUxlZnQpIHsNCiAgICAgICAgICAgIGRyYWdBcmVhLnN0eWxlLmxlZnQgPSBkcmFnQXJlYUxlZnQgKyAicHgiDQogICAgICAgICAgfQ0KICAgICAgICAgIC8vIDPjgIHorr7nva7pobbpg6jovrnnlYwNCiAgICAgICAgICBpZiAoZHJhZ0FyZWEuZ2V0Qm91bmRpbmdDbGllbnRSZWN0KCkudG9wIDw9IDApIHsNCiAgICAgICAgICAgIGRyYWdBcmVhLnN0eWxlLnRvcCA9ICIwcHgiDQogICAgICAgICAgfQ0KICAgICAgICAgIC8vIDTjgIHorr7nva7lupXpg6jovrnnlYwNCiAgICAgICAgICBpZiAoZHJhZ0FyZWEuZ2V0Qm91bmRpbmdDbGllbnRSZWN0KCkudG9wID49IGRyYWdBcmVhVG9wKSB7DQogICAgICAgICAgICBkcmFnQXJlYS5zdHlsZS50b3AgPSBkcmFnQXJlYVRvcCArICJweCINCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgICAgLy8g6byg5qCH5oqs6LW35pe277yM5riF6Zmk57uR5a6a5Zyo5paH5qGj5LiK55qEbW91c2Vtb3Zl5ZKMbW91c2V1cOS6i+S7tu+8m+WQpuWImem8oOagh+aKrOi1t+WQjui/mOWPr+S7pee7p+e7reaLluaLveaWueWdlw0KICAgICAgICBkb2N1bWVudC5vbm1vdXNldXAgPSBmdW5jdGlvbigpIHsNCiAgICAgICAgICBkb2N1bWVudC5vbm1vdXNlbW92ZSA9IG51bGwNCiAgICAgICAgICBkb2N1bWVudC5vbm1vdXNldXAgPSBudWxsDQogICAgICAgIH0NCiAgICAgIH0sIGZhbHNlKQ0KICAgICAgLy8g57uR5a6a6byg5qCH5p2+5byA5LqL5Lu2DQogICAgICBkcmFnQXJlYS5hZGRFdmVudExpc3RlbmVyKCJtb3VzZXVwIiwgKGV2ZW50KSA9PiB7DQogICAgICAgIGxldCBjbGllbnRYID0gZXZlbnQuY2xpZW50WA0KICAgICAgICBsZXQgY2xpZW50WSA9IGV2ZW50LmNsaWVudFkNCiAgICAgICAgLyogaWYgKGNsaWVudFggPT09IGNsaWVudE9mZnNldC5jbGllbnRYICYmIGNsaWVudFkgPT09IGNsaWVudE9mZnNldC5jbGllbnRZKSB7DQogICAgICAgICAgY29uc29sZS5sb2coJ2NsaWNrIOS6i+S7ticpOw0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIGNvbnNvbGUubG9nKCdkcmFnIOS6i+S7ticpOw0KICAgICAgICB9ICovDQogICAgICB9KQ0KICAgIH0pDQoNCiAgICAvLyDliJ3lp4vljJbmiZPljbANCiAgICB0aGlzLmluaXRQcmludCgpDQogIH0sDQogIG1ldGhvZHM6IHsNCiAgICBvdXRib3VuZFBsYW4oKSB7DQogICAgICB0aGlzLm91dGJvdW5kRGF0YS5yY3RJZCA9IHRoaXMuZm9ybS5yY3RJZA0KICAgICAgdGhpcy5vdXRib3VuZERhdGEuY2xpZW50TmFtZSA9IHRoaXMuZm9ybS5jbGllbnRTdW1tYXJ5LnNwbGl0KCIvIilbMV0NCiAgICAgIHRoaXMub3V0Ym91bmREYXRhLmN1c3RvbWVyT3JkZXJObyA9IHRoaXMuZm9ybS5yY3RObw0KICAgICAgdGhpcy5vdXRib3VuZERhdGEub3V0Ym91bmRUeXBlID0gIuaVtOafnCINCiAgICAgIHRoaXMub3V0Ym91bmREYXRhLm9wZXJhdG9yID0gdGhpcy4kc3RvcmUuc3RhdGUuZGF0YS5hbGxSc1N0YWZmTGlzdC5maW5kKGl0ZW0gPT4gaXRlbS5zdGFmZklkID09PSB0aGlzLmZvcm0ub3BJZCkuc3RhZmZHaXZpbmdFbk5hbWUgfHwgIiINCiAgICAgIHRoaXMub3V0Ym91bmREYXRhLmNvbnRhaW5lck5vID0gdGhpcy5mb3JtLnNxZENvbnRhaW5lcnNTZWFsc1N1bSB8fCAiIg0KICAgICAgdGhpcy5vdXRib3VuZERhdGEub3JkZXJEYXRlID0gbW9tZW50KCkuZm9ybWF0KCd5eXl5LU1NLUREIEhIOm1tOnNzJykNCiAgICAgIHRoaXMub3Blbk91dGJvdW5kID0gdHJ1ZQ0KICAgIH0sDQogICAgaGFuZGxlU3RhdHVzQ2hhbmdlKCkgew0KICAgICAgLy8g5bCG6L+b5bqm6K6+572u5Li654K55Ye755qE5pyA5paw5pel5pyfDQoNCiAgICB9LA0KICAgIGhhbmRsZVByb2ZpdCgpIHsNCg0KICAgIH0sDQogICAgY29udmVydE9iamVjdEtleXNUb1VwcGVyQ2FzZShvYmopIHsNCiAgICAgIGxldCByZXN1bHQgPSB7fQ0KICAgICAgZm9yIChsZXQga2V5IGluIG9iaikgew0KICAgICAgICBpZiAob2JqLmhhc093blByb3BlcnR5KGtleSkpIHsNCiAgICAgICAgICAvLyDovazmjaLlsZ7mgKflgLzkuLrlpKflhpnvvIzlubbkv53nlZnljp/lsZ7mgKflkI0NCiAgICAgICAgICByZXN1bHRba2V5XSA9IHR5cGVvZiBvYmpba2V5XSA9PT0gInN0cmluZyIgPyBvYmpba2V5XS50b1VwcGVyQ2FzZSgpIDogb2JqW2tleV0NCiAgICAgICAgfQ0KICAgICAgfQ0KICAgICAgcmV0dXJuIHJlc3VsdA0KICAgIH0sDQogICAgb3BlbkNoYXJnZVNlbGVjdChzZXJ2aWNlT2JqZWN0KSB7DQogICAgICB0aGlzLmNoYXJnZVNlbGVjdEl0ZW0gPSBzZXJ2aWNlT2JqZWN0DQogICAgICBsZXQgZGF0YSA9IHt9DQogICAgICBkYXRhLmxvY2F0aW9uT3B0aW9ucyA9IHRoaXMubG9jYXRpb25PcHRpb25zDQogICAgICBkYXRhLnBvbElkID0gdGhpcy5mb3JtLnBvbElkDQogICAgICBkYXRhLmRlc3RpbmF0aW9uUG9ydElkID0gdGhpcy5mb3JtLmRlc3RpbmF0aW9uUG9ydElkDQogICAgICBkYXRhLmNhcnJpZXJJZCA9IHRoaXMuZm9ybS5jYXJyaWVySWQNCiAgICAgIGRhdGEuc3VwcGxpZXJJZCA9IHNlcnZpY2VPYmplY3Quc3VwcGxpZXJJZA0KICAgICAgZGF0YS5zdXBwbGllckxpc3QgPSB0aGlzLnN1cHBsaWVyTGlzdA0KDQogICAgICB0aGlzLmNoYXJnZVNlYXJjaERhdGEgPSBkYXRhDQoNCiAgICAgIHRoaXMuY2hhcmdlT3BlbiA9IHRydWUNCiAgICB9LA0KICAgIGF1ZGl0Q2hhcmdlKHNlcnZpY2VPYmplY3QsIHJzQ2hhcmdlTGlzdCkgew0KICAgICAgdGhpcy4kc2V0KHNlcnZpY2VPYmplY3QsICJyc0NoYXJnZUxpc3QiLCByc0NoYXJnZUxpc3QpDQogICAgfSwNCiAgICBnZXRTZXJ2aWNlSW5zdGFuY2VEaXNhYmxlKHNlcnZpY2VJbnN0YW5jZSkgew0KICAgICAgLy8gcmV0dXJuICEhKHNlcnZpY2VJbnN0YW5jZS5pc0RuT3BDb25maXJtZWQgfHwgc2VydmljZUluc3RhbmNlLmlzRG5Qc2FDb25maXJtZWQgfHwgc2VydmljZUluc3RhbmNlLmlzRG5TdXBwbGllckNvbmZpcm1lZCB8fCBzZXJ2aWNlSW5zdGFuY2UuaXNBY2NvdW50Q29uZmlybWVkIHx8IHNlcnZpY2VJbnN0YW5jZS5pc0RuQ2xpZW50Q29uZmlybWVkIHx8IHNlcnZpY2VJbnN0YW5jZS5pc0RuU2FsZXNDb25maXJtZWQpDQogICAgICByZXR1cm4gISEoc2VydmljZUluc3RhbmNlLmlzRG5PcENvbmZpcm1lZCB8fCBzZXJ2aWNlSW5zdGFuY2UuaXNEblBzYUNvbmZpcm1lZCB8fCBzZXJ2aWNlSW5zdGFuY2UuaXNEblN1cHBsaWVyQ29uZmlybWVkIHx8IHNlcnZpY2VJbnN0YW5jZS5pc0RuQ2xpZW50Q29uZmlybWVkIHx8IHNlcnZpY2VJbnN0YW5jZS5pc0RuU2FsZXNDb25maXJtZWQpDQogICAgfSwNCiAgICBkZWxldGVSc09wRnJlZURlY2xhcmUoaXRlbSkgew0KICAgICAgaWYgKGl0ZW0ucnNDaGFyZ2VMaXN0ICYmIGl0ZW0ucnNDaGFyZ2VMaXN0Lmxlbmd0aCA+IDApIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCLor7flhYjliKDpmaTnm7jlhbPllYrotLnnlKgiKQ0KICAgICAgICByZXR1cm4NCiAgICAgIH0NCiAgICAgIHRoaXMuZm9ybS5yc09wRnJlZURlY2xhcmVMaXN0ID0gdGhpcy5mb3JtLnJzT3BGcmVlRGVjbGFyZUxpc3QuZmlsdGVyKHYgPT4gdiAhPT0gaXRlbSkNCiAgICB9LA0KICAgIGRlbGV0ZVJzT3BEb2NEZWNsYXJlKGl0ZW0pIHsNCiAgICAgIGlmIChpdGVtLnJzQ2hhcmdlTGlzdCAmJiBpdGVtLnJzQ2hhcmdlTGlzdC5sZW5ndGggPiAwKSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZygi6K+35YWI5Yig6Zmk55u45YWz5ZWK6LS555SoIikNCiAgICAgICAgcmV0dXJuDQogICAgICB9DQogICAgICB0aGlzLmZvcm0ucnNPcERvY0RlY2xhcmVMaXN0ID0gdGhpcy5mb3JtLnJzT3BEb2NEZWNsYXJlTGlzdC5maWx0ZXIodiA9PiB2ICE9PSBpdGVtKQ0KICAgIH0sDQogICAgZGVsZXRlUnNPcEJ1bGtUcnVjayhpdGVtKSB7DQogICAgICBpZiAoaXRlbS5yc0NoYXJnZUxpc3QgJiYgaXRlbS5yc0NoYXJnZUxpc3QubGVuZ3RoID4gMCkgew0KICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoIuivt+WFiOWIoOmZpOebuOWFs+WViui0ueeUqCIpDQogICAgICAgIHJldHVybg0KICAgICAgfQ0KICAgICAgdGhpcy5mb3JtLnJzT3BCdWxrVHJ1Y2tMaXN0ID0gdGhpcy5mb3JtLnJzT3BCdWxrVHJ1Y2tMaXN0LmZpbHRlcih2ID0+IHYgIT09IGl0ZW0pDQogICAgfSwNCiAgICBkZWxldGVSc09wQ3RuclRydWNrKGl0ZW0pIHsNCiAgICAgIGlmIChpdGVtLnJzQ2hhcmdlTGlzdCAmJiBpdGVtLnJzQ2hhcmdlTGlzdC5sZW5ndGggPiAwKSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZygi6K+35YWI5Yig6Zmk55u45YWz5ZWK6LS555SoIikNCiAgICAgICAgcmV0dXJuDQogICAgICB9DQogICAgICB0aGlzLmZvcm0ucnNPcEN0bnJUcnVja0xpc3QgPSB0aGlzLmZvcm0ucnNPcEN0bnJUcnVja0xpc3QuZmlsdGVyKHYgPT4gdiAhPT0gaXRlbSkNCiAgICB9LA0KICAgIGRlbGV0ZVJzT3BMY2xTZWEoaXRlbSkgew0KICAgICAgaWYgKChpdGVtLnJzQ2hhcmdlTGlzdCAmJiBpdGVtLnJzQ2hhcmdlTGlzdC5sZW5ndGggPiAwKSB8fCBpdGVtLnNxZFBzYU5vKSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZygi6K+35YWI5Yig6Zmk55u45YWz5ZWK6LS555So5oiW5Y+W5raI6K6i6IixIikNCiAgICAgICAgcmV0dXJuDQogICAgICB9DQogICAgICB0aGlzLmZvcm0ucnNPcFNlYUxjbExpc3QgPSB0aGlzLmZvcm0ucnNPcFNlYUxjbExpc3QuZmlsdGVyKHYgPT4gdiAhPT0gaXRlbSkNCiAgICB9LA0KICAgIGRlbGV0ZVJzT3BGY2xTZWEoaXRlbSkgew0KICAgICAgaWYgKChpdGVtLnJzQ2hhcmdlTGlzdCAmJiBpdGVtLnJzQ2hhcmdlTGlzdC5sZW5ndGggPiAwKSB8fCBpdGVtLnNxZFBzYU5vKSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZygi6K+35YWI5Yig6Zmk55u45YWz5ZWK6LS555So5oiW5Y+W5raI6K6i6IixIikNCiAgICAgICAgcmV0dXJuDQogICAgICB9DQogICAgICB0aGlzLmZvcm0ucnNPcFNlYUZjbExpc3QgPSB0aGlzLmZvcm0ucnNPcFNlYUZjbExpc3QuZmlsdGVyKHYgPT4gdiAhPT0gaXRlbSkNCiAgICB9LA0KICAgIGRlbGV0ZVJzT3BBaXIoaXRlbSkgew0KICAgICAgaWYgKGl0ZW0ucnNDaGFyZ2VMaXN0ICYmIGl0ZW0ucnNDaGFyZ2VMaXN0Lmxlbmd0aCA+IDApIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCLor7flhYjliKDpmaTnm7jlhbPllYrotLnnlKgiKQ0KICAgICAgICByZXR1cm4NCiAgICAgIH0NCiAgICAgIHRoaXMuZm9ybS5yc09wQWlyTGlzdCA9IHRoaXMuZm9ybS5yc09wQWlyTGlzdC5maWx0ZXIodiA9PiB2ICE9PSBpdGVtKQ0KICAgIH0sDQogICAgdXBkYXRlU2VydmljZUluc3RhbmNlKHNlcnZpY2VJbnN0YW5jZSkgew0KICAgICAgdXBkYXRlU2VydmljZWluc3RhbmNlcyhzZXJ2aWNlSW5zdGFuY2UpDQogICAgfSwNCiAgICBnZXRCb29raW5nU3RhdHVzKHN0YXR1cykgew0KICAgICAgc3dpdGNoIChzdGF0dXMpIHsNCiAgICAgICAgLy8g6K6i6Iix54q25oCBKOiNieeov+eusS0wL+W3suiuouiIsS0xL+W3suaUvuiIsS0yL+W3suS9v+eUqC0zL+W3suWPlua2iC0tMSknLA0KICAgICAgICBjYXNlICIwIjoNCiAgICAgICAgICByZXR1cm4gIuiNieeov+eusSINCiAgICAgICAgY2FzZSAiMSI6DQogICAgICAgICAgcmV0dXJuICLlt7LorqLoiLEiDQogICAgICAgIGNhc2UgIjIiOg0KICAgICAgICAgIHJldHVybiAi5bey5pS+6IixIg0KICAgICAgICBjYXNlICIzIjoNCiAgICAgICAgICByZXR1cm4gIuW3suS9v+eUqCINCiAgICAgICAgY2FzZSAiLTEiOg0KICAgICAgICAgIHJldHVybiAi5bey5Y+W5raIIg0KICAgICAgfQ0KICAgIH0sDQogICAgYWRkRnJlZURlY2xhcmUoKSB7DQogICAgICBsZXQgcnNPcEZyZWVEZWNsYXJlID0gdGhpcy5fLmNsb25lRGVlcCh0aGlzLnJzT3BGcmVlRGVjbGFyZSkNCiAgICAgIHJzT3BGcmVlRGVjbGFyZS5yc0NoYXJnZUxpc3QgPSBbXQ0KICAgICAgcnNPcEZyZWVEZWNsYXJlLnJzT3BMb2dMaXN0ID0gW10NCiAgICAgIHJzT3BGcmVlRGVjbGFyZS5yc0RvY0xpc3QgPSBbXQ0KICAgICAgcnNPcEZyZWVEZWNsYXJlLnJzU2VydmljZUluc3RhbmNlcyA9IHRoaXMucnNTZXJ2aWNlSW5zdGFuY2VzDQogICAgICB0aGlzLmZvcm0ucnNPcEZyZWVEZWNsYXJlTGlzdC5wdXNoKHJzT3BGcmVlRGVjbGFyZSkNCiAgICB9LA0KICAgIGFkZERvY0RlY2xhcmUoKSB7DQogICAgICBsZXQgcnNPcERvY0RlY2xhcmUgPSB0aGlzLl8uY2xvbmVEZWVwKHRoaXMucnNPcERvY0RlY2xhcmUpDQogICAgICByc09wRG9jRGVjbGFyZS5yc0NoYXJnZUxpc3QgPSBbXQ0KICAgICAgcnNPcERvY0RlY2xhcmUucnNPcExvZ0xpc3QgPSBbXQ0KICAgICAgcnNPcERvY0RlY2xhcmUucnNEb2NMaXN0ID0gW10NCiAgICAgIHJzT3BEb2NEZWNsYXJlLnJzU2VydmljZUluc3RhbmNlcyA9IHRoaXMucnNTZXJ2aWNlSW5zdGFuY2VzDQogICAgICB0aGlzLmZvcm0ucnNPcERvY0RlY2xhcmVMaXN0LnB1c2gocnNPcERvY0RlY2xhcmUpDQogICAgfSwNCiAgICBhZGRCdWxrVHJ1Y2soKSB7DQogICAgICBsZXQgcnNPcEJ1bGtUcnVjayA9IHRoaXMuXy5jbG9uZURlZXAodGhpcy5yc09wQnVsa1RydWNrKQ0KICAgICAgcnNPcEJ1bGtUcnVjay5yc0NoYXJnZUxpc3QgPSBbXQ0KICAgICAgcnNPcEJ1bGtUcnVjay5yc09wTG9nTGlzdCA9IFtdDQogICAgICByc09wQnVsa1RydWNrLnJzRG9jTGlzdCA9IFtdDQogICAgICByc09wQnVsa1RydWNrLnJzU2VydmljZUluc3RhbmNlcyA9IHRoaXMucnNTZXJ2aWNlSW5zdGFuY2VzDQogICAgICB0aGlzLmZvcm0ucnNPcEJ1bGtUcnVja0xpc3QucHVzaChyc09wQnVsa1RydWNrKQ0KICAgIH0sDQogICAgYWRkQ3RuclRydWNrKCkgew0KICAgICAgbGV0IHJzT3BDdG5yVHJ1Y2sgPSB0aGlzLl8uY2xvbmVEZWVwKHRoaXMucnNPcEN0bnJUcnVjaykNCiAgICAgIHJzT3BDdG5yVHJ1Y2sucnNDaGFyZ2VMaXN0ID0gW10NCiAgICAgIHJzT3BDdG5yVHJ1Y2sucnNPcExvZ0xpc3QgPSBbXQ0KICAgICAgcnNPcEN0bnJUcnVjay5yc0RvY0xpc3QgPSBbXQ0KICAgICAgcnNPcEN0bnJUcnVjay5yc1NlcnZpY2VJbnN0YW5jZXMgPSB0aGlzLnJzU2VydmljZUluc3RhbmNlcw0KICAgICAgdGhpcy5mb3JtLnJzT3BDdG5yVHJ1Y2tMaXN0LnB1c2gocnNPcEN0bnJUcnVjaykNCiAgICB9LA0KICAgIGFkZEFpcigpIHsNCiAgICAgIGxldCByc09wQWlyID0gdGhpcy5fLmNsb25lRGVlcCh0aGlzLnJzT3BBaXIpDQogICAgICByc09wQWlyLnJzQ2hhcmdlTGlzdCA9IFtdDQogICAgICByc09wQWlyLnJzT3BMb2dMaXN0ID0gW10NCiAgICAgIHJzT3BBaXIucnNEb2NMaXN0ID0gW10NCiAgICAgIHJzT3BBaXIucnNTZXJ2aWNlSW5zdGFuY2VzID0gdGhpcy5yc1NlcnZpY2VJbnN0YW5jZXMNCiAgICAgIHRoaXMuZm9ybS5yc09wQWlyTGlzdC5wdXNoKHJzT3BBaXIpDQogICAgfSwNCiAgICBhZGRTZWFMQ0woKSB7DQogICAgICBsZXQgcnNPcFNlYUxjbCA9IHRoaXMuXy5jbG9uZURlZXAodGhpcy5yc09wU2VhTGNsKQ0KICAgICAgcnNPcFNlYUxjbC5yc0NoYXJnZUxpc3QgPSBbXQ0KICAgICAgcnNPcFNlYUxjbC5yc09wTG9nTGlzdCA9IFtdDQogICAgICByc09wU2VhTGNsLnJzRG9jTGlzdCA9IFtdDQogICAgICByc09wU2VhTGNsLnJzU2VydmljZUluc3RhbmNlcyA9IHRoaXMucnNTZXJ2aWNlSW5zdGFuY2VzDQogICAgICB0aGlzLmZvcm0ucnNPcFNlYUxjbExpc3QucHVzaChyc09wU2VhTGNsKQ0KICAgIH0sDQogICAgYWRkU2VhRkNMKCkgew0KICAgICAgbGV0IHJzT3BTZWFGY2wgPSB0aGlzLl8uY2xvbmVEZWVwKHRoaXMucnNPcFNlYUZjbCkNCiAgICAgIHJzT3BTZWFGY2wucnNDaGFyZ2VMaXN0ID0gW10NCiAgICAgIHJzT3BTZWFGY2wucnNPcExvZ0xpc3QgPSBbXQ0KICAgICAgcnNPcFNlYUZjbC5yc0RvY0xpc3QgPSBbXQ0KICAgICAgcnNPcFNlYUZjbC5yc1NlcnZpY2VJbnN0YW5jZXMgPSB0aGlzLnJzU2VydmljZUluc3RhbmNlcw0KICAgICAgdGhpcy5mb3JtLnJzT3BTZWFGY2xMaXN0LnB1c2gocnNPcFNlYUZjbCkNCiAgICB9LA0KICAgIHBzYUJvb2tpbmdDYW5jZWwoc2VydmljZU9iamVjdCkgew0KICAgICAgaWYgKHRoaXMuZm9ybS5zZWFJZCA9PT0gbnVsbCkgew0KICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoIuW9k+WJjeWVhuWKoeiIseS9jeacqumAieaLqSIpDQogICAgICAgIHJldHVybg0KICAgICAgfQ0KICAgICAgdGhpcy4kY29uZmlybSgi56Gu6K6k5Y+W5raI77yfIiwgIiIsIHtjb25maXJtQnV0dG9uVGV4dDogIuehruiupOWPlua2iCJ9KQ0KICAgICAgICAudGhlbihfID0+IHsNCiAgICAgICAgICAvLyBzZXJ2aWNlT2JqZWN0LnNlYUlkID0gdGhpcy5mb3JtLnNlYUlkDQogICAgICAgICAgc2VydmljZU9iamVjdC5yY3RObyA9IG51bGwNCiAgICAgICAgICBzZXJ2aWNlT2JqZWN0LnNxZFBzYU5vID0gbnVsbA0KICAgICAgICAgIHNlcnZpY2VPYmplY3QuY2xpZW50U2hvcnROYW1lID0gbnVsbA0KICAgICAgICAgIHNlcnZpY2VPYmplY3Quc2FsZXNJZCA9IG51bGwNCiAgICAgICAgICBzZXJ2aWNlT2JqZWN0LnNhbGVzQXNzaXN0YW50SWQgPSBudWxsDQogICAgICAgICAgc2VydmljZU9iamVjdC5kaXN0cmlidXRpb25TdGF0dXMgPSAiMCINCiAgICAgICAgICBzZXJ2aWNlT2JqZWN0LmJvb2tpbmdTdGF0dXMgPSAiLTEiDQogICAgICAgICAgLy8gc2VydmljZU9iamVjdC5vcElkID0gdGhpcy4kc3RvcmUuc3RhdGUudXNlci5zaWQNCiAgICAgICAgICBzZXJ2aWNlT2JqZWN0LmJvb2tpbmdJZCA9IHRoaXMuJHN0b3JlLnN0YXRlLnVzZXIuc2lkDQogICAgICAgICAgc2VydmljZU9iamVjdC5uZXdCb29raW5nVGltZSA9IG1vbWVudCgpLmZvcm1hdCgieXl5eS1NTS1ERCBISDptbTpzcyIpDQoNCiAgICAgICAgICB0aGlzLnN1Ym1pdEZvcm0oKQ0KICAgICAgICAgIHRoaXMuc2hvd1BzYVJjdCA9IGZhbHNlDQogICAgICAgIH0pDQogICAgfSwNCiAgICBzZWxlY3RDb21tb25Vc2VkKHJvdykgew0KICAgICAgaWYgKHRoaXMuY29tbW9uVXNlZFR5cGUgPT09ICJjb21tb24iKSB7DQogICAgICAgIHRoaXMuZm9ybS5ib29raW5nU2hpcHBlciA9IHJvdy5ib29raW5nU2hpcHBlcg0KICAgICAgICB0aGlzLmZvcm0uYm9va2luZ0NvbnNpZ25lZSA9IHJvdy5ib29raW5nQ29uc2lnbmVlDQogICAgICAgIHRoaXMuZm9ybS5ib29raW5nTm90aWZ5UGFydHkgPSByb3cuYm9va2luZ05vdGlmeVBhcnR5DQogICAgICAgIHRoaXMuZm9ybS5ib29raW5nQWdlbnQgPSByb3cuYm9va2luZ0FnZW50DQogICAgICB9DQogICAgICBpZiAodGhpcy5jb21tb25Vc2VkVHlwZSA9PT0gInJlbGVhc2UiKSB7DQogICAgICAgIHRoaXMuYm9va2luZ01lc3NhZ2VGb3JtLmJvb2tpbmdTaGlwcGVyID0gcm93LmJvb2tpbmdTaGlwcGVyDQogICAgICAgIHRoaXMuYm9va2luZ01lc3NhZ2VGb3JtLmJvb2tpbmdDb25zaWduZWUgPSByb3cuYm9va2luZ0NvbnNpZ25lZQ0KICAgICAgICB0aGlzLmJvb2tpbmdNZXNzYWdlRm9ybS5ib29raW5nTm90aWZ5UGFydHkgPSByb3cuYm9va2luZ05vdGlmeVBhcnR5DQogICAgICAgIHRoaXMuYm9va2luZ01lc3NhZ2VGb3JtLmJvb2tpbmdBZ2VudCA9IHJvdy5ib29raW5nQWdlbnQNCiAgICAgIH0NCiAgICAgIGlmICh0aGlzLmNvbW1vblVzZWRUeXBlID09PSAiZGlzcGF0Y2giKSB7DQogICAgICAgIHRoaXMuZm9ybS5wcmVjYXJyaWFnZUFkZHJlc3MgPSByb3cucHJlY2FycmlhZ2VBZGRyZXNzDQogICAgICAgIHRoaXMuZm9ybS5wcmVjYXJyaWFnZVRlbCA9IHJvdy5wcmVjYXJyaWFnZVRlbA0KICAgICAgICB0aGlzLmZvcm0ucHJlY2FycmlhZ2VDb250YWN0ID0gcm93LnByZWNhcnJpYWdlQ29udGFjdA0KICAgICAgICB0aGlzLmZvcm0ucHJlY2FycmlhZ2VSZW1hcmsgPSByb3cucHJlY2FycmlhZ2VSZW1hcmsNCiAgICAgIH0NCg0KICAgICAgdGhpcy5vcGVuQ29tbW9uVXNlZFNlbGVjdCA9IGZhbHNlDQogICAgfSwNCiAgICBvcGVuRGlzcGF0Y2hDb21tb24oKSB7DQogICAgICB0aGlzLmNvbW1vblVzZWRTZWxlY3REYXRhLmRlc3RpbmF0aW9uUG9ydElkID0gdGhpcy5mb3JtLmRlc3RpbmF0aW9uUG9ydElkDQogICAgICB0aGlzLmNvbW1vblVzZWRTZWxlY3REYXRhLnBvbElkID0gdGhpcy5mb3JtLnBvbElkDQogICAgICB0aGlzLmNvbW1vblVzZWRTZWxlY3REYXRhLmNsaWVudElkID0gdGhpcy5mb3JtLmNsaWVudElkDQogICAgICB0aGlzLmNvbW1vblVzZWRTZWxlY3REYXRhLnR5cGUgPSAiZGlzcGF0Y2giDQoNCiAgICAgIHRoaXMuY29tbW9uVXNlZFR5cGUgPSAiZGlzcGF0Y2giDQoNCiAgICAgIHRoaXMub3BlbkNvbW1vblVzZWRTZWxlY3QgPSB0cnVlDQogICAgfSwNCiAgICBvcGVuQ29tbW9uVXNlZCgpIHsNCiAgICAgIHRoaXMuY29tbW9uVXNlZFNlbGVjdERhdGEuZGVzdGluYXRpb25Qb3J0SWQgPSB0aGlzLmZvcm0uZGVzdGluYXRpb25Qb3J0SWQNCiAgICAgIHRoaXMuY29tbW9uVXNlZFNlbGVjdERhdGEucG9sSWQgPSB0aGlzLmZvcm0ucG9sSWQNCiAgICAgIHRoaXMuY29tbW9uVXNlZFNlbGVjdERhdGEuY2xpZW50SWQgPSB0aGlzLmZvcm0uY2xpZW50SWQNCiAgICAgIHRoaXMuY29tbW9uVXNlZFNlbGVjdERhdGEudHlwZSA9ICJjb21tb24iDQoNCiAgICAgIHRoaXMuY29tbW9uVXNlZFR5cGUgPSAiY29tbW9uIg0KDQogICAgICB0aGlzLm9wZW5Db21tb25Vc2VkU2VsZWN0ID0gdHJ1ZQ0KICAgIH0sDQogICAgb3BlblJlbGVhc2VVc2VkKCkgew0KICAgICAgdGhpcy5jb21tb25Vc2VkU2VsZWN0RGF0YS5kZXN0aW5hdGlvblBvcnRJZCA9IHRoaXMuZm9ybS5kZXN0aW5hdGlvblBvcnRJZA0KICAgICAgdGhpcy5jb21tb25Vc2VkU2VsZWN0RGF0YS5wb2xJZCA9IHRoaXMuZm9ybS5wb2xJZA0KICAgICAgdGhpcy5jb21tb25Vc2VkU2VsZWN0RGF0YS5jbGllbnRJZCA9IHRoaXMuZm9ybS5jbGllbnRJZA0KICAgICAgdGhpcy5jb21tb25Vc2VkU2VsZWN0RGF0YS50eXBlID0gInJlbGVhc2UiDQoNCiAgICAgIHRoaXMuY29tbW9uVXNlZFR5cGUgPSAicmVsZWFzZSINCg0KICAgICAgdGhpcy5vcGVuQ29tbW9uVXNlZFNlbGVjdCA9IHRydWUNCiAgICB9LA0KICAgIGhhbmRsZUFkZENvbW1vbih0eXBlKSB7DQogICAgICBsZXQgZGF0YSA9IHt9DQogICAgICBkYXRhLnBvbElkID0gdGhpcy5mb3JtLnBvbElkDQogICAgICBkYXRhLmRlc3RpbmF0aW9uUG9ydElkID0gdGhpcy5mb3JtLmRlc3RpbmF0aW9uUG9ydElkDQogICAgICBkYXRhLmNsaWVudElkID0gdGhpcy5mb3JtLmNsaWVudElkDQogICAgICBpZiAodHlwZSA9PT0gImNvbW1vbiIpIHsNCiAgICAgICAgZGF0YS5ib29raW5nU2hpcHBlciA9IHRoaXMuZm9ybS5ib29raW5nU2hpcHBlcg0KICAgICAgICBkYXRhLmJvb2tpbmdDb25zaWduZWUgPSB0aGlzLmZvcm0uYm9va2luZ0NvbnNpZ25lZQ0KICAgICAgICBkYXRhLmJvb2tpbmdOb3RpZnlQYXJ0eSA9IHRoaXMuZm9ybS5ib29raW5nTm90aWZ5UGFydHkNCiAgICAgICAgZGF0YS5ib29raW5nQWdlbnQgPSB0aGlzLmZvcm0uYm9va2luZ0FnZW50DQogICAgICAgIGRhdGEuc2VydmljZVR5cGVJZCA9IHRoaXMuZm9ybS5sb2dpc3RpY3NUeXBlSWQNCiAgICAgIH0NCiAgICAgIGlmICh0eXBlID09PSAicmVsZWFzZSIpIHsNCiAgICAgICAgZGF0YS5ib29raW5nU2hpcHBlciA9IHRoaXMuYm9va2luZ01lc3NhZ2VGb3JtLmJvb2tpbmdTaGlwcGVyDQogICAgICAgIGRhdGEuYm9va2luZ0NvbnNpZ25lZSA9IHRoaXMuYm9va2luZ01lc3NhZ2VGb3JtLmJvb2tpbmdDb25zaWduZWUNCiAgICAgICAgZGF0YS5ib29raW5nTm90aWZ5UGFydHkgPSB0aGlzLmJvb2tpbmdNZXNzYWdlRm9ybS5ib29raW5nTm90aWZ5UGFydHkNCiAgICAgICAgZGF0YS5ib29raW5nQWdlbnQgPSB0aGlzLmJvb2tpbmdNZXNzYWdlRm9ybS5ib29raW5nQWdlbnQNCiAgICAgICAgZGF0YS5zZXJ2aWNlVHlwZUlkID0gdGhpcy5mb3JtLmxvZ2lzdGljc1R5cGVJZA0KICAgICAgfQ0KICAgICAgaWYgKHR5cGUgPT09ICJkaXNwYXRjaCIpIHsNCiAgICAgICAgZGF0YS5wcmVjYXJyaWFnZUFkZHJlc3MgPSB0aGlzLmZvcm0ucHJlY2FycmlhZ2VBZGRyZXNzDQogICAgICAgIGRhdGEucHJlY2FycmlhZ2VDb250YWN0ID0gdGhpcy5mb3JtLnByZWNhcnJpYWdlQ29udGFjdA0KICAgICAgICBkYXRhLnByZWNhcnJpYWdlVGVsID0gdGhpcy5mb3JtLnByZWNhcnJpYWdlVGVsDQogICAgICAgIGRhdGEuc2VydmljZVR5cGVJZCA9IDUNCiAgICAgICAgZGF0YS5kaXNwYXRjaEFkZHJlc3MNCiAgICAgICAgZGF0YS5kaXNwYXRjaENvbnRhY3QNCiAgICAgICAgZGF0YS5kaXNwYXRjaFRlbA0KICAgICAgICBkYXRhLmRpc3BhdGNoUmVtYXJrDQogICAgICB9DQoNCiAgICAgIGFkZENsaWVudHNpbmZvKGRhdGEpLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLkv6Hmga/mlrDlop7miJDlip8iKQ0KICAgICAgfSkNCiAgICB9LA0KICAgIGN1c3RvbU1lcmdlKG9iajEsIG9iajIpIHsNCiAgICAgIHJldHVybiB0aGlzLl8ubWVyZ2VXaXRoKHt9LCBvYmoxLCBvYmoyLCAob2JqVmFsdWUsIHNyY1ZhbHVlKSA9PiB7DQogICAgICAgIGlmICh0aGlzLl8uaXNOdWxsKHNyY1ZhbHVlKSkgew0KICAgICAgICAgIHJldHVybiBvYmpWYWx1ZQ0KICAgICAgICB9DQogICAgICAgIGlmICh0aGlzLl8uaXNCb29sZWFuKG9ialZhbHVlKSkgew0KICAgICAgICAgIHJldHVybiBvYmpWYWx1ZQ0KICAgICAgICB9DQogICAgICAgIHJldHVybiB1bmRlZmluZWQgIC8vIOeUsW1lcmdl5aSE55CGDQogICAgICB9KQ0KICAgIH0sDQogICAgY3VzdG9tTWVyZ2UyKG9iajEsIG9iajIpIHsNCiAgICAgIHJldHVybiB0aGlzLl8ubWVyZ2VXaXRoKHt9LCBvYmoxLCBvYmoyLCAob2JqVmFsdWUsIHNyY1ZhbHVlKSA9PiB7DQogICAgICAgIGlmICh0aGlzLl8uaXNOdWxsKHNyY1ZhbHVlKSkgew0KICAgICAgICAgIHJldHVybiBvYmpWYWx1ZQ0KICAgICAgICB9DQogICAgICAgIGlmICh0aGlzLl8uaXNCb29sZWFuKG9ialZhbHVlKSkgew0KICAgICAgICAgIHJldHVybiBvYmpWYWx1ZQ0KICAgICAgICB9DQogICAgICAgIHJldHVybiB1bmRlZmluZWQgIC8vIOeUsW1lcmdl5aSE55CGDQogICAgICB9KQ0KICAgIH0sDQogICAgLyoqDQogICAgICog6L+U5Zue6YCJ5oup5b6X5ZWG5Yqh6K6i6Iix5pWw5o2uDQogICAgICogQHBhcmFtIHJvdw0KICAgICAqLw0KICAgIHNlbGVjdFBzYUJvb2tpbmcocm93KSB7DQogICAgICB0aGlzLiRjb25maXJtKCLnoa7orqTpgInmi6nvvJ/pgInkuK3kv6Hmga/kvJropobnm5blvZPliY3mlbDmja4iLCAiIiwge2NvbmZpcm1CdXR0b25UZXh0OiAi56Gu6K6k6KaG55uWIn0pDQogICAgICAgIC50aGVuKF8gPT4gew0KICAgICAgICAgIHRoaXMuJHJlZnNbImZvcm0iXS52YWxpZGF0ZSh2YWxpZCA9PiB7DQogICAgICAgICAgICBpZiAodmFsaWQpIHsNCiAgICAgICAgICAgICAgLy8g5aaC5p6c5b2T5YmN5bey5pyJ5ZWG5Yqh5Y2V5Y+3KOivtOaYjuW3sue7j+mAieaLqSks6KaB5YWI5Y+W5raI5YaN6YCJ5oupDQogICAgICAgICAgICAgIGlmICh0aGlzLmZvcm0uc3FkUHNhTm8gIT09IG51bGwpIHsNCiAgICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoIuivt+WFiOWPlua2iOW9k+WJjeiuouiIsSIpDQogICAgICAgICAgICAgICAgcmV0dXJuDQogICAgICAgICAgICAgIH0NCg0KICAgICAgICAgICAgICBsZXQgbWVyZ2VSb3cgPSB0aGlzLmN1c3RvbU1lcmdlKHRoaXMuZm9ybS5yc09wU2VhRmNsTGlzdC5maWx0ZXIoaXRlbSA9PiBpdGVtLnNlYUlkID09PSB0aGlzLmN1clBzYVJvdy5zZWFJZClbMF0sIHJvdykNCg0KICAgICAgICAgICAgICBpZiAocm93LmNhcnJpZXJJZCkgew0KICAgICAgICAgICAgICAgIG1lcmdlUm93LmNhcnJpZXJJZCA9IHJvdy5jYXJyaWVySWQNCiAgICAgICAgICAgICAgfQ0KDQogICAgICAgICAgICAgIC8vIOWwhumAieWumueahOaVsOaNruimhuebluW9k+WJjeaVsOaNrg0KICAgICAgICAgICAgICB0aGlzLmZvcm0ucnNPcFNlYUZjbExpc3QgPSB0aGlzLmZvcm0ucnNPcFNlYUZjbExpc3QubWFwKGl0ZW0gPT4gew0KICAgICAgICAgICAgICAgIGlmIChpdGVtLnNlYUlkID09PSB0aGlzLmN1clBzYVJvdy5zZWFJZCkgew0KICAgICAgICAgICAgICAgICAgaXRlbSA9IG1lcmdlUm93DQogICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgIHJldHVybiBpdGVtDQogICAgICAgICAgICAgIH0pDQogICAgICAgICAgICAgIC8vIHNlbGVjdFJvdy5zcWRQc2FObyA9IHJvdy5wc2FObw0KICAgICAgICAgICAgICBsZXQgbG9jYXRpb25JZHMgPSBbcm93LnBvbElkLCByb3cuZGVzdGluYXRpb25Qb3J0SWRdDQogICAgICAgICAgICAgIGxvY2F0aW9uT3B0aW9ucyh7bG9jYXRpb25TZWxlY3RMaXN0OiBsb2NhdGlvbklkc30pLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICAgICAgICAgIHRoaXMubG9jYXRpb25PcHRpb25zID8gdGhpcy5sb2NhdGlvbk9wdGlvbnMgPSBBcnJheS5mcm9tKG5ldyBNYXAodGhpcy5sb2NhdGlvbk9wdGlvbnMuY29uY2F0KHJlc3BvbnNlLmRhdGEpLm1hcChpdGVtID0+IFtpdGVtLmxvY2F0aW9uSWQsIGl0ZW1dKSkudmFsdWVzKCkpIDogdGhpcy5sb2NhdGlvbk9wdGlvbnMgPSByZXNwb25zZS5kYXRhDQogICAgICAgICAgICAgICAgLy8g5pu05paw5ZWG5Yqh6K6i6Iix6KGoDQogICAgICAgICAgICAgICAgbGV0IGRhdGEgPSB7fQ0KICAgICAgICAgICAgICAgIGRhdGEuc2VhSWQgPSByb3cuc2VhSWQNCiAgICAgICAgICAgICAgICBkYXRhLnJjdE5vID0gdGhpcy5mb3JtLnJjdE5vDQogICAgICAgICAgICAgICAgZGF0YS5jbGllbnRTaG9ydE5hbWUgPSB0aGlzLmZvcm0uY2xpZW50TmFtZS5zcGxpdCgiLyIpWzFdDQogICAgICAgICAgICAgICAgZGF0YS5zYWxlc0lkID0gdGhpcy5mb3JtLnNhbGVzSWQNCiAgICAgICAgICAgICAgICBkYXRhLnNhbGVzQXNzaXN0YW50SWQgPSB0aGlzLmZvcm0uc2FsZXNBc3Npc3RhbnRJZA0KICAgICAgICAgICAgICAgIGRhdGEuZGlzdHJpYnV0aW9uU3RhdHVzID0gIjEiDQogICAgICAgICAgICAgICAgZGF0YS5vcElkID0gdGhpcy4kc3RvcmUuc3RhdGUudXNlci5zaWQNCiAgICAgICAgICAgICAgICBkYXRhLm5ld0Jvb2tpbmdUaW1lID0gbW9tZW50KCkuZm9ybWF0KCJ5eXl5LU1NLUREIEhIOm1tOnNzIikNCg0KICAgICAgICAgICAgICAgIHVwZGF0ZVBzYXJjdChkYXRhKS50aGVuKF8gPT4gew0KICAgICAgICAgICAgICAgICAgdGhpcy5zdWJtaXRGb3JtKCkNCiAgICAgICAgICAgICAgICB9KQ0KICAgICAgICAgICAgICAgIHRoaXMuc2hvd1BzYVJjdCA9IHRydWUNCg0KICAgICAgICAgICAgICAgIHRoaXMub3BlblBzYUJvb2tpbmdTZWxlY3QgPSBmYWxzZQ0KICAgICAgICAgICAgICB9KQ0KICAgICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCLor7flhYjlrozmlbTloavlhpnmk43kvZzljZUiKQ0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0pDQoNCiAgICAgICAgfSkNCiAgICAgICAgLmNhdGNoKF8gPT4gew0KICAgICAgICB9KQ0KICAgIH0sDQogICAgc2VsZWN0UHNhQm9va2luZ09wZW4oaXRlbSkgew0KICAgICAgdGhpcy5jdXJQc2FSb3cgPSBpdGVtDQogICAgICB0aGlzLnBzYUJvb2tpbmdTZWxlY3REYXRhLmRlc3RpbmF0aW9uUG9ydElkID0gdGhpcy5mb3JtLmRlc3RpbmF0aW9uUG9ydElkDQogICAgICB0aGlzLnBzYUJvb2tpbmdTZWxlY3REYXRhLnBvbElkID0gdGhpcy5mb3JtLnBvbElkDQogICAgICB0aGlzLnBzYUJvb2tpbmdTZWxlY3REYXRhLmxvY2F0aW9uT3B0aW9ucyA9IHRoaXMubG9jYXRpb25PcHRpb25zDQoNCiAgICAgIHRoaXMub3BlblBzYUJvb2tpbmdTZWxlY3QgPSB0cnVlDQogICAgfSwNCiAgICBhZGRQcm9ncmVzcyhyc09wTG9nTGlzdCwgcHJvY2Vzc0lkKSB7DQogICAgICBsZXQgZGF0YSA9IHt9DQogICAgICBpZiAocHJvY2Vzc0lkID09PSAxNSkgew0KICAgICAgICBkYXRhLnByb2Nlc3NJZCA9IHByb2Nlc3NJZA0KICAgICAgICBkYXRhLnByb2Nlc3NTdGF0dXNJZCA9IDcNCiAgICAgICAgZGF0YS5wcm9jZXNzU3RhdHVzVGltZSA9IG1vbWVudCgpLmZvcm1hdCgieXl5eS1NTS1ERCBISDptbTpzcyIpDQogICAgICAgIGRhdGEub3BJZCA9IHRoaXMuJHN0b3JlLnN0YXRlLnVzZXIuc2lkDQogICAgICAgIGRhdGEuYmFzUHJvY2VzcyA9IHtwcm9jZXNzU2hvcnROYW1lOiAi6KOF6Ii5In0NCiAgICAgICAgZGF0YS5iYXNQcm9jZXNzU3RhdHVzID0ge3Byb2Nlc3NTdGF0dXNTaG9ydE5hbWU6ICLlrozmiJAifQ0KICAgICAgfQ0KICAgICAgaWYgKHByb2Nlc3NJZCA9PT0gMTgpIHsNCiAgICAgICAgZGF0YS5wcm9jZXNzSWQgPSBwcm9jZXNzSWQNCiAgICAgICAgZGF0YS5wcm9jZXNzU3RhdHVzSWQgPSA3DQogICAgICAgIGRhdGEucHJvY2Vzc1N0YXR1c1RpbWUgPSBtb21lbnQoKS5mb3JtYXQoInl5eXktTU0tREQgSEg6bW06c3MiKQ0KICAgICAgICBkYXRhLm9wSWQgPSB0aGlzLiRzdG9yZS5zdGF0ZS51c2VyLnNpZA0KICAgICAgICBkYXRhLmJhc1Byb2Nlc3MgPSB7cHJvY2Vzc1Nob3J0TmFtZTogIuWIsOa4ryJ9DQogICAgICAgIGRhdGEuYmFzUHJvY2Vzc1N0YXR1cyA9IHtwcm9jZXNzU3RhdHVzU2hvcnROYW1lOiAi5a6M5oiQIn0NCiAgICAgIH0NCg0KICAgICAgcnNPcExvZ0xpc3QucHVzaChkYXRhKQ0KICAgIH0sDQogICAgLy8g5bqU5pS25Yu+6YCJDQogICAgaGFuZGxlUmVjZWl2ZVNlbGVjdGVkKHJvd3MpIHsNCiAgICAgIHRoaXMuc2VsZWN0ZWRQcmludENoYXJnZXMgPSByb3dzDQogICAgfSwNCiAgICAvLyDmj5DljZXmiZPljbANCiAgICBnZXRCaWxsT2ZMYWRpbmcodHlwZSkgew0KICAgICAgaGlwcmludFRlbXBsYXRlID0gbnVsbA0KICAgICAgbGV0IGRhdGEgPSB7fQ0KDQogICAgICBsZXQgcHJpbnRSb3cgPSB0aGlzLmJvb2tpbmdCaWxsUHJpbnRSb3cgPyB0aGlzLmJvb2tpbmdCaWxsUHJpbnRSb3dbMF0gOiBudWxsDQogICAgICBpZiAoIXByaW50Um93KSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZygi6K+35YWI5Yu+6YCJ5LiA5p2h5o+Q5Y2V5L+h5oGvIikNCiAgICAgICAgcmV0dXJuDQogICAgICB9DQoNCiAgICAgIGxldCBwcmVmaXggPSAiPGRpdiBzdHlsZT1cIndvcmQtYnJlYWs6IGtlZXAtYWxsICFpbXBvcnRhbnQ7IFxuIiArDQogICAgICAgICIgICAgICAgICAgICB3b3JkLXdyYXA6IGJyZWFrLXdvcmQgIWltcG9ydGFudDsgXG4iICsNCiAgICAgICAgIiAgICAgICAgICAgIHdoaXRlLXNwYWNlOiBwcmUtd3JhcCAhaW1wb3J0YW50O1wiPiINCiAgICAgIGxldCBzdWZmaXggPSAiPC9kaXY+Ig0KDQogICAgICBkYXRhLmJvb2tpbmdTaGlwcGVyID0gcHJpbnRSb3cuYm9va2luZ1NoaXBwZXIgPyBwcmludFJvdy5ib29raW5nU2hpcHBlci5yZXBsYWNlKC9cbi9nLCAiPC9icj4iKSA6IG51bGwNCiAgICAgIGRhdGEuYm9va2luZ1NoaXBwZXIgPSBwcmVmaXggKyBkYXRhLmJvb2tpbmdTaGlwcGVyICsgc3VmZml4DQogICAgICBkYXRhLmJvb2tpbmdDb25zaWduZWUgPSBwcmludFJvdy5ib29raW5nQ29uc2lnbmVlID8gcHJpbnRSb3cuYm9va2luZ0NvbnNpZ25lZS5yZXBsYWNlKC9cbi9nLCAiPC9icj4iKSA6IG51bGwNCiAgICAgIGRhdGEuYm9va2luZ0NvbnNpZ25lZSA9IHByZWZpeCArIGRhdGEuYm9va2luZ0NvbnNpZ25lZSArIHN1ZmZpeA0KICAgICAgZGF0YS5ib29raW5nTm90aWZ5UGFydHkgPSBwcmludFJvdy5ib29raW5nTm90aWZ5UGFydHkgPyBwcmludFJvdy5ib29raW5nTm90aWZ5UGFydHkucmVwbGFjZSgvXG4vZywgIjwvYnI+IikgOiBudWxsDQogICAgICBkYXRhLmJvb2tpbmdOb3RpZnlQYXJ0eSA9IHByZWZpeCArIGRhdGEuYm9va2luZ05vdGlmeVBhcnR5ICsgc3VmZml4DQogICAgICBkYXRhLmJvb2tpbmdBZ2VudCA9IHByaW50Um93LmJvb2tpbmdBZ2VudCA/IHByaW50Um93LmJvb2tpbmdBZ2VudC5yZXBsYWNlKC9cbi9nLCAiPC9icj4iKSA6IG51bGwNCiAgICAgIGRhdGEuYm9va2luZ0FnZW50ID0gcHJlZml4ICsgZGF0YS5ib29raW5nQWdlbnQgKyBzdWZmaXgNCiAgICAgIGRhdGEuY29udGFpbmVyTm8gPSBwcmludFJvdy5jb250YWluZXJObw0KICAgICAgZGF0YS5yY3RObyA9IHRoaXMuZm9ybS5yY3RObw0KICAgICAgZGF0YS5jb250YWluZXJUeXBlID0gcHJpbnRSb3cuY29udGFpbmVyVHlwZQ0KICAgICAgZGF0YS5jb250cmFjdE5vID0gcHJpbnRSb3cuY29udHJhY3RObw0KICAgICAgZGF0YS5zZWFsTm8gPSBwcmludFJvdy5zZWFsTm8NCiAgICAgIGRhdGEuc2hpcHBpbmdNYXJrID0gcHJpbnRSb3cuc2hpcHBpbmdNYXJrDQogICAgICBsZXQgcGFja2FnZVF1YW50aXR5U3VtID0gMA0KICAgICAgcHJpbnRSb3cucGFja2FnZVF1YW50aXR5LnNwbGl0KC9cbi9nKS5tYXAoaXRlbSA9PiB7DQogICAgICAgIC8vIOaPkOWPlnBhY2thZ2VRdWFudGl0eeS4reeahOaVsOWtlw0KICAgICAgICBsZXQgbnVtID0gaXRlbS5tYXRjaCgvXGQrL2cpDQogICAgICAgIHBhY2thZ2VRdWFudGl0eVN1bSArPSBOdW1iZXIobnVtKQ0KICAgICAgfSkNCiAgICAgIC8vIOacgOWQjuWKoOS4iuWOn+Wni+eahOWNleS9jQ0KICAgICAgZGF0YS5wYWNrYWdlUXVhbnRpdHkgPSBwYWNrYWdlUXVhbnRpdHlTdW0gKyBwcmludFJvdy5wYWNrYWdlUXVhbnRpdHkuc3BsaXQoL1xuL2cpWzBdLnNwbGl0KCIgIilbMV0NCiAgICAgIGRhdGEucmVjZWxwID0gcHJpbnRSb3cuY2l0eSA/IHByaW50Um93LmNpdHkgOiAiR1VBTkcgWkhPVSINCiAgICAgIGRhdGEuZ29vZHNEZXNjcmlwdGlvbiA9IHByaW50Um93Lmdvb2RzRGVzY3JpcHRpb24gPyBwcmludFJvdy5nb29kc0Rlc2NyaXB0aW9uLnJlcGxhY2UoL1xuL2csICI8L2JyPiIpIDogbnVsbA0KICAgICAgZGF0YS5nb29kc0Rlc2NyaXB0aW9uID0gcHJlZml4ICsgZGF0YS5nb29kc0Rlc2NyaXB0aW9uICsgc3VmZml4DQogICAgICBsZXQgZ29vZHNWb2x1bWVTdW0gPSAwDQogICAgICBwcmludFJvdy5nb29kc1ZvbHVtZSA/IHByaW50Um93Lmdvb2RzVm9sdW1lLnNwbGl0KC9cbi9nKS5tYXAoaXRlbSA9PiB7DQogICAgICAgIGdvb2RzVm9sdW1lU3VtICs9IE51bWJlcihpdGVtLnRyaW0oKSkNCiAgICAgIH0pIDogbnVsbA0KICAgICAgZGF0YS5nb29kc1ZvbHVtZSA9IGdvb2RzVm9sdW1lU3VtICsgIkNCTSINCiAgICAgIGxldCBncm9zc1dlaWdodFN1bSA9IDANCiAgICAgIHByaW50Um93Lmdyb3NzV2VpZ2h0ID8gcHJpbnRSb3cuZ3Jvc3NXZWlnaHQuc3BsaXQoL1xuL2cpLm1hcChpdGVtID0+IHsNCiAgICAgICAgZ3Jvc3NXZWlnaHRTdW0gKz0gTnVtYmVyKGl0ZW0udHJpbSgpKQ0KICAgICAgfSkgOiBudWxsDQogICAgICBkYXRhLmdyb3NzV2VpZ2h0ID0gZ3Jvc3NXZWlnaHRTdW0gKyAiS0dTIg0KICAgICAgZGF0YS5ibFR5cGVDb2RlID0gcHJpbnRSb3cuYmxUeXBlQ29kZQ0KICAgICAgZGF0YS5ibEZvcm1Db2RlID0gcHJpbnRSb3cuYmxGb3JtQ29kZQ0KICAgICAgZGF0YS5zcWREb2NEZWxpdmVyeVdheSA9IHByaW50Um93LnNxZERvY0RlbGl2ZXJ5V2F5DQogICAgICBkYXRhLnBvbE5hbWUgPSBwcmludFJvdy5wb2xOYW1lDQogICAgICBkYXRhLnBvZE5hbWUgPSBwcmludFJvdy5kZXN0aW5hdGlvblBvcnQNCiAgICAgIGRhdGEuZGVzdGluYXRpb25Qb3J0ID0gcHJpbnRSb3cuZGVzdGluYXRpb25Qb3J0DQogICAgICAvLyDotKflkI0NCiAgICAgIGRhdGEuZ29vZHNOYW1lU3VtbWFyeSA9IHRoaXMuZm9ybS5nb29kc05hbWVTdW1tYXJ5DQogICAgICAvLyBzb+WPtw0KICAgICAgZGF0YS5zb05vID0gdGhpcy5mb3JtLnNvTm8NCg0KICAgICAgZGF0YS5ibE51bWJlcnMgPSBwcmludFJvdy5ibE51bWJlcnMNCiAgICAgIGRhdGEuaXNzdWVEYXRlID0gcHJpbnRSb3cuaXNzdWVEYXRlDQogICAgICBkYXRhLm1ibE5vID0gcHJpbnRSb3cubUJsTm8NCiAgICAgIGRhdGEuaXNzdWVQbGFjZSA9IHByaW50Um93Lmlzc3VlUGxhY2UNCiAgICAgIGNvbnN0IG9wdGlvbnMgPSB7DQogICAgICAgIGRheTogIjItZGlnaXQiLA0KICAgICAgICBtb250aDogInNob3J0IiwNCiAgICAgICAgeWVhcjogIm51bWVyaWMiDQogICAgICB9DQogICAgICBkYXRhLm9uQm9hcmREYXRlID0gcHJpbnRSb3cub25Cb2FyZERhdGUgPyBuZXcgRGF0ZShwcmludFJvdy5vbkJvYXJkRGF0ZSkudG9Mb2NhbGVEYXRlU3RyaW5nKCJlbi1HQiIsIG9wdGlvbnMpLnRvVXBwZXJDYXNlKCkgOiBudWxsDQogICAgICBkYXRhLmRlY2xhcmVkVmFsdWUgPSBwcmludFJvdy5kZWNsYXJlZFZhbHVlDQogICAgICBkYXRhLmxvZ2lzdGljc1Rlcm1zID0gdGhpcy5mb3JtLmxvZ2lzdGljc1Rlcm1zDQogICAgICBkYXRhLmZpcnN0VmVzc2VsID0gdGhpcy5mb3JtLmZpcnN0VmVzc2VsDQogICAgICBkYXRhLnBheVdheSA9IHByaW50Um93LnBheVdheQ0KICAgICAgZGF0YS5mb3J3YXJkaW5nQWdlbnQgPSBwcmludFJvdy5ib29raW5nQWdlbnQNCiAgICAgIGRhdGEuYmxSZW1hcmsgPSBwcmludFJvdy5ibFJlbWFyayA/IHByaW50Um93LmJsUmVtYXJrLnJlcGxhY2UoL1xuL2csICI8L2JyPiIpIDogbnVsbA0KICAgICAgbGV0IGJvb2tpbmdObyA9ICIiDQogICAgICB0aGlzLmZvcm0uc29ObyA/IHRoaXMuZm9ybS5zb05vLnNwbGl0KCIvIikubWFwKGl0ZW0gPT4gew0KICAgICAgICBib29raW5nTm8gKz0gaXRlbSArICI8L2JyPiINCiAgICAgIH0pIDogbnVsbA0KICAgICAgZGF0YS5ib29raW5nTm8gPSBib29raW5nTm8NCiAgICAgIGlmIChwcmludFJvdy5jb250YWluZXJObykgew0KICAgICAgICBsZXQgc2VhbE5vQXJyID0gcHJpbnRSb3cuc2VhbE5vID8gcHJpbnRSb3cuc2VhbE5vLnNwbGl0KC9cbi9nKSA6IG51bGwNCiAgICAgICAgbGV0IHBhY2thZ2VRdWFudGl0eUFyciA9IHByaW50Um93LnBhY2thZ2VRdWFudGl0eSA/IHByaW50Um93LnBhY2thZ2VRdWFudGl0eS5zcGxpdCgvXG4vZykgOiBudWxsDQogICAgICAgIGxldCBnb29kc1ZvbHVtZUFyciA9IHByaW50Um93Lmdvb2RzVm9sdW1lID8gcHJpbnRSb3cuZ29vZHNWb2x1bWUuc3BsaXQoL1xuL2cpIDogbnVsbA0KICAgICAgICBsZXQgY29udGFpbmVyVHlwZUFyciA9IHByaW50Um93LmNvbnRhaW5lclR5cGUgPyBwcmludFJvdy5jb250YWluZXJUeXBlLnNwbGl0KC9cbi9nKSA6IG51bGwNCiAgICAgICAgbGV0IGdyb3NzV2VpZ2h0QXJyID0gcHJpbnRSb3cuZ3Jvc3NXZWlnaHQgPyBwcmludFJvdy5ncm9zc1dlaWdodC5zcGxpdCgvXG4vZykgOiBudWxsDQogICAgICAgIGxldCBjb250YWluZXJzID0gMA0KICAgICAgICBjb250YWluZXJUeXBlQXJyID8gY29udGFpbmVyVHlwZUFyci5tYXAoaXRlbSA9PiB7DQogICAgICAgICAgbGV0IHJldmVudWUgPSBpdGVtLnNwbGl0KCJ4IikNCiAgICAgICAgICBjb250YWluZXJzID0gTnVtYmVyKHJldmVudWVbMF0pICsgTnVtYmVyKGNvbnRhaW5lcnMpDQogICAgICAgIH0pIDogbnVsbA0KICAgICAgICBkYXRhLmNvbnRhaW5lclF1YW50aXR5ID0gdG9Xb3Jkcyhjb250YWluZXJzKSArICIoIiArIHRoaXMuZm9ybS5sb2dpc3RpY3NUeXBlRW5OYW1lICsgIikiDQogICAgICAgIHByaW50Um93Lmdvb2RzU3VtbWFyeSA9ICIiDQogICAgICAgIHByaW50Um93LmNvbnRhaW5lck5vLnNwbGl0KC9cbi9nKS5tYXAoKGl0ZW0sIGluZGV4KSA9PiB7DQogICAgICAgICAgcHJpbnRSb3cuZ29vZHNTdW1tYXJ5ICs9IGl0ZW0gKyAiLyIgKyAoKHNlYWxOb0FyciAmJiBzZWFsTm9BcnIubGVuZ3RoID4gaW5kZXgpID8gc2VhbE5vQXJyW2luZGV4XSA6IG51bGwpICsgIi8iICsgKChjb250YWluZXJUeXBlQXJyICYmIGNvbnRhaW5lclR5cGVBcnIubGVuZ3RoID4gaW5kZXgpID8gY29udGFpbmVyVHlwZUFycltpbmRleF0gOiBudWxsKSArICIvIiArICgocGFja2FnZVF1YW50aXR5QXJyICYmIHBhY2thZ2VRdWFudGl0eUFyci5sZW5ndGggPiBpbmRleCkgPyBwYWNrYWdlUXVhbnRpdHlBcnJbaW5kZXhdIDogbnVsbCkgKyAiLyIgKyAoKGdyb3NzV2VpZ2h0QXJyICYmIGdyb3NzV2VpZ2h0QXJyLmxlbmd0aCA+IGluZGV4KSA/IGdyb3NzV2VpZ2h0QXJyW2luZGV4XSA6IG51bGwpICsgIktHUyIgKyAiLyIgKyAoKGdvb2RzVm9sdW1lQXJyICYmIGdvb2RzVm9sdW1lQXJyLmxlbmd0aCA+IGluZGV4KSA/IGdvb2RzVm9sdW1lQXJyW2luZGV4XSA6IG51bGwpICsgIkNCTSIgKyAiPC9icj4iDQogICAgICAgIH0pDQogICAgICB9DQogICAgICAvLyDlh7rljZXlnLANCiAgICAgIGRhdGEuaXNzdWVQbGFjZSA9ICIiDQogICAgICBkYXRhLmNhcnJpZXIgPSBwcmludFJvdy5jb250YWluZXJUeXBlID8gcHJpbnRSb3cuY29udGFpbmVyVHlwZS5zcGxpdCgvXG4vZykubWFwKGl0ZW0gPT4gew0KICAgICAgICByZXR1cm4gaXRlbSArICI8L2JyPiINCiAgICAgIH0pIDogbnVsbA0KICAgICAgZGF0YS5nb29kc1N1bW1hcnkgPSBwcmludFJvdy5nb29kc1N1bW1hcnkNCg0KICAgICAgZGF0YSA9IHRoaXMuY29udmVydE9iamVjdEtleXNUb1VwcGVyQ2FzZShkYXRhKQ0KICAgICAgaWYgKHR5cGUgPT09ICLlpZfmiZPmj5DljZUiKSB7DQogICAgICAgIGhpcHJpbnRUZW1wbGF0ZSA9IG5ldyBoaXByaW50LlByaW50VGVtcGxhdGUoe3RlbXBsYXRlOiBiaWxsT2ZMYWRpbmd9KQ0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgaGlwcmludFRlbXBsYXRlID0gbmV3IGhpcHJpbnQuUHJpbnRUZW1wbGF0ZSh7dGVtcGxhdGU6IGJpbGxPZkxhZGluZ1JlbGVhc2V9KQ0KICAgICAgfQ0KDQogICAgICAvLyDmiZPlvIDpooTop4jnu4Tku7YNCiAgICAgIHRoaXMuJHJlZnMucHJlVmlldy5wcmludChoaXByaW50VGVtcGxhdGUsIGRhdGEpDQogICAgfSwNCiAgICBjbG9zZUJvb2tpbmdNZXNzYWdlKCkgew0KICAgICAgdGhpcy5vcGVuQm9va2luZ01lc3NhZ2UgPSBmYWxzZQ0KICAgICAgdGhpcy5ib29raW5nTWVzc2FnZUZvcm0gPSB7fQ0KICAgIH0sDQogICAgaGFuZGxlQm9va2luZ01lc3NhZ2VVcGRhdGUocm93KSB7DQogICAgICB0aGlzLmJvb2tpbmdNZXNzYWdlU3RhdHVzID0gImVkaXQiDQogICAgICB0aGlzLmJvb2tpbmdNZXNzYWdlRm9ybSA9IHJvdw0KICAgICAgdGhpcy5vcGVuQm9va2luZ01lc3NhZ2UgPSB0cnVlDQogICAgfSwNCiAgICBoYW5kbGVTZWxlY3Rpb25DaGFuZ2UodmFsKSB7DQogICAgICAvLyDli77pgInopoHmiZPljbDnmoTmj5DljZUs5Y+q6IO95Yu+6YCJ5LiA6aG5DQogICAgICBpZiAodmFsLmxlbmd0aCA+IDEpIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCLlj6rog73li77pgInkuIDmnaHmj5DljZXkv6Hmga8iKQ0KICAgICAgICByZXR1cm4NCiAgICAgIH0NCg0KICAgICAgdGhpcy5ib29raW5nQmlsbFByaW50Um93ID0gdmFsDQogICAgfSwNCiAgICBhZGRCb29raW5nTWVzc2FnZSgpIHsNCiAgICAgIHRoaXMuYm9va2luZ01lc3NhZ2VGb3JtID0gew0KICAgICAgICByY3RJZDogdGhpcy5mb3JtLnJjdElkLA0KICAgICAgICBwb2xOYW1lOiB0aGlzLmZvcm0ucG9sTmFtZSwNCiAgICAgICAgcG9kTmFtZTogdGhpcy5mb3JtLnBvZE5hbWUsDQogICAgICAgIGRlc3RpbmF0aW9uUG9ydDogdGhpcy5mb3JtLmRlc3RpbmF0aW9uDQogICAgICB9DQogICAgICB0aGlzLmJvb2tpbmdNZXNzYWdlU3RhdHVzID0gImFkZCINCiAgICAgIHRoaXMub3BlbkJvb2tpbmdNZXNzYWdlID0gdHJ1ZQ0KICAgIH0sDQogICAgYm9va2luZ01lc3NhZ2VDb25maXJtKCkgew0KICAgICAgaWYgKHRoaXMuYm9va2luZ01lc3NhZ2VTdGF0dXMgPT09ICJhZGQiKSB7DQogICAgICAgIHRoaXMuYm9va2luZ01lc3NhZ2VMaXN0LnB1c2godGhpcy5ib29raW5nTWVzc2FnZUZvcm0pDQogICAgICB9DQoNCiAgICAgIHRoaXMub3BlbkJvb2tpbmdNZXNzYWdlID0gZmFsc2UNCiAgICB9LA0KICAgIC8vIOiuouiIseWNleaJk+WNsA0KICAgIGdldEJvb2tpbmdCaWxsKHNlcnZpY2VPYmplY3QpIHsNCiAgICAgIGxldCBkYXRhID0ge30NCiAgICAgIGRhdGEuYm9va2luZ1NoaXBwZXIgPSB0aGlzLmZvcm0uYm9va2luZ1NoaXBwZXINCiAgICAgIGRhdGEuYm9va2luZ0NvbnNpZ25lZSA9IHRoaXMuZm9ybS5ib29raW5nQ29uc2lnbmVlDQogICAgICBkYXRhLmJvb2tpbmdOb3RpZnlQYXJ0eSA9IHRoaXMuZm9ybS5ib29raW5nTm90aWZ5UGFydHkNCiAgICAgIGRhdGEubG9naXN0aWNzVGVybXMgPSB0aGlzLmZvcm0ubG9naXN0aWNzVGVybXMNCiAgICAgIGRhdGEudHJhZGluZ1Rlcm1zID0gdGhpcy5mb3JtLnRyYWRpbmdUZXJtcw0KICAgICAgZGF0YS5jbGllbnROYW1lID0gc2VydmljZU9iamVjdC5zdXBwbGllck5hbWUNCiAgICAgIGRhdGEuY2xpZW50Q29udGFjdCA9IHRoaXMuZm9ybS5jbGllbnRDb250YWN0DQogICAgICBsZXQgc3RyID0gdGhpcy5mb3JtLmRlc3RpbmF0aW9uUG9ydCA/IHRoaXMuZm9ybS5kZXN0aW5hdGlvblBvcnQuc3BsaXQoIigiKSA6IFtdDQogICAgICBkYXRhLmRlc3RpbmF0aW9uUG9ydDEgPSBzdHJbMF0NCiAgICAgIGRhdGEuZGVzdGluYXRpb25Qb3J0MiA9IHN0clsxXSA/ICgiKCIgKyBzdHJbMV0pIDogIiINCiAgICAgIGxldCBwb2xTcGxpdEFyciA9IHRoaXMuZm9ybS5wb2xOYW1lID8gdGhpcy5mb3JtLnBvbE5hbWUuc3BsaXQoIigiKSA6IFtdDQogICAgICBkYXRhLnBvbE5hbWUgPSBwb2xTcGxpdEFyclswXQ0KICAgICAgZGF0YS5wb2xOYW1lMSA9IHBvbFNwbGl0QXJyWzFdID8gKCIoIiArIHBvbFNwbGl0QXJyWzFdKSA6ICIiDQogICAgICBsZXQgcG9kU3BsaXRBcnIgPSB0aGlzLmZvcm0ucG9kTmFtZSA/IHRoaXMuZm9ybS5wb2ROYW1lLnNwbGl0KCIoIikgOiBbXQ0KICAgICAgZGF0YS5wb2ROYW1lID0gcG9kU3BsaXRBcnJbMF0NCiAgICAgIGRhdGEucG9kTmFtZTEgPSBwb2RTcGxpdEFyclsxXSA/ICgiKCIgKyBwb2RTcGxpdEFyclsxXSkgOiAiIg0KICAgICAgZGF0YS5yY3RObyA9IHRoaXMuZm9ybS5yY3RObw0KICAgICAgZGF0YS5wYWNrYWdlUXVhbnRpdHkgPSB0aGlzLmZvcm0ucGFja2FnZVF1YW50aXR5ICsgIlBLRyINCiAgICAgIGRhdGEuZ3Jvc3NXZWlnaHQgPSB0aGlzLmZvcm0uZ3Jvc3NXZWlnaHQgKyAiIEtHUyINCiAgICAgIGRhdGEuZ29vZHNWb2x1bWUgPSB0aGlzLmZvcm0uZ29vZHNWb2x1bWUgKyAiIENCTSINCiAgICAgIGRhdGEubm9EaXZpZGVkQWxsb3dlZFllcyA9IHRoaXMuZm9ybS5ub0RpdmlkZWRBbGxvd2VkID8gIuKImiIgOiAiIg0KICAgICAgZGF0YS5ub0RpdmlkZWRBbGxvd2VkTm8gPSB0aGlzLmZvcm0ubm9EaXZpZGVkQWxsb3dlZCA/ICIiIDogIuKImiINCiAgICAgIGRhdGEubm9UcmFuc2ZlckFsbG93ZWRZZXMgPSB0aGlzLmZvcm0ubm9UcmFuc2ZlckFsbG93ZWQgPyAi4oiaIiA6ICIiDQogICAgICBkYXRhLm5vVHJhbnNmZXJBbGxvd2VkTm8gPSB0aGlzLmZvcm0ubm9UcmFuc2ZlckFsbG93ZWQgPyAiIiA6ICLiiJoiDQogICAgICBkYXRhLm9jZWFuVmVzc2VsID0gKHNlcnZpY2VPYmplY3QuZmlyc3RWZXNzZWwgPyBzZXJ2aWNlT2JqZWN0LmZpcnN0VmVzc2VsIDogIiIpICsgIiAiICsgKHNlcnZpY2VPYmplY3QuaW5xdWlyeVNjaGVkdWxlU3VtbWFyeSA/IHNlcnZpY2VPYmplY3QuaW5xdWlyeVNjaGVkdWxlU3VtbWFyeSA6ICIiKQ0KICAgICAgZGF0YS5wcmludERhdGUgPSBtb21lbnQoKS5mb3JtYXQoIllZWVkvTU0vREQiKQ0KICAgICAgZGF0YS5jbGllbnRDb250YWN0ID0gdGhpcy5mb3JtLmNsaWVudENvbnRhY3QNCiAgICAgIGRhdGEuZnJlaWdodFBhaWRXYXlDb2RlID0gdGhpcy5mb3JtLmZyZWlnaHRQYWlkV2F5Q29kZQ0KICAgICAgZGF0YS5sb2dpc3RpY3NUeXBlRW5OYW1lID0gdGhpcy5mb3JtLmxvZ2lzdGljc1R5cGVFbk5hbWUNCiAgICAgIGRhdGEuY2xpZW50Q29udGFjdFRlbCA9IHRoaXMuZm9ybS5jbGllbnRDb250YWN0VGVsDQogICAgICBkYXRhLmJvb2tpbmdBZ2VudFJlbWFyayA9IHNlcnZpY2VPYmplY3QuYm9va2luZ0FnZW50UmVtYXJrDQogICAgICBsZXQgY2hhcmdlTGlzdFN0ciA9ICIiDQogICAgICBzZXJ2aWNlT2JqZWN0LmJvb2tpbmdDaGFyZ2VSZW1hcmsgPyBzZXJ2aWNlT2JqZWN0LmJvb2tpbmdDaGFyZ2VSZW1hcmsuc3BsaXQoIlxuIikuZmlsdGVyKGl0ZW0gPT4gaXRlbSAhPT0gIiIpLm1hcChpdGVtID0+IGNoYXJnZUxpc3RTdHIgKz0gaXRlbSArICI8YnI+IikgOiAiIg0KICAgICAgZGF0YS5ib29raW5nQ2hhcmdlUmVtYXJrID0gY2hhcmdlTGlzdFN0cg0KICAgICAgZGF0YS5zaGlwcGluZ01hcmsgPSB0aGlzLmZvcm0uc2hpcHBpbmdNYXJrDQogICAgICBpZiAodGhpcy4kc3RvcmUuc3RhdGUuZGF0YS5hbGxSc1N0YWZmTGlzdC5sZW5ndGggPT0gMCB8fCB0aGlzLiRzdG9yZS5zdGF0ZS5kYXRhLnJlZGlzTGlzdC5hbGxSc1N0YWZmTGlzdCkgew0KICAgICAgICBzdG9yZS5kaXNwYXRjaCgiZ2V0QWxsUnNTdGFmZkxpc3QiKQ0KICAgICAgfQ0KDQogICAgICBsZXQgc3RhZmYgPSB0aGlzLiRzdG9yZS5zdGF0ZS5kYXRhLmFsbFJzU3RhZmZMaXN0LmZpbHRlcihyc1N0YWZmID0+IHJzU3RhZmYuc3RhZmZJZCA9PSB0aGlzLiRzdG9yZS5zdGF0ZS51c2VyLnNpZClbMF0NCiAgICAgIGlmIChzdGFmZikgew0KICAgICAgICBkYXRhLmN1clRlbCA9IHN0YWZmLnN0YWZmUGhvbmVOdW0NCiAgICAgICAgZGF0YS5jdXJOYW1lID0gc3RhZmYuc3RhZmZTaG9ydE5hbWUNCiAgICAgICAgZGF0YS5jdXJFbWFpbCA9IHN0YWZmLnN0YWZmRW1haWxFbnRlcnByaXNlDQogICAgICB9DQoNCiAgICAgIGRhdGEuZ29vZHNOYW1lU3VtbWFyeSA9IHRoaXMuZm9ybS5nb29kc05hbWVTdW1tYXJ5DQogICAgICBkYXRhLnZlc3NlbFN1bW1hcnkgPSAiYnkgIiArICh0aGlzLmZvcm0uY2FycmllckVuTmFtZSA/IHRoaXMuZm9ybS5jYXJyaWVyRW5OYW1lIDogIiIpICsgIjwvYnI+ICINCiAgICAgIGRhdGEudmVzc2VsU3VtbWFyeSArPSAiRVREOiAiICsgKHNlcnZpY2VPYmplY3QuZXRkID8gc2VydmljZU9iamVjdC5ldGQgOiAiIikNCg0KICAgICAgZGF0YS5yZXZlbnVlVG9uID0gdGhpcy5mb3JtLnJldmVudWVUb24NCiAgICAgIGRhdGEuY3VyRGF0ZSA9IG1vbWVudCgpLmZvcm1hdCgiWVlZWU1NREQiKQ0KICAgICAgZGF0YS5jb21wYW55TmFtZSA9ICLlub/lt57nkZ7ml5flm73pmYXotKfov5Dku6PnkIbmnInpmZDlhazlj7giDQogICAgICBpZiAodGhpcy5mb3JtLm9yZGVyQmVsb25nc1RvID09PSAiR1pSUyIpIHsNCiAgICAgICAgZGF0YS5jb21wYW55TmFtZSA9ICLlub/lt57nkZ7ml5flm73pmYXotKfov5Dku6PnkIbmnInpmZDlhazlj7giDQogICAgICB9IGVsc2UgaWYgKHRoaXMuZm9ybS5vcmRlckJlbG9uZ3NUbyA9PT0gIkhLUlMiKSB7DQogICAgICAgIGRhdGEuY29tcGFueU5hbWUgPSAi55Ge5peX5Zu96ZmF77yI5Lit5Zu977yJ5pyJ6ZmQ5YWs5Y+4Ig0KICAgICAgfSBlbHNlIGlmICh0aGlzLmZvcm0ub3JkZXJCZWxvbmdzVG8gPT09ICJDRkwiKSB7DQogICAgICAgIGRhdGEuY29tcGFueU5hbWUgPSAi5bm/5bee5q2j5rO95Zu96ZmF6LSn6L+Q5Luj55CG5pyJ6ZmQ5YWs5Y+4Ig0KICAgICAgfSBlbHNlIGlmICh0aGlzLmZvcm0ub3JkZXJCZWxvbmdzVG8gPT09ICJHWlZTIikgew0KICAgICAgICBkYXRhLmNvbXBhbnlOYW1lID0gIuW5v+W3nuWklua1t+WbvemZheS+m+W6lOmTvuaciemZkOWFrOWPuCINCiAgICAgIH0gZWxzZSBpZiAodGhpcy5mb3JtLm9yZGVyQmVsb25nc1RvID09PSAiQ0FTSCIpIHsNCiAgICAgICAgZGF0YS5jb21wYW55TmFtZSA9ICLlhazlj7jnjrDph5HotKbmiLciDQogICAgICB9DQoNCiAgICAgIGlmICh0aGlzLmZvcm0ucmN0Tm8gJiYgdGhpcy5mb3JtLnJjdE5vLnN0YXJ0c1dpdGgoIkNGTCIpKSB7DQogICAgICAgIGhpcHJpbnRUZW1wbGF0ZSA9IG5ldyBoaXByaW50LlByaW50VGVtcGxhdGUoe3RlbXBsYXRlOiBDRkxCb29raW5nfSkNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIGhpcHJpbnRUZW1wbGF0ZSA9IG5ldyBoaXByaW50LlByaW50VGVtcGxhdGUoe3RlbXBsYXRlOiBib29raW5nfSkNCiAgICAgIH0NCiAgICAgIC8vIOaJk+W8gOmihOiniOe7hOS7tg0KICAgICAgdGhpcy4kcmVmcy5wcmVWaWV3LnByaW50KGhpcHJpbnRUZW1wbGF0ZSwgZGF0YSkNCiAgICB9LA0KICAgIGdldERpc3BhdGNoaW5nQmlsbChzZXJ2aWNlT2JqZWN0KSB7DQogICAgICBoaXByaW50VGVtcGxhdGUgPSBudWxsDQogICAgICBsZXQgZGF0YSA9IHt9DQogICAgICBkYXRhLnRydWNrTGlzdCA9IHNlcnZpY2VPYmplY3QucnNPcFRydWNrTGlzdA0KICAgICAgLy8gZGF0YS5jb21wYW55ID0gKHRoaXMuZm9ybS5jbGllbnROYW1lLnNwbGl0KCIvIilbMl0gPT09ICJudWxsIiB8fCB0aGlzLmZvcm0uY2xpZW50TmFtZS5zcGxpdCgiLyIpWzJdID09PSAiIikgPyB0aGlzLmZvcm0uY2xpZW50TmFtZS5zcGxpdCgiLyIpWzFdIDogdGhpcy5mb3JtLmNsaWVudE5hbWUuc3BsaXQoIi8iKVsyXQ0KICAgICAgZGF0YS5jb21wYW55ID0gc2VydmljZU9iamVjdC5zdXBwbGllck5hbWUNCiAgICAgIGRhdGEuY2xpZW50Q29udGFjdCA9IHRoaXMuZm9ybS5jbGllbnRDb250YWN0DQogICAgICAvLyDotKflkI0NCiAgICAgIGRhdGEuZ29vZHNOYW1lU3VtbWFyeSA9IHRoaXMuZm9ybS5nb29kc05hbWVTdW1tYXJ5DQogICAgICAvLyBzb+WPtw0KICAgICAgZGF0YS5zb05vID0gdGhpcy5mb3JtLnNvTm8NCiAgICAgIGRhdGEucGFja2FnZVF1YW50aXR5ID0gdGhpcy5mb3JtLnBhY2thZ2VRdWFudGl0eSArICIgUEFDS0FHRVMiDQogICAgICBkYXRhLmdyb3NzV2VpZ2h0ID0gdGhpcy5mb3JtLmdyb3NzV2VpZ2h0ICsgIiBLR1MiDQogICAgICBkYXRhLmdvb2RzVm9sdW1lID0gdGhpcy5mb3JtLmdvb2RzVm9sdW1lICsgIiBDQk0iDQogICAgICBkYXRhLnJldmVudWVUb24gPSB0aGlzLmZvcm0ucmV2ZW51ZVRvbg0KICAgICAgZGF0YS5jYXJyaWVyRW5OYW1lID0gdGhpcy5mb3JtLnNxZENhcnJpZXIgPyB0aGlzLmZvcm0uc3FkQ2FycmllciA6IHRoaXMuZm9ybS5jYXJyaWVyRW5OYW1lDQogICAgICBkYXRhLnBvbCA9IHRoaXMuZm9ybS5wb2wNCiAgICAgIGRhdGEucG9kID0gdGhpcy5mb3JtLmRlc3RpbmF0aW9uUG9ydA0KICAgICAgZGF0YS5wb2ROYW1lID0gdGhpcy5mb3JtLnBvZE5hbWUNCiAgICAgIGRhdGEucHJlY2FycmlhZ2VUaW1lID0gbW9tZW50KHNlcnZpY2VPYmplY3QucHJlY2FycmlhZ2VUaW1lKS5mb3JtYXQoInl5eXktTU0tREQgSEg6bW06c3MiKQ0KICAgICAgZGF0YS5wcmVjYXJyaWFnZUFkZHJlc3MgPSBzZXJ2aWNlT2JqZWN0LnByZWNhcnJpYWdlQWRkcmVzcw0KICAgICAgZGF0YS5wcmVjYXJyaWFnZUNvbnRhY3QgPSBzZXJ2aWNlT2JqZWN0LnByZWNhcnJpYWdlQ29udGFjdA0KICAgICAgZGF0YS5wcmVjYXJyaWFnZVJlbWFyayA9IHNlcnZpY2VPYmplY3QucHJlY2FycmlhZ2VSZW1hcmsNCiAgICAgIGRhdGEuYmFja0xvY2F0aW9uDQogICAgICBkYXRhLlJFRiA9IHRoaXMuZm9ybS5yY3RObw0KICAgICAgZGF0YS5zdWJ0b3RhbCA9IDANCiAgICAgIHNlcnZpY2VPYmplY3QucnNDaGFyZ2VMaXN0Lm1hcChpdGVtID0+IHsNCiAgICAgICAgaXRlbS5zdWJ0b3RhbCA/IGRhdGEuc3VidG90YWwgPSBjdXJyZW5jeShpdGVtLnN1YnRvdGFsKS5hZGQoZGF0YS5zdWJ0b3RhbCkudmFsdWUgOiBudWxsDQogICAgICB9KQ0KDQogICAgICAvLyDnlKjkuo7nu4Too4VwZGbmlofku7blkI0NCiAgICAgIGRhdGEucGRmTmFtZSA9ICJb5rS+6L2m5Y2VXSIgKyAiLSIgKyB0aGlzLmZvcm0ucmN0Tm8gKyAiLSIgKyB0aGlzLmZvcm0ucmV2ZW51ZVRvbiArICItIiArIHNlcnZpY2VPYmplY3QucnNTZXJ2aWNlSW5zdGFuY2VzLnN1cHBsaWVyU3VtbWFyeSArICItIiArIG1vbWVudCgpLmZvcm1hdCgiWVlZWU1NREQiKQ0KDQogICAgICBoaXByaW50VGVtcGxhdGUgPSBuZXcgaGlwcmludC5QcmludFRlbXBsYXRlKHt0ZW1wbGF0ZTogZGlzcGF0Y2hCaWxsfSkNCiAgICAgIC8vIOaJk+W8gOmihOiniOe7hOS7tg0KICAgICAgdGhpcy4kcmVmcy5wcmVWaWV3LnByaW50KGhpcHJpbnRUZW1wbGF0ZSwgZGF0YSkNCiAgICB9LA0KICAgIC8vIOaTjeS9nOWNleaJk+WNsA0KICAgIGdldE9wQmlsbCh0eXBlKSB7DQogICAgICBoaXByaW50VGVtcGxhdGUgPSBudWxsDQogICAgICBsZXQgZGF0YSA9IHt9DQogICAgICBkYXRhLnRpdGxlID0gdGhpcy5mb3JtLmxvZ2lzdGljc1R5cGVFbk5hbWUNCiAgICAgIGRhdGEuY2hhcmdlTGlzdCA9IHRoaXMuc2VsZWN0ZWRQcmludENoYXJnZXMNCiAgICAgIGRhdGEucmN0Tm8gPSB0aGlzLmZvcm0ucmN0Tm8NCiAgICAgIGRhdGEucmV2ZW51ZVRvbiA9IHRoaXMuZm9ybS5yZXZlbnVlVG9uDQogICAgICBkYXRhLnByaW50RGF0ZSA9IG1vbWVudCgpLmZvcm1hdCgiWVlZWS9NTS9ERCIpDQogICAgICBkYXRhLmNvbXBhbnkgPSB0aGlzLmZvcm0uY2xpZW50TmFtZS5zcGxpdCgiLyIpWzJdID09PSAibnVsbCIgfHwgdGhpcy5mb3JtLmNsaWVudE5hbWUuc3BsaXQoIi8iKVsyXSA9PT0gIiIgPyB0aGlzLmZvcm0uY2xpZW50TmFtZS5zcGxpdCgiLyIpWzFdIDogdGhpcy5mb3JtLmNsaWVudE5hbWUuc3BsaXQoIi8iKVsyXQ0KICAgICAgZGF0YS5jbGllbnRDb250YWN0ID0gdGhpcy5mb3JtLmNsaWVudENvbnRhY3QNCiAgICAgIGxldCBxb3V0YXRpb25Ta2V0Y2ggPSAiIg0KICAgICAgbGV0IHFvdXRhdGlvblNrZXRjaEFyciA9IHRoaXMuZm9ybS5xb3V0YXRpb25Ta2V0Y2ggPyB0aGlzLmZvcm0ucW91dGF0aW9uU2tldGNoLnNwbGl0KCJcbiIpIDogbnVsbA0KICAgICAgcW91dGF0aW9uU2tldGNoQXJyID8gcW91dGF0aW9uU2tldGNoQXJyLm1hcChpdGVtID0+IHsNCiAgICAgICAgcW91dGF0aW9uU2tldGNoICs9IGl0ZW0gKyAiPC9icj4iDQogICAgICB9KSA6IG51bGwNCiAgICAgIGRhdGEucW91dGF0aW9uU2tldGNoID0gcW91dGF0aW9uU2tldGNoDQogICAgICBkYXRhLmNvbnRyYWN0ID0gdGhpcy5mb3JtLmNvbnRyYWN0DQogICAgICBkYXRhLmNsaWVudENvbnRhY3RUZWwgPSB0aGlzLmZvcm0uY2xpZW50Q29udGFjdFRlbA0KICAgICAgZGF0YS5yZXZlbnVlVG9uID0gdGhpcy5mb3JtLnJldmVudWVUb24NCiAgICAgIGRhdGEubmV3Qm9va2luZ1JlbWFyayA9IHRoaXMuZm9ybS5uZXdCb29raW5nUmVtYXJrDQogICAgICBkYXRhLmNhcnJpZXJFbk5hbWUgPSB0aGlzLmZvcm0uY2FycmllckVuTmFtZQ0KICAgICAgZGF0YS5ncm9zc1dlaWdodCA9IHRoaXMuZm9ybS5ncm9zc1dlaWdodCArICJLR1MiDQogICAgICBkYXRhLnRyYWRpbmdUZXJtcyA9IHRoaXMuZ2V0VHJhZGluZ1Rlcm1zKHRoaXMuZm9ybS50cmFkaW5nVGVybXMpDQogICAgICBkYXRhLmdvb2RzTmFtZVN1bW1hcnkgPSB0aGlzLmZvcm0uZ29vZHNOYW1lU3VtbWFyeQ0KICAgICAgZGF0YS52ZXNzZWwgPSB0aGlzLmZvcm0uY3ZDbG9zaW5nVGltZSA/IHRoaXMuZm9ybS5jdkNsb3NpbmdUaW1lIDogIioqIg0KICAgICAgZGF0YS50cnVrID0gdGhpcy5mb3JtLnRydWsNCiAgICAgIGRhdGEucG9sID0gdGhpcy5mb3JtLnBvbA0KICAgICAgY29uc3QgaW5kZXggPSB0aGlzLmZvcm0ucG9sID8gdGhpcy5mb3JtLnBvbC5pbmRleE9mKCIoIikgOiAtMQ0KICAgICAgZGF0YS5wb2wxID0gaW5kZXggIT09IC0xID8gdGhpcy5mb3JtLnBvbC5zbGljZSgwLCBpbmRleCkgOiAiIg0KICAgICAgZGF0YS5wb2wyID0gaW5kZXggIT09IC0xID8gdGhpcy5mb3JtLnBvbC5zbGljZShpbmRleCkgOiAiIg0KICAgICAgZGF0YS5wb2QgPSB0aGlzLmZvcm0uZGVzdGluYXRpb25Qb3J0DQogICAgICBjb25zdCBpbmRleDIgPSB0aGlzLmZvcm0uZGVzdGluYXRpb25Qb3J0ID8gdGhpcy5mb3JtLmRlc3RpbmF0aW9uUG9ydC5pbmRleE9mKCIoIikgOiAtMQ0KICAgICAgZGF0YS5wb2QxID0gaW5kZXgyICE9PSAtMSA/IHRoaXMuZm9ybS5kZXN0aW5hdGlvblBvcnQuc2xpY2UoMCwgaW5kZXgyKSA6ICIiDQogICAgICBkYXRhLnBvZDIgPSBpbmRleDIgIT09IC0xID8gdGhpcy5mb3JtLmRlc3RpbmF0aW9uUG9ydC5zbGljZShpbmRleDIpIDogIiINCiAgICAgIGRhdGEucG9kTmFtZSA9IHRoaXMuZm9ybS5wb2ROYW1lDQogICAgICBjb25zdCBpbmRleDMgPSB0aGlzLmZvcm0ucG9kTmFtZSA/IHRoaXMuZm9ybS5wb2ROYW1lLmluZGV4T2YoIigiKSA6IC0xDQogICAgICBkYXRhLnBvZE5hbWUxID0gaW5kZXgzICE9PSAtMSA/IHRoaXMuZm9ybS5wb2ROYW1lLnNsaWNlKDAsIGluZGV4MykgOiAiIg0KICAgICAgZGF0YS5wb2ROYW1lMiA9IGluZGV4MyAhPT0gLTEgPyB0aGlzLmZvcm0ucG9kTmFtZS5zbGljZShpbmRleDMpIDogIiINCiAgICAgIGRhdGEuc29ObyA9IHRoaXMuZm9ybS5zb05vDQogICAgICAvL+aUvui0p+aWueW8jw0KICAgICAgZGF0YS5yZWxlYXNlVHlwZSA9IHRoaXMuZ2V0UmVsZWFzZVR5cGUodGhpcy5mb3JtLnJlbGVhc2VUeXBlKQ0KICAgICAgLy8g5Ye65Y2V5pa55byPDQogICAgICBkYXRhLk9SRyA9IHRoaXMuZm9ybS5ibEZvcm1Db2RlID09PSAiT1JHIiA/ICLiiJoiIDogIiINCiAgICAgIGRhdGEuVExYID0gdGhpcy5mb3JtLmJsRm9ybUNvZGUgPT09ICJUTFgiID8gIuKImiIgOiAiIg0KICAgICAgZGF0YS5TV0IgPSB0aGlzLmZvcm0uYmxGb3JtQ29kZSA9PT0gIlNXQiIgPyAi4oiaIiA6ICIiDQogICAgICBkYXRhLnNpZ24gPSB0aGlzLmdldE5hbWUodGhpcy5mb3JtLnNhbGVzSWQpDQogICAgICBkYXRhLnNpZ25EYXRlID0gdGhpcy5mb3JtLnJjdENyZWF0ZVRpbWUNCiAgICAgIGRhdGEub3JkZXJCZWxvbmdzVG8gPSB0aGlzLmZvcm0ub3JkZXJCZWxvbmdzVG8NCiAgICAgIGRhdGEucGFja2FnZVF1YW50aXR5ID0gdGhpcy5mb3JtLnBhY2thZ2VRdWFudGl0eQ0KICAgICAgZGF0YS5nb29kc1ZvbHVtZSA9IHRoaXMuZm9ybS5nb29kc1ZvbHVtZSArICJDQk0iDQogICAgICBkYXRhLmZpcnN0VmVzc2VsID0gdGhpcy5mb3JtLmZpcnN0VmVzc2VsDQogICAgICBkYXRhLnRyYW5zVHlwZQ0KICAgICAgZGF0YS50cmN1a0FkZHIgPSAiIg0KICAgICAgdGhpcy5mb3JtLnJzT3BDdG5yVHJ1Y2tMaXN0ID8gdGhpcy5mb3JtLnJzT3BDdG5yVHJ1Y2tMaXN0Lm1hcChpdGVtID0+IHsNCiAgICAgICAgaXRlbS5wcmVjYXJyaWFnZUFkZHJlc3MgPyBkYXRhLnRyY3VrQWRkciArPSAoaXRlbS5wcmVjYXJyaWFnZUFkZHJlc3MgKyAiPC9icj4iKSA6IG51bGwNCiAgICAgICAgcmV0dXJuIGl0ZW0NCiAgICAgIH0pIDogbnVsbA0KICAgICAgdGhpcy5mb3JtLnJzT3BCdWxrVHJ1Y2tMaXN0ID8gdGhpcy5mb3JtLnJzT3BCdWxrVHJ1Y2tMaXN0Lm1hcChpdGVtID0+IHsNCiAgICAgICAgaXRlbS5wcmVjYXJyaWFnZUFkZHJlc3MgPyBkYXRhLnRyY3VrQWRkciArPSAoaXRlbS5wcmVjYXJyaWFnZUFkZHJlc3MgKyAiPC9icj4iKSA6IG51bGwNCiAgICAgICAgcmV0dXJuIGl0ZW0NCiAgICAgIH0pIDogbnVsbA0KICAgICAgbGV0IHRvdGFsID0gMA0KICAgICAgbGV0IFVTRCA9IDANCiAgICAgIGxldCBSTUIgPSAwDQogICAgICB0aGlzLnNlbGVjdGVkUHJpbnRDaGFyZ2VzLm1hcChpdGVtID0+IHsNCiAgICAgICAgLy8gdG90YWwgPSBjdXJyZW5jeShjdXJyZW5jeShpdGVtLmRuVW5pdFJhdGUpLm11bHRpcGx5KGl0ZW0uZG5BbW91bnQpKS5hZGQodG90YWwpLnZhbHVlDQogICAgICAgIGlmIChpdGVtLmRuQ3VycmVuY3lDb2RlID09PSAiVVNEIikgew0KICAgICAgICAgIFVTRCA9IGN1cnJlbmN5KFVTRCkuYWRkKGN1cnJlbmN5KGl0ZW0uZG5Vbml0UmF0ZSkubXVsdGlwbHkoaXRlbS5kbkFtb3VudCkpLnZhbHVlDQogICAgICAgIH0NCiAgICAgICAgaWYgKGl0ZW0uZG5DdXJyZW5jeUNvZGUgPT09ICJSTUIiKSB7DQogICAgICAgICAgUk1CID0gY3VycmVuY3koUk1CKS5hZGQoY3VycmVuY3koaXRlbS5kblVuaXRSYXRlKS5tdWx0aXBseShpdGVtLmRuQW1vdW50KSkudmFsdWUNCiAgICAgICAgfQ0KICAgICAgfSkNCiAgICAgIGRhdGEudG90YWwgPSAoVVNEID8gKCJVU0Q6ICIgKyBjdXJyZW5jeShVU0QpLnZhbHVlICsgIiAiKSA6ICIgIikgKyAoUk1CID8gKCJSTUI6ICIgKyBjdXJyZW5jeShSTUIpLnZhbHVlKSA6ICIgIikNCg0KICAgICAgZGF0YS5wZGZOYW1lID0gIlvmk43kvZzljZVdIiArICItIiArIHRoaXMuZm9ybS5yY3RObyArIG1vbWVudCgpLmZvcm1hdCgiWVlZWS1NTS1ERCIpDQogICAgICBkYXRhLmJvb2tpbmdSZW1hcmsgPSB0aGlzLmZvcm0ubmV3Qm9va2luZ1JlbWFyaw0KICAgICAgZGF0YS5zZWNvbmRWZXNzZWwgPSB0aGlzLmZvcm0uc2Vjb25kVmVzc2VsID8gdGhpcy5mb3JtLnNlY29uZFZlc3NlbCA6ICIqKiINCiAgICAgIGRhdGEuaW5jbHVkZXMNCg0KICAgICAgZGF0YS50cmFkaW5nUGF5bWVudENoYW5uZWwgPSB0aGlzLmZvcm0uZnJlaWdodFBhaWRXYXlDb2RlDQogICAgICBkYXRhLmNsZWFyVHlwZSA9IHRoaXMuZm9ybS5zZXJ2aWNlVHlwZUlkcy5pbmNsdWRlcyg2MCkgPyAi5Y2V6K+B5oql5YWzIiA6ICLml6DljZXmiqXlhbMiDQogICAgICBkYXRhLnRyYWRpbmdUZXJtcyA9IHRoaXMuZm9ybS50cmFkaW5nVGVybXMNCiAgICAgIGRhdGEuc3VwcGxpZXJOYW1lID0gIioqIg0KICAgICAgaWYgKHRoaXMuZm9ybS5yc09wU2VhRmNsTGlzdCAmJiB0aGlzLmZvcm0ucnNPcFNlYUZjbExpc3RbMF0pIHsNCiAgICAgICAgZGF0YS5zdXBwbGllck5hbWUgPSB0aGlzLmZvcm0ucnNPcFNlYUZjbExpc3RbMF0uc3VwcGxpZXJOYW1lID8gdGhpcy5mb3JtLnJzT3BTZWFGY2xMaXN0WzBdLnN1cHBsaWVyTmFtZSA6ICIqKiINCiAgICAgIH0NCiAgICAgIGlmICh0aGlzLmZvcm0ucnNPcFNlYUxjbExpc3QgJiYgdGhpcy5mb3JtLnJzT3BTZWFMY2xMaXN0WzBdKSB7DQogICAgICAgIGRhdGEuc3VwcGxpZXJOYW1lID0gdGhpcy5mb3JtLnJzT3BTZWFMY2xMaXN0WzBdLnN1cHBsaWVyTmFtZSA/IHRoaXMuZm9ybS5yc09wU2VhTGNsTGlzdFswXS5zdXBwbGllck5hbWUgOiAiKioiDQogICAgICB9DQoNCiAgICAgIGRhdGEuY2FycmllckVuTmFtZSA9IHRoaXMuZm9ybS5jYXJyaWVyRW5OYW1lDQogICAgICAvLyDlupTmlLYNCiAgICAgIGRhdGEucmVjZWl2ZSA9ICIiDQogICAgICB0aGlzLnJzQ2xpZW50TWVzc2FnZS5yc0NoYXJnZUxpc3QgPyB0aGlzLnJzQ2xpZW50TWVzc2FnZS5yc0NoYXJnZUxpc3QubWFwKHYgPT4gew0KICAgICAgICBkYXRhLnJlY2VpdmUgPSBkYXRhLnJlY2VpdmUgKyB2LmNoYXJnZU5hbWUgKyAiLyIgKyB2LmRuVW5pdENvZGUgKyAiICAiICsgdi5kblVuaXRSYXRlICsgIi8iICsgdi5kbkN1cnJlbmN5Q29kZSArICI8L2JyPiINCiAgICAgIH0pIDogbnVsbA0KICAgICAgLy8g5bqU5LuYDQogICAgICBkYXRhLnBheSA9ICIiDQogICAgICB0aGlzLmZvcm0ucnNPcFNlYUZjbExpc3QgPyB0aGlzLmZvcm0ucnNPcFNlYUZjbExpc3QubWFwKGl0ZW0gPT4gew0KICAgICAgICBpdGVtLnJzQ2hhcmdlTGlzdCA/IGl0ZW0ucnNDaGFyZ2VMaXN0Lm1hcCh2ID0+IHsNCiAgICAgICAgICBkYXRhLnBheSA9IGRhdGEucGF5ICsgdi5jaGFyZ2VOYW1lICsgIi8iICsgdi5kblVuaXRDb2RlICsgIiAgIiArIHYuZG5Vbml0UmF0ZSArICIvIiArIHYuZG5DdXJyZW5jeUNvZGUgKyAiPC9icj4iDQogICAgICAgIH0pIDogbnVsbA0KICAgICAgfSkgOiBudWxsDQogICAgICB0aGlzLmZvcm0ucnNPcFNlYUxjbExpc3QgPyB0aGlzLmZvcm0ucnNPcFNlYUxjbExpc3QubWFwKGl0ZW0gPT4gew0KICAgICAgICBpdGVtLnJzQ2hhcmdlTGlzdCA/IGl0ZW0ucnNDaGFyZ2VMaXN0Lm1hcCh2ID0+IHsNCiAgICAgICAgICBkYXRhLnBheSA9IGRhdGEucGF5ICsgdi5jaGFyZ2VOYW1lICsgIi8iICsgdi5kblVuaXRDb2RlICsgIiAgIiArIHYuZG5Vbml0UmF0ZSArICIvIiArIHYuZG5DdXJyZW5jeUNvZGUgKyAiPC9icj4iDQogICAgICAgIH0pIDogbnVsbA0KICAgICAgfSkgOiBudWxsDQogICAgICB0aGlzLmZvcm0ucnNPcEFpckxpc3QgPyB0aGlzLmZvcm0ucnNPcEFpckxpc3QubWFwKGl0ZW0gPT4gew0KICAgICAgICBpdGVtLnJzQ2hhcmdlTGlzdCA/IGl0ZW0ucnNDaGFyZ2VMaXN0Lm1hcCh2ID0+IHsNCiAgICAgICAgICBkYXRhLnBheSA9IGRhdGEucGF5ICsgdi5jaGFyZ2VOYW1lICsgIi8iICsgdi5kblVuaXRDb2RlICsgIiAgIiArIHYuZG5Vbml0UmF0ZSArICIvIiArIHYuZG5DdXJyZW5jeUNvZGUgKyAiPC9icj4iDQogICAgICAgIH0pIDogbnVsbA0KICAgICAgfSkgOiBudWxsDQogICAgICB0aGlzLmZvcm0ucnNPcEZyZWVEZWNsYXJlTGlzdCA/IHRoaXMuZm9ybS5yc09wRnJlZURlY2xhcmVMaXN0Lm1hcChpdGVtID0+IHsNCiAgICAgICAgaXRlbS5yc0NoYXJnZUxpc3QgPyBpdGVtLnJzQ2hhcmdlTGlzdC5tYXAodiA9PiB7DQogICAgICAgICAgZGF0YS5wYXkgPSBkYXRhLnBheSArIHYuY2hhcmdlTmFtZSArICIvIiArIHYuZG5Vbml0Q29kZSArICIgICIgKyB2LmRuVW5pdFJhdGUgKyAiLyIgKyB2LmRuQ3VycmVuY3lDb2RlICsgIjwvYnI+Ig0KICAgICAgICB9KSA6IG51bGwNCiAgICAgIH0pIDogbnVsbA0KICAgICAgdGhpcy5mb3JtLnJzT3BEb2NEZWNsYXJlTGlzdCA/IHRoaXMuZm9ybS5yc09wRG9jRGVjbGFyZUxpc3QubWFwKGl0ZW0gPT4gew0KICAgICAgICBpdGVtLnJzQ2hhcmdlTGlzdCA/IGl0ZW0ucnNDaGFyZ2VMaXN0Lm1hcCh2ID0+IHsNCiAgICAgICAgICBkYXRhLnBheSA9IGRhdGEucGF5ICsgdi5jaGFyZ2VOYW1lICsgIi8iICsgdi5kblVuaXRDb2RlICsgIiAgIiArIHYuZG5Vbml0UmF0ZSArICIvIiArIHYuZG5DdXJyZW5jeUNvZGUgKyAiPC9icj4iDQogICAgICAgIH0pIDogbnVsbA0KICAgICAgfSkgOiBudWxsDQogICAgICB0aGlzLmZvcm0ucnNPcEJ1bGtUcnVja0xpc3QgPyB0aGlzLmZvcm0ucnNPcEJ1bGtUcnVja0xpc3QubWFwKGl0ZW0gPT4gew0KICAgICAgICBpdGVtLnJzQ2hhcmdlTGlzdCA/IGl0ZW0ucnNDaGFyZ2VMaXN0Lm1hcCh2ID0+IHsNCiAgICAgICAgICBkYXRhLnBheSA9IGRhdGEucGF5ICsgdi5jaGFyZ2VOYW1lICsgIi8iICsgdi5kblVuaXRDb2RlICsgIiAgIiArIHYuZG5Vbml0UmF0ZSArICIvIiArIHYuZG5DdXJyZW5jeUNvZGUgKyAiPC9icj4iDQogICAgICAgIH0pIDogbnVsbA0KICAgICAgfSkgOiBudWxsDQogICAgICB0aGlzLmZvcm0ucnNPcEN0bnJUcnVja0xpc3QgPyB0aGlzLmZvcm0ucnNPcEN0bnJUcnVja0xpc3QubWFwKGl0ZW0gPT4gew0KICAgICAgICBpdGVtLnJzQ2hhcmdlTGlzdCA/IGl0ZW0ucnNDaGFyZ2VMaXN0Lm1hcCh2ID0+IHsNCiAgICAgICAgICBkYXRhLnBheSA9IGRhdGEucGF5ICsgdi5jaGFyZ2VOYW1lICsgIi8iICsgdi5kblVuaXRDb2RlICsgIiAgIiArIHYuZG5Vbml0UmF0ZSArICIvIiArIHYuZG5DdXJyZW5jeUNvZGUgKyAiPC9icj4iDQogICAgICAgIH0pIDogbnVsbA0KICAgICAgfSkgOiBudWxsDQoNCiAgICAgIHRoaXMuZm9ybS5zZXJ2aWNlVHlwZUlkcyA/IHRoaXMuZm9ybS5zZXJ2aWNlVHlwZUlkcy5tYXAodiA9PiB7DQogICAgICAgIHRoaXMuZ2V0U2VydmljZU9iamVjdCh2KSA/IHRoaXMuZ2V0U2VydmljZU9iamVjdCh2KS5yc0NoYXJnZUxpc3QubWFwKHYgPT4gew0KICAgICAgICAgIGRhdGEucGF5ID0gZGF0YS5wYXkgKyB2LmNoYXJnZU5hbWUgKyAiLyIgKyB2LmRuVW5pdENvZGUgKyAiICAiICsgdi5kblVuaXRSYXRlICsgIi8iICsgdi5kbkN1cnJlbmN5Q29kZSArICI8L2JyPiINCiAgICAgICAgfSkgOiBudWxsDQogICAgICB9KSA6IG51bGwNCg0KICAgICAgaWYgKHR5cGUgPT09ICLmlbTmn5wiKSB7DQogICAgICAgIGhpcHJpbnRUZW1wbGF0ZSA9IG5ldyBoaXByaW50LlByaW50VGVtcGxhdGUoe3RlbXBsYXRlOiBGQ0xCaWxsfSkNCiAgICAgIH0gZWxzZSBpZiAodHlwZSA9PT0gIuaVo+i0pyIpIHsNCiAgICAgICAgaGlwcmludFRlbXBsYXRlID0gbmV3IGhpcHJpbnQuUHJpbnRUZW1wbGF0ZSh7dGVtcGxhdGU6IEZDTEJpbGx9KQ0KICAgICAgfSBlbHNlIGlmICh0eXBlID09PSAi56m66L+QIikgew0KICAgICAgICBoaXByaW50VGVtcGxhdGUgPSBuZXcgaGlwcmludC5QcmludFRlbXBsYXRlKHt0ZW1wbGF0ZTogQWlyQmlsbH0pDQogICAgICB9IGVsc2Ugew0KICAgICAgICBoaXByaW50VGVtcGxhdGUgPSBuZXcgaGlwcmludC5QcmludFRlbXBsYXRlKHt0ZW1wbGF0ZTogRkNMQmlsbH0pDQogICAgICB9DQoNCiAgICAgIC8vIOaJk+W8gOmihOiniOe7hOS7tg0KICAgICAgdGhpcy4kcmVmcy5wcmVWaWV3LnByaW50KGhpcHJpbnRUZW1wbGF0ZSwgZGF0YSkNCiAgICB9LA0KICAgIGdldFJlbGVhc2VUeXBlKGlkKSB7DQogICAgICBpZiAoaWQgPT0gMSkgcmV0dXJuICLmnIjnu5MiDQogICAgICBpZiAoaWQgPT0gMikgcmV0dXJuICLmirzmlL4iDQogICAgICBpZiAoaWQgPT0gMykgcmV0dXJuICLnpajnu5MiDQogICAgICBpZiAoaWQgPT0gNCkgcmV0dXJuICLnrb7mlL4iDQogICAgICBpZiAoaWQgPT0gNSkgcmV0dXJuICLorqLph5EiDQogICAgICBpZiAoaWQgPT0gNikgcmV0dXJuICLpooTku5giDQogICAgICBpZiAoaWQgPT0gNykgcmV0dXJuICLmiaPotKciDQogICAgICBpZiAoaWQgPT0gOSkgcmV0dXJuICLlsYXpl7QiDQogICAgICByZXR1cm4gIiINCiAgICB9LA0KICAgIGdldFRyYWRpbmdUZXJtcyhpZCkgew0KICAgICAgaWYgKGlkID09IDEpIHJldHVybiAiRVhXIg0KICAgICAgaWYgKGlkID09IDIpIHJldHVybiAiRkNBIg0KICAgICAgaWYgKGlkID09IDMpIHJldHVybiAiRk9CIg0KICAgICAgaWYgKGlkID09IDQpIHJldHVybiAiQ05GIg0KICAgICAgaWYgKGlkID09IDUpIHJldHVybiAiQ0lGIg0KICAgICAgaWYgKGlkID09IDYpIHJldHVybiAiRERVIg0KICAgICAgaWYgKGlkID09IDcpIHJldHVybiAiRERQIg0KICAgICAgcmV0dXJuICIiDQogICAgfSwNCiAgICAvLyDotLnnlKjmuIXljZXmiZPljbANCiAgICBnZXRDaGFyZ2VMaXN0QmlsbCh0eXBlKSB7DQogICAgICAvLyDlpoLmnpznlKjmiLfli77pgInkuobkuI3lkIznu5PnrpfljZXkvY3nmoTotLnnlKgs57uZ5Ye65o+Q6YaSDQogICAgICBsZXQgY29udGludWVQcmludCA9IHRydWUNCiAgICAgIHRoaXMuc2VsZWN0ZWRQcmludENoYXJnZXMubWFwKGl0ZW0gPT4gew0KICAgICAgICBpZiAoaXRlbS5jbGVhcmluZ0NvbXBhbnlJZCAhPT0gdGhpcy5zZWxlY3RlZFByaW50Q2hhcmdlc1swXS5jbGVhcmluZ0NvbXBhbnlJZCkgew0KICAgICAgICAgIGNvbnRpbnVlUHJpbnQgPSBmYWxzZQ0KICAgICAgICAgIHJldHVybg0KICAgICAgICB9DQogICAgICB9KQ0KICAgICAgaWYgKCFjb250aW51ZVByaW50KSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZygi6K+36YCJ5oup5ZCM5LiA57uT566X5Y2V5L2N55qE6LS555SoIikNCiAgICAgICAgcmV0dXJuDQogICAgICB9DQoNCiAgICAgIGhpcHJpbnRUZW1wbGF0ZSA9IG51bGwNCiAgICAgIGxldCBkYXRhID0ge30NCg0KICAgICAgbGV0IGNoYXJnZUxpc3QNCiAgICAgIGlmICh0aGlzLiRzdG9yZS5zdGF0ZS5kYXRhLmNoYXJnZUxpc3QubGVuZ3RoID09IDAgfHwgdGhpcy4kc3RvcmUuc3RhdGUuZGF0YS5yZWRpc0xpc3QuY2hhcmdlKSB7DQogICAgICAgIHN0b3JlLmRpc3BhdGNoKCJnZXRDaGFyZ2VMaXN0IikudGhlbigoKSA9PiB7DQogICAgICAgICAgY2hhcmdlTGlzdCA9IHRoaXMuJHN0b3JlLnN0YXRlLmRhdGEuY2hhcmdlTGlzdA0KICAgICAgICB9KQ0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgY2hhcmdlTGlzdCA9IHRoaXMuJHN0b3JlLnN0YXRlLmRhdGEuY2hhcmdlTGlzdA0KICAgICAgfQ0KDQogICAgICBkYXRhLnJjdE5vID0gdGhpcy5mb3JtLnJjdE5vDQogICAgICBkYXRhLmNvbnRhaW5lck5vID0gdGhpcy5mb3JtLnJzT3BTZWFGY2xMaXN0ID8gdGhpcy5mb3JtLnJzT3BTZWFGY2xMaXN0WzBdLnNxZENvbnRhaW5lcnNTZWFsc1N1bSA6ICh0aGlzLmZvcm0ucnNPcFNlYUxjbExpc3QgPyB0aGlzLmZvcm0ucnNPcFNlYUxjbExpc3RbMF0uc3FkQ29udGFpbmVyc1NlYWxzU3VtIDogbnVsbCkNCiAgICAgIGRhdGEucmV2ZW51ZVRvbiA9IHRoaXMuZm9ybS5yZXZlbnVlVG9uDQogICAgICBkYXRhLmNvbnRyYWN0Tm8gPSB0aGlzLmZvcm0uY2xpZW50Sm9iTm8NCiAgICAgIGRhdGEucHJpbnREYXRlID0gbW9tZW50KCkuZm9ybWF0KCJZWVlZL01NL0REIikNCiAgICAgIC8vIGRhdGEuY29tcGFueSA9IHRoaXMuZm9ybS5jdXN0b21UaXRsZSA/IHRoaXMuZm9ybS5jdXN0b21UaXRsZSA6IHRoaXMuZm9ybS5jbGllbnROYW1lLnNwbGl0KCIvIilbMl0gPT09ICJudWxsIiB8fCB0aGlzLmZvcm0uY2xpZW50TmFtZS5zcGxpdCgiLyIpWzJdID09PSAiIiA/IHRoaXMuZm9ybS5jbGllbnROYW1lLnNwbGl0KCIvIilbMV0gOiB0aGlzLmZvcm0uY2xpZW50TmFtZS5zcGxpdCgiLyIpWzJdDQogICAgICBkYXRhLmNvbXBhbnkgPSB0aGlzLmNvbXBhbnlMaXN0LmZpbHRlcihpdGVtID0+IGl0ZW0uY29tcGFueUlkID09PSB0aGlzLnNlbGVjdGVkUHJpbnRDaGFyZ2VzWzBdLmNsZWFyaW5nQ29tcGFueUlkKVswXS5jb21wYW55TG9jYWxOYW1lDQogICAgICBkYXRhLmNsaWVudENvbnRhY3QgPSB0aGlzLmZvcm0uY2xpZW50Q29udGFjdA0KICAgICAgZGF0YS5jYXJyaWVyRW5OYW1lID0gdGhpcy5mb3JtLmNhcnJpZXJFbk5hbWUNCiAgICAgIGRhdGEucG9sID0gdGhpcy5mb3JtLnBvbA0KICAgICAgZGF0YS5wb2QgPSB0aGlzLmZvcm0uZGVzdGluYXRpb25Qb3J0DQogICAgICBkYXRhLnNvTm8gPSAodGhpcy5mb3JtLnJzT3BBaXJMaXN0ICYmIHRoaXMuZm9ybS5yc09wQWlyTGlzdC5sZW5ndGggPiAwKSA/IHRoaXMuZm9ybS5yc09wQWlyTGlzdFswXS5zb05vIDogdGhpcy5mb3JtLnNvTm8NCiAgICAgIGRhdGEudHJhbnNUeXBlID0gdGhpcy5mb3JtLnNlcnZpY2VUeXBlSWRzLmluY2x1ZGVzKDEpID8gIkZDTCIgOiAiTENMIg0KDQogICAgICBkYXRhLmNoYXJnZUxpc3QgPSB0aGlzLnNlbGVjdGVkUHJpbnRDaGFyZ2VzLm1hcChpdGVtID0+IHsNCiAgICAgICAgcmV0dXJuIHsNCiAgICAgICAgICAuLi5pdGVtLA0KICAgICAgICAgIHN1YnRvdGFsOiAodHlwZSA9PT0gIkNOLeW5v+W3nuato+azvVtVU0QtPlJNQl0iIHx8IHR5cGUgPT09ICJDTi3lub/lt57nkZ7ml5dbVVNELT5STUJdIikgPyBjdXJyZW5jeShpdGVtLnN1YnRvdGFsKS5tdWx0aXBseShpdGVtLmJhc2ljQ3VycmVuY3lSYXRlKS52YWx1ZSA6ICgodHlwZSA9PT0gIkVOLSDnkZ7ml5fpppnmuK/otKbmiLdbSFNCQyBSTUItPlVTRF0iIHx8IHR5cGUgPT09ICJFTi3lub/lt57nkZ7ml5dbUk1CLT5VU0RdIikgPyBjdXJyZW5jeShpdGVtLnN1YnRvdGFsLCB7cHJlY2lzaW9uOiAyfSkuZGl2aWRlKGl0ZW0uYmFzaWNDdXJyZW5jeVJhdGUpLnZhbHVlIDogaXRlbS5zdWJ0b3RhbCksDQogICAgICAgICAgZHV0eVJhdGU6IGN1cnJlbmN5KGl0ZW0uZHV0eVJhdGUsIHtzeW1ib2w6ICIifSkuZm9ybWF0KCkgKyAiJSIsDQogICAgICAgICAgY2hhcmdlTmFtZTogKHR5cGUgPT09ICJFTi3lub/lt57nkZ7ml5dbUk1CLT5VU0RdIiB8fCB0eXBlID09PSAiRU4t5bm/5bee55Ge5peXW+aLm+ihjFVTRF0iIHx8IHR5cGUgPT09ICJFTi0g55Ge5peX6aaZ5riv6LSm5oi3W0hTQkMgUk1CLT5VU0RdIiB8fCB0eXBlID09PSAiRU4tIOmmmea4r+eRnuaXl1tIU0JDXSIpID8gY2hhcmdlTGlzdC5maWx0ZXIodiA9PiB2LmNoYXJnZUlkID09PSBpdGVtLmRuQ2hhcmdlTmFtZUlkKVswXS5jaGFyZ2VFbk5hbWUudG9VcHBlckNhc2UoKSA6IGl0ZW0uY2hhcmdlTmFtZQ0KICAgICAgICB9DQogICAgICB9KQ0KICAgICAgbGV0IHRvdGFsID0gMA0KICAgICAgbGV0IFVTRCA9IDANCiAgICAgIGxldCBSTUIgPSAwDQogICAgICAvKiBsZXQgZXhjaGFuZ2VSYXRlDQogICAgICBmb3IgKGNvbnN0IGEgb2YgdGhpcy4kc3RvcmUuc3RhdGUuZGF0YS5leGNoYW5nZVJhdGVMaXN0KSB7DQogICAgICAgIGlmIChhLmxvY2FsQ3VycmVuY3kgPT0gIlJNQiINCiAgICAgICAgICAmJiAiVVNEIiA9PSBhLm92ZXJzZWFDdXJyZW5jeQ0KICAgICAgICApIHsNCiAgICAgICAgICBleGNoYW5nZVJhdGUgPSBjdXJyZW5jeShhLnNlbGxSYXRlKS5kaXZpZGUoYS5iYXNlKS52YWx1ZQ0KICAgICAgICB9DQogICAgICB9ICovDQogICAgICBkYXRhLmNoYXJnZUxpc3QuZm9yRWFjaChpdGVtID0+IHsNCiAgICAgICAgaWYgKHR5cGUgPT09ICJFTi3lub/lt57nkZ7ml5dbUk1CLT5VU0RdIiB8fCB0eXBlID09PSAiRU4tIOeRnuaXl+mmmea4r+i0puaIt1tIU0JDIFJNQi0+VVNEXSIpIHsNCiAgICAgICAgICBpZiAoaXRlbS5kbkN1cnJlbmN5Q29kZSA9PT0gIlVTRCIpIHsNCiAgICAgICAgICAgIFVTRCA9IGN1cnJlbmN5KFVTRCkuYWRkKGl0ZW0uc3VidG90YWwpLnZhbHVlDQogICAgICAgICAgfQ0KICAgICAgICAgIGlmIChpdGVtLmRuQ3VycmVuY3lDb2RlID09PSAiUk1CIikgew0KICAgICAgICAgICAgVVNEID0gY3VycmVuY3koVVNEKS5hZGQoaXRlbS5zdWJ0b3RhbCkudmFsdWUNCiAgICAgICAgICB9DQogICAgICAgIH0gZWxzZSBpZiAodHlwZSA9PT0gIkNOLeW5v+W3nueRnuaXl1tVU0QtPlJNQl0iIHx8IHR5cGUgPT09ICJDTi3lub/lt57mraPms71bVVNELT5STUJdIikgew0KICAgICAgICAgIGlmIChpdGVtLmRuQ3VycmVuY3lDb2RlID09PSAiVVNEIikgew0KICAgICAgICAgICAgUk1CID0gY3VycmVuY3koUk1CKS5hZGQoaXRlbS5zdWJ0b3RhbCkudmFsdWUNCiAgICAgICAgICB9DQogICAgICAgICAgaWYgKGl0ZW0uZG5DdXJyZW5jeUNvZGUgPT09ICJSTUIiKSB7DQogICAgICAgICAgICBSTUIgPSBjdXJyZW5jeShSTUIpLmFkZChpdGVtLnN1YnRvdGFsKS52YWx1ZQ0KICAgICAgICAgIH0NCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAvLyB0b3RhbCA9IGN1cnJlbmN5KGN1cnJlbmN5KGl0ZW0uZG5Vbml0UmF0ZSkubXVsdGlwbHkoaXRlbS5kbkFtb3VudCkpLmFkZCh0b3RhbCkudmFsdWUNCiAgICAgICAgICBpZiAoaXRlbS5kbkN1cnJlbmN5Q29kZSA9PT0gIlVTRCIpIHsNCiAgICAgICAgICAgIFVTRCA9IGN1cnJlbmN5KFVTRCkuYWRkKGl0ZW0uc3VidG90YWwpLnZhbHVlDQogICAgICAgICAgfQ0KICAgICAgICAgIGlmIChpdGVtLmRuQ3VycmVuY3lDb2RlID09PSAiUk1CIikgew0KICAgICAgICAgICAgUk1CID0gY3VycmVuY3koUk1CKS5hZGQoaXRlbS5zdWJ0b3RhbCkudmFsdWUNCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgIH0pDQogICAgICBkYXRhLlVTRCA9IGN1cnJlbmN5KFVTRCkudmFsdWUNCiAgICAgIGRhdGEuUk1CID0gY3VycmVuY3koUk1CKS52YWx1ZQ0KICAgICAgLy8gZGF0YS50b3RhbCA9IChVU0QgPyAoIlVTRDogIiArIGN1cnJlbmN5KFVTRCkudmFsdWUgKyAiPC9icj4iKSA6ICIgIikgKyAoUk1CID8gKCJSTUI6ICIgKyBjdXJyZW5jeShSTUIpLnZhbHVlKSA6ICIgIikNCg0KICAgICAgZGF0YS5wZGZOYW1lID0gIlvotLnnlKjmuIXljZVdIiArICItIiArIHRoaXMuZm9ybS5yY3RObyArIG1vbWVudCgpLmZvcm1hdCgiWVlZWS1NTS1ERCIpDQoNCiAgICAgIGRhdGEucG9sID0gKHR5cGUgPT09ICJFTi0g55Ge5peX6aaZ5riv6LSm5oi3W0hTQkMgUk1CLT5VU0RdIiB8fCB0eXBlID09PSAiRU4t5bm/5bee55Ge5peXW+aLm+ihjFVTRF0iIHx8ICJFTi3lub/lt57nkZ7ml5dbUk1CLT5VU0RdIiA9PT0gdHlwZSB8fCB0eXBlID09PSAiRU4tIOmmmea4r+eRnuaXl1tIU0JDXSIpID8gcGlueWluLmdldEZ1bGxDaGFycyh0aGlzLmZvcm0ucG9sLnNsaWNlKDAsIHRoaXMuZm9ybS5wb2wuaW5kZXhPZigiKCIpKSkudG9VcHBlckNhc2UoKSA6IGRhdGEucG9sDQoNCiAgICAgIGlmICh0eXBlID09PSAiQ04t5bm/5bee55Ge5peXW+aLm+ihjFVTRCvlt6XooYxSTUJdIikgew0KICAgICAgICBoaXByaW50VGVtcGxhdGUgPSBuZXcgaGlwcmludC5QcmludFRlbXBsYXRlKHt0ZW1wbGF0ZTogZGViaXROb2RlfSkNCiAgICAgIH0gZWxzZSBpZiAodHlwZSA9PT0gIkNOLeW5v+W3nueRnuaXl1tVU0QtPlJNQl0iKSB7DQogICAgICAgIGhpcHJpbnRUZW1wbGF0ZSA9IG5ldyBoaXByaW50LlByaW50VGVtcGxhdGUoe3RlbXBsYXRlOiBkZWJpdE5vZGVVU0RUb1JNQn0pDQogICAgICB9IGVsc2UgaWYgKHR5cGUgPT09ICJFTi0g6aaZ5riv55Ge5peXW0hTQkNdIikgew0KICAgICAgICBoaXByaW50VGVtcGxhdGUgPSBuZXcgaGlwcmludC5QcmludFRlbXBsYXRlKHt0ZW1wbGF0ZTogZGViaXROb2RlRW59KQ0KICAgICAgfSBlbHNlIGlmICh0eXBlID09PSAiRU4t5bm/5bee55Ge5peXW+aLm+ihjFVTRF0iKSB7DQogICAgICAgIGhpcHJpbnRUZW1wbGF0ZSA9IG5ldyBoaXByaW50LlByaW50VGVtcGxhdGUoe3RlbXBsYXRlOiBkZWJpdE5vZGVaU1VTRH0pDQogICAgICB9IGVsc2UgaWYgKHR5cGUgPT09ICJDTi3lub/lt57mraPms71b5oub6KGMVVNEK1JNQl0iKSB7DQogICAgICAgIGhpcHJpbnRUZW1wbGF0ZSA9IG5ldyBoaXByaW50LlByaW50VGVtcGxhdGUoe3RlbXBsYXRlOiBkZWJpdE5vZGVDRkx9KQ0KICAgICAgfSBlbHNlIGlmICh0eXBlID09PSAiRU4t5bm/5bee55Ge5peXW1JNQi0+VVNEXSIpIHsNCiAgICAgICAgaGlwcmludFRlbXBsYXRlID0gbmV3IGhpcHJpbnQuUHJpbnRUZW1wbGF0ZSh7dGVtcGxhdGU6IGRlYml0Tm9kZVpTVVNEfSkNCiAgICAgIH0gZWxzZSBpZiAodHlwZSA9PT0gIkNOLeW5v+W3nuato+azvVtVU0QtPlJNQl0iKSB7DQogICAgICAgIGhpcHJpbnRUZW1wbGF0ZSA9IG5ldyBoaXByaW50LlByaW50VGVtcGxhdGUoe3RlbXBsYXRlOiBkZWJpdE5vZGVDRkxUb1JNQn0pDQogICAgICB9IGVsc2UgaWYgKHR5cGUgPT09ICJFTi0g55Ge5peX6aaZ5riv6LSm5oi3W0hTQkMgUk1CLT5VU0RdIikgew0KICAgICAgICBoaXByaW50VGVtcGxhdGUgPSBuZXcgaGlwcmludC5QcmludFRlbXBsYXRlKHt0ZW1wbGF0ZTogZGViaXROb2RlRW5IS1JNQlRvVVNEfSkNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIGhpcHJpbnRUZW1wbGF0ZSA9IG5ldyBoaXByaW50LlByaW50VGVtcGxhdGUoe3RlbXBsYXRlOiBkZWJpdE5vZGV9KQ0KICAgICAgfQ0KICAgICAgLy8g5omT5byA6aKE6KeI57uE5Lu2DQogICAgICB0aGlzLiRyZWZzLnByZVZpZXcucHJpbnQoaGlwcmludFRlbXBsYXRlLCBkYXRhKQ0KICAgIH0sDQogICAgLy8g6LS555So5riF5Y2V5omT5Y2wDQogICAgY2hlY2tSb2xlLA0KICAgIGluaXRQcmludCgpIHsNCiAgICAgIGhpcHJpbnQuaW5pdCh7DQogICAgICAgIHByb3ZpZGVyczogW25ldyBkZWZhdWx0RWxlbWVudFR5cGVQcm92aWRlcigpXQ0KICAgICAgfSkNCiAgICB9LA0KICAgIGdldFByb2Nlc3MoaWQpIHsNCiAgICAgIGlmIChpZCA9PSAxKSByZXR1cm4gIuetieW+hSINCiAgICAgIGlmIChpZCA9PSAyKSByZXR1cm4gIui/m+ihjCINCiAgICAgIGlmIChpZCA9PSAzKSByZXR1cm4gIuWPmOabtCINCiAgICAgIGlmIChpZCA9PSA0KSByZXR1cm4gIuW8guW4uCINCiAgICAgIGlmIChpZCA9PSA1KSByZXR1cm4gIui0qOaKvCINCiAgICAgIGlmIChpZCA9PSA2KSByZXR1cm4gIuehruiupCINCiAgICAgIGlmIChpZCA9PSA3KSByZXR1cm4gIuWujOaIkCINCiAgICAgIGlmIChpZCA9PSA4KSByZXR1cm4gIuWPlua2iCINCiAgICAgIGlmIChpZCA9PSA5KSByZXR1cm4gIumps+WbniINCiAgICB9LA0KICAgIGVkaXRSZXZlbnVlVG9uKCkgew0KICAgICAgaWYgKHRoaXMuZm9ybS5yZXZlbnVlVG9uICYmIHRoaXMuZm9ybS5yZXZlbnVlVG9uLnNwbGl0KCIrIikubGVuZ3RoID4gMCkgew0KICAgICAgICB0aGlzLmZvcm0ucmV2ZW51ZVRvbi5zcGxpdCgiKyIpLm1hcCgocmV2ZW51ZVRvbiwgaSkgPT4gew0KICAgICAgICAgIGlmIChyZXZlbnVlVG9uLnNwbGl0KCJ4IikubGVuZ3RoID4gMCAmJiBpID09PSAwKSB7DQogICAgICAgICAgICB0aGlzLmZvcm0uY291bnRBID0gTnVtYmVyKHJldmVudWVUb24uc3BsaXQoIngiKVswXSkNCiAgICAgICAgICAgIHRoaXMuZm9ybS51bml0Q29kZUEgPSByZXZlbnVlVG9uLnNwbGl0KCJ4IilbMV0NCiAgICAgICAgICB9DQogICAgICAgICAgaWYgKHJldmVudWVUb24uc3BsaXQoIngiKS5sZW5ndGggPiAwICYmIGkgPT09IDEpIHsNCiAgICAgICAgICAgIHRoaXMuZm9ybS5jb3VudEIgPSBOdW1iZXIocmV2ZW51ZVRvbi5zcGxpdCgieCIpWzBdKQ0KICAgICAgICAgICAgdGhpcy5mb3JtLnVuaXRDb2RlQiA9IHJldmVudWVUb24uc3BsaXQoIngiKVsxXQ0KICAgICAgICAgIH0NCiAgICAgICAgICBpZiAocmV2ZW51ZVRvbi5zcGxpdCgieCIpLmxlbmd0aCA+IDAgJiYgaSA9PT0gMikgew0KICAgICAgICAgICAgdGhpcy5mb3JtLmNvdW50QyA9IE51bWJlcihyZXZlbnVlVG9uLnNwbGl0KCJ4IilbMF0pDQogICAgICAgICAgICB0aGlzLmZvcm0udW5pdENvZGVDID0gcmV2ZW51ZVRvbi5zcGxpdCgieCIpWzFdDQogICAgICAgICAgfQ0KICAgICAgICB9KQ0KICAgICAgfQ0KICAgICAgdGhpcy5vcGVuR2VuZXJhdGVSZXZlbnVlVG9ucyA9IHRydWUNCiAgICB9LA0KICAgIHJldmVudWVUb25Db25maXJtKCkgew0KICAgICAgbGV0IHJldmVudWVTdHJpbmcgPSBbXQ0KICAgICAgaWYgKHRoaXMuZm9ybS51bml0Q29kZUEpIHsNCiAgICAgICAgdGhpcy5mb3JtLmNvdW50QSA/IHJldmVudWVTdHJpbmcucHVzaCh0aGlzLmZvcm0uY291bnRBICsgIngiICsgdGhpcy5mb3JtLnVuaXRDb2RlQSkgOiByZXZlbnVlU3RyaW5nLnB1c2goMSArICJ4IiArIHRoaXMuZm9ybS51bml0Q29kZUEpDQogICAgICB9DQogICAgICBpZiAodGhpcy5mb3JtLnVuaXRDb2RlQiAmJiB0aGlzLmZvcm0uY291bnRCICE9IG51bGwpIHsNCiAgICAgICAgdGhpcy5mb3JtLmNvdW50QiA/IHJldmVudWVTdHJpbmcucHVzaCh0aGlzLmZvcm0uY291bnRCICsgIngiICsgdGhpcy5mb3JtLnVuaXRDb2RlQikgOiByZXZlbnVlU3RyaW5nLnB1c2goMSArICJ4IiArIHRoaXMuZm9ybS51bml0Q29kZUIpDQogICAgICB9DQogICAgICBpZiAodGhpcy5mb3JtLnVuaXRDb2RlQykgew0KICAgICAgICB0aGlzLmZvcm0uY291bnRDID8gcmV2ZW51ZVN0cmluZy5wdXNoKHRoaXMuZm9ybS5jb3VudEMgKyAieCIgKyB0aGlzLmZvcm0udW5pdENvZGVDKSA6IHJldmVudWVTdHJpbmcucHVzaCgxICsgIngiICsgdGhpcy5mb3JtLnVuaXRDb2RlQykNCiAgICAgIH0NCiAgICAgIHRoaXMuZm9ybS5yZXZlbnVlVG9uID0gcmV2ZW51ZVN0cmluZy5qb2luKCIrIikNCiAgICAgIHRoaXMuZm9ybS51bml0Q29kZUEgPSBudWxsDQogICAgICB0aGlzLmZvcm0udW5pdENvZGVCID0gbnVsbA0KICAgICAgdGhpcy5mb3JtLnVuaXRDb2RlQyA9IG51bGwNCiAgICAgIHRoaXMuZm9ybS5jb251dEEgPSBudWxsDQogICAgICB0aGlzLmZvcm0uY29udXRCID0gbnVsbA0KICAgICAgdGhpcy5mb3JtLmNvbnV0QyA9IG51bGwNCiAgICAgIHRoaXMub3BlbkdlbmVyYXRlUmV2ZW51ZVRvbnMgPSBmYWxzZQ0KICAgIH0sDQogICAgY2hhbmdlU2VydmljZU9iamVjdChzZXJ2aWNlVHlwZUlkLCBzZXJ2aWNlT2JqZWN0KSB7DQogICAgICBpZiAoc2VydmljZVR5cGVJZCA9PT0gMSkgew0KICAgICAgICB0aGlzLnJzT3BTZWFGY2wgPSBPYmplY3QuYXNzaWduKHt9LCBzZXJ2aWNlT2JqZWN0LCB0aGlzLnJzT3BTZWFGY2wgfHwge30pDQogICAgICB9DQogICAgICBpZiAoc2VydmljZVR5cGVJZCA9PT0gMikgew0KICAgICAgICB0aGlzLnJzT3BTZWFMY2wgPSBPYmplY3QuYXNzaWduKHt9LCBzZXJ2aWNlT2JqZWN0LCB0aGlzLnJzT3BTZWFMY2wgfHwge30pDQogICAgICB9DQogICAgICBpZiAoc2VydmljZVR5cGVJZCA9PT0gMTApIHsNCiAgICAgICAgdGhpcy5yc09wQWlyID0gT2JqZWN0LmFzc2lnbih7fSwgc2VydmljZU9iamVjdCwgdGhpcy5yc09wQWlyIHx8IHt9KQ0KICAgICAgfQ0KICAgICAgaWYgKHNlcnZpY2VUeXBlSWQgPT09IDIwKSB7DQogICAgICAgIHRoaXMucnNPcFJhaWxGQ0wgPSBPYmplY3QuYXNzaWduKHt9LCBzZXJ2aWNlT2JqZWN0LCB0aGlzLnJzT3BSYWlsRkNMIHx8IHt9KQ0KICAgICAgfQ0KICAgICAgaWYgKHNlcnZpY2VUeXBlSWQgPT09IDIxKSB7DQogICAgICAgIHRoaXMucnNPcFJhaWxMQ0wgPSBPYmplY3QuYXNzaWduKHt9LCBzZXJ2aWNlT2JqZWN0LCB0aGlzLnJzT3BSYWlsTENMIHx8IHt9KQ0KICAgICAgfQ0KICAgICAgaWYgKHNlcnZpY2VUeXBlSWQgPT09IDQwKSB7DQogICAgICAgIHRoaXMucnNPcEV4cHJlc3MgPSBPYmplY3QuYXNzaWduKHt9LCBzZXJ2aWNlT2JqZWN0LCB0aGlzLnJzT3BFeHByZXNzIHx8IHt9KQ0KICAgICAgfQ0KICAgICAgaWYgKHNlcnZpY2VUeXBlSWQgPT09IDUwKSB7DQogICAgICAgIHRoaXMucnNPcEN0bnJUcnVjayA9IE9iamVjdC5hc3NpZ24oe30sIHNlcnZpY2VPYmplY3QsIHRoaXMucnNPcEN0bnJUcnVjayB8fCB7fSkNCiAgICAgIH0NCiAgICAgIGlmIChzZXJ2aWNlVHlwZUlkID09PSA1MSkgew0KICAgICAgICB0aGlzLnJzT3BCdWxrVHJ1Y2sgPSBPYmplY3QuYXNzaWduKHt9LCBzZXJ2aWNlT2JqZWN0LCB0aGlzLnJzT3BCdWxrVHJ1Y2sgfHwge30pDQogICAgICB9DQogICAgICBpZiAoc2VydmljZVR5cGVJZCA9PT0gNjApIHsNCiAgICAgICAgdGhpcy5yc09wRG9jRGVjbGFyZSA9IE9iamVjdC5hc3NpZ24oe30sIHNlcnZpY2VPYmplY3QsIHRoaXMucnNPcERvY0RlY2xhcmUgfHwge30pDQogICAgICB9DQogICAgICBpZiAoc2VydmljZVR5cGVJZCA9PT0gNjEpIHsNCiAgICAgICAgdGhpcy5yc09wRnJlZURlY2xhcmUgPSBPYmplY3QuYXNzaWduKHt9LCBzZXJ2aWNlT2JqZWN0LCB0aGlzLnJzT3BGcmVlRGVjbGFyZSB8fCB7fSkNCiAgICAgIH0NCiAgICAgIGlmIChzZXJ2aWNlVHlwZUlkID09PSA3MCkgew0KICAgICAgICB0aGlzLnJzT3BET0FnZW50ID0gT2JqZWN0LmFzc2lnbih7fSwgc2VydmljZU9iamVjdCwgdGhpcy5yc09wRE9BZ2VudCB8fCB7fSkNCiAgICAgIH0NCiAgICAgIGlmIChzZXJ2aWNlVHlwZUlkID09PSA3MSkgew0KICAgICAgICB0aGlzLnJzT3BDbGVhckFnZW50ID0gT2JqZWN0LmFzc2lnbih7fSwgc2VydmljZU9iamVjdCwgdGhpcy5yc09wQ2xlYXJBZ2VudCB8fCB7fSkNCiAgICAgIH0NCiAgICAgIGlmIChzZXJ2aWNlVHlwZUlkID09PSA4MCkgew0KICAgICAgICB0aGlzLnJzT3BXSFMgPSBPYmplY3QuYXNzaWduKHt9LCBzZXJ2aWNlT2JqZWN0LCB0aGlzLnJzT3BXSFMgfHwge30pDQogICAgICB9DQogICAgICBpZiAoc2VydmljZVR5cGVJZCA9PT0gOTApIHsNCiAgICAgICAgdGhpcy5yc09wM3JkQ2VydCA9IE9iamVjdC5hc3NpZ24oe30sIHNlcnZpY2VPYmplY3QsIHRoaXMucnNPcDNyZENlcnQgfHwge30pDQogICAgICB9DQogICAgICBpZiAoc2VydmljZVR5cGVJZCA9PT0gMTAwKSB7DQogICAgICAgIHRoaXMucnNPcElOUyA9IE9iamVjdC5hc3NpZ24oe30sIHNlcnZpY2VPYmplY3QsIHRoaXMucnNPcElOUyB8fCB7fSkNCiAgICAgIH0NCiAgICAgIGlmIChzZXJ2aWNlVHlwZUlkID09PSAxMDEpIHsNCiAgICAgICAgdGhpcy5yc09wVHJhZGluZyA9IE9iamVjdC5hc3NpZ24oe30sIHNlcnZpY2VPYmplY3QsIHRoaXMucnNPcFRyYWRpbmcgfHwge30pDQogICAgICB9DQogICAgICBpZiAoc2VydmljZVR5cGVJZCA9PT0gMTAyKSB7DQogICAgICAgIHRoaXMucnNPcEZ1bWlnYXRpb24gPSBPYmplY3QuYXNzaWduKHt9LCBzZXJ2aWNlT2JqZWN0LCB0aGlzLnJzT3BGdW1pZ2F0aW9uIHx8IHt9KQ0KICAgICAgfQ0KICAgICAgaWYgKHNlcnZpY2VUeXBlSWQgPT09IDEwMykgew0KICAgICAgICB0aGlzLnJzT3BDTyA9IE9iamVjdC5hc3NpZ24oe30sIHNlcnZpY2VPYmplY3QsIHRoaXMucnNPcENPIHx8IHt9KQ0KICAgICAgfQ0KICAgICAgaWYgKHNlcnZpY2VUeXBlSWQgPT09IDEwNCkgew0KICAgICAgICB0aGlzLnJzT3BPdGhlciA9IE9iamVjdC5hc3NpZ24oe30sIHNlcnZpY2VPYmplY3QsIHRoaXMucnNPcE90aGVyIHx8IHt9KQ0KICAgICAgfQ0KICAgIH0sDQogICAgZ2V0Rm9ybURpc2FibGUoc2VydmljZVR5cGVJZCkgew0KICAgICAgaWYgKHNlcnZpY2VUeXBlSWQgPT09IDEpIHsNCiAgICAgICAgcmV0dXJuIHRoaXMucnNPcFNlYWxGY2xGb3JtRGlzYWJsZQ0KICAgICAgfQ0KICAgICAgaWYgKHNlcnZpY2VUeXBlSWQgPT09IDIpIHsNCiAgICAgICAgcmV0dXJuIHRoaXMucnNPcFNlYWxMY2xGb3JtRGlzYWJsZQ0KICAgICAgfQ0KICAgICAgaWYgKHNlcnZpY2VUeXBlSWQgPT09IDEwKSB7DQogICAgICAgIHJldHVybiB0aGlzLnJzT3BBaXJGb3JtRGlzYWJsZQ0KICAgICAgfQ0KICAgICAgaWYgKHNlcnZpY2VUeXBlSWQgPT09IDIwKSB7DQogICAgICAgIHJldHVybiB0aGlzLnJzT3BSYWlsRmNsRm9ybURpc2FibGUNCiAgICAgIH0NCiAgICAgIGlmIChzZXJ2aWNlVHlwZUlkID09PSAyMSkgew0KICAgICAgICByZXR1cm4gdGhpcy5yc09wUmFpbExjbEZvcm1EaXNhYmxlDQogICAgICB9DQogICAgICBpZiAoc2VydmljZVR5cGVJZCA9PT0gNDApIHsNCiAgICAgICAgcmV0dXJuIHRoaXMucnNPcEV4cHJlc3NGb3JtRGlzYWJsZQ0KICAgICAgfQ0KICAgICAgaWYgKHNlcnZpY2VUeXBlSWQgPT09IDUwKSB7DQogICAgICAgIHJldHVybiB0aGlzLnJzT3BDdG5yVHJ1Y2tGb3JtRGlzYWJsZQ0KICAgICAgfQ0KICAgICAgaWYgKHNlcnZpY2VUeXBlSWQgPT09IDUxKSB7DQogICAgICAgIHJldHVybiB0aGlzLnJzT3BCdWxrVHJ1Y2tGb3JtRGlzYWJsZQ0KICAgICAgfQ0KICAgICAgaWYgKHNlcnZpY2VUeXBlSWQgPT09IDYwKSB7DQogICAgICAgIHJldHVybiB0aGlzLnJzT3BEb2NEZWNsYXJlRm9ybURpc2FibGUNCiAgICAgIH0NCiAgICAgIGlmIChzZXJ2aWNlVHlwZUlkID09PSA2MSkgew0KICAgICAgICByZXR1cm4gdGhpcy5yc09wRnJlZURlY2xhcmVGb3JtRGlzYWJsZQ0KICAgICAgfQ0KICAgICAgaWYgKHNlcnZpY2VUeXBlSWQgPT09IDcwKSB7DQogICAgICAgIHJldHVybiB0aGlzLnJzT3BET0FnZW50Rm9ybURpc2FibGUNCiAgICAgIH0NCiAgICAgIGlmIChzZXJ2aWNlVHlwZUlkID09PSA3MSkgew0KICAgICAgICByZXR1cm4gdGhpcy5yc09wQ2xlYXJBZ2VudEZvcm1EaXNhYmxlDQogICAgICB9DQogICAgICBpZiAoc2VydmljZVR5cGVJZCA9PT0gODApIHsNCiAgICAgICAgcmV0dXJuIHRoaXMucnNPcFdIU0Zvcm1EaXNhYmxlDQogICAgICB9DQogICAgICBpZiAoc2VydmljZVR5cGVJZCA9PT0gOTApIHsNCiAgICAgICAgcmV0dXJuIHRoaXMucnNPcDNyZENlcnRGb3JtRGlzYWJsZQ0KICAgICAgfQ0KICAgICAgaWYgKHNlcnZpY2VUeXBlSWQgPT09IDEwMCkgew0KICAgICAgICByZXR1cm4gdGhpcy5yc09wSU5TRm9ybURpc2FibGUNCiAgICAgIH0NCiAgICAgIGlmIChzZXJ2aWNlVHlwZUlkID09PSAxMDEpIHsNCiAgICAgICAgcmV0dXJuIHRoaXMucnNPcFRyYWRpbmdGb3JtRGlzYWJsZQ0KICAgICAgfQ0KICAgICAgaWYgKHNlcnZpY2VUeXBlSWQgPT09IDEwMykgew0KICAgICAgICByZXR1cm4gdGhpcy5yc09wQ09Gb3JtRGlzYWJsZQ0KICAgICAgfQ0KICAgIH0sDQogICAgY2hhbmdlU2VydmljZUZvbGQoc2VydmljZUluc3RhbmNlKSB7DQogICAgICBzZXJ2aWNlSW5zdGFuY2Uuc2VydmljZUZvbGQgPSAhc2VydmljZUluc3RhbmNlLnNlcnZpY2VGb2xkDQogICAgfSwNCiAgICBjaGFuZ2VGb2xkKHNlcnZpY2VUeXBlSWQpIHsNCiAgICAgIGlmIChzZXJ2aWNlVHlwZUlkID09PSAxKSB7DQogICAgICAgIHRoaXMucnNPcFNlYWxGY2xGb2xkID0gIXRoaXMucnNPcFNlYWxGY2xGb2xkDQogICAgICB9DQogICAgICBpZiAoc2VydmljZVR5cGVJZCA9PT0gMikgew0KICAgICAgICB0aGlzLnJzT3BTZWFsTGNsRm9sZCA9ICF0aGlzLnJzT3BTZWFsTGNsRm9sZA0KICAgICAgfQ0KICAgICAgaWYgKHNlcnZpY2VUeXBlSWQgPT09IDEwKSB7DQogICAgICAgIHRoaXMucnNPcEFpckZvbGQgPSAhdGhpcy5yc09wQWlyRm9sZA0KICAgICAgfQ0KICAgICAgaWYgKHNlcnZpY2VUeXBlSWQgPT09IDIwKSB7DQogICAgICAgIHRoaXMucnNPcFJhaWxGY2xGb2xkID0gIXRoaXMucnNPcFJhaWxGY2xGb2xkDQogICAgICB9DQogICAgICBpZiAoc2VydmljZVR5cGVJZCA9PT0gMjEpIHsNCiAgICAgICAgdGhpcy5yc09wUmFpbExjbEZvbGQgPSAhdGhpcy5yc09wUmFpbExjbEZvbGQNCiAgICAgIH0NCiAgICAgIGlmIChzZXJ2aWNlVHlwZUlkID09PSA0MCkgew0KICAgICAgICB0aGlzLnJzT3BFeHByZXNzRm9sZCA9ICF0aGlzLnJzT3BFeHByZXNzRm9sZA0KICAgICAgfQ0KICAgICAgaWYgKHNlcnZpY2VUeXBlSWQgPT09IDUwKSB7DQogICAgICAgIHRoaXMucnNPcEN0bnJUcnVja0ZvbGQgPSAhdGhpcy5yc09wQ3RuclRydWNrRm9sZA0KICAgICAgfQ0KICAgICAgaWYgKHNlcnZpY2VUeXBlSWQgPT09IDUxKSB7DQogICAgICAgIHRoaXMucnNPcEJ1bGtUcnVja0ZvbGQgPSAhdGhpcy5yc09wQnVsa1RydWNrRm9sZA0KICAgICAgfQ0KICAgICAgaWYgKHNlcnZpY2VUeXBlSWQgPT09IDYwKSB7DQogICAgICAgIHRoaXMucnNPcERvY0RlY2xhcmVGb2xkID0gIXRoaXMucnNPcERvY0RlY2xhcmVGb2xkDQogICAgICB9DQogICAgICBpZiAoc2VydmljZVR5cGVJZCA9PT0gNjEpIHsNCiAgICAgICAgdGhpcy5yc09wRnJlZURlY2xhcmVGb2xkID0gIXRoaXMucnNPcEZyZWVEZWNsYXJlRm9sZA0KICAgICAgfQ0KICAgICAgaWYgKHNlcnZpY2VUeXBlSWQgPT09IDcwKSB7DQogICAgICAgIHRoaXMucnNPcERPQWdlbnRGb2xkID0gIXRoaXMucnNPcERPQWdlbnRGb2xkDQogICAgICB9DQogICAgICBpZiAoc2VydmljZVR5cGVJZCA9PT0gNzEpIHsNCiAgICAgICAgdGhpcy5yc09wQ2xlYXJBZ2VudEZvbGQgPSAhdGhpcy5yc09wQ2xlYXJBZ2VudEZvbGQNCiAgICAgIH0NCiAgICAgIGlmIChzZXJ2aWNlVHlwZUlkID09PSA4MCkgew0KICAgICAgICB0aGlzLnJzT3BXSFNGb2xkID0gIXRoaXMucnNPcFdIU0ZvbGQNCiAgICAgIH0NCiAgICAgIGlmIChzZXJ2aWNlVHlwZUlkID09PSA5MCkgew0KICAgICAgICB0aGlzLnJzT3AzcmRDZXJ0Rm9sZCA9ICF0aGlzLnJzT3AzcmRDZXJ0Rm9sZA0KICAgICAgfQ0KICAgICAgaWYgKHNlcnZpY2VUeXBlSWQgPT09IDEwMCkgew0KICAgICAgICB0aGlzLnJzT3BJTlNGb2xkID0gIXRoaXMucnNPcElOU0ZvbGQNCiAgICAgIH0NCiAgICAgIGlmIChzZXJ2aWNlVHlwZUlkID09PSAxMDEpIHsNCiAgICAgICAgdGhpcy5yc09wVHJhZGluZ0ZvbGQgPSAhdGhpcy5yc09wVHJhZGluZ0ZvbGQNCiAgICAgIH0NCiAgICAgIGlmIChzZXJ2aWNlVHlwZUlkID09PSAxMDIpIHsNCiAgICAgICAgdGhpcy5yc09wRnVtaWdhdGlvbkZvbGQgPSAhdGhpcy5yc09wRnVtaWdhdGlvbkZvbGQNCiAgICAgIH0NCiAgICAgIGlmIChzZXJ2aWNlVHlwZUlkID09PSAxMDMpIHsNCiAgICAgICAgdGhpcy5yc09wQ09Gb2xkID0gIXRoaXMucnNPcENPRm9sZA0KICAgICAgfQ0KICAgICAgaWYgKHNlcnZpY2VUeXBlSWQgPT09IDEwNCkgew0KICAgICAgICB0aGlzLnJzT3BPdGhlckZvbGQgPSAhdGhpcy5yc09wT3RoZXJGb2xkDQogICAgICB9DQogICAgfSwNCiAgICBnZXRGb2xkKHNlcnZpY2VUeXBlSWQpIHsNCiAgICAgIGlmIChzZXJ2aWNlVHlwZUlkID09PSAxKSB7DQogICAgICAgIHJldHVybiB0aGlzLnJzT3BTZWFsRmNsRm9sZA0KICAgICAgfQ0KICAgICAgaWYgKHNlcnZpY2VUeXBlSWQgPT09IDIpIHsNCiAgICAgICAgcmV0dXJuIHRoaXMucnNPcFNlYWxMY2xGb2xkDQogICAgICB9DQogICAgICBpZiAoc2VydmljZVR5cGVJZCA9PT0gMTApIHsNCiAgICAgICAgcmV0dXJuIHRoaXMucnNPcEFpckZvbGQNCiAgICAgIH0NCiAgICAgIGlmIChzZXJ2aWNlVHlwZUlkID09PSAyMCkgew0KICAgICAgICByZXR1cm4gdGhpcy5yc09wUmFpbEZjbEZvbGQNCiAgICAgIH0NCiAgICAgIGlmIChzZXJ2aWNlVHlwZUlkID09PSAyMSkgew0KICAgICAgICByZXR1cm4gdGhpcy5yc09wUmFpbExjbEZvbGQNCiAgICAgIH0NCiAgICAgIGlmIChzZXJ2aWNlVHlwZUlkID09PSA0MCkgew0KICAgICAgICByZXR1cm4gdGhpcy5yc09wRXhwcmVzc0ZvbGQNCiAgICAgIH0NCiAgICAgIGlmIChzZXJ2aWNlVHlwZUlkID09PSA1MCkgew0KICAgICAgICByZXR1cm4gdGhpcy5yc09wQ3RuclRydWNrRm9sZA0KICAgICAgfQ0KICAgICAgaWYgKHNlcnZpY2VUeXBlSWQgPT09IDUxKSB7DQogICAgICAgIHJldHVybiB0aGlzLnJzT3BCdWxrVHJ1Y2tGb2xkDQogICAgICB9DQogICAgICBpZiAoc2VydmljZVR5cGVJZCA9PT0gNjApIHsNCiAgICAgICAgcmV0dXJuIHRoaXMucnNPcERvY0RlY2xhcmVGb2xkDQogICAgICB9DQogICAgICBpZiAoc2VydmljZVR5cGVJZCA9PT0gNjEpIHsNCiAgICAgICAgcmV0dXJuIHRoaXMucnNPcEZyZWVEZWNsYXJlRm9sZA0KICAgICAgfQ0KICAgICAgaWYgKHNlcnZpY2VUeXBlSWQgPT09IDcwKSB7DQogICAgICAgIHJldHVybiB0aGlzLnJzT3BET0FnZW50Rm9sZA0KICAgICAgfQ0KICAgICAgaWYgKHNlcnZpY2VUeXBlSWQgPT09IDcxKSB7DQogICAgICAgIHJldHVybiB0aGlzLnJzT3BDbGVhckFnZW50Rm9sZA0KICAgICAgfQ0KICAgICAgaWYgKHNlcnZpY2VUeXBlSWQgPT09IDgwKSB7DQogICAgICAgIHJldHVybiB0aGlzLnJzT3BXSFNGb2xkDQogICAgICB9DQogICAgICBpZiAoc2VydmljZVR5cGVJZCA9PT0gOTApIHsNCiAgICAgICAgcmV0dXJuIHRoaXMucnNPcDNyZENlcnRGb2xkDQogICAgICB9DQogICAgICBpZiAoc2VydmljZVR5cGVJZCA9PT0gMTAwKSB7DQogICAgICAgIHJldHVybiB0aGlzLnJzT3BJTlNGb2xkDQogICAgICB9DQogICAgICBpZiAoc2VydmljZVR5cGVJZCA9PT0gMTAxKSB7DQogICAgICAgIHJldHVybiB0aGlzLnJzT3BUcmFkaW5nRm9sZA0KICAgICAgfQ0KICAgICAgaWYgKHNlcnZpY2VUeXBlSWQgPT09IDEwMikgew0KICAgICAgICByZXR1cm4gdGhpcy5yc09wRnVtaWdhdGlvbkZvbGQNCiAgICAgIH0NCiAgICAgIGlmIChzZXJ2aWNlVHlwZUlkID09PSAxMDMpIHsNCiAgICAgICAgcmV0dXJuIHRoaXMucnNPcENPRm9sZA0KICAgICAgfQ0KICAgICAgaWYgKHNlcnZpY2VUeXBlSWQgPT09IDEwNCkgew0KICAgICAgICByZXR1cm4gdGhpcy5yc09wT3RoZXJGb2xkDQogICAgICB9DQogICAgfSwNCiAgICBhZGRUdWNrKHJzT3BUdWNrTGlzdCkgew0KICAgICAgbGV0IG9iaiA9IHt9DQoNCiAgICAgIHJzT3BUdWNrTGlzdC5wdXNoKG9iaikNCiAgICB9LA0KICAgIGdldFNlcnZpY2VPYmplY3Qoc2VydmljZVR5cGVJZCkgew0KICAgICAgaWYgKHNlcnZpY2VUeXBlSWQgPT09IDIwKSB7DQogICAgICAgIGlmICghdGhpcy5mb3JtLnJzT3BSYWlsRkNMKSB7DQogICAgICAgICAgdGhpcy5mb3JtLnJzT3BSYWlsRkNMID0gdGhpcy5yc09wUmFpbEZDTA0KICAgICAgICB9DQogICAgICAgIHJldHVybiB0aGlzLnJzT3BSYWlsRkNMDQogICAgICB9DQogICAgICBpZiAoc2VydmljZVR5cGVJZCA9PT0gMjEpIHsNCiAgICAgICAgaWYgKCF0aGlzLmZvcm0ucnNPcFJhaWxMQ0wpIHsNCiAgICAgICAgICB0aGlzLmZvcm0ucnNPcFJhaWxMQ0wgPSB0aGlzLnJzT3BSYWlsTENMDQogICAgICAgIH0NCiAgICAgICAgcmV0dXJuIHRoaXMucnNPcFJhaWxMQ0wNCiAgICAgIH0NCiAgICAgIGlmIChzZXJ2aWNlVHlwZUlkID09PSA0MCkgew0KICAgICAgICBpZiAoIXRoaXMuZm9ybS5yc09wRXhwcmVzcykgew0KICAgICAgICAgIHRoaXMuZm9ybS5yc09wRXhwcmVzcyA9IHRoaXMucnNPcEV4cHJlc3MNCiAgICAgICAgfQ0KICAgICAgICByZXR1cm4gdGhpcy5yc09wRXhwcmVzcw0KICAgICAgfQ0KICAgICAgaWYgKHNlcnZpY2VUeXBlSWQgPT09IDYwKSB7DQogICAgICAgIGlmICghdGhpcy5mb3JtLnJzT3BEb2NEZWNsYXJlKSB7DQogICAgICAgICAgdGhpcy5mb3JtLnJzT3BEb2NEZWNsYXJlID0gdGhpcy5yc09wRG9jRGVjbGFyZQ0KICAgICAgICB9DQogICAgICAgIHJldHVybiB0aGlzLnJzT3BEb2NEZWNsYXJlDQogICAgICB9DQogICAgICBpZiAoc2VydmljZVR5cGVJZCA9PT0gNjEpIHsNCiAgICAgICAgaWYgKCF0aGlzLmZvcm0ucnNPcEZyZWVEZWNsYXJlKSB7DQogICAgICAgICAgdGhpcy5mb3JtLnJzT3BGcmVlRGVjbGFyZSA9IHRoaXMucnNPcEZyZWVEZWNsYXJlDQogICAgICAgIH0NCiAgICAgICAgcmV0dXJuIHRoaXMucnNPcEZyZWVEZWNsYXJlDQogICAgICB9DQogICAgICBpZiAoc2VydmljZVR5cGVJZCA9PT0gNzApIHsNCiAgICAgICAgaWYgKCF0aGlzLmZvcm0ucnNPcERPQWdlbnQpIHsNCiAgICAgICAgICB0aGlzLmZvcm0ucnNPcERPQWdlbnQgPSB0aGlzLnJzT3BET0FnZW50DQogICAgICAgIH0NCiAgICAgICAgcmV0dXJuIHRoaXMucnNPcERPQWdlbnQNCiAgICAgIH0NCiAgICAgIGlmIChzZXJ2aWNlVHlwZUlkID09PSA3MSkgew0KICAgICAgICBpZiAoIXRoaXMuZm9ybS5yc09wQ2xlYXJBZ2VudCkgew0KICAgICAgICAgIHRoaXMuZm9ybS5yc09wQ2xlYXJBZ2VudCA9IHRoaXMucnNPcENsZWFyQWdlbnQNCiAgICAgICAgfQ0KICAgICAgICByZXR1cm4gdGhpcy5yc09wQ2xlYXJBZ2VudA0KICAgICAgfQ0KICAgICAgaWYgKHNlcnZpY2VUeXBlSWQgPT09IDgwKSB7DQogICAgICAgIGlmICghdGhpcy5mb3JtLnJzT3BXSFMpIHsNCiAgICAgICAgICB0aGlzLmZvcm0ucnNPcFdIUyA9IHRoaXMucnNPcFdIUw0KICAgICAgICB9DQogICAgICAgIHJldHVybiB0aGlzLnJzT3BXSFMNCiAgICAgIH0NCiAgICAgIGlmIChzZXJ2aWNlVHlwZUlkID09PSA5MCkgew0KICAgICAgICBpZiAoIXRoaXMuZm9ybS5yc09wM3JkQ2VydCkgew0KICAgICAgICAgIHRoaXMuZm9ybS5yc09wM3JkQ2VydCA9IHRoaXMucnNPcDNyZENlcnQNCiAgICAgICAgfQ0KICAgICAgICByZXR1cm4gdGhpcy5yc09wM3JkQ2VydA0KICAgICAgfQ0KICAgICAgaWYgKHNlcnZpY2VUeXBlSWQgPT09IDEwMCkgew0KICAgICAgICBpZiAoIXRoaXMuZm9ybS5yc09wSU5TKSB7DQogICAgICAgICAgdGhpcy5mb3JtLnJzT3BJTlMgPSB0aGlzLnJzT3BJTlMNCiAgICAgICAgfQ0KICAgICAgICByZXR1cm4gdGhpcy5yc09wSU5TDQogICAgICB9DQogICAgICBpZiAoc2VydmljZVR5cGVJZCA9PT0gMTAxKSB7DQogICAgICAgIGlmICghdGhpcy5mb3JtLnJzT3BUcmFkaW5nKSB7DQogICAgICAgICAgdGhpcy5mb3JtLnJzT3BUcmFkaW5nID0gdGhpcy5yc09wVHJhZGluZw0KICAgICAgICB9DQogICAgICAgIHJldHVybiB0aGlzLnJzT3BUcmFkaW5nDQogICAgICB9DQogICAgICBpZiAoc2VydmljZVR5cGVJZCA9PT0gMTAyKSB7DQogICAgICAgIGlmICghdGhpcy5mb3JtLnJzT3BGdW1pZ2F0aW9uKSB7DQogICAgICAgICAgdGhpcy5mb3JtLnJzT3BGdW1pZ2F0aW9uID0gdGhpcy5yc09wRnVtaWdhdGlvbg0KICAgICAgICB9DQogICAgICAgIHJldHVybiB0aGlzLnJzT3BGdW1pZ2F0aW9uDQogICAgICB9DQogICAgICBpZiAoc2VydmljZVR5cGVJZCA9PT0gMTAzKSB7DQogICAgICAgIGlmICghdGhpcy5mb3JtLnJzT3BDTykgew0KICAgICAgICAgIHRoaXMuZm9ybS5yc09wQ08gPSB0aGlzLnJzT3BDTw0KICAgICAgICB9DQogICAgICAgIHJldHVybiB0aGlzLnJzT3BDTw0KICAgICAgfQ0KICAgICAgaWYgKHNlcnZpY2VUeXBlSWQgPT09IDEwNCkgew0KICAgICAgICBpZiAoIXRoaXMuZm9ybS5yc09wT3RoZXIpIHsNCiAgICAgICAgICB0aGlzLmZvcm0ucnNPcE90aGVyID0gdGhpcy5yc09wT3RoZXINCiAgICAgICAgfQ0KICAgICAgICByZXR1cm4gdGhpcy5yc09wT3RoZXINCiAgICAgIH0NCiAgICB9LA0KICAgIGdldFBheWFibGUoc2VydmljZVR5cGVJZCkgew0KICAgICAgaWYgKHNlcnZpY2VUeXBlSWQgPT09IDEpIHsNCiAgICAgICAgcmV0dXJuIHRoaXMucnNPcFNlYUZjbFBheWFibGUNCiAgICAgIH0NCiAgICAgIGlmIChzZXJ2aWNlVHlwZUlkID09PSAyKSB7DQogICAgICAgIHJldHVybiB0aGlzLnJzT3BTZWFMY2xQYXlhYmxlDQogICAgICB9DQogICAgICBpZiAoc2VydmljZVR5cGVJZCA9PT0gMTApIHsNCiAgICAgICAgcmV0dXJuIHRoaXMucnNPcEFpclBheWFibGUNCiAgICAgIH0NCiAgICAgIGlmIChzZXJ2aWNlVHlwZUlkID09PSAyMCkgew0KICAgICAgICByZXR1cm4gdGhpcy5yc09wUmFpbEZjbFBheWFibGUNCiAgICAgIH0NCiAgICAgIGlmIChzZXJ2aWNlVHlwZUlkID09PSAyMSkgew0KICAgICAgICByZXR1cm4gdGhpcy5yc09wUmFpbEZjbFBheWFibGUNCiAgICAgIH0NCiAgICAgIGlmIChzZXJ2aWNlVHlwZUlkID09PSA0MCkgew0KICAgICAgICByZXR1cm4gdGhpcy5yc09wRXhwcmVzc1BheWFibGUNCiAgICAgIH0NCiAgICAgIGlmIChzZXJ2aWNlVHlwZUlkID09PSA1MCkgew0KICAgICAgICByZXR1cm4gdGhpcy5yc09wQ3RuclRydWNrUGF5YWJsZQ0KICAgICAgfQ0KICAgICAgaWYgKHNlcnZpY2VUeXBlSWQgPT09IDUxKSB7DQogICAgICAgIHJldHVybiB0aGlzLnJzT3BCdWxrVHJ1Y2tQYXlhYmxlDQogICAgICB9DQogICAgICBpZiAoc2VydmljZVR5cGVJZCA9PT0gNjApIHsNCiAgICAgICAgcmV0dXJuIHRoaXMucnNPcERvY0RlY2xhcmVQYXlhYmxlDQogICAgICB9DQogICAgICBpZiAoc2VydmljZVR5cGVJZCA9PT0gNjEpIHsNCiAgICAgICAgcmV0dXJuIHRoaXMucnNPcEZyZWVEZWNsYXJlUGF5YWJsZQ0KICAgICAgfQ0KICAgICAgaWYgKHNlcnZpY2VUeXBlSWQgPT09IDcwKSB7DQogICAgICAgIHJldHVybiB0aGlzLnJzT3BET0FnZW50U2VydmljZUluc3RhbmNlDQogICAgICB9DQogICAgICBpZiAoc2VydmljZVR5cGVJZCA9PT0gNzEpIHsNCiAgICAgICAgcmV0dXJuIHRoaXMucnNPcENsZWFyQWdlbnRQYXlhYmxlDQogICAgICB9DQogICAgICBpZiAoc2VydmljZVR5cGVJZCA9PT0gODApIHsNCiAgICAgICAgcmV0dXJuIHRoaXMucnNPcFdIU1BheWFibGUNCiAgICAgIH0NCiAgICAgIGlmIChzZXJ2aWNlVHlwZUlkID09PSA5MCkgew0KICAgICAgICByZXR1cm4gdGhpcy5yc09wM3JkQ2VydFBheWFibGUNCiAgICAgIH0NCiAgICAgIGlmIChzZXJ2aWNlVHlwZUlkID09PSAxMDApIHsNCiAgICAgICAgcmV0dXJuIHRoaXMucnNPcElOU1BheWFibGUNCiAgICAgIH0NCiAgICAgIGlmIChzZXJ2aWNlVHlwZUlkID09PSAxMDEpIHsNCiAgICAgICAgcmV0dXJuIHRoaXMucnNPcFRyYWRpbmdQYXlhYmxlDQogICAgICB9DQogICAgICBpZiAoc2VydmljZVR5cGVJZCA9PT0gMTAyKSB7DQogICAgICAgIHJldHVybiB0aGlzLnJzT3BGdW1pZ2F0aW9uUGF5YWJsZQ0KICAgICAgfQ0KICAgICAgaWYgKHNlcnZpY2VUeXBlSWQgPT09IDEwMykgew0KICAgICAgICByZXR1cm4gdGhpcy5yc09wQ09QYXlhYmxlDQogICAgICB9DQogICAgfSwNCiAgICBnZXRTZXJ2aWNlSW5zdGFuY2Uoc2VydmljZVR5cGVJZCkgew0KICAgICAgaWYgKHNlcnZpY2VUeXBlSWQgPT09IDEpIHsNCiAgICAgICAgcmV0dXJuIHRoaXMucnNPcFNlYUZjbFNlcnZpY2VJbnN0YW5jZQ0KICAgICAgfQ0KICAgICAgaWYgKHNlcnZpY2VUeXBlSWQgPT09IDIpIHsNCiAgICAgICAgcmV0dXJuIHRoaXMucnNPcFNlYUxjbFNlcnZpY2VJbnN0YW5jZQ0KICAgICAgfQ0KICAgICAgaWYgKHNlcnZpY2VUeXBlSWQgPT09IDEwKSB7DQogICAgICAgIHJldHVybiB0aGlzLnJzT3BBaXJTZXJ2aWNlSW5zdGFuY2UNCiAgICAgIH0NCiAgICAgIGlmIChzZXJ2aWNlVHlwZUlkID09PSAyMCkgew0KICAgICAgICByZXR1cm4gdGhpcy5yc09wUmFpbEZjbFNlcnZpY2VJbnN0YW5jZQ0KICAgICAgfQ0KICAgICAgaWYgKHNlcnZpY2VUeXBlSWQgPT09IDIxKSB7DQogICAgICAgIHJldHVybiB0aGlzLnJzT3BSYWlsTGNsU2VydmljZUluc3RhbmNlDQogICAgICB9DQogICAgICBpZiAoc2VydmljZVR5cGVJZCA9PT0gNDApIHsNCiAgICAgICAgcmV0dXJuIHRoaXMucnNPcEV4cHJlc3NTZXJ2aWNlSW5zdGFuY2UNCiAgICAgIH0NCiAgICAgIGlmIChzZXJ2aWNlVHlwZUlkID09PSA1MCkgew0KICAgICAgICByZXR1cm4gdGhpcy5yc09wQ3RuclRydWNrU2VydmljZUluc3RhbmNlDQogICAgICB9DQogICAgICBpZiAoc2VydmljZVR5cGVJZCA9PT0gNTEpIHsNCiAgICAgICAgcmV0dXJuIHRoaXMucnNPcEJ1bGtUcnVja1NlcnZpY2VJbnN0YW5jZQ0KICAgICAgfQ0KICAgICAgaWYgKHNlcnZpY2VUeXBlSWQgPT09IDcwKSB7DQogICAgICAgIHJldHVybiB0aGlzLnJzT3BET0FnZW50U2VydmljZUluc3RhbmNlDQogICAgICB9DQogICAgICBpZiAoc2VydmljZVR5cGVJZCA9PT0gNzEpIHsNCiAgICAgICAgcmV0dXJuIHRoaXMucnNPcENsZWFyQWdlbnRTZXJ2aWNlSW5zdGFuY2UNCiAgICAgIH0NCiAgICAgIGlmIChzZXJ2aWNlVHlwZUlkID09PSA4MCkgew0KICAgICAgICByZXR1cm4gdGhpcy5yc09wV0hTU2VydmljZUluc3RhbmNlDQogICAgICB9DQogICAgICBpZiAoc2VydmljZVR5cGVJZCA9PT0gOTApIHsNCiAgICAgICAgcmV0dXJuIHRoaXMucnNPcDNyZENlcnRTZXJ2aWNlSW5zdGFuY2UNCiAgICAgIH0NCiAgICAgIGlmIChzZXJ2aWNlVHlwZUlkID09PSAxMDApIHsNCiAgICAgICAgcmV0dXJuIHRoaXMucnNPcElOU1NlcnZpY2VJbnN0YW5jZQ0KICAgICAgfQ0KICAgICAgaWYgKHNlcnZpY2VUeXBlSWQgPT09IDEwMSkgew0KICAgICAgICByZXR1cm4gdGhpcy5yc09wVHJhZGluZ1NlcnZpY2VJbnN0YW5jZQ0KICAgICAgfQ0KICAgICAgaWYgKHNlcnZpY2VUeXBlSWQgPT09IDEwMikgew0KICAgICAgICByZXR1cm4gdGhpcy5yc09wRnVtaWdhdGlvblNlcnZpY2VJbnN0YW5jZQ0KICAgICAgfQ0KICAgICAgaWYgKHNlcnZpY2VUeXBlSWQgPT09IDEwMykgew0KICAgICAgICByZXR1cm4gdGhpcy5yc09wQ09TZXJ2aWNlSW5zdGFuY2UNCiAgICAgIH0NCiAgICAgIGlmIChzZXJ2aWNlVHlwZUlkID09PSAxMDQpIHsNCiAgICAgICAgcmV0dXJuIHRoaXMucnNPcE90aGVyU2VydmljZUluc3RhbmNlDQogICAgICB9DQogICAgfSwNCiAgICBnZXRCYXNpY0luZm8oc2VydmljZUluc3RhbmNlKSB7DQogICAgICByZXR1cm4gc2VydmljZUluc3RhbmNlLmlucXVpcnlObyArICIvIiArIHNlcnZpY2VJbnN0YW5jZS5zdXBwbGllck5hbWUgKyAiLSIgKyBzZXJ2aWNlSW5zdGFuY2Uuc3VwcGxpZXJDb250YWN0ICsgIi8iICsgc2VydmljZUluc3RhbmNlLmFncmVlbWVudFR5cGVDb2RlICsgIi0iICsgc2VydmljZUluc3RhbmNlLmFncmVlbWVudE5vICsgIi8iICsgdGhpcy5nZXROYW1lKHNlcnZpY2VJbnN0YW5jZS5pbnF1aXJ5UHNhSWQpICsgIi0iICsgc2VydmljZUluc3RhbmNlLmlucXVpcnlMZWF0ZXN0VXBkYXRlZFRpbWUgKyAiLyIgKyBzZXJ2aWNlSW5zdGFuY2UuaW5xdWlyeU5vdGljZSArICItIiArIHNlcnZpY2VJbnN0YW5jZS5pbnF1aXJ5SW5uZXJSZW1hcmsNCiAgICB9LA0KICAgIGdldEJvb2tpbmdBZ2VudCh2YWwpIHsNCiAgICAgIHRoaXMuZm9ybS5ib29raW5nQWdlbnQgPSB2YWwNCiAgICB9LA0KICAgIGdldE5hbWUoaWQpIHsNCiAgICAgIGlmIChpZCkgew0KICAgICAgICBpZiAodGhpcy4kc3RvcmUuc3RhdGUuZGF0YS5hbGxSc1N0YWZmTGlzdC5sZW5ndGggPT0gMCB8fCB0aGlzLiRzdG9yZS5zdGF0ZS5kYXRhLnJlZGlzTGlzdC5hbGxSc1N0YWZmTGlzdCkgew0KICAgICAgICAgIHN0b3JlLmRpc3BhdGNoKCJnZXRBbGxSc1N0YWZmTGlzdCIpDQogICAgICAgIH0NCg0KICAgICAgICBpZiAoaWQpIHsNCiAgICAgICAgICBsZXQgc3RhZmYgPSB0aGlzLiRzdG9yZS5zdGF0ZS5kYXRhLmFsbFJzU3RhZmZMaXN0LmZpbHRlcihyc1N0YWZmID0+IHJzU3RhZmYuc3RhZmZJZCA9PSBpZClbMF0NCiAgICAgICAgICBpZiAoc3RhZmYpIHsNCiAgICAgICAgICAgIHJldHVybiBzdGFmZi5zdGFmZkZhbWlseUxvY2FsTmFtZSArIHN0YWZmLnN0YWZmR2l2aW5nTG9jYWxOYW1lICsgc3RhZmYuc3RhZmZTaG9ydE5hbWUNCiAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgcmV0dXJuICIiDQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICB9IGVsc2Ugew0KICAgICAgICByZXR1cm4gIiINCiAgICAgIH0NCiAgICB9LA0KICAgIGFzeW5jIGdldEJvb2tpbmdEZXRhaWwoaWQpIHsNCiAgICAgIHRoaXMucmVzZXQoKQ0KICAgICAgYXdhaXQgZ2V0Qm9va2luZyhpZCkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgIGxldCByciA9IFtdDQogICAgICAgIGlmIChyZXNwb25zZS5kYXRhLnJlbGF0aW9uQ2xpZW50SWRzKSB7DQogICAgICAgICAgcmVzcG9uc2UuZGF0YS5yZWxhdGlvbkNsaWVudElkcy5zcGxpdCgiLCIpLmZvckVhY2godiA9PiB7DQogICAgICAgICAgICByci5wdXNoKE51bWJlcih2KSkNCiAgICAgICAgICB9KQ0KICAgICAgICB9DQogICAgICAgIHRoaXMucmVsYXRpb25DbGllbnRJZHMgPSBycg0KICAgICAgICB0aGlzLmdyb3NzV2VpZ2h0ID0gcmVzcG9uc2UuZGF0YS5ncm9zc1dlaWdodA0KICAgICAgICB0aGlzLmdvb2RzVmFsdWUgPSByZXNwb25zZS5kYXRhLmdvb2RzVmFsdWUNCiAgICAgICAgdGhpcy5mb3JtID0gcmVzcG9uc2UuZGF0YQ0KICAgICAgICB0aGlzLmZvcm0ucmVsYXRpb25DbGllbnRJZHMgPSBycg0KICAgICAgICBsZXQgY0lkcyA9IG5ldyBTZXQoKQ0KICAgICAgICBpZiAoY0lkcy5zaXplID4gMCkgew0KICAgICAgICAgIGNJZHMuZm9yRWFjaChjID0+IHsNCiAgICAgICAgICAgIHRoaXMuY2Fycmllcklkcy5wdXNoKGMpDQogICAgICAgICAgfSkNCiAgICAgICAgfQ0KICAgICAgICBpZiAodGhpcy5iZWxvbmdMaXN0ICE9IHVuZGVmaW5lZCkgew0KICAgICAgICAgIGZvciAoY29uc3QgYSBvZiB0aGlzLmJlbG9uZ0xpc3QpIHsNCiAgICAgICAgICAgIGlmIChhLmNoaWxkcmVuICE9IHVuZGVmaW5lZCkgew0KICAgICAgICAgICAgICBmb3IgKGNvbnN0IGIgb2YgYS5jaGlsZHJlbikgew0KICAgICAgICAgICAgICAgIGlmIChiLmNoaWxkcmVuICE9IHVuZGVmaW5lZCkgew0KICAgICAgICAgICAgICAgICAgZm9yIChjb25zdCBjIG9mIGIuY2hpbGRyZW4pIHsNCiAgICAgICAgICAgICAgICAgICAgaWYgKGMuc3RhZmZJZCA9PSByZXNwb25zZS5kYXRhLnNhbGVzSWQpIHsNCiAgICAgICAgICAgICAgICAgICAgICB0aGlzLnNhbGVzSWQgPSBjLmRlcHRJZA0KICAgICAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgICAgICAgIGlmIChjLnN0YWZmSWQgPT0gcmVzcG9uc2UuZGF0YS5zYWxlc0Fzc2lzdGFudElkKSB7DQogICAgICAgICAgICAgICAgICAgICAgdGhpcy5zYWxlc0Fzc2lzdGFudElkID0gYy5kZXB0SWQNCiAgICAgICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgICAgICBpZiAoYy5zdGFmZklkID09IHJlc3BvbnNlLmRhdGEuc2FsZXNPYnNlcnZlcklkKSB7DQogICAgICAgICAgICAgICAgICAgICAgdGhpcy5zYWxlc09ic2VydmVySWQgPSBjLmRlcHRJZA0KICAgICAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICB9DQogICAgICAgICAgICB9DQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICAgIGlmICh0aGlzLm9wTGlzdCAhPSB1bmRlZmluZWQpIHsNCiAgICAgICAgICBmb3IgKGNvbnN0IGEgb2YgdGhpcy5vcExpc3QpIHsNCiAgICAgICAgICAgIGlmIChhLnN0YWZmSWQgPT0gcmVzcG9uc2UuZGF0YS5vcElkKSB7DQogICAgICAgICAgICAgIHRoaXMub3BJZCA9IGEucm9sZUlkDQogICAgICAgICAgICB9DQoNCiAgICAgICAgICAgIGlmIChhLmNoaWxkcmVuICE9IHVuZGVmaW5lZCkgew0KICAgICAgICAgICAgICBmb3IgKGNvbnN0IGIgb2YgYS5jaGlsZHJlbikgew0KICAgICAgICAgICAgICAgIGlmIChhLnJvbGUucm9sZUxvY2FsTmFtZSA9PSAi5pON5L2c5ZGYIiAmJiBiLnN0YWZmSWQgPT0gcmVzcG9uc2UuZGF0YS5vcElkKSB7DQogICAgICAgICAgICAgICAgICB0aGlzLm9wSWQgPSBiLnJvbGVJZA0KICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICBpZiAoYS5yb2xlLnJvbGVMb2NhbE5hbWUgPT0gIuiuouiIseWRmCIgJiYgYi5zdGFmZklkID09IHJlc3BvbnNlLmRhdGEuYm9va2luZ09wSWQpIHsNCiAgICAgICAgICAgICAgICAgIHRoaXMuYm9va2luZ09wSWQgPSBiLnJvbGVJZA0KICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICBpZiAoYS5yb2xlLnJvbGVMb2NhbE5hbWUgPT0gIuWNleivgeWRmCIgJiYgYi5zdGFmZklkID09IHJlc3BvbnNlLmRhdGEuZG9jT3BJZCkgew0KICAgICAgICAgICAgICAgICAgdGhpcy5kb2NPcElkID0gYi5yb2xlSWQNCiAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgICAgaWYgKGIuc3RhZmZJZCA9PSByZXNwb25zZS5kYXRhLm9wT2JzZXJ2ZXJJZCkgew0KICAgICAgICAgICAgICAgICAgdGhpcy5vcE9ic2VydmVySWQgPSBiLnJvbGVJZA0KICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgICBpZiAodGhpcy5idXNpbmVzc0xpc3QgIT0gdW5kZWZpbmVkKSB7DQogICAgICAgICAgZm9yIChjb25zdCBhIG9mIHRoaXMuYnVzaW5lc3NMaXN0KSB7DQogICAgICAgICAgICAvKiBpZiAoYS5jaGlsZHJlbiAhPSB1bmRlZmluZWQpIHsNCiAgICAgICAgICAgICAgZm9yIChjb25zdCBiIG9mIGEuY2hpbGRyZW4pIHsNCiAgICAgICAgICAgICAgICBpZiAoYi5zdGFmZklkID09IHJlc3BvbnNlLmRhdGEudmVyaWZ5UHNhSWQpIHsNCiAgICAgICAgICAgICAgICAgIHRoaXMudmVyaWZ5UHNhSWQgPSBiLnJvbGVJZA0KICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgfSAqLw0KICAgICAgICAgICAgaWYgKGEuc3RhZmZJZCA9PSByZXNwb25zZS5kYXRhLnZlcmlmeVBzYUlkKSB7DQogICAgICAgICAgICAgIHRoaXMudmVyaWZ5UHNhSWQgPSBhLnN0YWZmSWQNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgICAgLy8g6K6+572u5Z+656GA54mp5rWB5L+h5oGv55qE5YC8DQogICAgICAgIGlmIChyZXNwb25zZS5kYXRhLnJzQm9va2luZ0xvZ2lzdGljc1R5cGVCYXNpY0luZm8gIT0gbnVsbCkgew0KICAgICAgICAgIHRoaXMubG9naXN0aWNzQmFzaWNJbmZvID0gcmVzcG9uc2UuZGF0YS5yc0Jvb2tpbmdMb2dpc3RpY3NUeXBlQmFzaWNJbmZvDQogICAgICAgICAgdGhpcy5sb2dpc3RpY3NSZWNlaXZhYmxlUGF5YWJsZUxpc3QgPSByZXNwb25zZS5kYXRhLnJzQm9va2luZ0xvZ2lzdGljc1R5cGVCYXNpY0luZm8ucnNCb29raW5nUmVjZWl2YWJsZVBheWFibGVMaXN0DQogICAgICAgICAgLyogdGhpcy5sb2dpc3RpY3NSZWNlaXZhYmxlUGF5YWJsZUxpc3QubWFwKGxvZ2lzdGljc1JlY2VpdmFibGVQYXlhYmxlPT57DQogICAgICAgICAgICBsb2cNCiAgICAgICAgICB9KSAqLw0KICAgICAgICB9DQogICAgICAgIC8vIOiuvue9ruWJjeeoi+i/kOi+k+eahOWAvA0KICAgICAgICBpZiAocmVzcG9uc2UuZGF0YS5yc0Jvb2tpbmdQcmVDYXJyaWFnZUJhc2ljSW5mbyAhPSBudWxsKSB7DQogICAgICAgICAgdGhpcy5wcmVDYXJyaWFnZUJhc2ljSW5mbyA9IHJlc3BvbnNlLmRhdGEucnNCb29raW5nUHJlQ2FycmlhZ2VCYXNpY0luZm8NCiAgICAgICAgICB0aGlzLnByZUNhcnJpYWdlUmVjZWl2YWJsZVBheWFibGVMaXN0ID0gcmVzcG9uc2UuZGF0YS5yc0Jvb2tpbmdQcmVDYXJyaWFnZUJhc2ljSW5mby5yc0Jvb2tpbmdSZWNlaXZhYmxlUGF5YWJsZUxpc3QNCiAgICAgICAgfQ0KICAgICAgICAvLyDorr7nva7lh7rlj6PmiqXlhbPnmoTlgLwNCiAgICAgICAgaWYgKHJlc3BvbnNlLmRhdGEucnNCb29raW5nRXhwb3J0RGVjbGFyYXRpb25CYXNpY0luZm8gIT0gbnVsbCkgew0KICAgICAgICAgIHRoaXMuZXhwb3J0RGVjbGFyYXRpb25CYXNpY0luZm8gPSByZXNwb25zZS5kYXRhLnJzQm9va2luZ0V4cG9ydERlY2xhcmF0aW9uQmFzaWNJbmZvDQogICAgICAgICAgdGhpcy5leHBvcnREZWNsYXJhdGlvblJlY2VpdmFibGVQYXlhYmxlTGlzdCA9IHJlc3BvbnNlLmRhdGEucnNCb29raW5nRXhwb3J0RGVjbGFyYXRpb25CYXNpY0luZm8ucnNCb29raW5nUmVjZWl2YWJsZVBheWFibGVMaXN0DQogICAgICAgIH0NCiAgICAgICAgLy8g6K6+572u6L+b5Y+j5riF5YWz55qE5YC8DQogICAgICAgIGlmIChyZXNwb25zZS5kYXRhLnJzQm9va2luZ0ltcG9ydENsZWFyYW5jZUJhc2ljSW5mbyAhPSBudWxsKSB7DQogICAgICAgICAgdGhpcy5pbXBvcnRDbGVhcmFuY2VCYXNpY0luZm8gPSByZXNwb25zZS5kYXRhLnJzQm9va2luZ0ltcG9ydENsZWFyYW5jZUJhc2ljSW5mbw0KICAgICAgICAgIHRoaXMuaW1wb3J0Q2xlYXJhbmNlUmVjZWl2YWJsZVBheWFibGVMaXN0ID0gcmVzcG9uc2UuZGF0YS5yc0Jvb2tpbmdJbXBvcnRDbGVhcmFuY2VCYXNpY0luZm8ucnNCb29raW5nUmVjZWl2YWJsZVBheWFibGVMaXN0DQogICAgICAgIH0NCiAgICAgICAgdGhpcy5sb2NhdGlvbk9wdGlvbnMgPSByZXNwb25zZS5sb2NhdGlvbk9wdGlvbnMNCiAgICAgIH0pDQogICAgfSwNCiAgICAvLyDku47miqXku7fooajkuK3ov4fmnaUNCiAgICBhc3luYyBnZXRRdW90YXRpb24oaWQpIHsNCiAgICAgIHRoaXMucmVzZXQoKQ0KICAgICAgYXdhaXQgZ2V0UXVvdGF0aW9uKGlkKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgbGV0IHJldmVudWVUb25BcnIgPSBbXQ0KICAgICAgICByZXNwb25zZS5taWRSZXZlbnVlVG9uc0xpc3QubWFwKGl0ZW0gPT4gew0KICAgICAgICAgIHJldmVudWVUb25BcnIucHVzaChpdGVtLmNvdW50ICsgIngiICsgaXRlbS51bml0KQ0KICAgICAgICB9KQ0KICAgICAgICB0aGlzLmNvbXBhbnlMaXN0ID0gcmVzcG9uc2UuY29tcGFueSA/IFtyZXNwb25zZS5jb21wYW55XSA6IFtdDQogICAgICAgIHRoaXMuZm9ybS5jbGllbnROYW1lID0gcmVzcG9uc2UuZGF0YS5jb21wYW55TmFtZQ0KICAgICAgICB0aGlzLmZvcm0uY29tcGFueSA9IHJlc3BvbnNlLmRhdGEuY29tcGFueQ0KICAgICAgICB0aGlzLmZvcm0ucmV2ZW51ZVRvbiA9IHJldmVudWVUb25BcnIuam9pbigiKyIpDQogICAgICAgIHRoaXMuZm9ybS5sb2dpc3RpY3NUeXBlSWQgPSByZXNwb25zZS5kYXRhLmxvZ2lzdGljc1R5cGVJZA0KICAgICAgICB0aGlzLmZvcm0uc2FsZXNJZCA9IHJlc3BvbnNlLmRhdGEuc3RhZmZJZA0KICAgICAgICB0aGlzLmZvcm0uY2xpZW50SWQgPSByZXNwb25zZS5kYXRhLmNvbXBhbnlJZA0KICAgICAgICB0aGlzLmZvcm0uY2xpZW50Um9sZUlkID0gcmVzcG9uc2UuZGF0YS5jb21wYW55Um9sZUlkDQogICAgICAgIHRoaXMuZm9ybS5jbGllbnRDb250YWN0b3IgPSByZXNwb25zZS5kYXRhLmV4dFN0YWZmTmFtZQ0KICAgICAgICB0aGlzLmZvcm0uY2xpZW50Q29udGFjdG9yVGVsID0gcmVzcG9uc2UuZGF0YS5leHRTdGFmZlBob25lTnVtDQogICAgICAgIHRoaXMuZm9ybS5jbGllbnRDb250YWN0b3JFbWFpbCA9IHJlc3BvbnNlLmRhdGEuZXh0U3RhZmZFbWFpbEVudGVycHJpc2UNCiAgICAgICAgdGhpcy5mb3JtLnF1b3RhdGlvbk5vID0gcmVzcG9uc2UuZGF0YS5yaWNoTm8NCiAgICAgICAgdGhpcy5mb3JtLnF1b3RhdGlvbkRhdGUgPSBuZXcgRGF0ZSgpDQogICAgICAgIHRoaXMuZm9ybS5pbXBFeHBUeXBlSWQgPSByZXNwb25zZS5kYXRhLmltRXhQb3J0DQogICAgICAgIHRoaXMuZm9ybS5nb29kc05hbWVTdW1tYXJ5ID0gcmVzcG9uc2UuZGF0YS5jYXJnb05hbWUNCiAgICAgICAgdGhpcy5mb3JtLmdvb2RzVmFsdWUgPSByZXNwb25zZS5kYXRhLmNhcmdvUHJpY2UNCiAgICAgICAgdGhpcy5mb3JtLmdvb2RzQ3VycmVuY3lJZCA9IHJlc3BvbnNlLmRhdGEuY2FyZ29DdXJyZW5jeUlkDQogICAgICAgIHRoaXMuZm9ybS5ncm9zc1dlaWdodCA9IHJlc3BvbnNlLmRhdGEuZ3Jvc3NXZWlnaHQNCiAgICAgICAgdGhpcy5ncm9zc1dlaWdodCA9IHJlc3BvbnNlLmRhdGEuZ3Jvc3NXZWlnaHQNCiAgICAgICAgdGhpcy5mb3JtLndlaWdodFVuaXRJZCA9IHJlc3BvbnNlLmRhdGEuY2FyZ29Vbml0SWQNCiAgICAgICAgdGhpcy5mb3JtLnBvbElkID0gcmVzcG9uc2UuZGF0YS5kZXBhcnR1cmVJZA0KICAgICAgICB0aGlzLmZvcm0uZGVzdGluYXRpb25Qb3J0SWQgPSByZXNwb25zZS5kYXRhLmRlc3RpbmF0aW9uSWQNCiAgICAgICAgdGhpcy5mb3JtLnRyYW5zaXRQb3J0SWQgPSByZXNwb25zZS5kYXRhLnRyYW5zcG9ydGF0aW9uVGVybXNJZA0KICAgICAgICB0aGlzLmZvcm0ucmV2ZW51ZVRvbnMgPSByZXNwb25zZS5kYXRhLnJldmVudWVUb25zDQogICAgICAgIHRoaXMuZm9ybS5uZXdCb29raW5nUmVtYXJrID0gcmVzcG9uc2UuZGF0YS5yZW1hcmsNCiAgICAgICAgdGhpcy5mb3JtLmlucXVpcnlObyA9IHJlc3BvbnNlLmRhdGEucmljaE5vDQogICAgICAgIHRoaXMuZm9ybS5xb3V0YXRpb25ObyA9IHJlc3BvbnNlLmRhdGEucmljaE5vDQogICAgICAgIHRoaXMuZm9ybS5xb3V0YXRpb25Ta2V0Y2ggPSByZXNwb25zZS5kYXRhLnF1b3RhdGlvblNrZXRjaA0KICAgICAgICB0aGlzLmZvcm0ucW91dGF0aW9uVGltZSA9IG5ldyBEYXRlKCkNCiAgICAgICAgaWYgKHRoaXMuYmVsb25nTGlzdCAhPSB1bmRlZmluZWQpIHsNCiAgICAgICAgICBmb3IgKGNvbnN0IGEgb2YgdGhpcy5iZWxvbmdMaXN0KSB7DQogICAgICAgICAgICBpZiAoYS5jaGlsZHJlbiAhPSB1bmRlZmluZWQpIHsNCiAgICAgICAgICAgICAgZm9yIChjb25zdCBiIG9mIGEuY2hpbGRyZW4pIHsNCiAgICAgICAgICAgICAgICBpZiAoYi5jaGlsZHJlbiAhPSB1bmRlZmluZWQpIHsNCiAgICAgICAgICAgICAgICAgIGZvciAoY29uc3QgYyBvZiBiLmNoaWxkcmVuKSB7DQogICAgICAgICAgICAgICAgICAgIGlmIChjLnN0YWZmSWQgPT0gcmVzcG9uc2UuZGF0YS5zdGFmZklkKSB7DQogICAgICAgICAgICAgICAgICAgICAgdGhpcy5zYWxlc0lkID0gYy5kZXB0SWQNCiAgICAgICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgICBsZXQgY0lkcyA9IG5ldyBTZXQoKQ0KICAgICAgICBmb3IgKGNvbnN0IHYgb2YgdGhpcy5jYXJyaWVyTGlzdCkgew0KICAgICAgICAgIGlmICh2LmNoaWxkcmVuICE9IHVuZGVmaW5lZCAmJiB2LmNoaWxkcmVuLmxlbmd0aCA+IDApIHsNCiAgICAgICAgICAgIGZvciAoY29uc3QgYSBvZiB2LmNoaWxkcmVuKSB7DQogICAgICAgICAgICAgIGlmIChhLmNhcnJpZXIgIT0gbnVsbCAmJiBhLmNhcnJpZXIuY2FycmllcklkICE9IG51bGwgJiYgYS5jYXJyaWVyLmNhcnJpZXJJZCAhPSB1bmRlZmluZWQgJiYgcmVzcG9uc2UuZGF0YS5jYXJyaWVySWRzICE9IG51bGwgJiYgcmVzcG9uc2UuZGF0YS5jYXJyaWVySWRzLmxlbmd0aCA+IDApIHsNCiAgICAgICAgICAgICAgICBpZiAocmVzcG9uc2UuZGF0YS5jYXJyaWVySWRzLmluY2x1ZGVzKGEuY2Fycmllci5jYXJyaWVySWQpKSB7DQogICAgICAgICAgICAgICAgICBjSWRzLmFkZChhLnNlcnZpY2VUeXBlSWQpDQogICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgIGlmIChhLmNoaWxkcmVuICE9IHVuZGVmaW5lZCAmJiBhLmNoaWxkcmVuLmxlbmd0aCA+IDApIHsNCiAgICAgICAgICAgICAgICBmb3IgKGNvbnN0IGIgb2YgYS5jaGlsZHJlbikgew0KICAgICAgICAgICAgICAgICAgaWYgKGIuY2FycmllciAhPSBudWxsICYmIGIuY2Fycmllci5jYXJyaWVySWQgIT0gbnVsbCAmJiBiLmNhcnJpZXIuY2FycmllcklkICE9IHVuZGVmaW5lZCAmJiByZXNwb25zZS5kYXRhLmNhcnJpZXJJZHMgIT0gbnVsbCAmJiByZXNwb25zZS5kYXRhLmNhcnJpZXJJZHMubGVuZ3RoID4gMCkgew0KICAgICAgICAgICAgICAgICAgICBpZiAocmVzcG9uc2UuZGF0YS5jYXJyaWVySWRzLmluY2x1ZGVzKGIuY2Fycmllci5jYXJyaWVySWQpKSB7DQogICAgICAgICAgICAgICAgICAgICAgY0lkcy5hZGQoYi5zZXJ2aWNlVHlwZUlkKQ0KICAgICAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICB9DQogICAgICAgICAgICB9DQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICAgIGlmIChjSWRzLnNpemUgPiAwKSB7DQogICAgICAgICAgY0lkcy5mb3JFYWNoKGMgPT4gew0KICAgICAgICAgICAgdGhpcy5jYXJyaWVySWRzLnB1c2goYykNCiAgICAgICAgICB9KQ0KICAgICAgICB9DQoNCiAgICAgICAgLy8g5rOo5oSP5LqL6aG5DQogICAgICAgIGxldCBjaGFyYWN0ZXJpc3RpY3MgPSAiIg0KICAgICAgICBpZiAocmVzcG9uc2UuY2hhcmFjdGVyaXN0aWNzKSB7DQogICAgICAgICAgZm9yIChjb25zdCBjIG9mIHJlc3BvbnNlLmNoYXJhY3RlcmlzdGljcykgew0KICAgICAgICAgICAgY2hhcmFjdGVyaXN0aWNzICs9IChjLnNlcnZpY2VUeXBlICE9IG51bGwgPyBjLnNlcnZpY2VUeXBlIDogIiIpDQogICAgICAgICAgICAgICsgKGMuY2FyZ29UeXBlICE9IG51bGwgPyBjLmNhcmdvVHlwZSA6ICIiKQ0KICAgICAgICAgICAgICArIChjLmNvbXBhbnkgIT0gbnVsbCA/IGMuY29tcGFueSA6ICIiKQ0KICAgICAgICAgICAgICArIChjLmxvY2F0aW9uRGVwYXJ0dXJlICE9IG51bGwgPyBjLmxvY2F0aW9uRGVwYXJ0dXJlIDogIiIpDQogICAgICAgICAgICAgICsgKGMubG9jYXRpb25EZXN0aW5hdGlvbiAhPSBudWxsID8gYy5sb2NhdGlvbkRlc3RpbmF0aW9uIDogIiIpDQogICAgICAgICAgICAgICsgKGMuaW5mbyAhPSBudWxsID8gYy5pbmZvIDogIiIpDQogICAgICAgICAgICAgICsgKGMuZXNzZW50aWFsRGV0YWlsICE9IG51bGwgPyBjLmVzc2VudGlhbERldGFpbCA6ICIiKSArICJcbiINCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgICAgdGhpcy5mb3JtLmlucXVpcnlOb3RpY2UgPSBjaGFyYWN0ZXJpc3RpY3MNCiAgICAgICAgdGhpcy5mb3JtLnNlcnZpY2VUeXBlSWRzID0gcmVzcG9uc2Uuc2VydmljZVR5cGVJZHMNCiAgICAgICAgdGhpcy5mb3JtLmNhcmdvVHlwZUlkcyA9IHJlc3BvbnNlLmNhcmdvVHlwZUlkcw0KICAgICAgICB0aGlzLmNhcmdvVHlwZUNvZGVzID0gcmVzcG9uc2UuY2FyZ29UeXBlQ29kZVN1bSA/IHJlc3BvbnNlLmNhcmdvVHlwZUNvZGVTdW0udG9TdHJpbmcoKS5zcGxpdCgiLCIpIDogW10NCiAgICAgICAgdGhpcy5sb2NhdGlvbk9wdGlvbnMgPSByZXNwb25zZS5sb2NhdGlvbk9wdGlvbnMNCiAgICAgICAgdGhpcy5mb3JtLnByZUNhcnJpYWdlUmVnaW9uSWRzID0gcmVzcG9uc2UubG9jYXRpb25Mb2FkaW5nSWRzDQogICAgICAgIHRoaXMuZm9ybS5jbGllbnRSb2xlSWQgPSByZXNwb25zZS5yb2xlSWRzWzBdDQoNCiAgICAgICAgZm9yIChjb25zdCBxZiBvZiByZXNwb25zZS5xdW90YXRpb25GcmVpZ2h0KSB7DQogICAgICAgICAgLy8g5LiA5p2h6LS555So5ouG5YiG5oiQ5Lik5p2hKOW6lOaUtuWSjOW6lOS7mCkNCiAgICAgICAgICBsZXQgY2hhcmdlUGF5ID0ge30NCiAgICAgICAgICBjaGFyZ2VQYXkuc2hvd0NsaWVudCA9IGZhbHNlDQogICAgICAgICAgY2hhcmdlUGF5LnNob3dTdXBwbGllciA9IGZhbHNlDQogICAgICAgICAgY2hhcmdlUGF5LnNob3dRdW90YXRpb25DaGFyZ2UgPSBmYWxzZQ0KICAgICAgICAgIGNoYXJnZVBheS5zaG93Q29zdENoYXJnZSA9IGZhbHNlDQogICAgICAgICAgY2hhcmdlUGF5LnNob3dRdW90YXRpb25DdXJyZW5jeSA9IGZhbHNlDQogICAgICAgICAgY2hhcmdlUGF5LnNob3dDb3N0Q3VycmVuY3kgPSBmYWxzZQ0KICAgICAgICAgIGNoYXJnZVBheS5zaG93UXVvdGF0aW9uVW5pdCA9IGZhbHNlDQogICAgICAgICAgY2hhcmdlUGF5LnNob3dDb3N0VW5pdCA9IGZhbHNlDQogICAgICAgICAgY2hhcmdlUGF5LnNob3dTdHJhdGVneSA9IGZhbHNlDQogICAgICAgICAgY2hhcmdlUGF5LnNob3dVbml0UmF0ZSA9IGZhbHNlDQogICAgICAgICAgY2hhcmdlUGF5LnNob3dBbW91bnQgPSBmYWxzZQ0KICAgICAgICAgIGNoYXJnZVBheS5zaG93Q3VycmVuY3lSYXRlID0gZmFsc2UNCiAgICAgICAgICBjaGFyZ2VQYXkuc2hvd0R1dHlSYXRlID0gZmFsc2UNCg0KICAgICAgICAgIGNoYXJnZVBheS5jb21wYW55TmFtZSA9IHFmLmNvbXBhbnkNCiAgICAgICAgICBjaGFyZ2VQYXkuY2xlYXJpbmdDb21wYW55SWQgPSBxZi5jb21wYW55SWQNCiAgICAgICAgICBjaGFyZ2VQYXkuZG5DaGFyZ2VOYW1lSWQgPSBxZi5jaGFyZ2VJZA0KICAgICAgICAgIGNoYXJnZVBheS5jaGFyZ2VOYW1lID0gcWYuY2hhcmdlDQogICAgICAgICAgY2hhcmdlUGF5LmRuQ3VycmVuY3lDb2RlID0gcWYucXVvdGF0aW9uQ3VycmVuY3lDb2RlDQogICAgICAgICAgY2hhcmdlUGF5LmRuVW5pdFJhdGUgPSBxZi5pbnF1aXJ5UmF0ZQ0KICAgICAgICAgIGNoYXJnZVBheS5kblVuaXRDb2RlID0gcWYudW5pdENvZGUNCiAgICAgICAgICBjaGFyZ2VQYXkuZG5BbW91bnQgPSBxZi5pbnF1aXJ5QW1vdW50DQogICAgICAgICAgY2hhcmdlUGF5LmJhc2ljQ3VycmVuY3lSYXRlID0gcWYuZXhjaGFuZ2VSYXRlDQogICAgICAgICAgY2hhcmdlUGF5LmR1dHlSYXRlID0gcWYudGF4UmF0ZQ0KICAgICAgICAgIGNoYXJnZVBheS5zdWJ0b3RhbCA9IGN1cnJlbmN5KGNoYXJnZVBheS5kblVuaXRSYXRlKS5tdWx0aXBseShjaGFyZ2VQYXkuZG5BbW91bnQpLm11bHRpcGx5KGNoYXJnZVBheS5iYXNpY0N1cnJlbmN5UmF0ZSkudmFsdWUNCg0KICAgICAgICAgIGxldCBjaGFyZ2VSZWNlaXZlID0ge30NCiAgICAgICAgICBjaGFyZ2VSZWNlaXZlLnNob3dDbGllbnQgPSBmYWxzZQ0KICAgICAgICAgIGNoYXJnZVJlY2VpdmUuc2hvd1N1cHBsaWVyID0gZmFsc2UNCiAgICAgICAgICBjaGFyZ2VSZWNlaXZlLnNob3dRdW90YXRpb25DaGFyZ2UgPSBmYWxzZQ0KICAgICAgICAgIGNoYXJnZVJlY2VpdmUuc2hvd0Nvc3RDaGFyZ2UgPSBmYWxzZQ0KICAgICAgICAgIGNoYXJnZVJlY2VpdmUuc2hvd1F1b3RhdGlvbkN1cnJlbmN5ID0gZmFsc2UNCiAgICAgICAgICBjaGFyZ2VSZWNlaXZlLnNob3dDb3N0Q3VycmVuY3kgPSBmYWxzZQ0KICAgICAgICAgIGNoYXJnZVJlY2VpdmUuc2hvd1F1b3RhdGlvblVuaXQgPSBmYWxzZQ0KICAgICAgICAgIGNoYXJnZVJlY2VpdmUuc2hvd0Nvc3RVbml0ID0gZmFsc2UNCiAgICAgICAgICBjaGFyZ2VSZWNlaXZlLnNob3dTdHJhdGVneSA9IGZhbHNlDQogICAgICAgICAgY2hhcmdlUmVjZWl2ZS5zaG93VW5pdFJhdGUgPSBmYWxzZQ0KICAgICAgICAgIGNoYXJnZVJlY2VpdmUuc2hvd0Ftb3VudCA9IGZhbHNlDQogICAgICAgICAgY2hhcmdlUmVjZWl2ZS5zaG93Q3VycmVuY3lSYXRlID0gZmFsc2UNCiAgICAgICAgICBjaGFyZ2VSZWNlaXZlLnNob3dEdXR5UmF0ZSA9IGZhbHNlDQoNCiAgICAgICAgICBpZiAodGhpcy5mb3JtLmNsaWVudE5hbWUpIHsNCiAgICAgICAgICAgIGNoYXJnZVJlY2VpdmUuY2xlYXJpbmdDb21wYW55SWQgPSByZXNwb25zZS5kYXRhLmNvbXBhbnlJZA0KICAgICAgICAgICAgY2hhcmdlUmVjZWl2ZS5jb21wYW55TmFtZSA9IHJlc3BvbnNlLmRhdGEuY29tcGFueQ0KICAgICAgICAgIH0NCiAgICAgICAgICBjaGFyZ2VSZWNlaXZlLmRuQ2hhcmdlTmFtZUlkID0gcWYuY2hhcmdlSWQNCiAgICAgICAgICBjaGFyZ2VSZWNlaXZlLmNoYXJnZU5hbWUgPSBxZi5jaGFyZ2UNCiAgICAgICAgICBjaGFyZ2VSZWNlaXZlLmRuQ3VycmVuY3lDb2RlID0gcWYucXVvdGF0aW9uQ3VycmVuY3lDb2RlDQogICAgICAgICAgY2hhcmdlUmVjZWl2ZS5kblVuaXRSYXRlID0gcWYucXVvdGF0aW9uUmF0ZQ0KICAgICAgICAgIGNoYXJnZVJlY2VpdmUuZG5Vbml0Q29kZSA9IHFmLnVuaXRDb2RlDQogICAgICAgICAgY2hhcmdlUmVjZWl2ZS5kbkFtb3VudCA9IHFmLnF1b3RhdGlvbkFtb3VudA0KICAgICAgICAgIGNoYXJnZVJlY2VpdmUuYmFzaWNDdXJyZW5jeVJhdGUgPSBxZi5leGNoYW5nZVJhdGUNCiAgICAgICAgICBjaGFyZ2VSZWNlaXZlLmR1dHlSYXRlID0gcWYudGF4UmF0ZQ0KICAgICAgICAgIGNoYXJnZVJlY2VpdmUuc3VidG90YWwgPSBjdXJyZW5jeShjaGFyZ2VSZWNlaXZlLmRuVW5pdFJhdGUpLm11bHRpcGx5KGNoYXJnZVJlY2VpdmUuZG5BbW91bnQpLm11bHRpcGx5KGNoYXJnZVJlY2VpdmUuYmFzaWNDdXJyZW5jeVJhdGUpLnZhbHVlDQoNCiAgICAgICAgICBpZiAocWYuc2VydmljZVR5cGVJZCA9PT0gMSkgew0KICAgICAgICAgICAgdGhpcy5yc09wU2VhRmNsLnJzQ2hhcmdlTGlzdC5wdXNoKGNoYXJnZVBheSkNCiAgICAgICAgICAgIHRoaXMuZm9ybS5yc09wU2VhRmNsTGlzdC5tYXAoaXRlbSA9PiB7DQogICAgICAgICAgICAgIGl0ZW0ucnNDaGFyZ2VMaXN0LnB1c2goY2hhcmdlUGF5KQ0KICAgICAgICAgICAgfSkNCiAgICAgICAgICB9DQogICAgICAgICAgaWYgKHFmLnNlcnZpY2VUeXBlSWQgPT09IDIpIHsNCiAgICAgICAgICAgIHRoaXMucnNPcFNlYUxjbC5yc0NoYXJnZUxpc3QucHVzaChjaGFyZ2VQYXkpDQogICAgICAgICAgICB0aGlzLmZvcm0ucnNPcFNlYUxjbExpc3QubWFwKGl0ZW0gPT4gew0KICAgICAgICAgICAgICBpdGVtLnJzQ2hhcmdlTGlzdC5wdXNoKGNoYXJnZVBheSkNCiAgICAgICAgICAgIH0pDQogICAgICAgICAgfQ0KICAgICAgICAgIGlmIChxZi5zZXJ2aWNlVHlwZUlkID09PSAxMCkgew0KICAgICAgICAgICAgdGhpcy5yc09wQWlyLnJzQ2hhcmdlTGlzdC5wdXNoKGNoYXJnZVBheSkNCiAgICAgICAgICAgIHRoaXMuZm9ybS5yc09wQWlyTGlzdC5tYXAoaXRlbSA9PiB7DQogICAgICAgICAgICAgIGl0ZW0ucnNDaGFyZ2VMaXN0LnB1c2goY2hhcmdlUGF5KQ0KICAgICAgICAgICAgfSkNCiAgICAgICAgICB9DQogICAgICAgICAgaWYgKHFmLnNlcnZpY2VUeXBlSWQgPT09IDIwKSB7DQogICAgICAgICAgICB0aGlzLnJzT3BSYWlsRkNMLnJzQ2hhcmdlTGlzdC5wdXNoKGNoYXJnZVBheSkNCiAgICAgICAgICB9DQogICAgICAgICAgaWYgKHFmLnNlcnZpY2VUeXBlSWQgPT09IDIxKSB7DQogICAgICAgICAgICB0aGlzLnJzT3BSYWlsTENMLnJzQ2hhcmdlTGlzdC5wdXNoKGNoYXJnZVBheSkNCiAgICAgICAgICB9DQogICAgICAgICAgaWYgKHFmLnNlcnZpY2VUeXBlSWQgPT09IDQwKSB7DQogICAgICAgICAgICB0aGlzLnJzT3BFeHByZXNzLnJzQ2hhcmdlTGlzdC5wdXNoKGNoYXJnZVBheSkNCiAgICAgICAgICB9DQogICAgICAgICAgaWYgKHFmLnNlcnZpY2VUeXBlSWQgPT09IDUwKSB7DQogICAgICAgICAgICB0aGlzLnJzT3BDdG5yVHJ1Y2sucnNDaGFyZ2VMaXN0LnB1c2goY2hhcmdlUGF5KQ0KICAgICAgICAgICAgdGhpcy5mb3JtLnJzT3BDdG5yVHJ1Y2tMaXN0Lm1hcChpdGVtID0+IHsNCiAgICAgICAgICAgICAgaXRlbS5yc0NoYXJnZUxpc3QucHVzaChjaGFyZ2VQYXkpDQogICAgICAgICAgICB9KQ0KICAgICAgICAgIH0NCiAgICAgICAgICBpZiAocWYuc2VydmljZVR5cGVJZCA9PT0gNTEpIHsNCiAgICAgICAgICAgIHRoaXMucnNPcEJ1bGtUcnVjay5yc0NoYXJnZUxpc3QucHVzaChjaGFyZ2VQYXkpDQogICAgICAgICAgICB0aGlzLmZvcm0ucnNPcEJ1bGtUcnVja0xpc3QubWFwKGl0ZW0gPT4gew0KICAgICAgICAgICAgICBpdGVtLnJzQ2hhcmdlTGlzdC5wdXNoKGNoYXJnZVBheSkNCiAgICAgICAgICAgIH0pDQogICAgICAgICAgfQ0KICAgICAgICAgIGlmIChxZi5zZXJ2aWNlVHlwZUlkID09PSA2MCkgew0KICAgICAgICAgICAgdGhpcy5yc09wRG9jRGVjbGFyZS5yc0NoYXJnZUxpc3QucHVzaChjaGFyZ2VQYXkpDQogICAgICAgICAgICB0aGlzLmZvcm0ucnNPcERvY0RlY2xhcmVMaXN0Lm1hcChpdGVtID0+IHsNCiAgICAgICAgICAgICAgaXRlbS5yc0NoYXJnZUxpc3QucHVzaChjaGFyZ2VQYXkpDQogICAgICAgICAgICB9KQ0KICAgICAgICAgIH0NCiAgICAgICAgICBpZiAocWYuc2VydmljZVR5cGVJZCA9PT0gNjEpIHsNCiAgICAgICAgICAgIHRoaXMucnNPcEZyZWVEZWNsYXJlLnJzQ2hhcmdlTGlzdC5wdXNoKGNoYXJnZVBheSkNCiAgICAgICAgICAgIHRoaXMuZm9ybS5yc09wRnJlZURlY2xhcmVMaXN0Lm1hcChpdGVtID0+IHsNCiAgICAgICAgICAgICAgaXRlbS5yc0NoYXJnZUxpc3QucHVzaChjaGFyZ2VQYXkpDQogICAgICAgICAgICB9KQ0KICAgICAgICAgIH0NCiAgICAgICAgICBpZiAocWYuc2VydmljZVR5cGVJZCA9PT0gNzApIHsNCiAgICAgICAgICAgIHRoaXMucnNPcERPQWdlbnQucnNDaGFyZ2VMaXN0LnB1c2goY2hhcmdlUGF5KQ0KICAgICAgICAgIH0NCiAgICAgICAgICBpZiAocWYuc2VydmljZVR5cGVJZCA9PT0gNzEpIHsNCiAgICAgICAgICAgIHRoaXMucnNPcENsZWFyQWdlbnQucnNDaGFyZ2VMaXN0LnB1c2goY2hhcmdlUGF5KQ0KICAgICAgICAgIH0NCiAgICAgICAgICBpZiAocWYuc2VydmljZVR5cGVJZCA9PT0gODApIHsNCiAgICAgICAgICAgIHRoaXMucnNPcFdIUy5yc0NoYXJnZUxpc3QucHVzaChjaGFyZ2VQYXkpDQogICAgICAgICAgfQ0KICAgICAgICAgIGlmIChxZi5zZXJ2aWNlVHlwZUlkID09PSA5MCkgew0KICAgICAgICAgICAgdGhpcy5yc09wM3JkQ2VydC5yc0NoYXJnZUxpc3QucHVzaChjaGFyZ2VQYXkpDQogICAgICAgICAgfQ0KICAgICAgICAgIGlmIChxZi5zZXJ2aWNlVHlwZUlkID09PSAxMDApIHsNCiAgICAgICAgICAgIHRoaXMucnNPcElOUy5yc0NoYXJnZUxpc3QucHVzaChjaGFyZ2VQYXkpDQogICAgICAgICAgfQ0KICAgICAgICAgIGlmIChxZi5zZXJ2aWNlVHlwZUlkID09PSAxMDEpIHsNCiAgICAgICAgICAgIHRoaXMucnNPcFRyYWRpbmcucnNDaGFyZ2VMaXN0LnB1c2goY2hhcmdlUGF5KQ0KICAgICAgICAgIH0NCiAgICAgICAgICBpZiAocWYuc2VydmljZVR5cGVJZCA9PT0gMTAyKSB7DQogICAgICAgICAgICB0aGlzLnJzT3BGdW1pZ2F0aW9uLnJzQ2hhcmdlTGlzdC5wdXNoKGNoYXJnZVBheSkNCiAgICAgICAgICB9DQogICAgICAgICAgaWYgKHFmLnNlcnZpY2VUeXBlSWQgPT09IDEwMykgew0KICAgICAgICAgICAgdGhpcy5yc09wQ08ucnNDaGFyZ2VMaXN0LnB1c2goY2hhcmdlUGF5KQ0KICAgICAgICAgIH0NCiAgICAgICAgICBpZiAocWYuc2VydmljZVR5cGVJZCA9PT0gMTA0KSB7DQogICAgICAgICAgICB0aGlzLnJzT3BPdGhlci5yc0NoYXJnZUxpc3QucHVzaChjaGFyZ2VQYXkpDQogICAgICAgICAgfQ0KDQogICAgICAgICAgdGhpcy5yc0NsaWVudE1lc3NhZ2UucnNDaGFyZ2VMaXN0LnB1c2goY2hhcmdlUmVjZWl2ZSkNCiAgICAgICAgfQ0KICAgICAgfSkNCiAgICB9LA0KICAgIGdlbmVyYXRlRnJlaWdodCh0eXBlLCBzZXJ2aWNlVHlwZUlkLCBpdGVtKSB7DQogICAgICBpZiAoIXRoaXMuZm9ybS5yZXZlbnVlVG9uKSB7DQogICAgICAgIHRoaXMuJG1vZGFsLm1zZ1dhcm5pbmcoIuivt+WFiOW9leWFpeiuoei0uei0p+mHjyIpDQogICAgICAgIHJldHVybg0KICAgICAgfQ0KDQogICAgICB0aGlzLmN1ckZyZWlnaHRTZWxlY3RSb3cgPSBpdGVtDQoNCiAgICAgIHRoaXMuZnJlaWdodFNlbGVjdERhdGEudHlwZUlkID0gdHlwZQ0KICAgICAgaWYgKHR5cGUgIT0gNSkgew0KICAgICAgICB0aGlzLmZyZWlnaHRTZWxlY3REYXRhLmRlc3RpbmF0aW9uUG9ydElkID0gdGhpcy5mb3JtLmRlc3RpbmF0aW9uUG9ydElkDQogICAgICB9DQogICAgICBpZiAodHlwZSA9PSA1KSB7DQogICAgICAgIHRoaXMuZnJlaWdodFNlbGVjdERhdGEucHJlY2FycmlhZ2VSZWdpb25JZCA9IHRoaXMuZm9ybS5wcmVjYXJyaWFnZVJlZ2lvbklkDQogICAgICB9DQogICAgICB0aGlzLmZyZWlnaHRTZWxlY3REYXRhLnBvbElkID0gdGhpcy5mb3JtLnBvbElkDQogICAgICB0aGlzLmZyZWlnaHRTZWxlY3REYXRhLnNlcnZpY2VUeXBlSWQgPSBzZXJ2aWNlVHlwZUlkDQogICAgICB0aGlzLmZyZWlnaHRTZWxlY3REYXRhLnJldmVudWVUb25MaXN0ID0gdGhpcy5mb3JtLnJldmVudWVUb24uc3BsaXQoIisiKQ0KICAgICAgdGhpcy5mcmVpZ2h0U2VsZWN0RGF0YS5sb2NhdGlvbk9wdGlvbnMgPSB0aGlzLmxvY2F0aW9uT3B0aW9ucw0KDQogICAgICB0aGlzLm9wZW5GcmVpZ2h0U2VsZWN0ID0gdHJ1ZQ0KICAgIH0sDQogICAgY3VycmVuY3ksDQogICAgY2hlY2tQZXJtaSwNCiAgICBhc3luYyBnZXRSY3REZXRhaWwoaWQpIHsNCiAgICAgIHRoaXMucmVzZXQoKQ0KICAgICAgYXdhaXQgZ2V0UmN0KGlkKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgY29uc29sZS5sb2cocmVzcG9uc2UpDQogICAgICAgIGlmIChyZXNwb25zZS5vdXRib3VuZFJlY29yZCkgew0KICAgICAgICAgIHRoaXMub3V0Ym91bmRGb3JtID0gcmVzcG9uc2Uub3V0Ym91bmRSZWNvcmQNCiAgICAgICAgfQ0KDQogICAgICAgIHRoaXMuZm9ybSA9IHJlc3BvbnNlLmRhdGENCg0KICAgICAgICB0aGlzLnNob3dQc2FSY3QgPSByZXNwb25zZS5kYXRhLnBzYVJjdElkID8gdHJ1ZSA6IGZhbHNlDQoNCiAgICAgICAgdGhpcy5mb3JtLnNlcnZpY2VUeXBlSWRzID0gcmVzcG9uc2UuZGF0YS5zZXJ2aWNlVHlwZUlkcyA/IHJlc3BvbnNlLmRhdGEuc2VydmljZVR5cGVJZHMgOiB0aGlzLmZvcm0uc2VydmljZVR5cGVJZExpc3Quc3BsaXQoIiwiKQ0KDQogICAgICAgIHRoaXMuYm9va2luZ01lc3NhZ2VMaXN0ID0gcmVzcG9uc2UuZGF0YS5ib29raW5nTWVzc2FnZXNMaXN0ID8gcmVzcG9uc2UuZGF0YS5ib29raW5nTWVzc2FnZXNMaXN0IDogW10NCg0KICAgICAgICB0aGlzLmZvcm0ubm9UcmFuc2ZlckFsbG93ZWQgPSByZXNwb25zZS5kYXRhLm5vVHJhbnNmZXJBbGxvd2VkID09PSAiMSINCiAgICAgICAgdGhpcy5mb3JtLm5vRGl2aWRlZEFsbG93ZWQgPSByZXNwb25zZS5kYXRhLm5vRGl2aWRlZEFsbG93ZWQgPT09ICIxIg0KICAgICAgICB0aGlzLmZvcm0ubm9BZ3JlZW1lbnRTaG93ZWQgPSByZXNwb25zZS5kYXRhLm5vQWdyZWVtZW50U2hvd2VkID09PSAiMSINCiAgICAgICAgdGhpcy5mb3JtLmlzQ3VzdG9tc0ludHJhbnNpdFNob3dlZCA9IHJlc3BvbnNlLmRhdGEuaXNDdXN0b21zSW50cmFuc2l0U2hvd2VkID09PSAiMSINCiAgICAgICAgdGhpcy5ncm9zc1dlaWdodCA9IHJlc3BvbnNlLmRhdGEuZ3Jvc3NXZWlnaHQNCiAgICAgICAgdGhpcy5nb29kc1ZhbHVlID0gcmVzcG9uc2UuZGF0YS5nb29kc1ZhbHVlDQogICAgICAgIGxldCBjSWRzID0gbmV3IFNldCgpDQogICAgICAgIGxldCByciA9IFtdDQogICAgICAgIC8vIOWFs+iBlOWuouaItw0KICAgICAgICBpZiAocmVzcG9uc2UuZGF0YS5yZWxhdGlvbkNsaWVudElkcykgew0KICAgICAgICAgIHJlc3BvbnNlLmRhdGEucmVsYXRpb25DbGllbnRJZHMuc3BsaXQoIiwiKS5mb3JFYWNoKHYgPT4gew0KICAgICAgICAgICAgcnIucHVzaChOdW1iZXIodikpDQogICAgICAgICAgfSkNCiAgICAgICAgfQ0KICAgICAgICB0aGlzLnJlbGF0aW9uQ2xpZW50SWRzID0gcnINCiAgICAgICAgdGhpcy5mb3JtLnJlbGF0aW9uQ2xpZW50SWRzID0gcnINCiAgICAgICAgaWYgKHRoaXMuYmVsb25nTGlzdCAhPSB1bmRlZmluZWQpIHsNCiAgICAgICAgICBmb3IgKGNvbnN0IGEgb2YgdGhpcy5iZWxvbmdMaXN0KSB7DQogICAgICAgICAgICBpZiAoYS5jaGlsZHJlbiAhPSB1bmRlZmluZWQpIHsNCiAgICAgICAgICAgICAgZm9yIChjb25zdCBiIG9mIGEuY2hpbGRyZW4pIHsNCiAgICAgICAgICAgICAgICBpZiAoYi5jaGlsZHJlbiAhPSB1bmRlZmluZWQpIHsNCiAgICAgICAgICAgICAgICAgIGZvciAoY29uc3QgYyBvZiBiLmNoaWxkcmVuKSB7DQogICAgICAgICAgICAgICAgICAgIGlmIChjLnN0YWZmSWQgPT0gcmVzcG9uc2UuZGF0YS5zYWxlc0lkKSB7DQogICAgICAgICAgICAgICAgICAgICAgdGhpcy5zYWxlc0lkID0gYy5kZXB0SWQNCiAgICAgICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgICAgICBpZiAoYy5zdGFmZklkID09IHJlc3BvbnNlLmRhdGEuc2FsZXNBc3Npc3RhbnRJZCkgew0KICAgICAgICAgICAgICAgICAgICAgIHRoaXMuc2FsZXNBc3Npc3RhbnRJZCA9IGMuZGVwdElkDQogICAgICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICAgICAgaWYgKGMuc3RhZmZJZCA9PSByZXNwb25zZS5kYXRhLnNhbGVzT2JzZXJ2ZXJJZCkgew0KICAgICAgICAgICAgICAgICAgICAgIHRoaXMuc2FsZXNPYnNlcnZlcklkID0gYy5kZXB0SWQNCiAgICAgICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgICBpZiAodGhpcy5vcExpc3QgIT0gdW5kZWZpbmVkKSB7DQogICAgICAgICAgZm9yIChjb25zdCBhIG9mIHRoaXMub3BMaXN0KSB7DQogICAgICAgICAgICBpZiAoYS5zdGFmZklkID09IHJlc3BvbnNlLmRhdGEub3BJZCkgew0KICAgICAgICAgICAgICB0aGlzLm9wSWQgPSBhLnJvbGVJZA0KICAgICAgICAgICAgfQ0KDQogICAgICAgICAgICBpZiAoYS5jaGlsZHJlbiAhPSB1bmRlZmluZWQpIHsNCiAgICAgICAgICAgICAgZm9yIChjb25zdCBiIG9mIGEuY2hpbGRyZW4pIHsNCiAgICAgICAgICAgICAgICBpZiAoYS5yb2xlLnJvbGVMb2NhbE5hbWUgPT0gIuaTjeS9nOWRmCIgJiYgYi5zdGFmZklkID09IHJlc3BvbnNlLmRhdGEub3BJZCkgew0KICAgICAgICAgICAgICAgICAgdGhpcy5vcElkID0gYi5yb2xlSWQNCiAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgICAgaWYgKGEucm9sZS5yb2xlTG9jYWxOYW1lID09ICLorqLoiLHlkZgiICYmIGIuc3RhZmZJZCA9PSByZXNwb25zZS5kYXRhLmJvb2tpbmdPcElkKSB7DQogICAgICAgICAgICAgICAgICB0aGlzLmJvb2tpbmdPcElkID0gYi5yb2xlSWQNCiAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgICAgaWYgKGEucm9sZS5yb2xlTG9jYWxOYW1lID09ICLljZXor4HlkZgiICYmIGIuc3RhZmZJZCA9PSByZXNwb25zZS5kYXRhLmRvY09wSWQpIHsNCiAgICAgICAgICAgICAgICAgIHRoaXMuZG9jT3BJZCA9IGIucm9sZUlkDQogICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgIGlmIChiLnN0YWZmSWQgPT0gcmVzcG9uc2UuZGF0YS5vcE9ic2VydmVySWQpIHsNCiAgICAgICAgICAgICAgICAgIHRoaXMub3BPYnNlcnZlcklkID0gYi5yb2xlSWQNCiAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgIH0NCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9DQogICAgICAgIH0NCg0KICAgICAgICB0aGlzLnZlcmlmeVBzYUlkID0gcmVzcG9uc2UuZGF0YS52ZXJpZnlQc2FJZA0KICAgICAgICB0aGlzLm9wSWQgPSByZXNwb25zZS5kYXRhLm9wSWQNCg0KICAgICAgICBpZiAocmVzcG9uc2UuZGF0YS5yc0NsaWVudE1lc3NhZ2UgIT0gbnVsbCkgew0KICAgICAgICAgIHRoaXMucnNDbGllbnRNZXNzYWdlID0gcmVzcG9uc2UuZGF0YS5yc0NsaWVudE1lc3NhZ2UNCiAgICAgICAgICB0aGlzLnJzQ2xpZW50U2VydmljZUluc3RhbmNlID0gcmVzcG9uc2UuZGF0YS5yc0NsaWVudE1lc3NhZ2UucnNTZXJ2aWNlSW5zdGFuY2VzDQogICAgICAgIH0NCiAgICAgICAgaWYgKHJlc3BvbnNlLmRhdGEuc2VydmljZVR5cGVJZHMuaW5kZXhPZigyMCkgIT09IC0xICYmIHJlc3BvbnNlLmRhdGEucnNPcFJhaWxGQ0wgIT09IG51bGwpIHsNCiAgICAgICAgICB0aGlzLnJzT3BSYWlsRkNMID0gcmVzcG9uc2UuZGF0YS5yc09wUmFpbEZDTA0KICAgICAgICAgIHRoaXMucnNPcFJhaWxGY2xTZXJ2aWNlSW5zdGFuY2UgPSByZXNwb25zZS5kYXRhLnJzT3BSYWlsRkNMLnJzU2VydmljZUluc3RhbmNlcw0KICAgICAgICB9DQogICAgICAgIGlmIChyZXNwb25zZS5kYXRhLnNlcnZpY2VUeXBlSWRzLmluZGV4T2YoMjEpICE9PSAtMSAmJiByZXNwb25zZS5kYXRhLnJzT3BSYWlsTENMICE9PSBudWxsKSB7DQogICAgICAgICAgdGhpcy5yc09wUmFpbExDTCA9IHJlc3BvbnNlLmRhdGEucnNPcFJhaWxMQ0wNCiAgICAgICAgICB0aGlzLnJzT3BSYWlsTGNsU2VydmljZUluc3RhbmNlID0gcmVzcG9uc2UuZGF0YS5yc09wUmFpbExDTC5yc1NlcnZpY2VJbnN0YW5jZXMNCiAgICAgICAgfQ0KICAgICAgICBpZiAocmVzcG9uc2UuZGF0YS5zZXJ2aWNlVHlwZUlkcy5pbmRleE9mKDQwKSAhPT0gLTEgJiYgcmVzcG9uc2UuZGF0YS5yc09wRXhwcmVzcyAhPT0gbnVsbCkgew0KICAgICAgICAgIHRoaXMucnNPcEV4cHJlc3MgPSByZXNwb25zZS5kYXRhLnJzT3BFeHByZXNzDQogICAgICAgICAgdGhpcy5yc09wRXhwcmVzc1NlcnZpY2VJbnN0YW5jZSA9IHJlc3BvbnNlLmRhdGEucnNPcEV4cHJlc3MucnNTZXJ2aWNlSW5zdGFuY2VzDQogICAgICAgIH0NCiAgICAgICAgaWYgKHJlc3BvbnNlLmRhdGEuc2VydmljZVR5cGVJZHMuaW5kZXhPZig3MCkgIT09IC0xICYmIHJlc3BvbnNlLmRhdGEucnNPcERPQWdlbnQgIT09IG51bGwpIHsNCiAgICAgICAgICB0aGlzLnJzT3BET0FnZW50ID0gcmVzcG9uc2UuZGF0YS5yc09wRE9BZ2VudA0KICAgICAgICAgIHRoaXMucnNPcERPQWdlbnRTZXJ2aWNlSW5zdGFuY2UgPSByZXNwb25zZS5kYXRhLnJzT3BET0FnZW50LnJzU2VydmljZUluc3RhbmNlcw0KICAgICAgICB9DQogICAgICAgIGlmIChyZXNwb25zZS5kYXRhLnNlcnZpY2VUeXBlSWRzLmluZGV4T2YoNzEpICE9PSAtMSAmJiByZXNwb25zZS5kYXRhLnJzT3BDbGVhckFnZW50ICE9PSBudWxsKSB7DQogICAgICAgICAgdGhpcy5yc09wQ2xlYXJBZ2VudCA9IHJlc3BvbnNlLmRhdGEucnNPcENsZWFyQWdlbnQNCiAgICAgICAgICB0aGlzLnJzT3BDbGVhckFnZW50U2VydmljZUluc3RhbmNlID0gcmVzcG9uc2UuZGF0YS5yc09wQ2xlYXJBZ2VudC5yc1NlcnZpY2VJbnN0YW5jZXMNCiAgICAgICAgfQ0KICAgICAgICBpZiAocmVzcG9uc2UuZGF0YS5zZXJ2aWNlVHlwZUlkcy5pbmRleE9mKDgwKSAhPT0gLTEgJiYgcmVzcG9uc2UuZGF0YS5yc09wV0hTICE9PSBudWxsKSB7DQogICAgICAgICAgdGhpcy5yc09wV0hTID0gcmVzcG9uc2UuZGF0YS5yc09wV0hTDQogICAgICAgICAgdGhpcy5yc09wV0hTU2VydmljZUluc3RhbmNlID0gcmVzcG9uc2UuZGF0YS5yc09wV0hTLnJzU2VydmljZUluc3RhbmNlcw0KICAgICAgICB9DQogICAgICAgIGlmIChyZXNwb25zZS5kYXRhLnNlcnZpY2VUeXBlSWRzLmluZGV4T2YoOTApICE9PSAtMSAmJiByZXNwb25zZS5kYXRhLnJzT3AzcmRDZXJ0ICE9PSBudWxsKSB7DQogICAgICAgICAgdGhpcy5yc09wM3JkQ2VydCA9IHJlc3BvbnNlLmRhdGEucnNPcDNyZENlcnQNCiAgICAgICAgICB0aGlzLnJzT3AzcmRDZXJ0U2VydmljZUluc3RhbmNlID0gcmVzcG9uc2UuZGF0YS5yc09wM3JkQ2VydC5yc1NlcnZpY2VJbnN0YW5jZXMNCiAgICAgICAgfQ0KDQogICAgICAgIGlmIChyZXNwb25zZS5kYXRhLnNlcnZpY2VUeXBlSWRzLmluZGV4T2YoMTAwKSAhPT0gLTEgJiYgcmVzcG9uc2UuZGF0YS5yc09wSU5TICE9PSBudWxsKSB7DQogICAgICAgICAgdGhpcy5yc09wSU5TID0gcmVzcG9uc2UuZGF0YS5yc09wSU5TDQogICAgICAgICAgdGhpcy5yc09wSU5TU2VydmljZUluc3RhbmNlID0gcmVzcG9uc2UuZGF0YS5yc09wSU5TLnJzU2VydmljZUluc3RhbmNlcw0KICAgICAgICB9DQogICAgICAgIGlmIChyZXNwb25zZS5kYXRhLnNlcnZpY2VUeXBlSWRzLmluZGV4T2YoMTAxKSAhPT0gLTEgJiYgcmVzcG9uc2UuZGF0YS5yc09wVHJhZGluZyAhPT0gbnVsbCkgew0KICAgICAgICAgIHRoaXMucnNPcFRyYWRpbmcgPSByZXNwb25zZS5kYXRhLnJzT3BUcmFkaW5nDQogICAgICAgICAgdGhpcy5yc09wVHJhZGluZ1NlcnZpY2VJbnN0YW5jZSA9IHJlc3BvbnNlLmRhdGEucnNPcFRyYWRpbmcucnNTZXJ2aWNlSW5zdGFuY2VzDQogICAgICAgIH0NCiAgICAgICAgaWYgKHJlc3BvbnNlLmRhdGEuc2VydmljZVR5cGVJZHMuaW5kZXhPZigxMDIpICE9PSAtMSAmJiByZXNwb25zZS5kYXRhLnJzT3BGdW1pZ2F0aW9uICE9PSBudWxsKSB7DQogICAgICAgICAgdGhpcy5yc09wRnVtaWdhdGlvbiA9IHJlc3BvbnNlLmRhdGEucnNPcEZ1bWlnYXRpb24NCiAgICAgICAgICB0aGlzLnJzT3BGdW1pZ2F0aW9uU2VydmljZUluc3RhbmNlID0gcmVzcG9uc2UuZGF0YS5yc09wRnVtaWdhdGlvbi5yc1NlcnZpY2VJbnN0YW5jZXMNCiAgICAgICAgfQ0KICAgICAgICBpZiAocmVzcG9uc2UuZGF0YS5zZXJ2aWNlVHlwZUlkcy5pbmRleE9mKDEwMykgIT09IC0xICYmIHJlc3BvbnNlLmRhdGEucnNPcENPICE9PSBudWxsKSB7DQogICAgICAgICAgdGhpcy5yc09wQ08gPSByZXNwb25zZS5kYXRhLnJzT3BDTw0KICAgICAgICAgIHRoaXMucnNPcENPU2VydmljZUluc3RhbmNlID0gcmVzcG9uc2UuZGF0YS5yc09wQ08ucnNTZXJ2aWNlSW5zdGFuY2VzDQogICAgICAgIH0NCiAgICAgICAgaWYgKHJlc3BvbnNlLmRhdGEuc2VydmljZVR5cGVJZHMuaW5kZXhPZigxMDQpICE9PSAtMSAmJiByZXNwb25zZS5kYXRhLnJzT3BPdGhlciAhPT0gbnVsbCkgew0KICAgICAgICAgIHRoaXMucnNPcE90aGVyID0gcmVzcG9uc2UuZGF0YS5yc09wT3RoZXINCiAgICAgICAgICB0aGlzLnJzT3BPdGhlclNlcnZpY2VJbnN0YW5jZSA9IHJlc3BvbnNlLmRhdGEucnNPcE90aGVyLnJzU2VydmljZUluc3RhbmNlcw0KICAgICAgICB9DQoNCiAgICAgICAgLy8g5a6i5oi35L+h5oGv5a6h5qC4DQogICAgICAgIHRoaXMub3BDb25maXJtZWROYW1lID0gcmVzcG9uc2UuZGF0YS5yc0NsaWVudE1lc3NhZ2UucnNTZXJ2aWNlSW5zdGFuY2VzLmlzRG5PcENvbmZpcm1lZCA/IHRoaXMuJHN0b3JlLnN0YXRlLmRhdGEuYWxsUnNTdGFmZkxpc3QuZmlsdGVyKHJzU3RhZmYgPT4gcnNTdGFmZi5zdGFmZklkID09IHJlc3BvbnNlLmRhdGEucnNDbGllbnRNZXNzYWdlLnJzU2VydmljZUluc3RhbmNlcy5pc0RuT3BDb25maXJtZWQpWzBdLnN0YWZmRmFtaWx5TG9jYWxOYW1lICsgIiIgKyB0aGlzLiRzdG9yZS5zdGF0ZS5kYXRhLmFsbFJzU3RhZmZMaXN0LmZpbHRlcihyc1N0YWZmID0+IHJzU3RhZmYuc3RhZmZJZCA9PSByZXNwb25zZS5kYXRhLnJzQ2xpZW50TWVzc2FnZS5yc1NlcnZpY2VJbnN0YW5jZXMuaXNEbk9wQ29uZmlybWVkKVswXS5zdGFmZkdpdmluZ0xvY2FsTmFtZSA6IG51bGwNCiAgICAgICAgdGhpcy5vcENvbmZpcm1lZERhdGUgPSByZXNwb25zZS5kYXRhLnJzQ2xpZW50TWVzc2FnZS5yc1NlcnZpY2VJbnN0YW5jZXMub3BDb25maXJtZWRUaW1lID8gcmVzcG9uc2UuZGF0YS5yc0NsaWVudE1lc3NhZ2UucnNTZXJ2aWNlSW5zdGFuY2VzLm9wQ29uZmlybWVkVGltZSA6IG51bGwNCiAgICAgICAgdGhpcy5hY2NvdW50Q29uZmlybWVkTmFtZSA9IHJlc3BvbnNlLmRhdGEucnNDbGllbnRNZXNzYWdlLnJzU2VydmljZUluc3RhbmNlcy5pc0FjY291bnRDb25maXJtZWQgPyB0aGlzLiRzdG9yZS5zdGF0ZS5kYXRhLmFsbFJzU3RhZmZMaXN0LmZpbHRlcihyc1N0YWZmID0+IHJzU3RhZmYuc3RhZmZJZCA9PSByZXNwb25zZS5kYXRhLnJzQ2xpZW50TWVzc2FnZS5yc1NlcnZpY2VJbnN0YW5jZXMuaXNBY2NvdW50Q29uZmlybWVkKVswXS5zdGFmZkZhbWlseUxvY2FsTmFtZSArICIiICsgdGhpcy4kc3RvcmUuc3RhdGUuZGF0YS5hbGxSc1N0YWZmTGlzdC5maWx0ZXIocnNTdGFmZiA9PiByc1N0YWZmLnN0YWZmSWQgPT0gcmVzcG9uc2UuZGF0YS5yc0NsaWVudE1lc3NhZ2UucnNTZXJ2aWNlSW5zdGFuY2VzLmlzQWNjb3VudENvbmZpcm1lZClbMF0uc3RhZmZHaXZpbmdMb2NhbE5hbWUgOiBudWxsDQogICAgICAgIHRoaXMuYWNjb3VudENvbmZpcm1lZERhdGUgPSByZXNwb25zZS5kYXRhLnJzQ2xpZW50TWVzc2FnZS5yc1NlcnZpY2VJbnN0YW5jZXMuYWNjb3VudENvbmZpcm1UaW1lID8gcmVzcG9uc2UuZGF0YS5yc0NsaWVudE1lc3NhZ2UucnNTZXJ2aWNlSW5zdGFuY2VzLmFjY291bnRDb25maXJtVGltZSA6IG51bGwNCiAgICAgICAgdGhpcy5jbGllbnRDb25maXJtZWROYW1lID0gcmVzcG9uc2UuZGF0YS5yc0NsaWVudE1lc3NhZ2UucnNTZXJ2aWNlSW5zdGFuY2VzLmlzRG5DbGllbnRDb25maXJtZWQgPyB0aGlzLiRzdG9yZS5zdGF0ZS5kYXRhLmFsbFJzU3RhZmZMaXN0LmZpbHRlcihyc1N0YWZmID0+IHJzU3RhZmYuc3RhZmZJZCA9PSByZXNwb25zZS5kYXRhLnJzQ2xpZW50TWVzc2FnZS5yc1NlcnZpY2VJbnN0YW5jZXMuaXNEbkNsaWVudENvbmZpcm1lZClbMF0uc3RhZmZGYW1pbHlMb2NhbE5hbWUgKyAiIiArIHRoaXMuJHN0b3JlLnN0YXRlLmRhdGEuYWxsUnNTdGFmZkxpc3QuZmlsdGVyKHJzU3RhZmYgPT4gcnNTdGFmZi5zdGFmZklkID09IHJlc3BvbnNlLmRhdGEucnNDbGllbnRNZXNzYWdlLnJzU2VydmljZUluc3RhbmNlcy5pc0RuQ2xpZW50Q29uZmlybWVkKVswXS5zdGFmZkdpdmluZ0xvY2FsTmFtZSA6IG51bGwNCiAgICAgICAgdGhpcy5jbGllbnRDb25maXJtZWREYXRlID0gcmVzcG9uc2UuZGF0YS5yc0NsaWVudE1lc3NhZ2UucnNTZXJ2aWNlSW5zdGFuY2VzLmNsaWVudENvbmZpcm1lZFRpbWUgPyByZXNwb25zZS5kYXRhLnJzQ2xpZW50TWVzc2FnZS5yc1NlcnZpY2VJbnN0YW5jZXMuY2xpZW50Q29uZmlybWVkVGltZSA6IG51bGwNCiAgICAgICAgdGhpcy5zYWxlc0NvbmZpcm1lZE5hbWUgPSByZXNwb25zZS5kYXRhLnJzQ2xpZW50TWVzc2FnZS5yc1NlcnZpY2VJbnN0YW5jZXMuaXNEblNhbGVzQ29uZmlybWVkID8gdGhpcy4kc3RvcmUuc3RhdGUuZGF0YS5hbGxSc1N0YWZmTGlzdC5maWx0ZXIocnNTdGFmZiA9PiByc1N0YWZmLnN0YWZmSWQgPT0gcmVzcG9uc2UuZGF0YS5yc0NsaWVudE1lc3NhZ2UucnNTZXJ2aWNlSW5zdGFuY2VzLmlzRG5TYWxlc0NvbmZpcm1lZClbMF0uc3RhZmZGYW1pbHlMb2NhbE5hbWUgKyAiIiArIHRoaXMuJHN0b3JlLnN0YXRlLmRhdGEuYWxsUnNTdGFmZkxpc3QuZmlsdGVyKHJzU3RhZmYgPT4gcnNTdGFmZi5zdGFmZklkID09IHJlc3BvbnNlLmRhdGEucnNDbGllbnRNZXNzYWdlLnJzU2VydmljZUluc3RhbmNlcy5pc0RuU2FsZXNDb25maXJtZWQpWzBdLnN0YWZmR2l2aW5nTG9jYWxOYW1lIDogbnVsbA0KICAgICAgICB0aGlzLnNhbGVzQ29uZmlybWVkRGF0ZSA9IHJlc3BvbnNlLmRhdGEucnNDbGllbnRNZXNzYWdlLnJzU2VydmljZUluc3RhbmNlcy5zYWxlc0NvbmZpcm1lZFRpbWUgPyByZXNwb25zZS5kYXRhLnJzQ2xpZW50TWVzc2FnZS5yc1NlcnZpY2VJbnN0YW5jZXMuc2FsZXNDb25maXJtZWRUaW1lIDogbnVsbA0KDQogICAgICAgIHRoaXMubG9jYXRpb25PcHRpb25zID0gcmVzcG9uc2UubG9jYXRpb25PcHRpb25zDQogICAgICAgIHRoaXMuUmVsYXRpb25DbGllbnRJZExpc3QgPSBbXQ0KICAgICAgICByZXNwb25zZS5kYXRhLnJlbGF0aW9uQ2xpZW50SWRMaXN0ID8gcmVzcG9uc2UuZGF0YS5yZWxhdGlvbkNsaWVudElkTGlzdC5zcGxpdCgiLCIpLm1hcCh2ID0+IHRoaXMuUmVsYXRpb25DbGllbnRJZExpc3QucHVzaChOdW1iZXIodikpKSA6IFtdDQoNCiAgICAgICAgdGhpcy5yZWxhdGlvbkNsaWVudExpc3RzLnB1c2gocmVzcG9uc2UuY29tcGFueUxpc3QpDQogICAgICAgIHRoaXMuY29tcGFueUxpc3QgPSByZXNwb25zZS5jb21wYW55TGlzdC5maWx0ZXIodiA9PiB2LmNvbXBhbnlJZCA9PT0gcmVzcG9uc2UuZGF0YS5jbGllbnRJZCkuY29uY2F0KHJlc3BvbnNlLmNvbXBhbnlMaXN0LmZpbHRlcih2ID0+IHYuY29tcGFueUlkICE9PSByZXNwb25zZS5kYXRhLmNsaWVudElkKSkNCg0KICAgICAgICB0aGlzLnN1cHBsaWVyTGlzdCA9IHJlc3BvbnNlLnN1cHBsaWVyTGlzdA0KDQogICAgICAgIC8qIGxldCBjbGllbnRDb21wYW55ID0gcmVzcG9uc2UuY29tcGFueUxpc3QuZmlsdGVyKHYgPT4gdi5jb21wYW55SWQgPT09IHJlc3BvbnNlLmRhdGEuY2xpZW50SWQpLmxlbmd0aCA+IDAgPyByZXNwb25zZS5jb21wYW55TGlzdC5maWx0ZXIodiA9PiB2LmNvbXBhbnlJZCA9PT0gcmVzcG9uc2UuZGF0YS5jbGllbnRJZClbMF0gOiBudWxsDQogICAgICAgIGlmIChjbGllbnRDb21wYW55KSB7DQogICAgICAgICAgdGhpcy5mb3JtLmNsaWVudE5hbWUgPSBjbGllbnRDb21wYW55LmNvbXBhbnlUYXhDb2RlICsgIi8iICsgY2xpZW50Q29tcGFueS5jb21wYW55U2hvcnROYW1lICsgIi8iICsgY2xpZW50Q29tcGFueS5jb21wYW55TG9jYWxOYW1lDQogICAgICAgIH0gKi8NCiAgICAgICAgdGhpcy5mb3JtLmNsaWVudE5hbWUgPSByZXNwb25zZS5kYXRhLmNsaWVudFN1bW1hcnkNCg0KICAgICAgICAvLyDnrrHlnovnibnlvoENCiAgICAgICAgdGhpcy5jdG5yVHlwZUNvZGVJZHMgPSB0aGlzLmZvcm0uY3RuclR5cGVDb2RlID8gdGhpcy5mb3JtLmN0bnJUeXBlQ29kZS5zcGxpdCgiLCIpIDogW10NCiAgICAgICAgdGhpcy5jYXJnb1R5cGVDb2RlcyA9IHRoaXMuZm9ybS5jYXJnb1R5cGVDb2RlU3VtID8gdGhpcy5mb3JtLmNhcmdvVHlwZUNvZGVTdW0uc3BsaXQoIiwiKSA6IFtdDQoNCiAgICAgICAgLy8g5oqY5Y+g5L+h5oGv5Lul5Y+K5pi+56S65L+h5oGvDQogICAgICAgIGlmIChyZXNwb25zZS5kYXRhLm1lc3NhZ2VEaXNwbGF5KSB7DQogICAgICAgICAgcmVzcG9uc2UuZGF0YS5tZXNzYWdlRGlzcGxheS5zcGxpdCgiLCIpLmZvckVhY2goKHYsIGkpID0+IHsNCiAgICAgICAgICAgIGlmIChpID09PSAwKSB2ID09IDEgPyB0aGlzLnNlcnZpY2VJbmZvID0gdHJ1ZSA6IHRoaXMuc2VydmljZUluZm8gPSBmYWxzZQ0KICAgICAgICAgICAgaWYgKGkgPT09IDEpIHYgPT0gMSA/IHRoaXMub3JkZXJJbmZvID0gdHJ1ZSA6IHRoaXMub3JkZXJJbmZvID0gZmFsc2UNCiAgICAgICAgICAgIGlmIChpID09PSAyKSB2ID09IDEgPyB0aGlzLmJyYW5jaEluZm8gPSB0cnVlIDogdGhpcy5icmFuY2hJbmZvID0gZmFsc2UNCiAgICAgICAgICAgIGlmIChpID09PSAzKSB2ID09IDEgPyB0aGlzLmxvZ2lzdGljc0luZm8gPSB0cnVlIDogdGhpcy5sb2dpc3RpY3NJbmZvID0gZmFsc2UNCiAgICAgICAgICAgIGlmIChpID09PSA0KSB2ID09IDEgPyB0aGlzLmRvY0luZm8gPSB0cnVlIDogdGhpcy5kb2NJbmZvID0gZmFsc2UNCiAgICAgICAgICAgIGlmIChpID09PSA1KSB2ID09IDEgPyB0aGlzLmNoYXJnZUluZm8gPSB0cnVlIDogdGhpcy5jaGFyZ2VJbmZvID0gZmFsc2UNCiAgICAgICAgICAgIGlmIChpID09PSA2KSB2ID09IDEgPyB0aGlzLmF1ZGl0SW5mbyA9IHRydWUgOiB0aGlzLmF1ZGl0SW5mbyA9IGZhbHNlDQogICAgICAgICAgfSkNCiAgICAgICAgfQ0KICAgICAgICBpZiAocmVzcG9uc2UuZGF0YS5zZXJ2aWNlTWVzc2FnZUZvbGQpIHsNCiAgICAgICAgICByZXNwb25zZS5kYXRhLnNlcnZpY2VNZXNzYWdlRm9sZC5zcGxpdCgiLCIpLmZvckVhY2goKHYsIGkpID0+IHsNCiAgICAgICAgICAgIGlmIChpID09PSAwKSB2ID09IDEgPyB0aGlzLmNsaWVudE1lc3NhZ2UgPSB0cnVlIDogdGhpcy5jbGllbnRNZXNzYWdlID0gZmFsc2UNCiAgICAgICAgICAgIGlmIChpID09PSAxKSB2ID09IDEgPyB0aGlzLnJzT3BTZWFsRmNsRm9sZCA9IHRydWUgOiB0aGlzLnJzT3BTZWFsRmNsRm9sZCA9IGZhbHNlDQogICAgICAgICAgICBpZiAoaSA9PT0gMikgdiA9PSAxID8gdGhpcy5yc09wU2VhbExjbEZvbGQgPSB0cnVlIDogdGhpcy5yc09wU2VhbExjbEZvbGQgPSBmYWxzZQ0KICAgICAgICAgICAgaWYgKGkgPT09IDMpIHYgPT0gMSA/IHRoaXMucnNPcEFpckZvbGQgPSB0cnVlIDogdGhpcy5yc09wQWlyRm9sZCA9IGZhbHNlDQogICAgICAgICAgICBpZiAoaSA9PT0gNCkgdiA9PSAxID8gdGhpcy5yc09wUmFpbEZjbEZvbGQgPSB0cnVlIDogdGhpcy5yc09wUmFpbEZjbEZvbGQgPSBmYWxzZQ0KICAgICAgICAgICAgaWYgKGkgPT09IDUpIHYgPT0gMSA/IHRoaXMucnNPcFJhaWxMY2xGb2xkID0gdHJ1ZSA6IHRoaXMucnNPcFJhaWxMY2xGb2xkID0gZmFsc2UNCiAgICAgICAgICAgIGlmIChpID09PSA2KSB2ID09IDEgPyB0aGlzLnJzT3BFeHByZXNzRm9sZCA9IHRydWUgOiB0aGlzLnJzT3BFeHByZXNzRm9sZCA9IGZhbHNlDQogICAgICAgICAgICBpZiAoaSA9PT0gNykgdiA9PSAxID8gdGhpcy5yc09wQ3RuclRydWNrRm9sZCA9IHRydWUgOiB0aGlzLnJzT3BDdG5yVHJ1Y2tGb2xkID0gZmFsc2UNCiAgICAgICAgICAgIGlmIChpID09PSA4KSB2ID09IDEgPyB0aGlzLnJzT3BCdWxrVHJ1Y2tGb2xkID0gdHJ1ZSA6IHRoaXMucnNPcEJ1bGtUcnVja0ZvbGQgPSBmYWxzZQ0KICAgICAgICAgICAgaWYgKGkgPT09IDkpIHYgPT0gMSA/IHRoaXMucnNPcERvY0RlY2xhcmVGb2xkID0gdHJ1ZSA6IHRoaXMucnNPcERvY0RlY2xhcmVGb2xkID0gZmFsc2UNCiAgICAgICAgICAgIGlmIChpID09PSAxMCkgdiA9PSAxID8gdGhpcy5yc09wRnJlZURlY2xhcmVGb2xkID0gdHJ1ZSA6IHRoaXMucnNPcEZyZWVEZWNsYXJlRm9sZCA9IGZhbHNlDQogICAgICAgICAgICBpZiAoaSA9PT0gMTEpIHYgPT0gMSA/IHRoaXMucnNPcERPQWdlbnRGb2xkID0gdHJ1ZSA6IHRoaXMucnNPcERPQWdlbnRGb2xkID0gZmFsc2UNCiAgICAgICAgICAgIGlmIChpID09PSAxMikgdiA9PSAxID8gdGhpcy5yc09wQ2xlYXJBZ2VudEZvbGQgPSB0cnVlIDogdGhpcy5yc09wQ2xlYXJBZ2VudEZvbGQgPSBmYWxzZQ0KICAgICAgICAgICAgaWYgKGkgPT09IDEzKSB2ID09IDEgPyB0aGlzLnJzT3BXSFNGb2xkID0gdHJ1ZSA6IHRoaXMucnNPcFdIU0ZvbGQgPSBmYWxzZQ0KICAgICAgICAgICAgaWYgKGkgPT09IDE0KSB2ID09IDEgPyB0aGlzLnJzT3AzcmRDZXJ0Rm9sZCA9IHRydWUgOiB0aGlzLnJzT3AzcmRDZXJ0Rm9sZCA9IGZhbHNlDQogICAgICAgICAgICBpZiAoaSA9PT0gMTUpIHYgPT0gMSA/IHRoaXMucnNPcElOU0ZvbGQgPSB0cnVlIDogdGhpcy5yc09wSU5TRm9sZCA9IGZhbHNlDQogICAgICAgICAgICBpZiAoaSA9PT0gMTYpIHYgPT0gMSA/IHRoaXMucnNPcFRyYWRpbmdGb2xkID0gdHJ1ZSA6IHRoaXMucnNPcFRyYWRpbmdGb2xkID0gZmFsc2UNCiAgICAgICAgICAgIGlmIChpID09PSAxNykgdiA9PSAxID8gdGhpcy5yc09wRnVtaWdhdGlvbkZvbGQgPSB0cnVlIDogdGhpcy5yc09wRnVtaWdhdGlvbkZvbGQgPSBmYWxzZQ0KICAgICAgICAgICAgaWYgKGkgPT09IDE4KSB2ID09IDEgPyB0aGlzLnJzT3BDT0ZvbGQgPSB0cnVlIDogdGhpcy5yc09wQ09Gb2xkID0gZmFsc2UNCiAgICAgICAgICAgIGlmIChpID09PSAxOSkgdiA9PSAxID8gdGhpcy5yc09wT3RoZXJGb2xkID0gdHJ1ZSA6IHRoaXMucnNPcE90aGVyRm9sZCA9IGZhbHNlDQogICAgICAgICAgfSkNCiAgICAgICAgfQ0KDQogICAgICAgIHJlc3BvbnNlLmRhdGEucnNPcFNlYUZjbExpc3QgPyByZXNwb25zZS5kYXRhLnJzT3BTZWFGY2xMaXN0Lm1hcChpdGVtID0+IHsNCiAgICAgICAgICBpdGVtLnJzU2VydmljZUluc3RhbmNlcy5zZXJ2aWNlRm9sZCA9PSAxID8gaXRlbS5yc1NlcnZpY2VJbnN0YW5jZXMuc2VydmljZUZvbGQgPSB0cnVlIDogaXRlbS5yc1NlcnZpY2VJbnN0YW5jZXMuc2VydmljZUZvbGQgPSBmYWxzZQ0KICAgICAgICAgIHJldHVybiBpdGVtDQogICAgICAgIH0pIDogbnVsbA0KICAgICAgICByZXNwb25zZS5kYXRhLnJzT3BTZWFMY2xMaXN0ID8gcmVzcG9uc2UuZGF0YS5yc09wU2VhTGNsTGlzdC5tYXAoaXRlbSA9PiB7DQogICAgICAgICAgaXRlbS5yc1NlcnZpY2VJbnN0YW5jZXMuc2VydmljZUZvbGQgPT0gMSA/IGl0ZW0ucnNTZXJ2aWNlSW5zdGFuY2VzLnNlcnZpY2VGb2xkID0gdHJ1ZSA6IGl0ZW0ucnNTZXJ2aWNlSW5zdGFuY2VzLnNlcnZpY2VGb2xkID0gZmFsc2UNCiAgICAgICAgICByZXR1cm4gaXRlbQ0KICAgICAgICB9KSA6IG51bGwNCiAgICAgICAgcmVzcG9uc2UuZGF0YS5yc09wQWlyTGlzdCA/IHJlc3BvbnNlLmRhdGEucnNPcEFpckxpc3QubWFwKGl0ZW0gPT4gew0KICAgICAgICAgIGl0ZW0ucnNTZXJ2aWNlSW5zdGFuY2VzLnNlcnZpY2VGb2xkID09IDEgPyBpdGVtLnJzU2VydmljZUluc3RhbmNlcy5zZXJ2aWNlRm9sZCA9IHRydWUgOiBpdGVtLnJzU2VydmljZUluc3RhbmNlcy5zZXJ2aWNlRm9sZCA9IGZhbHNlDQogICAgICAgICAgcmV0dXJuIGl0ZW0NCiAgICAgICAgfSkgOiBudWxsDQogICAgICAgIHJlc3BvbnNlLmRhdGEucnNPcEN0bnJUcnVja0xpc3QgPyByZXNwb25zZS5kYXRhLnJzT3BDdG5yVHJ1Y2tMaXN0Lm1hcChpdGVtID0+IHsNCiAgICAgICAgICBpdGVtLnJzU2VydmljZUluc3RhbmNlcy5zZXJ2aWNlRm9sZCA9PSAxID8gaXRlbS5yc1NlcnZpY2VJbnN0YW5jZXMuc2VydmljZUZvbGQgPSB0cnVlIDogaXRlbS5yc1NlcnZpY2VJbnN0YW5jZXMuc2VydmljZUZvbGQgPSBmYWxzZQ0KICAgICAgICAgIHJldHVybiBpdGVtDQogICAgICAgIH0pIDogbnVsbA0KICAgICAgICByZXNwb25zZS5kYXRhLnJzT3BCdWxrVHJ1Y2tMaXN0ID8gcmVzcG9uc2UuZGF0YS5yc09wQnVsa1RydWNrTGlzdC5tYXAoaXRlbSA9PiB7DQogICAgICAgICAgaXRlbS5yc1NlcnZpY2VJbnN0YW5jZXMuc2VydmljZUZvbGQgPT0gMSA/IGl0ZW0ucnNTZXJ2aWNlSW5zdGFuY2VzLnNlcnZpY2VGb2xkID0gdHJ1ZSA6IGl0ZW0ucnNTZXJ2aWNlSW5zdGFuY2VzLnNlcnZpY2VGb2xkID0gZmFsc2UNCiAgICAgICAgICByZXR1cm4gaXRlbQ0KICAgICAgICB9KSA6IG51bGwNCiAgICAgICAgcmVzcG9uc2UuZGF0YS5yc09wRG9jRGVjbGFyZUxpc3QgPyByZXNwb25zZS5kYXRhLnJzT3BEb2NEZWNsYXJlTGlzdC5tYXAoaXRlbSA9PiB7DQogICAgICAgICAgaXRlbS5yc1NlcnZpY2VJbnN0YW5jZXMuc2VydmljZUZvbGQgPT0gMSA/IGl0ZW0ucnNTZXJ2aWNlSW5zdGFuY2VzLnNlcnZpY2VGb2xkID0gdHJ1ZSA6IGl0ZW0ucnNTZXJ2aWNlSW5zdGFuY2VzLnNlcnZpY2VGb2xkID0gZmFsc2UNCiAgICAgICAgICByZXR1cm4gaXRlbQ0KICAgICAgICB9KSA6IG51bGwNCiAgICAgICAgcmVzcG9uc2UuZGF0YS5yc09wRnJlZURlY2xhcmVMaXN0ID8gcmVzcG9uc2UuZGF0YS5yc09wRnJlZURlY2xhcmVMaXN0Lm1hcChpdGVtID0+IHsNCiAgICAgICAgICBpdGVtLnJzU2VydmljZUluc3RhbmNlcy5zZXJ2aWNlRm9sZCA9PSAxID8gaXRlbS5yc1NlcnZpY2VJbnN0YW5jZXMuc2VydmljZUZvbGQgPSB0cnVlIDogaXRlbS5yc1NlcnZpY2VJbnN0YW5jZXMuc2VydmljZUZvbGQgPSBmYWxzZQ0KICAgICAgICAgIHJldHVybiBpdGVtDQogICAgICAgIH0pIDogbnVsbA0KDQogICAgICAgIC8vIOm7mOiupOaUtuWPkemAmg0KICAgICAgICAvLyB0aGlzLmZvcm0uYm9va2luZ1NoaXBwZXIgPSByZXNwb25zZS5kYXRhLmJvb2tpbmdTaGlwcGVyID8gcmVzcG9uc2UuZGF0YS5ib29raW5nU2hpcHBlciA6ICJHVUFOR1pIT1UgUklDSCBTSElQUElORyBJTlQnTCBDTy4sIExURC4iDQogICAgICAgIHRoaXMuZm9ybS5ib29raW5nU2hpcHBlciA9IHJlc3BvbnNlLmRhdGEuYm9va2luZ1NoaXBwZXINCiAgICAgICAgICA/IHJlc3BvbnNlLmRhdGEuYm9va2luZ1NoaXBwZXINCiAgICAgICAgICA6ICh0aGlzLmZvcm0ucmN0Tm8gJiYgdGhpcy5mb3JtLnJjdE5vLnN0YXJ0c1dpdGgoIkNGTCIpDQogICAgICAgICAgICA/ICJHVUFOR1pIT1UgQ0hFUklTSCBGUkVJR0hUIElOVCdMIENPLixMVEQuIg0KICAgICAgICAgICAgOiAiR1VBTkdaSE9VIFJJQ0ggU0hJUFBJTkcgSU5UJ0wgQ08uLCBMVEQuIikNCiAgICAgICAgdGhpcy5mb3JtLmJvb2tpbmdDb25zaWduZWUgPSByZXNwb25zZS5kYXRhLmJvb2tpbmdDb25zaWduZWUgPyByZXNwb25zZS5kYXRhLmJvb2tpbmdDb25zaWduZWUgOiAiVE8gT1JERVIiDQogICAgICAgIHRoaXMuZm9ybS5ib29raW5nTm90aWZ5UGFydHkgPSByZXNwb25zZS5kYXRhLmJvb2tpbmdOb3RpZnlQYXJ0eSA/IHJlc3BvbnNlLmRhdGEuYm9va2luZ05vdGlmeVBhcnR5IDogIlNBTUUgQVMgQ09OU0lHTkVFIg0KICAgICAgfSkNCiAgICB9LA0KICAgIC8vIOaTjeS9nOWNlemps+Wbng0KICAgIHR1cm5Eb3duKCkgew0KICAgICAgdGhpcy4kcmVmc1siZm9ybSJdLnZhbGlkYXRlKHZhbGlkID0+IHsNCiAgICAgICAgdGhpcy5mb3JtLm9wSWQgPSBudWxsDQogICAgICAgIHRoaXMuZm9ybS5wc2FWZXJpZnlTdGF0dXNJZCA9IDANCiAgICAgICAgdGhpcy5mb3JtLnBzYVZlcmlmeSA9IDANCiAgICAgICAgaWYgKHRoaXMuZm9ybS5yY3RJZCAhPSBudWxsKSB7DQogICAgICAgICAgdGhpcy5mb3JtLm5vVHJhbnNmZXJBbGxvd2VkID0gdGhpcy5mb3JtLm5vVHJhbnNmZXJBbGxvd2VkID8gMSA6IDANCiAgICAgICAgICB0aGlzLmZvcm0ubm9EaXZpZGVkQWxsb3dlZCA9IHRoaXMuZm9ybS5ub0RpdmlkZWRBbGxvd2VkID8gMSA6IDANCiAgICAgICAgICB0aGlzLmZvcm0ubm9BZ3JlZW1lbnRTaG93ZWQgPSB0aGlzLmZvcm0ubm9BZ3JlZW1lbnRTaG93ZWQgPyAxIDogMA0KICAgICAgICAgIHRoaXMuZm9ybS5pc0N1c3RvbXNJbnRyYW5zaXRTaG93ZWQgPSB0aGlzLmZvcm0uaXNDdXN0b21zSW50cmFuc2l0U2hvd2VkID8gMSA6IDANCiAgICAgICAgICB1cGRhdGVSY3QodGhpcy5mb3JtKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgICAgIHRoaXMuZm9ybS5ub1RyYW5zZmVyQWxsb3dlZCA9IHRoaXMuZm9ybS5ub1RyYW5zZmVyQWxsb3dlZCA/IHRydWUgOiBmYWxzZQ0KICAgICAgICAgICAgdGhpcy5mb3JtLm5vRGl2aWRlZEFsbG93ZWQgPSB0aGlzLmZvcm0ubm9EaXZpZGVkQWxsb3dlZCA/IHRydWUgOiBmYWxzZQ0KICAgICAgICAgICAgdGhpcy5mb3JtLm5vQWdyZWVtZW50U2hvd2VkID0gdGhpcy5mb3JtLm5vQWdyZWVtZW50U2hvd2VkID8gdHJ1ZSA6IGZhbHNlDQogICAgICAgICAgICB0aGlzLmZvcm0uaXNDdXN0b21zSW50cmFuc2l0U2hvd2VkID0gdGhpcy5mb3JtLmlzQ3VzdG9tc0ludHJhbnNpdFNob3dlZCA/IHRydWUgOiBmYWxzZQ0KICAgICAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5pON5L2c5Y2V5bey6amz5ZueIikNCiAgICAgICAgICB9KQ0KICAgICAgICB9DQogICAgICB9KQ0KICAgIH0sDQogICAgYXN5bmMgc3VibWl0Rm9ybSh0eXBlKSB7DQogICAgICB0aGlzLiRyZWZzWyJmb3JtIl0udmFsaWRhdGUoYXN5bmMgdmFsaWQgPT4gew0KICAgICAgICBpZiAodGhpcy5zZXJ2aWNlTGlzdC5oYXMoMjApKSB7DQogICAgICAgICAgdGhpcy5yc09wUmFpbEZDTC5yc1NlcnZpY2VJbnN0YW5jZXMgPSB0aGlzLnJzT3BSYWlsRmNsU2VydmljZUluc3RhbmNlDQogICAgICAgICAgdGhpcy5mb3JtLnJzT3BSYWlsRkNMID0gdGhpcy5yc09wUmFpbEZDTA0KICAgICAgICB9DQogICAgICAgIGlmICh0aGlzLnNlcnZpY2VMaXN0LmhhcygyMSkpIHsNCiAgICAgICAgICB0aGlzLnJzT3BSYWlsTENMLnJzU2VydmljZUluc3RhbmNlcyA9IHRoaXMucnNPcFJhaWxMY2xTZXJ2aWNlSW5zdGFuY2UNCiAgICAgICAgICB0aGlzLmZvcm0ucnNPcFJhaWxMQ0wgPSB0aGlzLnJzT3BSYWlsTENMDQogICAgICAgIH0NCiAgICAgICAgaWYgKHRoaXMuc2VydmljZUxpc3QuaGFzKDQwKSkgew0KICAgICAgICAgIHRoaXMucnNPcEV4cHJlc3MucnNTZXJ2aWNlSW5zdGFuY2VzID0gdGhpcy5yc09wRXhwcmVzc1NlcnZpY2VJbnN0YW5jZQ0KICAgICAgICAgIHRoaXMuZm9ybS5yc09wRXhwcmVzcyA9IHRoaXMucnNPcEV4cHJlc3MNCiAgICAgICAgfQ0KICAgICAgICBpZiAodGhpcy5zZXJ2aWNlTGlzdC5oYXMoNzApKSB7DQogICAgICAgICAgdGhpcy5yc09wRE9BZ2VudC5yc1NlcnZpY2VJbnN0YW5jZXMgPSB0aGlzLnJzT3BET0FnZW50U2VydmljZUluc3RhbmNlDQogICAgICAgICAgdGhpcy5mb3JtLnJzT3BET0FnZW50ID0gdGhpcy5yc09wRE9BZ2VudA0KICAgICAgICB9DQogICAgICAgIGlmICh0aGlzLnNlcnZpY2VMaXN0Lmhhcyg3MSkpIHsNCiAgICAgICAgICB0aGlzLnJzT3BDbGVhckFnZW50LnJzU2VydmljZUluc3RhbmNlcyA9IHRoaXMucnNPcENsZWFyQWdlbnRTZXJ2aWNlSW5zdGFuY2UNCiAgICAgICAgICB0aGlzLmZvcm0ucnNPcENsZWFyQWdlbnQgPSB0aGlzLnJzT3BDbGVhckFnZW50DQogICAgICAgIH0NCiAgICAgICAgaWYgKHRoaXMuc2VydmljZUxpc3QuaGFzKDgwKSkgew0KICAgICAgICAgIHRoaXMucnNPcFdIUy5yc1NlcnZpY2VJbnN0YW5jZXMgPSB0aGlzLnJzT3BXSFNTZXJ2aWNlSW5zdGFuY2UNCiAgICAgICAgICB0aGlzLmZvcm0ucnNPcFdIUyA9IHRoaXMucnNPcFdIUw0KICAgICAgICB9DQogICAgICAgIGlmICh0aGlzLnNlcnZpY2VMaXN0Lmhhcyg5MCkpIHsNCiAgICAgICAgICB0aGlzLnJzT3AzcmRDZXJ0LnJzU2VydmljZUluc3RhbmNlcyA9IHRoaXMucnNPcDNyZENlcnRTZXJ2aWNlSW5zdGFuY2UNCiAgICAgICAgICB0aGlzLmZvcm0ucnNPcDNyZENlcnQgPSB0aGlzLnJzT3AzcmRDZXJ0DQogICAgICAgIH0NCiAgICAgICAgaWYgKHRoaXMuc2VydmljZUxpc3QuaGFzKDEwMCkpIHsNCiAgICAgICAgICB0aGlzLnJzT3BJTlMucnNTZXJ2aWNlSW5zdGFuY2VzID0gdGhpcy5yc09wSU5TU2VydmljZUluc3RhbmNlDQogICAgICAgICAgdGhpcy5mb3JtLnJzT3BJTlMgPSB0aGlzLnJzT3BJTlMNCiAgICAgICAgfQ0KICAgICAgICBpZiAodGhpcy5zZXJ2aWNlTGlzdC5oYXMoMTAxKSkgew0KICAgICAgICAgIHRoaXMucnNPcFRyYWRpbmcucnNTZXJ2aWNlSW5zdGFuY2VzID0gdGhpcy5yc09wVHJhZGluZ1NlcnZpY2VJbnN0YW5jZQ0KICAgICAgICAgIHRoaXMuZm9ybS5yc09wVHJhZGluZyA9IHRoaXMucnNPcFRyYWRpbmcNCiAgICAgICAgfQ0KICAgICAgICBpZiAodGhpcy5zZXJ2aWNlTGlzdC5oYXMoMTAyKSkgew0KICAgICAgICAgIHRoaXMucnNPcEZ1bWlnYXRpb24ucnNTZXJ2aWNlSW5zdGFuY2VzID0gdGhpcy5yc09wRnVtaWdhdGlvblNlcnZpY2VJbnN0YW5jZQ0KICAgICAgICAgIHRoaXMuZm9ybS5yc09wRnVtaWdhdGlvbiA9IHRoaXMucnNPcEZ1bWlnYXRpb24NCiAgICAgICAgfQ0KICAgICAgICBpZiAodGhpcy5zZXJ2aWNlTGlzdC5oYXMoMTAzKSkgew0KICAgICAgICAgIHRoaXMucnNPcENPLnJzU2VydmljZUluc3RhbmNlcyA9IHRoaXMucnNPcENPU2VydmljZUluc3RhbmNlDQogICAgICAgICAgdGhpcy5mb3JtLnJzT3BDTyA9IHRoaXMucnNPcENPDQogICAgICAgIH0NCiAgICAgICAgaWYgKHRoaXMuc2VydmljZUxpc3QuaGFzKDEwNCkpIHsNCiAgICAgICAgICB0aGlzLnJzT3BPdGhlci5yc1NlcnZpY2VJbnN0YW5jZXMgPSB0aGlzLnJzT3BPdGhlclNlcnZpY2VJbnN0YW5jZQ0KICAgICAgICAgIHRoaXMuZm9ybS5yc09wT3RoZXIgPSB0aGlzLnJzT3BPdGhlcg0KICAgICAgICB9DQoNCiAgICAgICAgdGhpcy5mb3JtLnJlbGF0aW9uQ2xpZW50SWRMaXN0ID0gdGhpcy5SZWxhdGlvbkNsaWVudElkTGlzdC50b1N0cmluZygpDQogICAgICAgIHRoaXMuZm9ybS5zZXJ2aWNlVHlwZUlkTGlzdCA9IHRoaXMuc2VydmljZUxpc3QudG9TdHJpbmcoKQ0KICAgICAgICAvLyBUT0RPDQogICAgICAgIGlmICgodGhpcy5yc09wQnVsa1RydWNrLnJzT3BUcnVja0xpc3QgJiYgdGhpcy5yc09wQnVsa1RydWNrLnJzT3BUcnVja0xpc3QpIHx8ICh0aGlzLnJzT3BDdG5yVHJ1Y2sucnNPcFRydWNrTGlzdCAmJiB0aGlzLnJzT3BDdG5yVHJ1Y2sucnNPcFRydWNrTGlzdC5sZW5ndGggPiAwKSkgew0KICAgICAgICAgIGlmICh0aGlzLnNlcnZpY2VMaXN0Lmhhcyg1MCkpIHsNCiAgICAgICAgICAgIHRoaXMuZm9ybS5yc09wQ3RuclRydWNrTGlzdC5tYXAoaXRlbSA9PiB7DQogICAgICAgICAgICAgIGl0ZW0uc3FkQ29udGFpbmVyc1NlYWxzU3VtID0gaXRlbS5yc09wVHJ1Y2tMaXN0Lm1hcCh2ID0+IHYuY29udGFpbmVyTm8pLmpvaW4oIiwiKQ0KICAgICAgICAgICAgfSkNCiAgICAgICAgICB9IGVsc2UgaWYgKHRoaXMuc2VydmljZUxpc3QuaGFzKDUxKSkgew0KICAgICAgICAgICAgdGhpcy5mb3JtLnJzT3BCdWxrVHJ1Y2tMaXN0Lm1hcChpdGVtID0+IHsNCiAgICAgICAgICAgICAgaXRlbS5zcWRDb250YWluZXJzU2VhbHNTdW0gPSBpdGVtLnJzT3BUcnVja0xpc3QubWFwKHYgPT4gdi5jb250YWluZXJObykuam9pbigiLCIpDQogICAgICAgICAgICB9KQ0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KDQogICAgICAgIC8vIOaPkOWNleS/oeaBr+WIl+ihqA0KICAgICAgICB0aGlzLmZvcm0uYm9va2luZ01lc3NhZ2VzTGlzdCA9IHRoaXMuYm9va2luZ01lc3NhZ2VMaXN0DQoNCiAgICAgICAgLy8g5LyY5YyW5Ye95pWw77ya5YiG5Ymy5a2X56ym5Liy5bm25re75Yqg5YiwU2V06ZuG5ZCIDQogICAgICAgIGNvbnN0IGFkZFRvU2V0ID0gKHRleHQsIHNldCwgc2VwYXJhdG9yID0gIi8iKSA9PiB7DQogICAgICAgICAgaWYgKCF0ZXh0KSByZXR1cm4NCiAgICAgICAgICB0ZXh0LnNwbGl0KHNlcGFyYXRvcikNCiAgICAgICAgICAgIC5tYXAoaXRlbSA9PiBpdGVtLnRyaW0oKSkNCiAgICAgICAgICAgIC5maWx0ZXIoQm9vbGVhbikNCiAgICAgICAgICAgIC5mb3JFYWNoKGl0ZW0gPT4gc2V0LmFkZChpdGVtKSkNCiAgICAgICAgfQ0KDQogICAgICAgIC8vIOS8mOWMluWHveaVsO+8muS7juaVsOe7hOWvueixoeS4reaPkOWPluWtl+auteW5tua3u+WKoOWIsFNldOmbhuWQiA0KICAgICAgICBjb25zdCBhZGRGcm9tTGlzdCA9IChsaXN0LCBmaWVsZE5hbWUsIHNldCwgc2VwYXJhdG9yID0gL1xyXG58XHJ8XG4vKSA9PiB7DQogICAgICAgICAgaWYgKCFsaXN0Py5sZW5ndGgpIHJldHVybg0KICAgICAgICAgIGxpc3QuZm9yRWFjaChpdGVtID0+IHsNCiAgICAgICAgICAgIGlmIChpdGVtW2ZpZWxkTmFtZV0pIHsNCiAgICAgICAgICAgICAgYWRkVG9TZXQoaXRlbVtmaWVsZE5hbWVdLCBzZXQsIHNlcGFyYXRvcikNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9KQ0KICAgICAgICB9DQoNCiAgICAgICAgLy8g6I635Y+WRkNM5oiWTENM5YiX6KGo55qE56ys5LiA6aG5DQogICAgICAgIGNvbnN0IGdldEZjbE9yTGNsID0gKGZvcm0pID0+IHsNCiAgICAgICAgICByZXR1cm4gZm9ybS5yc09wU2VhRmNsTGlzdD8uWzBdIHx8IGZvcm0ucnNPcFNlYUxjbExpc3Q/LlswXSB8fCBudWxsDQogICAgICAgIH0NCg0KICAgICAgICAvLyDmm7TmlrDooajljZXlgLwNCiAgICAgICAgY29uc3QgdXBkYXRlRm9ybVZhbHVlID0gKGZvcm0sIGZpZWxkTmFtZSwgdmFsdWUpID0+IHsNCiAgICAgICAgICBpZiAoZm9ybS5yc09wU2VhRmNsTGlzdD8uWzBdKSB7DQogICAgICAgICAgICBmb3JtLnJzT3BTZWFGY2xMaXN0WzBdW2ZpZWxkTmFtZV0gPSB2YWx1ZQ0KICAgICAgICAgIH0NCiAgICAgICAgICBpZiAoZm9ybS5yc09wU2VhTGNsTGlzdD8uWzBdKSB7DQogICAgICAgICAgICBmb3JtLnJzT3BTZWFMY2xMaXN0WzBdW2ZpZWxkTmFtZV0gPSB2YWx1ZQ0KICAgICAgICAgIH0NCiAgICAgICAgICBmb3JtW2ZpZWxkTmFtZV0gPSB2YWx1ZQ0KICAgICAgICB9DQoNCiAgICAgICAgLy8g5aSE55CG6ZuG6KOF566x5Y+3DQogICAgICAgIGNvbnN0IHByb2Nlc3NDb250YWluZXJzID0gKCkgPT4gew0KICAgICAgICAgIGNvbnN0IGFsbENvbnRhaW5lcnMgPSBuZXcgU2V0KCkNCg0KICAgICAgICAgIC8vIOS7juihqOWNleS4rea3u+WKoOmbhuijheeuseWPtw0KICAgICAgICAgIGNvbnN0IGZpcnN0SXRlbSA9IGdldEZjbE9yTGNsKHRoaXMuZm9ybSkNCiAgICAgICAgICBpZiAoZmlyc3RJdGVtPy5zcWRDb250YWluZXJzU2VhbHNTdW0pIHsNCiAgICAgICAgICAgIGFkZFRvU2V0KGZpcnN0SXRlbS5zcWRDb250YWluZXJzU2VhbHNTdW0sIGFsbENvbnRhaW5lcnMpDQogICAgICAgICAgfQ0KDQogICAgICAgICAgLy8g5LuOYm9va2luZ01lc3NhZ2VMaXN05re75Yqg6ZuG6KOF566x5Y+3DQogICAgICAgICAgYWRkRnJvbUxpc3QodGhpcy5ib29raW5nTWVzc2FnZUxpc3QsICJjb250YWluZXJObyIsIGFsbENvbnRhaW5lcnMpDQoNCiAgICAgICAgICAvLyDku45yc09wQ3RuclRydWNrTGlzdOa3u+WKoOmbhuijheeuseWPtw0KICAgICAgICAgIGlmICh0aGlzLmZvcm0ucnNPcEN0bnJUcnVja0xpc3Q/LlswXT8ucnNPcFRydWNrTGlzdCkgew0KICAgICAgICAgICAgdGhpcy5mb3JtLnJzT3BDdG5yVHJ1Y2tMaXN0WzBdLnJzT3BUcnVja0xpc3QNCiAgICAgICAgICAgICAgLm1hcChpdGVtID0+IGl0ZW0uY29udGFpbmVyTm8/LnRyaW0oKSkNCiAgICAgICAgICAgICAgLmZpbHRlcihCb29sZWFuKQ0KICAgICAgICAgICAgICAuZm9yRWFjaChpdGVtID0+IGFsbENvbnRhaW5lcnMuYWRkKGl0ZW0pKQ0KICAgICAgICAgIH0NCg0KICAgICAgICAgIC8vIOS7jnJzT3BCdWxrVHJ1Y2tMaXN05re75Yqg6ZuG6KOF566x5Y+3DQogICAgICAgICAgaWYgKHRoaXMuZm9ybS5yc09wQnVsa1RydWNrTGlzdD8uWzBdPy5yc09wVHJ1Y2tMaXN0KSB7DQogICAgICAgICAgICB0aGlzLmZvcm0ucnNPcEJ1bGtUcnVja0xpc3RbMF0ucnNPcFRydWNrTGlzdA0KICAgICAgICAgICAgICAubWFwKGl0ZW0gPT4gaXRlbS5jb250YWluZXJObz8udHJpbSgpKQ0KICAgICAgICAgICAgICAuZmlsdGVyKEJvb2xlYW4pDQogICAgICAgICAgICAgIC5mb3JFYWNoKGl0ZW0gPT4gYWxsQ29udGFpbmVycy5hZGQoaXRlbSkpDQogICAgICAgICAgfQ0KDQogICAgICAgICAgLy8g5pu05paw6KGo5Y2V5a2X5q61DQogICAgICAgICAgY29uc3QgY29udGFpbmVyc1N0ciA9IEFycmF5LmZyb20oYWxsQ29udGFpbmVycykuam9pbigiLyIpDQogICAgICAgICAgdXBkYXRlRm9ybVZhbHVlKHRoaXMuZm9ybSwgInNxZENvbnRhaW5lcnNTZWFsc1N1bSIsIGNvbnRhaW5lcnNTdHIpDQogICAgICAgIH0NCg0KICAgICAgICAvLyDlpITnkIbmj5DljZXlj7cNCiAgICAgICAgY29uc3QgcHJvY2Vzc0JsTm9zID0gKCkgPT4gew0KICAgICAgICAgIGNvbnN0IGFsbEJsTm9zID0gbmV3IFNldCgpDQoNCiAgICAgICAgICAvLyDku47ooajljZXkuK3mt7vliqDmj5DljZXlj7cNCiAgICAgICAgICBjb25zdCBmaXJzdEl0ZW0gPSBnZXRGY2xPckxjbCh0aGlzLmZvcm0pDQogICAgICAgICAgaWYgKGZpcnN0SXRlbT8uYmxObykgew0KICAgICAgICAgICAgYWRkVG9TZXQoZmlyc3RJdGVtLmJsTm8sIGFsbEJsTm9zKQ0KICAgICAgICAgIH0NCg0KICAgICAgICAgIC8vIOS7jmJvb2tpbmdNZXNzYWdlTGlzdOa3u+WKoOaPkOWNleWPtw0KICAgICAgICAgIGFkZEZyb21MaXN0KHRoaXMuYm9va2luZ01lc3NhZ2VMaXN0LCAibUJsTm8iLCBhbGxCbE5vcykNCg0KICAgICAgICAgIC8vIOabtOaWsOihqOWNleWtl+autQ0KICAgICAgICAgIGNvbnN0IGJsTm9TdHIgPSBBcnJheS5mcm9tKGFsbEJsTm9zKS5qb2luKCIvIikNCiAgICAgICAgICB1cGRhdGVGb3JtVmFsdWUodGhpcy5mb3JtLCAiYmxObyIsIGJsTm9TdHIpDQogICAgICAgIH0NCg0KICAgICAgICAvLyDmiafooYzlpITnkIYNCiAgICAgICAgcHJvY2Vzc0NvbnRhaW5lcnMoKQ0KICAgICAgICBwcm9jZXNzQmxOb3MoKQ0KDQogICAgICAgIGxldCBleGNoYW5nZVJhdGUNCiAgICAgICAgZm9yIChjb25zdCBhIG9mIHRoaXMuJHN0b3JlLnN0YXRlLmRhdGEuZXhjaGFuZ2VSYXRlTGlzdCkgew0KICAgICAgICAgIGlmICh0aGlzLmZvcm0ucG9kRXRhKSB7DQogICAgICAgICAgICBpZiAoYS5sb2NhbEN1cnJlbmN5ID09PSAiUk1CIg0KICAgICAgICAgICAgICAmJiAiVVNEIiA9PSBhLm92ZXJzZWFDdXJyZW5jeQ0KICAgICAgICAgICAgICAmJiBwYXJzZVRpbWUoYS52YWxpZEZyb20pIDw9IHBhcnNlVGltZSh0aGlzLmZvcm0ucG9kRXRhKQ0KICAgICAgICAgICAgICAmJiBwYXJzZVRpbWUodGhpcy5mb3JtLnBvZEV0YSkgPD0gcGFyc2VUaW1lKGEudmFsaWRUbykNCiAgICAgICAgICAgICkgew0KICAgICAgICAgICAgICBleGNoYW5nZVJhdGUgPSBjdXJyZW5jeShhLnNldHRsZVJhdGUpLmRpdmlkZShhLmJhc2UpLnZhbHVlDQogICAgICAgICAgICB9DQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIGlmIChhLmxvY2FsQ3VycmVuY3kgPT09ICJSTUIiDQogICAgICAgICAgICAgICYmICJVU0QiID09IGEub3ZlcnNlYUN1cnJlbmN5DQogICAgICAgICAgICAgICYmIHBhcnNlVGltZShhLnZhbGlkRnJvbSkgPD0gcGFyc2VUaW1lKG5ldyBEYXRlKCkpDQogICAgICAgICAgICAgICYmIHBhcnNlVGltZShuZXcgRGF0ZSgpKSA8PSBwYXJzZVRpbWUoYS52YWxpZFRvKSkgew0KICAgICAgICAgICAgICBleGNoYW5nZVJhdGUgPSBjdXJyZW5jeShhLnNldHRsZVJhdGUpLmRpdmlkZShhLmJhc2UpLnZhbHVlDQogICAgICAgICAgICB9DQogICAgICAgICAgfQ0KICAgICAgICB9DQoNCiAgICAgICAgLy8g6LS555SoDQogICAgICAgIHRoaXMuZm9ybS5kblJtYiA9IHRoaXMucnNDbGllbnRNZXNzYWdlUmVjZWl2YWJsZVRheFJNQiAvLyBybWLlkKvnqI7lupTmlLYNCiAgICAgICAgdGhpcy5mb3JtLmRuVXNkID0gdGhpcy5yc0NsaWVudE1lc3NhZ2VSZWNlaXZhYmxlVGF4VVNEIC8vIHVzZOWQq+eojuW6lOaUtg0KICAgICAgICB0aGlzLmZvcm0uY25SbWIgPSB0aGlzLnJzQ2xpZW50TWVzc2FnZVBheWFibGVUYXhSTUIgLy8gcm1i5ZCr56iO5bqU5LuYDQogICAgICAgIHRoaXMuZm9ybS5jblVzZCA9IHRoaXMucnNDbGllbnRNZXNzYWdlUGF5YWJsZVRheFVTRCAvLyB1c2TlkKvnqI7lupTku5gNCiAgICAgICAgLy8g5oqY5ZCI6LS555SoDQogICAgICAgIHRoaXMuZm9ybS5kbkluUm1iID0gY3VycmVuY3kodGhpcy5yc0NsaWVudE1lc3NhZ2VSZWNlaXZhYmxlVGF4Uk1CKS5hZGQoY3VycmVuY3kodGhpcy5yc0NsaWVudE1lc3NhZ2VSZWNlaXZhYmxlVGF4VVNEKS5tdWx0aXBseShleGNoYW5nZVJhdGUpKS52YWx1ZSAvLyDmipjlkIhybWLlkKvnqI7lupTmlLYNCiAgICAgICAgdGhpcy5mb3JtLmNuSW5SbWIgPSBjdXJyZW5jeSh0aGlzLnJzQ2xpZW50TWVzc2FnZVBheWFibGVUYXhSTUIpLmFkZChjdXJyZW5jeSh0aGlzLnJzQ2xpZW50TWVzc2FnZVBheWFibGVUYXhVU0QpLm11bHRpcGx5KGV4Y2hhbmdlUmF0ZSkpLnZhbHVlIC8vIOaKmOWQiHJtYuWQq+eojuW6lOS7mA0KDQogICAgICAgIC8vIOe7n+iuoeaUtuasvuWSjOS7mOasvui/m+W6pg0KICAgICAgICB0aGlzLmZvcm0uZG5SbWJCYWxhbmNlID0gdGhpcy5zcWRVbnJlY2VpdmVkUm1iU3VtIC8vIHJtYuWQq+eojuacquaUtg0KICAgICAgICB0aGlzLmZvcm0uZG5Vc2RCYWxhbmNlID0gdGhpcy5zcWRVbnJlY2VpdmVkVXNkU3VtIC8vIHVzZOWQq+eojuacquaUtg0KICAgICAgICB0aGlzLmZvcm0uY25SbWJCYWxhbmNlID0gdGhpcy5zcWRVbnBhaWRSbWJTdW0gLy8gcm1i5ZCr56iO5pyq5LuYDQogICAgICAgIHRoaXMuZm9ybS5jblVzZEJhbGFuY2UgPSB0aGlzLnNxZFVucGFpZFVzZFN1bSAvLyB1c2TlkKvnqI7mnKrku5gNCiAgICAgICAgLy8g5oqY5ZCI6L+b5bqmDQogICAgICAgIHRoaXMuZm9ybS5kbkluUm1iQmFsYW5jZSA9IGN1cnJlbmN5KHRoaXMuc3FkVW5yZWNlaXZlZFJtYlN1bSkuYWRkKGN1cnJlbmN5KHRoaXMuc3FkVW5yZWNlaXZlZFVzZFN1bSkubXVsdGlwbHkoZXhjaGFuZ2VSYXRlKSkudmFsdWUgLy8g5oqY5ZCIcm1i5pyq5pS2DQogICAgICAgIHRoaXMuZm9ybS5jbkluUm1iQmFsYW5jZSA9IGN1cnJlbmN5KHRoaXMuc3FkVW5wYWlkUm1iU3VtKS5hZGQoY3VycmVuY3kodGhpcy5zcWRVbnBhaWRVc2RTdW0pLm11bHRpcGx5KGV4Y2hhbmdlUmF0ZSkpLnZhbHVlIC8vIOaKmOWQiHJtYuacquS7mA0KDQogICAgICAgIC8vIOWIqea2pg0KICAgICAgICB0aGlzLmZvcm0ucHJvZml0VXNkID0gY3VycmVuY3kodGhpcy5mb3JtLmRuVXNkKS5zdWJ0cmFjdCh0aGlzLmZvcm0uY25Vc2QpLnZhbHVlIC8vIOe+juWFg+mDqOWIhuWQq+eojuWIqea2pg0KICAgICAgICB0aGlzLmZvcm0ucHJvZml0Um1iID0gY3VycmVuY3kodGhpcy5mb3JtLmRuUm1iKS5zdWJ0cmFjdCh0aGlzLmZvcm0uY25SbWIpLnZhbHVlIC8vIOS6uuawkeW4gemDqOWIhuWQq+eojuWIqea2pg0KICAgICAgICB0aGlzLmZvcm0ucHJvZml0SW5SbWIgPSBjdXJyZW5jeSh0aGlzLmZvcm0ucHJvZml0VXNkKS5tdWx0aXBseShleGNoYW5nZVJhdGUpLmFkZCh0aGlzLmZvcm0ucHJvZml0Um1iKS52YWx1ZSAvLyDmipjlkIjkurrmsJHluIHliKnmtqYNCg0KICAgICAgICB0aGlzLmZvcm0ubm9UcmFuc2ZlckFsbG93ZWQgPSB0aGlzLmZvcm0ubm9UcmFuc2ZlckFsbG93ZWQgPyAxIDogMA0KICAgICAgICB0aGlzLmZvcm0ubm9EaXZpZGVkQWxsb3dlZCA9IHRoaXMuZm9ybS5ub0RpdmlkZWRBbGxvd2VkID8gMSA6IDANCiAgICAgICAgdGhpcy5mb3JtLm5vQWdyZWVtZW50U2hvd2VkID0gdGhpcy5mb3JtLm5vQWdyZWVtZW50U2hvd2VkID8gMSA6IDANCiAgICAgICAgdGhpcy5mb3JtLmlzQ3VzdG9tc0ludHJhbnNpdFNob3dlZCA9IHRoaXMuZm9ybS5pc0N1c3RvbXNJbnRyYW5zaXRTaG93ZWQgPyAxIDogMA0KDQogICAgICAgIHRoaXMuZm9ybS5tZXNzYWdlRGlzcGxheSA9IFtOdW1iZXIodGhpcy5zZXJ2aWNlSW5mbyksIE51bWJlcih0aGlzLm9yZGVySW5mbyksIE51bWJlcih0aGlzLmJyYW5jaEluZm8pLCBOdW1iZXIodGhpcy5sb2dpc3RpY3NJbmZvKSwgTnVtYmVyKHRoaXMuZG9jSW5mbyksIE51bWJlcih0aGlzLmNoYXJnZUluZm8pLCBOdW1iZXIodGhpcy5hdWRpdEluZm8pXS50b1N0cmluZygpDQogICAgICAgIHRoaXMuZm9ybS5zZXJ2aWNlTWVzc2FnZUZvbGQgPSBbTnVtYmVyKHRoaXMuY2xpZW50TWVzc2FnZSksIE51bWJlcih0aGlzLnJzT3BTZWFsRmNsRm9sZCksIE51bWJlcih0aGlzLnJzT3BTZWFsTGNsRm9sZCksIE51bWJlcih0aGlzLnJzT3BBaXJGb2xkKSwgTnVtYmVyKHRoaXMucnNPcFJhaWxGY2xGb2xkKSwgTnVtYmVyKHRoaXMucnNPcFJhaWxMY2xGb2xkKSwgTnVtYmVyKHRoaXMucnNPcEV4cHJlc3NGb2xkKSwgTnVtYmVyKHRoaXMucnNPcEN0bnJUcnVja0ZvbGQpLCBOdW1iZXIodGhpcy5yc09wQnVsa1RydWNrRm9sZCksIE51bWJlcih0aGlzLnJzT3BEb2NEZWNsYXJlRm9sZCksIE51bWJlcih0aGlzLnJzT3BGcmVlRGVjbGFyZUZvbGQpLCBOdW1iZXIodGhpcy5yc09wRE9BZ2VudEZvbGQpLCBOdW1iZXIodGhpcy5yc09wQ2xlYXJBZ2VudEZvbGQpLCBOdW1iZXIodGhpcy5yc09wV0hTRm9sZCksIE51bWJlcih0aGlzLnJzT3AzcmRDZXJ0Rm9sZCksIE51bWJlcih0aGlzLnJzT3BJTlNGb2xkKSwgTnVtYmVyKHRoaXMucnNPcFRyYWRpbmdGb2xkKSwgTnVtYmVyKHRoaXMucnNPcEZ1bWlnYXRpb25Gb2xkKSwgTnVtYmVyKHRoaXMucnNPcENPRm9sZCksIE51bWJlcih0aGlzLnJzT3BPdGhlckZvbGQpXS50b1N0cmluZygpDQoNCiAgICAgICAgdGhpcy5mb3JtLnJzT3BTZWFGY2xMaXN0ID8gdGhpcy5mb3JtLnJzT3BTZWFGY2xMaXN0Lm1hcChpdGVtID0+IHsNCiAgICAgICAgICBpdGVtLnJzU2VydmljZUluc3RhbmNlcy5zZXJ2aWNlRm9sZCA9IE51bWJlcihpdGVtLnJzU2VydmljZUluc3RhbmNlcy5zZXJ2aWNlRm9sZCkNCiAgICAgICAgICByZXR1cm4gaXRlbQ0KICAgICAgICB9KSA6IG51bGwNCiAgICAgICAgdGhpcy5mb3JtLnJzT3BTZWFMY2xMaXN0ID8gdGhpcy5mb3JtLnJzT3BTZWFMY2xMaXN0Lm1hcChpdGVtID0+IHsNCiAgICAgICAgICBpdGVtLnJzU2VydmljZUluc3RhbmNlcy5zZXJ2aWNlRm9sZCA9IE51bWJlcihpdGVtLnJzU2VydmljZUluc3RhbmNlcy5zZXJ2aWNlRm9sZCkNCiAgICAgICAgICByZXR1cm4gaXRlbQ0KICAgICAgICB9KSA6IG51bGwNCiAgICAgICAgdGhpcy5mb3JtLnJzT3BBaXJMaXN0ID8gdGhpcy5mb3JtLnJzT3BBaXJMaXN0Lm1hcChpdGVtID0+IHsNCiAgICAgICAgICBpdGVtLnJzU2VydmljZUluc3RhbmNlcy5zZXJ2aWNlRm9sZCA9IE51bWJlcihpdGVtLnJzU2VydmljZUluc3RhbmNlcy5zZXJ2aWNlRm9sZCkNCiAgICAgICAgICByZXR1cm4gaXRlbQ0KICAgICAgICB9KSA6IG51bGwNCiAgICAgICAgdGhpcy5mb3JtLnJzT3BDdG5yVHJ1Y2tMaXN0ID8gdGhpcy5mb3JtLnJzT3BDdG5yVHJ1Y2tMaXN0Lm1hcChpdGVtID0+IHsNCiAgICAgICAgICBpdGVtLnJzU2VydmljZUluc3RhbmNlcy5zZXJ2aWNlRm9sZCA9IE51bWJlcihpdGVtLnJzU2VydmljZUluc3RhbmNlcy5zZXJ2aWNlRm9sZCkNCiAgICAgICAgICByZXR1cm4gaXRlbQ0KICAgICAgICB9KSA6IG51bGwNCiAgICAgICAgdGhpcy5mb3JtLnJzT3BCdWxrVHJ1Y2tMaXN0ID8gdGhpcy5mb3JtLnJzT3BCdWxrVHJ1Y2tMaXN0Lm1hcChpdGVtID0+IHsNCiAgICAgICAgICBpdGVtLnJzU2VydmljZUluc3RhbmNlcy5zZXJ2aWNlRm9sZCA9IE51bWJlcihpdGVtLnJzU2VydmljZUluc3RhbmNlcy5zZXJ2aWNlRm9sZCkNCiAgICAgICAgICByZXR1cm4gaXRlbQ0KICAgICAgICB9KSA6IG51bGwNCiAgICAgICAgdGhpcy5mb3JtLnJzT3BEb2NEZWNsYXJlTGlzdCA/IHRoaXMuZm9ybS5yc09wRG9jRGVjbGFyZUxpc3QubWFwKGl0ZW0gPT4gew0KICAgICAgICAgIGl0ZW0ucnNTZXJ2aWNlSW5zdGFuY2VzLnNlcnZpY2VGb2xkID0gTnVtYmVyKGl0ZW0ucnNTZXJ2aWNlSW5zdGFuY2VzLnNlcnZpY2VGb2xkKQ0KICAgICAgICAgIHJldHVybiBpdGVtDQogICAgICAgIH0pIDogbnVsbA0KICAgICAgICB0aGlzLmZvcm0ucnNPcEZyZWVEZWNsYXJlTGlzdCA/IHRoaXMuZm9ybS5yc09wRnJlZURlY2xhcmVMaXN0Lm1hcChpdGVtID0+IHsNCiAgICAgICAgICBpdGVtLnJzU2VydmljZUluc3RhbmNlcy5zZXJ2aWNlRm9sZCA9IE51bWJlcihpdGVtLnJzU2VydmljZUluc3RhbmNlcy5zZXJ2aWNlRm9sZCkNCiAgICAgICAgICByZXR1cm4gaXRlbQ0KICAgICAgICB9KSA6IG51bGwNCg0KICAgICAgICAvLyDmtbfov5DlrZDmnI3liqHlkIjlubbkuLvooajkv6Hmga8NCiAgICAgICAgdGhpcy5tZXJnZVNlYVNlcnZpY2VXaXRoTWFpblNlcnZpY2UoKQ0KDQogICAgICAgIC8vIOW9k+ebruWJjea1t+i/kOiuouiIseWPquacieS4gOS4queahOaXtuWAmQ0KICAgICAgICB0aGlzLmNob29zZVdoZW5Pbmx5T25lKCkNCg0KICAgICAgICAvLyDnoa7kv53lnKjmj5DkuqTliY3muIXpmaTljYPliIbkvY3liIbpmpTnrKYNCiAgICAgICAgaWYgKHRoaXMuZm9ybS5ncm9zc1dlaWdodCkgew0KICAgICAgICAgIHRoaXMuZm9ybS5ncm9zc1dlaWdodCA9IHRoaXMuZm9ybS5ncm9zc1dlaWdodC50b1N0cmluZygpLnJlcGxhY2UoLywvZywgIiIpDQogICAgICAgIH0NCg0KICAgICAgICBpZiAodmFsaWQpIHsNCiAgICAgICAgICBsZXQgbWVzc2FnZSA9ICLkv67mlLnmiJDlip8iDQogICAgICAgICAgLy8g5ZWG5Yqh5a6h5qC4DQogICAgICAgICAgaWYgKHRoaXMucHNhVmVyaWZ5ICYmIHR5cGUgPT09ICJwc2EiKSB7DQogICAgICAgICAgICBpZiAoIXRoaXMuZm9ybS5wc2FWZXJpZnlTdGF0dXNJZCkgew0KICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoIuivt+mAieaLqeWuoeaguOeKtuaAgSIpDQogICAgICAgICAgICB9DQogICAgICAgICAgICAvLyBUT0RPIOmps+WbnuS4jemcgOimgemAieaLqeaTjeS9nA0KICAgICAgICAgICAgaWYgKHRoaXMub3BJZCA9PT0gbnVsbCAmJiB0aGlzLmZvcm0ucHNhVmVyaWZ5U3RhdHVzSWQgPT0gMSkgew0KICAgICAgICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoIuivt+aMh+a0vuaTjeS9nOWRmCIpDQogICAgICAgICAgICAgIHJldHVybg0KICAgICAgICAgICAgfQ0KDQogICAgICAgICAgICAvLyDlrqHmoLjpqbPlm54NCiAgICAgICAgICAgIGlmICh0aGlzLnBzYVZlcmlmeSAmJiB0aGlzLmZvcm0ucHNhVmVyaWZ5U3RhdHVzSWQgPT0gOSkgew0KICAgICAgICAgICAgICAvLyBpZiDllYbliqHpqbPlm54gdGhlbiDllYbliqHml6XmnJ89bnVsbO+8jOS4muWKoeiuouiIseaXpeacnz1udWxs77yM5ZWG5Yqh5a6h5qC454q25oCBPeacquWuoeaguO+8jOS4muWKoeiuouiIseeKtuaAgT3mnKrorqLoiLENCiAgICAgICAgICAgICAgdGhpcy5mb3JtLnBzYVZlcmlmeVRpbWUgPSBudWxsDQogICAgICAgICAgICAgIHRoaXMuZm9ybS5uZXdCb29raW5nVGltZSA9IG51bGwNCiAgICAgICAgICAgICAgbWVzc2FnZSA9ICLlt7LpqbPlm54iDQogICAgICAgICAgICAgIHRoaXMuZm9ybS5zcWRTaGlwcGluZ0Jvb2tpbmdTdGF0dXMgPSAwDQogICAgICAgICAgICAgIHRoaXMuZm9ybS5wc2FWZXJpZnkgPSAwDQogICAgICAgICAgICB9DQogICAgICAgICAgICAvLyDlrqHmoLjpgJrov4cNCiAgICAgICAgICAgIGlmICh0aGlzLnBzYVZlcmlmeSAmJiB0aGlzLmZvcm0ucHNhVmVyaWZ5U3RhdHVzSWQgPT0gMSkgew0KICAgICAgICAgICAgICBtZXNzYWdlID0gIuWuoeaguOmAmui/hyINCiAgICAgICAgICAgICAgdGhpcy5mb3JtLnBzYVZlcmlmeSA9IDENCiAgICAgICAgICAgICAgdGhpcy5mb3JtLnBzYVZlcmlmeVRpbWUgPSBtb21lbnQoKS5mb3JtYXQoInl5eXktTU0tREQgSEg6bW06c3MiKQ0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0NCiAgICAgICAgICAvLyDnoa7orqTorqLoiLENCiAgICAgICAgICBpZiAodHlwZSA9PT0gImJvb2tpbmciKSB7DQogICAgICAgICAgICBpZiAodGhpcy5mb3JtLnZlcmlmeVBzYUlkID09IG51bGwpIHsNCiAgICAgICAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCLor7fmjIflrprllYbliqEiKQ0KICAgICAgICAgICAgICByZXR1cm4NCiAgICAgICAgICAgIH0NCiAgICAgICAgICAgIHRoaXMuZm9ybS5zcWRTaGlwcGluZ0Jvb2tpbmdTdGF0dXMgPSAxDQogICAgICAgICAgICAvLyDmipjlkIjlkKvnqI7miqXku7cNCiAgICAgICAgICAgIHRoaXMuZm9ybS5xdW90YXRpb25JblJtYiA9IHRoaXMucnNDbGllbnRNZXNzYWdlUmVjZWl2YWJsZQ0KICAgICAgICAgICAgLy8g5oqY5ZCI5ZCr56iO6K+i5Lu3DQogICAgICAgICAgICB0aGlzLmZvcm0uaW5xdWlyeUluUm1iID0gdGhpcy5yc0NsaWVudE1lc3NhZ2VQYXlhYmxlDQogICAgICAgICAgICAvLyDpooTmnJ/kuI3lkKvnqI7liKnmtqYNCiAgICAgICAgICAgIC8vIHRoaXMuZm9ybS5lc3RpbWF0ZWRQcm9maXRJblJtYiA9IHRoaXMucnNDbGllbnRNZXNzYWdlUHJvZml0Tm9UYXgNCiAgICAgICAgICAgIC8vIOmihOacn+WQq+eojuWIqea2pg0KICAgICAgICAgICAgdGhpcy5mb3JtLmVzdGltYXRlZFByb2ZpdEluUm1iID0gdGhpcy5yc0NsaWVudE1lc3NhZ2VQcm9maXQNCiAgICAgICAgICAgIC8vIOaKpeS7t+Wkh+azqCzoh6rliqjloavlhpkNCiAgICAgICAgICAgIGlmICh0aGlzLmZvcm0ucW91dGF0aW9uU2tldGNoID09PSAiIikgew0KICAgICAgICAgICAgICB0aGlzLmZvcm0ucnNPcFNlYUZjbExpc3QgJiYgdGhpcy5mb3JtLnJzT3BTZWFGY2xMaXN0WzBdLnJzQ2hhcmdlTGlzdCA/DQogICAgICAgICAgICAgICAgdGhpcy5mb3JtLnJzT3BTZWFGY2xMaXN0WzBdLnJzQ2hhcmdlTGlzdC5tYXAocnNDaGFyZ2UgPT4gew0KICAgICAgICAgICAgICAgICAgdGhpcy5mb3JtLnFvdXRhdGlvblNrZXRjaCArPSByc0NoYXJnZS5jaGFyZ2VOYW1lICsgIjpcdCIgKyByc0NoYXJnZS5kbkN1cnJlbmN5Q29kZSArICIgIiArIHJzQ2hhcmdlLmRuVW5pdFJhdGUgKyAiLyIgKyByc0NoYXJnZS5kblVuaXRDb2RlICsgIlxuIg0KICAgICAgICAgICAgICAgIH0pDQogICAgICAgICAgICAgICAgOiBudWxsDQogICAgICAgICAgICAgIHRoaXMuZm9ybS5yc09wU2VhTGNsTGlzdCAmJiB0aGlzLmZvcm0ucnNPcFNlYUxjbExpc3RbMF0ucnNDaGFyZ2VMaXN0ID8NCiAgICAgICAgICAgICAgICB0aGlzLmZvcm0ucnNPcFNlYUZjbExpc3RbMF0ucnNDaGFyZ2VMaXN0Lm1hcChyc0NoYXJnZSA9PiB7DQogICAgICAgICAgICAgICAgICB0aGlzLmZvcm0ucW91dGF0aW9uU2tldGNoICs9IHJzQ2hhcmdlLmNoYXJnZU5hbWUgKyAiOlx0IiArIHJzQ2hhcmdlLmRuQ3VycmVuY3lDb2RlICsgIiAiICsgcnNDaGFyZ2UuZG5Vbml0UmF0ZSArICIvIiArIHJzQ2hhcmdlLmRuVW5pdENvZGUgKyAiXG4iDQogICAgICAgICAgICAgICAgfSkNCiAgICAgICAgICAgICAgICA6IG51bGwNCiAgICAgICAgICAgIH0NCg0KICAgICAgICAgICAgdGhpcy5mb3JtLm5ld0Jvb2tpbmdUaW1lID0gbW9tZW50KCkuZm9ybWF0KCJ5eXl5LU1NLUREIEhIOm1tOnNzIikNCiAgICAgICAgICAgIHRoaXMuZm9ybS5wc2FWZXJpZnkgPSAwDQogICAgICAgICAgICBtZXNzYWdlID0gIuehruiupOiuouiIsSINCiAgICAgICAgICB9DQogICAgICAgICAgLy8g5pON5L2c56Gu6K6kDQogICAgICAgICAgaWYgKHRoaXMuJHJvdXRlLnF1ZXJ5LnR5cGUgPT09ICJvcCIgJiYgdHlwZSA9PT0gIm9wQ29uZmlybSIpIHsNCiAgICAgICAgICAgIGlmICh0aGlzLmZvcm0ucmN0Tm8gPT09IG51bGwpIHsNCiAgICAgICAgICAgICAgLy8gdGhpcy5nZW5lcmF0ZVJjdCh0cnVlKQ0KICAgICAgICAgICAgICAvLyDnlJ/miJByY3RObw0KICAgICAgICAgICAgICBpZiAodGhpcy5mb3JtLm9yZGVyQmVsb25nc1RvICYmIHRoaXMuZm9ybS5vcmRlckJlbG9uZ3NUbyA9PT0gIkdaQ0YiKSB7DQogICAgICAgICAgICAgICAgYXdhaXQgZ2V0UmN0Q0ZNb24oKS50aGVuKHYgPT4gew0KICAgICAgICAgICAgICAgICAgbGV0IG51bSA9IHYuZGF0YSArIDENCiAgICAgICAgICAgICAgICAgIGlmIChudW0udG9TdHJpbmcoKS5sZW5ndGggPCAzKSB7DQogICAgICAgICAgICAgICAgICAgIGNvbnN0IGogPSAzIC0gKG51bS50b1N0cmluZygpLmxlbmd0aCkNCiAgICAgICAgICAgICAgICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCBqOyBpKyspIHsNCiAgICAgICAgICAgICAgICAgICAgICBudW0gPSAiMCIgKyBudW0NCiAgICAgICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgICAgbGV0IGRhdGUgPSBuZXcgRGF0ZSgpDQogICAgICAgICAgICAgICAgICBsZXQgbW9udGggPSAoZGF0ZS5nZXRNb250aCgpICsgTnVtYmVyKHRoaXMucmN0Lm1vbnRoKSkudG9TdHJpbmcoKQ0KICAgICAgICAgICAgICAgICAgbGV0IHllYXIgPSAoZGF0ZS5nZXRGdWxsWWVhcigpICsgKG1vbnRoIC8gMTIgPiAxID8gMSA6IDApKS50b1N0cmluZygpLnN1YnN0cmluZygyLCA0KQ0KICAgICAgICAgICAgICAgICAgdGhpcy5mb3JtLnJjdE5vID0gdGhpcy5yY3QuR1pDRkxlYWRpbmdDaGFyYWN0ZXIgKyB5ZWFyICsgKG1vbnRoLmxlbmd0aCA9PSAxID8gIjAiICsgbW9udGggOiBtb250aCkgKyBudW0udG9TdHJpbmcoKQ0KICAgICAgICAgICAgICAgIH0pDQogICAgICAgICAgICAgIH0gZWxzZSBpZiAodGhpcy5mb3JtLm9yZGVyQmVsb25nc1RvICYmIHRoaXMuZm9ybS5vcmRlckJlbG9uZ3NUbyA9PT0gIlJTV0giKSB7DQogICAgICAgICAgICAgICAgYXdhaXQgZ2V0UmN0UlNXSE1vbigpLnRoZW4odiA9PiB7DQogICAgICAgICAgICAgICAgICBsZXQgbnVtID0gdi5kYXRhICsgMQ0KICAgICAgICAgICAgICAgICAgaWYgKG51bS50b1N0cmluZygpLmxlbmd0aCA8IDMpIHsNCiAgICAgICAgICAgICAgICAgICAgY29uc3QgaiA9IDMgLSAobnVtLnRvU3RyaW5nKCkubGVuZ3RoKQ0KICAgICAgICAgICAgICAgICAgICBmb3IgKGxldCBpID0gMDsgaSA8IGo7IGkrKykgew0KICAgICAgICAgICAgICAgICAgICAgIG51bSA9ICIwIiArIG51bQ0KICAgICAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgICAgICBsZXQgZGF0ZSA9IG5ldyBEYXRlKCkNCiAgICAgICAgICAgICAgICAgIGxldCBtb250aCA9IChkYXRlLmdldE1vbnRoKCkgKyBOdW1iZXIodGhpcy5yY3QubW9udGgpKS50b1N0cmluZygpDQogICAgICAgICAgICAgICAgICBsZXQgeWVhciA9IChkYXRlLmdldEZ1bGxZZWFyKCkgKyAobW9udGggLyAxMiA+IDEgPyAxIDogMCkpLnRvU3RyaW5nKCkuc3Vic3RyaW5nKDIsIDQpDQogICAgICAgICAgICAgICAgICB0aGlzLmZvcm0ucmN0Tm8gPSB0aGlzLnJjdC5SU1dITGVhZGluZ0NoYXJhY3RlciArIHllYXIgKyAobW9udGgubGVuZ3RoID09IDEgPyAiMCIgKyBtb250aCA6IG1vbnRoKSArIG51bS50b1N0cmluZygpDQogICAgICAgICAgICAgICAgfSkNCiAgICAgICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgICAgICBhd2FpdCBnZXRSY3RNb24oKS50aGVuKHYgPT4gew0KICAgICAgICAgICAgICAgICAgbGV0IG51bSA9IHYuZGF0YSArIDENCiAgICAgICAgICAgICAgICAgIGlmIChudW0udG9TdHJpbmcoKS5sZW5ndGggPCAzKSB7DQogICAgICAgICAgICAgICAgICAgIGNvbnN0IGogPSAzIC0gKG51bS50b1N0cmluZygpLmxlbmd0aCkNCiAgICAgICAgICAgICAgICAgICAgZm9yIChsZXQgaSA9IDA7IGkgPCBqOyBpKyspIHsNCiAgICAgICAgICAgICAgICAgICAgICBudW0gPSAiMCIgKyBudW0NCiAgICAgICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgICAgbGV0IGRhdGUgPSBuZXcgRGF0ZSgpDQogICAgICAgICAgICAgICAgICBsZXQgbW9udGggPSAoZGF0ZS5nZXRNb250aCgpICsgTnVtYmVyKHRoaXMucmN0Lm1vbnRoKSkudG9TdHJpbmcoKQ0KICAgICAgICAgICAgICAgICAgbGV0IHllYXIgPSAoZGF0ZS5nZXRGdWxsWWVhcigpICsgKG1vbnRoIC8gMTIgPiAxID8gMSA6IDApKS50b1N0cmluZygpLnN1YnN0cmluZygyLCA0KQ0KICAgICAgICAgICAgICAgICAgdGhpcy5mb3JtLnJjdE5vID0gdGhpcy5yY3QubGVhZGluZ0NoYXJhY3RlciArIHllYXIgKyAobW9udGgubGVuZ3RoID09IDEgPyAiMCIgKyBtb250aCA6IG1vbnRoKSArIG51bS50b1N0cmluZygpDQogICAgICAgICAgICAgICAgfSkNCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgfQ0KDQogICAgICAgICAgICAvLyDmjqXljZXml6XmnJ9UT0RPDQogICAgICAgICAgICB0aGlzLmZvcm0ucmN0Q3JlYXRlVGltZSA9IG1vbWVudCgpLmZvcm1hdCgieXl5eS1NTS1ERCBISDptbTpzcyIpDQogICAgICAgICAgICB0aGlzLmZvcm0ub3BBY2NlcHQgPSAxDQogICAgICAgICAgICB0aGlzLmZvcm0ucHJvY2Vzc1N0YXR1c0lkID0gNg0KICAgICAgICAgICAgbWVzc2FnZSA9ICLmk43kvZzmjqXljZXmiJDlip8iDQogICAgICAgICAgfQ0KDQogICAgICAgICAgLy8g566x5Z6L54m55b6BDQogICAgICAgICAgdGhpcy5mb3JtLmN0bnJUeXBlQ29kZSA9IHRoaXMuY3RuclR5cGVDb2RlSWRzLnRvU3RyaW5nKCkNCiAgICAgICAgICAvLyDotKfniannibnlvoENCiAgICAgICAgICB0aGlzLmZvcm0uY2FyZ29UeXBlQ29kZVN1bSA9IHRoaXMuY2FyZ29UeXBlQ29kZXMudG9TdHJpbmcoKQ0KDQogICAgICAgICAgLy8gIFRPRE8g5rW36L+Q5a2Q5pyN5Yqh5Ymp5LiA5Liq5pe25pu05paw5Yiw5Li76KGoDQoNCiAgICAgICAgICB0aGlzLmZvcm0uc2FsZXNJZCA9IHRoaXMuZm9ybS5zYWxlc0lkID8gdGhpcy5mb3JtLnNhbGVzSWQgOiB0aGlzLiRzdG9yZS5zdGF0ZS51c2VyLnNpZA0KDQogICAgICAgICAgLy8g5q+P5qyh5L+d5a2Y5pu05paw54q25oCB5pe26Ze0DQogICAgICAgICAgdGhpcy5mb3JtLnN0YXR1c1VwZGF0ZVRpbWUgPSBtb21lbnQoKS5mb3JtYXQoInl5eXktTU0tREQgSEg6bW06c3MiKQ0KICAgICAgICAgIGlmICh0aGlzLmZvcm0ucmN0SWQgIT0gbnVsbCkgew0KICAgICAgICAgICAgdXBkYXRlUmN0KHRoaXMuZm9ybSkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgICAgICAgIHRoaXMuc2F2ZUFsbCh0aGlzLmZvcm0ucmN0SWQpDQogICAgICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MobWVzc2FnZSkNCiAgICAgICAgICAgICAgLyogdGhpcy5vcGVuID0gZmFsc2UNCiAgICAgICAgICAgICAgdGhpcy5nZXRSY3RMaXN0KCkgKi8NCiAgICAgICAgICAgICAgdGhpcy5mb3JtLm5vVHJhbnNmZXJBbGxvd2VkID0gdGhpcy5mb3JtLm5vVHJhbnNmZXJBbGxvd2VkID8gdHJ1ZSA6IGZhbHNlDQogICAgICAgICAgICAgIHRoaXMuZm9ybS5ub0RpdmlkZWRBbGxvd2VkID0gdGhpcy5mb3JtLm5vRGl2aWRlZEFsbG93ZWQgPyB0cnVlIDogZmFsc2UNCiAgICAgICAgICAgICAgdGhpcy5mb3JtLm5vQWdyZWVtZW50U2hvd2VkID0gdGhpcy5mb3JtLm5vQWdyZWVtZW50U2hvd2VkID8gdHJ1ZSA6IGZhbHNlDQogICAgICAgICAgICAgIHRoaXMuZm9ybS5pc0N1c3RvbXNJbnRyYW5zaXRTaG93ZWQgPSB0aGlzLmZvcm0uaXNDdXN0b21zSW50cmFuc2l0U2hvd2VkID8gdHJ1ZSA6IGZhbHNlDQoNCiAgICAgICAgICAgICAgaWYgKHRoaXMuZm9ybS5vcEFjY2VwdCA9PSAxICYmICh0aGlzLmZvcm0uc2VydmljZVR5cGVJZHMuaW5kZXhPZigxKSAhPT0gLTEgfHwgdGhpcy5mb3JtLnNlcnZpY2VUeXBlSWRzLmluZGV4T2YoMikgIT09IC0xKSkgew0KICAgICAgICAgICAgICAgIC8vIFRPRE8NCiAgICAgICAgICAgICAgICAvLyDmm7TmlrDllYbliqHorqLoiLHkv6Hmga8NCiAgICAgICAgICAgICAgICB1cGRhdGVQc2FyY3Qoew0KICAgICAgICAgICAgICAgICAgLi4udGhpcy5mb3JtLCBub1RyYW5zZmVyQWxsb3dlZDogbnVsbCwNCiAgICAgICAgICAgICAgICAgIG5vRGl2aWRlZEFsbG93ZWQ6IG51bGwsDQogICAgICAgICAgICAgICAgICBub0FncmVlbWVudFNob3dlZDogbnVsbCwNCiAgICAgICAgICAgICAgICAgIGlzQ3VzdG9tc0ludHJhbnNpdFNob3dlZDogbnVsbA0KICAgICAgICAgICAgICAgIH0pDQogICAgICAgICAgICAgIH0NCiAgICAgICAgICAgIH0pDQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIC8vIOaTjeS9nOiuouiIseeahOaXtuWAmeWcqOWVhuWKoeiuouiIseS/oeaBr+S4reS5n+a3u+WKoOS4gOadoeiusOW9lQ0KICAgICAgICAgICAgLy8g5pON5L2c6K6i6Iix5bCx5LiN6ZyA6KaB5pON5L2c5o6l5Y2VLOebtOaOpeWHuueOsOWcqOaTjeS9nOWNleWIl+ihqA0KICAgICAgICAgICAgaWYgKHRoaXMuJHJvdXRlLnBhdGggPT09ICIvb3Bwcm9jZXNzL29wZGV0YWlsIiAmJiB0aGlzLmZvcm1UeXBlID09PSAicmN0Iikgew0KICAgICAgICAgICAgICBpZiAodGhpcy5mb3JtLnJjdE5vID09PSBudWxsKSB7DQogICAgICAgICAgICAgICAgLy8gdGhpcy5nZW5lcmF0ZVJjdCh0cnVlKQ0KICAgICAgICAgICAgICAgIC8vIOeUn+aIkHJjdE5vDQogICAgICAgICAgICAgICAgaWYgKHRoaXMuZm9ybS5vcmRlckJlbG9uZ3NUbyAmJiB0aGlzLmZvcm0ub3JkZXJCZWxvbmdzVG8gPT09ICJHWkNGIikgew0KICAgICAgICAgICAgICAgICAgYXdhaXQgZ2V0UmN0Q0ZNb24oKS50aGVuKHYgPT4gew0KICAgICAgICAgICAgICAgICAgICBsZXQgbnVtID0gdi5kYXRhICsgMQ0KICAgICAgICAgICAgICAgICAgICBpZiAobnVtLnRvU3RyaW5nKCkubGVuZ3RoIDwgMykgew0KICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IGogPSAzIC0gKG51bS50b1N0cmluZygpLmxlbmd0aCkNCiAgICAgICAgICAgICAgICAgICAgICBmb3IgKGxldCBpID0gMDsgaSA8IGo7IGkrKykgew0KICAgICAgICAgICAgICAgICAgICAgICAgbnVtID0gIjAiICsgbnVtDQogICAgICAgICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgICAgICAgIGxldCBkYXRlID0gbmV3IERhdGUoKQ0KICAgICAgICAgICAgICAgICAgICBsZXQgbW9udGggPSAoZGF0ZS5nZXRNb250aCgpICsgTnVtYmVyKHRoaXMucmN0Lm1vbnRoKSkudG9TdHJpbmcoKQ0KICAgICAgICAgICAgICAgICAgICBsZXQgeWVhciA9IChkYXRlLmdldEZ1bGxZZWFyKCkgKyAobW9udGggLyAxMiA+IDEgPyAxIDogMCkpLnRvU3RyaW5nKCkuc3Vic3RyaW5nKDIsIDQpDQogICAgICAgICAgICAgICAgICAgIHRoaXMuZm9ybS5yY3RObyA9IHRoaXMucmN0LkdaQ0ZMZWFkaW5nQ2hhcmFjdGVyICsgeWVhciArIChtb250aC5sZW5ndGggPT0gMSA/ICIwIiArIG1vbnRoIDogbW9udGgpICsgbnVtLnRvU3RyaW5nKCkNCiAgICAgICAgICAgICAgICAgIH0pDQogICAgICAgICAgICAgICAgfSBlbHNlIGlmICh0aGlzLmZvcm0ub3JkZXJCZWxvbmdzVG8gJiYgdGhpcy5mb3JtLm9yZGVyQmVsb25nc1RvID09PSAiUlNXSCIpIHsNCiAgICAgICAgICAgICAgICAgIGF3YWl0IGdldFJjdFJTV0hNb24oKS50aGVuKHYgPT4gew0KICAgICAgICAgICAgICAgICAgICBsZXQgbnVtID0gdi5kYXRhICsgMQ0KICAgICAgICAgICAgICAgICAgICBpZiAobnVtLnRvU3RyaW5nKCkubGVuZ3RoIDwgMykgew0KICAgICAgICAgICAgICAgICAgICAgIGNvbnN0IGogPSAzIC0gKG51bS50b1N0cmluZygpLmxlbmd0aCkNCiAgICAgICAgICAgICAgICAgICAgICBmb3IgKGxldCBpID0gMDsgaSA8IGo7IGkrKykgew0KICAgICAgICAgICAgICAgICAgICAgICAgbnVtID0gIjAiICsgbnVtDQogICAgICAgICAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgICAgICAgIGxldCBkYXRlID0gbmV3IERhdGUoKQ0KICAgICAgICAgICAgICAgICAgICBsZXQgbW9udGggPSAoZGF0ZS5nZXRNb250aCgpICsgTnVtYmVyKHRoaXMucmN0Lm1vbnRoKSkudG9TdHJpbmcoKQ0KICAgICAgICAgICAgICAgICAgICBsZXQgeWVhciA9IChkYXRlLmdldEZ1bGxZZWFyKCkgKyAobW9udGggLyAxMiA+IDEgPyAxIDogMCkpLnRvU3RyaW5nKCkuc3Vic3RyaW5nKDIsIDQpDQogICAgICAgICAgICAgICAgICAgIHRoaXMuZm9ybS5yY3RObyA9IHRoaXMucmN0LlJTV0hMZWFkaW5nQ2hhcmFjdGVyICsgeWVhciArIChtb250aC5sZW5ndGggPT0gMSA/ICIwIiArIG1vbnRoIDogbW9udGgpICsgbnVtLnRvU3RyaW5nKCkNCiAgICAgICAgICAgICAgICAgIH0pDQogICAgICAgICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgICAgICAgIGF3YWl0IGdldFJjdE1vbigpLnRoZW4odiA9PiB7DQogICAgICAgICAgICAgICAgICAgIGxldCBudW0gPSB2LmRhdGEgKyAxDQogICAgICAgICAgICAgICAgICAgIGlmIChudW0udG9TdHJpbmcoKS5sZW5ndGggPCAzKSB7DQogICAgICAgICAgICAgICAgICAgICAgY29uc3QgaiA9IDMgLSAobnVtLnRvU3RyaW5nKCkubGVuZ3RoKQ0KICAgICAgICAgICAgICAgICAgICAgIGZvciAobGV0IGkgPSAwOyBpIDwgajsgaSsrKSB7DQogICAgICAgICAgICAgICAgICAgICAgICBudW0gPSAiMCIgKyBudW0NCiAgICAgICAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICAgICAgbGV0IGRhdGUgPSBuZXcgRGF0ZSgpDQogICAgICAgICAgICAgICAgICAgIGxldCBtb250aCA9IChkYXRlLmdldE1vbnRoKCkgKyBOdW1iZXIodGhpcy5yY3QubW9udGgpKS50b1N0cmluZygpDQogICAgICAgICAgICAgICAgICAgIGxldCB5ZWFyID0gKGRhdGUuZ2V0RnVsbFllYXIoKSArIChtb250aCAvIDEyID4gMSA/IDEgOiAwKSkudG9TdHJpbmcoKS5zdWJzdHJpbmcoMiwgNCkNCiAgICAgICAgICAgICAgICAgICAgdGhpcy5mb3JtLnJjdE5vID0gdGhpcy5yY3QubGVhZGluZ0NoYXJhY3RlciArIHllYXIgKyAobW9udGgubGVuZ3RoID09IDEgPyAiMCIgKyBtb250aCA6IG1vbnRoKSArIG51bS50b1N0cmluZygpDQogICAgICAgICAgICAgICAgICB9KQ0KICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgfQ0KICAgICAgICAgICAgICB0aGlzLmZvcm0ucmN0Q3JlYXRlVGltZSA9IG1vbWVudCgpLmZvcm1hdCgieXl5eS1NTS1ERCBISDptbTpzcyIpDQogICAgICAgICAgICAgIHRoaXMuZm9ybS5vcEFjY2VwdCA9IDENCiAgICAgICAgICAgICAgdGhpcy5mb3JtLnByb2Nlc3NTdGF0dXNJZCA9IDYNCiAgICAgICAgICAgICAgLy8g5pON5L2c6Ieq5a6a6IixDQogICAgICAgICAgICAgIHRoaXMuZm9ybS5zcWRTaGlwcGluZ0Jvb2tpbmdTdGF0dXMgPSAxDQogICAgICAgICAgICAgIHRoaXMuZm9ybS5wc2FWZXJpZnlTdGF0dXNJZCA9IDENCiAgICAgICAgICAgICAgdGhpcy5mb3JtLnBzYVZlcmlmeSA9IDENCg0KICAgICAgICAgICAgICBsZXQgZGF0YSA9IHRoaXMuZm9ybQ0KICAgICAgICAgICAgICBkYXRhLmJvb2tpbmdJZCA9IHRoaXMuJHN0b3JlLnN0YXRlLnVzZXIuc2lkDQogICAgICAgICAgICAgIGRhdGEuYm9va2luZ1N0YXR1cyA9ICIxIiAvLyDlt7LorqLoiLENCiAgICAgICAgICAgICAgZGF0YS5ib29raW5nVGltZSA9IG1vbWVudCgpLmZvcm1hdCgieXl5eS1NTS1ERCBISDptbTpzcyIpDQogICAgICAgICAgICAgIGRhdGEuZGlzdHJpYnV0aW9uU3RhdHVzID0gIjIiIC8vIOaTjeS9nOiHquiuouiIsQ0KICAgICAgICAgICAgICAvLyDkuLvotLnnlKjluIHnp43jgIHljZXku7fjgIHljZXkvY0gPSDkvovvvJp1c2Q4NjAwLzQwSFENCiAgICAgICAgICAgICAgZGF0YS5yY3RObyA9IHRoaXMuZm9ybS5yY3RObw0KICAgICAgICAgICAgICBkYXRhLmNsaWVudFNob3J0TmFtZSA9IHRoaXMuZm9ybS5jbGllbnROYW1lLnNwbGl0KCIvIilbMV0NCiAgICAgICAgICAgICAgZGF0YS5zYWxlc0lkID0gdGhpcy5mb3JtLnNhbGVzSWQNCiAgICAgICAgICAgICAgZGF0YS5zYWxlc0Fzc2lzdGFudElkID0gdGhpcy5mb3JtLnNhbGVzQXNzaXN0YW50SWQNCiAgICAgICAgICAgICAgZGF0YS5vcElkID0gdGhpcy4kc3RvcmUuc3RhdGUudXNlci5zaWQNCg0KICAgICAgICAgICAgICBhZGRQc2FyY3QoZGF0YSkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgICAgICAgIH0pDQogICAgICAgICAgICB9DQogICAgICAgICAgICBhZGRSY3QodGhpcy5mb3JtKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgICAgICAgdGhpcy5mb3JtLnJjdElkID0gcmVzcG9uc2UuZGF0YQ0KICAgICAgICAgICAgICB0aGlzLnNhdmVBbGwocmVzcG9uc2UuZGF0YSkNCiAgICAgICAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5paw5aKe5oiQ5YqfIikNCiAgICAgICAgICAgICAgLyogIHRoaXMub3BlbiA9IGZhbHNlDQogICAgICAgICAgICAgIHRoaXMuZ2V0UmN0TGlzdCgpICovDQogICAgICAgICAgICAgIHRoaXMuZm9ybS5ub1RyYW5zZmVyQWxsb3dlZCA9IHRoaXMuZm9ybS5ub1RyYW5zZmVyQWxsb3dlZCA/IHRydWUgOiBmYWxzZQ0KICAgICAgICAgICAgICB0aGlzLmZvcm0ubm9EaXZpZGVkQWxsb3dlZCA9IHRoaXMuZm9ybS5ub0RpdmlkZWRBbGxvd2VkID8gdHJ1ZSA6IGZhbHNlDQogICAgICAgICAgICAgIHRoaXMuZm9ybS5ub0FncmVlbWVudFNob3dlZCA9IHRoaXMuZm9ybS5ub0FncmVlbWVudFNob3dlZCA/IHRydWUgOiBmYWxzZQ0KICAgICAgICAgICAgICB0aGlzLmZvcm0uaXNDdXN0b21zSW50cmFuc2l0U2hvd2VkID0gdGhpcy5mb3JtLmlzQ3VzdG9tc0ludHJhbnNpdFNob3dlZCA/IHRydWUgOiBmYWxzZQ0KDQogICAgICAgICAgICAgIC8vIOaWsOWinueahOihqOaYr3JjdOS4jeaYr2Jvb2tpbmcs5omA5Lul5Zyw5Z2A5qCP5Y+Y5YyWDQogICAgICAgICAgICAgIC8vIHRoaXMuJHRhYi5vcGVuUGFnZSgi6K6i6Iix5Y2V5piO57uGIiwgIi9zYWxlc3F1b3RhdGlvbi9ib29raW5nRGV0YWlsIiwge3JJZDogcmVzcG9uc2UuZGF0YSwgYm9va2luZzogdHJ1ZX0pDQogICAgICAgICAgICB9KQ0KICAgICAgICAgIH0NCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoIuivt+WujOaVtOWhq+WGmeaTjeS9nOWNlSIpDQogICAgICAgIH0NCiAgICAgIH0pDQogICAgfSwNCiAgICBzYXZlQXMoKSB7DQogICAgICBpZiAodGhpcy5zZXJ2aWNlTGlzdC5oYXMoMjApKSB7DQogICAgICAgIHRoaXMucnNPcFJhaWxGQ0wucnNTZXJ2aWNlSW5zdGFuY2VzID0gdGhpcy5yc09wUmFpbEZjbFNlcnZpY2VJbnN0YW5jZQ0KICAgICAgICB0aGlzLmZvcm0ucnNPcFJhaWxGQ0wgPSB0aGlzLnJzT3BSYWlsRkNMDQogICAgICB9DQogICAgICBpZiAodGhpcy5zZXJ2aWNlTGlzdC5oYXMoMjEpKSB7DQogICAgICAgIHRoaXMucnNPcFJhaWxMQ0wucnNTZXJ2aWNlSW5zdGFuY2VzID0gdGhpcy5yc09wUmFpbExjbFNlcnZpY2VJbnN0YW5jZQ0KICAgICAgICB0aGlzLmZvcm0ucnNPcFJhaWxMQ0wgPSB0aGlzLnJzT3BSYWlsTENMDQogICAgICB9DQogICAgICBpZiAodGhpcy5zZXJ2aWNlTGlzdC5oYXMoNDApKSB7DQogICAgICAgIHRoaXMucnNPcEV4cHJlc3MucnNTZXJ2aWNlSW5zdGFuY2VzID0gdGhpcy5yc09wRXhwcmVzc1NlcnZpY2VJbnN0YW5jZQ0KICAgICAgICB0aGlzLmZvcm0ucnNPcEV4cHJlc3MgPSB0aGlzLnJzT3BFeHByZXNzDQogICAgICB9DQogICAgICBpZiAodGhpcy5zZXJ2aWNlTGlzdC5oYXMoNzApKSB7DQogICAgICAgIHRoaXMucnNPcERPQWdlbnQucnNTZXJ2aWNlSW5zdGFuY2VzID0gdGhpcy5yc09wRE9BZ2VudFNlcnZpY2VJbnN0YW5jZQ0KICAgICAgICB0aGlzLmZvcm0ucnNPcERPQWdlbnQgPSB0aGlzLnJzT3BET0FnZW50DQogICAgICB9DQogICAgICBpZiAodGhpcy5zZXJ2aWNlTGlzdC5oYXMoNzEpKSB7DQogICAgICAgIHRoaXMucnNPcENsZWFyQWdlbnQucnNTZXJ2aWNlSW5zdGFuY2VzID0gdGhpcy5yc09wQ2xlYXJBZ2VudFNlcnZpY2VJbnN0YW5jZQ0KICAgICAgICB0aGlzLmZvcm0ucnNPcENsZWFyQWdlbnQgPSB0aGlzLnJzT3BDbGVhckFnZW50DQogICAgICB9DQogICAgICBpZiAodGhpcy5zZXJ2aWNlTGlzdC5oYXMoODApKSB7DQogICAgICAgIHRoaXMucnNPcFdIUy5yc1NlcnZpY2VJbnN0YW5jZXMgPSB0aGlzLnJzT3BXSFNTZXJ2aWNlSW5zdGFuY2UNCiAgICAgICAgdGhpcy5mb3JtLnJzT3BXSFMgPSB0aGlzLnJzT3BXSFMNCiAgICAgIH0NCiAgICAgIGlmICh0aGlzLnNlcnZpY2VMaXN0Lmhhcyg5MCkpIHsNCiAgICAgICAgdGhpcy5yc09wM3JkQ2VydC5yc1NlcnZpY2VJbnN0YW5jZXMgPSB0aGlzLnJzT3AzcmRDZXJ0U2VydmljZUluc3RhbmNlDQogICAgICAgIHRoaXMuZm9ybS5yc09wM3JkQ2VydCA9IHRoaXMucnNPcDNyZENlcnQNCiAgICAgIH0NCiAgICAgIGlmICh0aGlzLnNlcnZpY2VMaXN0LmhhcygxMDApKSB7DQogICAgICAgIHRoaXMucnNPcElOUy5yc1NlcnZpY2VJbnN0YW5jZXMgPSB0aGlzLnJzT3BJTlNTZXJ2aWNlSW5zdGFuY2UNCiAgICAgICAgdGhpcy5mb3JtLnJzT3BJTlMgPSB0aGlzLnJzT3BJTlMNCiAgICAgIH0NCiAgICAgIGlmICh0aGlzLnNlcnZpY2VMaXN0LmhhcygxMDEpKSB7DQogICAgICAgIHRoaXMucnNPcFRyYWRpbmcucnNTZXJ2aWNlSW5zdGFuY2VzID0gdGhpcy5yc09wVHJhZGluZ1NlcnZpY2VJbnN0YW5jZQ0KICAgICAgICB0aGlzLmZvcm0ucnNPcFRyYWRpbmcgPSB0aGlzLnJzT3BUcmFkaW5nDQogICAgICB9DQogICAgICBpZiAodGhpcy5zZXJ2aWNlTGlzdC5oYXMoMTAyKSkgew0KICAgICAgICB0aGlzLnJzT3BGdW1pZ2F0aW9uLnJzU2VydmljZUluc3RhbmNlcyA9IHRoaXMucnNPcEZ1bWlnYXRpb25TZXJ2aWNlSW5zdGFuY2UNCiAgICAgICAgdGhpcy5mb3JtLnJzT3BGdW1pZ2F0aW9uID0gdGhpcy5yc09wRnVtaWdhdGlvbg0KICAgICAgfQ0KICAgICAgaWYgKHRoaXMuc2VydmljZUxpc3QuaGFzKDEwMykpIHsNCiAgICAgICAgdGhpcy5yc09wQ08ucnNTZXJ2aWNlSW5zdGFuY2VzID0gdGhpcy5yc09wQ09TZXJ2aWNlSW5zdGFuY2UNCiAgICAgICAgdGhpcy5mb3JtLnJzT3BDTyA9IHRoaXMucnNPcENPDQogICAgICB9DQogICAgICBpZiAodGhpcy5zZXJ2aWNlTGlzdC5oYXMoMTA0KSkgew0KICAgICAgICB0aGlzLnJzT3BPdGhlci5yc1NlcnZpY2VJbnN0YW5jZXMgPSB0aGlzLnJzT3BPdGhlclNlcnZpY2VJbnN0YW5jZQ0KICAgICAgICB0aGlzLmZvcm0ucnNPcE90aGVyID0gdGhpcy5yc09wT3RoZXINCiAgICAgIH0NCg0KICAgICAgdGhpcy5mb3JtLnJlbGF0aW9uQ2xpZW50SWRMaXN0ID0gdGhpcy5SZWxhdGlvbkNsaWVudElkTGlzdC50b1N0cmluZygpDQogICAgICB0aGlzLmZvcm0uc2VydmljZVR5cGVJZExpc3QgPSB0aGlzLnNlcnZpY2VMaXN0LnRvU3RyaW5nKCkNCiAgICAgIC8vIFRPRE8NCiAgICAgIGlmICgodGhpcy5yc09wQnVsa1RydWNrLnJzT3BUcnVja0xpc3QgJiYgdGhpcy5yc09wQnVsa1RydWNrLnJzT3BUcnVja0xpc3QpIHx8ICh0aGlzLnJzT3BDdG5yVHJ1Y2sucnNPcFRydWNrTGlzdCAmJiB0aGlzLnJzT3BDdG5yVHJ1Y2sucnNPcFRydWNrTGlzdC5sZW5ndGggPiAwKSkgew0KICAgICAgICBpZiAodGhpcy5zZXJ2aWNlTGlzdC5oYXMoNTApKSB7DQogICAgICAgICAgdGhpcy5mb3JtLnJzT3BDdG5yVHJ1Y2tMaXN0Lm1hcChpdGVtID0+IHsNCiAgICAgICAgICAgIGl0ZW0uc3FkQ29udGFpbmVyc1NlYWxzU3VtID0gaXRlbS5yc09wVHJ1Y2tMaXN0Lm1hcCh2ID0+IHYuY29udGFpbmVyTm8pLmpvaW4oIiwiKQ0KICAgICAgICAgIH0pDQogICAgICAgIH0gZWxzZSBpZiAodGhpcy5zZXJ2aWNlTGlzdC5oYXMoNTEpKSB7DQogICAgICAgICAgdGhpcy5mb3JtLnJzT3BCdWxrVHJ1Y2tMaXN0Lm1hcChpdGVtID0+IHsNCiAgICAgICAgICAgIGl0ZW0uc3FkQ29udGFpbmVyc1NlYWxzU3VtID0gaXRlbS5yc09wVHJ1Y2tMaXN0Lm1hcCh2ID0+IHYuY29udGFpbmVyTm8pLmpvaW4oIiwiKQ0KICAgICAgICAgIH0pDQogICAgICAgIH0NCiAgICAgIH0NCg0KICAgICAgLy8g5o+Q5Y2V5L+h5oGv5YiX6KGoDQogICAgICB0aGlzLmZvcm0uYm9va2luZ01lc3NhZ2VzTGlzdCA9IHRoaXMuYm9va2luZ01lc3NhZ2VMaXN0DQoNCiAgICAgIC8vIOWIm+W7uuS4gOS4qlNldOadpeWtmOWCqOaJgOacieS4jemHjeWkjeeahOmbhuijheeuseWPtw0KICAgICAgbGV0IGFsbENvbnRhaW5lcnMgPSBuZXcgU2V0KCkNCg0KICAgICAgLy8g6aaW5YWI5re75Yqg6KGo5Y2V5Lit5bey5pyJ55qE6ZuG6KOF566x5Y+3DQogICAgICBpZiAodGhpcy5mb3JtLnNxZENvbnRhaW5lcnNTZWFsc1N1bSkgew0KICAgICAgICB0aGlzLmZvcm0uc3FkQ29udGFpbmVyc1NlYWxzU3VtLnNwbGl0KCIvIikNCiAgICAgICAgICAubWFwKGNvbnRhaW5lciA9PiBjb250YWluZXIudHJpbSgpKQ0KICAgICAgICAgIC5maWx0ZXIoY29udGFpbmVyID0+IGNvbnRhaW5lcikNCiAgICAgICAgICAuZm9yRWFjaChjb250YWluZXIgPT4gYWxsQ29udGFpbmVycy5hZGQoY29udGFpbmVyKSkNCiAgICAgIH0NCg0KICAgICAgLy8g54S25ZCO5re75YqgYm9va2luZ01lc3NhZ2VMaXN05Lit55qE6ZuG6KOF566x5Y+3DQogICAgICBpZiAodGhpcy5ib29raW5nTWVzc2FnZUxpc3QgJiYgdGhpcy5ib29raW5nTWVzc2FnZUxpc3QubGVuZ3RoID4gMCkgew0KICAgICAgICB0aGlzLmJvb2tpbmdNZXNzYWdlTGlzdC5mb3JFYWNoKGl0ZW0gPT4gew0KICAgICAgICAgIGlmIChpdGVtLmNvbnRhaW5lck5vKSB7DQogICAgICAgICAgICBpdGVtLmNvbnRhaW5lck5vLnNwbGl0KCIvIikNCiAgICAgICAgICAgICAgLm1hcChjb250YWluZXIgPT4gY29udGFpbmVyLnRyaW0oKSkNCiAgICAgICAgICAgICAgLmZpbHRlcihjb250YWluZXIgPT4gY29udGFpbmVyKQ0KICAgICAgICAgICAgICAuZm9yRWFjaChjb250YWluZXIgPT4gYWxsQ29udGFpbmVycy5hZGQoY29udGFpbmVyKSkNCiAgICAgICAgICB9DQogICAgICAgIH0pDQogICAgICB9DQoNCiAgICAgIC8vIOacgOWQju+8jOWwhlNldOi9rOaNouS4uumAl+WPt+WIhumalOeahOWtl+espuS4sg0KICAgICAgdGhpcy5mb3JtLnNxZENvbnRhaW5lcnNTZWFsc1N1bSA9IEFycmF5LmZyb20oYWxsQ29udGFpbmVycykuam9pbigiLyIpDQoNCiAgICAgIC8vIOWQjOagt+WkhOeQhmJsTm8NCiAgICAgIGxldCBhbGxCbE5vcyA9IG5ldyBTZXQoKQ0KDQogICAgICAvLyDmt7vliqDooajljZXkuK3lt7LmnInnmoTmj5DljZXlj7cNCiAgICAgIGlmICh0aGlzLmZvcm0uYmxObykgew0KICAgICAgICBhbGxCbE5vcy5hZGQodGhpcy5mb3JtLmJsTm8udHJpbSgpKQ0KICAgICAgfQ0KDQogICAgICAvLyDmt7vliqBib29raW5nTWVzc2FnZUxpc3TkuK3nmoTmj5DljZXlj7cNCiAgICAgIGlmICh0aGlzLmJvb2tpbmdNZXNzYWdlTGlzdCAmJiB0aGlzLmJvb2tpbmdNZXNzYWdlTGlzdC5sZW5ndGggPiAwKSB7DQogICAgICAgIHRoaXMuYm9va2luZ01lc3NhZ2VMaXN0LmZvckVhY2goaXRlbSA9PiB7DQogICAgICAgICAgaWYgKGl0ZW0ubUJsTm8pIHsNCiAgICAgICAgICAgIGFsbEJsTm9zLmFkZChpdGVtLm1CbE5vLnRyaW0oKSkNCiAgICAgICAgICB9DQogICAgICAgIH0pDQogICAgICB9DQoNCiAgICAgIC8vIOWwhlNldOi9rOaNouS4uumAl+WPt+WIhumalOeahOWtl+espuS4sg0KICAgICAgdGhpcy5mb3JtLmJsTm8gPSBBcnJheS5mcm9tKGFsbEJsTm9zKS5maWx0ZXIoYmxObyA9PiBibE5vKS5qb2luKCIvIikNCg0KICAgICAgbGV0IGV4Y2hhbmdlUmF0ZQ0KICAgICAgZm9yIChjb25zdCBhIG9mIHRoaXMuJHN0b3JlLnN0YXRlLmRhdGEuZXhjaGFuZ2VSYXRlTGlzdCkgew0KICAgICAgICBpZiAodGhpcy5mb3JtLnBvZEV0YSkgew0KICAgICAgICAgIGlmIChhLmxvY2FsQ3VycmVuY3kgPT09ICJSTUIiDQogICAgICAgICAgICAmJiAiVVNEIiA9PSBhLm92ZXJzZWFDdXJyZW5jeQ0KICAgICAgICAgICAgJiYgcGFyc2VUaW1lKGEudmFsaWRGcm9tKSA8PSBwYXJzZVRpbWUodGhpcy5mb3JtLnBvZEV0YSkNCiAgICAgICAgICAgICYmIHBhcnNlVGltZSh0aGlzLmZvcm0ucG9kRXRhKSA8PSBwYXJzZVRpbWUoYS52YWxpZFRvKQ0KICAgICAgICAgICkgew0KICAgICAgICAgICAgZXhjaGFuZ2VSYXRlID0gY3VycmVuY3koYS5zZXR0bGVSYXRlKS5kaXZpZGUoYS5iYXNlKS52YWx1ZQ0KICAgICAgICAgIH0NCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICBpZiAoYS5sb2NhbEN1cnJlbmN5ID09PSAiUk1CIg0KICAgICAgICAgICAgJiYgIlVTRCIgPT0gYS5vdmVyc2VhQ3VycmVuY3kNCiAgICAgICAgICAgICYmIHBhcnNlVGltZShhLnZhbGlkRnJvbSkgPD0gcGFyc2VUaW1lKG5ldyBEYXRlKCkpDQogICAgICAgICAgICAmJiBwYXJzZVRpbWUobmV3IERhdGUoKSkgPD0gcGFyc2VUaW1lKGEudmFsaWRUbykpIHsNCiAgICAgICAgICAgIGV4Y2hhbmdlUmF0ZSA9IGN1cnJlbmN5KGEuc2V0dGxlUmF0ZSkuZGl2aWRlKGEuYmFzZSkudmFsdWUNCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgIH0NCg0KICAgICAgLy8g6LS555SoDQogICAgICB0aGlzLmZvcm0uZG5SbWIgPSB0aGlzLnJzQ2xpZW50TWVzc2FnZVJlY2VpdmFibGVUYXhSTUIgLy8gcm1i5ZCr56iO5bqU5pS2DQogICAgICB0aGlzLmZvcm0uZG5Vc2QgPSB0aGlzLnJzQ2xpZW50TWVzc2FnZVJlY2VpdmFibGVUYXhVU0QgLy8gdXNk5ZCr56iO5bqU5pS2DQogICAgICB0aGlzLmZvcm0uY25SbWIgPSB0aGlzLnJzQ2xpZW50TWVzc2FnZVBheWFibGVUYXhSTUIgLy8gcm1i5ZCr56iO5bqU5LuYDQogICAgICB0aGlzLmZvcm0uY25Vc2QgPSB0aGlzLnJzQ2xpZW50TWVzc2FnZVBheWFibGVUYXhVU0QgLy8gdXNk5ZCr56iO5bqU5LuYDQogICAgICAvLyDmipjlkIjotLnnlKgNCiAgICAgIHRoaXMuZm9ybS5kbkluUm1iID0gY3VycmVuY3kodGhpcy5yc0NsaWVudE1lc3NhZ2VSZWNlaXZhYmxlVGF4Uk1CKS5hZGQoY3VycmVuY3kodGhpcy5yc0NsaWVudE1lc3NhZ2VSZWNlaXZhYmxlVGF4VVNEKS5tdWx0aXBseShleGNoYW5nZVJhdGUpKS52YWx1ZSAvLyDmipjlkIhybWLlkKvnqI7lupTmlLYNCiAgICAgIHRoaXMuZm9ybS5jbkluUm1iID0gY3VycmVuY3kodGhpcy5yc0NsaWVudE1lc3NhZ2VQYXlhYmxlVGF4Uk1CKS5hZGQoY3VycmVuY3kodGhpcy5yc0NsaWVudE1lc3NhZ2VQYXlhYmxlVGF4VVNEKS5tdWx0aXBseShleGNoYW5nZVJhdGUpKS52YWx1ZSAvLyDmipjlkIhybWLlkKvnqI7lupTku5gNCg0KICAgICAgLy8g57uf6K6h5pS25qy+5ZKM5LuY5qy+6L+b5bqmDQogICAgICB0aGlzLmZvcm0uZG5SbWJCYWxhbmNlID0gdGhpcy5zcWRVbnJlY2VpdmVkUm1iU3VtIC8vIHJtYuWQq+eojuacquaUtg0KICAgICAgdGhpcy5mb3JtLmRuVXNkQmFsYW5jZSA9IHRoaXMuc3FkVW5yZWNlaXZlZFVzZFN1bSAvLyB1c2TlkKvnqI7mnKrmlLYNCiAgICAgIHRoaXMuZm9ybS5jblJtYkJhbGFuY2UgPSB0aGlzLnNxZFVucGFpZFJtYlN1bSAvLyBybWLlkKvnqI7mnKrku5gNCiAgICAgIHRoaXMuZm9ybS5jblVzZEJhbGFuY2UgPSB0aGlzLnNxZFVucGFpZFVzZFN1bSAvLyB1c2TlkKvnqI7mnKrku5gNCiAgICAgIC8vIOaKmOWQiOi/m+W6pg0KICAgICAgdGhpcy5mb3JtLmRuSW5SbWJCYWxhbmNlID0gY3VycmVuY3kodGhpcy5zcWRVbnJlY2VpdmVkUm1iU3VtKS5hZGQoY3VycmVuY3kodGhpcy5zcWRVbnJlY2VpdmVkVXNkU3VtKS5tdWx0aXBseShleGNoYW5nZVJhdGUpKS52YWx1ZSAvLyDmipjlkIhybWLmnKrmlLYNCiAgICAgIHRoaXMuZm9ybS5jbkluUm1iQmFsYW5jZSA9IGN1cnJlbmN5KHRoaXMuc3FkVW5wYWlkUm1iU3VtKS5hZGQoY3VycmVuY3kodGhpcy5zcWRVbnBhaWRVc2RTdW0pLm11bHRpcGx5KGV4Y2hhbmdlUmF0ZSkpLnZhbHVlIC8vIOaKmOWQiHJtYuacquS7mA0KDQogICAgICAvLyDliKnmtqYNCiAgICAgIHRoaXMuZm9ybS5wcm9maXRVc2QgPSBjdXJyZW5jeSh0aGlzLmZvcm0uZG5Vc2QpLnN1YnRyYWN0KHRoaXMuZm9ybS5jblVzZCkudmFsdWUgLy8g576O5YWD6YOo5YiG5ZCr56iO5Yip5ramDQogICAgICB0aGlzLmZvcm0ucHJvZml0Um1iID0gY3VycmVuY3kodGhpcy5mb3JtLmRuUm1iKS5zdWJ0cmFjdCh0aGlzLmZvcm0uY25SbWIpLnZhbHVlIC8vIOS6uuawkeW4gemDqOWIhuWQq+eojuWIqea2pg0KICAgICAgdGhpcy5mb3JtLnByb2ZpdEluUm1iID0gY3VycmVuY3kodGhpcy5mb3JtLnByb2ZpdFVzZCkubXVsdGlwbHkoZXhjaGFuZ2VSYXRlKS5hZGQodGhpcy5mb3JtLnByb2ZpdFJtYikudmFsdWUgLy8g5oqY5ZCI5Lq65rCR5biB5Yip5ramDQoNCiAgICAgIHRoaXMuZm9ybS5ub1RyYW5zZmVyQWxsb3dlZCA9IHRoaXMuZm9ybS5ub1RyYW5zZmVyQWxsb3dlZCA/IDEgOiAwDQogICAgICB0aGlzLmZvcm0ubm9EaXZpZGVkQWxsb3dlZCA9IHRoaXMuZm9ybS5ub0RpdmlkZWRBbGxvd2VkID8gMSA6IDANCiAgICAgIHRoaXMuZm9ybS5ub0FncmVlbWVudFNob3dlZCA9IHRoaXMuZm9ybS5ub0FncmVlbWVudFNob3dlZCA/IDEgOiAwDQogICAgICB0aGlzLmZvcm0uaXNDdXN0b21zSW50cmFuc2l0U2hvd2VkID0gdGhpcy5mb3JtLmlzQ3VzdG9tc0ludHJhbnNpdFNob3dlZCA/IDEgOiAwDQoNCiAgICAgIHRoaXMuZm9ybS5tZXNzYWdlRGlzcGxheSA9IFtOdW1iZXIodGhpcy5zZXJ2aWNlSW5mbyksIE51bWJlcih0aGlzLm9yZGVySW5mbyksIE51bWJlcih0aGlzLmJyYW5jaEluZm8pLCBOdW1iZXIodGhpcy5sb2dpc3RpY3NJbmZvKSwgTnVtYmVyKHRoaXMuZG9jSW5mbyksIE51bWJlcih0aGlzLmNoYXJnZUluZm8pLCBOdW1iZXIodGhpcy5hdWRpdEluZm8pXS50b1N0cmluZygpDQogICAgICB0aGlzLmZvcm0uc2VydmljZU1lc3NhZ2VGb2xkID0gW051bWJlcih0aGlzLmNsaWVudE1lc3NhZ2UpLCBOdW1iZXIodGhpcy5yc09wU2VhbEZjbEZvbGQpLCBOdW1iZXIodGhpcy5yc09wU2VhbExjbEZvbGQpLCBOdW1iZXIodGhpcy5yc09wQWlyRm9sZCksIE51bWJlcih0aGlzLnJzT3BSYWlsRmNsRm9sZCksIE51bWJlcih0aGlzLnJzT3BSYWlsTGNsRm9sZCksIE51bWJlcih0aGlzLnJzT3BFeHByZXNzRm9sZCksIE51bWJlcih0aGlzLnJzT3BDdG5yVHJ1Y2tGb2xkKSwgTnVtYmVyKHRoaXMucnNPcEJ1bGtUcnVja0ZvbGQpLCBOdW1iZXIodGhpcy5yc09wRG9jRGVjbGFyZUZvbGQpLCBOdW1iZXIodGhpcy5yc09wRnJlZURlY2xhcmVGb2xkKSwgTnVtYmVyKHRoaXMucnNPcERPQWdlbnRGb2xkKSwgTnVtYmVyKHRoaXMucnNPcENsZWFyQWdlbnRGb2xkKSwgTnVtYmVyKHRoaXMucnNPcFdIU0ZvbGQpLCBOdW1iZXIodGhpcy5yc09wM3JkQ2VydEZvbGQpLCBOdW1iZXIodGhpcy5yc09wSU5TRm9sZCksIE51bWJlcih0aGlzLnJzT3BUcmFkaW5nRm9sZCksIE51bWJlcih0aGlzLnJzT3BGdW1pZ2F0aW9uRm9sZCksIE51bWJlcih0aGlzLnJzT3BDT0ZvbGQpLCBOdW1iZXIodGhpcy5yc09wT3RoZXJGb2xkKV0udG9TdHJpbmcoKQ0KDQogICAgICB0aGlzLmZvcm0ucnNPcFNlYUZjbExpc3QgPyB0aGlzLmZvcm0ucnNPcFNlYUZjbExpc3QubWFwKGl0ZW0gPT4gew0KICAgICAgICBpdGVtLnJzU2VydmljZUluc3RhbmNlcy5zZXJ2aWNlRm9sZCA9IE51bWJlcihpdGVtLnJzU2VydmljZUluc3RhbmNlcy5zZXJ2aWNlRm9sZCkNCiAgICAgICAgcmV0dXJuIGl0ZW0NCiAgICAgIH0pIDogbnVsbA0KICAgICAgdGhpcy5mb3JtLnJzT3BTZWFMY2xMaXN0ID8gdGhpcy5mb3JtLnJzT3BTZWFMY2xMaXN0Lm1hcChpdGVtID0+IHsNCiAgICAgICAgaXRlbS5yc1NlcnZpY2VJbnN0YW5jZXMuc2VydmljZUZvbGQgPSBOdW1iZXIoaXRlbS5yc1NlcnZpY2VJbnN0YW5jZXMuc2VydmljZUZvbGQpDQogICAgICAgIHJldHVybiBpdGVtDQogICAgICB9KSA6IG51bGwNCiAgICAgIHRoaXMuZm9ybS5yc09wQWlyTGlzdCA/IHRoaXMuZm9ybS5yc09wQWlyTGlzdC5tYXAoaXRlbSA9PiB7DQogICAgICAgIGl0ZW0ucnNTZXJ2aWNlSW5zdGFuY2VzLnNlcnZpY2VGb2xkID0gTnVtYmVyKGl0ZW0ucnNTZXJ2aWNlSW5zdGFuY2VzLnNlcnZpY2VGb2xkKQ0KICAgICAgICByZXR1cm4gaXRlbQ0KICAgICAgfSkgOiBudWxsDQogICAgICB0aGlzLmZvcm0ucnNPcEN0bnJUcnVja0xpc3QgPyB0aGlzLmZvcm0ucnNPcEN0bnJUcnVja0xpc3QubWFwKGl0ZW0gPT4gew0KICAgICAgICBpdGVtLnJzU2VydmljZUluc3RhbmNlcy5zZXJ2aWNlRm9sZCA9IE51bWJlcihpdGVtLnJzU2VydmljZUluc3RhbmNlcy5zZXJ2aWNlRm9sZCkNCiAgICAgICAgcmV0dXJuIGl0ZW0NCiAgICAgIH0pIDogbnVsbA0KICAgICAgdGhpcy5mb3JtLnJzT3BCdWxrVHJ1Y2tMaXN0ID8gdGhpcy5mb3JtLnJzT3BCdWxrVHJ1Y2tMaXN0Lm1hcChpdGVtID0+IHsNCiAgICAgICAgaXRlbS5yc1NlcnZpY2VJbnN0YW5jZXMuc2VydmljZUZvbGQgPSBOdW1iZXIoaXRlbS5yc1NlcnZpY2VJbnN0YW5jZXMuc2VydmljZUZvbGQpDQogICAgICAgIHJldHVybiBpdGVtDQogICAgICB9KSA6IG51bGwNCiAgICAgIHRoaXMuZm9ybS5yc09wRG9jRGVjbGFyZUxpc3QgPyB0aGlzLmZvcm0ucnNPcERvY0RlY2xhcmVMaXN0Lm1hcChpdGVtID0+IHsNCiAgICAgICAgaXRlbS5yc1NlcnZpY2VJbnN0YW5jZXMuc2VydmljZUZvbGQgPSBOdW1iZXIoaXRlbS5yc1NlcnZpY2VJbnN0YW5jZXMuc2VydmljZUZvbGQpDQogICAgICAgIHJldHVybiBpdGVtDQogICAgICB9KSA6IG51bGwNCiAgICAgIHRoaXMuZm9ybS5yc09wRnJlZURlY2xhcmVMaXN0ID8gdGhpcy5mb3JtLnJzT3BGcmVlRGVjbGFyZUxpc3QubWFwKGl0ZW0gPT4gew0KICAgICAgICBpdGVtLnJzU2VydmljZUluc3RhbmNlcy5zZXJ2aWNlRm9sZCA9IE51bWJlcihpdGVtLnJzU2VydmljZUluc3RhbmNlcy5zZXJ2aWNlRm9sZCkNCiAgICAgICAgcmV0dXJuIGl0ZW0NCiAgICAgIH0pIDogbnVsbA0KDQogICAgICAvLyBjb3B55LiA5Lu95pON5L2c5Y2VDQogICAgICBzYXZlQXNSY3QodGhpcy5mb3JtKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgdGhpcy5mb3JtLnJjdElkID0gcmVzcG9uc2UuZGF0YQ0KICAgICAgICB0aGlzLnNhdmVBc0FsbChyZXNwb25zZS5kYXRhKQ0KICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLlj6blrZjmiJDlip8iKQ0KICAgICAgICAvKiAgdGhpcy5vcGVuID0gZmFsc2UNCiAgICAgICAgdGhpcy5nZXRSY3RMaXN0KCkgKi8NCiAgICAgICAgdGhpcy5mb3JtLm5vVHJhbnNmZXJBbGxvd2VkID0gdGhpcy5mb3JtLm5vVHJhbnNmZXJBbGxvd2VkID8gdHJ1ZSA6IGZhbHNlDQogICAgICAgIHRoaXMuZm9ybS5ub0RpdmlkZWRBbGxvd2VkID0gdGhpcy5mb3JtLm5vRGl2aWRlZEFsbG93ZWQgPyB0cnVlIDogZmFsc2UNCiAgICAgICAgdGhpcy5mb3JtLm5vQWdyZWVtZW50U2hvd2VkID0gdGhpcy5mb3JtLm5vQWdyZWVtZW50U2hvd2VkID8gdHJ1ZSA6IGZhbHNlDQogICAgICAgIHRoaXMuZm9ybS5pc0N1c3RvbXNJbnRyYW5zaXRTaG93ZWQgPSB0aGlzLmZvcm0uaXNDdXN0b21zSW50cmFuc2l0U2hvd2VkID8gdHJ1ZSA6IGZhbHNlDQoNCiAgICAgICAgLy8g5paw5aKe55qE6KGo5pivcmN05LiN5pivYm9va2luZyzmiYDku6XlnLDlnYDmoI/lj5jljJYNCiAgICAgICAgLy8gdGhpcy4kdGFiLm9wZW5QYWdlKCLorqLoiLHljZXmmI7nu4YiLCAiL3NhbGVzcXVvdGF0aW9uL2Jvb2tpbmdEZXRhaWwiLCB7cklkOiByZXNwb25zZS5kYXRhLCBib29raW5nOiB0cnVlfSkNCiAgICAgIH0pDQoNCiAgICB9LA0KICAgIGhhbmRsZVNldHRsZWRSYXRlKHNlcnZpY2VPYmplY3QpIHsNCiAgICAgIGlmIChzZXJ2aWNlT2JqZWN0LnNldHRsZWRSYXRlKSB7DQogICAgICAgIGlmICh0aGlzLmZvcm0ucmV2ZW51ZVRvbiAmJiB0aGlzLmZvcm0ucmV2ZW51ZVRvbi5zcGxpdCgiKyIpLmxlbmd0aCA+IDApIHsNCiAgICAgICAgICB0aGlzLmZvcm0ucmV2ZW51ZVRvbi5zcGxpdCgiKyIpLm1hcCgocmV2ZW51ZVRvbiwgaSkgPT4gew0KICAgICAgICAgICAgaWYgKHJldmVudWVUb24uc3BsaXQoIngiKS5sZW5ndGggPiAwICYmIGkgPT09IDApIHsNCiAgICAgICAgICAgICAgdGhpcy5mb3JtLmNvdW50QSA9IE51bWJlcihyZXZlbnVlVG9uLnNwbGl0KCJ4IilbMF0pDQogICAgICAgICAgICAgIHRoaXMuZm9ybS51bml0Q29kZUEgPSByZXZlbnVlVG9uLnNwbGl0KCJ4IilbMV0NCiAgICAgICAgICAgIH0NCiAgICAgICAgICAgIGlmIChyZXZlbnVlVG9uLnNwbGl0KCJ4IikubGVuZ3RoID4gMCAmJiBpID09PSAxKSB7DQogICAgICAgICAgICAgIHRoaXMuZm9ybS5jb3VudEIgPSBOdW1iZXIocmV2ZW51ZVRvbi5zcGxpdCgieCIpWzBdKQ0KICAgICAgICAgICAgICB0aGlzLmZvcm0udW5pdENvZGVCID0gcmV2ZW51ZVRvbi5zcGxpdCgieCIpWzFdDQogICAgICAgICAgICB9DQogICAgICAgICAgICBpZiAocmV2ZW51ZVRvbi5zcGxpdCgieCIpLmxlbmd0aCA+IDAgJiYgaSA9PT0gMikgew0KICAgICAgICAgICAgICB0aGlzLmZvcm0uY291bnRDID0gTnVtYmVyKHJldmVudWVUb24uc3BsaXQoIngiKVswXSkNCiAgICAgICAgICAgICAgdGhpcy5mb3JtLnVuaXRDb2RlQyA9IHJldmVudWVUb24uc3BsaXQoIngiKVsxXQ0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0pDQogICAgICAgIH0NCg0KICAgICAgICBsZXQgcHJpY2VBcnIgPSBzZXJ2aWNlT2JqZWN0LnNldHRsZWRSYXRlLnNwbGl0KCIvIikNCiAgICAgICAgaWYgKHByaWNlQXJyWzBdKSB7DQogICAgICAgICAgLy8g5qC55o2u57uT566X5Lu35pu05pS55bqU5LuY5piO57uGDQogICAgICAgICAgc2VydmljZU9iamVjdC5yc0NoYXJnZUxpc3QubWFwKGl0ZW0gPT4gew0KICAgICAgICAgICAgaWYgKGl0ZW0uZG5BbW91bnQgPT09IHRoaXMuZm9ybS5jb3VudEEgJiYgaXRlbS5kblVuaXRDb2RlID09PSB0aGlzLmZvcm0udW5pdENvZGVBKSB7DQogICAgICAgICAgICAgIGl0ZW0uZG5Vbml0UmF0ZSA9IHByaWNlQXJyWzBdDQogICAgICAgICAgICB9DQogICAgICAgICAgfSkNCiAgICAgICAgfQ0KICAgICAgICBpZiAocHJpY2VBcnJbMV0pIHsNCiAgICAgICAgICAvLyDmoLnmja7nu5Pnrpfku7fmm7TmlLnlupTku5jmmI7nu4YNCiAgICAgICAgICBzZXJ2aWNlT2JqZWN0LnJzQ2hhcmdlTGlzdC5tYXAoaXRlbSA9PiB7DQogICAgICAgICAgICBpZiAoaXRlbS5kbkFtb3VudCA9PT0gdGhpcy5mb3JtLmNvdW50QiAmJiBpdGVtLmRuVW5pdENvZGUgPT09IHRoaXMuZm9ybS51bml0Q29kZUIpIHsNCiAgICAgICAgICAgICAgaXRlbS5kblVuaXRSYXRlID0gcHJpY2VBcnJbMV0NCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9KQ0KICAgICAgICB9DQogICAgICAgIGlmIChwcmljZUFyclsyXSkgew0KICAgICAgICAgIC8vIOagueaNrue7k+eul+S7t+abtOaUueW6lOS7mOaYjue7hg0KICAgICAgICAgIHNlcnZpY2VPYmplY3QucnNDaGFyZ2VMaXN0Lm1hcChpdGVtID0+IHsNCiAgICAgICAgICAgIGlmIChpdGVtLmRuQW1vdW50ID09PSB0aGlzLmZvcm0uY291bnRDICYmIGl0ZW0uZG5Vbml0Q29kZSA9PT0gdGhpcy5mb3JtLnVuaXRDb2RlQykgew0KICAgICAgICAgICAgICBpdGVtLmRuVW5pdFJhdGUgPSBwcmljZUFyclsyXQ0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0pDQogICAgICAgIH0NCiAgICAgIH0NCiAgICB9LA0KICAgIG1lcmdlU2VhU2VydmljZVdpdGhNYWluU2VydmljZSgpIHsNCiAgICAgIHRoaXMuZm9ybS5yc09wU2VhRmNsTGlzdCA/IHRoaXMuZm9ybS5yc09wU2VhRmNsTGlzdCA9IHRoaXMuZm9ybS5yc09wU2VhRmNsTGlzdC5tYXAoaXRlbSA9PiB7DQogICAgICAgIGxldCBkYXRhID0gdGhpcy5jdXN0b21NZXJnZSh0aGlzLmZvcm0sIGl0ZW0pDQogICAgICAgIC8vIOWQiOW5tuWQjuWhq+WFheWFtuS7luWxnuaApw0KICAgICAgICBkYXRhLnNxZFBzYU5vID0gaXRlbS5zcWRQc2FObw0KICAgICAgICBkYXRhLnNvTm8gPSBpdGVtLnNvTm8NCiAgICAgICAgZGF0YS5ibE5vID0gaXRlbS5ibE5vDQogICAgICAgIGRhdGEuc3FkQ29udGFpbmVyc1NlYWxzU3VtID0gaXRlbS5zcWRDb250YWluZXJzU2VhbHNTdW0NCiAgICAgICAgZGF0YS5jYXJyaWVySWQgPSBpdGVtLmNhcnJpZXJJZA0KICAgICAgICBkYXRhLmZpcnN0VmVzc2VsID0gaXRlbS5maXJzdFZlc3NlbA0KICAgICAgICBkYXRhLnNlY29uZFZlc3NlbCA9IGl0ZW0uc2Vjb25kVmVzc2VsDQogICAgICAgIGRhdGEuaW5xdWlyeVNjaGVkdWxlU3VtbWFyeSA9IGl0ZW0uaW5xdWlyeVNjaGVkdWxlU3VtbWFyeQ0KICAgICAgICBkYXRhLmZpcnN0Q3lPcGVuVGltZSA9IGl0ZW0uZmlyc3RDeU9wZW5UaW1lDQogICAgICAgIGRhdGEuZmlyc3RDeUNsb3NpbmdUaW1lID0gaXRlbS5maXJzdEN5Q2xvc2luZ1RpbWUNCiAgICAgICAgZGF0YS5jdkNsb3NpbmdUaW1lID0gaXRlbS5jdkNsb3NpbmdUaW1lDQogICAgICAgIGRhdGEuZXRkID0gaXRlbS5ldGQNCiAgICAgICAgZGF0YS5ldGEgPSBpdGVtLmV0YQ0KICAgICAgICBkYXRhLnNpQ2xvc2luZ1RpbWUgPSBpdGVtLnNpQ2xvc2luZ1RpbWUNCiAgICAgICAgZGF0YS5zcWRWZ21TdGF0dXMgPSBpdGVtLnNxZFZnbVN0YXR1cw0KICAgICAgICBkYXRhLnNxZEFtc0Vuc1Bvc3RTdGF0dXMgPSBpdGVtLnNxZEFtc0Vuc1Bvc3RTdGF0dXMNCiAgICAgICAgLy8gZGF0YS5wb2RFdGEgPSBpdGVtLnBvZEV0YQ0KICAgICAgICAvLyBkYXRhLmRlc3RpbmF0aW9uUG9ydEV0YSA9IGl0ZW0uZGVzdGluYXRpb25Qb3J0RXRhDQogICAgICAgIGRhdGEuYm9va2luZ0NoYXJnZVJlbWFyayA9IGl0ZW0uYm9va2luZ0NoYXJnZVJlbWFyaw0KICAgICAgICBkYXRhLmJvb2tpbmdBZ2VudFJlbWFyayA9IGl0ZW0uYm9va2luZ0FnZW50UmVtYXJrDQoNCiAgICAgICAgZGF0YS5ib29raW5nSWQgPSB0aGlzLiRzdG9yZS5zdGF0ZS51c2VyLnNpZA0KICAgICAgICBkYXRhLmJvb2tpbmdTdGF0dXMgPSAiMSIgLy8g5bey6K6i6IixDQogICAgICAgIGRhdGEuYm9va2luZ1RpbWUgPSBtb21lbnQoKS5mb3JtYXQoInl5eXktTU0tREQgSEg6bW06c3MiKQ0KICAgICAgICBkYXRhLmRpc3RyaWJ1dGlvblN0YXR1cyA9IGRhdGEuc3FkUHNhTm8gPyBkYXRhLmRpc3RyaWJ1dGlvblN0YXR1cyA6ICIyIiAvLyDmk43kvZzoh6rorqLoiLENCiAgICAgICAgLy8g5Li76LS555So5biB56eN44CB5Y2V5Lu344CB5Y2V5L2NID0g5L6L77yadXNkODYwMC80MEhRDQogICAgICAgIGRhdGEucmN0Tm8gPSB0aGlzLmZvcm0ucmN0Tm8NCiAgICAgICAgZGF0YS5jbGllbnRTaG9ydE5hbWUgPSB0aGlzLmZvcm0uY2xpZW50TmFtZS5zcGxpdCgiLyIpWzFdDQogICAgICAgIGRhdGEuc2FsZXNJZCA9IHRoaXMuZm9ybS5zYWxlc0lkDQogICAgICAgIGRhdGEuc2FsZXNBc3Npc3RhbnRJZCA9IHRoaXMuZm9ybS5zYWxlc0Fzc2lzdGFudElkDQogICAgICAgIC8vIGRhdGEub3BJZCA9IHRoaXMuJHN0b3JlLnN0YXRlLnVzZXIuc2lkDQoNCiAgICAgICAgcmV0dXJuIGRhdGENCiAgICAgIH0pIDogbnVsbA0KICAgICAgdGhpcy5mb3JtLnJzT3BTZWFMY2xMaXN0ID8gdGhpcy5mb3JtLnJzT3BTZWFMY2xMaXN0ID0gdGhpcy5mb3JtLnJzT3BTZWFMY2xMaXN0Lm1hcChpdGVtID0+IHsNCiAgICAgICAgbGV0IGRhdGEgPSB0aGlzLmN1c3RvbU1lcmdlKHRoaXMuZm9ybSwgaXRlbSkNCiAgICAgICAgLy8g5ZCI5bm25ZCO5aGr5YWF5YW25LuW5bGe5oCnDQogICAgICAgIGRhdGEuc3FkUHNhTm8gPSBpdGVtLnNxZFBzYU5vDQogICAgICAgIGRhdGEuc29ObyA9IGl0ZW0uc29Obw0KICAgICAgICBkYXRhLmJsTm8gPSBpdGVtLmJsTm8NCiAgICAgICAgZGF0YS5zcWRDb250YWluZXJzU2VhbHNTdW0gPSBpdGVtLnNxZENvbnRhaW5lcnNTZWFsc1N1bQ0KICAgICAgICBkYXRhLmNhcnJpZXJJZCA9IGl0ZW0uY2FycmllcklkDQogICAgICAgIGRhdGEuZmlyc3RWZXNzZWwgPSBpdGVtLmZpcnN0VmVzc2VsDQogICAgICAgIGRhdGEuc2Vjb25kVmVzc2VsID0gaXRlbS5zZWNvbmRWZXNzZWwNCiAgICAgICAgZGF0YS5pbnF1aXJ5U2NoZWR1bGVTdW1tYXJ5ID0gaXRlbS5pbnF1aXJ5U2NoZWR1bGVTdW1tYXJ5DQogICAgICAgIGRhdGEuZmlyc3RDeU9wZW5UaW1lID0gaXRlbS5maXJzdEN5T3BlblRpbWUNCiAgICAgICAgZGF0YS5maXJzdEN5Q2xvc2luZ1RpbWUgPSBpdGVtLmZpcnN0Q3lDbG9zaW5nVGltZQ0KICAgICAgICBkYXRhLmN2Q2xvc2luZ1RpbWUgPSBpdGVtLmN2Q2xvc2luZ1RpbWUNCiAgICAgICAgZGF0YS5ldGQgPSBpdGVtLmV0ZA0KICAgICAgICBkYXRhLmV0YSA9IGl0ZW0uZXRhDQogICAgICAgIGRhdGEuc2lDbG9zaW5nVGltZSA9IGl0ZW0uc2lDbG9zaW5nVGltZQ0KICAgICAgICBkYXRhLnNxZFZnbVN0YXR1cyA9IGl0ZW0uc3FkVmdtU3RhdHVzDQogICAgICAgIGRhdGEuc3FkQW1zRW5zUG9zdFN0YXR1cyA9IGl0ZW0uc3FkQW1zRW5zUG9zdFN0YXR1cw0KICAgICAgICAvLyBkYXRhLnBvZEV0YSA9IGl0ZW0ucG9kRXRhDQogICAgICAgIC8vIGRhdGEuZGVzdGluYXRpb25Qb3J0RXRhID0gaXRlbS5kZXN0aW5hdGlvblBvcnRFdGENCiAgICAgICAgZGF0YS5ib29raW5nQ2hhcmdlUmVtYXJrID0gaXRlbS5ib29raW5nQ2hhcmdlUmVtYXJrDQogICAgICAgIGRhdGEuYm9va2luZ0FnZW50UmVtYXJrID0gaXRlbS5ib29raW5nQWdlbnRSZW1hcmsNCg0KICAgICAgICBkYXRhLmJvb2tpbmdJZCA9IHRoaXMuJHN0b3JlLnN0YXRlLnVzZXIuc2lkDQogICAgICAgIGRhdGEuYm9va2luZ1N0YXR1cyA9ICIxIiAvLyDlt7LorqLoiLENCiAgICAgICAgZGF0YS5ib29raW5nVGltZSA9IG1vbWVudCgpLmZvcm1hdCgieXl5eS1NTS1ERCBISDptbTpzcyIpDQogICAgICAgIGRhdGEuZGlzdHJpYnV0aW9uU3RhdHVzID0gZGF0YS5zcWRQc2FObyA/IGRhdGEuZGlzdHJpYnV0aW9uU3RhdHVzIDogIjIiIC8vIOaTjeS9nOiHquiuouiIsQ0KICAgICAgICAvLyDkuLvotLnnlKjluIHnp43jgIHljZXku7fjgIHljZXkvY0gPSDkvovvvJp1c2Q4NjAwLzQwSFENCiAgICAgICAgZGF0YS5yY3RObyA9IHRoaXMuZm9ybS5yY3RObw0KICAgICAgICBkYXRhLmNsaWVudFNob3J0TmFtZSA9IHRoaXMuZm9ybS5jbGllbnROYW1lLnNwbGl0KCIvIilbMV0NCiAgICAgICAgZGF0YS5zYWxlc0lkID0gdGhpcy5mb3JtLnNhbGVzSWQNCiAgICAgICAgZGF0YS5zYWxlc0Fzc2lzdGFudElkID0gdGhpcy5mb3JtLnNhbGVzQXNzaXN0YW50SWQNCiAgICAgICAgLy8gZGF0YS5vcElkID0gdGhpcy4kc3RvcmUuc3RhdGUudXNlci5zaWQNCg0KICAgICAgICByZXR1cm4gZGF0YQ0KICAgICAgfSkgOiBudWxsDQogICAgfSwNCiAgICBjaG9vc2VXaGVuT25seU9uZSgpIHsNCiAgICAgIGlmICh0aGlzLmZvcm0ucnNPcFNlYUZjbExpc3QgJiYgdGhpcy5mb3JtLnJzT3BTZWFGY2xMaXN0Lmxlbmd0aCA9PT0gMSkgew0KICAgICAgICB0aGlzLmZvcm0ucnNPcFNlYUZjbExpc3QubWFwKGl0ZW0gPT4gew0KICAgICAgICAgIHRoaXMuZm9ybS5zcWRQc2FObyA9IGl0ZW0uc3FkUHNhTm8NCiAgICAgICAgICB0aGlzLmZvcm0uc29ObyA9IGl0ZW0uc29Obw0KICAgICAgICAgIHRoaXMuZm9ybS5ibE5vID0gaXRlbS5ibE5vDQogICAgICAgICAgdGhpcy5mb3JtLnNxZENvbnRhaW5lcnNTZWFsc1N1bSA9IGl0ZW0uc3FkQ29udGFpbmVyc1NlYWxzU3VtDQogICAgICAgICAgdGhpcy5mb3JtLmNhcnJpZXJJZCA9IGl0ZW0uY2FycmllcklkDQogICAgICAgICAgdGhpcy5mb3JtLmZpcnN0VmVzc2VsID0gaXRlbS5maXJzdFZlc3NlbA0KICAgICAgICAgIHRoaXMuZm9ybS5zZWNvbmRWZXNzZWwgPSBpdGVtLnNlY29uZFZlc3NlbA0KICAgICAgICAgIHRoaXMuZm9ybS5pbnF1aXJ5U2NoZWR1bGVTdW1tYXJ5ID0gaXRlbS5pbnF1aXJ5U2NoZWR1bGVTdW1tYXJ5DQogICAgICAgICAgdGhpcy5mb3JtLmZpcnN0Q3lPcGVuVGltZSA9IGl0ZW0uZmlyc3RDeU9wZW5UaW1lDQogICAgICAgICAgdGhpcy5mb3JtLmZpcnN0Q3lDbG9zaW5nVGltZSA9IGl0ZW0uZmlyc3RDeUNsb3NpbmdUaW1lDQogICAgICAgICAgdGhpcy5mb3JtLmN2Q2xvc2luZ1RpbWUgPSBpdGVtLmN2Q2xvc2luZ1RpbWUNCiAgICAgICAgICB0aGlzLmZvcm0uZXRkID0gaXRlbS5ldGQNCiAgICAgICAgICB0aGlzLmZvcm0uZXRhID0gaXRlbS5ldGENCiAgICAgICAgICB0aGlzLmZvcm0uc2lDbG9zaW5nVGltZSA9IGl0ZW0uc2lDbG9zaW5nVGltZQ0KICAgICAgICAgIHRoaXMuZm9ybS5zcWRWZ21TdGF0dXMgPSBpdGVtLnNxZFZnbVN0YXR1cw0KICAgICAgICAgIHRoaXMuZm9ybS5zcWRBbXNFbnNQb3N0U3RhdHVzID0gaXRlbS5zcWRBbXNFbnNQb3N0U3RhdHVzDQogICAgICAgICAgLy8gdGhpcy5mb3JtLnBvZEV0YSA9IGl0ZW0ucG9kRXRhDQogICAgICAgICAgLy8gdGhpcy5mb3JtLmRlc3RpbmF0aW9uUG9ydEV0YSA9IGl0ZW0uZGVzdGluYXRpb25Qb3J0RXRhDQogICAgICAgICAgdGhpcy5mb3JtLmJvb2tpbmdDaGFyZ2VSZW1hcmsgPSBpdGVtLmJvb2tpbmdDaGFyZ2VSZW1hcmsNCiAgICAgICAgICB0aGlzLmZvcm0uYm9va2luZ0FnZW50UmVtYXJrID0gaXRlbS5ib29raW5nQWdlbnRSZW1hcmsNCg0KICAgICAgICAgIHRoaXMuZm9ybS5ib29raW5nSWQgPSBpdGVtLmJvb2tpbmdJZA0KICAgICAgICAgIHRoaXMuZm9ybS5ib29raW5nU3RhdHVzID0gaXRlbS5ib29raW5nU3RhdHVzDQogICAgICAgICAgdGhpcy5mb3JtLmJvb2tpbmdUaW1lID0gaXRlbS5ib29raW5nVGltZQ0KICAgICAgICAgIHRoaXMuZm9ybS5kaXN0cmlidXRpb25TdGF0dXMgPSBpdGVtLmRpc3RyaWJ1dGlvblN0YXR1cw0KICAgICAgICAgIC8vIOS4u+i0ueeUqOW4geenjeOAgeWNleS7t+OAgeWNleS9jSA9IOS+i++8mnVzZDg2MDAvNDBIUQ0KICAgICAgICAgIHRoaXMuZm9ybS5yY3RObyA9IGl0ZW0ucmN0Tm8NCiAgICAgICAgICB0aGlzLmZvcm0uY2xpZW50U2hvcnROYW1lID0gaXRlbS5jbGllbnRTaG9ydE5hbWUNCiAgICAgICAgICB0aGlzLmZvcm0uc2FsZXNJZCA9IGl0ZW0uc2FsZXNJZA0KICAgICAgICAgIHRoaXMuZm9ybS5zYWxlc0Fzc2lzdGFudElkID0gaXRlbS5zYWxlc0Fzc2lzdGFudElkDQogICAgICAgICAgLy8gdGhpcy5mb3JtLm9wSWQgPSBpdGVtLm9wSWQNCiAgICAgICAgfSkNCiAgICAgIH0gZWxzZSBpZiAodGhpcy5mb3JtLnJzT3BTZWFMY2xMaXN0ICYmIHRoaXMuZm9ybS5yc09wU2VhTGNsTGlzdC5sZW5ndGggPT09IDEpIHsNCiAgICAgICAgdGhpcy5mb3JtLnJzT3BTZWFMY2xMaXN0Lm1hcChpdGVtID0+IHsNCiAgICAgICAgICB0aGlzLmZvcm0uc3FkUHNhTm8gPSBpdGVtLnNxZFBzYU5vDQogICAgICAgICAgdGhpcy5mb3JtLnNvTm8gPSBpdGVtLnNvTm8NCiAgICAgICAgICB0aGlzLmZvcm0uYmxObyA9IGl0ZW0uYmxObw0KICAgICAgICAgIHRoaXMuZm9ybS5zcWRDb250YWluZXJzU2VhbHNTdW0gPSBpdGVtLnNxZENvbnRhaW5lcnNTZWFsc1N1bQ0KICAgICAgICAgIHRoaXMuZm9ybS5jYXJyaWVySWQgPSBpdGVtLmNhcnJpZXJJZA0KICAgICAgICAgIHRoaXMuZm9ybS5maXJzdFZlc3NlbCA9IGl0ZW0uZmlyc3RWZXNzZWwNCiAgICAgICAgICB0aGlzLmZvcm0uc2Vjb25kVmVzc2VsID0gaXRlbS5zZWNvbmRWZXNzZWwNCiAgICAgICAgICB0aGlzLmZvcm0uaW5xdWlyeVNjaGVkdWxlU3VtbWFyeSA9IGl0ZW0uaW5xdWlyeVNjaGVkdWxlU3VtbWFyeQ0KICAgICAgICAgIHRoaXMuZm9ybS5maXJzdEN5T3BlblRpbWUgPSBpdGVtLmZpcnN0Q3lPcGVuVGltZQ0KICAgICAgICAgIHRoaXMuZm9ybS5maXJzdEN5Q2xvc2luZ1RpbWUgPSBpdGVtLmZpcnN0Q3lDbG9zaW5nVGltZQ0KICAgICAgICAgIHRoaXMuZm9ybS5jdkNsb3NpbmdUaW1lID0gaXRlbS5jdkNsb3NpbmdUaW1lDQogICAgICAgICAgdGhpcy5mb3JtLmV0ZCA9IGl0ZW0uZXRkDQogICAgICAgICAgdGhpcy5mb3JtLmV0YSA9IGl0ZW0uZXRhDQogICAgICAgICAgdGhpcy5mb3JtLnNpQ2xvc2luZ1RpbWUgPSBpdGVtLnNpQ2xvc2luZ1RpbWUNCiAgICAgICAgICB0aGlzLmZvcm0uc3FkVmdtU3RhdHVzID0gaXRlbS5zcWRWZ21TdGF0dXMNCiAgICAgICAgICB0aGlzLmZvcm0uc3FkQW1zRW5zUG9zdFN0YXR1cyA9IGl0ZW0uc3FkQW1zRW5zUG9zdFN0YXR1cw0KICAgICAgICAgIC8vIHRoaXMuZm9ybS5wb2RFdGEgPSBpdGVtLnBvZEV0YQ0KICAgICAgICAgIC8vIHRoaXMuZm9ybS5kZXN0aW5hdGlvblBvcnRFdGEgPSBpdGVtLmRlc3RpbmF0aW9uUG9ydEV0YQ0KICAgICAgICAgIHRoaXMuZm9ybS5ib29raW5nQ2hhcmdlUmVtYXJrID0gaXRlbS5ib29raW5nQ2hhcmdlUmVtYXJrDQogICAgICAgICAgdGhpcy5mb3JtLmJvb2tpbmdBZ2VudFJlbWFyayA9IGl0ZW0uYm9va2luZ0FnZW50UmVtYXJrDQoNCiAgICAgICAgICB0aGlzLmZvcm0uYm9va2luZ0lkID0gaXRlbS5ib29raW5nSWQNCiAgICAgICAgICB0aGlzLmZvcm0uYm9va2luZ1N0YXR1cyA9IGl0ZW0uYm9va2luZ1N0YXR1cw0KICAgICAgICAgIHRoaXMuZm9ybS5ib29raW5nVGltZSA9IGl0ZW0uYm9va2luZ1RpbWUNCiAgICAgICAgICB0aGlzLmZvcm0uZGlzdHJpYnV0aW9uU3RhdHVzID0gaXRlbS5kaXN0cmlidXRpb25TdGF0dXMNCiAgICAgICAgICAvLyDkuLvotLnnlKjluIHnp43jgIHljZXku7fjgIHljZXkvY0gPSDkvovvvJp1c2Q4NjAwLzQwSFENCiAgICAgICAgICB0aGlzLmZvcm0ucmN0Tm8gPSBpdGVtLnJjdE5vDQogICAgICAgICAgdGhpcy5mb3JtLmNsaWVudFNob3J0TmFtZSA9IGl0ZW0uY2xpZW50U2hvcnROYW1lDQogICAgICAgICAgdGhpcy5mb3JtLnNhbGVzSWQgPSBpdGVtLnNhbGVzSWQNCiAgICAgICAgICB0aGlzLmZvcm0uc2FsZXNBc3Npc3RhbnRJZCA9IGl0ZW0uc2FsZXNBc3Npc3RhbnRJZA0KICAgICAgICAgIC8vIHRoaXMuZm9ybS5vcElkID0gaXRlbS5vcElkDQogICAgICAgIH0pDQogICAgICB9DQogICAgfSwNCiAgICBzYXZlQWxsKGlkKSB7DQogICAgICB0aGlzLnJzQ2xpZW50U2VydmljZUluc3RhbmNlLnJjdElkID0gaWQNCiAgICAgIHRoaXMucnNDbGllbnRTZXJ2aWNlSW5zdGFuY2Uuc2VydmljZUJlbG9uZ1RvID0gImNsaWVudCINCiAgICAgIHRoaXMucnNDbGllbnRNZXNzYWdlLnJzU2VydmljZUluc3RhbmNlcyA9IHRoaXMucnNDbGllbnRTZXJ2aWNlSW5zdGFuY2UNCiAgICAgIHRoaXMuZm9ybS5yc0NsaWVudE1lc3NhZ2UgPSB0aGlzLnJzQ2xpZW50TWVzc2FnZQ0KDQogICAgICB0aGlzLnNhdmVBbGxTZXJ2aWNlKGlkKQ0KICAgIH0sDQogICAgc2F2ZUFzQWxsKGlkKSB7DQogICAgICB0aGlzLnJzQ2xpZW50U2VydmljZUluc3RhbmNlLnJjdElkID0gaWQNCiAgICAgIHRoaXMucnNDbGllbnRTZXJ2aWNlSW5zdGFuY2Uuc2VydmljZUJlbG9uZ1RvID0gImNsaWVudCINCiAgICAgIHRoaXMucnNDbGllbnRNZXNzYWdlLnJzU2VydmljZUluc3RhbmNlcyA9IHRoaXMucnNDbGllbnRTZXJ2aWNlSW5zdGFuY2UNCiAgICAgIHRoaXMuZm9ybS5yc0NsaWVudE1lc3NhZ2UgPSB0aGlzLnJzQ2xpZW50TWVzc2FnZQ0KDQogICAgICB0aGlzLnNhdmVBc0FsbFNlcnZpY2UoaWQpDQogICAgfSwNCiAgICBzYXZlQWxsU2VydmljZShpZCkgew0KICAgICAgaWYgKHRoaXMuZm9ybS5yY3RJZCA9PSBudWxsKSB7DQogICAgICAgIHRoaXMuJG1lc3NhZ2UuZXJyb3IoIuivt+WFiOehruWumuWNleaNriIpDQogICAgICAgIHJldHVybg0KICAgICAgfQ0KICAgICAgc2F2ZUFsbFNlcnZpY2UodGhpcy5mb3JtKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgaWYgKHR5cGVvZiBpZCAhPSAibnVtYmVyIikgew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygi5L+d5a2Y5oiQ5YqfIikNCiAgICAgICAgfQ0KDQogICAgICAgIHRoaXMudXBkYXRlQWxsU2VydmljZShyZXNwb25zZS5kYXRhKQ0KICAgICAgfSkNCiAgICB9LA0KICAgIHNhdmVBc0FsbFNlcnZpY2UoaWQpIHsNCiAgICAgIGlmICh0aGlzLmZvcm0ucmN0SWQgPT0gbnVsbCkgew0KICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCLor7flhYjnoa7lrprljZXmja4iKQ0KICAgICAgICByZXR1cm4NCiAgICAgIH0NCiAgICAgIHNhdmVBc0FsbFNlcnZpY2UodGhpcy5mb3JtKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgaWYgKHR5cGVvZiBpZCAhPSAibnVtYmVyIikgew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygi5Y+m5a2Y5oiQ5YqfIikNCiAgICAgICAgfQ0KDQogICAgICB9KQ0KICAgIH0sDQogICAgdXBkYXRlQWxsU2VydmljZShyc1JjdCkgew0KICAgICAgaWYgKHJzUmN0LnNlcnZpY2VUeXBlSWRzLmluZGV4T2YoMSkgIT09IC0xICYmIHJzUmN0LnJzT3BTZWFGY2xMaXN0ICE9PSBudWxsKSB7DQogICAgICAgIHRoaXMuZm9ybS5yc09wU2VhRmNsTGlzdCA9IHJzUmN0LnJzT3BTZWFGY2xMaXN0DQogICAgICB9DQogICAgICBpZiAocnNSY3Quc2VydmljZVR5cGVJZHMuaW5kZXhPZigyKSAhPT0gLTEgJiYgcnNSY3QucnNPcFNlYUxjbExpc3QgIT09IG51bGwpIHsNCiAgICAgICAgdGhpcy5mb3JtLnJzT3BTZWFMY2xMaXN0ID0gcnNSY3QucnNPcFNlYUxjbExpc3QNCiAgICAgIH0NCiAgICAgIGlmIChyc1JjdC5zZXJ2aWNlVHlwZUlkcy5pbmRleE9mKDEwKSAhPT0gLTEgJiYgcnNSY3QucnNPcEFpckxpc3QgIT09IG51bGwpIHsNCiAgICAgICAgdGhpcy5mb3JtLnJzT3BBaXJMaXN0ID0gcnNSY3QucnNPcEFpckxpc3QNCiAgICAgIH0NCiAgICAgIGlmIChyc1JjdC5zZXJ2aWNlVHlwZUlkcy5pbmRleE9mKDUwKSAhPT0gLTEgJiYgcnNSY3QucnNPcEN0bnJUcnVja0xpc3QgIT09IG51bGwpIHsNCiAgICAgICAgdGhpcy5mb3JtLnJzT3BDdG5yVHJ1Y2tMaXN0ID0gcnNSY3QucnNPcEN0bnJUcnVja0xpc3QNCiAgICAgIH0NCiAgICAgIGlmIChyc1JjdC5zZXJ2aWNlVHlwZUlkcy5pbmRleE9mKDUxKSAhPT0gLTEgJiYgcnNSY3QucnNPcEJ1bGtUcnVja0xpc3QgIT09IG51bGwpIHsNCiAgICAgICAgdGhpcy5mb3JtLnJzT3BCdWxrVHJ1Y2tMaXN0ID0gcnNSY3QucnNPcEJ1bGtUcnVja0xpc3QNCiAgICAgIH0NCiAgICAgIGlmIChyc1JjdC5zZXJ2aWNlVHlwZUlkcy5pbmRleE9mKDYwKSAhPT0gLTEgJiYgcnNSY3QucnNPcERvY0RlY2xhcmVMaXN0ICE9PSBudWxsKSB7DQogICAgICAgIHRoaXMuZm9ybS5yc09wRG9jRGVjbGFyZUxpc3QgPSByc1JjdC5yc09wRG9jRGVjbGFyZUxpc3QNCiAgICAgIH0NCiAgICAgIGlmIChyc1JjdC5zZXJ2aWNlVHlwZUlkcy5pbmRleE9mKDYxKSAhPT0gLTEgJiYgcnNSY3QucnNPcEZyZWVEZWNsYXJlTGlzdCAhPT0gbnVsbCkgew0KICAgICAgICB0aGlzLmZvcm0ucnNPcEZyZWVEZWNsYXJlTGlzdCA9IHJzUmN0LnJzT3BGcmVlRGVjbGFyZUxpc3QNCiAgICAgIH0NCg0KICAgICAgaWYgKHJzUmN0LnJzQ2xpZW50TWVzc2FnZSAhPSBudWxsKSB7DQogICAgICAgIHRoaXMucnNDbGllbnRNZXNzYWdlID0gcnNSY3QucnNDbGllbnRNZXNzYWdlDQogICAgICAgIHRoaXMucnNDbGllbnRTZXJ2aWNlSW5zdGFuY2UgPSByc1JjdC5yc0NsaWVudE1lc3NhZ2UucnNTZXJ2aWNlSW5zdGFuY2VzDQogICAgICB9DQogICAgICBpZiAocnNSY3Quc2VydmljZVR5cGVJZHMuaW5kZXhPZigyMCkgIT09IC0xICYmIHJzUmN0LnJzT3BSYWlsRkNMICE9PSBudWxsKSB7DQogICAgICAgIHRoaXMucnNPcFJhaWxGQ0wgPSByc1JjdC5yc09wUmFpbEZDTA0KICAgICAgICB0aGlzLnJzT3BSYWlsRmNsU2VydmljZUluc3RhbmNlID0gcnNSY3QucnNPcFJhaWxGQ0wucnNTZXJ2aWNlSW5zdGFuY2VzDQogICAgICB9DQogICAgICBpZiAocnNSY3Quc2VydmljZVR5cGVJZHMuaW5kZXhPZigyMSkgIT09IC0xICYmIHJzUmN0LnJzT3BSYWlsTENMICE9PSBudWxsKSB7DQogICAgICAgIHRoaXMucnNPcFJhaWxMQ0wgPSByc1JjdC5yc09wUmFpbExDTA0KICAgICAgICB0aGlzLnJzT3BSYWlsTGNsU2VydmljZUluc3RhbmNlID0gcnNSY3QucnNPcFJhaWxMQ0wucnNTZXJ2aWNlSW5zdGFuY2VzDQogICAgICB9DQogICAgICBpZiAocnNSY3Quc2VydmljZVR5cGVJZHMuaW5kZXhPZig0MCkgIT09IC0xICYmIHJzUmN0LnJzT3BFeHByZXNzICE9PSBudWxsKSB7DQogICAgICAgIHRoaXMucnNPcEV4cHJlc3MgPSByc1JjdC5yc09wRXhwcmVzcw0KICAgICAgICB0aGlzLnJzT3BFeHByZXNzU2VydmljZUluc3RhbmNlID0gcnNSY3QucnNPcEV4cHJlc3MucnNTZXJ2aWNlSW5zdGFuY2VzDQogICAgICB9DQogICAgICBpZiAocnNSY3Quc2VydmljZVR5cGVJZHMuaW5kZXhPZig3MCkgIT09IC0xICYmIHJzUmN0LnJzT3BET0FnZW50ICE9PSBudWxsKSB7DQogICAgICAgIHRoaXMucnNPcERPQWdlbnQgPSByc1JjdC5yc09wRE9BZ2VudA0KICAgICAgICB0aGlzLnJzT3BET0FnZW50U2VydmljZUluc3RhbmNlID0gcnNSY3QucnNPcERPQWdlbnQucnNTZXJ2aWNlSW5zdGFuY2VzDQogICAgICB9DQogICAgICBpZiAocnNSY3Quc2VydmljZVR5cGVJZHMuaW5kZXhPZig3MSkgIT09IC0xICYmIHJzUmN0LnJzT3BDbGVhckFnZW50ICE9PSBudWxsKSB7DQogICAgICAgIHRoaXMucnNPcENsZWFyQWdlbnQgPSByc1JjdC5yc09wQ2xlYXJBZ2VudA0KICAgICAgICB0aGlzLnJzT3BDbGVhckFnZW50U2VydmljZUluc3RhbmNlID0gcnNSY3QucnNPcENsZWFyQWdlbnQucnNTZXJ2aWNlSW5zdGFuY2VzDQogICAgICB9DQogICAgICBpZiAocnNSY3Quc2VydmljZVR5cGVJZHMuaW5kZXhPZig4MCkgIT09IC0xICYmIHJzUmN0LnJzT3BXSFMgIT09IG51bGwpIHsNCiAgICAgICAgdGhpcy5yc09wV0hTID0gcnNSY3QucnNPcFdIUw0KICAgICAgICB0aGlzLnJzT3BXSFNTZXJ2aWNlSW5zdGFuY2UgPSByc1JjdC5yc09wV0hTLnJzU2VydmljZUluc3RhbmNlcw0KICAgICAgfQ0KICAgICAgaWYgKHJzUmN0LnNlcnZpY2VUeXBlSWRzLmluZGV4T2YoOTApICE9PSAtMSAmJiByc1JjdC5yc09wM3JkQ2VydCAhPT0gbnVsbCkgew0KICAgICAgICB0aGlzLnJzT3AzcmRDZXJ0ID0gcnNSY3QucnNPcDNyZENlcnQNCiAgICAgICAgdGhpcy5yc09wM3JkQ2VydFNlcnZpY2VJbnN0YW5jZSA9IHJzUmN0LnJzT3AzcmRDZXJ0LnJzU2VydmljZUluc3RhbmNlcw0KICAgICAgfQ0KICAgICAgaWYgKHJzUmN0LnNlcnZpY2VUeXBlSWRzLmluZGV4T2YoMTAwKSAhPT0gLTEgJiYgcnNSY3QucnNPcElOUyAhPT0gbnVsbCkgew0KICAgICAgICB0aGlzLnJzT3BJTlMgPSByc1JjdC5yc09wSU5TDQogICAgICAgIHRoaXMucnNPcElOU1NlcnZpY2VJbnN0YW5jZSA9IHJzUmN0LnJzT3BJTlMucnNTZXJ2aWNlSW5zdGFuY2VzDQogICAgICB9DQogICAgICBpZiAocnNSY3Quc2VydmljZVR5cGVJZHMuaW5kZXhPZigxMDEpICE9PSAtMSAmJiByc1JjdC5yc09wVHJhZGluZyAhPT0gbnVsbCkgew0KICAgICAgICB0aGlzLnJzT3BUcmFkaW5nID0gcnNSY3QucnNPcFRyYWRpbmcNCiAgICAgICAgdGhpcy5yc09wVHJhZGluZ1NlcnZpY2VJbnN0YW5jZSA9IHJzUmN0LnJzT3BUcmFkaW5nLnJzU2VydmljZUluc3RhbmNlcw0KICAgICAgfQ0KICAgICAgaWYgKHJzUmN0LnNlcnZpY2VUeXBlSWRzLmluZGV4T2YoMTAyKSAhPT0gLTEgJiYgcnNSY3QucnNPcEZ1bWlnYXRpb24gIT09IG51bGwpIHsNCiAgICAgICAgdGhpcy5yc09wRnVtaWdhdGlvbiA9IHJzUmN0LnJzT3BGdW1pZ2F0aW9uDQogICAgICAgIHRoaXMucnNPcEZ1bWlnYXRpb25TZXJ2aWNlSW5zdGFuY2UgPSByc1JjdC5yc09wRnVtaWdhdGlvbi5yc1NlcnZpY2VJbnN0YW5jZXMNCiAgICAgIH0NCiAgICAgIGlmIChyc1JjdC5zZXJ2aWNlVHlwZUlkcy5pbmRleE9mKDEwMykgIT09IC0xICYmIHJzUmN0LnJzT3BDTyAhPT0gbnVsbCkgew0KICAgICAgICB0aGlzLnJzT3BDTyA9IHJzUmN0LnJzT3BDTw0KICAgICAgICB0aGlzLnJzT3BDT1NlcnZpY2VJbnN0YW5jZSA9IHJzUmN0LnJzT3BDTy5yc1NlcnZpY2VJbnN0YW5jZXMNCiAgICAgIH0NCiAgICAgIGlmIChyc1JjdC5zZXJ2aWNlVHlwZUlkcy5pbmRleE9mKDEwNCkgIT09IC0xICYmIHJzUmN0LnJzT3BPdGhlciAhPT0gbnVsbCkgew0KICAgICAgICB0aGlzLnJzT3BPdGhlciA9IHJzUmN0LnJzT3BPdGhlcg0KICAgICAgICB0aGlzLnJzT3BPdGhlclNlcnZpY2VJbnN0YW5jZSA9IHJzUmN0LnJzT3BPdGhlci5yc1NlcnZpY2VJbnN0YW5jZXMNCiAgICAgIH0NCiAgICB9LA0KICAgIHNhdmVDbGllbnRNZXNzYWdlKGlkKSB7DQogICAgICBpZiAodGhpcy5mb3JtLnJjdElkID09IG51bGwpIHsNCiAgICAgICAgdGhpcy4kbWVzc2FnZS5lcnJvcigi6K+35YWI56Gu5a6a5Y2V5o2uIikNCiAgICAgICAgcmV0dXJuDQogICAgICB9DQoNCiAgICAgIHRoaXMucnNDbGllbnRTZXJ2aWNlSW5zdGFuY2UucmN0SWQgPSBpZA0KICAgICAgdGhpcy5yc0NsaWVudFNlcnZpY2VJbnN0YW5jZS5zZXJ2aWNlQmVsb25nVG8gPSAiY2xpZW50Ig0KICAgICAgdGhpcy5mb3JtLnJzQ2xpZW50TWVzc2FnZS5yc1NlcnZpY2VJbnN0YW5jZXMgPSB0aGlzLnJzQ2xpZW50U2VydmljZUluc3RhbmNlDQoNCiAgICAgIGFkZENsaWVudE1lc3NhZ2UodGhpcy5mb3JtKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgaWYgKHR5cGVvZiBpZCAhPSAibnVtYmVyIikgew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygi5L+d5a2Y5oiQ5YqfIikNCiAgICAgICAgfQ0KICAgICAgfSkNCiAgICB9LA0KICAgIHNhdmVMb2dpc3RpY3MoaWQpIHsNCiAgICAgIGlmICh0aGlzLmZvcm0ucmN0SWQgPT0gbnVsbCkgew0KICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCLor7flhYjnoa7lrprljZXmja4iKQ0KICAgICAgICByZXR1cm4NCiAgICAgIH0NCg0KICAgICAgLy8g5YWI6K6w5b2V5a2Q5a6e5L6L5pyN5YqhDQogICAgICB0aGlzLnJzQmFzaWNMb2dpc3RpY3NTZXJ2aWNlSW5zdGFuY2Uuc2VydmljZVR5cGVJZCA9IDENCiAgICAgIHRoaXMucnNCYXNpY0xvZ2lzdGljc1NlcnZpY2VJbnN0YW5jZS5yY3RJZCA9IHR5cGVvZiBpZCA9PSAibnVtYmVyIiA/IGlkIDogdGhpcy5mb3JtLnJjdElkDQogICAgICB0aGlzLnJzQmFzaWNMb2dpc3RpY3NTZXJ2aWNlSW5zdGFuY2UucmN0Tm8gPSB0aGlzLmZvcm0ucmN0Tm8NCiAgICAgIHRoaXMucnNCYXNpY0xvZ2lzdGljc1NlcnZpY2VJbnN0YW5jZS5zdXBwbGllclN1bW1hcnkgPSB0aGlzLmZvcm0uc3VwcGxpZXJTdW1tYXJ5DQogICAgICAvLyB0aGlzLnJzQmFzaWNMb2dpc3RpY3NTZXJ2aWNlSW5zdGFuY2Uuc3VwcGxpZXJDb250YWN0PQ0KICAgICAgdGhpcy5yc0Jhc2ljTG9naXN0aWNzU2VydmljZUluc3RhbmNlLmlucXVpcnlMZWF0ZXN0VXBkYXRlZFRpbWUgPSB0aGlzLmZvcm0uY3JlYXRlVGltZQ0KICAgICAgdGhpcy5yc0Jhc2ljTG9naXN0aWNzU2VydmljZUluc3RhbmNlLmlucXVpcnlObyA9IHRoaXMuZm9ybS5pbnF1aXJ5Tm8NCiAgICAgIC8vIHRoaXMucnNCYXNpY0xvZ2lzdGljc1NlcnZpY2VJbnN0YW5jZS5hZ3JlZW1lbnRUeXBlQ29kZQ0KICAgICAgLy8gdGhpcy5yc0Jhc2ljTG9naXN0aWNzU2VydmljZUluc3RhbmNlLmFncmVlbWVudE5vDQogICAgICB0aGlzLnJzQmFzaWNMb2dpc3RpY3NTZXJ2aWNlSW5zdGFuY2UubWF4V2VpZ2h0ID0gdGhpcy5mb3JtLmdyb3NzV2VpZ2h0DQogICAgICB0aGlzLnJzQmFzaWNMb2dpc3RpY3NTZXJ2aWNlSW5zdGFuY2Uuc2VydmljZUJlbG9uZ1RvID0gImxvZ2lzdGljcyINCiAgICAgIHRoaXMuZm9ybS5yc0Jhc2ljTG9naXN0aWNzLnJzU2VydmljZUluc3RhbmNlcyA9IHRoaXMucnNCYXNpY0xvZ2lzdGljc1NlcnZpY2VJbnN0YW5jZQ0KDQogICAgICAvLyDmnI3liqHlrp7kvovorrDlvZXkuI7liY3nqIvov5DovpPnrYnlrZDmnI3liqHkuIDotbfnu4TmiJDkuIDkuKrkuovniakNCiAgICAgIGFkZEJhc2ljTG9naXN0aWNzKHRoaXMuZm9ybSkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgIGlmICh0eXBlb2YgaWQgIT0gIm51bWJlciIpIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoIuS/neWtmOaIkOWKnyIpDQogICAgICAgIH0NCiAgICAgIH0pDQogICAgfSwNCiAgICBzYXZlUHJlQ2FycmlhZ2UoaWQpIHsNCiAgICAgIGlmICh0aGlzLmZvcm0ucmN0SWQgPT0gbnVsbCkgew0KICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCLor7flhYjnoa7lrprljZXmja4iKQ0KICAgICAgICByZXR1cm4NCiAgICAgIH0NCg0KICAgICAgLy8g5YWI6K6w5b2V5a2Q5a6e5L6L5pyN5YqhDQogICAgICB0aGlzLnJzUHJlY2FycmlhZ2VTZXJ2aWNlSW5zdGFuY2Uuc2VydmljZVR5cGVJZCA9IDQNCiAgICAgIHRoaXMucnNQcmVjYXJyaWFnZVNlcnZpY2VJbnN0YW5jZS5yY3RJZCA9IHR5cGVvZiBpZCA9PSAibnVtYmVyIiA/IGlkIDogdGhpcy5mb3JtLnJjdElkDQogICAgICB0aGlzLnJzUHJlY2FycmlhZ2VTZXJ2aWNlSW5zdGFuY2UucmN0Tm8gPSB0aGlzLmZvcm0ucmN0Tm8NCiAgICAgIHRoaXMucnNQcmVjYXJyaWFnZVNlcnZpY2VJbnN0YW5jZS5zdXBwbGllclN1bW1hcnkgPSB0aGlzLmZvcm0uc3VwcGxpZXJTdW1tYXJ5DQogICAgICAvLyByc1NlcnZpY2VJbnN0YW5jZXMuc3VwcGxpZXJDb250YWN0PQ0KICAgICAgdGhpcy5yc1ByZWNhcnJpYWdlU2VydmljZUluc3RhbmNlLmlucXVpcnlMZWF0ZXN0VXBkYXRlZFRpbWUgPSB0aGlzLmZvcm0uY3JlYXRlVGltZQ0KICAgICAgdGhpcy5yc1ByZWNhcnJpYWdlU2VydmljZUluc3RhbmNlLmlucXVpcnlObyA9IHRoaXMuZm9ybS5pbnF1aXJ5Tm8NCiAgICAgIC8vIHJzU2VydmljZUluc3RhbmNlcy5hZ3JlZW1lbnRUeXBlQ29kZQ0KICAgICAgLy8gcnNTZXJ2aWNlSW5zdGFuY2VzLmFncmVlbWVudE5vDQogICAgICB0aGlzLnJzUHJlY2FycmlhZ2VTZXJ2aWNlSW5zdGFuY2UubWF4V2VpZ2h0ID0gdGhpcy5mb3JtLmdyb3NzV2VpZ2h0DQogICAgICB0aGlzLnJzUHJlY2FycmlhZ2VTZXJ2aWNlSW5zdGFuY2Uuc2VydmljZUJlbG9uZ1RvID0gInByZS1jYXJyaWFnZSINCiAgICAgIHRoaXMuZm9ybS5yc1ByZWNhcnJpYWdlLnJzU2VydmljZUluc3RhbmNlcyA9IHRoaXMucnNQcmVjYXJyaWFnZVNlcnZpY2VJbnN0YW5jZQ0KDQogICAgICAvLyDmnI3liqHlrp7kvovorrDlvZXkuI7liY3nqIvov5DovpPnrYnlrZDmnI3liqHkuIDotbfnu4TmiJDkuIDkuKrkuovniakNCiAgICAgIGFkZFByZUNhcnJpYWdlKHRoaXMuZm9ybSkudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgIGlmICh0eXBlb2YgaWQgIT0gIm51bWJlciIpIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoIuS/neWtmOaIkOWKnyIpDQogICAgICAgIH0NCiAgICAgIH0pDQogICAgfSwNCiAgICBzYXZlRXhwb3J0RGVjbGFyYXRpb24oaWQpIHsNCiAgICAgIGlmICh0aGlzLmZvcm0ucmN0SWQgPT0gbnVsbCkgew0KICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCLor7flhYjnoa7lrprljZXmja4iKQ0KICAgICAgICByZXR1cm4NCiAgICAgIH0NCg0KICAgICAgLy8g5YWI6K6w5b2V5a2Q5a6e5L6L5pyN5YqhDQogICAgICB0aGlzLnJzRXhwb3J0Q3VzdG9tc1NlcnZpY2VJbnN0YW5jZS5zZXJ2aWNlVHlwZUlkID0gNQ0KICAgICAgdGhpcy5yc0V4cG9ydEN1c3RvbXNTZXJ2aWNlSW5zdGFuY2UucmN0SWQgPSB0eXBlb2YgaWQgPT0gIm51bWJlciIgPyBpZCA6IHRoaXMuZm9ybS5yY3RJZA0KICAgICAgdGhpcy5yc0V4cG9ydEN1c3RvbXNTZXJ2aWNlSW5zdGFuY2UucmN0Tm8gPSB0aGlzLmZvcm0ucmN0Tm8NCiAgICAgIHRoaXMucnNFeHBvcnRDdXN0b21zU2VydmljZUluc3RhbmNlLnN1cHBsaWVyU3VtbWFyeSA9IHRoaXMuZm9ybS5zdXBwbGllclN1bW1hcnkNCiAgICAgIC8vIHRoaXMucnNFeHBvcnRDdXN0b21zU2VydmljZUluc3RhbmNlLnN1cHBsaWVyQ29udGFjdD0NCiAgICAgIHRoaXMucnNFeHBvcnRDdXN0b21zU2VydmljZUluc3RhbmNlLmlucXVpcnlMZWF0ZXN0VXBkYXRlZFRpbWUgPSB0aGlzLmZvcm0uY3JlYXRlVGltZQ0KICAgICAgdGhpcy5yc0V4cG9ydEN1c3RvbXNTZXJ2aWNlSW5zdGFuY2UuaW5xdWlyeU5vID0gdGhpcy5mb3JtLmlucXVpcnlObw0KICAgICAgLy8gdGhpcy5yc0V4cG9ydEN1c3RvbXNTZXJ2aWNlSW5zdGFuY2UuYWdyZWVtZW50VHlwZUNvZGUNCiAgICAgIC8vIHRoaXMucnNFeHBvcnRDdXN0b21zU2VydmljZUluc3RhbmNlLmFncmVlbWVudE5vDQogICAgICB0aGlzLnJzRXhwb3J0Q3VzdG9tc1NlcnZpY2VJbnN0YW5jZS5tYXhXZWlnaHQgPSB0aGlzLmZvcm0uZ3Jvc3NXZWlnaHQNCiAgICAgIHRoaXMucnNFeHBvcnRDdXN0b21zU2VydmljZUluc3RhbmNlLnNlcnZpY2VCZWxvbmdUbyA9ICJleHBvcnQtZGVjbGFyYXRpb24iDQogICAgICB0aGlzLmZvcm0ucnNFeHBvcnRDdXN0b21zLnJzU2VydmljZUluc3RhbmNlcyA9IHRoaXMucnNFeHBvcnRDdXN0b21zU2VydmljZUluc3RhbmNlDQoNCiAgICAgIC8vIOacjeWKoeWunuS+i+iusOW9leS4juWJjeeoi+i/kOi+k+etieWtkOacjeWKoeS4gOi1t+e7hOaIkOS4gOS4quS6i+eJqQ0KICAgICAgYWRkRXhwb3J0RGVjbGFyYXRpb24odGhpcy5mb3JtKS50aGVuKHJlc3BvbnNlID0+IHsNCiAgICAgICAgaWYgKHR5cGVvZiBpZCAhPSAibnVtYmVyIikgew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygi5L+d5a2Y5oiQ5YqfIikNCiAgICAgICAgfQ0KICAgICAgfSkNCiAgICB9LA0KICAgIHNhdmVJbXBvcnRDbGVhcmFuY2UoaWQpIHsNCiAgICAgIGlmICh0aGlzLmZvcm0ucmN0SWQgPT0gbnVsbCkgew0KICAgICAgICB0aGlzLiRtZXNzYWdlLmVycm9yKCLor7flhYjnoa7lrprljZXmja4iKQ0KICAgICAgICByZXR1cm4NCiAgICAgIH0NCg0KICAgICAgLy8g5YWI6K6w5b2V5a2Q5a6e5L6L5pyN5YqhDQogICAgICB0aGlzLnJzSW1wb3J0Q3VzdG9tc1NlcnZpY2VJbnN0YW5jZS5zZXJ2aWNlVHlwZUlkID0gNg0KICAgICAgdGhpcy5yc0ltcG9ydEN1c3RvbXNTZXJ2aWNlSW5zdGFuY2UucmN0SWQgPSB0eXBlb2YgaWQgPT0gIm51bWJlciIgPyBpZCA6IHRoaXMuZm9ybS5yY3RJZA0KICAgICAgdGhpcy5yc0ltcG9ydEN1c3RvbXNTZXJ2aWNlSW5zdGFuY2UucmN0Tm8gPSB0aGlzLmZvcm0ucmN0Tm8NCiAgICAgIHRoaXMucnNJbXBvcnRDdXN0b21zU2VydmljZUluc3RhbmNlLnN1cHBsaWVyU3VtbWFyeSA9IHRoaXMuZm9ybS5zdXBwbGllclN1bW1hcnkNCiAgICAgIC8vIHRoaXMucnNFeHBvcnRDdXN0b21zU2VydmljZUluc3RhbmNlLnN1cHBsaWVyQ29udGFjdD0NCiAgICAgIHRoaXMucnNJbXBvcnRDdXN0b21zU2VydmljZUluc3RhbmNlLmlucXVpcnlMZWF0ZXN0VXBkYXRlZFRpbWUgPSB0aGlzLmZvcm0uY3JlYXRlVGltZQ0KICAgICAgdGhpcy5yc0ltcG9ydEN1c3RvbXNTZXJ2aWNlSW5zdGFuY2UuaW5xdWlyeU5vID0gdGhpcy5mb3JtLmlucXVpcnlObw0KICAgICAgLy8gdGhpcy5yc0V4cG9ydEN1c3RvbXNTZXJ2aWNlSW5zdGFuY2UuYWdyZWVtZW50VHlwZUNvZGUNCiAgICAgIC8vIHRoaXMucnNFeHBvcnRDdXN0b21zU2VydmljZUluc3RhbmNlLmFncmVlbWVudE5vDQogICAgICB0aGlzLnJzSW1wb3J0Q3VzdG9tc1NlcnZpY2VJbnN0YW5jZS5tYXhXZWlnaHQgPSB0aGlzLmZvcm0uZ3Jvc3NXZWlnaHQNCiAgICAgIHRoaXMucnNJbXBvcnRDdXN0b21zU2VydmljZUluc3RhbmNlLnNlcnZpY2VCZWxvbmdUbyA9ICJpbXBvcnQtY2xlYXJhbmNlIg0KICAgICAgdGhpcy5mb3JtLnJzSW1wb3J0Q3VzdG9tcy5yc1NlcnZpY2VJbnN0YW5jZXMgPSB0aGlzLnJzSW1wb3J0Q3VzdG9tc1NlcnZpY2VJbnN0YW5jZQ0KDQogICAgICAvLyDmnI3liqHlrp7kvovorrDlvZXkuI7liY3nqIvov5DovpPnrYnlrZDmnI3liqHkuIDotbfnu4TmiJDkuIDkuKrkuovniakNCiAgICAgIGFkZEltcG9ydENsZWFyYW5jZSh0aGlzLmZvcm0pLnRoZW4ocmVzcG9uc2UgPT4gew0KICAgICAgICBpZiAodHlwZW9mIGlkICE9ICJudW1iZXIiKSB7DQogICAgICAgICAgdGhpcy4kbWVzc2FnZS5zdWNjZXNzKCLkv53lrZjmiJDlip8iKQ0KICAgICAgICB9DQogICAgICB9KQ0KICAgIH0sDQogICAgZ2VuZXJhdGVSY3Qodikgew0KICAgICAgaWYgKCFjaGVja1JvbGUoWyJPcCJdKSkgew0KICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoIuaXoOadg+mZkOS/ruaUueaTjeS9nOWNleWPtyIpDQogICAgICAgIHJldHVybg0KICAgICAgfQ0KICAgICAgaWYgKHYpIHsNCiAgICAgICAgZ2V0UmN0TW9uKCkudGhlbih2ID0+IHsNCiAgICAgICAgICBsZXQgbnVtID0gdi5kYXRhDQogICAgICAgICAgaWYgKG51bS50b1N0cmluZygpLmxlbmd0aCA8IDMpIHsNCiAgICAgICAgICAgIGNvbnN0IGogPSAzIC0gKG51bS50b1N0cmluZygpLmxlbmd0aCkNCiAgICAgICAgICAgIGZvciAobGV0IGkgPSAwOyBpIDwgajsgaSsrKSB7DQogICAgICAgICAgICAgIG51bSA9ICIwIiArIG51bQ0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0NCiAgICAgICAgICBsZXQgZGF0ZSA9IG5ldyBEYXRlKCkNCiAgICAgICAgICBsZXQgbW9udGggPSAoZGF0ZS5nZXRNb250aCgpICsgTnVtYmVyKHRoaXMucmN0Lm1vbnRoKSkudG9TdHJpbmcoKQ0KICAgICAgICAgIGxldCB5ZWFyID0gKGRhdGUuZ2V0RnVsbFllYXIoKSArIChtb250aCAvIDEyID4gMSA/IDEgOiAwKSkudG9TdHJpbmcoKS5zdWJzdHJpbmcoMiwgNCkNCiAgICAgICAgICB0aGlzLnJjdC5yY3RObyA9IHRoaXMucmN0LmxlYWRpbmdDaGFyYWN0ZXIgKyB5ZWFyICsgKG1vbnRoLmxlbmd0aCA9PSAxID8gIjAiICsgbW9udGggOiBtb250aCkgKyBudW0udG9TdHJpbmcoKQ0KICAgICAgICB9KQ0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgdGhpcy5vcGVuR2VuZXJhdGVSY3QgPSB0cnVlDQogICAgICB9DQogICAgfSwNCiAgICBmcmVpZ2h0U2VsZWN0KCkgew0KICAgICAgdGhpcy5vcGVuRnJlaWdodFNlbGVjdCA9IHRydWUNCiAgICB9LA0KICAgIGNvbmZpcm1SY3QoKSB7DQogICAgICB0aGlzLmZvcm0ucmN0Tm8gPSB0aGlzLnJjdC5yY3RObw0KICAgICAgdGhpcy5vcGVuR2VuZXJhdGVSY3QgPSBmYWxzZQ0KICAgIH0sDQogICAgY2FuY2VsKCkgew0KICAgICAgLy8gdGhpcy5yZXNldCgpDQogICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZQ0KICAgICAgdGhpcy5vcGVuID0gZmFsc2UNCiAgICAgIHRoaXMub3BlbkdlbmVyYXRlUmN0ID0gZmFsc2UNCiAgICB9LA0KICAgIHNlbGVjdGVkRGlmZmljdWx0eShwYXlsb2FkKSB7DQogICAgICB0aGlzLmZvcm0uZGlmZmljdWx0eUxldmVsID0gcGF5bG9hZA0KICAgIH0sDQogICAgLy8g5qC85byP5YyW6LSn5YC85LiO6LSn6YeNDQogICAgYXV0b0NvbXBsZXRpb24odmFsKSB7DQogICAgICBsZXQgcmUgPSAvXGR7MSwzfSg/PShcZHszfSkrJCkvZw0KICAgICAgbGV0IG51bSA9IC9bMC05XSsvZw0KICAgICAgaWYgKHZhbCA9PT0gImdyb3NzV2VpZ2h0Iikgew0KICAgICAgICBpZiAobnVtLnRlc3QodGhpcy5ncm9zc1dlaWdodCkpIHsNCiAgICAgICAgICAvLyDljrvpmaTliY3lr7zpm7YNCiAgICAgICAgICB0aGlzLmdyb3NzV2VpZ2h0ID0gdGhpcy5ncm9zc1dlaWdodC5yZXBsYWNlKC9eMCsvLCAiIikNCg0KICAgICAgICAgIC8vIOWmguaenOS4uuepuuWImeiuvuS4ujANCiAgICAgICAgICBpZiAoIXRoaXMuZ3Jvc3NXZWlnaHQpIHsNCiAgICAgICAgICAgIHRoaXMuZ3Jvc3NXZWlnaHQgPSAiMCINCiAgICAgICAgICB9DQoNCiAgICAgICAgICB0aGlzLmZvcm0uZ3Jvc3NXZWlnaHQgPSB0aGlzLmdyb3NzV2VpZ2h0DQoNCiAgICAgICAgICAvLyDliIblibLmlbTmlbDlkozlsI/mlbDpg6jliIYNCiAgICAgICAgICBjb25zdCBwYXJ0cyA9IHRoaXMuZ3Jvc3NXZWlnaHQuc3BsaXQoIi4iKQ0KDQogICAgICAgICAgLy8g5re75Yqg5Y2D5YiG5L2N5YiG6ZqU56ymDQogICAgICAgICAgY29uc3QgaW50ZWdlclBhcnQgPSBwYXJ0c1swXS5yZXBsYWNlKC9cQig/PShcZHszfSkrKD8hXGQpKS9nLCAiLCIpDQoNCiAgICAgICAgICAvLyDkv53nlZnkuKTkvY3lsI/mlbANCiAgICAgICAgICB0aGlzLmdyb3NzV2VpZ2h0ID0gcGFydHMubGVuZ3RoID4gMSA/DQogICAgICAgICAgICBgJHtpbnRlZ2VyUGFydH0uJHtwYXJ0c1sxXS5zbGljZSgwLCAyKX1gIDoNCiAgICAgICAgICAgIGAke2ludGVnZXJQYXJ0fS4wMGANCg0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHRoaXMuJG1lc3NhZ2Uud2FybmluZygi6K+36L6T5YWl5pyJ5pWI55qE5pWw5a2XIikNCiAgICAgICAgfQ0KICAgICAgfQ0KICAgICAgaWYgKHZhbCA9PSAiZ29vZHNWYWx1ZSIpIHsNCiAgICAgICAgaWYgKG51bS50ZXN0KHRoaXMuZ29vZHNWYWx1ZSkpIHsNCiAgICAgICAgICB0aGlzLmdvb2RzVmFsdWUgPSB0aGlzLmdvb2RzVmFsdWUucmVwbGFjZSgvXGIoMCspL2dpLCAiIikNCiAgICAgICAgICB0aGlzLmZvcm0uZ29vZHNWYWx1ZSA9IHRoaXMuZ29vZHNWYWx1ZQ0KICAgICAgICAgIGxldCBzdHIgPSB0aGlzLmdvb2RzVmFsdWUuc3BsaXQoIi4iKQ0KICAgICAgICAgIGxldCBuMSA9IHN0clswXS5yZXBsYWNlKHJlLCAiJCYsIikNCiAgICAgICAgICB0aGlzLmdvb2RzVmFsdWUgPSBzdHIubGVuZ3RoID4gMSAmJiBzdHJbMV0gPyBgJHtuMX0uJHtzdHJbMV19YCA6IGAke24xfS4wMGANCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoIuivt+i+k+WFpeaVsOWtlyIpDQogICAgICAgIH0NCiAgICAgIH0NCiAgICB9LA0KICAgIGdldFR5cGUobikgew0KICAgICAgaWYgKCFuKSB7DQogICAgICAgIHJldHVybg0KICAgICAgfQ0KICAgICAgZm9yIChjb25zdCBzIG9mIHRoaXMuJHN0b3JlLnN0YXRlLmRhdGEuc2VydmljZVR5cGVMaXN0KSB7DQogICAgICAgIGlmIChzLmNoaWxkcmVuKSB7DQogICAgICAgICAgZm9yIChjb25zdCBjIG9mIHMuY2hpbGRyZW4pIHsNCiAgICAgICAgICAgIGlmIChjLnNlcnZpY2VUeXBlSWQgPT0gbikgew0KICAgICAgICAgICAgICB0aGlzLmxvZ2lzdGljc1R5cGUgPSBjLnR5cGVJZA0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgICBpZiAocy5zZXJ2aWNlVHlwZUlkID09IG4pIHsNCiAgICAgICAgICB0aGlzLmxvZ2lzdGljc1R5cGUgPSBzLnR5cGVJZA0KICAgICAgICB9DQogICAgICB9DQogICAgfSwNCiAgICBhZGRFbXB0eVNlcnZpY2Uoc2VydmljZVR5cGVJZCkgew0KICAgICAgaWYgKDEgPT09IHNlcnZpY2VUeXBlSWQgJiYgKCF0aGlzLmZvcm0ucnNPcFNlYUZjbExpc3QgfHwgKHRoaXMuZm9ybS5yc09wU2VhRmNsTGlzdCAmJiB0aGlzLmZvcm0ucnNPcFNlYUZjbExpc3QubGVuZ3RoID09PSAwKSkpIHsNCiAgICAgICAgaWYgKCF0aGlzLmZvcm0ucnNPcFNlYUZjbExpc3QpIHsNCiAgICAgICAgICB0aGlzLmZvcm0ucnNPcFNlYUZjbExpc3QgPSBbXQ0KICAgICAgICB9DQogICAgICAgIGxldCByc09wU2VhRmNsID0gdGhpcy5yc09wU2VhRmNsDQogICAgICAgIHJzT3BTZWFGY2wucnNTZXJ2aWNlSW5zdGFuY2VzID0gdGhpcy5yc1NlcnZpY2VJbnN0YW5jZXMNCiAgICAgICAgdGhpcy5mb3JtLnJzT3BTZWFGY2xMaXN0LnB1c2gocnNPcFNlYUZjbCkNCiAgICAgIH0NCiAgICAgIGlmICgyID09PSBzZXJ2aWNlVHlwZUlkICYmICghdGhpcy5mb3JtLnJzT3BTZWFMY2xMaXN0IHx8ICh0aGlzLmZvcm0ucnNPcFNlYUxjbExpc3QgJiYgdGhpcy5mb3JtLnJzT3BTZWFMY2xMaXN0Lmxlbmd0aCA9PT0gMCkpKSB7DQogICAgICAgIGlmICghdGhpcy5mb3JtLnJzT3BTZWFMY2xMaXN0KSB7DQogICAgICAgICAgdGhpcy5mb3JtLnJzT3BTZWFMY2xMaXN0ID0gW10NCiAgICAgICAgfQ0KICAgICAgICBsZXQgcnNPcFNlYUxjbCA9IHRoaXMucnNPcFNlYUxjbA0KICAgICAgICByc09wU2VhTGNsLnJzU2VydmljZUluc3RhbmNlcyA9IHRoaXMucnNTZXJ2aWNlSW5zdGFuY2VzDQogICAgICAgIHRoaXMuZm9ybS5yc09wU2VhTGNsTGlzdC5wdXNoKHJzT3BTZWFMY2wpDQogICAgICB9DQogICAgICBpZiAoMTAgPT09IHNlcnZpY2VUeXBlSWQgJiYgKCF0aGlzLmZvcm0ucnNPcEFpckxpc3QgfHwgKHRoaXMuZm9ybS5yc09wQWlyTGlzdCAmJiB0aGlzLmZvcm0ucnNPcEFpckxpc3QubGVuZ3RoID09PSAwKSkpIHsNCiAgICAgICAgaWYgKCF0aGlzLmZvcm0ucnNPcEFpckxpc3QpIHsNCiAgICAgICAgICB0aGlzLmZvcm0ucnNPcEFpckxpc3QgPSBbXQ0KICAgICAgICB9DQogICAgICAgIGxldCByc09wQWlyID0gdGhpcy5yc09wQWlyDQogICAgICAgIHJzT3BBaXIucnNTZXJ2aWNlSW5zdGFuY2VzID0gdGhpcy5yc1NlcnZpY2VJbnN0YW5jZXMNCiAgICAgICAgdGhpcy5mb3JtLnJzT3BBaXJMaXN0LnB1c2gocnNPcEFpcikNCiAgICAgIH0NCiAgICAgIGlmICg1MCA9PT0gc2VydmljZVR5cGVJZCAmJiAoIXRoaXMuZm9ybS5yc09wQ3RuclRydWNrTGlzdCB8fCAodGhpcy5mb3JtLnJzT3BDdG5yVHJ1Y2tMaXN0ICYmIHRoaXMuZm9ybS5yc09wQ3RuclRydWNrTGlzdC5sZW5ndGggPT09IDApKSkgew0KICAgICAgICBpZiAoIXRoaXMuZm9ybS5yc09wQ3RuclRydWNrTGlzdCkgew0KICAgICAgICAgIHRoaXMuZm9ybS5yc09wQ3RuclRydWNrTGlzdCA9IFtdDQogICAgICAgIH0NCiAgICAgICAgbGV0IHJzT3BDdG5yVHJ1Y2sgPSB0aGlzLnJzT3BDdG5yVHJ1Y2sNCiAgICAgICAgcnNPcEN0bnJUcnVjay5yc1NlcnZpY2VJbnN0YW5jZXMgPSB0aGlzLnJzU2VydmljZUluc3RhbmNlcw0KICAgICAgICB0aGlzLmZvcm0ucnNPcEN0bnJUcnVja0xpc3QucHVzaChyc09wQ3RuclRydWNrKQ0KICAgICAgfQ0KICAgICAgaWYgKDUxID09PSBzZXJ2aWNlVHlwZUlkICYmICghdGhpcy5mb3JtLnJzT3BCdWxrVHJ1Y2tMaXN0IHx8ICh0aGlzLmZvcm0ucnNPcEJ1bGtUcnVja0xpc3QgJiYgdGhpcy5mb3JtLnJzT3BCdWxrVHJ1Y2tMaXN0Lmxlbmd0aCA9PT0gMCkpKSB7DQogICAgICAgIGlmICghdGhpcy5mb3JtLnJzT3BCdWxrVHJ1Y2tMaXN0KSB7DQogICAgICAgICAgdGhpcy5mb3JtLnJzT3BCdWxrVHJ1Y2tMaXN0ID0gW10NCiAgICAgICAgfQ0KICAgICAgICBsZXQgcnNPcEJ1bGtUcnVjayA9IHRoaXMucnNPcEJ1bGtUcnVjaw0KICAgICAgICByc09wQnVsa1RydWNrLnJzU2VydmljZUluc3RhbmNlcyA9IHRoaXMucnNTZXJ2aWNlSW5zdGFuY2VzDQogICAgICAgIHRoaXMuZm9ybS5yc09wQnVsa1RydWNrTGlzdC5wdXNoKHJzT3BCdWxrVHJ1Y2spDQogICAgICB9DQogICAgICBpZiAoNjAgPT09IHNlcnZpY2VUeXBlSWQgJiYgKCF0aGlzLmZvcm0ucnNPcERvY0RlY2xhcmVMaXN0IHx8ICh0aGlzLmZvcm0ucnNPcERvY0RlY2xhcmVMaXN0ICYmIHRoaXMuZm9ybS5yc09wRG9jRGVjbGFyZUxpc3QubGVuZ3RoID09PSAwKSkpIHsNCiAgICAgICAgaWYgKCF0aGlzLmZvcm0ucnNPcERvY0RlY2xhcmVMaXN0KSB7DQogICAgICAgICAgdGhpcy5mb3JtLnJzT3BEb2NEZWNsYXJlTGlzdCA9IFtdDQogICAgICAgIH0NCiAgICAgICAgbGV0IHJzT3BEb2NEZWNsYXJlID0gdGhpcy5yc09wRG9jRGVjbGFyZQ0KICAgICAgICByc09wRG9jRGVjbGFyZS5yc1NlcnZpY2VJbnN0YW5jZXMgPSB0aGlzLnJzU2VydmljZUluc3RhbmNlcw0KICAgICAgICB0aGlzLmZvcm0ucnNPcERvY0RlY2xhcmVMaXN0LnB1c2gocnNPcERvY0RlY2xhcmUpDQogICAgICB9DQogICAgICBpZiAoNjEgPT09IHNlcnZpY2VUeXBlSWQgJiYgKCF0aGlzLmZvcm0ucnNPcEZyZWVEZWNsYXJlTGlzdCB8fCAodGhpcy5mb3JtLnJzT3BGcmVlRGVjbGFyZUxpc3QgJiYgdGhpcy5mb3JtLnJzT3BGcmVlRGVjbGFyZUxpc3QubGVuZ3RoID09PSAwKSkpIHsNCiAgICAgICAgaWYgKCF0aGlzLmZvcm0ucnNPcEZyZWVEZWNsYXJlTGlzdCkgew0KICAgICAgICAgIHRoaXMuZm9ybS5yc09wRnJlZURlY2xhcmVMaXN0ID0gW10NCiAgICAgICAgfQ0KICAgICAgICBsZXQgcnNPcEZyZWVEZWNsYXJlID0gdGhpcy5yc09wRnJlZURlY2xhcmUNCiAgICAgICAgcnNPcEZyZWVEZWNsYXJlLnJzU2VydmljZUluc3RhbmNlcyA9IHRoaXMucnNTZXJ2aWNlSW5zdGFuY2VzDQogICAgICAgIHRoaXMuZm9ybS5yc09wRnJlZURlY2xhcmVMaXN0LnB1c2gocnNPcEZyZWVEZWNsYXJlKQ0KICAgICAgfQ0KICAgIH0sDQogICAgYXN5bmMgZ2V0U2VydmljZVR5cGVMaXN0KHZhbCkgew0KICAgICAgaWYgKHRoaXMuJHN0b3JlLnN0YXRlLmRhdGEuc2VydmljZVR5cGVMaXN0Lmxlbmd0aCA9PSAwKSB7DQogICAgICAgIGF3YWl0IHRoaXMuJHN0b3JlLmRpc3BhdGNoKCJnZXRTZXJ2aWNlVHlwZUxpc3QiKQ0KICAgICAgfQ0KICAgICAgdGhpcy5saXN0LmNsZWFyKCkNCiAgICAgIHRoaXMuc2VydmljZUxpc3QuY2xlYXIoKQ0KICAgICAgdGhpcy5SQUlMLmNsZWFyKCkNCiAgICAgIHRoaXMuRVhQUkVTUy5jbGVhcigpDQogICAgICB0aGlzLkNMRUFSLmNsZWFyKCkNCiAgICAgIHRoaXMuV0hTLmNsZWFyKCkNCiAgICAgIHRoaXMuRVhURU5ELmNsZWFyKCkNCiAgICAgIHRoaXMuZm9ybS5zZXJ2aWNlVHlwZUlkcyA9IHZhbA0KICAgICAgZm9yIChjb25zdCBzIG9mIHRoaXMuJHN0b3JlLnN0YXRlLmRhdGEuc2VydmljZVR5cGVMaXN0KSB7DQogICAgICAgIGlmIChzLmNoaWxkcmVuKSB7DQogICAgICAgICAgZm9yIChjb25zdCBjIG9mIHMuY2hpbGRyZW4pIHsNCiAgICAgICAgICAgIGlmICh2YWwuaW5jbHVkZXMoYy5zZXJ2aWNlVHlwZUlkKSkgew0KICAgICAgICAgICAgICBpZiAoYy50eXBlSWQgIT0gbnVsbCkgew0KICAgICAgICAgICAgICAgIHRoaXMubGlzdC5hZGQoYy50eXBlSWQpDQogICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgdGhpcy5zZXJ2aWNlTGlzdC5hZGQoYy5zZXJ2aWNlVHlwZUlkKQ0KDQogICAgICAgICAgICAgIHRoaXMuYWRkRW1wdHlTZXJ2aWNlKGMuc2VydmljZVR5cGVJZCkNCiAgICAgICAgICAgICAgYy50eXBlSWQgPT09ICIzIiA/IHRoaXMuUkFJTC5hZGQoYykgOiBudWxsDQogICAgICAgICAgICAgIGMudHlwZUlkID09PSAiNCIgPyB0aGlzLkVYUFJFU1MuYWRkKGMpIDogbnVsbA0KICAgICAgICAgICAgICBjLnR5cGVJZCA9PT0gIjciID8gdGhpcy5DTEVBUi5hZGQoYykgOiBudWxsDQogICAgICAgICAgICAgIGMudHlwZUlkID09PSAiOCIgPyB0aGlzLldIUy5hZGQoYykgOiBudWxsDQogICAgICAgICAgICAgIGMudHlwZUlkID09PSAiOSIgPyB0aGlzLkVYVEVORC5hZGQoYykgOiBudWxsDQogICAgICAgICAgICB9DQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICAgIGlmICh2YWwuaW5jbHVkZXMocy5zZXJ2aWNlVHlwZUlkKSkgew0KICAgICAgICAgIGlmIChzLnR5cGVJZCAhPSBudWxsKSB7DQogICAgICAgICAgICB0aGlzLmxpc3QuYWRkKHMudHlwZUlkKQ0KICAgICAgICAgIH0NCiAgICAgICAgICB0aGlzLnNlcnZpY2VMaXN0LmFkZChzLnNlcnZpY2VUeXBlSWQpDQoNCiAgICAgICAgICB0aGlzLmFkZEVtcHR5U2VydmljZShzLnNlcnZpY2VUeXBlSWQpDQogICAgICAgICAgcy50eXBlSWQgPT09ICIxIiA/IHRoaXMuU0VBLmFkZChzKSA6IG51bGwNCiAgICAgICAgICBzLnR5cGVJZCA9PT0gIjIiID8gdGhpcy5BSVIuYWRkKHMpIDogbnVsbA0KICAgICAgICAgIHMudHlwZUlkID09PSAiMyIgPyB0aGlzLlJBSUwuYWRkKHMpIDogbnVsbA0KICAgICAgICAgIHMudHlwZUlkID09PSAiNCIgPyB0aGlzLkVYUFJFU1MuYWRkKHMpIDogbnVsbA0KICAgICAgICAgIHMudHlwZUlkID09PSAiNSIgPyB0aGlzLlRSVUNLLmFkZChzKSA6IG51bGwNCiAgICAgICAgICBzLnR5cGVJZCA9PT0gIjYiID8gdGhpcy5DVVNUT00uYWRkKHMpIDogbnVsbA0KICAgICAgICAgIHMudHlwZUlkID09PSAiNyIgPyB0aGlzLkNMRUFSLmFkZChzKSA6IG51bGwNCiAgICAgICAgICBzLnR5cGVJZCA9PT0gIjgiID8gdGhpcy5XSFMuYWRkKHMpIDogbnVsbA0KICAgICAgICAgIHMudHlwZUlkID09PSAiOSIgPyB0aGlzLkVYVEVORC5hZGQocykgOiBudWxsDQogICAgICAgIH0NCiAgICAgIH0NCiAgICAgIHRoaXMuJGZvcmNlVXBkYXRlKCkNCiAgICB9LA0KICAgIC8qIGNhcnJpZXJOb3JtYWxpemVyKG5vZGUpIHsNCiAgICAgIGlmIChub2RlLmNoaWxkcmVuICYmICFub2RlLmNoaWxkcmVuLmxlbmd0aCkgew0KICAgICAgICBkZWxldGUgbm9kZS5jaGlsZHJlbg0KICAgICAgfQ0KICAgICAgbGV0IGwNCiAgICAgIGlmICghbm9kZS5jYXJyaWVyIHx8IChub2RlLmNhcnJpZXIuY2FycmllckxvY2FsTmFtZSA9PSBudWxsICYmIG5vZGUuY2Fycmllci5jYXJyaWVyRW5OYW1lID09IG51bGwpKSB7DQogICAgICAgIGwgPSBub2RlLnNlcnZpY2VMb2NhbE5hbWUgKyAiICIgKyBub2RlLnNlcnZpY2VFbk5hbWUgKyAiLCIgKyBwaW55aW4uZ2V0RnVsbENoYXJzKG5vZGUuc2VydmljZUxvY2FsTmFtZSAhPSB1bmRlZmluZWQgPyBub2RlLnNlcnZpY2VMb2NhbE5hbWUgOiAiIikNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIGwgPSAobm9kZS5jYXJyaWVyLmNhcnJpZXJJbnRsQ29kZSAhPSBudWxsID8gbm9kZS5jYXJyaWVyLmNhcnJpZXJJbnRsQ29kZSA6ICIiKSArICIgIiArIChub2RlLmNhcnJpZXIuY2FycmllckVuTmFtZSAhPSBudWxsID8gbm9kZS5jYXJyaWVyLmNhcnJpZXJFbk5hbWUgOiAiIikgKyAiICIgKyAobm9kZS5jYXJyaWVyLmNhcnJpZXJMb2NhbE5hbWUgIT0gbnVsbCA/IG5vZGUuY2Fycmllci5jYXJyaWVyTG9jYWxOYW1lIDogIiIpICsgIiwiICsgcGlueWluLmdldEZ1bGxDaGFycygobm9kZS5jYXJyaWVyLmNhcnJpZXJMb2NhbE5hbWUgIT0gbnVsbCA/IG5vZGUuY2Fycmllci5jYXJyaWVyTG9jYWxOYW1lIDogIiIpKQ0KICAgICAgfQ0KICAgICAgcmV0dXJuIHsNCiAgICAgICAgaWQ6IG5vZGUuY2FycmllcklkLA0KICAgICAgICBsYWJlbDogbCwNCiAgICAgICAgY2hpbGRyZW46IG5vZGUuY2hpbGRyZW4NCiAgICAgIH0NCiAgICB9LCAqLw0KICAgIGNhcnJpZXJOb3JtYWxpemVyKG5vZGUpIHsNCiAgICAgIHJldHVybiB7DQogICAgICAgIGlkOiBub2RlLmNhcnJpZXJJZCwNCiAgICAgICAgbGFiZWw6IChub2RlLmNhcnJpZXJTaG9ydE5hbWUgIT0gbnVsbCA/IG5vZGUuY2FycmllclNob3J0TmFtZSA6ICIiKSArICIgIiArIChub2RlLmNhcnJpZXJMb2NhbE5hbWUgIT0gbnVsbCA/IG5vZGUuY2FycmllckxvY2FsTmFtZSA6ICIiKSArICIgIiArIChub2RlLmNhcnJpZXJFbk5hbWUgIT0gbnVsbCA/IG5vZGUuY2FycmllckVuTmFtZSA6ICIiKSArICIsIiArIHBpbnlpbi5nZXRGdWxsQ2hhcnMoKG5vZGUuY2FycmllclNob3J0TmFtZSAhPSBudWxsID8gbm9kZS5jYXJyaWVyU2hvcnROYW1lIDogIiIpICsgIiAiICsgKG5vZGUuY2FycmllckxvY2FsTmFtZSAhPSBudWxsID8gbm9kZS5jYXJyaWVyTG9jYWxOYW1lIDogIiIpKQ0KICAgICAgfQ0KICAgIH0sDQogICAgLyogbG9hZENhcnJpZXIoKSB7DQogICAgICBpZiAodGhpcy4kc3RvcmUuc3RhdGUuZGF0YS5zZXJ2aWNlVHlwZUNhcnJpZXJzLmxlbmd0aCA9PSAwIHx8IHRoaXMuJHN0b3JlLnN0YXRlLmRhdGEucmVkaXNMaXN0LnNlcnZpY2VUeXBlQ2FycmllcnMpIHsNCiAgICAgICAgc3RvcmUuZGlzcGF0Y2goImdldFNlcnZpY2VUeXBlQ2FycmllcnNMaXN0IikudGhlbigoKSA9PiB7DQogICAgICAgICAgdGhpcy5jYXJyaWVyTGlzdCA9IHRoaXMuJHN0b3JlLnN0YXRlLmRhdGEuc2VydmljZVR5cGVDYXJyaWVycw0KICAgICAgICB9KQ0KICAgICAgfSBlbHNlIHsNCiAgICAgICAgdGhpcy5jYXJyaWVyTGlzdCA9IHRoaXMuJHN0b3JlLnN0YXRlLmRhdGEuc2VydmljZVR5cGVDYXJyaWVycw0KICAgICAgfQ0KICAgIH0sICovDQogICAgbG9hZENhcnJpZXIoKSB7DQogICAgICBpZiAodGhpcy4kc3RvcmUuc3RhdGUuZGF0YS5jYXJyaWVyTGlzdC5sZW5ndGggPT0gMCB8fCB0aGlzLiRzdG9yZS5zdGF0ZS5kYXRhLnJlZGlzTGlzdC5jYXJyaWVyKSB7DQogICAgICAgIHN0b3JlLmRpc3BhdGNoKCJnZXRDYXJyaWVyTGlzdCIpLnRoZW4oKCkgPT4gew0KICAgICAgICAgICAgdGhpcy5jYXJyaWVyTGlzdCA9IHRoaXMuJHN0b3JlLnN0YXRlLmRhdGEuY2Fycmllckxpc3QNCiAgICAgICAgICB9DQogICAgICAgICkNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHRoaXMuY2Fycmllckxpc3QgPSB0aGlzLiRzdG9yZS5zdGF0ZS5kYXRhLmNhcnJpZXJMaXN0DQogICAgICB9DQogICAgfSwNCiAgICBsb2FkU2VsZWN0aW9uKCkgew0KICAgICAgaWYgKHRoaXMuJHN0b3JlLnN0YXRlLmRhdGEudW5pdExpc3QubGVuZ3RoID09IDAgfHwgdGhpcy4kc3RvcmUuc3RhdGUuZGF0YS5yZWRpc0xpc3QudW5pdCkgew0KICAgICAgICB0aGlzLiRzdG9yZS5kaXNwYXRjaCgiZ2V0VW5pdExpc3QiKQ0KICAgICAgfQ0KICAgICAgaWYgKHRoaXMuJHN0b3JlLnN0YXRlLmRhdGEuY3VycmVuY3lMaXN0Lmxlbmd0aCA9PSAwIHx8IHRoaXMuJHN0b3JlLnN0YXRlLmRhdGEucmVkaXNMaXN0LmN1cnJlbmN5KSB7DQogICAgICAgIHRoaXMuJHN0b3JlLmRpc3BhdGNoKCJnZXRDdXJyZW5jeUxpc3QiKQ0KICAgICAgfQ0KICAgICAgaWYgKHRoaXMuJHN0b3JlLnN0YXRlLmRhdGEuY2hhcmdlTGlzdC5sZW5ndGggPT0gMCB8fCB0aGlzLiRzdG9yZS5zdGF0ZS5kYXRhLnJlZGlzTGlzdC5jaGFyZ2UpIHsNCiAgICAgICAgdGhpcy4kc3RvcmUuZGlzcGF0Y2goImdldENoYXJnZUxpc3QiKQ0KICAgICAgfQ0KICAgICAgLy8g5Yqg6L296LSn6L+Q57G75Z6LDQogICAgICBpZiAodGhpcy4kc3RvcmUuc3RhdGUuZGF0YS5zZXJ2aWNlVHlwZUxpc3QubGVuZ3RoID09IDAgfHwgdGhpcy4kc3RvcmUuc3RhdGUuZGF0YS5yZWRpc0xpc3Quc2VydmljZVR5cGUpIHsNCiAgICAgICAgdGhpcy4kc3RvcmUuZGlzcGF0Y2goImdldFNlcnZpY2VUeXBlTGlzdCIpDQogICAgICB9DQogICAgICB0aGlzLmxvYWRPcCgpDQogICAgICB0aGlzLmxvYWRDYXJyaWVyKCkNCiAgICAgIHRoaXMubG9hZFNhbGVzKCkNCiAgICAgIHRoaXMubG9hZEJ1c2luZXNzZXMoKQ0KICAgICAgdGhpcy5sb2FkU3RhZmZMaXN0KCkNCiAgICB9LA0KICAgIC8vIOafpeivouaTjeS9nOmDqOeUqOaItw0KICAgIGxvYWRPcCgpIHsNCiAgICAgIGlmICh0aGlzLiRzdG9yZS5zdGF0ZS5kYXRhLm9wTGlzdC5sZW5ndGggPT0gMCB8fCB0aGlzLiRzdG9yZS5zdGF0ZS5kYXRhLnJlZGlzTGlzdC5vcExpc3QpIHsNCiAgICAgICAgc3RvcmUuZGlzcGF0Y2goImdldE9wTGlzdCIpLnRoZW4oKCkgPT4gew0KICAgICAgICAgIHRoaXMub3BMaXN0ID0gdGhpcy4kc3RvcmUuc3RhdGUuZGF0YS5vcExpc3QNCiAgICAgICAgfSkNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHRoaXMub3BMaXN0ID0gdGhpcy4kc3RvcmUuc3RhdGUuZGF0YS5vcExpc3QNCiAgICAgIH0NCiAgICB9LA0KICAgIC8vIOafpeivouS4muWKoemDqOeUqOaItw0KICAgIGxvYWRTYWxlcygpIHsNCiAgICAgIGlmICh0aGlzLiRzdG9yZS5zdGF0ZS5kYXRhLnNhbGVzTGlzdC5sZW5ndGggPT0gMCB8fCB0aGlzLiRzdG9yZS5zdGF0ZS5kYXRhLnJlZGlzTGlzdC5zYWxlc0xpc3QpIHsNCiAgICAgICAgc3RvcmUuZGlzcGF0Y2goImdldFNhbGVzTGlzdCIpLnRoZW4oKCkgPT4gew0KICAgICAgICAgIHRoaXMuYmVsb25nTGlzdCA9IHRoaXMuJHN0b3JlLnN0YXRlLmRhdGEuc2FsZXNMaXN0DQogICAgICAgIH0pDQogICAgICB9IGVsc2Ugew0KICAgICAgICB0aGlzLmJlbG9uZ0xpc3QgPSB0aGlzLiRzdG9yZS5zdGF0ZS5kYXRhLnNhbGVzTGlzdA0KICAgICAgfQ0KICAgIH0sDQogICAgLy8g5p+l6K+i5ZWG5Yqh6YOo55So5oi3DQogICAgbG9hZEJ1c2luZXNzZXMoKSB7DQogICAgICBpZiAodGhpcy4kc3RvcmUuc3RhdGUuZGF0YS5idXNpbmVzc2VzTGlzdC5sZW5ndGggPT0gMCB8fCB0aGlzLiRzdG9yZS5zdGF0ZS5kYXRhLnJlZGlzTGlzdC5idXNpbmVzc2VzTGlzdCkgew0KICAgICAgICBzdG9yZS5kaXNwYXRjaCgiZ2V0QnVzaW5lc3Nlc0xpc3QiKS50aGVuKCgpID0+IHsNCiAgICAgICAgICB0aGlzLmJ1c2luZXNzTGlzdCA9IHRoaXMuJHN0b3JlLnN0YXRlLmRhdGEuYnVzaW5lc3Nlc0xpc3QNCiAgICAgICAgfSkNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHRoaXMuYnVzaW5lc3NMaXN0ID0gdGhpcy4kc3RvcmUuc3RhdGUuZGF0YS5idXNpbmVzc2VzTGlzdA0KICAgICAgfQ0KICAgIH0sDQogICAgY29uZmlybWVkKHYsIHJzQ2hhcmdlTGlzdCkgew0KICAgICAgaWYgKHYgPT0gIm9wIikgew0KICAgICAgICBpZiAodGhpcy5yc0NsaWVudFNlcnZpY2VJbnN0YW5jZS5pc0RuT3BDb25maXJtZWQpIHsNCiAgICAgICAgICB0aGlzLnJzQ2xpZW50U2VydmljZUluc3RhbmNlLmlzRG5PcENvbmZpcm1lZCA9IG51bGwNCiAgICAgICAgICB0aGlzLnJzQ2xpZW50U2VydmljZUluc3RhbmNlLm9wQ29uZmlybWVkVGltZSA9IG51bGwNCiAgICAgICAgICB0aGlzLm9wQ29uZmlybWVkTmFtZSA9IG51bGwNCiAgICAgICAgICB0aGlzLm9wQ29uZmlybWVkRGF0ZSA9IG51bGwNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB0aGlzLnJzQ2xpZW50U2VydmljZUluc3RhbmNlLmlzRG5PcENvbmZpcm1lZCA9IHRoaXMuJHN0b3JlLnN0YXRlLnVzZXIuc2lkDQogICAgICAgICAgdGhpcy5yc0NsaWVudFNlcnZpY2VJbnN0YW5jZS5vcENvbmZpcm1lZFRpbWUgPSBwYXJzZVRpbWUobmV3IERhdGUoKSwgInt5fS17bX0te2R9IikNCiAgICAgICAgICB0aGlzLm9wQ29uZmlybWVkTmFtZSA9IHRoaXMuJHN0b3JlLnN0YXRlLmRhdGEuYWxsUnNTdGFmZkxpc3QuZmlsdGVyKHJzU3RhZmYgPT4gcnNTdGFmZi5zdGFmZklkID09IHRoaXMucnNDbGllbnRTZXJ2aWNlSW5zdGFuY2UuaXNEbk9wQ29uZmlybWVkKVswXS5zdGFmZkZhbWlseUxvY2FsTmFtZSArICIiICsgdGhpcy4kc3RvcmUuc3RhdGUuZGF0YS5hbGxSc1N0YWZmTGlzdC5maWx0ZXIocnNTdGFmZiA9PiByc1N0YWZmLnN0YWZmSWQgPT0gdGhpcy5yc0NsaWVudFNlcnZpY2VJbnN0YW5jZS5pc0RuT3BDb25maXJtZWQpWzBdLnN0YWZmR2l2aW5nTG9jYWxOYW1lDQogICAgICAgICAgdGhpcy5vcENvbmZpcm1lZERhdGUgPSB0aGlzLnJzQ2xpZW50U2VydmljZUluc3RhbmNlLm9wQ29uZmlybWVkVGltZQ0KICAgICAgICB9DQogICAgICAgIHRoaXMudXBkYXRlU2VydmljZUluc3RhbmNlKHRoaXMucnNDbGllbnRTZXJ2aWNlSW5zdGFuY2UpDQogICAgICB9DQogICAgICBpZiAodiA9PSAiYWNjb3VudCIpIHsNCiAgICAgICAgaWYgKHRoaXMucnNDbGllbnRTZXJ2aWNlSW5zdGFuY2UuaXNBY2NvdW50Q29uZmlybWVkKSB7DQogICAgICAgICAgdGhpcy5yc0NsaWVudFNlcnZpY2VJbnN0YW5jZS5pc0FjY291bnRDb25maXJtZWQgPSBudWxsDQogICAgICAgICAgdGhpcy5yc0NsaWVudFNlcnZpY2VJbnN0YW5jZS5hY2NvdW50Q29uZmlybVRpbWUgPSBudWxsDQogICAgICAgICAgdGhpcy5hY2NvdW50Q29uZmlybWVkTmFtZSA9IG51bGwNCiAgICAgICAgICB0aGlzLmFjY291bnRDb25maXJtZWREYXRlID0gbnVsbA0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIHRoaXMucnNDbGllbnRTZXJ2aWNlSW5zdGFuY2UuaXNBY2NvdW50Q29uZmlybWVkID0gdGhpcy4kc3RvcmUuc3RhdGUudXNlci5zaWQNCiAgICAgICAgICB0aGlzLnJzQ2xpZW50U2VydmljZUluc3RhbmNlLmFjY291bnRDb25maXJtVGltZSA9IHBhcnNlVGltZShuZXcgRGF0ZSgpLCAie3l9LXttfS17ZH0iKQ0KICAgICAgICAgIHRoaXMuYWNjb3VudENvbmZpcm1lZE5hbWUgPSB0aGlzLiRzdG9yZS5zdGF0ZS5kYXRhLmFsbFJzU3RhZmZMaXN0LmZpbHRlcihyc1N0YWZmID0+IHJzU3RhZmYuc3RhZmZJZCA9PSB0aGlzLnJzQ2xpZW50U2VydmljZUluc3RhbmNlLmlzQWNjb3VudENvbmZpcm1lZClbMF0uc3RhZmZGYW1pbHlMb2NhbE5hbWUgKyAiIiArIHRoaXMuJHN0b3JlLnN0YXRlLmRhdGEuYWxsUnNTdGFmZkxpc3QuZmlsdGVyKHJzU3RhZmYgPT4gcnNTdGFmZi5zdGFmZklkID09IHRoaXMucnNDbGllbnRTZXJ2aWNlSW5zdGFuY2UuaXNBY2NvdW50Q29uZmlybWVkKVswXS5zdGFmZkdpdmluZ0xvY2FsTmFtZQ0KICAgICAgICAgIHRoaXMuYWNjb3VudENvbmZpcm1lZERhdGUgPSB0aGlzLnJzQ2xpZW50U2VydmljZUluc3RhbmNlLmFjY291bnRDb25maXJtVGltZQ0KDQogICAgICAgICAgcnNDaGFyZ2VMaXN0ID0gcnNDaGFyZ2VMaXN0Lm1hcChpdGVtID0+IHsNCiAgICAgICAgICAgIGlmIChpdGVtLmlzQWNjb3VudENvbmZpcm1lZCAhPSAxKSB7DQogICAgICAgICAgICAgIGl0ZW0uaXNBY2NvdW50Q29uZmlybWVkID0gMQ0KICAgICAgICAgICAgICB1cGRhdGVDaGFyZ2UoaXRlbSkNCiAgICAgICAgICAgICAgcmV0dXJuIGl0ZW0NCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9KQ0KICAgICAgICB9DQoNCiAgICAgICAgdGhpcy51cGRhdGVTZXJ2aWNlSW5zdGFuY2UodGhpcy5yc0NsaWVudFNlcnZpY2VJbnN0YW5jZSkNCiAgICAgIH0NCiAgICAgIGlmICh2ID09ICJzYWxlcyIpIHsNCiAgICAgICAgaWYgKHRoaXMucnNDbGllbnRTZXJ2aWNlSW5zdGFuY2UuaXNEblNhbGVzQ29uZmlybWVkKSB7DQogICAgICAgICAgdGhpcy5yc0NsaWVudFNlcnZpY2VJbnN0YW5jZS5pc0RuU2FsZXNDb25maXJtZWQgPSBudWxsDQogICAgICAgICAgdGhpcy5yc0NsaWVudFNlcnZpY2VJbnN0YW5jZS5zYWxlc0NvbmZpcm1lZFRpbWUgPSBudWxsDQogICAgICAgICAgdGhpcy5zYWxlc0NvbmZpcm1lZE5hbWUgPSBudWxsDQogICAgICAgICAgdGhpcy5zYWxlc0NvbmZpcm1lZERhdGUgPSBudWxsDQogICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgdGhpcy5yc0NsaWVudFNlcnZpY2VJbnN0YW5jZS5pc0RuU2FsZXNDb25maXJtZWQgPSB0aGlzLiRzdG9yZS5zdGF0ZS51c2VyLnNpZA0KICAgICAgICAgIHRoaXMucnNDbGllbnRTZXJ2aWNlSW5zdGFuY2Uuc2FsZXNDb25maXJtZWRUaW1lID0gcGFyc2VUaW1lKG5ldyBEYXRlKCksICJ7eX0te219LXtkfSIpDQogICAgICAgICAgdGhpcy5zYWxlc0NvbmZpcm1lZE5hbWUgPSB0aGlzLiRzdG9yZS5zdGF0ZS5kYXRhLmFsbFJzU3RhZmZMaXN0LmZpbHRlcihyc1N0YWZmID0+IHJzU3RhZmYuc3RhZmZJZCA9PSB0aGlzLnJzQ2xpZW50U2VydmljZUluc3RhbmNlLmlzRG5TYWxlc0NvbmZpcm1lZClbMF0uc3RhZmZGYW1pbHlMb2NhbE5hbWUgKyAiIiArIHRoaXMuJHN0b3JlLnN0YXRlLmRhdGEuYWxsUnNTdGFmZkxpc3QuZmlsdGVyKHJzU3RhZmYgPT4gcnNTdGFmZi5zdGFmZklkID09IHRoaXMucnNDbGllbnRTZXJ2aWNlSW5zdGFuY2UuaXNEblNhbGVzQ29uZmlybWVkKVswXS5zdGFmZkdpdmluZ0xvY2FsTmFtZQ0KICAgICAgICAgIHRoaXMuc2FsZXNDb25maXJtZWREYXRlID0gdGhpcy5yc0NsaWVudFNlcnZpY2VJbnN0YW5jZS5zYWxlc0NvbmZpcm1lZFRpbWUNCiAgICAgICAgfQ0KICAgICAgICB0aGlzLnVwZGF0ZVNlcnZpY2VJbnN0YW5jZSh0aGlzLnJzQ2xpZW50U2VydmljZUluc3RhbmNlKQ0KICAgICAgfQ0KICAgICAgaWYgKHYgPT0gImNsaWVudCIpIHsNCiAgICAgICAgaWYgKHRoaXMucnNDbGllbnRTZXJ2aWNlSW5zdGFuY2UuaXNEbkNsaWVudENvbmZpcm1lZCkgew0KICAgICAgICAgIHRoaXMucnNDbGllbnRTZXJ2aWNlSW5zdGFuY2UuaXNEbkNsaWVudENvbmZpcm1lZCA9IG51bGwNCiAgICAgICAgICB0aGlzLnJzQ2xpZW50U2VydmljZUluc3RhbmNlLmNsaWVudENvbmZpcm1lZFRpbWUgPSBudWxsDQogICAgICAgICAgdGhpcy5jbGllbnRDb25maXJtZWROYW1lID0gbnVsbA0KICAgICAgICAgIHRoaXMuY2xpZW50Q29uZmlybWVkRGF0ZSA9IG51bGwNCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICB0aGlzLnJzQ2xpZW50U2VydmljZUluc3RhbmNlLmlzRG5DbGllbnRDb25maXJtZWQgPSB0aGlzLiRzdG9yZS5zdGF0ZS51c2VyLnNpZA0KICAgICAgICAgIHRoaXMucnNDbGllbnRTZXJ2aWNlSW5zdGFuY2UuY2xpZW50Q29uZmlybWVkVGltZSA9IHBhcnNlVGltZShuZXcgRGF0ZSgpLCAie3l9LXttfS17ZH0iKQ0KICAgICAgICAgIHRoaXMuY2xpZW50Q29uZmlybWVkTmFtZSA9IHRoaXMuJHN0b3JlLnN0YXRlLmRhdGEuYWxsUnNTdGFmZkxpc3QuZmlsdGVyKHJzU3RhZmYgPT4gcnNTdGFmZi5zdGFmZklkID09IHRoaXMucnNDbGllbnRTZXJ2aWNlSW5zdGFuY2UuaXNEbkNsaWVudENvbmZpcm1lZClbMF0uc3RhZmZGYW1pbHlMb2NhbE5hbWUgKyAiIiArIHRoaXMuJHN0b3JlLnN0YXRlLmRhdGEuYWxsUnNTdGFmZkxpc3QuZmlsdGVyKHJzU3RhZmYgPT4gcnNTdGFmZi5zdGFmZklkID09IHRoaXMucnNDbGllbnRTZXJ2aWNlSW5zdGFuY2UuaXNEbkNsaWVudENvbmZpcm1lZClbMF0uc3RhZmZHaXZpbmdMb2NhbE5hbWUNCiAgICAgICAgICB0aGlzLmNsaWVudENvbmZpcm1lZERhdGUgPSB0aGlzLnJzQ2xpZW50U2VydmljZUluc3RhbmNlLmNsaWVudENvbmZpcm1lZFRpbWUNCiAgICAgICAgfQ0KICAgICAgICB0aGlzLnVwZGF0ZVNlcnZpY2VJbnN0YW5jZSh0aGlzLnJzQ2xpZW50U2VydmljZUluc3RhbmNlKQ0KICAgICAgfQ0KICAgIH0sDQogICAgc3RhZmZOb3JtYWxpemVyKG5vZGUpIHsNCiAgICAgIGlmIChub2RlLmNoaWxkcmVuICYmICFub2RlLmNoaWxkcmVuLmxlbmd0aCkgew0KICAgICAgICBkZWxldGUgbm9kZS5jaGlsZHJlbg0KICAgICAgfQ0KICAgICAgbGV0IGwNCiAgICAgIGlmIChub2RlLnN0YWZmKSB7DQogICAgICAgIGlmIChub2RlLnN0YWZmLnN0YWZmRmFtaWx5TG9jYWxOYW1lID09IG51bGwgJiYgbm9kZS5zdGFmZi5zdGFmZkdpdmluZ0xvY2FsTmFtZSA9PSBudWxsKSB7DQogICAgICAgICAgaWYgKG5vZGUucm9sZS5yb2xlTG9jYWxOYW1lICE9IG51bGwpIHsNCiAgICAgICAgICAgIGwgPSBub2RlLnJvbGUucm9sZUxvY2FsTmFtZSArICIsIiArIHBpbnlpbi5nZXRGdWxsQ2hhcnMobm9kZS5yb2xlLnJvbGVMb2NhbE5hbWUpDQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIGwgPSBub2RlLmRlcHQuZGVwdExvY2FsTmFtZSArICIsIiArIHBpbnlpbi5nZXRGdWxsQ2hhcnMobm9kZS5kZXB0LmRlcHRMb2NhbE5hbWUpDQogICAgICAgICAgfQ0KICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgIGwgPSBub2RlLnN0YWZmLnN0YWZmQ29kZSArICIgIiArIG5vZGUuc3RhZmYuc3RhZmZGYW1pbHlMb2NhbE5hbWUgKyBub2RlLnN0YWZmLnN0YWZmR2l2aW5nTG9jYWxOYW1lICsgIiAiICsgbm9kZS5zdGFmZi5zdGFmZkdpdmluZ0VuTmFtZSArICIsIiArIHBpbnlpbi5nZXRGdWxsQ2hhcnMobm9kZS5zdGFmZi5zdGFmZkZhbWlseUxvY2FsTmFtZSArIG5vZGUuc3RhZmYuc3RhZmZHaXZpbmdMb2NhbE5hbWUpDQogICAgICAgIH0NCiAgICAgIH0NCiAgICAgIGlmIChub2RlLnJvbGVJZCkgew0KICAgICAgICByZXR1cm4gew0KICAgICAgICAgIGlkOiBub2RlLnJvbGVJZCwNCiAgICAgICAgICBsYWJlbDogbCwNCiAgICAgICAgICBjaGlsZHJlbjogbm9kZS5jaGlsZHJlbiwNCiAgICAgICAgICBpc0Rpc2FibGVkOiBub2RlLnN0YWZmSWQgPT0gbnVsbCAmJiBub2RlLmNoaWxkcmVuID09IHVuZGVmaW5lZA0KICAgICAgICB9DQogICAgICB9IGVsc2Ugew0KICAgICAgICByZXR1cm4gew0KICAgICAgICAgIGlkOiBub2RlLmRlcHRJZCwNCiAgICAgICAgICBsYWJlbDogbCwNCiAgICAgICAgICBjaGlsZHJlbjogbm9kZS5jaGlsZHJlbiwNCiAgICAgICAgICBpc0Rpc2FibGVkOiBub2RlLnN0YWZmSWQgPT0gbnVsbCAmJiBub2RlLmNoaWxkcmVuID09IHVuZGVmaW5lZA0KICAgICAgICB9DQogICAgICB9DQogICAgfSwNCiAgICBidXNpbmVzc2VzTm9ybWFsaXplcihub2RlKSB7DQogICAgICBpZiAobm9kZS5jaGlsZHJlbiAmJiAhbm9kZS5jaGlsZHJlbi5sZW5ndGgpIHsNCiAgICAgICAgZGVsZXRlIG5vZGUuY2hpbGRyZW4NCiAgICAgIH0NCiAgICAgIGxldCBsDQogICAgICBpZiAobm9kZS5zdGFmZikgew0KICAgICAgICBpZiAobm9kZS5zdGFmZi5zdGFmZkZhbWlseUxvY2FsTmFtZSA9PSBudWxsICYmIG5vZGUuc3RhZmYuc3RhZmZHaXZpbmdMb2NhbE5hbWUgPT0gbnVsbCkgew0KICAgICAgICAgIGlmIChub2RlLnJvbGUucm9sZUxvY2FsTmFtZSAhPSBudWxsKSB7DQogICAgICAgICAgICBsID0gbm9kZS5yb2xlLnJvbGVMb2NhbE5hbWUgKyAiLCIgKyBwaW55aW4uZ2V0RnVsbENoYXJzKG5vZGUucm9sZS5yb2xlTG9jYWxOYW1lKQ0KICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICBsID0gbm9kZS5kZXB0LmRlcHRMb2NhbE5hbWUgKyAiLCIgKyBwaW55aW4uZ2V0RnVsbENoYXJzKG5vZGUuZGVwdC5kZXB0TG9jYWxOYW1lKQ0KICAgICAgICAgIH0NCiAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICBsID0gbm9kZS5zdGFmZi5zdGFmZkNvZGUgKyAiICIgKyBub2RlLnN0YWZmLnN0YWZmRmFtaWx5TG9jYWxOYW1lICsgbm9kZS5zdGFmZi5zdGFmZkdpdmluZ0xvY2FsTmFtZSArICIgIiArIG5vZGUuc3RhZmYuc3RhZmZHaXZpbmdFbk5hbWUgKyAiLCIgKyBwaW55aW4uZ2V0RnVsbENoYXJzKG5vZGUuc3RhZmYuc3RhZmZGYW1pbHlMb2NhbE5hbWUgKyBub2RlLnN0YWZmLnN0YWZmR2l2aW5nTG9jYWxOYW1lKQ0KICAgICAgICB9DQogICAgICB9DQogICAgICByZXR1cm4gew0KICAgICAgICBpZDogbm9kZS5zdGFmZklkLA0KICAgICAgICBsYWJlbDogbCwNCiAgICAgICAgaXNEaXNhYmxlZDogbm9kZS5zdGFmZklkID09IG51bGwgJiYgbm9kZS5jaGlsZHJlbiA9PSB1bmRlZmluZWQNCiAgICAgIH0NCiAgICB9LA0KICAgIGNvbXBhbnlOb3JtYWxpemVyKG5vZGUpIHsNCiAgICAgIHJldHVybiB7DQogICAgICAgIGlkOiBub2RlLmNvbXBhbnlJZCwNCiAgICAgICAgbGFiZWw6IChub2RlLmNvbXBhbnlTaG9ydE5hbWUgIT0gbnVsbCA/IG5vZGUuY29tcGFueVNob3J0TmFtZSA6ICIiKSArICIgIiArIChub2RlLmNvbXBhbnlMb2NhbE5hbWUgIT0gbnVsbCA/IG5vZGUuY29tcGFueUxvY2FsTmFtZSA6ICIiKSArICIsIiArIHBpbnlpbi5nZXRGdWxsQ2hhcnMoKG5vZGUuY29tcGFueVNob3J0TmFtZSAhPSBudWxsID8gbm9kZS5jb21wYW55U2hvcnROYW1lIDogIiIpICsgIiAiICsgKG5vZGUuY29tcGFueUxvY2FsTmFtZSAhPSBudWxsID8gbm9kZS5jb21wYW55TG9jYWxOYW1lIDogIiIpKQ0KICAgICAgfQ0KICAgIH0sDQogICAgcmVzZXQoKSB7DQogICAgICB0aGlzLlJlbGF0aW9uQ2xpZW50TGlzdCA9IFtdDQogICAgICB0aGlzLmJvb2tpbmdNZXNzYWdlTGlzdCA9IFtdDQogICAgICB0aGlzLmZvcm0gPSB7DQogICAgICAgIG5vVHJhbnNmZXJBbGxvd2VkOiBmYWxzZSwNCiAgICAgICAgbm9EaXZpZGVkQWxsb3dlZDogZmFsc2UsDQogICAgICAgIG5vQWdyZWVtZW50U2hvd2VkOiBmYWxzZSwNCiAgICAgICAgaXNDdXN0b21zSW50cmFuc2l0U2hvd2VkOiBmYWxzZSwNCiAgICAgICAgcmN0SWQ6IG51bGwsDQogICAgICAgIHJjdE5vOiBudWxsLA0KICAgICAgICBjbGllbnRJZDogbnVsbCwNCiAgICAgICAgY2xpZW50U3VtbWFyeTogbnVsbCwNCiAgICAgICAgY2xpZW50Um9sZUlkOiBudWxsLA0KICAgICAgICBjbGllbnRDb250YWN0OiBudWxsLA0KICAgICAgICBjbGllbnRDb250YWN0VGVsOiBudWxsLA0KICAgICAgICBjbGllbnRDb250YWN0RW1haWw6IG51bGwsDQogICAgICAgIHJlbGF0aW9uQ2xpZW50SWRMaXN0OiBudWxsLA0KICAgICAgICBlbWVyZ2VuY3lMZXZlbDogbnVsbCwNCiAgICAgICAgZGlmZmljdWx0eUxldmVsOiBudWxsLA0KICAgICAgICByZWxlYXNlVHlwZTogbnVsbCwNCiAgICAgICAgcGF5bWVudFRpdGxlQ29kZTogbnVsbCwNCiAgICAgICAgaW1wRXhwVHlwZTogbnVsbCwNCiAgICAgICAgdHJhZGluZ1Rlcm1zOiBudWxsLA0KICAgICAgICBsb2dpc3RpY3NUZXJtczogbnVsbCwNCiAgICAgICAgdHJhZGluZ1BheW1lbnRDaGFubmVsOiBudWxsLA0KICAgICAgICBjbGllbnRDb250cmFjdE5vOiBudWxsLA0KICAgICAgICBjbGllbnRJbnZvaWNlTm86IG51bGwsDQogICAgICAgIGNhcmdvVHlwZUlkU3VtOiBudWxsLA0KICAgICAgICBnb29kc05hbWVTdW1tYXJ5OiBudWxsLA0KICAgICAgICBwYWNrYWdlUXVhbnRpdHk6IG51bGwsDQogICAgICAgIGdvb2RzVm9sdW1lOiBudWxsLA0KICAgICAgICBncm9zc1dlaWdodDogbnVsbCwNCiAgICAgICAgd2VpZ2h0VW5pdENvZGU6IG51bGwsDQogICAgICAgIGdvb2RzQ3VycmVuY3lDb2RlOiBudWxsLA0KICAgICAgICBnb29kc1ZhbHVlOiBudWxsLA0KICAgICAgICBsb2dpc3RpY3NUeXBlSWQ6IG51bGwsDQogICAgICAgIHJldmVudWVUb246IG51bGwsDQogICAgICAgIHBvbElkOiBudWxsLA0KICAgICAgICBsb2NhbEJhc2ljUG9ydElkOiBudWxsLA0KICAgICAgICB0cmFuc2l0UG9ydElkOiBudWxsLA0KICAgICAgICBwb2RJZDogbnVsbCwNCiAgICAgICAgZGVzdGluYXRpb25Qb3J0SWQ6IG51bGwsDQogICAgICAgIGN2Q2xvc2luZ1RpbWU6IG51bGwsDQogICAgICAgIHNpQ2xvc2luZ1RpbWU6IG51bGwsDQogICAgICAgIGZpcnN0VmVzc2VsOiBudWxsLA0KICAgICAgICBmaXJzdFZveWFnZTogbnVsbCwNCiAgICAgICAgZmlyc3RDeU9wZW5UaW1lOiBudWxsLA0KICAgICAgICBmaXJzdEN5Q2xvc2luZ1RpbWU6IG51bGwsDQogICAgICAgIGZpcnN0RXRkOiBudWxsLA0KICAgICAgICBiYXNpY1Zlc3NlbDogbnVsbCwNCiAgICAgICAgYmFzaWNWb3lhZ2U6IG51bGwsDQogICAgICAgIGJhc2ljRmluYWxHYXRlaW5UaW1lOiBudWxsLA0KICAgICAgICBiYXNpY0V0ZDogbnVsbCwNCiAgICAgICAgcG9kRXRhOiBudWxsLA0KICAgICAgICBkZXN0aW5hdGlvblBvcnRFdGE6IG51bGwsDQogICAgICAgIGNhcnJpZXJJZDogbnVsbCwNCiAgICAgICAgaW5xdWlyeVNjaGVkdWxlU3VtbWFyeTogbnVsbCwNCiAgICAgICAgcG9sQm9va2luZ0FnZW50OiBudWxsLA0KICAgICAgICBwb2RIYW5kbGVBZ2VudDogbnVsbCwNCiAgICAgICAgc2VydmljZVR5cGVJZExpc3Q6IG51bGwsDQogICAgICAgIHNxZFNvTm9TdW06IG51bGwsDQogICAgICAgIHNxZE1ibE5vU3VtOiBudWxsLA0KICAgICAgICBzcWRDb250YWluZXJzU2VhbHNTdW06IG51bGwsDQogICAgICAgIGJsRm9ybUNvZGU6IG51bGwsDQogICAgICAgIHNxZEV4cG9ydEN1c3RvbXNUeXBlOiBudWxsLA0KICAgICAgICBzcWRUcmFpbGVyVHlwZTogbnVsbCwNCiAgICAgICAgcmN0UHJvY2Vzc1N0YXR1c1N1bW1hcnk6IG51bGwsDQogICAgICAgIHRyYW5zcG9ydFN0YXR1c1N1bW1hcnk6IG51bGwsDQogICAgICAgIGRvY1N0YXR1c1N1bW1hcnk6IG51bGwsDQogICAgICAgIHBheW1lbnRSZWNlaXZpbmdTdGF0dXNTdW1tYXJ5OiBudWxsLA0KICAgICAgICBwYXltZW50UGF5aW5nU3RhdHVzU3VtbWFyeTogbnVsbCwNCiAgICAgICAgdHJhbnNwb3J0U3RhdHVzOiAiMCIsDQogICAgICAgIGRvY1N0YXR1czogIjAiLA0KICAgICAgICBwYXltZW50UGF5aW5nU3RhdHVzOiAiMCIsDQogICAgICAgIHJjdFByb2Nlc3NJZDogbnVsbCwNCiAgICAgICAgcG9yY2Vzc0lkOiBudWxsLA0KICAgICAgICBwb3JjZXNzU3RhdHVzSWQ6IG51bGwsDQogICAgICAgIHByb2Nlc3NTdGF0dXNUaW1lOiBtb21lbnQoKS5mb3JtYXQoInl5eXktTU0tREQgSEg6bW06c3MiKSwNCiAgICAgICAgc3RhdHVzVXBkYXRlVGltZTogbW9tZW50KCkuZm9ybWF0KCJ5eXl5LU1NLUREIEhIOm1tOnNzIiksDQogICAgICAgIHByb2Nlc3NSZW1hcms6IG51bGwsDQogICAgICAgIHFvdXRhdGlvbk5vOiBudWxsLA0KICAgICAgICBxb3V0YXRpb25Ta2V0Y2g6IG51bGwsDQogICAgICAgIHNhbGVzSWQ6IG51bGwsDQogICAgICAgIHFvdXRhdGlvblRpbWU6IG51bGwsDQogICAgICAgIG5ld0Jvb2tpbmdObzogbnVsbCwNCiAgICAgICAgbmV3Qm9va2luZ1JlbWFyazogbnVsbCwNCiAgICAgICAgc2FsZXNBc3Npc3RhbnRJZDogbnVsbCwNCiAgICAgICAgc2FsZXNPYnNlcnZlcklkOiBudWxsLA0KICAgICAgICBuZXdCb29raW5nVGltZTogbW9tZW50KCkuZm9ybWF0KCJ5eXl5LU1NLUREIEhIOm1tOnNzIiksDQogICAgICAgIGlucXVpcnlOb3RpY2VTdW06IG51bGwsDQogICAgICAgIGlucXVpcnlJbm5lclJlbWFya1N1bTogbnVsbCwNCiAgICAgICAgdmVyaWZ5UHNhSWQ6IG51bGwsDQogICAgICAgIHBzYVZlcmlmeVRpbWU6IG51bGwsDQogICAgICAgIG9wTGVhZGVyTm90aWNlOiBudWxsLA0KICAgICAgICBvcElubmVyUmVtYXJrOiBudWxsLA0KICAgICAgICBvcElkOiBudWxsLA0KICAgICAgICBib29raW5nT3BJZDogbnVsbCwNCiAgICAgICAgZG9jT3BJZDogbnVsbCwNCiAgICAgICAgb3BPYnNlcnZlcklkOiBudWxsLA0KICAgICAgICByY3RDcmVhdGVUaW1lOiBudWxsLA0KICAgICAgICBjdXN0b21UaXRsZTogbnVsbCwNCiAgICAgICAgY3JlYXRlQnk6IG51bGwsDQogICAgICAgIGNyZWF0ZVRpbWU6IG51bGwsDQogICAgICAgIHVwZGF0ZUJ5OiBudWxsLA0KICAgICAgICB1cGRhdGVUaW1lOiBudWxsLA0KICAgICAgICBkZWxldGVCeTogbnVsbCwNCiAgICAgICAgZGVsZXRlVGltZTogbnVsbCwNCiAgICAgICAgZGVsZXRlU3RhdHVzOiAiMCIsDQogICAgICAgIHByZWNhcnJpYWdlUmVnaW9uSWQ6IG51bGwsDQogICAgICAgIHByZWNhcnJpYWdlQWRkcmVzczogbnVsbCwNCiAgICAgICAgcHJlY2FycmlhZ2VUaW1lOiBudWxsLA0KICAgICAgICBwcmVjYXJyaWFnZUNvbnRhY3Q6IG51bGwsDQogICAgICAgIHByZWNhcnJpYWdlVGVsOiBudWxsLA0KICAgICAgICBwcmVjYXJyaWFnZVJlbWFyazogbnVsbCwNCiAgICAgICAgZGlzcGF0Y2hSZWdpb25JZDogbnVsbCwNCiAgICAgICAgZGlzcGF0Y2hBZGRyZXNzOiBudWxsLA0KICAgICAgICBkaXNwYXRjaFRpbWU6IG51bGwsDQogICAgICAgIGRpc3BhdGNoQ29udGFjdDogbnVsbCwNCiAgICAgICAgZGlzcGF0Y2hUZWw6IG51bGwsDQogICAgICAgIGRpc3BhdGNoUmVtYXJrOiBudWxsLA0KICAgICAgICBwYXltZW50UmVjZWl2aW5nU3RhdHVzOiBudWxsLA0KICAgICAgICBzcWRXYXJlaG91c2luZ1N0YXR1czogbnVsbCwNCiAgICAgICAgc3FkU2hpcHBpbmdCb29raW5nU3RhdHVzOiBudWxsLA0KICAgICAgICBzcWRUcmFpbGVyQm9va2luZ1N0YXR1czogbnVsbCwNCiAgICAgICAgc3FkQ29udGFpbmVyQm9va2luZ1N0YXR1czogbnVsbCwNCiAgICAgICAgc3FkQ29udGFpbmVyTG9hZGluZ1N0YXR1czogbnVsbCwNCiAgICAgICAgc3FkVmVzc2VsQXJyYW5nZVN0YXR1czogbnVsbCwNCiAgICAgICAgc3FkVmdtU3RhdHVzOiBudWxsLA0KICAgICAgICBzcWRDdXN0b21Eb2NzU3RhdHVzOiBudWxsLA0KICAgICAgICBzcWRDdXN0b21BdXRob3JpemVkU3RhdHVzOiBudWxsLA0KICAgICAgICBzcWRDdXN0b21FeGFtaW5lU3RhdHVzOiBudWxsLA0KICAgICAgICBzcWRDdXN0b21SZWxlYXNlU3RhdHVzOiBudWxsLA0KICAgICAgICBzcWRTaVZlcmlmeVN0YXR1czogbnVsbCwNCiAgICAgICAgc3FkU2lQb3N0U3RhdHVzOiBudWxsLA0KICAgICAgICBzcWRBbXNFbnNQb3N0U3RhdHVzOiBudWxsLA0KICAgICAgICBzcWRJc2ZFbW5mUG9zdFN0YXR1czogbnVsbCwNCiAgICAgICAgc3FkTWFpblNlcnZpY2VQYXlpbmdTdGF0dXM6IG51bGwsDQogICAgICAgIHNxZEJsR2V0dGluZ1N0YXR1czogbnVsbCwNCiAgICAgICAgc3FkQmxSZWxlYXNpbmdTdGF0dXM6IG51bGwsDQogICAgICAgIHNxZENvbnRhaW5lck5vU3VtOiBudWxsLA0KICAgICAgICBzcWRQb2xCb29raW5nQWdlbnQ6IG51bGwsDQogICAgICAgIHNxZFBvZEhhbmRsZUFnZW50OiBudWxsLA0KICAgICAgICBzcWRDYXJyaWVySWQ6IG51bGwsDQogICAgICAgIGxvZ2lzdGljc1BheW1lbnRUZXJtc0NvZGU6IG51bGwsDQogICAgICAgIHNxZERvY0RlbGl2ZXJ5V2F5OiBudWxsLA0KICAgICAgICBzcWREblJtYlN1bVZhdDogbnVsbCwNCiAgICAgICAgc3FkQ25SbWJTdW1WYXQ6IG51bGwsDQogICAgICAgIHNxZFByb2ZpdFJtYlN1bTogbnVsbCwNCiAgICAgICAgc3FkUHJvZml0Um1iU3VtVmF0OiBudWxsLA0KICAgICAgICB3YXJlaG91c2luZ05vOiBudWxsLA0KICAgICAgICBjbGllbnRKb2JObzogbnVsbCwNCiAgICAgICAgYm9va2luZ1NoaXBwZXI6IG51bGwsDQogICAgICAgIGJvb2tpbmdDb25zaWduZWU6IG51bGwsDQogICAgICAgIGJvb2tpbmdOb3RpZnlQYXJ0eTogbnVsbCwNCiAgICAgICAgc3FkSW5zdXJhbmNlVHlwZTogbnVsbCwNCiAgICAgICAgaXNPcENvbmZpcm1lZDogbnVsbCwNCiAgICAgICAgb3BDb25maXJtZWRJZDogbnVsbCwNCiAgICAgICAgb3BDb25maXJtZWROYW1lOiBudWxsLA0KICAgICAgICBvcENvbmZpcm1lZERhdGU6IG51bGwsDQogICAgICAgIGlzU2FsZXNDb25maXJtZWQ6IG51bGwsDQogICAgICAgIHNhbGVzQ29uZmlybWVkSWQ6IG51bGwsDQogICAgICAgIHNhbGVzQ29uZmlybWVkTmFtZTogbnVsbCwNCiAgICAgICAgc2FsZXNDb25maXJtZWREYXRlOiBudWxsLA0KICAgICAgICBpc0NsaWVudENvbmZpcm1lZDogbnVsbCwNCiAgICAgICAgY2xpZW50Q29uZmlybWVkSWQ6IG51bGwsDQogICAgICAgIGNsaWVudENvbmZpcm1lZE5hbWU6IG51bGwsDQogICAgICAgIGNsaWVudENvbmZpcm1lZERhdGU6IG51bGwsDQogICAgICAgIGlzQWNjb3VudENvbmZpcm1lZDogbnVsbCwNCiAgICAgICAgYWNjb3VudENvbmZpcm1lZElkOiBudWxsLA0KICAgICAgICBhY2NvdW50Q29uZmlybWVkTmFtZTogbnVsbCwNCiAgICAgICAgYWNjb3VudENvbmZpcm1lZERhdGU6IG51bGwsDQogICAgICAgIF9QYXltZW50VGl0bGVDb2RlOiBbXSwNCiAgICAgICAgX1JlbGF0aW9uQ2xpZW50SWRMaXN0OiBbXSwNCiAgICAgICAgcnNPcFNlYUZjbExpc3Q6IFtdLA0KICAgICAgICByc09wU2VhTGNsTGlzdDogW10sDQogICAgICAgIHJzT3BBaXJMaXN0OiBbXSwNCiAgICAgICAgcnNPcEN0bnJUcnVja0xpc3Q6IFtdLA0KICAgICAgICByc09wQnVsa1RydWNrTGlzdDogW10sDQogICAgICAgIHJzT3BEb2NEZWNsYXJlTGlzdDogW10sDQogICAgICAgIHJzT3BGcmVlRGVjbGFyZUxpc3Q6IFtdLA0KICAgICAgICBvcmRlckJlbG9uZ3NUbzogbnVsbA0KICAgICAgfQ0KDQogICAgICB0aGlzLnJzQ2xpZW50TWVzc2FnZSA9IHsNCiAgICAgICAgcnNDaGFyZ2VMaXN0OiBbXSwNCiAgICAgICAgcnNPcExvZ0xpc3Q6IFtdLA0KICAgICAgICByc0RvY0xpc3Q6IFtdDQogICAgICB9LA0KICAgICAgICAvLyBuZXcg5a2Q5pyN5Yqh5pWw5o2uDQogICAgICAgIHRoaXMucnNPcFNlYUZjbCA9IHsNCiAgICAgICAgICByc0NoYXJnZUxpc3Q6IFtdLA0KICAgICAgICAgIHJzT3BMb2dMaXN0OiBbXSwNCiAgICAgICAgICByc0RvY0xpc3Q6IFtdDQogICAgICAgIH0sDQogICAgICAgIHRoaXMucnNPcFNlYUxjbCA9IHsNCiAgICAgICAgICByc0NoYXJnZUxpc3Q6IFtdLA0KICAgICAgICAgIHJzT3BMb2dMaXN0OiBbXSwNCiAgICAgICAgICByc0RvY0xpc3Q6IFtdDQogICAgICAgIH0sDQogICAgICAgIHRoaXMucnNPcEFpciA9IHsNCiAgICAgICAgICByc0NoYXJnZUxpc3Q6IFtdLA0KICAgICAgICAgIHJzT3BMb2dMaXN0OiBbXSwNCiAgICAgICAgICByc0RvY0xpc3Q6IFtdDQogICAgICAgIH0sDQogICAgICAgIHRoaXMucnNPcFJhaWxGQ0wgPSB7DQogICAgICAgICAgcnNDaGFyZ2VMaXN0OiBbXSwNCiAgICAgICAgICByc09wTG9nTGlzdDogW10sDQogICAgICAgICAgcnNEb2NMaXN0OiBbXQ0KICAgICAgICB9LA0KICAgICAgICB0aGlzLnJzT3BSYWlsTENMID0gew0KICAgICAgICAgIHJzQ2hhcmdlTGlzdDogW10sDQogICAgICAgICAgcnNPcExvZ0xpc3Q6IFtdLA0KICAgICAgICAgIHJzRG9jTGlzdDogW10NCiAgICAgICAgfSwNCiAgICAgICAgdGhpcy5yc09wUmFpbCA9IHsNCiAgICAgICAgICByc0NoYXJnZUxpc3Q6IFtdLA0KICAgICAgICAgIHJzT3BMb2dMaXN0OiBbXSwNCiAgICAgICAgICByc0RvY0xpc3Q6IFtdDQogICAgICAgIH0sDQogICAgICAgIHRoaXMucnNPcEV4cHJlc3MgPSB7DQogICAgICAgICAgcnNDaGFyZ2VMaXN0OiBbXSwNCiAgICAgICAgICByc09wTG9nTGlzdDogW10sDQogICAgICAgICAgcnNEb2NMaXN0OiBbXQ0KICAgICAgICB9LA0KICAgICAgICB0aGlzLnJzT3BUcnVjayA9IHsNCiAgICAgICAgICByc0NoYXJnZUxpc3Q6IFtdLA0KICAgICAgICAgIHJzT3BMb2dMaXN0OiBbXSwNCiAgICAgICAgICByc0RvY0xpc3Q6IFtdDQogICAgICAgIH0sDQogICAgICAgIHRoaXMucnNPcEN0bnJUcnVjayA9IHsNCiAgICAgICAgICByc0NoYXJnZUxpc3Q6IFtdLA0KICAgICAgICAgIHJzT3BMb2dMaXN0OiBbXSwNCiAgICAgICAgICByc0RvY0xpc3Q6IFtdLA0KICAgICAgICAgIHJzT3BUcnVja0xpc3Q6IFtdDQogICAgICAgIH0sDQogICAgICAgIHRoaXMucnNPcEJ1bGtUcnVjayA9IHsNCiAgICAgICAgICByc0NoYXJnZUxpc3Q6IFtdLA0KICAgICAgICAgIHJzT3BMb2dMaXN0OiBbXSwNCiAgICAgICAgICByc0RvY0xpc3Q6IFtdLA0KICAgICAgICAgIHJzT3BUcnVja0xpc3Q6IFtdDQogICAgICAgIH0sDQogICAgICAgIC8v5q2j5Y2V5oql5YWzDQogICAgICAgIHRoaXMucnNPcERvY0RlY2xhcmUgPSB7DQogICAgICAgICAgcnNDaGFyZ2VMaXN0OiBbXSwNCiAgICAgICAgICByc09wTG9nTGlzdDogW10sDQogICAgICAgICAgcnNEb2NMaXN0OiBbXQ0KICAgICAgICB9LA0KICAgICAgICAvLyDlhajljIXmiqXlhbMNCiAgICAgICAgdGhpcy5yc09wRnJlZURlY2xhcmUgPSB7DQogICAgICAgICAgcnNDaGFyZ2VMaXN0OiBbXSwNCiAgICAgICAgICByc09wTG9nTGlzdDogW10sDQogICAgICAgICAgcnNEb2NMaXN0OiBbXQ0KICAgICAgICB9LA0KICAgICAgICB0aGlzLnJzT3BFeHBvcnRDdXN0b21zQ2xlYXJhbmNlID0gew0KICAgICAgICAgIHJzQ2hhcmdlTGlzdDogW10sDQogICAgICAgICAgcnNPcExvZ0xpc3Q6IFtdLA0KICAgICAgICAgIHJzRG9jTGlzdDogW10NCiAgICAgICAgfSwNCiAgICAgICAgdGhpcy5yc09wSW1wb3J0Q3VzdG9tc0NsZWFyYW5jZSA9IHsNCiAgICAgICAgICByc0NoYXJnZUxpc3Q6IFtdLA0KICAgICAgICAgIHJzT3BMb2dMaXN0OiBbXSwNCiAgICAgICAgICByc0RvY0xpc3Q6IFtdDQogICAgICAgIH0sDQogICAgICAgIHRoaXMucnNPcEltcG9ydERpc3BhdGNoVHJ1Y2sgPSB7DQogICAgICAgICAgcnNDaGFyZ2VMaXN0OiBbXSwNCiAgICAgICAgICByc09wTG9nTGlzdDogW10sDQogICAgICAgICAgcnNEb2NMaXN0OiBbXQ0KICAgICAgICB9LA0KICAgICAgICAvLyDku6PnkIbmlL7ljZUNCiAgICAgICAgdGhpcy5yc09wRE9BZ2VudCA9IHsNCiAgICAgICAgICByc0NoYXJnZUxpc3Q6IFtdLA0KICAgICAgICAgIHJzT3BMb2dMaXN0OiBbXSwNCiAgICAgICAgICByc0RvY0xpc3Q6IFtdDQogICAgICAgIH0sDQogICAgICAgIHRoaXMucnNPcENsZWFyQWdlbnQgPSB7DQogICAgICAgICAgcnNDaGFyZ2VMaXN0OiBbXSwNCiAgICAgICAgICByc09wTG9nTGlzdDogW10sDQogICAgICAgICAgcnNEb2NMaXN0OiBbXQ0KICAgICAgICB9LA0KICAgICAgICB0aGlzLnJzT3BXSFMgPSB7DQogICAgICAgICAgcnNDaGFyZ2VMaXN0OiBbXSwNCiAgICAgICAgICByc09wTG9nTGlzdDogW10sDQogICAgICAgICAgcnNEb2NMaXN0OiBbXQ0KICAgICAgICB9LA0KICAgICAgICB0aGlzLnJzT3BXYXJlaG91c2UgPSB7DQogICAgICAgICAgcnNDaGFyZ2VMaXN0OiBbXSwNCiAgICAgICAgICByc09wTG9nTGlzdDogW10sDQogICAgICAgICAgcnNEb2NMaXN0OiBbXQ0KICAgICAgICB9LA0KICAgICAgICB0aGlzLnJzT3BJbnNwZWN0aW9uQW5kQ2VydGlmaWNhdGUgPSB7DQogICAgICAgICAgcnNDaGFyZ2VMaXN0OiBbXSwNCiAgICAgICAgICByc09wTG9nTGlzdDogW10sDQogICAgICAgICAgcnNEb2NMaXN0OiBbXQ0KICAgICAgICB9LA0KICAgICAgICB0aGlzLnJzT3BJbnN1cmFuY2UgPSB7DQogICAgICAgICAgcnNDaGFyZ2VMaXN0OiBbXSwNCiAgICAgICAgICByc09wTG9nTGlzdDogW10sDQogICAgICAgICAgcnNEb2NMaXN0OiBbXQ0KICAgICAgICB9LA0KICAgICAgICB0aGlzLnJzT3BFeHBhbmRTZXJ2aWNlID0gew0KICAgICAgICAgIHJzQ2hhcmdlTGlzdDogW10sDQogICAgICAgICAgcnNPcExvZ0xpc3Q6IFtdLA0KICAgICAgICAgIHJzRG9jTGlzdDogW10NCiAgICAgICAgfSwNCiAgICAgICAgdGhpcy5yc09wM3JkQ2VydCA9IHsNCiAgICAgICAgICByc0NoYXJnZUxpc3Q6IFtdLA0KICAgICAgICAgIHJzT3BMb2dMaXN0OiBbXSwNCiAgICAgICAgICByc0RvY0xpc3Q6IFtdDQogICAgICAgIH0sDQogICAgICAgIHRoaXMucnNPcElOUyA9IHsNCiAgICAgICAgICByc0NoYXJnZUxpc3Q6IFtdLA0KICAgICAgICAgIHJzT3BMb2dMaXN0OiBbXSwNCiAgICAgICAgICByc0RvY0xpc3Q6IFtdDQogICAgICAgIH0sDQogICAgICAgIHRoaXMucnNPcFRyYWRpbmcgPSB7DQogICAgICAgICAgcnNDaGFyZ2VMaXN0OiBbXSwNCiAgICAgICAgICByc09wTG9nTGlzdDogW10sDQogICAgICAgICAgcnNEb2NMaXN0OiBbXQ0KICAgICAgICB9LA0KICAgICAgICB0aGlzLnJzT3BGdW1pZ2F0aW9uID0gew0KICAgICAgICAgIHJzQ2hhcmdlTGlzdDogW10sDQogICAgICAgICAgcnNPcExvZ0xpc3Q6IFtdLA0KICAgICAgICAgIHJzRG9jTGlzdDogW10NCiAgICAgICAgfSwNCiAgICAgICAgdGhpcy5yc09wQ08gPSB7DQogICAgICAgICAgcnNDaGFyZ2VMaXN0OiBbXSwNCiAgICAgICAgICByc09wTG9nTGlzdDogW10sDQogICAgICAgICAgcnNEb2NMaXN0OiBbXQ0KICAgICAgICB9LA0KICAgICAgICB0aGlzLnJzT3BPdGhlciA9IHsNCiAgICAgICAgICByc0NoYXJnZUxpc3Q6IFtdLA0KICAgICAgICAgIHJzT3BMb2dMaXN0OiBbXSwNCiAgICAgICAgICByc0RvY0xpc3Q6IFtdDQogICAgICAgIH0NCg0KICAgICAgdGhpcy5yc0NsaWVudFNlcnZpY2VJbnN0YW5jZSA9IHsNCiAgICAgICAgc2VydmljZUlkOiBudWxsLA0KICAgICAgICBzZXJ2aWNlVHlwZUlkOiBudWxsLA0KICAgICAgICByY3RJZDogbnVsbCwNCiAgICAgICAgcmN0Tm86IG51bGwsDQogICAgICAgIHN1cHBsaWVySWQ6IG51bGwsDQogICAgICAgIHN1cHBsaWVyU3VtbWFyeTogbnVsbCwNCiAgICAgICAgc3VwcGxpZXJDb250YWN0OiBudWxsLA0KICAgICAgICBzdXBwbGllclRlbDogbnVsbCwNCiAgICAgICAgcGF5bWVudFRpdGxlQ29kZTogbnVsbCwNCiAgICAgICAgbG9naXN0aWNzUGF5bWVudFRlcm1zQ29kZTogbnVsbCwNCiAgICAgICAgaW5xdWlyeU5vOiBudWxsLA0KICAgICAgICBhZ3JlZW1lbnRUeXBlQ29kZTogbnVsbCwNCiAgICAgICAgYWdyZWVtZW50Tm86IG51bGwsDQogICAgICAgIG1heFdlaWdodDogbnVsbCwNCiAgICAgICAgaW5xdWlyeU5vdGljZTogbnVsbCwNCiAgICAgICAgaW5xdWlyeUlubmVyUmVtYXJrOiBudWxsLA0KICAgICAgICBpbnF1aXJ5UHNhSWQ6IG51bGwsDQogICAgICAgIGlucXVpcnlMZWF0ZXN0VXBkYXRlZFRpbWU6IG51bGwsDQogICAgICAgIHNlcnZpY2VCZWxvbmdUbzogbnVsbCwNCiAgICAgICAgaXNEblNhbGVzQ29uZmlybWVkOiBudWxsLA0KICAgICAgICBpc0RuQ2xpZW50Q29uZmlybWVkOiBudWxsLA0KICAgICAgICBpc0RuT3BDb25maXJtZWQ6IG51bGwsDQogICAgICAgIGlzRG5Qc2FDb25maXJtZWQ6IG51bGwsDQogICAgICAgIGlzRG5TdXBwbGllckNvbmZpcm1lZDogbnVsbCwNCiAgICAgICAgaXNBY2NvdW50Q29uZmlybWVkOiBudWxsLA0KICAgICAgICBjb25maXJtQWNjb3VudElkOiBudWxsLA0KICAgICAgICBhY2NvdW50Q29uZmlybVRpbWU6IG51bGwsDQogICAgICAgIHNhbGVzQ29uZmlybWVkVGltZTogbnVsbCwNCiAgICAgICAgY2xpZW50Q29uZmlybWVkVGltZTogbnVsbCwNCiAgICAgICAgb3BDb25maXJtZWRUaW1lOiBudWxsLA0KICAgICAgICBwc2FDb25maXJtZWRUaW1lOiBudWxsLA0KICAgICAgICBzdXBwbGllckNvbmZpcm1lZFRpbWU6IG51bGwNCiAgICAgIH0sDQogICAgICAgIHRoaXMucnNPcFNlYUZjbFNlcnZpY2VJbnN0YW5jZSA9IHsNCiAgICAgICAgICBzZXJ2aWNlSWQ6IG51bGwsDQogICAgICAgICAgc2VydmljZVR5cGVJZDogbnVsbCwNCiAgICAgICAgICByY3RJZDogbnVsbCwNCiAgICAgICAgICByY3RObzogbnVsbCwNCiAgICAgICAgICBzdXBwbGllcklkOiBudWxsLA0KICAgICAgICAgIHN1cHBsaWVyU3VtbWFyeTogbnVsbCwNCiAgICAgICAgICBzdXBwbGllckNvbnRhY3Q6IG51bGwsDQogICAgICAgICAgc3VwcGxpZXJUZWw6IG51bGwsDQogICAgICAgICAgcGF5bWVudFRpdGxlQ29kZTogbnVsbCwNCiAgICAgICAgICBsb2dpc3RpY3NQYXltZW50VGVybXNDb2RlOiBudWxsLA0KICAgICAgICAgIGlucXVpcnlObzogbnVsbCwNCiAgICAgICAgICBhZ3JlZW1lbnRUeXBlQ29kZTogbnVsbCwNCiAgICAgICAgICBhZ3JlZW1lbnRObzogbnVsbCwNCiAgICAgICAgICBtYXhXZWlnaHQ6IG51bGwsDQogICAgICAgICAgaW5xdWlyeU5vdGljZTogbnVsbCwNCiAgICAgICAgICBpbnF1aXJ5SW5uZXJSZW1hcms6IG51bGwsDQogICAgICAgICAgaW5xdWlyeVBzYUlkOiBudWxsLA0KICAgICAgICAgIGlucXVpcnlMZWF0ZXN0VXBkYXRlZFRpbWU6IG51bGwsDQogICAgICAgICAgc2VydmljZUJlbG9uZ1RvOiBudWxsLA0KICAgICAgICAgIGlzRG5TYWxlc0NvbmZpcm1lZDogbnVsbCwNCiAgICAgICAgICBpc0RuQ2xpZW50Q29uZmlybWVkOiBudWxsLA0KICAgICAgICAgIGlzRG5PcENvbmZpcm1lZDogbnVsbCwNCiAgICAgICAgICBpc0RuUHNhQ29uZmlybWVkOiBudWxsLA0KICAgICAgICAgIGlzRG5TdXBwbGllckNvbmZpcm1lZDogbnVsbCwNCiAgICAgICAgICBpc0FjY291bnRDb25maXJtZWQ6IG51bGwsDQogICAgICAgICAgY29uZmlybUFjY291bnRJZDogbnVsbCwNCiAgICAgICAgICBhY2NvdW50Q29uZmlybVRpbWU6IG51bGwsDQogICAgICAgICAgc2FsZXNDb25maXJtZWRUaW1lOiBudWxsLA0KICAgICAgICAgIGNsaWVudENvbmZpcm1lZFRpbWU6IG51bGwsDQogICAgICAgICAgb3BDb25maXJtZWRUaW1lOiBudWxsLA0KICAgICAgICAgIHBzYUNvbmZpcm1lZFRpbWU6IG51bGwsDQogICAgICAgICAgc3VwcGxpZXJDb25maXJtZWRUaW1lOiBudWxsDQogICAgICAgIH0sDQogICAgICAgIHRoaXMucnNPcFNlYUxjbFNlcnZpY2VJbnN0YW5jZSA9IHsNCiAgICAgICAgICBzZXJ2aWNlSWQ6IG51bGwsDQogICAgICAgICAgc2VydmljZVR5cGVJZDogbnVsbCwNCiAgICAgICAgICByY3RJZDogbnVsbCwNCiAgICAgICAgICByY3RObzogbnVsbCwNCiAgICAgICAgICBzdXBwbGllcklkOiBudWxsLA0KICAgICAgICAgIHN1cHBsaWVyU3VtbWFyeTogbnVsbCwNCiAgICAgICAgICBzdXBwbGllckNvbnRhY3Q6IG51bGwsDQogICAgICAgICAgc3VwcGxpZXJUZWw6IG51bGwsDQogICAgICAgICAgcGF5bWVudFRpdGxlQ29kZTogbnVsbCwNCiAgICAgICAgICBsb2dpc3RpY3NQYXltZW50VGVybXNDb2RlOiBudWxsLA0KICAgICAgICAgIGlucXVpcnlObzogbnVsbCwNCiAgICAgICAgICBhZ3JlZW1lbnRUeXBlQ29kZTogbnVsbCwNCiAgICAgICAgICBhZ3JlZW1lbnRObzogbnVsbCwNCiAgICAgICAgICBtYXhXZWlnaHQ6IG51bGwsDQogICAgICAgICAgaW5xdWlyeU5vdGljZTogbnVsbCwNCiAgICAgICAgICBpbnF1aXJ5SW5uZXJSZW1hcms6IG51bGwsDQogICAgICAgICAgaW5xdWlyeVBzYUlkOiBudWxsLA0KICAgICAgICAgIGlucXVpcnlMZWF0ZXN0VXBkYXRlZFRpbWU6IG51bGwsDQogICAgICAgICAgc2VydmljZUJlbG9uZ1RvOiBudWxsLA0KICAgICAgICAgIGlzRG5TYWxlc0NvbmZpcm1lZDogbnVsbCwNCiAgICAgICAgICBpc0RuQ2xpZW50Q29uZmlybWVkOiBudWxsLA0KICAgICAgICAgIGlzRG5PcENvbmZpcm1lZDogbnVsbCwNCiAgICAgICAgICBpc0RuUHNhQ29uZmlybWVkOiBudWxsLA0KICAgICAgICAgIGlzRG5TdXBwbGllckNvbmZpcm1lZDogbnVsbCwNCiAgICAgICAgICBpc0FjY291bnRDb25maXJtZWQ6IG51bGwsDQogICAgICAgICAgY29uZmlybUFjY291bnRJZDogbnVsbCwNCiAgICAgICAgICBhY2NvdW50Q29uZmlybVRpbWU6IG51bGwsDQogICAgICAgICAgc2FsZXNDb25maXJtZWRUaW1lOiBudWxsLA0KICAgICAgICAgIGNsaWVudENvbmZpcm1lZFRpbWU6IG51bGwsDQogICAgICAgICAgb3BDb25maXJtZWRUaW1lOiBudWxsLA0KICAgICAgICAgIHBzYUNvbmZpcm1lZFRpbWU6IG51bGwsDQogICAgICAgICAgc3VwcGxpZXJDb25maXJtZWRUaW1lOiBudWxsDQogICAgICAgIH0sDQogICAgICAgIHRoaXMucnNPcEFpclNlcnZpY2VJbnN0YW5jZSA9IHsNCiAgICAgICAgICBzZXJ2aWNlSWQ6IG51bGwsDQogICAgICAgICAgc2VydmljZVR5cGVJZDogbnVsbCwNCiAgICAgICAgICByY3RJZDogbnVsbCwNCiAgICAgICAgICByY3RObzogbnVsbCwNCiAgICAgICAgICBzdXBwbGllcklkOiBudWxsLA0KICAgICAgICAgIHN1cHBsaWVyU3VtbWFyeTogbnVsbCwNCiAgICAgICAgICBzdXBwbGllckNvbnRhY3Q6IG51bGwsDQogICAgICAgICAgc3VwcGxpZXJUZWw6IG51bGwsDQogICAgICAgICAgcGF5bWVudFRpdGxlQ29kZTogbnVsbCwNCiAgICAgICAgICBsb2dpc3RpY3NQYXltZW50VGVybXNDb2RlOiBudWxsLA0KICAgICAgICAgIGlucXVpcnlObzogbnVsbCwNCiAgICAgICAgICBhZ3JlZW1lbnRUeXBlQ29kZTogbnVsbCwNCiAgICAgICAgICBhZ3JlZW1lbnRObzogbnVsbCwNCiAgICAgICAgICBtYXhXZWlnaHQ6IG51bGwsDQogICAgICAgICAgaW5xdWlyeU5vdGljZTogbnVsbCwNCiAgICAgICAgICBpbnF1aXJ5SW5uZXJSZW1hcms6IG51bGwsDQogICAgICAgICAgaW5xdWlyeVBzYUlkOiBudWxsLA0KICAgICAgICAgIGlucXVpcnlMZWF0ZXN0VXBkYXRlZFRpbWU6IG51bGwsDQogICAgICAgICAgc2VydmljZUJlbG9uZ1RvOiBudWxsLA0KICAgICAgICAgIGlzRG5TYWxlc0NvbmZpcm1lZDogbnVsbCwNCiAgICAgICAgICBpc0RuQ2xpZW50Q29uZmlybWVkOiBudWxsLA0KICAgICAgICAgIGlzRG5PcENvbmZpcm1lZDogbnVsbCwNCiAgICAgICAgICBpc0RuUHNhQ29uZmlybWVkOiBudWxsLA0KICAgICAgICAgIGlzRG5TdXBwbGllckNvbmZpcm1lZDogbnVsbCwNCiAgICAgICAgICBpc0FjY291bnRDb25maXJtZWQ6IG51bGwsDQogICAgICAgICAgY29uZmlybUFjY291bnRJZDogbnVsbCwNCiAgICAgICAgICBhY2NvdW50Q29uZmlybVRpbWU6IG51bGwsDQogICAgICAgICAgc2FsZXNDb25maXJtZWRUaW1lOiBudWxsLA0KICAgICAgICAgIGNsaWVudENvbmZpcm1lZFRpbWU6IG51bGwsDQogICAgICAgICAgb3BDb25maXJtZWRUaW1lOiBudWxsLA0KICAgICAgICAgIHBzYUNvbmZpcm1lZFRpbWU6IG51bGwsDQogICAgICAgICAgc3VwcGxpZXJDb25maXJtZWRUaW1lOiBudWxsDQogICAgICAgIH0sDQogICAgICAgIHRoaXMucnNPcFJhaWxTZXJ2aWNlSW5zdGFuY2UgPSB7DQogICAgICAgICAgc2VydmljZUlkOiBudWxsLA0KICAgICAgICAgIHNlcnZpY2VUeXBlSWQ6IG51bGwsDQogICAgICAgICAgcmN0SWQ6IG51bGwsDQogICAgICAgICAgcmN0Tm86IG51bGwsDQogICAgICAgICAgc3VwcGxpZXJJZDogbnVsbCwNCiAgICAgICAgICBzdXBwbGllclN1bW1hcnk6IG51bGwsDQogICAgICAgICAgc3VwcGxpZXJDb250YWN0OiBudWxsLA0KICAgICAgICAgIHN1cHBsaWVyVGVsOiBudWxsLA0KICAgICAgICAgIHBheW1lbnRUaXRsZUNvZGU6IG51bGwsDQogICAgICAgICAgbG9naXN0aWNzUGF5bWVudFRlcm1zQ29kZTogbnVsbCwNCiAgICAgICAgICBpbnF1aXJ5Tm86IG51bGwsDQogICAgICAgICAgYWdyZWVtZW50VHlwZUNvZGU6IG51bGwsDQogICAgICAgICAgYWdyZWVtZW50Tm86IG51bGwsDQogICAgICAgICAgbWF4V2VpZ2h0OiBudWxsLA0KICAgICAgICAgIGlucXVpcnlOb3RpY2U6IG51bGwsDQogICAgICAgICAgaW5xdWlyeUlubmVyUmVtYXJrOiBudWxsLA0KICAgICAgICAgIGlucXVpcnlQc2FJZDogbnVsbCwNCiAgICAgICAgICBpbnF1aXJ5TGVhdGVzdFVwZGF0ZWRUaW1lOiBudWxsLA0KICAgICAgICAgIHNlcnZpY2VCZWxvbmdUbzogbnVsbCwNCiAgICAgICAgICBpc0RuU2FsZXNDb25maXJtZWQ6IG51bGwsDQogICAgICAgICAgaXNEbkNsaWVudENvbmZpcm1lZDogbnVsbCwNCiAgICAgICAgICBpc0RuT3BDb25maXJtZWQ6IG51bGwsDQogICAgICAgICAgaXNEblBzYUNvbmZpcm1lZDogbnVsbCwNCiAgICAgICAgICBpc0RuU3VwcGxpZXJDb25maXJtZWQ6IG51bGwsDQogICAgICAgICAgaXNBY2NvdW50Q29uZmlybWVkOiBudWxsLA0KICAgICAgICAgIGNvbmZpcm1BY2NvdW50SWQ6IG51bGwsDQogICAgICAgICAgYWNjb3VudENvbmZpcm1UaW1lOiBudWxsLA0KICAgICAgICAgIHNhbGVzQ29uZmlybWVkVGltZTogbnVsbCwNCiAgICAgICAgICBjbGllbnRDb25maXJtZWRUaW1lOiBudWxsLA0KICAgICAgICAgIG9wQ29uZmlybWVkVGltZTogbnVsbCwNCiAgICAgICAgICBwc2FDb25maXJtZWRUaW1lOiBudWxsLA0KICAgICAgICAgIHN1cHBsaWVyQ29uZmlybWVkVGltZTogbnVsbA0KICAgICAgICB9LA0KICAgICAgICB0aGlzLnJzT3BSYWlsRmNsU2VydmljZUluc3RhbmNlID0gew0KICAgICAgICAgIHNlcnZpY2VJZDogbnVsbCwNCiAgICAgICAgICBzZXJ2aWNlVHlwZUlkOiBudWxsLA0KICAgICAgICAgIHJjdElkOiBudWxsLA0KICAgICAgICAgIHJjdE5vOiBudWxsLA0KICAgICAgICAgIHN1cHBsaWVySWQ6IG51bGwsDQogICAgICAgICAgc3VwcGxpZXJTdW1tYXJ5OiBudWxsLA0KICAgICAgICAgIHN1cHBsaWVyQ29udGFjdDogbnVsbCwNCiAgICAgICAgICBzdXBwbGllclRlbDogbnVsbCwNCiAgICAgICAgICBwYXltZW50VGl0bGVDb2RlOiBudWxsLA0KICAgICAgICAgIGxvZ2lzdGljc1BheW1lbnRUZXJtc0NvZGU6IG51bGwsDQogICAgICAgICAgaW5xdWlyeU5vOiBudWxsLA0KICAgICAgICAgIGFncmVlbWVudFR5cGVDb2RlOiBudWxsLA0KICAgICAgICAgIGFncmVlbWVudE5vOiBudWxsLA0KICAgICAgICAgIG1heFdlaWdodDogbnVsbCwNCiAgICAgICAgICBpbnF1aXJ5Tm90aWNlOiBudWxsLA0KICAgICAgICAgIGlucXVpcnlJbm5lclJlbWFyazogbnVsbCwNCiAgICAgICAgICBpbnF1aXJ5UHNhSWQ6IG51bGwsDQogICAgICAgICAgaW5xdWlyeUxlYXRlc3RVcGRhdGVkVGltZTogbnVsbCwNCiAgICAgICAgICBzZXJ2aWNlQmVsb25nVG86IG51bGwsDQogICAgICAgICAgaXNEblNhbGVzQ29uZmlybWVkOiBudWxsLA0KICAgICAgICAgIGlzRG5DbGllbnRDb25maXJtZWQ6IG51bGwsDQogICAgICAgICAgaXNEbk9wQ29uZmlybWVkOiBudWxsLA0KICAgICAgICAgIGlzRG5Qc2FDb25maXJtZWQ6IG51bGwsDQogICAgICAgICAgaXNEblN1cHBsaWVyQ29uZmlybWVkOiBudWxsLA0KICAgICAgICAgIGlzQWNjb3VudENvbmZpcm1lZDogbnVsbCwNCiAgICAgICAgICBjb25maXJtQWNjb3VudElkOiBudWxsLA0KICAgICAgICAgIGFjY291bnRDb25maXJtVGltZTogbnVsbCwNCiAgICAgICAgICBzYWxlc0NvbmZpcm1lZFRpbWU6IG51bGwsDQogICAgICAgICAgY2xpZW50Q29uZmlybWVkVGltZTogbnVsbCwNCiAgICAgICAgICBvcENvbmZpcm1lZFRpbWU6IG51bGwsDQogICAgICAgICAgcHNhQ29uZmlybWVkVGltZTogbnVsbCwNCiAgICAgICAgICBzdXBwbGllckNvbmZpcm1lZFRpbWU6IG51bGwNCiAgICAgICAgfSwNCiAgICAgICAgdGhpcy5yc09wUmFpbExjbFNlcnZpY2VJbnN0YW5jZSA9IHsNCiAgICAgICAgICBzZXJ2aWNlSWQ6IG51bGwsDQogICAgICAgICAgc2VydmljZVR5cGVJZDogbnVsbCwNCiAgICAgICAgICByY3RJZDogbnVsbCwNCiAgICAgICAgICByY3RObzogbnVsbCwNCiAgICAgICAgICBzdXBwbGllcklkOiBudWxsLA0KICAgICAgICAgIHN1cHBsaWVyU3VtbWFyeTogbnVsbCwNCiAgICAgICAgICBzdXBwbGllckNvbnRhY3Q6IG51bGwsDQogICAgICAgICAgc3VwcGxpZXJUZWw6IG51bGwsDQogICAgICAgICAgcGF5bWVudFRpdGxlQ29kZTogbnVsbCwNCiAgICAgICAgICBsb2dpc3RpY3NQYXltZW50VGVybXNDb2RlOiBudWxsLA0KICAgICAgICAgIGlucXVpcnlObzogbnVsbCwNCiAgICAgICAgICBhZ3JlZW1lbnRUeXBlQ29kZTogbnVsbCwNCiAgICAgICAgICBhZ3JlZW1lbnRObzogbnVsbCwNCiAgICAgICAgICBtYXhXZWlnaHQ6IG51bGwsDQogICAgICAgICAgaW5xdWlyeU5vdGljZTogbnVsbCwNCiAgICAgICAgICBpbnF1aXJ5SW5uZXJSZW1hcms6IG51bGwsDQogICAgICAgICAgaW5xdWlyeVBzYUlkOiBudWxsLA0KICAgICAgICAgIGlucXVpcnlMZWF0ZXN0VXBkYXRlZFRpbWU6IG51bGwsDQogICAgICAgICAgc2VydmljZUJlbG9uZ1RvOiBudWxsLA0KICAgICAgICAgIGlzRG5TYWxlc0NvbmZpcm1lZDogbnVsbCwNCiAgICAgICAgICBpc0RuQ2xpZW50Q29uZmlybWVkOiBudWxsLA0KICAgICAgICAgIGlzRG5PcENvbmZpcm1lZDogbnVsbCwNCiAgICAgICAgICBpc0RuUHNhQ29uZmlybWVkOiBudWxsLA0KICAgICAgICAgIGlzRG5TdXBwbGllckNvbmZpcm1lZDogbnVsbCwNCiAgICAgICAgICBpc0FjY291bnRDb25maXJtZWQ6IG51bGwsDQogICAgICAgICAgY29uZmlybUFjY291bnRJZDogbnVsbCwNCiAgICAgICAgICBhY2NvdW50Q29uZmlybVRpbWU6IG51bGwsDQogICAgICAgICAgc2FsZXNDb25maXJtZWRUaW1lOiBudWxsLA0KICAgICAgICAgIGNsaWVudENvbmZpcm1lZFRpbWU6IG51bGwsDQogICAgICAgICAgb3BDb25maXJtZWRUaW1lOiBudWxsLA0KICAgICAgICAgIHBzYUNvbmZpcm1lZFRpbWU6IG51bGwsDQogICAgICAgICAgc3VwcGxpZXJDb25maXJtZWRUaW1lOiBudWxsDQogICAgICAgIH0sDQogICAgICAgIHRoaXMucnNPcEV4cHJlc3NTZXJ2aWNlSW5zdGFuY2UgPSB7DQogICAgICAgICAgc2VydmljZUlkOiBudWxsLA0KICAgICAgICAgIHNlcnZpY2VUeXBlSWQ6IG51bGwsDQogICAgICAgICAgcmN0SWQ6IG51bGwsDQogICAgICAgICAgcmN0Tm86IG51bGwsDQogICAgICAgICAgc3VwcGxpZXJJZDogbnVsbCwNCiAgICAgICAgICBzdXBwbGllclN1bW1hcnk6IG51bGwsDQogICAgICAgICAgc3VwcGxpZXJDb250YWN0OiBudWxsLA0KICAgICAgICAgIHN1cHBsaWVyVGVsOiBudWxsLA0KICAgICAgICAgIHBheW1lbnRUaXRsZUNvZGU6IG51bGwsDQogICAgICAgICAgbG9naXN0aWNzUGF5bWVudFRlcm1zQ29kZTogbnVsbCwNCiAgICAgICAgICBpbnF1aXJ5Tm86IG51bGwsDQogICAgICAgICAgYWdyZWVtZW50VHlwZUNvZGU6IG51bGwsDQogICAgICAgICAgYWdyZWVtZW50Tm86IG51bGwsDQogICAgICAgICAgbWF4V2VpZ2h0OiBudWxsLA0KICAgICAgICAgIGlucXVpcnlOb3RpY2U6IG51bGwsDQogICAgICAgICAgaW5xdWlyeUlubmVyUmVtYXJrOiBudWxsLA0KICAgICAgICAgIGlucXVpcnlQc2FJZDogbnVsbCwNCiAgICAgICAgICBpbnF1aXJ5TGVhdGVzdFVwZGF0ZWRUaW1lOiBudWxsLA0KICAgICAgICAgIHNlcnZpY2VCZWxvbmdUbzogbnVsbCwNCiAgICAgICAgICBpc0RuU2FsZXNDb25maXJtZWQ6IG51bGwsDQogICAgICAgICAgaXNEbkNsaWVudENvbmZpcm1lZDogbnVsbCwNCiAgICAgICAgICBpc0RuT3BDb25maXJtZWQ6IG51bGwsDQogICAgICAgICAgaXNEblBzYUNvbmZpcm1lZDogbnVsbCwNCiAgICAgICAgICBpc0RuU3VwcGxpZXJDb25maXJtZWQ6IG51bGwsDQogICAgICAgICAgaXNBY2NvdW50Q29uZmlybWVkOiBudWxsLA0KICAgICAgICAgIGNvbmZpcm1BY2NvdW50SWQ6IG51bGwsDQogICAgICAgICAgYWNjb3VudENvbmZpcm1UaW1lOiBudWxsLA0KICAgICAgICAgIHNhbGVzQ29uZmlybWVkVGltZTogbnVsbCwNCiAgICAgICAgICBjbGllbnRDb25maXJtZWRUaW1lOiBudWxsLA0KICAgICAgICAgIG9wQ29uZmlybWVkVGltZTogbnVsbCwNCiAgICAgICAgICBwc2FDb25maXJtZWRUaW1lOiBudWxsLA0KICAgICAgICAgIHN1cHBsaWVyQ29uZmlybWVkVGltZTogbnVsbA0KICAgICAgICB9LA0KICAgICAgICB0aGlzLnJzT3BUcnVja1NlcnZpY2VJbnN0YW5jZSA9IHsNCiAgICAgICAgICBzZXJ2aWNlSWQ6IG51bGwsDQogICAgICAgICAgc2VydmljZVR5cGVJZDogbnVsbCwNCiAgICAgICAgICByY3RJZDogbnVsbCwNCiAgICAgICAgICByY3RObzogbnVsbCwNCiAgICAgICAgICBzdXBwbGllcklkOiBudWxsLA0KICAgICAgICAgIHN1cHBsaWVyU3VtbWFyeTogbnVsbCwNCiAgICAgICAgICBzdXBwbGllckNvbnRhY3Q6IG51bGwsDQogICAgICAgICAgc3VwcGxpZXJUZWw6IG51bGwsDQogICAgICAgICAgcGF5bWVudFRpdGxlQ29kZTogbnVsbCwNCiAgICAgICAgICBsb2dpc3RpY3NQYXltZW50VGVybXNDb2RlOiBudWxsLA0KICAgICAgICAgIGlucXVpcnlObzogbnVsbCwNCiAgICAgICAgICBhZ3JlZW1lbnRUeXBlQ29kZTogbnVsbCwNCiAgICAgICAgICBhZ3JlZW1lbnRObzogbnVsbCwNCiAgICAgICAgICBtYXhXZWlnaHQ6IG51bGwsDQogICAgICAgICAgaW5xdWlyeU5vdGljZTogbnVsbCwNCiAgICAgICAgICBpbnF1aXJ5SW5uZXJSZW1hcms6IG51bGwsDQogICAgICAgICAgaW5xdWlyeVBzYUlkOiBudWxsLA0KICAgICAgICAgIGlucXVpcnlMZWF0ZXN0VXBkYXRlZFRpbWU6IG51bGwsDQogICAgICAgICAgc2VydmljZUJlbG9uZ1RvOiBudWxsLA0KICAgICAgICAgIGlzRG5TYWxlc0NvbmZpcm1lZDogbnVsbCwNCiAgICAgICAgICBpc0RuQ2xpZW50Q29uZmlybWVkOiBudWxsLA0KICAgICAgICAgIGlzRG5PcENvbmZpcm1lZDogbnVsbCwNCiAgICAgICAgICBpc0RuUHNhQ29uZmlybWVkOiBudWxsLA0KICAgICAgICAgIGlzRG5TdXBwbGllckNvbmZpcm1lZDogbnVsbCwNCiAgICAgICAgICBpc0FjY291bnRDb25maXJtZWQ6IG51bGwsDQogICAgICAgICAgY29uZmlybUFjY291bnRJZDogbnVsbCwNCiAgICAgICAgICBhY2NvdW50Q29uZmlybVRpbWU6IG51bGwsDQogICAgICAgICAgc2FsZXNDb25maXJtZWRUaW1lOiBudWxsLA0KICAgICAgICAgIGNsaWVudENvbmZpcm1lZFRpbWU6IG51bGwsDQogICAgICAgICAgb3BDb25maXJtZWRUaW1lOiBudWxsLA0KICAgICAgICAgIHBzYUNvbmZpcm1lZFRpbWU6IG51bGwsDQogICAgICAgICAgc3VwcGxpZXJDb25maXJtZWRUaW1lOiBudWxsDQogICAgICAgIH0sDQogICAgICAgIHRoaXMucnNPcEN0bnJUcnVja1NlcnZpY2VJbnN0YW5jZSA9IHsNCiAgICAgICAgICBzZXJ2aWNlSWQ6IG51bGwsDQogICAgICAgICAgc2VydmljZVR5cGVJZDogbnVsbCwNCiAgICAgICAgICByY3RJZDogbnVsbCwNCiAgICAgICAgICByY3RObzogbnVsbCwNCiAgICAgICAgICBzdXBwbGllcklkOiBudWxsLA0KICAgICAgICAgIHN1cHBsaWVyU3VtbWFyeTogbnVsbCwNCiAgICAgICAgICBzdXBwbGllckNvbnRhY3Q6IG51bGwsDQogICAgICAgICAgc3VwcGxpZXJUZWw6IG51bGwsDQogICAgICAgICAgcGF5bWVudFRpdGxlQ29kZTogbnVsbCwNCiAgICAgICAgICBsb2dpc3RpY3NQYXltZW50VGVybXNDb2RlOiBudWxsLA0KICAgICAgICAgIGlucXVpcnlObzogbnVsbCwNCiAgICAgICAgICBhZ3JlZW1lbnRUeXBlQ29kZTogbnVsbCwNCiAgICAgICAgICBhZ3JlZW1lbnRObzogbnVsbCwNCiAgICAgICAgICBtYXhXZWlnaHQ6IG51bGwsDQogICAgICAgICAgaW5xdWlyeU5vdGljZTogbnVsbCwNCiAgICAgICAgICBpbnF1aXJ5SW5uZXJSZW1hcms6IG51bGwsDQogICAgICAgICAgaW5xdWlyeVBzYUlkOiBudWxsLA0KICAgICAgICAgIGlucXVpcnlMZWF0ZXN0VXBkYXRlZFRpbWU6IG51bGwsDQogICAgICAgICAgc2VydmljZUJlbG9uZ1RvOiBudWxsLA0KICAgICAgICAgIGlzRG5TYWxlc0NvbmZpcm1lZDogbnVsbCwNCiAgICAgICAgICBpc0RuQ2xpZW50Q29uZmlybWVkOiBudWxsLA0KICAgICAgICAgIGlzRG5PcENvbmZpcm1lZDogbnVsbCwNCiAgICAgICAgICBpc0RuUHNhQ29uZmlybWVkOiBudWxsLA0KICAgICAgICAgIGlzRG5TdXBwbGllckNvbmZpcm1lZDogbnVsbCwNCiAgICAgICAgICBpc0FjY291bnRDb25maXJtZWQ6IG51bGwsDQogICAgICAgICAgY29uZmlybUFjY291bnRJZDogbnVsbCwNCiAgICAgICAgICBhY2NvdW50Q29uZmlybVRpbWU6IG51bGwsDQogICAgICAgICAgc2FsZXNDb25maXJtZWRUaW1lOiBudWxsLA0KICAgICAgICAgIGNsaWVudENvbmZpcm1lZFRpbWU6IG51bGwsDQogICAgICAgICAgb3BDb25maXJtZWRUaW1lOiBudWxsLA0KICAgICAgICAgIHBzYUNvbmZpcm1lZFRpbWU6IG51bGwsDQogICAgICAgICAgc3VwcGxpZXJDb25maXJtZWRUaW1lOiBudWxsDQogICAgICAgIH0sDQogICAgICAgIHRoaXMucnNPcEJ1bGtUcnVja1NlcnZpY2VJbnN0YW5jZSA9IHsNCiAgICAgICAgICBzZXJ2aWNlSWQ6IG51bGwsDQogICAgICAgICAgc2VydmljZVR5cGVJZDogbnVsbCwNCiAgICAgICAgICByY3RJZDogbnVsbCwNCiAgICAgICAgICByY3RObzogbnVsbCwNCiAgICAgICAgICBzdXBwbGllcklkOiBudWxsLA0KICAgICAgICAgIHN1cHBsaWVyU3VtbWFyeTogbnVsbCwNCiAgICAgICAgICBzdXBwbGllckNvbnRhY3Q6IG51bGwsDQogICAgICAgICAgc3VwcGxpZXJUZWw6IG51bGwsDQogICAgICAgICAgcGF5bWVudFRpdGxlQ29kZTogbnVsbCwNCiAgICAgICAgICBsb2dpc3RpY3NQYXltZW50VGVybXNDb2RlOiBudWxsLA0KICAgICAgICAgIGlucXVpcnlObzogbnVsbCwNCiAgICAgICAgICBhZ3JlZW1lbnRUeXBlQ29kZTogbnVsbCwNCiAgICAgICAgICBhZ3JlZW1lbnRObzogbnVsbCwNCiAgICAgICAgICBtYXhXZWlnaHQ6IG51bGwsDQogICAgICAgICAgaW5xdWlyeU5vdGljZTogbnVsbCwNCiAgICAgICAgICBpbnF1aXJ5SW5uZXJSZW1hcms6IG51bGwsDQogICAgICAgICAgaW5xdWlyeVBzYUlkOiBudWxsLA0KICAgICAgICAgIGlucXVpcnlMZWF0ZXN0VXBkYXRlZFRpbWU6IG51bGwsDQogICAgICAgICAgc2VydmljZUJlbG9uZ1RvOiBudWxsLA0KICAgICAgICAgIGlzRG5TYWxlc0NvbmZpcm1lZDogbnVsbCwNCiAgICAgICAgICBpc0RuQ2xpZW50Q29uZmlybWVkOiBudWxsLA0KICAgICAgICAgIGlzRG5PcENvbmZpcm1lZDogbnVsbCwNCiAgICAgICAgICBpc0RuUHNhQ29uZmlybWVkOiBudWxsLA0KICAgICAgICAgIGlzRG5TdXBwbGllckNvbmZpcm1lZDogbnVsbCwNCiAgICAgICAgICBpc0FjY291bnRDb25maXJtZWQ6IG51bGwsDQogICAgICAgICAgY29uZmlybUFjY291bnRJZDogbnVsbCwNCiAgICAgICAgICBhY2NvdW50Q29uZmlybVRpbWU6IG51bGwsDQogICAgICAgICAgc2FsZXNDb25maXJtZWRUaW1lOiBudWxsLA0KICAgICAgICAgIGNsaWVudENvbmZpcm1lZFRpbWU6IG51bGwsDQogICAgICAgICAgb3BDb25maXJtZWRUaW1lOiBudWxsLA0KICAgICAgICAgIHBzYUNvbmZpcm1lZFRpbWU6IG51bGwsDQogICAgICAgICAgc3VwcGxpZXJDb25maXJtZWRUaW1lOiBudWxsDQogICAgICAgIH0sDQogICAgICAgIHRoaXMucnNPcEV4cG9ydEN1c3RvbXNDbGVhcmFuY2VTZXJ2aWNlSW5zdGFuY2UgPSB7DQogICAgICAgICAgc2VydmljZUlkOiBudWxsLA0KICAgICAgICAgIHNlcnZpY2VUeXBlSWQ6IG51bGwsDQogICAgICAgICAgcmN0SWQ6IG51bGwsDQogICAgICAgICAgcmN0Tm86IG51bGwsDQogICAgICAgICAgc3VwcGxpZXJJZDogbnVsbCwNCiAgICAgICAgICBzdXBwbGllclN1bW1hcnk6IG51bGwsDQogICAgICAgICAgc3VwcGxpZXJDb250YWN0OiBudWxsLA0KICAgICAgICAgIHN1cHBsaWVyVGVsOiBudWxsLA0KICAgICAgICAgIHBheW1lbnRUaXRsZUNvZGU6IG51bGwsDQogICAgICAgICAgbG9naXN0aWNzUGF5bWVudFRlcm1zQ29kZTogbnVsbCwNCiAgICAgICAgICBpbnF1aXJ5Tm86IG51bGwsDQogICAgICAgICAgYWdyZWVtZW50VHlwZUNvZGU6IG51bGwsDQogICAgICAgICAgYWdyZWVtZW50Tm86IG51bGwsDQogICAgICAgICAgbWF4V2VpZ2h0OiBudWxsLA0KICAgICAgICAgIGlucXVpcnlOb3RpY2U6IG51bGwsDQogICAgICAgICAgaW5xdWlyeUlubmVyUmVtYXJrOiBudWxsLA0KICAgICAgICAgIGlucXVpcnlQc2FJZDogbnVsbCwNCiAgICAgICAgICBpbnF1aXJ5TGVhdGVzdFVwZGF0ZWRUaW1lOiBudWxsLA0KICAgICAgICAgIHNlcnZpY2VCZWxvbmdUbzogbnVsbCwNCiAgICAgICAgICBpc0RuU2FsZXNDb25maXJtZWQ6IG51bGwsDQogICAgICAgICAgaXNEbkNsaWVudENvbmZpcm1lZDogbnVsbCwNCiAgICAgICAgICBpc0RuT3BDb25maXJtZWQ6IG51bGwsDQogICAgICAgICAgaXNEblBzYUNvbmZpcm1lZDogbnVsbCwNCiAgICAgICAgICBpc0RuU3VwcGxpZXJDb25maXJtZWQ6IG51bGwsDQogICAgICAgICAgaXNBY2NvdW50Q29uZmlybWVkOiBudWxsLA0KICAgICAgICAgIGNvbmZpcm1BY2NvdW50SWQ6IG51bGwsDQogICAgICAgICAgYWNjb3VudENvbmZpcm1UaW1lOiBudWxsLA0KICAgICAgICAgIHNhbGVzQ29uZmlybWVkVGltZTogbnVsbCwNCiAgICAgICAgICBjbGllbnRDb25maXJtZWRUaW1lOiBudWxsLA0KICAgICAgICAgIG9wQ29uZmlybWVkVGltZTogbnVsbCwNCiAgICAgICAgICBwc2FDb25maXJtZWRUaW1lOiBudWxsLA0KICAgICAgICAgIHN1cHBsaWVyQ29uZmlybWVkVGltZTogbnVsbA0KICAgICAgICB9LA0KICAgICAgICB0aGlzLnJzT3BJbXBvcnRDdXN0b21zQ2xlYXJhbmNlU2VydmljZUluc3RhbmNlID0gew0KICAgICAgICAgIHNlcnZpY2VJZDogbnVsbCwNCiAgICAgICAgICBzZXJ2aWNlVHlwZUlkOiBudWxsLA0KICAgICAgICAgIHJjdElkOiBudWxsLA0KICAgICAgICAgIHJjdE5vOiBudWxsLA0KICAgICAgICAgIHN1cHBsaWVySWQ6IG51bGwsDQogICAgICAgICAgc3VwcGxpZXJTdW1tYXJ5OiBudWxsLA0KICAgICAgICAgIHN1cHBsaWVyQ29udGFjdDogbnVsbCwNCiAgICAgICAgICBzdXBwbGllclRlbDogbnVsbCwNCiAgICAgICAgICBwYXltZW50VGl0bGVDb2RlOiBudWxsLA0KICAgICAgICAgIGxvZ2lzdGljc1BheW1lbnRUZXJtc0NvZGU6IG51bGwsDQogICAgICAgICAgaW5xdWlyeU5vOiBudWxsLA0KICAgICAgICAgIGFncmVlbWVudFR5cGVDb2RlOiBudWxsLA0KICAgICAgICAgIGFncmVlbWVudE5vOiBudWxsLA0KICAgICAgICAgIG1heFdlaWdodDogbnVsbCwNCiAgICAgICAgICBpbnF1aXJ5Tm90aWNlOiBudWxsLA0KICAgICAgICAgIGlucXVpcnlJbm5lclJlbWFyazogbnVsbCwNCiAgICAgICAgICBpbnF1aXJ5UHNhSWQ6IG51bGwsDQogICAgICAgICAgaW5xdWlyeUxlYXRlc3RVcGRhdGVkVGltZTogbnVsbCwNCiAgICAgICAgICBzZXJ2aWNlQmVsb25nVG86IG51bGwsDQogICAgICAgICAgaXNEblNhbGVzQ29uZmlybWVkOiBudWxsLA0KICAgICAgICAgIGlzRG5DbGllbnRDb25maXJtZWQ6IG51bGwsDQogICAgICAgICAgaXNEbk9wQ29uZmlybWVkOiBudWxsLA0KICAgICAgICAgIGlzRG5Qc2FDb25maXJtZWQ6IG51bGwsDQogICAgICAgICAgaXNEblN1cHBsaWVyQ29uZmlybWVkOiBudWxsLA0KICAgICAgICAgIGlzQWNjb3VudENvbmZpcm1lZDogbnVsbCwNCiAgICAgICAgICBjb25maXJtQWNjb3VudElkOiBudWxsLA0KICAgICAgICAgIGFjY291bnRDb25maXJtVGltZTogbnVsbCwNCiAgICAgICAgICBzYWxlc0NvbmZpcm1lZFRpbWU6IG51bGwsDQogICAgICAgICAgY2xpZW50Q29uZmlybWVkVGltZTogbnVsbCwNCiAgICAgICAgICBvcENvbmZpcm1lZFRpbWU6IG51bGwsDQogICAgICAgICAgcHNhQ29uZmlybWVkVGltZTogbnVsbCwNCiAgICAgICAgICBzdXBwbGllckNvbmZpcm1lZFRpbWU6IG51bGwNCiAgICAgICAgfSwNCiAgICAgICAgdGhpcy5yc09wRG9jRGVjbGFyZVNlcnZpY2VJbnN0YW5jZSA9IHsNCiAgICAgICAgICBzZXJ2aWNlSWQ6IG51bGwsDQogICAgICAgICAgc2VydmljZVR5cGVJZDogbnVsbCwNCiAgICAgICAgICByY3RJZDogbnVsbCwNCiAgICAgICAgICByY3RObzogbnVsbCwNCiAgICAgICAgICBzdXBwbGllcklkOiBudWxsLA0KICAgICAgICAgIHN1cHBsaWVyU3VtbWFyeTogbnVsbCwNCiAgICAgICAgICBzdXBwbGllckNvbnRhY3Q6IG51bGwsDQogICAgICAgICAgc3VwcGxpZXJUZWw6IG51bGwsDQogICAgICAgICAgcGF5bWVudFRpdGxlQ29kZTogbnVsbCwNCiAgICAgICAgICBsb2dpc3RpY3NQYXltZW50VGVybXNDb2RlOiBudWxsLA0KICAgICAgICAgIGlucXVpcnlObzogbnVsbCwNCiAgICAgICAgICBhZ3JlZW1lbnRUeXBlQ29kZTogbnVsbCwNCiAgICAgICAgICBhZ3JlZW1lbnRObzogbnVsbCwNCiAgICAgICAgICBtYXhXZWlnaHQ6IG51bGwsDQogICAgICAgICAgaW5xdWlyeU5vdGljZTogbnVsbCwNCiAgICAgICAgICBpbnF1aXJ5SW5uZXJSZW1hcms6IG51bGwsDQogICAgICAgICAgaW5xdWlyeVBzYUlkOiBudWxsLA0KICAgICAgICAgIGlucXVpcnlMZWF0ZXN0VXBkYXRlZFRpbWU6IG51bGwsDQogICAgICAgICAgc2VydmljZUJlbG9uZ1RvOiBudWxsLA0KICAgICAgICAgIGlzRG5TYWxlc0NvbmZpcm1lZDogbnVsbCwNCiAgICAgICAgICBpc0RuQ2xpZW50Q29uZmlybWVkOiBudWxsLA0KICAgICAgICAgIGlzRG5PcENvbmZpcm1lZDogbnVsbCwNCiAgICAgICAgICBpc0RuUHNhQ29uZmlybWVkOiBudWxsLA0KICAgICAgICAgIGlzRG5TdXBwbGllckNvbmZpcm1lZDogbnVsbCwNCiAgICAgICAgICBpc0FjY291bnRDb25maXJtZWQ6IG51bGwsDQogICAgICAgICAgY29uZmlybUFjY291bnRJZDogbnVsbCwNCiAgICAgICAgICBhY2NvdW50Q29uZmlybVRpbWU6IG51bGwsDQogICAgICAgICAgc2FsZXNDb25maXJtZWRUaW1lOiBudWxsLA0KICAgICAgICAgIGNsaWVudENvbmZpcm1lZFRpbWU6IG51bGwsDQogICAgICAgICAgb3BDb25maXJtZWRUaW1lOiBudWxsLA0KICAgICAgICAgIHBzYUNvbmZpcm1lZFRpbWU6IG51bGwsDQogICAgICAgICAgc3VwcGxpZXJDb25maXJtZWRUaW1lOiBudWxsDQogICAgICAgIH0sDQogICAgICAgIHRoaXMucnNPcEZyZWVEZWNsYXJlU2VydmljZUluc3RhbmNlID0gew0KICAgICAgICAgIHNlcnZpY2VJZDogbnVsbCwNCiAgICAgICAgICBzZXJ2aWNlVHlwZUlkOiBudWxsLA0KICAgICAgICAgIHJjdElkOiBudWxsLA0KICAgICAgICAgIHJjdE5vOiBudWxsLA0KICAgICAgICAgIHN1cHBsaWVySWQ6IG51bGwsDQogICAgICAgICAgc3VwcGxpZXJTdW1tYXJ5OiBudWxsLA0KICAgICAgICAgIHN1cHBsaWVyQ29udGFjdDogbnVsbCwNCiAgICAgICAgICBzdXBwbGllclRlbDogbnVsbCwNCiAgICAgICAgICBwYXltZW50VGl0bGVDb2RlOiBudWxsLA0KICAgICAgICAgIGxvZ2lzdGljc1BheW1lbnRUZXJtc0NvZGU6IG51bGwsDQogICAgICAgICAgaW5xdWlyeU5vOiBudWxsLA0KICAgICAgICAgIGFncmVlbWVudFR5cGVDb2RlOiBudWxsLA0KICAgICAgICAgIGFncmVlbWVudE5vOiBudWxsLA0KICAgICAgICAgIG1heFdlaWdodDogbnVsbCwNCiAgICAgICAgICBpbnF1aXJ5Tm90aWNlOiBudWxsLA0KICAgICAgICAgIGlucXVpcnlJbm5lclJlbWFyazogbnVsbCwNCiAgICAgICAgICBpbnF1aXJ5UHNhSWQ6IG51bGwsDQogICAgICAgICAgaW5xdWlyeUxlYXRlc3RVcGRhdGVkVGltZTogbnVsbCwNCiAgICAgICAgICBzZXJ2aWNlQmVsb25nVG86IG51bGwsDQogICAgICAgICAgaXNEblNhbGVzQ29uZmlybWVkOiBudWxsLA0KICAgICAgICAgIGlzRG5DbGllbnRDb25maXJtZWQ6IG51bGwsDQogICAgICAgICAgaXNEbk9wQ29uZmlybWVkOiBudWxsLA0KICAgICAgICAgIGlzRG5Qc2FDb25maXJtZWQ6IG51bGwsDQogICAgICAgICAgaXNEblN1cHBsaWVyQ29uZmlybWVkOiBudWxsLA0KICAgICAgICAgIGlzQWNjb3VudENvbmZpcm1lZDogbnVsbCwNCiAgICAgICAgICBjb25maXJtQWNjb3VudElkOiBudWxsLA0KICAgICAgICAgIGFjY291bnRDb25maXJtVGltZTogbnVsbCwNCiAgICAgICAgICBzYWxlc0NvbmZpcm1lZFRpbWU6IG51bGwsDQogICAgICAgICAgY2xpZW50Q29uZmlybWVkVGltZTogbnVsbCwNCiAgICAgICAgICBvcENvbmZpcm1lZFRpbWU6IG51bGwsDQogICAgICAgICAgcHNhQ29uZmlybWVkVGltZTogbnVsbCwNCiAgICAgICAgICBzdXBwbGllckNvbmZpcm1lZFRpbWU6IG51bGwNCiAgICAgICAgfSwNCiAgICAgICAgdGhpcy5yc09wRE9BZ2VudFNlcnZpY2VJbnN0YW5jZSA9IHsNCiAgICAgICAgICBzZXJ2aWNlSWQ6IG51bGwsDQogICAgICAgICAgc2VydmljZVR5cGVJZDogbnVsbCwNCiAgICAgICAgICByY3RJZDogbnVsbCwNCiAgICAgICAgICByY3RObzogbnVsbCwNCiAgICAgICAgICBzdXBwbGllcklkOiBudWxsLA0KICAgICAgICAgIHN1cHBsaWVyU3VtbWFyeTogbnVsbCwNCiAgICAgICAgICBzdXBwbGllckNvbnRhY3Q6IG51bGwsDQogICAgICAgICAgc3VwcGxpZXJUZWw6IG51bGwsDQogICAgICAgICAgcGF5bWVudFRpdGxlQ29kZTogbnVsbCwNCiAgICAgICAgICBsb2dpc3RpY3NQYXltZW50VGVybXNDb2RlOiBudWxsLA0KICAgICAgICAgIGlucXVpcnlObzogbnVsbCwNCiAgICAgICAgICBhZ3JlZW1lbnRUeXBlQ29kZTogbnVsbCwNCiAgICAgICAgICBhZ3JlZW1lbnRObzogbnVsbCwNCiAgICAgICAgICBtYXhXZWlnaHQ6IG51bGwsDQogICAgICAgICAgaW5xdWlyeU5vdGljZTogbnVsbCwNCiAgICAgICAgICBpbnF1aXJ5SW5uZXJSZW1hcms6IG51bGwsDQogICAgICAgICAgaW5xdWlyeVBzYUlkOiBudWxsLA0KICAgICAgICAgIGlucXVpcnlMZWF0ZXN0VXBkYXRlZFRpbWU6IG51bGwsDQogICAgICAgICAgc2VydmljZUJlbG9uZ1RvOiBudWxsLA0KICAgICAgICAgIGlzRG5TYWxlc0NvbmZpcm1lZDogbnVsbCwNCiAgICAgICAgICBpc0RuQ2xpZW50Q29uZmlybWVkOiBudWxsLA0KICAgICAgICAgIGlzRG5PcENvbmZpcm1lZDogbnVsbCwNCiAgICAgICAgICBpc0RuUHNhQ29uZmlybWVkOiBudWxsLA0KICAgICAgICAgIGlzRG5TdXBwbGllckNvbmZpcm1lZDogbnVsbCwNCiAgICAgICAgICBpc0FjY291bnRDb25maXJtZWQ6IG51bGwsDQogICAgICAgICAgY29uZmlybUFjY291bnRJZDogbnVsbCwNCiAgICAgICAgICBhY2NvdW50Q29uZmlybVRpbWU6IG51bGwsDQogICAgICAgICAgc2FsZXNDb25maXJtZWRUaW1lOiBudWxsLA0KICAgICAgICAgIGNsaWVudENvbmZpcm1lZFRpbWU6IG51bGwsDQogICAgICAgICAgb3BDb25maXJtZWRUaW1lOiBudWxsLA0KICAgICAgICAgIHBzYUNvbmZpcm1lZFRpbWU6IG51bGwsDQogICAgICAgICAgc3VwcGxpZXJDb25maXJtZWRUaW1lOiBudWxsDQogICAgICAgIH0sDQogICAgICAgIHRoaXMucnNPcENsZWFyQWdlbnRTZXJ2aWNlSW5zdGFuY2UgPSB7DQogICAgICAgICAgc2VydmljZUlkOiBudWxsLA0KICAgICAgICAgIHNlcnZpY2VUeXBlSWQ6IG51bGwsDQogICAgICAgICAgcmN0SWQ6IG51bGwsDQogICAgICAgICAgcmN0Tm86IG51bGwsDQogICAgICAgICAgc3VwcGxpZXJJZDogbnVsbCwNCiAgICAgICAgICBzdXBwbGllclN1bW1hcnk6IG51bGwsDQogICAgICAgICAgc3VwcGxpZXJDb250YWN0OiBudWxsLA0KICAgICAgICAgIHN1cHBsaWVyVGVsOiBudWxsLA0KICAgICAgICAgIHBheW1lbnRUaXRsZUNvZGU6IG51bGwsDQogICAgICAgICAgbG9naXN0aWNzUGF5bWVudFRlcm1zQ29kZTogbnVsbCwNCiAgICAgICAgICBpbnF1aXJ5Tm86IG51bGwsDQogICAgICAgICAgYWdyZWVtZW50VHlwZUNvZGU6IG51bGwsDQogICAgICAgICAgYWdyZWVtZW50Tm86IG51bGwsDQogICAgICAgICAgbWF4V2VpZ2h0OiBudWxsLA0KICAgICAgICAgIGlucXVpcnlOb3RpY2U6IG51bGwsDQogICAgICAgICAgaW5xdWlyeUlubmVyUmVtYXJrOiBudWxsLA0KICAgICAgICAgIGlucXVpcnlQc2FJZDogbnVsbCwNCiAgICAgICAgICBpbnF1aXJ5TGVhdGVzdFVwZGF0ZWRUaW1lOiBudWxsLA0KICAgICAgICAgIHNlcnZpY2VCZWxvbmdUbzogbnVsbCwNCiAgICAgICAgICBpc0RuU2FsZXNDb25maXJtZWQ6IG51bGwsDQogICAgICAgICAgaXNEbkNsaWVudENvbmZpcm1lZDogbnVsbCwNCiAgICAgICAgICBpc0RuT3BDb25maXJtZWQ6IG51bGwsDQogICAgICAgICAgaXNEblBzYUNvbmZpcm1lZDogbnVsbCwNCiAgICAgICAgICBpc0RuU3VwcGxpZXJDb25maXJtZWQ6IG51bGwsDQogICAgICAgICAgaXNBY2NvdW50Q29uZmlybWVkOiBudWxsLA0KICAgICAgICAgIGNvbmZpcm1BY2NvdW50SWQ6IG51bGwsDQogICAgICAgICAgYWNjb3VudENvbmZpcm1UaW1lOiBudWxsLA0KICAgICAgICAgIHNhbGVzQ29uZmlybWVkVGltZTogbnVsbCwNCiAgICAgICAgICBjbGllbnRDb25maXJtZWRUaW1lOiBudWxsLA0KICAgICAgICAgIG9wQ29uZmlybWVkVGltZTogbnVsbCwNCiAgICAgICAgICBwc2FDb25maXJtZWRUaW1lOiBudWxsLA0KICAgICAgICAgIHN1cHBsaWVyQ29uZmlybWVkVGltZTogbnVsbA0KICAgICAgICB9LA0KICAgICAgICB0aGlzLnJzT3BJbXBvcnREaXNwYXRjaFRydWNrU2VydmljZUluc3RhbmNlID0gew0KICAgICAgICAgIHNlcnZpY2VJZDogbnVsbCwNCiAgICAgICAgICBzZXJ2aWNlVHlwZUlkOiBudWxsLA0KICAgICAgICAgIHJjdElkOiBudWxsLA0KICAgICAgICAgIHJjdE5vOiBudWxsLA0KICAgICAgICAgIHN1cHBsaWVySWQ6IG51bGwsDQogICAgICAgICAgc3VwcGxpZXJTdW1tYXJ5OiBudWxsLA0KICAgICAgICAgIHN1cHBsaWVyQ29udGFjdDogbnVsbCwNCiAgICAgICAgICBzdXBwbGllclRlbDogbnVsbCwNCiAgICAgICAgICBwYXltZW50VGl0bGVDb2RlOiBudWxsLA0KICAgICAgICAgIGxvZ2lzdGljc1BheW1lbnRUZXJtc0NvZGU6IG51bGwsDQogICAgICAgICAgaW5xdWlyeU5vOiBudWxsLA0KICAgICAgICAgIGFncmVlbWVudFR5cGVDb2RlOiBudWxsLA0KICAgICAgICAgIGFncmVlbWVudE5vOiBudWxsLA0KICAgICAgICAgIG1heFdlaWdodDogbnVsbCwNCiAgICAgICAgICBpbnF1aXJ5Tm90aWNlOiBudWxsLA0KICAgICAgICAgIGlucXVpcnlJbm5lclJlbWFyazogbnVsbCwNCiAgICAgICAgICBpbnF1aXJ5UHNhSWQ6IG51bGwsDQogICAgICAgICAgaW5xdWlyeUxlYXRlc3RVcGRhdGVkVGltZTogbnVsbCwNCiAgICAgICAgICBzZXJ2aWNlQmVsb25nVG86IG51bGwsDQogICAgICAgICAgaXNEblNhbGVzQ29uZmlybWVkOiBudWxsLA0KICAgICAgICAgIGlzRG5DbGllbnRDb25maXJtZWQ6IG51bGwsDQogICAgICAgICAgaXNEbk9wQ29uZmlybWVkOiBudWxsLA0KICAgICAgICAgIGlzRG5Qc2FDb25maXJtZWQ6IG51bGwsDQogICAgICAgICAgaXNEblN1cHBsaWVyQ29uZmlybWVkOiBudWxsLA0KICAgICAgICAgIGlzQWNjb3VudENvbmZpcm1lZDogbnVsbCwNCiAgICAgICAgICBjb25maXJtQWNjb3VudElkOiBudWxsLA0KICAgICAgICAgIGFjY291bnRDb25maXJtVGltZTogbnVsbCwNCiAgICAgICAgICBzYWxlc0NvbmZpcm1lZFRpbWU6IG51bGwsDQogICAgICAgICAgY2xpZW50Q29uZmlybWVkVGltZTogbnVsbCwNCiAgICAgICAgICBvcENvbmZpcm1lZFRpbWU6IG51bGwsDQogICAgICAgICAgcHNhQ29uZmlybWVkVGltZTogbnVsbCwNCiAgICAgICAgICBzdXBwbGllckNvbmZpcm1lZFRpbWU6IG51bGwNCiAgICAgICAgfSwNCiAgICAgICAgdGhpcy5yc09wV2FyZWhvdXNlU2VydmljZUluc3RhbmNlID0gew0KICAgICAgICAgIHNlcnZpY2VJZDogbnVsbCwNCiAgICAgICAgICBzZXJ2aWNlVHlwZUlkOiBudWxsLA0KICAgICAgICAgIHJjdElkOiBudWxsLA0KICAgICAgICAgIHJjdE5vOiBudWxsLA0KICAgICAgICAgIHN1cHBsaWVySWQ6IG51bGwsDQogICAgICAgICAgc3VwcGxpZXJTdW1tYXJ5OiBudWxsLA0KICAgICAgICAgIHN1cHBsaWVyQ29udGFjdDogbnVsbCwNCiAgICAgICAgICBzdXBwbGllclRlbDogbnVsbCwNCiAgICAgICAgICBwYXltZW50VGl0bGVDb2RlOiBudWxsLA0KICAgICAgICAgIGxvZ2lzdGljc1BheW1lbnRUZXJtc0NvZGU6IG51bGwsDQogICAgICAgICAgaW5xdWlyeU5vOiBudWxsLA0KICAgICAgICAgIGFncmVlbWVudFR5cGVDb2RlOiBudWxsLA0KICAgICAgICAgIGFncmVlbWVudE5vOiBudWxsLA0KICAgICAgICAgIG1heFdlaWdodDogbnVsbCwNCiAgICAgICAgICBpbnF1aXJ5Tm90aWNlOiBudWxsLA0KICAgICAgICAgIGlucXVpcnlJbm5lclJlbWFyazogbnVsbCwNCiAgICAgICAgICBpbnF1aXJ5UHNhSWQ6IG51bGwsDQogICAgICAgICAgaW5xdWlyeUxlYXRlc3RVcGRhdGVkVGltZTogbnVsbCwNCiAgICAgICAgICBzZXJ2aWNlQmVsb25nVG86IG51bGwsDQogICAgICAgICAgaXNEblNhbGVzQ29uZmlybWVkOiBudWxsLA0KICAgICAgICAgIGlzRG5DbGllbnRDb25maXJtZWQ6IG51bGwsDQogICAgICAgICAgaXNEbk9wQ29uZmlybWVkOiBudWxsLA0KICAgICAgICAgIGlzRG5Qc2FDb25maXJtZWQ6IG51bGwsDQogICAgICAgICAgaXNEblN1cHBsaWVyQ29uZmlybWVkOiBudWxsLA0KICAgICAgICAgIGlzQWNjb3VudENvbmZpcm1lZDogbnVsbCwNCiAgICAgICAgICBjb25maXJtQWNjb3VudElkOiBudWxsLA0KICAgICAgICAgIGFjY291bnRDb25maXJtVGltZTogbnVsbCwNCiAgICAgICAgICBzYWxlc0NvbmZpcm1lZFRpbWU6IG51bGwsDQogICAgICAgICAgY2xpZW50Q29uZmlybWVkVGltZTogbnVsbCwNCiAgICAgICAgICBvcENvbmZpcm1lZFRpbWU6IG51bGwsDQogICAgICAgICAgcHNhQ29uZmlybWVkVGltZTogbnVsbCwNCiAgICAgICAgICBzdXBwbGllckNvbmZpcm1lZFRpbWU6IG51bGwNCiAgICAgICAgfSwNCiAgICAgICAgdGhpcy5yc09wSW5zcGVjdGlvbkFuZENlcnRpZmljYXRlU2VydmljZUluc3RhbmNlID0gew0KICAgICAgICAgIHNlcnZpY2VJZDogbnVsbCwNCiAgICAgICAgICBzZXJ2aWNlVHlwZUlkOiBudWxsLA0KICAgICAgICAgIHJjdElkOiBudWxsLA0KICAgICAgICAgIHJjdE5vOiBudWxsLA0KICAgICAgICAgIHN1cHBsaWVySWQ6IG51bGwsDQogICAgICAgICAgc3VwcGxpZXJTdW1tYXJ5OiBudWxsLA0KICAgICAgICAgIHN1cHBsaWVyQ29udGFjdDogbnVsbCwNCiAgICAgICAgICBzdXBwbGllclRlbDogbnVsbCwNCiAgICAgICAgICBwYXltZW50VGl0bGVDb2RlOiBudWxsLA0KICAgICAgICAgIGxvZ2lzdGljc1BheW1lbnRUZXJtc0NvZGU6IG51bGwsDQogICAgICAgICAgaW5xdWlyeU5vOiBudWxsLA0KICAgICAgICAgIGFncmVlbWVudFR5cGVDb2RlOiBudWxsLA0KICAgICAgICAgIGFncmVlbWVudE5vOiBudWxsLA0KICAgICAgICAgIG1heFdlaWdodDogbnVsbCwNCiAgICAgICAgICBpbnF1aXJ5Tm90aWNlOiBudWxsLA0KICAgICAgICAgIGlucXVpcnlJbm5lclJlbWFyazogbnVsbCwNCiAgICAgICAgICBpbnF1aXJ5UHNhSWQ6IG51bGwsDQogICAgICAgICAgaW5xdWlyeUxlYXRlc3RVcGRhdGVkVGltZTogbnVsbCwNCiAgICAgICAgICBzZXJ2aWNlQmVsb25nVG86IG51bGwsDQogICAgICAgICAgaXNEblNhbGVzQ29uZmlybWVkOiBudWxsLA0KICAgICAgICAgIGlzRG5DbGllbnRDb25maXJtZWQ6IG51bGwsDQogICAgICAgICAgaXNEbk9wQ29uZmlybWVkOiBudWxsLA0KICAgICAgICAgIGlzRG5Qc2FDb25maXJtZWQ6IG51bGwsDQogICAgICAgICAgaXNEblN1cHBsaWVyQ29uZmlybWVkOiBudWxsLA0KICAgICAgICAgIGlzQWNjb3VudENvbmZpcm1lZDogbnVsbCwNCiAgICAgICAgICBjb25maXJtQWNjb3VudElkOiBudWxsLA0KICAgICAgICAgIGFjY291bnRDb25maXJtVGltZTogbnVsbCwNCiAgICAgICAgICBzYWxlc0NvbmZpcm1lZFRpbWU6IG51bGwsDQogICAgICAgICAgY2xpZW50Q29uZmlybWVkVGltZTogbnVsbCwNCiAgICAgICAgICBvcENvbmZpcm1lZFRpbWU6IG51bGwsDQogICAgICAgICAgcHNhQ29uZmlybWVkVGltZTogbnVsbCwNCiAgICAgICAgICBzdXBwbGllckNvbmZpcm1lZFRpbWU6IG51bGwNCiAgICAgICAgfSwNCiAgICAgICAgdGhpcy5yc09wTGFuZFNlcnZpY2VJbnN0YW5jZSA9IHsNCiAgICAgICAgICBzZXJ2aWNlSWQ6IG51bGwsDQogICAgICAgICAgc2VydmljZVR5cGVJZDogbnVsbCwNCiAgICAgICAgICByY3RJZDogbnVsbCwNCiAgICAgICAgICByY3RObzogbnVsbCwNCiAgICAgICAgICBzdXBwbGllcklkOiBudWxsLA0KICAgICAgICAgIHN1cHBsaWVyU3VtbWFyeTogbnVsbCwNCiAgICAgICAgICBzdXBwbGllckNvbnRhY3Q6IG51bGwsDQogICAgICAgICAgc3VwcGxpZXJUZWw6IG51bGwsDQogICAgICAgICAgcGF5bWVudFRpdGxlQ29kZTogbnVsbCwNCiAgICAgICAgICBsb2dpc3RpY3NQYXltZW50VGVybXNDb2RlOiBudWxsLA0KICAgICAgICAgIGlucXVpcnlObzogbnVsbCwNCiAgICAgICAgICBhZ3JlZW1lbnRUeXBlQ29kZTogbnVsbCwNCiAgICAgICAgICBhZ3JlZW1lbnRObzogbnVsbCwNCiAgICAgICAgICBtYXhXZWlnaHQ6IG51bGwsDQogICAgICAgICAgaW5xdWlyeU5vdGljZTogbnVsbCwNCiAgICAgICAgICBpbnF1aXJ5SW5uZXJSZW1hcms6IG51bGwsDQogICAgICAgICAgaW5xdWlyeVBzYUlkOiBudWxsLA0KICAgICAgICAgIGlucXVpcnlMZWF0ZXN0VXBkYXRlZFRpbWU6IG51bGwsDQogICAgICAgICAgc2VydmljZUJlbG9uZ1RvOiBudWxsLA0KICAgICAgICAgIGlzRG5TYWxlc0NvbmZpcm1lZDogbnVsbCwNCiAgICAgICAgICBpc0RuQ2xpZW50Q29uZmlybWVkOiBudWxsLA0KICAgICAgICAgIGlzRG5PcENvbmZpcm1lZDogbnVsbCwNCiAgICAgICAgICBpc0RuUHNhQ29uZmlybWVkOiBudWxsLA0KICAgICAgICAgIGlzRG5TdXBwbGllckNvbmZpcm1lZDogbnVsbCwNCiAgICAgICAgICBpc0FjY291bnRDb25maXJtZWQ6IG51bGwsDQogICAgICAgICAgY29uZmlybUFjY291bnRJZDogbnVsbCwNCiAgICAgICAgICBhY2NvdW50Q29uZmlybVRpbWU6IG51bGwsDQogICAgICAgICAgc2FsZXNDb25maXJtZWRUaW1lOiBudWxsLA0KICAgICAgICAgIGNsaWVudENvbmZpcm1lZFRpbWU6IG51bGwsDQogICAgICAgICAgb3BDb25maXJtZWRUaW1lOiBudWxsLA0KICAgICAgICAgIHBzYUNvbmZpcm1lZFRpbWU6IG51bGwsDQogICAgICAgICAgc3VwcGxpZXJDb25maXJtZWRUaW1lOiBudWxsDQogICAgICAgIH0sDQogICAgICAgIHRoaXMucnNPcEluc3VyYW5jZVNlcnZpY2VJbnN0YW5jZSA9IHsNCiAgICAgICAgICBzZXJ2aWNlSWQ6IG51bGwsDQogICAgICAgICAgc2VydmljZVR5cGVJZDogbnVsbCwNCiAgICAgICAgICByY3RJZDogbnVsbCwNCiAgICAgICAgICByY3RObzogbnVsbCwNCiAgICAgICAgICBzdXBwbGllcklkOiBudWxsLA0KICAgICAgICAgIHN1cHBsaWVyU3VtbWFyeTogbnVsbCwNCiAgICAgICAgICBzdXBwbGllckNvbnRhY3Q6IG51bGwsDQogICAgICAgICAgc3VwcGxpZXJUZWw6IG51bGwsDQogICAgICAgICAgcGF5bWVudFRpdGxlQ29kZTogbnVsbCwNCiAgICAgICAgICBsb2dpc3RpY3NQYXltZW50VGVybXNDb2RlOiBudWxsLA0KICAgICAgICAgIGlucXVpcnlObzogbnVsbCwNCiAgICAgICAgICBhZ3JlZW1lbnRUeXBlQ29kZTogbnVsbCwNCiAgICAgICAgICBhZ3JlZW1lbnRObzogbnVsbCwNCiAgICAgICAgICBtYXhXZWlnaHQ6IG51bGwsDQogICAgICAgICAgaW5xdWlyeU5vdGljZTogbnVsbCwNCiAgICAgICAgICBpbnF1aXJ5SW5uZXJSZW1hcms6IG51bGwsDQogICAgICAgICAgaW5xdWlyeVBzYUlkOiBudWxsLA0KICAgICAgICAgIGlucXVpcnlMZWF0ZXN0VXBkYXRlZFRpbWU6IG51bGwsDQogICAgICAgICAgc2VydmljZUJlbG9uZ1RvOiBudWxsLA0KICAgICAgICAgIGlzRG5TYWxlc0NvbmZpcm1lZDogbnVsbCwNCiAgICAgICAgICBpc0RuQ2xpZW50Q29uZmlybWVkOiBudWxsLA0KICAgICAgICAgIGlzRG5PcENvbmZpcm1lZDogbnVsbCwNCiAgICAgICAgICBpc0RuUHNhQ29uZmlybWVkOiBudWxsLA0KICAgICAgICAgIGlzRG5TdXBwbGllckNvbmZpcm1lZDogbnVsbCwNCiAgICAgICAgICBpc0FjY291bnRDb25maXJtZWQ6IG51bGwsDQogICAgICAgICAgY29uZmlybUFjY291bnRJZDogbnVsbCwNCiAgICAgICAgICBhY2NvdW50Q29uZmlybVRpbWU6IG51bGwsDQogICAgICAgICAgc2FsZXNDb25maXJtZWRUaW1lOiBudWxsLA0KICAgICAgICAgIGNsaWVudENvbmZpcm1lZFRpbWU6IG51bGwsDQogICAgICAgICAgb3BDb25maXJtZWRUaW1lOiBudWxsLA0KICAgICAgICAgIHBzYUNvbmZpcm1lZFRpbWU6IG51bGwsDQogICAgICAgICAgc3VwcGxpZXJDb25maXJtZWRUaW1lOiBudWxsDQogICAgICAgIH0sDQogICAgICAgIHRoaXMucnNPcEV4cGFuZFNlcnZpY2VTZXJ2aWNlSW5zdGFuY2UgPSB7DQogICAgICAgICAgc2VydmljZUlkOiBudWxsLA0KICAgICAgICAgIHNlcnZpY2VUeXBlSWQ6IG51bGwsDQogICAgICAgICAgcmN0SWQ6IG51bGwsDQogICAgICAgICAgcmN0Tm86IG51bGwsDQogICAgICAgICAgc3VwcGxpZXJJZDogbnVsbCwNCiAgICAgICAgICBzdXBwbGllclN1bW1hcnk6IG51bGwsDQogICAgICAgICAgc3VwcGxpZXJDb250YWN0OiBudWxsLA0KICAgICAgICAgIHN1cHBsaWVyVGVsOiBudWxsLA0KICAgICAgICAgIHBheW1lbnRUaXRsZUNvZGU6IG51bGwsDQogICAgICAgICAgbG9naXN0aWNzUGF5bWVudFRlcm1zQ29kZTogbnVsbCwNCiAgICAgICAgICBpbnF1aXJ5Tm86IG51bGwsDQogICAgICAgICAgYWdyZWVtZW50VHlwZUNvZGU6IG51bGwsDQogICAgICAgICAgYWdyZWVtZW50Tm86IG51bGwsDQogICAgICAgICAgbWF4V2VpZ2h0OiBudWxsLA0KICAgICAgICAgIGlucXVpcnlOb3RpY2U6IG51bGwsDQogICAgICAgICAgaW5xdWlyeUlubmVyUmVtYXJrOiBudWxsLA0KICAgICAgICAgIGlucXVpcnlQc2FJZDogbnVsbCwNCiAgICAgICAgICBpbnF1aXJ5TGVhdGVzdFVwZGF0ZWRUaW1lOiBudWxsLA0KICAgICAgICAgIHNlcnZpY2VCZWxvbmdUbzogbnVsbCwNCiAgICAgICAgICBpc0RuU2FsZXNDb25maXJtZWQ6IG51bGwsDQogICAgICAgICAgaXNEbkNsaWVudENvbmZpcm1lZDogbnVsbCwNCiAgICAgICAgICBpc0RuT3BDb25maXJtZWQ6IG51bGwsDQogICAgICAgICAgaXNEblBzYUNvbmZpcm1lZDogbnVsbCwNCiAgICAgICAgICBpc0RuU3VwcGxpZXJDb25maXJtZWQ6IG51bGwsDQogICAgICAgICAgaXNBY2NvdW50Q29uZmlybWVkOiBudWxsLA0KICAgICAgICAgIGNvbmZpcm1BY2NvdW50SWQ6IG51bGwsDQogICAgICAgICAgYWNjb3VudENvbmZpcm1UaW1lOiBudWxsLA0KICAgICAgICAgIHNhbGVzQ29uZmlybWVkVGltZTogbnVsbCwNCiAgICAgICAgICBjbGllbnRDb25maXJtZWRUaW1lOiBudWxsLA0KICAgICAgICAgIG9wQ29uZmlybWVkVGltZTogbnVsbCwNCiAgICAgICAgICBwc2FDb25maXJtZWRUaW1lOiBudWxsLA0KICAgICAgICAgIHN1cHBsaWVyQ29uZmlybWVkVGltZTogbnVsbA0KICAgICAgICB9LA0KICAgICAgICB0aGlzLnJzT3BXSFNTZXJ2aWNlSW5zdGFuY2UgPSB7DQogICAgICAgICAgc2VydmljZUlkOiBudWxsLA0KICAgICAgICAgIHNlcnZpY2VUeXBlSWQ6IG51bGwsDQogICAgICAgICAgcmN0SWQ6IG51bGwsDQogICAgICAgICAgcmN0Tm86IG51bGwsDQogICAgICAgICAgc3VwcGxpZXJJZDogbnVsbCwNCiAgICAgICAgICBzdXBwbGllclN1bW1hcnk6IG51bGwsDQogICAgICAgICAgc3VwcGxpZXJDb250YWN0OiBudWxsLA0KICAgICAgICAgIHN1cHBsaWVyVGVsOiBudWxsLA0KICAgICAgICAgIHBheW1lbnRUaXRsZUNvZGU6IG51bGwsDQogICAgICAgICAgbG9naXN0aWNzUGF5bWVudFRlcm1zQ29kZTogbnVsbCwNCiAgICAgICAgICBpbnF1aXJ5Tm86IG51bGwsDQogICAgICAgICAgYWdyZWVtZW50VHlwZUNvZGU6IG51bGwsDQogICAgICAgICAgYWdyZWVtZW50Tm86IG51bGwsDQogICAgICAgICAgbWF4V2VpZ2h0OiBudWxsLA0KICAgICAgICAgIGlucXVpcnlOb3RpY2U6IG51bGwsDQogICAgICAgICAgaW5xdWlyeUlubmVyUmVtYXJrOiBudWxsLA0KICAgICAgICAgIGlucXVpcnlQc2FJZDogbnVsbCwNCiAgICAgICAgICBpbnF1aXJ5TGVhdGVzdFVwZGF0ZWRUaW1lOiBudWxsLA0KICAgICAgICAgIHNlcnZpY2VCZWxvbmdUbzogbnVsbCwNCiAgICAgICAgICBpc0RuU2FsZXNDb25maXJtZWQ6IG51bGwsDQogICAgICAgICAgaXNEbkNsaWVudENvbmZpcm1lZDogbnVsbCwNCiAgICAgICAgICBpc0RuT3BDb25maXJtZWQ6IG51bGwsDQogICAgICAgICAgaXNEblBzYUNvbmZpcm1lZDogbnVsbCwNCiAgICAgICAgICBpc0RuU3VwcGxpZXJDb25maXJtZWQ6IG51bGwsDQogICAgICAgICAgaXNBY2NvdW50Q29uZmlybWVkOiBudWxsLA0KICAgICAgICAgIGNvbmZpcm1BY2NvdW50SWQ6IG51bGwsDQogICAgICAgICAgYWNjb3VudENvbmZpcm1UaW1lOiBudWxsLA0KICAgICAgICAgIHNhbGVzQ29uZmlybWVkVGltZTogbnVsbCwNCiAgICAgICAgICBjbGllbnRDb25maXJtZWRUaW1lOiBudWxsLA0KICAgICAgICAgIG9wQ29uZmlybWVkVGltZTogbnVsbCwNCiAgICAgICAgICBwc2FDb25maXJtZWRUaW1lOiBudWxsLA0KICAgICAgICAgIHN1cHBsaWVyQ29uZmlybWVkVGltZTogbnVsbA0KICAgICAgICB9LA0KICAgICAgICB0aGlzLnJzT3AzcmRDZXJ0U2VydmljZUluc3RhbmNlID0gew0KICAgICAgICAgIHNlcnZpY2VJZDogbnVsbCwNCiAgICAgICAgICBzZXJ2aWNlVHlwZUlkOiBudWxsLA0KICAgICAgICAgIHJjdElkOiBudWxsLA0KICAgICAgICAgIHJjdE5vOiBudWxsLA0KICAgICAgICAgIHN1cHBsaWVySWQ6IG51bGwsDQogICAgICAgICAgc3VwcGxpZXJTdW1tYXJ5OiBudWxsLA0KICAgICAgICAgIHN1cHBsaWVyQ29udGFjdDogbnVsbCwNCiAgICAgICAgICBzdXBwbGllclRlbDogbnVsbCwNCiAgICAgICAgICBwYXltZW50VGl0bGVDb2RlOiBudWxsLA0KICAgICAgICAgIGxvZ2lzdGljc1BheW1lbnRUZXJtc0NvZGU6IG51bGwsDQogICAgICAgICAgaW5xdWlyeU5vOiBudWxsLA0KICAgICAgICAgIGFncmVlbWVudFR5cGVDb2RlOiBudWxsLA0KICAgICAgICAgIGFncmVlbWVudE5vOiBudWxsLA0KICAgICAgICAgIG1heFdlaWdodDogbnVsbCwNCiAgICAgICAgICBpbnF1aXJ5Tm90aWNlOiBudWxsLA0KICAgICAgICAgIGlucXVpcnlJbm5lclJlbWFyazogbnVsbCwNCiAgICAgICAgICBpbnF1aXJ5UHNhSWQ6IG51bGwsDQogICAgICAgICAgaW5xdWlyeUxlYXRlc3RVcGRhdGVkVGltZTogbnVsbCwNCiAgICAgICAgICBzZXJ2aWNlQmVsb25nVG86IG51bGwsDQogICAgICAgICAgaXNEblNhbGVzQ29uZmlybWVkOiBudWxsLA0KICAgICAgICAgIGlzRG5DbGllbnRDb25maXJtZWQ6IG51bGwsDQogICAgICAgICAgaXNEbk9wQ29uZmlybWVkOiBudWxsLA0KICAgICAgICAgIGlzRG5Qc2FDb25maXJtZWQ6IG51bGwsDQogICAgICAgICAgaXNEblN1cHBsaWVyQ29uZmlybWVkOiBudWxsLA0KICAgICAgICAgIGlzQWNjb3VudENvbmZpcm1lZDogbnVsbCwNCiAgICAgICAgICBjb25maXJtQWNjb3VudElkOiBudWxsLA0KICAgICAgICAgIGFjY291bnRDb25maXJtVGltZTogbnVsbCwNCiAgICAgICAgICBzYWxlc0NvbmZpcm1lZFRpbWU6IG51bGwsDQogICAgICAgICAgY2xpZW50Q29uZmlybWVkVGltZTogbnVsbCwNCiAgICAgICAgICBvcENvbmZpcm1lZFRpbWU6IG51bGwsDQogICAgICAgICAgcHNhQ29uZmlybWVkVGltZTogbnVsbCwNCiAgICAgICAgICBzdXBwbGllckNvbmZpcm1lZFRpbWU6IG51bGwNCiAgICAgICAgfSwNCiAgICAgICAgdGhpcy5yc09wSU5TU2VydmljZUluc3RhbmNlID0gew0KICAgICAgICAgIHNlcnZpY2VJZDogbnVsbCwNCiAgICAgICAgICBzZXJ2aWNlVHlwZUlkOiBudWxsLA0KICAgICAgICAgIHJjdElkOiBudWxsLA0KICAgICAgICAgIHJjdE5vOiBudWxsLA0KICAgICAgICAgIHN1cHBsaWVySWQ6IG51bGwsDQogICAgICAgICAgc3VwcGxpZXJTdW1tYXJ5OiBudWxsLA0KICAgICAgICAgIHN1cHBsaWVyQ29udGFjdDogbnVsbCwNCiAgICAgICAgICBzdXBwbGllclRlbDogbnVsbCwNCiAgICAgICAgICBwYXltZW50VGl0bGVDb2RlOiBudWxsLA0KICAgICAgICAgIGxvZ2lzdGljc1BheW1lbnRUZXJtc0NvZGU6IG51bGwsDQogICAgICAgICAgaW5xdWlyeU5vOiBudWxsLA0KICAgICAgICAgIGFncmVlbWVudFR5cGVDb2RlOiBudWxsLA0KICAgICAgICAgIGFncmVlbWVudE5vOiBudWxsLA0KICAgICAgICAgIG1heFdlaWdodDogbnVsbCwNCiAgICAgICAgICBpbnF1aXJ5Tm90aWNlOiBudWxsLA0KICAgICAgICAgIGlucXVpcnlJbm5lclJlbWFyazogbnVsbCwNCiAgICAgICAgICBpbnF1aXJ5UHNhSWQ6IG51bGwsDQogICAgICAgICAgaW5xdWlyeUxlYXRlc3RVcGRhdGVkVGltZTogbnVsbCwNCiAgICAgICAgICBzZXJ2aWNlQmVsb25nVG86IG51bGwsDQogICAgICAgICAgaXNEblNhbGVzQ29uZmlybWVkOiBudWxsLA0KICAgICAgICAgIGlzRG5DbGllbnRDb25maXJtZWQ6IG51bGwsDQogICAgICAgICAgaXNEbk9wQ29uZmlybWVkOiBudWxsLA0KICAgICAgICAgIGlzRG5Qc2FDb25maXJtZWQ6IG51bGwsDQogICAgICAgICAgaXNEblN1cHBsaWVyQ29uZmlybWVkOiBudWxsLA0KICAgICAgICAgIGlzQWNjb3VudENvbmZpcm1lZDogbnVsbCwNCiAgICAgICAgICBjb25maXJtQWNjb3VudElkOiBudWxsLA0KICAgICAgICAgIGFjY291bnRDb25maXJtVGltZTogbnVsbCwNCiAgICAgICAgICBzYWxlc0NvbmZpcm1lZFRpbWU6IG51bGwsDQogICAgICAgICAgY2xpZW50Q29uZmlybWVkVGltZTogbnVsbCwNCiAgICAgICAgICBvcENvbmZpcm1lZFRpbWU6IG51bGwsDQogICAgICAgICAgcHNhQ29uZmlybWVkVGltZTogbnVsbCwNCiAgICAgICAgICBzdXBwbGllckNvbmZpcm1lZFRpbWU6IG51bGwNCiAgICAgICAgfSwNCiAgICAgICAgdGhpcy5yc09wVHJhZGluZ1NlcnZpY2VJbnN0YW5jZSA9IHsNCiAgICAgICAgICBzZXJ2aWNlSWQ6IG51bGwsDQogICAgICAgICAgc2VydmljZVR5cGVJZDogbnVsbCwNCiAgICAgICAgICByY3RJZDogbnVsbCwNCiAgICAgICAgICByY3RObzogbnVsbCwNCiAgICAgICAgICBzdXBwbGllcklkOiBudWxsLA0KICAgICAgICAgIHN1cHBsaWVyU3VtbWFyeTogbnVsbCwNCiAgICAgICAgICBzdXBwbGllckNvbnRhY3Q6IG51bGwsDQogICAgICAgICAgc3VwcGxpZXJUZWw6IG51bGwsDQogICAgICAgICAgcGF5bWVudFRpdGxlQ29kZTogbnVsbCwNCiAgICAgICAgICBsb2dpc3RpY3NQYXltZW50VGVybXNDb2RlOiBudWxsLA0KICAgICAgICAgIGlucXVpcnlObzogbnVsbCwNCiAgICAgICAgICBhZ3JlZW1lbnRUeXBlQ29kZTogbnVsbCwNCiAgICAgICAgICBhZ3JlZW1lbnRObzogbnVsbCwNCiAgICAgICAgICBtYXhXZWlnaHQ6IG51bGwsDQogICAgICAgICAgaW5xdWlyeU5vdGljZTogbnVsbCwNCiAgICAgICAgICBpbnF1aXJ5SW5uZXJSZW1hcms6IG51bGwsDQogICAgICAgICAgaW5xdWlyeVBzYUlkOiBudWxsLA0KICAgICAgICAgIGlucXVpcnlMZWF0ZXN0VXBkYXRlZFRpbWU6IG51bGwsDQogICAgICAgICAgc2VydmljZUJlbG9uZ1RvOiBudWxsLA0KICAgICAgICAgIGlzRG5TYWxlc0NvbmZpcm1lZDogbnVsbCwNCiAgICAgICAgICBpc0RuQ2xpZW50Q29uZmlybWVkOiBudWxsLA0KICAgICAgICAgIGlzRG5PcENvbmZpcm1lZDogbnVsbCwNCiAgICAgICAgICBpc0RuUHNhQ29uZmlybWVkOiBudWxsLA0KICAgICAgICAgIGlzRG5TdXBwbGllckNvbmZpcm1lZDogbnVsbCwNCiAgICAgICAgICBpc0FjY291bnRDb25maXJtZWQ6IG51bGwsDQogICAgICAgICAgY29uZmlybUFjY291bnRJZDogbnVsbCwNCiAgICAgICAgICBhY2NvdW50Q29uZmlybVRpbWU6IG51bGwsDQogICAgICAgICAgc2FsZXNDb25maXJtZWRUaW1lOiBudWxsLA0KICAgICAgICAgIGNsaWVudENvbmZpcm1lZFRpbWU6IG51bGwsDQogICAgICAgICAgb3BDb25maXJtZWRUaW1lOiBudWxsLA0KICAgICAgICAgIHBzYUNvbmZpcm1lZFRpbWU6IG51bGwsDQogICAgICAgICAgc3VwcGxpZXJDb25maXJtZWRUaW1lOiBudWxsDQogICAgICAgIH0sDQogICAgICAgIHRoaXMucnNPcEZ1bWlnYXRpb25TZXJ2aWNlSW5zdGFuY2UgPSB7DQogICAgICAgICAgc2VydmljZUlkOiBudWxsLA0KICAgICAgICAgIHNlcnZpY2VUeXBlSWQ6IG51bGwsDQogICAgICAgICAgcmN0SWQ6IG51bGwsDQogICAgICAgICAgcmN0Tm86IG51bGwsDQogICAgICAgICAgc3VwcGxpZXJJZDogbnVsbCwNCiAgICAgICAgICBzdXBwbGllclN1bW1hcnk6IG51bGwsDQogICAgICAgICAgc3VwcGxpZXJDb250YWN0OiBudWxsLA0KICAgICAgICAgIHN1cHBsaWVyVGVsOiBudWxsLA0KICAgICAgICAgIHBheW1lbnRUaXRsZUNvZGU6IG51bGwsDQogICAgICAgICAgbG9naXN0aWNzUGF5bWVudFRlcm1zQ29kZTogbnVsbCwNCiAgICAgICAgICBpbnF1aXJ5Tm86IG51bGwsDQogICAgICAgICAgYWdyZWVtZW50VHlwZUNvZGU6IG51bGwsDQogICAgICAgICAgYWdyZWVtZW50Tm86IG51bGwsDQogICAgICAgICAgbWF4V2VpZ2h0OiBudWxsLA0KICAgICAgICAgIGlucXVpcnlOb3RpY2U6IG51bGwsDQogICAgICAgICAgaW5xdWlyeUlubmVyUmVtYXJrOiBudWxsLA0KICAgICAgICAgIGlucXVpcnlQc2FJZDogbnVsbCwNCiAgICAgICAgICBpbnF1aXJ5TGVhdGVzdFVwZGF0ZWRUaW1lOiBudWxsLA0KICAgICAgICAgIHNlcnZpY2VCZWxvbmdUbzogbnVsbCwNCiAgICAgICAgICBpc0RuU2FsZXNDb25maXJtZWQ6IG51bGwsDQogICAgICAgICAgaXNEbkNsaWVudENvbmZpcm1lZDogbnVsbCwNCiAgICAgICAgICBpc0RuT3BDb25maXJtZWQ6IG51bGwsDQogICAgICAgICAgaXNEblBzYUNvbmZpcm1lZDogbnVsbCwNCiAgICAgICAgICBpc0RuU3VwcGxpZXJDb25maXJtZWQ6IG51bGwsDQogICAgICAgICAgaXNBY2NvdW50Q29uZmlybWVkOiBudWxsLA0KICAgICAgICAgIGNvbmZpcm1BY2NvdW50SWQ6IG51bGwsDQogICAgICAgICAgYWNjb3VudENvbmZpcm1UaW1lOiBudWxsLA0KICAgICAgICAgIHNhbGVzQ29uZmlybWVkVGltZTogbnVsbCwNCiAgICAgICAgICBjbGllbnRDb25maXJtZWRUaW1lOiBudWxsLA0KICAgICAgICAgIG9wQ29uZmlybWVkVGltZTogbnVsbCwNCiAgICAgICAgICBwc2FDb25maXJtZWRUaW1lOiBudWxsLA0KICAgICAgICAgIHN1cHBsaWVyQ29uZmlybWVkVGltZTogbnVsbA0KICAgICAgICB9LA0KICAgICAgICB0aGlzLnJzT3BDT1NlcnZpY2VJbnN0YW5jZSA9IHsNCiAgICAgICAgICBzZXJ2aWNlSWQ6IG51bGwsDQogICAgICAgICAgc2VydmljZVR5cGVJZDogbnVsbCwNCiAgICAgICAgICByY3RJZDogbnVsbCwNCiAgICAgICAgICByY3RObzogbnVsbCwNCiAgICAgICAgICBzdXBwbGllcklkOiBudWxsLA0KICAgICAgICAgIHN1cHBsaWVyU3VtbWFyeTogbnVsbCwNCiAgICAgICAgICBzdXBwbGllckNvbnRhY3Q6IG51bGwsDQogICAgICAgICAgc3VwcGxpZXJUZWw6IG51bGwsDQogICAgICAgICAgcGF5bWVudFRpdGxlQ29kZTogbnVsbCwNCiAgICAgICAgICBsb2dpc3RpY3NQYXltZW50VGVybXNDb2RlOiBudWxsLA0KICAgICAgICAgIGlucXVpcnlObzogbnVsbCwNCiAgICAgICAgICBhZ3JlZW1lbnRUeXBlQ29kZTogbnVsbCwNCiAgICAgICAgICBhZ3JlZW1lbnRObzogbnVsbCwNCiAgICAgICAgICBtYXhXZWlnaHQ6IG51bGwsDQogICAgICAgICAgaW5xdWlyeU5vdGljZTogbnVsbCwNCiAgICAgICAgICBpbnF1aXJ5SW5uZXJSZW1hcms6IG51bGwsDQogICAgICAgICAgaW5xdWlyeVBzYUlkOiBudWxsLA0KICAgICAgICAgIGlucXVpcnlMZWF0ZXN0VXBkYXRlZFRpbWU6IG51bGwsDQogICAgICAgICAgc2VydmljZUJlbG9uZ1RvOiBudWxsLA0KICAgICAgICAgIGlzRG5TYWxlc0NvbmZpcm1lZDogbnVsbCwNCiAgICAgICAgICBpc0RuQ2xpZW50Q29uZmlybWVkOiBudWxsLA0KICAgICAgICAgIGlzRG5PcENvbmZpcm1lZDogbnVsbCwNCiAgICAgICAgICBpc0RuUHNhQ29uZmlybWVkOiBudWxsLA0KICAgICAgICAgIGlzRG5TdXBwbGllckNvbmZpcm1lZDogbnVsbCwNCiAgICAgICAgICBpc0FjY291bnRDb25maXJtZWQ6IG51bGwsDQogICAgICAgICAgY29uZmlybUFjY291bnRJZDogbnVsbCwNCiAgICAgICAgICBhY2NvdW50Q29uZmlybVRpbWU6IG51bGwsDQogICAgICAgICAgc2FsZXNDb25maXJtZWRUaW1lOiBudWxsLA0KICAgICAgICAgIGNsaWVudENvbmZpcm1lZFRpbWU6IG51bGwsDQogICAgICAgICAgb3BDb25maXJtZWRUaW1lOiBudWxsLA0KICAgICAgICAgIHBzYUNvbmZpcm1lZFRpbWU6IG51bGwsDQogICAgICAgICAgc3VwcGxpZXJDb25maXJtZWRUaW1lOiBudWxsDQogICAgICAgIH0sDQogICAgICAgIHRoaXMucmVzZXRGb3JtKCJmb3JtIikNCiAgICB9LA0KICAgIGxvYWRTdGFmZkxpc3QoKSB7DQogICAgICBpZiAodGhpcy4kc3RvcmUuc3RhdGUuZGF0YS5hbGxSc1N0YWZmTGlzdC5sZW5ndGggPT0gMCB8fCB0aGlzLiRzdG9yZS5zdGF0ZS5kYXRhLnJlZGlzTGlzdC5hbGxSc1N0YWZmTGlzdCkgew0KICAgICAgICBzdG9yZS5kaXNwYXRjaCgiZ2V0QWxsUnNTdGFmZkxpc3QiKS50aGVuKCgpID0+IHsNCiAgICAgICAgICB0aGlzLnN0YWZmTGlzdCA9IHRoaXMuJHN0b3JlLnN0YXRlLmRhdGEuYWxsUnNTdGFmZkxpc3QNCiAgICAgICAgfSkNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHRoaXMuc3RhZmZMaXN0ID0gdGhpcy4kc3RvcmUuc3RhdGUuZGF0YS5hbGxSc1N0YWZmTGlzdA0KICAgICAgfQ0KICAgIH0sDQogICAgc2VsZWN0ZWRVcmdlbmN5RGVncmVlKHBheWxvYWQpIHsNCiAgICAgIHRoaXMuZm9ybS5lbWVyZ2VuY3lMZXZlbCA9IHBheWxvYWQNCiAgICB9LA0KICAgIGNvcHlGcmVpZ2h0KHJvdykgew0KICAgICAgcm93LnNob3dRdW90YXRpb25Vbml0ID0gZmFsc2UNCiAgICAgIGxldCBpdGVtID0gdGhpcy5fLmNsb25lRGVlcChyb3cpDQogICAgICBpdGVtLmNsZWFyaW5nQ29tcGFueUlkID0gcm93LnBheUNsZWFyaW5nQ29tcGFueUlkDQogICAgICBpdGVtLmNvbXBhbnlOYW1lID0gcm93LnBheUNvbXBhbnlOYW1lDQogICAgICAvLyDlpoLmnpzmmK/orqLoiLENCiAgICAgIGlmICh0aGlzLiRyb3V0ZS5xdWVyeS5pZCkgew0KICAgICAgICBpdGVtLmNsZWFyaW5nQ29tcGFueUlkID0gdGhpcy5mb3JtLmNsaWVudElkDQogICAgICAgIGl0ZW0uY29tcGFueU5hbWUgPSB0aGlzLmZvcm0uY29tcGFueQ0KICAgICAgfQ0KICAgICAgdGhpcy5yc0NsaWVudE1lc3NhZ2UucnNDaGFyZ2VMaXN0LnB1c2goaXRlbSkNCg0KICAgICAgdGhpcy5yc0NsaWVudE1lc3NhZ2VDaGFyZ2UodGhpcy5yc0NsaWVudE1lc3NhZ2UucnNDaGFyZ2VMaXN0KQ0KICAgIH0sDQogICAgcnNDbGllbnRNZXNzYWdlQ2hhcmdlKG4pIHsNCiAgICAgIC8vIOiuoeeul+WuouaIt+S/oeaBr+S4reeahOW6lOaUtg0KICAgICAgbGV0IHJlY2VpdmFibGVSTUIgPSAwDQogICAgICBsZXQgcmVjZWl2YWJsZVVTRCA9IDANCiAgICAgIGxldCByZWNlaXZhYmxlVGF4Uk1CID0gMA0KICAgICAgbGV0IHJlY2VpdmFibGVUYXhVU0QgPSAwDQoNCiAgICAgIC8vIOiuoeeul+WuouaIt+S/oeaBr+S4reeahOW6lOS7mA0KICAgICAgbGV0IHBheWFibGVSTUIgPSAwDQogICAgICBsZXQgcGF5YWJsZVVTRCA9IDANCiAgICAgIGxldCBwYXlhYmxlVGF4Uk1CID0gMA0KICAgICAgbGV0IHBheWFibGVUYXhVU0QgPSAwDQoNCiAgICAgIC8vIOiuoeeul+WuouaIt+S/oeaBr+S4reeahOacquaUti/mnKrku5gNCiAgICAgIGxldCB1bnJlY2VpdmVkUm1iU3VtID0gMA0KICAgICAgbGV0IHVucmVjZWl2ZWRVc2RTdW0gPSAwDQogICAgICBsZXQgdW5wYWlkUm1iU3VtID0gMA0KICAgICAgbGV0IHVucGFpZFVzZFN1bSA9IDANCiAgICAgIG4ubWFwKHJzQ2hhcmdlID0+IHsNCiAgICAgICAgaWYgKHJzQ2hhcmdlLnN1YnRvdGFsKSB7DQogICAgICAgICAgaWYgKHJzQ2hhcmdlLmRuQ3VycmVuY3lDb2RlID09PSAiVVNEIikgew0KICAgICAgICAgICAgcmVjZWl2YWJsZVVTRCA9IGN1cnJlbmN5KHJlY2VpdmFibGVVU0QpLmFkZChjdXJyZW5jeShyc0NoYXJnZS5zdWJ0b3RhbCkuZGl2aWRlKGN1cnJlbmN5KDEpLmFkZChjdXJyZW5jeShyc0NoYXJnZS5kdXR5UmF0ZSkuZGl2aWRlKDEwMCkpKSkudmFsdWUNCiAgICAgICAgICAgIHJlY2VpdmFibGVUYXhVU0QgPSBjdXJyZW5jeShyZWNlaXZhYmxlVGF4VVNEKS5hZGQocnNDaGFyZ2Uuc3VidG90YWwpLnZhbHVlDQogICAgICAgICAgICB1bnJlY2VpdmVkVXNkU3VtID0gY3VycmVuY3kodW5yZWNlaXZlZFVzZFN1bSkuYWRkKHJzQ2hhcmdlLnNxZERuQ3VycmVuY3lCYWxhbmNlKS52YWx1ZQ0KICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICByZWNlaXZhYmxlUk1CID0gY3VycmVuY3kocmVjZWl2YWJsZVJNQikuYWRkKGN1cnJlbmN5KHJzQ2hhcmdlLnN1YnRvdGFsKS5kaXZpZGUoY3VycmVuY3koMSkuYWRkKGN1cnJlbmN5KHJzQ2hhcmdlLmR1dHlSYXRlKS5kaXZpZGUoMTAwKSkpKS52YWx1ZQ0KICAgICAgICAgICAgcmVjZWl2YWJsZVRheFJNQiA9IGN1cnJlbmN5KHJlY2VpdmFibGVUYXhSTUIpLmFkZChyc0NoYXJnZS5zdWJ0b3RhbCkudmFsdWUNCiAgICAgICAgICAgIHVucmVjZWl2ZWRSbWJTdW0gPSBjdXJyZW5jeSh1bnJlY2VpdmVkUm1iU3VtKS5hZGQocnNDaGFyZ2Uuc3FkRG5DdXJyZW5jeUJhbGFuY2UpLnZhbHVlDQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICB9KQ0KICAgICAgdGhpcy5yc0NsaWVudE1lc3NhZ2VSZWNlaXZhYmxlUk1CID0gcmVjZWl2YWJsZVJNQg0KICAgICAgdGhpcy5yc0NsaWVudE1lc3NhZ2VSZWNlaXZhYmxlVVNEID0gcmVjZWl2YWJsZVVTRA0KICAgICAgdGhpcy5yc0NsaWVudE1lc3NhZ2VSZWNlaXZhYmxlVGF4Uk1CID0gcmVjZWl2YWJsZVRheFJNQg0KICAgICAgdGhpcy5yc0NsaWVudE1lc3NhZ2VSZWNlaXZhYmxlVGF4VVNEID0gcmVjZWl2YWJsZVRheFVTRA0KDQogICAgICB0aGlzLnNxZFVucmVjZWl2ZWRSbWJTdW0gPSB1bnJlY2VpdmVkUm1iU3VtDQogICAgICB0aGlzLnNxZFVucmVjZWl2ZWRVc2RTdW0gPSB1bnJlY2VpdmVkVXNkU3VtDQoNCiAgICAgIC8vIOaVtOafnOa1t+i/kOi0ueeUqA0KICAgICAgdGhpcy5mb3JtLnJzT3BTZWFGY2xMaXN0ID8gdGhpcy5mb3JtLnJzT3BTZWFGY2xMaXN0Lm1hcChpdGVtID0+IHsNCiAgICAgICAgaXRlbS5yc0NoYXJnZUxpc3QubWFwKHJzQ2hhcmdlID0+IHsNCiAgICAgICAgICBpZiAocnNDaGFyZ2Uuc3VidG90YWwpIHsNCiAgICAgICAgICAgIGlmIChyc0NoYXJnZS5kbkN1cnJlbmN5Q29kZSA9PT0gIlVTRCIpIHsNCiAgICAgICAgICAgICAgcGF5YWJsZVVTRCA9IGN1cnJlbmN5KHBheWFibGVVU0QpLmFkZChjdXJyZW5jeShyc0NoYXJnZS5zdWJ0b3RhbCkuZGl2aWRlKGN1cnJlbmN5KDEpLmFkZChjdXJyZW5jeShyc0NoYXJnZS5kdXR5UmF0ZSkuZGl2aWRlKDEwMCkpKSkudmFsdWUNCiAgICAgICAgICAgICAgcGF5YWJsZVRheFVTRCA9IGN1cnJlbmN5KHBheWFibGVUYXhVU0QpLmFkZChyc0NoYXJnZS5zdWJ0b3RhbCkudmFsdWUNCiAgICAgICAgICAgICAgdW5wYWlkVXNkU3VtID0gY3VycmVuY3kodW5wYWlkVXNkU3VtKS5hZGQocnNDaGFyZ2Uuc3FkRG5DdXJyZW5jeUJhbGFuY2UpLnZhbHVlDQogICAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgICBwYXlhYmxlUk1CID0gY3VycmVuY3kocGF5YWJsZVJNQikuYWRkKGN1cnJlbmN5KHJzQ2hhcmdlLnN1YnRvdGFsKS5kaXZpZGUoY3VycmVuY3koMSkuYWRkKGN1cnJlbmN5KHJzQ2hhcmdlLmR1dHlSYXRlKS5kaXZpZGUoMTAwKSkpKS52YWx1ZQ0KICAgICAgICAgICAgICBwYXlhYmxlVGF4Uk1CID0gY3VycmVuY3kocGF5YWJsZVRheFJNQikuYWRkKHJzQ2hhcmdlLnN1YnRvdGFsKS52YWx1ZQ0KICAgICAgICAgICAgICB1bnBhaWRSbWJTdW0gPSBjdXJyZW5jeSh1bnBhaWRSbWJTdW0pLmFkZChyc0NoYXJnZS5zcWREbkN1cnJlbmN5QmFsYW5jZSkudmFsdWUNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9DQogICAgICAgIH0pDQogICAgICB9KSA6IG51bGwNCiAgICAgIC8vIOaLvOafnOa1t+i/kOi0ueeUqA0KICAgICAgdGhpcy5mb3JtLnJzT3BTZWFMY2xMaXN0ID8gdGhpcy5mb3JtLnJzT3BTZWFMY2xMaXN0Lm1hcChpdGVtID0+IHsNCiAgICAgICAgaXRlbS5yc0NoYXJnZUxpc3QubWFwKHJzQ2hhcmdlID0+IHsNCiAgICAgICAgICBpZiAocnNDaGFyZ2Uuc3VidG90YWwpIHsNCiAgICAgICAgICAgIGlmIChyc0NoYXJnZS5kbkN1cnJlbmN5Q29kZSA9PT0gIlVTRCIpIHsNCiAgICAgICAgICAgICAgcGF5YWJsZVVTRCA9IGN1cnJlbmN5KHBheWFibGVVU0QpLmFkZChjdXJyZW5jeShyc0NoYXJnZS5zdWJ0b3RhbCkuZGl2aWRlKGN1cnJlbmN5KDEpLmFkZChjdXJyZW5jeShyc0NoYXJnZS5kdXR5UmF0ZSkuZGl2aWRlKDEwMCkpKSkudmFsdWUNCiAgICAgICAgICAgICAgcGF5YWJsZVRheFVTRCA9IGN1cnJlbmN5KHBheWFibGVUYXhVU0QpLmFkZChyc0NoYXJnZS5zdWJ0b3RhbCkudmFsdWUNCiAgICAgICAgICAgICAgdW5wYWlkVXNkU3VtID0gY3VycmVuY3kodW5wYWlkVXNkU3VtKS5hZGQocnNDaGFyZ2Uuc3FkRG5DdXJyZW5jeUJhbGFuY2UpLnZhbHVlDQogICAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgICBwYXlhYmxlUk1CID0gY3VycmVuY3kocGF5YWJsZVJNQikuYWRkKGN1cnJlbmN5KHJzQ2hhcmdlLnN1YnRvdGFsKS5kaXZpZGUoY3VycmVuY3koMSkuYWRkKGN1cnJlbmN5KHJzQ2hhcmdlLmR1dHlSYXRlKS5kaXZpZGUoMTAwKSkpKS52YWx1ZQ0KICAgICAgICAgICAgICBwYXlhYmxlVGF4Uk1CID0gY3VycmVuY3kocGF5YWJsZVRheFJNQikuYWRkKHJzQ2hhcmdlLnN1YnRvdGFsKS52YWx1ZQ0KICAgICAgICAgICAgICB1bnBhaWRSbWJTdW0gPSBjdXJyZW5jeSh1bnBhaWRSbWJTdW0pLmFkZChyc0NoYXJnZS5zcWREbkN1cnJlbmN5QmFsYW5jZSkudmFsdWUNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9DQogICAgICAgIH0pDQogICAgICB9KSA6IG51bGwNCiAgICAgIC8vIOepuui/kOi0ueeUqA0KICAgICAgdGhpcy5mb3JtLnJzT3BBaXJMaXN0ID8gdGhpcy5mb3JtLnJzT3BBaXJMaXN0Lm1hcChpdGVtID0+IHsNCiAgICAgICAgaXRlbS5yc0NoYXJnZUxpc3QubWFwKHJzQ2hhcmdlID0+IHsNCiAgICAgICAgICBpZiAocnNDaGFyZ2Uuc3VidG90YWwpIHsNCiAgICAgICAgICAgIGlmIChyc0NoYXJnZS5kbkN1cnJlbmN5Q29kZSA9PT0gIlVTRCIpIHsNCiAgICAgICAgICAgICAgcGF5YWJsZVVTRCA9IGN1cnJlbmN5KHBheWFibGVVU0QpLmFkZChjdXJyZW5jeShyc0NoYXJnZS5zdWJ0b3RhbCkuZGl2aWRlKGN1cnJlbmN5KDEpLmFkZChjdXJyZW5jeShyc0NoYXJnZS5kdXR5UmF0ZSkuZGl2aWRlKDEwMCkpKSkudmFsdWUNCiAgICAgICAgICAgICAgcGF5YWJsZVRheFVTRCA9IGN1cnJlbmN5KHBheWFibGVUYXhVU0QpLmFkZChyc0NoYXJnZS5zdWJ0b3RhbCkudmFsdWUNCiAgICAgICAgICAgICAgdW5wYWlkVXNkU3VtID0gY3VycmVuY3kodW5wYWlkVXNkU3VtKS5hZGQocnNDaGFyZ2Uuc3FkRG5DdXJyZW5jeUJhbGFuY2UpLnZhbHVlDQogICAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgICBwYXlhYmxlUk1CID0gY3VycmVuY3kocGF5YWJsZVJNQikuYWRkKGN1cnJlbmN5KHJzQ2hhcmdlLnN1YnRvdGFsKS5kaXZpZGUoY3VycmVuY3koMSkuYWRkKGN1cnJlbmN5KHJzQ2hhcmdlLmR1dHlSYXRlKS5kaXZpZGUoMTAwKSkpKS52YWx1ZQ0KICAgICAgICAgICAgICBwYXlhYmxlVGF4Uk1CID0gY3VycmVuY3kocGF5YWJsZVRheFJNQikuYWRkKHJzQ2hhcmdlLnN1YnRvdGFsKS52YWx1ZQ0KICAgICAgICAgICAgICB1bnBhaWRSbWJTdW0gPSBjdXJyZW5jeSh1bnBhaWRSbWJTdW0pLmFkZChyc0NoYXJnZS5zcWREbkN1cnJlbmN5QmFsYW5jZSkudmFsdWUNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9DQogICAgICAgIH0pDQogICAgICB9KSA6IG51bGwNCiAgICAgIC8vIOaVtOafnOaLlui9pui0ueeUqA0KICAgICAgdGhpcy5mb3JtLnJzT3BDdG5yVHJ1Y2tMaXN0ID8gdGhpcy5mb3JtLnJzT3BDdG5yVHJ1Y2tMaXN0Lm1hcChpdGVtID0+IHsNCiAgICAgICAgaXRlbS5yc0NoYXJnZUxpc3QubWFwKHJzQ2hhcmdlID0+IHsNCiAgICAgICAgICBpZiAocnNDaGFyZ2Uuc3VidG90YWwpIHsNCiAgICAgICAgICAgIGlmIChyc0NoYXJnZS5kbkN1cnJlbmN5Q29kZSA9PT0gIlVTRCIpIHsNCiAgICAgICAgICAgICAgcGF5YWJsZVVTRCA9IGN1cnJlbmN5KHBheWFibGVVU0QpLmFkZChjdXJyZW5jeShyc0NoYXJnZS5zdWJ0b3RhbCkuZGl2aWRlKGN1cnJlbmN5KDEpLmFkZChjdXJyZW5jeShyc0NoYXJnZS5kdXR5UmF0ZSkuZGl2aWRlKDEwMCkpKSkudmFsdWUNCiAgICAgICAgICAgICAgcGF5YWJsZVRheFVTRCA9IGN1cnJlbmN5KHBheWFibGVUYXhVU0QpLmFkZChyc0NoYXJnZS5zdWJ0b3RhbCkudmFsdWUNCiAgICAgICAgICAgICAgdW5wYWlkVXNkU3VtID0gY3VycmVuY3kodW5wYWlkVXNkU3VtKS5hZGQocnNDaGFyZ2Uuc3FkRG5DdXJyZW5jeUJhbGFuY2UpLnZhbHVlDQogICAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgICBwYXlhYmxlUk1CID0gY3VycmVuY3kocGF5YWJsZVJNQikuYWRkKGN1cnJlbmN5KHJzQ2hhcmdlLnN1YnRvdGFsKS5kaXZpZGUoY3VycmVuY3koMSkuYWRkKGN1cnJlbmN5KHJzQ2hhcmdlLmR1dHlSYXRlKS5kaXZpZGUoMTAwKSkpKS52YWx1ZQ0KICAgICAgICAgICAgICBwYXlhYmxlVGF4Uk1CID0gY3VycmVuY3kocGF5YWJsZVRheFJNQikuYWRkKHJzQ2hhcmdlLnN1YnRvdGFsKS52YWx1ZQ0KICAgICAgICAgICAgICB1bnBhaWRSbWJTdW0gPSBjdXJyZW5jeSh1bnBhaWRSbWJTdW0pLmFkZChyc0NoYXJnZS5zcWREbkN1cnJlbmN5QmFsYW5jZSkudmFsdWUNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9DQogICAgICAgIH0pDQogICAgICB9KSA6IG51bGwNCiAgICAgIC8vIOaLvOafnOaLlui9pui0ueeUqA0KICAgICAgdGhpcy5mb3JtLnJzT3BCdWxrVHJ1Y2tMaXN0ID8gdGhpcy5mb3JtLnJzT3BCdWxrVHJ1Y2tMaXN0Lm1hcChpdGVtID0+IHsNCiAgICAgICAgaXRlbS5yc0NoYXJnZUxpc3QubWFwKHJzQ2hhcmdlID0+IHsNCiAgICAgICAgICBpZiAocnNDaGFyZ2Uuc3VidG90YWwpIHsNCiAgICAgICAgICAgIGlmIChyc0NoYXJnZS5kbkN1cnJlbmN5Q29kZSA9PT0gIlVTRCIpIHsNCiAgICAgICAgICAgICAgcGF5YWJsZVVTRCA9IGN1cnJlbmN5KHBheWFibGVVU0QpLmFkZChjdXJyZW5jeShyc0NoYXJnZS5zdWJ0b3RhbCkuZGl2aWRlKGN1cnJlbmN5KDEpLmFkZChjdXJyZW5jeShyc0NoYXJnZS5kdXR5UmF0ZSkuZGl2aWRlKDEwMCkpKSkudmFsdWUNCiAgICAgICAgICAgICAgcGF5YWJsZVRheFVTRCA9IGN1cnJlbmN5KHBheWFibGVUYXhVU0QpLmFkZChyc0NoYXJnZS5zdWJ0b3RhbCkudmFsdWUNCiAgICAgICAgICAgICAgdW5wYWlkVXNkU3VtID0gY3VycmVuY3kodW5wYWlkVXNkU3VtKS5hZGQocnNDaGFyZ2Uuc3FkRG5DdXJyZW5jeUJhbGFuY2UpLnZhbHVlDQogICAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgICBwYXlhYmxlUk1CID0gY3VycmVuY3kocGF5YWJsZVJNQikuYWRkKGN1cnJlbmN5KHJzQ2hhcmdlLnN1YnRvdGFsKS5kaXZpZGUoY3VycmVuY3koMSkuYWRkKGN1cnJlbmN5KHJzQ2hhcmdlLmR1dHlSYXRlKS5kaXZpZGUoMTAwKSkpKS52YWx1ZQ0KICAgICAgICAgICAgICBwYXlhYmxlVGF4Uk1CID0gY3VycmVuY3kocGF5YWJsZVRheFJNQikuYWRkKHJzQ2hhcmdlLnN1YnRvdGFsKS52YWx1ZQ0KICAgICAgICAgICAgICB1bnBhaWRSbWJTdW0gPSBjdXJyZW5jeSh1bnBhaWRSbWJTdW0pLmFkZChyc0NoYXJnZS5zcWREbkN1cnJlbmN5QmFsYW5jZSkudmFsdWUNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9DQogICAgICAgIH0pDQogICAgICB9KSA6IG51bGwNCiAgICAgIC8vIOWNleivgeaKpeWFs+i0ueeUqA0KICAgICAgdGhpcy5mb3JtLnJzT3BEb2NEZWNsYXJlTGlzdCA/IHRoaXMuZm9ybS5yc09wRG9jRGVjbGFyZUxpc3QubWFwKGl0ZW0gPT4gew0KICAgICAgICBpdGVtLnJzQ2hhcmdlTGlzdC5tYXAocnNDaGFyZ2UgPT4gew0KICAgICAgICAgIGlmIChyc0NoYXJnZS5zdWJ0b3RhbCkgew0KICAgICAgICAgICAgaWYgKHJzQ2hhcmdlLmRuQ3VycmVuY3lDb2RlID09PSAiVVNEIikgew0KICAgICAgICAgICAgICBwYXlhYmxlVVNEID0gY3VycmVuY3kocGF5YWJsZVVTRCkuYWRkKGN1cnJlbmN5KHJzQ2hhcmdlLnN1YnRvdGFsKS5kaXZpZGUoY3VycmVuY3koMSkuYWRkKGN1cnJlbmN5KHJzQ2hhcmdlLmR1dHlSYXRlKS5kaXZpZGUoMTAwKSkpKS52YWx1ZQ0KICAgICAgICAgICAgICBwYXlhYmxlVGF4VVNEID0gY3VycmVuY3kocGF5YWJsZVRheFVTRCkuYWRkKHJzQ2hhcmdlLnN1YnRvdGFsKS52YWx1ZQ0KICAgICAgICAgICAgICB1bnBhaWRVc2RTdW0gPSBjdXJyZW5jeSh1bnBhaWRVc2RTdW0pLmFkZChyc0NoYXJnZS5zcWREbkN1cnJlbmN5QmFsYW5jZSkudmFsdWUNCiAgICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICAgIHBheWFibGVSTUIgPSBjdXJyZW5jeShwYXlhYmxlUk1CKS5hZGQoY3VycmVuY3kocnNDaGFyZ2Uuc3VidG90YWwpLmRpdmlkZShjdXJyZW5jeSgxKS5hZGQoY3VycmVuY3kocnNDaGFyZ2UuZHV0eVJhdGUpLmRpdmlkZSgxMDApKSkpLnZhbHVlDQogICAgICAgICAgICAgIHBheWFibGVUYXhSTUIgPSBjdXJyZW5jeShwYXlhYmxlVGF4Uk1CKS5hZGQocnNDaGFyZ2Uuc3VidG90YWwpLnZhbHVlDQogICAgICAgICAgICAgIHVucGFpZFJtYlN1bSA9IGN1cnJlbmN5KHVucGFpZFJtYlN1bSkuYWRkKHJzQ2hhcmdlLnNxZERuQ3VycmVuY3lCYWxhbmNlKS52YWx1ZQ0KICAgICAgICAgICAgfQ0KICAgICAgICAgIH0NCiAgICAgICAgfSkNCiAgICAgIH0pIDogbnVsbA0KICAgICAgLy8g5YWo5YyF5oql5YWz6LS555SoDQogICAgICB0aGlzLmZvcm0ucnNPcEZyZWVEZWNsYXJlTGlzdCA/IHRoaXMuZm9ybS5yc09wRnJlZURlY2xhcmVMaXN0Lm1hcChpdGVtID0+IHsNCiAgICAgICAgaXRlbS5yc0NoYXJnZUxpc3QubWFwKHJzQ2hhcmdlID0+IHsNCiAgICAgICAgICBpZiAocnNDaGFyZ2Uuc3VidG90YWwpIHsNCiAgICAgICAgICAgIGlmIChyc0NoYXJnZS5kbkN1cnJlbmN5Q29kZSA9PT0gIlVTRCIpIHsNCiAgICAgICAgICAgICAgcGF5YWJsZVVTRCA9IGN1cnJlbmN5KHBheWFibGVVU0QpLmFkZChjdXJyZW5jeShyc0NoYXJnZS5zdWJ0b3RhbCkuZGl2aWRlKGN1cnJlbmN5KDEpLmFkZChjdXJyZW5jeShyc0NoYXJnZS5kdXR5UmF0ZSkuZGl2aWRlKDEwMCkpKSkudmFsdWUNCiAgICAgICAgICAgICAgcGF5YWJsZVRheFVTRCA9IGN1cnJlbmN5KHBheWFibGVUYXhVU0QpLmFkZChyc0NoYXJnZS5zdWJ0b3RhbCkudmFsdWUNCiAgICAgICAgICAgICAgdW5wYWlkVXNkU3VtID0gY3VycmVuY3kodW5wYWlkVXNkU3VtKS5hZGQocnNDaGFyZ2Uuc3FkRG5DdXJyZW5jeUJhbGFuY2UpLnZhbHVlDQogICAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgICBwYXlhYmxlUk1CID0gY3VycmVuY3kocGF5YWJsZVJNQikuYWRkKGN1cnJlbmN5KHJzQ2hhcmdlLnN1YnRvdGFsKS5kaXZpZGUoY3VycmVuY3koMSkuYWRkKGN1cnJlbmN5KHJzQ2hhcmdlLmR1dHlSYXRlKS5kaXZpZGUoMTAwKSkpKS52YWx1ZQ0KICAgICAgICAgICAgICBwYXlhYmxlVGF4Uk1CID0gY3VycmVuY3kocGF5YWJsZVRheFJNQikuYWRkKHJzQ2hhcmdlLnN1YnRvdGFsKS52YWx1ZQ0KICAgICAgICAgICAgICB1bnBhaWRSbWJTdW0gPSBjdXJyZW5jeSh1bnBhaWRSbWJTdW0pLmFkZChyc0NoYXJnZS5zcWREbkN1cnJlbmN5QmFsYW5jZSkudmFsdWUNCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9DQogICAgICAgIH0pDQogICAgICB9KSA6IG51bGwNCg0KICAgICAgdGhpcy5yc09wUmFpbEZDTCA/IHRoaXMucnNPcFJhaWxGQ0wucnNDaGFyZ2VMaXN0Lm1hcChyc0NoYXJnZSA9PiB7DQogICAgICAgIGlmIChyc0NoYXJnZS5zdWJ0b3RhbCkgew0KICAgICAgICAgIGlmIChyc0NoYXJnZS5kbkN1cnJlbmN5Q29kZSA9PT0gIlVTRCIpIHsNCiAgICAgICAgICAgIHBheWFibGVVU0QgPSBjdXJyZW5jeShwYXlhYmxlVVNEKS5hZGQoY3VycmVuY3kocnNDaGFyZ2Uuc3VidG90YWwpLmRpdmlkZShjdXJyZW5jeSgxKS5hZGQoY3VycmVuY3kocnNDaGFyZ2UuZHV0eVJhdGUpLmRpdmlkZSgxMDApKSkpLnZhbHVlDQogICAgICAgICAgICBwYXlhYmxlVGF4VVNEID0gY3VycmVuY3kocGF5YWJsZVRheFVTRCkuYWRkKHJzQ2hhcmdlLnN1YnRvdGFsKS52YWx1ZQ0KICAgICAgICAgICAgdW5wYWlkVXNkU3VtID0gY3VycmVuY3kodW5wYWlkVXNkU3VtKS5hZGQocnNDaGFyZ2Uuc3FkRG5DdXJyZW5jeUJhbGFuY2UpLnZhbHVlDQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIHBheWFibGVSTUIgPSBjdXJyZW5jeShwYXlhYmxlUk1CKS5hZGQoY3VycmVuY3kocnNDaGFyZ2Uuc3VidG90YWwpLmRpdmlkZShjdXJyZW5jeSgxKS5hZGQoY3VycmVuY3kocnNDaGFyZ2UuZHV0eVJhdGUpLmRpdmlkZSgxMDApKSkpLnZhbHVlDQogICAgICAgICAgICBwYXlhYmxlVGF4Uk1CID0gY3VycmVuY3kocGF5YWJsZVRheFJNQikuYWRkKHJzQ2hhcmdlLnN1YnRvdGFsKS52YWx1ZQ0KICAgICAgICAgICAgdW5wYWlkUm1iU3VtID0gY3VycmVuY3kodW5wYWlkUm1iU3VtKS5hZGQocnNDaGFyZ2Uuc3FkRG5DdXJyZW5jeUJhbGFuY2UpLnZhbHVlDQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICB9KSA6IG51bGwNCiAgICAgIHRoaXMucnNPcFJhaWxMQ0wgPyB0aGlzLnJzT3BSYWlsTENMLnJzQ2hhcmdlTGlzdC5tYXAocnNDaGFyZ2UgPT4gew0KICAgICAgICBpZiAocnNDaGFyZ2Uuc3VidG90YWwpIHsNCiAgICAgICAgICBpZiAocnNDaGFyZ2UuZG5DdXJyZW5jeUNvZGUgPT09ICJVU0QiKSB7DQogICAgICAgICAgICBwYXlhYmxlVVNEID0gY3VycmVuY3kocGF5YWJsZVVTRCkuYWRkKGN1cnJlbmN5KHJzQ2hhcmdlLnN1YnRvdGFsKS5kaXZpZGUoY3VycmVuY3koMSkuYWRkKGN1cnJlbmN5KHJzQ2hhcmdlLmR1dHlSYXRlKS5kaXZpZGUoMTAwKSkpKS52YWx1ZQ0KICAgICAgICAgICAgcGF5YWJsZVRheFVTRCA9IGN1cnJlbmN5KHBheWFibGVUYXhVU0QpLmFkZChjdXJyZW5jeShyc0NoYXJnZS5zdWJ0b3RhbCkubXVsdGlwbHkoY3VycmVuY3koMSkuYWRkKGN1cnJlbmN5KHJzQ2hhcmdlLmR1dHlSYXRlKS5kaXZpZGUoMTAwKSkpKS52YWx1ZQ0KICAgICAgICAgICAgdW5wYWlkVXNkU3VtID0gY3VycmVuY3kodW5wYWlkVXNkU3VtKS5hZGQocnNDaGFyZ2Uuc3FkRG5DdXJyZW5jeUJhbGFuY2UpLnZhbHVlDQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIHBheWFibGVSTUIgPSBjdXJyZW5jeShwYXlhYmxlUk1CKS5hZGQoY3VycmVuY3kocnNDaGFyZ2Uuc3VidG90YWwpLmRpdmlkZShjdXJyZW5jeSgxKS5hZGQoY3VycmVuY3kocnNDaGFyZ2UuZHV0eVJhdGUpLmRpdmlkZSgxMDApKSkpLnZhbHVlDQogICAgICAgICAgICBwYXlhYmxlVGF4Uk1CID0gY3VycmVuY3kocGF5YWJsZVRheFJNQikuYWRkKGN1cnJlbmN5KHJzQ2hhcmdlLnN1YnRvdGFsKS5tdWx0aXBseShjdXJyZW5jeSgxKS5hZGQoY3VycmVuY3kocnNDaGFyZ2UuZHV0eVJhdGUpLmRpdmlkZSgxMDApKSkpLnZhbHVlDQogICAgICAgICAgICB1bnBhaWRSbWJTdW0gPSBjdXJyZW5jeSh1bnBhaWRSbWJTdW0pLmFkZChyc0NoYXJnZS5zcWREbkN1cnJlbmN5QmFsYW5jZSkudmFsdWUNCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgIH0pIDogbnVsbA0KICAgICAgdGhpcy5yc09wRXhwcmVzcyA/IHRoaXMucnNPcEV4cHJlc3MucnNDaGFyZ2VMaXN0Lm1hcChyc0NoYXJnZSA9PiB7DQogICAgICAgIGlmIChyc0NoYXJnZS5zdWJ0b3RhbCkgew0KICAgICAgICAgIGlmIChyc0NoYXJnZS5kbkN1cnJlbmN5Q29kZSA9PT0gIlVTRCIpIHsNCiAgICAgICAgICAgIHBheWFibGVVU0QgPSBjdXJyZW5jeShwYXlhYmxlVVNEKS5hZGQoY3VycmVuY3kocnNDaGFyZ2Uuc3VidG90YWwpLmRpdmlkZShjdXJyZW5jeSgxKS5hZGQoY3VycmVuY3kocnNDaGFyZ2UuZHV0eVJhdGUpLmRpdmlkZSgxMDApKSkpLnZhbHVlDQogICAgICAgICAgICBwYXlhYmxlVGF4VVNEID0gY3VycmVuY3kocGF5YWJsZVRheFVTRCkuYWRkKHJzQ2hhcmdlLnN1YnRvdGFsKS52YWx1ZQ0KICAgICAgICAgICAgdW5wYWlkVXNkU3VtID0gY3VycmVuY3kodW5wYWlkVXNkU3VtKS5hZGQocnNDaGFyZ2Uuc3FkRG5DdXJyZW5jeUJhbGFuY2UpLnZhbHVlDQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIHBheWFibGVSTUIgPSBjdXJyZW5jeShwYXlhYmxlUk1CKS5hZGQoY3VycmVuY3kocnNDaGFyZ2Uuc3VidG90YWwpLmRpdmlkZShjdXJyZW5jeSgxKS5hZGQoY3VycmVuY3kocnNDaGFyZ2UuZHV0eVJhdGUpLmRpdmlkZSgxMDApKSkpLnZhbHVlDQogICAgICAgICAgICBwYXlhYmxlVGF4Uk1CID0gY3VycmVuY3kocGF5YWJsZVRheFJNQikuYWRkKHJzQ2hhcmdlLnN1YnRvdGFsKS52YWx1ZQ0KICAgICAgICAgICAgdW5wYWlkUm1iU3VtID0gY3VycmVuY3kodW5wYWlkUm1iU3VtKS5hZGQocnNDaGFyZ2Uuc3FkRG5DdXJyZW5jeUJhbGFuY2UpLnZhbHVlDQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICB9KSA6IG51bGwNCiAgICAgIHRoaXMucnNPcERPQWdlbnQgPyB0aGlzLnJzT3BET0FnZW50LnJzQ2hhcmdlTGlzdC5tYXAocnNDaGFyZ2UgPT4gew0KICAgICAgICBpZiAocnNDaGFyZ2Uuc3VidG90YWwpIHsNCiAgICAgICAgICBpZiAocnNDaGFyZ2UuZG5DdXJyZW5jeUNvZGUgPT09ICJVU0QiKSB7DQogICAgICAgICAgICBwYXlhYmxlVVNEID0gY3VycmVuY3kocGF5YWJsZVVTRCkuYWRkKGN1cnJlbmN5KHJzQ2hhcmdlLnN1YnRvdGFsKS5kaXZpZGUoY3VycmVuY3koMSkuYWRkKGN1cnJlbmN5KHJzQ2hhcmdlLmR1dHlSYXRlKS5kaXZpZGUoMTAwKSkpKS52YWx1ZQ0KICAgICAgICAgICAgcGF5YWJsZVRheFVTRCA9IGN1cnJlbmN5KHBheWFibGVUYXhVU0QpLmFkZChyc0NoYXJnZS5zdWJ0b3RhbCkudmFsdWUNCiAgICAgICAgICAgIHVucGFpZFVzZFN1bSA9IGN1cnJlbmN5KHVucGFpZFVzZFN1bSkuYWRkKHJzQ2hhcmdlLnNxZERuQ3VycmVuY3lCYWxhbmNlKS52YWx1ZQ0KICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICBwYXlhYmxlUk1CID0gY3VycmVuY3kocGF5YWJsZVJNQikuYWRkKGN1cnJlbmN5KHJzQ2hhcmdlLnN1YnRvdGFsKS5kaXZpZGUoY3VycmVuY3koMSkuYWRkKGN1cnJlbmN5KHJzQ2hhcmdlLmR1dHlSYXRlKS5kaXZpZGUoMTAwKSkpKS52YWx1ZQ0KICAgICAgICAgICAgcGF5YWJsZVRheFJNQiA9IGN1cnJlbmN5KHBheWFibGVUYXhSTUIpLmFkZChyc0NoYXJnZS5zdWJ0b3RhbCkudmFsdWUNCiAgICAgICAgICAgIHVucGFpZFJtYlN1bSA9IGN1cnJlbmN5KHVucGFpZFJtYlN1bSkuYWRkKHJzQ2hhcmdlLnNxZERuQ3VycmVuY3lCYWxhbmNlKS52YWx1ZQ0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgfSkgOiBudWxsDQogICAgICAvLyBjb25zb2xlLmxvZyh0aGlzLnJzT3BDbGVhckFnZW50KQ0KICAgICAgdGhpcy5yc09wQ2xlYXJBZ2VudCA/IHRoaXMucnNPcENsZWFyQWdlbnQucnNDaGFyZ2VMaXN0Lm1hcChyc0NoYXJnZSA9PiB7DQogICAgICAgIGlmIChyc0NoYXJnZS5zdWJ0b3RhbCkgew0KICAgICAgICAgIGlmIChyc0NoYXJnZS5kbkN1cnJlbmN5Q29kZSA9PT0gIlVTRCIpIHsNCiAgICAgICAgICAgIHBheWFibGVVU0QgPSBjdXJyZW5jeShwYXlhYmxlVVNEKS5hZGQoY3VycmVuY3kocnNDaGFyZ2Uuc3VidG90YWwpLmRpdmlkZShjdXJyZW5jeSgxKS5hZGQoY3VycmVuY3kocnNDaGFyZ2UuZHV0eVJhdGUpLmRpdmlkZSgxMDApKSkpLnZhbHVlDQogICAgICAgICAgICBwYXlhYmxlVGF4VVNEID0gY3VycmVuY3kocGF5YWJsZVRheFVTRCkuYWRkKHJzQ2hhcmdlLnN1YnRvdGFsKS52YWx1ZQ0KICAgICAgICAgICAgdW5wYWlkVXNkU3VtID0gY3VycmVuY3kodW5wYWlkVXNkU3VtKS5hZGQocnNDaGFyZ2Uuc3FkRG5DdXJyZW5jeUJhbGFuY2UpLnZhbHVlDQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIHBheWFibGVSTUIgPSBjdXJyZW5jeShwYXlhYmxlUk1CKS5hZGQoY3VycmVuY3kocnNDaGFyZ2Uuc3VidG90YWwpLmRpdmlkZShjdXJyZW5jeSgxKS5hZGQoY3VycmVuY3kocnNDaGFyZ2UuZHV0eVJhdGUpLmRpdmlkZSgxMDApKSkpLnZhbHVlDQogICAgICAgICAgICBwYXlhYmxlVGF4Uk1CID0gY3VycmVuY3kocGF5YWJsZVRheFJNQikuYWRkKHJzQ2hhcmdlLnN1YnRvdGFsKS52YWx1ZQ0KICAgICAgICAgICAgdW5wYWlkUm1iU3VtID0gY3VycmVuY3kodW5wYWlkUm1iU3VtKS5hZGQocnNDaGFyZ2Uuc3FkRG5DdXJyZW5jeUJhbGFuY2UpLnZhbHVlDQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICB9KSA6IG51bGwNCiAgICAgIHRoaXMucnNPcFdIUyA/IHRoaXMucnNPcFdIUy5yc0NoYXJnZUxpc3QubWFwKHJzQ2hhcmdlID0+IHsNCiAgICAgICAgaWYgKHJzQ2hhcmdlLnN1YnRvdGFsKSB7DQogICAgICAgICAgaWYgKHJzQ2hhcmdlLmRuQ3VycmVuY3lDb2RlID09PSAiVVNEIikgew0KICAgICAgICAgICAgcGF5YWJsZVVTRCA9IGN1cnJlbmN5KHBheWFibGVVU0QpLmFkZChjdXJyZW5jeShyc0NoYXJnZS5zdWJ0b3RhbCkuZGl2aWRlKGN1cnJlbmN5KDEpLmFkZChjdXJyZW5jeShyc0NoYXJnZS5kdXR5UmF0ZSkuZGl2aWRlKDEwMCkpKSkudmFsdWUNCiAgICAgICAgICAgIHBheWFibGVUYXhVU0QgPSBjdXJyZW5jeShwYXlhYmxlVGF4VVNEKS5hZGQocnNDaGFyZ2Uuc3VidG90YWwpLnZhbHVlDQogICAgICAgICAgICB1bnBhaWRVc2RTdW0gPSBjdXJyZW5jeSh1bnBhaWRVc2RTdW0pLmFkZChyc0NoYXJnZS5zcWREbkN1cnJlbmN5QmFsYW5jZSkudmFsdWUNCiAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgcGF5YWJsZVJNQiA9IGN1cnJlbmN5KHBheWFibGVSTUIpLmFkZChjdXJyZW5jeShyc0NoYXJnZS5zdWJ0b3RhbCkuZGl2aWRlKGN1cnJlbmN5KDEpLmFkZChjdXJyZW5jeShyc0NoYXJnZS5kdXR5UmF0ZSkuZGl2aWRlKDEwMCkpKSkudmFsdWUNCiAgICAgICAgICAgIHBheWFibGVUYXhSTUIgPSBjdXJyZW5jeShwYXlhYmxlVGF4Uk1CKS5hZGQocnNDaGFyZ2Uuc3VidG90YWwpLnZhbHVlDQogICAgICAgICAgICB1bnBhaWRSbWJTdW0gPSBjdXJyZW5jeSh1bnBhaWRSbWJTdW0pLmFkZChyc0NoYXJnZS5zcWREbkN1cnJlbmN5QmFsYW5jZSkudmFsdWUNCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgIH0pIDogbnVsbA0KICAgICAgdGhpcy5yc09wM3JkQ2VydCA/IHRoaXMucnNPcDNyZENlcnQucnNDaGFyZ2VMaXN0Lm1hcChyc0NoYXJnZSA9PiB7DQogICAgICAgIGlmIChyc0NoYXJnZS5zdWJ0b3RhbCkgew0KICAgICAgICAgIGlmIChyc0NoYXJnZS5kbkN1cnJlbmN5Q29kZSA9PT0gIlVTRCIpIHsNCiAgICAgICAgICAgIHBheWFibGVVU0QgPSBjdXJyZW5jeShwYXlhYmxlVVNEKS5hZGQoY3VycmVuY3kocnNDaGFyZ2Uuc3VidG90YWwpLmRpdmlkZShjdXJyZW5jeSgxKS5hZGQoY3VycmVuY3kocnNDaGFyZ2UuZHV0eVJhdGUpLmRpdmlkZSgxMDApKSkpLnZhbHVlDQogICAgICAgICAgICBwYXlhYmxlVGF4VVNEID0gY3VycmVuY3kocGF5YWJsZVRheFVTRCkuYWRkKHJzQ2hhcmdlLnN1YnRvdGFsKS52YWx1ZQ0KICAgICAgICAgICAgdW5wYWlkVXNkU3VtID0gY3VycmVuY3kodW5wYWlkVXNkU3VtKS5hZGQocnNDaGFyZ2Uuc3FkRG5DdXJyZW5jeUJhbGFuY2UpLnZhbHVlDQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIHBheWFibGVSTUIgPSBjdXJyZW5jeShwYXlhYmxlUk1CKS5hZGQoY3VycmVuY3kocnNDaGFyZ2Uuc3VidG90YWwpLmRpdmlkZShjdXJyZW5jeSgxKS5hZGQoY3VycmVuY3kocnNDaGFyZ2UuZHV0eVJhdGUpLmRpdmlkZSgxMDApKSkpLnZhbHVlDQogICAgICAgICAgICBwYXlhYmxlVGF4Uk1CID0gY3VycmVuY3kocGF5YWJsZVRheFJNQikuYWRkKHJzQ2hhcmdlLnN1YnRvdGFsKS52YWx1ZQ0KICAgICAgICAgICAgdW5wYWlkUm1iU3VtID0gY3VycmVuY3kodW5wYWlkUm1iU3VtKS5hZGQocnNDaGFyZ2Uuc3FkRG5DdXJyZW5jeUJhbGFuY2UpLnZhbHVlDQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICB9KSA6IG51bGwNCiAgICAgIHRoaXMucnNPcElOUyA/IHRoaXMucnNPcElOUy5yc0NoYXJnZUxpc3QubWFwKHJzQ2hhcmdlID0+IHsNCiAgICAgICAgaWYgKHJzQ2hhcmdlLnN1YnRvdGFsKSB7DQogICAgICAgICAgaWYgKHJzQ2hhcmdlLmRuQ3VycmVuY3lDb2RlID09PSAiVVNEIikgew0KICAgICAgICAgICAgcGF5YWJsZVVTRCA9IGN1cnJlbmN5KHBheWFibGVVU0QpLmFkZChjdXJyZW5jeShyc0NoYXJnZS5zdWJ0b3RhbCkuZGl2aWRlKGN1cnJlbmN5KDEpLmFkZChjdXJyZW5jeShyc0NoYXJnZS5kdXR5UmF0ZSkuZGl2aWRlKDEwMCkpKSkudmFsdWUNCiAgICAgICAgICAgIHBheWFibGVUYXhVU0QgPSBjdXJyZW5jeShwYXlhYmxlVGF4VVNEKS5hZGQocnNDaGFyZ2Uuc3VidG90YWwpLnZhbHVlDQogICAgICAgICAgICB1bnBhaWRVc2RTdW0gPSBjdXJyZW5jeSh1bnBhaWRVc2RTdW0pLmFkZChyc0NoYXJnZS5zcWREbkN1cnJlbmN5QmFsYW5jZSkudmFsdWUNCiAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgcGF5YWJsZVJNQiA9IGN1cnJlbmN5KHBheWFibGVSTUIpLmFkZChjdXJyZW5jeShyc0NoYXJnZS5zdWJ0b3RhbCkuZGl2aWRlKGN1cnJlbmN5KDEpLmFkZChjdXJyZW5jeShyc0NoYXJnZS5kdXR5UmF0ZSkuZGl2aWRlKDEwMCkpKSkudmFsdWUNCiAgICAgICAgICAgIHBheWFibGVUYXhSTUIgPSBjdXJyZW5jeShwYXlhYmxlVGF4Uk1CKS5hZGQocnNDaGFyZ2Uuc3VidG90YWwpLnZhbHVlDQogICAgICAgICAgICB1bnBhaWRSbWJTdW0gPSBjdXJyZW5jeSh1bnBhaWRSbWJTdW0pLmFkZChyc0NoYXJnZS5zcWREbkN1cnJlbmN5QmFsYW5jZSkudmFsdWUNCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgIH0pIDogbnVsbA0KICAgICAgdGhpcy5yc09wVHJhZGluZyA/IHRoaXMucnNPcFRyYWRpbmcucnNDaGFyZ2VMaXN0Lm1hcChyc0NoYXJnZSA9PiB7DQogICAgICAgIGlmIChyc0NoYXJnZS5zdWJ0b3RhbCkgew0KICAgICAgICAgIGlmIChyc0NoYXJnZS5kbkN1cnJlbmN5Q29kZSA9PT0gIlVTRCIpIHsNCiAgICAgICAgICAgIHBheWFibGVVU0QgPSBjdXJyZW5jeShwYXlhYmxlVVNEKS5hZGQoY3VycmVuY3kocnNDaGFyZ2Uuc3VidG90YWwpLmRpdmlkZShjdXJyZW5jeSgxKS5hZGQoY3VycmVuY3kocnNDaGFyZ2UuZHV0eVJhdGUpLmRpdmlkZSgxMDApKSkpLnZhbHVlDQogICAgICAgICAgICBwYXlhYmxlVGF4VVNEID0gY3VycmVuY3kocGF5YWJsZVRheFVTRCkuYWRkKHJzQ2hhcmdlLnN1YnRvdGFsKS52YWx1ZQ0KICAgICAgICAgICAgdW5wYWlkVXNkU3VtID0gY3VycmVuY3kodW5wYWlkVXNkU3VtKS5hZGQocnNDaGFyZ2Uuc3FkRG5DdXJyZW5jeUJhbGFuY2UpLnZhbHVlDQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIHBheWFibGVSTUIgPSBjdXJyZW5jeShwYXlhYmxlUk1CKS5hZGQoY3VycmVuY3kocnNDaGFyZ2Uuc3VidG90YWwpLmRpdmlkZShjdXJyZW5jeSgxKS5hZGQoY3VycmVuY3kocnNDaGFyZ2UuZHV0eVJhdGUpLmRpdmlkZSgxMDApKSkpLnZhbHVlDQogICAgICAgICAgICBwYXlhYmxlVGF4Uk1CID0gY3VycmVuY3kocGF5YWJsZVRheFJNQikuYWRkKHJzQ2hhcmdlLnN1YnRvdGFsKS52YWx1ZQ0KICAgICAgICAgICAgdW5wYWlkUm1iU3VtID0gY3VycmVuY3kodW5wYWlkUm1iU3VtKS5hZGQocnNDaGFyZ2Uuc3FkRG5DdXJyZW5jeUJhbGFuY2UpLnZhbHVlDQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICB9KSA6IG51bGwNCiAgICAgIHRoaXMucnNPcEZ1bWlnYXRpb24gPyB0aGlzLnJzT3BGdW1pZ2F0aW9uLnJzQ2hhcmdlTGlzdC5tYXAocnNDaGFyZ2UgPT4gew0KICAgICAgICBpZiAocnNDaGFyZ2Uuc3VidG90YWwpIHsNCiAgICAgICAgICBpZiAocnNDaGFyZ2UuZG5DdXJyZW5jeUNvZGUgPT09ICJVU0QiKSB7DQogICAgICAgICAgICBwYXlhYmxlVVNEID0gY3VycmVuY3kocGF5YWJsZVVTRCkuYWRkKGN1cnJlbmN5KHJzQ2hhcmdlLnN1YnRvdGFsKS5kaXZpZGUoY3VycmVuY3koMSkuYWRkKGN1cnJlbmN5KHJzQ2hhcmdlLmR1dHlSYXRlKS5kaXZpZGUoMTAwKSkpKS52YWx1ZQ0KICAgICAgICAgICAgcGF5YWJsZVRheFVTRCA9IGN1cnJlbmN5KHBheWFibGVUYXhVU0QpLmFkZChyc0NoYXJnZS5zdWJ0b3RhbCkudmFsdWUNCiAgICAgICAgICAgIHVucGFpZFVzZFN1bSA9IGN1cnJlbmN5KHVucGFpZFVzZFN1bSkuYWRkKHJzQ2hhcmdlLnNxZERuQ3VycmVuY3lCYWxhbmNlKS52YWx1ZQ0KICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICBwYXlhYmxlUk1CID0gY3VycmVuY3kocGF5YWJsZVJNQikuYWRkKGN1cnJlbmN5KHJzQ2hhcmdlLnN1YnRvdGFsKS5kaXZpZGUoY3VycmVuY3koMSkuYWRkKGN1cnJlbmN5KHJzQ2hhcmdlLmR1dHlSYXRlKS5kaXZpZGUoMTAwKSkpKS52YWx1ZQ0KICAgICAgICAgICAgcGF5YWJsZVRheFJNQiA9IGN1cnJlbmN5KHBheWFibGVUYXhSTUIpLmFkZChyc0NoYXJnZS5zdWJ0b3RhbCkudmFsdWUNCiAgICAgICAgICAgIHVucGFpZFJtYlN1bSA9IGN1cnJlbmN5KHVucGFpZFJtYlN1bSkuYWRkKHJzQ2hhcmdlLnNxZERuQ3VycmVuY3lCYWxhbmNlKS52YWx1ZQ0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgfSkgOiBudWxsDQogICAgICB0aGlzLnJzT3BDTyA/IHRoaXMucnNPcENPLnJzQ2hhcmdlTGlzdC5tYXAocnNDaGFyZ2UgPT4gew0KICAgICAgICBpZiAocnNDaGFyZ2Uuc3VidG90YWwpIHsNCiAgICAgICAgICBpZiAocnNDaGFyZ2UuZG5DdXJyZW5jeUNvZGUgPT09ICJVU0QiKSB7DQogICAgICAgICAgICBwYXlhYmxlVVNEID0gY3VycmVuY3kocGF5YWJsZVVTRCkuYWRkKGN1cnJlbmN5KHJzQ2hhcmdlLnN1YnRvdGFsKS5kaXZpZGUoY3VycmVuY3koMSkuYWRkKGN1cnJlbmN5KHJzQ2hhcmdlLmR1dHlSYXRlKS5kaXZpZGUoMTAwKSkpKS52YWx1ZQ0KICAgICAgICAgICAgcGF5YWJsZVRheFVTRCA9IGN1cnJlbmN5KHBheWFibGVUYXhVU0QpLmFkZChyc0NoYXJnZS5zdWJ0b3RhbCkudmFsdWUNCiAgICAgICAgICAgIHVucGFpZFVzZFN1bSA9IGN1cnJlbmN5KHVucGFpZFVzZFN1bSkuYWRkKHJzQ2hhcmdlLnNxZERuQ3VycmVuY3lCYWxhbmNlKS52YWx1ZQ0KICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICBwYXlhYmxlUk1CID0gY3VycmVuY3kocGF5YWJsZVJNQikuYWRkKGN1cnJlbmN5KHJzQ2hhcmdlLnN1YnRvdGFsKS5kaXZpZGUoY3VycmVuY3koMSkuYWRkKGN1cnJlbmN5KHJzQ2hhcmdlLmR1dHlSYXRlKS5kaXZpZGUoMTAwKSkpKS52YWx1ZQ0KICAgICAgICAgICAgcGF5YWJsZVRheFJNQiA9IGN1cnJlbmN5KHBheWFibGVUYXhSTUIpLmFkZChyc0NoYXJnZS5zdWJ0b3RhbCkudmFsdWUNCiAgICAgICAgICAgIHVucGFpZFJtYlN1bSA9IGN1cnJlbmN5KHVucGFpZFJtYlN1bSkuYWRkKHJzQ2hhcmdlLnNxZERuQ3VycmVuY3lCYWxhbmNlKS52YWx1ZQ0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgfSkgOiBudWxsDQogICAgICB0aGlzLnJzT3BPdGhlciA/IHRoaXMucnNPcE90aGVyLnJzQ2hhcmdlTGlzdC5tYXAocnNDaGFyZ2UgPT4gew0KICAgICAgICBpZiAocnNDaGFyZ2Uuc3VidG90YWwpIHsNCiAgICAgICAgICBpZiAocnNDaGFyZ2UuZG5DdXJyZW5jeUNvZGUgPT09ICJVU0QiKSB7DQogICAgICAgICAgICBwYXlhYmxlVVNEID0gY3VycmVuY3kocGF5YWJsZVVTRCkuYWRkKGN1cnJlbmN5KHJzQ2hhcmdlLnN1YnRvdGFsKS5kaXZpZGUoY3VycmVuY3koMSkuYWRkKGN1cnJlbmN5KHJzQ2hhcmdlLmR1dHlSYXRlKS5kaXZpZGUoMTAwKSkpKS52YWx1ZQ0KICAgICAgICAgICAgcGF5YWJsZVRheFVTRCA9IGN1cnJlbmN5KHBheWFibGVUYXhVU0QpLmFkZChyc0NoYXJnZS5zdWJ0b3RhbCkudmFsdWUNCiAgICAgICAgICAgIHVucGFpZFVzZFN1bSA9IGN1cnJlbmN5KHVucGFpZFVzZFN1bSkuYWRkKHJzQ2hhcmdlLnNxZERuQ3VycmVuY3lCYWxhbmNlKS52YWx1ZQ0KICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICBwYXlhYmxlUk1CID0gY3VycmVuY3kocGF5YWJsZVJNQikuYWRkKGN1cnJlbmN5KHJzQ2hhcmdlLnN1YnRvdGFsKS5kaXZpZGUoY3VycmVuY3koMSkuYWRkKGN1cnJlbmN5KHJzQ2hhcmdlLmR1dHlSYXRlKS5kaXZpZGUoMTAwKSkpKS52YWx1ZQ0KICAgICAgICAgICAgcGF5YWJsZVRheFJNQiA9IGN1cnJlbmN5KHBheWFibGVUYXhSTUIpLmFkZChyc0NoYXJnZS5zdWJ0b3RhbCkudmFsdWUNCiAgICAgICAgICAgIHVucGFpZFJtYlN1bSA9IGN1cnJlbmN5KHVucGFpZFJtYlN1bSkuYWRkKHJzQ2hhcmdlLnNxZERuQ3VycmVuY3lCYWxhbmNlKS52YWx1ZQ0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgfSkgOiBudWxsDQoNCiAgICAgIHRoaXMucnNDbGllbnRNZXNzYWdlUGF5YWJsZVJNQiA9IHBheWFibGVSTUINCiAgICAgIHRoaXMucnNDbGllbnRNZXNzYWdlUGF5YWJsZVRheFJNQiA9IHBheWFibGVUYXhSTUINCiAgICAgIHRoaXMucnNDbGllbnRNZXNzYWdlUGF5YWJsZVVTRCA9IHBheWFibGVVU0QNCiAgICAgIHRoaXMucnNDbGllbnRNZXNzYWdlUGF5YWJsZVRheFVTRCA9IHBheWFibGVUYXhVU0QNCiAgICAgIHRoaXMuc3FkVW5wYWlkUm1iU3VtID0gdW5wYWlkUm1iU3VtDQogICAgICB0aGlzLnNxZFVucGFpZFVzZFN1bSA9IHVucGFpZFVzZFN1bQ0KICAgICAgLy8g5LiN5ZCr56iO5Yip5ramDQogICAgICB0aGlzLnJzQ2xpZW50TWVzc2FnZVByb2ZpdFJNQiA9IGN1cnJlbmN5KHJlY2VpdmFibGVSTUIpLnN1YnRyYWN0KHBheWFibGVSTUIpLnZhbHVlDQogICAgICB0aGlzLnJzQ2xpZW50TWVzc2FnZVByb2ZpdFVTRCA9IGN1cnJlbmN5KHJlY2VpdmFibGVVU0QpLnN1YnRyYWN0KHBheWFibGVVU0QpLnZhbHVlDQogICAgICBpZiAobi5sZW5ndGggPiAwKSB7DQogICAgICAgIC8vIOWQq+eojuWIqea2pg0KICAgICAgICB0aGlzLnJzQ2xpZW50TWVzc2FnZVByb2ZpdFRheFJNQiA9IGN1cnJlbmN5KHJlY2VpdmFibGVUYXhSTUIpLnN1YnRyYWN0KHBheWFibGVUYXhSTUIpLnZhbHVlDQogICAgICAgIHRoaXMucnNDbGllbnRNZXNzYWdlUHJvZml0VGF4VVNEID0gY3VycmVuY3kocmVjZWl2YWJsZVRheFVTRCkuc3VidHJhY3QocGF5YWJsZVRheFVTRCkudmFsdWUNCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHRoaXMucnNDbGllbnRNZXNzYWdlUHJvZml0VGF4Uk1CID0gY3VycmVuY3kocmVjZWl2YWJsZVJNQikuc3VidHJhY3QocGF5YWJsZVRheFJNQikudmFsdWUNCiAgICAgICAgdGhpcy5yc0NsaWVudE1lc3NhZ2VQcm9maXRUYXhVU0QgPSBjdXJyZW5jeShyZWNlaXZhYmxlVVNEKS5zdWJ0cmFjdChwYXlhYmxlVGF4VVNEKS52YWx1ZQ0KICAgICAgfQ0KICAgIH0sDQogICAgY2FsY3VsYXRlQ2hhcmdlKHNlcnZpY2VUeXBlSWQsIG4sIHJzT3BTZXJ2aWNlKSB7DQogICAgICBpZiAoc2VydmljZVR5cGVJZCA9PT0gMTA0KSB7DQogICAgICAgIGxldCBwYXlhYmxlUk1CID0gMA0KICAgICAgICBsZXQgcGF5YWJsZVJNQlRheCA9IDANCiAgICAgICAgbGV0IHBheWFibGVVU0QgPSAwDQogICAgICAgIGxldCBwYXlhYmxlVVNEVGF4ID0gMA0KICAgICAgICBuLm1hcChyc0NoYXJnZSA9PiB7DQogICAgICAgICAgaWYgKHJzQ2hhcmdlLmRuQ3VycmVuY3lDb2RlID09PSAiVVNEIikgew0KICAgICAgICAgICAgcGF5YWJsZVVTRCA9IGN1cnJlbmN5KHBheWFibGVVU0QpLmFkZChjdXJyZW5jeShyc0NoYXJnZS5zdWJ0b3RhbCkuZGl2aWRlKGN1cnJlbmN5KDEpLmFkZChjdXJyZW5jeShyc0NoYXJnZS5kdXR5UmF0ZSkuZGl2aWRlKDEwMCkpKSkudmFsdWUNCiAgICAgICAgICAgIHBheWFibGVVU0RUYXggPSBjdXJyZW5jeShwYXlhYmxlVVNEVGF4KS5hZGQocnNDaGFyZ2Uuc3VidG90YWwpLnZhbHVlDQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIHBheWFibGVSTUIgPSBjdXJyZW5jeShwYXlhYmxlUk1CKS5hZGQoY3VycmVuY3kocnNDaGFyZ2Uuc3VidG90YWwpLmRpdmlkZShjdXJyZW5jeSgxKS5hZGQoY3VycmVuY3kocnNDaGFyZ2UuZHV0eVJhdGUpLmRpdmlkZSgxMDApKSkpLnZhbHVlDQogICAgICAgICAgICBwYXlhYmxlUk1CVGF4ID0gY3VycmVuY3kocGF5YWJsZVJNQlRheCkuYWRkKHJzQ2hhcmdlLnN1YnRvdGFsKS52YWx1ZQ0KICAgICAgICAgIH0NCiAgICAgICAgfSkNCiAgICAgICAgcnNPcFNlcnZpY2UucGF5YWJsZVJNQiA9IHBheWFibGVSTUINCiAgICAgICAgcnNPcFNlcnZpY2UucGF5YWJsZVVTRCA9IHBheWFibGVVU0QNCiAgICAgICAgcnNPcFNlcnZpY2UucGF5YWJsZVJNQlRheCA9IHBheWFibGVSTUJUYXgNCiAgICAgICAgcnNPcFNlcnZpY2UucGF5YWJsZVVTRFRheCA9IHBheWFibGVVU0RUYXgNCg0KICAgICAgICB0aGlzLnJzQ2xpZW50TWVzc2FnZUNoYXJnZSh0aGlzLnJzQ2xpZW50TWVzc2FnZS5yc0NoYXJnZUxpc3QsIHJzT3BTZXJ2aWNlKQ0KICAgICAgfQ0KICAgICAgaWYgKHNlcnZpY2VUeXBlSWQgPT09IDEpIHsNCiAgICAgICAgbGV0IHBheWFibGVSTUIgPSAwDQogICAgICAgIGxldCBwYXlhYmxlUk1CVGF4ID0gMA0KICAgICAgICBsZXQgcGF5YWJsZVVTRCA9IDANCiAgICAgICAgbGV0IHBheWFibGVVU0RUYXggPSAwDQogICAgICAgIG4ubWFwKHJzQ2hhcmdlID0+IHsNCiAgICAgICAgICBpZiAocnNDaGFyZ2UuZG5DdXJyZW5jeUNvZGUgPT09ICJVU0QiKSB7DQogICAgICAgICAgICBwYXlhYmxlVVNEID0gY3VycmVuY3kocGF5YWJsZVVTRCkuYWRkKGN1cnJlbmN5KHJzQ2hhcmdlLnN1YnRvdGFsKS5kaXZpZGUoY3VycmVuY3koMSkuYWRkKGN1cnJlbmN5KHJzQ2hhcmdlLmR1dHlSYXRlKS5kaXZpZGUoMTAwKSkpKS52YWx1ZQ0KICAgICAgICAgICAgcGF5YWJsZVVTRFRheCA9IGN1cnJlbmN5KHBheWFibGVVU0RUYXgpLmFkZChyc0NoYXJnZS5zdWJ0b3RhbCkudmFsdWUNCiAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgcGF5YWJsZVJNQiA9IGN1cnJlbmN5KHBheWFibGVSTUIpLmFkZChjdXJyZW5jeShyc0NoYXJnZS5zdWJ0b3RhbCkuZGl2aWRlKGN1cnJlbmN5KDEpLmFkZChjdXJyZW5jeShyc0NoYXJnZS5kdXR5UmF0ZSkuZGl2aWRlKDEwMCkpKSkudmFsdWUNCiAgICAgICAgICAgIHBheWFibGVSTUJUYXggPSBjdXJyZW5jeShwYXlhYmxlUk1CVGF4KS5hZGQocnNDaGFyZ2Uuc3VidG90YWwpLnZhbHVlDQogICAgICAgICAgfQ0KICAgICAgICB9KQ0KICAgICAgICByc09wU2VydmljZS5wYXlhYmxlUk1CID0gcGF5YWJsZVJNQg0KICAgICAgICByc09wU2VydmljZS5wYXlhYmxlVVNEID0gcGF5YWJsZVVTRA0KICAgICAgICByc09wU2VydmljZS5wYXlhYmxlUk1CVGF4ID0gcGF5YWJsZVJNQlRheA0KICAgICAgICByc09wU2VydmljZS5wYXlhYmxlVVNEVGF4ID0gcGF5YWJsZVVTRFRheA0KDQogICAgICAgIHRoaXMucnNDbGllbnRNZXNzYWdlQ2hhcmdlKHRoaXMucnNDbGllbnRNZXNzYWdlLnJzQ2hhcmdlTGlzdCwgcnNPcFNlcnZpY2UpDQoNCiAgICAgICAgaWYgKHJzT3BTZXJ2aWNlLmJvb2tpbmdDaGFyZ2VSZW1hcmsgPT09IG51bGwgfHwgcnNPcFNlcnZpY2UuYm9va2luZ0NoYXJnZVJlbWFyayA9PT0gIiIpIHsNCiAgICAgICAgICBsZXQgYm9va2luZ0NoYXJnZVJlbWFyayA9ICIiDQogICAgICAgICAgLy8g6K6i6Iix5Lit55qE6LS555So5aSH5rOoDQogICAgICAgICAgbi5tYXAocnNDaGFyZ2UgPT4gew0KICAgICAgICAgICAgYm9va2luZ0NoYXJnZVJlbWFyayArPSByc0NoYXJnZS5jaGFyZ2VOYW1lICsgIjpcdCIgKyByc0NoYXJnZS5kbkN1cnJlbmN5Q29kZSArICIgIiArIHJzQ2hhcmdlLmRuVW5pdFJhdGUgKyAiLyIgKyByc0NoYXJnZS5kblVuaXRDb2RlICsgIlxuIg0KICAgICAgICAgIH0pDQogICAgICAgICAgcnNPcFNlcnZpY2UuYm9va2luZ0NoYXJnZVJlbWFyayA9IGJvb2tpbmdDaGFyZ2VSZW1hcmsNCiAgICAgICAgfQ0KICAgICAgfQ0KICAgICAgaWYgKHNlcnZpY2VUeXBlSWQgPT09IDIpIHsNCiAgICAgICAgbGV0IHBheWFibGVSTUIgPSAwDQogICAgICAgIGxldCBwYXlhYmxlUk1CVGF4ID0gMA0KICAgICAgICBsZXQgcGF5YWJsZVVTRCA9IDANCiAgICAgICAgbGV0IHBheWFibGVVU0RUYXggPSAwDQogICAgICAgIG4ubWFwKHJzQ2hhcmdlID0+IHsNCiAgICAgICAgICBpZiAocnNDaGFyZ2UuZG5DdXJyZW5jeUNvZGUgPT09ICJVU0QiKSB7DQogICAgICAgICAgICBwYXlhYmxlVVNEID0gY3VycmVuY3kocGF5YWJsZVVTRCkuYWRkKGN1cnJlbmN5KHJzQ2hhcmdlLnN1YnRvdGFsKS5kaXZpZGUoY3VycmVuY3koMSkuYWRkKGN1cnJlbmN5KHJzQ2hhcmdlLmR1dHlSYXRlKS5kaXZpZGUoMTAwKSkpKS52YWx1ZQ0KICAgICAgICAgICAgcGF5YWJsZVVTRFRheCA9IGN1cnJlbmN5KHBheWFibGVVU0RUYXgpLmFkZChyc0NoYXJnZS5zdWJ0b3RhbCkudmFsdWUNCiAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgcGF5YWJsZVJNQiA9IGN1cnJlbmN5KHBheWFibGVSTUIpLmFkZChjdXJyZW5jeShyc0NoYXJnZS5zdWJ0b3RhbCkuZGl2aWRlKGN1cnJlbmN5KDEpLmFkZChjdXJyZW5jeShyc0NoYXJnZS5kdXR5UmF0ZSkuZGl2aWRlKDEwMCkpKSkudmFsdWUNCiAgICAgICAgICAgIHBheWFibGVSTUJUYXggPSBjdXJyZW5jeShwYXlhYmxlUk1CVGF4KS5hZGQocnNDaGFyZ2Uuc3VidG90YWwpLnZhbHVlDQogICAgICAgICAgfQ0KICAgICAgICB9KQ0KICAgICAgICByc09wU2VydmljZS5wYXlhYmxlUk1CID0gcGF5YWJsZVJNQg0KICAgICAgICByc09wU2VydmljZS5wYXlhYmxlVVNEID0gcGF5YWJsZVVTRA0KICAgICAgICByc09wU2VydmljZS5wYXlhYmxlUk1CVGF4ID0gcGF5YWJsZVJNQlRheA0KICAgICAgICByc09wU2VydmljZS5wYXlhYmxlVVNEVGF4ID0gcGF5YWJsZVVTRFRheA0KDQogICAgICAgIHRoaXMucnNDbGllbnRNZXNzYWdlQ2hhcmdlKHRoaXMucnNDbGllbnRNZXNzYWdlLnJzQ2hhcmdlTGlzdCkNCg0KICAgICAgICBpZiAocnNPcFNlcnZpY2UuYm9va2luZ0NoYXJnZVJlbWFyayA9PT0gbnVsbCB8fCByc09wU2VydmljZS5ib29raW5nQ2hhcmdlUmVtYXJrID09PSAiIikgew0KICAgICAgICAgIGxldCBib29raW5nQ2hhcmdlUmVtYXJrID0gIiINCiAgICAgICAgICAvLyDorqLoiLHkuK3nmoTotLnnlKjlpIfms6gNCiAgICAgICAgICBuLm1hcChyc0NoYXJnZSA9PiB7DQogICAgICAgICAgICBib29raW5nQ2hhcmdlUmVtYXJrICs9IHJzQ2hhcmdlLmNoYXJnZU5hbWUgKyAiOlx0IiArIHJzQ2hhcmdlLmRuQ3VycmVuY3lDb2RlICsgIiAiICsgcnNDaGFyZ2UuZG5Vbml0UmF0ZSArICIvIiArIHJzQ2hhcmdlLmRuVW5pdENvZGUgKyAiXG4iDQogICAgICAgICAgfSkNCiAgICAgICAgICByc09wU2VydmljZS5ib29raW5nQ2hhcmdlUmVtYXJrID0gYm9va2luZ0NoYXJnZVJlbWFyaw0KICAgICAgICB9DQogICAgICB9DQogICAgICBpZiAoc2VydmljZVR5cGVJZCA9PT0gMTApIHsNCiAgICAgICAgbGV0IHBheWFibGVSTUIgPSAwDQogICAgICAgIGxldCBwYXlhYmxlUk1CVGF4ID0gMA0KICAgICAgICBsZXQgcGF5YWJsZVVTRCA9IDANCiAgICAgICAgbGV0IHBheWFibGVVU0RUYXggPSAwDQogICAgICAgIG4ubWFwKHJzQ2hhcmdlID0+IHsNCiAgICAgICAgICBpZiAocnNDaGFyZ2UuZG5DdXJyZW5jeUNvZGUgPT09ICJVU0QiKSB7DQogICAgICAgICAgICBwYXlhYmxlVVNEID0gY3VycmVuY3kocGF5YWJsZVVTRCkuYWRkKGN1cnJlbmN5KHJzQ2hhcmdlLnN1YnRvdGFsKS5kaXZpZGUoY3VycmVuY3koMSkuYWRkKGN1cnJlbmN5KHJzQ2hhcmdlLmR1dHlSYXRlKS5kaXZpZGUoMTAwKSkpKS52YWx1ZQ0KICAgICAgICAgICAgcGF5YWJsZVVTRFRheCA9IGN1cnJlbmN5KHBheWFibGVVU0RUYXgpLmFkZChyc0NoYXJnZS5zdWJ0b3RhbCkudmFsdWUNCiAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgcGF5YWJsZVJNQiA9IGN1cnJlbmN5KHBheWFibGVSTUIpLmFkZChjdXJyZW5jeShyc0NoYXJnZS5zdWJ0b3RhbCkuZGl2aWRlKGN1cnJlbmN5KDEpLmFkZChjdXJyZW5jeShyc0NoYXJnZS5kdXR5UmF0ZSkuZGl2aWRlKDEwMCkpKSkudmFsdWUNCiAgICAgICAgICAgIHBheWFibGVSTUJUYXggPSBjdXJyZW5jeShwYXlhYmxlUk1CVGF4KS5hZGQocnNDaGFyZ2Uuc3VidG90YWwpLnZhbHVlDQogICAgICAgICAgfQ0KICAgICAgICB9KQ0KICAgICAgICByc09wU2VydmljZS5wYXlhYmxlUk1CID0gcGF5YWJsZVJNQg0KICAgICAgICByc09wU2VydmljZS5wYXlhYmxlVVNEID0gcGF5YWJsZVVTRA0KICAgICAgICByc09wU2VydmljZS5wYXlhYmxlUk1CVGF4ID0gcGF5YWJsZVJNQlRheA0KICAgICAgICByc09wU2VydmljZS5wYXlhYmxlVVNEVGF4ID0gcGF5YWJsZVVTRFRheA0KDQogICAgICAgIHRoaXMucnNDbGllbnRNZXNzYWdlQ2hhcmdlKHRoaXMucnNDbGllbnRNZXNzYWdlLnJzQ2hhcmdlTGlzdCkNCiAgICAgIH0NCiAgICAgIGlmIChzZXJ2aWNlVHlwZUlkID09PSAyMCkgew0KICAgICAgICBsZXQgcGF5YWJsZVJNQiA9IDANCiAgICAgICAgbGV0IHBheWFibGVSTUJUYXggPSAwDQogICAgICAgIGxldCBwYXlhYmxlVVNEID0gMA0KICAgICAgICBsZXQgcGF5YWJsZVVTRFRheCA9IDANCiAgICAgICAgbi5tYXAocnNDaGFyZ2UgPT4gew0KICAgICAgICAgIGlmIChyc0NoYXJnZS5kbkN1cnJlbmN5Q29kZSA9PT0gIlVTRCIpIHsNCiAgICAgICAgICAgIHBheWFibGVVU0QgPSBjdXJyZW5jeShwYXlhYmxlVVNEKS5hZGQoY3VycmVuY3kocnNDaGFyZ2Uuc3VidG90YWwpLmRpdmlkZShjdXJyZW5jeSgxKS5hZGQoY3VycmVuY3kocnNDaGFyZ2UuZHV0eVJhdGUpLmRpdmlkZSgxMDApKSkpLnZhbHVlDQogICAgICAgICAgICBwYXlhYmxlVVNEVGF4ID0gY3VycmVuY3kocGF5YWJsZVVTRFRheCkuYWRkKHJzQ2hhcmdlLnN1YnRvdGFsKS52YWx1ZQ0KICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICBwYXlhYmxlUk1CID0gY3VycmVuY3kocGF5YWJsZVJNQikuYWRkKGN1cnJlbmN5KHJzQ2hhcmdlLnN1YnRvdGFsKS5kaXZpZGUoY3VycmVuY3koMSkuYWRkKGN1cnJlbmN5KHJzQ2hhcmdlLmR1dHlSYXRlKS5kaXZpZGUoMTAwKSkpKS52YWx1ZQ0KICAgICAgICAgICAgcGF5YWJsZVJNQlRheCA9IGN1cnJlbmN5KHBheWFibGVSTUJUYXgpLmFkZChyc0NoYXJnZS5zdWJ0b3RhbCkudmFsdWUNCiAgICAgICAgICB9DQogICAgICAgIH0pDQogICAgICAgIHJzT3BTZXJ2aWNlLnBheWFibGVSTUIgPSBwYXlhYmxlUk1CDQogICAgICAgIHJzT3BTZXJ2aWNlLnBheWFibGVVU0QgPSBwYXlhYmxlVVNEDQogICAgICAgIHJzT3BTZXJ2aWNlLnBheWFibGVSTUJUYXggPSBwYXlhYmxlUk1CVGF4DQogICAgICAgIHJzT3BTZXJ2aWNlLnBheWFibGVVU0RUYXggPSBwYXlhYmxlVVNEVGF4DQoNCiAgICAgICAgdGhpcy5yc0NsaWVudE1lc3NhZ2VDaGFyZ2UodGhpcy5yc0NsaWVudE1lc3NhZ2UucnNDaGFyZ2VMaXN0KQ0KICAgICAgfQ0KICAgICAgaWYgKHNlcnZpY2VUeXBlSWQgPT09IDIxKSB7DQogICAgICAgIGxldCBwYXlhYmxlUk1CID0gMA0KICAgICAgICBsZXQgcGF5YWJsZVJNQlRheCA9IDANCiAgICAgICAgbGV0IHBheWFibGVVU0QgPSAwDQogICAgICAgIGxldCBwYXlhYmxlVVNEVGF4ID0gMA0KICAgICAgICBuLm1hcChyc0NoYXJnZSA9PiB7DQogICAgICAgICAgaWYgKHJzQ2hhcmdlLmRuQ3VycmVuY3lDb2RlID09PSAiVVNEIikgew0KICAgICAgICAgICAgcGF5YWJsZVVTRCA9IGN1cnJlbmN5KHBheWFibGVVU0QpLmFkZChjdXJyZW5jeShyc0NoYXJnZS5zdWJ0b3RhbCkuZGl2aWRlKGN1cnJlbmN5KDEpLmFkZChjdXJyZW5jeShyc0NoYXJnZS5kdXR5UmF0ZSkuZGl2aWRlKDEwMCkpKSkudmFsdWUNCiAgICAgICAgICAgIHBheWFibGVVU0RUYXggPSBjdXJyZW5jeShwYXlhYmxlVVNEVGF4KS5hZGQocnNDaGFyZ2Uuc3VidG90YWwpLnZhbHVlDQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIHBheWFibGVSTUIgPSBjdXJyZW5jeShwYXlhYmxlUk1CKS5hZGQoY3VycmVuY3kocnNDaGFyZ2Uuc3VidG90YWwpLmRpdmlkZShjdXJyZW5jeSgxKS5hZGQoY3VycmVuY3kocnNDaGFyZ2UuZHV0eVJhdGUpLmRpdmlkZSgxMDApKSkpLnZhbHVlDQogICAgICAgICAgICBwYXlhYmxlUk1CVGF4ID0gY3VycmVuY3kocGF5YWJsZVJNQlRheCkuYWRkKHJzQ2hhcmdlLnN1YnRvdGFsKS52YWx1ZQ0KICAgICAgICAgIH0NCiAgICAgICAgfSkNCiAgICAgICAgcnNPcFNlcnZpY2UucGF5YWJsZVJNQiA9IHBheWFibGVSTUINCiAgICAgICAgcnNPcFNlcnZpY2UucGF5YWJsZVVTRCA9IHBheWFibGVVU0QNCiAgICAgICAgcnNPcFNlcnZpY2UucGF5YWJsZVJNQlRheCA9IHBheWFibGVSTUJUYXgNCiAgICAgICAgcnNPcFNlcnZpY2UucGF5YWJsZVVTRFRheCA9IHBheWFibGVVU0RUYXgNCg0KICAgICAgICB0aGlzLnJzQ2xpZW50TWVzc2FnZUNoYXJnZSh0aGlzLnJzQ2xpZW50TWVzc2FnZS5yc0NoYXJnZUxpc3QpDQogICAgICB9DQogICAgICBpZiAoc2VydmljZVR5cGVJZCA9PT0gNDApIHsNCiAgICAgICAgbGV0IHBheWFibGVSTUIgPSAwDQogICAgICAgIGxldCBwYXlhYmxlUk1CVGF4ID0gMA0KICAgICAgICBsZXQgcGF5YWJsZVVTRCA9IDANCiAgICAgICAgbGV0IHBheWFibGVVU0RUYXggPSAwDQogICAgICAgIG4ubWFwKHJzQ2hhcmdlID0+IHsNCiAgICAgICAgICBpZiAocnNDaGFyZ2UuZG5DdXJyZW5jeUNvZGUgPT09ICJVU0QiKSB7DQogICAgICAgICAgICBwYXlhYmxlVVNEID0gY3VycmVuY3kocGF5YWJsZVVTRCkuYWRkKGN1cnJlbmN5KHJzQ2hhcmdlLnN1YnRvdGFsKS5kaXZpZGUoY3VycmVuY3koMSkuYWRkKGN1cnJlbmN5KHJzQ2hhcmdlLmR1dHlSYXRlKS5kaXZpZGUoMTAwKSkpKS52YWx1ZQ0KICAgICAgICAgICAgcGF5YWJsZVVTRFRheCA9IGN1cnJlbmN5KHBheWFibGVVU0RUYXgpLmFkZChyc0NoYXJnZS5zdWJ0b3RhbCkudmFsdWUNCiAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgcGF5YWJsZVJNQiA9IGN1cnJlbmN5KHBheWFibGVSTUIpLmFkZChjdXJyZW5jeShyc0NoYXJnZS5zdWJ0b3RhbCkuZGl2aWRlKGN1cnJlbmN5KDEpLmFkZChjdXJyZW5jeShyc0NoYXJnZS5kdXR5UmF0ZSkuZGl2aWRlKDEwMCkpKSkudmFsdWUNCiAgICAgICAgICAgIHBheWFibGVSTUJUYXggPSBjdXJyZW5jeShwYXlhYmxlUk1CVGF4KS5hZGQocnNDaGFyZ2Uuc3VidG90YWwpLnZhbHVlDQogICAgICAgICAgfQ0KICAgICAgICB9KQ0KICAgICAgICByc09wU2VydmljZS5wYXlhYmxlUk1CID0gcGF5YWJsZVJNQg0KICAgICAgICByc09wU2VydmljZS5wYXlhYmxlVVNEID0gcGF5YWJsZVVTRA0KICAgICAgICByc09wU2VydmljZS5wYXlhYmxlUk1CVGF4ID0gcGF5YWJsZVJNQlRheA0KICAgICAgICByc09wU2VydmljZS5wYXlhYmxlVVNEVGF4ID0gcGF5YWJsZVVTRFRheA0KDQogICAgICAgIHRoaXMucnNDbGllbnRNZXNzYWdlQ2hhcmdlKHRoaXMucnNDbGllbnRNZXNzYWdlLnJzQ2hhcmdlTGlzdCkNCiAgICAgIH0NCiAgICAgIGlmIChzZXJ2aWNlVHlwZUlkID09PSA1MCkgew0KICAgICAgICBsZXQgcGF5YWJsZVJNQiA9IDANCiAgICAgICAgbGV0IHBheWFibGVSTUJUYXggPSAwDQogICAgICAgIGxldCBwYXlhYmxlVVNEID0gMA0KICAgICAgICBsZXQgcGF5YWJsZVVTRFRheCA9IDANCiAgICAgICAgbi5tYXAocnNDaGFyZ2UgPT4gew0KICAgICAgICAgIGlmIChyc0NoYXJnZS5kbkN1cnJlbmN5Q29kZSA9PT0gIlVTRCIpIHsNCiAgICAgICAgICAgIHBheWFibGVVU0QgPSBjdXJyZW5jeShwYXlhYmxlVVNEKS5hZGQoY3VycmVuY3kocnNDaGFyZ2Uuc3VidG90YWwpLmRpdmlkZShjdXJyZW5jeSgxKS5hZGQoY3VycmVuY3kocnNDaGFyZ2UuZHV0eVJhdGUpLmRpdmlkZSgxMDApKSkpLnZhbHVlDQogICAgICAgICAgICBwYXlhYmxlVVNEVGF4ID0gY3VycmVuY3kocGF5YWJsZVVTRFRheCkuYWRkKHJzQ2hhcmdlLnN1YnRvdGFsKS52YWx1ZQ0KICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICBwYXlhYmxlUk1CID0gY3VycmVuY3kocGF5YWJsZVJNQikuYWRkKGN1cnJlbmN5KHJzQ2hhcmdlLnN1YnRvdGFsKS5kaXZpZGUoY3VycmVuY3koMSkuYWRkKGN1cnJlbmN5KHJzQ2hhcmdlLmR1dHlSYXRlKS5kaXZpZGUoMTAwKSkpKS52YWx1ZQ0KICAgICAgICAgICAgcGF5YWJsZVJNQlRheCA9IGN1cnJlbmN5KHBheWFibGVSTUJUYXgpLmFkZChyc0NoYXJnZS5zdWJ0b3RhbCkudmFsdWUNCiAgICAgICAgICB9DQogICAgICAgIH0pDQogICAgICAgIHJzT3BTZXJ2aWNlLnBheWFibGVSTUIgPSBwYXlhYmxlUk1CDQogICAgICAgIHJzT3BTZXJ2aWNlLnBheWFibGVVU0QgPSBwYXlhYmxlVVNEDQogICAgICAgIHJzT3BTZXJ2aWNlLnBheWFibGVSTUJUYXggPSBwYXlhYmxlUk1CVGF4DQogICAgICAgIHJzT3BTZXJ2aWNlLnBheWFibGVVU0RUYXggPSBwYXlhYmxlVVNEVGF4DQoNCiAgICAgICAgdGhpcy5yc0NsaWVudE1lc3NhZ2VDaGFyZ2UodGhpcy5yc0NsaWVudE1lc3NhZ2UucnNDaGFyZ2VMaXN0KQ0KICAgICAgfQ0KICAgICAgaWYgKHNlcnZpY2VUeXBlSWQgPT09IDUxKSB7DQogICAgICAgIGxldCBwYXlhYmxlUk1CID0gMA0KICAgICAgICBsZXQgcGF5YWJsZVJNQlRheCA9IDANCiAgICAgICAgbGV0IHBheWFibGVVU0QgPSAwDQogICAgICAgIGxldCBwYXlhYmxlVVNEVGF4ID0gMA0KICAgICAgICBuLm1hcChyc0NoYXJnZSA9PiB7DQogICAgICAgICAgaWYgKHJzQ2hhcmdlLmRuQ3VycmVuY3lDb2RlID09PSAiVVNEIikgew0KICAgICAgICAgICAgcGF5YWJsZVVTRCA9IGN1cnJlbmN5KHBheWFibGVVU0QpLmFkZChjdXJyZW5jeShyc0NoYXJnZS5zdWJ0b3RhbCkuZGl2aWRlKGN1cnJlbmN5KDEpLmFkZChjdXJyZW5jeShyc0NoYXJnZS5kdXR5UmF0ZSkuZGl2aWRlKDEwMCkpKSkudmFsdWUNCiAgICAgICAgICAgIHBheWFibGVVU0RUYXggPSBjdXJyZW5jeShwYXlhYmxlVVNEVGF4KS5hZGQocnNDaGFyZ2Uuc3VidG90YWwpLnZhbHVlDQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIHBheWFibGVSTUIgPSBjdXJyZW5jeShwYXlhYmxlUk1CKS5hZGQoY3VycmVuY3kocnNDaGFyZ2Uuc3VidG90YWwpLmRpdmlkZShjdXJyZW5jeSgxKS5hZGQoY3VycmVuY3kocnNDaGFyZ2UuZHV0eVJhdGUpLmRpdmlkZSgxMDApKSkpLnZhbHVlDQogICAgICAgICAgICBwYXlhYmxlUk1CVGF4ID0gY3VycmVuY3kocGF5YWJsZVJNQlRheCkuYWRkKHJzQ2hhcmdlLnN1YnRvdGFsKS52YWx1ZQ0KICAgICAgICAgIH0NCiAgICAgICAgfSkNCiAgICAgICAgcnNPcFNlcnZpY2UucGF5YWJsZVJNQiA9IHBheWFibGVSTUINCiAgICAgICAgcnNPcFNlcnZpY2UucGF5YWJsZVVTRCA9IHBheWFibGVVU0QNCiAgICAgICAgcnNPcFNlcnZpY2UucGF5YWJsZVJNQlRheCA9IHBheWFibGVSTUJUYXgNCiAgICAgICAgcnNPcFNlcnZpY2UucGF5YWJsZVVTRFRheCA9IHBheWFibGVVU0RUYXgNCg0KICAgICAgICB0aGlzLnJzQ2xpZW50TWVzc2FnZUNoYXJnZSh0aGlzLnJzQ2xpZW50TWVzc2FnZS5yc0NoYXJnZUxpc3QpDQogICAgICB9DQogICAgICBpZiAoc2VydmljZVR5cGVJZCA9PT0gNjApIHsNCiAgICAgICAgbGV0IHBheWFibGVSTUIgPSAwDQogICAgICAgIGxldCBwYXlhYmxlUk1CVGF4ID0gMA0KICAgICAgICBsZXQgcGF5YWJsZVVTRCA9IDANCiAgICAgICAgbGV0IHBheWFibGVVU0RUYXggPSAwDQogICAgICAgIG4ubWFwKHJzQ2hhcmdlID0+IHsNCiAgICAgICAgICBpZiAocnNDaGFyZ2UuZG5DdXJyZW5jeUNvZGUgPT09ICJVU0QiKSB7DQogICAgICAgICAgICBwYXlhYmxlVVNEID0gY3VycmVuY3kocGF5YWJsZVVTRCkuYWRkKGN1cnJlbmN5KHJzQ2hhcmdlLnN1YnRvdGFsKS5kaXZpZGUoY3VycmVuY3koMSkuYWRkKGN1cnJlbmN5KHJzQ2hhcmdlLmR1dHlSYXRlKS5kaXZpZGUoMTAwKSkpKS52YWx1ZQ0KICAgICAgICAgICAgcGF5YWJsZVVTRFRheCA9IGN1cnJlbmN5KHBheWFibGVVU0RUYXgpLmFkZChyc0NoYXJnZS5zdWJ0b3RhbCkudmFsdWUNCiAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgcGF5YWJsZVJNQiA9IGN1cnJlbmN5KHBheWFibGVSTUIpLmFkZChjdXJyZW5jeShyc0NoYXJnZS5zdWJ0b3RhbCkuZGl2aWRlKGN1cnJlbmN5KDEpLmFkZChjdXJyZW5jeShyc0NoYXJnZS5kdXR5UmF0ZSkuZGl2aWRlKDEwMCkpKSkudmFsdWUNCiAgICAgICAgICAgIHBheWFibGVSTUJUYXggPSBjdXJyZW5jeShwYXlhYmxlUk1CVGF4KS5hZGQocnNDaGFyZ2Uuc3VidG90YWwpLnZhbHVlDQogICAgICAgICAgfQ0KICAgICAgICB9KQ0KICAgICAgICByc09wU2VydmljZS5wYXlhYmxlUk1CID0gcGF5YWJsZVJNQg0KICAgICAgICByc09wU2VydmljZS5wYXlhYmxlVVNEID0gcGF5YWJsZVVTRA0KICAgICAgICByc09wU2VydmljZS5wYXlhYmxlUk1CVGF4ID0gcGF5YWJsZVJNQlRheA0KICAgICAgICByc09wU2VydmljZS5wYXlhYmxlVVNEVGF4ID0gcGF5YWJsZVVTRFRheA0KDQogICAgICAgIHRoaXMucnNDbGllbnRNZXNzYWdlQ2hhcmdlKHRoaXMucnNDbGllbnRNZXNzYWdlLnJzQ2hhcmdlTGlzdCkNCiAgICAgIH0NCiAgICAgIGlmIChzZXJ2aWNlVHlwZUlkID09PSA2MSkgew0KICAgICAgICBsZXQgcGF5YWJsZVJNQiA9IDANCiAgICAgICAgbGV0IHBheWFibGVSTUJUYXggPSAwDQogICAgICAgIGxldCBwYXlhYmxlVVNEID0gMA0KICAgICAgICBsZXQgcGF5YWJsZVVTRFRheCA9IDANCiAgICAgICAgbi5tYXAocnNDaGFyZ2UgPT4gew0KICAgICAgICAgIGlmIChyc0NoYXJnZS5kbkN1cnJlbmN5Q29kZSA9PT0gIlVTRCIpIHsNCiAgICAgICAgICAgIHBheWFibGVVU0QgPSBjdXJyZW5jeShwYXlhYmxlVVNEKS5hZGQoY3VycmVuY3kocnNDaGFyZ2Uuc3VidG90YWwpLmRpdmlkZShjdXJyZW5jeSgxKS5hZGQoY3VycmVuY3kocnNDaGFyZ2UuZHV0eVJhdGUpLmRpdmlkZSgxMDApKSkpLnZhbHVlDQogICAgICAgICAgICBwYXlhYmxlVVNEVGF4ID0gY3VycmVuY3kocGF5YWJsZVVTRFRheCkuYWRkKHJzQ2hhcmdlLnN1YnRvdGFsKS52YWx1ZQ0KICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICBwYXlhYmxlUk1CID0gY3VycmVuY3kocGF5YWJsZVJNQikuYWRkKGN1cnJlbmN5KHJzQ2hhcmdlLnN1YnRvdGFsKS5kaXZpZGUoY3VycmVuY3koMSkuYWRkKGN1cnJlbmN5KHJzQ2hhcmdlLmR1dHlSYXRlKS5kaXZpZGUoMTAwKSkpKS52YWx1ZQ0KICAgICAgICAgICAgcGF5YWJsZVJNQlRheCA9IGN1cnJlbmN5KHBheWFibGVSTUJUYXgpLmFkZChyc0NoYXJnZS5zdWJ0b3RhbCkudmFsdWUNCiAgICAgICAgICB9DQogICAgICAgIH0pDQogICAgICAgIHJzT3BTZXJ2aWNlLnBheWFibGVSTUIgPSBwYXlhYmxlUk1CDQogICAgICAgIHJzT3BTZXJ2aWNlLnBheWFibGVVU0QgPSBwYXlhYmxlVVNEDQogICAgICAgIHJzT3BTZXJ2aWNlLnBheWFibGVSTUJUYXggPSBwYXlhYmxlUk1CVGF4DQogICAgICAgIHJzT3BTZXJ2aWNlLnBheWFibGVVU0RUYXggPSBwYXlhYmxlVVNEVGF4DQoNCiAgICAgICAgdGhpcy5yc0NsaWVudE1lc3NhZ2VDaGFyZ2UodGhpcy5yc0NsaWVudE1lc3NhZ2UucnNDaGFyZ2VMaXN0KQ0KICAgICAgfQ0KICAgICAgaWYgKHNlcnZpY2VUeXBlSWQgPT09IDcwKSB7DQogICAgICAgIGxldCBwYXlhYmxlUk1CID0gMA0KICAgICAgICBsZXQgcGF5YWJsZVJNQlRheCA9IDANCiAgICAgICAgbGV0IHBheWFibGVVU0QgPSAwDQogICAgICAgIGxldCBwYXlhYmxlVVNEVGF4ID0gMA0KICAgICAgICBuLm1hcChyc0NoYXJnZSA9PiB7DQogICAgICAgICAgaWYgKHJzQ2hhcmdlLmRuQ3VycmVuY3lDb2RlID09PSAiVVNEIikgew0KICAgICAgICAgICAgcGF5YWJsZVVTRCA9IGN1cnJlbmN5KHBheWFibGVVU0QpLmFkZChjdXJyZW5jeShyc0NoYXJnZS5zdWJ0b3RhbCkuZGl2aWRlKGN1cnJlbmN5KDEpLmFkZChjdXJyZW5jeShyc0NoYXJnZS5kdXR5UmF0ZSkuZGl2aWRlKDEwMCkpKSkudmFsdWUNCiAgICAgICAgICAgIHBheWFibGVVU0RUYXggPSBjdXJyZW5jeShwYXlhYmxlVVNEVGF4KS5hZGQocnNDaGFyZ2Uuc3VidG90YWwpLnZhbHVlDQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIHBheWFibGVSTUIgPSBjdXJyZW5jeShwYXlhYmxlUk1CKS5hZGQoY3VycmVuY3kocnNDaGFyZ2Uuc3VidG90YWwpLmRpdmlkZShjdXJyZW5jeSgxKS5hZGQoY3VycmVuY3kocnNDaGFyZ2UuZHV0eVJhdGUpLmRpdmlkZSgxMDApKSkpLnZhbHVlDQogICAgICAgICAgICBwYXlhYmxlUk1CVGF4ID0gY3VycmVuY3kocGF5YWJsZVJNQlRheCkuYWRkKHJzQ2hhcmdlLnN1YnRvdGFsKS52YWx1ZQ0KICAgICAgICAgIH0NCiAgICAgICAgfSkNCiAgICAgICAgcnNPcFNlcnZpY2UucGF5YWJsZVJNQiA9IHBheWFibGVSTUINCiAgICAgICAgcnNPcFNlcnZpY2UucGF5YWJsZVVTRCA9IHBheWFibGVVU0QNCiAgICAgICAgcnNPcFNlcnZpY2UucGF5YWJsZVJNQlRheCA9IHBheWFibGVSTUJUYXgNCiAgICAgICAgcnNPcFNlcnZpY2UucGF5YWJsZVVTRFRheCA9IHBheWFibGVVU0RUYXgNCg0KICAgICAgICB0aGlzLnJzQ2xpZW50TWVzc2FnZUNoYXJnZSh0aGlzLnJzQ2xpZW50TWVzc2FnZS5yc0NoYXJnZUxpc3QpDQogICAgICB9DQogICAgICBpZiAoc2VydmljZVR5cGVJZCA9PT0gNzEpIHsNCiAgICAgICAgbGV0IHBheWFibGVSTUIgPSAwDQogICAgICAgIGxldCBwYXlhYmxlUk1CVGF4ID0gMA0KICAgICAgICBsZXQgcGF5YWJsZVVTRCA9IDANCiAgICAgICAgbGV0IHBheWFibGVVU0RUYXggPSAwDQogICAgICAgIG4ubWFwKHJzQ2hhcmdlID0+IHsNCiAgICAgICAgICBpZiAocnNDaGFyZ2UuZG5DdXJyZW5jeUNvZGUgPT09ICJVU0QiKSB7DQogICAgICAgICAgICBwYXlhYmxlVVNEID0gY3VycmVuY3kocGF5YWJsZVVTRCkuYWRkKGN1cnJlbmN5KHJzQ2hhcmdlLnN1YnRvdGFsKS5kaXZpZGUoY3VycmVuY3koMSkuYWRkKGN1cnJlbmN5KHJzQ2hhcmdlLmR1dHlSYXRlKS5kaXZpZGUoMTAwKSkpKS52YWx1ZQ0KICAgICAgICAgICAgcGF5YWJsZVVTRFRheCA9IGN1cnJlbmN5KHBheWFibGVVU0RUYXgpLmFkZChyc0NoYXJnZS5zdWJ0b3RhbCkudmFsdWUNCiAgICAgICAgICB9IGVsc2Ugew0KICAgICAgICAgICAgcGF5YWJsZVJNQiA9IGN1cnJlbmN5KHBheWFibGVSTUIpLmFkZChjdXJyZW5jeShyc0NoYXJnZS5zdWJ0b3RhbCkuZGl2aWRlKGN1cnJlbmN5KDEpLmFkZChjdXJyZW5jeShyc0NoYXJnZS5kdXR5UmF0ZSkuZGl2aWRlKDEwMCkpKSkudmFsdWUNCiAgICAgICAgICAgIHBheWFibGVSTUJUYXggPSBjdXJyZW5jeShwYXlhYmxlUk1CVGF4KS5hZGQocnNDaGFyZ2Uuc3VidG90YWwpLnZhbHVlDQogICAgICAgICAgfQ0KICAgICAgICB9KQ0KICAgICAgICByc09wU2VydmljZS5wYXlhYmxlUk1CID0gcGF5YWJsZVJNQg0KICAgICAgICByc09wU2VydmljZS5wYXlhYmxlVVNEID0gcGF5YWJsZVVTRA0KICAgICAgICByc09wU2VydmljZS5wYXlhYmxlUk1CVGF4ID0gcGF5YWJsZVJNQlRheA0KICAgICAgICByc09wU2VydmljZS5wYXlhYmxlVVNEVGF4ID0gcGF5YWJsZVVTRFRheA0KDQogICAgICAgIHRoaXMucnNDbGllbnRNZXNzYWdlQ2hhcmdlKHRoaXMucnNDbGllbnRNZXNzYWdlLnJzQ2hhcmdlTGlzdCkNCiAgICAgIH0NCiAgICAgIGlmIChzZXJ2aWNlVHlwZUlkID09PSA4MCkgew0KICAgICAgICBsZXQgcGF5YWJsZVJNQiA9IDANCiAgICAgICAgbGV0IHBheWFibGVSTUJUYXggPSAwDQogICAgICAgIGxldCBwYXlhYmxlVVNEID0gMA0KICAgICAgICBsZXQgcGF5YWJsZVVTRFRheCA9IDANCiAgICAgICAgbi5tYXAocnNDaGFyZ2UgPT4gew0KICAgICAgICAgIGlmIChyc0NoYXJnZS5kbkN1cnJlbmN5Q29kZSA9PT0gIlVTRCIpIHsNCiAgICAgICAgICAgIHBheWFibGVVU0QgPSBjdXJyZW5jeShwYXlhYmxlVVNEKS5hZGQoY3VycmVuY3kocnNDaGFyZ2Uuc3VidG90YWwpLmRpdmlkZShjdXJyZW5jeSgxKS5hZGQoY3VycmVuY3kocnNDaGFyZ2UuZHV0eVJhdGUpLmRpdmlkZSgxMDApKSkpLnZhbHVlDQogICAgICAgICAgICBwYXlhYmxlVVNEVGF4ID0gY3VycmVuY3kocGF5YWJsZVVTRFRheCkuYWRkKHJzQ2hhcmdlLnN1YnRvdGFsKS52YWx1ZQ0KICAgICAgICAgIH0gZWxzZSB7DQogICAgICAgICAgICBwYXlhYmxlUk1CID0gY3VycmVuY3kocGF5YWJsZVJNQikuYWRkKGN1cnJlbmN5KHJzQ2hhcmdlLnN1YnRvdGFsKS5kaXZpZGUoY3VycmVuY3koMSkuYWRkKGN1cnJlbmN5KHJzQ2hhcmdlLmR1dHlSYXRlKS5kaXZpZGUoMTAwKSkpKS52YWx1ZQ0KICAgICAgICAgICAgcGF5YWJsZVJNQlRheCA9IGN1cnJlbmN5KHBheWFibGVSTUJUYXgpLmFkZChyc0NoYXJnZS5zdWJ0b3RhbCkudmFsdWUNCiAgICAgICAgICB9DQogICAgICAgIH0pDQogICAgICAgIHJzT3BTZXJ2aWNlLnBheWFibGVSTUIgPSBwYXlhYmxlUk1CDQogICAgICAgIHJzT3BTZXJ2aWNlLnBheWFibGVVU0QgPSBwYXlhYmxlVVNEDQogICAgICAgIHJzT3BTZXJ2aWNlLnBheWFibGVSTUJUYXggPSBwYXlhYmxlUk1CVGF4DQogICAgICAgIHJzT3BTZXJ2aWNlLnBheWFibGVVU0RUYXggPSBwYXlhYmxlVVNEVGF4DQoNCiAgICAgICAgdGhpcy5yc0NsaWVudE1lc3NhZ2VDaGFyZ2UodGhpcy5yc0NsaWVudE1lc3NhZ2UucnNDaGFyZ2VMaXN0KQ0KICAgICAgfQ0KICAgICAgaWYgKHNlcnZpY2VUeXBlSWQgPT09IDkwKSB7DQogICAgICAgIGxldCBwYXlhYmxlUk1CID0gMA0KICAgICAgICBsZXQgcGF5YWJsZVJNQlRheCA9IDANCiAgICAgICAgbGV0IHBheWFibGVVU0QgPSAwDQogICAgICAgIGxldCBwYXlhYmxlVVNEVGF4ID0gMA0KICAgICAgICBuLm1hcChyc0NoYXJnZSA9PiB7DQogICAgICAgICAgaWYgKHJzQ2hhcmdlLmRuQ3VycmVuY3lDb2RlID09PSAiVVNEIikgew0KICAgICAgICAgICAgcGF5YWJsZVVTRCA9IGN1cnJlbmN5KHBheWFibGVVU0QpLmFkZChjdXJyZW5jeShyc0NoYXJnZS5zdWJ0b3RhbCkuZGl2aWRlKGN1cnJlbmN5KDEpLmFkZChjdXJyZW5jeShyc0NoYXJnZS5kdXR5UmF0ZSkuZGl2aWRlKDEwMCkpKSkudmFsdWUNCiAgICAgICAgICAgIHBheWFibGVVU0RUYXggPSBjdXJyZW5jeShwYXlhYmxlVVNEVGF4KS5hZGQocnNDaGFyZ2Uuc3VidG90YWwpLnZhbHVlDQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIHBheWFibGVSTUIgPSBjdXJyZW5jeShwYXlhYmxlUk1CKS5hZGQoY3VycmVuY3kocnNDaGFyZ2Uuc3VidG90YWwpLmRpdmlkZShjdXJyZW5jeSgxKS5hZGQoY3VycmVuY3kocnNDaGFyZ2UuZHV0eVJhdGUpLmRpdmlkZSgxMDApKSkpLnZhbHVlDQogICAgICAgICAgICBwYXlhYmxlUk1CVGF4ID0gY3VycmVuY3kocGF5YWJsZVJNQlRheCkuYWRkKHJzQ2hhcmdlLnN1YnRvdGFsKS52YWx1ZQ0KICAgICAgICAgIH0NCiAgICAgICAgfSkNCiAgICAgICAgcnNPcFNlcnZpY2UucGF5YWJsZVJNQiA9IHBheWFibGVSTUINCiAgICAgICAgcnNPcFNlcnZpY2UucGF5YWJsZVVTRCA9IHBheWFibGVVU0QNCiAgICAgICAgcnNPcFNlcnZpY2UucGF5YWJsZVJNQlRheCA9IHBheWFibGVSTUJUYXgNCiAgICAgICAgcnNPcFNlcnZpY2UucGF5YWJsZVVTRFRheCA9IHBheWFibGVVU0RUYXgNCg0KICAgICAgICB0aGlzLnJzQ2xpZW50TWVzc2FnZUNoYXJnZSh0aGlzLnJzQ2xpZW50TWVzc2FnZS5yc0NoYXJnZUxpc3QpDQogICAgICB9DQogICAgICBpZiAoc2VydmljZVR5cGVJZCA9PT0gMTAwKSB7DQogICAgICAgIGxldCBwYXlhYmxlUk1CID0gMA0KICAgICAgICBsZXQgcGF5YWJsZVJNQlRheCA9IDANCiAgICAgICAgbGV0IHBheWFibGVVU0QgPSAwDQogICAgICAgIGxldCBwYXlhYmxlVVNEVGF4ID0gMA0KICAgICAgICBuLm1hcChyc0NoYXJnZSA9PiB7DQogICAgICAgICAgaWYgKHJzQ2hhcmdlLmRuQ3VycmVuY3lDb2RlID09PSAiVVNEIikgew0KICAgICAgICAgICAgcGF5YWJsZVVTRCA9IGN1cnJlbmN5KHBheWFibGVVU0QpLmFkZChjdXJyZW5jeShyc0NoYXJnZS5zdWJ0b3RhbCkuZGl2aWRlKGN1cnJlbmN5KDEpLmFkZChjdXJyZW5jeShyc0NoYXJnZS5kdXR5UmF0ZSkuZGl2aWRlKDEwMCkpKSkudmFsdWUNCiAgICAgICAgICAgIHBheWFibGVVU0RUYXggPSBjdXJyZW5jeShwYXlhYmxlVVNEVGF4KS5hZGQocnNDaGFyZ2Uuc3VidG90YWwpLnZhbHVlDQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIHBheWFibGVSTUIgPSBjdXJyZW5jeShwYXlhYmxlUk1CKS5hZGQoY3VycmVuY3kocnNDaGFyZ2Uuc3VidG90YWwpLmRpdmlkZShjdXJyZW5jeSgxKS5hZGQoY3VycmVuY3kocnNDaGFyZ2UuZHV0eVJhdGUpLmRpdmlkZSgxMDApKSkpLnZhbHVlDQogICAgICAgICAgICBwYXlhYmxlUk1CVGF4ID0gY3VycmVuY3kocGF5YWJsZVJNQlRheCkuYWRkKHJzQ2hhcmdlLnN1YnRvdGFsKS52YWx1ZQ0KICAgICAgICAgIH0NCiAgICAgICAgfSkNCiAgICAgICAgcnNPcFNlcnZpY2UucGF5YWJsZVJNQiA9IHBheWFibGVSTUINCiAgICAgICAgcnNPcFNlcnZpY2UucGF5YWJsZVVTRCA9IHBheWFibGVVU0QNCiAgICAgICAgcnNPcFNlcnZpY2UucGF5YWJsZVJNQlRheCA9IHBheWFibGVSTUJUYXgNCiAgICAgICAgcnNPcFNlcnZpY2UucGF5YWJsZVVTRFRheCA9IHBheWFibGVVU0RUYXgNCg0KICAgICAgICB0aGlzLnJzQ2xpZW50TWVzc2FnZUNoYXJnZSh0aGlzLnJzQ2xpZW50TWVzc2FnZS5yc0NoYXJnZUxpc3QpDQogICAgICB9DQogICAgICBpZiAoc2VydmljZVR5cGVJZCA9PT0gMTAxKSB7DQogICAgICAgIGxldCBwYXlhYmxlUk1CID0gMA0KICAgICAgICBsZXQgcGF5YWJsZVJNQlRheCA9IDANCiAgICAgICAgbGV0IHBheWFibGVVU0QgPSAwDQogICAgICAgIGxldCBwYXlhYmxlVVNEVGF4ID0gMA0KICAgICAgICBuLm1hcChyc0NoYXJnZSA9PiB7DQogICAgICAgICAgaWYgKHJzQ2hhcmdlLmRuQ3VycmVuY3lDb2RlID09PSAiVVNEIikgew0KICAgICAgICAgICAgcGF5YWJsZVVTRCA9IGN1cnJlbmN5KHBheWFibGVVU0QpLmFkZChjdXJyZW5jeShyc0NoYXJnZS5zdWJ0b3RhbCkuZGl2aWRlKGN1cnJlbmN5KDEpLmFkZChjdXJyZW5jeShyc0NoYXJnZS5kdXR5UmF0ZSkuZGl2aWRlKDEwMCkpKSkudmFsdWUNCiAgICAgICAgICAgIHBheWFibGVVU0RUYXggPSBjdXJyZW5jeShwYXlhYmxlVVNEVGF4KS5hZGQocnNDaGFyZ2Uuc3VidG90YWwpLnZhbHVlDQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIHBheWFibGVSTUIgPSBjdXJyZW5jeShwYXlhYmxlUk1CKS5hZGQoY3VycmVuY3kocnNDaGFyZ2Uuc3VidG90YWwpLmRpdmlkZShjdXJyZW5jeSgxKS5hZGQoY3VycmVuY3kocnNDaGFyZ2UuZHV0eVJhdGUpLmRpdmlkZSgxMDApKSkpLnZhbHVlDQogICAgICAgICAgICBwYXlhYmxlUk1CVGF4ID0gY3VycmVuY3kocGF5YWJsZVJNQlRheCkuYWRkKHJzQ2hhcmdlLnN1YnRvdGFsKS52YWx1ZQ0KICAgICAgICAgIH0NCiAgICAgICAgfSkNCiAgICAgICAgcnNPcFNlcnZpY2UucGF5YWJsZVJNQiA9IHBheWFibGVSTUINCiAgICAgICAgcnNPcFNlcnZpY2UucGF5YWJsZVVTRCA9IHBheWFibGVVU0QNCiAgICAgICAgcnNPcFNlcnZpY2UucGF5YWJsZVJNQlRheCA9IHBheWFibGVSTUJUYXgNCiAgICAgICAgcnNPcFNlcnZpY2UucGF5YWJsZVVTRFRheCA9IHBheWFibGVVU0RUYXgNCg0KICAgICAgICB0aGlzLnJzQ2xpZW50TWVzc2FnZUNoYXJnZSh0aGlzLnJzQ2xpZW50TWVzc2FnZS5yc0NoYXJnZUxpc3QpDQogICAgICB9DQogICAgICBpZiAoc2VydmljZVR5cGVJZCA9PT0gMTAyKSB7DQogICAgICAgIGxldCBwYXlhYmxlUk1CID0gMA0KICAgICAgICBsZXQgcGF5YWJsZVJNQlRheCA9IDANCiAgICAgICAgbGV0IHBheWFibGVVU0QgPSAwDQogICAgICAgIGxldCBwYXlhYmxlVVNEVGF4ID0gMA0KICAgICAgICBuLm1hcChyc0NoYXJnZSA9PiB7DQogICAgICAgICAgaWYgKHJzQ2hhcmdlLmRuQ3VycmVuY3lDb2RlID09PSAiVVNEIikgew0KICAgICAgICAgICAgcGF5YWJsZVVTRCA9IGN1cnJlbmN5KHBheWFibGVVU0QpLmFkZChjdXJyZW5jeShyc0NoYXJnZS5zdWJ0b3RhbCkuZGl2aWRlKGN1cnJlbmN5KDEpLmFkZChjdXJyZW5jeShyc0NoYXJnZS5kdXR5UmF0ZSkuZGl2aWRlKDEwMCkpKSkudmFsdWUNCiAgICAgICAgICAgIHBheWFibGVVU0RUYXggPSBjdXJyZW5jeShwYXlhYmxlVVNEVGF4KS5hZGQocnNDaGFyZ2Uuc3VidG90YWwpLnZhbHVlDQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIHBheWFibGVSTUIgPSBjdXJyZW5jeShwYXlhYmxlUk1CKS5hZGQoY3VycmVuY3kocnNDaGFyZ2Uuc3VidG90YWwpLmRpdmlkZShjdXJyZW5jeSgxKS5hZGQoY3VycmVuY3kocnNDaGFyZ2UuZHV0eVJhdGUpLmRpdmlkZSgxMDApKSkpLnZhbHVlDQogICAgICAgICAgICBwYXlhYmxlUk1CVGF4ID0gY3VycmVuY3kocGF5YWJsZVJNQlRheCkuYWRkKHJzQ2hhcmdlLnN1YnRvdGFsKS52YWx1ZQ0KICAgICAgICAgIH0NCiAgICAgICAgfSkNCiAgICAgICAgcnNPcFNlcnZpY2UucGF5YWJsZVJNQiA9IHBheWFibGVSTUINCiAgICAgICAgcnNPcFNlcnZpY2UucGF5YWJsZVVTRCA9IHBheWFibGVVU0QNCiAgICAgICAgcnNPcFNlcnZpY2UucGF5YWJsZVJNQlRheCA9IHBheWFibGVSTUJUYXgNCiAgICAgICAgcnNPcFNlcnZpY2UucGF5YWJsZVVTRFRheCA9IHBheWFibGVVU0RUYXgNCg0KICAgICAgICB0aGlzLnJzQ2xpZW50TWVzc2FnZUNoYXJnZSh0aGlzLnJzQ2xpZW50TWVzc2FnZS5yc0NoYXJnZUxpc3QpDQogICAgICB9DQogICAgICBpZiAoc2VydmljZVR5cGVJZCA9PT0gMTAzKSB7DQogICAgICAgIGxldCBwYXlhYmxlUk1CID0gMA0KICAgICAgICBsZXQgcGF5YWJsZVJNQlRheCA9IDANCiAgICAgICAgbGV0IHBheWFibGVVU0QgPSAwDQogICAgICAgIGxldCBwYXlhYmxlVVNEVGF4ID0gMA0KICAgICAgICBuLm1hcChyc0NoYXJnZSA9PiB7DQogICAgICAgICAgaWYgKHJzQ2hhcmdlLmRuQ3VycmVuY3lDb2RlID09PSAiVVNEIikgew0KICAgICAgICAgICAgcGF5YWJsZVVTRCA9IGN1cnJlbmN5KHBheWFibGVVU0QpLmFkZChjdXJyZW5jeShyc0NoYXJnZS5zdWJ0b3RhbCkuZGl2aWRlKGN1cnJlbmN5KDEpLmFkZChjdXJyZW5jeShyc0NoYXJnZS5kdXR5UmF0ZSkuZGl2aWRlKDEwMCkpKSkudmFsdWUNCiAgICAgICAgICAgIHBheWFibGVVU0RUYXggPSBjdXJyZW5jeShwYXlhYmxlVVNEVGF4KS5hZGQocnNDaGFyZ2Uuc3VidG90YWwpLnZhbHVlDQogICAgICAgICAgfSBlbHNlIHsNCiAgICAgICAgICAgIHBheWFibGVSTUIgPSBjdXJyZW5jeShwYXlhYmxlUk1CKS5hZGQoY3VycmVuY3kocnNDaGFyZ2Uuc3VidG90YWwpLmRpdmlkZShjdXJyZW5jeSgxKS5hZGQoY3VycmVuY3kocnNDaGFyZ2UuZHV0eVJhdGUpLmRpdmlkZSgxMDApKSkpLnZhbHVlDQogICAgICAgICAgICBwYXlhYmxlUk1CVGF4ID0gY3VycmVuY3kocGF5YWJsZVJNQlRheCkuYWRkKHJzQ2hhcmdlLnN1YnRvdGFsKS52YWx1ZQ0KICAgICAgICAgIH0NCiAgICAgICAgfSkNCiAgICAgICAgcnNPcFNlcnZpY2UucGF5YWJsZVJNQiA9IHBheWFibGVSTUINCiAgICAgICAgcnNPcFNlcnZpY2UucGF5YWJsZVVTRCA9IHBheWFibGVVU0QNCiAgICAgICAgcnNPcFNlcnZpY2UucGF5YWJsZVJNQlRheCA9IHBheWFibGVSTUJUYXgNCiAgICAgICAgcnNPcFNlcnZpY2UucGF5YWJsZVVTRFRheCA9IHBheWFibGVVU0RUYXgNCg0KICAgICAgICB0aGlzLnJzQ2xpZW50TWVzc2FnZUNoYXJnZSh0aGlzLnJzQ2xpZW50TWVzc2FnZS5yc0NoYXJnZUxpc3QpDQogICAgICB9DQogICAgfSwNCiAgICBnZXRSc1BheW1lbnRUaXRsZSh2YWwpIHsNCiAgICAgIHRoaXMuUGF5bWVudFRpdGxlQ29kZSA9IHZhbA0KICAgIH0sDQogICAgc2VsZWN0U2FsZXMoc3RhZmYpIHsNCg0KICAgICAgdGhpcy5mb3JtLnNhbGVzSWQgPSBzdGFmZg0KICAgIH0sDQogICAgc2VsZWN0Q2FycmllcihpdGVtLCB2KSB7DQogICAgICBpdGVtLmNhcnJpZXJJZCA9IHYuY2FycmllcklkDQogICAgICAvLyB0aGlzLmZvcm0uY2FycmllcklkID0gdi5jYXJyaWVySWQNCiAgICAgIHRoaXMuZm9ybS5zcWRDYXJyaWVyID0gdi5jYXJyaWVySW50bENvZGUNCiAgICAgIHRoaXMuZm9ybS5jYXJyaWVyRW5OYW1lID0gdi5jYXJyaWVySW50bENvZGUNCiAgICB9LA0KICAgIGhhbmRsZURlc2VsZWN0Q29tcGFueUlkcyhpZCkgew0KICAgICAgdGhpcy5SZWxhdGlvbkNsaWVudElkTGlzdCA9IHRoaXMuUmVsYXRpb25DbGllbnRJZExpc3QuZmlsdGVyKGl0ZW0gPT4gaXRlbSAhPT0gaWQpDQogICAgfSwNCiAgICBoYW5kbGVTZWxlY3RDb21wYW55SWRzKG5vZGUpIHsNCiAgICAgIGlmIChub2RlLmZpbHRlcihpdGVtID0+IHRoaXMuUmVsYXRpb25DbGllbnRJZExpc3QuaW5kZXhPZihpdGVtLmNvbXBhbnlJZCkgPT09IC0xKS5sZW5ndGggPiAwKSB7DQogICAgICAgIG5vZGUuZmlsdGVyKGl0ZW0gPT4gdGhpcy5SZWxhdGlvbkNsaWVudElkTGlzdC5pbmRleE9mKGl0ZW0uY29tcGFueUlkKSA9PT0gLTEpLm1hcChpdGVtID0+IHRoaXMuUmVsYXRpb25DbGllbnRJZExpc3QucHVzaChpdGVtLmNvbXBhbnlJZCkpDQogICAgICB9DQogICAgICBub2RlLm1hcChpdGVtID0+IHsNCiAgICAgICAgdGhpcy5jb21wYW55TGlzdC5pbmRleE9mKGl0ZW0pID09PSAtMSA/IHRoaXMuY29tcGFueUxpc3QucHVzaChpdGVtKSA6IG51bGwNCiAgICAgIH0pDQogICAgfSwNCiAgICBzZWxlY3RSZWxhdGlvbkNsaWVudChpZCkgew0KICAgIH0sDQogICAgLy8g5p+l6K+i6ZmE5Yqg6LS55YiX6KGoDQogICAgZ2V0TG9jYWxMaXN0KHJvdykgew0KICAgICAgbGV0IGluZGV4ID0gcm93LmxvY2Fscw0KICAgICAgaWYgKGluZGV4ID09IG51bGwgfHwgaW5kZXgubGVuZ3RoID09IDApIHsNCiAgICAgICAgcmV0dXJuIG5ldyBQcm9taXNlKChyZXNvbHZlLCByZWplY3QpID0+IHsNCiAgICAgICAgICBxdWVyeUxvY2FsKHJvdykudGhlbihyZXNwb25zZSA9PiB7DQogICAgICAgICAgICByb3cubG9jYWxzID0gcmVzcG9uc2UuZGF0YQ0KICAgICAgICAgICAgcmVzb2x2ZShyb3cpDQogICAgICAgICAgfSkNCiAgICAgICAgfSkNCiAgICAgIH0NCiAgICB9LA0KICAgIGhhbmRsZUNoYXJnZVNlbGVjdChjaGFyZ2VzKSB7DQogICAgICBjaGFyZ2VzLm1hcChpdGVtID0+IHsNCiAgICAgICAgdGhpcy5jaGFyZ2VTZWxlY3RJdGVtLnJzQ2hhcmdlTGlzdC5wdXNoKHsNCiAgICAgICAgICAuLi50aGlzLl8uY2xvbmVEZWVwKGl0ZW0pLA0KICAgICAgICAgIGlzQWNjb3VudENvbmZpcm1lZDogMCwNCiAgICAgICAgICBzcWREbkN1cnJlbmN5UGFpZDogMCwNCiAgICAgICAgICBzcWREbkN1cnJlbmN5QmFsYW5jZTogY3VycmVuY3koaXRlbS5zdWJ0b3RhbCkudmFsdWUsDQogICAgICAgICAgY3VycmVuY3lSYXRlQ2FsY3VsYXRlRGF0ZTogbmV3IERhdGUoKSwNCiAgICAgICAgICBjaGFyZ2VJZDogbnVsbCwNCiAgICAgICAgICBzcWRSY3RJZDogdGhpcy5mb3JtLnJjdElkLA0KICAgICAgICAgIHNlcnZpY2VJZDogdGhpcy5jaGFyZ2VTZWxlY3RJdGVtLnNlcnZpY2VJZCwNCiAgICAgICAgICBzcWRTZXJ2aWNlVHlwZUlkOiB0aGlzLmNoYXJnZVNlbGVjdEl0ZW0uc3FkU2VydmljZVR5cGVJZCwNCiAgICAgICAgICBzcWRSY3RObzogdGhpcy5mb3JtLnJjdE5vDQogICAgICAgIH0pDQogICAgICB9KQ0KICAgICAgLy8gY2hhcmdlU2VsZWN0SXRlbS5yc0NoYXJnZUxpc3QgPSBjaGFyZ2VzDQogICAgfSwNCiAgICAvKioNCiAgICAgKg0KICAgICAqIEBwYXJhbSBzZXJ2aWNlVHlwZUlkDQogICAgICogQHBhcmFtIHVuaXQg5Y2V5L2NDQogICAgICogQHBhcmFtIGZyZWlnaHQg6YCJ5oup55qE5pW05p2h6LS555SoDQogICAgICogQHJldHVybnMge1Byb21pc2U8dm9pZD59DQogICAgICovDQogICAgYXN5bmMgaGFuZGxlRnJlaWdodFNlbGVjdChzZXJ2aWNlVHlwZUlkLCB1bml0LCBmcmVpZ2h0KSB7DQogICAgICAvLyDotLnnlKjlvZXlhaXml7bmmL7npLrliqDovb0NCiAgICAgIGNvbnN0IGxvYWRpbmcgPSB0aGlzLiRsb2FkaW5nKHsNCiAgICAgICAgbG9jazogdHJ1ZSwNCiAgICAgICAgdGV4dDogZnJlaWdodC5pbnF1aXJ5Tm8gKyAiIOeahCAiICsgdW5pdCArICLotLnnlKjoh6rliqjlvZXlhaXkuK0uLi4iLA0KICAgICAgICBzcGlubmVyOiAiZWwtaWNvbi1sb2FkaW5nIiwNCiAgICAgICAgYmFja2dyb3VuZDogInJnYmEoMCwgMCwgMCwgMC43KSINCiAgICAgIH0pDQogICAgICB0aGlzLnNldEJhc2ljSW5mbyhzZXJ2aWNlVHlwZUlkLCBmcmVpZ2h0LCB0aGlzLmN1ckZyZWlnaHRTZWxlY3RSb3cpDQogICAgICBsZXQgdW5pdENvZGUgPSB1bml0LnNwbGl0KCJ4IilbMV0NCiAgICAgIC8vIOWwhuivt+axgueahGxvY2Fs6K6+572u5YiwZnJlaWdodOS4rQ0KICAgICAgYXdhaXQgdGhpcy5nZXRMb2NhbExpc3QoZnJlaWdodCkNCiAgICAgIGlmICh0aGlzLmZyZWlnaHRTZWxlY3REYXRhLnJldmVudWVUb25MaXN0ICYmIHRoaXMuZnJlaWdodFNlbGVjdERhdGEucmV2ZW51ZVRvbkxpc3QubGVuZ3RoID4gMCkgew0KICAgICAgICAvLyBHUCBGUiBIUe+8iOiuoei0ueWNleS9je+8iQ0KICAgICAgICAvLyBnb29kID0+IDJ4MjBHUA0KICAgICAgICBmb3IgKGNvbnN0IGdvb2Qgb2YgdGhpcy5mcmVpZ2h0U2VsZWN0RGF0YS5yZXZlbnVlVG9uTGlzdCkgew0KICAgICAgICAgIC8vIOiuoei0ueWNleS9jeeahGlk562J5LqO55So5oi35Zyo56Gu6K6k6YCJ5oup5pe26YCJ5oup55qE6K6h6LS55Y2V5L2NaWQNCiAgICAgICAgICBpZiAoZ29vZC5zcGxpdCgieCIpWzFdID09IHVuaXRDb2RlKSB7DQogICAgICAgICAgICAvLyDlhYjlsIbmiYDpgInnmoTov5DotLnliqDlhaXmiqXku7fotLnnlKjkuK0o5pyN5Yqh6aG555uuKQ0KICAgICAgICAgICAgLy8g5re75Yqg6LS555So5pe25p+Q5Lqb6LS555So5rKh5pyJ6KKr5re75Yqg6L+b5Y67LS0+IEZS5Y2V5L2N5pe2cHJpY2VC44CBQ+OAgUTpg73kuI3pgILnlKjvvIxwcmljZUHkuLrnqbrpgqPkuYjov5nmnaHotLnnlKjlsLHkuI3kvJrov5vlhaXliLDmnI3liqHpobnnm67liJfooajkuK0NCiAgICAgICAgICAgIGF3YWl0IHRoaXMuYWRkQ2hhcmdlKGdvb2QsIGZyZWlnaHQsIGZyZWlnaHQuZnJlaWdodElkLCBzZXJ2aWNlVHlwZUlkKQ0KICAgICAgICAgICAgLy8g5YaN5bCG5omA6YCJ6L+Q6LS555qE5Z+656GA6ZmE5Yqg6LS577yIbGNvYWzvvInkuZ/liqDlhaXmiqXku7fotLnnlKjkuK3vvIjmnI3liqHpobnnm67vvIkNCiAgICAgICAgICAgIGZvciAoY29uc3QgbG9jYWwgb2YgZnJlaWdodC5sb2NhbHMpIHsNCiAgICAgICAgICAgICAgYXdhaXQgdGhpcy5hZGRDaGFyZ2UoZ29vZCwgbG9jYWwsIGZyZWlnaHQuZnJlaWdodElkLCBzZXJ2aWNlVHlwZUlkKQ0KICAgICAgICAgICAgfQ0KICAgICAgICAgICAgLy8g5Y+q5Yy56YWN5LiA5Liq5Y2V5L2NDQogICAgICAgICAgICBicmVhaw0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgfQ0KICAgICAgc2V0VGltZW91dCgoKSA9PiB7DQogICAgICAgIHRoaXMuY3VyRnJlaWdodFNlbGVjdFJvdyA9IG51bGwNCiAgICAgICAgbG9hZGluZy5jbG9zZSgpDQogICAgICB9KQ0KICAgICAgLy8gdGhpcy5vcGVuRnJlaWdodFNlbGVjdCA9IGZhbHNlDQogICAgfSwNCiAgICBmaW5kQ2FycmllckJ5SW50bENvZGUodHJlZSwgY2FycmllckludGxDb2RlKSB7DQogICAgICAvLyDpgY3ljobmr4/kuKroioLngrkNCiAgICAgIGZvciAoY29uc3Qgbm9kZSBvZiB0cmVlKSB7DQogICAgICAgIC8vIOWmguaenOW9k+WJjeiKgueCueWMuemFjeadoeS7tu+8jOi/lOWbnuivpeiKgueCuQ0KICAgICAgICBpZiAobm9kZS5jYXJyaWVyICYmIG5vZGUuY2Fycmllci5jYXJyaWVySW50bENvZGUgPT09IGNhcnJpZXJJbnRsQ29kZSkgew0KICAgICAgICAgIHJldHVybiBub2RlDQogICAgICAgIH0NCiAgICAgICAgLy8g5aaC5p6c5pyJ5a2Q6IqC54K577yM6YCS5b2S5p+l5om+DQogICAgICAgIGlmIChub2RlLmNoaWxkcmVuICYmIG5vZGUuY2hpbGRyZW4ubGVuZ3RoID4gMCkgew0KICAgICAgICAgIGNvbnN0IHJlc3VsdCA9IHRoaXMuZmluZENhcnJpZXJCeUludGxDb2RlKG5vZGUuY2hpbGRyZW4sIGNhcnJpZXJJbnRsQ29kZSkNCiAgICAgICAgICBpZiAocmVzdWx0KSB7DQogICAgICAgICAgICByZXR1cm4gcmVzdWx0IC8vIOaJvuWIsOebruagh+WFg+e0oOWQjui/lOWbng0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgfQ0KICAgICAgLy8g5aaC5p6c5pW05Liq5qCR5Lit6YO95om+5LiN5Yiw5Yy56YWN55qE5YWD57Sg77yM6L+U5ZueIG51bGwNCiAgICAgIHJldHVybiBudWxsDQogICAgfSwNCiAgICAvLyDln7rnoYDkv6Hmga/orr7nva4NCiAgICBzZXRCYXNpY0luZm8oc2VydmljZVR5cGVJZCwgZnJlaWdodCwgY3VyUm93KSB7DQogICAgICBpZiAoY3VyUm93KSB7DQogICAgICAgIGNvbnNvbGUubG9nKGN1clJvdykNCiAgICAgICAgY3VyUm93LnJzU2VydmljZUluc3RhbmNlcy5pbnF1aXJ5Tm8gPSBmcmVpZ2h0LmlucXVpcnlObw0KICAgICAgICBjdXJSb3cucnNTZXJ2aWNlSW5zdGFuY2VzLnN1cHBsaWVySWQgPSBmcmVpZ2h0LnN1cHBsaWVySWQNCiAgICAgICAgY3VyUm93LnJzU2VydmljZUluc3RhbmNlcy5zdXBwbGllcl90ZWwgPSBmcmVpZ2h0LnN1cHBsaWVyVGVsDQogICAgICAgIGN1clJvdy5yc1NlcnZpY2VJbnN0YW5jZXMuc3VwcGxpZXJOYW1lID0gZnJlaWdodC5jb21wYW55DQogICAgICAgIGN1clJvdy5yc1NlcnZpY2VJbnN0YW5jZXMuYWdyZWVtZW50Tm8gPSBmcmVpZ2h0LmFncmVlbWVudE5vDQogICAgICAgIGN1clJvdy5yc1NlcnZpY2VJbnN0YW5jZXMuYWdyZWVtZW50VHlwZUNvZGUgPSBmcmVpZ2h0LmNvbnRyYWN0VHlwZQ0KICAgICAgICBjdXJSb3cucnNTZXJ2aWNlSW5zdGFuY2VzLmlucXVpcnlJbm5lclJlbWFyayA9IGZyZWlnaHQucHNhUmVtYXJrDQogICAgICAgIGN1clJvdy5yc1NlcnZpY2VJbnN0YW5jZXMuaW5xdWlyeUxlYXRlc3RVcGRhdGVkVGltZSA9IGZyZWlnaHQuY3JlYXRlVGltZQ0KICAgICAgICBjdXJSb3cucnNTZXJ2aWNlSW5zdGFuY2VzLmlucXVpcnlOb3RpY2UgPSBmcmVpZ2h0Lm5vdGljZUZvclNhbGVzDQogICAgICAgIGN1clJvdy5yc1NlcnZpY2VJbnN0YW5jZXMubWF4V2VpZ2h0ID0gZnJlaWdodC5tYXhXZWlnaHQNCiAgICAgICAgbGV0IGN1ckNhcnJpZXIgPSB0aGlzLmNhcnJpZXJMaXN0LmZpbHRlcihpdGVtID0+IGl0ZW0uY2FycmllckludGxDb2RlID09PSBmcmVpZ2h0LmNhcnJpZXJDb2RlKVswXQ0KICAgICAgICBpZiAoY3VyQ2Fycmllcikgew0KICAgICAgICAgIGN1clJvdy5jYXJyaWVySWQgPSBjdXJDYXJyaWVyLmNhcnJpZXJJZA0KICAgICAgICAgIC8vIHRoaXMuZm9ybS5jYXJyaWVySWQgPSB2LmNhcnJpZXJJZA0KICAgICAgICAgIHRoaXMuZm9ybS5zcWRDYXJyaWVyID0gY3VyQ2Fycmllci5jYXJyaWVySW50bENvZGUNCiAgICAgICAgICB0aGlzLmZvcm0uY2FycmllckVuTmFtZSA9IGN1ckNhcnJpZXIuY2FycmllckludGxDb2RlDQogICAgICAgIH0NCiAgICAgICAgY3VyUm93LmlucXVpcnlTY2hlZHVsZVN1bW1hcnkgPSBmcmVpZ2h0LmxvZ2lzdGljc1NjaGVkdWxlDQogICAgICB9DQogICAgfSwNCiAgICBhc3luYyBhZGRDaGFyZ2UocmV2ZW51ZVRvbnMsIHJvdywgZnJlaWdodElkLCBzZXJ2aWNlVHlwZUlkKSB7DQogICAgICBsZXQgdW5pdENvZGUgPSByZXZlbnVlVG9ucy5zcGxpdCgieCIpWzFdDQogICAgICBsZXQgYW1vdW50ID0gcmV2ZW51ZVRvbnMuc3BsaXQoIngiKVswXQ0KICAgICAgaWYgKHJldmVudWVUb25zLnNwbGl0KCJ4IikubGVuZ3RoID09PSAxKSB7DQogICAgICAgIHVuaXRDb2RlID0gcmV2ZW51ZVRvbnMuc3BsaXQoIngiKVswXQ0KICAgICAgICBhbW91bnQgPSAxDQogICAgICB9DQoNCiAgICAgIC8vIOWIneWni+WMluS4gOS4qui0ueeUqO+8jOa4heepunF1b3RhdGlvbkZyZWlnaHTnmoTlsZ7mgKcNCiAgICAgIHRoaXMucmVzZXRDaGFyZ2UoKQ0KICAgICAgLy8g6LS555So5o6S5bqPDQogICAgICB0aGlzLnJzQ2hhcmdlLmNoYXJnZU9yZGVyTnVtID0gcm93LmNoYXJnZU9yZGVyTnVtDQogICAgICAvLyDotLnnlKjnsbvlnovmjpLluo8NCiAgICAgIHRoaXMucnNDaGFyZ2UuY2hhcmdlVHlwZU9yZGVyTnVtID0gcm93LmNoYXJnZVR5cGVPcmRlck51bQ0KICAgICAgdGhpcy5yc0NoYXJnZS5zcWRTZXJ2aWNlVHlwZUlkID0gcm93LnNlcnZpY2VUeXBlSWQNCiAgICAgIHRoaXMucnNDaGFyZ2UudHlwZUlkID0gdGhpcy50eXBlSWQNCiAgICAgIC8vIOiuoei0ueWNleS9jeS4uuKAmOelqOKAmQ0KICAgICAgaWYgKHJvdy51bml0ID09PSAiQkwiKSB7DQogICAgICAgIHRoaXMucnNDaGFyZ2UuaW5xdWlyeUFtb3VudCA9IDENCiAgICAgICAgdGhpcy5yc0NoYXJnZS5xdW90YXRpb25BbW91bnQgPSAxDQogICAgICAgIHRoaXMucnNDaGFyZ2UuZG5BbW91bnQgPSAxDQogICAgICB9IGVsc2Ugew0KICAgICAgICB0aGlzLnJzQ2hhcmdlLmlucXVpcnlBbW91bnQgPSBhbW91bnQgIT0gbnVsbCA/IGFtb3VudCA6IDENCiAgICAgICAgdGhpcy5yc0NoYXJnZS5xdW90YXRpb25BbW91bnQgPSBhbW91bnQgIT0gbnVsbCA/IGFtb3VudCA6IDENCiAgICAgICAgdGhpcy5yc0NoYXJnZS5kbkFtb3VudCA9IGFtb3VudCAhPSBudWxsID8gYW1vdW50IDogMQ0KICAgICAgfQ0KICAgICAgdGhpcy5yc0NoYXJnZS5kblVuaXRDb2RlID0gcm93LnVuaXRDb2RlICE9PSAiQ3RuciIgPyByb3cudW5pdENvZGUgOiAocmV2ZW51ZVRvbnMgIT09IG51bGwgPyB1bml0Q29kZSA6IG51bGwpDQogICAgICAvLyDljZXkvY3mmK/mn5zvvIzmiJDmnKzotYvlgLzvvIjmtonlj4rliLDlkI7pnaLmmK/lkKbmj5LlhaXmnI3liqHpobnnm67vvIkNCiAgICAgIGlmIChyb3cudW5pdENvZGUgPT09ICJDdG5yIiAmJiByZXZlbnVlVG9ucyAhPSBudWxsKSB7DQogICAgICAgIGlmICh1bml0Q29kZSA9PT0gIjIwR1AiKSB7DQogICAgICAgICAgdGhpcy5yc0NoYXJnZS5kblVuaXRSYXRlID0gcm93LnByaWNlQg0KICAgICAgICB9IGVsc2UgaWYgKHVuaXRDb2RlID09PSAiNDBHUCIpIHsNCiAgICAgICAgICB0aGlzLnJzQ2hhcmdlLmRuVW5pdFJhdGUgPSByb3cucHJpY2VDDQogICAgICAgIH0gZWxzZSBpZiAodW5pdENvZGUgPT09ICI0MEhRIikgew0KICAgICAgICAgIHRoaXMucnNDaGFyZ2UuZG5Vbml0UmF0ZSA9IHJvdy5wcmljZUQNCiAgICAgICAgfQ0KICAgICAgfQ0KICAgICAgaWYgKHRoaXMucnNDaGFyZ2UuZG5Vbml0UmF0ZSA9PSBudWxsKSB7DQogICAgICAgIHRoaXMucnNDaGFyZ2UuZG5Vbml0UmF0ZSA9IHJvdy5wcmljZUENCiAgICAgIH0NCiAgICAgIHRoaXMucnNDaGFyZ2UuY29zdEN1cnJlbmN5Q29kZSA9IHJvdy5jdXJyZW5jeUNvZGUNCiAgICAgIHRoaXMucnNDaGFyZ2UuY29zdEN1cnJlbmN5ID0gcm93LmN1cnJlbmN5DQogICAgICAvLw0KICAgICAgdGhpcy5yc0NoYXJnZS5xdW90YXRpb25SYXRlID0gdGhpcy5yc0NoYXJnZS5pbnF1aXJ5UmF0ZQ0KICAgICAgLy8gcm93LmNoYXJnZSAtLT4g5qCH5YeGL1RIQw0KICAgICAgLy8g5Y+W6LS555So6Iux5paH566A5YaZDQogICAgICBpZiAocm93LmNoYXJnZSAhPSBudWxsICYmIHJvdy5jaGFyZ2UuaW5jbHVkZXMoIi8iKSkgew0KICAgICAgICB0aGlzLnJzQ2hhcmdlLmNoYXJnZSA9IHJvdy5jaGFyZ2Uuc3BsaXQoIi8iKVsxXQ0KICAgICAgICB0aGlzLnJzQ2hhcmdlLmNoYXJnZU5hbWUgPSByb3cuY2hhcmdlLnNwbGl0KCIvIilbMV0NCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHRoaXMucnNDaGFyZ2UuY2hhcmdlID0gcm93LmNoYXJnZQ0KICAgICAgICB0aGlzLnJzQ2hhcmdlLmNoYXJnZU5hbWUgPSByb3cuY2hhcmdlDQogICAgICB9DQogICAgICB0aGlzLnJzQ2hhcmdlLmNoYXJnZUVuID0gcm93LmNoYXJnZUVuDQogICAgICB0aGlzLnJzQ2hhcmdlLmZyZWlnaHRJZCA9IGZyZWlnaHRJZA0KICAgICAgdGhpcy5yc0NoYXJnZS5sb2NhbENoYXJnZUlkID0gcm93LmxvY2FsQ2hhcmdlSWQNCiAgICAgIHRoaXMucnNDaGFyZ2UucHJvZml0ID0gMA0KICAgICAgdGhpcy5yc0NoYXJnZS50YXhSYXRlID0gMA0KICAgICAgdGhpcy5yc0NoYXJnZS5xdW90YXRpb25DdXJyZW5jeUNvZGUgPSByb3cuY3VycmVuY3lDb2RlDQogICAgICB0aGlzLnJzQ2hhcmdlLnF1b3RhdGlvbkN1cnJlbmN5ID0gcm93LmN1cnJlbmN5DQogICAgICB0aGlzLnJzQ2hhcmdlLmNyZWF0ZVRpbWUgPSBwYXJzZVRpbWUobmV3IERhdGUoKSkNCiAgICAgIHRoaXMucnNDaGFyZ2UuY3JlYXRlQnkgPSB0aGlzLiRzdG9yZS5zdGF0ZS51c2VyLnNpZA0KICAgICAgdGhpcy5yc0NoYXJnZS5jb21wYW55ID0gcm93LmNvbXBhbnkNCiAgICAgIHRoaXMucnNDaGFyZ2UucmljaE5vID0gcm93LnJpY2hObw0KICAgICAgdGhpcy5yc0NoYXJnZS5kbkN1cnJlbmN5Q29kZSA9IHJvdy5jdXJyZW5jeSA/IHJvdy5jdXJyZW5jeSA6IHJvdy5jdXJyZW5jeUNvZGUNCiAgICAgIHRoaXMucnNDaGFyZ2UuY2xlYXJpbmdDb21wYW55SWQgPSByb3cuc3VwcGxpZXJJZA0KICAgICAgdGhpcy5yc0NoYXJnZS5jb21wYW55TmFtZSA9IHJvdy5jb21wYW55DQogICAgICB0aGlzLnJzQ2hhcmdlLmRuQ2hhcmdlTmFtZUlkID0gcm93LmNoYXJnZUlkDQogICAgICB0aGlzLnJzQ2hhcmdlLmR1dHlSYXRlID0gMA0KDQogICAgICB0aGlzLnJzQ2hhcmdlLnNob3dBbW91bnQgPSBmYWxzZQ0KICAgICAgdGhpcy5yc0NoYXJnZS5zaG93Q2xpZW50ID0gZmFsc2UNCiAgICAgIHRoaXMucnNDaGFyZ2Uuc2hvd0Nvc3RDaGFyZ2UgPSBmYWxzZQ0KICAgICAgdGhpcy5yc0NoYXJnZS5zaG93Q29zdEN1cnJlbmN5ID0gZmFsc2UNCiAgICAgIHRoaXMucnNDaGFyZ2Uuc2hvd0Nvc3RVbml0ID0gZmFsc2UNCiAgICAgIHRoaXMucnNDaGFyZ2Uuc2hvd0N1cnJlbmN5UmF0ZSA9IGZhbHNlDQogICAgICB0aGlzLnJzQ2hhcmdlLnNob3dEdXR5UmF0ZSA9IGZhbHNlDQogICAgICB0aGlzLnJzQ2hhcmdlLnNob3dRdW90YXRpb25DaGFyZ2UgPSBmYWxzZQ0KICAgICAgdGhpcy5yc0NoYXJnZS5zaG93UXVvdGF0aW9uQ3VycmVuY3kgPSBmYWxzZQ0KICAgICAgdGhpcy5yc0NoYXJnZS5zaG93UXVvdGF0aW9uVW5pdCA9IGZhbHNlDQogICAgICB0aGlzLnJzQ2hhcmdlLnNob3dTdHJhdGVneSA9IHRydWUNCiAgICAgIHRoaXMucnNDaGFyZ2Uuc2hvd1N1cHBsaWVyID0gZmFsc2UNCiAgICAgIHRoaXMucnNDaGFyZ2Uuc2hvd1VuaXRSYXRlID0gZmFsc2UNCg0KICAgICAgLy8g6I635Y+W5rGH546HDQogICAgICBsZXQgZXhjaGFuZ2VSYXRlDQogICAgICAvKiAgICAgICB0aGlzLmdldEV4Y2hhbmdlUmF0ZSh0aGlzLnJzQ2hhcmdlLCAocmVzdWx0KSA9PiB7DQogICAgICAgICAgICAgIGV4Y2hhbmdlUmF0ZSA9IHJlc3VsdA0KICAgICAgICAgICAgfSkNCg0KICAgICAgICAgICAgZXhjaGFuZ2VSYXRlID0gYXdhaXQgdGhpcy5nZXRFeGNoYW5nZVJhdGVEaXJlY3QodGhpcy5yc0NoYXJnZSkgKi8NCg0KICAgICAgaWYgKHRoaXMucnNDaGFyZ2UpIHsNCiAgICAgICAgZm9yIChjb25zdCBhIG9mIHRoaXMuJHN0b3JlLnN0YXRlLmRhdGEuZXhjaGFuZ2VSYXRlTGlzdCkgew0KICAgICAgICAgIGlmIChhLmxvY2FsQ3VycmVuY3kgPT0gIlJNQiINCiAgICAgICAgICAgICYmIHJvdy5kbkN1cnJlbmN5Q29kZSA9PSBhLm92ZXJzZWFDdXJyZW5jeQ0KICAgICAgICAgICkgew0KICAgICAgICAgICAgZXhjaGFuZ2VSYXRlID0gY3VycmVuY3koYS5zZXR0bGVSYXRlKS5kaXZpZGUoYS5iYXNlKS52YWx1ZQ0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgfQ0KDQogICAgICBpZiAoZXhjaGFuZ2VSYXRlID09IG51bGwpIHsNCiAgICAgICAgdGhpcy5yc0NoYXJnZS5iYXNpY0N1cnJlbmN5UmF0ZSA9IDENCiAgICAgIH0gZWxzZSB7DQogICAgICAgIHRoaXMucnNDaGFyZ2UuYmFzaWNDdXJyZW5jeVJhdGUgPSBleGNoYW5nZVJhdGUNCiAgICAgIH0NCg0KICAgICAgbGV0IGR1dHlSYXRlID0gY3VycmVuY3kodGhpcy5yc0NoYXJnZS5kblVuaXRSYXRlKS5tdWx0aXBseSh0aGlzLnJzQ2hhcmdlLmRuQW1vdW50KS5tdWx0aXBseShjdXJyZW5jeShyb3cuZHV0eVJhdGUpLmRpdmlkZSgxMDApKS52YWx1ZQ0KICAgICAgdGhpcy5yc0NoYXJnZS5zdWJ0b3RhbCA9IGN1cnJlbmN5KGN1cnJlbmN5KHRoaXMucnNDaGFyZ2UuZG5Vbml0UmF0ZSkubXVsdGlwbHkodGhpcy5yc0NoYXJnZS5kbkFtb3VudCkpLmFkZChkdXR5UmF0ZSkudmFsdWUNCiAgICAgIHRoaXMucnNDaGFyZ2Uuc3FkRG5DdXJyZW5jeUJhbGFuY2UgPSBjdXJyZW5jeSh0aGlzLnJzQ2hhcmdlLmRuVW5pdFJhdGUpLm11bHRpcGx5KHRoaXMucnNDaGFyZ2UuZG5BbW91bnQpLnZhbHVlDQoNCiAgICAgIC8vIOaYr+WQpuimgeaPkuWFpeaWsOeahOacjeWKoemhueebrg0KICAgICAgaWYgKHRoaXMucnNDaGFyZ2UuZG5Vbml0UmF0ZSAhPSBudWxsKSB7DQogICAgICAgIC8vIOaPkuWFpeWIsOWvueW6lOacjeWKoeeahGNoYXJnZUxpc3TkuK0NCiAgICAgICAgdGhpcy5pbnNlcnRDaGFyZ2UodGhpcy5yc0NoYXJnZSwgc2VydmljZVR5cGVJZCkNCiAgICAgIH0NCiAgICB9LA0KICAgIGluc2VydENoYXJnZShyb3csIHNlcnZpY2VUeXBlSWQpIHsNCiAgICAgIGlmIChzZXJ2aWNlVHlwZUlkID09PSAxKSB7DQogICAgICAgIC8vIHRoaXMucnNPcFNlYUZjbC5yc0NoYXJnZUxpc3QucHVzaChyb3cpDQogICAgICAgIHRoaXMuZm9ybS5yc09wU2VhRmNsTGlzdCA9IHRoaXMuZm9ybS5yc09wU2VhRmNsTGlzdC5tYXAoaXRlbSA9PiB7DQogICAgICAgICAgaWYgKGl0ZW0uc2VhSWQgPT09IHRoaXMuY3VyRnJlaWdodFNlbGVjdFJvdy5zZWFJZCkgew0KICAgICAgICAgICAgcm93LmNsZWFyaW5nQ29tcGFueUlkID0gaXRlbS5yc1NlcnZpY2VJbnN0YW5jZXMuc3VwcGxpZXJJZA0KICAgICAgICAgICAgcm93LmNvbXBhbnlOYW1lID0gaXRlbS5yc1NlcnZpY2VJbnN0YW5jZXMuc3VwcGxpZXJOYW1lDQogICAgICAgICAgICBpdGVtLnJzQ2hhcmdlTGlzdC5wdXNoKHJvdykNCiAgICAgICAgICB9DQogICAgICAgICAgcmV0dXJuIGl0ZW0NCiAgICAgICAgfSkNCiAgICAgIH0NCiAgICAgIGlmIChzZXJ2aWNlVHlwZUlkID09PSAyKSB7DQogICAgICAgIHRoaXMuZm9ybS5yc09wU2VhTGNsTGlzdCA9IHRoaXMuZm9ybS5yc09wU2VhTGNsTGlzdC5tYXAoaXRlbSA9PiB7DQogICAgICAgICAgaWYgKGl0ZW0uc2VhSWQgPT09IHRoaXMuY3VyRnJlaWdodFNlbGVjdFJvdy5zZWFJZCkgew0KICAgICAgICAgICAgcm93LmNsZWFyaW5nQ29tcGFueUlkID0gaXRlbS5yc1NlcnZpY2VJbnN0YW5jZXMuc3VwcGxpZXJJZA0KICAgICAgICAgICAgcm93LmNvbXBhbnlOYW1lID0gaXRlbS5yc1NlcnZpY2VJbnN0YW5jZXMuc3VwcGxpZXJOYW1lDQogICAgICAgICAgICBpdGVtLnJzQ2hhcmdlTGlzdC5wdXNoKHJvdykNCiAgICAgICAgICB9DQogICAgICAgICAgcmV0dXJuIGl0ZW0NCiAgICAgICAgfSkNCiAgICAgIH0NCiAgICAgIGlmIChzZXJ2aWNlVHlwZUlkID09PSAxMCkgew0KICAgICAgICByb3cuY2xlYXJpbmdDb21wYW55SWQgPSB0aGlzLnJzT3BBaXJTZXJ2aWNlSW5zdGFuY2Uuc3VwcGxpZXJJZA0KICAgICAgICByb3cuY29tcGFueU5hbWUgPSB0aGlzLnJzT3BBaXJTZXJ2aWNlSW5zdGFuY2Uuc3VwcGxpZXJOYW1lDQogICAgICAgIHRoaXMucnNPcEFpci5yc0NoYXJnZUxpc3QucHVzaChyb3cpDQogICAgICB9DQogICAgICBpZiAoc2VydmljZVR5cGVJZCA9PT0gMjApIHsNCiAgICAgICAgcm93LmNsZWFyaW5nQ29tcGFueUlkID0gdGhpcy5yc09wUmFpbEZjbFNlcnZpY2VJbnN0YW5jZS5zdXBwbGllcklkDQogICAgICAgIHJvdy5jb21wYW55TmFtZSA9IHRoaXMucnNPcFJhaWxGY2xTZXJ2aWNlSW5zdGFuY2Uuc3VwcGxpZXJOYW1lDQogICAgICAgIHRoaXMucnNPcFJhaWxGQ0wucnNDaGFyZ2VMaXN0LnB1c2gocm93KQ0KICAgICAgfQ0KICAgICAgaWYgKHNlcnZpY2VUeXBlSWQgPT09IDIxKSB7DQogICAgICAgIHJvdy5jbGVhcmluZ0NvbXBhbnlJZCA9IHRoaXMucnNPcFJhaWxMY2xTZXJ2aWNlSW5zdGFuY2Uuc3VwcGxpZXJJZA0KICAgICAgICByb3cuY29tcGFueU5hbWUgPSB0aGlzLnJzT3BSYWlsTGNsU2VydmljZUluc3RhbmNlLnN1cHBsaWVyTmFtZQ0KICAgICAgICB0aGlzLnJzT3BSYWlsTENMLnJzQ2hhcmdlTGlzdC5wdXNoKHJvdykNCiAgICAgIH0NCiAgICAgIGlmIChzZXJ2aWNlVHlwZUlkID09PSA0MCkgew0KICAgICAgICByb3cuY2xlYXJpbmdDb21wYW55SWQgPSB0aGlzLnJzT3BFeHByZXNzU2VydmljZUluc3RhbmNlLnN1cHBsaWVySWQNCiAgICAgICAgcm93LmNvbXBhbnlOYW1lID0gdGhpcy5yc09wRXhwcmVzc1NlcnZpY2VJbnN0YW5jZS5zdXBwbGllck5hbWUNCiAgICAgICAgdGhpcy5yc09wRXhwcmVzcy5yc0NoYXJnZUxpc3QucHVzaChyb3cpDQogICAgICB9DQogICAgICBpZiAoc2VydmljZVR5cGVJZCA9PT0gNTApIHsNCiAgICAgICAgdGhpcy5mb3JtLnJzT3BDdG5yVHJ1Y2tMaXN0ID0gdGhpcy5mb3JtLnJzT3BDdG5yVHJ1Y2tMaXN0Lm1hcChpdGVtID0+IHsNCiAgICAgICAgICBpZiAoaXRlbS5zZWFJZCA9PT0gdGhpcy5jdXJGcmVpZ2h0U2VsZWN0Um93LnNlYUlkKSB7DQogICAgICAgICAgICByb3cuY2xlYXJpbmdDb21wYW55SWQgPSBpdGVtLnJzU2VydmljZUluc3RhbmNlcy5zdXBwbGllcklkDQogICAgICAgICAgICByb3cuY29tcGFueU5hbWUgPSBpdGVtLnJzU2VydmljZUluc3RhbmNlcy5zdXBwbGllck5hbWUNCiAgICAgICAgICAgIGl0ZW0ucnNDaGFyZ2VMaXN0LnB1c2gocm93KQ0KICAgICAgICAgIH0NCiAgICAgICAgICByZXR1cm4gaXRlbQ0KICAgICAgICB9KQ0KICAgICAgfQ0KICAgICAgaWYgKHNlcnZpY2VUeXBlSWQgPT09IDUxKSB7DQogICAgICAgIHRoaXMuZm9ybS5yc09wQnVsa1RydWNrTGlzdCA9IHRoaXMuZm9ybS5yc09wQnVsa1RydWNrTGlzdC5tYXAoaXRlbSA9PiB7DQogICAgICAgICAgaWYgKGl0ZW0uc2VhSWQgPT09IHRoaXMuY3VyRnJlaWdodFNlbGVjdFJvdy5zZWFJZCkgew0KICAgICAgICAgICAgcm93LmNsZWFyaW5nQ29tcGFueUlkID0gaXRlbS5yc1NlcnZpY2VJbnN0YW5jZXMuc3VwcGxpZXJJZA0KICAgICAgICAgICAgcm93LmNvbXBhbnlOYW1lID0gaXRlbS5yc1NlcnZpY2VJbnN0YW5jZXMuc3VwcGxpZXJOYW1lDQogICAgICAgICAgICBpdGVtLnJzQ2hhcmdlTGlzdC5wdXNoKHJvdykNCiAgICAgICAgICB9DQogICAgICAgICAgcmV0dXJuIGl0ZW0NCiAgICAgICAgfSkNCiAgICAgIH0NCiAgICAgIGlmIChzZXJ2aWNlVHlwZUlkID09PSA2MCkgew0KICAgICAgICB0aGlzLmZvcm0ucnNPcERvY0RlY2xhcmVMaXN0ID0gdGhpcy5mb3JtLnJzT3BEb2NEZWNsYXJlTGlzdC5tYXAoaXRlbSA9PiB7DQogICAgICAgICAgaWYgKGl0ZW0uc2VhSWQgPT09IHRoaXMuY3VyRnJlaWdodFNlbGVjdFJvdy5zZWFJZCkgew0KICAgICAgICAgICAgcm93LmNsZWFyaW5nQ29tcGFueUlkID0gaXRlbS5yc1NlcnZpY2VJbnN0YW5jZXMuc3VwcGxpZXJJZA0KICAgICAgICAgICAgcm93LmNvbXBhbnlOYW1lID0gaXRlbS5yc1NlcnZpY2VJbnN0YW5jZXMuc3VwcGxpZXJOYW1lDQogICAgICAgICAgICBpdGVtLnJzQ2hhcmdlTGlzdC5wdXNoKHJvdykNCiAgICAgICAgICB9DQogICAgICAgICAgcmV0dXJuIGl0ZW0NCiAgICAgICAgfSkNCiAgICAgIH0NCiAgICAgIGlmIChzZXJ2aWNlVHlwZUlkID09PSA2MSkgew0KICAgICAgICB0aGlzLmZvcm0ucnNPcEZyZWVEZWNsYXJlTGlzdCA9IHRoaXMuZm9ybS5yc09wRnJlZURlY2xhcmVMaXN0Lm1hcChpdGVtID0+IHsNCiAgICAgICAgICBpZiAoaXRlbS5zZWFJZCA9PT0gdGhpcy5jdXJGcmVpZ2h0U2VsZWN0Um93LnNlYUlkKSB7DQogICAgICAgICAgICByb3cuY2xlYXJpbmdDb21wYW55SWQgPSBpdGVtLnJzU2VydmljZUluc3RhbmNlcy5zdXBwbGllcklkDQogICAgICAgICAgICByb3cuY29tcGFueU5hbWUgPSBpdGVtLnJzU2VydmljZUluc3RhbmNlcy5zdXBwbGllck5hbWUNCiAgICAgICAgICAgIGl0ZW0ucnNDaGFyZ2VMaXN0LnB1c2gocm93KQ0KICAgICAgICAgIH0NCiAgICAgICAgICByZXR1cm4gaXRlbQ0KICAgICAgICB9KQ0KICAgICAgfQ0KICAgICAgaWYgKHNlcnZpY2VUeXBlSWQgPT09IDcwKSB7DQogICAgICAgIHJvdy5jbGVhcmluZ0NvbXBhbnlJZCA9IHRoaXMucnNPcERPQWdlbnRTZXJ2aWNlSW5zdGFuY2Uuc3VwcGxpZXJJZA0KICAgICAgICByb3cuY29tcGFueU5hbWUgPSB0aGlzLnJzT3BET0FnZW50U2VydmljZUluc3RhbmNlLnN1cHBsaWVyTmFtZQ0KICAgICAgICB0aGlzLnJzT3BET0FnZW50LnJzQ2hhcmdlTGlzdC5wdXNoKHJvdykNCiAgICAgIH0NCiAgICAgIGlmIChzZXJ2aWNlVHlwZUlkID09PSA3MSkgew0KICAgICAgICByb3cuY2xlYXJpbmdDb21wYW55SWQgPSB0aGlzLnJzT3BDbGVhckFnZW50U2VydmljZUluc3RhbmNlLnN1cHBsaWVySWQNCiAgICAgICAgcm93LmNvbXBhbnlOYW1lID0gdGhpcy5yc09wQ2xlYXJBZ2VudFNlcnZpY2VJbnN0YW5jZS5zdXBwbGllck5hbWUNCiAgICAgICAgdGhpcy5yc09wQ2xlYXJBZ2VudC5yc0NoYXJnZUxpc3QucHVzaChyb3cpDQogICAgICB9DQogICAgICBpZiAoc2VydmljZVR5cGVJZCA9PT0gODApIHsNCiAgICAgICAgcm93LmNsZWFyaW5nQ29tcGFueUlkID0gdGhpcy5yc09wV0hTU2VydmljZUluc3RhbmNlLnN1cHBsaWVySWQNCiAgICAgICAgcm93LmNvbXBhbnlOYW1lID0gdGhpcy5yc09wV0hTU2VydmljZUluc3RhbmNlLnN1cHBsaWVyTmFtZQ0KICAgICAgICB0aGlzLnJzT3BXSFMucnNDaGFyZ2VMaXN0LnB1c2gocm93KQ0KICAgICAgfQ0KICAgICAgaWYgKHNlcnZpY2VUeXBlSWQgPT09IDkwKSB7DQogICAgICAgIHJvdy5jbGVhcmluZ0NvbXBhbnlJZCA9IHRoaXMucnNPcDNyZENlcnRTZXJ2aWNlSW5zdGFuY2Uuc3VwcGxpZXJJZA0KICAgICAgICByb3cuY29tcGFueU5hbWUgPSB0aGlzLnJzT3AzcmRDZXJ0U2VydmljZUluc3RhbmNlLnN1cHBsaWVyTmFtZQ0KICAgICAgICB0aGlzLnJzT3AzcmRDZXJ0LnJzQ2hhcmdlTGlzdC5wdXNoKHJvdykNCiAgICAgIH0NCiAgICAgIGlmIChzZXJ2aWNlVHlwZUlkID09PSAxMDApIHsNCiAgICAgICAgcm93LmNsZWFyaW5nQ29tcGFueUlkID0gdGhpcy5yc09wSU5TU2VydmljZUluc3RhbmNlLnN1cHBsaWVySWQNCiAgICAgICAgcm93LmNvbXBhbnlOYW1lID0gdGhpcy5yc09wSU5TU2VydmljZUluc3RhbmNlLnN1cHBsaWVyTmFtZQ0KICAgICAgICB0aGlzLnJzT3BJTlMucnNDaGFyZ2VMaXN0LnB1c2gocm93KQ0KICAgICAgfQ0KICAgICAgaWYgKHNlcnZpY2VUeXBlSWQgPT09IDEwMSkgew0KICAgICAgICByb3cuY2xlYXJpbmdDb21wYW55SWQgPSB0aGlzLnJzT3BUcmFkaW5nU2VydmljZUluc3RhbmNlLnN1cHBsaWVySWQNCiAgICAgICAgcm93LmNvbXBhbnlOYW1lID0gdGhpcy5yc09wVHJhZGluZ1NlcnZpY2VJbnN0YW5jZS5zdXBwbGllck5hbWUNCiAgICAgICAgdGhpcy5yc09wVHJhZGluZy5yc0NoYXJnZUxpc3QucHVzaChyb3cpDQogICAgICB9DQogICAgICBpZiAoc2VydmljZVR5cGVJZCA9PT0gMTAyKSB7DQogICAgICAgIHJvdy5jbGVhcmluZ0NvbXBhbnlJZCA9IHRoaXMucnNPcEZ1bWlnYXRpb25TZXJ2aWNlSW5zdGFuY2Uuc3VwcGxpZXJJZA0KICAgICAgICByb3cuY29tcGFueU5hbWUgPSB0aGlzLnJzT3BGdW1pZ2F0aW9uU2VydmljZUluc3RhbmNlLnN1cHBsaWVyTmFtZQ0KICAgICAgICB0aGlzLnJzT3BGdW1pZ2F0aW9uLnJzQ2hhcmdlTGlzdC5wdXNoKHJvdykNCiAgICAgIH0NCiAgICAgIGlmIChzZXJ2aWNlVHlwZUlkID09PSAxMDMpIHsNCiAgICAgICAgcm93LmNsZWFyaW5nQ29tcGFueUlkID0gdGhpcy5yc09wQ09TZXJ2aWNlSW5zdGFuY2Uuc3VwcGxpZXJJZA0KICAgICAgICByb3cuY29tcGFueU5hbWUgPSB0aGlzLnJzT3BDT1NlcnZpY2VJbnN0YW5jZS5zdXBwbGllck5hbWUNCiAgICAgICAgdGhpcy5yc09wQ08ucnNDaGFyZ2VMaXN0LnB1c2gocm93KQ0KICAgICAgfQ0KICAgICAgaWYgKHNlcnZpY2VUeXBlSWQgPT09IDEwNCkgew0KICAgICAgICByb3cuY2xlYXJpbmdDb21wYW55SWQgPSB0aGlzLnJzT3BDT1NlcnZpY2VJbnN0YW5jZS5zdXBwbGllcklkDQogICAgICAgIHJvdy5jb21wYW55TmFtZSA9IHRoaXMucnNPcENPU2VydmljZUluc3RhbmNlLnN1cHBsaWVyTmFtZQ0KICAgICAgICB0aGlzLnJzT3BPdGhlci5yc0NoYXJnZUxpc3QucHVzaChyb3cpDQogICAgICB9DQogICAgfSwNCiAgICBnZXRFeGNoYW5nZVJhdGUocm93LCBjYWxsYmFjaykgew0KICAgICAgbGV0IHJlDQogICAgICBpZiAocm93KSB7DQogICAgICAgIGZvciAoY29uc3QgYSBvZiB0aGlzLiRzdG9yZS5zdGF0ZS5kYXRhLmV4Y2hhbmdlUmF0ZUxpc3QpIHsNCiAgICAgICAgICBpZiAoYS5iYXNpY0N1cnJlbmN5ID09ICJSTUIiDQogICAgICAgICAgICAmJiByb3cuZG5DdXJyZW5jeUNvZGUgPT0gYS5jdXJyZW5jeUNvZGUNCiAgICAgICAgICAgICYmIHBhcnNlVGltZShhLnZhbGlkRnJvbSkgPD0gcGFyc2VUaW1lKHJvdy5jcmVhdGVUaW1lKQ0KICAgICAgICAgICAgJiYgcGFyc2VUaW1lKHJvdy5jcmVhdGVUaW1lKSA8PSBwYXJzZVRpbWUoYS52YWxpZFRvKSkgew0KICAgICAgICAgICAgcmUgPSBhLmV4Y2hhbmdlUmF0ZSAvIGEuYmFzZQ0KICAgICAgICAgIH0NCiAgICAgICAgfQ0KICAgICAgICBjYWxsYmFjayhyZSkNCiAgICAgIH0NCiAgICB9LA0KICAgIGFzeW5jIGdldEV4Y2hhbmdlUmF0ZURpcmVjdChyb3cpIHsNCiAgICAgIGxldCByZQ0KICAgICAgbGV0IHJlc3BvbnNlID0gYXdhaXQgc2VsZWN0TGlzdEV4Y2hhbmdlcmF0ZSgpDQogICAgICBpZiAocm93KSB7DQogICAgICAgIGZvciAobGV0IGEgb2YgcmVzcG9uc2UuZGF0YSkgew0KICAgICAgICAgIGlmIChhLmJhc2ljQ3VycmVuY3kgPT0gIlJNQiINCiAgICAgICAgICAgICYmIHJvdy5xdW90YXRpb25DdXJyZW5jeUNvZGUgPT0gYS5jdXJyZW5jeQ0KICAgICAgICAgICAgJiYgcGFyc2VUaW1lKGEudmFsaWRGcm9tKSA8PSBwYXJzZVRpbWUocm93LmNyZWF0ZVRpbWUpDQogICAgICAgICAgICAmJiBwYXJzZVRpbWUocm93LmNyZWF0ZVRpbWUpIDw9IHBhcnNlVGltZShhLnZhbGlkVG8pKSB7DQogICAgICAgICAgICByZSA9IGEuZXhjaGFuZ2VSYXRlIC8gYS5iYXNlDQogICAgICAgICAgfQ0KICAgICAgICB9DQogICAgICB9DQogICAgICByZXR1cm4gcmUNCiAgICB9LA0KICAgIHJlc2V0Q2hhcmdlKCkgew0KICAgICAgdGhpcy5yc0NoYXJnZSA9IHsNCiAgICAgICAgY2hhcmdlSWQ6IG51bGwsDQogICAgICAgIHNxZFJjdElkOiBudWxsLA0KICAgICAgICBzZXJ2aWNlSWQ6IG51bGwsDQogICAgICAgIHNxZFNlcnZpY2VUeXBlSWQ6IG51bGwsDQogICAgICAgIHNxZFJjdE5vOiBudWxsLA0KICAgICAgICByZWxhdGVkRnJlaWdodElkOiBudWxsLA0KICAgICAgICBpc1JlY2lldmluZ09yUGF5aW5nOiBudWxsLA0KICAgICAgICBjbGVhcmluZ0NvbXBhbnlJZDogbnVsbCwNCiAgICAgICAgY2xlYXJpbmdDb21wYW55U3VtbWFyeTogbnVsbCwNCiAgICAgICAgcXVvdGF0aW9uU3RyYXRlZ3lJZDogbnVsbCwNCiAgICAgICAgZG5DaGFyZ2VOYW1lSWQ6IG51bGwsDQogICAgICAgIGRuQ3VycmVuY3lDb2RlOiBudWxsLA0KICAgICAgICBkblVuaXRSYXRlOiBudWxsLA0KICAgICAgICBkblVuaXRDb2RlOiBudWxsLA0KICAgICAgICBkbkFtb3VudDogbnVsbCwNCiAgICAgICAgYmFzaWNDdXJyZW5jeVJhdGU6IG51bGwsDQogICAgICAgIGR1dHlSYXRlOiBudWxsLA0KICAgICAgICBzdWJ0b3RhbDogbnVsbCwNCiAgICAgICAgY2hhcmdlUmVtYXJrOiBudWxsLA0KICAgICAgICBjbGVhcmluZ0N1cnJlbmN5Q29kZTogbnVsbCwNCiAgICAgICAgZG5DdXJyZW5jeVJlY2VpdmVkOiBudWxsLA0KICAgICAgICBkbkN1cnJlbmN5UGFpZDogbnVsbCwNCiAgICAgICAgZG5DdXJyZW5jeUJhbGFuY2U6IG51bGwsDQogICAgICAgIGFjY291bnRSZWNlaXZlZElkTGlzdDogbnVsbCwNCiAgICAgICAgYWNjb3VudFBhaWRJZExpc3Q6IG51bGwsDQogICAgICAgIGxvZ2lzdGljc0ludm9pY2VJZExpc3Q6IG51bGwNCiAgICAgIH0NCiAgICB9LA0KICAgIHNlbGVjdENvbXBhbnkoKSB7DQogICAgICB0aGlzLm9wZW5Db21wYW55U2VsZWN0ID0gdHJ1ZQ0KICAgIH0sDQogICAgc2VsZWN0Q29tcGFueURhdGEocm93KSB7DQogICAgICB0aGlzLmZvcm0uY2xpZW50TmFtZSA9IHJvdy5jb21wYW55VGF4Q29kZSArICIvIiArIChyb3cuY29tcGFueVNob3J0TmFtZSA/IHJvdy5jb21wYW55U2hvcnROYW1lIDogcm93LmNvbXBhbnlFblNob3J0TmFtZSkgKyAiLyIgKyByb3cuY29tcGFueUxvY2FsTmFtZQ0KICAgICAgdGhpcy5mb3JtLmNsaWVudElkID0gcm93LmNvbXBhbnlJZA0KICAgICAgdGhpcy5mb3JtLmNsaWVudENvbnRhY3QgPSByb3cubWFpblN0YWZmT2ZmaWNpYWxOYW1lDQogICAgICB0aGlzLmZvcm0uY2xpZW50Q29udGFjdFRlbCA9IHJvdy5zdGFmZk1vYmlsZQ0KICAgICAgdGhpcy5mb3JtLmNsaWVudENvbnRhY3RFbWFpbCA9IHJvdy5zdGFmZkVtYWlsDQogICAgICB0aGlzLmZvcm0ucm9sZUlkcyA9IHJvdy5yb2xlSWRzDQogICAgICB0aGlzLmZvcm0uc2FsZXNJZCA9IHJvdy5iZWxvbmdUbw0KICAgICAgLy8gdGhpcy5zYWxlc0lkID0gcm93LmJlbG9uZ1RvDQogICAgICB0aGlzLmZvcm0uc2FsZXNBc3Npc3RhbnRJZCA9IHJvdy5mb2xsb3dVcA0KICAgICAgLy8gdGhpcy5zYWxlc0Fzc2lzdGFudElkID0gcm93LmZvbGxvd1VwDQogICAgICB0aGlzLmZvcm0ub3JkZXJCZWxvbmdzVG8gPSByb3cuY29tcGFueUJlbG9uZ1RvDQogICAgICB0aGlzLmZvcm0uYWdyZWVtZW50TnVtYmVyID0gcm93LmFncmVlbWVudE51bWJlcg0KICAgICAgdGhpcy5mb3JtLnBheW1lbnROb2RlID0gKHJvdy5yZWNlaXZlU3RhbmRhcmQgPyByb3cucmVjZWl2ZVN0YW5kYXJkIDogIiIpICsgKHJvdy5yZWNlaXZlVGVybSA/IHJvdy5yZWNlaXZlVGVybSA6ICIiKQ0KICAgICAgdGhpcy5jb21wYW55TGlzdC5pbmRleE9mKHJvdy5jb21wYW55SWQpID09PSAtMSA/IHRoaXMuY29tcGFueUxpc3QucHVzaChyb3cpIDogbnVsbA0KDQogICAgICB0aGlzLm9wZW5Db21wYW55U2VsZWN0ID0gZmFsc2UNCg0KICAgICAgaWYgKHRoaXMuYmVsb25nTGlzdCAhPSB1bmRlZmluZWQpIHsNCiAgICAgICAgZm9yIChjb25zdCBhIG9mIHRoaXMuYmVsb25nTGlzdCkgew0KICAgICAgICAgIGlmIChhLmNoaWxkcmVuICE9IHVuZGVmaW5lZCkgew0KICAgICAgICAgICAgZm9yIChjb25zdCBiIG9mIGEuY2hpbGRyZW4pIHsNCiAgICAgICAgICAgICAgaWYgKGIuY2hpbGRyZW4gIT0gdW5kZWZpbmVkKSB7DQogICAgICAgICAgICAgICAgZm9yIChjb25zdCBjIG9mIGIuY2hpbGRyZW4pIHsNCiAgICAgICAgICAgICAgICAgIGlmIChjLnN0YWZmSWQgPT0gdGhpcy5mb3JtLnNhbGVzSWQpIHsNCiAgICAgICAgICAgICAgICAgICAgdGhpcy5zYWxlc0lkID0gYy5kZXB0SWQNCiAgICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICAgIGlmIChjLnN0YWZmSWQgPT0gdGhpcy5mb3JtLnNhbGVzQXNzaXN0YW50SWQpIHsNCiAgICAgICAgICAgICAgICAgICAgdGhpcy5zYWxlc0Fzc2lzdGFudElkID0gYy5kZXB0SWQNCiAgICAgICAgICAgICAgICAgIH0NCiAgICAgICAgICAgICAgICB9DQogICAgICAgICAgICAgIH0NCiAgICAgICAgICAgIH0NCiAgICAgICAgICB9DQogICAgICAgIH0NCiAgICAgIH0NCiAgICB9LA0KICAgIGdldENvbXBhbnlSb2xlSWRzKHZhbCkgew0KICAgICAgdGhpcy5mb3JtLnJvbGVJZHMgPSB2YWwNCiAgICB9LA0KICAgIG9wZW5QcmludFRlbXBsYXRlSW5OZXdUYWIoKSB7DQogICAgICAvLyB0aGlzLiR0YWIub3BlblBhZ2UoJ+aJk+WNsCcsICcvcHJpbnQvdGVtcGxhdGUnKQ0KICAgICAgY29uc3Qgcm91dGVVcmwgPSB0aGlzLiRyb3V0ZXIucmVzb2x2ZSh7DQogICAgICAgIHBhdGg6ICIvcHJpbnQvdGVtcGxhdGUiLA0KICAgICAgICBwYXJhbXM6IHtmb3JtOiAieHh4In0NCiAgICAgIH0pDQogICAgICB3aW5kb3cub3Blbihyb3V0ZVVybC5ocmVmLCAiX2JsYW5rIikNCiAgICB9DQogIH0NCn0NCg=="}, null]}