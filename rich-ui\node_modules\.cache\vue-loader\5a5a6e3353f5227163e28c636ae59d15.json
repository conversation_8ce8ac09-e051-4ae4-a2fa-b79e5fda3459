{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\outboundRecord\\index.vue?vue&type=style&index=0&id=1cb4d65a&scoped=true&lang=css&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\outboundRecord\\index.vue", "mtime": 1750840424298}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\css-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\loaders\\stylePostLoader.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\postcss-loader\\src\\index.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:CgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoKCgoNCi8qIOaVsOWtl+Wtl+auteaYvuekuuagt+W8jyAqLw0KLmVsLXRhYmxlIC5jZWxsIHNwYW4gew0KICBjdXJzb3I6IHBvaW50ZXI7DQogIGNvbG9yOiAjNjA2MjY2Ow0KICBwYWRkaW5nOiAycHggNXB4Ow0KICBib3JkZXItcmFkaXVzOiAzcHg7DQogIHRyYW5zaXRpb246IGJhY2tncm91bmQtY29sb3IgMC4zczsNCn0NCg0KLmVsLXRhYmxlIC5jZWxsIHNwYW46aG92ZXIgew0KICBiYWNrZ3JvdW5kLWNvbG9yOiAjZjVmN2ZhOw0KfQ0KDQoNCg=="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA05CA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/system/outboundRecord", "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-row :gutter=\"20\">\r\n      <el-col :span=\"showLeft\">\r\n        <el-form v-show=\"showSearch\" ref=\"queryForm\" :inline=\"true\" :model=\"queryParams\" class=\"query\"\r\n                 label-width=\"68px\"\r\n                 size=\"mini\"\r\n        >\r\n          <el-form-item label=\"出仓单号\" prop=\"outboundNo\">\r\n            <el-input\r\n              v-model=\"queryParams.outboundNo\"\r\n              clearable\r\n              placeholder=\"出仓单号\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"客户代码\" prop=\"clientCode\">\r\n            <el-input\r\n              v-model=\"queryParams.clientCode\"\r\n              clearable\r\n              placeholder=\"客户代码\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"出仓日期\" prop=\"outboundDate\">\r\n            <el-date-picker v-model=\"queryParams.outboundDateRange\"\r\n                            clearable style=\"width: 100%\"\r\n                            placeholder=\"出仓日期\"\r\n                            type=\"daterange\"\r\n                            :default-time=\"['00:00:00', '23:59:59']\"\r\n                            value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n            >\r\n            </el-date-picker>\r\n          </el-form-item>\r\n          <el-form-item>\r\n            <el-button icon=\"el-icon-search\" size=\"mini\" type=\"primary\" @click=\"handleQuery\">搜索</el-button>\r\n            <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n          </el-form-item>\r\n        </el-form>\r\n      </el-col>\r\n      <el-col :span=\"showRight\">\r\n        <el-row :gutter=\"10\" class=\"mb8\">\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              v-hasPermi=\"['system:inventory:export']\"\r\n              icon=\"el-icon-download\"\r\n              plain\r\n              size=\"mini\"\r\n              type=\"primary\"\r\n              @click=\"handleExport\"\r\n            >导出\r\n            </el-button>\r\n          </el-col>\r\n          <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n        </el-row>\r\n\r\n        <!--出仓记录列表(预出仓记录列表)-->\r\n        <el-table v-loading=\"loading\" :data=\"outboundrecordList\" @selection-change=\"handleSelectionChange\"\r\n                  @row-dblclick=\"findOutboundRecord\"\r\n        >\r\n          <el-table-column align=\"center\" type=\"selection\" width=\"28\"/>\r\n          <el-table-column align=\"center\" label=\"出仓单号\" prop=\"outboundNo\"/>\r\n          <el-table-column align=\"center\" label=\"客户单号\" prop=\"customerOrderNo\"/>\r\n          <el-table-column align=\"center\" label=\"客户代码\" prop=\"clientCode\"/>\r\n          <el-table-column align=\"center\" label=\"客户名称\" prop=\"clientName\" show-overflow-tooltip/>\r\n          <el-table-column align=\"center\" label=\"操作员\" prop=\"operator\"/>\r\n          <el-table-column align=\"center\" label=\"柜型\" prop=\"containerType\"/>\r\n          <el-table-column align=\"center\" label=\"柜号\" prop=\"containerNo\" show-overflow-tooltip/>\r\n          <el-table-column align=\"center\" label=\"封号\" prop=\"sealNo\"/>\r\n          <el-table-column align=\"center\" label=\"出仓日期\" prop=\"outboundDate\" width=\"100\">\r\n            <template slot-scope=\"scope\">\r\n              <span>{{ parseTime(scope.row.outboundDate, \"{y}-{m}-{d}\") }}</span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"仓库报价\" prop=\"warehouseQuote\"/>\r\n          <el-table-column align=\"center\" label=\"工人装柜费\" prop=\"workerLoadingFee\"/>\r\n          <el-table-column align=\"center\" label=\"仓管代收\" prop=\"warehouseCollection\"/>\r\n          <el-table-column align=\"center\" label=\"补收入仓费\" prop=\"additionalStorageFee\">\r\n            <template slot-scope=\"scope\">\r\n              <el-popover\r\n                placement=\"top\"\r\n                popper-class=\"fee-edit-popover\"\r\n                trigger=\"click\"\r\n                width=\"200\"\r\n              >\r\n                <el-input-number\r\n                  v-model=\"scope.row.additionalStorageFee\"\r\n                  :controls=\"false\"\r\n                  :precision=\"2\"\r\n                  size=\"mini\"\r\n                  style=\"width: 100%\"\r\n                  @change=\"handleFeeChange(scope.row, 'additionalStorageFee', $event)\"\r\n                />\r\n                <span slot=\"reference\">{{ scope.row.additionalStorageFee || 0 }}</span>\r\n              </el-popover>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"已收入仓费\" prop=\"receivedStorageFee\">\r\n            <template slot-scope=\"scope\">\r\n              <el-popover\r\n                placement=\"top\"\r\n                popper-class=\"fee-edit-popover\"\r\n                trigger=\"click\"\r\n                width=\"200\"\r\n              >\r\n                <el-input-number\r\n                  v-model=\"scope.row.receivedStorageFee\"\r\n                  :controls=\"false\"\r\n                  :precision=\"2\"\r\n                  size=\"mini\"\r\n                  style=\"width: 100%\"\r\n                  @change=\"handleFeeChange(scope.row, 'receivedStorageFee', $event)\"\r\n                />\r\n                <span slot=\"reference\">{{ scope.row.receivedStorageFee || 0 }}</span>\r\n              </el-popover>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"未收卸货费\" prop=\"unpaidUnloadingFee\">\r\n            <template slot-scope=\"scope\">\r\n              <el-popover\r\n                placement=\"top\"\r\n                popper-class=\"fee-edit-popover\"\r\n                trigger=\"click\"\r\n                width=\"200\"\r\n              >\r\n                <el-input-number\r\n                  v-model=\"scope.row.unpaidUnloadingFee\"\r\n                  :controls=\"false\"\r\n                  :precision=\"2\"\r\n                  size=\"mini\"\r\n                  style=\"width: 100%\"\r\n                  @change=\"handleFeeChange(scope.row, 'unpaidUnloadingFee', $event)\"\r\n                />\r\n                <span slot=\"reference\">{{ scope.row.unpaidUnloadingFee || 0 }}</span>\r\n              </el-popover>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"实付卸货费\" prop=\"receivedUnloadingFee\">\r\n            <template slot-scope=\"scope\">\r\n              <el-popover\r\n                placement=\"top\"\r\n                popper-class=\"fee-edit-popover\"\r\n                trigger=\"click\"\r\n                width=\"200\"\r\n              >\r\n                <el-input-number\r\n                  v-model=\"scope.row.receivedUnloadingFee\"\r\n                  :controls=\"false\"\r\n                  :precision=\"2\"\r\n                  size=\"mini\"\r\n                  style=\"width: 100%\"\r\n                  @change=\"handleFeeChange(scope.row, 'receivedUnloadingFee', $event)\"\r\n                />\r\n                <span slot=\"reference\">{{ scope.row.receivedUnloadingFee || 0 }}</span>\r\n              </el-popover>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"未收打包费\" prop=\"unpaidPackingFee\">\r\n            <template slot-scope=\"scope\">\r\n              <el-popover\r\n                placement=\"top\"\r\n                popper-class=\"fee-edit-popover\"\r\n                trigger=\"click\"\r\n                width=\"200\"\r\n              >\r\n                <el-input-number\r\n                  v-model=\"scope.row.unpaidPackingFee\"\r\n                  :controls=\"false\"\r\n                  :precision=\"2\"\r\n                  size=\"mini\"\r\n                  style=\"width: 100%\"\r\n                  @change=\"handleFeeChange(scope.row, 'unpaidPackingFee', $event)\"\r\n                />\r\n                <span slot=\"reference\">{{ scope.row.unpaidPackingFee || 0 }}</span>\r\n              </el-popover>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"实付打包费\" prop=\"receivedPackingFee\">\r\n            <template slot-scope=\"scope\">\r\n              <el-popover\r\n                placement=\"top\"\r\n                popper-class=\"fee-edit-popover\"\r\n                trigger=\"click\"\r\n                width=\"200\"\r\n              >\r\n                <el-input-number\r\n                  v-model=\"scope.row.receivedPackingFee\"\r\n                  :controls=\"false\"\r\n                  :precision=\"2\"\r\n                  size=\"mini\"\r\n                  style=\"width: 100%\"\r\n                  @change=\"handleFeeChange(scope.row, 'receivedPackingFee', $event)\"\r\n                />\r\n                <span slot=\"reference\">{{ scope.row.receivedPackingFee || 0 }}</span>\r\n              </el-popover>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"物流代垫费\" prop=\"logisticsAdvanceFee\">\r\n            <template slot-scope=\"scope\">\r\n              <el-popover\r\n                placement=\"top\"\r\n                popper-class=\"fee-edit-popover\"\r\n                trigger=\"click\"\r\n                width=\"200\"\r\n              >\r\n                <el-input-number\r\n                  v-model=\"scope.row.logisticsAdvanceFee\"\r\n                  :controls=\"false\"\r\n                  :precision=\"2\"\r\n                  size=\"mini\"\r\n                  style=\"width: 100%\"\r\n                  @change=\"handleFeeChange(scope.row, 'logisticsAdvanceFee', $event)\"\r\n                />\r\n                <span slot=\"reference\">{{ scope.row.logisticsAdvanceFee || 0 }}</span>\r\n              </el-popover>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"超期租金单价\" prop=\"overdueRentalUnitPrice\"/>\r\n          <el-table-column align=\"center\" label=\"超期租金\" prop=\"overdueRentalFee\"/>\r\n          <el-table-column align=\"center\" label=\"唛头\" prop=\"sqdShippingMark\" show-overflow-tooltip/>\r\n          <el-table-column align=\"center\" label=\"总货名\" prop=\"cargoName\" show-overflow-tooltip/>\r\n          <el-table-column align=\"center\" label=\"免堆期\" prop=\"freeStackPeriod\" width=\"50\"/>\r\n          <el-table-column align=\"center\" class-name=\"small-padding fixed-width\" label=\"操作\">\r\n            <template slot-scope=\"scope\">\r\n              <!--<el-button\r\n                v-hasPermi=\"['system:outboundrecord:edit']\"\r\n                icon=\"el-icon-edit\"\r\n                size=\"mini\"\r\n                style=\"margin-right: -8px\"\r\n                type=\"success\"\r\n                @click=\"handleUpdate(scope.row)\"\r\n              >修改\r\n              </el-button>-->\r\n              <el-button\r\n                v-hasPermi=\"['system:outboundrecord:remove']\"\r\n                icon=\"el-icon-delete\"\r\n                size=\"mini\"\r\n                style=\"margin-right: -8px\"\r\n                type=\"danger\"\r\n                @click=\"handleDelete(scope.row)\"\r\n              >删除\r\n              </el-button>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n        <pagination\r\n          v-show=\"total>0\"\r\n          :limit.sync=\"queryParams.pageSize\"\r\n          :page.sync=\"queryParams.pageNum\"\r\n          :total=\"total\"\r\n          @pagination=\"getList\"\r\n        />\r\n      </el-col>\r\n    </el-row>\r\n\r\n    <!-- 出仓对话框 -->\r\n    <el-dialog\r\n      v-dialogDrag\r\n      v-dialogDragWidth :close-on-click-modal=\"false\" :modal-append-to-body=\"false\" :title=\"'出库单'\"\r\n      :visible.sync=\"openOutbound\"\r\n      append-to-body\r\n      width=\"80%\"\r\n    >\r\n      <el-form ref=\"outboundForm\" :model=\"outboundForm\" :rules=\"rules\" class=\"edit\" label-width=\"80px\">\r\n        <el-row :gutter=\"10\">\r\n          <el-row :gutter=\"10\">\r\n            <el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"出仓单号\" prop=\"outboundNo\">\r\n                  <el-input v-model=\"outboundForm.outboundNo\" class=\"disable-form\" disabled placeholder=\"出仓单号\"/>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"客户单号\" prop=\"outboundNo\">\r\n                  <el-input v-model=\"outboundForm.customerOrderNo\" placeholder=\"客户单号\"/>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"客户代码\" prop=\"outboundNo\">\r\n                  <tree-select :flat=\"false\" :multiple=\"false\" :pass=\"outboundForm.clientCode\"\r\n                               :placeholder=\"'客户代码'\"\r\n                               :type=\"'warehouseClient'\" @return=\"outboundForm.clientCode=$event\"\r\n                               @returnData=\"outboundClient($event)\"\r\n                  />\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"客户名称\" prop=\"outboundNo\">\r\n                  <el-input v-model=\"outboundForm.clientName\" class=\"disable-form\" disabled\r\n                            placeholder=\"客户名称\"\r\n                  />\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"10\">\r\n            <el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"计划出仓\" prop=\"inboundDate\">\r\n                  <el-date-picker v-model=\"outboundForm.plannedOutboundDate\"\r\n                                  clearable\r\n                                  placeholder=\"计划出仓日期\"\r\n                                  style=\"width: 100%\"\r\n                                  type=\"date\"\r\n                                  value-format=\"yyyy-MM-dd\"\r\n                  >\r\n                  </el-date-picker>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"出仓方式\" prop=\"outboundType\">\r\n                  <el-select v-model=\"outboundForm.outboundType\" placeholder=\"出仓方式\" style=\"width: 100%\">\r\n                    <el-option label=\"整柜\" value=\"整柜\"></el-option>\r\n                    <el-option label=\"散货\" value=\"散货\"></el-option>\r\n                    <el-option label=\"快递\" value=\"快递\"></el-option>\r\n                    <el-option label=\"其他\" value=\"其他\"></el-option>\r\n                  </el-select>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"柜型\" prop=\"containerType\">\r\n                  <el-select v-model=\"outboundForm.containerType\" style=\"width: 100%\" @change=\"selectContainerType\">\r\n                    <el-option label=\"20GP\" value=\"20GP\"/>\r\n                    <el-option label=\"20OT\" value=\"20OT\"/>\r\n                    <el-option label=\"20FR\" value=\"20FR\"/>\r\n                    <el-option label=\"TANK\" value=\"TANK\"/>\r\n                    <el-option label=\"40GP\" value=\"40GP\"/>\r\n                    <el-option label=\"40HQ\" value=\"40HQ\"/>\r\n                    <el-option label=\"40NOR\" value=\"40NOR\"/>\r\n                    <el-option label=\"40OT\" value=\"40OT\"/>\r\n                    <el-option label=\"40FR\" value=\"40FR\"/>\r\n                    <el-option label=\"40RH\" value=\"40RH\"/>\r\n                    <el-option label=\"45HQ\" value=\"45HQ\"/>\r\n                  </el-select>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"货物类型\" prop=\"cargoType\">\r\n                  <el-select v-model=\"form.cargoType\" placeholder=\"请选择货物类型\" style=\"width: 100%\">\r\n                    <el-option label=\"普货\" value=\"普货\"></el-option>\r\n                    <el-option label=\"大件\" value=\"大件\"></el-option>\r\n                    <el-option label=\"鲜活\" value=\"鲜活\"></el-option>\r\n                    <el-option label=\"危品\" value=\"危品\"></el-option>\r\n                    <el-option label=\"冷冻\" value=\"冷冻\"></el-option>\r\n                    <el-option label=\"标记\" value=\"标记\"></el-option>\r\n                  </el-select>\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"10\">\r\n            <el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"柜号\" prop=\"containerNo\">\r\n                  <el-input v-model=\"outboundForm.containerNo\" placeholder=\"柜号\"/>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"封号\" prop=\"sealNo\">\r\n                  <el-input v-model=\"outboundForm.sealNo\" placeholder=\"封号\"/>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"车牌\" prop=\"plateNumber\">\r\n                  <el-input v-model=\"outboundForm.plateNumber\" placeholder=\"车牌\"/>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"司机电话\" prop=\"driverPhone\">\r\n                  <el-input v-model=\"outboundForm.driverPhone\" placeholder=\"司机电话\"/>\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"10\">\r\n            <el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"仓库报价\" prop=\"warehouseQuote\">\r\n                  <el-input v-model=\"outboundForm.warehouseQuote\" class=\"number\" placeholder=\"仓库报价\"/>\r\n                  <!--<el-col :span=\"12\">\r\n                    <el-input v-model=\"outboundForm.difficultyWorkFee\" class=\"number\" placeholder=\"困难作业费\"/>\r\n                  </el-col>-->\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"仓管代收\" prop=\"outboundNotes\">\r\n                  <el-input v-model=\"outboundForm.warehouseCollection\" class=\"number\" placeholder=\"仓管代收\"/>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"工人装柜费\" prop=\"workerLoadingFee\">\r\n                  <el-input v-model=\"outboundForm.workerLoadingFee\" class=\"number\" placeholder=\"工人装柜费\"/>\r\n                  <!--<el-col :span=\"12\">\r\n                    <el-input v-model=\"outboundForm.warehouseAdvanceOtherFee\" class=\"number\"\r\n                              placeholder=\"仓库代付其他费用\"\r\n                    />\r\n                  </el-col>-->\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"仓管代付\" prop=\"outboundNotes\">\r\n                  <el-input v-model=\"outboundForm.warehousePay\" class=\"number\" placeholder=\"仓管代收\"/>\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"10\">\r\n            <el-col>\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"操作要求\" prop=\"operationRequirement\">\r\n                  <el-input v-model=\"outboundForm.operationRequirement\" :autosize=\"{ minRows: 4}\" maxlength=\"250\"\r\n                            placeholder=\"内容\"\r\n                            show-word-limit type=\"textarea\"\r\n                  />\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"仓管指示\" prop=\"outboundNote\">\r\n                  <el-input v-model=\"outboundForm.outboundNote\" :autosize=\"{ minRows: 4}\" maxlength=\"250\"\r\n                            placeholder=\"内容\"\r\n                            show-word-limit type=\"textarea\"\r\n                  />\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"10\">\r\n            <el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"操作员\" prop=\"operator\">\r\n                  <el-input v-model=\"outboundForm.operator\" class=\"disable-form\" disabled placeholder=\"操作员\"/>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"下单日期\" prop=\"orderDate\">\r\n                  <el-date-picker v-model=\"outboundForm.orderDate\"\r\n                                  class=\"disable-form\" clearable disabled\r\n                                  placeholder=\"出仓日期\"\r\n                                  style=\"width: 100%\"\r\n                                  type=\"date\"\r\n                                  value-format=\"yyyy-MM-dd\"\r\n                  >\r\n                  </el-date-picker>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"出仓经手人\" prop=\"outboundHandler\">\r\n                  <el-input v-model=\"outboundForm.outboundHandler\" class=\"disable-form\" disabled\r\n                            placeholder=\"出仓经手人\"\r\n                  />\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"6\">\r\n                <el-row>\r\n                  <el-col :span=\"12\">\r\n                    <el-button :disabled=\"outboundForm.clientCode===null\" type=\"primary\" @click=\"warehouseConfirm\">\r\n                      {{ \"仓管确认\" }}\r\n                    </el-button>\r\n                  </el-col>\r\n                  <el-col :span=\"12\">\r\n                    <el-button :disabled=\"outboundForm.clientCode===null\" type=\"primary\"\r\n                               @click=\"loadPreOutboundInventoryList\">\r\n                      {{ \"加载待出库\" }}\r\n                    </el-button>\r\n                  </el-col>\r\n                </el-row>\r\n              </el-col>\r\n            </el-col>\r\n          </el-row>\r\n        </el-row>\r\n        <!--库存记录列表(未出库)-->\r\n        <el-row :gutter=\"10\">\r\n          <el-col>\r\n            <el-col>\r\n              <el-table ref=\"table\" v-loading=\"preOutboundInventoryListLoading\"\r\n                        :data=\"outboundForm.rsInventoryList\" :load=\"loadChildInventory\" :summary-method=\"getSummaries\"\r\n                        :tree-props=\"{children: 'children', hasChildren: 'hasChildren'}\" lazy\r\n                        max-height=\"300\" row-key=\"inventoryId\" show-summary\r\n                        style=\"width: 100%;\"\r\n                        @selection-change=\"handleOutboundSelectionChange\"\r\n              >\r\n                <el-table-column align=\"center\" label=\"入仓流水号\" prop=\"inboundSerialNo\"/>\r\n                <el-table-column align=\"center\" label=\"货物明细\" width=\"50\">\r\n                  <template slot-scope=\"scope\">\r\n                    <el-popover\r\n                      trigger=\"click\"\r\n                      width=\"800\"\r\n                    >\r\n                      <el-table :data=\"scope.row.rsCargoDetailsList\"\r\n                                @selection-change=\"(selectedRows) => handleOutboundCargoDetailSelectionChange(selectedRows,scope.row)\"\r\n                      >\r\n                        <el-table-column align=\"center\" type=\"selection\" width=\"28\"/>\r\n                        <el-table-column\r\n                          label=\"唛头\"\r\n                          prop=\"shippingMark\"\r\n                          width=\"150\"\r\n                        >\r\n                        </el-table-column>\r\n                        <el-table-column\r\n                          label=\"货名\"\r\n                          prop=\"itemName\"\r\n                          width=\"150\"\r\n                        >\r\n                        </el-table-column>\r\n                        <el-table-column\r\n                          label=\"箱数\"\r\n                          prop=\"boxCount\"\r\n                        >\r\n                        </el-table-column>\r\n                        <el-table-column\r\n                          label=\"包装类型\"\r\n                          prop=\"packageType\"\r\n                        >\r\n                        </el-table-column>\r\n                        <el-table-column\r\n                          label=\"单件毛重\"\r\n                          prop=\"unitGrossWeight\"\r\n                        >\r\n                        </el-table-column>\r\n                        <el-table-column\r\n                          label=\"单件长\"\r\n                          prop=\"unitLength\"\r\n                        >\r\n                        </el-table-column>\r\n                        <el-table-column\r\n                          label=\"单件宽\"\r\n                          prop=\"unitWidth\"\r\n                        >\r\n                        </el-table-column>\r\n                        <el-table-column\r\n                          label=\"单件高\"\r\n                          prop=\"unitHeight\"\r\n                        >\r\n                        </el-table-column>\r\n                        <el-table-column\r\n                          label=\"单件体积\"\r\n                          prop=\"unitVolume\"\r\n                        >\r\n                        </el-table-column>\r\n                        <el-table-column\r\n                          label=\"破损标志\"\r\n                          prop=\"damageStatus\"\r\n                        >\r\n                        </el-table-column>\r\n                      </el-table>\r\n                      <el-button slot=\"reference\" style=\"margin: 0;padding: 5px\">查看</el-button>\r\n                    </el-popover>\r\n                  </template>\r\n                </el-table-column>\r\n                <el-table-column align=\"center\" label=\"入仓日期\" prop=\"actualInboundTime\" width=\"80\">\r\n                  <template slot-scope=\"scope\">\r\n                    <span>{{ parseTime(scope.row.actualInboundTime, \"{y}-{m}-{d}\") }}</span>\r\n                  </template>\r\n                </el-table-column>\r\n                <el-table-column align=\"center\" label=\"货代单号\" prop=\"forwarderNo\"/>\r\n                <el-table-column align=\"center\" label=\"客户代码\" prop=\"clientCode\"/>\r\n                <el-table-column align=\"center\" label=\"司机信息\" prop=\"driverInfo\"/>\r\n                <el-table-column align=\"center\" label=\"箱数\" prop=\"totalBoxes\" width=\"50\"/>\r\n                <el-table-column align=\"center\" label=\"毛重\" prop=\"totalGrossWeight\" width=\"80\"/>\r\n                <el-table-column align=\"center\" label=\"体积\" prop=\"totalVolume\" width=\"50\"/>\r\n                <el-table-column align=\"center\" label=\"已收供应商\" prop=\"receivedSupplier\">\r\n                  <template slot-scope=\"scope\">\r\n                    <el-popover\r\n                      placement=\"top\"\r\n                      popper-class=\"fee-edit-popover\"\r\n                      trigger=\"click\"\r\n                      width=\"200\"\r\n                    >\r\n                      <el-input-number\r\n                        v-model=\"scope.row.receivedSupplier\"\r\n                        :controls=\"false\"\r\n                        :precision=\"2\"\r\n                        size=\"mini\"\r\n                        style=\"width: 100%\"\r\n                        @change=\"handleFeeChange(scope.row, 'receivedSupplier', $event)\"\r\n                      />\r\n                      <span slot=\"reference\">{{ scope.row.receivedSupplier || 0 }}</span>\r\n                    </el-popover>\r\n                  </template>\r\n                </el-table-column>\r\n                <el-table-column align=\"center\" label=\"入仓费\" prop=\"inboundFee\">\r\n                  <template slot-scope=\"scope\">\r\n                    <el-popover\r\n                      placement=\"top\"\r\n                      popper-class=\"fee-edit-popover\"\r\n                      trigger=\"click\"\r\n                      width=\"200\"\r\n                    >\r\n                      <el-input-number\r\n                        v-model=\"scope.row.inboundFee\"\r\n                        :controls=\"false\"\r\n                        :precision=\"2\"\r\n                        size=\"mini\"\r\n                        style=\"width: 100%\"\r\n                        @change=\"handleFeeChange(scope.row, 'inboundFee', $event)\"\r\n                      />\r\n                      <span slot=\"reference\">{{ scope.row.inboundFee || 0 }}</span>\r\n                    </el-popover>\r\n                  </template>\r\n                </el-table-column>\r\n                <el-table-column align=\"center\" label=\"已收入仓费\" prop=\"receivedStorageFee\">\r\n                  <template slot-scope=\"scope\">\r\n                    <el-popover\r\n                      placement=\"top\"\r\n                      popper-class=\"fee-edit-popover\"\r\n                      trigger=\"click\"\r\n                      width=\"200\"\r\n                    >\r\n                      <el-input-number\r\n                        v-model=\"scope.row.receivedStorageFee\"\r\n                        :controls=\"false\"\r\n                        :precision=\"2\"\r\n                        size=\"mini\"\r\n                        style=\"width: 100%\"\r\n                        @change=\"handleFeeChange(scope.row, 'receivedStorageFee', $event)\"\r\n                      />\r\n                      <span slot=\"reference\">{{ scope.row.receivedStorageFee || 0 }}</span>\r\n                    </el-popover>\r\n                  </template>\r\n                </el-table-column>\r\n                <el-table-column align=\"center\" label=\"补收入仓费\" prop=\"additionalStorageFee\">\r\n                  <template slot-scope=\"scope\">\r\n                    <el-popover\r\n                      placement=\"top\"\r\n                      popper-class=\"fee-edit-popover\"\r\n                      trigger=\"click\"\r\n                      width=\"200\"\r\n                    >\r\n                      <el-input-number\r\n                        v-model=\"scope.row.additionalStorageFee\"\r\n                        :controls=\"false\"\r\n                        :precision=\"2\"\r\n                        size=\"mini\"\r\n                        style=\"width: 100%\"\r\n                        @change=\"handleFeeChange(scope.row, 'additionalStorageFee', $event)\"\r\n                      />\r\n                      <span slot=\"reference\">{{ scope.row.additionalStorageFee || 0 }}</span>\r\n                    </el-popover>\r\n                  </template>\r\n                </el-table-column>\r\n                <el-table-column align=\"center\" label=\"未收卸货费\" prop=\"unpaidUnloadingFee\">\r\n                  <template slot-scope=\"scope\">\r\n                    <el-popover\r\n                      placement=\"top\"\r\n                      popper-class=\"fee-edit-popover\"\r\n                      trigger=\"click\"\r\n                      width=\"200\"\r\n                    >\r\n                      <el-input-number\r\n                        v-model=\"scope.row.unpaidUnloadingFee\"\r\n                        :controls=\"false\"\r\n                        :precision=\"2\"\r\n                        size=\"mini\"\r\n                        style=\"width: 100%\"\r\n                        @change=\"handleFeeChange(scope.row, 'unpaidUnloadingFee', $event)\"\r\n                      />\r\n                      <span slot=\"reference\">{{ scope.row.unpaidUnloadingFee || 0 }}</span>\r\n                    </el-popover>\r\n                  </template>\r\n                </el-table-column>\r\n                <el-table-column align=\"center\" label=\"实付卸货费\" prop=\"receivedUnloadingFee\">\r\n                  <template slot-scope=\"scope\">\r\n                    <el-popover\r\n                      placement=\"top\"\r\n                      popper-class=\"fee-edit-popover\"\r\n                      trigger=\"click\"\r\n                      width=\"200\"\r\n                    >\r\n                      <el-input-number\r\n                        v-model=\"scope.row.receivedUnloadingFee\"\r\n                        :controls=\"false\"\r\n                        :precision=\"2\"\r\n                        size=\"mini\"\r\n                        style=\"width: 100%\"\r\n                        @change=\"handleFeeChange(scope.row, 'receivedUnloadingFee', $event)\"\r\n                      />\r\n                      <span slot=\"reference\">{{ scope.row.receivedUnloadingFee || 0 }}</span>\r\n                    </el-popover>\r\n                  </template>\r\n                </el-table-column>\r\n                  <el-table-column align=\"center\" label=\"未收打包费\" prop=\"unpaidPackingFee\">\r\n                    <template slot-scope=\"scope\">\r\n                      <el-popover\r\n                        placement=\"top\"\r\n                        popper-class=\"fee-edit-popover\"\r\n                        trigger=\"click\"\r\n                        width=\"200\"\r\n                      >\r\n                        <el-input-number\r\n                          v-model=\"scope.row.unpaidPackingFee\"\r\n                          :controls=\"false\"\r\n                          :precision=\"2\"\r\n                          size=\"mini\"\r\n                          style=\"width: 100%\"\r\n                          @change=\"handleFeeChange(scope.row, 'unpaidPackingFee', $event)\"\r\n                        />\r\n                        <span slot=\"reference\">{{ scope.row.unpaidPackagingFee || 0 }}</span>\r\n                        <span slot=\"reference\">{{ scope.row.unpaidPackingFee || 0 }}</span>\r\n                      </el-popover>\r\n                    </template>\r\n                  </el-table-column>\r\n                  <el-table-column align=\"center\" label=\"实付打包费\" prop=\"receivedPackingFee\">\r\n                    <template slot-scope=\"scope\">\r\n                      <el-popover\r\n                        placement=\"top\"\r\n                        popper-class=\"fee-edit-popover\"\r\n                        trigger=\"click\"\r\n                        width=\"200\"\r\n                      >\r\n                        <el-input-number\r\n                          v-model=\"scope.row.receivedPackingFee\"\r\n                          :controls=\"false\"\r\n                          :precision=\"2\"\r\n                          size=\"mini\"\r\n                          style=\"width: 100%\"\r\n                          @change=\"handleFeeChange(scope.row, 'receivedPackingFee', $event)\"\r\n                        />\r\n                        <span slot=\"reference\">{{ scope.row.receivedPackingFee || 0 }}</span>\r\n                      </el-popover>\r\n                    </template>\r\n                  </el-table-column>\r\n                  <el-table-column align=\"center\" label=\"物流代垫费\" prop=\"logisticsAdvanceFee\">\r\n                    <template slot-scope=\"scope\">\r\n                      <el-popover\r\n                        placement=\"top\"\r\n                        popper-class=\"fee-edit-popover\"\r\n                        trigger=\"click\"\r\n                        width=\"200\"\r\n                      >\r\n                        <el-input-number\r\n                          v-model=\"scope.row.logisticsAdvanceFee\"\r\n                          :controls=\"false\"\r\n                          :precision=\"2\"\r\n                          size=\"mini\"\r\n                          style=\"width: 100%\"\r\n                          @change=\"handleFeeChange(scope.row, 'logisticsAdvanceFee', $event)\"\r\n                        />\r\n                        <span slot=\"reference\">{{ scope.row.logisticsAdvanceFee || 0 }}</span>\r\n                      </el-popover>\r\n                    </template>\r\n                  </el-table-column>\r\n                  <el-table-column align=\"center\" label=\"超期租金\" prop=\"overdueRentalFee\">\r\n                    <template slot-scope=\"scope\">\r\n                      <el-popover\r\n                        placement=\"top\"\r\n                        popper-class=\"fee-edit-popover\"\r\n                        trigger=\"click\"\r\n                        width=\"200\"\r\n                      >\r\n                        <el-input-number\r\n                          v-model=\"scope.row.overdueRentalFee\"\r\n                          :controls=\"false\"\r\n                          :precision=\"2\"\r\n                          size=\"mini\"\r\n                          style=\"width: 100%\"\r\n                          @change=\"handleFeeChange(scope.row, 'overdueRentalFee', $event)\"\r\n                        />\r\n                        <span slot=\"reference\">{{ scope.row.overdueRentalFee || 0 }}</span>\r\n                      </el-popover>\r\n                    </template>\r\n                  </el-table-column>\r\n              </el-table>\r\n            </el-col>\r\n          </el-col>\r\n        </el-row>\r\n\r\n        <!--汇总费用-->\r\n        <el-row>\r\n          <el-col :span=\"4\">\r\n            <span>未收客户：{{ outboundForm.unreceivedFromCustomer }}</span>\r\n          </el-col>\r\n          <el-col :span=\"4\">\r\n            <span>已收客户：{{ outboundForm.receivedFromCustomer }}</span>\r\n          </el-col>\r\n          <el-col :span=\"4\">\r\n            <span>应收客户余额：{{ outboundForm.customerReceivableBalance }}</span>\r\n          </el-col>\r\n          <el-col :span=\"4\">\r\n            <span>应付工人：{{ outboundForm.payableToWorker }}</span>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"4\">\r\n            <span>本票销售额：{{ outboundForm.promissoryNoteSales }}</span>\r\n          </el-col>\r\n          <el-col :span=\"4\">\r\n            <span>本票成本：{{ outboundForm.promissoryNoteCost }}</span>\r\n          </el-col>\r\n          <el-col :span=\"4\">\r\n            <span>本票结余：{{ outboundForm.promissoryNoteGrossProfit }}</span>\r\n          </el-col>\r\n          <el-col :span=\"4\">\r\n            <span>已收供应商总额：{{ outboundForm.receivedFromSupplier }}</span>\r\n          </el-col>\r\n        </el-row>\r\n      </el-form>\r\n      <span slot=\"footer\" class=\"dialog-footer\">\r\n    <el-button type=\"primary\" @click=\"submitForm\">保存</el-button>\r\n    <el-button @click=\"generateOutboundBill\">生成出仓单</el-button>\r\n        <!--    <el-button v-if=\"outboundType===0\" type=\"primary\" @click=\"outboundConfirm(0)\">确定预出仓</el-button>\r\n            <el-button v-else type=\"primary\" @click=\"outboundConfirm(1)\">出仓</el-button>-->\r\n  </span>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  addOutboundrecord,\r\n  changeStatus,\r\n  delOutboundrecord, downloadOutboundBill,\r\n  getOutboundrecord, getOutboundrecords,\r\n  listOutboundrecord, listOutboundrecords,\r\n  updateOutboundrecord\r\n} from \"@/api/system/outboundrecord\"\r\nimport {parseTime} from \"../../../utils/rich\"\r\nimport {listInventory, outboundInventory, preOutboundInventory} from \"@/api/system/inventory\"\r\nimport currency from \"currency.js\"\r\n\r\nexport default {\r\n  name: \"Outboundrecord\",\r\n  data() {\r\n    return {\r\n      selectedCargoDetail:[],\r\n      search: null,\r\n      showLeft: 0,\r\n      showRight: 24,\r\n      // 遮罩层\r\n      loading: true,\r\n      selectOutboundList: [],\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: false,\r\n      // 总条数\r\n      total: 0,\r\n      // 出仓记录表格数据\r\n      outboundrecordList: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 20,\r\n        isRentSettlement: 0,\r\n        outboundNo: null,\r\n        clientCode: null,\r\n        clientName: null,\r\n        operatorId: null,\r\n        containerType: null,\r\n        containerNo: null,\r\n        sealNo: null,\r\n        outboundDate: null,\r\n        warehouseQuote: null,\r\n        workerLoadingFee: null,\r\n        warehouseCollection: null,\r\n        collectionNotes: null,\r\n        totalBoxes: null,\r\n        totalGrossWeight: null,\r\n        totalVolume: null,\r\n        totalRows: null,\r\n        receivedStorageFee: null,\r\n        unpaidUnloadingFee: null,\r\n        unpaidPackingFee: null,\r\n        logisticsAdvanceFee: null,\r\n        rentalBalanceFee: null,\r\n        overdueRent: null,\r\n        freeStackDays: null,\r\n        overdueUnitPrice: null\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      outboundType: null,\r\n      preOutboundInventoryListLoading: false,\r\n      // 表单校验\r\n      rules: {},\r\n      outboundForm: {},\r\n      openOutbound: false,\r\n      preOutboundInventoryList: []\r\n    }\r\n  },\r\n  watch: {\r\n    showSearch(n) {\r\n      if (n === true) {\r\n        this.showRight = 21\r\n        this.showLeft = 3\r\n      } else {\r\n        this.showRight = 24\r\n        this.showLeft = 0\r\n      }\r\n    }\r\n  },\r\n  created() {\r\n    this.getList()\r\n  },\r\n  methods: {\r\n    // 判断当前行是否被选中\r\n    isRowSelected(row) {\r\n      return this.selectedCargoDetail.includes(row)\r\n    },\r\n    // 添加搜索并滚动到匹配行的方法\r\n    handleSearchEnter() {\r\n      if (!this.search) return;\r\n\r\n      // 查找匹配的行索引\r\n      const index = this.preOutboundInventoryList.findIndex(\r\n        item => {\r\n          // 确保 inboundSerialNo 存在且为字符串\r\n          const serialNo = String(item.inboundSerialNo || '');\r\n          const searchValue = String(this.search);\r\n          // 打印每次比较的值，帮助调试\r\n          return serialNo.includes(searchValue);\r\n        }\r\n      );\r\n\r\n      if (index > -1) {\r\n        // 获取表格DOM\r\n        const table = this.$refs.table;\r\n\r\n        this.$nextTick(() => {\r\n          // 获取表格的滚动容器\r\n          const scrollWrapper = table.$el.querySelector('.el-table__body-wrapper');\r\n          // 获取所有行\r\n          const rows = scrollWrapper.querySelectorAll('.el-table__row');\r\n\r\n          // 遍历所有行，找到匹配的流水号\r\n          let targetIndex = -1;\r\n          rows.forEach((row, idx) => {\r\n            const rowText = row.textContent;\r\n            if (rowText.includes(this.search)) {\r\n              targetIndex = idx;\r\n            }\r\n          });\r\n\r\n\r\n          if (targetIndex > -1) {\r\n            const targetRow = rows[targetIndex];\r\n            // 计算需要滚动的位置\r\n            const rowTop = targetRow.offsetTop;\r\n\r\n            // 使用平滑滚动\r\n            scrollWrapper.scrollTo({\r\n              top: rowTop - scrollWrapper.clientHeight / 2,\r\n              behavior: 'smooth'\r\n            });\r\n\r\n            // 高亮显示该行\r\n            targetRow.classList.add('highlight-row');\r\n            // 1秒后移除高亮\r\n            setTimeout(() => {\r\n              targetRow.classList.remove('highlight-row');\r\n            }, 2000);\r\n          }\r\n        });\r\n      } else {\r\n        this.$message.warning('未找到匹配的记录');\r\n      }\r\n    },\r\n    warehouseConfirm() {\r\n\r\n    },\r\n    // 加载子节点数据\r\n    loadChildInventory(tree, treeNode, resolve) {\r\n      // 使用packageTo字段查询子节点\r\n      listInventory({packageTo: tree.inventoryId}).then(response => {\r\n        const rows = response.rows\r\n\r\n        // 先将数据传递给表格，确保子节点渲染\r\n        resolve(rows)\r\n        tree.children = rows\r\n\r\n        // 如果父项被选中，在子节点渲染完成后选中它们\r\n        if (this.ids.includes(tree.inventoryId)) {\r\n          setTimeout(() => {\r\n            rows.forEach(child => {\r\n              if (!this.ids.includes(child.inventoryId)) {\r\n                this.ids.push(child.inventoryId)\r\n                this.selectOutboundList.push(child)\r\n              }\r\n              // 在UI上选中子项\r\n              this.$refs.table.toggleRowSelection(child, true)\r\n            })\r\n          }, 50) // 等待DOM更新\r\n        }\r\n      })\r\n    },\r\n    selectContainerType(type) {\r\n      switch (type) {\r\n        case \"20GP\":\r\n          this.outboundForm.warehouseQuote = this.clientRow.rate20gp\r\n          break\r\n        case \"40HQ\":\r\n          this.outboundForm.warehouseQuote = this.clientRow.rate40hq\r\n          break\r\n\r\n      }\r\n    },\r\n    countSummary() {\r\n      this.outboundForm.unreceivedFromCustomer = currency(this.outboundForm.warehouseQuote).add(this.outboundForm.additionalStorageFee).add(this.outboundForm.unpaidUnloadingFee).add(this.outboundForm.unpaidPackingFee).add(this.outboundForm.logisticsAdvanceFee).add(this.outboundForm.overdueRentalFee).add(this.outboundForm.difficultyWorkFee).value\r\n      this.outboundForm.receivedFromCustomer = currency(this.outboundForm.warehouseCollection).value\r\n      this.outboundForm.customerReceivableBalance = currency(this.outboundForm.unreceivedFromCustomer).subtract(this.outboundForm.receivedFromCustomer).value\r\n      this.outboundForm.payableToWorker = currency(this.outboundForm.receivedUnloadingFee).add(this.outboundForm.receivedPackingFee).add(this.outboundForm.workerLoadingFee).value\r\n      this.outboundForm.receivedFromSupplier = currency(this.outboundForm.receivedSupplier).add(this.outboundForm.receivedStorageFee).value\r\n      this.outboundForm.promissoryNoteSales = currency(this.outboundForm.unreceivedFromCustomer).add(this.outboundForm.receivedFromSupplier).value\r\n      this.outboundForm.promissoryNoteCost = currency(this.outboundForm.payableToWorker).add(this.outboundForm.logisticsAdvanceFee).add(this.outboundForm.warehouseAdvanceOtherFee).value\r\n      this.outboundForm.promissoryNoteGrossProfit = currency(this.outboundForm.promissoryNoteSales).subtract(this.outboundForm.promissoryNoteCost).value\r\n    },\r\n    generateOutboundBill() {\r\n      downloadOutboundBill(this.outboundForm)\r\n        .then(response => {\r\n          // 获取文件的字节数组 (ArrayBuffer)\r\n          const data = response\r\n\r\n          // 获取文件名（如果在后端响应头中包含文件名）\r\n          let fileName = this.outboundForm.clientCode + \"-\" + this.outboundForm.operator + \"-\" + this.outboundForm.outboundNo + \".xlsx\"  // 默认文件名\r\n\r\n          // 创建一个 Blob 对象来存储文件\r\n          const blob = new Blob([data], {\r\n            type: \"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet\"  // Excel 文件类型\r\n          })\r\n\r\n          // 创建一个临时链接，模拟点击来下载文件\r\n          const link = document.createElement(\"a\")\r\n          const url = window.URL.createObjectURL(blob)  // 创建一个 URL 指向 Blob 对象\r\n          link.href = url\r\n          link.download = fileName  // 设置下载的文件名\r\n\r\n          // 模拟点击链接，触发下载\r\n          document.body.appendChild(link)\r\n          link.click()\r\n\r\n          // 下载完成后移除链接，并释放 URL 对象\r\n          document.body.removeChild(link)\r\n          window.URL.revokeObjectURL(url)\r\n        })\r\n        .catch(error => {\r\n          console.error(\"文件下载失败:\", error)\r\n        })\r\n    },\r\n    // 查看出仓记录\r\n    findOutboundRecord(row) {\r\n      this.outboundReset()\r\n      getOutboundrecords(row.outboundRecordId).then(response => {\r\n        this.outboundForm = response.data\r\n        this.outboundForm.rsInventoryList = this.outboundForm.rsInventoryList ? this.outboundForm.rsInventoryList.map(item => {\r\n          // 计算补收入仓费\r\n          if (item.includesInboundFee === 0) {\r\n            const receivedFee = Number(item.receivedStorageFee || 0)\r\n            const inboundFee = Number(item.inboundFee || 0)\r\n            const difference = currency(inboundFee).subtract(receivedFee).value\r\n\r\n            // 只有当差值大于0时才设置补收费用\r\n            item.additionalStorageFee = difference > 0 ? difference : 0\r\n          } else {\r\n            item.additionalStorageFee = 0\r\n          }\r\n\r\n          return item\r\n        }) : []\r\n        this.openOutbound = true\r\n      })\r\n    },\r\n    /**\r\n     *\r\n     * @param type 0:预出仓/1:出仓\r\n     */\r\n    outboundConfirm(type) {\r\n      this.selectOutboundList.map(item => {\r\n        item.partialOutboundFlag = Number(item.partialOutboundFlag)\r\n      })\r\n\r\n      addOutboundrecord(this.outboundForm).then(response => {\r\n        if (response.data) {\r\n          const outboundRecordId = response.data\r\n\r\n          // 列表克隆一份,打上预出仓标志\r\n          let data = this.selectOutboundList.map(item => {\r\n            if (item.preOutboundFlag === \"1\") {\r\n              this.$message.warning(\"勾选记录中有以预出库记录,请重新勾选\")\r\n              return\r\n            }\r\n            type === 0 ? item.preOutboundFlag = \"1\" : null\r\n            item.outboundRecordId = outboundRecordId\r\n            item.rsCargoDetailsList ? item.rsCargoDetailsList.map(item => {\r\n              item.outboundRecordId = outboundRecordId\r\n              type === 0 ? item.preOutboundFlag = \"1\" : null\r\n              return item\r\n            }) : null\r\n            return item\r\n          })\r\n\r\n          if (type === 0) {\r\n            preOutboundInventory(data).then(response => {\r\n              this.getList()\r\n              this.$message.success(\"预出仓成功\")\r\n              this.openOutbound = false\r\n            })\r\n          } else {\r\n            // 出仓\r\n            outboundInventory(data).then(response => {\r\n              this.getList()\r\n              this.$message.success(\"出仓成功\")\r\n              this.openOutbound = false\r\n            })\r\n          }\r\n        }\r\n      })\r\n    },\r\n    // 根据出库信息加载待出库的记录\r\n    loadPreOutboundInventoryList() {\r\n      this.loading = true\r\n      let data = {}\r\n      data.sqdPlannedOutboundDate = this.outboundForm.plannedOutboundDate\r\n      data.clientCode = this.outboundForm.clientCode\r\n      listInventory(data).then(response => {\r\n        console.log(response)\r\n        this.preOutboundInventoryList = response.rows\r\n        this.total = response.total\r\n        this.loading = false\r\n      })\r\n    },\r\n    /**\r\n     *\r\n     * @param type 0:预出仓/1:正式出仓\r\n     */\r\n    handleOutbound(selectedRows, type) {\r\n      // this.outboundList = this.inventoryList.filter(item => this.ids.includes(item.inventoryId))\r\n      if (type === 1) {\r\n        this.outboundReset()\r\n        this.outboundForm = selectedRows\r\n      }\r\n      this.outboundType = type\r\n      this.openOutbound = true\r\n    },\r\n    parseTime,\r\n    handleOutboundCargoDetailSelectionChange(selection, row) {\r\n      row.rsCargoDetailsList = selection\r\n      this.selectedCargoDetail = selection\r\n    },\r\n    getSummaries(param) {\r\n      const {columns, data} = param\r\n      const sums = []\r\n      const statisticalField = [\r\n        \"receivedSupplier\", \"totalBoxes\", \"unpaidInboundFee\", \"totalGrossWeight\",\r\n        \"totalVolume\", \"receivedStorageFee\", \"unpaidUnloadingFee\", \"logisticsAdvanceFee\",\r\n        \"rentalBalanceFee\", \"overdueRentalFee\", \"additionalStorageFee\", \"unpaidUnloadingFee\",\r\n        \"unpaidPackingFee\", \"receivedUnloadingFee\", \"receivedPackingFee\", \"inboundFee\"\r\n      ]\r\n\r\n      // 汇总结果存储对象\r\n      const summaryResults = {}\r\n\r\n      columns.forEach((column, index) => {\r\n        if (index === 0) {\r\n          sums[index] = \"汇总\"\r\n          return\r\n        }\r\n\r\n        const values = data.map(item => Number(item[column.property]))\r\n\r\n        if (statisticalField.includes(column.property) && !values.every(value => isNaN(value))) {\r\n          const sumValue = values.reduce((prev, curr) => {\r\n            const value = Number(curr)\r\n            if (!isNaN(value)) {\r\n              return currency(prev).add(curr).value\r\n            } else {\r\n              return prev\r\n            }\r\n          }, 0)\r\n          sums[index] = sumValue\r\n\r\n          // 将汇总结果存储在 summaryResults 对象中\r\n          summaryResults[column.property] = sumValue\r\n        } else {\r\n          sums[index] = \" \"\r\n        }\r\n      })\r\n\r\n      // 如果需要将汇总结果赋值到表单字段中，可以在此处操作\r\n      // 假设表单字段的命名与统计字段一致\r\n      Object.keys(summaryResults).forEach(field => {\r\n        // if (this.outboundForm && this.outboundForm.hasOwnProperty(field)) {\r\n        if (this.outboundForm) {\r\n          this.outboundForm[field] = summaryResults[field]  // 将汇总值赋给表单字段\r\n        }\r\n      })\r\n\r\n      return sums\r\n    },\r\n\r\n    /* getSummaries(param) {\r\n      const {columns, data} = param\r\n      const sums = []\r\n      const statisticalField = [\"totalBoxes\", \"totalGrossWeight\", \"totalVolume\", \"receivedStorageFee\", \"unpaidUnloadingFee\", \"logisticsAdvanceFee\", \"rentalBalanceFee\", \"overdueRentalFee\"]\r\n      columns.forEach((column, index) => {\r\n        if (index === 0) {\r\n          sums[index] = \"汇总\"\r\n          return\r\n        }\r\n        const values = data.map(item => Number(item[column.property]))\r\n        if (statisticalField.includes(column.property) && !values.every(value => isNaN(value))) {\r\n          sums[index] = values.reduce((prev, curr) => {\r\n            const value = Number(curr)\r\n            if (!isNaN(value)) {\r\n              return prev + curr\r\n            } else {\r\n              return prev\r\n            }\r\n          }, 0)\r\n        } else {\r\n          sums[index] = \" \"\r\n        }\r\n      })\r\n      return sums\r\n    }, */\r\n    handleOutboundSelectionChange(selection) {\r\n      this.selectOutboundList = selection\r\n    },\r\n    outboundClient(row) {\r\n      this.outboundForm.warehouseQuote = row.rateLcl\r\n      this.outboundForm.freeStackDays = row.freeStackPeriod\r\n      this.outboundForm.overdueRent = row.overdueRent\r\n      this.outboundForm.clientName = row.clientName\r\n      // this.outboundForm.workerLoadingFee=row.workerLoadingFee\r\n      this.$forceUpdate()\r\n    },\r\n    /** 查询出仓记录列表 */\r\n    getList() {\r\n      this.loading = true\r\n      listOutboundrecords(this.queryParams).then(response => {\r\n        this.outboundrecordList = response.rows\r\n        this.total = response.total\r\n        this.loading = false\r\n      })\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false\r\n      this.reset()\r\n    },\r\n    outboundReset() {\r\n      this.outboundForm = {\r\n        outboundRecordId: null,\r\n        outboundNo: null,\r\n        clientCode: null,\r\n        clientName: null,\r\n        operatorId: null,\r\n        containerType: null,\r\n        containerNo: null,\r\n        sealNo: null,\r\n        outboundDate: null,\r\n        warehouseQuote: null,\r\n        workerLoadingFee: null,\r\n        warehouseCollection: null,\r\n        collectionNotes: null,\r\n        totalBoxes: null,\r\n        totalGrossWeight: null,\r\n        totalVolume: null,\r\n        totalRows: null,\r\n        receivedStorageFee: null,\r\n        unpaidUnloadingFee: null,\r\n        unpaidPackingFee: null,\r\n        logisticsAdvanceFee: null,\r\n        rentalBalanceFee: null,\r\n        overdueRent: null,\r\n        freeStackDays: null,\r\n        overdueUnitPrice: null\r\n      }\r\n      this.preOutboundInventoryList = []\r\n      this.resetForm(\"outboundForm\")\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        outboundRecordId: null,\r\n        outboundNo: null,\r\n        clientCode: null,\r\n        clientName: null,\r\n        operatorId: null,\r\n        containerType: null,\r\n        containerNo: null,\r\n        sealNo: null,\r\n        outboundDate: null,\r\n        warehouseQuote: null,\r\n        workerLoadingFee: null,\r\n        warehouseCollection: null,\r\n        collectionNotes: null,\r\n        totalBoxes: null,\r\n        totalGrossWeight: null,\r\n        totalVolume: null,\r\n        totalRows: null,\r\n        receivedStorageFee: null,\r\n        unpaidUnloadingFee: null,\r\n        unpaidPackingFee: null,\r\n        logisticsAdvanceFee: null,\r\n        rentalBalanceFee: null,\r\n        overdueRent: null,\r\n        freeStackDays: null,\r\n        overdueUnitPrice: null\r\n      }\r\n      this.resetForm(\"form\")\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1\r\n      this.getList()\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\")\r\n      this.handleQuery()\r\n    },\r\n    handleStatusChange(row) {\r\n      let text = row.status === \"0\" ? \"启用\" : \"停用\"\r\n      this.$modal.confirm(\"确认要\\\"\" + text + \"吗？\").then(function () {\r\n        return changeStatus(row.outboundRecordId, row.status)\r\n      }).then(() => {\r\n        this.$modal.msgSuccess(text + \"成功\")\r\n      }).catch(function () {\r\n        row.status = row.status === \"0\" ? \"1\" : \"0\"\r\n      })\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.outboundRecordId)\r\n      this.single = selection.length !== 1\r\n      this.multiple = !selection.length\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.outboundReset()\r\n      this.open = true\r\n      this.title = \"添加出仓记录\"\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.outboundReset()\r\n      const outboundRecordId = row.outboundRecordId || this.ids\r\n      getOutboundrecord(outboundRecordId).then(response => {\r\n        this.form = response.data\r\n        this.open = true\r\n        this.title = \"修改出仓记录\"\r\n      })\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      this.$refs[\"outboundForm\"].validate(valid => {\r\n        if (valid) {\r\n          if (this.outboundForm.outboundRecordId != null) {\r\n            updateOutboundrecord(this.outboundForm).then(response => {\r\n              this.$modal.msgSuccess(\"修改成功\")\r\n              this.open = false\r\n              this.getList()\r\n            })\r\n          }\r\n        }\r\n      })\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const outboundRecordIds = row.outboundRecordId || this.ids\r\n      this.$modal.confirm(\"是否确认删除出仓记录编号为\\\"\" + outboundRecordIds + \"\\\"的数据项？\").then(function () {\r\n        return delOutboundrecord(outboundRecordIds)\r\n      }).then(() => {\r\n        this.getList()\r\n        this.$modal.msgSuccess(\"删除成功\")\r\n      }).catch(() => {\r\n      })\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.download(\"system/outboundrecord/export\", {\r\n        ...this.queryParams\r\n      }, `outboundrecord_${new Date().getTime()}.xlsx`)\r\n    },\r\n    // 处理费用字段变更的通用逻辑\r\n    handleFeeChange(row, field, value) {\r\n      // 确保值为数字\r\n      value = Number(value) || 0\r\n\r\n      // 使用$set确保响应式更新\r\n      this.$set(row, field, value)\r\n\r\n      // 对特定字段做额外处理\r\n      if (field === \"receivedStorageFee\" && row.includesInboundFee === 0) {\r\n        const inboundFee = Number(row.inboundFee || 0)\r\n        const difference = currency(inboundFee).subtract(value).value\r\n        this.$set(row, \"additionalStorageFee\", difference > 0 ? difference : 0)\r\n      }\r\n\r\n      if (field === 'inboundFee') {\r\n        const difference = currency(value).subtract(row.receivedStorageFee).value\r\n        this.$set(row, \"additionalStorageFee\", difference > 0 ? difference : 0)\r\n      }\r\n\r\n      // 强制更新表格视图\r\n      this.$forceUpdate()\r\n\r\n      // 将修改后的值设置回表格的数据源，确保表格显示最新值\r\n      const index = this.outboundForm.rsInventoryList.findIndex(item => item === row)\r\n      if (index !== -1) {\r\n        this.$set(this.outboundForm.rsInventoryList, index, {...row})\r\n      }\r\n\r\n      // 更新表格和汇总\r\n      this.updateTableData()\r\n    },\r\n    // 更新表格数据并重新计算汇总\r\n    updateTableData() {\r\n      // 强制更新视图\r\n      this.$forceUpdate()\r\n      // 重新计算汇总\r\n      this.$nextTick(() => {\r\n        // 调用计算汇总的方法\r\n        if (this.outboundForm.rsInventoryList && this.outboundForm.rsInventoryList.length > 0) {\r\n          this.getSummaries({\r\n            columns: this.$refs.outboundInventoryTable ? this.$refs.outboundInventoryTable.columns : [],\r\n            data: this.outboundForm.rsInventoryList\r\n          })\r\n        }\r\n        this.countSummary()\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n/* 数字字段显示样式 */\r\n.el-table .cell span {\r\n  cursor: pointer;\r\n  color: #606266;\r\n  padding: 2px 5px;\r\n  border-radius: 3px;\r\n  transition: background-color 0.3s;\r\n}\r\n\r\n.el-table .cell span:hover {\r\n  background-color: #f5f7fa;\r\n}\r\n\r\n\r\n</style>\r\n"]}]}