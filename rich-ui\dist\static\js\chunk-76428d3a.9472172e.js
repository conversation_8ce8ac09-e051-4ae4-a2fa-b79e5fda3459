(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-76428d3a"],{5788:function(t,e,o){"use strict";o.r(e);var i=function(){var t=this,e=t.$createElement,o=t._self._c||e;return o("div",{staticClass:"app-container"},[o("el-row",{attrs:{gutter:20}},[o("el-col",{attrs:{span:t.showLeft}},[o("el-form",{directives:[{name:"show",rawName:"v-show",value:t.showSearch,expression:"showSearch"}],ref:"queryForm",staticClass:"query",attrs:{inline:!0,model:t.queryParams,size:"mini"}},[o("el-form-item",{attrs:{label:"编码",prop:"positionEnName"}},[o("el-input",{staticStyle:{width:"158px"},attrs:{clearable:"",placeholder:"职级编码"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.handleQuery(e)}},model:{value:t.queryParams.positionEnName,callback:function(e){t.$set(t.queryParams,"positionEnName",e)},expression:"queryParams.positionEnName"}})],1),o("el-form-item",{attrs:{label:"名称",prop:"positionQuery"}},[o("el-input",{staticStyle:{width:"158px"},attrs:{clearable:"",placeholder:"职级名称"},nativeOn:{keyup:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.handleQuery(e)}},model:{value:t.queryParams.positionQuery,callback:function(e){t.$set(t.queryParams,"positionQuery",e)},expression:"queryParams.positionQuery"}})],1),o("el-form-item",[o("el-button",{attrs:{icon:"el-icon-search",size:"mini",type:"primary"},on:{click:t.handleQuery}},[t._v("搜索")]),o("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:t.resetQuery}},[t._v("重置")])],1)],1)],1),o("el-col",{attrs:{span:t.showRight}},[o("el-row",{staticClass:"mb8",attrs:{gutter:10}},[o("el-col",{attrs:{span:1.5}},[o("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:post:add"],expression:"['system:post:add']"}],attrs:{icon:"el-icon-plus",plain:"",size:"mini",type:"primary"},on:{click:t.handleAdd}},[t._v("新增 ")])],1),o("el-col",{attrs:{span:1.5}},[o("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:post:remove"],expression:"['system:post:remove']"}],attrs:{disabled:t.multiple,icon:"el-icon-delete",plain:"",size:"mini",type:"danger"},on:{click:t.handleDelete}},[t._v("删除 ")])],1),o("el-col",{attrs:{span:1.5}},[o("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:post:export"],expression:"['system:post:export']"}],attrs:{icon:"el-icon-download",plain:"",size:"mini",type:"warning"},on:{click:t.handleExport}},[t._v("导出 ")])],1),o("right-toolbar",{attrs:{showSearch:t.showSearch},on:{"update:showSearch":function(e){t.showSearch=e},"update:show-search":function(e){t.showSearch=e},queryTable:t.getList}})],1),o("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],attrs:{data:t.postList},on:{"selection-change":t.handleSelectionChange}},[o("el-table-column",{attrs:{type:"selection",width:"28",align:"center"}}),o("el-table-column",{attrs:{label:"职级名称",width:"300"},scopedSlots:t._u([{key:"default",fn:function(e){return[o("a",{class:"Max"===e.row.positionEnName||"Basic"===e.row.positionEnName?"nonEmphasis":"",staticStyle:{margin:"0","font-weight":"bold","font-size":"small"}},[t._v(t._s(null!=e.row.positionLocalName?e.row.positionLocalName:"")+" "+t._s(null!=e.row.positionEnName?e.row.positionEnName:"")+" ")])]}}])}),o("el-table-column",{attrs:{align:"left",label:"备注",prop:"remark"}}),o("el-table-column",{attrs:{align:"center",label:"排序",prop:"positionSort",width:"48"}}),o("el-table-column",{attrs:{align:"center",label:"状态",prop:"status",width:"58"},scopedSlots:t._u([{key:"default",fn:function(e){return[o("dict-tag",{attrs:{options:t.dict.type.sys_normal_disable,value:e.row.status}})]}}])}),o("el-table-column",{attrs:{align:"center","class-name":"small-padding fixed-width",label:"操作",width:"100"},scopedSlots:t._u([{key:"default",fn:function(e){return[o("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:post:edit"],expression:"['system:post:edit']"}],staticStyle:{"margin-right":"-8px"},attrs:{icon:"el-icon-edit",size:"mini",type:"success"},on:{click:function(o){return t.handleUpdate(e.row)}}},[t._v("修改 ")]),o("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:post:remove"],expression:"['system:post:remove']"}],staticStyle:{"margin-right":"-8px"},attrs:{icon:"el-icon-delete",size:"mini",type:"danger"},on:{click:function(o){return t.handleDelete(e.row)}}},[t._v("删除 ")])]}}])})],1)],1)],1),o("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"},{name:"dialogDragWidth",rawName:"v-dialogDragWidth"}],attrs:{"close-on-click-modal":!1,"modal-append-to-body":!1,title:t.title,visible:t.open,"append-to-body":"",width:"500px"},on:{"update:visible":function(e){t.open=e}}},[o("el-form",{ref:"form",attrs:{model:t.form,rules:t.rules,"label-width":"80px"}},[o("el-form-item",{attrs:{label:"职级名称",prop:"positionLocalName"}},[o("el-input",{attrs:{placeholder:"职级名称"},model:{value:t.form.positionLocalName,callback:function(e){t.$set(t.form,"positionLocalName",e)},expression:"form.positionLocalName"}})],1),o("el-form-item",{attrs:{label:"职级编码",prop:"positionEnName"}},[o("el-input",{attrs:{placeholder:"编码名称"},model:{value:t.form.positionEnName,callback:function(e){t.$set(t.form,"positionEnName",e)},expression:"form.positionEnName"}})],1),o("el-form-item",{attrs:{label:"职级顺序",prop:"positionSort"}},[o("el-input-number",{attrs:{controls:!1,min:0,"controls-position":"right"},model:{value:t.form.positionSort,callback:function(e){t.$set(t.form,"positionSort",e)},expression:"form.positionSort"}})],1),o("el-form-item",{attrs:{label:"备注",prop:"remark"}},[o("el-input",{attrs:{autosize:{minRows:10,maxRows:20},"controls-position":"right",maxlength:"150","show-word-limit":"",type:"textarea"},model:{value:t.form.remark,callback:function(e){t.$set(t.form,"remark",e)},expression:"form.remark"}})],1),o("el-form-item",{attrs:{label:"职级状态",prop:"status"}},[o("el-radio-group",{model:{value:t.form.status,callback:function(e){t.$set(t.form,"status",e)},expression:"form.status"}},t._l(t.dict.type.sys_normal_disable,(function(e){return o("el-radio",{key:e.value,attrs:{label:e.value}},[t._v(t._s(e.label)+" ")])})),1)],1),o("el-form-item",{attrs:{label:"备注",prop:"remark"}},[o("el-input",{attrs:{autosize:{minRows:10,maxRows:20},maxlength:"150",placeholder:"内容","show-word-limit":"",type:"textarea"},model:{value:t.form.remark,callback:function(e){t.$set(t.form,"remark",e)},expression:"form.remark"}})],1)],1),o("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[o("el-button",{attrs:{type:"primary"},on:{click:t.submitForm}},[t._v("确 定")]),o("el-button",{on:{click:t.cancel}},[t._v("取 消")])],1)],1)],1)},s=[],n=o("5530"),r=(o("d81d"),o("74b1")),a={name:"Post",dicts:["sys_normal_disable"],data:function(){return{showLeft:3,showRight:21,loading:!0,ids:[],single:!0,multiple:!0,showSearch:!1,postList:[],title:"",open:!1,queryParams:{positionQuery:null},form:{},rules:{positionLocalName:[{required:!0,trigger:"blur"}],positionEnName:[{required:!0,trigger:"blur"}],positionSort:[{required:!0,message:"职级顺序不能为空",trigger:"blur"}]}}},watch:{showSearch:function(t){1==t?(this.showRight=21,this.showLeft=3):(this.showRight=24,this.showLeft=0)}},created:function(){this.getList()},methods:{getList:function(){var t=this;this.loading=!0,Object(r["d"])(this.queryParams).then((function(e){t.postList=e.rows,t.total=e.total,t.loading=!1}))},cancel:function(){this.open=!1,this.reset()},reset:function(){this.form={positionId:void 0,positionEnName:void 0,positionLocalName:void 0,positionSort:0,status:"0",remark:void 0},this.resetForm("form")},handleQuery:function(){this.getList()},resetQuery:function(){this.resetForm("queryForm"),this.handleQuery()},handleSelectionChange:function(t){this.ids=t.map((function(t){return t.positionId})),this.single=1!=t.length,this.multiple=!t.length},handleAdd:function(){this.reset(),this.open=!0,this.title="添加职级"},handleUpdate:function(t){var e=this;this.reset();var o=t.positionId||this.ids;Object(r["c"])(o).then((function(t){e.form=t.data,e.open=!0,e.title="修改职级"}))},submitForm:function(){var t=this;this.$refs["form"].validate((function(e){e&&(void 0!=t.form.positionId?Object(r["g"])(t.form).then((function(e){t.$modal.msgSuccess("修改成功"),t.open=!1,t.getList()})):Object(r["a"])(t.form).then((function(e){t.$modal.msgSuccess("新增成功"),t.open=!1,t.getList()})))}))},handleDelete:function(t){var e=this,o=t.positionId||this.ids;this.$confirm('是否确认删除职级编号为"'+o+'"的数据项？',"提示",{customClass:"modal-confirm"}).then((function(){return Object(r["b"])(o)})).then((function(){e.getList(),e.$modal.msgSuccess("删除成功")})).catch((function(){}))},handleExport:function(){this.download("system/post/export",Object(n["a"])({},this.queryParams),"post_".concat((new Date).getTime(),".xlsx"))}}},l=a,c=(o("f672"),o("2877")),m=Object(c["a"])(l,i,s,!1,null,"26a81264",null);e["default"]=m.exports},"5dc6":function(t,e,o){},"74b1":function(t,e,o){"use strict";o.d(e,"d",(function(){return s})),o.d(e,"c",(function(){return n})),o.d(e,"a",(function(){return r})),o.d(e,"g",(function(){return a})),o.d(e,"b",(function(){return l})),o.d(e,"e",(function(){return c})),o.d(e,"f",(function(){return m}));var i=o("b775");function s(t){return Object(i["a"])({url:"/system/post/list",method:"get",params:t})}function n(t){return Object(i["a"])({url:"/system/post/"+t,method:"get"})}function r(t){return Object(i["a"])({url:"/system/post",method:"post",data:t})}function a(t){return Object(i["a"])({url:"/system/post",method:"put",data:t})}function l(t){return Object(i["a"])({url:"/system/post/"+t,method:"delete"})}function c(t){return Object(i["a"])({url:"/system/post/underUser/"+t,method:"get"})}function m(t){return Object(i["a"])({url:"/system/post/user/"+t,method:"get"})}},f672:function(t,e,o){"use strict";o("5dc6")}}]);