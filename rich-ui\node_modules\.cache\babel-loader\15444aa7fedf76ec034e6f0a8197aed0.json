{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\thread-loader\\dist\\cjs.js!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\inventory\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\inventory\\index.vue", "mtime": 1750818094548}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\babel.config.js", "mtime": 1684722674797}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\thread-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}