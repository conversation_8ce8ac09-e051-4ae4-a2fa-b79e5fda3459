(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d0de67c"],{8608:function(e,t,o){"use strict";o.r(t);var n=function(){var e=this,t=e.$createElement,o=e._self._c||t;return o("div",{staticClass:"app-container"},[o("el-row",{attrs:{gutter:20}},[o("el-col",{attrs:{span:e.showLeft}},[o("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],ref:"queryForm",staticClass:"query",attrs:{inline:!0,model:e.queryParams,size:"mini"}},[o("el-form-item",{attrs:{label:"搜索",prop:"infoTypeQuery"}},[o("el-input",{staticStyle:{width:"100%"},attrs:{clearable:"",placeholder:"中英文简称"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.infoTypeQuery,callback:function(t){e.$set(e.queryParams,"infoTypeQuery",t)},expression:"queryParams.infoTypeQuery"}})],1),o("el-form-item",[o("el-button",{attrs:{icon:"el-icon-search",size:"mini",type:"primary"},on:{click:e.handleQuery}},[e._v("搜索")]),o("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1)],1),o("el-col",{attrs:{span:e.showRight}},[o("el-row",{staticClass:"mb8",attrs:{gutter:10}},[o("el-col",{attrs:{span:1.5}},[o("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:commoninfotype:add"],expression:"['system:commoninfotype:add']"}],attrs:{icon:"el-icon-plus",plain:"",size:"mini",type:"primary"},on:{click:e.handleAdd}},[e._v("新增 ")])],1),o("el-col",{attrs:{span:1.5}},[o("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:commoninfotype:remove"],expression:"['system:commoninfotype:remove']"}],attrs:{disabled:e.multiple,icon:"el-icon-delete",plain:"",size:"mini",type:"danger"},on:{click:e.handleDelete}},[e._v("删除 ")])],1),o("right-toolbar",{attrs:{showSearch:e.showSearch},on:{"update:showSearch":function(t){e.showSearch=t},"update:show-search":function(t){e.showSearch=t},queryTable:e.getList}})],1),o("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.commoninfotypeList,"tree-props":{children:"children",hasChildren:"hasChildren"},"row-key":"infoTypeId"},on:{"selection-change":e.handleSelectionChange}},[o("el-table-column",{attrs:{type:"selection",width:"28",align:"center"}}),o("el-table-column",{attrs:{align:"left",label:"类别名称",prop:"infoTypeShortName"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(t.row.infoTypeShortName)+" "),o("a",{staticStyle:{margin:"0","font-weight":"bold","font-size":"small"}},[e._v(e._s(t.row.infoTypeLocalName))]),e._v(" "+e._s(t.row.infoTypeEnName)+" ")]}}])}),o("el-table-column",{attrs:{align:"center",label:"文件类型",prop:"docType","show-tooltip-when-overflow":""}}),o("el-table-column",{attrs:{align:"center",label:"备注",prop:"remark"}}),o("el-table-column",{attrs:{align:"center",label:"排序",prop:"orderNum",width:"48"}}),o("el-table-column",{attrs:{align:"center",label:"状态",prop:"status",width:"68"},scopedSlots:e._u([{key:"default",fn:function(t){return[o("el-switch",{attrs:{"active-value":"0","inactive-value":"1"},on:{change:function(o){return e.handleStatusChange(t.row)}},model:{value:t.row.status,callback:function(o){e.$set(t.row,"status",o)},expression:"scope.row.status"}})]}}])}),o("el-table-column",{attrs:{align:"center","class-name":"small-padding fixed-width",label:"操作",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[o("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:commoninfotype:edit"],expression:"['system:commoninfotype:edit']"}],staticStyle:{"margin-right":"-8px"},attrs:{icon:"el-icon-edit",size:"mini",type:"success"},on:{click:function(o){return e.handleUpdate(t.row)}}},[e._v("修改 ")]),o("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:commoninfotype:remove"],expression:"['system:commoninfotype:remove']"}],staticStyle:{"margin-right":"-8px"},attrs:{icon:"el-icon-delete",size:"mini",type:"danger"},on:{click:function(o){return e.handleDelete(t.row)}}},[e._v("删除 ")])]}}])})],1)],1)],1),o("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"},{name:"dialogDragWidth",rawName:"v-dialogDragWidth"}],attrs:{"close-on-click-modal":!1,"modal-append-to-body":!1,title:e.title,visible:e.open,"append-to-body":"",width:"500px"},on:{"update:visible":function(t){e.open=t}}},[o("el-form",{ref:"form",staticClass:"edit",attrs:{model:e.form,rules:e.rules,"label-width":"80px"}},[o("el-form-item",{attrs:{label:"简称",prop:"infoTypeShortName"}},[o("el-input",{attrs:{placeholder:"简称"},model:{value:e.form.infoTypeShortName,callback:function(t){e.$set(e.form,"infoTypeShortName",t)},expression:"form.infoTypeShortName"}})],1),o("el-form-item",{attrs:{label:"中文名",prop:"infoTypeLocalName"}},[o("el-input",{attrs:{placeholder:"中文名"},model:{value:e.form.infoTypeLocalName,callback:function(t){e.$set(e.form,"infoTypeLocalName",t)},expression:"form.infoTypeLocalName"}})],1),o("el-form-item",{attrs:{label:"英文名",prop:"infoTypeEnName"}},[o("el-input",{attrs:{placeholder:"英文名"},model:{value:e.form.infoTypeEnName,callback:function(t){e.$set(e.form,"infoTypeEnName",t)},expression:"form.infoTypeEnName"}})],1),o("el-form-item",{attrs:{label:"文件类型",prop:"docIds"}},[o("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"进度分类",multiple:""},model:{value:e.form.docId,callback:function(t){e.$set(e.form,"docId",t)},expression:"form.docId"}},e._l(e.docList,(function(e){return o("el-option",{key:e.docId,attrs:{label:e.docLocalName,value:e.docId}})})),1)],1),o("el-form-item",{attrs:{label:"排序",prop:"orderNum"}},[o("el-input-number",{staticStyle:{width:"100%"},attrs:{controls:!1,placeholder:"排序"},model:{value:e.form.orderNum,callback:function(t){e.$set(e.form,"orderNum",t)},expression:"form.orderNum"}})],1),o("el-form-item",{attrs:{label:"备注",prop:"remark"}},[o("el-input",{attrs:{autosize:{minRows:10,maxRows:20},maxlength:"150",placeholder:"备注","show-word-limit":"",type:"textarea"},model:{value:e.form.remark,callback:function(t){e.$set(e.form,"remark",t)},expression:"form.remark"}})],1)],1),o("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[o("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("确 定")]),o("el-button",{on:{click:e.cancel}},[e._v("取 消")])],1)],1)],1)},a=[],i=o("5530"),l=(o("d81d"),o("8f36")),r=o("a9e4"),s={name:"Commoninfotype",data:function(){return{showLeft:3,showRight:21,docList:[],loading:!0,ids:[],single:!0,multiple:!0,showSearch:!0,commoninfotypeList:[],title:"",open:!1,queryParams:{infoTypeQuery:null},form:{},rules:{}}},watch:{showSearch:function(e){1==e?(this.showRight=21,this.showLeft=3):(this.showRight=24,this.showLeft=0)}},created:function(){var e=this;this.getList(),Object(r["e"])({pageNum:1,pageSize:100}).then((function(t){e.docList=t.rows}))},methods:{getList:function(){var e=this;this.loading=!0,Object(l["e"])(this.queryParams).then((function(t){e.commoninfotypeList=e.handleTree(t.data,"infoTypeId"),e.loading=!1}))},cancel:function(){this.open=!1,this.reset()},reset:function(){this.form={infoTypeId:null,infoTypeShortName:null,infoTypeLocalName:null,infoTypeEnName:null,orderNum:null,status:"0",remark:null,createBy:null,createTime:null,updateBy:null,updateTime:null,deleteBy:null,deleteTime:null,deleteStatus:"0"},this.resetForm("form")},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.resetForm("queryForm"),this.handleQuery()},handleSelectionChange:function(e){this.ids=e.map((function(e){return e.infoTypeId})),this.single=1!=e.length,this.multiple=!e.length},handleAdd:function(){this.reset(),this.open=!0,this.title="添加通用信息类型"},handleUpdate:function(e){var t=this;this.reset();var o=e.infoTypeId||this.ids;Object(l["d"])(o).then((function(e){t.form=e.data,t.open=!0,t.title="修改通用信息类型"}))},submitForm:function(){var e=this;this.$refs["form"].validate((function(t){t&&(null!=e.form.infoTypeId?Object(l["h"])(e.form).then((function(t){e.$modal.msgSuccess("修改成功"),e.open=!1,e.getList()})):Object(l["a"])(e.form).then((function(t){e.$modal.msgSuccess("新增成功"),e.open=!1,e.getList()})))}))},handleDelete:function(e){var t=this,o=e.infoTypeId||this.ids;this.$confirm('是否确认删除通用信息类型编号为"'+o+'"的数据项？',"提示",{customClass:"modal-confirm"}).then((function(){return Object(l["c"])(o)})).then((function(){t.getList(),t.$modal.msgSuccess("删除成功")})).catch((function(){}))},handleExport:function(){this.download("system/commoninfotype/export",Object(i["a"])({},this.queryParams),"commoninfotype_".concat((new Date).getTime(),".xlsx"))},handleStatusChange:function(e){var t=this,o="0"==e.status?"启用":"停用";this.$confirm('确认要"'+o+'""'+e.infoTypeLocalName+'"吗？',"提示",{customClass:"modal-confirm"}).then((function(){return Object(l["b"])(e.infoTypeId,e.status)})).then((function(){t.$modal.msgSuccess(o+"成功")})).catch((function(){e.status="0"==e.status?"1":"0"}))},getDocTypeIds:function(e){this.form.docIds=e}}},c=s,m=o("2877"),u=Object(m["a"])(c,n,a,!1,null,null,null);t["default"]=u.exports}}]);