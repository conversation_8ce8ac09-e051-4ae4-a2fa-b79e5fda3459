(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-82f1a0e0","chunk-759e139f","chunk-2d230c18","chunk-2d0b6974"],{"0b95":function(e,t,s){},"1e8b":function(e,t,s){"use strict";s.r(t);var a=function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("el-form",{ref:"form",attrs:{model:e.user,rules:e.rules,"label-width":"88px"}},[s("el-form-item",{attrs:{label:"管理分配"}},[s("el-row",[s("el-col",{attrs:{span:4}},[s("tree-select",{attrs:{pass:e.user.sqdDeptId,placeholder:"归属部门",type:"dept"},on:{return:function(t){e.user.sqdDeptId=t}}})],1),s("el-col",{attrs:{span:5}},[s("el-input",{attrs:{disabled:"",placeholder:"员工编号",maxlength:"10"},model:{value:e.user.staffCode,callback:function(t){e.$set(e.user,"staffCode",t)},expression:"user.staffCode"}})],1),s("el-col",{attrs:{span:5}},[s("el-input",{attrs:{disabled:"",placeholder:"登录账户",maxlength:"30"},model:{value:e.user.staffUsername,callback:function(t){e.$set(e.user,"staffUsername",t)},expression:"user.staffUsername"}})],1),s("el-col",{attrs:{span:5}},[s("el-select",{staticStyle:{width:"100%"},model:{value:e.user.staffJobStatus,callback:function(t){e.$set(e.user,"staffJobStatus",t)},expression:"user.staffJobStatus"}},e._l(e.dict.type.sys_normal_disable,(function(e){return s("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),s("el-col",{attrs:{span:5}},[s("el-input",{attrs:{disabled:"admin"!=e.$store.state.user.userRole,placeholder:"mac地址"},model:{value:e.user.macAddress,callback:function(t){e.$set(e.user,"macAddress",t)},expression:"user.macAddress"}})],1)],1)],1),s("el-form-item",{attrs:{label:"基础资料"}},[s("el-row",[s("el-col",{attrs:{span:4}},[s("el-input",{attrs:{placeholder:"员工昵称",maxlength:"30"},model:{value:e.user.staffShortName,callback:function(t){e.$set(e.user,"staffShortName",t)},expression:"user.staffShortName"}})],1),s("el-col",{attrs:{span:5}},[s("el-input",{attrs:{placeholder:"中文姓氏",maxlength:"30"},model:{value:e.user.staffFamilyLocalName,callback:function(t){e.$set(e.user,"staffFamilyLocalName",t)},expression:"user.staffFamilyLocalName"}})],1),s("el-col",{attrs:{span:5}},[s("el-input",{attrs:{placeholder:"中文名称",maxlength:"30"},model:{value:e.user.staffGivingLocalName,callback:function(t){e.$set(e.user,"staffGivingLocalName",t)},expression:"user.staffGivingLocalName"}})],1),s("el-col",{attrs:{span:5}},[s("el-input",{attrs:{placeholder:"英文姓氏",maxlength:"30"},model:{value:e.user.staffFamilyEnName,callback:function(t){e.$set(e.user,"staffFamilyEnName",t)},expression:"user.staffFamilyEnName"}})],1),s("el-col",{attrs:{span:5}},[s("el-input",{attrs:{placeholder:"英文名称",maxlength:"30"},model:{value:e.user.staffGivingEnName,callback:function(t){e.$set(e.user,"staffGivingEnName",t)},expression:"user.staffGivingEnName"}})],1),s("el-col",{attrs:{span:4}},[s("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"性别"},model:{value:e.user.staffGender,callback:function(t){e.$set(e.user,"staffGender",t)},expression:"user.staffGender"}},e._l(e.dict.type.sys_user_sex,(function(e){return s("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),s("el-col",{attrs:{span:5}},[s("el-date-picker",{staticStyle:{width:"100%"},attrs:{format:"yyyy 年 MM 月 dd 日",placeholder:"生日",type:"date","value-format":"yyyy-MM-dd"},model:{value:e.user.staffBirthday,callback:function(t){e.$set(e.user,"staffBirthday",t)},expression:"user.staffBirthday"}})],1),s("el-col",{attrs:{span:5}},[s("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"证件类型"},model:{value:e.user.credentialType,callback:function(t){e.$set(e.user,"credentialType",t)},expression:"user.credentialType"}},e._l(e.dict.type.sys_credential_type,(function(e){return s("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),s("el-col",{attrs:{span:10}},[s("el-input",{attrs:{placeholder:"身份证号",maxlength:"50"},model:{value:e.user.staffIdentity,callback:function(t){e.$set(e.user,"staffIdentity",t)},expression:"user.staffIdentity"}})],1),s("el-col",{attrs:{span:4}},[s("el-input",{attrs:{placeholder:"民族",maxlength:"50"},model:{value:e.user.staffNativeplace,callback:function(t){e.$set(e.user,"staffNativeplace",t)},expression:"user.staffNativeplace"}})],1),s("el-col",{attrs:{span:5}},[s("el-input",{attrs:{placeholder:"国籍",maxlength:"50"},model:{value:e.user.staffNation,callback:function(t){e.$set(e.user,"staffNation",t)},expression:"user.staffNation"}})],1),s("el-col",{attrs:{span:5}},[s("el-input",{attrs:{placeholder:"宗教信仰",maxlength:"50"},model:{value:e.user.staffReligion,callback:function(t){e.$set(e.user,"staffReligion",t)},expression:"user.staffReligion"}})],1),s("el-col",{attrs:{span:5}},[s("el-input",{attrs:{placeholder:"政治面貌",maxlength:"50"},model:{value:e.user.staffPoliticalCountenance,callback:function(t){e.$set(e.user,"staffPoliticalCountenance",t)},expression:"user.staffPoliticalCountenance"}})],1),s("el-col",{attrs:{span:5}},[s("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"婚姻状况"},model:{value:e.user.staffMarital,callback:function(t){e.$set(e.user,"staffMarital",t)},expression:"user.staffMarital"}},e._l(e.dict.type.sys_user_marriage,(function(e){return s("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1)],1)],1),s("el-form-item",{attrs:{label:"联系方式"}},[s("el-row",[s("el-col",{attrs:{span:4}},[s("el-input",{attrs:{placeholder:"手机号码",maxlength:"11"},model:{value:e.user.staffTelNum,callback:function(t){e.$set(e.user,"staffTelNum",t)},expression:"user.staffTelNum"}})],1),s("el-col",{attrs:{span:5}},[s("el-input",{attrs:{placeholder:"工作QQ",maxlength:"20"},model:{value:e.user.staffQ,callback:function(t){e.$set(e.user,"staffQ",t)},expression:"user.staffQ"}})],1),s("el-col",{attrs:{span:5}},[s("el-input",{attrs:{placeholder:"固定电话",maxlength:"20"},model:{value:e.user.staffPhoneNum,callback:function(t){e.$set(e.user,"staffPhoneNum",t)},expression:"user.staffPhoneNum"}})],1),s("el-col",{attrs:{span:10}},[s("el-input",{attrs:{placeholder:"公司邮箱",maxlength:"50"},model:{value:e.user.staffEmailEnterprise,callback:function(t){e.$set(e.user,"staffEmailEnterprise",t)},expression:"user.staffEmailEnterprise"}})],1),s("el-col",{attrs:{span:4}},[s("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"居住性质"},model:{value:e.user.residentType,callback:function(t){e.$set(e.user,"residentType",t)},expression:"user.residentType"}},e._l(e.dict.type.sys_resident_type,(function(e){return s("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),s("el-col",{attrs:{span:5}},[s("location-select",{ref:"location",attrs:{pass:e.user.locationId,type:"location",placeholder:"居住区域"},on:{return:function(t){e.user.locationId=t}}})],1),s("el-col",{attrs:{span:15}},[s("el-input",{attrs:{maxlength:"50",placeholder:"详细地址"},model:{value:e.user.address,callback:function(t){e.$set(e.user,"address",t)},expression:"user.address"}})],1),s("el-col",{attrs:{span:4}},[s("el-input",{attrs:{placeholder:"紧急联系人",maxlength:"20"},model:{value:e.user.emergencyContactor,callback:function(t){e.$set(e.user,"emergencyContactor",t)},expression:"user.emergencyContactor"}})],1),s("el-col",{attrs:{span:5}},[s("el-input",{attrs:{placeholder:"联系人关系",maxlength:"20"},model:{value:e.user.emergencyContactorRelation,callback:function(t){e.$set(e.user,"emergencyContactorRelation",t)},expression:"user.emergencyContactorRelation"}})],1),s("el-col",{attrs:{span:15}},[s("el-input",{attrs:{placeholder:"紧急联系方式",maxlength:"20"},model:{value:e.user.emergencyPhone,callback:function(t){e.$set(e.user,"emergencyPhone",t)},expression:"user.emergencyPhone"}})],1)],1)],1),s("el-form-item",{attrs:{label:"学历信息"}},[s("el-row",[s("el-col",{attrs:{span:4}},[s("el-input",{attrs:{maxlength:"50",placeholder:"第一学历"},model:{value:e.user.firstDegree,callback:function(t){e.$set(e.user,"firstDegree",t)},expression:"user.firstDegree"}})],1),s("el-col",{attrs:{span:5}},[s("el-date-picker",{staticStyle:{width:"100%"},attrs:{format:"yyyy 年 MM 月 dd 日",placeholder:"毕业时间",type:"date","value-format":"yyyy-MM-dd"},model:{value:e.user.graduationDate,callback:function(t){e.$set(e.user,"graduationDate",t)},expression:"user.graduationDate"}})],1),s("el-col",{attrs:{span:5}},[s("el-input",{attrs:{maxlength:"50",placeholder:"毕业院校"},model:{value:e.user.graduationFrom,callback:function(t){e.$set(e.user,"graduationFrom",t)},expression:"user.graduationFrom"}})],1),s("el-col",{attrs:{span:5}},[s("el-input",{attrs:{maxlength:"50",placeholder:"专业"},model:{value:e.user.major,callback:function(t){e.$set(e.user,"major",t)},expression:"user.major"}})],1),s("el-col",{attrs:{span:5}},[s("el-input",{attrs:{maxlength:"50",placeholder:"最高学历"},model:{value:e.user.degree,callback:function(t){e.$set(e.user,"degree",t)},expression:"user.degree"}})],1)],1)],1),s("el-form-item",{attrs:{label:"人事备忘"}},[s("el-row",[s("el-col",{attrs:{span:4}},[s("el-date-picker",{staticStyle:{width:"100%"},attrs:{format:"yyyy 年 MM 月 dd 日",placeholder:"入职时间",type:"date","value-format":"yyyy-MM-dd"},model:{value:e.user.inductionDate,callback:function(t){e.$set(e.user,"inductionDate",t)},expression:"user.inductionDate"}})],1),s("el-col",{attrs:{span:5}},[s("el-input",{attrs:{placeholder:"健康状况",maxlength:"100"},model:{value:e.user.healthy,callback:function(t){e.$set(e.user,"healthy",t)},expression:"user.healthy"}})],1),s("el-col",{attrs:{span:5}},[s("el-input",{attrs:{maxlength:"50",placeholder:"过敏源"},model:{value:e.user.allergySource,callback:function(t){e.$set(e.user,"allergySource",t)},expression:"user.allergySource"}})],1),s("el-col",{attrs:{span:10}},[s("el-input",{attrs:{maxlength:"100",placeholder:"特别注意"},model:{value:e.user.specifically,callback:function(t){e.$set(e.user,"specifically",t)},expression:"user.specifically"}})],1)],1),s("el-input",{attrs:{autosize:{minRows:5,maxRows:20},maxlength:"200",placeholder:"人事部备注","show-word-limit":"",type:"textarea"},model:{value:e.user.remark,callback:function(t){e.$set(e.user,"remark",t)},expression:"user.remark"}})],1),s("el-form-item",[s("el-button",{attrs:{size:"mini",type:"primary"},on:{click:e.submit}},[e._v("保存")]),s("el-button",{attrs:{size:"mini",type:"danger"},on:{click:e.close}},[e._v("关闭")])],1)],1)},r=[],l=s("c0c7"),o={dicts:["sys_user_marriage","sys_normal_disable","sys_upload_status","sys_user_sex"],props:{user:{type:Object}},data:function(){return{rules:{staffUsername:[{required:!0,trigger:"blur"}],staffFamilyLocalName:[{required:!0,trigger:"blur"}],staffGivingLocalName:[{required:!0,trigger:"blur"}],staffFamilyEnName:[{required:!0,trigger:"blur"}],staffGivingEnName:[{required:!0,trigger:"blur"}],staffIdentity:[{pattern:/(^\d{15}$)|(^\d{18}$)|(^\d{17}(\d|X|x)$)/,message:"身份证格式错误",trigger:"blur"}],staffEmailEnterprise:[{required:!0,trigger:"blur"},{type:"email",trigger:["blur","change"]}],staffPhoneNum:[{required:!0,trigger:"blur"},{pattern:/^1[3|4|5|6|7|8|9][0-9]\d{8}$/,message:"正确的手机号码",trigger:"blur"}]}}},methods:{submit:function(){var e=this;this.$refs["form"].validate((function(t){t&&Object(l["l"])(e.user).then((function(t){e.$modal.msgSuccess("修改成功")}))}))},close:function(){this.$tab.closePage()}}},i=o,n=s("2877"),u=Object(n["a"])(i,a,r,!1,null,null,null);t["default"]=u.exports},"4c1b":function(e,t,s){"use strict";s.r(t);var a=function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("div",{staticClass:"app-container"},[s("el-row",{attrs:{gutter:20}},[s("el-col",{attrs:{span:6,xs:24}},[s("el-card",{staticClass:"box-card"},[s("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[s("span",[e._v("个人信息")])]),s("div",[s("div",{staticClass:"text-center"},[s("userAvatar",{attrs:{user:e.user}})],1),s("ul",{staticClass:"list-group list-group-striped"},[s("li",{staticClass:"list-group-item"},[s("svg-icon",{attrs:{"icon-class":"user"}}),e._v(" 用户名称 "),s("div",{staticClass:"pull-right"},[e._v(e._s(e.user.staffUsername))])],1),s("li",{staticClass:"list-group-item"},[s("svg-icon",{attrs:{"icon-class":"phone"}}),e._v(" 手机号码 "),s("div",{staticClass:"pull-right"},[e._v(e._s(e.user.staffPhoneNum))])],1),s("li",{staticClass:"list-group-item"},[s("svg-icon",{attrs:{"icon-class":"email"}}),e._v(" 用户邮箱 "),s("div",{staticClass:"pull-right"},[e._v(e._s(e.user.staffEmailEnterprise))])],1),s("li",{staticClass:"list-group-item"},[s("svg-icon",{attrs:{"icon-class":"tree"}}),e._v(" 所属部门 "),e.user.dept?s("div",{staticClass:"pull-right"},[e._v(e._s(e.user.dept.deptLocalName))]):e._e()],1),s("li",{staticClass:"list-group-item"},[s("svg-icon",{attrs:{"icon-class":"date"}}),e._v(" 创建日期 "),s("div",{staticClass:"pull-right"},[e._v(e._s(e.user.createTime))])],1)])])])],1),s("el-col",{attrs:{span:18,xs:24}},[s("el-card",[s("div",{staticClass:"clearfix",attrs:{slot:"header"},slot:"header"},[s("span",[e._v("基本资料")])]),s("el-tabs",{model:{value:e.activeTab,callback:function(t){e.activeTab=t},expression:"activeTab"}},[s("el-tab-pane",{attrs:{label:"基本资料",name:"userinfo"}},[s("userInfo",{attrs:{user:e.user}})],1),s("el-tab-pane",{attrs:{label:"修改密码",name:"resetPwd"}},[s("resetPwd")],1)],1)],1)],1)],1)],1)},r=[],l=s("9429"),o=s("1e8b"),i=s("ee46"),n=s("c0c7"),u={name:"Profile",components:{userAvatar:l["default"],userInfo:o["default"],resetPwd:i["default"]},data:function(){return{user:{},roleGroup:{},postGroup:{},activeTab:"userinfo"}},created:function(){this.getUser()},methods:{getUser:function(){var e=this;Object(n["g"])().then((function(t){e.user=t.data}))}}},c=u,p=s("2877"),d=Object(p["a"])(c,a,r,!1,null,null,null);t["default"]=d.exports},9429:function(e,t,s){"use strict";s.r(t);var a=function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("div",[s("div",{staticClass:"user-info-head",on:{click:function(t){return e.editCropper()}}},[s("img",{staticClass:"img-circle img-lg",attrs:{title:"点击上传头像",src:e.options.img}})]),s("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"},{name:"dialogDragWidth",rawName:"v-dialogDragWidth"}],attrs:{"close-on-click-modal":!1,"modal-append-to-body":!1,title:e.title,visible:e.open,"append-to-body":"",width:"800px"},on:{"update:visible":function(t){e.open=t},close:e.closeDialog,opened:e.modalOpened}},[s("el-row",[s("el-col",{style:{height:"350px"},attrs:{md:12,xs:24}},[e.visible?s("vue-cropper",{ref:"cropper",attrs:{autoCrop:e.options.autoCrop,autoCropHeight:e.options.autoCropHeight,autoCropWidth:e.options.autoCropWidth,fixedBox:e.options.fixedBox,img:e.options.img,info:!0,outputType:e.options.outputType},on:{realTime:e.realTime}}):e._e()],1),s("el-col",{style:{height:"350px"},attrs:{md:12,xs:24}},[s("div",{staticClass:"avatar-upload-preview"},[s("img",{style:e.previews.img,attrs:{src:e.previews.url}})])])],1),s("br"),s("el-row",[s("el-col",{attrs:{lg:2,sm:3,xs:3}},[s("el-upload",{attrs:{"before-upload":e.beforeUpload,"http-request":e.requestUpload,"show-file-list":!1,action:"#"}},[s("el-button",{attrs:{size:"mini"}},[e._v(" 选择 "),s("i",{staticClass:"el-icon-upload el-icon--right"})])],1)],1),s("el-col",{attrs:{lg:{span:1,offset:2},sm:2,xs:2}},[s("el-button",{attrs:{icon:"el-icon-plus",size:"mini"},on:{click:function(t){return e.changeScale(1)}}})],1),s("el-col",{attrs:{lg:{span:1,offset:2},sm:2,xs:2}},[s("el-button",{attrs:{icon:"el-icon-minus",size:"mini"},on:{click:function(t){return e.changeScale(-1)}}})],1),s("el-col",{attrs:{lg:{span:1,offset:2},sm:2,xs:2}},[s("el-button",{attrs:{icon:"el-icon-refresh-left",size:"mini"},on:{click:function(t){return e.rotateLeft()}}})],1),s("el-col",{attrs:{lg:{span:1,offset:2},sm:2,xs:2}},[s("el-button",{attrs:{icon:"el-icon-refresh-right",size:"mini"},on:{click:function(t){return e.rotateRight()}}})],1),s("el-col",{attrs:{lg:{span:2,offset:6},sm:2,xs:2}},[s("el-button",{attrs:{size:"mini",type:"primary"},on:{click:function(t){return e.uploadImg()}}},[e._v("提 交")])],1)],1)],1)],1)},r=[],l=s("4360"),o=s("7e79"),i=s("c0c7"),n=s("ed08"),u={components:{VueCropper:o["VueCropper"]},props:{user:{type:Object}},data:function(){return{open:!1,visible:!1,title:"修改头像",options:{img:l["a"].getters.avatar,autoCrop:!0,autoCropWidth:200,autoCropHeight:200,fixedBox:!0,outputType:"png"},previews:{},resizeHandler:null}},methods:{editCropper:function(){this.open=!0},modalOpened:function(){var e=this;this.visible=!0,this.resizeHandler||(this.resizeHandler=Object(n["b"])((function(){e.refresh()}),100)),window.addEventListener("resize",this.resizeHandler)},refresh:function(){this.$refs.cropper.refresh()},requestUpload:function(){},rotateLeft:function(){this.$refs.cropper.rotateLeft()},rotateRight:function(){this.$refs.cropper.rotateRight()},changeScale:function(e){e=e||1,this.$refs.cropper.changeScale(e)},beforeUpload:function(e){var t=this;if(-1==e.type.indexOf("image/"))this.$modal.msgError("文件格式错误，请上传图片类型,如：JPG，PNG后缀的文件。");else{var s=new FileReader;s.readAsDataURL(e),s.onload=function(){t.options.img=s.result}}},uploadImg:function(){var e=this;this.$refs.cropper.getCropBlob((function(t){var s=new FormData;s.append("avatarfile",t),Object(i["n"])(s).then((function(t){e.open=!1,e.options.img="/prod-api"+t.imgUrl,l["a"].commit("SET_AVATAR",e.options.img),e.$modal.msgSuccess("修改成功"),e.visible=!1}))}))},realTime:function(e){this.previews=e},closeDialog:function(){this.options.img=l["a"].getters.avatar,this.visible=!1,window.removeEventListener("resize",this.resizeHandler)}}},c=u,p=(s("bed7"),s("2877")),d=Object(p["a"])(c,a,r,!1,null,"129b2dc5",null);t["default"]=d.exports},bed7:function(e,t,s){"use strict";s("0b95")},ee46:function(e,t,s){"use strict";s.r(t);var a=function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("el-form",{ref:"form",attrs:{model:e.user,rules:e.rules,"label-width":"80px"}},[s("el-form-item",{attrs:{label:"旧密码",prop:"oldPassword"}},[s("el-input",{attrs:{placeholder:"旧密码","show-password":"",type:"password"},model:{value:e.user.oldPassword,callback:function(t){e.$set(e.user,"oldPassword",t)},expression:"user.oldPassword"}})],1),s("el-form-item",{attrs:{label:"新密码",prop:"newPassword"}},[s("el-input",{attrs:{placeholder:"新密码","show-password":"",type:"password"},model:{value:e.user.newPassword,callback:function(t){e.$set(e.user,"newPassword",t)},expression:"user.newPassword"}})],1),s("el-form-item",{attrs:{label:"确认密码",prop:"confirmPassword"}},[s("el-input",{attrs:{placeholder:"请确认新密码","show-password":"",type:"password"},model:{value:e.user.confirmPassword,callback:function(t){e.$set(e.user,"confirmPassword",t)},expression:"user.confirmPassword"}})],1),s("el-form-item",[s("el-button",{attrs:{size:"mini",type:"primary"},on:{click:e.submit}},[e._v("保存")]),s("el-button",{attrs:{size:"mini",type:"danger"},on:{click:e.close}},[e._v("关闭")])],1)],1)},r=[],l=(s("d9e2"),s("c0c7")),o={data:function(){var e=this,t=function(t,s,a){e.user.newPassword!=s?a(new Error("两次输入的密码不一致")):a()};return{user:{oldPassword:void 0,newPassword:void 0,confirmPassword:void 0},rules:{oldPassword:[{required:!0,trigger:"blur"}],newPassword:[{required:!0,trigger:"blur"},{min:6,max:20,message:"长度在 6 到 20 个字符",trigger:"blur"}],confirmPassword:[{required:!0,trigger:"blur"},{required:!0,validator:t,trigger:"blur"}]}}},methods:{submit:function(){var e=this;this.$refs["form"].validate((function(t){t&&Object(l["m"])(e.user.oldPassword,e.user.newPassword).then((function(t){e.$modal.msgSuccess("修改成功")}))}))},close:function(){this.$tab.closePage()}}},i=o,n=s("2877"),u=Object(n["a"])(i,a,r,!1,null,null,null);t["default"]=u.exports}}]);