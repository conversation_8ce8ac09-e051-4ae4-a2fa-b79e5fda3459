(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-6c9ae873"],{"4ddb":function(e,t,a){"use strict";a.d(t,"d",(function(){return s})),a.d(t,"e",(function(){return l})),a.d(t,"c",(function(){return n})),a.d(t,"a",(function(){return i})),a.d(t,"f",(function(){return o})),a.d(t,"b",(function(){return c}));var r=a("b775");function s(e){return Object(r["a"])({url:"/system/staffrole/list",method:"get",params:e})}function l(e){return Object(r["a"])({url:"/system/staffrole/menuList",method:"get",params:e})}function n(e){return Object(r["a"])({url:"/system/staffrole/"+e,method:"get"})}function i(e){return Object(r["a"])({url:"/system/staffrole",method:"post",data:e})}function o(e){return Object(r["a"])({url:"/system/staffrole",method:"put",data:e})}function c(e){return Object(r["a"])({url:"/system/staffrole/"+e,method:"delete"})}},f61e:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:e.showLeft}},[a("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],ref:"queryForm",staticClass:"query",attrs:{inline:!0,model:e.queryParams,"label-width":"35px",size:"mini"}},[a("el-form-item",{attrs:{label:"员工",prop:"staffId"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{filterable:"",placeholder:"选择员工"},on:{change:e.handleQuery},model:{value:e.queryParams.staffId,callback:function(t){e.$set(e.queryParams,"staffId",t)},expression:"queryParams.staffId"}},e._l(e.userList,(function(e){return a("el-option",{key:e.staffId,attrs:{label:e.staffCode+" "+e.staffFamilyLocalName+e.staffGivingLocalName+" "+e.staffGivingEnName,value:e.staffId}})})),1)],1),a("el-form-item",{attrs:{label:"角色",prop:"roleId"}},[a("tree-select",{staticStyle:{width:"100%"},attrs:{pass:e.queryParams.roleId,placeholder:"选择权限",type:"role",dbn:!0},on:{return:e.queryRoleId}})],1),a("el-form-item",{attrs:{label:"部门",prop:"deptId"}},[a("tree-select",{staticStyle:{width:"100%"},attrs:{multiple:!1,pass:e.queryParams.deptId,placeholder:"选择部门",type:"dept"},on:{return:e.queryDeptId}})],1),a("el-form-item",{attrs:{label:"主职",prop:"isMain"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"是否主职"},on:{change:e.handleQuery},model:{value:e.queryParams.isMain,callback:function(t){e.$set(e.queryParams,"isMain",t)},expression:"queryParams.isMain"}},e._l(e.dict.type.sys_yes_no,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),a("el-form-item",[a("el-button",{attrs:{icon:"el-icon-search",size:"mini",type:"primary"},on:{click:e.handleQuery}},[e._v("搜索")]),a("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1)],1),a("el-col",{attrs:{span:e.showRight}},[a("el-row",{staticClass:"mb8",attrs:{gutter:10}},[a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:role:add"],expression:"['system:role:add']"}],attrs:{icon:"el-icon-plus",plain:"",size:"mini",type:"primary"},on:{click:e.handleAdd}},[e._v("新增 ")])],1),a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:role:remove"],expression:"['system:role:remove']"}],attrs:{disabled:e.multiple,icon:"el-icon-delete",plain:"",size:"mini",type:"danger"},on:{click:e.handleDelete}},[e._v("删除 ")])],1),a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:role:export"],expression:"['system:role:export']"}],attrs:{icon:"el-icon-download",plain:"",size:"mini",type:"warning"},on:{click:e.handleExport}},[e._v("导出 ")])],1),a("right-toolbar",{attrs:{showSearch:e.showSearch},on:{"update:showSearch":function(t){e.showSearch=t},"update:show-search":function(t){e.showSearch=t},queryTable:e.getList}})],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.staffroleList},on:{"selection-change":e.handleSelectionChange}},[a("el-table-column",{attrs:{type:"selection",width:"28",align:"center"}}),a("el-table-column",{attrs:{align:"left",label:"员工"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(t.row.staff.staffCode)+" "),a("a",{staticStyle:{margin:"0","font-weight":"bold","font-size":"small"}},[e._v(e._s(t.row.staff.staffFamilyLocalName+t.row.staff.staffGivingLocalName))]),e._v(" "+e._s(t.row.staff.staffGivingEnName)+" ")]}}])}),a("el-table-column",{attrs:{align:"left",label:"角色名称"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("a",{staticStyle:{margin:"0","font-weight":"bold","font-size":"small"}},[e._v(e._s(t.row.role.roleLocalName))]),e._v(" "+e._s(t.row.role.roleEnName)+" ")]}}])}),a("el-table-column",{key:"role",attrs:{align:"center",label:"权限概览"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-popover",{attrs:{placement:"right",trigger:"hover",width:"400"}},[a("el-tree",{ref:t.row.roleId,staticClass:"tree-border",attrs:{"check-strictly":!t.row.role.menuCheckStrictly,data:t.row.menuList,props:e.defaultProps,"empty-text":"不存在权限","node-key":"id"}}),a("el-button",{staticStyle:{margin:"0",padding:"5px"},attrs:{slot:"reference"},nativeOn:{mouseenter:function(a){return e.loadMenu(t.row)}},slot:"reference"},[e._v("查看 ")])],1)]}}])}),a("el-table-column",{attrs:{align:"center",label:"所属部门",prop:"dept.deptLocalName",width:"100"}}),a("el-table-column",{attrs:{align:"center",label:"是否主职",prop:"isMain",width:"68"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("dict-tag",{attrs:{options:e.dict.type.sys_yes_no,value:t.row.isMain}})]}}])}),a("el-table-column",{attrs:{align:"center","class-name":"small-padding fixed-width",label:"操作",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:role:edit"],expression:"['system:role:edit']"}],staticStyle:{"margin-right":"-8px"},attrs:{icon:"el-icon-edit",size:"mini",type:"success"},on:{click:function(a){return e.handleUpdate(t.row)}}},[e._v("修改 ")]),a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:role:remove"],expression:"['system:role:remove']"}],staticStyle:{"margin-right":"-8px"},attrs:{icon:"el-icon-delete",size:"mini",type:"danger"},on:{click:function(a){return e.handleDelete(t.row)}}},[e._v("删除 ")])]}}])})],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total>0"}],attrs:{limit:e.queryParams.pageSize,page:e.queryParams.pageNum,total:e.total},on:{"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},pagination:e.getList}})],1)],1),a("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"},{name:"dialogDragWidth",rawName:"v-dialogDragWidth"}],attrs:{"close-on-click-modal":!1,"modal-append-to-body":!1,title:e.title,visible:e.open,"append-to-body":"",width:"500px"},on:{"update:visible":function(t){e.open=t}}},[a("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"120px"}},[a("el-form-item",{attrs:{label:"员工",prop:"staffId"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{filterable:"",placeholder:"选择员工"},model:{value:e.form.staffId,callback:function(t){e.$set(e.form,"staffId",t)},expression:"form.staffId"}},e._l(e.userList,(function(e){return a("el-option",{key:e.staffId,attrs:{label:e.staffCode+" "+e.staffFamilyLocalName+e.staffGivingLocalName+" "+e.staffGivingEnName,value:e.staffId}})})),1)],1),a("el-form-item",{attrs:{label:"角色名称",prop:"roleId"}},[a("tree-select",{attrs:{pass:e.form.roleId,type:"role",dbn:!0},on:{return:e.getRoleId}})],1),a("el-form-item",{attrs:{label:"所属部门",prop:"deptId"}},[a("tree-select",{attrs:{multiple:!1,pass:e.form.deptId,type:"dept"},on:{return:e.getDeptId}})],1),a("el-form-item",{attrs:{label:"是否主职",prop:"isMain"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"是否主职"},model:{value:e.form.isMain,callback:function(t){e.$set(e.form,"isMain",t)},expression:"form.isMain"}},e._l(e.dict.type.sys_yes_no,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("确 定")]),a("el-button",{on:{click:e.cancel}},[e._v("取 消")])],1)],1)],1)},s=[],l=a("5530"),n=a("b85c"),i=(a("d81d"),a("4ddb")),o=a("ca17"),c=a.n(o),d=(a("6f8d"),a("c0c7")),f={name:"Staffrole",dicts:["sys_yes_no"],components:{Treeselect:c.a},data:function(){return{showLeft:3,showRight:21,total:0,userList:[],defaultProps:{children:"children",label:"label"},loading:!0,ids:[],single:!0,multiple:!0,showSearch:!0,staffroleList:[],title:"",open:!1,queryParams:{pageNum:1,pageSize:20,roleId:null,deptId:null,isMain:null,staffId:null},form:{},rules:{staffId:[{required:!0,trigger:"blur"}],roleId:[{required:!0,message:"权限不能为空",trigger:"blur"}],isMain:[{required:!0,message:"是否主要权限不能为空",trigger:"blur"}],deptId:[{required:!0,message:"部门ID不能为空",trigger:"blur"}]}}},watch:{showSearch:function(e){1==e?(this.showRight=21,this.showLeft=3):(this.showRight=24,this.showLeft=0)}},created:function(){var e=this;this.getList(),Object(d["j"])().then((function(t){e.userList=t.data}))},methods:{getList:function(){var e=this;this.loading=!0,Object(i["d"])(this.queryParams).then((function(t){e.staffroleList=t.rows,e.total=t.total,e.loading=!1}))},loadMenu:function(e){if(null==e.menuList){var t={staffId:e.staffId};Object(i["e"])(t).then((function(t){e.menuList=t.data}))}},cancel:function(){this.open=!1,this.reset()},reset:function(){this.form={staffRoleDeptId:null,staffId:null,roleId:null,isMain:"N",deptId:null},this.resetForm("form")},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.resetForm("queryForm"),this.handleQuery()},handleSelectionChange:function(e){this.ids=e.map((function(e){return e.staffRoleDeptId})),this.single=1!=e.length,this.multiple=!e.length},handleAdd:function(){this.reset(),this.open=!0,this.title="添加权限分配"},handleUpdate:function(e){var t=this;this.reset();var a=e.staffRoleDeptId||this.ids;Object(i["c"])(a).then((function(e){var a,r=Object(n["a"])(t.userList);try{for(r.s();!(a=r.n()).done;){var s=a.value;if(void 0!=s.children){var l,i=Object(n["a"])(s.children);try{for(i.s();!(l=i.n()).done;){var o=l.value;if(void 0!=o.children){var c,d=Object(n["a"])(o.children);try{for(d.s();!(c=d.n()).done;){var f=c.value;if(void 0!=f.children){var u,m=Object(n["a"])(f.children);try{for(m.s();!(u=m.n()).done;){var h=u.value;if(h.staffId==e.data.staffId&&(t.staffId=h.deptId),void 0!=h.children){var p,y=Object(n["a"])(h.children);try{for(y.s();!(p=y.n()).done;){var g=p.value;g.staffId==e.data.staffId&&(t.staffId=g.deptId)}}catch(v){y.e(v)}finally{y.f()}}}}catch(v){m.e(v)}finally{m.f()}}}}catch(v){d.e(v)}finally{d.f()}}}}catch(v){i.e(v)}finally{i.f()}}}}catch(v){r.e(v)}finally{r.f()}t.form=e.data,t.open=!0,t.title="修改权限分配"}))},submitForm:function(){var e=this;this.$refs["form"].validate((function(t){t&&(null!=e.form.staffRoleDeptId?Object(i["f"])(e.form).then((function(t){e.$modal.msgSuccess("修改成功"),e.open=!1,e.getList()})):Object(i["a"])(e.form).then((function(t){e.$modal.msgSuccess("新增成功"),e.open=!1,e.getList()})))}))},handleDelete:function(e){var t=this,a=e.staffRoleDeptId||this.ids;this.$confirm('是否确认删除权限分配编号为"'+a+'"的数据项？',"提示",{customClass:"modal-confirm"}).then((function(){return Object(i["b"])(a)})).then((function(){t.getList(),t.$modal.msgSuccess("删除成功")})).catch((function(){}))},handleExport:function(){this.download("system/staffrole/export",Object(l["a"])({},this.queryParams),"staffrole_".concat((new Date).getTime(),".xlsx"))},getDeptId:function(e){this.form.deptId=e},getRoleId:function(e){this.form.roleId=e},queryDeptId:function(e){this.queryParams.deptId=e},queryRoleId:function(e){this.queryParams.roleId=e}}},u=f,m=a("2877"),h=Object(m["a"])(u,r,s,!1,null,null,null);t["default"]=h.exports}}]);