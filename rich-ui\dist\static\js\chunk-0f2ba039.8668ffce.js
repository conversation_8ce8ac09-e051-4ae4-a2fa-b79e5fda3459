(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-0f2ba039","chunk-46f65df6","chunk-2d0cc474","chunk-2d20feaa","chunk-2d0cbcf1"],{"4af4":function(e,t,r){"use strict";r.r(t);var i=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",[r("h6",{staticStyle:{margin:"0"}},[e._v(e._s(0!=e.scope.row.grossWeight?"+"+e.scope.row.grossWeight+e.scope.row.cargoUnit:""))])])},a=[],s={name:"weight",props:["scope"],data:function(){return{size:this.$store.state.app.size||"mini"}}},n=s,o=r("2877"),l=Object(o["a"])(n,i,a,!1,null,"de015dcc",null);t["default"]=l.exports},"4cf5":function(e,t,r){"use strict";r.r(t);var i=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",[r("el-tooltip",{attrs:{disabled:null==e.scope.row.psaRemark||e.scope.row.psaRemark.length<12,placement:"top"}},[r("div",{attrs:{slot:"content"},slot:"content"},[r("h6",{staticStyle:{margin:"0"}},[e._v(e._s(e.scope.row.psaRemark))])]),r("div",[r("h6",{staticStyle:{margin:"0",overflow:"hidden","text-overflow":"ellipsis","white-space":"nowrap"}},[e._v(" "+e._s(e.scope.row.psaRemark)+" ")])])])],1)},a=[],s={name:"businessRemark",props:["scope"],data:function(){return{size:this.$store.state.app.size||"mini"}}},n=s,o=r("2877"),l=Object(o["a"])(n,i,a,!1,null,"b581cfc8",null);t["default"]=l.exports},5768:function(e,t,r){},"86ea":function(e,t,r){"use strict";r.r(t);var i=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",[r("el-tooltip",{attrs:{disabled:null==e.scope.row.company||e.scope.row.company.length<3||((null!=e.scope.row.contractType?e.scope.row.contractType:"")+(null!=e.scope.row.contractType&&null!=e.scope.row.contractNo?"：":"")+(null!=e.scope.row.contractNo?e.scope.row.contractNo:"")).length<10,placement:"top"}},[r("div",{attrs:{slot:"content"},slot:"content"},[r("div",{staticStyle:{margin:"0","font-size":"15px","font-weight":"bold"}},[e._v(e._s(e.scope.row.carrierCode)+" "),r("span",{staticClass:"unHighlight-text",staticStyle:{margin:"0","font-weight":"bold","font-size":"12px"}},[e._v(" "+e._s("("+(null!=e.scope.row.contractType?e.scope.row.contractType:"")+")")+" ")])])]),r("div",{staticStyle:{margin:"0",overflow:"hidden","text-overflow":"ellipsis","white-space":"nowrap"}},[r("div",{staticStyle:{margin:"0","font-size":"15px","font-weight":"bold"}},[e._v(e._s(e.scope.row.carrierCode)+" "),r("span",{staticClass:"unHighlight-text",staticStyle:{margin:"0","font-weight":"bold","font-size":"12px"}},[e._v(" "+e._s("("+(null!=e.scope.row.contractType?e.scope.row.contractType:"")+")")+" ")])])])])],1)},a=[],s={name:"carrierNoSupplier",props:["scope"],data:function(){return{size:this.$store.state.app.size||"mini"}}},n=s,o=(r("efc0"),r("2877")),l=Object(o["a"])(n,i,a,!1,null,"36cb6b05",null);t["default"]=l.exports},b655:function(e,t,r){"use strict";r.r(t);var i=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",[r("el-tooltip",{attrs:{placement:"top",disabled:null==e.scope.row.company||e.scope.row.company.length<3||((null!=e.scope.row.contractType?e.scope.row.contractType:"")+(null!=e.scope.row.contractType&&null!=e.scope.row.contractNo?"：":"")+(null!=e.scope.row.contractNo?e.scope.row.contractNo:"")).length<10}},[r("div",{attrs:{slot:"content"},slot:"content"},[r("h6",{staticStyle:{margin:"0"}},[e._v(" "+e._s(e.scope.row.company)+" ")]),r("h6",{staticStyle:{margin:"0","font-weight":"bold","font-size":"small"}},[e._v(" "+e._s((null!=e.scope.row.contractType?e.scope.row.contractType:"")+(null!=e.scope.row.contractType&&null!=e.scope.row.contractNo&&""!=e.scope.row.contractNo?"：":"")+(null!=e.scope.row.contractNo?e.scope.row.contractNo:""))+" ")])]),r("div",[r("h6",{staticStyle:{margin:"0",overflow:"hidden","text-overflow":"ellipsis","white-space":"nowrap"}},[e._v(" "+e._s(e.scope.row.company)+" ")]),r("h6",{staticStyle:{margin:"0",overflow:"hidden","text-overflow":"ellipsis","white-space":"nowrap"}},[e._v(" "+e._s((null!=e.scope.row.contractType?e.scope.row.contractType:"")+(null!=e.scope.row.contractType&&null!=e.scope.row.contractNo&&""!=e.scope.row.contractNo?"：":"")+(null!=e.scope.row.contractNo?e.scope.row.contractNo:""))+" ")])])])],1)},a=[],s={name:"company",props:["scope"],data:function(){return{size:this.$store.state.app.size||"mini"}}},n=s,o=r("2877"),l=Object(o["a"])(n,i,a,!1,null,"6d377e90",null);t["default"]=l.exports},c3e2:function(e,t,r){"use strict";r.r(t);var i=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",{staticClass:"app-container"},[r("el-row",{attrs:{gutter:20}},[r("el-col",{staticStyle:{margin:"0",padding:"0"},attrs:{span:e.showLeft}},[e.showSearch?r("el-form",{ref:"queryForm",staticClass:"query",attrs:{inline:!0,model:e.queryParams,"label-width":"35px",size:"mini"}},[r("el-form-item",{attrs:{label:"服务",prop:"logisticsTypeId"}},[r("tree-select",{staticStyle:{width:"100%"},attrs:{"d-load":!0,dbn:!0,flat:!1,multiple:!1,pass:e.queryParams.serviceTypeId,placeholder:"服务类型",type:"serviceTypeLoad",typeId:e.typeId},on:{return:e.queryLogisticsTypeId,returnData:e.queryLogisticsType}})],1),r("el-form-item",{attrs:{label:"序号",prop:"inquiryNo"}},[r("el-input",{staticStyle:{width:"100%"},attrs:{placeholder:"询价单号"},model:{value:e.queryParams.inquiryNo,callback:function(t){e.$set(e.queryParams,"inquiryNo",t)},expression:"queryParams.inquiryNo"}})],1),-1!==[5].indexOf(parseInt(e.typeId))?r("el-form-item",{attrs:{label:"装运",prop:"departureIds"}},[r("location-select",{attrs:{"load-options":e.locationOptions,multiple:!1,pass:e.queryParams.precarriageRegionId},on:{return:e.queryPrecarriageRegionId}})],1):e._e(),-1!==[1,2,3,4,5,6,7].indexOf(parseInt(e.typeId))?r("el-form-item",{attrs:{label:"启运",prop:"departureIds"}},[r("location-select",{staticStyle:{width:"100%"},attrs:{"load-options":e.freightSelectData.locationOptions,multiple:!0,pass:e.queryParams.departureIds},on:{return:e.queryDepartureIds}})],1):e._e(),-1!==[1].indexOf(parseInt(e.typeId))?r("el-form-item",{attrs:{label:"中转",prop:"transitPortIds"}},[r("location-select",{staticStyle:{width:"100%"},attrs:{"load-options":e.locationOptions,multiple:!0,pass:e.queryParams.transitPortIds},on:{return:e.queryTransitPortIds}})],1):e._e(),-1!==[1,2,3,4,6,7].indexOf(parseInt(e.typeId))?r("el-form-item",{attrs:{label:"目的",prop:"destinationIds"}},[r("location-select",{staticStyle:{width:"100%"},attrs:{en:!0,"load-options":e.freightSelectData.locationOptions,multiple:!0,pass:e.queryParams.destinationIds},on:{return:e.queryDestinationIds}})],1):e._e(),-1!==[1].indexOf(parseInt(e.typeId))?r("el-form-item",{attrs:{label:"航线",prop:"lineDestinationIds"}},[r("tree-select",{staticStyle:{width:"100%"},attrs:{"d-load":!0,flat:!1,multiple:!0,pass:e.queryParams.lineDestinationIds,placeholder:"目的航线",type:"line"},on:{return:e.queryLineDestinationIds}})],1):e._e(),r("el-form-item",{attrs:{label:"柜型",prop:"unitIds"}},[r("tree-select",{staticStyle:{width:"100%"},attrs:{"d-load":!0,multiple:!0,pass:e.queryParams.unitIds,placeholder:"柜型",type:"unit"},on:{return:e.queryUnitIds}})],1),r("el-form-item",{attrs:{label:"货物",prop:"cargoTypeIds"}},[r("tree-select",{staticStyle:{width:"100%"},attrs:{"d-load":!0,flat:!0,multiple:!0,pass:e.queryParams.cargoTypeIds,placeholder:"货物特征",type:"cargoType"},on:{return:e.queryCargoTypeIds}})],1),r("el-form-item",{attrs:{label:"承运",prop:"carrierIds"}},[r("treeselect",{staticStyle:{width:"100%"},attrs:{"disable-branch-nodes":!0,"disable-fuzzy-matching":!0,"flatten-search-results":!0,multiple:!0,normalizer:e.carrierNormalizer,options:e.carrierList,"show-count":!0,placeholder:"承运人"},on:{deselect:e.handleDeselectQueryCarrierIds,input:e.deselectAllQueryCarrierIds,open:e.loadCarrier,select:e.handleSelectQueryCarrierIds},scopedSlots:e._u([{key:"value-label",fn:function(t){var i=t.node;return r("div",{},[e._v(" "+e._s(i.raw.carrier.carrierIntlCode)+" "+e._s(null==i.raw.carrier.carrierIntlCode?i.raw.carrier.carrierShortName:"")+" ")])}},{key:"option-label",fn:function(t){var i=t.node,a=t.shouldShowCount,s=t.count,n=t.labelClassName,o=t.countClassName;return r("label",{class:n},[e._v(" "+e._s(-1!=i.label.indexOf(",")?i.label.substring(0,i.label.indexOf(",")):i.label)+" "),a?r("span",{class:o},[e._v("("+e._s(s)+")")]):e._e()])}}],null,!1,4006291921),model:{value:e.queryCarrierIds,callback:function(t){e.queryCarrierIds=t},expression:"queryCarrierIds"}})],1),r("el-form-item",{attrs:{label:e.company.substring(0,2),prop:"companyIds"}},[r("tree-select",{ref:"supplier",staticStyle:{width:"100%"},attrs:{"d-load":!0,flat:!1,multiple:!0,pass:e.queryParams.companyIds,placeholder:e.company,type:"supplier"},on:{return:e.queryCompanyIds},nativeOn:{click:function(t){return e.remoteSupplier(!0)}}})],1),r("el-form-item",{attrs:{label:"合约",prop:"contractTypeIds"}},[r("tree-select",{staticStyle:{width:"100%"},attrs:{"d-load":!0,dbn:!0,flat:!1,multiple:!0,pass:e.queryParams.contractTypeIds,placeholder:"合约类别",type:"contractType"},on:{return:e.queryContractTypeIds}})],1),r("el-form-item",{attrs:{label:"有效",prop:"validFrom"}},[r("el-date-picker",{staticStyle:{width:"100%"},attrs:{"end-placeholder":"有效期结束日期","range-separator":"至","start-placeholder":"有效期开始日期","default-time":["00:00:00","23:59:59"],type:"daterange","value-format":"yyyy-MM-dd HH:mm:ss"},on:{change:e.handleQuery},model:{value:e.queryParams.dateRange,callback:function(t){e.$set(e.queryParams,"dateRange",t)},expression:"queryParams.dateRange"}})],1),r("el-form-item",{attrs:{label:"搜索",prop:"freightQuery"}},[r("el-input",{staticStyle:{width:"100%"},attrs:{placeholder:"合约号/船期/报价总述/备注"},model:{value:e.queryParams.freightQuery,callback:function(t){e.$set(e.queryParams,"freightQuery",t)},expression:"queryParams.freightQuery"}})],1),r("el-form-item",{attrs:{label:"录入",prop:"updateBy"}},[r("treeselect",{staticStyle:{width:"100%"},attrs:{"disable-branch-nodes":!0,"disable-fuzzy-matching":!0,"flatten-search-results":!0,normalizer:e.staffNormalizer,options:e.businessList,"show-count":!0,placeholder:"所属人"},on:{input:e.cleanRecordBy,open:e.loadBusinesses,select:e.handleSelectRecordBy},scopedSlots:e._u([{key:"value-label",fn:function(t){var i=t.node;return r("div",{},[e._v(" "+e._s(void 0!=i.raw.staff?i.raw.staff.staffFamilyLocalName+i.raw.staff.staffGivingLocalName+" "+i.raw.staff.staffGivingEnName:"")+" ")])}},{key:"option-label",fn:function(t){var i=t.node,a=t.shouldShowCount,s=t.count,n=t.labelClassName,o=t.countClassName;return r("label",{class:n},[e._v(" "+e._s(-1!=i.label.indexOf(",")?i.label.substring(0,i.label.indexOf(",")):i.label)+" "),a?r("span",{class:o},[e._v("("+e._s(s)+")")]):e._e()])}}],null,!1,553526311),model:{value:e.updateBy,callback:function(t){e.updateBy=t},expression:"updateBy"}})],1),r("el-form-item",{attrs:{label:"来源",prop:"isSalesRequired"}},[r("el-select",{attrs:{clearable:""},on:{change:e.handleQuery},model:{value:e.queryParams.isSalesRequired,callback:function(t){e.$set(e.queryParams,"isSalesRequired",t)},expression:"queryParams.isSalesRequired"}},[r("el-option",{attrs:{label:"业务询价",value:"1"}}),r("el-option",{attrs:{label:"商务播报",value:"0"}})],1)],1),r("el-form-item",{attrs:{label:"回复",prop:"replyMark"}},[r("el-select",{attrs:{clearable:""},on:{change:e.handleQuery},model:{value:e.queryParams.replyMark,callback:function(t){e.$set(e.queryParams,"replyMark",t)},expression:"queryParams.replyMark"}},[r("el-option",{attrs:{label:"未回复",value:"0"}}),r("el-option",{attrs:{label:"已回复",value:"1"}})],1)],1),r("el-form-item",[r("el-button",{attrs:{icon:"el-icon-search",size:"mini",type:"primary"},on:{click:e.handleQuery}},[e._v("搜索")]),r("el-button",{staticStyle:{"margin-left":"3px"},attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1):e._e()],1),r("el-col",{attrs:{span:e.showRight}},[r("div",{staticStyle:{display:"flex"}},[r("h4",{staticStyle:{margin:"0","font-weight":"bold"}},[e._v(" 服务类型："+e._s(e.serviceType)+"-"+e._s(e.charge)+" ")]),r("el-button",{attrs:{type:"primary"},on:{click:e.confirmRequire}},[e._v(" 请求更新 ")])],1),e.refreshTable?r("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.freightList,"row-class-name":e.tableRowClassName,border:""},on:{"row-dblclick":e.dbclick,"selection-change":e.handleSelectionChange}},[e._l(e.columns,(function(t){return t.visible?r("el-table-column",{key:t.key,attrs:{align:t.align,label:t.label,width:t.width},scopedSlots:e._u([{key:"default",fn:function(i){return[r(t.prop,{tag:"component",attrs:{scope:i,typeId:e.typeId}})]}}],null,!0)}):e._e()})),r("el-table-column",{key:"isSalesRequired",attrs:{align:"center",label:"价格来源",width:"75"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("h6",{staticStyle:{margin:"0"}},[e._v(e._s(t.row.salesName))]),r("h6",{staticStyle:{margin:"0"}},[e._v(" "+e._s(1==t.row.isSalesRequired?"业务询价":"")+" "+e._s(0==t.row.isSalesRequired?"商务播报":"")+" ")])]}}],null,!1,2589291158)}),r("el-table-column",{attrs:{align:"center","class-name":"small-padding fixed-width",label:"操作",width:"90"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("el-dropdown",{attrs:{trigger:"click"},on:{command:e.handleSelect}},[r("el-button",{staticStyle:{margin:"2px"},attrs:{size:"mini",type:"success"},on:{click:function(r){e.select=t.row}}},[e._v("确认选择 ")]),r("el-dropdown-menu",{attrs:{slot:"dropdown"},slot:"dropdown"},e._l(e.freightSelectData.revenueTonList,(function(t,i){return r("el-dropdown-item",{key:i,attrs:{command:t}},[e._v(" "+e._s(t)+" ")])})),1)],1)]}}],null,!1,3168294462)})],2):e._e(),r("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total>0"}],attrs:{limit:e.queryParams.pageSize,page:e.queryParams.pageNum,total:e.total},on:{"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},pagination:e.getList}})],1)],1)],1)},a=[],s=r("c7eb"),n=r("1da1"),o=r("5530"),l=r("b85c"),c=(r("d3b7"),r("6062"),r("3ca3"),r("ddb0"),r("99af"),r("14d9"),r("d81d"),r("a15b"),r("a9e3"),r("25f0"),r("4de4"),r("d6c9")),d=r("ca17"),u=r.n(d),h=(r("6f8d"),r("b0b8")),p=r.n(h),f=r("5f87"),m=r("4360"),y=r("aff7"),g=r("4cf5"),I=r("2a23"),v=r("e996"),b=r("b655"),T=r("b66c"),w=r("0f6b"),S=r("6b1f"),C=r("98dc"),q=r("87df"),N=r("42dd"),L=r("8eee"),P=r("0c23"),_=r("27cd"),k=r("86a4"),D=r("363f"),$=r("2947"),x=r("46aa"),O=r("2a3a"),R=r("efbd"),F=r("8eca"),z=r("4af4"),Q=r("ed08"),j=r("fba1"),U=r("06ee"),B=r("de8e"),E=r("bfbe"),G=r("86ea"),M={name:"FreightSelect",dicts:["sys_normal_disable","sys_yes_no"],components:{Treeselect:u.a,destination:B["default"],departure:E["default"],businessRemark:g["default"],logisticsType:I["default"],serviceType:v["default"],cargoPrice:S["default"],cargoType:C["default"],company:b["default"],contract:T["default"],currency:w["default"],demurrage:q["default"],departureTodestination:N["default"],freeStorage:L["default"],loading:P["default"],price:_["default"],recorder:k["default"],salesRemark:D["default"],shippingDate:$["default"],unit:x["default"],validTime:O["default"],carrier:R["default"],carrierNoSupplier:G["default"],local:F["default"],weight:z["default"]},props:["typeId","freightSelectData"],data:function(){return{showLeft:3,showRight:21,carrierCode:null,unitCode:null,queryCarrierIds:null,temCarrierList:[],locationOptions:new Set,logisticsEfficiencyNodeId:null,validPeriodTimeNodeId:null,serviceTypeList:[],businessList:[],carrierList:[],commonInfoList:[],serviceType:null,refreshTable:!0,serviceTypeId:null,logisticsTypeId:null,updateBy:null,charge:null,chargeId:null,validTime:[],company:"订舱口",types:null,edit:!1,loading:!0,ids:[],single:!0,multiple:!0,showSearch:!0,total:0,freightList:[],title:"",open:!1,queryParams:{pageNum:1,pageSize:20,typeId:this.freightSelectData.typeId,serviceTypeId:this.freightSelectData.serviceTypeId,inquiryNo:null,cargoTypeIds:[],carrierIds:[],companyIds:[],departureIds:this.freightSelectData.polId?[].concat(this.freightSelectData.polId):[],lineDepartureIds:[],transitPortIds:[],destinationIds:this.freightSelectData.destinationPortId?[].concat(this.freightSelectData.destinationPortId):[],lineDestinationIds:[],contractTypeIds:[],unitIds:[],validFrom:null,validTo:null,freightQuery:null,updateBy:null,recordFrom:null,recordTo:null},upload:{open:!1,title:"",isUploading:!1,updateSupport:!0,headers:{Authorization:"Bearer "+Object(f["a"])()},url:"/prod-api/system/freight/importData"},form:{},pattern:"2"==this.typeId||"3"==this.typeId?"/((-)?([0-9]{1,6})(.?)(d{0,2})/){0,5}$/":"5"==this.typeId||"6"==this.typeId||"7"==this.typeId?"/((-)?([0-9]{1,6})(.?)(d{0,2})/){0,1}$/":"/((-)?([0-9]{1,6})(.?)(d{0,2})/){0,4}$/",rules:{polId:[{required:!0,trigger:"blur"}],destinationPortId:[{required:"5"!=this.typeId,trigger:"blur"}],carrierCode:[{required:!0,trigger:"blur"}],supplierId:[{required:!0,trigger:"blur"}],unitCode:[{required:!0,trigger:"blur"}],formValue:[{required:!0,pattern:this.pattern,trigger:"blur"}]},quotation:{},select:{}}},computed:{columns:{get:function(){return"1"==this.typeId?(this.logisticsTypeId=1,this.roleId=200,this.ttUnitCode="Day",this.chargeId=1,this.unitId=2,this.unitCode="Ctnr",this.serviceType="整柜海运",this.charge="海运费",this.types="seafreight",this.$store.state.listSettings.seafreightSetting2):"2"==this.typeId?(this.logisticsTypeId=10,this.roleId=200,this.ttUnitCode="Day",this.chargeId=7,this.unitId=5,this.unitCode="KGS",this.serviceType="空运",this.charge="空运费",this.types="airfreight",this.$store.state.listSettings.airfreightSetting):"3"==this.typeId?(this.logisticsTypeId=20,this.roleId=200,this.ttUnitCode="Day",this.chargeId=52,this.unitId=5,this.unitCode="KGS",this.serviceType="铁路",this.charge="铁路费",this.company="铁路公司",this.types="Railway",this.$store.state.listSettings.expressdeliverySetting):"4"==this.typeId?(this.logisticsTypeId=40,this.roleId=200,this.ttUnitCode="Day",this.chargeId=43,this.unitId=5,this.unitCode="KGS",this.serviceType="快递",this.charge="快递费",this.company="快递公司",this.types="expressdelivery",this.$store.state.listSettings.expressdeliverySetting):"5"==this.typeId?(this.logisticsTypeId=50,this.roleId=203,this.serviceTypeId=21,this.chargeId=8,this.unitId=2,this.unitCode="Ctnr",this.serviceType="整柜拖车",this.charge="拖车费",this.company="拖车行",this.types="trailer",this.$store.state.listSettings.trailerSetting):"6"==this.typeId?(this.logisticsTypeId=60,this.roleId=205,this.serviceTypeId=60,this.chargeId=42,this.unitId=7,this.unitCode="BL",this.serviceType="报关",this.charge="报关费",this.company="报关行",this.types="declare",this.$store.state.listSettings.declareSetting):"7"==this.typeId?(this.logisticsTypeId=90,this.chargeId=44,this.roleId=206,this.serviceType="清关",this.charge="清关费",this.company="报关行",this.types="benchmark",this.$store.state.listSettings.declareSetting):"8"==this.typeId?(this.logisticsTypeId=100,this.chargeId=45,this.roleId=207,this.serviceType="仓储",this.charge="仓储费",this.company="仓储公司",this.types="custom",this.$store.state.listSettings.warehouseSetting):"9"==this.typeId?(this.logisticsTypeId=80,this.chargeId=46,this.roleId=204,this.serviceType="拓展服务",this.charge="拓展服务费",this.company="第三方公司",this.types="warehouse",this.$store.state.listSettings.warehouseSetting):void 0}}},watch:{showSearch:function(e){1==e?(this.showRight=21,this.showLeft=3):(this.showRight=24,this.showLeft=0)},"form.logisticsTypeId":function(e){this.loadCarrier();var t=[];if(void 0!=this.carrierList){var r,i=Object(l["a"])(this.carrierList);try{for(i.s();!(r=i.n()).done;){var a=r.value;if(a.serviceTypeId==e&&t.push(a),void 0!=a.children&&a.children.length>0){var s,n=Object(l["a"])(a.children);try{for(n.s();!(s=n.n()).done;){var o=s.value;o.serviceTypeId==e&&t.push(o)}}catch(m){n.e(m)}finally{n.f()}}}}catch(m){i.e(m)}finally{i.f()}}this.temCarrierList=t;var c,d=Object(l["a"])(this.carrierList);try{for(d.s();!(c=d.n()).done;){var u=c.value;if(null!=u.carrier&&null!=u.carrier.carrierId&&void 0!=u.carrier.carrierId&&null!=this.carrierId&&void 0!=this.carrierId&&u.carrier.carrierId==this.carrierId&&-1==this.carrierId&&(this.carrierId=u.serviceTypeId),void 0!=u.children&&u.children.length>0){var h,p=Object(l["a"])(u.children);try{for(p.s();!(h=p.n()).done;){var f=h.value;null!=f.carrier&&null!=f.carrier.carrierId&&void 0!=f.carrier.carrierId&&null!=this.carrierId&&void 0!=this.carrierId&&f.carrier.carrierId==this.carrierId&&-1==this.carrierId&&(this.carrierId=f.serviceTypeId)}}catch(m){p.e(m)}finally{p.f()}}}}catch(m){d.e(m)}finally{d.f()}}},created:function(){var e=this;this.getList().then((function(){e.loadCarrier(),e.loadCommonInfo()}))},methods:{changeTime:function(e){void 0==e&&(this.form.validFrom=null,this.form.validTo=null),this.form.validFrom=e[0],this.form.validTo=e[1]},remoteSupplier:function(e){var t=this,r={pageNum:1,pageSize:99999,roleSupplier:1,serviceTypeIds:[e?this.queryParams.logisticsTypeId:this.form.logisticsTypeId]};Object(y["f"])(r).then((function(e){t.$nextTick((function(){t.$refs.supplier.getLoadOptions(e.rows)}))}))},cancel:function(){this.open=!1,this.edit=!1,this.reset()},reset:function(){this.form={freightId:null,serviceTypeId:this.serviceTypeId,agreementNo:null,agreementCode:null,carrierId:null,carrierCode:null,supplierId:null,logisticsTypeId:this.logisticsTypeId,importExport:"1",precarriageRegionId:null,polId:null,transitPortId:null,destinationPortId:null,maxWeight:null,cargoTypeIds:[1],weightUnitId:5,cargoValue:null,cargoCurrencyCode:null,chargeId:this.chargeId,chargeTypeId:null,currencyId:"1"==this.typeId?10:1,unitId:this.unitId,unitCode:this.unitCode,priceA:null,priceB:null,priceC:null,priceD:null,priceE:null,shippingDate:null,shippingWeek:["Mon","Tue","Wed","Thur","Fri","Sat","Sun"],logisticsSchedule:null,logisticsEfficiencyNodeId:null,logisticsTimeliness:null,ttUnitCode:this.ttUnitCode,validPeriodTimeNodeId:null,freeStorage:null,demurrage:null,storageUnitCode:null,demurrageUnitCode:null,validFrom:null,validTo:null,isValid:"Y",status:"0",psaRemark:null,noticeForSales:null,requireRemark:null,createBy:null,createTime:null,updateBy:this.$store.state.user.sid,updateTime:Object(j["f"])(new Date),deleteBy:null,deleteTime:null,deleteStatus:"0",isSalesRequired:0,replyMark:0},this.validTime=[],this.carrierId=null,this.resetForm("form")},handleQuery:function(){this.queryParams.pageNum=1,this.queryParams.validFrom=this.queryParams.dateRange&&this.queryParams.dateRange[0]?this.queryParams.dateRange[0]:null,this.queryParams.validTo=this.queryParams.dateRange&&this.queryParams.dateRange[1]?this.queryParams.dateRange[1]:null,this.getList()},resetQuery:function(){this.resetForm("queryForm"),this.handleQuery()},handleSelectionChange:function(e){this.ids=e.map((function(e){return e.freightId})),this.single=1!=e.length,this.multiple=!e.length},handleAdd:function(){this.reset(),this.open=!0,this.title="添加"+this.$route.matched[1].meta.title;var e,t=Object(l["a"])(this.commonInfoList[0].children);try{for(t.s();!(e=t.n()).done;){var r=e.value;46==r.basCommonInfo.infoId&&(this.logisticsEfficiencyNodeId=r.infoTypeId,this.validPeriodTimeNodeId=r.infoTypeId)}}catch(a){t.e(a)}finally{t.f()}if(7!=this.typeId){var i=new Date;this.validTime.push(i),this.validTime.push(new Date(new Date(i.getFullYear(),i.getMonth()+1,1)-864e5))}},handleUpdate:function(e){console.log(e)},autoCompletion:function(){var e=this.form.formValue.split("/");2==e.length&&(e[2]=e[1]),this.form.formValue=e.join("/")},submitForm:function(e){var t=this;this.$refs["form"].validate((function(r){if(r){var i=t.form.formValue.split("/");i.length>1&&2!=t.typeId?(t.form.priceB=void 0!=i[0]?Number(i[0]):null,t.form.priceC=void 0!=i[1]?Number(i[1]):null,t.form.priceD=void 0!=i[2]?Number(i[2]):void 0!=i[1]?Number(i[1]):null,t.form.priceA=void 0!=i[3]?Number(i[3]):null,t.form.unitId=t.unitId,t.form.unitCode=t.unitCode):i.length>1&&2==t.typeId?(t.form.priceB=void 0!=i[0]?Number(i[0]):null,t.form.priceC=void 0!=i[1]?Number(i[1]):null,t.form.priceD=void 0!=i[2]?Number(i[2]):null,t.form.priceE=void 0!=i[3]?Number(i[3]):null,t.form.priceA=void 0!=i[4]?Number(i[4]):null,t.form.unitId=t.unitId,t.form.unitCode=t.unitCode):1==i.length&&(t.form.priceB=null,t.form.priceC=null,t.form.priceD=null,t.form.priceE=null,t.form.priceA=Number(t.form.formValue),"Ctnr"==t.form.unitCode&&(t.form.unitId=11,t.form.unitCode="20GP")),t.form.replyMark=1,null!=t.form.shippingWeek&&t.form.shippingWeek.length>0?t.form.shippingWeek=t.form.shippingWeek.toString():t.form.shippingWeek=null,"true"==e&&(t.form.freightId=null,t.form.inquiryNo=null),null!=t.form.freightId?Object(c["e"])(t.form).then((function(e){t.$modal.msgSuccess("修改成功"),t.open=!1,t.edit=!1,t.getList()})):Object(c["a"])(t.form).then((function(e){t.$modal.msgSuccess("新增成功"),t.reset(),t.getList()}))}}))},handleDelete:function(e){var t=e.freightId||this.ids,r=this;r.$modal.confirm('是否确认删除基础运费编号为"'+t+'"的数据项？').then((function(){return Object(c["b"])(t).then((function(e){r.getList(),r.$modal.msgSuccess("成功删除"+t.length+"个数据，但有"+e.data+"不能删除")}))}))},handleExport:function(){this.download("system/freight/export",Object(o["a"])({},this.queryParams),"SeaFCL_Export_".concat(Object(Q["d"])(new Date),".xlsx"))},handleImport:function(){this.upload.title="用户导入",this.upload.open=!0},importTemplate:function(){this.download("system/freight/importTemplate",{},"SeaFCL_Template_".concat(Object(Q["d"])(new Date),".xlsx"))},handleFileUploadProgress:function(e,t,r){this.upload.isUploading=!0},handleFileSuccess:function(e,t,r){this.upload.open=!1,this.upload.isUploading=!1,this.$refs.upload.clearFiles(),this.$message.info(e.msg),"全部上传成功"!=e.msg&&this.download("system/freight/failList",{},"上传失败列表.xlsx"),this.getList()},submitFileForm:function(){this.$refs.upload.submit()},queryLogisticsTypeId:function(e){void 0==e&&(this.queryParams.logisticsTypeId=21,this.serviceType="海运整箱")},queryLogisticsType:function(e){this.queryParams.logisticsTypeId=e.serviceTypeId,this.serviceType=e.serviceLocalName,this.handleQuery()},queryContractTypeIds:function(e){this.queryParams.contractTypeIds=e,this.handleQuery()},queryUnitIds:function(e){this.queryParams.unitIds=e,this.handleQuery()},queryCompanyIds:function(e){this.queryParams.companyIds=e,this.handleQuery()},queryPrecarriageRegionId:function(e){this.queryParams.precarriageRegionId=e,this.handleQuery()},queryDepartureIds:function(e){this.queryParams.departureIds=e,this.handleQuery()},queryLineDepartureIds:function(e){this.queryParams.lineDepartureIds=e,this.handleQuery()},queryTransitPortIds:function(e){this.queryParams.transitPortIds=e,this.handleQuery()},queryDestinationIds:function(e){this.queryParams.destinationIds=e,this.handleQuery()},queryLineDestinationIds:function(e){this.queryParams.lineDestinationIds=e,this.handleQuery()},queryCargoTypeIds:function(e){this.queryParams.cargoTypeIds=e,this.handleQuery()},getContractTypeId:function(e){this.form.agreementCode=e},handleSelectQueryCarrierIds:function(e){this.queryParams.carrierIds.push(e.carrier.carrierId),this.handleQuery()},handleDeselectQueryCarrierIds:function(e){this.queryParams.carrierIds=this.queryParams.carrierIds.filter((function(t){return t!=e.carrier.carrierId})),this.handleQuery()},deselectAllQueryCarrierIds:function(e){0==e.length&&(this.queryParams.carrierIds=[],this.handleQuery())},handleSelectRecordBy:function(e){this.queryParams.updateBy=e.staffId,this.handleQuery()},cleanRecordBy:function(e){void 0==e&&(this.queryParams.updateBy=null,this.handleQuery())},handleSelectCarrierId:function(e){this.form.carrierCode=e.carrier.carrierIntlCode},deselectCarrierId:function(e){void 0==e&&(this.form.carrierId=null)},getLogisticsTypeId:function(e){this.form.logisticsTypeId=e},getServiceTypeId:function(e){this.form.serviceTypeId=e},getChargeId:function(e){this.form.chargeId=e},getCompanyId:function(e){this.form.supplierId=e},getLoadingId:function(e){this.form.precarriageRegionId=e},getDepartureId:function(e){this.form.polId=e},getTransitPortId:function(e){this.form.transitPortId=e},getDestinationId:function(e){this.form.destinationPortId=e},getCargoCurrencyId:function(e){this.form.cargoCurrencyCode=e},getCargoUnitId:function(e){this.form.weightUnitId=e},getStorageUnitId:function(e){this.form.storageUnitCode=e},getDemurrageUnitId:function(e){this.form.demurrageUnitCode=e},getCurrencyId:function(e){this.form.currencyCode=e},getUnitId:function(e){this.form.unitCode=e},getLogisticsUnitId:function(e){this.form.ttUnitCode=e},getValidPeriodTimeNodeId:function(e){this.form.validPeriodTimeNodeId=e.basCommonInfo.infoId},deselectValidPeriodTimeNodeId:function(e){void 0==e&&(this.form.validPeriodTimeNodeId=null)},getCargoTypeIds:function(e){this.form.cargoTypeIds=e},refreshColumns:function(){var e=this;this.refreshTable=!1,this.$nextTick((function(){e.refreshTable=!0}))},loadBusinesses:function(){var e=this;0==this.$store.state.data.businessesList.length||this.$store.state.data.redisList.businessesList?m["a"].dispatch("getBusinessesList").then((function(){e.businessList=e.$store.state.data.businessesList})):this.businessList=this.$store.state.data.businessesList},loadCarrier:function(){var e=this;0==this.$store.state.data.serviceTypeCarriers.length||this.$store.state.data.redisList.serviceTypeCarriers?m["a"].dispatch("getServiceTypeCarriersList").then((function(){e.carrierList=e.$store.state.data.serviceTypeCarriers})):this.carrierList=this.$store.state.data.serviceTypeCarriers},loadCommonInfo:function(){var e=this;0==this.$store.state.data.logisticsTimeNodeList.length||this.$store.state.data.redisList.logisticsTimeNodeList?m["a"].dispatch("getLogisticsTimeNodeList").then((function(){e.commonInfoList=e.$store.state.data.logisticsTimeNodeList})):this.commonInfoList=this.$store.state.data.logisticsTimeNodeList},getList:function(){var e=this;return Object(n["a"])(Object(s["a"])().mark((function t(){return Object(s["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.loading=!0,t.next=3,Object(c["d"])(e.queryParams).then((function(t){e.freightList=t.rows,e.total=t.total?t.total:0,e.loading=!1}));case 3:case"end":return t.stop()}}),t)})))()},tableRowClassName:function(e){var t=e.row,r=Object(j["f"])(new Date,"{y}-{m}-{d}"),i=Object(j["f"])(t.validFrom,"{y}-{m}-{d}"),a=Object(j["f"])(t.validTo,"{y}-{m}-{d}");return i<r<a?"":a<r?"valid-row":i>r?"valid-before":""},carrierNormalizer:function(e){var t;return e.children&&!e.children.length&&delete e.children,t=!e.carrier||null==e.carrier.carrierLocalName&&null==e.carrier.carrierEnName?e.serviceLocalName+" "+e.serviceEnName+","+p.a.getFullChars(e.serviceLocalName):(null!=e.carrier.carrierIntlCode?e.carrier.carrierIntlCode:"")+" "+(null!=e.carrier.carrierLocalName?e.carrier.carrierLocalName:"")+" "+(null!=e.carrier.carrierEnName?e.carrier.carrierEnName:"")+","+p.a.getFullChars(null!=e.carrier.carrierLocalName?e.carrier.carrierLocalName:""),{id:e.serviceTypeId,label:t,children:e.children}},staffNormalizer:function(e){var t;return e.children&&!e.children.length&&delete e.children,e.staff&&(t=null==e.staff.staffFamilyLocalName&&null==e.staff.staffGivingLocalName?null!=e.role.roleLocalName?e.role.roleLocalName+","+p.a.getFullChars(e.role.roleLocalName):e.dept.deptLocalName+","+p.a.getFullChars(e.dept.deptLocalName):e.staff.staffCode+" "+e.staff.staffFamilyLocalName+e.staff.staffGivingLocalName+" "+e.staff.staffGivingEnName+","+p.a.getFullChars(e.staff.staffFamilyLocalName+e.staff.staffGivingLocalName)),e.roleId?{id:e.roleId,label:t,children:e.children,isDisabled:null==e.staffId&&void 0==e.children}:{id:e.deptId,label:t,children:e.children,isDisabled:null==e.staffId&&void 0==e.children}},nodeNormalizer:function(e){var t;return t=null==e.basCommonInfo?(null!=e.infoTypeShortName?e.infoTypeShortName:"")+" "+(null!=e.infoTypeLocalName?e.infoTypeLocalName:"")+" "+(null!=e.infoTypeEnName?e.infoTypeEnName:"")+","+p.a.getFullChars(e.infoTypeShortName+e.infoTypeLocalName):(null!=e.basCommonInfo.infoShortName?e.basCommonInfo.infoShortName:"")+" "+(null!=e.basCommonInfo.infoLocalName?e.basCommonInfo.infoLocalName:"")+" "+(null!=e.basCommonInfo.infoEnName?e.basCommonInfo.infoEnName:"")+","+p.a.getFullChars(e.basCommonInfo.infoShortName+e.basCommonInfo.infoLocalName),{id:e.infoTypeId,label:t}},dbclick:function(e,t,r){},getType:function(e){if(this.serviceTypeId=e.serviceTypeId,this.serviceTypeList){var t,r=Object(l["a"])(this.serviceTypeList);try{for(r.s();!(t=r.n()).done;){var i=t.value;if(i.serviceTypeId==e.serviceTypeId&&(this.typeId=i.typeId),i.children){var a,s=Object(l["a"])(i.children);try{for(s.s();!(a=s.n()).done;){var n=a.value;n.serviceTypeId==e.serviceTypeId&&(this.typeId=i.typeId)}}catch(o){s.e(o)}finally{s.f()}}}}catch(o){r.e(o)}finally{r.f()}}this.freightSelectData.logisticsTypeId=e.serviceTypeId,this.getFreightList()},getFreightList:function(){var e=this;this.quotation.pageSize=this.queryParams.pageSize,this.quotation.pageNum=this.queryParams.pageNum,this.quotation.typeId=this.freightSelectData.typeId,this.quotation.logisticsTypeId=this.serviceTypeId,Object(U["f"])(this.quotation).then((function(t){e.freightList=t.rows,e.total=t.total,e.loading=!1}))},confirmRequire:function(){var e=this,t={};t.serviceTypeId=this.freightSelectData.serviceTypeId,t.isSalesRequired=1,t.isReplied=0,t.requireSalesId=this.$store.state.user.sid,t.polId=this.freightSelectData.polId,t.destinationPortId=this.freightSelectData.destinationPortId,null!=this.freightSelectData.polId&&null!=this.freightSelectData.destinationPortId&&Object(c["a"])(t).then((function(t){e.$message.success(t.msg)})),null==this.freightSelectData.polId?this.$message.warning("启运港为空"):null==this.freightSelectData.destinationPortId&&this.$message.warning("目的港为空")},handleSelect:function(e){var t=this;return Object(n["a"])(Object(s["a"])().mark((function r(){return Object(s["a"])().wrap((function(r){while(1)switch(r.prev=r.next){case 0:t.$emit("returnFreight",t.freightSelectData.serviceTypeId,e,t.select);case 1:case"end":return r.stop()}}),r)})))()}}},W=M,V=r("2877"),A=Object(V["a"])(W,i,a,!1,null,null,null);t["default"]=A.exports},efc0:function(e,t,r){"use strict";r("5768")}}]);