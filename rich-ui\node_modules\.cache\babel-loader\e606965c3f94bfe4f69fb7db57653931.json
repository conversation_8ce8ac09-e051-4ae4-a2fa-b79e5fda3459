{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\inventory\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\inventory\\index.vue", "mtime": 1750818094548}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\babel.config.js", "mtime": 1684722674797}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_inventory", "require", "_", "_warehouseReceipt", "_interopRequireDefault", "_warehouseReceiptNew", "_preview", "_PrintTemplate", "_currency", "_log", "_moment", "_store", "_rich", "_index", "_rctFieldLabelMap", "_rsInventoryFieldLabelMap", "_rct", "_auth", "hiprintTemplate", "_default", "name", "data", "_this", "validateInboundSerialNo", "rule", "value", "callback", "console", "log", "cargoDetailOpen", "cargoDetailRow", "cargoDetailsId", "inboundSerialNo", "inboundSerialSplit", "clientCode", "shippingMark", "itemName", "boxCount", "boxItemCount", "subtotalItemCount", "expressDate", "moment", "format", "additionalFee", "packageType", "unitGrossWeight", "unitLength", "unitWidth", "unitHeight", "unitVolume", "damageStatus", "barcode", "showLeft", "showRight", "loading", "ids", "aggregatorRctList", "fieldLabelMap", "rsInventoryFieldLabelMap", "single", "openAggregator", "multiple", "selectedInventoryList", "selectedPkgList", "showSearch", "total", "inventoryList", "outboundList", "title", "openPkgTo", "outboundType", "open", "openPkg", "pkgList", "upload", "isUploading", "updateSupport", "headers", "Authorization", "getToken", "url", "process", "env", "VUE_APP_BASE_API", "queryParams", "pageNum", "pageSize", "inventoryStatus", "inboundDate", "outboundNo", "forwarder<PERSON><PERSON>", "rentalSettlementDate", "outboundDate", "subOrderNo", "supplier", "driverInfo", "sqdShippingMark", "cargoName", "totalBoxes", "totalGrossWeight", "totalVolume", "storageLocation1", "storageLocation2", "storageLocation3", "receivedStorageFee", "unpaidUnloadingFee", "logisticsAdvanceFee", "rentalBalanceFee", "freeStackPeriod", "overdueRentalUnitPrice", "overdueRentalFee", "notes", "isTopLevel", "form", "pkgDetailsList", "rules", "validator", "Error", "prefix", "concat", "startsWith", "trigger", "selectedClient", "components", "DataAggregatorBackGround", "printPreview", "PrintTemplate", "watch", "n", "mounted", "initPrint", "created", "getList", "methods", "submitFileForm", "$refs", "submit", "handleFileUploadProgress", "event", "file", "fileList", "handleFileSuccess", "response", "clearFiles", "$message", "info", "msg", "download", "loadPkgDetail", "_this2", "listInventory", "packageTo", "inventoryId", "then", "rows", "loadChildInventory", "tree", "treeNode", "resolve", "_this3", "children", "includes", "setTimeout", "for<PERSON>ach", "child", "push", "inventoryTable", "toggleRowSelection", "handleAddPkg", "reset", "handlePackingTo", "length", "warning", "firstClientCode", "isSameClient", "every", "item", "isPackaged", "packageIntoNo", "clientName", "repackingStatus", "now", "Date", "actualInboundTime", "packingSourceIds", "pkgCancel", "_this4", "cancelPkg", "success", "loadPkgToList", "_this5", "getPackage", "packingTo", "_this6", "packUp", "parseTime", "handleBlur", "inboundSerialNoSub", "padStart", "selectInboundFee", "recordType", "inboundFee", "standardInboundFee", "preciseInboundFee", "expressInboundFee", "cmToCbm", "cmVolume", "currency", "divide", "formatNumber", "symbol", "precision", "formatInput", "e", "rawValue", "replace", "isNaN", "amount", "parseFloat", "loadCargoDetail", "_this7", "rsCargoDetailsList", "map", "updateFormatter", "$forceUpdate", "row", "singlePieceWeightFormatter", "singlePieceWeight", "unitLengthFormatter", "unitHeightFormatter", "singlePieceVolumeFormatter", "singlePieceVolume", "unitGrossWeightFormatter", "unitVolumeFormatter", "unitWidthFormatter", "boxItemCountFormatter", "Number", "subtotalItemCountFormatter", "parseInput", "type", "_typeof2", "default", "singlePieceWeightFormatterValue", "String", "unitLengthValue", "unitWidthValue", "unitHeightFormatterValue", "singlePieceVolumeFormatterValue", "unitGrossWeightFormatterValue", "unitVolumeFormatterValue", "countCargomeasure", "_this8", "cm", "multiply", "selectWarehouseClient", "overdueRent", "includesUnloadingFee", "includesPackingFee", "unpaidPackingFee", "includesInboundFee", "immediatePaymentFee", "getSummariesInventory", "param", "_this9", "columns", "sums", "statisticalField", "column", "index", "property", "values", "filter", "reduce", "prev", "curr", "formatNumberFixed", "getSummaries", "_this10", "toFixed", "handleOpOutbound", "openOutbound", "<PERSON><PERSON><PERSON>", "init", "providers", "defaultElementTypeProvider", "printInboundBill", "inboundAddr", "consignee<PERSON><PERSON>", "expressNo", "template", "warehouseReceipt", "warehouseReceiptNew", "preView", "print", "deleteCargoDetail", "handleUpdateCargoDetail", "handlePkgSelectionChange", "selection", "pkgFinish", "_this11", "updateInventory", "res", "addCargoDetailRow", "addCargoDetail", "cloneDeep", "listAggregatorRsInventory", "params", "config", "JSON", "stringify", "_this12", "permissionLevel", "$store", "state", "user", "permissionLevelList", "C", "packageRecord", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "cancel", "inboundSerialNoPre", "warehouseCode", "inboundType", "cargoNature", "createdAt", "preOutboundFlag", "outboundRequestFlag", "sqdPlannedOutboundDate", "confirmInboundRequestFlag", "confirmOutboundRequestFlag", "sqdInboundHandler", "partialOutboundFlag", "outboundRecordId", "actualOutboundTime", "cargoDetailRows", "resetForm", "handleQuery", "reset<PERSON><PERSON>y", "handleStatusChange", "_this13", "text", "status", "$modal", "confirm", "changeStatus", "msgSuccess", "catch", "handleSelectionChange", "_this14", "treeData", "store", "states", "previousIds", "_toConsumableArray2", "newlySelected", "id", "newlyDeselected", "parentNode", "find", "node", "childrenLoaded", "toggleRowExpansion", "parentId", "childIndex", "indexOf", "splice", "itemIndex", "findIndex", "handleOpenAggregator", "handleAdd", "_this15", "split", "$nextTick", "handleUpdate", "_this16", "getInventory", "_objectSpread2", "submitForm", "_this17", "validate", "valid", "msgError", "add", "mark", "join", "addInventory", "handleDelete", "_this18", "inventoryIds", "delInventory", "handleImport", "handleExport", "getTime", "exports"], "sources": ["src/views/system/inventory/index.vue"], "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-row :gutter=\"20\">\n      <el-col :span=\"showLeft\">\n        <el-form v-show=\"showSearch\" ref=\"queryForm\" :inline=\"true\" :model=\"queryParams\" class=\"query\"\n                 size=\"mini\"\n        >\n          <el-form-item label=\"流水\" prop=\"inboundSerialNo\">\n            <el-input\n              v-model=\"queryParams.inboundSerialNo\"\n              clearable\n              placeholder=\"入仓流水号\"\n              @keyup.enter.native=\"handleQuery\"\n            />\n          </el-form-item>\n\n          <el-form-item label=\"快递\" prop=\"inboundSerialNo\">\n            <el-input\n              v-model=\"queryParams.driverInfo\"\n              clearable\n              placeholder=\"入仓快递单号\"\n              @keyup.enter.native=\"handleQuery\"\n            />\n          </el-form-item>\n          <el-form-item label=\"打包\">\n            <el-select v-model=\"queryParams.packageRecord\" clearable @change=\"handleQuery\">\n              <el-option label=\"打包箱\" value=\"1\"/>\n              <el-option label=\"-\" value=\"0\"/>\n            </el-select>\n          </el-form-item>\n\n          <el-form-item label=\"日期\" prop=\"inboundDate\">\n            <el-date-picker v-model=\"queryParams.inboundDate\"\n                            clearable\n                            placeholder=\"入仓日期\"\n                            type=\"date\"\n                            style=\"width: 100%\"\n                            value-format=\"yyyy-MM-dd\"\n            >\n            </el-date-picker>\n          </el-form-item>\n          <el-form-item label=\"单号\" prop=\"forwarderNo\">\n            <el-input\n              v-model=\"queryParams.forwarderNo\"\n              clearable\n              placeholder=\"货代单号\"\n              @keyup.enter.native=\"handleQuery\"\n            />\n          </el-form-item>\n          <el-form-item label=\"唛头\" prop=\"forwarderNo\">\n            <el-input\n              v-model=\"queryParams.sqdShippingMark\"\n              clearable\n              placeholder=\"唛头\"\n              @keyup.enter.native=\"handleQuery\"\n            />\n          </el-form-item>\n          <el-form-item label=\"结算\" prop=\"rentalSettlementDate\">\n            <el-date-picker v-model=\"queryParams.rentalSettlementDate\"\n                            clearable\n                            placeholder=\"仓租结算至\"\n                            style=\"width: 100%\" type=\"date\"\n                            value-format=\"yyyy-MM-dd\"\n            >\n            </el-date-picker>\n          </el-form-item>\n          <el-form-item label=\"代码\" prop=\"clientCode\">\n            <el-input\n              v-model=\"queryParams.clientCode\"\n              clearable\n              placeholder=\"客户代码\"\n              @keyup.enter.native=\"handleQuery\"\n            />\n          </el-form-item>\n          <el-form-item label=\"分单\" prop=\"subOrderNo\">\n            <el-input\n              v-model=\"queryParams.subOrderNo\"\n              clearable\n              placeholder=\"分单号\"\n              @keyup.enter.native=\"handleQuery\"\n            />\n          </el-form-item>\n          <el-form-item label=\"供货\" prop=\"supplier\">\n            <el-input\n              v-model=\"queryParams.supplier\"\n              clearable\n              placeholder=\"供货商\"\n              @keyup.enter.native=\"handleQuery\"\n            />\n          </el-form-item>\n          <el-form-item label=\"货名\" prop=\"cargoName\">\n            <el-input\n              v-model=\"queryParams.cargoName\"\n              clearable\n              placeholder=\"总货名\"\n              @keyup.enter.native=\"handleQuery\"\n            />\n          </el-form-item>\n          <el-form-item>\n            <el-button icon=\"el-icon-search\" size=\"mini\" type=\"primary\" @click=\"handleQuery\">搜索</el-button>\n            <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\n          </el-form-item>\n        </el-form>\n      </el-col>\n      <el-col :span=\"showRight\">\n        <el-row :gutter=\"10\" class=\"mb8\">\n          <el-col :span=\"1.5\">\n            <el-button\n              v-hasPermi=\"['system:inventory:add']\"\n              icon=\"el-icon-plus\"\n              plain\n              size=\"mini\"\n              type=\"primary\"\n              @click=\"handleAdd\"\n            >入仓\n            </el-button>\n          </el-col>\n          <el-col :span=\"1.5\">\n            <el-button\n              v-hasPermi=\"['system:inventory:add']\"\n              icon=\"el-icon-plus\"\n              plain\n              size=\"mini\"\n              type=\"primary\"\n              @click=\"handleAddPkg\"\n            >添加打包箱\n            </el-button>\n          </el-col>\n          <el-col :span=\"1.5\">\n            <el-button\n              v-hasPermi=\"['system:inventory:add']\"\n              :disabled=\"ids.length === 0\"\n              icon=\"el-icon-plus\"\n              plain\n              size=\"mini\"\n              type=\"primary\"\n              @click=\"handlePackingTo\"\n            >打包至\n            </el-button>\n          </el-col>\n          <el-col :span=\"1.5\">\n            <el-button @click=\"handleOpenAggregator\">数据汇总</el-button>\n            <el-dialog v-dialogDrag v-dialogDragWidth\n                       :visible.sync=\"openAggregator\" append-to-body width=\"80%\"\n            >\n              <!--<data-aggregator :data-source=\"aggregatorRctList\" :field-label-map=\"fieldLabelMap\"/>-->\n              <data-aggregator-back-ground :aggregate-function=\"listAggregatorRsInventory\"\n                                           :config-type=\"'warehouse-agg'\"\n                                           :data-source=\"aggregatorRctList\"\n                                           :data-source-type=\"'warehouse'\"\n                                           :field-label-map=\"fieldLabelMap\"\n              />\n            </el-dialog>\n          </el-col>\n          <el-col :span=\"1.5\">\n            <el-button\n              v-hasPermi=\"['system:inventory:import']\"\n              icon=\"el-icon-upload2\"\n              plain\n              size=\"mini\"\n              type=\"info\"\n              @click=\"handleImport\"\n            >导入\n            </el-button>\n          </el-col>\n          <el-col :span=\"1.5\">\n            <el-button\n              v-hasPermi=\"['system:inventory:export']\"\n              icon=\"el-icon-download\"\n              plain\n              size=\"mini\"\n              type=\"warning\"\n              @click=\"handleExport\"\n            >导出\n            </el-button>\n          </el-col>\n          <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\n        </el-row>\n\n        <el-table v-loading=\"loading\" :data=\"inventoryList\" @selection-change=\"handleSelectionChange\"\n                  :summary-method=\"getSummariesInventory\" @row-dblclick=\"handleUpdate\"\n                  show-summary\n                  ref=\"inventoryTable\"\n                  :load=\"loadChildInventory\"\n                  :tree-props=\"{children: 'children', hasChildren: 'hasChildren'}\"\n                  lazy\n                  row-key=\"inventoryId\"\n                  style=\"width: 100%;\"\n        >\n          <el-table-column align=\"center\" type=\"selection\" width=\"28\"/>\n          <el-table-column align=\"center\" label=\"入仓流水号\" prop=\"inboundSerialNo\" width=\"120\"/>\n          <el-table-column align=\"center\" label=\"入仓日期\" prop=\"actualInboundTime\" width=\"80\">\n            <template #default=\"scope\">\n              <span>{{ parseTime(scope.row.actualInboundTime, \"{y}-{m}-{d}\") }}</span>\n            </template>\n          </el-table-column>\n          <el-table-column align=\"center\" label=\"库存标志\" prop=\"inventoryStatus\" width=\"60\">\n            <template #default=\"scope\">\n              <span>{{\n                  scope.row.inventoryStatus == 0 ? \"在库\" : scope.row.inventoryStatus == 1 ? \"出库\" : \"被打包\"\n                }}</span>\n            </template>\n          </el-table-column>\n          <el-table-column align=\"center\" label=\"被打包至\" prop=\"packageIntoNo\" show-overflow-tooltip width=\"100\"/>\n          <!--<el-table-column align=\"center\" label=\"打包标志\" prop=\"repackedInto\" show-overflow-tooltip width=\"100\">\n            <template #default=\"scope\">\n              <span>{{\n                  scope.row.packageRecord == 0 ? \"-\" : \"打包箱\"\n                }}</span>\n            </template>\n          </el-table-column>-->\n          <el-table-column align=\"center\" label=\"打包标志\" prop=\"repackingStatus\" show-overflow-tooltip width=\"100\"/>\n          <!--<el-table-column align=\"center\" label=\"货代单号\" prop=\"forwarderNo\" show-overflow-tooltip/>-->\n          <el-table-column align=\"center\" label=\"仓租结算至\" prop=\"rentalSettlementDate\" width=\"80\">\n            <template #default=\"scope\">\n              <span>{{ parseTime(scope.row.rentalSettlementDate, \"{y}-{m}-{d}\") }}</span>\n            </template>\n          </el-table-column>\n          <el-table-column align=\"center\" label=\"客户代码\" prop=\"clientCode\" show-overflow-tooltip width=\"60\"/>\n          <el-table-column align=\"center\" label=\"分单号\" prop=\"subOrderNo\" show-overflow-tooltip width=\"200\"/>\n          <el-table-column align=\"center\" label=\"收货人名称\" prop=\"consigneeName\" show-overflow-tooltip width=\"100\"/>\n          <el-table-column align=\"center\" label=\"收货人电话\" prop=\"consigneeTel\" show-overflow-tooltip width=\"100\"/>\n          <el-table-column align=\"center\" label=\"供货商\" prop=\"supplier\" show-overflow-tooltip width=\"60\"/>\n          <el-table-column align=\"center\" label=\"司机信息\" prop=\"driverInfo\" show-overflow-tooltip width=\"120\"/>\n          <el-table-column align=\"center\" label=\"唛头\" prop=\"sqdShippingMark\" show-overflow-tooltip width=\"80\"/>\n          <el-table-column align=\"center\" label=\"总货名\" prop=\"cargoName\" show-overflow-tooltip/>\n          <el-table-column align=\"center\" label=\"总箱数\" prop=\"totalBoxes\"/>\n          <!--<el-table-column align=\"center\" label=\"包装类型\" prop=\"packageType\"/>-->\n          <el-table-column align=\"center\" label=\"总毛重\" prop=\"totalGrossWeight\"/>\n          <el-table-column align=\"center\" label=\"总体积\" prop=\"totalVolume\"/>\n          <el-table-column align=\"center\" label=\"已收供应商\" prop=\"receivedSupplier\"/>\n          <el-table-column align=\"center\" label=\"入仓费标准\" prop=\"inboundFee\"/>\n          <el-table-column align=\"center\" label=\"已收入仓费\" prop=\"receivedStorageFee\"/>\n          <el-table-column align=\"center\" label=\"未收卸货费\" prop=\"unpaidUnloadingFee\"/>\n          <el-table-column align=\"center\" label=\"实付卸货费\" prop=\"receivedUnloadingFee\"/>\n          <el-table-column align=\"center\" label=\"未收打包费\" prop=\"unpaidPackingFee\"/>\n          <el-table-column align=\"center\" label=\"实付打包费\" prop=\"receivedPackingFee\"/>\n          <el-table-column align=\"center\" label=\"物流代垫费\" prop=\"logisticsAdvanceFee\"/>\n          <el-table-column align=\"center\" label=\"免堆期\" prop=\"freeStackPeriod\"/>\n          <el-table-column align=\"center\" label=\"超期租金单价\" prop=\"overdueRentalUnitPrice\"/>\n          <el-table-column align=\"center\" label=\"超期租金\" prop=\"overdueRentalFee\"/>\n          <el-table-column align=\"center\" label=\"备注\" prop=\"notes\" show-overflow-tooltip/>\n          <el-table-column align=\"center\" class-name=\"small-padding fixed-width\" label=\"操作\">\n            <template #default=\"scope\">\n              <el-button\n                v-hasPermi=\"['system:inventory:edit']\"\n                icon=\"el-icon-edit\"\n                size=\"mini\"\n                style=\"margin-right: -8px\"\n                type=\"success\"\n                @click=\"handleUpdate(scope.row)\"\n              >修改\n              </el-button>\n              <el-button\n                v-hasPermi=\"['system:inventory:remove']\"\n                icon=\"el-icon-delete\"\n                size=\"mini\"\n                style=\"margin-right: -8px\"\n                type=\"danger\"\n                @click=\"handleDelete(scope.row)\"\n              >删除\n              </el-button>\n            </template>\n          </el-table-column>\n        </el-table>\n        <pagination\n          v-show=\"total>0\"\n          :limit.sync=\"queryParams.pageSize\"\n          :page.sync=\"queryParams.pageNum\"\n          :total=\"total\"\n          @pagination=\"getList\"\n        />\n      </el-col>\n    </el-row>\n    <!-- 添加或修改库存对话框 -->\n    <el-dialog\n      v-dialogDrag\n      v-dialogDragWidth :close-on-click-modal=\"false\" :title=\"title\"\n      :visible.sync=\"open\"\n      append-to-body width=\"70%\"\n      @open=\"loadCargoDetail\"\n    >\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" class=\"edit\" label-width=\"80px\">\n        <el-row :gutter=\"10\">\n          <el-col :span=\"5\">\n            <el-form-item label=\"流水号\" prop=\"inboundSerialNo\">\n              <el-input v-model=\"form.inboundSerialNo\" class=\"disable-form\" disabled placeholder=\"入仓流水号\"/>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"5\">\n            <el-form-item label=\"客户代码\" prop=\"clientCode\">\n              <tree-select :flat=\"false\" :multiple=\"false\" :pass=\"form.clientCode\"\n                           :placeholder=\"'客户代码'\" :type=\"'warehouseClient'\"\n                           @returnData=\"selectWarehouseClient($event)\"\n              />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"5\">\n            <el-form-item label=\"客户名称\" prop=\"clientCode\">\n              <el-input v-model=\"form.clientName\" class=\"disable-form\" disabled placeholder=\"客户名\"/>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"4\">\n            <el-form-item label=\"入仓时间\" prop=\"inboundDate\">\n              <el-date-picker v-model=\"form.actualInboundTime\"\n                              :default-value=\"new Date()\"\n                              clearable\n                              placeholder=\"实际入仓日期\"\n                              style=\"width: 100%;\"\n                              type=\"datetime\"\n                              value-format=\"yyyy-MM-dd HH:mm:ss\"\n              >\n              </el-date-picker>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"5\">\n            <el-form-item label=\"入仓费\" prop=\"forwarderNo\">\n              <el-row>\n                <el-col :span=\"12\">\n                  <el-input v-model=\"form.inboundFee\" class=\"disable-form number\" disabled placeholder=\"入仓费标准\"/>\n                </el-col>\n                <el-col :span=\"12\">\n                  <el-input v-model=\"form.receivedStorageFee\" class=\"number\" placeholder=\"已收入仓费\"/>\n                </el-col>\n              </el-row>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row :gutter=\"10\">\n          <el-col :span=\"10\">\n            <el-form-item label=\"分单号\" prop=\"subOrderNo\">\n              <el-input v-model=\"form.subOrderNo\" placeholder=\"分单号(最多只能输入八个字符)\"/>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"5\">\n            <el-form-item label=\"收货人名称\" prop=\"consigneeName\">\n              <el-input v-model=\"form.consigneeName\" placeholder=\"收货人名称\"/>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"4\">\n            <el-form-item label=\"收货人电话\" prop=\"consigneeTel\">\n              <el-input v-model=\"form.consigneeTel\" placeholder=\"收货人电话\"/>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"5\">\n            <el-form-item label=\"卸货费\" prop=\"supplier\">\n              <el-row>\n                <el-col :span=\"12\">\n                  <el-input v-model=\"form.unpaidUnloadingFee\"\n                            class=\"number\" placeholder=\"未收卸货费\"\n                  />\n                </el-col>\n                <el-col :span=\"12\">\n                  <el-input v-model=\"form.receivedUnloadingFee\" class=\"number\" placeholder=\"实付卸货费\"/>\n                </el-col>\n              </el-row>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row :gutter=\"10\">\n          <el-col :span=\"10\">\n            <el-form-item label=\"货物描述\" prop=\"cargoName\">\n              <el-input v-model=\"form.cargoName\" placeholder=\"总货名\"/>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"5\">\n            <el-form-item label=\"货物性质\" prop=\"cargoNature\">\n              <el-select v-model=\"form.cargoNature\" placeholder=\"请选择货物性质\" style=\"width: 100%\">\n                <el-option label=\"普货\" value=\"普货\"></el-option>\n                <el-option label=\"大件\" value=\"大件\"></el-option>\n                <el-option label=\"鲜活\" value=\"鲜活\"></el-option>\n                <el-option label=\"危品\" value=\"危品\"></el-option>\n                <el-option label=\"冷冻\" value=\"冷冻\"></el-option>\n                <el-option label=\"标记\" value=\"标记\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"4\">\n            <el-form-item label=\"记录方式\" prop=\"recordType\">\n              <el-select v-model=\"form.recordType\" placeholder=\"请选择记录方式\" @change=\"selectInboundFee\">\n                <el-option label=\"标准\" value=\"标准\"></el-option>\n                <el-option label=\"精确\" value=\"精确\"></el-option>\n                <el-option label=\"快递\" value=\"快递\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"5\">\n            <el-form-item label=\"打包费\">\n              <el-row>\n                <el-col :span=\"12\">\n                  <el-input v-model=\"form.unpaidPackingFee\" :class=\"form.includesPackingFee==1?'disable-form':''\"\n                            :disabled=\"form.includesPackingFee==1\" class=\"number\" placeholder=\"未收打包费\"\n                  />\n                </el-col>\n                <el-col :span=\"12\">\n                  <el-input v-model=\"form.receivedPackingFee\" class=\"number\" placeholder=\"实付打包费\"/>\n                </el-col>\n              </el-row>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row :gutter=\"10\">\n          <el-col :span=\"10\">\n            <el-form-item label=\"送货人信息\" prop=\"driverInfo\">\n              <el-input v-model=\"form.driverInfo\" placeholder=\"内容\"/>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"5\">\n            <el-form-item label=\"入仓方式\" prop=\"inboundType\">\n              <el-select v-model=\"form.inboundType\" placeholder=\"请选择包装类型\" style=\"width: 100%\">\n                <el-option label=\"入仓\" value=\"入仓\"></el-option>\n                <el-option label=\"外置\" value=\"外置\"></el-option>\n                <el-option label=\"对装\" value=\"对装\"></el-option>\n                <el-option label=\"自提\" value=\"自提\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"4\">\n            <el-form-item label=\"库存标志\" prop=\"inventoryStatus\">\n              <el-select v-model=\"form.inventoryStatus\" class=\"disable-form\" disabled placeholder=\"请选择库存标志\">\n                <el-option label=\"在仓\" value=\"0\"></el-option>\n                <el-option label=\"出库\" value=\"1\"></el-option>\n                <el-option label=\"被打包\" value=\"-1\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"5\">\n            <el-form-item label=\"物流代垫费\" prop=\"logisticsAdvanceFee\">\n              <el-input v-model=\"form.logisticsAdvanceFee\" class=\"number\" placeholder=\"物流代垫费\"/>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row :gutter=\"10\">\n          <el-col :span=\"10\">\n            <el-form-item label=\"入仓备注\" prop=\"notes\">\n              <el-input v-model=\"form.notes\" :autosize=\"{ minRows: 1}\" maxlength=\"150\" placeholder=\"内容\"\n                        show-word-limit\n                        type=\"textarea\"\n              />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"5\">\n            <el-form-item label=\"被打包至\">\n              <el-input v-model=\"form.packageIntoNo\" class=\"disable-form\" disabled\n              />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"4\">\n            <el-form-item class=\"disable-form\" disabled label=\"父级流水号\">\n              <el-input v-model=\"form.repackedInto\" class=\"disable-form\" disabled\n              />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"5\">\n            <el-form-item label=\"已收供应商\" prop=\"receivedSupplier\">\n              <el-input v-model=\"form.receivedSupplier\"\n                        :class=\"form.includesUnloadingFee==1?'disable-form':''\"\n                        :disabled=\"form.includesUnloadingFee==1\" placeholder=\"已收供应商总额\"\n              />\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col>\n            货物明细：<span style=\"color: #b7bbc2; margin: 0;\"\n          >(填写毛重/体积小计时如果单件毛重/长宽高不为0则小计不可更改,系统自动计算)</span>\n            <el-table\n              :data=\"form.rsCargoDetailsList\"\n              :summary-method=\"getSummaries\" border\n              show-summary\n              stripe style=\"width: 100%\" @row-dblclick=\"handleUpdateCargoDetail\"\n            >\n              <el-table-column align=\"center\" type=\"selection\" width=\"30\"/>\n              <el-table-column\n                align=\"center\" label=\"唛头\"\n                prop=\"shippingMark\"\n                width=\"100\"\n              >\n                <template slot-scope=\"scope\">\n                  <el-input v-model=\"scope.row.shippingMark\"></el-input>\n                </template>\n              </el-table-column>\n              <el-table-column\n                align=\"center\" label=\"货名\"\n                prop=\"itemName\"\n                width=\"100\"\n              >\n                <template slot-scope=\"scope\">\n                  <el-input v-model=\"scope.row.itemName\"></el-input>\n                </template>\n              </el-table-column>\n              <el-table-column\n                align=\"center\" label=\"英文货名\"\n                prop=\"itemName\"\n                width=\"100\"\n              >\n                <template slot-scope=\"scope\">\n                  <el-input v-model=\"scope.row.itemEnName\"></el-input>\n                </template>\n              </el-table-column>\n              <el-table-column\n                align=\"right\" class=\"no-spinner\"\n                label=\"件数\"\n                prop=\"boxCount\"\n              >\n                <template slot-scope=\"scope\">\n                  <el-row>\n                    <el-col :span=\"12\">\n                      <el-input v-model=\"scope.row.boxCount\" class=\"number\" @input=\"countCargomeasure(scope.row,'')\"/>\n                    </el-col>\n                    <el-col :span=\"12\">\n                      <el-select v-model=\"scope.row.packageType\" placeholder=\"请选择包装类型\">\n                        <el-option label=\"纸箱\" value=\"纸箱\"></el-option>\n                        <el-option label=\"木箱\" value=\"木箱\"></el-option>\n                        <el-option label=\"托盘\" value=\"托盘\"></el-option>\n                        <el-option label=\"吨袋\" value=\"吨袋\"></el-option>\n                      </el-select>\n                    </el-col>\n                  </el-row>\n                </template>\n              </el-table-column>\n              <el-table-column\n                align=\"right\" class=\"no-spinner\"\n                label=\"单箱件数\" prop=\"boxItemCount\"\n                width=\"60\"\n              >\n                <template slot-scope=\"scope\">\n                  <el-input v-model=\"scope.row.boxItemCount\" class=\"number\"\n                            @blur=\"parseInput(scope.row,'boxItemCount')\"\n                            @input=\"countCargomeasure(scope.row,'boxItemCount')\"\n                  />\n                </template>\n              </el-table-column>\n              <el-table-column\n                align=\"right\" label=\"单件毛重(KGS)\"\n                prop=\"singlePieceWeight\"\n              >\n                <template slot-scope=\"scope\">\n                  <el-input v-model=\"scope.row.singlePieceWeightFormatter\" class=\"number\"\n                            @blur=\"parseInput(scope.row,'singlePieceWeight')\"\n                            @input=\"countCargomeasure(scope.row,'singlePieceWeight')\"\n                  />\n                </template>\n              </el-table-column>\n              <el-table-column\n                align=\"right\" class=\"no-spinner\"\n                label=\"单件长(cm)\" prop=\"unitLength\"\n                width=\"70\"\n              >\n                <template slot-scope=\"scope\">\n                  <el-input v-model=\"scope.row.unitLengthFormatter\" class=\"number\"\n                            @blur=\"parseInput(scope.row,'unitLength')\"\n                            @input=\"countCargomeasure(scope.row,'unitLength')\"\n                  />\n                </template>\n              </el-table-column>\n              <el-table-column\n                align=\"right\" class=\"no-spinner\"\n                label=\"单件宽(cm)\" prop=\"unitWidth\"\n                width=\"70\"\n              >\n                <template slot-scope=\"scope\">\n                  <el-input v-model=\"scope.row.unitWidthFormatter\" @blur=\"parseInput(scope.row,'unitWidth')\"\n                            class=\"number\" @input=\"countCargomeasure(scope.row,'unitWidth')\"\n                  />\n                </template>\n              </el-table-column>\n              <el-table-column\n                align=\"right\" class=\"no-spinner\"\n                label=\"单件高(cm)\" prop=\"unitHeight\"\n                width=\"70\"\n              >\n                <template slot-scope=\"scope\">\n                  <el-input v-model=\"scope.row.unitHeightFormatter\" class=\"number\"\n                            @blur=\"parseInput(scope.row,'unitHeight')\"\n                            @input=\"countCargomeasure(scope.row,'unitHeight')\"\n                  />\n                </template>\n              </el-table-column>\n              <el-table-column\n                align=\"right\" class=\"no-spinner\"\n                label=\"单件体积(CBM)\"\n                prop=\"singlePieceVolume\"\n              >\n                <template slot-scope=\"scope\">\n                  <el-input v-model=\"scope.row.singlePieceVolumeFormatter\" class=\"number\"\n                            @blur=\"parseInput(scope.row,'singlePieceVolume')\"\n                            @input=\"countCargomeasure(scope.row,'singlePieceVolume')\"\n                  />\n                </template>\n              </el-table-column>\n              <el-table-column\n                align=\"right\" class=\"no-spinner\"\n                label=\"件数小计\" prop=\"subtotalItemCount\"\n                width=\"60\"\n              >\n                <template slot-scope=\"scope\">\n                  <el-input v-model=\"scope.row.subtotalItemCount\" class=\"number\"\n                            @blur=\"parseInput(scope.row,'subtotalItemCount')\"\n                            @input=\"countCargomeasure(scope.row,'subtotalItemCount')\"\n                  />\n                </template>\n              </el-table-column>\n              <el-table-column\n                align=\"right\" class=\"no-spinner\"\n                label=\"毛重小计(KGS)\"\n                prop=\"unitGrossWeight\"\n              >\n                <template slot-scope=\"scope\">\n                  <el-input v-model=\"scope.row.unitGrossWeightFormatter\" class=\"number\"\n                            @blur=\"parseInput(scope.row,'unitGrossWeight')\"\n                            @input=\"countCargomeasure(scope.row,'unitGrossWeight')\"\n                  />\n                </template>\n              </el-table-column>\n              <el-table-column\n                align=\"right\" class=\"no-spinner\"\n                label=\"体积小计(CBM)\"\n                prop=\"unitVolume\"\n              >\n                <template slot-scope=\"scope\">\n                  <el-input v-model=\"scope.row.unitVolumeFormatter\" class=\"number\"\n                            @blur=\"parseInput(scope.row,'unitVolume')\"\n                            @input=\"countCargomeasure(scope.row,'unitVolume')\"\n                  />\n                </template>\n              </el-table-column>\n              <el-table-column\n                align=\"right\" label=\"破损标志\"\n                prop=\"damageStatus\" width=\"60\"\n              >\n                <template slot-scope=\"scope\">\n                  <el-input v-model=\"scope.row.damageStatus\"></el-input>\n                </template>\n              </el-table-column>\n              <el-table-column align=\"center\" class-name=\"small-padding fixed-width\" label=\"操作\" width=\"48px\">\n                <template slot-scope=\"scope\">\n                  <div style=\"height: 15px;padding: 0;margin: 0\">\n                    <el-button icon=\"el-icon-delete\"\n                               style=\"display: flex\" type=\"danger\"\n                               @click=\"deleteCargoDetail(scope.row)\"\n                    >删除\n                    </el-button>\n                  </div>\n                </template>\n              </el-table-column>\n            </el-table>\n          </el-col>\n          <el-button style=\"padding: 0\"\n                     type=\"text\"\n                     @click=\"addCargoDetail\"\n          >[＋]\n          </el-button>\n        </el-row>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <div style=\"float: left\">\n          <span>仓管：{{ form.sqdInboundHandler }}</span>\n        </div>\n\n        <el-button type=\"primary\" @click=\"submitForm\">保 存</el-button>\n        <el-button type=\"primary\" @click=\"printInboundBill('旧模板')\">打印旧版入仓单</el-button>\n        <el-button type=\"primary\" @click=\"printInboundBill('新模板')\">打印新版入仓单</el-button>\n        <el-button @click=\"cancel\">关 闭</el-button>\n      </div>\n    </el-dialog>\n\n    <!-- 添加或修改打包对话框 -->\n    <el-dialog\n      v-dialogDrag\n      v-dialogDragWidth :close-on-click-modal=\"false\" :title=\"title\"\n      :visible.sync=\"openPkg\"\n      append-to-body width=\"70%\"\n      @open=\"loadPkgDetail\"\n    >\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" class=\"edit\" label-width=\"80px\">\n        <el-row :gutter=\"10\">\n          <el-col :span=\"5\">\n            <el-form-item label=\"流水号\" prop=\"inboundSerialNo\">\n              <el-input v-model=\"form.inboundSerialNo\" class=\"disable-form\" disabled placeholder=\"入仓流水号\"/>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"5\">\n            <el-form-item label=\"客户代码\" prop=\"clientCode\">\n              <tree-select :flat=\"false\" :multiple=\"false\" :pass=\"form.clientCode\"\n                           :placeholder=\"'客户代码'\" :type=\"'warehouseClient'\"\n                           @returnData=\"selectWarehouseClient($event)\"\n              />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"5\">\n            <el-form-item label=\"客户名称\" prop=\"clientCode\">\n              <el-input v-model=\"form.clientName\" class=\"disable-form\" disabled placeholder=\"客户名\"/>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"5\">\n            <el-form-item label=\"分单号\" prop=\"subOrderNo\">\n              <el-input v-model=\"form.subOrderNo\" :class=\"this.form.inventoryId!==null?'disable-form':''\"\n                        :disabled=\"this.form.inventoryId!==null\" placeholder=\"分单号(最多只能输入八个字符)\"\n              />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"4\">\n            <el-form-item label=\"生成时间\" prop=\"inboundDate\">\n              <el-date-picker v-model=\"form.actualInboundTime\"\n                              :default-value=\"new Date()\"\n                              clearable\n                              placeholder=\"实际入仓日期\"\n                              style=\"width: 100%;\"\n                              type=\"datetime\"\n                              value-format=\"yyyy-MM-dd HH:mm:ss\"\n              >\n              </el-date-picker>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row :gutter=\"10\">\n          <el-col :span=\"10\">\n            <el-form-item label=\"入仓备注\" prop=\"notes\">\n              <el-input v-model=\"form.notes\" :autosize=\"{ minRows: 1}\" maxlength=\"150\" placeholder=\"内容\"\n                        show-word-limit\n                        type=\"textarea\"\n              />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"5\">\n            <el-form-item label=\"打包标志\" prop=\"inventoryStatus\">\n              <el-input v-model=\"form.repackingStatus\" class=\"disable-form\" disabled/>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"5\">\n            <el-button type=\"primary\" @click=\"pkgFinish\"\n            >{{ form.repackingStatus === \"打包完\" ? \"取消打包\" : \"打包完成\" }}\n            </el-button>\n          </el-col>\n          <el-col :span=\"4\">\n            <el-form-item label=\"库存标志\" prop=\"inventoryStatus\">\n              <el-select v-model=\"form.inventoryStatus\" class=\"disable-form\" disabled placeholder=\"请选择库存标志\">\n                <el-option label=\"在仓\" value=\"0\"></el-option>\n                <el-option label=\"出库\" value=\"1\"></el-option>\n                <el-option label=\"被打包\" value=\"-1\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row :gutter=\"10\">\n          <el-col :span=\"5\">\n            <el-form-item label=\"打包箱重量\" prop=\"receivedSupplier\">\n              <el-input v-model=\"form.totalGrossWeight\" placeholder=\"打包箱重量\"/>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"5\">\n            <el-form-item label=\"打包箱体积\" prop=\"receivedSupplier\">\n              <el-input v-model=\"form.totalVolume\" placeholder=\"打包箱体积\"/>\n            </el-form-item>\n          </el-col>\n        </el-row>\n\n        <el-row>\n          <el-col>\n            包装箱明细：\n            <el-table\n              :data=\"pkgDetailsList\"\n              :summary-method=\"getSummaries\" border\n              show-summary @selection-change=\"handlePkgSelectionChange\"\n              stripe style=\"width: 100%\" @row-dblclick=\"handleUpdateCargoDetail\"\n            >\n              <el-table-column align=\"center\" type=\"selection\" width=\"30\"/>\n              <el-table-column align=\"center\" label=\"入仓流水号\" prop=\"inboundSerialNo\" width=\"100\"/>\n              <el-table-column align=\"center\" label=\"入仓日期\" prop=\"actualInboundTime\" width=\"80\">\n                <template slot-scope=\"scope\">\n                  <span>{{ parseTime(scope.row.actualInboundTime, \"{y}-{m}-{d}\") }}</span>\n                </template>\n              </el-table-column>\n              <el-table-column align=\"center\" label=\"库存标志\" prop=\"inventoryStatus\" width=\"60\">\n                <template slot-scope=\"scope\">\n              <span>{{\n                  scope.row.inventoryStatus == 0 ? \"在库\" : scope.row.inventoryStatus == 1 ? \"出库\" : \"被打包\"\n                }}</span>\n                </template>\n              </el-table-column>\n              <el-table-column align=\"center\" label=\"被打包至\" prop=\"packageIntoNo\" show-overflow-tooltip width=\"100\"/>\n              <el-table-column align=\"center\" label=\"打包标志\" prop=\"repackedInto\" show-overflow-tooltip width=\"100\">\n                <template slot-scope=\"scope\">\n              <span>{{\n                  scope.row.packageRecord == 0 ? \"-\" : \"打包箱\"\n                }}</span>\n                </template>\n              </el-table-column>\n              <!--<el-table-column align=\"center\" label=\"货代单号\" prop=\"forwarderNo\" show-overflow-tooltip/>-->\n              <el-table-column align=\"center\" label=\"仓租结算至\" prop=\"rentalSettlementDate\" width=\"80\">\n                <template slot-scope=\"scope\">\n                  <span>{{ parseTime(scope.row.rentalSettlementDate, \"{y}-{m}-{d}\") }}</span>\n                </template>\n              </el-table-column>\n              <el-table-column align=\"center\" label=\"客户代码\" prop=\"clientCode\" show-overflow-tooltip width=\"60\"/>\n              <el-table-column align=\"center\" label=\"分单号\" prop=\"subOrderNo\" show-overflow-tooltip width=\"200\"/>\n              <el-table-column align=\"center\" label=\"收货人名称\" prop=\"consigneeName\" show-overflow-tooltip\n                               width=\"100\"\n              />\n              <el-table-column align=\"center\" label=\"收货人电话\" prop=\"consigneeTel\" show-overflow-tooltip width=\"100\"/>\n              <el-table-column align=\"center\" label=\"供货商\" prop=\"supplier\" show-overflow-tooltip width=\"60\"/>\n              <el-table-column align=\"center\" label=\"司机信息\" prop=\"driverInfo\" show-overflow-tooltip width=\"120\"/>\n              <el-table-column align=\"center\" label=\"唛头\" prop=\"sqdShippingMark\" show-overflow-tooltip width=\"80\"/>\n              <el-table-column align=\"center\" label=\"货物性质\" prop=\"cargoNature\" show-overflow-tooltip/>\n              <el-table-column align=\"center\" label=\"总货名\" prop=\"cargoName\" show-overflow-tooltip/>\n              <el-table-column align=\"center\" label=\"总箱数\" prop=\"totalBoxes\"/>\n              <!--<el-table-column align=\"center\" label=\"包装类型\" prop=\"packageType\"/>-->\n              <el-table-column align=\"center\" label=\"总毛重\" prop=\"totalGrossWeight\"/>\n              <el-table-column align=\"center\" label=\"总体积\" prop=\"totalVolume\"/>\n              <el-table-column align=\"center\" label=\"已收供应商\" prop=\"receivedSupplier\"/>\n              <el-table-column align=\"center\" label=\"入仓费标准\" prop=\"inboundFee\"/>\n              <el-table-column align=\"center\" label=\"已收入仓费\" prop=\"receivedStorageFee\"/>\n              <el-table-column align=\"center\" label=\"未收卸货费\" prop=\"unpaidUnloadingFee\"/>\n              <el-table-column align=\"center\" label=\"实付卸货费\" prop=\"receivedUnloadingFee\"/>\n              <el-table-column align=\"center\" label=\"未收打包费\" prop=\"unpaidPackingFee\"/>\n              <el-table-column align=\"center\" label=\"实付打包费\" prop=\"receivedPackingFee\"/>\n              <el-table-column align=\"center\" label=\"物流代垫费\" prop=\"logisticsAdvanceFee\"/>\n              <el-table-column align=\"center\" label=\"免堆期\" prop=\"freeStackPeriod\"/>\n              <el-table-column align=\"center\" label=\"超期租金单价\" prop=\"overdueRentalUnitPrice\"/>\n              <el-table-column align=\"center\" label=\"超期租金\" prop=\"overdueRentalFee\"/>\n              <el-table-column align=\"center\" label=\"备注\" prop=\"notes\" show-overflow-tooltip/>\n            </el-table>\n          </el-col>\n          <el-button style=\"padding: 0\"\n                     type=\"text\"\n                     @click=\"addCargoDetail\"\n          >[＋]\n          </el-button>\n        </el-row>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <div style=\"float: left\">\n          <span>仓管：{{ form.sqdInboundHandler }}</span>\n        </div>\n\n        <el-button :class=\"form.repackingStatus==='打包完'?'disable-form':''\"\n                   :disabled=\"form.repackingStatus==='打包完'\" type=\"danger\"\n                   @click=\"pkgCancel\"\n        >移出打包箱\n        </el-button>\n        <el-button type=\"primary\" @click=\"submitForm('pkg')\">保 存</el-button>\n        <el-button @click=\"openPkg=false\">关 闭</el-button>\n      </div>\n    </el-dialog>\n\n    <!-- 添加打包对话框 -->\n    <el-dialog\n      v-dialogDrag\n      v-dialogDragWidth :close-on-click-modal=\"false\" :title=\"title\"\n      :visible.sync=\"openPkgTo\"\n      append-to-body width=\"30%\"\n      @open=\"loadPkgToList\"\n    >\n\n      <el-select\n        v-model=\"form.packageTo\"\n        class=\"field-select\"\n        filterable\n        placeholder=\"请选择搜索字段\"\n      >\n        <el-option\n          v-for=\"pkg in pkgList\"\n          :key=\"pkg.inventoryId\"\n          :label=\"pkg.subOrderNo\"\n          :value=\"pkg.inventoryId\"\n        />\n      </el-select>\n\n      <el-button type=\"primary\" @click=\"packingTo\">保 存</el-button>\n    </el-dialog>\n\n    <!-- 预览 -->\n    <print-preview ref=\"preView\"/>\n\n    <!--    // excel 上传导入组件-->\n    <el-dialog\n      v-dialogDrag\n      v-dialogDragWidth :close-on-click-modal=\"false\" :modal-append-to-body=\"false\" :title=\"upload.title\"\n      :visible.sync=\"upload.open\"\n      append-to-body width=\"400px\"\n    >\n      <el-upload\n        ref=\"upload\"\n        :action=\"upload.url + '?updateSupport=' + upload.updateSupport\"\n        :auto-upload=\"false\"\n        :disabled=\"upload.isUploading\"\n        :headers=\"upload.headers\"\n        :limit=\"1\"\n        :on-progress=\"handleFileUploadProgress\"\n        :on-success=\"handleFileSuccess\"\n        accept=\".xlsx, .xls\"\n        drag\n      >\n        <i class=\"el-icon-upload\"></i>\n        <div class=\"el-upload__text\">将文件拖到此处，或<em>点击上传</em></div>\n        <div slot=\"tip\" class=\"el-upload__tip text-center\">\n          <div slot=\"tip\" class=\"el-upload__tip\">\n            <el-checkbox v-model=\"upload.updateSupport\"/>\n            是否更新已经存在的用户数据\n          </div>\n          <span>仅允许导入xls、xlsx格式文件。</span>\n        </div>\n      </el-upload>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"submitFileForm\">确 定</el-button>\n        <el-button @click=\"upload.open = false\">取 消</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport {\n  addInventory, cancelPkg,\n  changeStatus,\n  delInventory,\n  getInventory, getPackage, listAggregatorRsInventory,\n  listInventory, packUp,\n  updateInventory\n} from \"@/api/system/inventory\"\nimport {defaultElementTypeProvider, hiprint} from \"@\"\nimport warehouseReceipt from \"@/print-template/warehouseReceipt\"\nimport warehouseReceiptNew from \"@/print-template/warehouseReceiptNew\"\nimport printPreview from \"@/views/print/demo/design/preview.vue\"\nimport PrintTemplate from \"@/views/system/print/PrintTemplate.vue\"\nimport currency from \"currency.js\"\nimport log from \"@/views/monitor/job/log.vue\"\nimport moment from \"moment/moment\"\nimport store from \"@/store\"\nimport {parseTime} from \"../../../utils/rich\"\nimport DataAggregatorBackGround from \"@/views/system/DataAggregatorBackGround/index.vue\"\nimport {rctFieldLabelMap} from \"@/config/rctFieldLabelMap\"\nimport {rsInventoryFieldLabelMap} from \"@/config/rsInventoryFieldLabelMap\"\nimport {listAggregatorRct} from \"@/api/system/rct\"\nimport {getToken} from \"@/utils/auth\"\n\nlet hiprintTemplate\nexport default {\n  name: \"Inventory\",\n  data() {\n    const validateInboundSerialNo = (rule, value, callback) => {\n      console.log(value)\n      /* if (value === '') {\n        callback(new Error('请输入流水号'));\n      } else {\n        if (this.form.inboundSerialNoSub !== '') {\n          this.$refs.form.validateField('inboundSerialNo');\n        }\n        callback();\n      } */\n    }\n    return {\n      cargoDetailOpen: false,\n      cargoDetailRow: {\n        cargoDetailsId: null,\n        inboundSerialNo: null,\n        inboundSerialSplit: null,\n        clientCode: null,\n        shippingMark: null,\n        itemName: null,\n        boxCount: null,\n        boxItemCount: null,\n        subtotalItemCount: null,\n        expressDate: moment().format(\"yyyy-MM-DD\"),\n        additionalFee: null,\n        packageType: \"纸箱\",\n        unitGrossWeight: null,\n        unitLength: null,\n        unitWidth: null,\n        unitHeight: null,\n        unitVolume: null,\n        damageStatus: \"0\",\n        barcode: null\n      },\n      showLeft: 0,\n      showRight: 24,\n      // 遮罩层\n      loading: true,\n      // 选中数组\n      ids: [],\n      aggregatorRctList: [],\n      fieldLabelMap: rsInventoryFieldLabelMap,\n      // 非单个禁用\n      single: true,\n      openAggregator: false,\n      // 非多个禁用\n      multiple: true,\n      selectedInventoryList: [],\n      selectedPkgList: [],\n      // 显示搜索条件\n      showSearch: false,\n      // 总条数\n      total: 0,\n      // 库存表格数据\n      inventoryList: [],\n      outboundList: [],\n      // 弹出层标题\n      title: \"\",\n      openPkgTo: false,\n\n      outboundType: null,\n\n      // 是否显示弹出层\n      open: false,\n      openPkg: false,\n      pkgList: [],\n      // 用户导入参数\n      upload: {\n        // 是否显示弹出层（用户导入）\n        open: false,\n        // 弹出层标题（用户导入）\n        title: \"\",\n        // 是否禁用上传\n        isUploading: false,\n        // 是否更新已经存在的用户数据\n        updateSupport: true,\n        // 设置上传的请求头部\n        headers: {Authorization: \"Bearer \" + getToken()},\n        // 上传的地址\n        url: process.env.VUE_APP_BASE_API + \"/system/inventory/importData\"\n      },\n      // 查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 20,\n        inventoryStatus: null,\n        inboundSerialNo: null,\n        inboundSerialSplit: null,\n        inboundDate: null,\n        outboundNo: null,\n        forwarderNo: null,\n        rentalSettlementDate: null,\n        outboundDate: null,\n        clientCode: null,\n        subOrderNo: null,\n        supplier: null,\n        driverInfo: null,\n        sqdShippingMark: null,\n        cargoName: null,\n        totalBoxes: null,\n        packageType: null,\n        totalGrossWeight: null,\n        totalVolume: null,\n        damageStatus: null,\n        storageLocation1: null,\n        storageLocation2: null,\n        storageLocation3: null,\n        receivedStorageFee: null,\n        unpaidUnloadingFee: null,\n        logisticsAdvanceFee: null,\n        rentalBalanceFee: null,\n        freeStackPeriod: null,\n        overdueRentalUnitPrice: null,\n        overdueRentalFee: null,\n        notes: null,\n        isTopLevel: true // 默认只查询顶层数据\n      },\n      // 表单参数\n      form: {},\n      pkgDetailsList: [],\n      // 表单校验\n      rules: {\n        // cargoName: [\n        //   { required: true, message: \"请输入货物描述\", trigger: \"blur\" }\n        // ],\n        // consigneeName: [\n        //   { required: true, message: \"请输入收货人名称\", trigger: \"blur\" }\n        // ],\n        // consigneeTel: [\n        //   { required: true, message: \"请输入收货人电话\", trigger: \"blur\" }\n        // ],\n        subOrderNo: [\n          {\n            validator: (rule, value, callback) => {\n              if (!value) {\n                callback()\n                return\n              }\n              if (!this.form.clientCode) {\n                callback(new Error(\"请先选择客户代码\"))\n                return\n              }\n              const prefix = `${this.form.clientCode}-`\n              if (!value.startsWith(prefix)) {\n                callback(new Error(`分单号必须以 ${prefix} 开头`))\n                return\n              }\n              callback()\n            },\n            trigger: \"blur\"\n          }\n        ]\n      },\n      selectedClient: null\n    }\n  },\n  components: {\n    DataAggregatorBackGround,\n    printPreview,\n    PrintTemplate\n  },\n  watch: {\n    showSearch(n) {\n      if (n === true) {\n        this.showRight = 21\n        this.showLeft = 3\n      } else {\n        this.showRight = 24\n        this.showLeft = 0\n      }\n    }\n  },\n  mounted() {\n    this.initPrint()\n  },\n  created() {\n    this.getList()\n  },\n  methods: {\n    // 提交上传文件\n    submitFileForm() {\n      this.$refs.upload.submit()\n    },\n    // 文件上传中处理\n    handleFileUploadProgress(event, file, fileList) {\n      this.upload.isUploading = true\n    },\n    // 文件上传成功处理\n    handleFileSuccess(response, file, fileList) {\n      this.upload.open = false\n      this.upload.isUploading = false\n      this.$refs.upload.clearFiles()\n      this.$message.info(response.msg)\n      if (response.msg != \"全部上传成功\") {\n        this.download(\"system/inventory/failList\", {}, `上传失败列表.xlsx`)\n      }\n      this.getList()\n    },\n    loadPkgDetail() {\n      if (this.form.clientCode) {\n        listInventory({packageTo: this.form.inventoryId}).then(response => {\n          this.pkgDetailsList = response.rows\n        })\n      }\n    },\n    // 加载子节点数据\n    loadChildInventory(tree, treeNode, resolve) {\n      // 使用packageTo字段查询子节点\n      listInventory({packageTo: tree.inventoryId}).then(response => {\n        const rows = response.rows\n\n        // 先将数据传递给表格，确保子节点渲染\n        resolve(rows)\n        tree.children = rows\n\n        // 如果父项被选中，在子节点渲染完成后选中它们\n        if (this.ids.includes(tree.inventoryId)) {\n          setTimeout(() => {\n            rows.forEach(child => {\n              if (!this.ids.includes(child.inventoryId)) {\n                this.ids.push(child.inventoryId)\n                this.selectedInventoryList.push(child)\n              }\n              // 在UI上选中子项\n              this.$refs.inventoryTable.toggleRowSelection(child, true)\n            })\n          }, 50) // 等待DOM更新\n        }\n      })\n    },\n    handleAddPkg() {\n      this.reset()\n      this.openPkg = true\n    },\n    handlePackingTo() {\n      // 检查是否选择了货物\n      if (this.ids.length === 0) {\n        this.$message.warning(\"请先选择需要打包的货物\")\n        return\n      }\n\n      // 检查是否为同一客户的货物\n      const firstClientCode = this.selectedInventoryList[0].clientCode\n      const isSameClient = this.selectedInventoryList.every(item => item.clientCode === firstClientCode)\n      const isPackaged = this.selectedInventoryList.every(item => item.packageIntoNo == null)\n\n      if (!isSameClient) {\n        this.$message.warning(\"只能打包同一个客户的货物\")\n        return\n      }\n      if (!isPackaged) {\n        this.$message.warning(\"有货物已被打包\")\n        return\n      }\n\n      // 打开打包箱选择对话框\n      this.reset()\n      this.form.clientCode = firstClientCode\n      this.form.clientName = this.selectedInventoryList[0].clientName\n      this.form.repackingStatus = \"打包中\"\n\n      // 使用准确的当前时间，不进行取整\n      const now = new Date()\n      this.form.actualInboundTime = now\n\n      this.form.inventoryStatus = \"0\"\n      this.form.subOrderNo = firstClientCode + \"-\"\n\n      // 记录被打包的货物ID\n      this.form.packingSourceIds = this.ids\n\n      this.title = \"打包装箱至\"\n      this.openPkgTo = true\n    },\n    pkgCancel() {\n      cancelPkg(this.selectedPkgList).then(response => {\n        this.$message.success(\"移出成功\")\n        this.openPkg = false\n        this.getList()\n      })\n    },\n    loadPkgToList() {\n      getPackage({clientCode: this.form.clientCode, repackingStatus: \"打包中\"}).then(response => {\n        this.pkgList = response.data\n      })\n    },\n    packingTo() {\n      this.selectedInventoryList.forEach(item => {\n        item.packageTo = this.form.packageTo\n        item.repackingStatus = \"被打包\"\n      })\n\n      packUp(this.selectedInventoryList).then(response => {\n        this.$message.success(\"打包成功\")\n        this.openPkgTo = false\n        this.getList()\n      })\n    },\n    parseTime,\n    handleBlur() {\n      // 判断长度是否小于4位，若是则补齐0\n      this.form.inboundSerialNoSub = this.form.inboundSerialNoSub.padStart(4, \"0\")\n    },\n    selectInboundFee() {\n      switch (this.form.recordType) {\n        case \"标准\":\n          this.form.inboundFee = this.selectedClient.standardInboundFee\n          break\n        case \"精确\":\n          this.form.inboundFee = this.selectedClient.preciseInboundFee\n          break\n        case \"快递\":\n          this.form.inboundFee = this.selectedClient.expressInboundFee\n          break\n      }\n    },\n    cmToCbm(cmVolume) {\n      return currency(cmVolume).divide(1_000_000).value // 1 CBM = 1,000,000 cm³\n    },\n    // 格式化为带逗号和小数的字符串\n    formatNumber(value) {\n      return currency(value, {symbol: \"\", precision: 2}).format() // eg: 1234.56 => \"1,234.56\"\n    },\n    // 当用户输入时实时格式化，但保留光标位置\n    formatInput(e) {\n      const rawValue = e.replace(/,/g, \"\") // 去除逗号\n      if (!isNaN(rawValue)) {\n        this.amount = parseFloat(rawValue) // 更新原始值\n        return this.formatNumber(rawValue)\n      }\n    },\n    loadCargoDetail() {\n      this.form.rsCargoDetailsList.map(item => {\n        this.updateFormatter(item)\n      })\n\n      this.$forceUpdate()\n    },\n    updateFormatter(row) {\n      row.singlePieceWeightFormatter = this.formatNumber(row.singlePieceWeight)\n      row.unitLengthFormatter = this.formatNumber(row.unitLength)\n      row.unitHeightFormatter = this.formatNumber(row.unitHeight)\n      row.singlePieceVolumeFormatter = this.formatNumber(row.singlePieceVolume)\n      row.unitGrossWeightFormatter = this.formatNumber(row.unitGrossWeight)\n      row.unitVolumeFormatter = this.formatNumber(row.unitVolume)\n      row.unitWidthFormatter = this.formatNumber(row.unitWidth)\n      row.boxItemCountFormatter = Number(row.boxItemCount) ? Number(row.boxItemCount) : 0\n      row.subtotalItemCountFormatter = Number(row.subtotalItemCount) ? Number(row.subtotalItemCount) : 0\n    },\n    // 输入失焦时确保格式正确\n    parseInput(row, type) {\n      if (!row || typeof row !== \"object\") return // 空值校验：row 不存在或非对象直接返回\n      switch (type) {\n        case \"singlePieceWeight\":\n          const singlePieceWeightFormatterValue = String(row.singlePieceWeightFormatter || \"\").replace(/,/g, \"\") // 空值安全处理\n          if (!isNaN(singlePieceWeightFormatterValue) && singlePieceWeightFormatterValue !== \"\") {\n            row.singlePieceWeightFormatter = this.formatNumber(singlePieceWeightFormatterValue)\n          }\n          break\n        case \"unitLength\":\n          const unitLengthValue = String(row.unitLengthFormatter || \"\").replace(/,/g, \"\") // 空值安全处理\n          if (!isNaN(unitLengthValue) && unitLengthValue !== \"\") {\n            row.unitLengthFormatter = this.formatNumber(unitLengthValue)\n          }\n          break\n        case \"unitWidth\":\n          const unitWidthValue = String(row.unitWidthFormatter || \"\").replace(/,/g, \"\") // 空值安全处理\n          if (!isNaN(unitWidthValue) && unitWidthValue !== \"\") {\n            row.unitWidthFormatter = this.formatNumber(unitWidthValue)\n          }\n          break\n        case \"unitHeight\":\n          const unitHeightFormatterValue = String(row.unitHeightFormatter || \"\").replace(/,/g, \"\") // 空值安全处理\n          if (!isNaN(unitHeightFormatterValue) && unitHeightFormatterValue !== \"\") {\n            row.unitHeightFormatter = this.formatNumber(unitHeightFormatterValue)\n          }\n          break\n        case \"singlePieceVolume\":\n          const singlePieceVolumeFormatterValue = String(row.singlePieceVolumeFormatter || \"\").replace(/,/g, \"\") // 空值安全处理\n          if (!isNaN(singlePieceVolumeFormatterValue) && singlePieceVolumeFormatterValue !== \"\") {\n            row.singlePieceVolumeFormatter = this.formatNumber(singlePieceVolumeFormatterValue)\n          }\n          break\n        case \"unitGrossWeight\":\n          const unitGrossWeightFormatterValue = String(row.unitGrossWeightFormatter || \"\").replace(/,/g, \"\") // 空值安全处理\n          if (!isNaN(unitGrossWeightFormatterValue) && unitGrossWeightFormatterValue !== \"\") {\n            row.unitGrossWeightFormatter = this.formatNumber(unitGrossWeightFormatterValue)\n          }\n          break\n        case \"unitVolume\":\n          const unitVolumeFormatterValue = String(row.unitVolumeFormatter || \"\").replace(/,/g, \"\") // 空值安全处理\n          if (!isNaN(unitVolumeFormatterValue) && unitVolumeFormatterValue !== \"\") {\n            row.unitVolumeFormatter = this.formatNumber(unitVolumeFormatterValue)\n          }\n          break\n      }\n    },\n    countCargomeasure(row, type) {\n      if (!row || typeof row !== \"object\") return // 空值校验：row 不存在或非对象直接返回\n      switch (type) {\n        case \"singlePieceWeight\":\n          const singlePieceWeightFormatterValue = String(row.singlePieceWeightFormatter || \"\").replace(/,/g, \"\") // 空值安全处理\n          if (!isNaN(singlePieceWeightFormatterValue) && singlePieceWeightFormatterValue !== \"\") {\n            row.singlePieceWeight = parseFloat(singlePieceWeightFormatterValue) // 更新原始值\n          }\n          break\n        case \"unitLength\":\n          const unitLengthValue = String(row.unitLengthFormatter || \"\").replace(/,/g, \"\") // 空值安全处理\n          if (!isNaN(unitLengthValue) && unitLengthValue !== \"\") {\n            row.unitLength = parseFloat(unitLengthValue) // 更新原始值\n          }\n          break\n        case \"unitWidth\":\n          const unitWidthValue = String(row.unitWidthFormatter || \"\").replace(/,/g, \"\") // 空值安全处理\n          if (!isNaN(unitWidthValue) && unitWidthValue !== \"\") {\n            row.unitWidth = parseFloat(unitWidthValue) // 更新原始值\n          }\n          break\n        case \"unitHeight\":\n          const unitHeightFormatterValue = String(row.unitHeightFormatter || \"\").replace(/,/g, \"\") // 空值安全处理\n          if (!isNaN(unitHeightFormatterValue) && unitHeightFormatterValue !== \"\") {\n            row.unitHeight = parseFloat(unitHeightFormatterValue) // 更新原始值\n          }\n          break\n        case \"singlePieceVolume\":\n          const singlePieceVolumeFormatterValue = String(row.singlePieceVolumeFormatter || \"\").replace(/,/g, \"\") // 空值安全处理\n          if (!isNaN(singlePieceVolumeFormatterValue) && singlePieceVolumeFormatterValue !== \"\") {\n            row.singlePieceVolume = parseFloat(singlePieceVolumeFormatterValue) // 更新原始值\n          }\n          break\n        case \"unitGrossWeight\":\n          const unitGrossWeightFormatterValue = String(row.unitGrossWeightFormatter || \"\").replace(/,/g, \"\") // 空值安全处理\n          if (!isNaN(unitGrossWeightFormatterValue) && unitGrossWeightFormatterValue !== \"\") {\n            row.unitGrossWeight = parseFloat(unitGrossWeightFormatterValue) // 更新原始值\n          }\n          break\n        case \"unitVolume\":\n          const unitVolumeFormatterValue = String(row.unitVolumeFormatter || \"\").replace(/,/g, \"\") // 空值安全处理\n          if (!isNaN(unitVolumeFormatterValue) && unitVolumeFormatterValue !== \"\") {\n            row.unitVolume = parseFloat(unitVolumeFormatterValue) // 更新原始值\n          }\n          break\n      }\n\n      // 更新相关字段\n      this.form.rsCargoDetailsList = this.form.rsCargoDetailsList.map(item => {\n        if (row === item) {\n          if (item.unitLength && item.unitWidth && item.unitHeight) {\n            const cm = currency(item.unitLength).multiply(item.unitWidth).multiply(item.unitHeight).value\n            item.singlePieceVolume = this.cmToCbm(cm)\n            item.singlePieceVolumeFormatter = this.formatNumber(item.singlePieceVolume)\n          }\n          if (item.singlePieceVolume && item.boxCount) {\n            item.unitVolume = currency(item.singlePieceVolume).multiply(item.boxCount).value\n            item.unitVolumeFormatter = this.formatNumber(item.unitVolume)\n          }\n          if (item.singlePieceWeight && item.boxCount) {\n            item.unitGrossWeight = currency(item.singlePieceWeight).multiply(item.boxCount).value\n            item.unitGrossWeightFormatter = this.formatNumber(item.unitGrossWeight)\n          }\n          // 件数小计\n          if (item.boxItemCount) {\n            if (item.boxCount) {\n              item.subtotalItemCount = currency(item.boxItemCount).multiply(item.boxCount).value\n            } else {\n              item.subtotalItemCount = currency(item.boxItemCount).value\n            }\n          }\n        }\n        return item\n      })\n\n      // this.$forceUpdate()\n    },\n    selectWarehouseClient(row) {\n      this.form.clientCode = row.clientCode\n      this.form.clientName = row.clientName\n      this.form.overdueRentalUnitPrice = row.overdueRent\n      this.form.freeStackPeriod = row.freeStackPeriod\n      this.selectedClient = row\n      /* if (row.clientType === \"直客\") {\n        this.form.inboundSerialNoPre = \"No.90\"\n      } else {\n        this.form.inboundSerialNoPre = \"No.80\"\n      } */\n      if (row.includesUnloadingFee != null) {\n        this.form.includesUnloadingFee = row.includesUnloadingFee\n        /* if (row.includesUnloadingFee == 1) {\n          this.form.unpaidUnloadingFee = 0\n        } */\n      }\n      if (row.includesPackingFee != null) {\n        this.form.includesPackingFee = row.includesPackingFee\n        if (row.includesPackingFee == 1) {\n          this.form.unpaidPackingFee = 0\n        }\n      }\n      if (row.includesInboundFee != null) {\n        this.form.includesInboundFee = row.includesInboundFee\n        if (row.includesInboundFee == 1) {\n          this.form.inboundFee = 0\n        }\n      }\n      if (row.immediatePaymentFee) {\n        this.form.immediatePaymentFee = row.immediatePaymentFee\n      }\n      // 根据记录方式选择入仓标准费\n      if (this.form.recordType && row.includesInboundFee == 0) {\n        switch (this.form.recordType) {\n          case \"标准\":\n            this.form.inboundFee = row.standardInboundFee\n            break\n          case \"精确\":\n            this.form.inboundFee = row.preciseInboundFee\n            break\n          case \"快递\":\n            this.form.inboundFee = row.expressInboundFee\n            break\n        }\n      }\n\n      // 当客户代码改变时,清空分单号\n      this.form.subOrderNo = \"\"\n      this.form.subOrderNo = row.clientCode + \"-\"\n    },\n    getSummariesInventory(param) {\n      const {columns, data} = param\n      const sums = []\n      const statisticalField = [\"totalBoxes\", \"totalGrossWeight\", \"totalVolume\",\n        \"inboundFee\", \"receivedStorageFee\", \"unpaidUnloadingFee\", \"receivedUnloadingFee\",\n        \"unpaidPackingFee\", \"receivedPackingFee\", \"logisticsAdvanceFee\"]\n\n      columns.forEach((column, index) => {\n        if (index === 0) {\n          sums[index] = \"总计:\"\n          return\n        }\n\n        // 筛选出被统计的字段\n        if (statisticalField.includes(column.property)) {\n          // 将数据转为有效数字\n          const values = data\n            .map(item => Number(item[column.property]))\n            .filter(value => !isNaN(value)) // 排除非数字\n\n          if (values.length > 0) {\n            // 计算总和并格式化\n            const total = values.reduce((prev, curr) => prev + curr, 0)\n            sums[index] =\n              this.formatNumberFixed(total) +\n              (column.property === \"totalVolume\"\n                ? \" CBM\"\n                : column.property === \"totalGrossWeight\"\n                  ? \" KGS\"\n                  : column.property === \"totalBoxes\"\n                    ? \" 件\"\n                    : \"\")\n          } else {\n            sums[index] = \" \"\n          }\n        } else {\n          sums[index] = \" \"\n        }\n      })\n\n      return sums\n    },\n    getSummaries(param) {\n      const {columns, data} = param\n      const sums = []\n      const statisticalField = [\"unitVolume\", \"unitGrossWeight\", \"boxCount\"]\n\n      columns.forEach((column, index) => {\n        if (index === 0) {\n          sums[index] = \"总计:\"\n          return\n        }\n\n        // 筛选出被统计的字段\n        if (statisticalField.includes(column.property)) {\n          // 将数据转为有效数字\n          const values = data\n            .map(item => Number(item[column.property]))\n            .filter(value => !isNaN(value)) // 排除非数字\n\n          if (values.length > 0) {\n            // 计算总和并格式化\n            const total = values.reduce((prev, curr) => prev + curr, 0)\n            sums[index] =\n              this.formatNumberFixed(total) +\n              (column.property === \"unitVolume\"\n                ? \" CBM\"\n                : column.property === \"unitGrossWeight\"\n                  ? \" KGS\"\n                  : column.property === \"boxCount\"\n                    ? \" 件\"\n                    : \"\")\n          } else {\n            sums[index] = \" \"\n          }\n        } else {\n          sums[index] = \" \"\n        }\n      })\n\n      return sums\n    },\n    formatNumberFixed(value) {\n      return Number(value).toFixed(2) // 保留两位小数\n    },\n    handleOpOutbound() {\n      this.openOutbound = true\n    },\n    initPrint() {\n      hiprint.init({\n        providers: [new defaultElementTypeProvider()]\n      })\n    },\n    printInboundBill(type) {\n      let data = {}\n      data.clientCode = this.form.clientCode\n      data.subOrderNo = this.form.subOrderNo\n      data.inboundSerialNo = this.form.inboundSerialNo\n      data.forwarderNo = this.form.forwarderNo\n      data.actualInboundTime = moment(this.form.actualInboundTime).format(\"yyyy-MM-DD HH:mm:ss\")\n      data.sqdShippingMark = this.form.sqdShippingMark\n      data.cargoName = this.form.cargoName\n      data.totalBoxes = this.form.totalBoxes\n      data.totalGrossWeight = this.form.totalGrossWeight\n      data.totalVolume = this.form.totalVolume\n      data.notes = this.form.notes\n      data.supplier = this.form.supplier\n      data.driverInfo = this.form.driverInfo\n      data.inboundAddr = this.form.inboundAddr\n      data.consigneeName = this.form.consigneeName\n      data.inboundSerialNo = this.form.inboundSerialNo\n\n      data.expressNo = this.form.driverInfo\n\n      if (type === \"旧模板\") {\n        hiprintTemplate = new hiprint.PrintTemplate({template: warehouseReceipt})\n      } else {\n        hiprintTemplate = new hiprint.PrintTemplate({template: warehouseReceiptNew})\n      }\n      // 打开预览组件\n      // this.$refs.preView.show(hiprintTemplate, data)\n      // 直接打印\n      this.$refs.preView.print(hiprintTemplate, data)\n    },\n    deleteCargoDetail(row) {\n      this.form.rsCargoDetailsList = this.form.rsCargoDetailsList.filter(item => item !== row)\n      this.$forceUpdate()\n    },\n    handleUpdateCargoDetail(row) {\n      this.cargoDetailOpen = true\n      this.cargoDetailRow = row\n    },\n    handlePkgSelectionChange(selection) {\n      this.selectedPkgList = selection\n    },\n    pkgFinish() {\n      if (this.form.repackingStatus === \"打包完\") {\n        this.form.repackingStatus = \"打包中\"\n      } else {\n        this.form.repackingStatus = \"打包完\"\n      }\n\n      updateInventory(this.form).then(res => {\n        this.$message.success(\"修改成功\")\n      })\n    },\n    addCargoDetailRow(row) {\n\n    },\n    addCargoDetail() {\n      this.form.rsCargoDetailsList.push(this._.cloneDeep(this.cargoDetailRow))\n      this.cargoDetailOpen = true\n    },\n    listAggregatorRsInventory(params) {\n      params.config = JSON.stringify(params.config)\n      this.queryParams.params = params\n      return listAggregatorRsInventory(this.queryParams)\n    },\n    /** 查询库存列表 */\n    getList() {\n      this.loading = true\n      this.queryParams.permissionLevel = this.$store.state.user.permissionLevelList.C\n      // 添加条件，只查询顶层数据（没有父级的数据）\n      this.queryParams.isTopLevel = true\n      listInventory(this.queryParams).then(response => {\n        // 处理数据，标记有子节点的数据\n        let rows = response.rows\n\n        // 如果后端不支持isTopLevel参数，在前端进行过滤\n        // 仅当快递单号未填写时才过滤packageTo，保证快递查询时能显示所有匹配的记录\n        if (this.queryParams.isTopLevel && !this.queryParams.driverInfo) {\n          rows = rows.filter(item => !item.packageTo)\n        }\n\n        this.inventoryList = rows.map(item => {\n          // 如果是打包箱，标记为有子节点\n          if (item.packageRecord === \"1\") {\n            item.hasChildren = true\n          }\n          return item\n        })\n        this.total = response.total\n        this.loading = false\n      })\n    },\n    // 取消按钮\n    cancel() {\n      this.open = false\n      this.reset()\n    },\n    // 表单重置\n    reset() {\n      this.pkgDetailsList = []\n      this.form = {\n        repackingStatus: \"-\",\n        inventoryId: null,\n        inventoryStatus: \"0\",\n        inboundSerialNo: null,\n        inboundSerialNoPre: null,\n        inboundSerialSplit: null,\n        inboundDate: null,\n        outboundNo: null,\n        forwarderNo: null,\n        rentalSettlementDate: null,\n        outboundDate: null,\n        clientCode: null,\n        subOrderNo: null,\n        supplier: null,\n        driverInfo: null,\n        sqdShippingMark: null,\n        cargoName: null,\n        totalBoxes: null,\n        packageType: null,\n        totalGrossWeight: null,\n        totalVolume: null,\n        damageStatus: \"0\",\n        storageLocation1: null,\n        storageLocation2: null,\n        storageLocation3: null,\n        receivedStorageFee: null,\n        unpaidUnloadingFee: null,\n        logisticsAdvanceFee: null,\n        rentalBalanceFee: null,\n        freeStackPeriod: null,\n        overdueRentalUnitPrice: null,\n        overdueRentalFee: null,\n        notes: null,\n        warehouseCode: null,\n        recordType: null,\n        inboundType: null,\n        cargoNature: null,\n        createdAt: null,\n        preOutboundFlag: null,\n        outboundRequestFlag: null,\n        sqdPlannedOutboundDate: null,\n        confirmInboundRequestFlag: null,\n        confirmOutboundRequestFlag: null,\n        sqdInboundHandler: null,\n        partialOutboundFlag: null,\n        outboundRecordId: null,\n        actualInboundTime: moment().format(\"yyyy-MM-DD HH:mm:ss\"),\n        actualOutboundTime: null,\n        cargoDetailRows: null,\n        includesUnloadingFee: null,\n        unpaidPackingFee: null,\n        inboundFee: null,\n        singlePieceWeightFormatter: null,\n        unitLengthFormatter: null,\n        unitWidthFormatter: null,\n        unitHeightFormatter: null,\n        singlePieceVolumeFormatter: null,\n        unitGrossWeightFormatter: null,\n        unitVolumeFormatter: null,\n        rsCargoDetailsList: [\n          {\n            shippingMark: \"\",\n            itemName: \"\",\n            boxCount: 0,\n            packageType: \"纸箱\",\n            singlePieceWeightFormatter: \"\",\n            unitLengthFormatter: \"\",\n            unitWidthFormatter: \"\",\n            unitHeightFormatter: \"\",\n            singlePieceVolumeFormatter: \"\",\n            unitGrossWeightFormatter: \"\",\n            unitVolumeFormatter: \"\",\n            damageStatus: \"\",\n            boxItemCount: 0,\n            subtotalItemCount: 0,\n            expressDate: moment().format(\"yyyy-MM-DD\"),\n            additionalFee: 0\n          }\n        ]\n      }\n      this.resetForm(\"form\")\n    },\n    /** 搜索按钮操作 */\n    handleQuery() {\n      this.queryParams.pageNum = 1\n      this.getList()\n    },\n    /** 重置按钮操作 */\n    resetQuery() {\n      this.resetForm(\"queryForm\")\n      // 保留isTopLevel参数，确保重置后仍然只查询顶层数据\n      this.queryParams.isTopLevel = true\n      this.handleQuery()\n    },\n    handleStatusChange(row) {\n      let text = row.status === \"0\" ? \"启用\" : \"停用\"\n      this.$modal.confirm(\"确认要\\\"\" + text + \"吗？\").then(function () {\n        return changeStatus(row.inventoryId, row.status)\n      }).then(() => {\n        this.$modal.msgSuccess(text + \"成功\")\n      }).catch(function () {\n        row.status = row.status === \"0\" ? \"1\" : \"0\"\n      })\n    },\n    // 多选框选中数据\n    handleSelectionChange(selection) {\n      // 正确获取表格数据 - 通过data属性\n      const treeData = this.$refs.inventoryTable.store.states.data\n\n      // 获取之前的选择状态，用于比较变化\n      const previousIds = [...this.ids]\n\n      // 清空当前选择\n      this.ids = []\n      this.selectedInventoryList = []\n\n      // 重新填充选择数据\n      this.ids = selection.map(item => item.inventoryId)\n      this.single = selection.length !== 1\n      this.multiple = !selection.length\n      this.selectedInventoryList = selection\n\n      // 找出新选中和取消选中的项\n      const newlySelected = this.ids.filter(id => !previousIds.includes(id))\n      const newlyDeselected = previousIds.filter(id => !this.ids.includes(id))\n\n      // 处理新选中的打包箱：自动选中其子项\n      selection.forEach(item => {\n        if (item.packageRecord === \"1\" && newlySelected.includes(item.inventoryId)) {\n          // 如果是新选中的打包箱节点\n\n          // 在树形表格数据中找到对应的节点\n          const parentNode = treeData.find(node => node.inventoryId === item.inventoryId)\n\n          // 检查节点是否已展开(已有children属性且有内容)\n          if (parentNode && parentNode.children && parentNode.children.length > 0) {\n            // 如果节点已展开，直接选中其所有子项\n            setTimeout(() => {\n              parentNode.children.forEach(child => {\n                if (!this.ids.includes(child.inventoryId)) {\n                  this.ids.push(child.inventoryId)\n                  this.selectedInventoryList.push(child)\n                  this.$refs.inventoryTable.toggleRowSelection(child, true)\n                }\n              })\n            }, 50) // 给一点时间让UI更新\n          } else if (parentNode && !parentNode.childrenLoaded && parentNode.hasChildren) {\n            // 如果节点未展开且未加载过但有子节点标记\n            parentNode.childrenLoaded = true\n\n            // 手动展开行，触发懒加载\n            this.$refs.inventoryTable.toggleRowExpansion(parentNode, true)\n\n            // 监听子节点加载完成后再选中它们\n            // 这里利用了loadChildInventory方法中的逻辑，它会在子节点加载后处理选中状态\n          }\n        }\n      })\n\n      // 处理取消选中的打包箱：取消选中其子项\n      newlyDeselected.forEach(parentId => {\n        // 找出对应的父节点\n        const parentNode = treeData.find(node =>\n          node.inventoryId === parentId && node.packageRecord === \"1\"\n        )\n\n        if (parentNode && parentNode.children && parentNode.children.length > 0) {\n          // 取消选中所有子项\n          parentNode.children.forEach(child => {\n            const childIndex = this.ids.indexOf(child.inventoryId)\n            if (childIndex > -1) {\n              // 从选中列表中移除\n              this.ids.splice(childIndex, 1)\n              const itemIndex = this.selectedInventoryList.findIndex(\n                item => item.inventoryId === child.inventoryId\n              )\n              if (itemIndex > -1) {\n                this.selectedInventoryList.splice(itemIndex, 1)\n              }\n              // 在UI上取消选中\n              this.$refs.inventoryTable.toggleRowSelection(child, false)\n            }\n          })\n        }\n      })\n    },\n    handleOpenAggregator() {\n      // listAggregatorRct(this.queryParams).then(response => {\n      //   this.aggregatorRctList = response\n      // })\n\n      this.openAggregator = true\n    },\n    /** 新增按钮操作 */\n    handleAdd() {\n      this.reset()\n      this.open = true\n      this.title = \"入仓\"\n      this.form.sqdInboundHandler = this.$store.state.user.name.split(\" \")[1]\n      this.form.actualInboundTime = new Date()\n      this.form.inventoryStatus = \"0\"\n      // this.form.rsCargoDetailsList ? null : this.form.rsCargoDetailsList = []\n      // this.form.rsCargoDetailsList.push(this._.cloneDeep(this.cargoDetailRow))\n      this.$nextTick(() => {\n        this.form.inboundSerialNoPre = \"RS.91\"\n        this.form.cargoNature = \"普货\"\n        this.form.recordType = \"标准\"\n        this.form.inboundType = \"入仓\"\n      })\n\n    },\n    /** 修改按钮操作 */\n    handleUpdate(row) {\n      this.reset()\n      const inventoryId = row.inventoryId || this.ids\n      getInventory(inventoryId).then(response => {\n        this.form = response.data\n        this.form.rsCargoDetailsList = response.data.rsCargoDetailsList.map(item => ({\n          ...item,\n          shippingMark: item.shippingMark || \"\",\n          itemName: item.itemName || \"\",\n          boxCount: item.boxCount || 0,\n          packageType: item.packageType || \"\",\n          singlePieceWeight: item.singlePieceWeight || 0,\n          singlePieceWeightFormatter: this.formatNumber(item.singlePieceWeight || 0),\n          unitLength: item.unitLength || 0,\n          unitLengthFormatter: this.formatNumber(item.unitLength || 0),\n          unitWidth: item.unitWidth || 0,\n          unitWidthFormatter: this.formatNumber(item.unitWidth || 0),\n          unitHeight: item.unitHeight || 0,\n          unitHeightFormatter: this.formatNumber(item.unitHeight || 0),\n          singlePieceVolume: item.singlePieceVolume || 0,\n          singlePieceVolumeFormatter: this.formatNumber(item.singlePieceVolume || 0),\n          unitGrossWeight: item.unitGrossWeight || 0,\n          unitGrossWeightFormatter: this.formatNumber(item.unitGrossWeight || 0),\n          unitVolume: item.unitVolume || 0,\n          unitVolumeFormatter: this.formatNumber(item.unitVolume || 0),\n          damageStatus: item.damageStatus || \"\"\n        }))\n        if (response.data.packageRecord === \"0\") {\n          this.open = true\n        } else {\n          this.openPkg = true\n        }\n\n        this.title = \"修改库存\"\n      })\n    },\n    /** 提交按钮 */\n    submitForm(type) {\n      this.$refs[\"form\"].validate(valid => {\n        if (valid) {\n          // 检查分单号格式\n          if (this.form.subOrderNo && !this.form.subOrderNo.startsWith(`${this.form.clientCode}-`)) {\n            this.$modal.msgError(`分单号必须以 ${this.form.clientCode}- 开头`)\n            return\n          }\n\n          this.form.inboundDate = this.form.inboundDate ? moment(this.form.inboundDate).format(\"yyyy-MM-DD HH:mm:ss\") : null\n          this.form.actualInboundTime = this.form.actualInboundTime ? moment(this.form.actualInboundTime).format(\"yyyy-MM-DD HH:mm:ss\") : null\n\n          // this.form.inboundSerialNo = this.form.inboundSerialNoPre + this.form.inboundSerialNoSub.padStart(3, \"0\")\n          this.form.actualInboundTime = moment(this.form.actualInboundTime).format(\"yyyy-MM-DD HH:mm:ss\")\n          if (type !== \"pkg\") {\n            this.form.totalBoxes = 0\n            this.form.totalGrossWeight = 0\n            this.form.totalVolume = 0\n            this.form.rsCargoDetailsList.forEach((item, index) => {\n              // 汇总计算\n              this.form.totalBoxes = currency(item.boxCount || 0).add(this.form.totalBoxes).value\n              this.form.totalGrossWeight = currency(item.unitGrossWeight || 0).add(this.form.totalGrossWeight).value\n              this.form.totalVolume = currency(item.unitVolume || 0).add(this.form.totalVolume).value\n            })\n          }\n\n          // 统计唛头\n          let mark = []\n          this.form.rsCargoDetailsList.map(item => {\n            mark.push(item.shippingMark)\n          })\n          this.form.sqdShippingMark = mark.join(\",\")\n\n          if (this.form.inventoryId != null) {\n            this.form.rentalSettlementDate ? null : this.form.rentalSettlementDate = this.form.actualInboundTime\n            updateInventory(this.form).then(response => {\n              this.$modal.msgSuccess(\"修改成功\")\n              this.getList()\n            })\n          } else {\n            if (type === \"pkg\") {\n              this.form.packageRecord = \"1\"\n              this.form.repackingStatus = \"打包中\"\n\n            }\n            this.form.rentalSettlementDate = this.form.actualInboundTime\n            addInventory(this.form).then(response => {\n              this.form = response.data\n              this.$modal.msgSuccess(\"新增成功\")\n              this.getList()\n            })\n          }\n        }\n      })\n    },\n    /** 删除按钮操作 */\n    handleDelete(row) {\n      const inventoryIds = row.inventoryId || this.ids\n      this.$modal.confirm(\"是否确认删除库存编号为\\\"\" + inventoryIds + \"\\\"的数据项？\").then(function () {\n        return delInventory(inventoryIds)\n      }).then(() => {\n        this.getList()\n        this.$modal.msgSuccess(\"删除成功\")\n      }).catch(() => {\n      })\n    },\n    /** 导入按钮操作 */\n    handleImport() {\n      this.upload.title = \"用户导入\"\n      this.upload.open = true\n    },\n    /** 导出按钮操作 */\n    handleExport() {\n\n      this.download(\"system/inventory/export\", {\n        ...this.queryParams, pageSize: 999\n      }, `inventory_${new Date().getTime()}.xlsx`)\n    }\n  }\n}\n</script>\n<style lang=\"scss\" scoped>\n/* 针对不同浏览器的 spinner 样式隐藏 */\n.no-spinner ::-webkit-inner-spin-button,\n.no-spinner ::-webkit-outer-spin-button {\n  -webkit-appearance: none;\n  margin: 0;\n}\n\n.no-spinner {\n  -moz-appearance: textfield; /* 针对 Firefox */\n}\n\n::v-deep .edit .number .el-input__inner {\n  text-align: right;\n}\n\n</style>\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAi5BA,IAAAA,UAAA,GAAAC,OAAA;AAQA,IAAAC,CAAA,GAAAD,OAAA;AACA,IAAAE,iBAAA,GAAAC,sBAAA,CAAAH,OAAA;AACA,IAAAI,oBAAA,GAAAD,sBAAA,CAAAH,OAAA;AACA,IAAAK,QAAA,GAAAF,sBAAA,CAAAH,OAAA;AACA,IAAAM,cAAA,GAAAH,sBAAA,CAAAH,OAAA;AACA,IAAAO,SAAA,GAAAJ,sBAAA,CAAAH,OAAA;AACA,IAAAQ,IAAA,GAAAL,sBAAA,CAAAH,OAAA;AACA,IAAAS,OAAA,GAAAN,sBAAA,CAAAH,OAAA;AACA,IAAAU,MAAA,GAAAP,sBAAA,CAAAH,OAAA;AACA,IAAAW,KAAA,GAAAX,OAAA;AACA,IAAAY,MAAA,GAAAT,sBAAA,CAAAH,OAAA;AACA,IAAAa,iBAAA,GAAAb,OAAA;AACA,IAAAc,yBAAA,GAAAd,OAAA;AACA,IAAAe,IAAA,GAAAf,OAAA;AACA,IAAAgB,KAAA,GAAAhB,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAEA,IAAAiB,eAAA;AAAA,IAAAC,QAAA,GACA;EACAC,IAAA;EACAC,IAAA,WAAAA,KAAA;IAAA,IAAAC,KAAA;IACA,IAAAC,uBAAA,YAAAA,wBAAAC,IAAA,EAAAC,KAAA,EAAAC,QAAA;MACAC,OAAA,CAAAC,GAAA,CAAAH,KAAA;MACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACA;;IACA;MACAI,eAAA;MACAC,cAAA;QACAC,cAAA;QACAC,eAAA;QACAC,kBAAA;QACAC,UAAA;QACAC,YAAA;QACAC,QAAA;QACAC,QAAA;QACAC,YAAA;QACAC,iBAAA;QACAC,WAAA,MAAAC,eAAA,IAAAC,MAAA;QACAC,aAAA;QACAC,WAAA;QACAC,eAAA;QACAC,UAAA;QACAC,SAAA;QACAC,UAAA;QACAC,UAAA;QACAC,YAAA;QACAC,OAAA;MACA;MACAC,QAAA;MACAC,SAAA;MACA;MACAC,OAAA;MACA;MACAC,GAAA;MACAC,iBAAA;MACAC,aAAA,EAAAC,kDAAA;MACA;MACAC,MAAA;MACAC,cAAA;MACA;MACAC,QAAA;MACAC,qBAAA;MACAC,eAAA;MACA;MACAC,UAAA;MACA;MACAC,KAAA;MACA;MACAC,aAAA;MACAC,YAAA;MACA;MACAC,KAAA;MACAC,SAAA;MAEAC,YAAA;MAEA;MACAC,IAAA;MACAC,OAAA;MACAC,OAAA;MACA;MACAC,MAAA;QACA;QACAH,IAAA;QACA;QACAH,KAAA;QACA;QACAO,WAAA;QACA;QACAC,aAAA;QACA;QACAC,OAAA;UAAAC,aAAA,kBAAAC,cAAA;QAAA;QACA;QACAC,GAAA,EAAAC,OAAA,CAAAC,GAAA,CAAAC,gBAAA;MACA;MACA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAC,eAAA;QACAvD,eAAA;QACAC,kBAAA;QACAuD,WAAA;QACAC,UAAA;QACAC,WAAA;QACAC,oBAAA;QACAC,YAAA;QACA1D,UAAA;QACA2D,UAAA;QACAC,QAAA;QACAC,UAAA;QACAC,eAAA;QACAC,SAAA;QACAC,UAAA;QACAtD,WAAA;QACAuD,gBAAA;QACAC,WAAA;QACAlD,YAAA;QACAmD,gBAAA;QACAC,gBAAA;QACAC,gBAAA;QACAC,kBAAA;QACAC,kBAAA;QACAC,mBAAA;QACAC,gBAAA;QACAC,eAAA;QACAC,sBAAA;QACAC,gBAAA;QACAC,KAAA;QACAC,UAAA;MACA;;MACA;MACAC,IAAA;MACAC,cAAA;MACA;MACAC,KAAA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACA;QACAtB,UAAA,GACA;UACAuB,SAAA,WAAAA,UAAA5F,IAAA,EAAAC,KAAA,EAAAC,QAAA;YACA,KAAAD,KAAA;cACAC,QAAA;cACA;YACA;YACA,KAAAJ,KAAA,CAAA2F,IAAA,CAAA/E,UAAA;cACAR,QAAA,KAAA2F,KAAA;cACA;YACA;YACA,IAAAC,MAAA,MAAAC,MAAA,CAAAjG,KAAA,CAAA2F,IAAA,CAAA/E,UAAA;YACA,KAAAT,KAAA,CAAA+F,UAAA,CAAAF,MAAA;cACA5F,QAAA,KAAA2F,KAAA,yCAAAE,MAAA,CAAAD,MAAA;cACA;YACA;YACA5F,QAAA;UACA;UACA+F,OAAA;QACA;MAEA;MACAC,cAAA;IACA;EACA;EACAC,UAAA;IACAC,wBAAA,EAAAA,cAAA;IACAC,YAAA,EAAAA,gBAAA;IACAC,aAAA,EAAAA;EACA;EACAC,KAAA;IACA/D,UAAA,WAAAA,WAAAgE,CAAA;MACA,IAAAA,CAAA;QACA,KAAA3E,SAAA;QACA,KAAAD,QAAA;MACA;QACA,KAAAC,SAAA;QACA,KAAAD,QAAA;MACA;IACA;EACA;EACA6E,OAAA,WAAAA,QAAA;IACA,KAAAC,SAAA;EACA;EACAC,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;EACA;EACAC,OAAA;IACA;IACAC,cAAA,WAAAA,eAAA;MACA,KAAAC,KAAA,CAAA7D,MAAA,CAAA8D,MAAA;IACA;IACA;IACAC,wBAAA,WAAAA,yBAAAC,KAAA,EAAAC,IAAA,EAAAC,QAAA;MACA,KAAAlE,MAAA,CAAAC,WAAA;IACA;IACA;IACAkE,iBAAA,WAAAA,kBAAAC,QAAA,EAAAH,IAAA,EAAAC,QAAA;MACA,KAAAlE,MAAA,CAAAH,IAAA;MACA,KAAAG,MAAA,CAAAC,WAAA;MACA,KAAA4D,KAAA,CAAA7D,MAAA,CAAAqE,UAAA;MACA,KAAAC,QAAA,CAAAC,IAAA,CAAAH,QAAA,CAAAI,GAAA;MACA,IAAAJ,QAAA,CAAAI,GAAA;QACA,KAAAC,QAAA;MACA;MACA,KAAAf,OAAA;IACA;IACAgB,aAAA,WAAAA,cAAA;MAAA,IAAAC,MAAA;MACA,SAAApC,IAAA,CAAA/E,UAAA;QACA,IAAAoH,wBAAA;UAAAC,SAAA,OAAAtC,IAAA,CAAAuC;QAAA,GAAAC,IAAA,WAAAX,QAAA;UACAO,MAAA,CAAAnC,cAAA,GAAA4B,QAAA,CAAAY,IAAA;QACA;MACA;IACA;IACA;IACAC,kBAAA,WAAAA,mBAAAC,IAAA,EAAAC,QAAA,EAAAC,OAAA;MAAA,IAAAC,MAAA;MACA;MACA,IAAAT,wBAAA;QAAAC,SAAA,EAAAK,IAAA,CAAAJ;MAAA,GAAAC,IAAA,WAAAX,QAAA;QACA,IAAAY,IAAA,GAAAZ,QAAA,CAAAY,IAAA;;QAEA;QACAI,OAAA,CAAAJ,IAAA;QACAE,IAAA,CAAAI,QAAA,GAAAN,IAAA;;QAEA;QACA,IAAAK,MAAA,CAAAxG,GAAA,CAAA0G,QAAA,CAAAL,IAAA,CAAAJ,WAAA;UACAU,UAAA;YACAR,IAAA,CAAAS,OAAA,WAAAC,KAAA;cACA,KAAAL,MAAA,CAAAxG,GAAA,CAAA0G,QAAA,CAAAG,KAAA,CAAAZ,WAAA;gBACAO,MAAA,CAAAxG,GAAA,CAAA8G,IAAA,CAAAD,KAAA,CAAAZ,WAAA;gBACAO,MAAA,CAAAjG,qBAAA,CAAAuG,IAAA,CAAAD,KAAA;cACA;cACA;cACAL,MAAA,CAAAxB,KAAA,CAAA+B,cAAA,CAAAC,kBAAA,CAAAH,KAAA;YACA;UACA;QACA;MACA;IACA;IACAI,YAAA,WAAAA,aAAA;MACA,KAAAC,KAAA;MACA,KAAAjG,OAAA;IACA;IACAkG,eAAA,WAAAA,gBAAA;MACA;MACA,SAAAnH,GAAA,CAAAoH,MAAA;QACA,KAAA3B,QAAA,CAAA4B,OAAA;QACA;MACA;;MAEA;MACA,IAAAC,eAAA,QAAA/G,qBAAA,IAAA5B,UAAA;MACA,IAAA4I,YAAA,QAAAhH,qBAAA,CAAAiH,KAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAA9I,UAAA,KAAA2I,eAAA;MAAA;MACA,IAAAI,UAAA,QAAAnH,qBAAA,CAAAiH,KAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,CAAAE,aAAA;MAAA;MAEA,KAAAJ,YAAA;QACA,KAAA9B,QAAA,CAAA4B,OAAA;QACA;MACA;MACA,KAAAK,UAAA;QACA,KAAAjC,QAAA,CAAA4B,OAAA;QACA;MACA;;MAEA;MACA,KAAAH,KAAA;MACA,KAAAxD,IAAA,CAAA/E,UAAA,GAAA2I,eAAA;MACA,KAAA5D,IAAA,CAAAkE,UAAA,QAAArH,qBAAA,IAAAqH,UAAA;MACA,KAAAlE,IAAA,CAAAmE,eAAA;;MAEA;MACA,IAAAC,GAAA,OAAAC,IAAA;MACA,KAAArE,IAAA,CAAAsE,iBAAA,GAAAF,GAAA;MAEA,KAAApE,IAAA,CAAA1B,eAAA;MACA,KAAA0B,IAAA,CAAApB,UAAA,GAAAgF,eAAA;;MAEA;MACA,KAAA5D,IAAA,CAAAuE,gBAAA,QAAAjI,GAAA;MAEA,KAAAa,KAAA;MACA,KAAAC,SAAA;IACA;IACAoH,SAAA,WAAAA,UAAA;MAAA,IAAAC,MAAA;MACA,IAAAC,oBAAA,OAAA5H,eAAA,EAAA0F,IAAA,WAAAX,QAAA;QACA4C,MAAA,CAAA1C,QAAA,CAAA4C,OAAA;QACAF,MAAA,CAAAlH,OAAA;QACAkH,MAAA,CAAAtD,OAAA;MACA;IACA;IACAyD,aAAA,WAAAA,cAAA;MAAA,IAAAC,MAAA;MACA,IAAAC,qBAAA;QAAA7J,UAAA,OAAA+E,IAAA,CAAA/E,UAAA;QAAAkJ,eAAA;MAAA,GAAA3B,IAAA,WAAAX,QAAA;QACAgD,MAAA,CAAArH,OAAA,GAAAqE,QAAA,CAAAzH,IAAA;MACA;IACA;IACA2K,SAAA,WAAAA,UAAA;MAAA,IAAAC,MAAA;MACA,KAAAnI,qBAAA,CAAAqG,OAAA,WAAAa,IAAA;QACAA,IAAA,CAAAzB,SAAA,GAAA0C,MAAA,CAAAhF,IAAA,CAAAsC,SAAA;QACAyB,IAAA,CAAAI,eAAA;MACA;MAEA,IAAAc,iBAAA,OAAApI,qBAAA,EAAA2F,IAAA,WAAAX,QAAA;QACAmD,MAAA,CAAAjD,QAAA,CAAA4C,OAAA;QACAK,MAAA,CAAA5H,SAAA;QACA4H,MAAA,CAAA7D,OAAA;MACA;IACA;IACA+D,SAAA,EAAAA,eAAA;IACAC,UAAA,WAAAA,WAAA;MACA;MACA,KAAAnF,IAAA,CAAAoF,kBAAA,QAAApF,IAAA,CAAAoF,kBAAA,CAAAC,QAAA;IACA;IACAC,gBAAA,WAAAA,iBAAA;MACA,aAAAtF,IAAA,CAAAuF,UAAA;QACA;UACA,KAAAvF,IAAA,CAAAwF,UAAA,QAAA/E,cAAA,CAAAgF,kBAAA;UACA;QACA;UACA,KAAAzF,IAAA,CAAAwF,UAAA,QAAA/E,cAAA,CAAAiF,iBAAA;UACA;QACA;UACA,KAAA1F,IAAA,CAAAwF,UAAA,QAAA/E,cAAA,CAAAkF,iBAAA;UACA;MACA;IACA;IACAC,OAAA,WAAAA,QAAAC,QAAA;MACA,WAAAC,iBAAA,EAAAD,QAAA,EAAAE,MAAA,UAAAvL,KAAA;IACA;IACA;IACAwL,YAAA,WAAAA,aAAAxL,KAAA;MACA,WAAAsL,iBAAA,EAAAtL,KAAA;QAAAyL,MAAA;QAAAC,SAAA;MAAA,GAAAzK,MAAA;IACA;IACA;IACA0K,WAAA,WAAAA,YAAAC,CAAA;MACA,IAAAC,QAAA,GAAAD,CAAA,CAAAE,OAAA;MACA,KAAAC,KAAA,CAAAF,QAAA;QACA,KAAAG,MAAA,GAAAC,UAAA,CAAAJ,QAAA;QACA,YAAAL,YAAA,CAAAK,QAAA;MACA;IACA;IACAK,eAAA,WAAAA,gBAAA;MAAA,IAAAC,MAAA;MACA,KAAA3G,IAAA,CAAA4G,kBAAA,CAAAC,GAAA,WAAA9C,IAAA;QACA4C,MAAA,CAAAG,eAAA,CAAA/C,IAAA;MACA;MAEA,KAAAgD,YAAA;IACA;IACAD,eAAA,WAAAA,gBAAAE,GAAA;MACAA,GAAA,CAAAC,0BAAA,QAAAjB,YAAA,CAAAgB,GAAA,CAAAE,iBAAA;MACAF,GAAA,CAAAG,mBAAA,QAAAnB,YAAA,CAAAgB,GAAA,CAAAnL,UAAA;MACAmL,GAAA,CAAAI,mBAAA,QAAApB,YAAA,CAAAgB,GAAA,CAAAjL,UAAA;MACAiL,GAAA,CAAAK,0BAAA,QAAArB,YAAA,CAAAgB,GAAA,CAAAM,iBAAA;MACAN,GAAA,CAAAO,wBAAA,QAAAvB,YAAA,CAAAgB,GAAA,CAAApL,eAAA;MACAoL,GAAA,CAAAQ,mBAAA,QAAAxB,YAAA,CAAAgB,GAAA,CAAAhL,UAAA;MACAgL,GAAA,CAAAS,kBAAA,QAAAzB,YAAA,CAAAgB,GAAA,CAAAlL,SAAA;MACAkL,GAAA,CAAAU,qBAAA,GAAAC,MAAA,CAAAX,GAAA,CAAA3L,YAAA,IAAAsM,MAAA,CAAAX,GAAA,CAAA3L,YAAA;MACA2L,GAAA,CAAAY,0BAAA,GAAAD,MAAA,CAAAX,GAAA,CAAA1L,iBAAA,IAAAqM,MAAA,CAAAX,GAAA,CAAA1L,iBAAA;IACA;IACA;IACAuM,UAAA,WAAAA,WAAAb,GAAA,EAAAc,IAAA;MACA,KAAAd,GAAA,QAAAe,QAAA,CAAAC,OAAA,EAAAhB,GAAA;MACA,QAAAc,IAAA;QACA;UACA,IAAAG,+BAAA,GAAAC,MAAA,CAAAlB,GAAA,CAAAC,0BAAA,QAAAX,OAAA;UACA,KAAAC,KAAA,CAAA0B,+BAAA,KAAAA,+BAAA;YACAjB,GAAA,CAAAC,0BAAA,QAAAjB,YAAA,CAAAiC,+BAAA;UACA;UACA;QACA;UACA,IAAAE,eAAA,GAAAD,MAAA,CAAAlB,GAAA,CAAAG,mBAAA,QAAAb,OAAA;UACA,KAAAC,KAAA,CAAA4B,eAAA,KAAAA,eAAA;YACAnB,GAAA,CAAAG,mBAAA,QAAAnB,YAAA,CAAAmC,eAAA;UACA;UACA;QACA;UACA,IAAAC,cAAA,GAAAF,MAAA,CAAAlB,GAAA,CAAAS,kBAAA,QAAAnB,OAAA;UACA,KAAAC,KAAA,CAAA6B,cAAA,KAAAA,cAAA;YACApB,GAAA,CAAAS,kBAAA,QAAAzB,YAAA,CAAAoC,cAAA;UACA;UACA;QACA;UACA,IAAAC,wBAAA,GAAAH,MAAA,CAAAlB,GAAA,CAAAI,mBAAA,QAAAd,OAAA;UACA,KAAAC,KAAA,CAAA8B,wBAAA,KAAAA,wBAAA;YACArB,GAAA,CAAAI,mBAAA,QAAApB,YAAA,CAAAqC,wBAAA;UACA;UACA;QACA;UACA,IAAAC,+BAAA,GAAAJ,MAAA,CAAAlB,GAAA,CAAAK,0BAAA,QAAAf,OAAA;UACA,KAAAC,KAAA,CAAA+B,+BAAA,KAAAA,+BAAA;YACAtB,GAAA,CAAAK,0BAAA,QAAArB,YAAA,CAAAsC,+BAAA;UACA;UACA;QACA;UACA,IAAAC,6BAAA,GAAAL,MAAA,CAAAlB,GAAA,CAAAO,wBAAA,QAAAjB,OAAA;UACA,KAAAC,KAAA,CAAAgC,6BAAA,KAAAA,6BAAA;YACAvB,GAAA,CAAAO,wBAAA,QAAAvB,YAAA,CAAAuC,6BAAA;UACA;UACA;QACA;UACA,IAAAC,wBAAA,GAAAN,MAAA,CAAAlB,GAAA,CAAAQ,mBAAA,QAAAlB,OAAA;UACA,KAAAC,KAAA,CAAAiC,wBAAA,KAAAA,wBAAA;YACAxB,GAAA,CAAAQ,mBAAA,QAAAxB,YAAA,CAAAwC,wBAAA;UACA;UACA;MACA;IACA;IACAC,iBAAA,WAAAA,kBAAAzB,GAAA,EAAAc,IAAA;MAAA,IAAAY,MAAA;MACA,KAAA1B,GAAA,QAAAe,QAAA,CAAAC,OAAA,EAAAhB,GAAA;MACA,QAAAc,IAAA;QACA;UACA,IAAAG,+BAAA,GAAAC,MAAA,CAAAlB,GAAA,CAAAC,0BAAA,QAAAX,OAAA;UACA,KAAAC,KAAA,CAAA0B,+BAAA,KAAAA,+BAAA;YACAjB,GAAA,CAAAE,iBAAA,GAAAT,UAAA,CAAAwB,+BAAA;UACA;;UACA;QACA;UACA,IAAAE,eAAA,GAAAD,MAAA,CAAAlB,GAAA,CAAAG,mBAAA,QAAAb,OAAA;UACA,KAAAC,KAAA,CAAA4B,eAAA,KAAAA,eAAA;YACAnB,GAAA,CAAAnL,UAAA,GAAA4K,UAAA,CAAA0B,eAAA;UACA;;UACA;QACA;UACA,IAAAC,cAAA,GAAAF,MAAA,CAAAlB,GAAA,CAAAS,kBAAA,QAAAnB,OAAA;UACA,KAAAC,KAAA,CAAA6B,cAAA,KAAAA,cAAA;YACApB,GAAA,CAAAlL,SAAA,GAAA2K,UAAA,CAAA2B,cAAA;UACA;;UACA;QACA;UACA,IAAAC,wBAAA,GAAAH,MAAA,CAAAlB,GAAA,CAAAI,mBAAA,QAAAd,OAAA;UACA,KAAAC,KAAA,CAAA8B,wBAAA,KAAAA,wBAAA;YACArB,GAAA,CAAAjL,UAAA,GAAA0K,UAAA,CAAA4B,wBAAA;UACA;;UACA;QACA;UACA,IAAAC,+BAAA,GAAAJ,MAAA,CAAAlB,GAAA,CAAAK,0BAAA,QAAAf,OAAA;UACA,KAAAC,KAAA,CAAA+B,+BAAA,KAAAA,+BAAA;YACAtB,GAAA,CAAAM,iBAAA,GAAAb,UAAA,CAAA6B,+BAAA;UACA;;UACA;QACA;UACA,IAAAC,6BAAA,GAAAL,MAAA,CAAAlB,GAAA,CAAAO,wBAAA,QAAAjB,OAAA;UACA,KAAAC,KAAA,CAAAgC,6BAAA,KAAAA,6BAAA;YACAvB,GAAA,CAAApL,eAAA,GAAA6K,UAAA,CAAA8B,6BAAA;UACA;;UACA;QACA;UACA,IAAAC,wBAAA,GAAAN,MAAA,CAAAlB,GAAA,CAAAQ,mBAAA,QAAAlB,OAAA;UACA,KAAAC,KAAA,CAAAiC,wBAAA,KAAAA,wBAAA;YACAxB,GAAA,CAAAhL,UAAA,GAAAyK,UAAA,CAAA+B,wBAAA;UACA;;UACA;MACA;;MAEA;MACA,KAAAxI,IAAA,CAAA4G,kBAAA,QAAA5G,IAAA,CAAA4G,kBAAA,CAAAC,GAAA,WAAA9C,IAAA;QACA,IAAAiD,GAAA,KAAAjD,IAAA;UACA,IAAAA,IAAA,CAAAlI,UAAA,IAAAkI,IAAA,CAAAjI,SAAA,IAAAiI,IAAA,CAAAhI,UAAA;YACA,IAAA4M,EAAA,OAAA7C,iBAAA,EAAA/B,IAAA,CAAAlI,UAAA,EAAA+M,QAAA,CAAA7E,IAAA,CAAAjI,SAAA,EAAA8M,QAAA,CAAA7E,IAAA,CAAAhI,UAAA,EAAAvB,KAAA;YACAuJ,IAAA,CAAAuD,iBAAA,GAAAoB,MAAA,CAAA9C,OAAA,CAAA+C,EAAA;YACA5E,IAAA,CAAAsD,0BAAA,GAAAqB,MAAA,CAAA1C,YAAA,CAAAjC,IAAA,CAAAuD,iBAAA;UACA;UACA,IAAAvD,IAAA,CAAAuD,iBAAA,IAAAvD,IAAA,CAAA3I,QAAA;YACA2I,IAAA,CAAA/H,UAAA,OAAA8J,iBAAA,EAAA/B,IAAA,CAAAuD,iBAAA,EAAAsB,QAAA,CAAA7E,IAAA,CAAA3I,QAAA,EAAAZ,KAAA;YACAuJ,IAAA,CAAAyD,mBAAA,GAAAkB,MAAA,CAAA1C,YAAA,CAAAjC,IAAA,CAAA/H,UAAA;UACA;UACA,IAAA+H,IAAA,CAAAmD,iBAAA,IAAAnD,IAAA,CAAA3I,QAAA;YACA2I,IAAA,CAAAnI,eAAA,OAAAkK,iBAAA,EAAA/B,IAAA,CAAAmD,iBAAA,EAAA0B,QAAA,CAAA7E,IAAA,CAAA3I,QAAA,EAAAZ,KAAA;YACAuJ,IAAA,CAAAwD,wBAAA,GAAAmB,MAAA,CAAA1C,YAAA,CAAAjC,IAAA,CAAAnI,eAAA;UACA;UACA;UACA,IAAAmI,IAAA,CAAA1I,YAAA;YACA,IAAA0I,IAAA,CAAA3I,QAAA;cACA2I,IAAA,CAAAzI,iBAAA,OAAAwK,iBAAA,EAAA/B,IAAA,CAAA1I,YAAA,EAAAuN,QAAA,CAAA7E,IAAA,CAAA3I,QAAA,EAAAZ,KAAA;YACA;cACAuJ,IAAA,CAAAzI,iBAAA,OAAAwK,iBAAA,EAAA/B,IAAA,CAAA1I,YAAA,EAAAb,KAAA;YACA;UACA;QACA;QACA,OAAAuJ,IAAA;MACA;;MAEA;IACA;IACA8E,qBAAA,WAAAA,sBAAA7B,GAAA;MACA,KAAAhH,IAAA,CAAA/E,UAAA,GAAA+L,GAAA,CAAA/L,UAAA;MACA,KAAA+E,IAAA,CAAAkE,UAAA,GAAA8C,GAAA,CAAA9C,UAAA;MACA,KAAAlE,IAAA,CAAAJ,sBAAA,GAAAoH,GAAA,CAAA8B,WAAA;MACA,KAAA9I,IAAA,CAAAL,eAAA,GAAAqH,GAAA,CAAArH,eAAA;MACA,KAAAc,cAAA,GAAAuG,GAAA;MACA;AACA;AACA;AACA;AACA;MACA,IAAAA,GAAA,CAAA+B,oBAAA;QACA,KAAA/I,IAAA,CAAA+I,oBAAA,GAAA/B,GAAA,CAAA+B,oBAAA;QACA;AACA;AACA;MACA;;MACA,IAAA/B,GAAA,CAAAgC,kBAAA;QACA,KAAAhJ,IAAA,CAAAgJ,kBAAA,GAAAhC,GAAA,CAAAgC,kBAAA;QACA,IAAAhC,GAAA,CAAAgC,kBAAA;UACA,KAAAhJ,IAAA,CAAAiJ,gBAAA;QACA;MACA;MACA,IAAAjC,GAAA,CAAAkC,kBAAA;QACA,KAAAlJ,IAAA,CAAAkJ,kBAAA,GAAAlC,GAAA,CAAAkC,kBAAA;QACA,IAAAlC,GAAA,CAAAkC,kBAAA;UACA,KAAAlJ,IAAA,CAAAwF,UAAA;QACA;MACA;MACA,IAAAwB,GAAA,CAAAmC,mBAAA;QACA,KAAAnJ,IAAA,CAAAmJ,mBAAA,GAAAnC,GAAA,CAAAmC,mBAAA;MACA;MACA;MACA,SAAAnJ,IAAA,CAAAuF,UAAA,IAAAyB,GAAA,CAAAkC,kBAAA;QACA,aAAAlJ,IAAA,CAAAuF,UAAA;UACA;YACA,KAAAvF,IAAA,CAAAwF,UAAA,GAAAwB,GAAA,CAAAvB,kBAAA;YACA;UACA;YACA,KAAAzF,IAAA,CAAAwF,UAAA,GAAAwB,GAAA,CAAAtB,iBAAA;YACA;UACA;YACA,KAAA1F,IAAA,CAAAwF,UAAA,GAAAwB,GAAA,CAAArB,iBAAA;YACA;QACA;MACA;;MAEA;MACA,KAAA3F,IAAA,CAAApB,UAAA;MACA,KAAAoB,IAAA,CAAApB,UAAA,GAAAoI,GAAA,CAAA/L,UAAA;IACA;IACAmO,qBAAA,WAAAA,sBAAAC,KAAA;MAAA,IAAAC,MAAA;MACA,IAAAC,OAAA,GAAAF,KAAA,CAAAE,OAAA;QAAAnP,IAAA,GAAAiP,KAAA,CAAAjP,IAAA;MACA,IAAAoP,IAAA;MACA,IAAAC,gBAAA,qDACA,kFACA;MAEAF,OAAA,CAAArG,OAAA,WAAAwG,MAAA,EAAAC,KAAA;QACA,IAAAA,KAAA;UACAH,IAAA,CAAAG,KAAA;UACA;QACA;;QAEA;QACA,IAAAF,gBAAA,CAAAzG,QAAA,CAAA0G,MAAA,CAAAE,QAAA;UACA;UACA,IAAAC,MAAA,GAAAzP,IAAA,CACAyM,GAAA,WAAA9C,IAAA;YAAA,OAAA4D,MAAA,CAAA5D,IAAA,CAAA2F,MAAA,CAAAE,QAAA;UAAA,GACAE,MAAA,WAAAtP,KAAA;YAAA,QAAA+L,KAAA,CAAA/L,KAAA;UAAA;;UAEA,IAAAqP,MAAA,CAAAnG,MAAA;YACA;YACA,IAAA1G,KAAA,GAAA6M,MAAA,CAAAE,MAAA,WAAAC,IAAA,EAAAC,IAAA;cAAA,OAAAD,IAAA,GAAAC,IAAA;YAAA;YACAT,IAAA,CAAAG,KAAA,IACAL,MAAA,CAAAY,iBAAA,CAAAlN,KAAA,KACA0M,MAAA,CAAAE,QAAA,qBACA,SACAF,MAAA,CAAAE,QAAA,0BACA,SACAF,MAAA,CAAAE,QAAA,oBACA,OACA;UACA;YACAJ,IAAA,CAAAG,KAAA;UACA;QACA;UACAH,IAAA,CAAAG,KAAA;QACA;MACA;MAEA,OAAAH,IAAA;IACA;IACAW,YAAA,WAAAA,aAAAd,KAAA;MAAA,IAAAe,OAAA;MACA,IAAAb,OAAA,GAAAF,KAAA,CAAAE,OAAA;QAAAnP,IAAA,GAAAiP,KAAA,CAAAjP,IAAA;MACA,IAAAoP,IAAA;MACA,IAAAC,gBAAA;MAEAF,OAAA,CAAArG,OAAA,WAAAwG,MAAA,EAAAC,KAAA;QACA,IAAAA,KAAA;UACAH,IAAA,CAAAG,KAAA;UACA;QACA;;QAEA;QACA,IAAAF,gBAAA,CAAAzG,QAAA,CAAA0G,MAAA,CAAAE,QAAA;UACA;UACA,IAAAC,MAAA,GAAAzP,IAAA,CACAyM,GAAA,WAAA9C,IAAA;YAAA,OAAA4D,MAAA,CAAA5D,IAAA,CAAA2F,MAAA,CAAAE,QAAA;UAAA,GACAE,MAAA,WAAAtP,KAAA;YAAA,QAAA+L,KAAA,CAAA/L,KAAA;UAAA;;UAEA,IAAAqP,MAAA,CAAAnG,MAAA;YACA;YACA,IAAA1G,KAAA,GAAA6M,MAAA,CAAAE,MAAA,WAAAC,IAAA,EAAAC,IAAA;cAAA,OAAAD,IAAA,GAAAC,IAAA;YAAA;YACAT,IAAA,CAAAG,KAAA,IACAS,OAAA,CAAAF,iBAAA,CAAAlN,KAAA,KACA0M,MAAA,CAAAE,QAAA,oBACA,SACAF,MAAA,CAAAE,QAAA,yBACA,SACAF,MAAA,CAAAE,QAAA,kBACA,OACA;UACA;YACAJ,IAAA,CAAAG,KAAA;UACA;QACA;UACAH,IAAA,CAAAG,KAAA;QACA;MACA;MAEA,OAAAH,IAAA;IACA;IACAU,iBAAA,WAAAA,kBAAA1P,KAAA;MACA,OAAAmN,MAAA,CAAAnN,KAAA,EAAA6P,OAAA;IACA;IACAC,gBAAA,WAAAA,iBAAA;MACA,KAAAC,YAAA;IACA;IACAtJ,SAAA,WAAAA,UAAA;MACAuJ,SAAA,CAAAC,IAAA;QACAC,SAAA,OAAAC,4BAAA;MACA;IACA;IACAC,gBAAA,WAAAA,iBAAA9C,IAAA;MACA,IAAA1N,IAAA;MACAA,IAAA,CAAAa,UAAA,QAAA+E,IAAA,CAAA/E,UAAA;MACAb,IAAA,CAAAwE,UAAA,QAAAoB,IAAA,CAAApB,UAAA;MACAxE,IAAA,CAAAW,eAAA,QAAAiF,IAAA,CAAAjF,eAAA;MACAX,IAAA,CAAAqE,WAAA,QAAAuB,IAAA,CAAAvB,WAAA;MACArE,IAAA,CAAAkK,iBAAA,OAAA9I,eAAA,OAAAwE,IAAA,CAAAsE,iBAAA,EAAA7I,MAAA;MACArB,IAAA,CAAA2E,eAAA,QAAAiB,IAAA,CAAAjB,eAAA;MACA3E,IAAA,CAAA4E,SAAA,QAAAgB,IAAA,CAAAhB,SAAA;MACA5E,IAAA,CAAA6E,UAAA,QAAAe,IAAA,CAAAf,UAAA;MACA7E,IAAA,CAAA8E,gBAAA,QAAAc,IAAA,CAAAd,gBAAA;MACA9E,IAAA,CAAA+E,WAAA,QAAAa,IAAA,CAAAb,WAAA;MACA/E,IAAA,CAAA0F,KAAA,QAAAE,IAAA,CAAAF,KAAA;MACA1F,IAAA,CAAAyE,QAAA,QAAAmB,IAAA,CAAAnB,QAAA;MACAzE,IAAA,CAAA0E,UAAA,QAAAkB,IAAA,CAAAlB,UAAA;MACA1E,IAAA,CAAAyQ,WAAA,QAAA7K,IAAA,CAAA6K,WAAA;MACAzQ,IAAA,CAAA0Q,aAAA,QAAA9K,IAAA,CAAA8K,aAAA;MACA1Q,IAAA,CAAAW,eAAA,QAAAiF,IAAA,CAAAjF,eAAA;MAEAX,IAAA,CAAA2Q,SAAA,QAAA/K,IAAA,CAAAlB,UAAA;MAEA,IAAAgJ,IAAA;QACA7N,eAAA,OAAAuQ,SAAA,CAAA3J,aAAA;UAAAmK,QAAA,EAAAC;QAAA;MACA;QACAhR,eAAA,OAAAuQ,SAAA,CAAA3J,aAAA;UAAAmK,QAAA,EAAAE;QAAA;MACA;MACA;MACA;MACA;MACA,KAAA5J,KAAA,CAAA6J,OAAA,CAAAC,KAAA,CAAAnR,eAAA,EAAAG,IAAA;IACA;IACAiR,iBAAA,WAAAA,kBAAArE,GAAA;MACA,KAAAhH,IAAA,CAAA4G,kBAAA,QAAA5G,IAAA,CAAA4G,kBAAA,CAAAkD,MAAA,WAAA/F,IAAA;QAAA,OAAAA,IAAA,KAAAiD,GAAA;MAAA;MACA,KAAAD,YAAA;IACA;IACAuE,uBAAA,WAAAA,wBAAAtE,GAAA;MACA,KAAApM,eAAA;MACA,KAAAC,cAAA,GAAAmM,GAAA;IACA;IACAuE,wBAAA,WAAAA,yBAAAC,SAAA;MACA,KAAA1O,eAAA,GAAA0O,SAAA;IACA;IACAC,SAAA,WAAAA,UAAA;MAAA,IAAAC,OAAA;MACA,SAAA1L,IAAA,CAAAmE,eAAA;QACA,KAAAnE,IAAA,CAAAmE,eAAA;MACA;QACA,KAAAnE,IAAA,CAAAmE,eAAA;MACA;MAEA,IAAAwH,0BAAA,OAAA3L,IAAA,EAAAwC,IAAA,WAAAoJ,GAAA;QACAF,OAAA,CAAA3J,QAAA,CAAA4C,OAAA;MACA;IACA;IACAkH,iBAAA,WAAAA,kBAAA7E,GAAA,GAEA;IACA8E,cAAA,WAAAA,eAAA;MACA,KAAA9L,IAAA,CAAA4G,kBAAA,CAAAxD,IAAA,MAAAnK,CAAA,CAAA8S,SAAA,MAAAlR,cAAA;MACA,KAAAD,eAAA;IACA;IACAoR,yBAAA,WAAAA,0BAAAC,MAAA;MACAA,MAAA,CAAAC,MAAA,GAAAC,IAAA,CAAAC,SAAA,CAAAH,MAAA,CAAAC,MAAA;MACA,KAAA/N,WAAA,CAAA8N,MAAA,GAAAA,MAAA;MACA,WAAAD,oCAAA,OAAA7N,WAAA;IACA;IACA,aACAgD,OAAA,WAAAA,QAAA;MAAA,IAAAkL,OAAA;MACA,KAAAhQ,OAAA;MACA,KAAA8B,WAAA,CAAAmO,eAAA,QAAAC,MAAA,CAAAC,KAAA,CAAAC,IAAA,CAAAC,mBAAA,CAAAC,CAAA;MACA;MACA,KAAAxO,WAAA,CAAA4B,UAAA;MACA,IAAAsC,wBAAA,OAAAlE,WAAA,EAAAqE,IAAA,WAAAX,QAAA;QACA;QACA,IAAAY,IAAA,GAAAZ,QAAA,CAAAY,IAAA;;QAEA;QACA;QACA,IAAA4J,OAAA,CAAAlO,WAAA,CAAA4B,UAAA,KAAAsM,OAAA,CAAAlO,WAAA,CAAAW,UAAA;UACA2D,IAAA,GAAAA,IAAA,CAAAqH,MAAA,WAAA/F,IAAA;YAAA,QAAAA,IAAA,CAAAzB,SAAA;UAAA;QACA;QAEA+J,OAAA,CAAApP,aAAA,GAAAwF,IAAA,CAAAoE,GAAA,WAAA9C,IAAA;UACA;UACA,IAAAA,IAAA,CAAA6I,aAAA;YACA7I,IAAA,CAAA8I,WAAA;UACA;UACA,OAAA9I,IAAA;QACA;QACAsI,OAAA,CAAArP,KAAA,GAAA6E,QAAA,CAAA7E,KAAA;QACAqP,OAAA,CAAAhQ,OAAA;MACA;IACA;IACA;IACAyQ,MAAA,WAAAA,OAAA;MACA,KAAAxP,IAAA;MACA,KAAAkG,KAAA;IACA;IACA;IACAA,KAAA,WAAAA,MAAA;MACA,KAAAvD,cAAA;MACA,KAAAD,IAAA;QACAmE,eAAA;QACA5B,WAAA;QACAjE,eAAA;QACAvD,eAAA;QACAgS,kBAAA;QACA/R,kBAAA;QACAuD,WAAA;QACAC,UAAA;QACAC,WAAA;QACAC,oBAAA;QACAC,YAAA;QACA1D,UAAA;QACA2D,UAAA;QACAC,QAAA;QACAC,UAAA;QACAC,eAAA;QACAC,SAAA;QACAC,UAAA;QACAtD,WAAA;QACAuD,gBAAA;QACAC,WAAA;QACAlD,YAAA;QACAmD,gBAAA;QACAC,gBAAA;QACAC,gBAAA;QACAC,kBAAA;QACAC,kBAAA;QACAC,mBAAA;QACAC,gBAAA;QACAC,eAAA;QACAC,sBAAA;QACAC,gBAAA;QACAC,KAAA;QACAkN,aAAA;QACAzH,UAAA;QACA0H,WAAA;QACAC,WAAA;QACAC,SAAA;QACAC,eAAA;QACAC,mBAAA;QACAC,sBAAA;QACAC,yBAAA;QACAC,0BAAA;QACAC,iBAAA;QACAC,mBAAA;QACAC,gBAAA;QACArJ,iBAAA,MAAA9I,eAAA,IAAAC,MAAA;QACAmS,kBAAA;QACAC,eAAA;QACA9E,oBAAA;QACAE,gBAAA;QACAzD,UAAA;QACAyB,0BAAA;QACAE,mBAAA;QACAM,kBAAA;QACAL,mBAAA;QACAC,0BAAA;QACAE,wBAAA;QACAC,mBAAA;QACAZ,kBAAA,GACA;UACA1L,YAAA;UACAC,QAAA;UACAC,QAAA;UACAO,WAAA;UACAsL,0BAAA;UACAE,mBAAA;UACAM,kBAAA;UACAL,mBAAA;UACAC,0BAAA;UACAE,wBAAA;UACAC,mBAAA;UACAvL,YAAA;UACAZ,YAAA;UACAC,iBAAA;UACAC,WAAA,MAAAC,eAAA,IAAAC,MAAA;UACAC,aAAA;QACA;MAEA;MACA,KAAAoS,SAAA;IACA;IACA,aACAC,WAAA,WAAAA,YAAA;MACA,KAAA5P,WAAA,CAAAC,OAAA;MACA,KAAA+C,OAAA;IACA;IACA,aACA6M,UAAA,WAAAA,WAAA;MACA,KAAAF,SAAA;MACA;MACA,KAAA3P,WAAA,CAAA4B,UAAA;MACA,KAAAgO,WAAA;IACA;IACAE,kBAAA,WAAAA,mBAAAjH,GAAA;MAAA,IAAAkH,OAAA;MACA,IAAAC,IAAA,GAAAnH,GAAA,CAAAoH,MAAA;MACA,KAAAC,MAAA,CAAAC,OAAA,WAAAH,IAAA,SAAA3L,IAAA;QACA,WAAA+L,uBAAA,EAAAvH,GAAA,CAAAzE,WAAA,EAAAyE,GAAA,CAAAoH,MAAA;MACA,GAAA5L,IAAA;QACA0L,OAAA,CAAAG,MAAA,CAAAG,UAAA,CAAAL,IAAA;MACA,GAAAM,KAAA;QACAzH,GAAA,CAAAoH,MAAA,GAAApH,GAAA,CAAAoH,MAAA;MACA;IACA;IACA;IACAM,qBAAA,WAAAA,sBAAAlD,SAAA;MAAA,IAAAmD,OAAA;MACA;MACA,IAAAC,QAAA,QAAAtN,KAAA,CAAA+B,cAAA,CAAAwL,KAAA,CAAAC,MAAA,CAAA1U,IAAA;;MAEA;MACA,IAAA2U,WAAA,OAAAC,mBAAA,CAAAhH,OAAA,OAAA1L,GAAA;;MAEA;MACA,KAAAA,GAAA;MACA,KAAAO,qBAAA;;MAEA;MACA,KAAAP,GAAA,GAAAkP,SAAA,CAAA3E,GAAA,WAAA9C,IAAA;QAAA,OAAAA,IAAA,CAAAxB,WAAA;MAAA;MACA,KAAA7F,MAAA,GAAA8O,SAAA,CAAA9H,MAAA;MACA,KAAA9G,QAAA,IAAA4O,SAAA,CAAA9H,MAAA;MACA,KAAA7G,qBAAA,GAAA2O,SAAA;;MAEA;MACA,IAAAyD,aAAA,QAAA3S,GAAA,CAAAwN,MAAA,WAAAoF,EAAA;QAAA,QAAAH,WAAA,CAAA/L,QAAA,CAAAkM,EAAA;MAAA;MACA,IAAAC,eAAA,GAAAJ,WAAA,CAAAjF,MAAA,WAAAoF,EAAA;QAAA,QAAAP,OAAA,CAAArS,GAAA,CAAA0G,QAAA,CAAAkM,EAAA;MAAA;;MAEA;MACA1D,SAAA,CAAAtI,OAAA,WAAAa,IAAA;QACA,IAAAA,IAAA,CAAA6I,aAAA,YAAAqC,aAAA,CAAAjM,QAAA,CAAAe,IAAA,CAAAxB,WAAA;UACA;;UAEA;UACA,IAAA6M,UAAA,GAAAR,QAAA,CAAAS,IAAA,WAAAC,IAAA;YAAA,OAAAA,IAAA,CAAA/M,WAAA,KAAAwB,IAAA,CAAAxB,WAAA;UAAA;;UAEA;UACA,IAAA6M,UAAA,IAAAA,UAAA,CAAArM,QAAA,IAAAqM,UAAA,CAAArM,QAAA,CAAAW,MAAA;YACA;YACAT,UAAA;cACAmM,UAAA,CAAArM,QAAA,CAAAG,OAAA,WAAAC,KAAA;gBACA,KAAAwL,OAAA,CAAArS,GAAA,CAAA0G,QAAA,CAAAG,KAAA,CAAAZ,WAAA;kBACAoM,OAAA,CAAArS,GAAA,CAAA8G,IAAA,CAAAD,KAAA,CAAAZ,WAAA;kBACAoM,OAAA,CAAA9R,qBAAA,CAAAuG,IAAA,CAAAD,KAAA;kBACAwL,OAAA,CAAArN,KAAA,CAAA+B,cAAA,CAAAC,kBAAA,CAAAH,KAAA;gBACA;cACA;YACA;UACA,WAAAiM,UAAA,KAAAA,UAAA,CAAAG,cAAA,IAAAH,UAAA,CAAAvC,WAAA;YACA;YACAuC,UAAA,CAAAG,cAAA;;YAEA;YACAZ,OAAA,CAAArN,KAAA,CAAA+B,cAAA,CAAAmM,kBAAA,CAAAJ,UAAA;;YAEA;YACA;UACA;QACA;MACA;;MAEA;MACAD,eAAA,CAAAjM,OAAA,WAAAuM,QAAA;QACA;QACA,IAAAL,UAAA,GAAAR,QAAA,CAAAS,IAAA,WAAAC,IAAA;UAAA,OACAA,IAAA,CAAA/M,WAAA,KAAAkN,QAAA,IAAAH,IAAA,CAAA1C,aAAA;QAAA,CACA;QAEA,IAAAwC,UAAA,IAAAA,UAAA,CAAArM,QAAA,IAAAqM,UAAA,CAAArM,QAAA,CAAAW,MAAA;UACA;UACA0L,UAAA,CAAArM,QAAA,CAAAG,OAAA,WAAAC,KAAA;YACA,IAAAuM,UAAA,GAAAf,OAAA,CAAArS,GAAA,CAAAqT,OAAA,CAAAxM,KAAA,CAAAZ,WAAA;YACA,IAAAmN,UAAA;cACA;cACAf,OAAA,CAAArS,GAAA,CAAAsT,MAAA,CAAAF,UAAA;cACA,IAAAG,SAAA,GAAAlB,OAAA,CAAA9R,qBAAA,CAAAiT,SAAA,CACA,UAAA/L,IAAA;gBAAA,OAAAA,IAAA,CAAAxB,WAAA,KAAAY,KAAA,CAAAZ,WAAA;cAAA,CACA;cACA,IAAAsN,SAAA;gBACAlB,OAAA,CAAA9R,qBAAA,CAAA+S,MAAA,CAAAC,SAAA;cACA;cACA;cACAlB,OAAA,CAAArN,KAAA,CAAA+B,cAAA,CAAAC,kBAAA,CAAAH,KAAA;YACA;UACA;QACA;MACA;IACA;IACA4M,oBAAA,WAAAA,qBAAA;MACA;MACA;MACA;;MAEA,KAAApT,cAAA;IACA;IACA,aACAqT,SAAA,WAAAA,UAAA;MAAA,IAAAC,OAAA;MACA,KAAAzM,KAAA;MACA,KAAAlG,IAAA;MACA,KAAAH,KAAA;MACA,KAAA6C,IAAA,CAAAyN,iBAAA,QAAAlB,MAAA,CAAAC,KAAA,CAAAC,IAAA,CAAAtS,IAAA,CAAA+V,KAAA;MACA,KAAAlQ,IAAA,CAAAsE,iBAAA,OAAAD,IAAA;MACA,KAAArE,IAAA,CAAA1B,eAAA;MACA;MACA;MACA,KAAA6R,SAAA;QACAF,OAAA,CAAAjQ,IAAA,CAAA+M,kBAAA;QACAkD,OAAA,CAAAjQ,IAAA,CAAAkN,WAAA;QACA+C,OAAA,CAAAjQ,IAAA,CAAAuF,UAAA;QACA0K,OAAA,CAAAjQ,IAAA,CAAAiN,WAAA;MACA;IAEA;IACA,aACAmD,YAAA,WAAAA,aAAApJ,GAAA;MAAA,IAAAqJ,OAAA;MACA,KAAA7M,KAAA;MACA,IAAAjB,WAAA,GAAAyE,GAAA,CAAAzE,WAAA,SAAAjG,GAAA;MACA,IAAAgU,uBAAA,EAAA/N,WAAA,EAAAC,IAAA,WAAAX,QAAA;QACAwO,OAAA,CAAArQ,IAAA,GAAA6B,QAAA,CAAAzH,IAAA;QACAiW,OAAA,CAAArQ,IAAA,CAAA4G,kBAAA,GAAA/E,QAAA,CAAAzH,IAAA,CAAAwM,kBAAA,CAAAC,GAAA,WAAA9C,IAAA;UAAA,WAAAwM,cAAA,CAAAvI,OAAA,MAAAuI,cAAA,CAAAvI,OAAA,MACAjE,IAAA;YACA7I,YAAA,EAAA6I,IAAA,CAAA7I,YAAA;YACAC,QAAA,EAAA4I,IAAA,CAAA5I,QAAA;YACAC,QAAA,EAAA2I,IAAA,CAAA3I,QAAA;YACAO,WAAA,EAAAoI,IAAA,CAAApI,WAAA;YACAuL,iBAAA,EAAAnD,IAAA,CAAAmD,iBAAA;YACAD,0BAAA,EAAAoJ,OAAA,CAAArK,YAAA,CAAAjC,IAAA,CAAAmD,iBAAA;YACArL,UAAA,EAAAkI,IAAA,CAAAlI,UAAA;YACAsL,mBAAA,EAAAkJ,OAAA,CAAArK,YAAA,CAAAjC,IAAA,CAAAlI,UAAA;YACAC,SAAA,EAAAiI,IAAA,CAAAjI,SAAA;YACA2L,kBAAA,EAAA4I,OAAA,CAAArK,YAAA,CAAAjC,IAAA,CAAAjI,SAAA;YACAC,UAAA,EAAAgI,IAAA,CAAAhI,UAAA;YACAqL,mBAAA,EAAAiJ,OAAA,CAAArK,YAAA,CAAAjC,IAAA,CAAAhI,UAAA;YACAuL,iBAAA,EAAAvD,IAAA,CAAAuD,iBAAA;YACAD,0BAAA,EAAAgJ,OAAA,CAAArK,YAAA,CAAAjC,IAAA,CAAAuD,iBAAA;YACA1L,eAAA,EAAAmI,IAAA,CAAAnI,eAAA;YACA2L,wBAAA,EAAA8I,OAAA,CAAArK,YAAA,CAAAjC,IAAA,CAAAnI,eAAA;YACAI,UAAA,EAAA+H,IAAA,CAAA/H,UAAA;YACAwL,mBAAA,EAAA6I,OAAA,CAAArK,YAAA,CAAAjC,IAAA,CAAA/H,UAAA;YACAC,YAAA,EAAA8H,IAAA,CAAA9H,YAAA;UAAA;QAAA,CACA;QACA,IAAA4F,QAAA,CAAAzH,IAAA,CAAAwS,aAAA;UACAyD,OAAA,CAAA/S,IAAA;QACA;UACA+S,OAAA,CAAA9S,OAAA;QACA;QAEA8S,OAAA,CAAAlT,KAAA;MACA;IACA;IACA,WACAqT,UAAA,WAAAA,WAAA1I,IAAA;MAAA,IAAA2I,OAAA;MACA,KAAAnP,KAAA,SAAAoP,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA;UACA,IAAAF,OAAA,CAAAzQ,IAAA,CAAApB,UAAA,KAAA6R,OAAA,CAAAzQ,IAAA,CAAApB,UAAA,CAAA2B,UAAA,IAAAD,MAAA,CAAAmQ,OAAA,CAAAzQ,IAAA,CAAA/E,UAAA;YACAwV,OAAA,CAAApC,MAAA,CAAAuC,QAAA,yCAAAtQ,MAAA,CAAAmQ,OAAA,CAAAzQ,IAAA,CAAA/E,UAAA;YACA;UACA;UAEAwV,OAAA,CAAAzQ,IAAA,CAAAzB,WAAA,GAAAkS,OAAA,CAAAzQ,IAAA,CAAAzB,WAAA,OAAA/C,eAAA,EAAAiV,OAAA,CAAAzQ,IAAA,CAAAzB,WAAA,EAAA9C,MAAA;UACAgV,OAAA,CAAAzQ,IAAA,CAAAsE,iBAAA,GAAAmM,OAAA,CAAAzQ,IAAA,CAAAsE,iBAAA,OAAA9I,eAAA,EAAAiV,OAAA,CAAAzQ,IAAA,CAAAsE,iBAAA,EAAA7I,MAAA;;UAEA;UACAgV,OAAA,CAAAzQ,IAAA,CAAAsE,iBAAA,OAAA9I,eAAA,EAAAiV,OAAA,CAAAzQ,IAAA,CAAAsE,iBAAA,EAAA7I,MAAA;UACA,IAAAqM,IAAA;YACA2I,OAAA,CAAAzQ,IAAA,CAAAf,UAAA;YACAwR,OAAA,CAAAzQ,IAAA,CAAAd,gBAAA;YACAuR,OAAA,CAAAzQ,IAAA,CAAAb,WAAA;YACAsR,OAAA,CAAAzQ,IAAA,CAAA4G,kBAAA,CAAA1D,OAAA,WAAAa,IAAA,EAAA4F,KAAA;cACA;cACA8G,OAAA,CAAAzQ,IAAA,CAAAf,UAAA,OAAA6G,iBAAA,EAAA/B,IAAA,CAAA3I,QAAA,OAAAyV,GAAA,CAAAJ,OAAA,CAAAzQ,IAAA,CAAAf,UAAA,EAAAzE,KAAA;cACAiW,OAAA,CAAAzQ,IAAA,CAAAd,gBAAA,OAAA4G,iBAAA,EAAA/B,IAAA,CAAAnI,eAAA,OAAAiV,GAAA,CAAAJ,OAAA,CAAAzQ,IAAA,CAAAd,gBAAA,EAAA1E,KAAA;cACAiW,OAAA,CAAAzQ,IAAA,CAAAb,WAAA,OAAA2G,iBAAA,EAAA/B,IAAA,CAAA/H,UAAA,OAAA6U,GAAA,CAAAJ,OAAA,CAAAzQ,IAAA,CAAAb,WAAA,EAAA3E,KAAA;YACA;UACA;;UAEA;UACA,IAAAsW,IAAA;UACAL,OAAA,CAAAzQ,IAAA,CAAA4G,kBAAA,CAAAC,GAAA,WAAA9C,IAAA;YACA+M,IAAA,CAAA1N,IAAA,CAAAW,IAAA,CAAA7I,YAAA;UACA;UACAuV,OAAA,CAAAzQ,IAAA,CAAAjB,eAAA,GAAA+R,IAAA,CAAAC,IAAA;UAEA,IAAAN,OAAA,CAAAzQ,IAAA,CAAAuC,WAAA;YACAkO,OAAA,CAAAzQ,IAAA,CAAAtB,oBAAA,UAAA+R,OAAA,CAAAzQ,IAAA,CAAAtB,oBAAA,GAAA+R,OAAA,CAAAzQ,IAAA,CAAAsE,iBAAA;YACA,IAAAqH,0BAAA,EAAA8E,OAAA,CAAAzQ,IAAA,EAAAwC,IAAA,WAAAX,QAAA;cACA4O,OAAA,CAAApC,MAAA,CAAAG,UAAA;cACAiC,OAAA,CAAAtP,OAAA;YACA;UACA;YACA,IAAA2G,IAAA;cACA2I,OAAA,CAAAzQ,IAAA,CAAA4M,aAAA;cACA6D,OAAA,CAAAzQ,IAAA,CAAAmE,eAAA;YAEA;YACAsM,OAAA,CAAAzQ,IAAA,CAAAtB,oBAAA,GAAA+R,OAAA,CAAAzQ,IAAA,CAAAsE,iBAAA;YACA,IAAA0M,uBAAA,EAAAP,OAAA,CAAAzQ,IAAA,EAAAwC,IAAA,WAAAX,QAAA;cACA4O,OAAA,CAAAzQ,IAAA,GAAA6B,QAAA,CAAAzH,IAAA;cACAqW,OAAA,CAAApC,MAAA,CAAAG,UAAA;cACAiC,OAAA,CAAAtP,OAAA;YACA;UACA;QACA;MACA;IACA;IACA,aACA8P,YAAA,WAAAA,aAAAjK,GAAA;MAAA,IAAAkK,OAAA;MACA,IAAAC,YAAA,GAAAnK,GAAA,CAAAzE,WAAA,SAAAjG,GAAA;MACA,KAAA+R,MAAA,CAAAC,OAAA,mBAAA6C,YAAA,cAAA3O,IAAA;QACA,WAAA4O,uBAAA,EAAAD,YAAA;MACA,GAAA3O,IAAA;QACA0O,OAAA,CAAA/P,OAAA;QACA+P,OAAA,CAAA7C,MAAA,CAAAG,UAAA;MACA,GAAAC,KAAA,cACA;IACA;IACA,aACA4C,YAAA,WAAAA,aAAA;MACA,KAAA5T,MAAA,CAAAN,KAAA;MACA,KAAAM,MAAA,CAAAH,IAAA;IACA;IACA,aACAgU,YAAA,WAAAA,aAAA;MAEA,KAAApP,QAAA,gCAAAqO,cAAA,CAAAvI,OAAA,MAAAuI,cAAA,CAAAvI,OAAA,MACA,KAAA7J,WAAA;QAAAE,QAAA;MAAA,iBAAAiC,MAAA,CACA,IAAA+D,IAAA,GAAAkN,OAAA;IACA;EACA;AACA;AAAAC,OAAA,CAAAxJ,OAAA,GAAA9N,QAAA"}]}