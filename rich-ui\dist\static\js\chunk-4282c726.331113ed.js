(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-4282c726","chunk-2d0d69a4"],{4678:function(e,t,o){var n={"./af":"2bfb","./af.js":"2bfb","./ar":"8e73","./ar-dz":"a356","./ar-dz.js":"a356","./ar-kw":"423e","./ar-kw.js":"423e","./ar-ly":"1cfd","./ar-ly.js":"1cfd","./ar-ma":"0a84","./ar-ma.js":"0a84","./ar-ps":"4c98","./ar-ps.js":"4c98","./ar-sa":"8230","./ar-sa.js":"8230","./ar-tn":"6d83","./ar-tn.js":"6d83","./ar.js":"8e73","./az":"485c","./az.js":"485c","./be":"1fc1","./be.js":"1fc1","./bg":"84aa","./bg.js":"84aa","./bm":"a7fa","./bm.js":"a7fa","./bn":"9043","./bn-bd":"9686","./bn-bd.js":"9686","./bn.js":"9043","./bo":"d26a","./bo.js":"d26a","./br":"6887","./br.js":"6887","./bs":"25548","./bs.js":"25548","./ca":"d716","./ca.js":"d716","./cs":"3c0d","./cs.js":"3c0d","./cv":"03ec","./cv.js":"03ec","./cy":"9797","./cy.js":"9797","./da":"0f14","./da.js":"0f14","./de":"b469","./de-at":"b3eb","./de-at.js":"b3eb","./de-ch":"bb71","./de-ch.js":"bb71","./de.js":"b469","./dv":"598a","./dv.js":"598a","./el":"8d47","./el.js":"8d47","./en-au":"0e6b","./en-au.js":"0e6b","./en-ca":"3886","./en-ca.js":"3886","./en-gb":"39a6","./en-gb.js":"39a6","./en-ie":"e1d3","./en-ie.js":"e1d3","./en-il":"7333","./en-il.js":"7333","./en-in":"ec2e","./en-in.js":"ec2e","./en-nz":"6f50","./en-nz.js":"6f50","./en-sg":"b7e9","./en-sg.js":"b7e9","./eo":"65db","./eo.js":"65db","./es":"898b","./es-do":"0a3c","./es-do.js":"0a3c","./es-mx":"b5b7","./es-mx.js":"b5b7","./es-us":"55c9","./es-us.js":"55c9","./es.js":"898b","./et":"ec18","./et.js":"ec18","./eu":"0ff2","./eu.js":"0ff2","./fa":"8df48","./fa.js":"8df48","./fi":"81e9","./fi.js":"81e9","./fil":"d69a","./fil.js":"d69a","./fo":"0721","./fo.js":"0721","./fr":"9f26","./fr-ca":"d9f8","./fr-ca.js":"d9f8","./fr-ch":"0e49","./fr-ch.js":"0e49","./fr.js":"9f26","./fy":"7118","./fy.js":"7118","./ga":"5120","./ga.js":"5120","./gd":"f6b46","./gd.js":"f6b46","./gl":"8840","./gl.js":"8840","./gom-deva":"aaf2","./gom-deva.js":"aaf2","./gom-latn":"0caa","./gom-latn.js":"0caa","./gu":"e0c5","./gu.js":"e0c5","./he":"c7aa","./he.js":"c7aa","./hi":"dc4d","./hi.js":"dc4d","./hr":"4ba9","./hr.js":"4ba9","./hu":"5b14","./hu.js":"5b14","./hy-am":"d6b6","./hy-am.js":"d6b6","./id":"5038","./id.js":"5038","./is":"0558","./is.js":"0558","./it":"6e98","./it-ch":"6f12","./it-ch.js":"6f12","./it.js":"6e98","./ja":"079e","./ja.js":"079e","./jv":"b540","./jv.js":"b540","./ka":"201b","./ka.js":"201b","./kk":"6d79","./kk.js":"6d79","./km":"e81d","./km.js":"e81d","./kn":"3e92","./kn.js":"3e92","./ko":"22f8","./ko.js":"22f8","./ku":"2421","./ku-kmr":"7558","./ku-kmr.js":"7558","./ku.js":"2421","./ky":"9609","./ky.js":"9609","./lb":"440c","./lb.js":"440c","./lo":"b29d","./lo.js":"b29d","./lt":"26f9","./lt.js":"26f9","./lv":"b97c","./lv.js":"b97c","./me":"293c","./me.js":"293c","./mi":"688b","./mi.js":"688b","./mk":"6909","./mk.js":"6909","./ml":"02fb","./ml.js":"02fb","./mn":"958b","./mn.js":"958b","./mr":"39bd","./mr.js":"39bd","./ms":"ebe4","./ms-my":"6403","./ms-my.js":"6403","./ms.js":"ebe4","./mt":"1b45","./mt.js":"1b45","./my":"8689","./my.js":"8689","./nb":"6ce3","./nb.js":"6ce3","./ne":"3a39","./ne.js":"3a39","./nl":"facd","./nl-be":"db29","./nl-be.js":"db29","./nl.js":"facd","./nn":"b84c","./nn.js":"b84c","./oc-lnc":"167b","./oc-lnc.js":"167b","./pa-in":"f3ff","./pa-in.js":"f3ff","./pl":"8d57","./pl.js":"8d57","./pt":"f260","./pt-br":"d2d4","./pt-br.js":"d2d4","./pt.js":"f260","./ro":"972c","./ro.js":"972c","./ru":"957c","./ru.js":"957c","./sd":"6784","./sd.js":"6784","./se":"ffff","./se.js":"ffff","./si":"eda5","./si.js":"eda5","./sk":"7be6","./sk.js":"7be6","./sl":"8155","./sl.js":"8155","./sq":"c8f3","./sq.js":"c8f3","./sr":"cf1e","./sr-cyrl":"13e9","./sr-cyrl.js":"13e9","./sr.js":"cf1e","./ss":"52bd","./ss.js":"52bd","./sv":"5fbd","./sv.js":"5fbd","./sw":"74dc","./sw.js":"74dc","./ta":"3de5","./ta.js":"3de5","./te":"5cbb","./te.js":"5cbb","./tet":"576c","./tet.js":"576c","./tg":"3b1b","./tg.js":"3b1b","./th":"10e8","./th.js":"10e8","./tk":"5aff","./tk.js":"5aff","./tl-ph":"0f38","./tl-ph.js":"0f38","./tlh":"cf75","./tlh.js":"cf75","./tr":"0e81","./tr.js":"0e81","./tzl":"cf51","./tzl.js":"cf51","./tzm":"c109","./tzm-latn":"b53d","./tzm-latn.js":"b53d","./tzm.js":"c109","./ug-cn":"6117","./ug-cn.js":"6117","./uk":"ada2","./uk.js":"ada2","./ur":"5294","./ur.js":"5294","./uz":"2e8c","./uz-latn":"010e","./uz-latn.js":"010e","./uz.js":"2e8c","./vi":"2921","./vi.js":"2921","./x-pseudo":"fd7e","./x-pseudo.js":"fd7e","./yo":"7f33","./yo.js":"7f33","./zh-cn":"5c3a","./zh-cn.js":"5c3a","./zh-hk":"49ab","./zh-hk.js":"49ab","./zh-mo":"3a6c","./zh-mo.js":"3a6c","./zh-tw":"90ea","./zh-tw.js":"90ea"};function r(e){var t=a(e);return o(t)}function a(e){if(!o.o(n,e)){var t=new Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}return n[e]}r.keys=function(){return Object.keys(n)},r.resolve=a,e.exports=r,r.id="4678"},"5fb3":function(e,t,o){"use strict";o.d(t,"h",(function(){return r})),o.d(t,"g",(function(){return a})),o.d(t,"i",(function(){return u})),o.d(t,"e",(function(){return l})),o.d(t,"f",(function(){return s})),o.d(t,"a",(function(){return i})),o.d(t,"n",(function(){return d})),o.d(t,"d",(function(){return c})),o.d(t,"c",(function(){return b})),o.d(t,"j",(function(){return m})),o.d(t,"m",(function(){return p})),o.d(t,"l",(function(){return f})),o.d(t,"k",(function(){return h})),o.d(t,"b",(function(){return g}));var n=o("b775");function r(e){return Object(n["a"])({url:"/system/inventory/list",method:"get",params:e})}function a(e){return Object(n["a"])({url:"/system/inventory/aggregator",method:"get",params:e})}function u(e){return Object(n["a"])({url:"/system/inventory/lists",method:"get",params:e})}function l(e){return Object(n["a"])({url:"/system/inventory/"+e,method:"get"})}function s(e){return Object(n["a"])({url:"/system/inventory/package",method:"get",params:e})}function i(e){return Object(n["a"])({url:"/system/inventory",method:"post",data:e})}function d(e){return Object(n["a"])({url:"/system/inventory",method:"put",data:e})}function c(e){return Object(n["a"])({url:"/system/inventory/"+e,method:"delete"})}function b(e,t){var o={inventoryId:e,status:t};return Object(n["a"])({url:"/system/inventory/changeStatus",method:"put",data:o})}function m(e){return Object(n["a"])({url:"/system/inventory/outbound",method:"put",data:e})}function p(e){return Object(n["a"])({url:"/system/inventory/settlement",method:"put",data:e})}function f(e){return Object(n["a"])({url:"/system/inventory/preOutbound",method:"put",data:e})}function h(e){return Object(n["a"])({url:"/system/inventory/packUp",method:"put",data:e})}function g(e){return Object(n["a"])({url:"/system/inventory/cancelPkg",method:"put",data:e})}},"72f9":function(e,t,o){(function(t,o){e.exports=o()})(0,(function(){function e(a,u){if(!(this instanceof e))return new e(a,u);u=Object.assign({},o,u);var l=Math.pow(10,u.precision);this.intValue=a=t(a,u),this.value=a/l,u.increment=u.increment||1/l,u.groups=u.useVedic?r:n,this.s=u,this.p=l}function t(t,o){var n=!(2<arguments.length&&void 0!==arguments[2])||arguments[2],r=o.decimal,a=o.errorOnInvalid,u=o.fromCents,l=Math.pow(10,o.precision),s=t instanceof e;if(s&&u)return t.intValue;if("number"===typeof t||s)r=s?t.value:t;else if("string"===typeof t)a=new RegExp("[^-\\d"+r+"]","g"),r=new RegExp("\\"+r,"g"),r=(r=t.replace(/\((.*)\)/,"-$1").replace(a,"").replace(r,"."))||0;else{if(a)throw Error("Invalid Input");r=0}return u||(r=(r*l).toFixed(4)),n?Math.round(r):r}var o={symbol:"$",separator:",",decimal:".",errorOnInvalid:!1,precision:2,pattern:"!#",negativePattern:"-!#",format:function(e,t){var o=t.pattern,n=t.negativePattern,r=t.symbol,a=t.separator,u=t.decimal;t=t.groups;var l=(""+e).replace(/^-/,"").split("."),s=l[0];return l=l[1],(0<=e.value?o:n).replace("!",r).replace("#",s.replace(t,"$1"+a)+(l?u+l:""))},fromCents:!1},n=/(\d)(?=(\d{3})+\b)/g,r=/(\d)(?=(\d\d)+\d\b)/g;return e.prototype={add:function(o){var n=this.s,r=this.p;return e((this.intValue+t(o,n))/(n.fromCents?1:r),n)},subtract:function(o){var n=this.s,r=this.p;return e((this.intValue-t(o,n))/(n.fromCents?1:r),n)},multiply:function(t){var o=this.s;return e(this.intValue*t/(o.fromCents?1:Math.pow(10,o.precision)),o)},divide:function(o){var n=this.s;return e(this.intValue/t(o,n,!1),n)},distribute:function(t){var o=this.intValue,n=this.p,r=this.s,a=[],u=Math[0<=o?"floor":"ceil"](o/t),l=Math.abs(o-u*t);for(n=r.fromCents?1:n;0!==t;t--){var s=e(u/n,r);0<l--&&(s=s[0<=o?"add":"subtract"](1/n)),a.push(s)}return a},dollars:function(){return~~this.value},cents:function(){return~~(this.intValue%this.p)},format:function(e){var t=this.s;return"function"===typeof e?e(this,t):t.format(this,Object.assign({},t,e))},toString:function(){var e=this.s,t=e.increment;return(Math.round(this.intValue/this.p/t)*t).toFixed(e.precision)},toJSON:function(){return this.value}},e}))},7478:function(e,t,o){"use strict";o("8423")},"82ad":function(e,t,o){"use strict";o.d(t,"h",(function(){return r})),o.d(t,"e",(function(){return a})),o.d(t,"a",(function(){return u})),o.d(t,"k",(function(){return l})),o.d(t,"c",(function(){return s})),o.d(t,"b",(function(){return i})),o.d(t,"i",(function(){return d})),o.d(t,"j",(function(){return c})),o.d(t,"f",(function(){return b})),o.d(t,"g",(function(){return m})),o.d(t,"d",(function(){return p}));var n=o("b775");function r(e){return Object(n["a"])({url:"/system/outboundrecord/list",method:"get",params:e})}function a(e){return Object(n["a"])({url:"/system/outboundrecord/"+e,method:"get"})}function u(e){return Object(n["a"])({url:"/system/outboundrecord",method:"post",data:e})}function l(e){return Object(n["a"])({url:"/system/outboundrecord",method:"put",data:e})}function s(e){return Object(n["a"])({url:"/system/outboundrecord/"+e,method:"delete"})}function i(e,t){var o={outboundRecordId:e,status:t};return Object(n["a"])({url:"/system/outboundrecord/changeStatus",method:"put",data:o})}function d(e){return Object(n["a"])({url:"/system/outboundrecord/outboundrecords",method:"get",params:e})}function c(e){return Object(n["a"])({url:"/system/outboundrecord/listRental",method:"get",params:e})}function b(e){return Object(n["a"])({url:"/system/outboundrecord/outboundrecords/"+e,method:"get"})}function m(e){return Object(n["a"])({url:"/system/outboundrecord/rentals/"+e,method:"get"})}function p(e){return Object(n["a"])({url:"/system/outboundrecord/outboundBill",method:"put",data:e,responseType:"arraybuffer"})}},8423:function(e,t,o){},9129:function(e,t,o){var n=o("23e7");n({target:"Number",stat:!0},{isNaN:function(e){return e!=e}})},f74a:function(e,t,o){"use strict";o.r(t);var n=function(){var e=this,t=e.$createElement,o=e._self._c||t;return o("div",{staticClass:"app-container"},[o("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"},{name:"dialogDragWidth",rawName:"v-dialogDragWidth"}],attrs:{"close-on-click-modal":!1,"modal-append-to-body":!1,title:"出库单",visible:e.dialogVisible,"append-to-body":"",width:"70%"},on:{open:e.outboundOpen,"update:visible":function(t){e.dialogVisible=t}},scopedSlots:e._u([{key:"footer",fn:function(){return[o("span",{staticClass:"dialog-footer"},[0===e.outboundType?o("el-button",{attrs:{type:"primary"},on:{click:function(t){return e.outboundConfirm(0)}}},[e._v("确定预出仓")]):e._e(),o("el-button",{on:{click:e.closeOutbound}},[e._v("关 闭")])],1)]},proxy:!0}])},[o("el-form",{ref:"outboundForm",staticClass:"edit",attrs:{model:e.outboundForm,rules:e.rules,"label-width":"80px"}},[o("el-row",{attrs:{gutter:10}},[o("el-row",{attrs:{gutter:10}},[o("el-col",[o("el-col",{attrs:{span:6}},[o("el-form-item",{attrs:{label:"出仓单号",prop:"outboundNo"}},[o("el-input",{staticClass:"disable-form",attrs:{disabled:"",placeholder:"出仓单号"},model:{value:e.outboundForm.outboundNo,callback:function(t){e.$set(e.outboundForm,"outboundNo",t)},expression:"outboundForm.outboundNo"}})],1)],1),o("el-col",{attrs:{span:6}},[o("el-form-item",{attrs:{label:"客户单号",prop:"outboundNo"}},[o("el-input",{attrs:{placeholder:"客户单号"},model:{value:e.outboundForm.customerOrderNo,callback:function(t){e.$set(e.outboundForm,"customerOrderNo",t)},expression:"outboundForm.customerOrderNo"}})],1)],1),o("el-col",{attrs:{span:6}},[o("el-form-item",{attrs:{label:"客户代码",prop:"outboundNo"}},[o("tree-select",{attrs:{flat:!1,multiple:!1,pass:e.outboundForm.clientCode,placeholder:"客户代码",type:"warehouseClient"},on:{return:function(t){e.outboundForm.clientCode=t},returnData:function(t){return e.outboundClient(t)}}})],1)],1),o("el-col",{attrs:{span:6}},[o("el-form-item",{attrs:{label:"客户名称",prop:"outboundNo"}},[o("el-input",{staticClass:"disable-form",attrs:{disabled:"",placeholder:"客户名称"},model:{value:e.outboundForm.clientName,callback:function(t){e.$set(e.outboundForm,"clientName",t)},expression:"outboundForm.clientName"}})],1)],1)],1)],1),o("el-row",{attrs:{gutter:10}},[o("el-col",[o("el-col",{attrs:{span:6}},[o("el-form-item",{attrs:{label:"计划出仓",prop:"inboundDate"}},[o("el-date-picker",{staticStyle:{width:"100%"},attrs:{clearable:"",placeholder:"计划出仓日期",type:"date","value-format":"yyyy-MM-dd"},model:{value:e.outboundForm.plannedOutboundDate,callback:function(t){e.$set(e.outboundForm,"plannedOutboundDate",t)},expression:"outboundForm.plannedOutboundDate"}})],1)],1),o("el-col",{attrs:{span:6}},[o("el-form-item",{attrs:{label:"出仓方式",prop:"outboundType"}},[o("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"出仓方式"},model:{value:e.outboundForm.outboundType,callback:function(t){e.$set(e.outboundForm,"outboundType",t)},expression:"outboundForm.outboundType"}},[o("el-option",{attrs:{label:"整柜",value:"整柜"}}),o("el-option",{attrs:{label:"散货",value:"散货"}}),o("el-option",{attrs:{label:"快递",value:"快递"}}),o("el-option",{attrs:{label:"其他",value:"其他"}})],1)],1)],1),o("el-col",{attrs:{span:6}},[o("el-form-item",{attrs:{label:"柜型",prop:"containerType"}},[o("el-select",{staticStyle:{width:"100%"},on:{change:e.selectContainerType},model:{value:e.outboundForm.containerType,callback:function(t){e.$set(e.outboundForm,"containerType",t)},expression:"outboundForm.containerType"}},[o("el-option",{attrs:{label:"20GP",value:"20GP"}}),o("el-option",{attrs:{label:"20OT",value:"20OT"}}),o("el-option",{attrs:{label:"20FR",value:"20FR"}}),o("el-option",{attrs:{label:"TANK",value:"TANK"}}),o("el-option",{attrs:{label:"40GP",value:"40GP"}}),o("el-option",{attrs:{label:"40HQ",value:"40HQ"}}),o("el-option",{attrs:{label:"40NOR",value:"40NOR"}}),o("el-option",{attrs:{label:"40OT",value:"40OT"}}),o("el-option",{attrs:{label:"40FR",value:"40FR"}}),o("el-option",{attrs:{label:"40RH",value:"40RH"}}),o("el-option",{attrs:{label:"45HQ",value:"45HQ"}})],1)],1)],1),o("el-col",{attrs:{span:6}},[o("el-form-item",{attrs:{label:"货物类型",prop:"cargoType"}},[o("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"请选择货物类型"},model:{value:e.form.cargoType,callback:function(t){e.$set(e.form,"cargoType",t)},expression:"form.cargoType"}},[o("el-option",{attrs:{label:"普货",value:"普货"}}),o("el-option",{attrs:{label:"大件",value:"大件"}}),o("el-option",{attrs:{label:"鲜活",value:"鲜活"}}),o("el-option",{attrs:{label:"危品",value:"危品"}}),o("el-option",{attrs:{label:"冷冻",value:"冷冻"}}),o("el-option",{attrs:{label:"标记",value:"标记"}})],1)],1)],1)],1)],1),o("el-row",{attrs:{gutter:10}},[o("el-col",[o("el-col",{attrs:{span:6}},[o("el-form-item",{attrs:{label:"柜号",prop:"containerNo"}},[o("el-input",{attrs:{placeholder:"柜号"},model:{value:e.outboundForm.containerNo,callback:function(t){e.$set(e.outboundForm,"containerNo",t)},expression:"outboundForm.containerNo"}})],1)],1),o("el-col",{attrs:{span:6}},[o("el-form-item",{attrs:{label:"封号",prop:"sealNo"}},[o("el-input",{attrs:{placeholder:"封号"},model:{value:e.outboundForm.sealNo,callback:function(t){e.$set(e.outboundForm,"sealNo",t)},expression:"outboundForm.sealNo"}})],1)],1),o("el-col",{attrs:{span:6}},[o("el-form-item",{attrs:{label:"车牌",prop:"plateNumber"}},[o("el-input",{attrs:{placeholder:"车牌"},model:{value:e.outboundForm.plateNumber,callback:function(t){e.$set(e.outboundForm,"plateNumber",t)},expression:"outboundForm.plateNumber"}})],1)],1),o("el-col",{attrs:{span:6}},[o("el-form-item",{attrs:{label:"司机电话",prop:"driverPhone"}},[o("el-input",{attrs:{placeholder:"司机电话"},model:{value:e.outboundForm.driverPhone,callback:function(t){e.$set(e.outboundForm,"driverPhone",t)},expression:"outboundForm.driverPhone"}})],1)],1)],1)],1),o("el-row",{attrs:{gutter:10}},[o("el-col",[o("el-col",{attrs:{span:6}},[o("el-form-item",{attrs:{label:"仓库报价",prop:"warehouseQuote"}},[o("el-input",{staticClass:"number",attrs:{placeholder:"仓库报价"},model:{value:e.outboundForm.warehouseQuote,callback:function(t){e.$set(e.outboundForm,"warehouseQuote",t)},expression:"outboundForm.warehouseQuote"}})],1)],1),o("el-col",{attrs:{span:6}},[o("el-form-item",{attrs:{label:"仓管代收",prop:"outboundNotes"}},[o("el-input",{staticClass:"number",attrs:{placeholder:"仓管代收"},model:{value:e.outboundForm.warehouseCollection,callback:function(t){e.$set(e.outboundForm,"warehouseCollection",t)},expression:"outboundForm.warehouseCollection"}})],1)],1),o("el-col",{attrs:{span:6}},[o("el-form-item",{attrs:{label:"工人装柜费",prop:"workerLoadingFee"}},[o("el-input",{staticClass:"number",attrs:{placeholder:"工人装柜费"},model:{value:e.outboundForm.workerLoadingFee,callback:function(t){e.$set(e.outboundForm,"workerLoadingFee",t)},expression:"outboundForm.workerLoadingFee"}})],1)],1),o("el-col",{attrs:{span:6}},[o("el-form-item",{attrs:{label:"仓管代付",prop:"outboundNotes"}},[o("el-input",{staticClass:"number",attrs:{placeholder:"仓管代收"},model:{value:e.outboundForm.warehousePay,callback:function(t){e.$set(e.outboundForm,"warehousePay",t)},expression:"outboundForm.warehousePay"}})],1)],1)],1)],1),o("el-row",{attrs:{gutter:10}},[o("el-col",[o("el-col",{attrs:{span:12}},[o("el-form-item",{attrs:{label:"操作要求",prop:"operationRequirement"}},[o("el-input",{attrs:{autosize:{minRows:4},maxlength:"250",placeholder:"内容","show-word-limit":"",type:"textarea"},model:{value:e.outboundForm.operationRequirement,callback:function(t){e.$set(e.outboundForm,"operationRequirement",t)},expression:"outboundForm.operationRequirement"}})],1)],1),o("el-col",{attrs:{span:12}},[o("el-form-item",{attrs:{label:"仓管指示",prop:"outboundNote"}},[o("el-input",{attrs:{autosize:{minRows:4},maxlength:"250",placeholder:"内容","show-word-limit":"",type:"textarea"},model:{value:e.outboundForm.outboundNote,callback:function(t){e.$set(e.outboundForm,"outboundNote",t)},expression:"outboundForm.outboundNote"}})],1)],1)],1)],1),o("el-row",{attrs:{gutter:10}},[o("el-col",[o("el-col",{attrs:{span:6}},[o("el-form-item",{attrs:{label:"操作员",prop:"operator"}},[o("el-input",{staticClass:"disable-form",attrs:{disabled:"",placeholder:"操作员"},model:{value:e.outboundForm.operator,callback:function(t){e.$set(e.outboundForm,"operator",t)},expression:"outboundForm.operator"}})],1)],1),o("el-col",{attrs:{span:6}},[o("el-form-item",{attrs:{label:"下单日期",prop:"orderDate"}},[o("el-date-picker",{staticClass:"disable-form",staticStyle:{width:"100%"},attrs:{clearable:"",disabled:"",placeholder:"出仓日期",type:"date","value-format":"yyyy-MM-dd"},model:{value:e.outboundForm.orderDate,callback:function(t){e.$set(e.outboundForm,"orderDate",t)},expression:"outboundForm.orderDate"}})],1)],1),o("el-col",{attrs:{span:6}},[o("el-form-item",{attrs:{label:"出仓经手人",prop:"outboundHandler"}},[o("el-input",{staticClass:"disable-form",attrs:{disabled:"",placeholder:"出仓经手人"},model:{value:e.outboundForm.outboundHandler,callback:function(t){e.$set(e.outboundForm,"outboundHandler",t)},expression:"outboundForm.outboundHandler"}})],1)],1),o("el-col",{attrs:{span:6}},[o("el-row",[o("el-col",{attrs:{span:12}},[o("el-button",{attrs:{disabled:null===e.outboundForm.clientCode,type:"primary"},on:{click:e.warehouseConfirm}},[e._v(" "+e._s("仓管确认")+" ")])],1),o("el-col",{attrs:{span:12}},[o("el-button",{attrs:{disabled:null===e.outboundForm.clientCode,type:"primary"},on:{click:e.loadPreOutboundInventoryList}},[e._v(" "+e._s("加载待出库")+" ")])],1)],1)],1)],1)],1)],1),o("el-row",{attrs:{gutter:10}},[o("el-col",[o("el-col",[o("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.preOutboundInventoryListLoading,expression:"preOutboundInventoryListLoading"}],ref:"table",staticStyle:{width:"100%"},attrs:{data:e.preOutboundInventoryList,load:e.loadChildInventory,"summary-method":e.getSummaries,"tree-props":{children:"children",hasChildren:"hasChildren"},lazy:"","max-height":"300","row-key":"inventoryId","show-summary":""},on:{"selection-change":e.handleOutboundSelectionChange}},[o("el-table-column",{attrs:{align:"center",fixed:"",type:"selection",width:"28"}}),o("el-table-column",{attrs:{align:"center",fixed:"",label:"序号",type:"index",width:"28"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(t.$index+1)+" ")]}}])}),o("el-table-column",{attrs:{align:"center",label:"入仓流水号",prop:"inboundSerialNo",width:"120"},scopedSlots:e._u([{key:"header",fn:function(){return[o("el-input",{attrs:{clearable:"",placeholder:"输入流水号搜索",size:"mini"},on:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleSearchEnter(t)}},model:{value:e.search,callback:function(t){e.search=t},expression:"search"}})]},proxy:!0}])}),o("el-table-column",{attrs:{align:"center",label:"部分出库",prop:"inboundDate",width:"50"},scopedSlots:e._u([{key:"default",fn:function(t){return[o("el-switch",{model:{value:t.row.partialOutboundFlag,callback:function(o){e.$set(t.row,"partialOutboundFlag",o)},expression:"scope.row.partialOutboundFlag"}})]}}])}),o("el-table-column",{attrs:{align:"center",label:"货物明细"},scopedSlots:e._u([{key:"default",fn:function(t){return[o("el-popover",{attrs:{disabled:0==t.row.partialOutboundFlag,trigger:"click",width:"800"},scopedSlots:e._u([{key:"reference",fn:function(){return[o("el-button",{staticStyle:{margin:"0",padding:"5px"}},[e._v("查看")])]},proxy:!0}],null,!0)},[o("el-table",{attrs:{data:t.row.rsCargoDetailsList},on:{"selection-change":function(o){return e.handleOutboundCargoDetailSelectionChange(o,t.row)}}},[o("el-table-column",{attrs:{align:"center",type:"selection",width:"28"}}),o("el-table-column",{attrs:{label:"唛头",prop:"shippingMark",width:"150"}}),o("el-table-column",{attrs:{label:"货名",prop:"itemName",width:"150"}}),o("el-table-column",{attrs:{label:"箱数",prop:"boxCount"},scopedSlots:e._u([{key:"default",fn:function(t){return[o("el-input",{attrs:{disabled:!e.isRowSelected(t.row)},model:{value:t.row.boxCount,callback:function(o){e.$set(t.row,"boxCount",o)},expression:"scope.row.boxCount"}})]}}],null,!0)}),o("el-table-column",{attrs:{label:"包装类型",prop:"packageType"}}),o("el-table-column",{attrs:{label:"单件长",prop:"unitLength"}}),o("el-table-column",{attrs:{label:"单件宽",prop:"unitWidth"}}),o("el-table-column",{attrs:{label:"单件高",prop:"unitHeight"}}),o("el-table-column",{attrs:{label:"体积小计",prop:"unitVolume"}}),o("el-table-column",{attrs:{label:"毛重小计",prop:"unitGrossWeight"}}),o("el-table-column",{attrs:{label:"破损标志",prop:"damageStatus"}})],1)],1)]}}])}),o("el-table-column",{attrs:{align:"center",label:"最新计租日",prop:"inboundDate",width:"80"},scopedSlots:e._u([{key:"default",fn:function(t){return[o("span",[e._v(e._s(e.parseTime(t.row.rentalSettlementDate,"{y}-{m}-{d}")))])]}}])}),o("el-table-column",{attrs:{align:"center",label:"货代单号",prop:"forwarderNo","show-overflow-tooltip":""}}),o("el-table-column",{attrs:{align:"center",label:"客户代码",prop:"clientCode"}}),o("el-table-column",{attrs:{align:"center",label:"箱数",prop:"totalBoxes"}}),o("el-table-column",{attrs:{align:"center",label:"毛重",prop:"totalGrossWeight"}}),o("el-table-column",{attrs:{align:"center",label:"体积",prop:"totalVolume"}}),o("el-table-column",{attrs:{align:"center",label:"已收供应商",prop:"receivedSupplier"}}),o("el-table-column",{attrs:{align:"center",label:"已收入仓费",prop:"receivedStorageFee"}}),o("el-table-column",{attrs:{align:"center",label:"补收入仓费",prop:"additionalStorageFee"}}),o("el-table-column",{attrs:{align:"center",label:"补收卸货费",prop:"unpaidUnloadingFee"}}),o("el-table-column",{attrs:{align:"center",label:"应付卸货费",prop:"receivedUnloadingFee"}}),o("el-table-column",{attrs:{align:"center",label:"补收打包费",prop:"unpaidPackingFee"}}),o("el-table-column",{attrs:{align:"center",label:"应付打包费",prop:"receivedPackingFee"}}),o("el-table-column",{attrs:{align:"center",label:"物流代垫费",prop:"logisticsAdvanceFee"}}),o("el-table-column",{attrs:{align:"center",label:"免堆期",prop:"freeStackPeriod"}}),o("el-table-column",{attrs:{align:"center",label:"超期租金单价",prop:"overdueRentalUnitPrice"}}),o("el-table-column",{attrs:{align:"center",label:"超租天数",prop:"rentalDays"}}),o("el-table-column",{attrs:{align:"center",label:"超期租金",prop:"overdueRentalFee"}})],1)],1)],1)],1),o("el-row",[o("el-col",{attrs:{span:4}},[o("span",[e._v("未收客户："+e._s(e.outboundForm.unreceivedFromCustomer))])]),o("el-col",{attrs:{span:4}},[o("span",[e._v("已收客户："+e._s(e.outboundForm.receivedFromCustomer))])]),o("el-col",{attrs:{span:4}},[o("span",[e._v("应收客户余额："+e._s(e.outboundForm.customerReceivableBalance))])]),o("el-col",{attrs:{span:4}},[o("span",[e._v("应付工人："+e._s(e.outboundForm.payableToWorker))])])],1),o("el-row",[o("el-col",{attrs:{span:4}},[o("span",[e._v("本票销售额："+e._s(e.outboundForm.promissoryNoteSales))])]),o("el-col",{attrs:{span:4}},[o("span",[e._v("本票成本："+e._s(e.outboundForm.promissoryNoteCost))])]),o("el-col",{attrs:{span:4}},[o("span",[e._v("本票结余："+e._s(e.outboundForm.promissoryNoteGrossProfit))])]),o("el-col",{attrs:{span:4}},[o("span",[e._v("已收供应商总额："+e._s(e.outboundForm.receivedFromSupplier))])])],1)],1)],1)],1)},r=[],a=o("5530"),u=o("2909"),l=(o("b64b"),o("e9c4"),o("caad"),o("2532"),o("d3b7"),o("159b"),o("14d9"),o("b0c0"),o("d81d"),o("a9e3"),o("4de4"),o("13d5"),o("9129"),o("7db0"),o("a434"),o("c740"),o("ac1f"),o("841c"),o("82ad")),s=o("fba1"),i=o("5fb3"),d=o("c1df"),c=o.n(d),b=o("72f9"),m=o.n(b),p={name:"OutboundPlan",props:["openOutbound","outboundData","outboundFormProp"],data:function(){return{dialogVisible:!1,showLeft:0,showRight:24,loading:!0,selectOutboundList:[],ids:[],single:!0,multiple:!0,showSearch:!1,total:0,outboundrecordList:[],title:"",open:!1,queryParams:{pageNum:1,pageSize:20,outboundNo:null,clientCode:null,clientName:null,operator:null,containerType:null,containerNo:null,sealNo:null,outboundDate:null,warehouseQuote:null,workerLoadingFee:null,warehouseCollection:null,collectionNotes:null,totalBoxes:null,totalGrossWeight:null,totalVolume:null,totalRows:null,receivedStorageFee:null,unpaidUnloadingFee:null,unpaidPackagingFee:null,logisticsAdvanceFee:null,rentalBalanceFee:null,overdueRent:null,freeStackDays:null,overdueUnitPrice:null},form:{},outboundType:0,preOutboundInventoryListLoading:!1,search:null,rules:{clientCode:[{required:!0,message:"客户代码不能为空",trigger:"blur"}]},outboundForm:{outboundDate:c()().format("yyyy-MM-DD")},clientRow:{},preOutboundInventoryList:[],selectedCargoDetail:[]}},watch:{showSearch:function(e){!0===e?(this.showRight=21,this.showLeft=3):(this.showRight=24,this.showLeft=0)},openOutbound:function(e){this.dialogVisible=e},dialogVisible:function(e){e||this.$emit("closeOutbound")}},created:function(){this.dialogVisible=this.openOutbound},methods:{warehouseConfirm:function(){},outboundOpen:function(){this.outboundFormProp?this.outboundForm=JSON.parse(JSON.stringify(this.outboundFormProp)):this.outboundData&&(this.outboundForm=JSON.parse(JSON.stringify(this.outboundData)))},closeOutbound:function(){this.dialogVisible=!1},loadChildInventory:function(e,t,o){var n=this;Object(i["h"])({packageTo:e.inventoryId}).then((function(t){var r=t.rows;o(r),e.children=r,n.ids.includes(e.inventoryId)&&setTimeout((function(){r.forEach((function(e){n.ids.includes(e.inventoryId)||(n.ids.push(e.inventoryId),n.selectOutboundList.push(e)),n.$refs.table.toggleRowSelection(e,!0)}))}),50)}))},warehouseRentSettlement:function(){this.outboundReset(),this.outboundForm.outboundHandler=this.$store.state.user.name.split(" ")[1],this.outboundType=4,this.openOutbound=!0},countSummary:function(){this.outboundForm.unreceivedFromCustomer=m()(this.outboundForm.warehouseQuote).add(this.outboundForm.additionalStorageFee).add(this.outboundForm.unpaidUnloadingFee).add(this.outboundForm.unpaidPackagingFee).add(this.outboundForm.logisticsAdvanceFee).add(this.outboundForm.overdueRentalFee).add(this.outboundForm.difficultyWorkFee).value,this.outboundForm.receivedFromCustomer=m()(this.outboundForm.warehouseCollection).value,this.outboundForm.customerReceivableBalance=m()(this.outboundForm.unreceivedFromCustomer).subtract(this.outboundForm.receivedFromCustomer).value,this.outboundForm.payableToWorker=m()(this.outboundForm.receivedUnloadingFee).add(this.outboundForm.receivedPackingFee).add(this.outboundForm.workerLoadingFee).value,this.outboundForm.receivedFromSupplier=m()(this.outboundForm.receivedSupplier).add(this.outboundForm.receivedStorageFee).value,this.outboundForm.promissoryNoteSales=m()(this.outboundForm.unreceivedFromCustomer).add(this.outboundForm.receivedFromSupplier).value,this.outboundForm.promissoryNoteCost=m()(this.outboundForm.payableToWorker).add(this.outboundForm.logisticsAdvanceFee).add(this.outboundForm.warehouseAdvanceOtherFee).value,this.outboundForm.promissoryNoteGrossProfit=m()(this.outboundForm.promissoryNoteSales).subtract(this.outboundForm.promissoryNoteCost).value},currency:m.a,outboundConfirm:function(e){var t=this;this.selectOutboundList.map((function(e){1!=e.cargoDeduction||t.$message.error("有扣货库存请重新勾选，流水号："+e.inboundSerialNoSub)})),this.selectOutboundList.map((function(e){e.partialOutboundFlag=Number(e.partialOutboundFlag)})),this.outboundForm.totalBoxes=0,this.outboundForm.totalGrossWeight=0,this.outboundForm.totalVolume=0,this.selectOutboundList.map((function(e){return e.rsCargoDetailsList&&e.rsCargoDetailsList.map((function(e){return t.outboundForm.totalBoxes=m()(e.boxCount).add(t.outboundForm.totalBoxes).value,t.outboundForm.totalGrossWeight=m()(e.unitGrossWeight).add(t.outboundForm.totalGrossWeight).value,t.outboundForm.totalVolume=m()(e.unitVolume).add(t.outboundForm.totalVolume).value,e})),e})),0===e&&Object(l["a"])(this.outboundForm).then((function(e){var o=t.selectOutboundList.map((function(t){return t.preOutboundFlag="1",t.preOutboundRecordId=e.data,t.rsCargoDetailsList&&t.rsCargoDetailsList.map((function(e){return e.preOutboundFlag="1",e})),t}));Object(i["l"])(o).then((function(e){t.getList(),t.$message.success("预出仓成功"),t.$emit("closeOutbound")}))}))},loadPreOutboundInventoryList:function(){var e=this;this.loading=!0;var t={sqdPlannedOutboundDate:this.outboundForm.plannedOutboundDate,clientCode:this.outboundForm.clientCode,inventoryStatus:"0"};1===this.outboundType&&(t.preOutboundFlag="1"),Object(i["i"])(t).then((function(t){e.preOutboundInventoryList=t.rows.filter((function(e){return!e.packageTo})),!e.preOutboundInventoryList||t.rows.map((function(e){if(0===e.includesInboundFee){var t=Number(e.receivedStorageFee||0),o=Number(e.inboundFee||0),n=m()(o).subtract(t).value;e.additionalStorageFee=n>0?n:0}else e.additionalStorageFee=0;return"1"===e.packageRecord&&(e.hasChildren=!0),e})),e.total=t.total||0,0===e.outboundType&&e.$refs.table&&e.$nextTick((function(){e.preOutboundInventoryList.forEach((function(t){1===t.preOutboundFlag&&e.$refs.table.toggleRowSelection(t,!0)}))}))})).catch((function(t){console.error("加载预出库库存列表失败:",t),e.$message.error("加载预出库库存列表失败")})).finally((function(){e.loading=!1}))},handleOutbound:function(e){this.outboundReset(),this.outboundForm=e,this.outboundType=1,this.loadPreOutboundInventoryList(),this.openOutbound=!0},handlePreOutbound:function(){this.outboundReset(),this.outboundForm.outboundHandler=this.$store.state.user.name.split(" ")[1],this.outboundType=0,this.openOutbound=!0},handleDirectOutbound:function(){this.outboundReset(),this.outboundForm.outboundHandler=this.$store.state.user.name.split(" ")[1],this.outboundType=2,this.openOutbound=!0},handleRentSettlement:function(){this.outboundReset(),this.outboundForm.outboundHandler=this.$store.state.user.name.split(" ")[1],this.outboundType=3,this.openOutbound=!0},parseTime:s["f"],handleOutboundCargoDetailSelectionChange:function(e,t){t.outboundCargoDetailsList=e,this.selectedCargoDetail=e},isRowSelected:function(e){return this.selectedCargoDetail.includes(e)},getSummaries:function(e){var t=this,o=e.columns,n=(e.data,[]),r=["receivedSupplier","totalBoxes","unpaidInboundFee","totalGrossWeight","totalVolume","receivedStorageFee","unpaidUnloadingFee","logisticsAdvanceFee","rentalBalanceFee","overdueRentalFee","additionalStorageFee","unpaidUnloadingFee","unpaidPackingFee","receivedUnloadingFee","receivedPackingFee"],a={};return o.forEach((function(e,o){if(0===o)n[o]="汇总";else{var u=e.property;if(r.includes(u)){var l=t.selectOutboundList.reduce((function(e,t){return m()(e).add(Number(t[u])||0).value}),0);n[o]=l,a[e.property]=l}else n[o]=" "}})),Object.keys(a).forEach((function(e){t.outboundForm&&(t.outboundForm[e]=a[e])})),this.countSummary(),n},handleOutboundSelectionChange:function(e){var t=this,o=this.$refs.table.store.states.data,n=Object(u["a"])(this.ids);this.ids=[],this.ids=e.map((function(e){return e.inventoryId}));var r=this.ids.filter((function(e){return!n.includes(e)})),a=n.filter((function(e){return!t.ids.includes(e)}));this.selectOutboundList=e,this.$refs.table.doLayout(),e.map((function(e){var n=c()(t.outboundForm.outboundDate),a=c()(e.rentalSettlementDate);e.rentalDays=n.diff(a,"days")+1;var u=e.totalVolume;if(!Number.isNaN(e.rentalDays)&&e.rentalDays>0)if("整柜"!==t.outboundForm.outboundType)e.overdueRentalFee=m()(e.rentalDays).multiply(e.overdueRentalUnitPrice).multiply(u).value;else{var l=m()(e.rentalDays).subtract(e.freeStackPeriod).value;l=l>0?l:0,e.rentalDays=l,e.overdueRentalFee=m()(l).multiply(e.overdueRentalUnitPrice).multiply(u).value}if("1"===e.packageRecord&&r.includes(e.inventoryId)){var s=o.find((function(t){return t.inventoryId===e.inventoryId}));s&&s.children&&s.children.length>0?setTimeout((function(){s.children.forEach((function(e){t.ids.includes(e.inventoryId)||(t.ids.push(e.inventoryId),t.selectOutboundList.push(e),t.$refs.table.toggleRowSelection(e,!0))}))}),50):s&&!s.childrenLoaded&&s.hasChildren&&(s.childrenLoaded=!0,t.$refs.table.toggleRowExpansion(s,!0))}})),a.forEach((function(e){var n=o.find((function(t){return t.inventoryId===e&&"1"===t.packageRecord}));n&&n.children&&n.children.length>0&&n.children.forEach((function(e){var o=t.ids.indexOf(e.inventoryId);if(o>-1){t.ids.splice(o,1);var n=t.selectOutboundList.findIndex((function(t){return t.inventoryId===e.inventoryId}));n>-1&&t.selectOutboundList.splice(n,1),t.$refs.table.toggleRowSelection(e,!1)}}))})),this.countSummary()},selectContainerType:function(e){switch(e){case"20GP":this.outboundForm.warehouseQuote=this.clientRow.rate20gp;break;case"40HQ":this.outboundForm.warehouseQuote=this.clientRow.rate40hq;break}},outboundClient:function(e){this.outboundForm.warehouseQuote=e.rateLcl,this.outboundForm.freeStackDays=e.freeStackPeriod,this.clientRow=e,this.outboundForm.overdueRentalUnitPrice=e.overdueRent,this.$forceUpdate()},getList:function(){var e=this;this.loading=!0,Object(l["h"])(this.queryParams).then((function(t){e.outboundrecordList=t.rows,e.total=t.total,e.loading=!1}))},cancel:function(){this.open=!1,this.reset()},outboundReset:function(){this.outboundForm={outboundRecordId:null,receivedSupplier:null,outboundNo:null,clientCode:null,clientName:null,operator:null,containerType:null,containerNo:null,sealNo:null,warehouseQuote:null,workerLoadingFee:null,warehouseCollection:null,collectionNotes:null,totalBoxes:null,totalGrossWeight:null,totalVolume:null,totalRows:null,receivedStorageFee:null,unpaidUnloadingFee:null,unpaidPackagingFee:null,logisticsAdvanceFee:null,rentalBalanceFee:null,overdueRent:null,operationRequirement:null,freeStackDays:null,overdueUnitPrice:null,receivedFromSupplier:null,unreceivedFromCustomer:null,receivedFromCustomer:null,customerReceivableBalance:null,payableToWorker:null,promissoryNoteSales:null,promissoryNoteCost:null,promissoryNoteGrossProfit:null,outboundDate:c()().format("yyyy-MM-DD")},this.preOutboundInventoryList=[],this.resetForm("outboundForm")},reset:function(){this.form={outboundDate:c()().format("yyyy-MM-DD"),outboundRecordId:null,outboundNo:null,clientCode:null,clientName:null,operator:null,containerType:null,containerNo:null,sealNo:null,warehouseQuote:null,workerLoadingFee:null,warehouseCollection:null,collectionNotes:null,totalBoxes:null,totalGrossWeight:null,totalVolume:null,totalRows:null,receivedStorageFee:null,unpaidUnloadingFee:null,unpaidPackagingFee:null,logisticsAdvanceFee:null,rentalBalanceFee:null,overdueRent:null,freeStackDays:null,overdueUnitPrice:null},this.resetForm("form")},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.resetForm("queryForm"),this.handleQuery()},handleStatusChange:function(e){var t=this,o="0"===e.status?"启用":"停用";this.$modal.confirm('确认要"'+o+"吗？").then((function(){return Object(l["b"])(e.outboundRecordId,e.status)})).then((function(){t.$modal.msgSuccess(o+"成功")})).catch((function(){e.status="0"===e.status?"1":"0"}))},handleSelectionChange:function(e){this.ids=e.map((function(e){return e.outboundRecordId})),this.single=1!==e.length,this.multiple=!e.length},handleAdd:function(){this.reset(),this.open=!0,this.title="添加出仓记录"},handleUpdate:function(e){var t=this;this.reset();var o=e.outboundRecordId||this.ids;Object(l["e"])(o).then((function(e){t.form=e.data,t.open=!0,t.title="修改出仓记录"}))},submitForm:function(){var e=this;this.$refs["form"].validate((function(t){t&&(null!=e.form.outboundRecordId?Object(l["k"])(e.form).then((function(t){e.$modal.msgSuccess("修改成功"),e.open=!1,e.getList()})):Object(l["a"])(e.form).then((function(t){e.$modal.msgSuccess("新增成功"),e.open=!1,e.getList()})))}))},handleDelete:function(e){var t=this,o=e.outboundRecordId||this.ids;this.$modal.confirm('是否确认删除出仓记录编号为"'+o+'"的数据项？').then((function(){return Object(l["c"])(o)})).then((function(){t.getList(),t.$modal.msgSuccess("删除成功")})).catch((function(){}))},handleExport:function(){this.download("system/outboundrecord/export",Object(a["a"])({},this.queryParams),"outboundrecord_".concat((new Date).getTime(),".xlsx"))},handleSearchEnter:function(){var e=this;if(this.search){var t=this.preOutboundInventoryList.findIndex((function(t){var o=String(t.inboundSerialNo||""),n=String(e.search);return o.includes(n)}));if(t>-1){var o=this.$refs.table;this.$nextTick((function(){var t=o.$el.querySelector(".el-table__body-wrapper"),n=t.querySelectorAll(".el-table__row"),r=-1;if(n.forEach((function(t,o){var n=t.textContent;n.includes(e.search)&&(r=o)})),r>-1){var a=n[r],u=a.offsetTop;t.scrollTo({top:u-t.clientHeight/2,behavior:"smooth"}),a.classList.add("highlight-row"),setTimeout((function(){a.classList.remove("highlight-row")}),2e3)}}))}else this.$message.warning("未找到匹配的记录")}}}},f=p,h=(o("7478"),o("2877")),g=Object(h["a"])(f,n,r,!1,null,"4c39c49e",null);t["default"]=g.exports}}]);