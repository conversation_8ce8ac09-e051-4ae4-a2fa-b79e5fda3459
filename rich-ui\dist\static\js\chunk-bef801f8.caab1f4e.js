(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-bef801f8"],{"59e5":function(t,e,a){"use strict";a.r(e);var n=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"},{name:"dialogDragWidth",rawName:"v-dialogDragWidth"}],attrs:{"close-on-click-modal":!1,"append-to-body":!0,visible:t.openContent,title:"沟通记录",width:"1500px"},on:{"update:visible":function(e){t.openContent=e}}},[a("el-row",{staticClass:"mb8",attrs:{gutter:10}},[a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:communication:add"],expression:"['system:communication:add']"}],attrs:{icon:"el-icon-plus",plain:"",size:"mini",type:"primary"},on:{click:t.handleAdd}},[t._v("新增 ")])],1),a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:communication:edit"],expression:"['system:communication:edit']"}],attrs:{disabled:t.single,icon:"el-icon-edit",plain:"",size:"mini",type:"success"},on:{click:t.handleUpdate}},[t._v("修改 ")])],1),a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:communication:remove"],expression:"['system:communication:remove']"}],attrs:{disabled:t.multiple,icon:"el-icon-delete",plain:"",size:"mini",type:"danger"},on:{click:t.handleDelete}},[t._v("删除 ")])],1)],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],attrs:{data:t.communicationList,border:"",stripe:""},on:{"selection-change":t.handleSelectionChange}},[a("el-table-column",{attrs:{type:"selection",width:"28",align:"center"}}),a("el-table-column",{key:"shortName",attrs:{label:"员工信息","show-tooltip-when-overflow":"",width:"100px"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(t.company.companyShortName)+" "+t._s(null!=e.row.extStaff?(null!=e.row.extStaff.staffLocalName?e.row.extStaff.staffLocalName:"")+" "+(null!=e.row.extStaff.staffEnName?e.row.extStaff.staffEnName:""):"")+" ")]}}])}),a("el-table-column",{attrs:{align:"center",label:"物流节点",prop:"stage.stageLocalName",width:"68px"}}),a("el-table-column",{attrs:{align:"center",label:"问题",prop:"issue.issueLocalName",width:"88px"}}),a("el-table-column",{attrs:{align:"center",label:"沟通详细",prop:"content","show-tooltip-when-overflow":""}}),a("el-table-column",{attrs:{align:"center",label:"评分",prop:"score",width:"48px"}}),a("el-table-column",{attrs:{align:"center",label:"优先度",prop:"orderNum",width:"58px"}}),a("el-table-column",{attrs:{align:"center",label:"备注",prop:"remark"}}),a("el-table-column",{key:"rsStaff.staffLocalName",attrs:{align:"center",label:"跟进人",width:"120px"},scopedSlots:t._u([{key:"default",fn:function(e){return[t._v(" "+t._s(null!=e.row.rsStaff?(null!=e.row.rsStaff.staffLocalName?e.row.rsStaff.staffLocalName:"")+" "+(null!=e.row.rsStaff.staffEnName?e.row.rsStaff.staffEnName:""):"")+" ")]}}])}),a("el-table-column",{attrs:{align:"center",label:"沟通时间",prop:"communicationDatetime",width:"135px"}}),a("el-table-column",{attrs:{align:"center","class-name":"small-padding fixed-width",label:"操作",width:"48px"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("div",{staticStyle:{height:"15px",padding:"0",margin:"0"}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:communication:edit"],expression:"['system:communication:edit']"}],staticStyle:{display:"flex"},attrs:{size:t.size,icon:"el-icon-edit",type:"success"},on:{click:function(a){return t.handleUpdate(e.row)}}},[t._v("修改 ")])],1),a("div",{staticStyle:{height:"15px",padding:"0",margin:"0"}},[t.id==e.row.staffId||1==t.id?a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:communication:remove"],expression:"['system:communication:remove']"}],staticStyle:{display:"flex"},attrs:{size:t.size,icon:"el-icon-delete",type:"danger"},on:{click:function(a){return t.handleDelete(e.row)}}},[t._v(" 删除 ")]):t._e()],1)]}}])})],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total>0"}],attrs:{limit:t.queryParams.pageSize,page:t.queryParams.pageNum,total:t.total},on:{"update:limit":function(e){return t.$set(t.queryParams,"pageSize",e)},"update:page":function(e){return t.$set(t.queryParams,"pageNum",e)},pagination:t.getList}})],1),a("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"},{name:"dialogDragWidth",rawName:"v-dialogDragWidth"}],attrs:{"close-on-click-modal":!1,"modal-append-to-body":!1,title:t.title,visible:t.oopen,"append-to-body":"",width:"800px"},on:{"update:visible":function(e){t.oopen=e}}},[a("el-form",{ref:"form",attrs:{model:t.form,rules:t.rules,"label-width":"100px"}},[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"公司",prop:"companyId"}},[a("a",{staticStyle:{margin:"0","font-weight":"bold","font-size":"small"}},[t._v(t._s(this.company.companyShortName))])])],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"沟通时间"}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{format:"yyyy 年 MM 月 dd 日 HH 时 mm 分 ss 秒",placeholder:"选择日期",type:"datetime","value-format":"yyyy-MM-dd HH:mm:ss"},model:{value:t.form.communicationDatetime,callback:function(e){t.$set(t.form,"communicationDatetime",e)},expression:"form.communicationDatetime"}})],1)],1),a("el-form-item",{attrs:{label:"沟通员工",prop:"extStaffId"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{filterable:"",placeholder:""},model:{value:t.form.extStaffId,callback:function(e){t.$set(t.form,"extStaffId",e)},expression:"form.extStaffId"}},t._l(t.extStaffOptions,(function(t){return a("el-option",{key:t.staffId,attrs:{label:(null!=t.staffLocalName?t.staffLocalName:"")+" "+(null!=t.staffEnName?t.staffEnName:""),value:t.staffId}})})),1)],1),a("el-form-item",{attrs:{label:"物流节点",prop:"stageId"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"进度分类"},model:{value:t.form.processTypeId,callback:function(e){t.$set(t.form,"processTypeId",e)},expression:"form.processTypeId"}},t._l(t.processtypeList,(function(t){return a("el-option",{key:t.processTypeId,attrs:{label:t.processTypeLocalName,value:t.processTypeId}})})),1)],1),a("el-form-item",{attrs:{label:"问题",prop:"issueId"}},[a("tree-select",{attrs:{pass:t.form.issueId,placeholder:"",type:"issue"},on:{return:t.getIssueId}})],1),a("el-form-item",{attrs:{label:"沟通详细"}},[a("el-input",{attrs:{autosize:{minRows:10,maxRows:20},maxlength:"300",placeholder:"沟通详细","show-word-limit":"",type:"textarea"},model:{value:t.form.content,callback:function(e){t.$set(t.form,"content",e)},expression:"form.content"}})],1),a("el-form-item",{attrs:{label:"备注",prop:"remark"}},[a("el-input",{attrs:{rows:3,maxlength:"150",placeholder:"备注","show-word-limit":"",type:"textarea"},model:{value:t.form.remark,callback:function(e){t.$set(t.form,"remark",e)},expression:"form.remark"}})],1),a("el-form-item",{attrs:{label:"评分1~10",prop:"score"}},[a("el-input-number",{staticStyle:{width:"100%"},attrs:{controls:!1,max:10,min:1,placeholder:"评分1~10"},model:{value:t.form.score,callback:function(e){t.$set(t.form,"score",e)},expression:"form.score"}})],1),a("el-form-item",{attrs:{label:"优先度",prop:"orderNum"}},[a("el-input-number",{staticStyle:{width:"100%"},attrs:{controls:!1,placeholder:"优先度"},model:{value:t.form.orderNum,callback:function(e){t.$set(t.form,"orderNum",e)},expression:"form.orderNum"}})],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:t.submitForm}},[t._v("确 定")]),a("el-button",{on:{click:t.cancel}},[t._v("取 消")])],1)],1)],1)},o=[],i=a("5530"),s=(a("d81d"),a("998b")),r=a("b857"),l=a("ed08"),c=a("f0c6"),m={name:"communications",props:["type","open","loadOptions","company"],data:function(){return{extStaffOptions:[],processtypeList:[],id:this.$store.state.user.sid,openContent:!1,mainStaffId:null,oopen:!1,size:this.$store.state.app.size||"mini",loading:!0,ids:[],single:!0,multiple:!0,showSearch:!0,total:0,communicationList:[],title:"",queryParams:{pageNum:1,pageSize:20,sqdCompanyId:null,staffId:null,extStaffId:null,stageId:null,issueId:null,content:null,score:null},form:{},rules:{}}},watch:{loadOptions:function(){this.communicationList=this.loadOptions,this.loading=!1},open:function(t){this.openContent=t},openContent:function(t){0==t&&this.$emit("openCommunications")}},created:function(){var t=this;Object(c["e"])({pageNum:1,pageSize:100}).then((function(e){t.processtypeList=e.rows}))},methods:{getList:function(){var t=this;this.loading=!0,this.queryParams.sqdCompanyId=this.company.companyId,Object(s["d"])(this.queryParams).then((function(e){t.communicationList=e.rows,t.total=e.total,t.loading=!1}))},cancel:function(){this.oopen=!1,this.reset()},reset:function(){this.form={communicationId:null,staffId:this.$store.state.user.id,extStaffId:this.company.mainStaffId,stageId:null,issueId:null,content:null,score:null,orderNum:null,remark:null,createBy:null,createTime:null,updateBy:null,updateTime:null,deleteBy:null,deleteTime:null,deleteStatus:0},this.resetForm("form")},resetQuery:function(){this.resetForm("queryForm"),this.handleQuery()},handleSelectionChange:function(t){this.ids=t.map((function(t){return t.communicationId})),this.single=1!=t.length,this.multiple=!t.length},handleAdd:function(){var t=this;this.reset(),this.oopen=!0,this.title="添加记录",Object(r["e"])({sqdCompanyId:this.company.companyId}).then((function(e){t.extStaffOptions=e.data,t.form.extStaffId=t.company.mainStaffId})),this.form.communicationDatetime=Object(l["d"])(new Date)},handleUpdate:function(t){var e=this;this.reset();var a=t.communicationId||this.ids;Object(s["c"])(a).then((function(t){e.form=t.data,e.oopen=!0,e.title="修改交流",e.getList()}))},submitForm:function(){var t=this;this.$refs["form"].validate((function(e){e&&(t.form.sqdCompanyId=t.company.companyId,null!=t.form.communicationId?Object(s["e"])(t.form).then((function(e){t.$modal.msgSuccess("修改成功"),t.oopen=!1,t.getList()})):Object(s["a"])(t.form).then((function(e){t.$modal.msgSuccess("新增成功"),t.oopen=!1,t.getList()})))}))},handleDelete:function(t){var e=this,a=t.communicationId||this.ids;this.$confirm('是否确认删除交流编号为"'+a+'"的数据项？',"提示",{customClass:"modal-confirm"}).then((function(){return Object(s["b"])(a)})).then((function(){e.getList(),e.$modal.msgSuccess("删除成功")})).catch((function(){}))},handleExport:function(){this.download("system/communication/export",Object(i["a"])({},this.queryParams),"communication_".concat((new Date).getTime(),".xlsx"))},getStageId:function(t){this.form.stageId=t},getIssueId:function(t){this.form.issueId=t}}},u=m,d=a("2877"),f=Object(d["a"])(u,n,o,!1,null,null,null);e["default"]=f.exports},"998b":function(t,e,a){"use strict";a.d(e,"d",(function(){return o})),a.d(e,"c",(function(){return i})),a.d(e,"a",(function(){return s})),a.d(e,"e",(function(){return r})),a.d(e,"b",(function(){return l}));var n=a("b775");function o(t){return Object(n["a"])({url:"/system/communication/list",method:"get",params:t})}function i(t){return Object(n["a"])({url:"/system/communication/"+t,method:"get"})}function s(t){return Object(n["a"])({url:"/system/communication",method:"post",data:t})}function r(t){return Object(n["a"])({url:"/system/communication",method:"put",data:t})}function l(t){return Object(n["a"])({url:"/system/communication/"+t,method:"delete"})}},b857:function(t,e,a){"use strict";a.d(e,"e",(function(){return o})),a.d(e,"d",(function(){return i})),a.d(e,"a",(function(){return s})),a.d(e,"f",(function(){return r})),a.d(e,"c",(function(){return l})),a.d(e,"b",(function(){return c}));var n=a("b775");function o(t){return Object(n["a"])({url:"/system/extStaff/list",method:"get",params:t})}function i(t){return Object(n["a"])({url:"/system/extStaff/"+t,method:"get"})}function s(t){return Object(n["a"])({url:"/system/extStaff",method:"post",data:t})}function r(t){return Object(n["a"])({url:"/system/extStaff",method:"put",data:t})}function l(t){return Object(n["a"])({url:"/system/extStaff/"+t,method:"delete"})}function c(t,e){var a={staffId:t,staffJobStatus:e};return Object(n["a"])({url:"/system/extStaff",method:"put",data:a})}},f0c6:function(t,e,a){"use strict";a.d(e,"e",(function(){return o})),a.d(e,"d",(function(){return i})),a.d(e,"a",(function(){return s})),a.d(e,"f",(function(){return r})),a.d(e,"c",(function(){return l})),a.d(e,"b",(function(){return c}));var n=a("b775");function o(t){return Object(n["a"])({url:"/system/processtype/list",method:"get",params:t})}function i(t){return Object(n["a"])({url:"/system/processtype/"+t,method:"get"})}function s(t){return Object(n["a"])({url:"/system/processtype",method:"post",data:t})}function r(t){return Object(n["a"])({url:"/system/processtype",method:"put",data:t})}function l(t){return Object(n["a"])({url:"/system/processtype/"+t,method:"delete"})}function c(t,e){var a={processTypeId:t,status:e};return Object(n["a"])({url:"/system/processtype/changeStatus",method:"put",data:a})}}}]);