(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d0cc474"],{"4cf5":function(e,t,s){"use strict";s.r(t);var a=function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("div",[s("el-tooltip",{attrs:{disabled:null==e.scope.row.psaRemark||e.scope.row.psaRemark.length<12,placement:"top"}},[s("div",{attrs:{slot:"content"},slot:"content"},[s("h6",{staticStyle:{margin:"0"}},[e._v(e._s(e.scope.row.psaRemark))])]),s("div",[s("h6",{staticStyle:{margin:"0",overflow:"hidden","text-overflow":"ellipsis","white-space":"nowrap"}},[e._v(" "+e._s(e.scope.row.psaRemark)+" ")])])])],1)},o=[],n={name:"businessRemark",props:["scope"],data:function(){return{size:this.$store.state.app.size||"mini"}}},r=n,c=s("2877"),i=Object(c["a"])(r,a,o,!1,null,"b581cfc8",null);t["default"]=i.exports}}]);