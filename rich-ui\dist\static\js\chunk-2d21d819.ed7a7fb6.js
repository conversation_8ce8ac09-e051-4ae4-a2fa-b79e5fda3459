(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d21d819"],{d21f:function(t,n,e){"use strict";e.r(n);e("b0c0"),e("d3b7"),e("7db0"),e("25f0");n["default"]=function(){function t(){this.name="fontSize"}return t.prototype.css=function(t,n){if(t&&t.length){if(n)return t.css("font-size",n+"pt"),"font-size:"+n+"pt";t[0].style.fontSize=""}return null},t.prototype.createTarget=function(){var t=[8,9,10,11,12,14,16,18,20,22,24,26,28,36,48,72],n='\n            <option value="" >默认</option>';return t.forEach((function(t){n+='\n            <option value="'+t+'">'+t+"pt</option>"})),this.target=$(' <div class="hiprint-option-item">\n        <div class="hiprint-option-item-label">\n        字体大小\n        </div>\n        <div class="hiprint-option-item-field">\n        <select class="auto-submit">        </select>\n        </div>\n    </div>'),this.target.find(".auto-submit").append($(n)),this.target},t.prototype.getValue=function(){var t=this.target.find("select").val();if(t)return parseFloat(t.toString())},t.prototype.setValue=function(t){t&&(this.target.find('option[value="'+t+'"]').length||this.target.find("select").prepend('<option value="'+t+'" >'+t+"</option>")),this.target.find("select").val(t)},t.prototype.destroy=function(){this.target.remove()},t}()}}]);