(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d0e20f4"],{"7ca3":function(e,t,l){"use strict";l.r(t);var a=function(){var e=this,t=e.$createElement,l=e._self._c||t;return l("div",{staticClass:"app-container"},[l("el-row",{attrs:{gutter:20}},[l("el-col",{attrs:{span:e.showLeft}},[l("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],ref:"queryForm",staticClass:"query",attrs:{inline:!0,model:e.queryParams,size:"mini"}},[l("el-form-item",{attrs:{label:"名称",prop:"clientName"}},[l("el-input",{attrs:{clearable:"",placeholder:"客户名称"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.clientName,callback:function(t){e.$set(e.queryParams,"clientName",t)},expression:"queryParams.clientName"}})],1),l("el-form-item",{attrs:{label:"编码",prop:"clientCode"}},[l("el-input",{attrs:{clearable:"",placeholder:"客户编码"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.clientCode,callback:function(t){e.$set(e.queryParams,"clientCode",t)},expression:"queryParams.clientCode"}})],1),l("el-form-item",[l("el-button",{attrs:{icon:"el-icon-search",size:"mini",type:"primary"},on:{click:e.handleQuery}},[e._v("搜索")]),l("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1)],1),l("el-col",{attrs:{span:e.showRight}},[l("el-row",{staticClass:"mb8",attrs:{gutter:10}},[l("el-col",{attrs:{span:1.5}},[l("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:warehouseclient:add"],expression:"['system:warehouseclient:add']"}],attrs:{icon:"el-icon-plus",plain:"",size:"mini",type:"primary"},on:{click:e.handleAdd}},[e._v("新增 ")])],1),l("el-col",{attrs:{span:1.5}},[l("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:warehouseclient:edit"],expression:"['system:warehouseclient:edit']"}],attrs:{disabled:e.single,icon:"el-icon-edit",plain:"",size:"mini",type:"success"},on:{click:e.handleUpdate}},[e._v("修改 ")])],1),l("el-col",{attrs:{span:1.5}},[l("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:warehouseclient:remove"],expression:"['system:warehouseclient:remove']"}],attrs:{disabled:e.multiple,icon:"el-icon-delete",plain:"",size:"mini",type:"danger"},on:{click:e.handleDelete}},[e._v("删除 ")])],1),l("el-col",{attrs:{span:1.5}},[l("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:warehouseclient:export"],expression:"['system:warehouseclient:export']"}],attrs:{icon:"el-icon-download",plain:"",size:"mini",type:"warning"},on:{click:e.handleExport}},[e._v("导出 ")])],1),l("right-toolbar",{attrs:{showSearch:e.showSearch},on:{"update:showSearch":function(t){e.showSearch=t},"update:show-search":function(t){e.showSearch=t},queryTable:e.getList}})],1),l("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.warehouseclientList},on:{"selection-change":e.handleSelectionChange}},[l("el-table-column",{attrs:{align:"center",type:"selection",width:"28"}}),l("el-table-column",{attrs:{align:"center",label:"客户名称",prop:"clientName"}}),l("el-table-column",{attrs:{align:"center",label:"客户编码",prop:"clientCode"}}),l("el-table-column",{attrs:{align:"center",label:"客户性质",prop:"clientType"}}),l("el-table-column",{attrs:{align:"center",label:"目的国",prop:"destinationCountry"}}),l("el-table-column",{attrs:{align:"center",label:"电话",prop:"consigneePhone"}}),l("el-table-column",{attrs:{align:"center",label:"报价LCL",prop:"rateLcl"}}),l("el-table-column",{attrs:{align:"center",label:"报价20GP",prop:"rate20gp"}}),l("el-table-column",{attrs:{align:"center",label:"报价40HQ",prop:"rate40hq"}}),l("el-table-column",{attrs:{align:"center",label:"报价4",prop:"rate4"}}),l("el-table-column",{attrs:{align:"center",label:"报价5",prop:"rate5"}}),l("el-table-column",{attrs:{align:"center",label:"免堆期",prop:"freeStackPeriod"}}),l("el-table-column",{attrs:{align:"center",label:"超期仓租",prop:"overdueRent"}}),l("el-table-column",{attrs:{align:"center",label:"卸货费",prop:"includesUnloadingFee"}}),l("el-table-column",{attrs:{align:"center",label:"扣货",prop:"cargoDeduction"}}),l("el-table-column",{attrs:{align:"center",label:"有效",prop:"isActive"}}),l("el-table-column",{attrs:{align:"center",label:"业务员",prop:"salesId"},scopedSlots:e._u([{key:"default",fn:function(t){return[l("span",[e._v(e._s(e.getName(t.row.salesId)))])]}}])}),l("el-table-column",{attrs:{align:"center","class-name":"small-padding fixed-width",label:"操作"},scopedSlots:e._u([{key:"default",fn:function(t){return[l("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:warehouseclient:edit"],expression:"['system:warehouseclient:edit']"}],staticStyle:{"margin-right":"-8px"},attrs:{icon:"el-icon-edit",size:"mini",type:"success"},on:{click:function(l){return e.handleUpdate(t.row)}}},[e._v("修改 ")]),l("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:warehouseclient:remove"],expression:"['system:warehouseclient:remove']"}],staticStyle:{"margin-right":"-8px"},attrs:{icon:"el-icon-delete",size:"mini",type:"danger"},on:{click:function(l){return e.handleDelete(t.row)}}},[e._v("删除 ")])]}}])})],1),l("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total>0"}],attrs:{limit:e.queryParams.pageSize,page:e.queryParams.pageNum,total:e.total},on:{"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},pagination:e.getList}})],1)],1),l("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"},{name:"dialogDragWidth",rawName:"v-dialogDragWidth"}],attrs:{"close-on-click-modal":!1,"modal-append-to-body":!1,title:e.title,visible:e.open,"append-to-body":"",width:"25%"},on:{"update:visible":function(t){e.open=t}}},[l("el-form",{ref:"form",staticClass:"edit",attrs:{model:e.form,rules:e.rules,"label-width":"80px"}},[l("el-form-item",{attrs:{label:"系统客户",prop:"clientSystemId"}},[l("company-select",{attrs:{"load-options":e.companyList,multiple:!1,"no-parent":!0,pass:e.form.clientSystemId?e.form.clientSystemId:null,placeholder:"客户","role-client":"1","role-control":!1},on:{return:function(t){e.form.clientSystemId=t},returnData:function(t){e.form.clientName=t.companyShortName}}})],1),l("el-form-item",{attrs:{label:"客户名称",prop:"clientName"}},[l("el-input",{attrs:{placeholder:"客户名称"},model:{value:e.form.clientName,callback:function(t){e.$set(e.form,"clientName",t)},expression:"form.clientName"}})],1),l("el-form-item",{attrs:{label:"客户编码",prop:"clientCode"}},[l("el-input",{attrs:{placeholder:"客户的仓库简称/编码"},model:{value:e.form.clientCode,callback:function(t){e.$set(e.form,"clientCode",t)},expression:"form.clientCode"}})],1),l("el-form-item",{attrs:{label:"目的国",prop:"destinationCountry"}},[l("el-input",{attrs:{placeholder:"目的国"},model:{value:e.form.destinationCountry,callback:function(t){e.$set(e.form,"destinationCountry",t)},expression:"form.destinationCountry"}})],1),l("el-form-item",{attrs:{label:"跟进人",prop:"consigneePhone"}},[l("el-input",{attrs:{placeholder:"跟进人"},model:{value:e.form.follower,callback:function(t){e.$set(e.form,"follower",t)},expression:"form.follower"}})],1),l("el-form-item",{attrs:{label:"跟进人电话",prop:"consigneePhone"}},[l("el-input",{attrs:{placeholder:"跟进人电话"},model:{value:e.form.consigneePhone,callback:function(t){e.$set(e.form,"consigneePhone",t)},expression:"form.consigneePhone"}})],1),l("el-form-item",{attrs:{label:"报价LCL",prop:"rateLcl"}},[l("el-row",[l("el-col",{attrs:{span:19}},[l("el-input",{attrs:{placeholder:"报价LCL"},model:{value:e.form.rateLcl,callback:function(t){e.$set(e.form,"rateLcl",t)},expression:"form.rateLcl"}})],1),l("el-col",{attrs:{span:5}},[l("el-input",{staticClass:"disable-form",attrs:{value:"RMB/CBM",disabled:""}})],1)],1)],1),l("el-form-item",{attrs:{label:"报价20GP",prop:"rate20gp"}},[l("el-row",[l("el-col",{attrs:{span:19}},[l("el-input",{attrs:{placeholder:"报价20GP"},model:{value:e.form.rate20gp,callback:function(t){e.$set(e.form,"rate20gp",t)},expression:"form.rate20gp"}})],1),l("el-col",{attrs:{span:5}},[l("el-input",{staticClass:"disable-form",attrs:{value:"RMB/20GP",disabled:""}})],1)],1)],1),l("el-form-item",{attrs:{label:"报价40HQ",prop:"rate40hq"}},[l("el-row",[l("el-col",{attrs:{span:19}},[l("el-input",{attrs:{placeholder:"报价40HQ"},model:{value:e.form.rate40hq,callback:function(t){e.$set(e.form,"rate40hq",t)},expression:"form.rate40hq"}})],1),l("el-col",{attrs:{span:5}},[l("el-input",{staticClass:"disable-form",attrs:{value:"RMB/40HQ",disabled:""}})],1)],1)],1),l("el-form-item",{attrs:{label:"免堆期",prop:"freeStackPeriod"}},[l("el-row",[l("el-col",{attrs:{span:21}},[l("el-input",{attrs:{placeholder:"免堆期"},model:{value:e.form.freeStackPeriod,callback:function(t){e.$set(e.form,"freeStackPeriod",t)},expression:"form.freeStackPeriod"}})],1),l("el-col",{attrs:{span:3}},[l("el-input",{staticClass:"disable-form",attrs:{value:"Day",disabled:""}})],1)],1)],1),l("el-form-item",{attrs:{label:"超期仓租",prop:"overdueRent"}},[l("el-row",[l("el-col",{attrs:{span:18}},[l("el-input",{attrs:{placeholder:"报价40HQ"},model:{value:e.form.overdueRent,callback:function(t){e.$set(e.form,"overdueRent",t)},expression:"form.overdueRent"}})],1),l("el-col",{attrs:{span:6}},[l("el-input",{staticClass:"disable-form",attrs:{value:"RMB/Day/CBM",disabled:""}})],1)],1)],1),l("el-form-item",{attrs:{label:"入仓费已含",prop:"includesInboundFee"}},[l("el-switch",{attrs:{"active-value":1,"inactive-value":0},model:{value:e.form.includesInboundFee,callback:function(t){e.$set(e.form,"includesInboundFee",t)},expression:"form.includesInboundFee"}})],1),l("el-form-item",{attrs:{label:"卸货费已含",prop:"includesUnloadingFee"}},[l("el-switch",{attrs:{"active-value":"1","inactive-value":"0"},model:{value:e.form.includesUnloadingFee,callback:function(t){e.$set(e.form,"includesUnloadingFee",t)},expression:"form.includesUnloadingFee"}})],1),l("el-form-item",{attrs:{label:"打包费已含",prop:"cargoDeduction"}},[l("el-switch",{attrs:{"active-value":1,"inactive-value":0},model:{value:e.form.includesPackingFee,callback:function(t){e.$set(e.form,"includesPackingFee",t)},expression:"form.includesPackingFee"}})],1),l("el-form-item",{attrs:{label:"标准入仓费",prop:"cargoDeduction"}},[l("el-row",[l("el-col",{attrs:{span:20}},[l("el-input",{attrs:{placeholder:"标准入仓费"},model:{value:e.form.standardInboundFee,callback:function(t){e.$set(e.form,"standardInboundFee",t)},expression:"form.standardInboundFee"}})],1),l("el-col",{attrs:{span:4}},[l("el-input",{staticClass:"disable-form",attrs:{value:"RMB/BL",disabled:""}})],1)],1)],1),l("el-form-item",{attrs:{label:"精确入仓费",prop:"cargoDeduction"}},[l("el-row",[l("el-col",{attrs:{span:20}},[l("el-input",{attrs:{placeholder:"精确入仓费"},model:{value:e.form.preciseInboundFee,callback:function(t){e.$set(e.form,"preciseInboundFee",t)},expression:"form.preciseInboundFee"}})],1),l("el-col",{attrs:{span:4}},[l("el-input",{staticClass:"disable-form",attrs:{value:"RMB/BL",disabled:""}})],1)],1)],1),l("el-form-item",{attrs:{label:"快递入仓费",prop:"cargoDeduction"}},[l("el-row",[l("el-col",{attrs:{span:20}},[l("el-input",{attrs:{placeholder:"快递入仓费"},model:{value:e.form.expressInboundFee,callback:function(t){e.$set(e.form,"expressInboundFee",t)},expression:"form.expressInboundFee"}})],1),l("el-col",{attrs:{span:4}},[l("el-input",{staticClass:"disable-form",attrs:{value:"RMB/BL",disabled:""}})],1)],1)],1),l("el-form-item",{attrs:{label:"记录方式",prop:"defaultRecordMode"}},[l("el-select",{attrs:{placeholder:"默认记录方式"},model:{value:e.form.defaultRecordMode,callback:function(t){e.$set(e.form,"defaultRecordMode",t)},expression:"form.defaultRecordMode"}},[l("el-option",{attrs:{label:"标准",value:"标准"}}),l("el-option",{attrs:{label:"精确",value:"精确"}}),l("el-option",{attrs:{label:"快递",value:"快递"}})],1)],1),l("el-form-item",{attrs:{label:"费用现结",prop:"immediatePaymentFeeBoolean"}},[l("el-switch",{attrs:{"active-value":"1","inactive-value":"0"},model:{value:e.form.immediatePaymentFee,callback:function(t){e.$set(e.form,"immediatePaymentFee",t)},expression:"form.immediatePaymentFee"}})],1),l("el-form-item",{attrs:{label:"扣货",prop:"cargoDeduction"}},[l("el-switch",{attrs:{"active-value":"1","inactive-value":"0"},model:{value:e.form.cargoDeduction,callback:function(t){e.$set(e.form,"cargoDeduction",t)},expression:"form.cargoDeduction"}})],1),l("el-form-item",{attrs:{label:"有效",prop:"isActiveBoolean"}},[l("el-switch",{attrs:{"active-value":"1","inactive-value":"0"},model:{value:e.form.isActive,callback:function(t){e.$set(e.form,"isActive",t)},expression:"form.isActive"}})],1),l("el-form-item",{attrs:{label:"瑞旗业务员",prop:"salesId"}},[l("treeselect",{attrs:{"disabled-branch-nodes":!0,"flatten-search-results":!0,normalizer:e.staffNormalizer,options:e.belongList,"show-count":!0,placeholder:"业务员"},on:{input:function(t){void 0==t&&(e.form.salesId=null)},select:function(t){e.form.salesId=t.staffId}},scopedSlots:e._u([{key:"value-label",fn:function(t){var a=t.node;return l("div",{},[e._v(" "+e._s(void 0!=a.raw.staff?a.raw.staff.staffFamilyLocalName+a.raw.staff.staffGivingLocalName+" "+a.raw.staff.staffGivingEnName:"")+" ")])}},{key:"option-label",fn:function(t){var a=t.node,n=t.shouldShowCount,s=t.count,o=t.labelClassName,r=t.countClassName;return l("label",{class:o},[e._v(" "+e._s(-1!=a.label.indexOf(",")?a.label.substring(0,a.label.indexOf(",")):a.label)+" "),n?l("span",{class:r},[e._v("("+e._s(s)+")")]):e._e()])}}]),model:{value:e.salesId,callback:function(t){e.salesId=t},expression:"salesId"}})],1)],1),l("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[l("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("确 定")]),l("el-button",{on:{click:e.cancel}},[e._v("取 消")])],1)],1)],1)},n=[],s=l("5530"),o=l("b85c"),r=(l("4de4"),l("d3b7"),l("d81d"),l("fd49")),i=l("6e71"),c=l("4360"),u=l("ca17"),d=l.n(u),m=l("b0b8"),f=l.n(m),p={name:"Warehouseclient",components:{Treeselect:d.a,CompanySelect:i["a"]},data:function(){return{belongList:[],salesId:null,companyList:[],showLeft:0,showRight:24,loading:!0,ids:[],single:!0,multiple:!0,showSearch:!1,total:0,warehouseclientList:[],title:"",open:!1,queryParams:{pageNum:1,pageSize:20,clientSystemId:null,clientName:null,clientCode:null,clientType:null,destinationCountry:null,consigneePhone:null,rateLcl:null,rate20gp:null,rate40hq:null,rate4:null,rate5:null,freeStackPeriod:null,overdueRent:null,includesUnloadingFee:null,includesInboundFee:null,cargoDeduction:null,isActive:null,salesId:null},form:{},rules:{clientCode:[{required:!0,message:"请填写客户代码",trigger:"blur"}],clientName:[{required:!0,message:"请填写客户名称",trigger:"blur"}]},staffList:[]}},watch:{showSearch:function(e){!0===e?(this.showRight=21,this.showLeft=3):(this.showRight=24,this.showLeft=0)}},beforeMount:function(){this.loadStaff(),this.loadSales()},created:function(){this.getList()},methods:{getName:function(e){if(e){var t=this.$store.state.data.allRsStaffList.filter((function(t){return t.staffId==e}))[0];return t?t.staffFamilyLocalName+t.staffGivingLocalName+t.staffShortName:""}return""},getList:function(){var e=this;this.loading=!0,Object(r["e"])(this.queryParams).then((function(t){e.warehouseclientList=t.rows,e.total=t.total,e.loading=!1}))},loadStaff:function(){var e=this;0==this.$store.state.data.allRsStaffList.length||this.$store.state.data.redisList.allRsStaffList?c["a"].dispatch("getAllRsStaffList").then((function(){e.staffList=e.$store.state.data.allRsStaffList.filter((function(e){return"业务部"===e.dept.deptLocalName}))})):this.staffList=this.$store.state.data.allRsStaffList.filter((function(e){return"业务部"===e.dept.deptLocalName}))},loadSales:function(){var e=this;0==this.$store.state.data.salesList.length||this.$store.state.data.redisList.salesList?c["a"].dispatch("getSalesList").then((function(){e.belongList=e.$store.state.data.salesList})):this.belongList=this.$store.state.data.salesList},cancel:function(){this.open=!1,this.reset()},reset:function(){this.form={companyId:null,warehouseClientId:null,clientSystemId:null,clientName:null,clientCode:null,clientType:null,destinationCountry:null,consigneePhone:null,rateLcl:null,rate20gp:null,rate40hq:null,rate4:null,rate5:null,freeStackPeriod:null,overdueRent:null,inboundFee:null,includesUnloadingFee:null,includesInboundFee:null,cargoDeduction:null,isActive:null,salesId:null,singlePieceWeight:null,singlePieceVolume:null,standardInboundFee:null,preciseInboundFee:null,expressInboundFee:null,defaultRecordMode:null,immediatePaymentFeeBoolean:null,isActiveBoolean:null,immediatePaymentFee:null},this.resetForm("form")},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.resetForm("queryForm"),this.handleQuery()},handleStatusChange:function(e){var t=this,l="0"===e.status?"启用":"停用";this.$modal.confirm('确认要"'+l+"吗？").then((function(){return Object(r["b"])(e.warehouseClientId,e.status)})).then((function(){t.$modal.msgSuccess(l+"成功")})).catch((function(){e.status="0"===e.status?"1":"0"}))},handleSelectionChange:function(e){this.ids=e.map((function(e){return e.warehouseClientId})),this.single=1!==e.length,this.multiple=!e.length},handleAdd:function(){this.reset(),this.open=!0,this.title="添加仓库客户信息"},handleUpdate:function(e){var t=this;this.reset();var l=e.warehouseClientId||this.ids;Object(r["d"])(l).then((function(e){if(t.form=e.data,void 0!=t.belongList){var l,a=Object(o["a"])(t.belongList);try{for(a.s();!(l=a.n()).done;){var n=l.value;if(void 0!=n.children){var s,r=Object(o["a"])(n.children);try{for(r.s();!(s=r.n()).done;){var i=s.value;if(void 0!=i.children){var c,u=Object(o["a"])(i.children);try{for(u.s();!(c=u.n()).done;){var d=c.value;d.staffId==e.data.salesId&&(t.salesId=d.deptId)}}catch(m){u.e(m)}finally{u.f()}}}}catch(m){r.e(m)}finally{r.f()}}}}catch(m){a.e(m)}finally{a.f()}}t.companyList=e.data.companyList,t.open=!0,t.title="修改仓库客户信息"}))},submitForm:function(){var e=this;this.$refs["form"].validate((function(t){t&&(null!=e.form.warehouseClientId?Object(r["g"])(e.form).then((function(t){e.$modal.msgSuccess("修改成功"),e.open=!1,e.getList()})):Object(r["a"])(e.form).then((function(t){e.$modal.msgSuccess("新增成功"),e.open=!1,e.getList()})))}))},handleDelete:function(e){var t=this,l=e.warehouseClientId||this.ids;this.$modal.confirm('是否确认删除仓库客户信息编号为"'+l+'"的数据项？').then((function(){return Object(r["c"])(l)})).then((function(){t.getList(),t.$modal.msgSuccess("删除成功")})).catch((function(){}))},handleExport:function(){this.download("system/warehouseclient/export",Object(s["a"])({},this.queryParams),"warehouseclient_".concat((new Date).getTime(),".xlsx"))},staffNormalizer:function(e){var t;return e.children&&!e.children.length&&delete e.children,e.staff&&(t=null==e.staff.staffFamilyLocalName&&null==e.staff.staffGivingLocalName?null!=e.role.roleLocalName?e.role.roleLocalName+","+f.a.getFullChars(e.role.roleLocalName):e.dept.deptLocalName+","+f.a.getFullChars(e.dept.deptLocalName):e.staff.staffCode+" "+e.staff.staffFamilyLocalName+e.staff.staffGivingLocalName+" "+e.staff.staffGivingEnName+","+f.a.getFullChars(e.staff.staffFamilyLocalName+e.staff.staffGivingLocalName)),e.roleId?{id:e.roleId,label:t,children:e.children,isDisabled:null==e.staffId&&void 0==e.children}:{id:e.deptId,label:t,children:e.children,isDisabled:null==e.staffId&&void 0==e.children}}}},h=p,b=l("2877"),v=Object(b["a"])(h,a,n,!1,null,null,null);t["default"]=v.exports}}]);