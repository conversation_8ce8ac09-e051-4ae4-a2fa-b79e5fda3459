(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d0cfc88"],{"64d8":function(e,r,a){"use strict";a.r(r);var o=function(){var e=this,r=e.$createElement,a=e._self._c||r;return a("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"},{name:"dialogDragWidth",rawName:"v-dialogDragWidth"}],attrs:{"close-on-click-modal":!1,"modal-append-to-body":!1,visible:e.oopen,"append-to-body":"",width:"1200px"},on:{"update:visible":function(r){e.oopen=r}}},[a("div",{staticStyle:{display:"flex"}},[a("h2",{staticStyle:{"font-weight":"bold",margin:"10px"}},[e._v("新增编号信息")]),a("div",{staticStyle:{"vertical-align":"middle","line-height":"41px"}},[a("el-button",{attrs:{type:"primary"},on:{click:function(r){e.open=!0}}},[e._v("新增")])],1)]),a("el-table",{attrs:{border:"",data:e.preCarriageNoInfo}},[a("el-table-column",{attrs:{label:"SO号码",prop:"soNo"}}),a("el-table-column",{attrs:{label:"司机姓名",prop:"preCarriageDriverName"}}),a("el-table-column",{attrs:{label:"司机电话",prop:"preCarriageDriverTel"}}),a("el-table-column",{attrs:{label:"司机车牌",prop:"preCarriageTruckNo"}}),a("el-table-column",{attrs:{label:"司机备注",prop:"preCarriageTruckRemark"}}),a("el-table-column",{attrs:{label:"装柜地址",prop:"preCarriageAddress"}}),a("el-table-column",{attrs:{label:"到场时间",prop:"preCarriageTime"}}),a("el-table-column",{attrs:{label:"柜号",prop:"containerNo"}}),a("el-table-column",{attrs:{label:"柜型",prop:"containerType"}}),a("el-table-column",{attrs:{label:"封条",prop:"sealNo"}}),a("el-table-column",{attrs:{label:"磅单",prop:"weightPaper"}}),a("el-table-column",{attrs:{"header-align":"center",align:"center","class-name":"small-padding fixed-width",label:"操作",width:"100"},scopedSlots:e._u([{key:"default",fn:function(r){return[a("el-button",{staticStyle:{"margin-right":"-8px"},attrs:{icon:"el-icon-edit",size:"mini",type:"success"},on:{click:function(a){return e.handleUpdate(r.row)}}},[e._v("修改 ")]),a("el-button",{staticStyle:{"margin-right":"-8px"},attrs:{icon:"el-icon-delete",size:"mini",type:"danger"},on:{click:function(a){return e.handleDelete(r.row)}}},[e._v("删除 ")])]}}])})],1),a("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"},{name:"dialogDragWidth",rawName:"v-dialogDragWidth"}],attrs:{"close-on-click-modal":!1,"modal-append-to-body":!1,visible:e.open,"append-to-body":"",width:"500px",title:"新增编号信息"},on:{"update:visible":function(r){e.open=r}}},[a("el-form",{attrs:{border:"",data:e.form,"label-width":"105px"}},[a("el-form-item",{attrs:{label:"SO号码",prop:"soNo"}},[a("el-input",{attrs:{placeholder:"SO号码"},model:{value:e.form.soNo,callback:function(r){e.$set(e.form,"soNo",r)},expression:"form.soNo"}})],1),a("el-form-item",{attrs:{label:"司机姓名",prop:"preCarriageDriverName"}},[a("el-input",{attrs:{placeholder:"司机姓名"},model:{value:e.form.preCarriageDriverName,callback:function(r){e.$set(e.form,"preCarriageDriverName",r)},expression:"form.preCarriageDriverName"}})],1),a("el-form-item",{attrs:{label:"司机电话",prop:"preCarriageDriverTel"}},[a("el-input",{attrs:{placeholder:"司机电话"},model:{value:e.form.preCarriageDriverTel,callback:function(r){e.$set(e.form,"preCarriageDriverTel",r)},expression:"form.preCarriageDriverTel"}})],1),a("el-form-item",{attrs:{label:"司机车牌",prop:"preCarriageTruckNo"}},[a("el-input",{attrs:{placeholder:"司机车牌"},model:{value:e.form.preCarriageTruckNo,callback:function(r){e.$set(e.form,"preCarriageTruckNo",r)},expression:"form.preCarriageTruckNo"}})],1),a("el-form-item",{attrs:{label:"司机备注",prop:"preCarriageTruckRemark"}},[a("el-input",{attrs:{placeholder:"司机备注"},model:{value:e.form.preCarriageTruckRemark,callback:function(r){e.$set(e.form,"preCarriageTruckRemark",r)},expression:"form.preCarriageTruckRemark"}})],1),a("el-form-item",{attrs:{label:"装柜地址",prop:"preCarriageAddress"}},[a("el-input",{attrs:{placeholder:"装柜地址"},model:{value:e.form.preCarriageAddress,callback:function(r){e.$set(e.form,"preCarriageAddress",r)},expression:"form.preCarriageAddress"}})],1),a("el-form-item",{attrs:{label:"到场时间",prop:"preCarriageTime"}},[a("el-input",{attrs:{placeholder:"到场时间"},model:{value:e.form.preCarriageTime,callback:function(r){e.$set(e.form,"preCarriageTime",r)},expression:"form.preCarriageTime"}})],1),a("el-form-item",{attrs:{label:"柜号",prop:"containerNo"}},[a("el-input",{attrs:{placeholder:"柜号"},model:{value:e.form.containerNo,callback:function(r){e.$set(e.form,"containerNo",r)},expression:"form.containerNo"}})],1),a("el-form-item",{attrs:{label:"柜型",prop:"containerType"}},[a("el-input",{attrs:{placeholder:"柜型"},model:{value:e.form.containerType,callback:function(r){e.$set(e.form,"containerType",r)},expression:"form.containerType"}})],1),a("el-form-item",{attrs:{label:"封条",prop:"sealNo"}},[a("el-input",{attrs:{placeholder:"封条"},model:{value:e.form.sealNo,callback:function(r){e.$set(e.form,"sealNo",r)},expression:"form.sealNo"}})],1),a("el-form-item",{attrs:{label:"磅单",prop:"weightPaper"}},[a("el-input",{attrs:{placeholder:"磅单"},model:{value:e.form.weightPaper,callback:function(r){e.$set(e.form,"weightPaper",r)},expression:"form.weightPaper"}})],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("确 定")]),a("el-button",{on:{click:e.cancel}},[e._v("取 消")])],1)],1)],1)},t=[],l=(a("4de4"),a("d3b7"),a("14d9"),{name:"PreCarriageNoInfo",props:["openPreCarriageNoInfo"],watch:{preCarriageNoInfo:function(){this.$emit("return",this.preCarriageNoInfo)},openPreCarriageNoInfo:function(e){this.oopen=e},oopen:function(e){0==e&&this.$emit("close")}},data:function(){return{open:!1,oopen:!1,preCarriageNoInfo:[],form:{}}},methods:{rowIndex:function(e){var r=e.row,a=e.rowIndex;r.id=a+1},handleUpdate:function(e){this.form=e,this.open=!0},handleDelete:function(e){this.preCarriageNoInfo=this.preCarriageNoInfo.filter((function(r){return r.id!=e.id}))},submitForm:function(){null!=this.form.id?(this.reset(),this.open=!1):(this.preCarriageNoInfo.push(this.form),this.reset(),this.open=!1)},reset:function(){this.form={id:null,soNo:null,preCarriageDriverName:null,preCarriageDriverTel:null,preCarriageTruckNo:null,preCarriageTruckRemark:null,preCarriageAddress:null,preCarriageTime:null,containerNo:null,containerType:null,sealNo:null,weightPaper:null},this.resetForm("form")},cancel:function(){this.open=!1}}}),i=l,n=a("2877"),p=Object(n["a"])(i,o,t,!1,null,null,null);r["default"]=p.exports}}]);