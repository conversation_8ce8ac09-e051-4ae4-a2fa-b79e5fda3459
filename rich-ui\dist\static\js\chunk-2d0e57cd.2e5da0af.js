(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d0e57cd"],{9561:function(e,r,n){"use strict";n.r(r);var t=function(){var e=this,r=e.$createElement,n=e._self._c||r;return n("i-frame",{attrs:{id:"jimuReportFrame",src:e.openUrl}})},a=[],o=n("5f87"),c=n("061b"),i={name:"JimuReportDesign",components:{iFrame:c["a"]},data:function(){return{openUrl:"/prod-api/jmreport/list?token=Bearer "+Object(o["a"])()}}},p=i,s=n("2877"),u=Object(s["a"])(p,t,a,!1,null,"67ec6346",null);r["default"]=u.exports}}]);