(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-d8fa964c","chunk-68702101"],{"0062":function(e,t,a){"use strict";a.r(t);var s=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],ref:"queryForm",staticClass:"query",attrs:{inline:!0,model:e.queryParams,"label-width":"68px",size:"mini"}},[a("el-form-item",{attrs:{label:"任务名称",prop:"jobName"}},[a("el-input",{staticStyle:{width:"240px"},attrs:{clearable:"",placeholder:"任务名称"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.jobName,callback:function(t){e.$set(e.queryParams,"jobName",t)},expression:"queryParams.jobName"}})],1),a("el-form-item",{attrs:{label:"任务组名",prop:"jobGroup"}},[a("el-select",{staticStyle:{width:"240px"},attrs:{clearable:"",placeholder:"任务组名"},model:{value:e.queryParams.jobGroup,callback:function(t){e.$set(e.queryParams,"jobGroup",t)},expression:"queryParams.jobGroup"}},e._l(e.dict.type.sys_job_group,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),a("el-form-item",{attrs:{label:"执行状态",prop:"status"}},[a("el-select",{staticStyle:{width:"240px"},attrs:{clearable:"",placeholder:"执行状态"},model:{value:e.queryParams.status,callback:function(t){e.$set(e.queryParams,"status",t)},expression:"queryParams.status"}},e._l(e.dict.type.sys_common_status,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),a("el-form-item",{attrs:{label:"执行时间"}},[a("el-date-picker",{staticStyle:{width:"240px"},attrs:{"end-placeholder":"结束日期","range-separator":"-","start-placeholder":"开始日期",type:"daterange","default-time":["00:00:00","23:59:59"],"value-format":"yyyy-MM-dd HH:mm:ss"},model:{value:e.dateRange,callback:function(t){e.dateRange=t},expression:"dateRange"}})],1),a("el-form-item",[a("el-button",{attrs:{icon:"el-icon-search",size:"mini",type:"primary"},on:{click:e.handleQuery}},[e._v("搜索")]),a("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1),a("el-row",{staticClass:"mb8",attrs:{gutter:10}},[a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["monitor:job:remove"],expression:"['monitor:job:remove']"}],attrs:{disabled:e.multiple,icon:"el-icon-delete",plain:"",size:"mini",type:"danger"},on:{click:e.handleDelete}},[e._v("删除 ")])],1),a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["monitor:job:remove"],expression:"['monitor:job:remove']"}],attrs:{icon:"el-icon-delete",plain:"",size:"mini",type:"danger"},on:{click:e.handleClean}},[e._v("清空 ")])],1),a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["monitor:job:export"],expression:"['monitor:job:export']"}],attrs:{icon:"el-icon-download",plain:"",size:"mini",type:"warning"},on:{click:e.handleExport}},[e._v("导出 ")])],1),a("el-col",{attrs:{span:1.5}},[a("el-button",{attrs:{icon:"el-icon-close",plain:"",size:"mini",type:"warning"},on:{click:e.handleClose}},[e._v("关闭 ")])],1),a("right-toolbar",{attrs:{showSearch:e.showSearch},on:{"update:showSearch":function(t){e.showSearch=t},"update:show-search":function(t){e.showSearch=t},queryTable:e.getList}})],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.jobLogList},on:{"selection-change":e.handleSelectionChange}},[a("el-table-column",{attrs:{type:"selection",width:"28",align:"center"}}),a("el-table-column",{attrs:{align:"center",label:"日志编号",prop:"jobLogId",width:"80"}}),a("el-table-column",{attrs:{"show-tooltip-when-overflow":!0,align:"center",label:"任务名称",prop:"jobName"}}),a("el-table-column",{attrs:{"show-tooltip-when-overflow":!0,align:"center",label:"任务组名",prop:"jobGroup"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("dict-tag",{attrs:{options:e.dict.type.sys_job_group,value:t.row.jobGroup}})]}}])}),a("el-table-column",{attrs:{"show-tooltip-when-overflow":!0,align:"center",label:"调用目标字符串",prop:"invokeTarget"}}),a("el-table-column",{attrs:{"show-tooltip-when-overflow":!0,align:"center",label:"日志信息",prop:"jobMessage"}}),a("el-table-column",{attrs:{align:"center",label:"执行状态",prop:"status"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("dict-tag",{attrs:{options:e.dict.type.sys_common_status,value:t.row.status}})]}}])}),a("el-table-column",{attrs:{align:"center",label:"执行时间",prop:"createTime",width:"180"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(e.parseTime(t.row.createTime)))])]}}])}),a("el-table-column",{attrs:{align:"center","class-name":"small-padding fixed-width",label:"操作"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["monitor:job:query"],expression:"['monitor:job:query']"}],attrs:{icon:"el-icon-view",size:"mini",type:"text"},on:{click:function(a){return e.handleView(t.row)}}},[e._v("详细 ")])]}}])})],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total>0"}],attrs:{limit:e.queryParams.pageSize,page:e.queryParams.pageNum,total:e.total},on:{"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},pagination:e.getList}}),a("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"},{name:"dialogDragWidth",rawName:"v-dialogDragWidth"}],attrs:{"close-on-click-modal":!1,"modal-append-to-body":!1,visible:e.open,"append-to-body":"",title:"调度日志详细",width:"700px"},on:{"update:visible":function(t){e.open=t}}},[a("el-form",{ref:"form",attrs:{model:e.form,"label-width":"100px",size:"mini"}},[a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"日志序号："}},[e._v(e._s(e.form.jobLogId))]),a("el-form-item",{attrs:{label:"任务名称："}},[e._v(e._s(e.form.jobName))])],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"任务分组："}},[e._v(e._s(e.form.jobGroup))]),a("el-form-item",{attrs:{label:"执行时间："}},[e._v(e._s(e.form.createTime))])],1),a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"调用方法："}},[e._v(e._s(e.form.invokeTarget))])],1),a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"日志信息："}},[e._v(e._s(e.form.jobMessage))])],1),a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"执行状态："}},[0==e.form.status?a("div",[e._v("正常")]):1==e.form.status?a("div",[e._v("失败")]):e._e()])],1),a("el-col",{attrs:{span:24}},[1==e.form.status?a("el-form-item",{attrs:{label:"异常信息："}},[e._v(e._s(e.form.exceptionInfo))]):e._e()],1)],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(t){e.open=!1}}},[e._v("关 闭")])],1)],1)],1)},l=[],o=a("5530"),r=(a("d81d"),a("a159")),i=a("b775");function n(e){return Object(i["a"])({url:"/monitor/jobLog/list",method:"get",params:e})}function c(e){return Object(i["a"])({url:"/monitor/jobLog/"+e,method:"delete"})}function u(){return Object(i["a"])({url:"/monitor/jobLog/clean",method:"delete"})}var d={name:"JobLog",dicts:["sys_common_status","sys_job_group"],data:function(){return{loading:!0,ids:[],multiple:!0,showSearch:!0,total:0,jobLogList:[],open:!1,dateRange:[],form:{},queryParams:{pageNum:1,pageSize:10,jobName:void 0,jobGroup:void 0,status:void 0}}},created:function(){var e=this,t=this.$route.params&&this.$route.params.jobId;void 0!=t&&0!=t?Object(r["d"])(t).then((function(t){e.queryParams.jobName=t.data.jobName,e.queryParams.jobGroup=t.data.jobGroup,e.getList()})):this.getList()},methods:{getList:function(){var e=this;this.loading=!0,n(this.addDateRange(this.queryParams,this.dateRange)).then((function(t){e.jobLogList=t.rows,e.total=t.total,e.loading=!1}))},handleClose:function(){var e={path:"/monitor/job"};this.$tab.closeOpenPage(e)},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.dateRange=[],this.resetForm("queryForm"),this.handleQuery()},handleSelectionChange:function(e){this.ids=e.map((function(e){return e.jobLogId})),this.multiple=!e.length},handleView:function(e){this.open=!0,this.form=e},handleDelete:function(e){var t=this,a=this.ids;this.$confirm('是否确认删除调度日志编号为"'+a+'"的数据项？',"提示",{customClass:"modal-confirm"}).then((function(){return c(a)})).then((function(){t.getList(),t.$modal.msgSuccess("删除成功")})).catch((function(){}))},handleClean:function(){var e=this;this.$confirm("是否确认清空所有调度日志数据项？","提示",{customClass:"modal-confirm"}).then((function(){return u()})).then((function(){e.getList(),e.$modal.msgSuccess("清空成功")})).catch((function(){}))},handleExport:function(){this.download("/monitor/jobLog/export",Object(o["a"])({},this.queryParams),"log_".concat((new Date).getTime(),".xlsx"))}}},m=d,p=a("2877"),f=Object(p["a"])(m,s,l,!1,null,null,null);t["default"]=f.exports},"083c":function(e,t,a){"use strict";a.r(t);var s=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-table",{staticStyle:{width:"100%"},attrs:{data:e.tableData,border:""}},[a("el-table-column",{attrs:{label:"操作员",prop:"opName",width:"180"}}),a("el-table-column",{attrs:{label:"上月累计",prop:"lastMonth",width:"180"}}),a("el-table-column",{attrs:{label:"本月累计",prop:"currentMonth"}}),a("el-table-column",{attrs:{label:"昨天累计",prop:"lastDay"}}),a("el-table-column",{attrs:{label:"当天累计",prop:"currentDay"}})],1)},l=[],o=a("5530"),r=(a("d81d"),a("20f5")),i={name:"OpStatistics",data:function(){return{tableData:[]}},mounted:function(){this.loadData(),this.$on("updateStatistics",(function(){this.loadData()}))},methods:{loadData:function(){var e=this;Object(r["e"])().then((function(t){t.data&&(e.tableData=t.data.map((function(e){return null==e.opId?Object(o["a"])(Object(o["a"])({},e),{},{opName:"合计"}):e})))}))}}},n=i,c=a("2877"),u=Object(c["a"])(n,s,l,!1,null,"1770f753",null);t["default"]=u.exports},"4b7d":function(e,t,a){"use strict";a("ab30")},a159:function(e,t,a){"use strict";a.d(t,"e",(function(){return l})),a.d(t,"d",(function(){return o})),a.d(t,"a",(function(){return r})),a.d(t,"g",(function(){return i})),a.d(t,"c",(function(){return n})),a.d(t,"b",(function(){return c})),a.d(t,"f",(function(){return u}));var s=a("b775");function l(e){return Object(s["a"])({url:"/monitor/job/list",method:"get",params:e})}function o(e){return Object(s["a"])({url:"/monitor/job/"+e,method:"get"})}function r(e){return Object(s["a"])({url:"/monitor/job",method:"post",data:e})}function i(e){return Object(s["a"])({url:"/monitor/job",method:"put",data:e})}function n(e){return Object(s["a"])({url:"/monitor/job/"+e,method:"delete"})}function c(e,t){var a={jobId:e,status:t};return Object(s["a"])({url:"/monitor/job/changeStatus",method:"put",data:a})}function u(e,t){var a={jobId:e,jobGroup:t};return Object(s["a"])({url:"/monitor/job/run",method:"put",data:a})}},ab30:function(e,t,a){},ff3d:function(e,t,a){"use strict";a.r(t);var s=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:e.showLeft}},[a("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],ref:"queryForm",staticClass:"query",attrs:{inline:!0,model:e.queryParams,size:"mini"}},[a("el-form-item",{attrs:{label:"进度",prop:"processStatusId"}},[a("tree-select",{attrs:{"d-load":!0,flat:!1,multiple:!1,pass:e.queryParams.processStatusId,placeholder:"进度状态",type:"processStatus"},on:{return:function(t){e.queryParams.processStatusId=t}}})],1),a("el-form-item",{attrs:{label:"单号",prop:"rctNo"}},[a("el-input",{attrs:{placeholder:"操作单号"},model:{value:e.queryParams.rctNo,callback:function(t){e.$set(e.queryParams,"rctNo",t)},expression:"queryParams.rctNo"}})],1),a("el-form-item",{attrs:{label:"时间",prop:"pasVerifyTime"}},[a("el-date-picker",{staticStyle:{width:"100%",color:"#b7bbc2"},attrs:{clearable:"",placeholder:"商务审核时间",type:"daterange","default-time":["00:00:00","23:59:59"],"value-format":"yyyy-MM-dd HH:mm:ss"},model:{value:e.queryParams.pasVerifyTime,callback:function(t){e.$set(e.queryParams,"pasVerifyTime",t)},expression:"queryParams.pasVerifyTime"}})],1),a("el-form-item",{attrs:{label:"业务",prop:"salesId"}},[a("treeselect",{attrs:{"disable-branch-nodes":!0,"disabled-fuzzy-matching":!0,"flatten-search-results":!0,normalizer:e.staffNormalizer,options:e.belongList,"show-count":!0,placeholder:"业务员"},on:{input:function(t){void 0==t&&(e.queryParams.salesId=null)},open:e.loadSales,select:function(t){e.queryParams.salesId=t.staffId}},scopedSlots:e._u([{key:"value-label",fn:function(t){var s=t.node;return a("div",{},[e._v(" "+e._s(void 0!=s.raw.staff?s.raw.staff.staffFamilyLocalName+s.raw.staff.staffGivingLocalName+" "+s.raw.staff.staffGivingEnName:"")+" ")])}},{key:"option-label",fn:function(t){var s=t.node,l=t.shouldShowCount,o=t.count,r=t.labelClassName,i=t.countClassName;return a("label",{class:r},[e._v(" "+e._s(-1!=s.label.indexOf(",")?s.label.substring(0,s.label.indexOf(",")):s.label)+" "),l?a("span",{class:i},[e._v("("+e._s(o)+")")]):e._e()])}}]),model:{value:e.salesId,callback:function(t){e.salesId=t},expression:"salesId"}})],1),a("el-form-item",{attrs:{label:"助理",prop:"salesAssistantId"}},[a("treeselect",{attrs:{"disable-branch-nodes":!0,"disabled-fuzzy-matching":!0,"flatten-search-results":!0,normalizer:e.staffNormalizer,options:e.belongList,"show-count":!0,placeholder:"业务助理"},on:{input:function(t){void 0==t&&(e.queryParams.salesAssistantId=null)},open:e.loadSales,select:function(t){e.queryParams.salesAssistantId=t.staffId}},scopedSlots:e._u([{key:"value-label",fn:function(t){var s=t.node;return a("div",{},[e._v(" "+e._s(void 0!=s.raw.staff?s.raw.staff.staffFamilyLocalName+s.raw.staff.staffGivingLocalName+" "+s.raw.staff.staffGivingEnName:"")+" ")])}},{key:"option-label",fn:function(t){var s=t.node,l=t.shouldShowCount,o=t.count,r=t.labelClassName,i=t.countClassName;return a("label",{class:r},[e._v(" "+e._s(-1!=s.label.indexOf(",")?s.label.substring(0,s.label.indexOf(",")):s.label)+" "),l?a("span",{class:i},[e._v("("+e._s(o)+")")]):e._e()])}}]),model:{value:e.salesAssistantId,callback:function(t){e.salesAssistantId=t},expression:"salesAssistantId"}})],1),a("el-form-item",{attrs:{label:"商务",prop:"verifyPsaId"}},[a("treeselect",{attrs:{"disable-branch-nodes":!0,"disabled-fuzzy-matching":!0,"flatten-search-results":!0,normalizer:e.staffNormalizer,options:e.businessList,"show-count":!0,placeholder:"商务"},on:{input:function(t){void 0==t&&(e.queryParams.verifyPsaId=null)},open:e.loadBusinesses,select:function(t){e.queryParams.verifyPsaId=t.staffId}},scopedSlots:e._u([{key:"value-label",fn:function(t){var s=t.node;return a("div",{},[e._v(" "+e._s(void 0!=s.raw.staff?s.raw.staff.staffFamilyLocalName+s.raw.staff.staffGivingLocalName+" "+s.raw.staff.staffGivingEnName:"")+" ")])}},{key:"option-label",fn:function(t){var s=t.node,l=t.shouldShowCount,o=t.count,r=t.labelClassName,i=t.countClassName;return a("label",{class:r},[e._v(" "+e._s(-1!=s.label.indexOf(",")?s.label.substring(0,s.label.indexOf(",")):s.label)+" "),l?a("span",{class:i},[e._v("("+e._s(o)+")")]):e._e()])}}]),model:{value:e.verifyPsaId,callback:function(t){e.verifyPsaId=t},expression:"verifyPsaId"}})],1),a("el-form-item",{attrs:{label:"操作",prop:"opId"}},[a("treeselect",{attrs:{"disable-branch-nodes":!0,"disabled-fuzzy-matching":!0,"flatten-search-results":!0,normalizer:e.staffNormalizer,options:e.opList.filter((function(e){return"操作员"==e.role.roleLocalName})),"show-count":!0,placeholder:"操作员"},on:{input:function(t){void 0==t&&(e.queryParams.opId=null)},open:e.loadOp,select:function(t){e.queryParams.opId=t.staffId}},scopedSlots:e._u([{key:"value-label",fn:function(t){var s=t.node;return a("div",{},[e._v(" "+e._s(void 0!=s.raw.staff?s.raw.staff.staffFamilyLocalName+s.raw.staff.staffGivingLocalName+" "+s.raw.staff.staffGivingEnName:"")+" ")])}},{key:"option-label",fn:function(t){var s=t.node,l=t.shouldShowCount,o=t.count,r=t.labelClassName,i=t.countClassName;return a("label",{class:r},[e._v(" "+e._s(-1!=s.label.indexOf(",")?s.label.substring(0,s.label.indexOf(",")):s.label)+" "),l?a("span",{class:i},[e._v("("+e._s(o)+")")]):e._e()])}}]),model:{value:e.opId,callback:function(t){e.opId=t},expression:"opId"}})],1),a("el-form-item",{attrs:{label:"日期",prop:"rctOpDate"}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{clearable:"",placeholder:"操作日期",type:"daterange","default-time":["00:00:00","23:59:59"],"value-format":"yyyy-MM-dd HH:mm:ss"},model:{value:e.queryParams.rctOpDate,callback:function(t){e.$set(e.queryParams,"rctOpDate",t)},expression:"queryParams.rctOpDate"}})],1),a("el-form-item",{attrs:{label:"紧急",prop:"urgencyDegree"}},[a("el-input",{attrs:{placeholder:"紧急程度"},model:{value:e.queryParams.urgencyDegree,callback:function(t){e.$set(e.queryParams,"urgencyDegree",t)},expression:"queryParams.urgencyDegree"}})],1),a("el-form-item",{attrs:{label:"放货",prop:"releaseTypeId"}},[a("tree-select",{attrs:{"d-load":!0,flat:!1,multiple:!1,pass:e.queryParams.releaseTypeId,placeholder:"放货方式",type:"releaseType"},on:{return:function(t){e.queryParams.releaseTypeId=t}}})],1),a("el-form-item",{attrs:{label:"客户",prop:"clientId"}},[a("tree-select",{attrs:{"d-load":!0,flat:!1,multiple:!1,pass:e.queryParams.clientId,placeholder:"委托单位",type:"client"},on:{return:function(t){e.queryParams.clientId=t}}})],1),a("el-form-item",{attrs:{label:"物流",prop:"logisticsTypeId"}},[a("tree-select",{attrs:{"d-load":!0,flat:!1,multiple:!1,pass:e.queryParams.logisticsTypeId,placeholder:"物流类型",type:"serviceType"},on:{return:function(t){e.queryParams.logisticsTypeId=t}}})],1),a("el-form-item",{attrs:{label:"启运",prop:"polIds"}},[a("location-select",{attrs:{multiple:!0,"no-parent":!0,pass:e.queryParams.polIds,placeholder:"启运港"},on:{return:function(t){e.queryParams.polIds=t}}})],1),a("el-form-item",{attrs:{label:"目的",prop:"destinationPortIds"}},[a("location-select",{attrs:{"check-port":e.logisticsType,en:!0,multiple:!0,pass:e.queryParams.destinationPortIds,placeholder:"目的港"},on:{return:function(t){e.queryParams.destinationPortIds=t}}})],1),a("el-form-item",{attrs:{label:"航线",prop:"destinationLineIds"}},[a("tree-select",{staticStyle:{width:"100%"},attrs:{"d-load":!0,flat:!1,multiple:!0,pass:e.queryParams.destinationLineIds,placeholder:"目的航线",type:"line"},on:{return:function(t){e.queryParams.destinationLineIds=t}}})],1),a("el-form-item",{attrs:{label:"货量",prop:"revenueTons"}},[a("el-input",{staticStyle:{width:"100%"},attrs:{placeholder:"计费货量"},model:{value:e.queryParams.revenueTons,callback:function(t){e.$set(e.queryParams,"revenueTons",t)},expression:"queryParams.revenueTons"}})],1),a("el-form-item",{attrs:{label:"标记",prop:"isPsaVerified"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{clearable:"",placeholder:"商务审核标记"},model:{value:e.queryParams.isPsaVerified,callback:function(t){e.$set(e.queryParams,"isPsaVerified",t)},expression:"queryParams.isPsaVerified"}},[a("el-option",{attrs:{label:"已审",value:"0"}},[e._v("已审")]),a("el-option",{attrs:{label:"未审",value:"1"}},[e._v("未审")])],1)],1),a("el-form-item",{attrs:{label:"标记",prop:"isOpAllotted"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{clearable:"",placeholder:"操作分配标记"},model:{value:e.queryParams.isOpAllotted,callback:function(t){e.$set(e.queryParams,"isOpAllotted",t)},expression:"queryParams.isOpAllotted"}},[a("el-option",{attrs:{label:"未分配",value:"0"}},[e._v("未分配")]),a("el-option",{attrs:{label:"已分配",value:"1"}},[e._v("已分配")])],1)],1),a("el-form-item",[a("el-button",{attrs:{icon:"el-icon-search",size:"mini",type:"primary"},on:{click:e.handleQuery}},[e._v("搜索")]),a("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1)],1),a("el-col",{attrs:{span:e.showRight}},[a("el-row",{staticClass:"mb8",attrs:{gutter:10}},[a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:rct:add"],expression:"['system:rct:add']"}],attrs:{icon:"el-icon-plus",plain:"",size:"mini",type:"primary"},on:{click:e.handleAdd}},[e._v("新增 ")])],1),a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:rct:remove"],expression:"['system:rct:remove']"}],attrs:{disabled:e.multiple,icon:"el-icon-delete",plain:"",size:"mini",type:"danger"},on:{click:e.handleDelete}},[e._v("删除 ")])],1),a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:rct:export"],expression:"['system:rct:export']"}],attrs:{icon:"el-icon-download",plain:"",size:"mini",type:"warning"},on:{click:e.handleExport}},[e._v("导出 ")])],1),a("right-toolbar",{attrs:{showSearch:e.showSearch},on:{"update:showSearch":function(t){e.showSearch=t},"update:show-search":function(t){e.showSearch=t},queryTable:e.getList}})],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.rctList},on:{"selection-change":e.handleSelectionChange}},[a("el-table-column",{attrs:{align:"left",type:"selection",width:"28"}}),a("el-table-column",{attrs:{align:"left",label:"进度",prop:"processStatusName",width:"30"}}),a("el-table-column",{attrs:{align:"left",label:"操作单号",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("div",{staticClass:"flex-box"},[a("p",{staticClass:"column-text top-box",staticStyle:{"font-weight":"600","font-size":"15px"}},[e._v(e._s(t.row.rctNo))]),a("p",{staticClass:"date-column-text bottom-box"},[e._v(e._s(e.parseTime(t.row.rctOpDate,"{y}-{m}-{d}")))])])]}}])}),a("el-table-column",{attrs:{align:"left",label:"业务","show-overflow-tooltip":"",width:"80"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("div",{staticClass:"flex-box"},[a("p",{staticClass:"name-column-text top-box",staticStyle:{"font-weight":"600"}},[e._v(e._s(e.getName(t.row.salesId)))]),a("p",{staticClass:"name-column-text bottom-box",staticStyle:{}},[e._v(e._s(e.getName(t.row.salesAssistantId)))])])]}}])}),a("el-table-column",{attrs:{align:"left",label:"商务","show-overflow-tooltip":"",width:"80"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("div",{staticClass:"flex-box"},[a("p",{staticClass:"name-column-text top-box",staticStyle:{"font-weight":"600"}},[e._v(e._s(e.getName(t.row.verifyPsaId)))]),a("p",{staticClass:"column-text bottom-box",staticStyle:{color:"#b7bbc2"}},[e._v(e._s(t.row.psaVerifyTime))])])]}}])}),a("el-table-column",{attrs:{align:"left",label:"操作",prop:"opName","show-overflow-tooltip":"",width:"80"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("div",{staticClass:"flex-box"},[a("p",{staticClass:"name-column-text top-box",staticStyle:{margin:"0",padding:"0","font-weight":"600"}},[e._v(e._s(e.getName(t.row.opId)))]),a("p",{staticClass:"name-column-text bottom-box",staticStyle:{margin:"0",padding:"0"}},[e._v(e._s(e.getName(t.row.opObserverId)))])])]}}])}),a("el-table-column",{attrs:{align:"left",label:"订单特征","show-overflow-tooltip":"",width:"60"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",{staticClass:"column-text",staticStyle:{}},[e._v(e._s(e._f("switchOrderDifficulty")(t.row.difficultyLevel)))]),a("span",[e._v(e._s("/"))]),a("span",{staticClass:"column-text",staticStyle:{}},[e._v(e._s(e._f("switchUrgencyDegree")(t.row.emergencyLevel)))]),a("div"),a("span",{staticClass:"column-text",staticStyle:{}},[e._v(e._s(e._f("receiveAndPayType")(t.row.logisticsPaymentTermsCode)))]),a("span",[e._v(e._s("/"))]),a("span",{staticClass:"column-text",staticStyle:{}},[e._v(e._s(e._f("releaseTypeFilter")(t.row.releaseType)))])]}}])}),a("el-table-column",{attrs:{align:"left",label:"委托单位","show-overflow-tooltip":"",width:"120"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("p",{staticClass:"column-text",staticStyle:{"font-weight":"600","font-size":"medium"}},[e._v(" "+e._s(t.row.clientSummary?t.row.clientSummary.split("/")[1]:null))])]}}])}),a("el-table-column",{attrs:{align:"left",label:"物流类型",width:"60"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("div",{staticClass:"flex-box"},[a("p",{staticClass:"column-text top-box",staticStyle:{"font-weight":"600"}},[e._v(e._s(t.row.logisticsTypeEnName))]),a("p",{staticClass:"column-text bottom-box",staticStyle:{color:"#b7bbc2"}},[e._v(" "+e._s(1==t.row.impExpType?"出口":"")+" "+e._s(2==t.row.impExpType?"进口":"")+" ")])])]}}])}),a("el-table-column",{attrs:{align:"left",label:"起讫港口","show-overflow-tooltip":"",width:"120"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("div",{staticClass:"flex-box"},[a("p",{staticClass:"column-text top-box",staticStyle:{}},[e._v(e._s(t.row.pol))]),a("p",{staticClass:"column-text bottom-box",staticStyle:{}},[e._v(e._s(t.row.destinationPort))])])]}}])}),a("el-table-column",{attrs:{align:"left",label:"计费货量",width:"120"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("div",{staticClass:"flex-box"},[a("p",{staticClass:"column-text top-box",staticStyle:{}},[e._v(e._s(t.row.revenueTon))]),a("p",{staticClass:"column-text bottom-box",staticStyle:{}},[e._v(e._s(t.row.goodsNameSummary))])])]}}])}),a("el-table-column",{attrs:{align:"left",label:"订舱口","show-overflow-tooltip":"",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("div",{staticClass:"flex-box"},[a("p",{staticClass:"column-text top-box",staticStyle:{}},[e._v(e._s(t.row.carrierEnName))]),a("p",{staticClass:"column-text bottom-box",staticStyle:{"text-overflow":"ellipsis","white-space":"nowrap"}},[e._v(e._s(t.row.bookingAgentName))])])]}}])}),a("el-table-column",{attrs:{align:"left",label:"SO号",prop:"sqdSoNoSum",width:"120"}}),a("el-table-column",{attrs:{align:"left",label:"商务备注",prop:"inquiryInnerRemarkSum"}}),a("el-table-column",{attrs:{align:"left","class-name":"small-padding fixed-width",label:"操作",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:rct:edit"],expression:"['system:rct:edit']"}],staticStyle:{"margin-right":"-8px"},attrs:{icon:"el-icon-edit",size:"mini",type:"success"},on:{click:function(a){return e.handleUpdate(t.row)}}},[e._v("修改 ")]),e.hasPermit(t.row)?a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:rct:remove"],expression:"['system:rct:remove']"}],staticStyle:{"margin-right":"-8px"},attrs:{icon:"el-icon-delete",size:"mini",type:"danger"},on:{click:function(a){return e.handleDelete(t.row)}}},[e._v("删除 ")]):e._e()]}}])})],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total>0"}],attrs:{limit:e.queryParams.pageSize,page:e.queryParams.pageNum,total:e.total},on:{"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},pagination:e.getList}})],1)],1),a("RctDialog",{attrs:{"open-company":this.openCompany,row:this.row,title:this.title,type:this.type,"has-permit-submit":e.hasPermit(this.row)},on:{closeDialog:e.closeDialog,getRctList:e.getList}}),a("op-statistics")],1)},l=[],o=a("b85c"),r=a("5530"),i=a("c7eb"),n=a("1da1"),c=(a("4de4"),a("d3b7"),a("d81d"),a("b0c0"),a("20f5")),u=a("b0b8"),d=a.n(u),m=a("ca17"),p=a.n(m),f=(a("6f8d"),a("4360")),h=a("e11a"),b=a("083c"),y=a("1e69"),v=(a("0062"),a("dce4"),a("74b1")),g={name:"Rct",components:{OpStatistics:b["default"],RctDialog:h["default"],Treeselect:p.a},data:function(){return{logisticsType:"",title:"",openCompany:!1,showLeft:3,showRight:21,loading:!0,ids:[],single:!0,multiple:!0,showSearch:!0,total:0,salesId:null,verifyPsaId:null,salesAssistantId:null,opId:null,belongList:[],opList:[],businessList:[],rctList:[],queryParams:{pageNum:1,pageSize:20,logisticsTypeId:null},form:{},rules:{},row:{},type:"",userPositionId:null}},watch:{"queryParams.logisticsTypeId":function(e){var t=this;0==this.$store.state.data.serviceTypeList.length||this.$store.state.data.redisList.serviceType?f["a"].dispatch("getServiceTypeList").then((function(){t.getType(e)})):this.getType(e)},showSearch:function(e){!0===e?(this.showRight=21,this.showLeft=3):(this.showRight=24,this.showLeft=0)},deep:!0},created:function(){var e=this;return Object(n["a"])(Object(i["a"])().mark((function t(){var a;return Object(i["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return a=!1,e.getList().then((function(){a=!0})),a&&(e.loadSales(),e.loadOp(),e.loadBusinesses()),t.next=5,Object(v["f"])(0);case 5:e.userPositionId=t.sent;case 6:case"end":return t.stop()}}),t)})))()},filters:{releaseTypeFilter:function(e){if(e)switch(e){case"1":return"月结";case"2":return"签放";case"3":return"押放";case"4":return"票结";case"5":return"居间";case"6":return"订金";case"7":return"预付";case"8":return"扣货"}},receiveAndPayType:function(e){if(e)switch(e){case"1":return"先付";case"2":return"先收";case"3":return"其他"}},switchOrderDifficulty:function(e){if(e)switch(e){case"0":return"简易";case"1":return"标准";case"2":return"高级";case"3":return"特别"}},switchUrgencyDegree:function(e){if(e)switch(e){case"0":return"预定";case"1":return"当天";case"2":return"常规";case"4":return"紧急";case"3":return"立即"}}},methods:{getName:function(e){if((0==this.$store.state.data.allRsStaffList.length||this.$store.state.data.redisList.allRsStaffList)&&f["a"].dispatch("getAllRsStaffList"),e){var t=this.$store.state.data.allRsStaffList.filter((function(t){return t.staffId==e}))[0];if(t)return t.staffFamilyLocalName+t.staffGivingLocalName+t.staffShortName}},handleSelectionChange:function(e){this.ids=e.map((function(e){return e.rctId})),this.single=1!==e.length,this.multiple=!e.length},handleAdd:function(){this.title="新增操作单",this.type="add",this.openCompany=!0},handleUpdate:function(e){this.title="修改操作单",this.row=e,this.type="edit",this.openCompany=!0},handleDelete:function(e){var t=this,a=e.rctId||this.ids;this.$confirm('是否确认删除操作单列表编号为"'+a+'"的数据项？',"提示",{customClass:"modal-confirm"}).then((function(){return Object(c["b"])(a)})).then((function(){t.getList(),t.$modal.msgSuccess("删除成功")})).catch((function(){}))},handleExport:function(){this.download("system/rct/export",Object(r["a"])({},this.queryParams),"rct_".concat((new Date).getTime(),".xlsx"))},staffNormalizer:function(e){var t;return e.children&&!e.children.length&&delete e.children,e.staff&&(t=null==e.staff.staffFamilyLocalName&&null==e.staff.staffGivingLocalName?null!=e.role.roleLocalName?e.role.roleLocalName+","+d.a.getFullChars(e.role.roleLocalName):e.dept.deptLocalName+","+d.a.getFullChars(e.dept.deptLocalName):e.staff.staffCode+" "+e.staff.staffFamilyLocalName+e.staff.staffGivingLocalName+" "+e.staff.staffGivingEnName+","+d.a.getFullChars(e.staff.staffFamilyLocalName+e.staff.staffGivingLocalName)),e.roleId?{id:e.roleId,label:t,children:e.children,isDisabled:null==e.staffId&&void 0==e.children}:{id:e.deptId,label:t,children:e.children,isDisabled:null==e.staffId&&void 0==e.children}},closeDialog:function(){this.openCompany=!1},initWebSocket:function(){this.uid=this.$store.state.user.sid+this.$store.state.user.name.substring(0,8),Object(y["a"])(("https:"===window.location.protocol?"wss://sys.richgz.com/":"ws://192.168.1.33:8088/")+"websocket/"+this.uid),window.addEventListener("onmessageWS",(function(e){return console.log(e)}))},getType:function(e){var t,a=Object(o["a"])(this.$store.state.data.serviceTypeList[0].children);try{for(a.s();!(t=a.n()).done;){var s=t.value;s.serviceTypeId===e&&(this.logisticsType=s.typeId)}}catch(l){a.e(l)}finally{a.f()}},hasPermit:function(e){var t=this.$store.state.user.sid;return t===e.salesId||t===e.salesAssistantId||t===e.verifyPsaId||t===e.opId||t===e.opObserverId||this.userPositionId>2},loadSales:function(){var e=this;0==this.$store.state.data.salesList.length||this.$store.state.data.redisList.salesList?f["a"].dispatch("getSalesList").then((function(){e.belongList=e.$store.state.data.salesList})):this.belongList=this.$store.state.data.salesList},loadBusinesses:function(){var e=this;0==this.$store.state.data.businessesList.length||this.$store.state.data.redisList.businessesList?f["a"].dispatch("getBusinessesList").then((function(){e.businessList=e.$store.state.data.businessesList})):this.businessList=this.$store.state.data.businessesList},loadOp:function(){var e=this;0==this.$store.state.data.opList.length||this.$store.state.data.redisList.opList?f["a"].dispatch("getOpList").then((function(){e.opList=e.$store.state.data.opList})):this.opList=this.$store.state.data.opList},getList:function(){var e=this;return Object(n["a"])(Object(i["a"])().mark((function t(){return Object(i["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.loading=!0,t.next=3,Object(c["f"])(Object(r["a"])(Object(r["a"])({},e.queryParams),{},{permissionLevel:e.$store.state.user.permissionLevelList.C})).then((function(t){e.rctList=t.rows,e.total=void 0!=t.total?t.total:0,e.loading=!1}));case 3:case"end":return t.stop()}}),t)})))()},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.resetForm("queryForm"),this.handleQuery()}},destroyed:function(){window.removeEventListener("onmessageWS",this.getSocketData)}},w=g,_=(a("4b7d"),a("2877")),x=Object(_["a"])(w,s,l,!1,null,"33d2ceb5",null);t["default"]=x.exports}}]);