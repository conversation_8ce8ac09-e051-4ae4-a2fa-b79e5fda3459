(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-03cfb2bf"],{"25dd":function(e,t,s){},bf79:function(e,t,s){"use strict";s("25dd")},bfbe:function(e,t,s){"use strict";s.r(t);var i=function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("div",[s("div",[s("h6",{staticStyle:{margin:"0","font-weight":"bold","font-size":"small",overflow:"hidden","text-overflow":"ellipsis","white-space":"nowrap"}},[e._v(" "+e._s(e.scope.row.departure?e.scope.row.departure.split("(")[0]:"")+" ")])]),s("div",{staticClass:"unHighlight-text"},[e._v(e._s(e.scope.row.departure?"("+e.scope.row.departure.split("(")[1]:""))])])},r=[],o={name:"departure",props:["scope","typeId"],data:function(){return{size:this.$store.state.app.size||"mini"}}},n=o,p=(s("bf79"),s("2877")),a=Object(p["a"])(n,i,r,!1,null,"087ec7ee",null);t["default"]=a.exports}}]);