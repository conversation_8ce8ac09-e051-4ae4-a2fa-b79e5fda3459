(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-4ad75721"],{"1f34":function(e,t,a){"use strict";a.r(t);var l=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:e.showLeft}},[a("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],ref:"queryForm",staticClass:"query",attrs:{inline:!0,model:e.queryParams,size:"mini"}},[a("el-form-item",{attrs:{label:"部门",prop:"deptId"}},[a("tree-select",{ref:"dept",staticStyle:{width:"100%"},attrs:{flat:!1,multiple:!1,pass:e.queryParams.sqdDeptId,placeholder:"选择部门",type:"dept"},on:{return:e.getDept}})],1),a("el-form-item",{attrs:{label:"账户",prop:"staffUsername"}},[a("el-input",{staticStyle:{width:"100%"},attrs:{clearable:"",placeholder:"登录账户"},on:{change:e.handleQuery},model:{value:e.queryParams.staffUsername,callback:function(t){e.$set(e.queryParams,"staffUsername",t)},expression:"queryParams.staffUsername"}})],1),a("el-form-item",{attrs:{label:"中文",prop:"staffLocalName"}},[a("el-input",{staticStyle:{width:"100%"},attrs:{clearable:"",placeholder:"中文名"},on:{change:e.handleQuery},model:{value:e.queryParams.staffLocalName,callback:function(t){e.$set(e.queryParams,"staffLocalName",t)},expression:"queryParams.staffLocalName"}})],1),a("el-form-item",{attrs:{label:"英文",prop:"staffEnName"}},[a("el-input",{staticStyle:{width:"100%"},attrs:{clearable:"",placeholder:"英文名"},on:{change:e.handleQuery},model:{value:e.queryParams.staffEnName,callback:function(t){e.$set(e.queryParams,"staffEnName",t)},expression:"queryParams.staffEnName"}})],1),a("el-form-item",{attrs:{label:"邮箱",prop:"staffEmailEnterprise"}},[a("el-input",{staticStyle:{width:"100%"},attrs:{clearable:"",placeholder:"企业邮箱"},on:{change:e.handleQuery},model:{value:e.queryParams.staffEmailEnterprise,callback:function(t){e.$set(e.queryParams,"staffEmailEnterprise",t)},expression:"queryParams.staffEmailEnterprise"}})],1),a("el-form-item",{attrs:{label:"手机",prop:"staffPhoneNum"}},[a("el-input",{staticStyle:{width:"100%"},attrs:{clearable:"",placeholder:"手机号码"},on:{change:e.handleQuery},model:{value:e.queryParams.staffPhoneNum,callback:function(t){e.$set(e.queryParams,"staffPhoneNum",t)},expression:"queryParams.staffPhoneNum"}})],1),a("el-form-item",{attrs:{label:"状态",prop:"staffJobStatus"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{clearable:"",filterable:"",placeholder:"用户状态"},on:{change:e.handleQuery},model:{value:e.queryParams.staffJobStatus,callback:function(t){e.$set(e.queryParams,"staffJobStatus",t)},expression:"queryParams.staffJobStatus"}},e._l(e.dict.type.sys_normal_disable,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),a("el-form-item",[a("el-button",{attrs:{icon:"el-icon-search",size:"mini",type:"primary"},on:{click:e.handleQuery}},[e._v("搜索")]),a("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1)],1),a("el-col",{attrs:{span:e.showRight}},[a("el-row",{staticClass:"mb8",attrs:{gutter:10}},[a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:user:add"],expression:"['system:user:add']"}],attrs:{icon:"el-icon-plus",plain:"",size:"mini",type:"primary"},on:{click:e.handleAdd}},[e._v("新增 ")])],1),a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:user:remove"],expression:"['system:user:remove']"}],attrs:{disabled:e.multiple,icon:"el-icon-delete",plain:"",size:"mini",type:"danger"},on:{click:e.handleDelete}},[e._v("删除 ")])],1),a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:user:export"],expression:"['system:user:export']"}],attrs:{icon:"el-icon-download",plain:"",size:"mini",type:"warning"},on:{click:e.handleExport}},[e._v("导出 ")])],1),a("right-toolbar",{attrs:{columns:e.columns,showSearch:e.showSearch,types:"staff"},on:{"update:showSearch":function(t){e.showSearch=t},"update:show-search":function(t){e.showSearch=t},queryTable:e.getList}})],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.userList},on:{"selection-change":e.handleSelectionChange}},[a("el-table-column",{attrs:{type:"selection",width:"28",align:"center"}}),e.columns[0].visible?a("el-table-column",{key:"staffCode",attrs:{align:"center",label:"用户编号",prop:"staffCode",width:"100"}}):e._e(),e.columns[1].visible?a("el-table-column",{key:"staffUsername",attrs:{align:"center",label:"登录账户",prop:"staffUsername",width:"100"}}):e._e(),e.columns[2].visible?a("el-table-column",{attrs:{label:"用户名称",prop:"staffName",width:"200"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(t.row.staffShortName)+" "),a("a",{staticStyle:{margin:"0","font-weight":"bold","font-size":"small"}},[e._v(e._s(t.row.staffFamilyLocalName)+e._s(t.row.staffGivingLocalName))]),e._v(" "+e._s(t.row.staffGivingEnName)+" "+e._s(t.row.staffFamilyEnName)+" ")]}}],null,!1,3395148129)}):e._e(),e.columns[3].visible?a("el-table-column",{attrs:{align:"center",label:"所属部门",prop:"dept.deptLocalName",width:"68"}}):e._e(),e.columns[4].visible?a("el-table-column",{key:"staffGender",attrs:{align:"center",label:"性别",prop:"staffGender",width:"45"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("dict-tag",{attrs:{options:e.dict.type.sys_user_sex,value:t.row.staffGender}})]}}],null,!1,3726846588)}):e._e(),e.columns[5].visible?a("el-table-column",{key:"staffEmailEnterprise",attrs:{align:"center",label:"公司邮箱",prop:"staffEmailEnterprise","show-tooltip-when-overflow":"",width:"140"}}):e._e(),e.columns[6].visible?a("el-table-column",{key:"staffEmailPersonal",attrs:{align:"center",label:"个人邮箱",prop:"staffEmailPersonal","show-tooltip-when-overflow":"",width:"100"}}):e._e(),e.columns[7].visible?a("el-table-column",{key:"staffHeight",attrs:{align:"center",label:"身高",prop:"staffHeight"}}):e._e(),e.columns[8].visible?a("el-table-column",{key:"staffPhoneNum",attrs:{align:"center",label:"手机号码",prop:"staffPhoneNum",width:"110"}}):e._e(),e.columns[9].visible?a("el-table-column",{key:"staffNativeplace",attrs:{align:"center",label:"籍贯",prop:"staffNativeplace"}}):e._e(),e.columns[10].visible?a("el-table-column",{key:"staffIdentity",attrs:{"show-tooltip-when-overflow":!0,align:"center",label:"身份证",prop:"staffIdentity",width:"120"}}):e._e(),e.columns[11].visible?a("el-table-column",{key:"staffAddress",attrs:{"show-tooltip-when-overflow":!0,align:"center",label:"详细住址",prop:"staffAddress",width:"120"}}):e._e(),e.columns[12].visible?a("el-table-column",{key:"staffBirthday",attrs:{align:"center",label:"生日",prop:"staffBirthday",width:"100"}}):e._e(),e.columns[13].visible?a("el-table-column",{key:"staffLanguage",attrs:{align:"center",label:"母语",prop:"staffLanguage"}}):e._e(),e.columns[14].visible?a("el-table-column",{key:"staffMarital",attrs:{align:"center",label:"婚姻状况",prop:"staffMarital"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("dict-tag",{attrs:{options:e.dict.type.sys_user_marriage,value:t.row.staffMarital}})]}}],null,!1,2059773381)}):e._e(),e.columns[15].visible?a("el-table-column",{key:"staffGraduation",attrs:{"show-tooltip-when-overflow":!0,align:"center",label:"毕业院校",prop:"staffGraduation",width:"120"}}):e._e(),e.columns[16].visible?a("el-table-column",{key:"loginDate",attrs:{align:"center",label:"最后登录时间",prop:"loginDate",width:"170"}}):e._e(),e.columns[17].visible?a("el-table-column",{key:"staffInduction",attrs:{align:"center",label:"入职资料"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-switch",{attrs:{"active-value":"0","inactive-value":"1"},on:{change:function(a){return e.handleInductionChange(t.row)}},model:{value:t.row.staffInduction,callback:function(a){e.$set(t.row,"staffInduction",a)},expression:"scope.row.staffInduction"}})]}}],null,!1,2871465400)}):e._e(),e.columns[18].visible?a("el-table-column",{key:"remark",attrs:{align:"center",label:"员工评价",prop:"remark","show-tooltip-when-overflow":""}}):e._e(),e.columns[19].visible?a("el-table-column",{key:"macAddress",attrs:{align:"center",label:"mac地址",prop:"macAddress","show-tooltip-when-overflow":""}}):e._e(),a("el-table-column",{key:"midRsStaffRoles",attrs:{align:"center",label:"角色",width:"58"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-popover",{attrs:{placement:"right",trigger:"hover",width:"400"}},[a("el-table",{attrs:{data:t.row.midRsStaffRoles}},[a("el-table-column",{attrs:{align:"center",label:"部门",prop:"dept.deptLocalName"}}),a("el-table-column",{attrs:{align:"center",label:"职级",prop:"position.positionLocalName"}}),a("el-table-column",{attrs:{align:"center",label:"角色",prop:"role.roleLocalName"}}),a("el-table-column",{attrs:{align:"center",label:"是否主职",prop:"isMain"},scopedSlots:e._u([{key:"default",fn:function(t){var l=t.row;return[a("dict-tag",{attrs:{options:e.dict.type.sys_yes_no,value:l.isMain}})]}}],null,!0)})],1),a("el-button",{staticStyle:{margin:"0",padding:"5px"},attrs:{slot:"reference"},nativeOn:{mouseenter:function(a){return e.loadStaffRoles(t.row)}},slot:"reference"},[e._v(" 查看 ")])],1)]}}])}),a("el-table-column",{key:"midRsStaffAccounts",attrs:{align:"center",label:"银行账户",width:"68"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-popover",{attrs:{placement:"right",trigger:"hover",width:"400"}},[a("el-table",{attrs:{data:t.row.staffAccounts}},[a("el-table-column",{attrs:{align:"center",label:"银行名称",prop:"accountLocalName"}}),a("el-table-column",{attrs:{align:"center",label:"银行账号",prop:"accountCode"}})],1),a("el-button",{staticStyle:{margin:"0",padding:"5px"},attrs:{slot:"reference"},nativeOn:{mouseenter:function(a){return e.loadAccount(t.row)}},slot:"reference"},[e._v(" 查看 ")])],1)]}}])}),a("el-table-column",{key:"staffJobStatus",attrs:{align:"center",label:"状态",width:"65"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-switch",{attrs:{"active-value":"0","inactive-value":"1"},on:{change:function(a){return e.handleStatusChange(t.row)}},model:{value:t.row.staffJobStatus,callback:function(a){e.$set(t.row,"staffJobStatus",a)},expression:"scope.row.staffJobStatus"}})]}}])}),a("el-table-column",{attrs:{align:"center","class-name":"small-padding fixed-width",label:"修改",width:"155"},scopedSlots:e._u([{key:"default",fn:function(t){return 1!=t.row.staffId?[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:user:edit"],expression:"['system:user:edit']"}],staticStyle:{"margin-right":"-8px"},attrs:{icon:"el-icon-edit",size:"mini",type:"success"},on:{click:function(a){return e.handleUpdate(t.row)}}},[e._v("修改 ")]),a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:user:remove"],expression:"['system:user:remove']"}],staticStyle:{"margin-right":"-8px"},attrs:{icon:"el-icon-delete",size:"mini",type:"danger"},on:{click:function(a){return e.handleDelete(t.row)}}},[e._v("删除 ")]),e.$store.state.user.sid==t.row.staffId||1==e.$store.state.user.sid?a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:user:resetPwd"],expression:"['system:user:resetPwd']"}],staticStyle:{"margin-right":"-8px"},attrs:{icon:"el-icon-key",size:"mini",type:"primary"},on:{click:function(a){return e.handleResetPwd(t.row)}}},[e._v("重置密码 ")]):e._e()]:void 0}}],null,!0)})],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total>0"}],attrs:{limit:e.queryParams.pageSize,page:e.queryParams.pageNum,total:e.total},on:{"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},pagination:e.getList}})],1)],1),a("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"},{name:"dialogDragWidth",rawName:"v-dialogDragWidth"}],attrs:{"close-on-click-modal":!1,"modal-append-to-body":!1,title:e.title,visible:e.open,"append-to-body":"",width:"1000px"},on:{"update:visible":function(t){e.open=t}}},[a("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"90px"}},[a("el-form-item",{attrs:{label:"管理分配"}},[a("el-row",[a("el-col",{attrs:{span:4}},[a("tree-select",{ref:"dept",attrs:{pass:e.form.sqdDeptId,placeholder:"归属部门",type:"dept"},on:{return:function(t){e.form.sqdDeptId=t}}})],1),a("el-col",{attrs:{span:5}},[a("el-input",{attrs:{disabled:!e.add,placeholder:"员工编号",maxlength:"10"},model:{value:e.form.staffCode,callback:function(t){e.$set(e.form,"staffCode",t)},expression:"form.staffCode"}})],1),a("el-col",{attrs:{span:5}},[a("el-input",{attrs:{disabled:!e.add,placeholder:"登录账户",maxlength:"30"},model:{value:e.form.staffUsername,callback:function(t){e.$set(e.form,"staffUsername",t)},expression:"form.staffUsername"}})],1),a("el-col",{attrs:{span:5}},[a("el-select",{staticStyle:{width:"100%"},model:{value:e.form.staffJobStatus,callback:function(t){e.$set(e.form,"staffJobStatus",t)},expression:"form.staffJobStatus"}},e._l(e.dict.type.sys_normal_disable,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),a("el-col",{attrs:{span:5}},[a("el-input",{attrs:{disabled:"admin"!=e.$store.state.user.userRole,placeholder:"mac地址"},model:{value:e.form.macAddress,callback:function(t){e.$set(e.form,"macAddress",t)},expression:"form.macAddress"}})],1)],1)],1),a("el-form-item",{attrs:{label:"基础资料"}},[a("el-row",[a("el-col",{attrs:{span:4}},[a("el-input",{attrs:{placeholder:"员工昵称",maxlength:"30"},model:{value:e.form.staffShortName,callback:function(t){e.$set(e.form,"staffShortName",t)},expression:"form.staffShortName"}})],1),a("el-col",{attrs:{span:5}},[a("el-input",{attrs:{placeholder:"中文姓氏",maxlength:"30"},model:{value:e.form.staffFamilyLocalName,callback:function(t){e.$set(e.form,"staffFamilyLocalName",t)},expression:"form.staffFamilyLocalName"}})],1),a("el-col",{attrs:{span:5}},[a("el-input",{attrs:{placeholder:"中文名称",maxlength:"30"},model:{value:e.form.staffGivingLocalName,callback:function(t){e.$set(e.form,"staffGivingLocalName",t)},expression:"form.staffGivingLocalName"}})],1),a("el-col",{attrs:{span:5}},[a("el-input",{attrs:{placeholder:"英文姓氏",maxlength:"30"},model:{value:e.form.staffFamilyEnName,callback:function(t){e.$set(e.form,"staffFamilyEnName",t)},expression:"form.staffFamilyEnName"}})],1),a("el-col",{attrs:{span:5}},[a("el-input",{attrs:{placeholder:"英文名称",maxlength:"30"},model:{value:e.form.staffGivingEnName,callback:function(t){e.$set(e.form,"staffGivingEnName",t)},expression:"form.staffGivingEnName"}})],1),a("el-col",{attrs:{span:4}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"性别"},model:{value:e.form.staffGender,callback:function(t){e.$set(e.form,"staffGender",t)},expression:"form.staffGender"}},e._l(e.dict.type.sys_user_sex,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),a("el-col",{attrs:{span:5}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{format:"yyyy 年 MM 月 dd 日",placeholder:"生日",type:"date","value-format":"yyyy-MM-dd"},model:{value:e.form.staffBirthday,callback:function(t){e.$set(e.form,"staffBirthday",t)},expression:"form.staffBirthday"}})],1),a("el-col",{attrs:{span:5}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"证件类型"},model:{value:e.form.credentialType,callback:function(t){e.$set(e.form,"credentialType",t)},expression:"form.credentialType"}},e._l(e.dict.type.sys_credential_type,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),a("el-col",{attrs:{span:10}},[a("el-input",{attrs:{placeholder:"身份证号",maxlength:"50"},model:{value:e.form.staffIdentity,callback:function(t){e.$set(e.form,"staffIdentity",t)},expression:"form.staffIdentity"}})],1),a("el-col",{attrs:{span:4}},[a("el-input",{attrs:{placeholder:"民族",maxlength:"50"},model:{value:e.form.staffNativeplace,callback:function(t){e.$set(e.form,"staffNativeplace",t)},expression:"form.staffNativeplace"}})],1),a("el-col",{attrs:{span:5}},[a("el-input",{attrs:{placeholder:"国籍",maxlength:"50"},model:{value:e.form.staffNation,callback:function(t){e.$set(e.form,"staffNation",t)},expression:"form.staffNation"}})],1),a("el-col",{attrs:{span:5}},[a("el-input",{attrs:{placeholder:"宗教信仰",maxlength:"50"},model:{value:e.form.staffReligion,callback:function(t){e.$set(e.form,"staffReligion",t)},expression:"form.staffReligion"}})],1),a("el-col",{attrs:{span:5}},[a("el-input",{attrs:{placeholder:"政治面貌",maxlength:"50"},model:{value:e.form.staffPoliticalCountenance,callback:function(t){e.$set(e.form,"staffPoliticalCountenance",t)},expression:"form.staffPoliticalCountenance"}})],1),a("el-col",{attrs:{span:5}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"婚姻状况"},model:{value:e.form.staffMarital,callback:function(t){e.$set(e.form,"staffMarital",t)},expression:"form.staffMarital"}},e._l(e.dict.type.sys_user_marriage,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1)],1)],1),a("el-form-item",{attrs:{label:"联系方式"}},[a("el-row",[a("el-col",{attrs:{span:4}},[a("el-input",{attrs:{placeholder:"手机号码",maxlength:"11"},model:{value:e.form.staffTelNum,callback:function(t){e.$set(e.form,"staffTelNum",t)},expression:"form.staffTelNum"}})],1),a("el-col",{attrs:{span:5}},[a("el-input",{attrs:{placeholder:"工作QQ",maxlength:"20"},model:{value:e.form.staffQ,callback:function(t){e.$set(e.form,"staffQ",t)},expression:"form.staffQ"}})],1),a("el-col",{attrs:{span:5}},[a("el-input",{attrs:{placeholder:"固定电话",maxlength:"20"},model:{value:e.form.staffPhoneNum,callback:function(t){e.$set(e.form,"staffPhoneNum",t)},expression:"form.staffPhoneNum"}})],1),a("el-col",{attrs:{span:10}},[a("el-input",{attrs:{placeholder:"公司邮箱",maxlength:"50"},model:{value:e.form.staffEmailEnterprise,callback:function(t){e.$set(e.form,"staffEmailEnterprise",t)},expression:"form.staffEmailEnterprise"}})],1),a("el-col",{attrs:{span:4}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"居住性质"},model:{value:e.form.residentType,callback:function(t){e.$set(e.form,"residentType",t)},expression:"form.residentType"}},e._l(e.dict.type.sys_resident_type,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),a("el-col",{attrs:{span:5}},[a("location-select",{ref:"location",attrs:{pass:e.form.locationId,type:"location",placeholder:"居住区域"},on:{return:function(t){e.form.locationId=t}}})],1),a("el-col",{attrs:{span:15}},[a("el-input",{attrs:{maxlength:"50",placeholder:"详细地址"},model:{value:e.form.address,callback:function(t){e.$set(e.form,"address",t)},expression:"form.address"}})],1),a("el-col",{attrs:{span:4}},[a("el-input",{attrs:{placeholder:"紧急联系人",maxlength:"20"},model:{value:e.form.emergencyContactor,callback:function(t){e.$set(e.form,"emergencyContactor",t)},expression:"form.emergencyContactor"}})],1),a("el-col",{attrs:{span:5}},[a("el-input",{attrs:{placeholder:"联系人关系",maxlength:"20"},model:{value:e.form.emergencyContactorRelation,callback:function(t){e.$set(e.form,"emergencyContactorRelation",t)},expression:"form.emergencyContactorRelation"}})],1),a("el-col",{attrs:{span:15}},[a("el-input",{attrs:{placeholder:"紧急联系方式",maxlength:"20"},model:{value:e.form.emergencyPhone,callback:function(t){e.$set(e.form,"emergencyPhone",t)},expression:"form.emergencyPhone"}})],1)],1)],1),a("el-form-item",{attrs:{label:"学历信息"}},[a("el-row",[a("el-col",{attrs:{span:4}},[a("el-input",{attrs:{maxlength:"50",placeholder:"第一学历"},model:{value:e.form.firstDegree,callback:function(t){e.$set(e.form,"firstDegree",t)},expression:"form.firstDegree"}})],1),a("el-col",{attrs:{span:5}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{format:"yyyy 年 MM 月 dd 日",placeholder:"毕业时间",type:"date","value-format":"yyyy-MM-dd"},model:{value:e.form.graduationDate,callback:function(t){e.$set(e.form,"graduationDate",t)},expression:"form.graduationDate"}})],1),a("el-col",{attrs:{span:5}},[a("el-input",{attrs:{maxlength:"50",placeholder:"毕业院校"},model:{value:e.form.graduationFrom,callback:function(t){e.$set(e.form,"graduationFrom",t)},expression:"form.graduationFrom"}})],1),a("el-col",{attrs:{span:5}},[a("el-input",{attrs:{maxlength:"50",placeholder:"专业"},model:{value:e.form.major,callback:function(t){e.$set(e.form,"major",t)},expression:"form.major"}})],1),a("el-col",{attrs:{span:5}},[a("el-input",{attrs:{maxlength:"50",placeholder:"最高学历"},model:{value:e.form.degree,callback:function(t){e.$set(e.form,"degree",t)},expression:"form.degree"}})],1)],1)],1),a("el-form-item",{attrs:{label:"人事备忘"}},[a("el-row",[a("el-col",{attrs:{span:4}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{format:"yyyy 年 MM 月 dd 日",placeholder:"入职时间",type:"date","value-format":"yyyy-MM-dd"},model:{value:e.form.inductionDate,callback:function(t){e.$set(e.form,"inductionDate",t)},expression:"form.inductionDate"}})],1),a("el-col",{attrs:{span:5}},[a("el-input",{attrs:{placeholder:"健康状况",maxlength:"100"},model:{value:e.form.healthy,callback:function(t){e.$set(e.form,"healthy",t)},expression:"form.healthy"}})],1),a("el-col",{attrs:{span:5}},[a("el-input",{attrs:{maxlength:"50",placeholder:"过敏源"},model:{value:e.form.allergySource,callback:function(t){e.$set(e.form,"allergySource",t)},expression:"form.allergySource"}})],1),a("el-col",{attrs:{span:10}},[a("el-input",{attrs:{maxlength:"100",placeholder:"特别注意"},model:{value:e.form.specifically,callback:function(t){e.$set(e.form,"specifically",t)},expression:"form.specifically"}})],1)],1),a("el-input",{attrs:{autosize:{minRows:5,maxRows:20},maxlength:"200",placeholder:"人事部备注","show-word-limit":"",type:"textarea"},model:{value:e.form.remark,callback:function(t){e.$set(e.form,"remark",t)},expression:"form.remark"}})],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("确 定")]),a("el-button",{on:{click:e.cancel}},[e._v("取 消")])],1)],1),a("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"},{name:"dialogDragWidth",rawName:"v-dialogDragWidth"}],attrs:{"close-on-click-modal":!1,"modal-append-to-body":!1,title:e.upload.title,visible:e.upload.open,"append-to-body":"",width:"400px"},on:{"update:visible":function(t){return e.$set(e.upload,"open",t)}}},[a("el-upload",{ref:"upload",attrs:{action:e.upload.url+"?updateSupport="+e.upload.updateSupport,"auto-upload":!1,disabled:e.upload.isUploading,headers:e.upload.headers,limit:1,"on-progress":e.handleFileUploadProgress,"on-success":e.handleFileSuccess,accept:".xlsx, .xls",drag:""}},[a("i",{staticClass:"el-icon-upload"}),a("div",{staticClass:"el-upload__text"},[e._v("将文件拖到此处，或"),a("em",[e._v("点击上传")])]),a("div",{staticClass:"el-upload__tip text-center",attrs:{slot:"tip"},slot:"tip"},[a("div",{staticClass:"el-upload__tip",attrs:{slot:"tip"},slot:"tip"},[a("el-checkbox",{model:{value:e.upload.updateSupport,callback:function(t){e.$set(e.upload,"updateSupport",t)},expression:"upload.updateSupport"}}),e._v(" 是否更新已经存在的用户数据 ")],1),a("span",[e._v("仅允许导入xls、xlsx格式文件。")]),a("el-link",{staticStyle:{"font-size":"12px","vertical-align":"baseline"},attrs:{underline:!1,type:"primary"},on:{click:e.importTemplate}},[e._v("下载模板 ")])],1)]),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:e.submitFileForm}},[e._v("确 定")]),a("el-button",{on:{click:function(t){e.upload.open=!1}}},[e._v("取 消")])],1)],1)],1)},s=[],o=a("5530"),r=a("b85c"),n=(a("d81d"),a("c0c7")),i=a("5f87"),c=a("ca17"),f=a.n(c),u=(a("542c"),a("dce4")),m=a("4ddb"),d=a("a287"),p=a("4360"),h={name:"User",dicts:["sys_normal_disable","sys_user_sex","sys_user_marriage","sys_yes_no","sys_upload_status","sys_credential_type","sys_resident_type"],components:{Treeselect:f.a},data:function(){return{add:!1,showLeft:3,showRight:21,loading:!0,ids:[],total:0,single:!0,multiple:!0,showSearch:!1,userList:null,title:"",accountTitle:"",open:!1,openAccount:!1,deptLocalName:void 0,initPassword:void 0,form:{},i:0,upload:{open:!1,title:"",isUploading:!1,updateSupport:0,headers:{Authorization:"Bearer "+Object(i["a"])()},url:"/prod-api/system/user/importData"},queryParams:{pageNum:1,pageSize:20,staffUsername:void 0,staffPhoneNum:void 0,staffJobStatus:void 0,staffLocalName:void 0,staffEnName:void 0,staffEmailEnterprise:void 0,sqdDeptId:void 0},rules:{staffUsername:[{required:!0,trigger:"blur"},{min:5,max:15,message:"账户名称长度必须介于 5 和 15 之间",trigger:"blur"}],staffFamilyLocalName:[{required:!0,trigger:"blur"}],staffPhoneNum:[{pattern:/^1[3|4|5|6|7|8|9][0-9]\d{8}$/,message:"正确的手机号码",trigger:"blur"}],staffPassword:[{required:!0,message:"用户密码不能为空",trigger:"blur"},{min:5,max:20,message:"用户密码长度必须介于 5 和 20 之间",trigger:"blur"}],staffEmailEnterprise:[{type:"email",message:"正确的邮箱地址",trigger:["blur","change"]}]}}},created:function(){var e=this;this.getList(),this.getConfigKey("sys.user.initPassword").then((function(t){e.initPassword=t.msg})),this.getDeptList()},computed:{columns:{get:function(){return this.$store.state.listSettings.staffSetting}}},watch:{showSearch:function(e){1==e?(this.showRight=21,this.showLeft=3):(this.showRight=24,this.showLeft=0)}},methods:{getDeptList:function(){var e=this;if(0==this.$store.state.data.deptList.length||this.$store.state.data.redisList.dept)p["a"].dispatch("getDeptList").then((function(){var t,a=e.$store.state.data.deptList[0].children,l=Object(r["a"])(a[0].children);try{for(l.s();!(t=l.n()).done;){var s=t.value;s.children&&delete s.children}}catch(o){l.e(o)}finally{l.f()}e.$refs.dept&&e.$refs.dept.getLoadOptions(a)}));else{var t,a=this.$store.state.data.deptList[0].children,l=Object(r["a"])(a[0].children);try{for(l.s();!(t=l.n()).done;){var s=t.value;s.children&&delete s.children}}catch(o){l.e(o)}finally{l.f()}this.$refs.dept&&this.$refs.dept.getLoadOptions(a)}},getList:function(){var e=this;this.loading=!0,u["a"].hasPermi("system:user:query")?Object(n["d"])(this.queryParams).then((function(t){e.userList=t.rows,e.total=t.total,e.loading=!1})):Object(n["h"])(this.queryParams).then((function(t){e.userList=t.rows,e.total=t.total,e.loading=!1}))},loadStaffRoles:function(e){if(null==e.midRsStaffRoles){var t={staffId:e.staffId};Object(m["d"])(t).then((function(t){e.midRsStaffRoles=t.rows}))}},loadAccount:function(e){if(null==e.staffAccounts){var t={accountOwnerId:e.staffId,belongTo:0};Object(d["e"])(t).then((function(t){e.staffAccounts=t.rows}))}},handleStatusChange:function(e){var t=this,a="0"==e.staffJobStatus?"启用":"停用";this.$confirm('确认要"'+a+'""'+e.staffUsername+'"用户吗？',"提示",{customClass:"modal-confirm"}).then((function(){return Object(n["b"])(e.staffId,e.staffJobStatus)})).then((function(){t.$modal.msgSuccess(a+"成功")})).catch((function(){e.staffJobStatus="0"==e.staffJobStatus?"1":"0"}))},cancel:function(){this.open=!1,this.reset()},reset:function(){this.form={staffId:null,staffCode:null,sqdDeptId:null,staffUsername:null,staffPassword:null,staffShortName:null,staffFamilyLocalName:null,staffGivingLocalName:null,staffFamilyEnName:null,staffGivingEnName:null,staffEmailEnterprise:null,staffIdentity:null,staffTelNum:null,staffPhoneNum:null,staffAvatar:null,staffBirthday:null,staffQ:null,staffGender:null,staffNativeplace:null,locationId:null,staffAddress:null,emergencyContactor:null,emergencyContactorRelation:null,emergencyPhone:null,staffGraduation:null,credentialType:null,staffMarital:null,staffNation:null,staffReligion:null,staffPoliticalCountenance:null,residentType:null,firstDegree:null,graduationDate:null,graduationFrom:null,major:null,degree:null,inductionDate:null,healthy:null,allergySource:null,specifically:null,staffJobStatus:"0",loginIp:null,loginDate:null,macAddress:null,remark:null,createBy:null,createTime:null,updateBy:null,updateTime:null,deleteBy:null,deleteTime:null,deleteStatus:"0"},this.resetForm("form")},handleQuery:function(){this.getList()},resetQuery:function(){this.dateRange=[],this.resetForm("queryForm"),this.queryParams.sqdDeptId=null,this.$refs.tree.setCurrentKey(null),this.handleQuery()},handleSelectionChange:function(e){this.ids=e.map((function(e){return e.staffId})),this.single=1!=e.length,this.multiple=!e.length},handleAdd:function(){var e=this;this.reset(),Object(n["f"])(this.$store.state.user.sid).then((function(t){e.postOptions=t.posts,e.open=!0,e.title="添加用户",e.form.staffPassword=e.initPassword,e.add=!0,e.$nextTick((function(){e.getDeptList()}))}))},handleUpdate:function(e){var t=this;this.reset();var a=e.staffId||this.ids;Object(n["f"])(a).then((function(a){t.form=a.data,t.postOptions=a.posts,t.$set(t.form,"postIds",a.postIds),t.$set(t.form,"roleIds",a.roleIds),t.open=!0,t.title="修改用户",void 0!=e.locationLocalName&&t.$refs.location.remoteMethod(e.locationLocalName),t.$nextTick((function(){t.getDeptList()}))}))},handleResetPwd:function(e){var t=this;this.$prompt('"'+e.staffUsername+'"的新密码',"提示",{confirmButtonText:"确定",cancelButtonText:"取消",closeOnClickModal:!1,inputPattern:/^.{5,20}$/,inputErrorMessage:"用户密码长度必须介于 5 和 20 之间"}).then((function(a){var l=a.value;Object(n["i"])(e.staffId,l).then((function(e){t.$modal.msgSuccess("修改成功，新密码是："+l)}))})).catch((function(){}))},submitForm:function(){var e=this;this.$refs["form"].validate((function(t){t&&(void 0!=e.form.staffId?Object(n["k"])(e.form).then((function(t){e.$modal.msgSuccess("修改成功"),e.open=!1,e.getList(),e.add=!1})):(e.form.staffType="rich",Object(n["a"])(e.form).then((function(t){e.$modal.msgSuccess("新增成功"),e.open=!1,e.getList(),e.add=!1}))))}))},handleDelete:function(e){var t=this,a=e.staffId||this.ids;this.$confirm('是否确认删除用户编号为"'+e.staffCode+"/"+e.staffGivingLocalName+e.staffFamilyLocalName+"/"+e.staffFamilyEnName+e.staffGivingEnName+'"的数据项？',"提示",{customClass:"modal-confirm"}).then((function(){return Object(n["c"])(a)})).then((function(){t.getList(),t.$modal.msgSuccess("删除成功")})).catch((function(){}))},handleExport:function(){this.download("system/user/export",Object(o["a"])({},this.queryParams),"user_".concat((new Date).getTime(),".xlsx"))},handleImport:function(){this.upload.title="用户导入",this.upload.open=!0},importTemplate:function(){this.download("system/user/importTemplate",{},"user_template_".concat((new Date).getTime(),".xlsx"))},handleFileUploadProgress:function(e,t,a){this.upload.isUploading=!0},handleFileSuccess:function(e,t,a){this.upload.open=!1,this.upload.isUploading=!1,this.$refs.upload.clearFiles(),this.$alert("<div style='overflow: auto;overflow-x: hidden;max-height: 70vh;padding: 10px 20px 0;'>"+e.msg+"</div>","导入结果",{dangerouslyUseHTMLString:!0}),this.getList()},submitFileForm:function(){this.$refs.upload.submit()},getDept:function(e){this.queryParams.sqdDeptId=e,this.handleQuery()}}},y=h,g=a("2877"),b=Object(g["a"])(y,l,s,!1,null,null,null);t["default"]=b.exports},"4ddb":function(e,t,a){"use strict";a.d(t,"d",(function(){return s})),a.d(t,"e",(function(){return o})),a.d(t,"c",(function(){return r})),a.d(t,"a",(function(){return n})),a.d(t,"f",(function(){return i})),a.d(t,"b",(function(){return c}));var l=a("b775");function s(e){return Object(l["a"])({url:"/system/staffrole/list",method:"get",params:e})}function o(e){return Object(l["a"])({url:"/system/staffrole/menuList",method:"get",params:e})}function r(e){return Object(l["a"])({url:"/system/staffrole/"+e,method:"get"})}function n(e){return Object(l["a"])({url:"/system/staffrole",method:"post",data:e})}function i(e){return Object(l["a"])({url:"/system/staffrole",method:"put",data:e})}function c(e){return Object(l["a"])({url:"/system/staffrole/"+e,method:"delete"})}}}]);