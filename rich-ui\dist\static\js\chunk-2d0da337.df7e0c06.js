(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d0da337"],{"6b1f":function(e,r,c){"use strict";c.r(r);var o=function(){var e=this,r=e.$createElement,c=e._self._c||r;return c("div",[c("h6",{staticStyle:{margin:"0"}},[e._v(e._s(0!=e.scope.row.cargoPrice?e.scope.row.cargoPrice+e.scope.row.cargoCurrency:""))])])},t=[],a={name:"cargoPrice",props:["scope"],data:function(){return{size:this.$store.state.app.size||"mini"}}},n=a,s=c("2877"),i=Object(s["a"])(n,o,t,!1,null,"4b4eb70a",null);r["default"]=i.exports}}]);