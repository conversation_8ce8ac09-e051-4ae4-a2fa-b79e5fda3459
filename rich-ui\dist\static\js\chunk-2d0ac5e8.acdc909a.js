(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d0ac5e8"],{"18f1":function(e,t,r){"use strict";r.r(t);var o=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",[r("el-tooltip",{attrs:{disabled:null==e.scope.row.remark||e.scope.row.remark.length<22,placement:"top"}},[r("div",{attrs:{slot:"content"},slot:"content"},[r("h6",{staticStyle:{margin:"0"}},[e._v(" "+e._s(e.scope.row.remark)+" ")])]),r("div",[r("h6",{staticStyle:{margin:"0",overflow:"hidden","text-overflow":"ellipsis"}},[e._v(" "+e._s(e.scope.row.remark)+" ")])])])],1)},s=[],n={name:"remark",props:["scope"]},a=n,l=r("2877"),c=Object(l["a"])(a,o,s,!1,null,"7e8a6be1",null);t["default"]=c.exports}}]);