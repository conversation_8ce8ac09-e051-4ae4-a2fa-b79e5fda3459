(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d0f066e"],{"9be4":function(e,t,o){"use strict";o.r(t);var a=function(){var e=this,t=e.$createElement,o=e._self._c||t;return o("div",{staticClass:"app-container"},[o("el-row",{attrs:{gutter:20}},[o("el-col",{attrs:{span:e.showLeft}},[o("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],ref:"queryForm",staticClass:"query",attrs:{model:e.queryParams,size:"mini",inline:!0,"label-width":"68px"}},[o("el-form-item",{attrs:{label:"简称",prop:"docFlowDirectionShortName"}},[o("el-input",{attrs:{placeholder:"简称",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.docFlowDirectionShortName,callback:function(t){e.$set(e.queryParams,"docFlowDirectionShortName",t)},expression:"queryParams.docFlowDirectionShortName"}})],1),o("el-form-item",{attrs:{label:"中文名",prop:"docFlowDirectionLocalName"}},[o("el-input",{attrs:{placeholder:"中文名",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.docFlowDirectionLocalName,callback:function(t){e.$set(e.queryParams,"docFlowDirectionLocalName",t)},expression:"queryParams.docFlowDirectionLocalName"}})],1),o("el-form-item",{attrs:{label:"英文名",prop:"docFlowDirectionEnName"}},[o("el-input",{attrs:{placeholder:"英文名",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.docFlowDirectionEnName,callback:function(t){e.$set(e.queryParams,"docFlowDirectionEnName",t)},expression:"queryParams.docFlowDirectionEnName"}})],1),o("el-form-item",{attrs:{label:"排序",prop:"orderNum"}},[o("el-input",{attrs:{placeholder:"排序",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.orderNum,callback:function(t){e.$set(e.queryParams,"orderNum",t)},expression:"queryParams.orderNum"}})],1),o("el-form-item",[o("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"mini"},on:{click:e.handleQuery}},[e._v("搜索")]),o("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1)],1),o("el-col",{attrs:{span:e.showRight}},[o("el-row",{staticClass:"mb8",attrs:{gutter:10}},[o("el-col",{attrs:{span:1.5}},[o("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:docflowdirection:add"],expression:"['system:docflowdirection:add']"}],attrs:{type:"primary",plain:"",icon:"el-icon-plus",size:"mini"},on:{click:e.handleAdd}},[e._v("新增 ")])],1),o("el-col",{attrs:{span:1.5}},[o("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:docflowdirection:edit"],expression:"['system:docflowdirection:edit']"}],attrs:{type:"success",plain:"",icon:"el-icon-edit",size:"mini",disabled:e.single},on:{click:e.handleUpdate}},[e._v("修改 ")])],1),o("el-col",{attrs:{span:1.5}},[o("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:docflowdirection:remove"],expression:"['system:docflowdirection:remove']"}],attrs:{type:"danger",plain:"",icon:"el-icon-delete",size:"mini",disabled:e.multiple},on:{click:e.handleDelete}},[e._v("删除 ")])],1),o("el-col",{attrs:{span:1.5}},[o("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:docflowdirection:export"],expression:"['system:docflowdirection:export']"}],attrs:{type:"warning",plain:"",icon:"el-icon-download",size:"mini"},on:{click:e.handleExport}},[e._v("导出 ")])],1),o("right-toolbar",{attrs:{showSearch:e.showSearch},on:{"update:showSearch":function(t){e.showSearch=t},"update:show-search":function(t){e.showSearch=t},queryTable:e.getList}})],1),o("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.docflowdirectionList},on:{"selection-change":e.handleSelectionChange}},[o("el-table-column",{attrs:{type:"selection",width:"28",align:"center"}}),o("el-table-column",{attrs:{label:"简称",align:"center",prop:"docFlowDirectionShortName"}}),o("el-table-column",{attrs:{label:"中文名",align:"center",prop:"docFlowDirectionLocalName"}}),o("el-table-column",{attrs:{label:"英文名",align:"center",prop:"docFlowDirectionEnName"}}),o("el-table-column",{attrs:{label:"代码",align:"center",prop:"code"}}),o("el-table-column",{attrs:{label:"排序",align:"center",prop:"orderNum"}}),o("el-table-column",{attrs:{label:"状态",align:"center",prop:"status"},scopedSlots:e._u([{key:"default",fn:function(t){return[o("el-switch",{attrs:{"active-value":"0","inactive-value":"1"},on:{change:function(o){return e.handleStatusChange(t.row)}},model:{value:t.row.status,callback:function(o){e.$set(t.row,"status",o)},expression:"scope.row.status"}})]}}])}),o("el-table-column",{attrs:{label:"备注",align:"center",prop:"remark"}}),o("el-table-column",{attrs:{label:"操作",align:"center","class-name":"small-padding fixed-width"},scopedSlots:e._u([{key:"default",fn:function(t){return[o("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:docflowdirection:edit"],expression:"['system:docflowdirection:edit']"}],staticStyle:{"margin-right":"-8px"},attrs:{size:"mini",type:"success",icon:"el-icon-edit"},on:{click:function(o){return e.handleUpdate(t.row)}}},[e._v("修改 ")]),o("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:docflowdirection:remove"],expression:"['system:docflowdirection:remove']"}],staticStyle:{"margin-right":"-8px"},attrs:{size:"mini",type:"danger",icon:"el-icon-delete"},on:{click:function(o){return e.handleDelete(t.row)}}},[e._v("删除 ")])]}}])})],1),o("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total>0"}],attrs:{total:e.total,page:e.queryParams.pageNum,limit:e.queryParams.pageSize},on:{"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},pagination:e.getList}})],1)],1),o("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"},{name:"dialogDragWidth",rawName:"v-dialogDragWidth"}],attrs:{"close-on-click-modal":!1,"modal-append-to-body":!1,title:e.title,visible:e.open,width:"500px","append-to-body":""},on:{"update:visible":function(t){e.open=t}}},[o("el-form",{ref:"form",staticClass:"edit",attrs:{model:e.form,rules:e.rules,"label-width":"80px"}},[o("el-form-item",{attrs:{label:"简称",prop:"docFlowDirectionShortName"}},[o("el-input",{attrs:{placeholder:"简称"},model:{value:e.form.docFlowDirectionShortName,callback:function(t){e.$set(e.form,"docFlowDirectionShortName",t)},expression:"form.docFlowDirectionShortName"}})],1),o("el-form-item",{attrs:{label:"中文名",prop:"docFlowDirectionLocalName"}},[o("el-input",{attrs:{placeholder:"中文名"},model:{value:e.form.docFlowDirectionLocalName,callback:function(t){e.$set(e.form,"docFlowDirectionLocalName",t)},expression:"form.docFlowDirectionLocalName"}})],1),o("el-form-item",{attrs:{label:"英文名",prop:"docFlowDirectionEnName"}},[o("el-input",{attrs:{placeholder:"英文名"},model:{value:e.form.docFlowDirectionEnName,callback:function(t){e.$set(e.form,"docFlowDirectionEnName",t)},expression:"form.docFlowDirectionEnName"}})],1),o("el-form-item",{attrs:{label:"代码",prop:"code"}},[o("el-input",{attrs:{placeholder:"代码"},model:{value:e.form.code,callback:function(t){e.$set(e.form,"code",t)},expression:"form.code"}})],1),o("el-form-item",{attrs:{label:"排序",prop:"orderNum"}},[o("el-input-number",{staticStyle:{width:"100%"},attrs:{controls:!1,placeholder:"排序"},model:{value:e.form.orderNum,callback:function(t){e.$set(e.form,"orderNum",t)},expression:"form.orderNum"}})],1),o("el-form-item",{attrs:{label:"备注",prop:"remark"}},[o("el-input",{attrs:{placeholder:"备注"},model:{value:e.form.remark,callback:function(t){e.$set(e.form,"remark",t)},expression:"form.remark"}})],1)],1),o("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[o("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("确 定")]),o("el-button",{on:{click:e.cancel}},[e._v("取 消")])],1)],1)],1)},r=[],l=o("5530"),i=(o("d81d"),o("6345")),n={name:"Docflowdirection",data:function(){return{showLeft:3,showRight:21,loading:!0,ids:[],single:!0,multiple:!0,showSearch:!0,total:0,docflowdirectionList:[],title:"",open:!1,queryParams:{pageNum:1,pageSize:20,docFlowDirectionShortName:null,docFlowDirectionLocalName:null,docFlowDirectionEnName:null,orderNum:null,status:null},form:{},rules:{}}},watch:{showSearch:function(e){!0===e?(this.showRight=21,this.showLeft=3):(this.showRight=24,this.showLeft=0)}},created:function(){this.getList()},methods:{getList:function(){var e=this;this.loading=!0,Object(i["e"])(this.queryParams).then((function(t){e.docflowdirectionList=t.rows,e.total=t.total,e.loading=!1}))},cancel:function(){this.open=!1,this.reset()},reset:function(){this.form={docFlowDirectionId:null,docFlowDirectionShortName:null,docFlowDirectionLocalName:null,docFlowDirectionEnName:null,orderNum:null,status:"0",remark:null,createBy:null,createTime:null,updateBy:null,updateTime:null,deleteBy:null,deleteTime:null,deleteStatus:"0"},this.resetForm("form")},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.resetForm("queryForm"),this.handleQuery()},handleStatusChange:function(e){var t=this,o="0"===e.status?"启用":"停用";this.$confirm('确认要"'+o+"吗？","提示",{customClass:"modal-confirm"}).then((function(){return Object(i["b"])(e.docFlowDirectionId,e.status)})).then((function(){t.$modal.msgSuccess(o+"成功")})).catch((function(){e.status="0"===e.status?"1":"0"}))},handleSelectionChange:function(e){this.ids=e.map((function(e){return e.docFlowDirectionId})),this.single=1!==e.length,this.multiple=!e.length},handleAdd:function(){this.reset(),this.open=!0,this.title="添加文件流向"},handleUpdate:function(e){var t=this;this.reset();var o=e.docFlowDirectionId||this.ids;Object(i["d"])(o).then((function(e){t.form=e.data,t.open=!0,t.title="修改文件流向"}))},submitForm:function(){var e=this;this.$refs["form"].validate((function(t){t&&(null!=e.form.docFlowDirectionId?Object(i["g"])(e.form).then((function(t){e.$modal.msgSuccess("修改成功"),e.open=!1,e.getList()})):Object(i["a"])(e.form).then((function(t){e.$modal.msgSuccess("新增成功"),e.open=!1,e.getList()})))}))},handleDelete:function(e){var t=this,o=e.docFlowDirectionId||this.ids;this.$confirm('是否确认删除文件流向编号为"'+o+'"的数据项？',"提示",{customClass:"modal-confirm"}).then((function(){return Object(i["c"])(o)})).then((function(){t.getList(),t.$modal.msgSuccess("删除成功")})).catch((function(){}))},handleExport:function(){this.download("system/docflowdirection/export",Object(l["a"])({},this.queryParams),"docflowdirection_".concat((new Date).getTime(),".xlsx"))}}},s=n,c=o("2877"),d=Object(c["a"])(s,a,r,!1,null,null,null);t["default"]=d.exports}}]);