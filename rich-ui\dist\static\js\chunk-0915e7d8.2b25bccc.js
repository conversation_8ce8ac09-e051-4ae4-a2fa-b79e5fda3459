(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-0915e7d8","chunk-95a89c3c","chunk-2e28b918","chunk-2d0c1231","chunk-2d0cfc88","chunk-2d22252d","chunk-2d0d69a4"],{"06ee":function(e,t,a){"use strict";a.d(t,"d",(function(){return o})),a.d(t,"c",(function(){return i})),a.d(t,"a",(function(){return l})),a.d(t,"h",(function(){return n})),a.d(t,"j",(function(){return s})),a.d(t,"b",(function(){return c})),a.d(t,"f",(function(){return d})),a.d(t,"e",(function(){return u})),a.d(t,"g",(function(){return f})),a.d(t,"i",(function(){return p}));var r=a("b775");function o(e){return Object(r["a"])({url:"/system/quotation/list",method:"get",params:e})}function i(e){return Object(r["a"])({url:"/system/quotation/"+e,method:"get"})}function l(e){return Object(r["a"])({url:"/system/quotation",method:"post",data:e})}function n(e){return Object(r["a"])({url:"/system/quotation",method:"put",data:e})}function s(e){return Object(r["a"])({url:"/system/quotation/remark",method:"put",data:e})}function c(e){return Object(r["a"])({url:"/system/quotation/"+e,method:"delete"})}function d(e){return Object(r["a"])({url:"/system/quotation/queryFreight",method:"post",data:e})}function u(e){return Object(r["a"])({url:"/system/quotation/queryCharacteristics",method:"post",data:e})}function f(e){return Object(r["a"])({url:"/system/quotation/queryLocal",method:"post",data:e})}function p(e){return Object(r["a"])({url:"/system/quotation/quotationFreight",method:"post",data:e})}},"18c9":function(e,t,a){},"20f5":function(e,t,a){"use strict";a.d(t,"f",(function(){return o})),a.d(t,"c",(function(){return i})),a.d(t,"d",(function(){return l})),a.d(t,"a",(function(){return n})),a.d(t,"k",(function(){return s})),a.d(t,"b",(function(){return c})),a.d(t,"i",(function(){return d})),a.d(t,"j",(function(){return u})),a.d(t,"g",(function(){return f})),a.d(t,"h",(function(){return p})),a.d(t,"e",(function(){return m}));var r=a("b775");function o(e){return Object(r["a"])({url:"/system/rct/list",method:"get",params:e})}function i(e){return Object(r["a"])({url:"/system/rct/"+e,method:"get"})}function l(){return Object(r["a"])({url:"/system/rctold/mon",method:"get"})}function n(e){return Object(r["a"])({url:"/system/rct",method:"post",data:e})}function s(e){return Object(r["a"])({url:"/system/rct",method:"put",data:e})}function c(e){return Object(r["a"])({url:"/system/rct/"+e,method:"delete"})}function d(e){return Object(r["a"])({url:"/system/rctold/saveRctLogistics",method:"post",data:e})}function u(e){return Object(r["a"])({url:"/system/rctold/saveRctPreCarriage",method:"post",data:e})}function f(e){return Object(r["a"])({url:"/system/rctold/saveRctExportDeclaration",method:"post",data:e})}function p(e){return Object(r["a"])({url:"/system/rctold/saveRctImportClearance",method:"post",data:e})}function m(){return Object(r["a"])({url:"system/rctold/getRctStatistics",method:"get"})}},"34bb":function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-col",{style:{display:e.openOpHistory?"":"none"},attrs:{span:16}},[a("div",{staticClass:"titleStyle"},[a("div",{staticClass:"titleText"},[e._v("操作历史记录（文件管理）")])]),a("div",{class:{inactive:0==e.openOpHistory,active:e.openOpHistory}},[a("el-table",{staticClass:"pd0",attrs:{data:e.opHistory,border:""}},[a("el-table-column",{attrs:{align:"center",label:"操作进度流水",prop:"operationalProcessId","show-tooltip-when-overflow":"",width:"80px"}}),a("el-table-column",{attrs:{align:"center",label:"进度名称",prop:"processId",width:"68px"},scopedSlots:e._u([{key:"default",fn:function(e){return[a("tree-select",{attrs:{flat:!1,multiple:!1,pass:e.row.processId,placeholder:"进度状态",type:"process"},on:{return:function(t){e.row.processId=t}}})]}}])}),a("el-table-column",{attrs:{align:"center",label:"进度状态",prop:"processStatusId",width:"68px"},scopedSlots:e._u([{key:"default",fn:function(e){return[a("tree-select",{attrs:{flat:!1,multiple:!1,pass:e.row.processStatusId,placeholder:"进度状态",type:"processStatus"},on:{return:function(t){e.row.processStatusId=t}}})]}}])}),a("el-table-column",{attrs:{align:"center",label:"发送方",prop:"senderId",width:"68px"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-select",{attrs:{placeholder:"发送方",filterable:""},on:{change:function(a){return e.getSender(a,t.row)}},model:{value:t.row.senderId,callback:function(a){e.$set(t.row,"senderId",a)},expression:"scope.row.senderId"}},e._l(e.$store.state.data.companyList,(function(e){return a("el-option",{key:e.companyId,attrs:{label:e.companyShortName,value:e.companyId}})})),1)]}}])}),a("el-table-column",{attrs:{align:"center",label:"接收方",prop:"receiverId",width:"68px"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-select",{attrs:{filterable:"",placeholder:"接收方"},on:{change:function(a){return e.getReceiver(a,t.row)}},model:{value:t.row.receiverId,callback:function(a){e.$set(t.row,"receiverId",a)},expression:"scope.row.receiverId"}},e._l(e.$store.state.data.companyList,(function(e){return a("el-option",{key:e.companyId,attrs:{label:e.companyShortName,value:e.companyId}})})),1)]}}])}),a("el-table-column",{attrs:{label:"随附文件列表"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._l(t.row.docList,(function(r){return a("el-button",{key:r.docDetailId,staticStyle:{padding:"0"},attrs:{size:"small",type:"text"},on:{click:function(a){return e.checkDoc(r,t.row)}}},[e._v(" "+e._s("["+r.flowNo+"]")+" ")])})),a("el-button",{staticStyle:{padding:"0"},attrs:{size:"mini",type:"text"},on:{click:function(a){return e.handleAddDocList(t.row)}}},[e._v(" [＋] ")])]}}])}),a("el-table-column",{attrs:{align:"center",label:"交互方式",prop:"releaseWayId",width:"68px"},scopedSlots:e._u([{key:"default",fn:function(e){return[a("tree-select",{attrs:{flat:!1,multiple:!1,pass:e.row.releaseWayId,placeholder:"交互方式",type:"docReleaseWay"},on:{return:function(t){e.row.releaseWayId=t}}})]}}])}),a("el-table-column",{attrs:{align:"center",label:"发生时间",prop:"processStatusTime",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{clearable:"",placeholder:"进度发生时间",type:"date","value-format":"yyyy-MM-dd"},model:{value:t.row.processStatusTime,callback:function(a){e.$set(t.row,"processStatusTime",a)},expression:"scope.row.processStatusTime"}})]}}])}),a("el-table-column",{attrs:{align:"center",label:"备注",prop:"remark"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-input",{attrs:{placeholder:"备注"},model:{value:t.row.remark,callback:function(a){e.$set(t.row,"remark",a)},expression:"scope.row.remark"}})]}}])}),a("el-table-column",{attrs:{align:"center",label:"发送",width:"50px"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{staticStyle:{padding:"0"},attrs:{icon:"el-icon-s-promotion",size:"mini",type:"primary"},on:{click:e.handleSend}},[e._v("Send ")])]}}])}),a("el-table-column",{attrs:{align:"center",label:"操作员",prop:"opName",width:"50px"}}),a("el-table-column",{attrs:{align:"center",label:"录入时间",prop:"createTime",width:"80"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(e.parseTime(t.row.createTime,"{y}/{m}/{d}"))+" ")]}}])}),a("el-table-column",{attrs:{align:"center","class-name":"small-padding fixed-width",label:"操作",width:"50"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{attrs:{icon:"el-icon-delete",size:"mini",type:"danger"},on:{click:function(a){return e.delOpHistory(t.row)}}},[e._v("删除 ")])]}}])})],1),a("el-button",{staticStyle:{padding:"0"},attrs:{type:"text"},on:{click:e.addOpHistory}},[e._v("[＋] ")]),a("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"},{name:"dialogDragWidth",rawName:"v-dialogDragWidth"}],attrs:{"close-on-click-modal":!1,"modal-append-to-body":!1,visible:e.open,"append-to-body":"",title:"新增操作历史记录",width:"500px"},on:{"update:visible":function(t){e.open=t}}},[a("el-form",{ref:"docList",attrs:{model:e.doc,border:"","label-width":"68px"}},[a("el-form-item",{attrs:{label:"流向编号",prop:"flowNo"}},[a("el-input",{attrs:{placeholder:"文件名/流向编号"},model:{value:e.doc.flowNo,callback:function(t){e.$set(e.doc,"flowNo",t)},expression:"doc.flowNo"}})],1),a("el-form-item",{attrs:{label:"文件类型",prop:"docId"}},[a("tree-select",{attrs:{flat:!1,multiple:!1,pass:e.doc.docId,placeholder:"文件类型",type:"doc"},on:{return:function(t){e.doc.docId=t}}})],1),a("el-form-item",{attrs:{label:"文件流向",prop:"docFlowDirectionId"}},[a("tree-select",{attrs:{flat:!1,multiple:!1,pass:e.doc.docFlowDirectionId,placeholder:"文件流向",type:"docFlowDirection"},on:{return:function(t){e.doc.docFlowDirectionId=t}}})],1),a("el-form-item",{attrs:{label:"文件形式",prop:"issueTypeId"}},[a("tree-select",{attrs:{flat:!1,multiple:!1,pass:e.doc.issueTypeId,placeholder:"文件形式",type:"docIssueType"},on:{return:function(t){e.doc.issueTypeId=t}}})],1),a("el-form-item",{attrs:{label:"创建时间",prop:"createTime"}},[e._v(" "+e._s(e.parseTime(e.doc.createTime,"{y}/{m}/{d}"))+" ")]),a("el-form-item",{attrs:{label:"备注",prop:"remark"}},[a("el-input",{attrs:{autosize:{minRows:1},maxlength:"300",placeholder:"备注","show-word-limit":"",type:"textarea"},model:{value:e.doc.remark,callback:function(t){e.$set(e.doc,"remark",t)},expression:"doc.remark"}})],1),a("el-form-item",{attrs:{label:"创建人",prop:"createByName"}},[e._v(" "+e._s(e.doc.createByName)+" ")]),a("el-form-item",{attrs:{label:"录入时间",prop:"updateTime"}},[e._v(" "+e._s(e.parseTime(e.doc.updateTime,"{y}/{m}/{d}"))+" ")]),a("file-upload",{attrs:{"file-type":["xlsx","xls","docx","doc","pdf"],limit:3,value:e.doc.fileList},on:{input:function(t){e.doc.fileList=t}}})],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:e.submitDoc}},[e._v("确 定")]),a("el-button",{attrs:{type:"danger"},on:{click:e.delDoc}},[e._v("删 除")]),a("el-button",{on:{click:e.cancel}},[e._v("取 消")])],1)],1)],1)])},o=[],i=(a("b0c0"),a("14d9"),a("4de4"),a("d3b7"),a("fba1")),l=a("3a13"),n=a("b775");function s(e){return Object(n["a"])({url:"/system/docdetail/"+e,method:"get"})}function c(e){return Object(n["a"])({url:"/system/docdetail",method:"post",data:e})}function d(e){return Object(n["a"])({url:"/system/docdetail",method:"put",data:e})}function u(e){return Object(n["a"])({url:"/system/docdetail/"+e,method:"delete"})}var f={name:"opHistory",props:["opHistory","openOpHistory","typeId","rctId","basicInfoId"],data:function(){return{open:!1,doc:{},dList:{},receive:!1,send:!1}},watch:{opHistory:function(e){this.$emit("return",e)}},methods:{handleAddDocList:function(e){this.open=!0,this.doc={operationalProcessId:e.operationalProcessId,docId:null,flowNo:null,docFlowDirectionId:null,issueTypeId:null,fileList:null,createBy:this.$store.state.user.sid,createByName:this.$store.state.user.name.split(" ")[1],createTime:Object(i["f"])(new Date),remark:null,updateTime:Object(i["f"])(new Date)},this.dList=e},checkDoc:function(e,t){var a=this;s(e.docDetailId).then((function(e){a.doc=e.data,a.open=!0})),this.dList=t},getSender:function(e,t){this.send=!0,this.receive=!1,this.receive||(t.senderId=e,t.receiverId=1)},getReceiver:function(e,t){this.receive=!0,this.send=!1,this.send||(t.receiverId=e,t.senderId=1)},handleSend:function(){},addOpHistory:function(){var e=this,t={docList:[],typeId:this.typeId,rctId:this.rctId,basicInfoId:this.basicInfoId,processId:null,processStatusId:null,senderId:null,receiverId:null,releaseWayId:null,processStatusTime:Object(i["f"])(new Date),remark:null,opId:this.$store.state.user.sid,opName:this.$store.state.user.name.split(" ")[1],createTime:Object(i["f"])(new Date)};Object(l["a"])(t).then((function(a){e.opHistory.push(t)}))},delOpHistory:function(e){var t=this,a={operationalProcessId:e.operationalProcessId,typeId:e.typeId,rctId:e.rctId,basicInfoId:e.basicInfoId};Object(l["c"])(a).then((function(a){t.$message.success(a.msg),t.opHistory=t.opHistory.filter((function(t){return t.operationalProcessId!=e.operationalProcessId}))}))},submitDoc:function(){var e=this;null==this.doc.docDetailId?c(this.doc).then((function(t){null==e.dList.docList&&(e.dList.docList=[]),e.dList.docList.push(e.doc),console.log(e.doc)})):d(this.doc).then((function(t){e.$message.success("修改成功"),console.log(e.doc)})),this.open=!1},delDoc:function(){var e=this;null!=this.doc.docDetailId&&u(this.doc.docDetailId).then((function(t){e.$message.success("删除成功"),e.dList.docList=e.dList.docList.filter((function(t){return t.docDetailId!=e.doc.docDetailId})),e.doc={}}))},cancel:function(){this.open=!1}}},p=f,m=a("2877"),h=Object(m["a"])(p,r,o,!1,null,"5f398ee3",null);t["default"]=h.exports},38868:function(e,t,a){"use strict";a("18c9")},"3a13":function(e,t,a){"use strict";a.d(t,"e",(function(){return o})),a.d(t,"d",(function(){return i})),a.d(t,"a",(function(){return l})),a.d(t,"f",(function(){return n})),a.d(t,"c",(function(){return s})),a.d(t,"b",(function(){return c}));var r=a("b775");function o(e){return Object(r["a"])({url:"/system/operationalprocess/list",method:"get",params:e})}function i(e){return Object(r["a"])({url:"/system/operationalprocess/"+e,method:"get"})}function l(e){return Object(r["a"])({url:"/system/operationalprocess",method:"post",data:e})}function n(e){return Object(r["a"])({url:"/system/operationalprocess",method:"put",data:e})}function s(e){return Object(r["a"])({url:"/system/operationalprocess/del",method:"post",data:e})}function c(e,t){var a={operationalProcessId:e,status:t};return Object(r["a"])({url:"/system/operationalprocess/changeStatus",method:"put",data:a})}},4582:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"},{name:"dialogDragWidth",rawName:"v-dialogDragWidth"}],attrs:{"close-on-click-modal":!1,"modal-append-to-body":!1,visible:e.oopen,"append-to-body":"",width:"1500px"},on:{"update:visible":function(t){e.oopen=t}}},[a("div",{staticStyle:{display:"flex"}},[a("h2",{staticStyle:{"font-weight":"bold",margin:"10px"}},[e._v("新增编号信息")]),a("div",{staticStyle:{"vertical-align":"middle","line-height":"41px"}},[a("el-button",{attrs:{type:"primary"},on:{click:function(t){e.open=!0}}},[e._v("新增")])],1)]),a("el-table",{attrs:{border:"",data:e.logisticsNoInfo,"row-class-name":e.rowIndex}},[a("el-table-column",{attrs:{"header-align":"center",label:"SO号码",prop:"soNo"}}),a("el-table-column",{attrs:{"header-align":"center",label:"主提单号",prop:"mblNo"}}),a("el-table-column",{attrs:{"header-align":"center",label:"货代单号",prop:"hblNo"}}),a("el-table-column",{attrs:{"header-align":"center",label:"柜号信息",prop:"containersInfo"}}),a("el-table-column",{attrs:{"header-align":"center",label:"发货人",prop:"shipper"}}),a("el-table-column",{attrs:{"header-align":"center",label:"收货人",prop:"consignee"}}),a("el-table-column",{attrs:{"header-align":"center",label:"通知人",prop:"notifyParty"}}),a("el-table-column",{attrs:{"header-align":"center",label:"启运港放舱代理",prop:"polBookingAgent"}}),a("el-table-column",{attrs:{"header-align":"center",label:"目的港换单代理",prop:"podHandleAgent"}}),a("el-table-column",{attrs:{"header-align":"center",label:"唛头",prop:"shippingMark"}}),a("el-table-column",{attrs:{"header-align":"center",label:"货描",prop:"goodsDescription"}}),a("el-table-column",{attrs:{"header-align":"center",label:"签单日期",prop:"blIssueDate"}}),a("el-table-column",{attrs:{"header-align":"center",label:"签单地点",prop:"blIssueLocation"}}),a("el-table-column",{attrs:{"header-align":"center",align:"center","class-name":"small-padding fixed-width",label:"操作",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{staticStyle:{"margin-right":"-8px"},attrs:{icon:"el-icon-edit",size:"mini",type:"success"},on:{click:function(a){return e.handleUpdate(t.row)}}},[e._v("修改 ")]),a("el-button",{staticStyle:{"margin-right":"-8px"},attrs:{icon:"el-icon-delete",size:"mini",type:"danger"},on:{click:function(a){return e.handleDelete(t.row)}}},[e._v("删除 ")])]}}])})],1),a("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"},{name:"dialogDragWidth",rawName:"v-dialogDragWidth"}],attrs:{"close-on-click-modal":!1,"modal-append-to-body":!1,visible:e.open,"append-to-body":"",width:"500px",title:"新增编号信息"},on:{"update:visible":function(t){e.open=t}}},[a("el-form",{attrs:{border:"",data:e.form,"label-width":"105px"}},[a("el-form-item",{attrs:{label:"SO号码",prop:"soNo"}},[a("el-input",{attrs:{placeholder:"SO号码"},model:{value:e.form.soNo,callback:function(t){e.$set(e.form,"soNo",t)},expression:"form.soNo"}})],1),a("el-form-item",{attrs:{label:"主提单号",prop:"mblNo"}},[a("el-input",{attrs:{placeholder:"主提单号"},model:{value:e.form.mblNo,callback:function(t){e.$set(e.form,"mblNo",t)},expression:"form.mblNo"}})],1),a("el-form-item",{attrs:{label:"货代单号",prop:"hblNo"}},[a("el-input",{attrs:{placeholder:"货代单号"},model:{value:e.form.hblNo,callback:function(t){e.$set(e.form,"hblNo",t)},expression:"form.hblNo"}})],1),a("el-form-item",{attrs:{label:"柜号信息",prop:"containersInfo"}},[a("el-input",{attrs:{placeholder:"柜号信息"},model:{value:e.form.containersInfo,callback:function(t){e.$set(e.form,"containersInfo",t)},expression:"form.containersInfo"}})],1),a("el-form-item",{attrs:{label:"发货人",prop:"shipper"}},[a("el-input",{attrs:{placeholder:"发货人"},model:{value:e.form.shipper,callback:function(t){e.$set(e.form,"shipper",t)},expression:"form.shipper"}})],1),a("el-form-item",{attrs:{label:"收货人",prop:"consignee"}},[a("el-input",{attrs:{placeholder:"收货人"},model:{value:e.form.consignee,callback:function(t){e.$set(e.form,"consignee",t)},expression:"form.consignee"}})],1),a("el-form-item",{attrs:{label:"通知人",prop:"notifyParty"}},[a("el-input",{attrs:{placeholder:"通知人"},model:{value:e.form.notifyParty,callback:function(t){e.$set(e.form,"notifyParty",t)},expression:"form.notifyParty"}})],1),a("el-form-item",{attrs:{label:"启运港放舱代理",prop:"polBookingAgent"}},[a("el-input",{attrs:{placeholder:"启运港放舱代理"},model:{value:e.form.polBookingAgent,callback:function(t){e.$set(e.form,"polBookingAgent",t)},expression:"form.polBookingAgent"}})],1),a("el-form-item",{attrs:{label:"目的港换单代理",prop:"podHandleAgent"}},[a("el-input",{attrs:{placeholder:"目的港换单代理"},model:{value:e.form.podHandleAgent,callback:function(t){e.$set(e.form,"podHandleAgent",t)},expression:"form.podHandleAgent"}})],1),a("el-form-item",{attrs:{label:"唛头",prop:"shippingMark"}},[a("el-input",{attrs:{placeholder:"唛头"},model:{value:e.form.shippingMark,callback:function(t){e.$set(e.form,"shippingMark",t)},expression:"form.shippingMark"}})],1),a("el-form-item",{attrs:{label:"货描",prop:"goodsDescription"}},[a("el-input",{attrs:{placeholder:"货描"},model:{value:e.form.goodsDescription,callback:function(t){e.$set(e.form,"goodsDescription",t)},expression:"form.goodsDescription"}})],1),a("el-form-item",{attrs:{label:"签单日期",prop:"blIssueDate"}},[a("el-input",{attrs:{placeholder:"签单日期"},model:{value:e.form.blIssueDate,callback:function(t){e.$set(e.form,"blIssueDate",t)},expression:"form.blIssueDate"}})],1),a("el-form-item",{attrs:{label:"签单地点",prop:"blIssueLocation"}},[a("el-input",{attrs:{placeholder:"签单地点"},model:{value:e.form.blIssueLocation,callback:function(t){e.$set(e.form,"blIssueLocation",t)},expression:"form.blIssueLocation"}})],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("确 定")]),a("el-button",{on:{click:e.cancel}},[e._v("取 消")])],1)],1)],1)},o=[],i=(a("4de4"),a("d3b7"),a("14d9"),{name:"logisticsNoInfo",props:["openLogisticsNoInfo"],watch:{logisticsNoInfo:function(){this.$emit("return",this.logisticsNoInfo)},openLogisticsNoInfo:function(e){this.oopen=e},oopen:function(e){0==e&&this.$emit("close")}},data:function(){return{open:!1,oopen:!1,logisticsNoInfo:[],form:{}}},methods:{rowIndex:function(e){var t=e.row,a=e.rowIndex;t.id=a+1},handleUpdate:function(e){this.form=e,this.open=!0},handleDelete:function(e){this.logisticsNoInfo=this.logisticsNoInfo.filter((function(t){return t.id!=e.id}))},submitForm:function(){null!=this.form.id?(this.reset(),this.open=!1):(this.logisticsNoInfo.push(this.form),this.reset(),this.open=!1)},reset:function(){this.form={id:null,soNo:null,mblNo:null,hblNo:null,containersInfo:null,shipper:null,consignee:null,notifyParty:null,polBookingAgent:null,podHandleAgent:null,shippingMark:null,goodsDescription:null,blIssueDate:null,blIssueLocation:null},this.resetForm("form")},cancel:function(){this.open=!1}}}),l=i,n=a("2877"),s=Object(n["a"])(l,r,o,!1,null,null,null);t["default"]=s.exports},"50fe":function(e,t,a){"use strict";a.d(t,"a",(function(){return o}));var r=a("b775");function o(e){return Object(r["a"])({url:"/system/serviceinstances",method:"put",data:e})}},"5eaa":function(e,t,a){"use strict";a.d(t,"d",(function(){return o})),a.d(t,"e",(function(){return i})),a.d(t,"c",(function(){return l})),a.d(t,"a",(function(){return n})),a.d(t,"j",(function(){return s})),a.d(t,"b",(function(){return c})),a.d(t,"h",(function(){return d})),a.d(t,"i",(function(){return u})),a.d(t,"f",(function(){return f})),a.d(t,"g",(function(){return p}));var r=a("b775");function o(e){return Object(r["a"])({url:"/system/booking/list",method:"get",params:e})}function i(e){return Object(r["a"])({url:"/system/booking/psalist",method:"get",params:e})}function l(e){return Object(r["a"])({url:"/system/booking/"+e,method:"get"})}function n(e){return Object(r["a"])({url:"/system/booking",method:"post",data:e})}function s(e){return Object(r["a"])({url:"/system/booking",method:"put",data:e})}function c(e){return Object(r["a"])({url:"/system/booking/"+e,method:"delete"})}function d(e){return Object(r["a"])({url:"/system/booking/saveBookingLogistics",method:"post",data:e})}function u(e){return Object(r["a"])({url:"/system/booking/saveBookingPreCarriage",method:"post",data:e})}function f(e){return Object(r["a"])({url:"/system/booking/saveBookingExportDeclaration",method:"post",data:e})}function p(e){return Object(r["a"])({url:"/system/booking/saveBookingImportClearance",method:"post",data:e})}},"64d8":function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"},{name:"dialogDragWidth",rawName:"v-dialogDragWidth"}],attrs:{"close-on-click-modal":!1,"modal-append-to-body":!1,visible:e.oopen,"append-to-body":"",width:"1200px"},on:{"update:visible":function(t){e.oopen=t}}},[a("div",{staticStyle:{display:"flex"}},[a("h2",{staticStyle:{"font-weight":"bold",margin:"10px"}},[e._v("新增编号信息")]),a("div",{staticStyle:{"vertical-align":"middle","line-height":"41px"}},[a("el-button",{attrs:{type:"primary"},on:{click:function(t){e.open=!0}}},[e._v("新增")])],1)]),a("el-table",{attrs:{border:"",data:e.preCarriageNoInfo}},[a("el-table-column",{attrs:{label:"SO号码",prop:"soNo"}}),a("el-table-column",{attrs:{label:"司机姓名",prop:"preCarriageDriverName"}}),a("el-table-column",{attrs:{label:"司机电话",prop:"preCarriageDriverTel"}}),a("el-table-column",{attrs:{label:"司机车牌",prop:"preCarriageTruckNo"}}),a("el-table-column",{attrs:{label:"司机备注",prop:"preCarriageTruckRemark"}}),a("el-table-column",{attrs:{label:"装柜地址",prop:"preCarriageAddress"}}),a("el-table-column",{attrs:{label:"到场时间",prop:"preCarriageTime"}}),a("el-table-column",{attrs:{label:"柜号",prop:"containerNo"}}),a("el-table-column",{attrs:{label:"柜型",prop:"containerType"}}),a("el-table-column",{attrs:{label:"封条",prop:"sealNo"}}),a("el-table-column",{attrs:{label:"磅单",prop:"weightPaper"}}),a("el-table-column",{attrs:{"header-align":"center",align:"center","class-name":"small-padding fixed-width",label:"操作",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{staticStyle:{"margin-right":"-8px"},attrs:{icon:"el-icon-edit",size:"mini",type:"success"},on:{click:function(a){return e.handleUpdate(t.row)}}},[e._v("修改 ")]),a("el-button",{staticStyle:{"margin-right":"-8px"},attrs:{icon:"el-icon-delete",size:"mini",type:"danger"},on:{click:function(a){return e.handleDelete(t.row)}}},[e._v("删除 ")])]}}])})],1),a("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"},{name:"dialogDragWidth",rawName:"v-dialogDragWidth"}],attrs:{"close-on-click-modal":!1,"modal-append-to-body":!1,visible:e.open,"append-to-body":"",width:"500px",title:"新增编号信息"},on:{"update:visible":function(t){e.open=t}}},[a("el-form",{attrs:{border:"",data:e.form,"label-width":"105px"}},[a("el-form-item",{attrs:{label:"SO号码",prop:"soNo"}},[a("el-input",{attrs:{placeholder:"SO号码"},model:{value:e.form.soNo,callback:function(t){e.$set(e.form,"soNo",t)},expression:"form.soNo"}})],1),a("el-form-item",{attrs:{label:"司机姓名",prop:"preCarriageDriverName"}},[a("el-input",{attrs:{placeholder:"司机姓名"},model:{value:e.form.preCarriageDriverName,callback:function(t){e.$set(e.form,"preCarriageDriverName",t)},expression:"form.preCarriageDriverName"}})],1),a("el-form-item",{attrs:{label:"司机电话",prop:"preCarriageDriverTel"}},[a("el-input",{attrs:{placeholder:"司机电话"},model:{value:e.form.preCarriageDriverTel,callback:function(t){e.$set(e.form,"preCarriageDriverTel",t)},expression:"form.preCarriageDriverTel"}})],1),a("el-form-item",{attrs:{label:"司机车牌",prop:"preCarriageTruckNo"}},[a("el-input",{attrs:{placeholder:"司机车牌"},model:{value:e.form.preCarriageTruckNo,callback:function(t){e.$set(e.form,"preCarriageTruckNo",t)},expression:"form.preCarriageTruckNo"}})],1),a("el-form-item",{attrs:{label:"司机备注",prop:"preCarriageTruckRemark"}},[a("el-input",{attrs:{placeholder:"司机备注"},model:{value:e.form.preCarriageTruckRemark,callback:function(t){e.$set(e.form,"preCarriageTruckRemark",t)},expression:"form.preCarriageTruckRemark"}})],1),a("el-form-item",{attrs:{label:"装柜地址",prop:"preCarriageAddress"}},[a("el-input",{attrs:{placeholder:"装柜地址"},model:{value:e.form.preCarriageAddress,callback:function(t){e.$set(e.form,"preCarriageAddress",t)},expression:"form.preCarriageAddress"}})],1),a("el-form-item",{attrs:{label:"到场时间",prop:"preCarriageTime"}},[a("el-input",{attrs:{placeholder:"到场时间"},model:{value:e.form.preCarriageTime,callback:function(t){e.$set(e.form,"preCarriageTime",t)},expression:"form.preCarriageTime"}})],1),a("el-form-item",{attrs:{label:"柜号",prop:"containerNo"}},[a("el-input",{attrs:{placeholder:"柜号"},model:{value:e.form.containerNo,callback:function(t){e.$set(e.form,"containerNo",t)},expression:"form.containerNo"}})],1),a("el-form-item",{attrs:{label:"柜型",prop:"containerType"}},[a("el-input",{attrs:{placeholder:"柜型"},model:{value:e.form.containerType,callback:function(t){e.$set(e.form,"containerType",t)},expression:"form.containerType"}})],1),a("el-form-item",{attrs:{label:"封条",prop:"sealNo"}},[a("el-input",{attrs:{placeholder:"封条"},model:{value:e.form.sealNo,callback:function(t){e.$set(e.form,"sealNo",t)},expression:"form.sealNo"}})],1),a("el-form-item",{attrs:{label:"磅单",prop:"weightPaper"}},[a("el-input",{attrs:{placeholder:"磅单"},model:{value:e.form.weightPaper,callback:function(t){e.$set(e.form,"weightPaper",t)},expression:"form.weightPaper"}})],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("确 定")]),a("el-button",{on:{click:e.cancel}},[e._v("取 消")])],1)],1)],1)},o=[],i=(a("4de4"),a("d3b7"),a("14d9"),{name:"PreCarriageNoInfo",props:["openPreCarriageNoInfo"],watch:{preCarriageNoInfo:function(){this.$emit("return",this.preCarriageNoInfo)},openPreCarriageNoInfo:function(e){this.oopen=e},oopen:function(e){0==e&&this.$emit("close")}},data:function(){return{open:!1,oopen:!1,preCarriageNoInfo:[],form:{}}},methods:{rowIndex:function(e){var t=e.row,a=e.rowIndex;t.id=a+1},handleUpdate:function(e){this.form=e,this.open=!0},handleDelete:function(e){this.preCarriageNoInfo=this.preCarriageNoInfo.filter((function(t){return t.id!=e.id}))},submitForm:function(){null!=this.form.id?(this.reset(),this.open=!1):(this.preCarriageNoInfo.push(this.form),this.reset(),this.open=!1)},reset:function(){this.form={id:null,soNo:null,preCarriageDriverName:null,preCarriageDriverTel:null,preCarriageTruckNo:null,preCarriageTruckRemark:null,preCarriageAddress:null,preCarriageTime:null,containerNo:null,containerType:null,sealNo:null,weightPaper:null},this.resetForm("form")},cancel:function(){this.open=!1}}}),l=i,n=a("2877"),s=Object(n["a"])(l,r,o,!1,null,null,null);t["default"]=s.exports},"72f9":function(e,t,a){(function(t,a){e.exports=a()})(0,(function(){function e(i,l){if(!(this instanceof e))return new e(i,l);l=Object.assign({},a,l);var n=Math.pow(10,l.precision);this.intValue=i=t(i,l),this.value=i/n,l.increment=l.increment||1/n,l.groups=l.useVedic?o:r,this.s=l,this.p=n}function t(t,a){var r=!(2<arguments.length&&void 0!==arguments[2])||arguments[2],o=a.decimal,i=a.errorOnInvalid,l=a.fromCents,n=Math.pow(10,a.precision),s=t instanceof e;if(s&&l)return t.intValue;if("number"===typeof t||s)o=s?t.value:t;else if("string"===typeof t)i=new RegExp("[^-\\d"+o+"]","g"),o=new RegExp("\\"+o,"g"),o=(o=t.replace(/\((.*)\)/,"-$1").replace(i,"").replace(o,"."))||0;else{if(i)throw Error("Invalid Input");o=0}return l||(o=(o*n).toFixed(4)),r?Math.round(o):o}var a={symbol:"$",separator:",",decimal:".",errorOnInvalid:!1,precision:2,pattern:"!#",negativePattern:"-!#",format:function(e,t){var a=t.pattern,r=t.negativePattern,o=t.symbol,i=t.separator,l=t.decimal;t=t.groups;var n=(""+e).replace(/^-/,"").split("."),s=n[0];return n=n[1],(0<=e.value?a:r).replace("!",o).replace("#",s.replace(t,"$1"+i)+(n?l+n:""))},fromCents:!1},r=/(\d)(?=(\d{3})+\b)/g,o=/(\d)(?=(\d\d)+\d\b)/g;return e.prototype={add:function(a){var r=this.s,o=this.p;return e((this.intValue+t(a,r))/(r.fromCents?1:o),r)},subtract:function(a){var r=this.s,o=this.p;return e((this.intValue-t(a,r))/(r.fromCents?1:o),r)},multiply:function(t){var a=this.s;return e(this.intValue*t/(a.fromCents?1:Math.pow(10,a.precision)),a)},divide:function(a){var r=this.s;return e(this.intValue/t(a,r,!1),r)},distribute:function(t){var a=this.intValue,r=this.p,o=this.s,i=[],l=Math[0<=a?"floor":"ceil"](a/t),n=Math.abs(a-l*t);for(r=o.fromCents?1:r;0!==t;t--){var s=e(l/r,o);0<n--&&(s=s[0<=a?"add":"subtract"](1/r)),i.push(s)}return i},dollars:function(){return~~this.value},cents:function(){return~~(this.intValue%this.p)},format:function(e){var t=this.s;return"function"===typeof e?e(this,t):t.format(this,Object.assign({},t,e))},toString:function(){var e=this.s,t=e.increment;return(Math.round(this.intValue/this.p/t)*t).toFixed(e.precision)},toJSON:function(){return this.value}},e}))},"788f":function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-col",{style:{display:e.audit?"":"none"},attrs:{span:15}},[a("div",{class:{inactive:0==e.audit,active:e.audit}},[a("el-col",{staticStyle:{display:"flex","border-radius":"5px"}},[a("div",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:booking:opapproval","system:rct:opapproval"],expression:"['system:booking:opapproval','system:rct:opapproval']"}],staticStyle:{width:"25%",display:"flex","font-size":"12px"}},[a("el-button",{staticStyle:{padding:"0"},attrs:{disabled:!e.checkPermi(["system:rct:opapproval"]),icon:e.basicInfo.isDnOpConfirmed?"el-icon-check":"el-icon-minus",type:"text"},on:{click:function(t){return e.confirmed("op")}}},[e._v("操作确认 ")]),a("div",{staticStyle:{"text-align":"left",width:"120px"}},[a("div",[a("i",{staticClass:"el-icon-user"}),e._v(e._s(e.opConfirmedName))]),a("div",[a("i",{staticClass:"el-icon-alarm-clock"}),e._v(e._s(e.opConfirmedDate))])])],1),a("div",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:booking:psaapproval","system:rct:psaapproval"],expression:"['system:booking:psaapproval','system:rct:psaapproval']"}],staticStyle:{width:"25%",display:"flex","font-size":"12px"}},[a("el-button",{staticStyle:{padding:"0"},attrs:{disabled:!e.checkPermi(["system:rct:psaapproval"]),icon:e.basicInfo.isDnPsaConfirmed?"el-icon-check":"el-icon-minus",type:"text"},on:{click:function(t){return e.confirmed("psa")}}},[e._v("商务确认 ")]),a("div",{staticStyle:{"text-align":"left",width:"120px"}},[a("div",[a("i",{staticClass:"el-icon-user"}),e._v(e._s(e.psaConfirmedName))]),a("div",[a("i",{staticClass:"el-icon-alarm-clock"}),e._v(e._s(e.psaConfirmedDate))])])],1),a("div",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:booking:supplierapproval","system:rct:supplierapproval"],expression:"['system:booking:supplierapproval','system:rct:supplierapproval']"}],staticStyle:{width:"25%",display:"flex","font-size":"12px"}},[a("el-button",{staticStyle:{padding:"0"},attrs:{disabled:!e.checkPermi(["system:rct:supplierapproval"]),icon:e.basicInfo.isDnSupplierConfirmed?"el-icon-check":"el-icon-minus",type:"text"},on:{click:function(t){return e.confirmed("supplier")}}},[e._v("供应商确认 ")]),a("div",{staticStyle:{"text-align":"left",width:"120px"}},[a("div",[a("i",{staticClass:"el-icon-user"}),e._v(e._s(e.supplierConfirmedName))]),a("div",[a("i",{staticClass:"el-icon-alarm-clock"}),e._v(e._s(e.supplierConfirmedDate))])])],1),e.checkPermi(["system:booking:financeapproval","system:rct:financeapproval"])?a("div",{staticStyle:{width:"25%",display:"flex","font-size":"12px"}},[a("el-button",{staticStyle:{padding:"0"},attrs:{disabled:!e.checkPermi(["system:rct:financeapproval"]),icon:e.basicInfo.isAccountConfirmed?"el-icon-check":"el-icon-minus",type:"text"},on:{click:function(t){return e.confirmed("account")}}},[e._v("财务确认 ")]),a("div",{staticStyle:{"text-align":"left",width:"120px"}},[a("div",[a("i",{staticClass:"el-icon-user"}),e._v(e._s(e.accountConfirmedName))]),a("div",[a("i",{staticClass:"el-icon-alarm-clock"}),e._v(e._s(e.accountConfirmedDate))])])],1):e._e()])],1)])},o=[],i=(a("4de4"),a("d3b7"),a("d81d"),a("fba1")),l=a("e350"),n=a("72f9"),s=a.n(n),c=a("fff5"),d=a("50fe"),u={name:"audit",props:["audit","basicInfo","audits","payable","disabled","rsChargeList"],data:function(){var e=this;return{opConfirmedName:this.basicInfo&&this.basicInfo.isDnOpConfirmed?this.$store.state.data.allRsStaffList.filter((function(t){return t.staffId==e.basicInfo.isDnOpConfirmed}))[0].staffFamilyLocalName+""+this.$store.state.data.allRsStaffList.filter((function(t){return t.staffId==e.basicInfo.isDnOpConfirmed}))[0].staffGivingLocalName:null,opConfirmedDate:this.basicInfo&&this.basicInfo.opConfirmedTime?this.basicInfo.opConfirmedTime:null,accountConfirmedName:this.basicInfo&&this.basicInfo.isAccountConfirmed?this.$store.state.data.allRsStaffList.filter((function(t){return t.staffId==e.basicInfo.isAccountConfirmed}))[0].staffFamilyLocalName+""+this.$store.state.data.allRsStaffList.filter((function(t){return t.staffId==e.basicInfo.isAccountConfirmed}))[0].staffGivingLocalName:null,accountConfirmedDate:this.basicInfo&&this.basicInfo.accountConfirmTime?this.basicInfo.accountConfirmTime:null,supplierConfirmedName:this.basicInfo&&this.basicInfo.isDnSupplierConfirmed?this.$store.state.data.allRsStaffList.filter((function(t){return t.staffId==e.basicInfo.isDnSupplierConfirmed}))[0].staffFamilyLocalName+""+this.$store.state.data.allRsStaffList.filter((function(t){return t.staffId==e.basicInfo.isDnSupplierConfirmed}))[0].staffGivingLocalName:null,supplierConfirmedDate:this.basicInfo&&this.basicInfo.supplierConfirmedTime?this.basicInfo.supplierConfirmedTime:null,psaConfirmedName:this.basicInfo&&this.basicInfo.isDnPsaConfirmed?this.$store.state.data.allRsStaffList.filter((function(t){return t.staffId==e.basicInfo.isDnPsaConfirmed}))[0].staffFamilyLocalName+""+this.$store.state.data.allRsStaffList.filter((function(t){return t.staffId==e.basicInfo.isDnPsaConfirmed}))[0].staffGivingLocalName:null,psaConfirmedDate:this.basicInfo&&this.basicInfo.psaConfirmedTime?this.basicInfo.psaConfirmedTime:null,salesConfirmedName:null,salesConfirmedDate:null}},watch:{basicInfo:function(e){this.$emit("return",e)}},methods:{currency:s.a,checkPermi:l["a"],confirmed:function(e){var t=this;"op"==e&&(this.basicInfo.isDnOpConfirmed?(this.basicInfo.isDnOpConfirmed=null,this.basicInfo.opConfirmedTime=null,this.opConfirmedName=null,this.opConfirmedDate=null):(this.basicInfo.isDnOpConfirmed=this.$store.state.user.sid,this.basicInfo.opConfirmedTime=Object(i["f"])(new Date,"{y}-{m}-{d}"),this.opConfirmedName=this.$store.state.data.allRsStaffList.filter((function(e){return e.staffId==t.basicInfo.isDnOpConfirmed}))[0].staffFamilyLocalName+""+this.$store.state.data.allRsStaffList.filter((function(e){return e.staffId==t.basicInfo.isDnOpConfirmed}))[0].staffGivingLocalName,this.opConfirmedDate=this.basicInfo.opConfirmedTime),this.updateServiceInstance(this.basicInfo)),"account"==e&&(this.basicInfo.isAccountConfirmed?(this.basicInfo.isAccountConfirmed=null,this.basicInfo.accountConfirmTime=null,this.accountConfirmedName=null,this.accountConfirmedDate=null):(this.basicInfo.isAccountConfirmed=this.$store.state.user.sid,this.basicInfo.accountConfirmTime=Object(i["f"])(new Date,"{y}-{m}-{d}"),this.accountConfirmedName=this.$store.state.data.allRsStaffList.filter((function(e){return e.staffId==t.basicInfo.isAccountConfirmed}))[0].staffFamilyLocalName+""+this.$store.state.data.allRsStaffList.filter((function(e){return e.staffId==t.basicInfo.isAccountConfirmed}))[0].staffGivingLocalName,this.accountConfirmedDate=this.basicInfo.accountConfirmTime,this.$emit("auditFee",this.rsChargeList.map((function(e){return 1!=e.isAccountConfirmed&&(e.isAccountConfirmed=1,Object(c["l"])(e)),e})))),this.updateServiceInstance(this.basicInfo)),"sales"==e&&(this.basicInfo.isDnSalesConfirmed?(this.basicInfo.isDnSalesConfirmed=null,this.basicInfo.salesConfirmedTime=null,this.salesConfirmedName=null,this.salesConfirmedDate=null):(this.basicInfo.isDnSalesConfirmed=this.$store.state.user.sid,this.basicInfo.salesConfirmedTime=Object(i["f"])(new Date,"{y}-{m}-{d}"),this.accountConfirmedName=this.$store.state.data.allRsStaffList.filter((function(e){return e.staffId==t.basicInfo.isDnSalesConfirmed}))[0].staffFamilyLocalName+""+this.$store.state.data.allRsStaffList.filter((function(e){return e.staffId==t.basicInfo.isDnSalesConfirmed}))[0].staffGivingLocalName,this.accountConfirmedDate=this.basicInfo.salesConfirmedTime),this.updateServiceInstance(this.basicInfo)),"psa"==e&&(this.basicInfo.isDnPsaConfirmed?(this.basicInfo.isDnPsaConfirmed=null,this.basicInfo.psaConfirmedTime=null,this.psaConfirmedName=null,this.psaConfirmedDate=null):(this.basicInfo.isDnPsaConfirmed=this.$store.state.user.sid,this.basicInfo.psaConfirmedTime=Object(i["f"])(new Date,"{y}-{m}-{d}"),this.psaConfirmedName=this.$store.state.data.allRsStaffList.filter((function(e){return e.staffId==t.basicInfo.isDnPsaConfirmed}))[0].staffFamilyLocalName+""+this.$store.state.data.allRsStaffList.filter((function(e){return e.staffId==t.basicInfo.isDnPsaConfirmed}))[0].staffGivingLocalName,this.psaConfirmedDate=this.basicInfo.psaConfirmedTime),this.updateServiceInstance(this.basicInfo)),"supplier"==e&&(this.basicInfo.isDnSupplierConfirmed?(this.basicInfo.isDnSupplierConfirmed=null,this.basicInfo.supplierConfirmedTime=null,this.supplierConfirmedName=null,this.supplierConfirmedDate=null):(this.basicInfo.isDnSupplierConfirmed=this.$store.state.user.sid,this.basicInfo.supplierConfirmedTime=Object(i["f"])(new Date,"{y}-{m}-{d}"),this.supplierConfirmedName=this.$store.state.data.allRsStaffList.filter((function(e){return e.staffId==t.basicInfo.isDnSupplierConfirmed}))[0].staffFamilyLocalName+""+this.$store.state.data.allRsStaffList.filter((function(e){return e.staffId==t.basicInfo.isDnSupplierConfirmed}))[0].staffGivingLocalName,this.supplierConfirmedDate=this.basicInfo.supplierConfirmedTime),this.updateServiceInstance(this.basicInfo))},updateServiceInstance:function(e){Object(d["a"])(e)}}},f=u,p=(a("38868"),a("2877")),m=Object(p["a"])(f,r,o,!1,null,"7e84911e",null);t["default"]=m.exports},"9f16":function(e,t,a){"use strict";a("da57")},b7ea:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticStyle:{margin:"15px",width:"auto"}},[a("el-form",{ref:"form",staticClass:"edit",attrs:{model:e.form,rules:e.rules,"label-width":"63px"}},[a("el-row",[a("el-col",{staticStyle:{width:"75%","margin-right":"10px"}},[a("el-row",{staticStyle:{"margin-bottom":"15px"},attrs:{gutter:10}},[a("el-col",{class:"booking"==e.type?"booking":"",attrs:{span:4}},[a("div",{staticStyle:{margin:"0 0 15px",padding:"0","font-size":"40px","text-align":"center",border:"1px solid #76933C"}},[e._v(" "+e._s("临时操作单")+" ")]),a("el-form-item",{attrs:{label:"操作单号",prop:"rctNo"}},[a("el-input",{attrs:{disabled:"booking"==e.type||e.psaVerify,placeholder:"操作单号"},on:{focus:function(t){return e.generateRct(!1)}},model:{value:e.form.rctNo,callback:function(t){e.$set(e.form,"rctNo",t)},expression:"form.rctNo"}}),a("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"},{name:"dialogDragWidth",rawName:"v-dialogDragWidth"}],attrs:{"close-on-click-modal":!1,"modal-append-to-body":!1,visible:e.openGenerateRct,"append-to-body":"",title:"新增操作单号",width:"350px"},on:{"update:visible":function(t){e.openGenerateRct=t}}},[a("el-form",{ref:"form",staticClass:"edit",attrs:{model:e.rct,rules:e.rules,"label-width":"65px"}},[a("el-form-item",{attrs:{label:"单号规则"}},[a("el-input",{attrs:{disabled:"",placeholder:"前导字符+2位年份+2位月份+4位序列"},model:{value:e.rct.rules,callback:function(t){e.$set(e.rct,"rules",t)},expression:"rct.rules"}})],1),a("el-form-item",{attrs:{label:"前导字符",prop:"leadingCharacter"}},[a("el-input",{attrs:{disabled:"",placeholder:"前导字符"},model:{value:e.rct.leadingCharacter,callback:function(t){e.$set(e.rct,"leadingCharacter",t)},expression:"rct.leadingCharacter"}})],1),a("el-form-item",{attrs:{label:"所属月份"}},[a("el-radio-group",{model:{value:e.rct.month,callback:function(t){e.$set(e.rct,"month",t)},expression:"rct.month"}},[a("el-radio-button",{attrs:{label:"1"}},[e._v("本月单号")]),a("el-radio-button",{attrs:{label:"2"}},[e._v("显示下月")])],1)],1),a("el-form-item",{attrs:{label:"单号序列"}},[a("el-radio-group",{model:{value:e.rct.noNum,callback:function(t){e.$set(e.rct,"noNum",t)},expression:"rct.noNum"}},[a("el-radio-button",{attrs:{label:"1"}},[e._v("自然序列")]),a("el-radio-button",{attrs:{label:"2"}},[e._v("手动分配")])],1)],1),a("el-form-item",{attrs:{label:"单号预览"}},[a("div",{staticStyle:{display:"flex"}},[a("el-input",{attrs:{disabled:"1"==e.rct.noNum},model:{value:e.rct.rctNo,callback:function(t){e.$set(e.rct,"rctNo",t)},expression:"rct.rctNo"}}),a("el-button",{attrs:{size:"mini",type:"success"},on:{click:function(t){return e.generateRct(!0)}}},[e._v(" "+e._s("1"==e.rct.noNum?"生成":"")+" "+e._s("2"==e.rct.noNum?"校验":"")+" ")])],1)])],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{size:"mini",type:"primary"},on:{click:e.confirmRct}},[e._v("确 定")]),a("el-button",{attrs:{size:"mini"},on:{click:e.cancel}},[e._v("取 消")])],1)],1)],1),a("el-form-item",{attrs:{label:"操作日期",prop:"rctOpDate"}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{disabled:"booking"==e.type||e.psaVerify,clearable:"",placeholder:"操作日期",type:"date","value-format":"yyyy-MM-dd"},model:{value:e.form.rctOpDate,callback:function(t){e.$set(e.form,"rctOpDate",t)},expression:"form.rctOpDate"}})],1)],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{class:e.psaVerify?"booking":"",attrs:{label:"操作员",prop:"opId"}},[a("treeselect",{attrs:{"disable-branch-nodes":!0,disabled:e.psaVerify,"disabled-fuzzy-matching":!0,"flatten-search-results":!0,normalizer:e.staffNormalizer,options:e.opList.filter((function(e){return"操作员"==e.role.roleLocalName})),"show-count":!0,placeholder:"操作员"},on:{input:function(t){void 0==t&&(e.form.opId=null)},open:e.loadOp,select:function(t){e.form.opId=t.staffId}},scopedSlots:e._u([{key:"value-label",fn:function(t){var r=t.node;return a("div",{},[e._v(" "+e._s(void 0!=r.raw.staff?r.raw.staff.staffFamilyLocalName+r.raw.staff.staffGivingLocalName+" "+r.raw.staff.staffGivingEnName:"")+" ")])}},{key:"option-label",fn:function(t){var r=t.node,o=t.shouldShowCount,i=t.count,l=t.labelClassName,n=t.countClassName;return a("label",{class:l},[e._v(" "+e._s(-1!=r.label.indexOf(",")?r.label.substring(0,r.label.indexOf(",")):r.label)+" "),o?a("span",{class:n},[e._v("("+e._s(i)+")")]):e._e()])}}]),model:{value:e.opId,callback:function(t){e.opId=t},expression:"opId"}})],1),a("el-form-item",{class:"booking"==e.type?"booking":"",attrs:{label:"订舱员",prop:"bookingOpId"}},[a("treeselect",{attrs:{"disable-branch-nodes":!0,disabled:"booking"==e.type||e.psaVerify,"disabled-fuzzy-matching":!0,"flatten-search-results":!0,normalizer:e.staffNormalizer,options:e.opList.filter((function(e){return"订舱员"==e.role.roleLocalName})),"show-count":!0,placeholder:"订舱员"},on:{input:function(t){void 0==t&&(e.form.bookingOpId=null)},open:e.loadOp,select:function(t){e.form.bookingOpId=t.staffId}},scopedSlots:e._u([{key:"value-label",fn:function(t){var r=t.node;return a("div",{},[e._v(" "+e._s(void 0!=r.raw.staff?r.raw.staff.staffFamilyLocalName+r.raw.staff.staffGivingLocalName+" "+r.raw.staff.staffGivingEnName:"")+" ")])}},{key:"option-label",fn:function(t){var r=t.node,o=t.shouldShowCount,i=t.count,l=t.labelClassName,n=t.countClassName;return a("label",{class:l},[e._v(" "+e._s(-1!=r.label.indexOf(",")?r.label.substring(0,r.label.indexOf(",")):r.label)+" "),o?a("span",{class:n},[e._v("("+e._s(i)+")")]):e._e()])}}]),model:{value:e.bookingOpId,callback:function(t){e.bookingOpId=t},expression:"bookingOpId"}})],1),a("el-form-item",{class:"booking"==e.type?"booking":"",attrs:{label:"单证员",prop:"docOpId"}},[a("treeselect",{attrs:{"disable-branch-nodes":!0,disabled:"booking"==e.type||e.psaVerify,"disabled-fuzzy-matching":!0,"flatten-search-results":!0,normalizer:e.staffNormalizer,options:e.opList.filter((function(e){return"单证员"==e.role.roleLocalName})),"show-count":!0,placeholder:"单证员"},on:{input:function(t){void 0==t&&(e.form.docOpId=null)},open:e.loadOp,select:function(t){e.form.docOpId=t.staffId}},scopedSlots:e._u([{key:"value-label",fn:function(t){var r=t.node;return a("div",{},[e._v(" "+e._s(void 0!=r.raw.staff?r.raw.staff.staffFamilyLocalName+r.raw.staff.staffGivingLocalName+" "+r.raw.staff.staffGivingEnName:"")+" ")])}},{key:"option-label",fn:function(t){var r=t.node,o=t.shouldShowCount,i=t.count,l=t.labelClassName,n=t.countClassName;return a("label",{class:l},[e._v(" "+e._s(-1!=r.label.indexOf(",")?r.label.substring(0,r.label.indexOf(",")):r.label)+" "),o?a("span",{class:n},[e._v("("+e._s(i)+")")]):e._e()])}}]),model:{value:e.docOpId,callback:function(t){e.docOpId=t},expression:"docOpId"}})],1),a("el-form-item",{class:"booking"==e.type?"booking":"",attrs:{label:"协助操作",prop:"opObserverId"}},[a("treeselect",{attrs:{"disable-branch-nodes":!0,disabled:"booking"==e.type||e.psaVerify,"disabled-fuzzy-matching":!0,"flatten-search-results":!0,normalizer:e.staffNormalizer,options:e.opList,"show-count":!0,placeholder:"协助操作"},on:{input:function(t){void 0==t&&(e.form.opObserverId=null)},open:e.loadOp,select:function(t){e.form.opObserverId=t.staffId}},scopedSlots:e._u([{key:"value-label",fn:function(t){var r=t.node;return a("div",{},[e._v(" "+e._s(void 0!=r.raw.staff?r.raw.staff.staffFamilyLocalName+r.raw.staff.staffGivingLocalName+" "+r.raw.staff.staffGivingEnName:"")+" ")])}},{key:"option-label",fn:function(t){var r=t.node,o=t.shouldShowCount,i=t.count,l=t.labelClassName,n=t.countClassName;return a("label",{class:l},[e._v(" "+e._s(-1!=r.label.indexOf(",")?r.label.substring(0,r.label.indexOf(",")):r.label)+" "),o?a("span",{class:n},[e._v("("+e._s(i)+")")]):e._e()])}}]),model:{value:e.opObserverId,callback:function(t){e.opObserverId=t},expression:"opObserverId"}})],1)],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"订舱单号",prop:"newBookingNo"}},[a("el-input",{attrs:{disabled:e.psaVerify,placeholder:"订舱申请单号"},model:{value:e.form.newBookingNo,callback:function(t){e.$set(e.form,"newBookingNo",t)},expression:"form.newBookingNo"}})],1),a("el-form-item",{attrs:{label:"订舱日期",prop:"newBookingTime"}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{disabled:e.psaVerify,clearable:"",placeholder:"订舱申请单日期",type:"date","value-format":"yyyy-MM-dd"},model:{value:e.form.newBookingTime,callback:function(t){e.$set(e.form,"newBookingTime",t)},expression:"form.newBookingTime"}})],1),a("el-form-item",{attrs:{label:"报价单号",prop:"quotationNo"}},[a("el-input",{attrs:{disabled:e.psaVerify,placeholder:"报价单号"},model:{value:e.form.quotationNo,callback:function(t){e.$set(e.form,"quotationNo",t)},expression:"form.quotationNo"}})],1),a("el-form-item",{attrs:{label:"报价日期",prop:"quotationDate"}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{disabled:e.psaVerify,clearable:"",placeholder:"报价日期",type:"date","value-format":"yyyy-MM-dd"},model:{value:e.form.quotationDate,callback:function(t){e.$set(e.form,"quotationDate",t)},expression:"form.quotationDate"}})],1)],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"业务员",prop:"salesId"}},[a("treeselect",{attrs:{"disable-branch-nodes":!0,disabled:e.psaVerify,"disabled-fuzzy-matching":!0,"flatten-search-results":!0,normalizer:e.staffNormalizer,options:e.belongList,"show-count":!0,placeholder:"业务员"},on:{input:function(t){void 0==t&&(e.form.salesId=null)},open:e.loadSales,select:function(t){e.form.salesId=t.staffId}},scopedSlots:e._u([{key:"value-label",fn:function(t){var r=t.node;return a("div",{},[e._v(" "+e._s(void 0!=r.raw.staff?r.raw.staff.staffFamilyLocalName+r.raw.staff.staffGivingLocalName+" "+r.raw.staff.staffGivingEnName:"")+" ")])}},{key:"option-label",fn:function(t){var r=t.node,o=t.shouldShowCount,i=t.count,l=t.labelClassName,n=t.countClassName;return a("label",{class:l},[e._v(" "+e._s(-1!=r.label.indexOf(",")?r.label.substring(0,r.label.indexOf(",")):r.label)+" "),o?a("span",{class:n},[e._v("("+e._s(i)+")")]):e._e()])}}]),model:{value:e.salesId,callback:function(t){e.salesId=t},expression:"salesId"}})],1),a("el-form-item",{attrs:{label:"业务助理",prop:"salesAssistantId"}},[a("treeselect",{attrs:{"disable-branch-nodes":!0,disabled:e.psaVerify,"disabled-fuzzy-matching":!0,"flatten-search-results":!0,normalizer:e.staffNormalizer,options:e.belongList,"show-count":!0,placeholder:"业务助理"},on:{input:function(t){void 0==t&&(e.form.salesAssistantId=null)},open:e.loadSales,select:function(t){e.form.salesAssistantId=t.staffId}},scopedSlots:e._u([{key:"value-label",fn:function(t){var r=t.node;return a("div",{},[e._v(" "+e._s(void 0!=r.raw.staff?r.raw.staff.staffFamilyLocalName+r.raw.staff.staffGivingLocalName+" "+r.raw.staff.staffGivingEnName:"")+" ")])}},{key:"option-label",fn:function(t){var r=t.node,o=t.shouldShowCount,i=t.count,l=t.labelClassName,n=t.countClassName;return a("label",{class:l},[e._v(" "+e._s(-1!=r.label.indexOf(",")?r.label.substring(0,r.label.indexOf(",")):r.label)+" "),o?a("span",{class:n},[e._v("("+e._s(i)+")")]):e._e()])}}]),model:{value:e.salesAssistantId,callback:function(t){e.salesAssistantId=t},expression:"salesAssistantId"}})],1),a("el-form-item",{attrs:{label:"协助业务",prop:"salesObserverId"}},[a("treeselect",{attrs:{"disable-branch-nodes":!0,disabled:e.psaVerify,"disabled-fuzzy-matching":!0,"flatten-search-results":!0,normalizer:e.staffNormalizer,options:e.belongList,"show-count":!0,placeholder:"协助业务"},on:{input:function(t){void 0==t&&(e.form.salesObserverId=null)},open:e.loadSales,select:function(t){e.form.salesObserverId=t.staffId}},scopedSlots:e._u([{key:"value-label",fn:function(t){var r=t.node;return a("div",{},[e._v(" "+e._s(void 0!=r.raw.staff?r.raw.staff.staffFamilyLocalName+r.raw.staff.staffGivingLocalName+" "+r.raw.staff.staffGivingEnName:"")+" ")])}},{key:"option-label",fn:function(t){var r=t.node,o=t.shouldShowCount,i=t.count,l=t.labelClassName,n=t.countClassName;return a("label",{class:l},[e._v(" "+e._s(-1!=r.label.indexOf(",")?r.label.substring(0,r.label.indexOf(",")):r.label)+" "),o?a("span",{class:n},[e._v("("+e._s(i)+")")]):e._e()])}}]),model:{value:e.salesObserverId,callback:function(t){e.salesObserverId=t},expression:"salesObserverId"}})],1)],1),a("el-col",{class:"booking"!=e.type||e.psaVerify?"":"booking",attrs:{span:4}},[a("el-form-item",{attrs:{label:"商务审核",prop:"verifyPsaId"}},[a("treeselect",{attrs:{"disable-branch-nodes":!0,"disabled-fuzzy-matching":!0,"flatten-search-results":!0,normalizer:e.staffNormalizer,options:e.businessList,"show-count":!0,placeholder:"商务"},on:{input:function(t){void 0==t&&(e.form.verifyPsaId=null)},open:e.loadBusinesses,select:function(t){e.form.verifyPsaId=t.staffId}},scopedSlots:e._u([{key:"value-label",fn:function(t){var r=t.node;return a("div",{},[e._v(" "+e._s(void 0!=r.raw.staff?r.raw.staff.staffFamilyLocalName+r.raw.staff.staffGivingLocalName+" "+r.raw.staff.staffGivingEnName:"")+" ")])}},{key:"option-label",fn:function(t){var r=t.node,o=t.shouldShowCount,i=t.count,l=t.labelClassName,n=t.countClassName;return a("label",{class:l},[e._v(" "+e._s(-1!=r.label.indexOf(",")?r.label.substring(0,r.label.indexOf(",")):r.label)+" "),o?a("span",{class:n},[e._v("("+e._s(i)+")")]):e._e()])}}]),model:{value:e.verifyPsaId,callback:function(t){e.verifyPsaId=t},expression:"verifyPsaId"}})],1),a("el-form-item",{attrs:{label:"审核时间",prop:"psaVerifyTime"}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{clearable:"",placeholder:"商务审核时间",type:"date","value-format":"yyyy-MM-dd"},model:{value:e.form.psaVerifyTime,callback:function(t){e.$set(e.form,"psaVerifyTime",t)},expression:"form.psaVerifyTime"}})],1)],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"紧急程度",prop:"urgencyDegree"}},[a("el-input",{attrs:{disabled:e.psaVerify,placeholder:"紧急程度"},model:{value:e.form.urgencyDegree,callback:function(t){e.$set(e.form,"urgencyDegree",t)},expression:"form.urgencyDegree"}})],1),a("el-form-item",{attrs:{label:"收付方式",prop:"paymentTypeId"}},[a("tree-select",{attrs:{disabled:e.psaVerify,flat:!1,multiple:!1,pass:e.form.paymentTypeId,placeholder:"收付方式",type:"paymentType"},on:{return:function(t){e.form.paymentTypeId=t}}})],1),a("el-form-item",{attrs:{label:"放货方式",prop:"releaseTypeId"}},[a("tree-select",{attrs:{disabled:e.psaVerify,flat:!1,multiple:!1,pass:e.form.releaseTypeId,placeholder:"放货方式",type:"releaseType"},on:{return:function(t){e.form.releaseTypeId=t}}})],1),a("el-form-item",{attrs:{label:"进度状态",prop:"processStatusId"}},[a("tree-select",{attrs:{flat:!1,multiple:!1,pass:e.form.processStatusId,placeholder:"进度状态",type:"processStatus"},on:{return:function(t){e.form.processStatusId=t}}})],1)],1)],1),a("el-row",{staticStyle:{"margin-bottom":"15px"},attrs:{gutter:10}},[a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"委托单位",prop:"clientId"}},[e.$store.state.data.clientList.length>0?a("tree-select",{attrs:{disabled:e.psaVerify,flat:!1,multiple:!1,pass:e.form.clientId,placeholder:"委托单位",type:"client"},on:{return:function(t){e.form.clientId=t}}}):e._e()],1),a("el-form-item",{attrs:{label:"客户角色",prop:"clientRoleId"}},[a("tree-select",{attrs:{disabled:e.psaVerify,flat:!1,multiple:!1,pass:e.form.clientRoleId,placeholder:"客户角色",type:"companyRole"},on:{return:function(t){e.form.clientRoleId=t}}})],1)],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"联系人",prop:"clientContactor"}},[a("el-input",{attrs:{disabled:e.psaVerify,placeholder:"联系人称谓"},model:{value:e.form.clientContactor,callback:function(t){e.$set(e.form,"clientContactor",t)},expression:"form.clientContactor"}})],1),a("el-form-item",{attrs:{label:"电话",prop:"clientContactorTel"}},[a("el-input",{attrs:{placeholder:"联系人电话"},model:{value:e.form.clientContactorTel,callback:function(t){e.$set(e.form,"clientContactorTel",t)},expression:"form.clientContactorTel"}})],1)],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"邮箱",prop:"clientContactorEmail"}},[a("el-input",{attrs:{disabled:e.psaVerify,placeholder:"联系人邮箱"},model:{value:e.form.clientContactorEmail,callback:function(t){e.$set(e.form,"clientContactorEmail",t)},expression:"form.clientContactorEmail"}})],1),a("el-form-item",{attrs:{label:"关联单位",prop:"relationClientIds"}},[e.$store.state.data.clientList.length>0?a("tree-select",{attrs:{disabled:e.psaVerify,flat:!0,multiple:!0,pass:e.relationClientIds,placeholder:"客户",type:"client"},on:{return:e.getRelationClientIds}}):e._e()],1)],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"进出口",prop:"impExpTypeId"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{disabled:e.psaVerify,clearable:"",filterable:"",placeholder:"进出口"},model:{value:e.form.impExpTypeId,callback:function(t){e.$set(e.form,"impExpTypeId",t)},expression:"form.impExpTypeId"}},[a("el-option",{attrs:{value:1,label:"出口"}},[e._v("出口")]),a("el-option",{attrs:{value:2,label:"进口"}},[e._v("进口")])],1)],1),a("el-form-item",{attrs:{label:"收汇方式",prop:"tradingPaymentChannelId"}},[a("tree-select",{attrs:{disabled:e.psaVerify,flat:!1,multiple:!1,pass:e.form.tradingPaymentChannelId,placeholder:"贸易付款方式",type:"paymentChannels"},on:{return:function(t){e.form.tradingPaymentChannelId=t}}})],1)],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"贸易条款",prop:"tradingTermsId"}},[a("tree-select",{attrs:{disabled:e.psaVerify,flat:!1,multiple:!1,pass:e.form.tradingTermsId,placeholder:"贸易条款",type:"tradingTerms"},on:{return:function(t){e.form.tradingTermsId=t}}})],1),a("el-form-item",{attrs:{label:"运输条款",prop:"logisticsTermsId"}},[a("tree-select",{attrs:{disabled:e.psaVerify,flat:!1,multiple:!1,pass:e.form.logisticsTermsId,placeholder:"运输条款",type:"transportationTerms"},on:{return:function(t){e.form.logisticsTermsId=t}}})],1)],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"合同号",prop:"contractNo"}},[a("el-input",{attrs:{disabled:e.psaVerify,placeholder:"(委托单位)合同号"},model:{value:e.form.contractNo,callback:function(t){e.$set(e.form,"contractNo",t)},expression:"form.contractNo"}})],1),a("el-form-item",{attrs:{label:"发票号",prop:"clientInvoiceNo"}},[a("el-input",{attrs:{disabled:e.psaVerify,placeholder:"(委托单位)发票号"},model:{value:e.form.clientInvoiceNo,callback:function(t){e.$set(e.form,"clientInvoiceNo",t)},expression:"form.clientInvoiceNo"}})],1)],1)],1),a("el-row",{staticStyle:{"margin-bottom":"15px"},attrs:{gutter:10}},[a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"SO号",prop:"soNo"}},[a("el-input",{attrs:{disabled:e.psaVerify,placeholder:"SO号"},model:{value:e.form.soNo,callback:function(t){e.$set(e.form,"soNo",t)},expression:"form.soNo"}})],1),a("el-form-item",{attrs:{label:"柜号",prop:"soNo"}},[a("el-input",{attrs:{disabled:e.psaVerify,placeholder:"柜号"},model:{value:e.form.containerNo,callback:function(t){e.$set(e.form,"containerNo",t)},expression:"form.containerNo"}})],1)],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"封号",prop:"sealNo"}},[a("el-input",{attrs:{disabled:e.psaVerify,placeholder:"封号"},model:{value:e.form.sealNo,callback:function(t){e.$set(e.form,"sealNo",t)},expression:"form.sealNo"}})],1),a("el-form-item",{attrs:{label:"订舱概述",prop:"bookingDetail"}},[a("el-input",{attrs:{placeholder:"订舱概述"},model:{value:e.form.bookingDetail,callback:function(t){e.$set(e.form,"bookingDetail",t)},expression:"form.bookingDetail"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"装柜时间",prop:"precarriageTime"}},[a("el-date-picker",{attrs:{disabled:e.psaVerify,align:"left",placeholder:"选择装柜时间",size:"small",type:"datetime"},model:{value:e.form.precarriageTime,callback:function(t){e.$set(e.form,"precarriageTime",t)},expression:"form.precarriageTime"}})],1),a("el-form-item",{attrs:{label:"截关时间",prop:"cvClosingTime"}},[a("el-date-picker",{attrs:{disabled:e.psaVerify,placeholder:"选择截关时间",size:"small",type:"datetime"},model:{value:e.form.cvClosingTime,callback:function(t){e.$set(e.form,"cvClosingTime",t)},expression:"form.cvClosingTime"}})],1)],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"报关",prop:"cvDeclaringTime"}},[a("el-input",{attrs:{disabled:e.psaVerify,placeholder:"报关"},model:{value:e.form.cvDeclaringTime,callback:function(t){e.$set(e.form,"cvDeclaringTime",t)},expression:"form.cvDeclaringTime"}})],1),a("el-form-item",{attrs:{label:"截VGM",prop:"vgm"}},[a("el-input",{attrs:{disabled:e.psaVerify,placeholder:"截VGM"},model:{value:e.form.vgm,callback:function(t){e.$set(e.form,"vgm",t)},expression:"form.vgm"}})],1)],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"截SI",prop:"siClosingTime"}},[a("el-input",{attrs:{disabled:e.psaVerify,placeholder:"截SI"},model:{value:e.form.siClosingTime,callback:function(t){e.$set(e.form,"siClosingTime",t)},expression:"form.siClosingTime"}})],1),a("el-form-item",{attrs:{label:"拖车",prop:"trailer"}},[a("el-input",{attrs:{disabled:e.psaVerify,placeholder:"拖车"},model:{value:e.form.trailer,callback:function(t){e.$set(e.form,"trailer",t)},expression:"form.trailer"}})],1)],1)],1),a("el-row",{staticStyle:{"margin-bottom":"15px"},attrs:{gutter:10}},[a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"到港时间",prop:"podETA"}},[a("el-date-picker",{attrs:{disabled:e.psaVerify,placeholder:"选择到港时间",size:"small",type:"datetime"},model:{value:e.form.podETA,callback:function(t){e.$set(e.form,"podETA",t)},expression:"form.podETA"}})],1),a("el-form-item",{attrs:{label:"大船时间",prop:"shipTime"}},[a("el-input",{attrs:{disabled:e.psaVerify,placeholder:"大船时间",size:"small"},model:{value:e.form.shipTime,callback:function(t){e.$set(e.form,"shipTime",t)},expression:"form.shipTime"}})],1)],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"是否放货",prop:"isReleasable"}},[a("el-input",{attrs:{disabled:e.psaVerify,placeholder:"是否放货"},model:{value:e.form.isReleasable,callback:function(t){e.$set(e.form,"isReleasable",t)},expression:"form.isReleasable"}})],1),a("el-form-item",{attrs:{label:"发资料给代理",prop:"sendToAgent"}},[a("el-input",{attrs:{placeholder:"发资料给代理"},model:{value:e.form.sendToAgent,callback:function(t){e.$set(e.form,"sendToAgent",t)},expression:"form.sendToAgent"}})],1)],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"航次",prop:"boatId"}},[a("el-input",{attrs:{disabled:e.psaVerify,placeholder:"航次"},model:{value:e.form.boatId,callback:function(t){e.$set(e.form,"boatId",t)},expression:"form.boatId"}})],1),a("el-form-item",{attrs:{label:"备注",prop:"remark"}},[a("el-input",{attrs:{disabled:e.psaVerify,placeholder:"备注"},model:{value:e.form.remark,callback:function(t){e.$set(e.form,"remark",t)},expression:"form.remark"}})],1)],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"船名",prop:"shipName"}},[a("el-input",{attrs:{disabled:e.psaVerify,placeholder:"船名"},model:{value:e.form.shipName,callback:function(t){e.$set(e.form,"shipName",t)},expression:"form.shipName"}})],1),a("el-form-item",{attrs:{label:"电放方式",prop:"telexReleaseType"}},[a("el-input",{attrs:{disabled:e.psaVerify,placeholder:"电放方式"},model:{value:e.form.telexReleaseType,callback:function(t){e.$set(e.form,"telexReleaseType",t)},expression:"form.telexReleaseType"}})],1)],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"货名概要",prop:"goodsNameSummary"}},[a("el-input",{attrs:{disabled:e.psaVerify,placeholder:"货名概要"},model:{value:e.form.goodsNameSummary,callback:function(t){e.$set(e.form,"goodsNameSummary",t)},expression:"form.goodsNameSummary"}})],1)],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"件数",prop:"packageQuantity"}},[a("el-input-number",{staticStyle:{width:"100%"},attrs:{controls:!1,disabled:e.psaVerify,placeholder:"总件数"},model:{value:e.form.packageQuantity,callback:function(t){e.$set(e.form,"packageQuantity",t)},expression:"form.packageQuantity"}})],1)],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"毛重",prop:"grossWeight"}},[a("div",{staticStyle:{display:"flex"}},[a("el-input",{staticStyle:{width:"64%"},attrs:{disabled:e.psaVerify,placeholder:"总毛重"},nativeOn:{change:function(t){return e.autoCompletion("grossWeight")}},model:{value:e.grossWeight,callback:function(t){e.grossWeight=t},expression:"grossWeight"}}),a("tree-select",{staticStyle:{width:"36%"},attrs:{disabled:e.psaVerify,pass:e.form.weightUnitId,placeholder:"重量单位",type:"unit"},on:{return:function(t){e.form.weightUnitId=t}}})],1)])],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"总体积",prop:"volume"}},[a("div",{staticStyle:{display:"flex"}},[a("el-input-number",{staticStyle:{width:"64%"},attrs:{controls:!1,disabled:e.psaVerify,precision:2,step:.01,placeholder:"总体积"},model:{value:e.form.volume,callback:function(t){e.$set(e.form,"volume",t)},expression:"form.volume"}}),a("tree-select",{staticStyle:{width:"36%"},attrs:{disabled:null!=e.form.volumeUnitId&&!e.psaVerify,pass:e.form.volumeUnitId,placeholder:"体积单位",type:"unit"},on:{return:function(t){e.form.volumeUnitId=t}}})],1)])],1)],1),a("el-row",{staticStyle:{"margin-bottom":"15px"},attrs:{gutter:10}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"货物特征",prop:"cargoTypeIds"}},[a("tree-select",{attrs:{disabled:e.psaVerify,flat:!1,multiple:!0,pass:e.form.cargoTypeIds,placeholder:"货物特征",type:"cargoType"},on:{return:function(t){e.form.cargoTypeIds=t}}})],1)],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"货值",prop:"goodsValue"}},[a("div",{staticStyle:{display:"flex"}},[a("el-input",{staticStyle:{width:"64%"},attrs:{disabled:e.psaVerify,placeholder:"总货值"},nativeOn:{change:function(t){return e.autoCompletion("goodsValue")}},model:{value:e.goodsValue,callback:function(t){e.goodsValue=t},expression:"goodsValue"}}),a("tree-select",{staticStyle:{width:"36%"},attrs:{disabled:e.psaVerify,pass:e.form.goodsCurrencyId,placeholder:"货值币种",type:"currency"},on:{return:function(t){e.form.goodsCurrencyId=t}}})],1)])],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"货物限重",prop:"maxWeight"}},[a("el-input-number",{staticStyle:{width:"100%"},attrs:{controls:!1,disabled:e.psaVerify,precision:2,step:.01,placeholder:"货物限重"},model:{value:e.form.maxWeight,callback:function(t){e.$set(e.form,"maxWeight",t)},expression:"form.maxWeight"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"计费货量",prop:"revenueTons"}},[a("el-input",{staticStyle:{width:"100%"},attrs:{disabled:e.psaVerify,placeholder:"计费货量"},model:{value:e.form.revenueTons,callback:function(t){e.$set(e.form,"revenueTons",t)},expression:"form.revenueTons"}})],1)],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"物流类型",prop:"logisticsTypeId"}},[a("tree-select",{attrs:{disabled:e.psaVerify,flat:!1,main:!0,multiple:!1,pass:e.form.logisticsTypeId,placeholder:"物流类型",type:"serviceType"},on:{return:function(t){e.form.logisticsTypeId=t}}})],1)],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"启运港",prop:"polId"}},[a("location-select",{attrs:{"check-port":e.logisticsType,disabled:e.psaVerify,"load-options":e.locationOptions,multiple:!1,"no-parent":!0,pass:e.form.polId,placeholder:"启运港"},on:{return:function(t){e.form.polId=t}}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"目的港",prop:"destinationPortId"}},[a("location-select",{attrs:{"check-port":e.logisticsType,disabled:e.psaVerify,en:!0,"load-options":e.locationOptions,multiple:!1,pass:e.form.destinationPortId,placeholder:"目的港"},on:{return:function(t){e.form.destinationPortId=t}}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"承运人",prop:"carrierIds"}},[a("treeselect",{attrs:{"disable-branch-nodes":!0,disabled:e.psaVerify,"disabled-fuzzy-matching":!0,flat:!0,"flatten-search-results":!0,multiple:!0,normalizer:e.carrierNormalizer,options:e.carrierList,"show-count":!0,placeholder:"选择承运人"},on:{deselect:e.handleDeselectCarrierIds,open:e.loadCarrier,select:e.handleSelectCarrierIds},scopedSlots:e._u([{key:"value-label",fn:function(t){var r=t.node;return a("div",{},[e._v(" "+e._s(null!=r.raw.carrier.carrierIntlCode?r.raw.carrier.carrierIntlCode:r.raw.carrier.carrierShortName)+" ")])}},{key:"option-label",fn:function(t){var r=t.node,o=t.shouldShowCount,i=t.count,l=t.labelClassName,n=t.countClassName;return a("label",{class:l},[e._v(" "+e._s(-1!=r.label.indexOf(",")?r.label.substring(0,r.label.indexOf(",")):r.label)+" "),o?a("span",{class:n},[e._v("("+e._s(i)+")")]):e._e()])}}]),model:{value:e.carrierIds,callback:function(t){e.carrierIds=t},expression:"carrierIds"}})],1)],1)],1),a("el-row",{staticStyle:{"margin-bottom":"15px"},attrs:{gutter:10}},[a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"船期"}},[a("el-input",{attrs:{disabled:e.psaVerify,placeholder:"内容"},model:{value:e.form.schedule,callback:function(t){e.$set(e.form,"schedule",t)},expression:"form.schedule"}})],1)],1),a("el-col",{attrs:{span:8}},[a("el-form-item",{attrs:{label:"有效期"}},[a("el-input",{attrs:{disabled:e.psaVerify,placeholder:"内容"},model:{value:e.form.validTimeForm,callback:function(t){e.$set(e.form,"validTimeForm",t)},expression:"form.validTimeForm"}})],1)],1)],1)],1)],1),a("el-row",{staticClass:"spc",staticStyle:{"margin-bottom":"15px",display:"-webkit-box"},attrs:{gutter:10}},[a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"业务报价综述","label-width":"100",prop:"quotationSummary"}},[a("el-input",{attrs:{autosize:{minRows:10,maxRows:20},disabled:e.psaVerify,maxlength:"150",placeholder:"内容","show-word-limit":"",type:"textarea"},model:{value:e.form.quotationSummary,callback:function(t){e.$set(e.form,"quotationSummary",t)},expression:"form.quotationSummary"}})],1)],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"业务订舱备注","label-width":"100",prop:"newBookingRemark"}},[a("el-input",{attrs:{autosize:{minRows:10,maxRows:20},disabled:e.psaVerify,maxlength:"150",placeholder:"业务订舱备注","show-word-limit":"",type:"textarea"},model:{value:e.form.newBookingRemark,callback:function(t){e.$set(e.form,"newBookingRemark",t)},expression:"form.newBookingRemark"}})],1)],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"业务须知","label-width":"100",prop:"inquiryNotice"}},[a("el-input",{attrs:{autosize:{minRows:10,maxRows:20},disabled:e.psaVerify,maxlength:"150",placeholder:"内容","show-word-limit":"",type:"textarea"},model:{value:e.form.inquiryNotice,callback:function(t){e.$set(e.form,"inquiryNotice",t)},expression:"form.inquiryNotice"}})],1)],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"商务备注","label-width":"100",prop:"inquiryInnerRemark"}},[a("el-input",{attrs:{autosize:{minRows:10,maxRows:20},maxlength:"150",placeholder:"内容","show-word-limit":"",type:"textarea"},model:{value:e.form.inquiryInnerRemark,callback:function(t){e.$set(e.form,"inquiryInnerRemark",t)},expression:"form.inquiryInnerRemark"}})],1)],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"操作主管备注","label-width":"100",prop:"opLeaderRemark"}},[a("el-input",{attrs:{autosize:{minRows:10,maxRows:20},disabled:e.psaVerify,maxlength:"150",placeholder:"内容","show-word-limit":"",type:"textarea"},model:{value:e.form.opLeaderRemark,callback:function(t){e.$set(e.form,"opLeaderRemark",t)},expression:"form.opLeaderRemark"}})],1)],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"操作备注","label-width":"100",prop:"opInnerRemark"}},[a("el-input",{attrs:{autosize:{minRows:10,maxRows:20},disabled:e.psaVerify,maxlength:"150",placeholder:"内容","show-word-limit":"",type:"textarea"},model:{value:e.form.opInnerRemark,callback:function(t){e.$set(e.form,"opInnerRemark",t)},expression:"form.opInnerRemark"}})],1)],1)],1),a("div",[a("el-form-item",{attrs:{label:"合约类型",prop:"agreementTypeId"}},[a("el-input",{attrs:{disabled:e.psaVerify,placeholder:"合约类型"},model:{value:e.form.agreementTypeId,callback:function(t){e.$set(e.form,"agreementTypeId",t)},expression:"form.agreementTypeId"}})],1),a("el-form-item",{attrs:{label:"合约号",prop:"agreementNo"}},[a("el-input",{attrs:{disabled:e.psaVerify,placeholder:"合约号"},model:{value:e.form.agreementNo,callback:function(t){e.$set(e.form,"agreementNo",t)},expression:"form.agreementNo"}})],1)],1),a("el-col",{staticStyle:{width:"100%"}},[a("el-row",{staticClass:"button-group"},["booking"==e.type?a("el-button",{attrs:{disabled:e.psaVerify,size:"medium",type:"primary"},on:{click:function(t){return e.submitForm("saveCopy")}}},[e._v(" 另存为 ")]):e._e(),a("el-button",{attrs:{size:"medium",type:"primary"},on:{click:e.submitForm}},[e._v(e._s(e.psaVerify?"确认审核":"保 存"))]),e.psaVerify?a("el-button",{attrs:{size:"medium",type:"warning"},on:{click:e.rejected}},[e._v("驳 回")]):e._e(),a("el-button",{attrs:{disabled:e.psaVerify,size:"medium"},on:{click:e.cancel}},[e._v("重 置")])],1)],1)],1)],1)},o=[],i=a("c7eb"),l=a("b85c"),n=a("1da1"),s=(a("d3b7"),a("6062"),a("3ca3"),a("ddb0"),a("159b"),a("14d9"),a("caad"),a("2532"),a("a9e3"),a("4de4"),a("25f0"),a("ac1f"),a("00b4"),a("5319"),a("99af"),a("06ee")),c=a("4360"),d=a("b0b8"),u=a.n(d),f=a("ca17"),p=a.n(f),m=(a("6f8d"),a("4582")),h=a("64d8"),b=a("34bb"),g=a("cdb8"),y=a("788f"),I=a("20f5"),v=a("5eaa"),C=a("fba1"),w={name:"Document",dicts:["sys_yes_no"],props:["type"],components:{PreCarriageNoInfo:h["default"],LogisticsNoInfo:m["default"],opHistory:b["default"],receivablePayable:g["default"],audit:y["default"],Treeselect:p.a},data:function(){return{opList:[],businessList:[],belongList:[],carrierList:[],locationOptions:[],goodsValue:null,grossWeight:null,list:new Set,editOpHistory:{},size:this.$store.state.app.size||"mini",title:"",logisticsType:"SEA",carrierId:null,carrierIds:[],relationClientIds:[],verifyPsaId:null,salesId:null,salesAssistantId:null,salesObserverId:null,opId:null,bookingOpId:null,docOpId:null,opObserverId:null,openGenerateRct:!1,psaVerify:!1,logistics:!1,basicInfo:!0,noInfo:"booking"!=this.type,opHistory:"booking"!=this.type,receivablePayable:!0,audit:"booking"!=this.type,open:!1,loading:!1,preCarriage:!1,importClearance:!1,exportDeclaration:!1,logisticsProcess:[],logisticsNoInfo:[],showLogisticsNoInfo:[],openLogisticsNoInfo:!1,logisticsOpHistory:[],logisticsReceivablePayableList:[],preCarriageNoInfo:[],showPreCarriageNoInfo:[],openPreCarriageNoInfo:!1,preCarriageOpHistory:[],preCarriageReceivablePayableList:[],openExportDeclarationNoInfo:!1,exportDeclarationNoInfo:[],showExportDeclarationNoInfo:[],exportDeclarationOpHistory:[],exportDeclarationReceivablePayableList:[],openImportPassNoInfo:!1,importClearanceNoInfo:[],showImportClearanceNoInfo:[],importClearanceOpHistory:[],importClearanceReceivablePayableList:[],bookingList:[],rctList:[],form:{},logisticsBasicInfo:{},preCarriageBasicInfo:{},exportDeclarationBasicInfo:{},importClearanceBasicInfo:{},rct:{leadingCharacter:"RCT",month:1,noNum:1,rctNo:null},pageNum:1,pageSize:20,total:0,rules:{}}},watch:{"form.logisticsTypeId":function(e){var t=this;0==this.$store.state.data.serviceTypeList.length||this.$store.state.data.redisList.serviceType?c["a"].dispatch("getServiceTypeList").then((function(){t.getType(e)})):this.getType(e)}},beforeMount:function(){var e=this;this.reset(),"booking"==this.type&&this.getBookingList().then((function(){e.loadSelection()})),"op"==this.type&&this.getRctList().then((function(){e.loadSelection()})),this.$route.query.id&&"booking"==this.type&&this.getQuotation(this.$route.query.id).then((function(){e.loadSelection()})),this.$route.query.bId&&this.getBookingDetail(this.$route.query.bId).then((function(){e.loadSelection()})),this.$route.query.rId&&this.getRctDetail(this.$route.query.rId).then((function(){e.loadSelection()})),this.$route.query.psaVerify&&(this.psaVerify=!0)},methods:{loadSelection:function(){(0==this.$store.state.data.serviceTypeList.length||this.$store.state.data.redisList.serviceType)&&this.$store.dispatch("getServiceTypeList"),(0==this.$store.state.data.clientList.length||this.$store.state.data.redisList.client)&&this.$store.dispatch("getClientList"),(0==this.$store.state.data.supplierList.length||this.$store.state.data.redisList.supplier)&&this.$store.dispatch("getSupplierList"),this.loadOp(),this.loadCarrier(),this.loadSales(),this.loadBusinesses()},loadOp:function(){var e=this;0==this.$store.state.data.opList.length||this.$store.state.data.redisList.opList?c["a"].dispatch("getOpList").then((function(){e.opList=e.$store.state.data.opList})):this.opList=this.$store.state.data.opList},loadSales:function(){var e=this;0==this.$store.state.data.salesList.length||this.$store.state.data.redisList.salesList?c["a"].dispatch("getSalesList").then((function(){e.belongList=e.$store.state.data.salesList})):this.belongList=this.$store.state.data.salesList},loadCarrier:function(){var e=this;0==this.$store.state.data.serviceTypeCarriers.length||this.$store.state.data.redisList.serviceTypeCarriers?c["a"].dispatch("getServiceTypeCarriersList").then((function(){e.carrierList=e.$store.state.data.serviceTypeCarriers})):this.carrierList=this.$store.state.data.serviceTypeCarriers},loadBusinesses:function(){var e=this;0==this.$store.state.data.businessesList.length||this.$store.state.data.redisList.businessesList?c["a"].dispatch("getBusinessesList").then((function(){e.businessList=e.$store.state.data.businessesList})):this.businessList=this.$store.state.data.businessesList},staffNormalizer:function(e){var t;return e.children&&!e.children.length&&delete e.children,e.staff&&(t=null==e.staff.staffFamilyLocalName&&null==e.staff.staffGivingLocalName?null!=e.role.roleLocalName?e.role.roleLocalName+","+u.a.getFullChars(e.role.roleLocalName):e.dept.deptLocalName+","+u.a.getFullChars(e.dept.deptLocalName):e.staff.staffCode+" "+e.staff.staffFamilyLocalName+e.staff.staffGivingLocalName+" "+e.staff.staffGivingEnName+","+u.a.getFullChars(e.staff.staffFamilyLocalName+e.staff.staffGivingLocalName)),e.roleId?{id:e.roleId,label:t,children:e.children,isDisabled:null==e.staffId&&void 0==e.children}:{id:e.deptId,label:t,children:e.children,isDisabled:null==e.staffId&&void 0==e.children}},carrierNormalizer:function(e){var t;return e.children&&!e.children.length&&delete e.children,t=!e.carrier||null==e.carrier.carrierLocalName&&null==e.carrier.carrierEnName?e.serviceLocalName+" "+e.serviceEnName+","+u.a.getFullChars(void 0!=e.serviceLocalName?e.serviceLocalName:""):(null!=e.carrier.carrierIntlCode?e.carrier.carrierIntlCode:"")+" "+(null!=e.carrier.carrierEnName?e.carrier.carrierEnName:"")+" "+(null!=e.carrier.carrierLocalName?e.carrier.carrierLocalName:"")+","+u.a.getFullChars(null!=e.carrier.carrierLocalName?e.carrier.carrierLocalName:""),{id:e.serviceTypeId,label:t,children:e.children}},getQuotation:function(e){var t=this;return Object(n["a"])(Object(i["a"])().mark((function a(){return Object(i["a"])().wrap((function(a){while(1)switch(a.prev=a.next){case 0:return t.reset(),a.next=3,Object(s["c"])(e).then((function(e){if(t.form.logisticsTypeId=e.data.logisticsTypeId,t.form.salesId=e.data.staffId,t.form.clientId=e.data.companyId,t.form.clientRoleId=e.data.companyRoleId,t.form.clientContactor=e.data.extStaffName,t.form.clientContactorTel=e.data.extStaffPhoneNum,t.form.clientContactorEmail=e.data.extStaffEmailEnterprise,t.form.quotationNo=e.data.richNo,t.form.quotationDate=new Date,t.form.impExpTypeId=e.data.imExPort,t.form.goodsNameSummary=e.data.cargoName,t.form.goodsValue=e.data.cargoPrice,t.form.goodsCurrencyId=e.data.cargoCurrencyId,t.form.grossWeight=e.data.grossWeight,t.form.weightUnitId=e.data.cargoUnitId,t.form.polId=e.data.departureId,t.form.destinationPortId=e.data.destinationId,t.form.transitPortId=e.data.transportationTermsId,t.form.revenueTons=e.data.revenueTons,t.form.newBookingRemark=e.data.remark,void 0!=t.belongList){var a,r=Object(l["a"])(t.belongList);try{for(r.s();!(a=r.n()).done;){var o=a.value;if(void 0!=o.children){var i,n=Object(l["a"])(o.children);try{for(n.s();!(i=n.n()).done;){var s=i.value;if(void 0!=s.children){var c,d=Object(l["a"])(s.children);try{for(d.s();!(c=d.n()).done;){var u=c.value;u.staffId==e.data.staffId&&(t.salesId=u.deptId)}}catch(D){d.e(D)}finally{d.f()}}}}catch(D){n.e(D)}finally{n.f()}}}}catch(D){r.e(D)}finally{r.f()}}var f,p=new Set,m=Object(l["a"])(t.carrierList);try{for(m.s();!(f=m.n()).done;){var h=f.value;if(void 0!=h.children&&h.children.length>0){var b,g=Object(l["a"])(h.children);try{for(g.s();!(b=g.n()).done;){var y=b.value;if(null!=y.carrier&&null!=y.carrier.carrierId&&void 0!=y.carrier.carrierId&&null!=e.data.carrierIds&&e.data.carrierIds.length>0&&e.data.carrierIds.includes(y.carrier.carrierId)&&p.add(y.serviceTypeId),void 0!=y.children&&y.children.length>0){var I,v=Object(l["a"])(y.children);try{for(v.s();!(I=v.n()).done;){var C=I.value;null!=C.carrier&&null!=C.carrier.carrierId&&void 0!=C.carrier.carrierId&&null!=e.data.carrierIds&&e.data.carrierIds.length>0&&e.data.carrierIds.includes(C.carrier.carrierId)&&p.add(C.serviceTypeId)}}catch(D){v.e(D)}finally{v.f()}}}}catch(D){g.e(D)}finally{g.f()}}}}catch(D){m.e(D)}finally{m.f()}p.size>0&&p.forEach((function(e){t.carrierIds.push(e)}));var w,k="",N=Object(l["a"])(e.quotationFreight);try{for(N.s();!(w=N.n()).done;){var x=w.value;k+=(null!=x.charge?x.charge+":":"")+((null!=x.quotationCurrency?x.quotationCurrency.toLowerCase():"")+Number(x.quotationPrice))+(null!=x.unit?"/"+x.unit:"")+"\n",x.showClient=!1,x.showSupplier=!1,x.showQuotationCharge=!1,x.showCostCharge=!1,x.showQuotationCurrency=!1,x.showCostCurrency=!1,x.showQuotationUnit=!1,x.showCostUnit=!1,x.quotationStrategyId=x.strategyId,x.costStrategyId=x.strategyId,x.quotationChargeId=x.chargeId,x.quotationCharge=x.charge,x.costChargeId=x.chargeId,x.costCharge=x.charge,x.quotationUnitId=x.unitId,x.quotationUnit=x.unit,x.costUnitId=x.unitId,x.costUnit=x.unit,x.costExchangeRate=x.exchangeRate,x.quotationExchangeRate=x.exchangeRate,x.quotationTaxRate=x.taxRate,x.costTaxRate=x.taxRate,x.clientId=e.data.companyId,x.client=e.data.company,x.supplierId=x.companyId,x.supplier=x.company,x.quotationTotal=Number(x.quotationPrice)*Number(x.quotationAmount)*Number(x.quotationExchangeRate)*(1+Number(x.quotationTaxRate)/100),x.costTotal=Number(x.costPrice)*Number(x.costAmount)*Number(x.costExchangeRate)*(1+Number(x.costTaxRate)/100),"1"!=x.typeId&&"2"!=x.typeId&&"3"!=x.typeId||t.logisticsReceivablePayableList.push(x),"4"==x.typeId&&t.preCarriageReceivablePayableList.push(x),"5"==x.typeId&&t.exportDeclarationReceivablePayableList.push(x)}}catch(D){N.e(D)}finally{N.f()}t.form.quotationSummary=k;var O,L="",T=Object(l["a"])(e.characteristics);try{for(T.s();!(O=T.n()).done;){var S=O.value;L+=(null!=S.serviceType?S.serviceType:"")+(null!=S.cargoType?S.cargoType:"")+(null!=S.company?S.company:"")+(null!=S.locationDeparture?S.locationDeparture:"")+(null!=S.locationDestination?S.locationDestination:"")+(null!=S.info?S.info:"")+(null!=S.essentialDetail?S.essentialDetail:"")+"\n"}}catch(D){T.e(D)}finally{T.f()}t.form.inquiryNotice=L,t.form.serviceTypeIds=e.serviceTypeIds,t.form.cargoTypeIds=e.cargoTypeIds,t.locationOptions=e.locationOptions,t.form.preCarriageRegionIds=e.locationLoadingIds,t.form.clientRoleId=e.roleIds[0]}));case 3:case"end":return a.stop()}}),a)})))()},getBookingList:function(){var e=this;return Object(n["a"])(Object(i["a"])().mark((function t(){return Object(i["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.loading=!0,t.next=3,Object(v["d"])({pageNum:e.pageNum,pageSize:e.pageSize}).then((function(t){""!=t&&(e.bookingList=t.rows,e.total=t.total),e.loading=!1}));case 3:case"end":return t.stop()}}),t)})))()},getBookingDetail:function(e){var t=this;return Object(n["a"])(Object(i["a"])().mark((function a(){return Object(i["a"])().wrap((function(a){while(1)switch(a.prev=a.next){case 0:return t.reset(),a.next=3,Object(v["c"])(e).then((function(e){var a=[];e.data.relationClientIds&&e.data.relationClientIds.split(",").forEach((function(e){a.push(Number(e))})),t.relationClientIds=a,t.grossWeight=e.data.grossWeight,t.goodsValue=e.data.goodsValue,t.form=e.data,t.form.relationClientIds=a;var r=new Set;if(e.data.carrierIds){var o,i=Object(l["a"])(t.carrierList);try{for(i.s();!(o=i.n()).done;){var n=o.value;if(void 0!=n.children&&n.children.length>0){var s,c=Object(l["a"])(n.children);try{for(c.s();!(s=c.n()).done;){var d=s.value;if(null!=d.carrier&&null!=d.carrier.carrierId&&void 0!=d.carrier.carrierId&&null!=e.data.carrierIds&&e.data.carrierIds.length>0&&e.data.carrierIds.includes(d.carrier.carrierId)&&r.add(d.serviceTypeId),void 0!=d.children&&d.children.length>0){var u,f=Object(l["a"])(d.children);try{for(f.s();!(u=f.n()).done;){var p=u.value;null!=p.carrier&&null!=p.carrier.carrierId&&void 0!=p.carrier.carrierId&&null!=e.data.carrierIds&&e.data.carrierIds.length>0&&e.data.carrierIds.includes(p.carrier.carrierId)&&r.add(p.serviceTypeId)}}catch(B){f.e(B)}finally{f.f()}}}}catch(B){c.e(B)}finally{c.f()}}}}catch(B){i.e(B)}finally{i.f()}}if(r.size>0&&r.forEach((function(e){t.carrierIds.push(e)})),void 0!=t.belongList){var m,h=Object(l["a"])(t.belongList);try{for(h.s();!(m=h.n()).done;){var b=m.value;if(void 0!=b.children){var g,y=Object(l["a"])(b.children);try{for(y.s();!(g=y.n()).done;){var I=g.value;if(void 0!=I.children){var v,C=Object(l["a"])(I.children);try{for(C.s();!(v=C.n()).done;){var w=v.value;w.staffId==e.data.salesId&&(t.salesId=w.deptId),w.staffId==e.data.salesAssistantId&&(t.salesAssistantId=w.deptId),w.staffId==e.data.salesObserverId&&(t.salesObserverId=w.deptId)}}catch(B){C.e(B)}finally{C.f()}}}}catch(B){y.e(B)}finally{y.f()}}}}catch(B){h.e(B)}finally{h.f()}}if(void 0!=t.opList){var k,N=Object(l["a"])(t.opList);try{for(N.s();!(k=N.n()).done;){var x=k.value;if(void 0!=x.children){var O,L=Object(l["a"])(x.children);try{for(L.s();!(O=L.n()).done;){var T=O.value;"操作员"==x.role.roleLocalName&&T.staffId==e.data.opId&&(t.opId=T.roleId),"订舱员"==x.role.roleLocalName&&T.staffId==e.data.bookingOpId&&(t.bookingOpId=T.roleId),"单证员"==x.role.roleLocalName&&T.staffId==e.data.docOpId&&(t.docOpId=T.roleId),T.staffId==e.data.opObserverId&&(t.opObserverId=T.roleId)}}catch(B){L.e(B)}finally{L.f()}}}}catch(B){N.e(B)}finally{N.f()}}if(void 0!=t.businessList){var S,D=Object(l["a"])(t.businessList);try{for(D.s();!(S=D.n()).done;){var R=S.value;if(void 0!=R.children){var _,$=Object(l["a"])(R.children);try{for($.s();!(_=$.n()).done;){var P=_.value;P.staffId==e.data.verifyPsaId&&(t.verifyPsaId=P.roleId)}}catch(B){$.e(B)}finally{$.f()}}}}catch(B){D.e(B)}finally{D.f()}}null!=e.data.rsBookingLogisticsTypeBasicInfo&&(t.logisticsBasicInfo=e.data.rsBookingLogisticsTypeBasicInfo,t.logisticsReceivablePayableList=e.data.rsBookingLogisticsTypeBasicInfo.rsBookingReceivablePayableList),null!=e.data.rsBookingPreCarriageBasicInfo&&(t.preCarriageBasicInfo=e.data.rsBookingPreCarriageBasicInfo,t.preCarriageReceivablePayableList=e.data.rsBookingPreCarriageBasicInfo.rsBookingReceivablePayableList),null!=e.data.rsBookingExportDeclarationBasicInfo&&(t.exportDeclarationBasicInfo=e.data.rsBookingExportDeclarationBasicInfo,t.exportDeclarationReceivablePayableList=e.data.rsBookingExportDeclarationBasicInfo.rsBookingReceivablePayableList),null!=e.data.rsBookingImportClearanceBasicInfo&&(t.importClearanceBasicInfo=e.data.rsBookingImportClearanceBasicInfo,t.importClearanceReceivablePayableList=e.data.rsBookingImportClearanceBasicInfo.rsBookingReceivablePayableList),t.locationOptions=e.locationOptions}));case 3:case"end":return a.stop()}}),a)})))()},getRctList:function(){var e=this;return Object(n["a"])(Object(i["a"])().mark((function t(){return Object(i["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.loading=!0,t.next=3,Object(I["f"])({pageNum:e.pageNum,pageSize:e.pageSize}).then((function(t){""!=t&&(e.rctList=t.rows,e.total=t.total),e.loading=!1}));case 3:case"end":return t.stop()}}),t)})))()},getRctDetail:function(e){var t=this;return Object(n["a"])(Object(i["a"])().mark((function a(){return Object(i["a"])().wrap((function(a){while(1)switch(a.prev=a.next){case 0:return t.reset(),a.next=3,Object(I["c"])(e).then((function(e){t.grossWeight=e.data.grossWeight,t.goodsValue=e.data.goodsValue;var a=new Set;if(e.data.carrierIds){var r,o=Object(l["a"])(t.carrierList);try{for(o.s();!(r=o.n()).done;){var i=r.value;if(void 0!=i.children&&i.children.length>0){var n,s=Object(l["a"])(i.children);try{for(s.s();!(n=s.n()).done;){var c=n.value;if(null!=c.carrier&&null!=c.carrier.carrierId&&void 0!=c.carrier.carrierId&&null!=e.data.carrierIds&&e.data.carrierIds.length>0&&e.data.carrierIds.includes(c.carrier.carrierId)&&a.add(c.serviceTypeId),void 0!=c.children&&c.children.length>0){var d,u=Object(l["a"])(c.children);try{for(u.s();!(d=u.n()).done;){var f=d.value;null!=f.carrier&&null!=f.carrier.carrierId&&void 0!=f.carrier.carrierId&&null!=e.data.carrierIds&&e.data.carrierIds.length>0&&e.data.carrierIds.includes(f.carrier.carrierId)&&a.add(f.serviceTypeId)}}catch(B){u.e(B)}finally{u.f()}}}}catch(B){s.e(B)}finally{s.f()}}}}catch(B){o.e(B)}finally{o.f()}}a.size>0&&a.forEach((function(e){t.carrierIds.push(e)}));var p=[];if(e.data.relationClientIds&&e.data.relationClientIds.split(",").forEach((function(e){p.push(Number(e))})),t.relationClientIds=p,t.form=e.data,t.form.relationClientIds=p,void 0!=t.belongList){var m,h=Object(l["a"])(t.belongList);try{for(h.s();!(m=h.n()).done;){var b=m.value;if(void 0!=b.children){var g,y=Object(l["a"])(b.children);try{for(y.s();!(g=y.n()).done;){var I=g.value;if(void 0!=I.children){var v,C=Object(l["a"])(I.children);try{for(C.s();!(v=C.n()).done;){var w=v.value;w.staffId==e.data.salesId&&(t.salesId=w.deptId),w.staffId==e.data.salesAssistantId&&(t.salesAssistantId=w.deptId),w.staffId==e.data.salesObserverId&&(t.salesObserverId=w.deptId)}}catch(B){C.e(B)}finally{C.f()}}}}catch(B){y.e(B)}finally{y.f()}}}}catch(B){h.e(B)}finally{h.f()}}if(void 0!=t.opList){var k,N=Object(l["a"])(t.opList);try{for(N.s();!(k=N.n()).done;){var x=k.value;if(void 0!=x.children){var O,L=Object(l["a"])(x.children);try{for(L.s();!(O=L.n()).done;){var T=O.value;"操作员"==x.role.roleLocalName&&T.staffId==e.data.opId&&(t.opId=T.roleId),"订舱员"==x.role.roleLocalName&&T.staffId==e.data.bookingOpId&&(t.bookingOpId=T.roleId),"单证员"==x.role.roleLocalName&&T.staffId==e.data.docOpId&&(t.docOpId=T.roleId),T.staffId==e.data.opObserverId&&(t.opObserverId=T.roleId)}}catch(B){L.e(B)}finally{L.f()}}}}catch(B){N.e(B)}finally{N.f()}}if(void 0!=t.businessList){var S,D=Object(l["a"])(t.businessList);try{for(D.s();!(S=D.n()).done;){var R=S.value;if(void 0!=R.children){var _,$=Object(l["a"])(R.children);try{for($.s();!(_=$.n()).done;){var P=_.value;P.staffId==e.data.verifyPsaId&&(t.verifyPsaId=P.roleId)}}catch(B){$.e(B)}finally{$.f()}}}}catch(B){D.e(B)}finally{D.f()}}null!=e.data.rsRctLogisticsTypeBasicInfo&&(t.logisticsBasicInfo=e.data.rsRctLogisticsTypeBasicInfo,t.logisticsReceivablePayableList=e.data.rsRctLogisticsTypeBasicInfo.rsRctReceivablePayableList,t.logisticsOpHistory=e.data.rsRctLogisticsTypeBasicInfo.rsOperationalProcessList),null!=e.data.rsRctPreCarriageBasicInfo&&(t.preCarriageBasicInfo=e.data.rsRctPreCarriageBasicInfo,t.preCarriageReceivablePayableList=e.data.rsRctPreCarriageBasicInfo.rsRctReceivablePayableList,t.preCarriageOpHistory=e.data.rsRctPreCarriageBasicInfo.rsOperationalProcessList),null!=e.data.rsRctExportDeclarationBasicInfo&&(t.exportDeclarationBasicInfo=e.data.rsRctExportDeclarationBasicInfo,t.exportDeclarationReceivablePayableList=e.data.rsRctExportDeclarationBasicInfo.rsRctReceivablePayableList,t.exportDeclarationOpHistory=e.data.rsRctExportDeclarationBasicInfo.rsOperationalProcessList),null!=e.data.rsRctImportClearanceBasicInfo&&(t.importClearanceBasicInfo=e.data.rsRctImportClearanceBasicInfo,t.importClearanceReceivablePayableList=e.data.rsRctImportClearanceBasicInfo.rsRctReceivablePayableList,t.importClearanceOpHistory=e.data.rsRctImportClearanceBasicInfo.rsOperationalProcessList),t.locationOptions=e.locationOptions}));case 3:case"end":return a.stop()}}),a)})))()},getServiceTypeList:function(e){var t=this;return Object(n["a"])(Object(i["a"])().mark((function a(){var r,o,n,s,c,d;return Object(i["a"])().wrap((function(a){while(1)switch(a.prev=a.next){case 0:if(0!=t.$store.state.data.serviceTypeList.length){a.next=3;break}return a.next=3,t.$store.dispatch("getServiceTypeList");case 3:t.list.clear(),t.form.serviceTypeIds=e,r=Object(l["a"])(t.$store.state.data.serviceTypeList[0].children);try{for(r.s();!(o=r.n()).done;)if(n=o.value,e.includes(n.serviceTypeId)&&null!=n.typeId&&t.list.add(n.typeId),n.children){s=Object(l["a"])(n.children);try{for(s.s();!(c=s.n()).done;)d=c.value,e.includes(d.serviceTypeId)&&(null!=n.typeId&&t.list.add(n.typeId),null!=d.typeId&&t.list.add(d.typeId))}catch(i){s.e(i)}finally{s.f()}}}catch(i){r.e(i)}finally{r.f()}e.includes(-1)&&t.list.add("-1"),t.$forceUpdate();case 9:case"end":return a.stop()}}),a)})))()},getType:function(e){var t,a=Object(l["a"])(this.$store.state.data.serviceTypeList[0].children);try{for(a.s();!(t=a.n()).done;){var r=t.value;if(r.serviceTypeId==e&&(this.logisticsType=r.typeId),r.children){var o,i=Object(l["a"])(r.children);try{for(i.s();!(o=i.n()).done;){var n=o.value;n.serviceTypeId==e&&(this.logisticsType=r.typeId)}}catch(s){i.e(s)}finally{i.f()}}}}catch(s){a.e(s)}finally{a.f()}},getRelationClientIds:function(e){this.form.relationClientIds=e,this.relationClientIds=e},handleSelectCarrierIds:function(e){this.form.carrierIds.push(e.carrier.carrierId)},handleDeselectCarrierIds:function(e){this.form.carrierIds=this.form.carrierIds.filter((function(t){return t!=e.carrier.carrierId}))},generateRct:function(e){var t=this;e?Object(I["d"])().then((function(e){var a=e.data;if(a.toString().length<4)for(var r=4-a.toString().length,o=0;o<r;o++)a="0"+a;var i=new Date,l=(i.getMonth()+Number(t.rct.month)).toString(),n=(i.getFullYear()+(l/12>1?1:0)).toString().substring(2,4);t.rct.rctNo=t.rct.leadingCharacter+n+(1==l.length?"0"+l:l)+a.toString()})):this.openGenerateRct=!0},confirmRct:function(){this.form.rctNo=this.rct.rctNo,this.openGenerateRct=!1},autoCompletion:function(e){var t=/\d{1,3}(?=(\d{3})+$)/g,a=/[0-9]+/g;if("grossWeight"==e)if(a.test(this.grossWeight)){this.grossWeight=this.grossWeight.replace(/\b(0+)/gi,""),this.form.grossWeight=this.grossWeight;var r=this.grossWeight.split("."),o=r[0].replace(t,"$&,");this.grossWeight=r.length>1&&r[1]?"".concat(o,".").concat(r[1]):"".concat(o,".00")}else this.$message.warning("请输入数字");if("goodsValue"==e)if(a.test(this.goodsValue)){this.goodsValue=this.goodsValue.replace(/\b(0+)/gi,""),this.form.goodsValue=this.goodsValue;var i=this.goodsValue.split("."),l=i[0].replace(t,"$&,");this.goodsValue=i.length>1&&i[1]?"".concat(l,".").concat(i[1]):"".concat(l,".00")}else this.$message.warning("请输入数字")},getNoInfo:function(e,t){var a;"logistics"==e&&(this.logisticsNoInfo=t,a=this.showLogisticsNoInfo=[{type:"soNo",logisticsNo:"SO号码",details:""},{type:"mblNo",logisticsNo:"主提单号",details:""},{type:"hblNo",logisticsNo:"货代单号",details:""},{type:"containersInfo",logisticsNo:"柜号信息",details:""},{type:"shipper",logisticsNo:"发货人",details:""},{type:"consignee",logisticsNo:"收货人",details:""},{type:"notifyParty",logisticsNo:"通知人",details:""},{type:"polBookingAgent",logisticsNo:"启运港放舱代理",details:""},{type:"podHandleAgent",logisticsNo:"目的港换单代理",details:""},{type:"shippingMark",logisticsNo:"唛头",details:""},{type:"goodsDescription",logisticsNo:"货描",details:""},{type:"blIssueDate",logisticsNo:"签单日期",details:""},{type:"blIssueLocation",logisticsNo:"签单地点",details:""}]),"preCarriage"==e&&(this.preCarriageNoInfo=t,a=this.showPreCarriageNoInfo=[{type:"soNo",preCarriageNo:"SO号码",details:""},{type:"preCarriageDriverName",preCarriageNo:"司机姓名",details:""},{type:"preCarriageDriverTel",preCarriageNo:"司机电话",details:""},{type:"preCarriageTruckNo",preCarriageNo:"司机车牌",details:""},{type:"preCarriageTruckRemark",preCarriageNo:"司机备注",details:""},{type:"preCarriageAddress",preCarriageNo:"装柜地址",details:""},{type:"preCarriageTime",preCarriageNo:"到场时间",details:""},{type:"containerNo",preCarriageNo:"柜号",details:""},{type:"containerType",preCarriageNo:"柜型",details:""},{type:"sealNo",preCarriageNo:"封条",details:""},{type:"weightPaper",preCarriageNo:"磅单",details:""}]);var r,o=Object(l["a"])(a);try{var i=function(){var e=r.value;t.forEach((function(t){for(var a in t)a==e.type&&(e.details=""==e.details?null!=t[a]?t[a]:"":e.details+(null!=t[a]?","+t[a]:""))}))};for(o.s();!(r=o.n()).done;)i()}catch(n){o.e(n)}finally{o.f()}},cancel:function(){this.reset(),this.loading=!1,this.open=!1,this.openGenerateRct=!1},reset:function(){this.form={rctId:null,rctNo:null,rctOpDate:null,opId:null,bookingOpId:null,docOpId:null,opObserverId:null,newBookingNo:null,newBookingTime:null,quotationNo:null,quotationDate:null,salesId:null,salesAssistantId:null,salesObserverId:null,verifyPsaId:null,psaVerifyTime:null,urgencyDegree:null,paymentTypeId:null,releaseTypeId:null,processStatusId:null,clientId:null,clientRoleId:null,clientContactor:null,clientContactorTel:null,clientContactorEmail:null,relationClientIds:[],impExpTypeId:null,tradingPaymentChannelId:null,tradingTermsId:null,logisticsTermsId:null,contractNo:null,clientInvoiceNo:null,goodsNameSummary:null,packageQuantity:null,grossWeight:null,weightUnitId:null,volume:null,volumeUnitId:null,cargoTypeIds:[],goodsValue:null,goodsCurrencyId:null,maxWeight:null,revenueTons:null,logisticsTypeId:null,polId:null,destinationPortId:null,carrierIds:[],schedule:null,validTimeForm:null,isMblNeeded:0,mblNo:null,isUnderAgreementMbl:0,isCustomsIntransitMbl:0,isSwitchMbl:0,isDividedMbl:0,mblIssueTypeId:null,mblGetWayId:null,mblReleaseWayId:null,isHblNeeded:0,hblNoList:null,isUnderAgreementHbl:0,isCustomsIntransitHbl:0,isSwitchHbl:0,isDividedHbl:0,hblIssueTypeId:null,hblGetWayId:null,hblReleaseWayId:null,serviceTypeIds:[],quotationSummary:null,newBookingRemark:null,inquiryNotice:null,inquiryInnerRemark:null,opLeaderRemark:null,opInnerRemark:null,agreementTypeId:null,agreementNo:null,readOnly:null,createTime:null,updateTime:null,deleteTime:null,deleteStatus:"0",deleteBy:null,updateBy:null,createBy:null,soNo:null,containerNo:null,sealNo:null,bookingDetail:null,precarriageTime:null,cvClosingTime:null,cvDeclaringTime:null,vgm:null,siClosingTime:null,trailer:null,shipName:null,shipTime:null,podETA:null,telexReleaseType:null,isReleasable:null,sendToAgent:null,boatId:null,remark:null},this.logisticsBasicInfo={logisticsTypeInfoId:null,rctId:null,logisticsTypeId:null,carrierId:null,polId:null,firstCvClosingTime:null,firstCyOpenTime:null,firstCyClosingTime:null,firstEtd:null,firstVessel:null,firstVoyage:null,localBasicPortId:null,basicClosingTime:null,basicFinalGateinTime:null,basicEtd:null,basicVessel:null,basicVoyage:null,transitPortId:null,podId:null,podEta:null,destinationPortId:null,destinationPortEta:null,opConfirmed:null,opConfirmedId:null,opConfirmedName:null,opConfirmedDate:null,financeConfirmed:null,financeConfirmedId:null,financeConfirmedName:null,financeConfirmedDate:null,salesConfirmed:null,salesConfirmedId:null,salesConfirmedName:null,salesConfirmedDate:null,supplierConfirmed:null,supplierConfirmedId:null,supplierConfirmedName:null,supplierConfirmedDate:null,invoiceQueryNo:null},this.preCarriageBasicInfo={preCarriageInfoId:null,rctId:null,logisticsTypeId:null,preCarriageRegionId:null,preCarriageAddress:null,preCarriageTime:null,preCarriageContact:null,preCarriageTel:null,preCarriageRemark:null,opConfirmed:null,opConfirmedId:null,opConfirmedName:null,opConfirmedDate:null,financeConfirmed:null,financeConfirmedId:null,financeConfirmedName:null,financeConfirmedDate:null,salesConfirmed:null,salesConfirmedId:null,salesConfirmedName:null,salesConfirmedDate:null,supplierConfirmed:null,supplierConfirmedId:null,supplierConfirmedName:null,supplierConfirmedDate:null,invoiceQueryNo:null},this.exportDeclarationBasicInfo={exportDeclarationId:null,rctId:null,logisticsTypeId:null,dispatchRegionId:null,dispatchAddress:null,dispatchTime:null,dispatchContact:null,dispatchTel:null,dispatchRemark:null,dispatchDriverName:null,dispatchDriverTel:null,dispatchTruckNo:null,dispatchTruckRemark:null,opConfirmed:null,opConfirmedId:null,opConfirmedName:null,opConfirmedDate:null,financeConfirmed:null,financeConfirmedId:null,financeConfirmedName:null,financeConfirmedDate:null,salesConfirmed:null,salesConfirmedId:null,salesConfirmedName:null,salesConfirmedDate:null,supplierConfirmed:null,supplierConfirmedId:null,supplierConfirmedName:null,supplierConfirmedDate:null,invoiceQueryNo:null},this.importClearanceBasicInfo={importClearanceId:null,rctId:null,exportCustomsTypeId:null,importCustomsTypeId:null,opConfirmed:null,opConfirmedId:null,opConfirmedName:null,opConfirmedDate:null,financeConfirmed:null,financeConfirmedId:null,financeConfirmedName:null,financeConfirmedDate:null,salesConfirmed:null,salesConfirmedId:null,salesConfirmedName:null,salesConfirmedDate:null,supplierConfirmed:null,supplierConfirmedId:null,supplierConfirmedName:null,supplierConfirmedDate:null,invoiceQueryNo:null},this.grossWeight=0,this.goodsValue=0,this.carrierId=null,this.relationClientIds=[],this.verifyPsaId=null,this.salesId=null,this.salesAssistantId=null,this.salesObserverId=null,this.opId=null,this.bookingOpId=null,this.docOpId=null,this.opObserverId=null,this.carrierIds=[],this.preCarriage=!1,this.importClearance=!1,this.exportDeclaration=!1,this.logisticsProcess=[],this.logisticsNoInfo=[],this.openLogisticsNoInfo=!1,this.logisticsOpHistory=[],this.logisticsReceivablePayableList=[],this.preCarriageNoInfo=[],this.openPreCarriageNoInfo=!1,this.preCarriageOpHistory=[],this.preCarriageReceivablePayableList=[],this.openExportDeclarationNoInfo=!1,this.exportDeclarationNoInfo=[],this.exportDeclarationOpHistory=[],this.exportDeclarationReceivablePayableList=[],this.openImportPassNoInfo=!1,this.importClearanceNoInfo=[],this.importClearanceOpHistory=[],this.importClearanceReceivablePayableList=[],this.resetForm("form")},submitForm:function(e){var t=this;this.form.relationClientIds=null!=this.form.relationClientIds&&this.form.relationClientIds.length>0?this.form.relationClientIds.toString():null,this.psaVerify&&(this.form.isPsaVerified=1,this.form.processStatusId=2,this.form.psaVerifyTime=Object(C["f"])(new Date)),this.$refs["form"].validate((function(a){("op"==t.type||"booking"==t.type&&t.psaVerify)&&("saveCopy"==e&&(t.form.rctId=null),a&&(null!=t.form.rctId?(t.form.processStatusId=3,Object(I["k"])(t.form).then((function(e){t.saveAll(t.form.rctId),t.$modal.msgSuccess("修改成功"),t.open=!1,t.getRctList()}))):(t.form.processStatusId=1,Object(I["a"])(t.form).then((function(e){t.form.rctId=e.data,t.saveAll(e.data),t.$modal.msgSuccess("新增成功"),t.open=!1,t.getRctList()}))))),"booking"==t.type&&("saveCopy"==e&&(t.form.bookingId=null),a&&(null!=t.form.bookingId?(t.form.processStatusId=3,Object(v["j"])(t.form).then((function(e){t.psaVerify||(t.saveAll(t.form.bookingId),t.$modal.msgSuccess("修改成功"),t.open=!1,t.getBookingList())}))):(t.form.processStatusId=1,Object(v["a"])(t.form).then((function(e){t.form.bookingId=e.data,t.saveAll(e.data),t.$modal.msgSuccess("新增成功"),t.open=!1,t.getBookingList()})))))}))},rejected:function(){var e=this;this.$confirm("确认单据后不可更改，是否确认？","",{confirmButtonText:"确定",cancelButtonText:"取消",type:"warning"}).then((function(t){"confirm"==t&&(e.form.relationClientIds=null!=e.form.relationClientIds&&e.form.relationClientIds.length>0?e.form.relationClientIds.toString():null,e.$refs["form"].validate((function(t){e.form.isPsaVerified=1,e.form.processStatusId=9,t&&Object(v["j"])(e.form).then((function(t){e.$modal.msgSuccess("修改成功"),e.open=!1,e.getBookingList()}))})))}))},saveAll:function(e){this.saveLogistics(e),this.savePreCarriage(e),this.saveExportDeclaration(e),this.saveImportClearance(e)},saveLogistics:function(e){var t=this;null==this.form.bookingId&&null==this.form.rctId?this.$message.error("请先确定单据"):(this.form.relationClientIds=null!=this.form.relationClientIds&&this.form.relationClientIds.length>0?this.form.relationClientIds.toString():null,"booking"!=this.type||this.psaVerify||(this.logisticsReceivablePayableList.length>0&&(this.logisticsBasicInfo.rsBookingReceivablePayableList=this.logisticsReceivablePayableList),this.logisticsBasicInfo.bookingId="number"==typeof e?e:this.form.bookingId,this.logisticsBasicInfo.typeId=1,this.form.rsBookingLogisticsTypeBasicInfo=this.logisticsBasicInfo,Object(v["h"])(this.form).then((function(){"number"!=typeof e&&t.$message.success("保存成功")}))),("op"==this.type||"booking"==this.type&&this.psaVerify)&&(this.logisticsReceivablePayableList.length>0&&(this.logisticsBasicInfo.rsRctReceivablePayableList=this.logisticsReceivablePayableList),this.logisticsNoInfo.length>0&&(this.logisticsBasicInfo.rsRctLogisticsNoInfos=this.logisticsNoInfo),this.logisticsOpHistory.length>0&&(this.logisticsBasicInfo.rsOperationalProcessList=this.logisticsOpHistory),this.logisticsBasicInfo.rctId="number"==typeof e?e:this.form.rctId,this.logisticsBasicInfo.typeId=1,this.form.rsRctLogisticsTypeBasicInfo=this.logisticsBasicInfo,Object(I["i"])(this.form).then((function(){"number"!=typeof e&&t.$message.success("保存成功")}))))},savePreCarriage:function(e){var t=this;null==this.form.bookingId&&null==this.form.rctId?this.$message.error("请先确定单据"):(this.form.relationClientIds=null!=this.form.relationClientIds&&this.form.relationClientIds.length>0?this.form.relationClientIds.toString():null,"booking"!=this.type||this.psaVerify||(this.preCarriageReceivablePayableList.length>0&&(this.preCarriageBasicInfo.rsBookingReceivablePayableList=this.preCarriageReceivablePayableList),this.preCarriageBasicInfo.bookingId="number"==typeof e?e:this.form.bookingId,this.preCarriageBasicInfo.typeId=4,this.form.rsBookingPreCarriageBasicInfo=this.preCarriageBasicInfo,Object(v["i"])(this.form).then((function(){"number"!=typeof e&&t.$message.success("保存成功")}))),("op"==this.type||"booking"==this.type&&this.psaVerify)&&(this.preCarriageReceivablePayableList.length>0&&(this.preCarriageBasicInfo.rsRctReceivablePayableList=this.preCarriageReceivablePayableList),this.preCarriageNoInfo.length>0&&(this.preCarriageBasicInfo.rsRctPreCarriageNoInfos=this.preCarriageNoInfo),this.preCarriageOpHistory.length>0&&(this.preCarriageBasicInfo.rsOperationalProcessList=this.preCarriageOpHistory),this.preCarriageBasicInfo.rctId="number"==typeof e?e:this.form.rctId,this.preCarriageBasicInfo.typeId=4,this.form.rsRctPreCarriageBasicInfo=this.preCarriageBasicInfo,Object(I["j"])(this.form).then((function(){"number"!=typeof e&&t.$message.success("保存成功")}))))},saveExportDeclaration:function(e){var t=this;null==this.form.bookingId&&null==this.form.rctId?this.$message.error("请先确定单据"):(this.form.relationClientIds=null!=this.form.relationClientIds&&this.form.relationClientIds.length>0?this.form.relationClientIds.toString():null,"booking"!=this.type||this.psaVerify||(this.exportDeclarationReceivablePayableList.length>0&&(this.exportDeclarationBasicInfo.rsBookingReceivablePayableList=this.exportDeclarationReceivablePayableList),this.exportDeclarationBasicInfo.bookingId="number"==typeof e?e:this.form.bookingId,this.exportDeclarationBasicInfo.typeId=5,this.form.rsBookingExportDeclarationBasicInfo=this.exportDeclarationBasicInfo,Object(v["f"])(this.form).then((function(){"number"!=typeof e&&t.$message.success("保存成功")}))),("op"==this.type||"booking"==this.type&&this.psaVerify)&&(this.exportDeclarationReceivablePayableList.length>0&&(this.exportDeclarationBasicInfo.rsRctReceivablePayableList=this.exportDeclarationReceivablePayableList),this.exportDeclarationOpHistory.length>0&&(this.exportDeclarationBasicInfo.rsOperationalProcessList=this.exportDeclarationOpHistory),this.exportDeclarationBasicInfo.rctId="number"==typeof e?e:this.form.rctId,this.exportDeclarationBasicInfo.typeId=5,this.form.rsRctExportDeclarationBasicInfo=this.exportDeclarationBasicInfo,Object(I["g"])(this.form).then((function(){"number"!=typeof e&&t.$message.success("保存成功")}))))},saveImportClearance:function(e){var t=this;null==this.form.bookingId&&null==this.form.rctId?this.$message.error("请先确定单据"):(this.form.relationClientIds=null!=this.form.relationClientIds&&this.form.relationClientIds.length>0?this.form.relationClientIds.toString():null,"booking"!=this.type||this.psaVerify||(this.importClearanceReceivablePayableList.length>0&&(this.importClearanceBasicInfo.rsBookingReceivablePayableList=this.importClearanceReceivablePayableList),this.importClearanceBasicInfo.bookingId="number"==typeof e?e:this.form.bookingId,this.importClearanceBasicInfo.typeId=6,this.form.rsBookingImportClearanceBasicInfo=this.importClearanceBasicInfo,Object(v["g"])(this.form).then((function(){"number"!=typeof e&&t.$message.success("保存成功")}))),("op"==this.type||"booking"==this.type&&this.psaVerify)&&(this.importClearanceReceivablePayableList.length>0&&(this.importClearanceBasicInfo.rsRctReceivablePayableList=this.importClearanceReceivablePayableList),this.importClearanceOpHistory.length>0&&(this.importClearanceBasicInfo.rsOperationalProcessList=this.importClearanceOpHistory),this.importClearanceBasicInfo.rctId="number"==typeof e?e:this.form.rctId,this.importClearanceBasicInfo.typeId=6,this.form.rsRctImportClearanceBasicInfo=this.importClearanceBasicInfo,Object(I["h"])(this.form).then((function(){"number"!=typeof e&&t.$message.success("保存成功")}))))},changeMaster:function(){console.log(this.form.isMblNeeded)}}},k=w,N=(a("9f16"),a("2877")),x=Object(N["a"])(k,r,o,!1,null,"34ce9298",null);t["default"]=x.exports},cdb8:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("el-col",{style:{display:e.openReceivablePayable?"":"none"},attrs:{span:21.5}},[a("div",{class:{inactive:0==e.openReceivablePayable,active:e.openReceivablePayable}},[a("el-table",{staticClass:"pd0",attrs:{data:e.receivablePayable,"row-class-name":e.rowIndex,border:""}},[a("el-table-column",{attrs:{label:"应收明细"}},[a("el-table-column",{attrs:{label:"客户关联单位"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.showClient?e._e():a("div",{on:{click:function(e){t.row.showClient=!0}}},[e._v(" "+e._s(t.row.client)+" ")]),e.$store.state.data.clientList.length>0&&t.row.showClient?a("tree-select",{attrs:{flat:!1,multiple:!1,pass:t.row.clientId,placeholder:"客户",type:"client"},on:{return:function(e){t.row.clientId=e}}}):e._e()]}}])}),a("el-table-column",{attrs:{align:"center",label:"费用",prop:"quotationChargeId",width:"80px"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.showQuotationCharge?e._e():a("div",{on:{click:function(e){t.row.showQuotationCharge=!0}}},[e._v(" "+e._s(t.row.quotationCharge)+" ")]),t.row.showQuotationCharge?a("tree-select",{attrs:{dbn:!0,flat:!1,multiple:!1,pass:t.row.quotationChargeId,placeholder:"运费",type:"charge"},on:{return:function(e){t.row.quotationChargeId=e}}}):e._e()]}}])}),a("el-table-column",{attrs:{align:"center",label:"策略",prop:"quotationStrategyId",width:"58"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-select",{attrs:{clearable:"",filterable:""},on:{change:function(e){1==t.row.quotationStrategyId&&(t.row.quotationRate=0)}},model:{value:t.row.quotationStrategyId,callback:function(a){e.$set(t.row,"quotationStrategyId",a)},expression:"scope.row.quotationStrategyId"}},[a("el-option",{attrs:{label:"已含",value:"1"}},[e._v("已含")]),a("el-option",{attrs:{label:"未含",value:"0"}},[e._v("未含")])],1)]}}])}),a("el-table-column",{attrs:{align:"center",label:"货币",prop:"quotationCurrencyId",width:"70"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.showQuotationCurrency?e._e():a("div",{staticStyle:{width:"69px",height:"23px"},on:{click:function(e){t.row.showQuotationCurrency=!0}}},[e._v(" "+e._s(t.row.quotationCurrency)+" ")]),t.row.showQuotationCurrency?a("tree-select",{attrs:{pass:t.row.quotationCurrencyCode,type:"currency"},on:{return:function(e){t.row.currencyId=e}}}):e._e()]}}])}),a("el-table-column",{attrs:{align:"center",label:"单价",prop:"quotationRate",width:"68px"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-input-number",{staticStyle:{display:"flex",width:"100%"},attrs:{controls:!1,min:0},on:{change:function(a){return e.countProfit(t.row)}},model:{value:t.row.quotationRate,callback:function(a){e.$set(t.row,"quotationRate",a)},expression:"scope.row.quotationRate"}})]}}])}),a("el-table-column",{attrs:{label:"单位",lign:"center",prop:"quotationUnitId",width:"70"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.showQuotationUnit?e._e():a("div",{on:{click:function(e){t.row.showQuotationUnit=!0}}},[e._v(" "+e._s(t.row.quotationUnitCode)+" ")]),t.row.showQuotationUnit?a("tree-select",{attrs:{pass:t.row.quotationUnitId,type:"unit"},on:{return:function(e){t.row.quotationUnitCode=e}}}):e._e()]}}])}),a("el-table-column",{attrs:{align:"center",label:"数量",prop:"quotationAmount",width:"48px"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-input-number",{staticStyle:{display:"flex",width:"100%"},attrs:{controls:!1,min:1,placeholder:"数量"},on:{change:function(a){return e.countProfit(t.row)}},model:{value:t.row.quotationAmount,callback:function(a){e.$set(t.row,"quotationAmount",a)},expression:"scope.row.quotationAmount"}})]}}])}),a("el-table-column",{attrs:{align:"center",label:"汇率",prop:"quotationExchangeRate",width:"60"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-input-number",{staticStyle:{width:"100%"},attrs:{controls:!1,min:1,precision:4,step:1e-4},on:{change:function(a){return e.countProfit(t.row)}},model:{value:t.row.quotationExchangeRate,callback:function(a){e.$set(t.row,"quotationExchangeRate",a)},expression:"scope.row.quotationExchangeRate"}})]}}])}),a("el-table-column",{attrs:{align:"center",label:"税率",prop:"quotationTaxRate",width:"68px"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-input-number",{staticStyle:{width:"75%"},attrs:{controls:!1,min:0},on:{change:function(a){return e.countProfit(t.row)}},model:{value:t.row.quotationTaxRate,callback:function(a){e.$set(t.row,"quotationTaxRate",a)},expression:"scope.row.quotationTaxRate"}}),e._v(" % ")]}}])}),a("el-table-column",{attrs:{label:"小计",prop:"quotationTotal",width:"48"}})],1),a("el-table-column",{attrs:{"class-name":"showBorder",width:"20px"}}),a("el-table-column",{attrs:{label:"应付明细"}},[a("el-table-column",{attrs:{label:"供应商关联单位",width:"90"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.showSupplier?e._e():a("div",{on:{click:function(e){t.row.showSupplier=!0}}},[e._v(" "+e._s(t.row.supplier)+" ")]),e.$store.state.data.supplierList.length>0&&t.row.showSupplier?a("tree-select",{attrs:{flat:!1,multiple:!1,pass:t.row.supplierId,placeholder:"供应商",type:"supplier"},on:{return:function(e){t.row.supplierId=e}}}):e._e()]}}])}),a("el-table-column",{attrs:{align:"center",label:"费用",prop:"costChargeId",width:"80px"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.showCostCharge?e._e():a("div",{on:{click:function(e){t.row.showCostCharge=!0}}},[e._v(" "+e._s(t.row.costCharge)+" ")]),t.row.showCostCharge?a("tree-select",{attrs:{dbn:!0,flat:!1,multiple:!1,pass:t.row.inquiryChargeId,placeholder:"运费",type:"charge"},on:{return:function(e){t.row.inquiryChargeId=e}}}):e._e()]}}])}),a("el-table-column",{attrs:{align:"center",label:"策略",prop:"costStrategyId",width:"58px"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-select",{attrs:{clearable:"",filterable:""},on:{change:function(e){1==t.row.inquiryStrategyId&&(t.row.inquiryRate=0)}},model:{value:t.row.inquiryStrategyId,callback:function(a){e.$set(t.row,"inquiryStrategyId",a)},expression:"scope.row.inquiryStrategyId"}},[a("el-option",{attrs:{label:"已含",value:"1"}},[e._v("已含")]),a("el-option",{attrs:{label:"未含",value:"0"}},[e._v("未含")])],1)]}}])}),a("el-table-column",{attrs:{align:"center",label:"货币",prop:"costCurrencyId",width:"70px"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.showCostCurrency?e._e():a("div",{staticStyle:{width:"69px",height:"23px"},on:{click:function(e){t.row.showCostCurrency=!0}}},[e._v(" "+e._s(t.row.inquiryCurrencyCode)+" ")]),t.row.showCostCurrency?a("tree-select",{attrs:{pass:t.row.inquiryCurrencyCode,type:"currency"},on:{return:function(e){t.row.inquiryCurrencyCode=e}}}):e._e()]}}])}),a("el-table-column",{attrs:{align:"center",label:"单价",prop:"inquiryRate",width:"68px"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-input-number",{staticStyle:{display:"flex",width:"100%"},attrs:{controls:!1,min:0},on:{change:function(a){return e.countProfit(t.row)}},model:{value:t.row.inquiryRate,callback:function(a){e.$set(t.row,"inquiryRate",a)},expression:"scope.row.inquiryRate"}})]}}])}),a("el-table-column",{attrs:{label:"单位",lign:"center",prop:"costUnitId",width:"70px"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.showCostUnit?e._e():a("div",{on:{click:function(e){t.row.showCostUnit=!0}}},[e._v(" "+e._s(t.row.inquiryUnitCode)+" ")]),t.row.showCostUnit?a("tree-select",{attrs:{pass:t.row.inquiryUnitCode,type:"unit"},on:{return:function(e){t.row.inquiryUnitCode=e}}}):e._e()]}}])}),a("el-table-column",{attrs:{align:"center",label:"数量",prop:"costAmount",width:"48px"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-input-number",{staticStyle:{display:"flex",width:"100%"},attrs:{controls:!1,min:1,placeholder:"数量"},on:{change:function(a){return e.countProfit(t.row)}},model:{value:t.row.inquiryAmount,callback:function(a){e.$set(t.row,"inquiryAmount",a)},expression:"scope.row.inquiryAmount"}})]}}])}),a("el-table-column",{attrs:{align:"center",label:"汇率",prop:"costExchangeRate",width:"60px"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-input-number",{staticStyle:{width:"100%"},attrs:{controls:!1,min:1,precision:4,step:1e-4},on:{change:function(a){return e.countProfit(t.row)}},model:{value:t.row.inquiryExchangeRate,callback:function(a){e.$set(t.row,"inquiryExchangeRate",a)},expression:"scope.row.inquiryExchangeRate"}})]}}])}),a("el-table-column",{attrs:{align:"center",label:"税率",prop:"costTaxRate",width:"68px"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-input-number",{staticStyle:{width:"75%"},attrs:{controls:!1,min:0},on:{change:function(a){return e.countProfit(t.row)}},model:{value:t.row.inquiryTaxRate,callback:function(a){e.$set(t.row,"inquiryTaxRate",a)},expression:"scope.row.inquiryTaxRate"}}),e._v(" % ")]}}])}),a("el-table-column",{attrs:{label:"小计",prop:"costTotal",width:"48"}})],1),a("el-table-column",{attrs:{"class-name":"showBorder",width:"20px"}}),a("el-table-column",{attrs:{label:"辅助决策"}},[a("el-table-column",{attrs:{align:"center",label:"单项利润",prop:"profit"}})],1),a("el-table-column",{attrs:{align:"center","class-name":"small-padding fixed-width",label:"操作",width:"50"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{attrs:{icon:"el-icon-delete",size:"mini",type:"danger"},on:{click:function(a){e.receivablePayable=e.receivablePayable.filter((function(e){return e!=t.row}))}}},[e._v("删除 ")])]}}])})],1)],1),a("el-button",{staticStyle:{padding:"0"},attrs:{type:"text"},on:{click:e.addReceivablePayable}},[e._v("[＋] ")])],1)},o=[],i=(a("14d9"),a("b680"),a("a9e3"),{name:"receivablePayable",props:["receivablePayable","openReceivablePayable"],watch:{receivablePayable:function(e){this.$emit("return",e)}},methods:{rowIndex:function(e){var t=e.row,a=e.rowIndex;t.id=a+1},addReceivablePayable:function(){var e={showClient:!0,showSupplier:!0,showQuotationCharge:!0,showCostCharge:!0,showQuotationCurrency:!0,showCostCurrency:!0,showQuotationUnit:!0,showCostUnit:!0};this.receivablePayable.push(e)},countProfit:function(e){e.profit=(Number(e.quotationRate)*Number(e.quotationAmount)-Number(e.inquiryRate)*Number(e.costAmount)).toFixed(2)}}}),l=i,n=a("2877"),s=Object(n["a"])(l,r,o,!1,null,"2a2d1ebc",null);t["default"]=s.exports},da57:function(e,t,a){},e350:function(e,t,a){"use strict";a.d(t,"a",(function(){return o})),a.d(t,"b",(function(){return i}));a("d3b7"),a("caad"),a("2532");var r=a("4360");function o(e){if(e&&e instanceof Array&&e.length>0){var t=r["a"].getters&&r["a"].getters.permissions,a=e,o="*:*:*",i=t.some((function(e){return o==e||a.includes(e)}));return!!i}return console.error("need roles! Like checkPermi=\"['system:user:add','system:user:edit']\""),!1}function i(e){if(e&&e instanceof Array&&e.length>0){var t=r["a"].getters&&r["a"].getters.roles,a=e,o="admin",i=t.some((function(e){return o==e||a.includes(e)}));return!!i}return console.error("need roles! Like checkRole=\"['admin','editor']\""),!1}},fff5:function(e,t,a){"use strict";a.d(t,"i",(function(){return o})),a.d(t,"b",(function(){return i})),a.d(t,"g",(function(){return l})),a.d(t,"a",(function(){return n})),a.d(t,"l",(function(){return s})),a.d(t,"e",(function(){return c})),a.d(t,"c",(function(){return d})),a.d(t,"h",(function(){return u})),a.d(t,"j",(function(){return f})),a.d(t,"f",(function(){return p})),a.d(t,"d",(function(){return m})),a.d(t,"m",(function(){return h})),a.d(t,"k",(function(){return b}));var r=a("b775");function o(e){return Object(r["a"])({url:"/system/rscharge/list",method:"get",params:e})}function i(e){return Object(r["a"])({url:"/system/rscharge/aggregator",method:"post",data:e})}function l(e){return Object(r["a"])({url:"/system/rscharge/"+e,method:"get"})}function n(e){return Object(r["a"])({url:"/system/rscharge",method:"post",data:e})}function s(e){return Object(r["a"])({url:"/system/rscharge",method:"put",data:e})}function c(e){return Object(r["a"])({url:"/system/rscharge/"+e,method:"delete"})}function d(e,t){var a={chargeId:e,status:t};return Object(r["a"])({url:"/system/rscharge/changeStatus",method:"put",data:a})}function u(e){return Object(r["a"])({url:"/system/rscharge/charges",method:"get",params:e})}function f(e){return Object(r["a"])({url:"/system/rscharge/selectList",method:"get",params:e})}function p(e){return Object(r["a"])({url:"/system/rscharge/findHedging",method:"get",params:e})}function m(e){return Object(r["a"])({url:"/system/rscharge/writeoff",method:"post",data:e})}function h(e){return Object(r["a"])({url:"/system/rscharge/verify",method:"post",data:e})}function b(e){return Object(r["a"])({url:"/system/rscharge/turnback",method:"post",data:e})}}}]);