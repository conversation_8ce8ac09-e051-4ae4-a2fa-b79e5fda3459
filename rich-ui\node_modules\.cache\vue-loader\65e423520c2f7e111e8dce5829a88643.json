{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\inventory\\index.vue?vue&type=template&id=cbe3c4ce&scoped=true&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\inventory\\index.vue", "mtime": 1750818094548}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\loaders\\templateLoader.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, null]}