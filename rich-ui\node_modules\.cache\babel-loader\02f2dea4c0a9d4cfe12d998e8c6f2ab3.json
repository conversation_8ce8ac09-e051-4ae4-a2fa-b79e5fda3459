{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js??ref--0-0!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\outboundRecord\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\outboundRecord\\index.vue", "mtime": 1750840424298}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\babel.config.js", "mtime": 1684722674797}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64: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"}, {"version": 3, "names": ["_outboundrecord", "require", "_rich", "_inventory", "_currency", "_interopRequireDefault", "name", "data", "selectedCargoDetail", "search", "showLeft", "showRight", "loading", "selectOutboundList", "ids", "single", "multiple", "showSearch", "total", "outboundrecordList", "title", "open", "queryParams", "pageNum", "pageSize", "isRentSettlement", "outboundNo", "clientCode", "clientName", "operatorId", "containerType", "containerNo", "sealNo", "outboundDate", "warehouseQuote", "workerLoadingFee", "warehouseCollection", "collectionNotes", "totalBoxes", "totalGrossWeight", "totalVolume", "totalRows", "receivedStorageFee", "unpaidUnloadingFee", "unpaidPackingFee", "logisticsAdvanceFee", "rentalBalanceFee", "overdueRent", "freeStackDays", "overdueUnitPrice", "form", "outboundType", "preOutboundInventoryListLoading", "rules", "outboundForm", "openOutbound", "preOutboundInventoryList", "watch", "n", "created", "getList", "methods", "isRowSelected", "row", "includes", "handleSearchEnter", "_this", "index", "findIndex", "item", "serialNo", "String", "inboundSerialNo", "searchValue", "table", "$refs", "$nextTick", "scrollWrapper", "$el", "querySelector", "rows", "querySelectorAll", "targetIndex", "for<PERSON>ach", "idx", "rowText", "textContent", "targetRow", "rowTop", "offsetTop", "scrollTo", "top", "clientHeight", "behavior", "classList", "add", "setTimeout", "remove", "$message", "warning", "warehouseConfirm", "loadChildInventory", "tree", "treeNode", "resolve", "_this2", "listInventory", "packageTo", "inventoryId", "then", "response", "children", "child", "push", "toggleRowSelection", "selectContainerType", "type", "clientRow", "rate20gp", "rate40hq", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "unreceivedFromCustomer", "currency", "additionalStorageFee", "overdueRentalFee", "difficultyWorkFee", "value", "receivedFromCustomer", "customerReceivableBalance", "subtract", "payableToWorker", "receivedUnloadingFee", "receivedPackingFee", "receivedFromSupplier", "receivedSupplier", "promissoryNoteSales", "promissoryNoteCost", "warehouseAdvanceOtherFee", "promissoryNoteGrossProfit", "generateOutboundBill", "_this3", "downloadOutboundBill", "fileName", "operator", "blob", "Blob", "link", "document", "createElement", "url", "window", "URL", "createObjectURL", "href", "download", "body", "append<PERSON><PERSON><PERSON>", "click", "<PERSON><PERSON><PERSON><PERSON>", "revokeObjectURL", "catch", "error", "console", "findOutboundRecord", "_this4", "outbound<PERSON><PERSON><PERSON>", "getOutboundrecords", "outboundRecordId", "rsInventoryList", "map", "includesInboundFee", "receivedFee", "Number", "inboundFee", "difference", "outboundConfirm", "_this5", "partialOutboundFlag", "addOutboundrecord", "preOutboundFlag", "rsCargoDetailsList", "preOutboundInventory", "success", "outboundInventory", "loadPreOutboundInventoryList", "_this6", "sqdPlannedOutboundDate", "plannedOutboundDate", "log", "handleOutbound", "selectedRows", "parseTime", "handleOutboundCargoDetailSelectionChange", "selection", "getSummaries", "param", "_this7", "columns", "sums", "statisticalField", "summaryResults", "column", "values", "property", "every", "isNaN", "sumValue", "reduce", "prev", "curr", "Object", "keys", "field", "handleOutboundSelectionChange", "outboundClient", "rateLcl", "freeStackPeriod", "$forceUpdate", "_this8", "listOutboundrecords", "cancel", "reset", "resetForm", "handleQuery", "reset<PERSON><PERSON>y", "handleStatusChange", "_this9", "text", "status", "$modal", "confirm", "changeStatus", "msgSuccess", "handleSelectionChange", "length", "handleAdd", "handleUpdate", "_this10", "getOutboundrecord", "submitForm", "_this11", "validate", "valid", "updateOutboundrecord", "handleDelete", "_this12", "outboundRecordIds", "delOutboundrecord", "handleExport", "_objectSpread2", "default", "concat", "Date", "getTime", "handleFeeChange", "$set", "updateTableData", "_this13", "outboundInventoryTable", "exports", "_default"], "sources": ["src/views/system/outboundRecord/index.vue"], "sourcesContent": ["<template>\r\n  <div class=\"app-container\">\r\n    <el-row :gutter=\"20\">\r\n      <el-col :span=\"showLeft\">\r\n        <el-form v-show=\"showSearch\" ref=\"queryForm\" :inline=\"true\" :model=\"queryParams\" class=\"query\"\r\n                 label-width=\"68px\"\r\n                 size=\"mini\"\r\n        >\r\n          <el-form-item label=\"出仓单号\" prop=\"outboundNo\">\r\n            <el-input\r\n              v-model=\"queryParams.outboundNo\"\r\n              clearable\r\n              placeholder=\"出仓单号\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"客户代码\" prop=\"clientCode\">\r\n            <el-input\r\n              v-model=\"queryParams.clientCode\"\r\n              clearable\r\n              placeholder=\"客户代码\"\r\n              @keyup.enter.native=\"handleQuery\"\r\n            />\r\n          </el-form-item>\r\n          <el-form-item label=\"出仓日期\" prop=\"outboundDate\">\r\n            <el-date-picker v-model=\"queryParams.outboundDateRange\"\r\n                            clearable style=\"width: 100%\"\r\n                            placeholder=\"出仓日期\"\r\n                            type=\"daterange\"\r\n                            :default-time=\"['00:00:00', '23:59:59']\"\r\n                            value-format=\"yyyy-MM-dd HH:mm:ss\"\r\n            >\r\n            </el-date-picker>\r\n          </el-form-item>\r\n          <el-form-item>\r\n            <el-button icon=\"el-icon-search\" size=\"mini\" type=\"primary\" @click=\"handleQuery\">搜索</el-button>\r\n            <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\r\n          </el-form-item>\r\n        </el-form>\r\n      </el-col>\r\n      <el-col :span=\"showRight\">\r\n        <el-row :gutter=\"10\" class=\"mb8\">\r\n          <el-col :span=\"1.5\">\r\n            <el-button\r\n              v-hasPermi=\"['system:inventory:export']\"\r\n              icon=\"el-icon-download\"\r\n              plain\r\n              size=\"mini\"\r\n              type=\"primary\"\r\n              @click=\"handleExport\"\r\n            >导出\r\n            </el-button>\r\n          </el-col>\r\n          <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\r\n        </el-row>\r\n\r\n        <!--出仓记录列表(预出仓记录列表)-->\r\n        <el-table v-loading=\"loading\" :data=\"outboundrecordList\" @selection-change=\"handleSelectionChange\"\r\n                  @row-dblclick=\"findOutboundRecord\"\r\n        >\r\n          <el-table-column align=\"center\" type=\"selection\" width=\"28\"/>\r\n          <el-table-column align=\"center\" label=\"出仓单号\" prop=\"outboundNo\"/>\r\n          <el-table-column align=\"center\" label=\"客户单号\" prop=\"customerOrderNo\"/>\r\n          <el-table-column align=\"center\" label=\"客户代码\" prop=\"clientCode\"/>\r\n          <el-table-column align=\"center\" label=\"客户名称\" prop=\"clientName\" show-overflow-tooltip/>\r\n          <el-table-column align=\"center\" label=\"操作员\" prop=\"operator\"/>\r\n          <el-table-column align=\"center\" label=\"柜型\" prop=\"containerType\"/>\r\n          <el-table-column align=\"center\" label=\"柜号\" prop=\"containerNo\" show-overflow-tooltip/>\r\n          <el-table-column align=\"center\" label=\"封号\" prop=\"sealNo\"/>\r\n          <el-table-column align=\"center\" label=\"出仓日期\" prop=\"outboundDate\" width=\"100\">\r\n            <template slot-scope=\"scope\">\r\n              <span>{{ parseTime(scope.row.outboundDate, \"{y}-{m}-{d}\") }}</span>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"仓库报价\" prop=\"warehouseQuote\"/>\r\n          <el-table-column align=\"center\" label=\"工人装柜费\" prop=\"workerLoadingFee\"/>\r\n          <el-table-column align=\"center\" label=\"仓管代收\" prop=\"warehouseCollection\"/>\r\n          <el-table-column align=\"center\" label=\"补收入仓费\" prop=\"additionalStorageFee\">\r\n            <template slot-scope=\"scope\">\r\n              <el-popover\r\n                placement=\"top\"\r\n                popper-class=\"fee-edit-popover\"\r\n                trigger=\"click\"\r\n                width=\"200\"\r\n              >\r\n                <el-input-number\r\n                  v-model=\"scope.row.additionalStorageFee\"\r\n                  :controls=\"false\"\r\n                  :precision=\"2\"\r\n                  size=\"mini\"\r\n                  style=\"width: 100%\"\r\n                  @change=\"handleFeeChange(scope.row, 'additionalStorageFee', $event)\"\r\n                />\r\n                <span slot=\"reference\">{{ scope.row.additionalStorageFee || 0 }}</span>\r\n              </el-popover>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"已收入仓费\" prop=\"receivedStorageFee\">\r\n            <template slot-scope=\"scope\">\r\n              <el-popover\r\n                placement=\"top\"\r\n                popper-class=\"fee-edit-popover\"\r\n                trigger=\"click\"\r\n                width=\"200\"\r\n              >\r\n                <el-input-number\r\n                  v-model=\"scope.row.receivedStorageFee\"\r\n                  :controls=\"false\"\r\n                  :precision=\"2\"\r\n                  size=\"mini\"\r\n                  style=\"width: 100%\"\r\n                  @change=\"handleFeeChange(scope.row, 'receivedStorageFee', $event)\"\r\n                />\r\n                <span slot=\"reference\">{{ scope.row.receivedStorageFee || 0 }}</span>\r\n              </el-popover>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"未收卸货费\" prop=\"unpaidUnloadingFee\">\r\n            <template slot-scope=\"scope\">\r\n              <el-popover\r\n                placement=\"top\"\r\n                popper-class=\"fee-edit-popover\"\r\n                trigger=\"click\"\r\n                width=\"200\"\r\n              >\r\n                <el-input-number\r\n                  v-model=\"scope.row.unpaidUnloadingFee\"\r\n                  :controls=\"false\"\r\n                  :precision=\"2\"\r\n                  size=\"mini\"\r\n                  style=\"width: 100%\"\r\n                  @change=\"handleFeeChange(scope.row, 'unpaidUnloadingFee', $event)\"\r\n                />\r\n                <span slot=\"reference\">{{ scope.row.unpaidUnloadingFee || 0 }}</span>\r\n              </el-popover>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"实付卸货费\" prop=\"receivedUnloadingFee\">\r\n            <template slot-scope=\"scope\">\r\n              <el-popover\r\n                placement=\"top\"\r\n                popper-class=\"fee-edit-popover\"\r\n                trigger=\"click\"\r\n                width=\"200\"\r\n              >\r\n                <el-input-number\r\n                  v-model=\"scope.row.receivedUnloadingFee\"\r\n                  :controls=\"false\"\r\n                  :precision=\"2\"\r\n                  size=\"mini\"\r\n                  style=\"width: 100%\"\r\n                  @change=\"handleFeeChange(scope.row, 'receivedUnloadingFee', $event)\"\r\n                />\r\n                <span slot=\"reference\">{{ scope.row.receivedUnloadingFee || 0 }}</span>\r\n              </el-popover>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"未收打包费\" prop=\"unpaidPackingFee\">\r\n            <template slot-scope=\"scope\">\r\n              <el-popover\r\n                placement=\"top\"\r\n                popper-class=\"fee-edit-popover\"\r\n                trigger=\"click\"\r\n                width=\"200\"\r\n              >\r\n                <el-input-number\r\n                  v-model=\"scope.row.unpaidPackingFee\"\r\n                  :controls=\"false\"\r\n                  :precision=\"2\"\r\n                  size=\"mini\"\r\n                  style=\"width: 100%\"\r\n                  @change=\"handleFeeChange(scope.row, 'unpaidPackingFee', $event)\"\r\n                />\r\n                <span slot=\"reference\">{{ scope.row.unpaidPackingFee || 0 }}</span>\r\n              </el-popover>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"实付打包费\" prop=\"receivedPackingFee\">\r\n            <template slot-scope=\"scope\">\r\n              <el-popover\r\n                placement=\"top\"\r\n                popper-class=\"fee-edit-popover\"\r\n                trigger=\"click\"\r\n                width=\"200\"\r\n              >\r\n                <el-input-number\r\n                  v-model=\"scope.row.receivedPackingFee\"\r\n                  :controls=\"false\"\r\n                  :precision=\"2\"\r\n                  size=\"mini\"\r\n                  style=\"width: 100%\"\r\n                  @change=\"handleFeeChange(scope.row, 'receivedPackingFee', $event)\"\r\n                />\r\n                <span slot=\"reference\">{{ scope.row.receivedPackingFee || 0 }}</span>\r\n              </el-popover>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"物流代垫费\" prop=\"logisticsAdvanceFee\">\r\n            <template slot-scope=\"scope\">\r\n              <el-popover\r\n                placement=\"top\"\r\n                popper-class=\"fee-edit-popover\"\r\n                trigger=\"click\"\r\n                width=\"200\"\r\n              >\r\n                <el-input-number\r\n                  v-model=\"scope.row.logisticsAdvanceFee\"\r\n                  :controls=\"false\"\r\n                  :precision=\"2\"\r\n                  size=\"mini\"\r\n                  style=\"width: 100%\"\r\n                  @change=\"handleFeeChange(scope.row, 'logisticsAdvanceFee', $event)\"\r\n                />\r\n                <span slot=\"reference\">{{ scope.row.logisticsAdvanceFee || 0 }}</span>\r\n              </el-popover>\r\n            </template>\r\n          </el-table-column>\r\n          <el-table-column align=\"center\" label=\"超期租金单价\" prop=\"overdueRentalUnitPrice\"/>\r\n          <el-table-column align=\"center\" label=\"超期租金\" prop=\"overdueRentalFee\"/>\r\n          <el-table-column align=\"center\" label=\"唛头\" prop=\"sqdShippingMark\" show-overflow-tooltip/>\r\n          <el-table-column align=\"center\" label=\"总货名\" prop=\"cargoName\" show-overflow-tooltip/>\r\n          <el-table-column align=\"center\" label=\"免堆期\" prop=\"freeStackPeriod\" width=\"50\"/>\r\n          <el-table-column align=\"center\" class-name=\"small-padding fixed-width\" label=\"操作\">\r\n            <template slot-scope=\"scope\">\r\n              <!--<el-button\r\n                v-hasPermi=\"['system:outboundrecord:edit']\"\r\n                icon=\"el-icon-edit\"\r\n                size=\"mini\"\r\n                style=\"margin-right: -8px\"\r\n                type=\"success\"\r\n                @click=\"handleUpdate(scope.row)\"\r\n              >修改\r\n              </el-button>-->\r\n              <el-button\r\n                v-hasPermi=\"['system:outboundrecord:remove']\"\r\n                icon=\"el-icon-delete\"\r\n                size=\"mini\"\r\n                style=\"margin-right: -8px\"\r\n                type=\"danger\"\r\n                @click=\"handleDelete(scope.row)\"\r\n              >删除\r\n              </el-button>\r\n            </template>\r\n          </el-table-column>\r\n        </el-table>\r\n        <pagination\r\n          v-show=\"total>0\"\r\n          :limit.sync=\"queryParams.pageSize\"\r\n          :page.sync=\"queryParams.pageNum\"\r\n          :total=\"total\"\r\n          @pagination=\"getList\"\r\n        />\r\n      </el-col>\r\n    </el-row>\r\n\r\n    <!-- 出仓对话框 -->\r\n    <el-dialog\r\n      v-dialogDrag\r\n      v-dialogDragWidth :close-on-click-modal=\"false\" :modal-append-to-body=\"false\" :title=\"'出库单'\"\r\n      :visible.sync=\"openOutbound\"\r\n      append-to-body\r\n      width=\"80%\"\r\n    >\r\n      <el-form ref=\"outboundForm\" :model=\"outboundForm\" :rules=\"rules\" class=\"edit\" label-width=\"80px\">\r\n        <el-row :gutter=\"10\">\r\n          <el-row :gutter=\"10\">\r\n            <el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"出仓单号\" prop=\"outboundNo\">\r\n                  <el-input v-model=\"outboundForm.outboundNo\" class=\"disable-form\" disabled placeholder=\"出仓单号\"/>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"客户单号\" prop=\"outboundNo\">\r\n                  <el-input v-model=\"outboundForm.customerOrderNo\" placeholder=\"客户单号\"/>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"客户代码\" prop=\"outboundNo\">\r\n                  <tree-select :flat=\"false\" :multiple=\"false\" :pass=\"outboundForm.clientCode\"\r\n                               :placeholder=\"'客户代码'\"\r\n                               :type=\"'warehouseClient'\" @return=\"outboundForm.clientCode=$event\"\r\n                               @returnData=\"outboundClient($event)\"\r\n                  />\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"客户名称\" prop=\"outboundNo\">\r\n                  <el-input v-model=\"outboundForm.clientName\" class=\"disable-form\" disabled\r\n                            placeholder=\"客户名称\"\r\n                  />\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"10\">\r\n            <el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"计划出仓\" prop=\"inboundDate\">\r\n                  <el-date-picker v-model=\"outboundForm.plannedOutboundDate\"\r\n                                  clearable\r\n                                  placeholder=\"计划出仓日期\"\r\n                                  style=\"width: 100%\"\r\n                                  type=\"date\"\r\n                                  value-format=\"yyyy-MM-dd\"\r\n                  >\r\n                  </el-date-picker>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"出仓方式\" prop=\"outboundType\">\r\n                  <el-select v-model=\"outboundForm.outboundType\" placeholder=\"出仓方式\" style=\"width: 100%\">\r\n                    <el-option label=\"整柜\" value=\"整柜\"></el-option>\r\n                    <el-option label=\"散货\" value=\"散货\"></el-option>\r\n                    <el-option label=\"快递\" value=\"快递\"></el-option>\r\n                    <el-option label=\"其他\" value=\"其他\"></el-option>\r\n                  </el-select>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"柜型\" prop=\"containerType\">\r\n                  <el-select v-model=\"outboundForm.containerType\" style=\"width: 100%\" @change=\"selectContainerType\">\r\n                    <el-option label=\"20GP\" value=\"20GP\"/>\r\n                    <el-option label=\"20OT\" value=\"20OT\"/>\r\n                    <el-option label=\"20FR\" value=\"20FR\"/>\r\n                    <el-option label=\"TANK\" value=\"TANK\"/>\r\n                    <el-option label=\"40GP\" value=\"40GP\"/>\r\n                    <el-option label=\"40HQ\" value=\"40HQ\"/>\r\n                    <el-option label=\"40NOR\" value=\"40NOR\"/>\r\n                    <el-option label=\"40OT\" value=\"40OT\"/>\r\n                    <el-option label=\"40FR\" value=\"40FR\"/>\r\n                    <el-option label=\"40RH\" value=\"40RH\"/>\r\n                    <el-option label=\"45HQ\" value=\"45HQ\"/>\r\n                  </el-select>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"货物类型\" prop=\"cargoType\">\r\n                  <el-select v-model=\"form.cargoType\" placeholder=\"请选择货物类型\" style=\"width: 100%\">\r\n                    <el-option label=\"普货\" value=\"普货\"></el-option>\r\n                    <el-option label=\"大件\" value=\"大件\"></el-option>\r\n                    <el-option label=\"鲜活\" value=\"鲜活\"></el-option>\r\n                    <el-option label=\"危品\" value=\"危品\"></el-option>\r\n                    <el-option label=\"冷冻\" value=\"冷冻\"></el-option>\r\n                    <el-option label=\"标记\" value=\"标记\"></el-option>\r\n                  </el-select>\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"10\">\r\n            <el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"柜号\" prop=\"containerNo\">\r\n                  <el-input v-model=\"outboundForm.containerNo\" placeholder=\"柜号\"/>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"封号\" prop=\"sealNo\">\r\n                  <el-input v-model=\"outboundForm.sealNo\" placeholder=\"封号\"/>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"车牌\" prop=\"plateNumber\">\r\n                  <el-input v-model=\"outboundForm.plateNumber\" placeholder=\"车牌\"/>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"司机电话\" prop=\"driverPhone\">\r\n                  <el-input v-model=\"outboundForm.driverPhone\" placeholder=\"司机电话\"/>\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"10\">\r\n            <el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"仓库报价\" prop=\"warehouseQuote\">\r\n                  <el-input v-model=\"outboundForm.warehouseQuote\" class=\"number\" placeholder=\"仓库报价\"/>\r\n                  <!--<el-col :span=\"12\">\r\n                    <el-input v-model=\"outboundForm.difficultyWorkFee\" class=\"number\" placeholder=\"困难作业费\"/>\r\n                  </el-col>-->\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"仓管代收\" prop=\"outboundNotes\">\r\n                  <el-input v-model=\"outboundForm.warehouseCollection\" class=\"number\" placeholder=\"仓管代收\"/>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"工人装柜费\" prop=\"workerLoadingFee\">\r\n                  <el-input v-model=\"outboundForm.workerLoadingFee\" class=\"number\" placeholder=\"工人装柜费\"/>\r\n                  <!--<el-col :span=\"12\">\r\n                    <el-input v-model=\"outboundForm.warehouseAdvanceOtherFee\" class=\"number\"\r\n                              placeholder=\"仓库代付其他费用\"\r\n                    />\r\n                  </el-col>-->\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"仓管代付\" prop=\"outboundNotes\">\r\n                  <el-input v-model=\"outboundForm.warehousePay\" class=\"number\" placeholder=\"仓管代收\"/>\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"10\">\r\n            <el-col>\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"操作要求\" prop=\"operationRequirement\">\r\n                  <el-input v-model=\"outboundForm.operationRequirement\" :autosize=\"{ minRows: 4}\" maxlength=\"250\"\r\n                            placeholder=\"内容\"\r\n                            show-word-limit type=\"textarea\"\r\n                  />\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"12\">\r\n                <el-form-item label=\"仓管指示\" prop=\"outboundNote\">\r\n                  <el-input v-model=\"outboundForm.outboundNote\" :autosize=\"{ minRows: 4}\" maxlength=\"250\"\r\n                            placeholder=\"内容\"\r\n                            show-word-limit type=\"textarea\"\r\n                  />\r\n                </el-form-item>\r\n              </el-col>\r\n            </el-col>\r\n          </el-row>\r\n          <el-row :gutter=\"10\">\r\n            <el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"操作员\" prop=\"operator\">\r\n                  <el-input v-model=\"outboundForm.operator\" class=\"disable-form\" disabled placeholder=\"操作员\"/>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"下单日期\" prop=\"orderDate\">\r\n                  <el-date-picker v-model=\"outboundForm.orderDate\"\r\n                                  class=\"disable-form\" clearable disabled\r\n                                  placeholder=\"出仓日期\"\r\n                                  style=\"width: 100%\"\r\n                                  type=\"date\"\r\n                                  value-format=\"yyyy-MM-dd\"\r\n                  >\r\n                  </el-date-picker>\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"6\">\r\n                <el-form-item label=\"出仓经手人\" prop=\"outboundHandler\">\r\n                  <el-input v-model=\"outboundForm.outboundHandler\" class=\"disable-form\" disabled\r\n                            placeholder=\"出仓经手人\"\r\n                  />\r\n                </el-form-item>\r\n              </el-col>\r\n              <el-col :span=\"6\">\r\n                <el-row>\r\n                  <el-col :span=\"12\">\r\n                    <el-button :disabled=\"outboundForm.clientCode===null\" type=\"primary\" @click=\"warehouseConfirm\">\r\n                      {{ \"仓管确认\" }}\r\n                    </el-button>\r\n                  </el-col>\r\n                  <el-col :span=\"12\">\r\n                    <el-button :disabled=\"outboundForm.clientCode===null\" type=\"primary\"\r\n                               @click=\"loadPreOutboundInventoryList\">\r\n                      {{ \"加载待出库\" }}\r\n                    </el-button>\r\n                  </el-col>\r\n                </el-row>\r\n              </el-col>\r\n            </el-col>\r\n          </el-row>\r\n        </el-row>\r\n        <!--库存记录列表(未出库)-->\r\n        <el-row :gutter=\"10\">\r\n          <el-col>\r\n            <el-col>\r\n              <el-table ref=\"table\" v-loading=\"preOutboundInventoryListLoading\"\r\n                        :data=\"outboundForm.rsInventoryList\" :load=\"loadChildInventory\" :summary-method=\"getSummaries\"\r\n                        :tree-props=\"{children: 'children', hasChildren: 'hasChildren'}\" lazy\r\n                        max-height=\"300\" row-key=\"inventoryId\" show-summary\r\n                        style=\"width: 100%;\"\r\n                        @selection-change=\"handleOutboundSelectionChange\"\r\n              >\r\n                <el-table-column align=\"center\" label=\"入仓流水号\" prop=\"inboundSerialNo\"/>\r\n                <el-table-column align=\"center\" label=\"货物明细\" width=\"50\">\r\n                  <template slot-scope=\"scope\">\r\n                    <el-popover\r\n                      trigger=\"click\"\r\n                      width=\"800\"\r\n                    >\r\n                      <el-table :data=\"scope.row.rsCargoDetailsList\"\r\n                                @selection-change=\"(selectedRows) => handleOutboundCargoDetailSelectionChange(selectedRows,scope.row)\"\r\n                      >\r\n                        <el-table-column align=\"center\" type=\"selection\" width=\"28\"/>\r\n                        <el-table-column\r\n                          label=\"唛头\"\r\n                          prop=\"shippingMark\"\r\n                          width=\"150\"\r\n                        >\r\n                        </el-table-column>\r\n                        <el-table-column\r\n                          label=\"货名\"\r\n                          prop=\"itemName\"\r\n                          width=\"150\"\r\n                        >\r\n                        </el-table-column>\r\n                        <el-table-column\r\n                          label=\"箱数\"\r\n                          prop=\"boxCount\"\r\n                        >\r\n                        </el-table-column>\r\n                        <el-table-column\r\n                          label=\"包装类型\"\r\n                          prop=\"packageType\"\r\n                        >\r\n                        </el-table-column>\r\n                        <el-table-column\r\n                          label=\"单件毛重\"\r\n                          prop=\"unitGrossWeight\"\r\n                        >\r\n                        </el-table-column>\r\n                        <el-table-column\r\n                          label=\"单件长\"\r\n                          prop=\"unitLength\"\r\n                        >\r\n                        </el-table-column>\r\n                        <el-table-column\r\n                          label=\"单件宽\"\r\n                          prop=\"unitWidth\"\r\n                        >\r\n                        </el-table-column>\r\n                        <el-table-column\r\n                          label=\"单件高\"\r\n                          prop=\"unitHeight\"\r\n                        >\r\n                        </el-table-column>\r\n                        <el-table-column\r\n                          label=\"单件体积\"\r\n                          prop=\"unitVolume\"\r\n                        >\r\n                        </el-table-column>\r\n                        <el-table-column\r\n                          label=\"破损标志\"\r\n                          prop=\"damageStatus\"\r\n                        >\r\n                        </el-table-column>\r\n                      </el-table>\r\n                      <el-button slot=\"reference\" style=\"margin: 0;padding: 5px\">查看</el-button>\r\n                    </el-popover>\r\n                  </template>\r\n                </el-table-column>\r\n                <el-table-column align=\"center\" label=\"入仓日期\" prop=\"actualInboundTime\" width=\"80\">\r\n                  <template slot-scope=\"scope\">\r\n                    <span>{{ parseTime(scope.row.actualInboundTime, \"{y}-{m}-{d}\") }}</span>\r\n                  </template>\r\n                </el-table-column>\r\n                <el-table-column align=\"center\" label=\"货代单号\" prop=\"forwarderNo\"/>\r\n                <el-table-column align=\"center\" label=\"客户代码\" prop=\"clientCode\"/>\r\n                <el-table-column align=\"center\" label=\"司机信息\" prop=\"driverInfo\"/>\r\n                <el-table-column align=\"center\" label=\"箱数\" prop=\"totalBoxes\" width=\"50\"/>\r\n                <el-table-column align=\"center\" label=\"毛重\" prop=\"totalGrossWeight\" width=\"80\"/>\r\n                <el-table-column align=\"center\" label=\"体积\" prop=\"totalVolume\" width=\"50\"/>\r\n                <el-table-column align=\"center\" label=\"已收供应商\" prop=\"receivedSupplier\">\r\n                  <template slot-scope=\"scope\">\r\n                    <el-popover\r\n                      placement=\"top\"\r\n                      popper-class=\"fee-edit-popover\"\r\n                      trigger=\"click\"\r\n                      width=\"200\"\r\n                    >\r\n                      <el-input-number\r\n                        v-model=\"scope.row.receivedSupplier\"\r\n                        :controls=\"false\"\r\n                        :precision=\"2\"\r\n                        size=\"mini\"\r\n                        style=\"width: 100%\"\r\n                        @change=\"handleFeeChange(scope.row, 'receivedSupplier', $event)\"\r\n                      />\r\n                      <span slot=\"reference\">{{ scope.row.receivedSupplier || 0 }}</span>\r\n                    </el-popover>\r\n                  </template>\r\n                </el-table-column>\r\n                <el-table-column align=\"center\" label=\"入仓费\" prop=\"inboundFee\">\r\n                  <template slot-scope=\"scope\">\r\n                    <el-popover\r\n                      placement=\"top\"\r\n                      popper-class=\"fee-edit-popover\"\r\n                      trigger=\"click\"\r\n                      width=\"200\"\r\n                    >\r\n                      <el-input-number\r\n                        v-model=\"scope.row.inboundFee\"\r\n                        :controls=\"false\"\r\n                        :precision=\"2\"\r\n                        size=\"mini\"\r\n                        style=\"width: 100%\"\r\n                        @change=\"handleFeeChange(scope.row, 'inboundFee', $event)\"\r\n                      />\r\n                      <span slot=\"reference\">{{ scope.row.inboundFee || 0 }}</span>\r\n                    </el-popover>\r\n                  </template>\r\n                </el-table-column>\r\n                <el-table-column align=\"center\" label=\"已收入仓费\" prop=\"receivedStorageFee\">\r\n                  <template slot-scope=\"scope\">\r\n                    <el-popover\r\n                      placement=\"top\"\r\n                      popper-class=\"fee-edit-popover\"\r\n                      trigger=\"click\"\r\n                      width=\"200\"\r\n                    >\r\n                      <el-input-number\r\n                        v-model=\"scope.row.receivedStorageFee\"\r\n                        :controls=\"false\"\r\n                        :precision=\"2\"\r\n                        size=\"mini\"\r\n                        style=\"width: 100%\"\r\n                        @change=\"handleFeeChange(scope.row, 'receivedStorageFee', $event)\"\r\n                      />\r\n                      <span slot=\"reference\">{{ scope.row.receivedStorageFee || 0 }}</span>\r\n                    </el-popover>\r\n                  </template>\r\n                </el-table-column>\r\n                <el-table-column align=\"center\" label=\"补收入仓费\" prop=\"additionalStorageFee\">\r\n                  <template slot-scope=\"scope\">\r\n                    <el-popover\r\n                      placement=\"top\"\r\n                      popper-class=\"fee-edit-popover\"\r\n                      trigger=\"click\"\r\n                      width=\"200\"\r\n                    >\r\n                      <el-input-number\r\n                        v-model=\"scope.row.additionalStorageFee\"\r\n                        :controls=\"false\"\r\n                        :precision=\"2\"\r\n                        size=\"mini\"\r\n                        style=\"width: 100%\"\r\n                        @change=\"handleFeeChange(scope.row, 'additionalStorageFee', $event)\"\r\n                      />\r\n                      <span slot=\"reference\">{{ scope.row.additionalStorageFee || 0 }}</span>\r\n                    </el-popover>\r\n                  </template>\r\n                </el-table-column>\r\n                <el-table-column align=\"center\" label=\"未收卸货费\" prop=\"unpaidUnloadingFee\">\r\n                  <template slot-scope=\"scope\">\r\n                    <el-popover\r\n                      placement=\"top\"\r\n                      popper-class=\"fee-edit-popover\"\r\n                      trigger=\"click\"\r\n                      width=\"200\"\r\n                    >\r\n                      <el-input-number\r\n                        v-model=\"scope.row.unpaidUnloadingFee\"\r\n                        :controls=\"false\"\r\n                        :precision=\"2\"\r\n                        size=\"mini\"\r\n                        style=\"width: 100%\"\r\n                        @change=\"handleFeeChange(scope.row, 'unpaidUnloadingFee', $event)\"\r\n                      />\r\n                      <span slot=\"reference\">{{ scope.row.unpaidUnloadingFee || 0 }}</span>\r\n                    </el-popover>\r\n                  </template>\r\n                </el-table-column>\r\n                <el-table-column align=\"center\" label=\"实付卸货费\" prop=\"receivedUnloadingFee\">\r\n                  <template slot-scope=\"scope\">\r\n                    <el-popover\r\n                      placement=\"top\"\r\n                      popper-class=\"fee-edit-popover\"\r\n                      trigger=\"click\"\r\n                      width=\"200\"\r\n                    >\r\n                      <el-input-number\r\n                        v-model=\"scope.row.receivedUnloadingFee\"\r\n                        :controls=\"false\"\r\n                        :precision=\"2\"\r\n                        size=\"mini\"\r\n                        style=\"width: 100%\"\r\n                        @change=\"handleFeeChange(scope.row, 'receivedUnloadingFee', $event)\"\r\n                      />\r\n                      <span slot=\"reference\">{{ scope.row.receivedUnloadingFee || 0 }}</span>\r\n                    </el-popover>\r\n                  </template>\r\n                </el-table-column>\r\n                  <el-table-column align=\"center\" label=\"未收打包费\" prop=\"unpaidPackingFee\">\r\n                    <template slot-scope=\"scope\">\r\n                      <el-popover\r\n                        placement=\"top\"\r\n                        popper-class=\"fee-edit-popover\"\r\n                        trigger=\"click\"\r\n                        width=\"200\"\r\n                      >\r\n                        <el-input-number\r\n                          v-model=\"scope.row.unpaidPackingFee\"\r\n                          :controls=\"false\"\r\n                          :precision=\"2\"\r\n                          size=\"mini\"\r\n                          style=\"width: 100%\"\r\n                          @change=\"handleFeeChange(scope.row, 'unpaidPackingFee', $event)\"\r\n                        />\r\n                        <span slot=\"reference\">{{ scope.row.unpaidPackagingFee || 0 }}</span>\r\n                        <span slot=\"reference\">{{ scope.row.unpaidPackingFee || 0 }}</span>\r\n                      </el-popover>\r\n                    </template>\r\n                  </el-table-column>\r\n                  <el-table-column align=\"center\" label=\"实付打包费\" prop=\"receivedPackingFee\">\r\n                    <template slot-scope=\"scope\">\r\n                      <el-popover\r\n                        placement=\"top\"\r\n                        popper-class=\"fee-edit-popover\"\r\n                        trigger=\"click\"\r\n                        width=\"200\"\r\n                      >\r\n                        <el-input-number\r\n                          v-model=\"scope.row.receivedPackingFee\"\r\n                          :controls=\"false\"\r\n                          :precision=\"2\"\r\n                          size=\"mini\"\r\n                          style=\"width: 100%\"\r\n                          @change=\"handleFeeChange(scope.row, 'receivedPackingFee', $event)\"\r\n                        />\r\n                        <span slot=\"reference\">{{ scope.row.receivedPackingFee || 0 }}</span>\r\n                      </el-popover>\r\n                    </template>\r\n                  </el-table-column>\r\n                  <el-table-column align=\"center\" label=\"物流代垫费\" prop=\"logisticsAdvanceFee\">\r\n                    <template slot-scope=\"scope\">\r\n                      <el-popover\r\n                        placement=\"top\"\r\n                        popper-class=\"fee-edit-popover\"\r\n                        trigger=\"click\"\r\n                        width=\"200\"\r\n                      >\r\n                        <el-input-number\r\n                          v-model=\"scope.row.logisticsAdvanceFee\"\r\n                          :controls=\"false\"\r\n                          :precision=\"2\"\r\n                          size=\"mini\"\r\n                          style=\"width: 100%\"\r\n                          @change=\"handleFeeChange(scope.row, 'logisticsAdvanceFee', $event)\"\r\n                        />\r\n                        <span slot=\"reference\">{{ scope.row.logisticsAdvanceFee || 0 }}</span>\r\n                      </el-popover>\r\n                    </template>\r\n                  </el-table-column>\r\n                  <el-table-column align=\"center\" label=\"超期租金\" prop=\"overdueRentalFee\">\r\n                    <template slot-scope=\"scope\">\r\n                      <el-popover\r\n                        placement=\"top\"\r\n                        popper-class=\"fee-edit-popover\"\r\n                        trigger=\"click\"\r\n                        width=\"200\"\r\n                      >\r\n                        <el-input-number\r\n                          v-model=\"scope.row.overdueRentalFee\"\r\n                          :controls=\"false\"\r\n                          :precision=\"2\"\r\n                          size=\"mini\"\r\n                          style=\"width: 100%\"\r\n                          @change=\"handleFeeChange(scope.row, 'overdueRentalFee', $event)\"\r\n                        />\r\n                        <span slot=\"reference\">{{ scope.row.overdueRentalFee || 0 }}</span>\r\n                      </el-popover>\r\n                    </template>\r\n                  </el-table-column>\r\n              </el-table>\r\n            </el-col>\r\n          </el-col>\r\n        </el-row>\r\n\r\n        <!--汇总费用-->\r\n        <el-row>\r\n          <el-col :span=\"4\">\r\n            <span>未收客户：{{ outboundForm.unreceivedFromCustomer }}</span>\r\n          </el-col>\r\n          <el-col :span=\"4\">\r\n            <span>已收客户：{{ outboundForm.receivedFromCustomer }}</span>\r\n          </el-col>\r\n          <el-col :span=\"4\">\r\n            <span>应收客户余额：{{ outboundForm.customerReceivableBalance }}</span>\r\n          </el-col>\r\n          <el-col :span=\"4\">\r\n            <span>应付工人：{{ outboundForm.payableToWorker }}</span>\r\n          </el-col>\r\n        </el-row>\r\n        <el-row>\r\n          <el-col :span=\"4\">\r\n            <span>本票销售额：{{ outboundForm.promissoryNoteSales }}</span>\r\n          </el-col>\r\n          <el-col :span=\"4\">\r\n            <span>本票成本：{{ outboundForm.promissoryNoteCost }}</span>\r\n          </el-col>\r\n          <el-col :span=\"4\">\r\n            <span>本票结余：{{ outboundForm.promissoryNoteGrossProfit }}</span>\r\n          </el-col>\r\n          <el-col :span=\"4\">\r\n            <span>已收供应商总额：{{ outboundForm.receivedFromSupplier }}</span>\r\n          </el-col>\r\n        </el-row>\r\n      </el-form>\r\n      <span slot=\"footer\" class=\"dialog-footer\">\r\n    <el-button type=\"primary\" @click=\"submitForm\">保存</el-button>\r\n    <el-button @click=\"generateOutboundBill\">生成出仓单</el-button>\r\n        <!--    <el-button v-if=\"outboundType===0\" type=\"primary\" @click=\"outboundConfirm(0)\">确定预出仓</el-button>\r\n            <el-button v-else type=\"primary\" @click=\"outboundConfirm(1)\">出仓</el-button>-->\r\n  </span>\r\n    </el-dialog>\r\n  </div>\r\n</template>\r\n\r\n<script>\r\nimport {\r\n  addOutboundrecord,\r\n  changeStatus,\r\n  delOutboundrecord, downloadOutboundBill,\r\n  getOutboundrecord, getOutboundrecords,\r\n  listOutboundrecord, listOutboundrecords,\r\n  updateOutboundrecord\r\n} from \"@/api/system/outboundrecord\"\r\nimport {parseTime} from \"../../../utils/rich\"\r\nimport {listInventory, outboundInventory, preOutboundInventory} from \"@/api/system/inventory\"\r\nimport currency from \"currency.js\"\r\n\r\nexport default {\r\n  name: \"Outboundrecord\",\r\n  data() {\r\n    return {\r\n      selectedCargoDetail:[],\r\n      search: null,\r\n      showLeft: 0,\r\n      showRight: 24,\r\n      // 遮罩层\r\n      loading: true,\r\n      selectOutboundList: [],\r\n      // 选中数组\r\n      ids: [],\r\n      // 非单个禁用\r\n      single: true,\r\n      // 非多个禁用\r\n      multiple: true,\r\n      // 显示搜索条件\r\n      showSearch: false,\r\n      // 总条数\r\n      total: 0,\r\n      // 出仓记录表格数据\r\n      outboundrecordList: [],\r\n      // 弹出层标题\r\n      title: \"\",\r\n      // 是否显示弹出层\r\n      open: false,\r\n      // 查询参数\r\n      queryParams: {\r\n        pageNum: 1,\r\n        pageSize: 20,\r\n        isRentSettlement: 0,\r\n        outboundNo: null,\r\n        clientCode: null,\r\n        clientName: null,\r\n        operatorId: null,\r\n        containerType: null,\r\n        containerNo: null,\r\n        sealNo: null,\r\n        outboundDate: null,\r\n        warehouseQuote: null,\r\n        workerLoadingFee: null,\r\n        warehouseCollection: null,\r\n        collectionNotes: null,\r\n        totalBoxes: null,\r\n        totalGrossWeight: null,\r\n        totalVolume: null,\r\n        totalRows: null,\r\n        receivedStorageFee: null,\r\n        unpaidUnloadingFee: null,\r\n        unpaidPackingFee: null,\r\n        logisticsAdvanceFee: null,\r\n        rentalBalanceFee: null,\r\n        overdueRent: null,\r\n        freeStackDays: null,\r\n        overdueUnitPrice: null\r\n      },\r\n      // 表单参数\r\n      form: {},\r\n      outboundType: null,\r\n      preOutboundInventoryListLoading: false,\r\n      // 表单校验\r\n      rules: {},\r\n      outboundForm: {},\r\n      openOutbound: false,\r\n      preOutboundInventoryList: []\r\n    }\r\n  },\r\n  watch: {\r\n    showSearch(n) {\r\n      if (n === true) {\r\n        this.showRight = 21\r\n        this.showLeft = 3\r\n      } else {\r\n        this.showRight = 24\r\n        this.showLeft = 0\r\n      }\r\n    }\r\n  },\r\n  created() {\r\n    this.getList()\r\n  },\r\n  methods: {\r\n    // 判断当前行是否被选中\r\n    isRowSelected(row) {\r\n      return this.selectedCargoDetail.includes(row)\r\n    },\r\n    // 添加搜索并滚动到匹配行的方法\r\n    handleSearchEnter() {\r\n      if (!this.search) return;\r\n\r\n      // 查找匹配的行索引\r\n      const index = this.preOutboundInventoryList.findIndex(\r\n        item => {\r\n          // 确保 inboundSerialNo 存在且为字符串\r\n          const serialNo = String(item.inboundSerialNo || '');\r\n          const searchValue = String(this.search);\r\n          // 打印每次比较的值，帮助调试\r\n          return serialNo.includes(searchValue);\r\n        }\r\n      );\r\n\r\n      if (index > -1) {\r\n        // 获取表格DOM\r\n        const table = this.$refs.table;\r\n\r\n        this.$nextTick(() => {\r\n          // 获取表格的滚动容器\r\n          const scrollWrapper = table.$el.querySelector('.el-table__body-wrapper');\r\n          // 获取所有行\r\n          const rows = scrollWrapper.querySelectorAll('.el-table__row');\r\n\r\n          // 遍历所有行，找到匹配的流水号\r\n          let targetIndex = -1;\r\n          rows.forEach((row, idx) => {\r\n            const rowText = row.textContent;\r\n            if (rowText.includes(this.search)) {\r\n              targetIndex = idx;\r\n            }\r\n          });\r\n\r\n\r\n          if (targetIndex > -1) {\r\n            const targetRow = rows[targetIndex];\r\n            // 计算需要滚动的位置\r\n            const rowTop = targetRow.offsetTop;\r\n\r\n            // 使用平滑滚动\r\n            scrollWrapper.scrollTo({\r\n              top: rowTop - scrollWrapper.clientHeight / 2,\r\n              behavior: 'smooth'\r\n            });\r\n\r\n            // 高亮显示该行\r\n            targetRow.classList.add('highlight-row');\r\n            // 1秒后移除高亮\r\n            setTimeout(() => {\r\n              targetRow.classList.remove('highlight-row');\r\n            }, 2000);\r\n          }\r\n        });\r\n      } else {\r\n        this.$message.warning('未找到匹配的记录');\r\n      }\r\n    },\r\n    warehouseConfirm() {\r\n\r\n    },\r\n    // 加载子节点数据\r\n    loadChildInventory(tree, treeNode, resolve) {\r\n      // 使用packageTo字段查询子节点\r\n      listInventory({packageTo: tree.inventoryId}).then(response => {\r\n        const rows = response.rows\r\n\r\n        // 先将数据传递给表格，确保子节点渲染\r\n        resolve(rows)\r\n        tree.children = rows\r\n\r\n        // 如果父项被选中，在子节点渲染完成后选中它们\r\n        if (this.ids.includes(tree.inventoryId)) {\r\n          setTimeout(() => {\r\n            rows.forEach(child => {\r\n              if (!this.ids.includes(child.inventoryId)) {\r\n                this.ids.push(child.inventoryId)\r\n                this.selectOutboundList.push(child)\r\n              }\r\n              // 在UI上选中子项\r\n              this.$refs.table.toggleRowSelection(child, true)\r\n            })\r\n          }, 50) // 等待DOM更新\r\n        }\r\n      })\r\n    },\r\n    selectContainerType(type) {\r\n      switch (type) {\r\n        case \"20GP\":\r\n          this.outboundForm.warehouseQuote = this.clientRow.rate20gp\r\n          break\r\n        case \"40HQ\":\r\n          this.outboundForm.warehouseQuote = this.clientRow.rate40hq\r\n          break\r\n\r\n      }\r\n    },\r\n    countSummary() {\r\n      this.outboundForm.unreceivedFromCustomer = currency(this.outboundForm.warehouseQuote).add(this.outboundForm.additionalStorageFee).add(this.outboundForm.unpaidUnloadingFee).add(this.outboundForm.unpaidPackingFee).add(this.outboundForm.logisticsAdvanceFee).add(this.outboundForm.overdueRentalFee).add(this.outboundForm.difficultyWorkFee).value\r\n      this.outboundForm.receivedFromCustomer = currency(this.outboundForm.warehouseCollection).value\r\n      this.outboundForm.customerReceivableBalance = currency(this.outboundForm.unreceivedFromCustomer).subtract(this.outboundForm.receivedFromCustomer).value\r\n      this.outboundForm.payableToWorker = currency(this.outboundForm.receivedUnloadingFee).add(this.outboundForm.receivedPackingFee).add(this.outboundForm.workerLoadingFee).value\r\n      this.outboundForm.receivedFromSupplier = currency(this.outboundForm.receivedSupplier).add(this.outboundForm.receivedStorageFee).value\r\n      this.outboundForm.promissoryNoteSales = currency(this.outboundForm.unreceivedFromCustomer).add(this.outboundForm.receivedFromSupplier).value\r\n      this.outboundForm.promissoryNoteCost = currency(this.outboundForm.payableToWorker).add(this.outboundForm.logisticsAdvanceFee).add(this.outboundForm.warehouseAdvanceOtherFee).value\r\n      this.outboundForm.promissoryNoteGrossProfit = currency(this.outboundForm.promissoryNoteSales).subtract(this.outboundForm.promissoryNoteCost).value\r\n    },\r\n    generateOutboundBill() {\r\n      downloadOutboundBill(this.outboundForm)\r\n        .then(response => {\r\n          // 获取文件的字节数组 (ArrayBuffer)\r\n          const data = response\r\n\r\n          // 获取文件名（如果在后端响应头中包含文件名）\r\n          let fileName = this.outboundForm.clientCode + \"-\" + this.outboundForm.operator + \"-\" + this.outboundForm.outboundNo + \".xlsx\"  // 默认文件名\r\n\r\n          // 创建一个 Blob 对象来存储文件\r\n          const blob = new Blob([data], {\r\n            type: \"application/vnd.openxmlformats-officedocument.spreadsheetml.sheet\"  // Excel 文件类型\r\n          })\r\n\r\n          // 创建一个临时链接，模拟点击来下载文件\r\n          const link = document.createElement(\"a\")\r\n          const url = window.URL.createObjectURL(blob)  // 创建一个 URL 指向 Blob 对象\r\n          link.href = url\r\n          link.download = fileName  // 设置下载的文件名\r\n\r\n          // 模拟点击链接，触发下载\r\n          document.body.appendChild(link)\r\n          link.click()\r\n\r\n          // 下载完成后移除链接，并释放 URL 对象\r\n          document.body.removeChild(link)\r\n          window.URL.revokeObjectURL(url)\r\n        })\r\n        .catch(error => {\r\n          console.error(\"文件下载失败:\", error)\r\n        })\r\n    },\r\n    // 查看出仓记录\r\n    findOutboundRecord(row) {\r\n      this.outboundReset()\r\n      getOutboundrecords(row.outboundRecordId).then(response => {\r\n        this.outboundForm = response.data\r\n        this.outboundForm.rsInventoryList = this.outboundForm.rsInventoryList ? this.outboundForm.rsInventoryList.map(item => {\r\n          // 计算补收入仓费\r\n          if (item.includesInboundFee === 0) {\r\n            const receivedFee = Number(item.receivedStorageFee || 0)\r\n            const inboundFee = Number(item.inboundFee || 0)\r\n            const difference = currency(inboundFee).subtract(receivedFee).value\r\n\r\n            // 只有当差值大于0时才设置补收费用\r\n            item.additionalStorageFee = difference > 0 ? difference : 0\r\n          } else {\r\n            item.additionalStorageFee = 0\r\n          }\r\n\r\n          return item\r\n        }) : []\r\n        this.openOutbound = true\r\n      })\r\n    },\r\n    /**\r\n     *\r\n     * @param type 0:预出仓/1:出仓\r\n     */\r\n    outboundConfirm(type) {\r\n      this.selectOutboundList.map(item => {\r\n        item.partialOutboundFlag = Number(item.partialOutboundFlag)\r\n      })\r\n\r\n      addOutboundrecord(this.outboundForm).then(response => {\r\n        if (response.data) {\r\n          const outboundRecordId = response.data\r\n\r\n          // 列表克隆一份,打上预出仓标志\r\n          let data = this.selectOutboundList.map(item => {\r\n            if (item.preOutboundFlag === \"1\") {\r\n              this.$message.warning(\"勾选记录中有以预出库记录,请重新勾选\")\r\n              return\r\n            }\r\n            type === 0 ? item.preOutboundFlag = \"1\" : null\r\n            item.outboundRecordId = outboundRecordId\r\n            item.rsCargoDetailsList ? item.rsCargoDetailsList.map(item => {\r\n              item.outboundRecordId = outboundRecordId\r\n              type === 0 ? item.preOutboundFlag = \"1\" : null\r\n              return item\r\n            }) : null\r\n            return item\r\n          })\r\n\r\n          if (type === 0) {\r\n            preOutboundInventory(data).then(response => {\r\n              this.getList()\r\n              this.$message.success(\"预出仓成功\")\r\n              this.openOutbound = false\r\n            })\r\n          } else {\r\n            // 出仓\r\n            outboundInventory(data).then(response => {\r\n              this.getList()\r\n              this.$message.success(\"出仓成功\")\r\n              this.openOutbound = false\r\n            })\r\n          }\r\n        }\r\n      })\r\n    },\r\n    // 根据出库信息加载待出库的记录\r\n    loadPreOutboundInventoryList() {\r\n      this.loading = true\r\n      let data = {}\r\n      data.sqdPlannedOutboundDate = this.outboundForm.plannedOutboundDate\r\n      data.clientCode = this.outboundForm.clientCode\r\n      listInventory(data).then(response => {\r\n        console.log(response)\r\n        this.preOutboundInventoryList = response.rows\r\n        this.total = response.total\r\n        this.loading = false\r\n      })\r\n    },\r\n    /**\r\n     *\r\n     * @param type 0:预出仓/1:正式出仓\r\n     */\r\n    handleOutbound(selectedRows, type) {\r\n      // this.outboundList = this.inventoryList.filter(item => this.ids.includes(item.inventoryId))\r\n      if (type === 1) {\r\n        this.outboundReset()\r\n        this.outboundForm = selectedRows\r\n      }\r\n      this.outboundType = type\r\n      this.openOutbound = true\r\n    },\r\n    parseTime,\r\n    handleOutboundCargoDetailSelectionChange(selection, row) {\r\n      row.rsCargoDetailsList = selection\r\n      this.selectedCargoDetail = selection\r\n    },\r\n    getSummaries(param) {\r\n      const {columns, data} = param\r\n      const sums = []\r\n      const statisticalField = [\r\n        \"receivedSupplier\", \"totalBoxes\", \"unpaidInboundFee\", \"totalGrossWeight\",\r\n        \"totalVolume\", \"receivedStorageFee\", \"unpaidUnloadingFee\", \"logisticsAdvanceFee\",\r\n        \"rentalBalanceFee\", \"overdueRentalFee\", \"additionalStorageFee\", \"unpaidUnloadingFee\",\r\n        \"unpaidPackingFee\", \"receivedUnloadingFee\", \"receivedPackingFee\", \"inboundFee\"\r\n      ]\r\n\r\n      // 汇总结果存储对象\r\n      const summaryResults = {}\r\n\r\n      columns.forEach((column, index) => {\r\n        if (index === 0) {\r\n          sums[index] = \"汇总\"\r\n          return\r\n        }\r\n\r\n        const values = data.map(item => Number(item[column.property]))\r\n\r\n        if (statisticalField.includes(column.property) && !values.every(value => isNaN(value))) {\r\n          const sumValue = values.reduce((prev, curr) => {\r\n            const value = Number(curr)\r\n            if (!isNaN(value)) {\r\n              return currency(prev).add(curr).value\r\n            } else {\r\n              return prev\r\n            }\r\n          }, 0)\r\n          sums[index] = sumValue\r\n\r\n          // 将汇总结果存储在 summaryResults 对象中\r\n          summaryResults[column.property] = sumValue\r\n        } else {\r\n          sums[index] = \" \"\r\n        }\r\n      })\r\n\r\n      // 如果需要将汇总结果赋值到表单字段中，可以在此处操作\r\n      // 假设表单字段的命名与统计字段一致\r\n      Object.keys(summaryResults).forEach(field => {\r\n        // if (this.outboundForm && this.outboundForm.hasOwnProperty(field)) {\r\n        if (this.outboundForm) {\r\n          this.outboundForm[field] = summaryResults[field]  // 将汇总值赋给表单字段\r\n        }\r\n      })\r\n\r\n      return sums\r\n    },\r\n\r\n    /* getSummaries(param) {\r\n      const {columns, data} = param\r\n      const sums = []\r\n      const statisticalField = [\"totalBoxes\", \"totalGrossWeight\", \"totalVolume\", \"receivedStorageFee\", \"unpaidUnloadingFee\", \"logisticsAdvanceFee\", \"rentalBalanceFee\", \"overdueRentalFee\"]\r\n      columns.forEach((column, index) => {\r\n        if (index === 0) {\r\n          sums[index] = \"汇总\"\r\n          return\r\n        }\r\n        const values = data.map(item => Number(item[column.property]))\r\n        if (statisticalField.includes(column.property) && !values.every(value => isNaN(value))) {\r\n          sums[index] = values.reduce((prev, curr) => {\r\n            const value = Number(curr)\r\n            if (!isNaN(value)) {\r\n              return prev + curr\r\n            } else {\r\n              return prev\r\n            }\r\n          }, 0)\r\n        } else {\r\n          sums[index] = \" \"\r\n        }\r\n      })\r\n      return sums\r\n    }, */\r\n    handleOutboundSelectionChange(selection) {\r\n      this.selectOutboundList = selection\r\n    },\r\n    outboundClient(row) {\r\n      this.outboundForm.warehouseQuote = row.rateLcl\r\n      this.outboundForm.freeStackDays = row.freeStackPeriod\r\n      this.outboundForm.overdueRent = row.overdueRent\r\n      this.outboundForm.clientName = row.clientName\r\n      // this.outboundForm.workerLoadingFee=row.workerLoadingFee\r\n      this.$forceUpdate()\r\n    },\r\n    /** 查询出仓记录列表 */\r\n    getList() {\r\n      this.loading = true\r\n      listOutboundrecords(this.queryParams).then(response => {\r\n        this.outboundrecordList = response.rows\r\n        this.total = response.total\r\n        this.loading = false\r\n      })\r\n    },\r\n    // 取消按钮\r\n    cancel() {\r\n      this.open = false\r\n      this.reset()\r\n    },\r\n    outboundReset() {\r\n      this.outboundForm = {\r\n        outboundRecordId: null,\r\n        outboundNo: null,\r\n        clientCode: null,\r\n        clientName: null,\r\n        operatorId: null,\r\n        containerType: null,\r\n        containerNo: null,\r\n        sealNo: null,\r\n        outboundDate: null,\r\n        warehouseQuote: null,\r\n        workerLoadingFee: null,\r\n        warehouseCollection: null,\r\n        collectionNotes: null,\r\n        totalBoxes: null,\r\n        totalGrossWeight: null,\r\n        totalVolume: null,\r\n        totalRows: null,\r\n        receivedStorageFee: null,\r\n        unpaidUnloadingFee: null,\r\n        unpaidPackingFee: null,\r\n        logisticsAdvanceFee: null,\r\n        rentalBalanceFee: null,\r\n        overdueRent: null,\r\n        freeStackDays: null,\r\n        overdueUnitPrice: null\r\n      }\r\n      this.preOutboundInventoryList = []\r\n      this.resetForm(\"outboundForm\")\r\n    },\r\n    // 表单重置\r\n    reset() {\r\n      this.form = {\r\n        outboundRecordId: null,\r\n        outboundNo: null,\r\n        clientCode: null,\r\n        clientName: null,\r\n        operatorId: null,\r\n        containerType: null,\r\n        containerNo: null,\r\n        sealNo: null,\r\n        outboundDate: null,\r\n        warehouseQuote: null,\r\n        workerLoadingFee: null,\r\n        warehouseCollection: null,\r\n        collectionNotes: null,\r\n        totalBoxes: null,\r\n        totalGrossWeight: null,\r\n        totalVolume: null,\r\n        totalRows: null,\r\n        receivedStorageFee: null,\r\n        unpaidUnloadingFee: null,\r\n        unpaidPackingFee: null,\r\n        logisticsAdvanceFee: null,\r\n        rentalBalanceFee: null,\r\n        overdueRent: null,\r\n        freeStackDays: null,\r\n        overdueUnitPrice: null\r\n      }\r\n      this.resetForm(\"form\")\r\n    },\r\n    /** 搜索按钮操作 */\r\n    handleQuery() {\r\n      this.queryParams.pageNum = 1\r\n      this.getList()\r\n    },\r\n    /** 重置按钮操作 */\r\n    resetQuery() {\r\n      this.resetForm(\"queryForm\")\r\n      this.handleQuery()\r\n    },\r\n    handleStatusChange(row) {\r\n      let text = row.status === \"0\" ? \"启用\" : \"停用\"\r\n      this.$modal.confirm(\"确认要\\\"\" + text + \"吗？\").then(function () {\r\n        return changeStatus(row.outboundRecordId, row.status)\r\n      }).then(() => {\r\n        this.$modal.msgSuccess(text + \"成功\")\r\n      }).catch(function () {\r\n        row.status = row.status === \"0\" ? \"1\" : \"0\"\r\n      })\r\n    },\r\n    // 多选框选中数据\r\n    handleSelectionChange(selection) {\r\n      this.ids = selection.map(item => item.outboundRecordId)\r\n      this.single = selection.length !== 1\r\n      this.multiple = !selection.length\r\n    },\r\n    /** 新增按钮操作 */\r\n    handleAdd() {\r\n      this.outboundReset()\r\n      this.open = true\r\n      this.title = \"添加出仓记录\"\r\n    },\r\n    /** 修改按钮操作 */\r\n    handleUpdate(row) {\r\n      this.outboundReset()\r\n      const outboundRecordId = row.outboundRecordId || this.ids\r\n      getOutboundrecord(outboundRecordId).then(response => {\r\n        this.form = response.data\r\n        this.open = true\r\n        this.title = \"修改出仓记录\"\r\n      })\r\n    },\r\n    /** 提交按钮 */\r\n    submitForm() {\r\n      this.$refs[\"outboundForm\"].validate(valid => {\r\n        if (valid) {\r\n          if (this.outboundForm.outboundRecordId != null) {\r\n            updateOutboundrecord(this.outboundForm).then(response => {\r\n              this.$modal.msgSuccess(\"修改成功\")\r\n              this.open = false\r\n              this.getList()\r\n            })\r\n          }\r\n        }\r\n      })\r\n    },\r\n    /** 删除按钮操作 */\r\n    handleDelete(row) {\r\n      const outboundRecordIds = row.outboundRecordId || this.ids\r\n      this.$modal.confirm(\"是否确认删除出仓记录编号为\\\"\" + outboundRecordIds + \"\\\"的数据项？\").then(function () {\r\n        return delOutboundrecord(outboundRecordIds)\r\n      }).then(() => {\r\n        this.getList()\r\n        this.$modal.msgSuccess(\"删除成功\")\r\n      }).catch(() => {\r\n      })\r\n    },\r\n    /** 导出按钮操作 */\r\n    handleExport() {\r\n      this.download(\"system/outboundrecord/export\", {\r\n        ...this.queryParams\r\n      }, `outboundrecord_${new Date().getTime()}.xlsx`)\r\n    },\r\n    // 处理费用字段变更的通用逻辑\r\n    handleFeeChange(row, field, value) {\r\n      // 确保值为数字\r\n      value = Number(value) || 0\r\n\r\n      // 使用$set确保响应式更新\r\n      this.$set(row, field, value)\r\n\r\n      // 对特定字段做额外处理\r\n      if (field === \"receivedStorageFee\" && row.includesInboundFee === 0) {\r\n        const inboundFee = Number(row.inboundFee || 0)\r\n        const difference = currency(inboundFee).subtract(value).value\r\n        this.$set(row, \"additionalStorageFee\", difference > 0 ? difference : 0)\r\n      }\r\n\r\n      if (field === 'inboundFee') {\r\n        const difference = currency(value).subtract(row.receivedStorageFee).value\r\n        this.$set(row, \"additionalStorageFee\", difference > 0 ? difference : 0)\r\n      }\r\n\r\n      // 强制更新表格视图\r\n      this.$forceUpdate()\r\n\r\n      // 将修改后的值设置回表格的数据源，确保表格显示最新值\r\n      const index = this.outboundForm.rsInventoryList.findIndex(item => item === row)\r\n      if (index !== -1) {\r\n        this.$set(this.outboundForm.rsInventoryList, index, {...row})\r\n      }\r\n\r\n      // 更新表格和汇总\r\n      this.updateTableData()\r\n    },\r\n    // 更新表格数据并重新计算汇总\r\n    updateTableData() {\r\n      // 强制更新视图\r\n      this.$forceUpdate()\r\n      // 重新计算汇总\r\n      this.$nextTick(() => {\r\n        // 调用计算汇总的方法\r\n        if (this.outboundForm.rsInventoryList && this.outboundForm.rsInventoryList.length > 0) {\r\n          this.getSummaries({\r\n            columns: this.$refs.outboundInventoryTable ? this.$refs.outboundInventoryTable.columns : [],\r\n            data: this.outboundForm.rsInventoryList\r\n          })\r\n        }\r\n        this.countSummary()\r\n      })\r\n    }\r\n  }\r\n}\r\n</script>\r\n\r\n<style scoped>\r\n/* 数字字段显示样式 */\r\n.el-table .cell span {\r\n  cursor: pointer;\r\n  color: #606266;\r\n  padding: 2px 5px;\r\n  border-radius: 3px;\r\n  transition: background-color 0.3s;\r\n}\r\n\r\n.el-table .cell span:hover {\r\n  background-color: #f5f7fa;\r\n}\r\n\r\n\r\n</style>\r\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;AAuyBA,IAAAA,eAAA,GAAAC,OAAA;AAQA,IAAAC,KAAA,GAAAD,OAAA;AACA,IAAAE,UAAA,GAAAF,OAAA;AACA,IAAAG,SAAA,GAAAC,sBAAA,CAAAJ,OAAA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;eAEA;EACAK,IAAA;EACAC,IAAA,WAAAA,KAAA;IACA;MACAC,mBAAA;MACAC,MAAA;MACAC,QAAA;MACAC,SAAA;MACA;MACAC,OAAA;MACAC,kBAAA;MACA;MACAC,GAAA;MACA;MACAC,MAAA;MACA;MACAC,QAAA;MACA;MACAC,UAAA;MACA;MACAC,KAAA;MACA;MACAC,kBAAA;MACA;MACAC,KAAA;MACA;MACAC,IAAA;MACA;MACAC,WAAA;QACAC,OAAA;QACAC,QAAA;QACAC,gBAAA;QACAC,UAAA;QACAC,UAAA;QACAC,UAAA;QACAC,UAAA;QACAC,aAAA;QACAC,WAAA;QACAC,MAAA;QACAC,YAAA;QACAC,cAAA;QACAC,gBAAA;QACAC,mBAAA;QACAC,eAAA;QACAC,UAAA;QACAC,gBAAA;QACAC,WAAA;QACAC,SAAA;QACAC,kBAAA;QACAC,kBAAA;QACAC,gBAAA;QACAC,mBAAA;QACAC,gBAAA;QACAC,WAAA;QACAC,aAAA;QACAC,gBAAA;MACA;MACA;MACAC,IAAA;MACAC,YAAA;MACAC,+BAAA;MACA;MACAC,KAAA;MACAC,YAAA;MACAC,YAAA;MACAC,wBAAA;IACA;EACA;EACAC,KAAA;IACAxC,UAAA,WAAAA,WAAAyC,CAAA;MACA,IAAAA,CAAA;QACA,KAAA/C,SAAA;QACA,KAAAD,QAAA;MACA;QACA,KAAAC,SAAA;QACA,KAAAD,QAAA;MACA;IACA;EACA;EACAiD,OAAA,WAAAA,QAAA;IACA,KAAAC,OAAA;EACA;EACAC,OAAA;IACA;IACAC,aAAA,WAAAA,cAAAC,GAAA;MACA,YAAAvD,mBAAA,CAAAwD,QAAA,CAAAD,GAAA;IACA;IACA;IACAE,iBAAA,WAAAA,kBAAA;MAAA,IAAAC,KAAA;MACA,UAAAzD,MAAA;;MAEA;MACA,IAAA0D,KAAA,QAAAX,wBAAA,CAAAY,SAAA,CACA,UAAAC,IAAA;QACA;QACA,IAAAC,QAAA,GAAAC,MAAA,CAAAF,IAAA,CAAAG,eAAA;QACA,IAAAC,WAAA,GAAAF,MAAA,CAAAL,KAAA,CAAAzD,MAAA;QACA;QACA,OAAA6D,QAAA,CAAAN,QAAA,CAAAS,WAAA;MACA,CACA;MAEA,IAAAN,KAAA;QACA;QACA,IAAAO,KAAA,QAAAC,KAAA,CAAAD,KAAA;QAEA,KAAAE,SAAA;UACA;UACA,IAAAC,aAAA,GAAAH,KAAA,CAAAI,GAAA,CAAAC,aAAA;UACA;UACA,IAAAC,IAAA,GAAAH,aAAA,CAAAI,gBAAA;;UAEA;UACA,IAAAC,WAAA;UACAF,IAAA,CAAAG,OAAA,WAAApB,GAAA,EAAAqB,GAAA;YACA,IAAAC,OAAA,GAAAtB,GAAA,CAAAuB,WAAA;YACA,IAAAD,OAAA,CAAArB,QAAA,CAAAE,KAAA,CAAAzD,MAAA;cACAyE,WAAA,GAAAE,GAAA;YACA;UACA;UAGA,IAAAF,WAAA;YACA,IAAAK,SAAA,GAAAP,IAAA,CAAAE,WAAA;YACA;YACA,IAAAM,MAAA,GAAAD,SAAA,CAAAE,SAAA;;YAEA;YACAZ,aAAA,CAAAa,QAAA;cACAC,GAAA,EAAAH,MAAA,GAAAX,aAAA,CAAAe,YAAA;cACAC,QAAA;YACA;;YAEA;YACAN,SAAA,CAAAO,SAAA,CAAAC,GAAA;YACA;YACAC,UAAA;cACAT,SAAA,CAAAO,SAAA,CAAAG,MAAA;YACA;UACA;QACA;MACA;QACA,KAAAC,QAAA,CAAAC,OAAA;MACA;IACA;IACAC,gBAAA,WAAAA,iBAAA,GAEA;IACA;IACAC,kBAAA,WAAAA,mBAAAC,IAAA,EAAAC,QAAA,EAAAC,OAAA;MAAA,IAAAC,MAAA;MACA;MACA,IAAAC,wBAAA;QAAAC,SAAA,EAAAL,IAAA,CAAAM;MAAA,GAAAC,IAAA,WAAAC,QAAA;QACA,IAAA9B,IAAA,GAAA8B,QAAA,CAAA9B,IAAA;;QAEA;QACAwB,OAAA,CAAAxB,IAAA;QACAsB,IAAA,CAAAS,QAAA,GAAA/B,IAAA;;QAEA;QACA,IAAAyB,MAAA,CAAA3F,GAAA,CAAAkD,QAAA,CAAAsC,IAAA,CAAAM,WAAA;UACAZ,UAAA;YACAhB,IAAA,CAAAG,OAAA,WAAA6B,KAAA;cACA,KAAAP,MAAA,CAAA3F,GAAA,CAAAkD,QAAA,CAAAgD,KAAA,CAAAJ,WAAA;gBACAH,MAAA,CAAA3F,GAAA,CAAAmG,IAAA,CAAAD,KAAA,CAAAJ,WAAA;gBACAH,MAAA,CAAA5F,kBAAA,CAAAoG,IAAA,CAAAD,KAAA;cACA;cACA;cACAP,MAAA,CAAA9B,KAAA,CAAAD,KAAA,CAAAwC,kBAAA,CAAAF,KAAA;YACA;UACA;QACA;MACA;IACA;IACAG,mBAAA,WAAAA,oBAAAC,IAAA;MACA,QAAAA,IAAA;QACA;UACA,KAAA9D,YAAA,CAAApB,cAAA,QAAAmF,SAAA,CAAAC,QAAA;UACA;QACA;UACA,KAAAhE,YAAA,CAAApB,cAAA,QAAAmF,SAAA,CAAAE,QAAA;UACA;MAEA;IACA;IACAC,YAAA,WAAAA,aAAA;MACA,KAAAlE,YAAA,CAAAmE,sBAAA,OAAAC,iBAAA,OAAApE,YAAA,CAAApB,cAAA,EAAA6D,GAAA,MAAAzC,YAAA,CAAAqE,oBAAA,EAAA5B,GAAA,MAAAzC,YAAA,CAAAX,kBAAA,EAAAoD,GAAA,MAAAzC,YAAA,CAAAV,gBAAA,EAAAmD,GAAA,MAAAzC,YAAA,CAAAT,mBAAA,EAAAkD,GAAA,MAAAzC,YAAA,CAAAsE,gBAAA,EAAA7B,GAAA,MAAAzC,YAAA,CAAAuE,iBAAA,EAAAC,KAAA;MACA,KAAAxE,YAAA,CAAAyE,oBAAA,OAAAL,iBAAA,OAAApE,YAAA,CAAAlB,mBAAA,EAAA0F,KAAA;MACA,KAAAxE,YAAA,CAAA0E,yBAAA,OAAAN,iBAAA,OAAApE,YAAA,CAAAmE,sBAAA,EAAAQ,QAAA,MAAA3E,YAAA,CAAAyE,oBAAA,EAAAD,KAAA;MACA,KAAAxE,YAAA,CAAA4E,eAAA,OAAAR,iBAAA,OAAApE,YAAA,CAAA6E,oBAAA,EAAApC,GAAA,MAAAzC,YAAA,CAAA8E,kBAAA,EAAArC,GAAA,MAAAzC,YAAA,CAAAnB,gBAAA,EAAA2F,KAAA;MACA,KAAAxE,YAAA,CAAA+E,oBAAA,OAAAX,iBAAA,OAAApE,YAAA,CAAAgF,gBAAA,EAAAvC,GAAA,MAAAzC,YAAA,CAAAZ,kBAAA,EAAAoF,KAAA;MACA,KAAAxE,YAAA,CAAAiF,mBAAA,OAAAb,iBAAA,OAAApE,YAAA,CAAAmE,sBAAA,EAAA1B,GAAA,MAAAzC,YAAA,CAAA+E,oBAAA,EAAAP,KAAA;MACA,KAAAxE,YAAA,CAAAkF,kBAAA,OAAAd,iBAAA,OAAApE,YAAA,CAAA4E,eAAA,EAAAnC,GAAA,MAAAzC,YAAA,CAAAT,mBAAA,EAAAkD,GAAA,MAAAzC,YAAA,CAAAmF,wBAAA,EAAAX,KAAA;MACA,KAAAxE,YAAA,CAAAoF,yBAAA,OAAAhB,iBAAA,OAAApE,YAAA,CAAAiF,mBAAA,EAAAN,QAAA,MAAA3E,YAAA,CAAAkF,kBAAA,EAAAV,KAAA;IACA;IACAa,oBAAA,WAAAA,qBAAA;MAAA,IAAAC,MAAA;MACA,IAAAC,oCAAA,OAAAvF,YAAA,EACAuD,IAAA,WAAAC,QAAA;QACA;QACA,IAAAvG,IAAA,GAAAuG,QAAA;;QAEA;QACA,IAAAgC,QAAA,GAAAF,MAAA,CAAAtF,YAAA,CAAA3B,UAAA,SAAAiH,MAAA,CAAAtF,YAAA,CAAAyF,QAAA,SAAAH,MAAA,CAAAtF,YAAA,CAAA5B,UAAA;;QAEA;QACA,IAAAsH,IAAA,OAAAC,IAAA,EAAA1I,IAAA;UACA6G,IAAA;QACA;;QAEA;QACA,IAAA8B,IAAA,GAAAC,QAAA,CAAAC,aAAA;QACA,IAAAC,GAAA,GAAAC,MAAA,CAAAC,GAAA,CAAAC,eAAA,CAAAR,IAAA;QACAE,IAAA,CAAAO,IAAA,GAAAJ,GAAA;QACAH,IAAA,CAAAQ,QAAA,GAAAZ,QAAA;;QAEA;QACAK,QAAA,CAAAQ,IAAA,CAAAC,WAAA,CAAAV,IAAA;QACAA,IAAA,CAAAW,KAAA;;QAEA;QACAV,QAAA,CAAAQ,IAAA,CAAAG,WAAA,CAAAZ,IAAA;QACAI,MAAA,CAAAC,GAAA,CAAAQ,eAAA,CAAAV,GAAA;MACA,GACAW,KAAA,WAAAC,KAAA;QACAC,OAAA,CAAAD,KAAA,YAAAA,KAAA;MACA;IACA;IACA;IACAE,kBAAA,WAAAA,mBAAApG,GAAA;MAAA,IAAAqG,MAAA;MACA,KAAAC,aAAA;MACA,IAAAC,kCAAA,EAAAvG,GAAA,CAAAwG,gBAAA,EAAA1D,IAAA,WAAAC,QAAA;QACAsD,MAAA,CAAA9G,YAAA,GAAAwD,QAAA,CAAAvG,IAAA;QACA6J,MAAA,CAAA9G,YAAA,CAAAkH,eAAA,GAAAJ,MAAA,CAAA9G,YAAA,CAAAkH,eAAA,GAAAJ,MAAA,CAAA9G,YAAA,CAAAkH,eAAA,CAAAC,GAAA,WAAApG,IAAA;UACA;UACA,IAAAA,IAAA,CAAAqG,kBAAA;YACA,IAAAC,WAAA,GAAAC,MAAA,CAAAvG,IAAA,CAAA3B,kBAAA;YACA,IAAAmI,UAAA,GAAAD,MAAA,CAAAvG,IAAA,CAAAwG,UAAA;YACA,IAAAC,UAAA,OAAApD,iBAAA,EAAAmD,UAAA,EAAA5C,QAAA,CAAA0C,WAAA,EAAA7C,KAAA;;YAEA;YACAzD,IAAA,CAAAsD,oBAAA,GAAAmD,UAAA,OAAAA,UAAA;UACA;YACAzG,IAAA,CAAAsD,oBAAA;UACA;UAEA,OAAAtD,IAAA;QACA;QACA+F,MAAA,CAAA7G,YAAA;MACA;IACA;IACA;AACA;AACA;AACA;IACAwH,eAAA,WAAAA,gBAAA3D,IAAA;MAAA,IAAA4D,MAAA;MACA,KAAAnK,kBAAA,CAAA4J,GAAA,WAAApG,IAAA;QACAA,IAAA,CAAA4G,mBAAA,GAAAL,MAAA,CAAAvG,IAAA,CAAA4G,mBAAA;MACA;MAEA,IAAAC,iCAAA,OAAA5H,YAAA,EAAAuD,IAAA,WAAAC,QAAA;QACA,IAAAA,QAAA,CAAAvG,IAAA;UACA,IAAAgK,gBAAA,GAAAzD,QAAA,CAAAvG,IAAA;;UAEA;UACA,IAAAA,IAAA,GAAAyK,MAAA,CAAAnK,kBAAA,CAAA4J,GAAA,WAAApG,IAAA;YACA,IAAAA,IAAA,CAAA8G,eAAA;cACAH,MAAA,CAAA9E,QAAA,CAAAC,OAAA;cACA;YACA;YACAiB,IAAA,SAAA/C,IAAA,CAAA8G,eAAA;YACA9G,IAAA,CAAAkG,gBAAA,GAAAA,gBAAA;YACAlG,IAAA,CAAA+G,kBAAA,GAAA/G,IAAA,CAAA+G,kBAAA,CAAAX,GAAA,WAAApG,IAAA;cACAA,IAAA,CAAAkG,gBAAA,GAAAA,gBAAA;cACAnD,IAAA,SAAA/C,IAAA,CAAA8G,eAAA;cACA,OAAA9G,IAAA;YACA;YACA,OAAAA,IAAA;UACA;UAEA,IAAA+C,IAAA;YACA,IAAAiE,+BAAA,EAAA9K,IAAA,EAAAsG,IAAA,WAAAC,QAAA;cACAkE,MAAA,CAAApH,OAAA;cACAoH,MAAA,CAAA9E,QAAA,CAAAoF,OAAA;cACAN,MAAA,CAAAzH,YAAA;YACA;UACA;YACA;YACA,IAAAgI,4BAAA,EAAAhL,IAAA,EAAAsG,IAAA,WAAAC,QAAA;cACAkE,MAAA,CAAApH,OAAA;cACAoH,MAAA,CAAA9E,QAAA,CAAAoF,OAAA;cACAN,MAAA,CAAAzH,YAAA;YACA;UACA;QACA;MACA;IACA;IACA;IACAiI,4BAAA,WAAAA,6BAAA;MAAA,IAAAC,MAAA;MACA,KAAA7K,OAAA;MACA,IAAAL,IAAA;MACAA,IAAA,CAAAmL,sBAAA,QAAApI,YAAA,CAAAqI,mBAAA;MACApL,IAAA,CAAAoB,UAAA,QAAA2B,YAAA,CAAA3B,UAAA;MACA,IAAA+E,wBAAA,EAAAnG,IAAA,EAAAsG,IAAA,WAAAC,QAAA;QACAoD,OAAA,CAAA0B,GAAA,CAAA9E,QAAA;QACA2E,MAAA,CAAAjI,wBAAA,GAAAsD,QAAA,CAAA9B,IAAA;QACAyG,MAAA,CAAAvK,KAAA,GAAA4F,QAAA,CAAA5F,KAAA;QACAuK,MAAA,CAAA7K,OAAA;MACA;IACA;IACA;AACA;AACA;AACA;IACAiL,cAAA,WAAAA,eAAAC,YAAA,EAAA1E,IAAA;MACA;MACA,IAAAA,IAAA;QACA,KAAAiD,aAAA;QACA,KAAA/G,YAAA,GAAAwI,YAAA;MACA;MACA,KAAA3I,YAAA,GAAAiE,IAAA;MACA,KAAA7D,YAAA;IACA;IACAwI,SAAA,EAAAA,eAAA;IACAC,wCAAA,WAAAA,yCAAAC,SAAA,EAAAlI,GAAA;MACAA,GAAA,CAAAqH,kBAAA,GAAAa,SAAA;MACA,KAAAzL,mBAAA,GAAAyL,SAAA;IACA;IACAC,YAAA,WAAAA,aAAAC,KAAA;MAAA,IAAAC,MAAA;MACA,IAAAC,OAAA,GAAAF,KAAA,CAAAE,OAAA;QAAA9L,IAAA,GAAA4L,KAAA,CAAA5L,IAAA;MACA,IAAA+L,IAAA;MACA,IAAAC,gBAAA,IACA,0EACA,kFACA,sFACA,+EACA;;MAEA;MACA,IAAAC,cAAA;MAEAH,OAAA,CAAAlH,OAAA,WAAAsH,MAAA,EAAAtI,KAAA;QACA,IAAAA,KAAA;UACAmI,IAAA,CAAAnI,KAAA;UACA;QACA;QAEA,IAAAuI,MAAA,GAAAnM,IAAA,CAAAkK,GAAA,WAAApG,IAAA;UAAA,OAAAuG,MAAA,CAAAvG,IAAA,CAAAoI,MAAA,CAAAE,QAAA;QAAA;QAEA,IAAAJ,gBAAA,CAAAvI,QAAA,CAAAyI,MAAA,CAAAE,QAAA,MAAAD,MAAA,CAAAE,KAAA,WAAA9E,KAAA;UAAA,OAAA+E,KAAA,CAAA/E,KAAA;QAAA;UACA,IAAAgF,QAAA,GAAAJ,MAAA,CAAAK,MAAA,WAAAC,IAAA,EAAAC,IAAA;YACA,IAAAnF,KAAA,GAAA8C,MAAA,CAAAqC,IAAA;YACA,KAAAJ,KAAA,CAAA/E,KAAA;cACA,WAAAJ,iBAAA,EAAAsF,IAAA,EAAAjH,GAAA,CAAAkH,IAAA,EAAAnF,KAAA;YACA;cACA,OAAAkF,IAAA;YACA;UACA;UACAV,IAAA,CAAAnI,KAAA,IAAA2I,QAAA;;UAEA;UACAN,cAAA,CAAAC,MAAA,CAAAE,QAAA,IAAAG,QAAA;QACA;UACAR,IAAA,CAAAnI,KAAA;QACA;MACA;;MAEA;MACA;MACA+I,MAAA,CAAAC,IAAA,CAAAX,cAAA,EAAArH,OAAA,WAAAiI,KAAA;QACA;QACA,IAAAhB,MAAA,CAAA9I,YAAA;UACA8I,MAAA,CAAA9I,YAAA,CAAA8J,KAAA,IAAAZ,cAAA,CAAAY,KAAA;QACA;MACA;;MAEA,OAAAd,IAAA;IACA;IAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;IACAe,6BAAA,WAAAA,8BAAApB,SAAA;MACA,KAAApL,kBAAA,GAAAoL,SAAA;IACA;IACAqB,cAAA,WAAAA,eAAAvJ,GAAA;MACA,KAAAT,YAAA,CAAApB,cAAA,GAAA6B,GAAA,CAAAwJ,OAAA;MACA,KAAAjK,YAAA,CAAAN,aAAA,GAAAe,GAAA,CAAAyJ,eAAA;MACA,KAAAlK,YAAA,CAAAP,WAAA,GAAAgB,GAAA,CAAAhB,WAAA;MACA,KAAAO,YAAA,CAAA1B,UAAA,GAAAmC,GAAA,CAAAnC,UAAA;MACA;MACA,KAAA6L,YAAA;IACA;IACA,eACA7J,OAAA,WAAAA,QAAA;MAAA,IAAA8J,MAAA;MACA,KAAA9M,OAAA;MACA,IAAA+M,mCAAA,OAAArM,WAAA,EAAAuF,IAAA,WAAAC,QAAA;QACA4G,MAAA,CAAAvM,kBAAA,GAAA2F,QAAA,CAAA9B,IAAA;QACA0I,MAAA,CAAAxM,KAAA,GAAA4F,QAAA,CAAA5F,KAAA;QACAwM,MAAA,CAAA9M,OAAA;MACA;IACA;IACA;IACAgN,MAAA,WAAAA,OAAA;MACA,KAAAvM,IAAA;MACA,KAAAwM,KAAA;IACA;IACAxD,aAAA,WAAAA,cAAA;MACA,KAAA/G,YAAA;QACAiH,gBAAA;QACA7I,UAAA;QACAC,UAAA;QACAC,UAAA;QACAC,UAAA;QACAC,aAAA;QACAC,WAAA;QACAC,MAAA;QACAC,YAAA;QACAC,cAAA;QACAC,gBAAA;QACAC,mBAAA;QACAC,eAAA;QACAC,UAAA;QACAC,gBAAA;QACAC,WAAA;QACAC,SAAA;QACAC,kBAAA;QACAC,kBAAA;QACAC,gBAAA;QACAC,mBAAA;QACAC,gBAAA;QACAC,WAAA;QACAC,aAAA;QACAC,gBAAA;MACA;MACA,KAAAO,wBAAA;MACA,KAAAsK,SAAA;IACA;IACA;IACAD,KAAA,WAAAA,MAAA;MACA,KAAA3K,IAAA;QACAqH,gBAAA;QACA7I,UAAA;QACAC,UAAA;QACAC,UAAA;QACAC,UAAA;QACAC,aAAA;QACAC,WAAA;QACAC,MAAA;QACAC,YAAA;QACAC,cAAA;QACAC,gBAAA;QACAC,mBAAA;QACAC,eAAA;QACAC,UAAA;QACAC,gBAAA;QACAC,WAAA;QACAC,SAAA;QACAC,kBAAA;QACAC,kBAAA;QACAC,gBAAA;QACAC,mBAAA;QACAC,gBAAA;QACAC,WAAA;QACAC,aAAA;QACAC,gBAAA;MACA;MACA,KAAA6K,SAAA;IACA;IACA,aACAC,WAAA,WAAAA,YAAA;MACA,KAAAzM,WAAA,CAAAC,OAAA;MACA,KAAAqC,OAAA;IACA;IACA,aACAoK,UAAA,WAAAA,WAAA;MACA,KAAAF,SAAA;MACA,KAAAC,WAAA;IACA;IACAE,kBAAA,WAAAA,mBAAAlK,GAAA;MAAA,IAAAmK,MAAA;MACA,IAAAC,IAAA,GAAApK,GAAA,CAAAqK,MAAA;MACA,KAAAC,MAAA,CAAAC,OAAA,WAAAH,IAAA,SAAAtH,IAAA;QACA,WAAA0H,4BAAA,EAAAxK,GAAA,CAAAwG,gBAAA,EAAAxG,GAAA,CAAAqK,MAAA;MACA,GAAAvH,IAAA;QACAqH,MAAA,CAAAG,MAAA,CAAAG,UAAA,CAAAL,IAAA;MACA,GAAAnE,KAAA;QACAjG,GAAA,CAAAqK,MAAA,GAAArK,GAAA,CAAAqK,MAAA;MACA;IACA;IACA;IACAK,qBAAA,WAAAA,sBAAAxC,SAAA;MACA,KAAAnL,GAAA,GAAAmL,SAAA,CAAAxB,GAAA,WAAApG,IAAA;QAAA,OAAAA,IAAA,CAAAkG,gBAAA;MAAA;MACA,KAAAxJ,MAAA,GAAAkL,SAAA,CAAAyC,MAAA;MACA,KAAA1N,QAAA,IAAAiL,SAAA,CAAAyC,MAAA;IACA;IACA,aACAC,SAAA,WAAAA,UAAA;MACA,KAAAtE,aAAA;MACA,KAAAhJ,IAAA;MACA,KAAAD,KAAA;IACA;IACA,aACAwN,YAAA,WAAAA,aAAA7K,GAAA;MAAA,IAAA8K,OAAA;MACA,KAAAxE,aAAA;MACA,IAAAE,gBAAA,GAAAxG,GAAA,CAAAwG,gBAAA,SAAAzJ,GAAA;MACA,IAAAgO,iCAAA,EAAAvE,gBAAA,EAAA1D,IAAA,WAAAC,QAAA;QACA+H,OAAA,CAAA3L,IAAA,GAAA4D,QAAA,CAAAvG,IAAA;QACAsO,OAAA,CAAAxN,IAAA;QACAwN,OAAA,CAAAzN,KAAA;MACA;IACA;IACA,WACA2N,UAAA,WAAAA,WAAA;MAAA,IAAAC,OAAA;MACA,KAAArK,KAAA,iBAAAsK,QAAA,WAAAC,KAAA;QACA,IAAAA,KAAA;UACA,IAAAF,OAAA,CAAA1L,YAAA,CAAAiH,gBAAA;YACA,IAAA4E,oCAAA,EAAAH,OAAA,CAAA1L,YAAA,EAAAuD,IAAA,WAAAC,QAAA;cACAkI,OAAA,CAAAX,MAAA,CAAAG,UAAA;cACAQ,OAAA,CAAA3N,IAAA;cACA2N,OAAA,CAAApL,OAAA;YACA;UACA;QACA;MACA;IACA;IACA,aACAwL,YAAA,WAAAA,aAAArL,GAAA;MAAA,IAAAsL,OAAA;MACA,IAAAC,iBAAA,GAAAvL,GAAA,CAAAwG,gBAAA,SAAAzJ,GAAA;MACA,KAAAuN,MAAA,CAAAC,OAAA,qBAAAgB,iBAAA,cAAAzI,IAAA;QACA,WAAA0I,iCAAA,EAAAD,iBAAA;MACA,GAAAzI,IAAA;QACAwI,OAAA,CAAAzL,OAAA;QACAyL,OAAA,CAAAhB,MAAA,CAAAG,UAAA;MACA,GAAAxE,KAAA,cACA;IACA;IACA,aACAwF,YAAA,WAAAA,aAAA;MACA,KAAA9F,QAAA,qCAAA+F,cAAA,CAAAC,OAAA,MACA,KAAApO,WAAA,qBAAAqO,MAAA,CACA,IAAAC,IAAA,GAAAC,OAAA;IACA;IACA;IACAC,eAAA,WAAAA,gBAAA/L,GAAA,EAAAqJ,KAAA,EAAAtF,KAAA;MACA;MACAA,KAAA,GAAA8C,MAAA,CAAA9C,KAAA;;MAEA;MACA,KAAAiI,IAAA,CAAAhM,GAAA,EAAAqJ,KAAA,EAAAtF,KAAA;;MAEA;MACA,IAAAsF,KAAA,6BAAArJ,GAAA,CAAA2G,kBAAA;QACA,IAAAG,UAAA,GAAAD,MAAA,CAAA7G,GAAA,CAAA8G,UAAA;QACA,IAAAC,UAAA,OAAApD,iBAAA,EAAAmD,UAAA,EAAA5C,QAAA,CAAAH,KAAA,EAAAA,KAAA;QACA,KAAAiI,IAAA,CAAAhM,GAAA,0BAAA+G,UAAA,OAAAA,UAAA;MACA;MAEA,IAAAsC,KAAA;QACA,IAAAtC,WAAA,OAAApD,iBAAA,EAAAI,KAAA,EAAAG,QAAA,CAAAlE,GAAA,CAAArB,kBAAA,EAAAoF,KAAA;QACA,KAAAiI,IAAA,CAAAhM,GAAA,0BAAA+G,WAAA,OAAAA,WAAA;MACA;;MAEA;MACA,KAAA2C,YAAA;;MAEA;MACA,IAAAtJ,KAAA,QAAAb,YAAA,CAAAkH,eAAA,CAAApG,SAAA,WAAAC,IAAA;QAAA,OAAAA,IAAA,KAAAN,GAAA;MAAA;MACA,IAAAI,KAAA;QACA,KAAA4L,IAAA,MAAAzM,YAAA,CAAAkH,eAAA,EAAArG,KAAA,MAAAsL,cAAA,CAAAC,OAAA,MAAA3L,GAAA;MACA;;MAEA;MACA,KAAAiM,eAAA;IACA;IACA;IACAA,eAAA,WAAAA,gBAAA;MAAA,IAAAC,OAAA;MACA;MACA,KAAAxC,YAAA;MACA;MACA,KAAA7I,SAAA;QACA;QACA,IAAAqL,OAAA,CAAA3M,YAAA,CAAAkH,eAAA,IAAAyF,OAAA,CAAA3M,YAAA,CAAAkH,eAAA,CAAAkE,MAAA;UACAuB,OAAA,CAAA/D,YAAA;YACAG,OAAA,EAAA4D,OAAA,CAAAtL,KAAA,CAAAuL,sBAAA,GAAAD,OAAA,CAAAtL,KAAA,CAAAuL,sBAAA,CAAA7D,OAAA;YACA9L,IAAA,EAAA0P,OAAA,CAAA3M,YAAA,CAAAkH;UACA;QACA;QACAyF,OAAA,CAAAzI,YAAA;MACA;IACA;EACA;AACA;AAAA2I,OAAA,CAAAT,OAAA,GAAAU,QAAA"}]}