(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d20fe3b"],{b60a:function(e,t,n){"use strict";n.r(t);var l=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("div",{staticClass:"app-container"},[n("el-row",{attrs:{gutter:20}},[n("el-col",{attrs:{span:e.showLeft}},[n("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],ref:"queryForm",staticClass:"query",attrs:{inline:!0,model:e.queryParams,size:"mini"}},[n("el-form-item",{attrs:{label:"搜索",prop:"lineQuery"}},[n("el-input",{staticStyle:{width:"158px"},attrs:{clearable:"",placeholder:"中英文简称"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.lineQuery,callback:function(t){e.$set(e.queryParams,"lineQuery",t)},expression:"queryParams.lineQuery"}})],1),n("el-form-item"),n("el-form-item",[n("el-button",{attrs:{icon:"el-icon-search",size:"mini",type:"primary"},on:{click:e.handleQuery}},[e._v("搜索")]),n("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1)],1),n("el-col",{attrs:{span:e.showRight}},[n("el-row",{staticClass:"mb8",attrs:{gutter:10}},[n("el-col",{attrs:{span:1.5}},[n("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:line:export"],expression:"['system:line:export']"}],attrs:{icon:"el-icon-download",plain:"",size:"mini",type:"warning"},on:{click:e.handleExport}},[e._v("导出 ")])],1),n("el-col",{attrs:{span:1.5}},[n("el-button",{attrs:{icon:"el-icon-sort",plain:"",size:"mini",type:"info"},on:{click:e.toggleExpandAll}},[e._v("展开/折叠 ")])],1),n("right-toolbar",{attrs:{showSearch:e.showSearch},on:{"update:showSearch":function(t){e.showSearch=t},"update:show-search":function(t){e.showSearch=t},queryTable:e.getList}})],1),e.refreshTable?n("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.lineList,"default-expand-all":e.isExpandAll,"tree-props":{children:"children",hasChildren:"hasChildren"},"row-key":"lineId"}},[n("el-table-column",{attrs:{label:"航线名称",prop:"name"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(t.row.lineShortName)+" "),n("a",{staticStyle:{margin:"0","font-weight":"bold","font-size":"small"}},[e._v(e._s(t.row.lineLocalName))]),e._v(" "+e._s(t.row.lineEnName)+" ")]}}],null,!1,1257221572)}),n("el-table-column",{attrs:{align:"left",label:"备注",prop:"remark",width:"300"}}),n("el-table-column",{attrs:{label:"排序",align:"center",prop:"orderNum"}}),n("el-table-column",{key:"status",attrs:{align:"center",label:"状态",width:"65"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("el-switch",{attrs:{"active-value":"0","inactive-value":"1"},on:{change:function(n){return e.handleStatusChange(t.row)}},model:{value:t.row.status,callback:function(n){e.$set(t.row,"status",n)},expression:"scope.row.status"}})]}}],null,!1,3955094654)}),n("el-table-column",{attrs:{align:"center","class-name":"small-padding fixed-width",label:"操作",width:"170"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:line:add"],expression:"['system:line:add']"}],staticStyle:{"margin-right":"-8px"},attrs:{icon:"el-icon-plus",size:"mini",type:"primary"},on:{click:function(n){return e.handleAdd(t.row)}}},[e._v("新增 ")]),n("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:line:edit"],expression:"['system:line:edit']"}],staticStyle:{"margin-right":"-8px"},attrs:{icon:"el-icon-edit",size:"mini",type:"success"},on:{click:function(n){return e.handleUpdate(t.row)}}},[e._v("修改 ")]),0!=t.row.parentId?n("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:line:remove"],expression:"['system:line:remove']"}],staticStyle:{"margin-right":"-8px"},attrs:{icon:"el-icon-delete",size:"mini",type:"danger"},on:{click:function(n){return e.handleDelete(t.row)}}},[e._v("删除 ")]):e._e()]}}],null,!1,1603856582)})],1):e._e()],1)],1),n("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"},{name:"dialogDragWidth",rawName:"v-dialogDragWidth"}],attrs:{"close-on-click-modal":!1,"modal-append-to-body":!1,title:e.title,visible:e.open,"append-to-body":"",width:"500px"},on:{"update:visible":function(t){e.open=t}}},[n("el-form",{ref:"form",staticClass:"edit",attrs:{model:e.form,rules:e.rules,"label-width":"80px"}},[0!=e.form.parentId?n("el-form-item",{attrs:{label:"上级部门",prop:"parentId"}},[n("tree-select",{attrs:{multiple:!1,pass:e.form.parentId,placeholder:"上级类名",type:"line"},on:{return:e.ParentId}})],1):e._e(),n("el-form-item",{attrs:{label:"航线简称",prop:"lineShortName"}},[n("el-input",{attrs:{placeholder:"航线简称"},model:{value:e.form.lineShortName,callback:function(t){e.$set(e.form,"lineShortName",t)},expression:"form.lineShortName"}})],1),n("el-form-item",{attrs:{label:"航线中文名",prop:"lineLocalName"}},[n("el-input",{attrs:{placeholder:"航线中文名"},model:{value:e.form.lineLocalName,callback:function(t){e.$set(e.form,"lineLocalName",t)},expression:"form.lineLocalName"}})],1),n("el-form-item",{attrs:{label:"航线英文名",prop:"lineEnName"}},[n("el-input",{attrs:{placeholder:"航线英文名"},model:{value:e.form.lineEnName,callback:function(t){e.$set(e.form,"lineEnName",t)},expression:"form.lineEnName"}})],1),n("el-form-item",{attrs:{label:"排序",prop:"orderNum"}},[n("el-input-number",{staticStyle:{width:"100%"},attrs:{controls:!1,placeholder:"排序",min:0},model:{value:e.form.orderNum,callback:function(t){e.$set(e.form,"orderNum",t)},expression:"form.orderNum"}})],1),n("el-form-item",{attrs:{label:"备注",prop:"remark"}},[n("el-input",{attrs:{placeholder:"备注"},model:{value:e.form.remark,callback:function(t){e.$set(e.form,"remark",t)},expression:"form.remark"}})],1)],1),n("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[n("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("确 定")]),n("el-button",{on:{click:e.cancel}},[e._v("取 消")])],1)],1)],1)},a=[],i=n("5530"),r=n("e673"),s={name:"CourseLine",data:function(){return{showLeft:3,showRight:21,loading:!0,ids:[],single:!0,multiple:!0,showSearch:!1,lineList:[],title:"",open:!1,refreshTable:!0,isExpandAll:!1,queryParams:{parentId:null,lineShortName:null,lineLocalName:null,lineEnName:null},form:{},rules:{}}},watch:{showSearch:function(e){1==e?(this.showRight=21,this.showLeft=3):(this.showRight=24,this.showLeft=0)}},created:function(){this.getList()},methods:{getList:function(){var e=this;this.loading=!0,Object(r["e"])(this.queryParams).then((function(t){e.lineList=e.handleTree(t.data,"lineId"),e.loading=!1}))},toggleExpandAll:function(){var e=this;this.refreshTable=!1,this.isExpandAll=!this.isExpandAll,this.$nextTick((function(){e.refreshTable=!0}))},cancel:function(){this.open=!1,this.reset()},reset:function(){this.form={lineId:null,parentId:null,ancestors:null,lineShortName:null,lineLocalName:null,lineEnName:null,remark:null,createBy:null,createTime:null,updateBy:null,updateTime:null,deleteBy:null,deleteTime:null,deleteStatus:0},this.resetForm("form")},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.resetForm("queryForm"),this.handleQuery()},handleAdd:function(e){this.reset(),this.open=!0,void 0!=e&&(this.form.parentId=e.lineId),this.title="添加航线"},handleStatusChange:function(e){var t=this,n="0"==e.status?"启用":"停用";this.$confirm("确认要"+n+"吗？","提示",{customClass:"modal-confirm"}).then((function(){return Object(r["b"])(e.lineId,e.status)})).then((function(){t.$modal.msgSuccess(n+"成功")})).catch((function(){e.status="0"==e.status?"1":"0"}))},handleUpdate:function(e){var t=this;this.reset();var n=e.lineId||this.ids;Object(r["d"])(n).then((function(e){t.form=e.data,t.open=!0,t.title="修改航线"}))},submitForm:function(){var e=this;this.$refs["form"].validate((function(t){t&&(null!=e.form.lineId?Object(r["g"])(e.form).then((function(t){e.$modal.msgSuccess("修改成功"),e.open=!1,e.getList()})):Object(r["a"])(e.form).then((function(t){e.$modal.msgSuccess("新增成功"),e.open=!1,e.getList()})))}))},handleDelete:function(e){var t=this,n=e.lineId||this.ids;this.$confirm('是否确认删除航线编号为"'+n+'"的数据项？',"提示",{customClass:"modal-confirm"}).then((function(){return Object(r["c"])(n)})).then((function(){t.getList(),t.$modal.msgSuccess("删除成功")})).catch((function(){}))},handleExport:function(){this.download("system/line/export",Object(i["a"])({},this.queryParams),"line_".concat((new Date).getTime(),".xlsx"))},ParentId:function(e){this.form.parentId=e}}},o=s,c=n("2877"),m=Object(c["a"])(o,l,a,!1,null,null,null);t["default"]=m.exports}}]);