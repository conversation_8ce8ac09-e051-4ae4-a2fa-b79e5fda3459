(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d0bce33"],{"2a3a":function(e,i,a){"use strict";a.r(i);var s=function(){var e=this,i=e.$createElement,a=e._self._c||i;return a("div",[a("h6",{staticStyle:{margin:"0"}},[e._v(e._s(e.parseTime(e.scope.row.validFrom,"{m}.{d}"))+"-"+e._s(e.parseTime(e.scope.row.validTo,"{m}.{d}")))]),a("h6",{staticStyle:{margin:"0"}},[e._v(e._s(e.scope.row.validPeriodTimeNode))])])},t=[],o={name:"validTime",props:["scope"],data:function(){return{size:this.$store.state.app.size||"mini"}}},r=o,n=a("2877"),c=Object(n["a"])(r,s,t,!1,null,"25778fad",null);i["default"]=c.exports}}]);