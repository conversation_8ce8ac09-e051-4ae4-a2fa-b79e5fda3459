(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2e1863d5"],{"8f75":function(t,e,a){"use strict";a.r(e);var l=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",[a("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"},{name:"dialogDragWidth",rawName:"v-dialogDragWidth"}],staticClass:"staffInfo",attrs:{"close-on-click-modal":!1,"modal-append-to-body":!1,"append-to-body":!0,visible:t.openContent,title:"员工信息",width:"1800px"},on:{"update:visible":function(e){t.openContent=e},close:t.handleClose}},[a("el-row",{staticClass:"mb8",attrs:{gutter:10}},[a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:extstaff:add"],expression:"['system:extstaff:add']"}],attrs:{size:t.size,icon:"el-icon-plus",plain:"",type:"primary"},on:{click:t.handleAdd}},[t._v("新增 ")])],1),a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:extstaff:edit"],expression:"['system:extstaff:edit']"}],attrs:{disabled:t.single,size:t.size,icon:"el-icon-edit",plain:"",type:"success"},on:{click:t.handleUpdate}},[t._v("修改 ")])],1),a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:extstaff:remove"],expression:"['system:extstaff:remove']"}],attrs:{disabled:t.multiple,size:t.size,icon:"el-icon-delete",plain:"",type:"danger"},on:{click:t.handleDelete}},[t._v("删除 ")])],1)],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],attrs:{data:t.extStaffList,border:"",stripe:""}},[a("el-table-column",{key:"staffShortName",attrs:{align:"left",label:"员工",prop:"staffShortName","show-tooltip-when-overflow":"",width:"180"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-row",[a("el-col",{attrs:{span:5}},["Y"==e.row.isMain?a("el-tag",{attrs:{size:t.size,type:"primary"}},[t._v("主")]):t._e()],1),a("el-col",{attrs:{span:19}},[a("h6",{staticStyle:{margin:"5px",overflow:"hidden","text-overflow":"ellipsis","white-space":"nowrap"}},[t._v(" "+t._s(null!=t.company.companyShortName?t.company.companyShortName:"")+" "+t._s(null!=e.row.staffLocalName?e.row.staffLocalName:"")+" "+t._s(null!=e.row.staffEnName?e.row.staffEnName:"")+" "+t._s(null!=e.row.staffShortName?e.row.staffShortName:"")+" "+t._s(null!=e.row.deptName?e.row.deptName:"")+" "+t._s(null!=e.row.deptPositionName?e.row.deptPositionName:"")+" ")])])],1)]}}])}),a("el-table-column",{attrs:{align:"center",label:"职位","show-tooltip-when-overflow":"",width:"60px"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("h6",{staticStyle:{margin:"5px",overflow:"hidden","text-overflow":"ellipsis","white-space":"nowrap"}},[t._v(" "+t._s(null!=e.row.deptName?e.row.deptName+"/":"")+t._s(e.row.deptPositionName)+" ")])]}}])}),a("el-table-column",{attrs:{align:"center",label:"微信",prop:"staffWechat","show-tooltip-when-overflow":"",width:"100px"}}),a("el-table-column",{attrs:{align:"center",label:"QQ",prop:"staffQq","show-tooltip-when-overflow":"",width:"80px"}}),a("el-table-column",{attrs:{align:"center",label:"WhatsApp",prop:"staffWhatsapp","show-tooltip-when-overflow":"",width:"80px"}}),a("el-table-column",{attrs:{align:"center",label:"其他联系人方式",prop:"staffOtherContact","show-tooltip-when-overflow":"",width:"80px"}}),a("el-table-column",{key:"staffTelNum",attrs:{align:"center",label:"个人邮箱",prop:"staffTelNum","show-tooltip-when-overflow":"",width:"150px"}}),a("el-table-column",{key:"staffEmailEnterprise",attrs:{align:"center",label:"企业邮箱",prop:"staffEmailEnterprise","show-tooltip-when-overflow":"",width:"150px"}}),a("el-table-column",{key:"staffGender",attrs:{align:"center",label:"性别",prop:"staffGender",width:"38px"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("dict-tag",{attrs:{options:t.dict.type.sys_user_sex,value:e.row.staffGender}})]}}])}),a("el-table-column",{key:"staffPhoneNum",attrs:{align:"center",label:"电话号码",prop:"staffPhoneNum","show-tooltip-when-overflow":"",width:"100"}}),a("el-table-column",{key:"staffAddress",attrs:{align:"center",label:"详细住址",prop:"staffAddress","show-tooltip-when-overflow":"",width:"100px"}}),a("el-table-column",{key:"staffBirthday",attrs:{align:"center",label:"生日",prop:"staffBirthday",width:"78px"}}),a("el-table-column",{key:"staffLanguage",attrs:{align:"center",label:"母语",prop:"staffLanguage",width:"48px"}}),a("el-table-column",{key:"staffCountry",attrs:{align:"center",label:"国籍",prop:"staffCountry",width:"48px"}}),a("el-table-column",{key:"staffNation",attrs:{align:"center",label:"民族",prop:"staffNation",width:"48px"}}),a("el-table-column",{key:"staffReligion",attrs:{align:"center",label:"宗教",prop:"staffReligion",width:"48px"}}),a("el-table-column",{key:"remark",attrs:{align:"center",label:"员工评价",prop:"remark"}}),a("el-table-column",{key:"staffJobStatus",attrs:{align:"center",label:"状态",width:"58px"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-switch",{attrs:{"active-value":"0","inactive-value":"1"},on:{change:function(a){return t.handleStatusChange(e.row)}},model:{value:e.row.staffJobStatus,callback:function(a){t.$set(e.row,"staffJobStatus",a)},expression:"scope.row.staffJobStatus"}})]}}])}),a("el-table-column",{attrs:{align:"center","class-name":"small-padding fixed-width",label:"操作",width:"100px"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:extstaff:edit"],expression:"['system:extstaff:edit']"}],staticStyle:{margin:"2px"},attrs:{size:t.size,icon:"el-icon-edit",type:"success"},on:{click:function(a){return t.handleUpdate(e.row)}}},[t._v("修改 ")]),a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:extstaff:remove"],expression:"['system:extstaff:remove']"}],staticStyle:{margin:"2px"},attrs:{size:t.size,icon:"el-icon-delete",type:"danger"},on:{click:function(a){return t.handleDelete(e.row)}}},[t._v("删除 ")])]}}])})],1)],1),a("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"},{name:"dialogDragWidth",rawName:"v-dialogDragWidth"}],attrs:{"close-on-click-modal":!1,"append-to-body":!0,"modal-append-to-body":!1,title:t.title,visible:t.oopen,width:"800px"},on:{"update:visible":function(e){t.oopen=e}}},[a("el-form",{ref:"form",attrs:{model:t.form,rules:t.rules,"label-width":"88px"}},[a("el-form-item",{attrs:{label:"所属公司："}},[a("h4",{staticStyle:{margin:"0","font-weight":"bold"}},[t._v(t._s(t.company.companyTaxCode)+" "),a("span",{staticStyle:{"font-size":"large"}},[t._v(t._s(t.company.companyShortName))])]),t._v(" "+t._s(t.company.companyLocalName)+" ")]),a("el-form-item",{attrs:{label:"姓名称谓："}},[a("el-row",[a("el-col",{attrs:{span:8}},[a("el-input",{attrs:{placeholder:"正式称谓"},model:{value:t.form.staffShortName,callback:function(e){t.$set(t.form,"staffShortName",e)},expression:"form.staffShortName"}})],1),a("el-col",{attrs:{span:8}},[a("el-input",{attrs:{placeholder:"中文姓名"},model:{value:t.form.staffLocalName,callback:function(e){t.$set(t.form,"staffLocalName",e)},expression:"form.staffLocalName"}})],1),a("el-col",{attrs:{span:8}},[a("el-input",{attrs:{placeholder:"英文姓名"},model:{value:t.form.staffEnName,callback:function(e){t.$set(t.form,"staffEnName",e)},expression:"form.staffEnName"}})],1)],1)],1),a("el-form-item",{attrs:{label:"部门职位："}},[a("el-row",[a("el-col",{attrs:{span:8}},[a("el-input",{attrs:{placeholder:"部门名称，手动输入"},model:{value:t.form.deptName,callback:function(e){t.$set(t.form,"deptName",e)},expression:"form.deptName"}})],1),a("el-col",{attrs:{span:8}},[a("el-input",{attrs:{placeholder:"职位名称，手动输入"},model:{value:t.form.deptPositionName,callback:function(e){t.$set(t.form,"deptPositionName",e)},expression:"form.deptPositionName"}})],1),a("el-col",{attrs:{span:8}},[a("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"是否主要联系人"},model:{value:t.form.isMain,callback:function(e){t.$set(t.form,"isMain",e)},expression:"form.isMain"}},t._l(t.dict.type.sys_yes_no,(function(t){return a("el-option",{key:t.value,attrs:{label:t.label,value:t.value}})})),1)],1)],1)],1),a("el-form-item",{attrs:{label:"联系方式："}},[a("el-row",[a("el-col",{attrs:{span:8}},[a("el-input",{attrs:{placeholder:"手机号码"},model:{value:t.form.staffPhoneNum,callback:function(e){t.$set(t.form,"staffPhoneNum",e)},expression:"form.staffPhoneNum"}})],1),a("el-col",{attrs:{span:8}},[a("el-input",{attrs:{placeholder:"微信"},model:{value:t.form.staffWechat,callback:function(e){t.$set(t.form,"staffWechat",e)},expression:"form.staffWechat"}})],1),a("el-col",{attrs:{span:8}},[a("el-input",{attrs:{placeholder:"QQ"},model:{value:t.form.staffQq,callback:function(e){t.$set(t.form,"staffQq",e)},expression:"form.staffQq"}})],1),a("el-col",{attrs:{span:8}},[a("el-input",{attrs:{placeholder:"Whatsapp"},model:{value:t.form.staffWhatsapp,callback:function(e){t.$set(t.form,"staffWhatsapp",e)},expression:"form.staffWhatsapp"}})],1),a("el-col",{attrs:{span:8}},[a("el-input",{attrs:{placeholder:"其他联系方式"},model:{value:t.form.staffOtherContact,callback:function(e){t.$set(t.form,"staffOtherContact",e)},expression:"form.staffOtherContact"}})],1),a("el-col",{attrs:{span:8}},[a("el-input",{attrs:{placeholder:"固定电话"},model:{value:t.form.staffTelNum,callback:function(e){t.$set(t.form,"staffTelNum",e)},expression:"form.staffTelNum"}})],1),a("el-col",{attrs:{span:16}},[a("el-input",{attrs:{placeholder:"企业邮箱"},model:{value:t.form.staffEmailEnterprise,callback:function(e){t.$set(t.form,"staffEmailEnterprise",e)},expression:"form.staffEmailEnterprise"}})],1)],1)],1),a("el-form-item",{attrs:{label:"个人信息："}},[a("el-row",[a("el-col",{attrs:{span:8}},[a("el-select",{staticStyle:{width:"100%"},model:{value:t.form.staffGender,callback:function(e){t.$set(t.form,"staffGender",e)},expression:"form.staffGender"}},[a("el-option",{attrs:{label:"0"}},[t._v("男")]),a("el-option",{attrs:{label:"1"}},[t._v("女")])],1)],1),a("el-col",{attrs:{span:8}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{clearable:"",placeholder:"生日",type:"date","value-format":"yyyy-MM-dd"},model:{value:t.form.staffBirthday,callback:function(e){t.$set(t.form,"staffBirthday",e)},expression:"form.staffBirthday"}})],1),a("el-col",{attrs:{span:8}},[a("el-input",{attrs:{placeholder:"擅长语种"},model:{value:t.form.staffLanguage,callback:function(e){t.$set(t.form,"staffLanguage",e)},expression:"form.staffLanguage"}})],1),a("el-col",{attrs:{span:8}},[a("el-input",{attrs:{placeholder:"国籍"},model:{value:t.form.staffCountry,callback:function(e){t.$set(t.form,"staffCountry",e)},expression:"form.staffCountry"}})],1),a("el-col",{attrs:{span:8}},[a("el-input",{attrs:{placeholder:"民族"},model:{value:t.form.staffNation,callback:function(e){t.$set(t.form,"staffNation",e)},expression:"form.staffNation"}})],1),a("el-col",{attrs:{span:8}},[a("el-input",{attrs:{placeholder:"宗教信仰"},model:{value:t.form.staffReligion,callback:function(e){t.$set(t.form,"staffReligion",e)},expression:"form.staffReligion"}})],1),a("el-col",{attrs:{span:8}},[a("location-select",{ref:"location",attrs:{pass:t.form.locationId,type:"location"},on:{return:t.getLocationId}})],1),a("el-col",{attrs:{span:16}},[a("el-input",{attrs:{placeholder:"详细住址"},model:{value:t.form.staffAddress,callback:function(e){t.$set(t.form,"staffAddress",e)},expression:"form.staffAddress"}})],1)],1)],1),a("el-form-item",{attrs:{label:"备注信息：",prop:"remark"}},[a("el-input",{attrs:{maxlength:"200",placeholder:"内容","show-word-limit":"",type:"textarea"},model:{value:t.form.remark,callback:function(e){t.$set(t.form,"remark",e)},expression:"form.remark"}})],1),a("el-form-item",{attrs:{label:"员工状态：",prop:"staffJobStatus"}},[a("el-select",{model:{value:t.form.staffJobStatus,callback:function(e){t.$set(t.form,"staffJobStatus",e)},expression:"form.staffJobStatus"}},t._l(t.dict.type.sys_normal_disable,(function(t){return a("el-option",{key:t.value,attrs:{label:t.label,value:t.value}})})),1)],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:t.submitForm}},[t._v("确 定")]),a("el-button",{on:{click:t.cancel}},[t._v("取 消")])],1)],1)],1)},o=[],s=a("b857"),n={name:"staffInfo",dicts:["sys_user_sex","sys_normal_disable","sys_yes_no"],props:["loadOptions","open","company"],data:function(){return{size:this.$store.state.app.size||"mini",single:!0,multiple:!0,openContent:!1,loading:!0,oopen:!1,title:null,extStaffList:[],form:{},queryParams:{sqdCompanyId:null,staffShortName:null,staffLocalName:null,staffEnName:null,staffTelNum:null,staffEmailEnterprise:null,staffPhoneNum:null,staffBirthday:null,staffGender:null,staffWechat:null,staffQq:null,staffCountry:null,staffNation:null,staffReligion:null,locationId:null,staffAddress:null,staffLanguage:null,deptName:null,deptPositionName:null,staffJobStatus:null},rules:{}}},watch:{loadOptions:function(){this.extStaffList=this.loadOptions,this.loading=!1},open:function(t){this.openContent=t},openContent:function(t){0==t&&this.$emit("openStaffs")}},methods:{getList:function(){var t=this;this.loading=!0,this.queryParams.sqdCompanyId=this.company.companyId,Object(s["e"])(this.queryParams).then((function(e){t.extStaffList=e.data,t.loading=!1}))},cancel:function(){this.oopen=!1,this.reset()},reset:function(){this.form={staffId:null,sqdCompanyId:null,staffShortName:null,staffLocalName:null,staffEnName:null,staffTelNum:null,staffEmailEnterprise:null,credentialTypeId:null,staffPhoneNum:null,staffBirthday:null,staffGender:null,locationId:null,staffAddress:null,staffLanguage:null,deptName:null,deptPositionName:null,staffJobStatus:0,isMain:"N"},this.resetForm("form")},handleAdd:function(){this.reset(),this.oopen=!0,this.title="添加外部员工"},handleUpdate:function(t){var e=this;this.reset();var a=t.staffId||this.ids;Object(s["d"])(a).then((function(a){e.form=a.data,e.oopen=!0,e.title="修改外部员工",e.$refs.location.remoteMethod(t.locationLocalName)}))},submitForm:function(){var t=this;this.$refs["form"].validate((function(e){e&&(t.form.sqdCompanyId=t.company.companyId,null!=t.form.staffId?Object(s["f"])(t.form).then((function(e){t.$modal.msgSuccess("修改成功"),t.oopen=!1,t.getList()})):Object(s["a"])(t.form).then((function(e){t.$modal.msgSuccess("新增成功"),t.oopen=!1,t.getList()})))}))},handleDelete:function(t){var e=this,a=t.staffId||this.ids;this.$confirm('是否确认删除外部员工编号为"'+a+'"的数据项？',"提示",{customClass:"modal-confirm"}).then((function(){return Object(s["c"])(a)})).then((function(){e.getList(),e.$modal.msgSuccess("删除成功")})).catch((function(){}))},handleStatusChange:function(t){var e=this,a="0"==t.staffJobStatus?"启用":"停用",l=(null!=t.staffShortName?t.staffShortName:"")+(null!=t.staffLocalName?t.staffLocalName:"")+(null!=t.staffEnName?t.staffEnName:"");this.$confirm('确认要"'+a+'""'+l+'"用户吗？',"提示",{customClass:"modal-confirm"}).then((function(){return Object(s["b"])(t.staffId,t.staffJobStatus)})).then((function(){e.$modal.msgSuccess(a+"成功")})).catch((function(){t.staffJobStatus="0"==t.staffJobStatus?"1":"0"}))},getLocationId:function(t){this.form.locationId=t},handleClose:function(){}}},r=n,f=a("2877"),i=Object(f["a"])(r,l,o,!1,null,null,null);e["default"]=i.exports},b857:function(t,e,a){"use strict";a.d(e,"e",(function(){return o})),a.d(e,"d",(function(){return s})),a.d(e,"a",(function(){return n})),a.d(e,"f",(function(){return r})),a.d(e,"c",(function(){return f})),a.d(e,"b",(function(){return i}));var l=a("b775");function o(t){return Object(l["a"])({url:"/system/extStaff/list",method:"get",params:t})}function s(t){return Object(l["a"])({url:"/system/extStaff/"+t,method:"get"})}function n(t){return Object(l["a"])({url:"/system/extStaff",method:"post",data:t})}function r(t){return Object(l["a"])({url:"/system/extStaff",method:"put",data:t})}function f(t){return Object(l["a"])({url:"/system/extStaff/"+t,method:"delete"})}function i(t,e){var a={staffId:t,staffJobStatus:e};return Object(l["a"])({url:"/system/extStaff",method:"put",data:a})}}}]);