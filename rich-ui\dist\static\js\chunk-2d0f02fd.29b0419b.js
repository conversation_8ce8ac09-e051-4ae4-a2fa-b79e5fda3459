(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d0f02fd"],{"9afe":function(e,r,t){"use strict";t.r(r);var a=function(){var e=this,r=e.$createElement,t=e._self._c||r;return t("div",{staticClass:"app-container"},[t("el-row",{attrs:{gutter:20}},[t("el-col",{attrs:{span:e.showLeft}},[e.showSearch?t("el-form",{ref:"queryForm",staticClass:"query",attrs:{inline:!0,model:e.queryParams,size:"mini"}},[t("el-form-item",{attrs:{label:"物流",prop:"logisticsTypeId"}},[t("tree-select",{staticStyle:{width:"100%"},attrs:{flat:!1,multiple:!1,pass:e.queryParams.logisticsTypeId,placeholder:"服务类型",type:"serviceType",dbn:!0},on:{return:e.queryLogisticsTypeId}})],1),t("el-form-item",{attrs:{label:"服务",prop:"serviceTypeId"}},[t("tree-select",{staticStyle:{width:"100%"},attrs:{flat:!1,multiple:!1,pass:e.queryParams.serviceTypeId,placeholder:"物流类型",type:"serviceType"},on:{return:e.queryServiceTypeId}})],1),t("el-form-item",{attrs:{label:"货物",prop:"cargoTypeIds"}},[t("tree-select",{staticStyle:{width:"100%"},attrs:{flat:!1,multiple:!0,pass:e.queryParams.cargoTypeIds,placeholder:"货物特征",type:"cargoType"},on:{return:e.queryCargoTypeIds}})],1),t("el-form-item",{attrs:{label:"承运",prop:"carrierIds"}},[t("treeselect",{staticStyle:{width:"100%"},attrs:{"disable-fuzzy-matching":!0,"disable-branch-nodes":!0,flat:!0,"flatten-search-results":!0,multiple:!0,normalizer:e.carrierNormalizer,options:e.carrierList,"show-count":!0,placeholder:"承运人"},on:{input:e.deselectAllQueryCarrierIds,open:e.loadCarrier,deselect:e.handleDeselectQueryCarrierIds,select:e.handleSelectQueryCarrierIds},scopedSlots:e._u([{key:"value-label",fn:function(r){var a=r.node;return t("div",{},[e._v(" "+e._s(a.raw.carrier.carrierIntlCode)+" "+e._s(null==a.raw.carrier.carrierIntlCode?a.raw.carrier.carrierShortName:"")+" ")])}},{key:"option-label",fn:function(r){var a=r.node,l=r.shouldShowCount,i=r.count,s=r.labelClassName,o=r.countClassName;return t("label",{class:s},[e._v(" "+e._s(-1!=a.label.indexOf(",")?a.label.substring(0,a.label.indexOf(",")):a.label)+" "),l?t("span",{class:o},[e._v("("+e._s(i)+")")]):e._e()])}}],null,!1,4006291921),model:{value:e.queryCarrierIds,callback:function(r){e.queryCarrierIds=r},expression:"queryCarrierIds"}})],1),t("el-form-item",{attrs:{label:"订舱",prop:"supplierId"}},[t("tree-select",{staticStyle:{width:"100%"},attrs:{flat:!1,multiple:!1,pass:e.queryParams.supplierId,placeholder:"订舱口",type:"supplier"},on:{return:e.queryCompanyId}})],1),t("el-form-item",{attrs:{label:"启运",prop:"locationDepartureIds"}},[t("location-select",{staticStyle:{width:"100%"},attrs:{multiple:!0,pass:e.queryParams.locationDepartureIds,"load-options":e.locationOptions},on:{return:e.queryLocationDepartureIds}})],1),t("el-form-item",{attrs:{label:"目的",prop:"locationDestinationIds"}},[t("location-select",{staticStyle:{width:"100%"},attrs:{en:!0,multiple:!0,pass:e.queryParams.locationDestinationIds,"load-options":e.locationOptions},on:{return:e.queryLocationDestinationIds}})],1),t("el-form-item",{attrs:{label:"航线",prop:"lineDestinationIds"}},[t("tree-select",{staticStyle:{width:"100%"},attrs:{flat:!1,multiple:!0,pass:e.queryParams.lineDestinationIds,placeholder:"目的航线",type:"line"},on:{return:e.queryLineDestinationIds}})],1),t("el-form-item",{attrs:{label:"费用",prop:"chargeTypeId"}},[t("treeselect",{staticStyle:{width:"100%"},attrs:{"disable-fuzzy-matching":!0,"flatten-search-results":!0,"disable-branch-nodes":!0,normalizer:e.chargeNormalizer,placeholder:"选择费用",options:e.chargeOptions,"show-count":!0},on:{open:e.loadCharge,input:e.handleDeselectQueryChargeId,select:e.handleSelectQueryChargeId},scopedSlots:e._u([{key:"value-label",fn:function(r){var a=r.node;return t("div",{},[e._v(" "+e._s(a.raw.charge.chargeShortName)+" ")])}},{key:"option-label",fn:function(r){var a=r.node,l=r.shouldShowCount,i=r.count,s=r.labelClassName,o=r.countClassName;return t("label",{class:s},[e._v(" "+e._s(-1!=a.label.indexOf(",")?a.label.substring(0,a.label.indexOf(",")):a.label)+" "),l?t("span",{class:o},[e._v("("+e._s(i)+")")]):e._e()])}}],null,!1,4284726914),model:{value:e.queryParams.chargeTypeId,callback:function(r){e.$set(e.queryParams,"chargeTypeId",r)},expression:"queryParams.chargeTypeId"}})],1),t("el-form-item",{attrs:{label:"有效",prop:"isValid"}},[t("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"是否有效"},on:{change:e.handleQuery},model:{value:e.queryParams.isValid,callback:function(r){e.$set(e.queryParams,"isValid",r)},expression:"queryParams.isValid"}},e._l(e.dict.type.sys_yes_no,(function(e){return t("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),t("el-form-item",{attrs:{label:"员工",prop:"updateBy"}},[t("el-select",{staticStyle:{width:"100%"},attrs:{filterable:"",placeholder:"选择员工",clearable:""},on:{change:e.handleQuery},model:{value:e.queryParams.updateBy,callback:function(r){e.$set(e.queryParams,"updateBy",r)},expression:"queryParams.updateBy"}},e._l(e.userList,(function(e){return t("el-option",{key:e.staffId,attrs:{label:e.staffCode+" "+e.staffFamilyLocalName+e.staffGivingLocalName+" "+e.staffGivingEnName,value:e.staffId}})})),1)],1),t("el-form-item",{attrs:{label:"时间",prop:"updateTime"}},[t("el-date-picker",{staticStyle:{width:"100%"},attrs:{clearable:"",placeholder:"录入起始时间",type:"date","value-format":"yyyy-MM-dd"},on:{change:e.handleQuery},model:{value:e.queryParams.updateTime,callback:function(r){e.$set(e.queryParams,"updateTime",r)},expression:"queryParams.updateTime"}})],1),t("el-form-item",[t("el-button",{attrs:{icon:"el-icon-search",size:"mini",type:"primary"},on:{click:e.handleQuery}},[e._v("搜索")]),t("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1):e._e()],1),t("el-col",{attrs:{span:e.showRight}},[t("el-row",{staticClass:"mb8",attrs:{gutter:10}},[t("el-col",{attrs:{span:1.5}},[t("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:local:add"],expression:"['system:local:add']"}],attrs:{icon:"el-icon-plus",plain:"",size:"mini",type:"primary"},on:{click:e.handleAdd}},[e._v("新增 ")])],1),t("el-col",{attrs:{span:1.5}},[t("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:local:remove"],expression:"['system:local:remove']"}],attrs:{disabled:e.multiple,icon:"el-icon-delete",plain:"",size:"mini",type:"danger"},on:{click:e.handleDelete}},[e._v("删除 ")])],1),t("el-col",{attrs:{span:1.5}},[t("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:local:remove"],expression:"['system:local:remove']"}],attrs:{icon:"el-icon-upload2",plain:"",size:"mini",type:"info"},on:{click:e.handleImport}},[e._v("导入 ")])],1),t("el-col",{attrs:{span:1.5}},[t("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:local:remove"],expression:"['system:local:remove']"}],attrs:{icon:"el-icon-download",plain:"",size:"mini",type:"warning"},on:{click:e.handleExport}},[e._v("导出 ")])],1),t("right-toolbar",{attrs:{showSearch:e.showSearch},on:{"update:showSearch":function(r){e.showSearch=r},"update:show-search":function(r){e.showSearch=r},queryTable:e.getList}})],1),t("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.localList,"row-class-name":e.tableRowClassName,border:""},on:{"selection-change":e.handleSelectionChange}},[t("el-table-column",{attrs:{type:"selection",width:"28",align:"center"}}),t("el-table-column",{attrs:{align:"center",label:"附加序号",prop:"inquiryNo","show-tooltip-when-overflow":"",width:"80"}}),t("el-table-column",{attrs:{align:"center",label:"物流类型",prop:"logisticsType",width:"58px"}}),t("el-table-column",{attrs:{align:"center",label:"服务类型",prop:"serviceType",width:"58px"}}),t("el-table-column",{key:"cargoType",attrs:{align:"left",label:"货物特征",prop:"cargoType",width:"68px","show-tooltip-when-overflow":""}}),t("el-table-column",{key:"carrier",attrs:{align:"left",label:"承运人",width:"58px","show-tooltip-when-overflow":""},scopedSlots:e._u([{key:"default",fn:function(r){return[t("h6",{staticStyle:{margin:"0",overflow:"hidden","text-overflow":"ellipsis","white-space":"nowrap"}},[e._v(" "+e._s(null==r.row.carrier?"全部":r.row.carrier)+" ")])]}}])}),t("el-table-column",{attrs:{align:"center",label:"订舱口","show-tooltip-when-overflow":"",width:"68px"},scopedSlots:e._u([{key:"default",fn:function(r){return[t("h6",{staticStyle:{margin:"0"}},[e._v(" "+e._s(null==r.row.supplierId?"全部":r.row.company)+" ")])]}}])}),t("el-table-column",{attrs:{align:"left",label:"启运区域","show-tooltip-when-overflow":"",width:"68px"},scopedSlots:e._u([{key:"default",fn:function(r){return[t("h6",{staticStyle:{margin:"0",overflow:"hidden","text-overflow":"ellipsis","white-space":"nowrap"}},[e._v(" "+e._s((null!=r.row.locationDeparture?r.row.locationDeparture:"")+(null!=r.row.locationDeparture&&null!=r.row.lineDeparture?",":"")+(null!=r.row.lineDeparture?r.row.lineDeparture:""))+" ")])]}}])}),t("el-table-column",{attrs:{align:"left",label:"目的区域","show-tooltip-when-overflow":"",width:"100px"},scopedSlots:e._u([{key:"default",fn:function(r){return[t("h6",{staticStyle:{margin:"0",overflow:"hidden","text-overflow":"ellipsis","white-space":"nowrap"}},[e._v(" "+e._s((null!=r.row.locationDestination?r.row.locationDestination:"")+(null!=r.row.lineDestination&&null!=r.row.locationDestination?",":"")+(null!=r.row.lineDestination?r.row.lineDestination:""))+" ")])]}}])}),t("el-table-column",{attrs:{align:"center",label:"费用",prop:"charge",width:"80"}}),t("el-table-column",{attrs:{align:"center",label:"币种",prop:"currency",width:"48"}}),t("el-table-column",{attrs:{align:"center",label:"具体费用",width:"200"},scopedSlots:e._u([{key:"default",fn:function(r){return[t("h2",{staticStyle:{margin:"0","font-weight":"bold"}},[e._v(" "+e._s((null!=r.row.priceB?r.row.priceB+(null!=r.row.priceC?" / ":""):"")+(null!=r.row.priceC?r.row.priceC+(null!=r.row.priceD?" / ":""):"")+(null!=r.row.priceD?r.row.priceD+(null!=r.row.priceA?" / ":""):"")+(null!=r.row.priceA?r.row.priceA:""))+" ")])]}}])}),t("el-table-column",{attrs:{align:"center",label:"单位",prop:"unitCode",width:"58"}}),t("el-table-column",{attrs:{align:"center",label:"有效时间",prop:"validTime",width:"130"},scopedSlots:e._u([{key:"default",fn:function(r){return[t("span",[e._v(e._s(e.parseTime(r.row.validFrom,"{m}.{d}"))+"-"+e._s(e.parseTime(r.row.validTo,"{m}.{d}")))])]}}])}),t("el-table-column",{attrs:{align:"center",label:"有效",prop:"isValid",width:"38"},scopedSlots:e._u([{key:"default",fn:function(r){return[t("dict-tag",{attrs:{options:e.dict.type.sys_yes_no,value:r.row.isValid}})]}}])}),t("el-table-column",{attrs:{align:"left",label:"备注",prop:"remark","show-tooltip-when-overflow":""}}),t("el-table-column",{attrs:{align:"center",label:"录入人",prop:"updateTime",width:"70"},scopedSlots:e._u([{key:"default",fn:function(r){return[t("h6",{staticStyle:{margin:"0"}},[e._v(e._s(r.row.updateByName))]),t("h6",{staticStyle:{margin:"0"}},[e._v(e._s(e.parseTime(r.row.updateTime,"{y}-{m}-{d}")))])]}}])}),t("el-table-column",{attrs:{align:"center","class-name":"small-padding fixed-width",label:"操作",width:"100"},scopedSlots:e._u([{key:"default",fn:function(r){return[t("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:local:edit"],expression:"['system:local:edit']"}],staticStyle:{"margin-right":"-8px"},attrs:{icon:"el-icon-edit",size:"mini",type:"success"},on:{click:function(t){return e.handleUpdate(r.row)}}},[e._v("修改 ")]),t("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:local:remove"],expression:"['system:local:remove']"}],staticStyle:{"margin-right":"-8px"},attrs:{icon:"el-icon-delete",size:"mini",type:"danger"},on:{click:function(t){return e.handleDelete(r.row)}}},[e._v("删除 ")])]}}])})],1),t("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total>0"}],attrs:{limit:e.queryParams.pageSize,page:e.queryParams.pageNum,total:e.total},on:{"update:limit":function(r){return e.$set(e.queryParams,"pageSize",r)},"update:page":function(r){return e.$set(e.queryParams,"pageNum",r)},pagination:e.getList}})],1)],1),t("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"},{name:"dialogDragWidth",rawName:"v-dialogDragWidth"}],attrs:{"close-on-click-modal":!1,"modal-append-to-body":!1,title:e.title,visible:e.open,"append-to-body":"",width:"500px"},on:{"update:visible":function(r){e.open=r}}},[t("el-form",{ref:"form",staticClass:"edit",attrs:{model:e.form,rules:e.rules,"label-width":"68px"}},[t("el-form-item",{attrs:{label:"服务项目",prop:"serviceTypeId"}},[t("tree-select",{attrs:{flat:!1,multiple:!1,pass:e.form.serviceTypeId,type:"serviceType"},on:{return:e.getServiceTypeId}})],1),t("el-form-item",{attrs:{label:"物流类型",prop:"logisticsTypeId"}},[t("tree-select",{attrs:{flat:!1,multiple:!1,pass:e.form.logisticsTypeId,type:"serviceType",main:!0,dbn:!0},on:{return:e.getLogisticsTypeId}})],1),t("el-form-item",{attrs:{label:"货物特征",prop:"cargoTypeIds"}},[t("tree-select",{attrs:{flat:!1,multiple:!0,pass:e.form.cargoTypeIds,type:"cargoType"},on:{return:e.getCargoTypeIds}})],1),t("el-form-item",{attrs:{label:"承运人",prop:"carrierIds"}},[t("treeselect",{attrs:{"disable-fuzzy-matching":!0,"disable-branch-nodes":!0,flat:!0,"flatten-search-results":!0,multiple:!0,normalizer:e.carrierNormalizer,placeholder:"选择承运人",options:e.temCarrierList,"show-count":!0},on:{input:e.deselectAllCarrrierIds,open:e.loadCarrier,deselect:e.handleDeselectCarrierIds,select:e.handleSelectCarrierIds},scopedSlots:e._u([{key:"value-label",fn:function(r){var a=r.node;return t("div",{},[e._v(" "+e._s(a.raw.carrier.carrierIntlCode)+" "+e._s(null==a.raw.carrier.carrierIntlCode?a.raw.carrier.carrierShortName:"")+" ")])}},{key:"option-label",fn:function(r){var a=r.node,l=r.shouldShowCount,i=r.count,s=r.labelClassName,o=r.countClassName;return t("label",{class:s},[e._v(" "+e._s(-1!=a.label.indexOf(",")?a.label.substring(0,a.label.indexOf(",")):a.label)+" "),l?t("span",{class:o},[e._v("("+e._s(i)+")")]):e._e()])}}]),model:{value:e.carrierIds,callback:function(r){e.carrierIds=r},expression:"carrierIds"}})],1),t("el-form-item",{attrs:{label:"订舱口",prop:"supplierId"}},[t("tree-select",{attrs:{flat:!1,multiple:!1,pass:e.form.supplierId,placeholder:"订舱口",type:"supplier"},on:{return:e.getCompanyId}})],1),t("el-row",[t("el-form-item",{attrs:{label:"启运区域",prop:"locationDepartureIds"}},[t("location-select",{ref:"location",attrs:{multiple:!0,pass:e.form.locationDepartureIds,"load-options":e.locationOptions},on:{return:e.getLocationDepartureIds}})],1)],1),t("el-row",[t("el-form-item",{attrs:{label:"目的区域",prop:"locationDestinationIds"}},[t("location-select",{attrs:{multiple:!0,pass:e.form.locationDestinationIds,en:!0,"load-options":e.locationOptions},on:{return:e.getLocationDestinationIds}})],1)],1),t("el-row",[t("el-form-item",{attrs:{label:"目的航线",prop:"lineDestinationIds"}},[t("tree-select",{attrs:{flat:!1,multiple:!0,pass:e.form.lineDestinationIds,type:"line"},on:{return:e.getLineDestinationIds}})],1)],1),t("el-form-item",{attrs:{label:"费用名称",prop:"chargeTypeId"}},[t("treeselect",{attrs:{"disable-fuzzy-matching":!0,"flatten-search-results":!0,"disable-branch-nodes":!0,normalizer:e.chargeNormalizer,placeholder:"费用名称",options:e.chargeOptions,"show-count":!0},on:{open:e.loadCharge,input:e.handleDeselectChargeId,select:e.handleSelectChargeId},scopedSlots:e._u([{key:"value-label",fn:function(r){var a=r.node;return t("div",{},[e._v(" "+e._s(a.raw.charge.chargeShortName)+" ")])}},{key:"option-label",fn:function(r){var a=r.node,l=r.shouldShowCount,i=r.count,s=r.labelClassName,o=r.countClassName;return t("label",{class:s},[e._v(" "+e._s(-1!=a.label.indexOf(",")?a.label.substring(0,a.label.indexOf(",")):a.label)+" "),l?t("span",{class:o},[e._v("("+e._s(i)+")")]):e._e()])}}]),model:{value:e.form.chargeTypeId,callback:function(r){e.$set(e.form,"chargeTypeId",r)},expression:"form.chargeTypeId"}})],1),t("el-form-item",{attrs:{label:"价格",prop:"currencyCode"}},[t("el-row",[t("el-col",{attrs:{span:5}},[t("tree-select",{attrs:{flat:!1,multiple:!1,pass:e.form.currencyCode,type:"currency",placeholder:"币种"},on:{return:e.getCurrencyId}})],1),t("el-col",{attrs:{span:14}},[t("el-input",{attrs:{placeholder:"具体费用"},model:{value:e.form.formValue,callback:function(r){e.$set(e.form,"formValue",r)},expression:"form.formValue"}})],1),t("el-col",{attrs:{span:5}},[t("tree-select",{attrs:{pass:e.form.unitCode,type:"unit",placeholder:"单位"},on:{return:e.getUnitId}})],1)],1)],1),t("el-row",{attrs:{gutter:5}},[t("el-col",[t("el-form-item",{attrs:{label:"有效时间",prop:"validTime"}},[t("el-date-picker",{staticStyle:{width:"100%"},attrs:{clearable:"",placeholder:"有效时间",type:"daterange","default-time":["00:00:00","23:59:59"],"value-format":"yyyy-MM-dd HH:mm:ss"},on:{change:e.changeTime},model:{value:e.validTime,callback:function(r){e.validTime=r},expression:"validTime"}})],1)],1)],1),t("el-row",{attrs:{gutter:5}},[t("el-col",[t("el-form-item",{attrs:{label:"是否有效",prop:"isValid"}},[t("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"是否有效"},model:{value:e.form.isValid,callback:function(r){e.$set(e.form,"isValid",r)},expression:"form.isValid"}},e._l(e.dict.type.sys_yes_no,(function(e){return t("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1)],1)],1),t("el-form-item",{attrs:{label:"备注",prop:"remark"}},[t("el-input",{attrs:{type:"textarea",placeholder:"备注"},model:{value:e.form.remark,callback:function(r){e.$set(e.form,"remark",r)},expression:"form.remark"}})],1),t("el-row",[t("el-col",[t("el-form-item",{attrs:{label:"录入人",prop:"updateTime"}},[e._v(" "+e._s(e.form.updateByName)+"-"+e._s(e.form.updateTime)+" ")])],1)],1)],1),t("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("确 定")]),t("el-button",{on:{click:e.cancel}},[e._v("取 消")])],1)],1),t("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"},{name:"dialogDragWidth",rawName:"v-dialogDragWidth"}],attrs:{"close-on-click-modal":!1,"modal-append-to-body":!1,title:e.upload.title,visible:e.upload.open,"append-to-body":"",width:"400px"},on:{"update:visible":function(r){return e.$set(e.upload,"open",r)}}},[t("el-upload",{ref:"upload",attrs:{action:e.upload.url+"?updateSupport="+e.upload.updateSupport,"auto-upload":!1,disabled:e.upload.isUploading,headers:e.upload.headers,limit:1,"on-progress":e.handleFileUploadProgress,"on-success":e.handleFileSuccess,accept:".xlsx, .xls",drag:""}},[t("i",{staticClass:"el-icon-upload"}),t("div",{staticClass:"el-upload__text"},[e._v("将文件拖到此处，或"),t("em",[e._v("点击上传")])]),t("div",{staticClass:"el-upload__tip text-center",attrs:{slot:"tip"},slot:"tip"},[t("div",{staticClass:"el-upload__tip",attrs:{slot:"tip"},slot:"tip"},[t("el-checkbox",{model:{value:e.upload.updateSupport,callback:function(r){e.$set(e.upload,"updateSupport",r)},expression:"upload.updateSupport"}}),e._v(" 是否更新已经存在的用户数据 ")],1),t("span",[e._v("仅允许导入xls、xlsx格式文件。")])])]),t("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[t("el-button",{attrs:{type:"primary"},on:{click:e.submitFileForm}},[e._v("确 定")]),t("el-button",{on:{click:function(r){e.upload.open=!1}}},[e._v("取 消")])],1)],1)],1)},l=[],i=t("5530"),s=t("b85c"),o=(t("14d9"),t("d81d"),t("caad"),t("2532"),t("a9e3"),t("4de4"),t("d3b7"),t("b775"));function n(e){return Object(o["a"])({url:"/system/local/list",method:"get",params:e})}function c(e){return Object(o["a"])({url:"/system/local/"+e,method:"get"})}function d(e){return Object(o["a"])({url:"/system/local",method:"post",data:e})}function u(e){return Object(o["a"])({url:"/system/local",method:"put",data:e})}function p(e){return Object(o["a"])({url:"/system/local/"+e,method:"delete"})}var h=t("ca17"),m=t.n(h),f=(t("6f8d"),t("b0b8")),y=t.n(f),g=t("4360"),v=t("fba1"),I=t("c0c7"),b=t("5f87"),w={name:"Local",dicts:["sys_normal_disable","sys_yes_no"],components:{Treeselect:m.a},data:function(){return{showLeft:3,showRight:21,validTime:[],carrierIds:[],carrierList:[],chargeOptions:[],queryCarrierIds:[],temCarrierList:[],locationOptions:[],userList:[],loading:!0,ids:[],single:!0,multiple:!0,showSearch:!0,total:0,localList:[],title:"",open:!1,queryParams:{pageNum:1,pageSize:20,serviceTypeId:null,logisticsTypeId:null,cargoTypeIds:[],locationDepartureIds:[],lineDepartureIds:[],locationDestinationIds:[],lineDestinationIds:[],carrierIds:[],chargeId:null,chargeTypeId:null,supplierId:null,isValid:"Y",updateBy:null,updateTime:null},form:{},rules:{formValue:{required:!0,pattern:/((\-)?([0-9]{1,6})(\.?)(\d{0,2})\/){0,4}$/,trigger:"blur"},unitId:{required:!0,trigger:"blur"},chargeTypeId:{required:!0,trigger:"blur"}},upload:{open:!1,title:"",isUploading:!1,updateSupport:!0,headers:{Authorization:"Bearer "+Object(b["a"])()},url:"/prod-api/system/local/importData"}}},watch:{showSearch:function(e){1==e?(this.showRight=21,this.showLeft=3):(this.showRight=24,this.showLeft=0)},"form.serviceTypeId":function(e){this.loadCarrier();var r=[];if(void 0!=this.carrierList&&null!=e&&(this.temCarrierList=this.carrierList),void 0!=this.carrierList&&null!=e){var t,a=Object(s["a"])(this.carrierList);try{for(a.s();!(t=a.n()).done;){var l=t.value;if(null!=e&&void 0!=e&&(l.serviceTypeId==e&&r.push(l),void 0!=l.children&&l.children.length>0)){var i,o=Object(s["a"])(l.children);try{for(o.s();!(i=o.n()).done;){var n=i.value;n.serviceTypeId==e&&r.push(n)}}catch(c){o.e(c)}finally{o.f()}}}}catch(c){a.e(c)}finally{a.f()}this.temCarrierList=r,this.temCarrierList.length}}},created:function(){var e=this;this.getList(),this.loadCarrier(),this.loadCharge(),Object(I["j"])().then((function(r){e.userList=r.data}))},methods:{changeTime:function(e){void 0==e&&(this.form.validFrom=null,this.form.validTo=null),this.form.validFrom=e[0],this.form.validTo=e[1]},loadCharge:function(){var e=this;0==this.$store.state.data.chargeOptions.length||this.$store.state.data.redisList.chargeList?g["a"].dispatch("getChargeOptions").then((function(){e.chargeOptions=e.$store.state.data.chargeOptions})):this.chargeOptions=this.$store.state.data.chargeOptions},loadCarrier:function(){var e=this;0==this.$store.state.data.serviceTypeCarriers.length||this.$store.state.data.redisList.serviceTypeCarriers?g["a"].dispatch("getServiceTypeCarriersList").then((function(){e.carrierList=e.$store.state.data.serviceTypeCarriers})):this.carrierList=this.$store.state.data.serviceTypeCarriers},getList:function(){var e=this;this.loading=!0,n(this.queryParams).then((function(r){e.localList=r.rows,e.total=r.total,e.loading=!1}))},tableRowClassName:function(e){var r=e.row,t=Object(v["f"])(new Date,"{y}-{m}-{d}"),a=Object(v["f"])(r.validFrom,"{y}-{m}-{d}"),l=Object(v["f"])(r.validTo,"{y}-{m}-{d}");return a<t<l?"":l<t?"valid-row":a>t?"valid-before":""},cancel:function(){this.open=!1,this.reset(),this.getList()},chargeNormalizer:function(e){var r;return r=null==e.charge.chargeId?(null!=e.chargeTypeShortName?e.chargeTypeShortName:"")+" "+(null!=e.chargeTypeLocalName?e.chargeTypeLocalName:"")+" "+(null!=e.chargeTypeEnName?e.chargeTypeEnName:"")+","+y.a.getFullChars(e.chargeTypeShortName+e.chargeTypeLocalName):(null!=e.charge.chargeShortName?e.charge.chargeShortName:"")+" "+(null!=e.charge.chargeLocalName?e.charge.chargeLocalName:"")+" "+(null!=e.charge.chargeEnName?e.charge.chargeEnName:"")+","+y.a.getFullChars(e.charge.chargeShortName+e.charge.chargeLocalName),{id:e.chargeTypeId,label:r}},carrierNormalizer:function(e){var r;return e.children&&!e.children.length&&delete e.children,r=!e.carrier||null==e.carrier.carrierLocalName&&null==e.carrier.carrierEnName?e.serviceLocalName+" "+e.serviceEnName+","+y.a.getFullChars(e.serviceLocalName?e.serviceLocalName:""):(null!=e.carrier.carrierIntlCode?e.carrier.carrierIntlCode:"")+" "+(null!=e.carrier.carrierEnName?e.carrier.carrierEnName:"")+" "+(null!=e.carrier.carrierLocalName?e.carrier.carrierLocalName:"")+","+y.a.getFullChars(null!=e.carrier.carrierLocalName?e.carrier.carrierLocalName:""),{id:e.serviceTypeId,label:r,children:e.children}},reset:function(){this.carrierIds=[],this.validTime=[],this.form={localChargeId:null,serviceTypeId:void 0!=this.form.serviceTypeId?this.form.serviceTypeId:1,logisticsTypeId:void 0!=this.form.logisticsTypeId?this.form.logisticsTypeId:1,chargeTypeId:null,chargeId:null,currencyId:1,unitId:2,priceB:null,priceC:null,priceD:null,priceA:null,updateTime:Object(v["f"])(new Date),validFrom:null,validTo:null,isValid:"Y",status:"0",remark:null,cargoTypeIds:void 0!=this.form.cargoTypeIds?this.form.cargoTypeIds:[-1],locationDepartureIds:void 0!=this.form.locationDepartureIds?this.form.locationDepartureIds:[13716],lineDepartureIds:[],locationDestinationIds:[],lineDestinationIds:[],carrierIds:[],unitCode:null,currencyCode:"RMB"},this.resetForm("form")},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.resetForm("queryForm"),this.handleQuery()},handleSelectionChange:function(e){this.ids=e.map((function(e){return e.localChargeId})),this.single=1!=e.length,this.multiple=!e.length},handleAdd:function(){var e=this;this.reset(),this.open=!0,this.title="添加物流附加费策略",setTimeout((function(){e.$refs.location.remoteMethod("广东")}),0)},handleUpdate:function(e){var r=this;this.reset(),this.loading=!0;var t=e.localChargeId||this.ids;c(t).then((function(e){r.form=e.data;var t,a=Object(s["a"])(r.chargeOptions);try{for(a.s();!(t=a.n()).done;){var l,i=t.value,o=Object(s["a"])(i.children);try{for(o.s();!(l=o.n()).done;){var n=l.value;if(n.charge.chargeId==e.data.chargeId){r.form.chargeTypeId=n.chargeTypeId;break}}}catch(N){o.e(N)}finally{o.f()}}}catch(N){a.e(N)}finally{a.f()}r.$set(r.form,"formValue",(null!=e.data.priceB?e.data.priceB+(null!=e.data.priceC?"/":""):"")+(null!=e.data.priceC?e.data.priceC+(null!=e.data.priceD?"/":""):"")+(null!=e.data.priceD?e.data.priceD+(null!=e.data.priceA?"/":""):"")+(null!=e.data.priceA?e.data.priceA:"")),r.form.cargoTypeIds=e.cargoTypeIds,r.form.lineDepartureIds=e.lineDepartureIds,r.form.locationDepartureIds=e.locationDepartureIds,r.form.lineDestinationIds=e.lineDestinationIds,r.form.locationDestinationIds=e.locationDestinationIds,r.form.carrierIds=e.carrierIds,r.loadCarrier();var c=[];if(void 0!=r.carrierList&&null!=r.form.serviceTypeId){r.temCarrierList=r.carrierList;var d,u=Object(s["a"])(r.carrierList);try{for(u.s();!(d=u.n()).done;){var p=d.value;if(void 0!=p.children&&p.children.length>0){var h,m=Object(s["a"])(p.children);try{for(m.s();!(h=m.n()).done;){var f=h.value;null!=e.carrierIds&&e.carrierIds.includes(f.carrier.carrierId)&&!e.carrierIds.includes(f.serviceTypeId)&&r.carrierIds.push(f.serviceTypeId)}}catch(N){m.e(N)}finally{m.f()}}}}catch(N){u.e(N)}finally{u.f()}}if(void 0!=r.carrierList&&null!=r.form.serviceTypeId){var y,g=Object(s["a"])(r.carrierList);try{for(g.s();!(y=g.n()).done;){var v=y.value;if(null!=r.form.serviceTypeId&&void 0!=r.form.serviceTypeId&&(v.serviceTypeId==r.form.serviceTypeId&&c.push(v),void 0!=v.children&&v.children.length>0)){var I,b=Object(s["a"])(v.children);try{for(b.s();!(I=b.n()).done;){var w=I.value;w.serviceTypeId==r.form.serviceTypeId&&c.push(w)}}catch(N){b.e(N)}finally{b.f()}}}}catch(N){g.e(N)}finally{g.f()}if(r.temCarrierList=c,null==r.form.serviceTypeId&&r.temCarrierList.length>0){var T,C=Object(s["a"])(r.temCarrierList);try{for(C.s();!(T=C.n()).done;){var _=T.value;if(void 0!=_.children&&_.children.length>0){var D,S=Object(s["a"])(_.children);try{for(S.s();!(D=S.n()).done;){var L=D.value;null!=e.carrierIds&&e.carrierIds.includes(L.carrier.carrierId)&&!e.carrierIds.includes(L.serviceTypeId)&&r.carrierIds.push(L.serviceTypeId)}}catch(N){S.e(N)}finally{S.f()}}}}catch(N){C.e(N)}finally{C.f()}}}e.data.validFrom&&r.validTime.push(e.data.validFrom),e.data.validTo&&r.validTime.push(e.data.validTo),r.locationOptions=e.locationOptions,r.open=!0,r.title="修改物流附加费策略",r.loading=!1}))},submitForm:function(){var e=this;this.$refs["form"].validate((function(r){if(r){var t=e.form.formValue.split("/");t.length>1?(e.form.priceB=void 0!=t[0]?Number(t[0]):null,e.form.priceC=void 0!=t[1]?Number(t[1]):null,e.form.priceD=void 0!=t[2]?Number(t[2]):void 0!=t[1]?Number(t[1]):null,e.form.priceA=void 0!=t[3]?Number(t[3]):null,e.form.unitId=2):(e.form.priceB=null,e.form.priceC=null,e.form.priceD=null,e.form.priceA=Number(e.form.formValue)),e.form.updateBy=e.$store.state.user.sid,null!=e.form.localChargeId?u(e.form).then((function(r){e.$modal.msgSuccess("修改成功"),e.open=!1,e.getList()})):d(e.form).then((function(r){e.$modal.msgSuccess("新增成功，请继续"),e.reset(),e.getList()}))}}))},handleDelete:function(e){var r=this,t=e.localChargeId||this.ids;this.$confirm('是否确认删除物流附加费策略编号为"'+t+'"的数据项？',"提示",{customClass:"modal-confirm"}).then((function(){return p(t)})).then((function(){r.getList(),r.$modal.msgSuccess("删除成功")})).catch((function(){}))},handleImport:function(){this.upload.title="用户导入",this.upload.open=!0},handleFileUploadProgress:function(e,r,t){this.upload.isUploading=!0},handleFileSuccess:function(e,r,t){this.upload.open=!1,this.upload.isUploading=!1,this.$refs.upload.clearFiles(),this.$message.info(e.msg),"全部上传成功"!==e.msg&&this.download("system/local/failList",{},"上传失败列表.xlsx"),this.getList()},submitFileForm:function(){this.$refs.upload.submit()},handleExport:function(){this.download("system/local/export",Object(i["a"])({},this.queryParams),"local_".concat((new Date).getTime(),".xlsx"))},queryServiceTypeId:function(e){this.queryParams.serviceTypeId=e,this.handleQuery()},queryLogisticsTypeId:function(e){this.queryParams.logisticsTypeId=e,this.handleQuery()},queryCargoTypeIds:function(e){this.queryParams.cargoTypeIds=e,this.handleQuery()},queryLocationDepartureIds:function(e){this.queryParams.locationDepartureIds=e,this.handleQuery()},queryLineDepartureIds:function(e){this.queryParams.lineDepartureIds=e,this.handleQuery()},queryLocationDestinationIds:function(e){this.queryParams.locationDestinationIds=e,this.handleQuery()},queryLineDestinationIds:function(e){this.queryParams.lineDestinationIds=e,this.handleQuery()},getServiceTypeId:function(e){void 0==e?(this.form.serviceTypeId=null,this.carrierIds=null,this.form.carrierIds=null):this.form.serviceTypeId=e},getLogisticsTypeId:function(e){this.form.logisticsTypeId=e},getCurrencyId:function(e){void 0==e?(this.form.currencyId=null,this.form.currencyCode=null):this.form.currencyCode=e},getUnitId:function(e){void 0==e?(this.form.unitId=null,this.form.unitCode=null):this.form.unitCode=e},getCargoTypeIds:function(e){this.form.cargoTypeIds=e},getCompanyId:function(e){this.form.supplierId=e},getLocationDepartureIds:function(e){this.form.locationDepartureIds=e},getLineDepartureIds:function(e){this.form.lineDepartureIds=e},getLocationDestinationIds:function(e){this.form.locationDestinationIds=e},getLineDestinationIds:function(e){this.form.lineDestinationIds=e},handleSelectCarrierIds:function(e){this.form.carrierIds.push(e.carrier.carrierId)},handleSelectQueryCarrierIds:function(e){this.queryParams.carrierIds.push(e.carrier.carrierId),this.handleQuery()},handleDeselectCarrierIds:function(e){this.form.carrierIds=this.form.carrierIds.filter((function(r){return r!=e.carrier.carrierId}))},handleDeselectQueryCarrierIds:function(e){this.queryParams.carrierIds=this.queryParams.carrierIds.filter((function(r){return r!=e.carrier.carrierId})),this.handleQuery()},deselectAllCarrrierIds:function(e){0==e.length&&(this.form.carrierIds=[])},deselectAllQueryCarrierIds:function(e){0==e.length&&(this.queryParams.carrierIds=[],this.handleQuery())},handleSelectChargeId:function(e){this.form.chargeId=e.charge.chargeId},handleSelectQueryChargeId:function(e){this.queryParams.chargeId=e.charge.chargeId,this.handleQuery()},handleDeselectQueryChargeId:function(e){void 0==e&&(this.queryParams.chargeId=null,this.handleQuery())},handleDeselectChargeId:function(e){void 0==e&&(this.form.chargeId=null)},queryCompanyId:function(e){this.queryParams.supplierId=e,this.handleQuery()}}},T=w,C=t("2877"),_=Object(C["a"])(T,a,l,!1,null,null,null);r["default"]=_.exports}}]);