(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-6565058d"],{"2a9c":function(e,t,r){"use strict";r.r(t);var a=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",{staticClass:"app-container"},[r("el-row",{attrs:{gutter:20}},[r("el-col",{attrs:{span:e.showLeft}},[r("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],ref:"queryForm",staticClass:"query",attrs:{model:e.queryParams,size:"mini",inline:!0,"label-width":"68px"}},[r("el-form-item",{attrs:{label:"简称",prop:"docShortName"}},[r("el-input",{attrs:{placeholder:"简称",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.docShortName,callback:function(t){e.$set(e.queryParams,"docShortName",t)},expression:"queryParams.docShortName"}})],1),r("el-form-item",{attrs:{label:"中文名",prop:"docLocalName"}},[r("el-input",{attrs:{placeholder:"中文名",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.docLocalName,callback:function(t){e.$set(e.queryParams,"docLocalName",t)},expression:"queryParams.docLocalName"}})],1),r("el-form-item",{attrs:{label:"英文名",prop:"docEnName"}},[r("el-input",{attrs:{placeholder:"英文名",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.docEnName,callback:function(t){e.$set(e.queryParams,"docEnName",t)},expression:"queryParams.docEnName"}})],1),r("el-form-item",{attrs:{label:"所属进度",prop:"processTypeId"}},[r("el-input",{attrs:{placeholder:"所属进度",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.processTypeId,callback:function(t){e.$set(e.queryParams,"processTypeId",t)},expression:"queryParams.processTypeId"}})],1),r("el-form-item",{attrs:{label:"系统生成",prop:"isGenerated"}},[r("el-input",{attrs:{placeholder:"系统生成",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.isGenerated,callback:function(t){e.$set(e.queryParams,"isGenerated",t)},expression:"queryParams.isGenerated"}})],1),r("el-form-item",{attrs:{label:"系统隔离",prop:"isIsolation"}},[r("el-input",{attrs:{placeholder:"系统隔离",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.isIsolation,callback:function(t){e.$set(e.queryParams,"isIsolation",t)},expression:"queryParams.isIsolation"}})],1),r("el-form-item",{attrs:{label:"横向优先级",prop:"priority"}},[r("el-input",{attrs:{placeholder:"横向优先级",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.priority,callback:function(t){e.$set(e.queryParams,"priority",t)},expression:"queryParams.priority"}})],1),r("el-form-item",{attrs:{label:"排序",prop:"orderNum"}},[r("el-input",{attrs:{placeholder:"排序",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.orderNum,callback:function(t){e.$set(e.queryParams,"orderNum",t)},expression:"queryParams.orderNum"}})],1),r("el-form-item",[r("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"mini"},on:{click:e.handleQuery}},[e._v("搜索")]),r("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1)],1),r("el-col",{attrs:{span:e.showRight}},[r("el-row",{staticClass:"mb8",attrs:{gutter:10}},[r("el-col",{attrs:{span:1.5}},[r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:doc:add"],expression:"['system:doc:add']"}],attrs:{type:"primary",plain:"",icon:"el-icon-plus",size:"mini"},on:{click:e.handleAdd}},[e._v("新增 ")])],1),r("el-col",{attrs:{span:1.5}},[r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:doc:edit"],expression:"['system:doc:edit']"}],attrs:{type:"success",plain:"",icon:"el-icon-edit",size:"mini",disabled:e.single},on:{click:e.handleUpdate}},[e._v("修改 ")])],1),r("el-col",{attrs:{span:1.5}},[r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:doc:remove"],expression:"['system:doc:remove']"}],attrs:{type:"danger",plain:"",icon:"el-icon-delete",size:"mini",disabled:e.multiple},on:{click:e.handleDelete}},[e._v("删除 ")])],1),r("el-col",{attrs:{span:1.5}},[r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:doc:export"],expression:"['system:doc:export']"}],attrs:{type:"warning",plain:"",icon:"el-icon-download",size:"mini"},on:{click:e.handleExport}},[e._v("导出 ")])],1),r("right-toolbar",{attrs:{showSearch:e.showSearch},on:{"update:showSearch":function(t){e.showSearch=t},"update:show-search":function(t){e.showSearch=t},queryTable:e.getList}})],1),r("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.docList},on:{"selection-change":e.handleSelectionChange}},[r("el-table-column",{attrs:{type:"selection",width:"28",align:"center"}}),r("el-table-column",{attrs:{label:"名称",align:"left",width:"150px"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(t.row.docShortName)+" "),r("span",{staticStyle:{"font-weight":"bold","font-size":"small"}},[e._v(e._s(t.row.docLocalName))]),e._v(" "+e._s(t.row.docEnName)+" ")]}}])}),r("el-table-column",{attrs:{label:"所属进度",align:"center",prop:"processType",width:"68"}}),r("el-table-column",{attrs:{label:"适用货名",align:"center",prop:"cargoType",width:"88"}}),r("el-table-column",{attrs:{label:"适用服务类型",align:"center",prop:"serviceType",width:"88"}}),r("el-table-column",{attrs:{label:"适用启运区域",align:"center",prop:"locationDeparture",width:"88"}}),r("el-table-column",{attrs:{label:"适用启运航线",align:"center",prop:"lineDeparture",width:"88"}}),r("el-table-column",{attrs:{label:"适用目的区域",align:"center",prop:"locationDestination",width:"88"}}),r("el-table-column",{attrs:{label:"适用目的航线",align:"center",prop:"lineDestination",width:"88"}}),r("el-table-column",{attrs:{label:"适用承运人",align:"center",prop:"carrier",width:"88"}}),r("el-table-column",{attrs:{label:"系统生成",align:"center",prop:"isGenerated",width:"58"}}),r("el-table-column",{attrs:{label:"系统隔离",align:"center",prop:"isIsolation",width:"58"}}),r("el-table-column",{attrs:{label:"文件流向",align:"center",prop:"docFlowDirection",width:"88"}}),r("el-table-column",{attrs:{label:"适用出单方式",align:"center",prop:"docIssueType",width:"88"}}),r("el-table-column",{attrs:{label:"纵向优先级",align:"center",prop:"priority",width:"68"}}),r("el-table-column",{attrs:{label:"横向优先级",align:"center",prop:"orderNum",width:"68"}}),r("el-table-column",{attrs:{label:"备注",align:"center",prop:"remark"}}),r("el-table-column",{attrs:{label:"状态",align:"center",prop:"status",width:"58"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("el-switch",{attrs:{"active-value":"0","inactive-value":"1"},on:{change:function(r){return e.handleStatusChange(t.row)}},model:{value:t.row.status,callback:function(r){e.$set(t.row,"status",r)},expression:"scope.row.status"}})]}}])}),r("el-table-column",{attrs:{label:"操作",align:"center","class-name":"small-padding fixed-width",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:doc:edit"],expression:"['system:doc:edit']"}],staticStyle:{"margin-right":"-8px"},attrs:{size:"mini",type:"success",icon:"el-icon-edit"},on:{click:function(r){return e.handleUpdate(t.row)}}},[e._v("修改 ")]),r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:doc:remove"],expression:"['system:doc:remove']"}],staticStyle:{"margin-right":"-8px"},attrs:{size:"mini",type:"danger",icon:"el-icon-delete"},on:{click:function(r){return e.handleDelete(t.row)}}},[e._v("删除 ")])]}}])})],1),r("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total>0"}],attrs:{total:e.total,page:e.queryParams.pageNum,limit:e.queryParams.pageSize},on:{"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},pagination:e.getList}})],1)],1),r("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"},{name:"dialogDragWidth",rawName:"v-dialogDragWidth"}],attrs:{"close-on-click-modal":!1,"modal-append-to-body":!1,title:e.title,visible:e.open,width:"500px","append-to-body":""},on:{"update:visible":function(t){e.open=t}}},[r("el-form",{ref:"form",staticClass:"edit",attrs:{model:e.form,rules:e.rules,"label-width":"80px"}},[r("el-form-item",{attrs:{label:"简称",prop:"docShortName"}},[r("el-input",{attrs:{placeholder:"简称"},model:{value:e.form.docShortName,callback:function(t){e.$set(e.form,"docShortName",t)},expression:"form.docShortName"}})],1),r("el-form-item",{attrs:{label:"中文名",prop:"docLocalName"}},[r("el-input",{attrs:{placeholder:"中文名"},model:{value:e.form.docLocalName,callback:function(t){e.$set(e.form,"docLocalName",t)},expression:"form.docLocalName"}})],1),r("el-form-item",{attrs:{label:"英文名",prop:"docEnName"}},[r("el-input",{attrs:{placeholder:"英文名"},model:{value:e.form.docEnName,callback:function(t){e.$set(e.form,"docEnName",t)},expression:"form.docEnName"}})],1),r("el-form-item",{attrs:{label:"进度分类",prop:"processTypeId"}},[r("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"进度分类"},model:{value:e.form.processTypeId,callback:function(t){e.$set(e.form,"processTypeId",t)},expression:"form.processTypeId"}},e._l(e.processtypeList,(function(t){return r("el-option",{key:t.processTypeId,attrs:{label:t.processTypeShortName,value:t.processTypeId}},[r("span",[e._v(e._s(t.processTypeShortName))]),r("span",[e._v(e._s(t.processTypeLocalName))])])})),1)],1),r("el-form-item",{attrs:{label:"货物特征",prop:"cargoTypeIds"}},[r("tree-select",{attrs:{flat:!1,multiple:!0,pass:e.form.cargoTypeIds,type:"cargoType"},on:{return:e.getCargoTypeIds}})],1),r("el-form-item",{attrs:{label:"服务类型",prop:"serviceTypeIds"}},[r("tree-select",{attrs:{flat:!1,multiple:!0,pass:e.form.serviceTypeIds,type:"serviceType"},on:{return:e.getServiceTypeIds}})],1),r("el-form-item",{attrs:{label:"启运区域",prop:"locationDepartureIds"}},[r("location-select",{attrs:{multiple:!0,pass:e.form.locationDepartureIds,"load-options":e.locationOptions},on:{return:e.getLocationDepartureIds}})],1),r("el-form-item",{attrs:{label:"目的区域",prop:"locationDestinationIds"}},[r("location-select",{attrs:{multiple:!0,pass:e.form.locationDestinationIds,"load-options":e.locationOptions,en:!0},on:{return:e.getLocationDestinationIds}})],1),r("el-form-item",{attrs:{label:"目的航线",prop:"lineDestinationIds"}},[r("tree-select",{attrs:{flat:!1,multiple:!0,pass:e.form.lineDestinationIds,type:"line"},on:{return:e.getLineDestinationIds}})],1),r("el-form-item",{attrs:{label:"优选承运",prop:"carrierIds"}},[r("treeselect",{attrs:{"disable-fuzzy-matching":!0,"disable-branch-nodes":!0,flat:!0,"flatten-search-results":!0,multiple:!0,normalizer:e.carrierNormalizer,options:e.temCarrierList,"show-count":!0,placeholder:"选择承运人"},on:{deselect:e.handleDeselectCarrierIds,open:e.loadCarrier,select:e.handleSelectCarrierIds},scopedSlots:e._u([{key:"value-label",fn:function(t){var a=t.node;return r("div",{},[e._v(" "+e._s(a.raw.carrier.carrierIntlCode)+" "+e._s(null==a.raw.carrier.carrierIntlCode?a.raw.carrier.carrierShortName:"")+" ")])}},{key:"option-label",fn:function(t){var a=t.node,l=t.shouldShowCount,i=t.count,s=t.labelClassName,o=t.countClassName;return r("label",{class:s},[e._v(" "+e._s(-1!=a.label.indexOf(",")?a.label.substring(0,a.label.indexOf(",")):a.label)+" "),l?r("span",{class:o},[e._v("("+e._s(i)+")")]):e._e()])}}]),model:{value:e.carrierIds,callback:function(t){e.carrierIds=t},expression:"carrierIds"}})],1),r("el-form-item",{attrs:{label:"系统生成",prop:"isGenerated"}},[r("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"是否系统生成"},model:{value:e.form.isGenerated,callback:function(t){e.$set(e.form,"isGenerated",t)},expression:"form.isGenerated"}},e._l(e.dict.type.sys_yes_no,(function(e){return r("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),r("el-form-item",{attrs:{label:"系统隔离",prop:"isIsolation"}},[r("el-select",{staticStyle:{width:"100%"},attrs:{placeholder:"是否系统隔离"},model:{value:e.form.isIsolation,callback:function(t){e.$set(e.form,"isIsolation",t)},expression:"form.isIsolation"}},e._l(e.dict.type.sys_yes_no,(function(e){return r("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),r("el-form-item",{attrs:{label:"纵向优先级",prop:"priority"}},[r("el-input-number",{staticStyle:{width:"100%"},attrs:{controls:!1,min:0,"controls-position":"right"},model:{value:e.form.priority,callback:function(t){e.$set(e.form,"priority",t)},expression:"form.priority"}})],1),r("el-form-item",{attrs:{label:"横向优先级",prop:"orderNum"}},[r("el-input-number",{staticStyle:{width:"100%"},attrs:{controls:!1,min:0,"controls-position":"right"},model:{value:e.form.orderNum,callback:function(t){e.$set(e.form,"orderNum",t)},expression:"form.orderNum"}})],1),r("el-form-item",{attrs:{label:"备注",prop:"remark"}},[r("el-input",{attrs:{placeholder:"备注"},model:{value:e.form.remark,callback:function(t){e.$set(e.form,"remark",t)},expression:"form.remark"}})],1)],1),r("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[r("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("确 定")]),r("el-button",{on:{click:e.cancel}},[e._v("取 消")])],1)],1)],1)},l=[],i=r("5530"),s=r("b85c"),o=(r("caad"),r("2532"),r("14d9"),r("d81d"),r("4de4"),r("d3b7"),r("a9e4")),n=r("b0b8"),c=r.n(n),d=r("4360"),u=r("ca17"),m=r.n(u),p=(r("6f8d"),r("f0c6")),h={name:"Doc",dicts:["sys_yes_no"],components:{Treeselect:m.a},data:function(){return{showLeft:3,showRight:21,loading:!0,ids:[],single:!0,multiple:!0,showSearch:!1,total:0,docList:[],locationOptions:[],carrierIds:[],carrierList:[],temCarrierList:[],processtypeList:[],title:"",open:!1,queryParams:{pageNum:1,pageSize:20,docShortName:null,docLocalName:null,docEnName:null,processTypeId:null,isGenerated:null,isIsolation:null,priority:null,orderNum:null,status:null},form:{},rules:{}}},watch:{showSearch:function(e){!0===e?(this.showRight=21,this.showLeft=3):(this.showRight=24,this.showLeft=0)},"form.serviceTypeIds":function(e){this.loadCarrier();var t=[];if(void 0!=this.carrierList&&null!=e&&e.includes(-1)){this.temCarrierList=this.carrierList;var r,a=Object(s["a"])(this.carrierList);try{for(a.s();!(r=a.n()).done;){var l=r.value;if(void 0!=l.children&&l.children.length>0){var i,o=Object(s["a"])(l.children);try{for(o.s();!(i=o.n()).done;){var n=i.value;if(void 0!=n.children&&n.children.length>0){var c,d=Object(s["a"])(n.children);try{for(d.s();!(c=d.n()).done;){var u=c.value;null!=this.form.carrierIds&&this.form.carrierIds.includes(u.carrier.carrierId)&&!this.carrierIds.includes(u.serviceTypeId)&&this.carrierIds.push(u.serviceTypeId)}}catch(x){d.e(x)}finally{d.f()}}}}catch(x){o.e(x)}finally{o.f()}}}}catch(x){a.e(x)}finally{a.f()}}if(void 0!=this.carrierList&&null!=e&&!e.includes(-1)){var m,p=Object(s["a"])(this.carrierList);try{for(p.s();!(m=p.n()).done;){var h=m.value;if(null!=e&&void 0!=e){var f,y=Object(s["a"])(e);try{for(y.s();!(f=y.n()).done;){var v=f.value;if(h.serviceTypeId==v&&t.push(h),void 0!=h.children&&h.children.length>0){var b,g=Object(s["a"])(h.children);try{for(g.s();!(b=g.n()).done;){var I=b.value;I.serviceTypeId==v&&t.push(I)}}catch(x){g.e(x)}finally{g.f()}}}}catch(x){y.e(x)}finally{y.f()}}}}catch(x){p.e(x)}finally{p.f()}if(this.temCarrierList=t,this.temCarrierList.length>0){var w,k=Object(s["a"])(this.temCarrierList);try{for(k.s();!(w=k.n()).done;){var N=w.value;if(void 0!=N.children&&N.children.length>0){var L,S=Object(s["a"])(N.children);try{for(S.s();!(L=S.n()).done;){var T=L.value;null!=this.form.carrierIds&&this.form.carrierIds.includes(T.carrier.carrierId)&&!this.carrierIds.includes(T.serviceTypeId)&&this.carrierIds.push(T.serviceTypeId)}}catch(x){S.e(x)}finally{S.f()}}}}catch(x){k.e(x)}finally{k.f()}}}}},created:function(){var e=this;this.getList(),this.loadCarrier(),Object(p["e"])({pageNum:1,pageSize:100}).then((function(t){e.processtypeList=t.rows}))},methods:{getList:function(){var e=this;this.loading=!0,Object(o["e"])(this.queryParams).then((function(t){e.docList=t.rows,e.total=t.total,e.loading=!1}))},cancel:function(){this.open=!1,this.reset()},reset:function(){this.form={docId:null,docShortName:null,docLocalName:null,docEnName:null,processTypeId:null,cargoTypeIds:[],serviceTypeIds:[],locationDepartureIds:[],lineDepartureIds:[],locationDestinationIds:[],lineDestinationIds:[],carrierIds:[],docFlowDirectionIds:[],docIssueTypeIds:[],isGenerated:null,isIsolation:null,priority:null,orderNum:null,status:"0",remark:null,createBy:null,createTime:null,updateBy:null,updateTime:null,deleteBy:null,deleteTime:null,deleteStatus:"0"},this.carrierIds=[],this.resetForm("form")},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.resetForm("queryForm"),this.handleQuery()},handleStatusChange:function(e){var t=this,r="0"===e.status?"启用":"停用";this.$confirm('确认要"'+r+"吗？","提示",{customClass:"modal-confirm"}).then((function(){return Object(o["b"])(e.docId,e.status)})).then((function(){t.$modal.msgSuccess(r+"成功")})).catch((function(){e.status="0"===e.status?"1":"0"}))},handleSelectionChange:function(e){this.ids=e.map((function(e){return e.docId})),this.single=1!==e.length,this.multiple=!e.length},handleAdd:function(){this.reset(),this.open=!0,this.title="添加文件名称"},handleUpdate:function(e){var t=this;this.reset();var r=e.docId||this.ids;Object(o["d"])(r).then((function(e){t.form=e.data,t.locationOptions=e.locationOptions,t.open=!0,t.title="修改文件名称"}))},submitForm:function(){var e=this;this.$refs["form"].validate((function(t){t&&(null!=e.form.docId?Object(o["g"])(e.form).then((function(t){e.$modal.msgSuccess("修改成功"),e.open=!1,e.getList()})):Object(o["a"])(e.form).then((function(t){e.$modal.msgSuccess("新增成功"),e.open=!1,e.getList()})))}))},handleDelete:function(e){var t=this,r=e.docId||this.ids;this.$confirm('是否确认删除文件名称编号为"'+r+'"的数据项？',"提示",{customClass:"modal-confirm"}).then((function(){return Object(o["c"])(r)})).then((function(){t.getList(),t.$modal.msgSuccess("删除成功")})).catch((function(){}))},handleExport:function(){this.download("system/doc/export",Object(i["a"])({},this.queryParams),"doc_".concat((new Date).getTime(),".xlsx"))},carrierNormalizer:function(e){var t;return e.children&&!e.children.length&&delete e.children,t=!e.carrier||null==e.carrier.carrierLocalName&&null==e.carrier.carrierEnName?e.serviceLocalName+" "+e.serviceEnName+","+c.a.getFullChars(e.serviceLocalName):(null!=e.carrier.carrierIntlCode?e.carrier.carrierIntlCode:"")+" "+(null!=e.carrier.carrierEnName?e.carrier.carrierEnName:"")+" "+(null!=e.carrier.carrierLocalName?e.carrier.carrierLocalName:"")+","+c.a.getFullChars(null!=e.carrier.carrierLocalName?e.carrier.carrierLocalName:""),{id:e.serviceTypeId,label:t,children:e.children}},loadCarrier:function(){var e=this;0==this.$store.state.data.serviceTypeCarriers.length||this.$store.state.data.redisList.serviceTypeCarriers?d["a"].dispatch("getServiceTypeCarriersList").then((function(){e.carrierList=e.$store.state.data.serviceTypeCarriers})):this.carrierList=this.$store.state.data.serviceTypeCarriers},handleDeselectCarrierIds:function(e){this.form.carrierIds=this.form.carrierIds.filter((function(t){return t!=e.carrier.carrierId}))},handleSelectCarrierIds:function(e){this.form.carrierIds.push(e.carrier.carrierId)},getServiceTypeIds:function(e){this.form.serviceTypeIds=e,void 0==e&&(this.carrierIds=null,this.form.carrierIds=null)},getCargoTypeIds:function(e){this.form.cargoTypeIds=e},getLineDepartureIds:function(e){this.form.lineDepartureIds=e},getLocationDestinationIds:function(e){this.form.locationDestinationIds=e},getLocationDepartureIds:function(e){this.form.locationDepartureIds=e},getLineDestinationIds:function(e){this.form.lineDestinationIds=e}}},f=h,y=r("2877"),v=Object(y["a"])(f,a,l,!1,null,null,null);t["default"]=v.exports},f0c6:function(e,t,r){"use strict";r.d(t,"e",(function(){return l})),r.d(t,"d",(function(){return i})),r.d(t,"a",(function(){return s})),r.d(t,"f",(function(){return o})),r.d(t,"c",(function(){return n})),r.d(t,"b",(function(){return c}));var a=r("b775");function l(e){return Object(a["a"])({url:"/system/processtype/list",method:"get",params:e})}function i(e){return Object(a["a"])({url:"/system/processtype/"+e,method:"get"})}function s(e){return Object(a["a"])({url:"/system/processtype",method:"post",data:e})}function o(e){return Object(a["a"])({url:"/system/processtype",method:"put",data:e})}function n(e){return Object(a["a"])({url:"/system/processtype/"+e,method:"delete"})}function c(e,t){var r={processTypeId:e,status:t};return Object(a["a"])({url:"/system/processtype/changeStatus",method:"put",data:r})}}}]);