(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-41139fd0"],{"645b":function(e,t,r){"use strict";r("d4c0")},"7358a":function(e,t,r){"use strict";r.d(t,"e",(function(){return n})),r.d(t,"d",(function(){return l})),r.d(t,"a",(function(){return o})),r.d(t,"f",(function(){return i})),r.d(t,"c",(function(){return s})),r.d(t,"b",(function(){return c}));var a=r("b775");function n(e){return Object(a["a"])({url:"/system/clientsinfo/list",method:"get",params:e})}function l(e){return Object(a["a"])({url:"/system/clientsinfo/"+e,method:"get"})}function o(e){return Object(a["a"])({url:"/system/clientsinfo",method:"post",data:e})}function i(e){return Object(a["a"])({url:"/system/clientsinfo",method:"put",data:e})}function s(e){return Object(a["a"])({url:"/system/clientsinfo/"+e,method:"delete"})}function c(e,t){var r={clientsInfoId:e,status:t};return Object(a["a"])({url:"/system/clientsinfo/changeStatus",method:"put",data:r})}},"9cec":function(e,t,r){"use strict";r.r(t);var a=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",{staticClass:"app-container"},[r("el-row",{attrs:{gutter:20}},[r("el-col",{attrs:{span:e.showLeft}},[r("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],ref:"queryForm",staticClass:"query",attrs:{inline:!0,model:e.queryParams,"label-width":"68px",size:"mini"}},[r("el-form-item",{attrs:{label:"客户简称",prop:"clientId"}},[r("el-input",{attrs:{clearable:"",placeholder:"所属客户"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.clientId,callback:function(t){e.$set(e.queryParams,"clientId",t)},expression:"queryParams.clientId"}})],1),r("el-form-item",{attrs:{label:"速查标记",prop:"searchMark"}},[r("el-input",{attrs:{clearable:"",placeholder:"速查标记"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.searchMark,callback:function(t){e.$set(e.queryParams,"searchMark",t)},expression:"queryParams.searchMark"}})],1),r("el-form-item",{attrs:{label:"服务类型",prop:"serviceTypeId"}},[r("el-input",{attrs:{clearable:"",placeholder:"服务类型"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.serviceTypeId,callback:function(t){e.$set(e.queryParams,"serviceTypeId",t)},expression:"queryParams.serviceTypeId"}})],1),r("el-form-item",{attrs:{label:"装运区域",prop:"precarriageRegionId"}},[r("el-input",{attrs:{clearable:"",placeholder:"装运区域"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.precarriageRegionId,callback:function(t){e.$set(e.queryParams,"precarriageRegionId",t)},expression:"queryParams.precarriageRegionId"}})],1),r("el-form-item",{attrs:{label:"启运港",prop:"polId"}},[r("el-input",{attrs:{clearable:"",placeholder:"启运港"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.polId,callback:function(t){e.$set(e.queryParams,"polId",t)},expression:"queryParams.polId"}})],1),r("el-form-item",{attrs:{label:"目的港",prop:"destinationPortId"}},[r("el-input",{attrs:{clearable:"",placeholder:"目的港"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.destinationPortId,callback:function(t){e.$set(e.queryParams,"destinationPortId",t)},expression:"queryParams.destinationPortId"}})],1),r("el-form-item",{attrs:{label:"派送区域",prop:"dispatchRegionId"}},[r("el-input",{attrs:{clearable:"",placeholder:"派送区域"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.dispatchRegionId,callback:function(t){e.$set(e.queryParams,"dispatchRegionId",t)},expression:"queryParams.dispatchRegionId"}})],1),r("el-form-item",{attrs:{label:"发货人简称",prop:"shipperShortName"}},[r("el-input",{attrs:{clearable:"",placeholder:"发货人简称"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.shipperShortName,callback:function(t){e.$set(e.queryParams,"shipperShortName",t)},expression:"queryParams.shipperShortName"}})],1),r("el-form-item",{attrs:{label:"发货人明细",prop:"bookingShipper"}},[r("el-input",{attrs:{clearable:"",placeholder:"发货人明细"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.bookingShipper,callback:function(t){e.$set(e.queryParams,"bookingShipper",t)},expression:"queryParams.bookingShipper"}})],1),r("el-form-item",{attrs:{label:"收货人简称",prop:"consigneeShortName"}},[r("el-input",{attrs:{clearable:"",placeholder:"收货人简称"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.consigneeShortName,callback:function(t){e.$set(e.queryParams,"consigneeShortName",t)},expression:"queryParams.consigneeShortName"}})],1),r("el-form-item",{attrs:{label:"收货人明细",prop:"bookingConsignee"}},[r("el-input",{attrs:{clearable:"",placeholder:"收货人明细"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.bookingConsignee,callback:function(t){e.$set(e.queryParams,"bookingConsignee",t)},expression:"queryParams.bookingConsignee"}})],1),r("el-form-item",{attrs:{label:"通知人简称",prop:"notifyPartyShortName"}},[r("el-input",{attrs:{clearable:"",placeholder:"通知人简称"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.notifyPartyShortName,callback:function(t){e.$set(e.queryParams,"notifyPartyShortName",t)},expression:"queryParams.notifyPartyShortName"}})],1),r("el-form-item",{attrs:{label:"通知人明细",prop:"bookingNotifyParty"}},[r("el-input",{attrs:{clearable:"",placeholder:"通知人明细"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.bookingNotifyParty,callback:function(t){e.$set(e.queryParams,"bookingNotifyParty",t)},expression:"queryParams.bookingNotifyParty"}})],1),r("el-form-item",{attrs:{label:"装运详址",prop:"precarriageAddress"}},[r("el-input",{attrs:{clearable:"",placeholder:"装运详址"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.precarriageAddress,callback:function(t){e.$set(e.queryParams,"precarriageAddress",t)},expression:"queryParams.precarriageAddress"}})],1),r("el-form-item",{attrs:{label:"装运联系人",prop:"precarriageContact"}},[r("el-input",{attrs:{clearable:"",placeholder:"装运联系人"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.precarriageContact,callback:function(t){e.$set(e.queryParams,"precarriageContact",t)},expression:"queryParams.precarriageContact"}})],1),r("el-form-item",{attrs:{label:"装运电话",prop:"precarriageTel"}},[r("el-input",{attrs:{clearable:"",placeholder:"装运电话"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.precarriageTel,callback:function(t){e.$set(e.queryParams,"precarriageTel",t)},expression:"queryParams.precarriageTel"}})],1),r("el-form-item",{attrs:{label:"装运备注",prop:"precarriageRemark"}},[r("el-input",{attrs:{clearable:"",placeholder:"装运备注"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.precarriageRemark,callback:function(t){e.$set(e.queryParams,"precarriageRemark",t)},expression:"queryParams.precarriageRemark"}})],1),r("el-form-item",{attrs:{label:"派送详址",prop:"dispatchAddress"}},[r("el-input",{attrs:{clearable:"",placeholder:"派送详址"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.dispatchAddress,callback:function(t){e.$set(e.queryParams,"dispatchAddress",t)},expression:"queryParams.dispatchAddress"}})],1),r("el-form-item",{attrs:{label:"派送联系人",prop:"dispatchContact"}},[r("el-input",{attrs:{clearable:"",placeholder:"派送联系人"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.dispatchContact,callback:function(t){e.$set(e.queryParams,"dispatchContact",t)},expression:"queryParams.dispatchContact"}})],1),r("el-form-item",{attrs:{label:"派送电话",prop:"dispatchTel"}},[r("el-input",{attrs:{clearable:"",placeholder:"派送电话"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.dispatchTel,callback:function(t){e.$set(e.queryParams,"dispatchTel",t)},expression:"queryParams.dispatchTel"}})],1),r("el-form-item",{attrs:{label:"派送备注",prop:"dispatchRemark"}},[r("el-input",{attrs:{clearable:"",placeholder:"派送备注"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.dispatchRemark,callback:function(t){e.$set(e.queryParams,"dispatchRemark",t)},expression:"queryParams.dispatchRemark"}})],1),r("el-form-item",[r("el-button",{attrs:{icon:"el-icon-search",size:"mini",type:"primary"},on:{click:e.handleQuery}},[e._v("搜索")]),r("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1)],1),r("el-col",{attrs:{span:e.showRight}},[r("el-row",{staticClass:"mb8",attrs:{gutter:10}},[r("el-col",{attrs:{span:1.5}},[r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:clientsinfo:add"],expression:"['system:clientsinfo:add']"}],attrs:{icon:"el-icon-plus",plain:"",size:"mini",type:"primary"},on:{click:e.handleAdd}},[e._v("新增 ")])],1),r("el-col",{attrs:{span:1.5}},[r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:clientsinfo:edit"],expression:"['system:clientsinfo:edit']"}],attrs:{disabled:e.single,icon:"el-icon-edit",plain:"",size:"mini",type:"success"},on:{click:e.handleUpdate}},[e._v("修改 ")])],1),r("el-col",{attrs:{span:1.5}},[r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:clientsinfo:remove"],expression:"['system:clientsinfo:remove']"}],attrs:{disabled:e.multiple,icon:"el-icon-delete",plain:"",size:"mini",type:"danger"},on:{click:e.handleDelete}},[e._v("删除 ")])],1),r("el-col",{attrs:{span:1.5}},[r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:clientsinfo:export"],expression:"['system:clientsinfo:export']"}],attrs:{icon:"el-icon-download",plain:"",size:"mini",type:"warning"},on:{click:e.handleExport}},[e._v("导出 ")])],1),r("right-toolbar",{attrs:{showSearch:e.showSearch},on:{"update:showSearch":function(t){e.showSearch=t},"update:show-search":function(t){e.showSearch=t},queryTable:e.getList}})],1),r("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.clientsinfoList},on:{"selection-change":e.handleSelectionChange,"row-dblclick":e.dbclick}},[r("el-table-column",{attrs:{align:"center",type:"selection",width:"28"}}),r("el-table-column",{attrs:{align:"center",label:"所属客户",prop:"companyName"}}),r("el-table-column",{attrs:{align:"center",label:"速查标记",prop:"searchMark"}}),r("el-table-column",{attrs:{align:"center",label:"服务类型",prop:"serviceTypeId"}}),r("el-table-column",{attrs:{align:"center",label:"启运港",prop:"pol"}}),r("el-table-column",{attrs:{align:"center",label:"目的港",prop:"destinationPort"}}),"common"===e.commonUsedSelectData.type?r("el-table-column",{attrs:{align:"center",label:"发货人简称",prop:"shipperShortName"}}):e._e(),"common"===e.commonUsedSelectData.type?r("el-table-column",{attrs:{align:"center",label:"发货人明细",prop:"bookingShipper"}}):e._e(),"common"===e.commonUsedSelectData.type?r("el-table-column",{attrs:{align:"center",label:"收货人简称",prop:"consigneeShortName"}}):e._e(),"common"===e.commonUsedSelectData.type?r("el-table-column",{attrs:{align:"center",label:"收货人明细",prop:"bookingConsignee"}}):e._e(),"common"===e.commonUsedSelectData.type?r("el-table-column",{attrs:{align:"center",label:"通知人简称",prop:"notifyPartyShortName"}}):e._e(),"common"===e.commonUsedSelectData.type?r("el-table-column",{attrs:{align:"center",label:"通知人明细",prop:"bookingNotifyParty"}}):e._e(),"dispatch"===e.commonUsedSelectData.type?r("el-table-column",{attrs:{align:"center",label:"装运区域",prop:"precarriageRegionId"}}):e._e(),"dispatch"===e.commonUsedSelectData.type?r("el-table-column",{attrs:{align:"center",label:"装运详址",prop:"precarriageAddress"}}):e._e(),"dispatch"===e.commonUsedSelectData.type?r("el-table-column",{attrs:{align:"center",label:"装运联系人",prop:"precarriageContact"}}):e._e(),"dispatch"===e.commonUsedSelectData.type?r("el-table-column",{attrs:{align:"center",label:"装运电话",prop:"precarriageTel"}}):e._e(),"dispatch"===e.commonUsedSelectData.type?r("el-table-column",{attrs:{align:"center",label:"装运备注",prop:"precarriageRemark"}}):e._e(),e._e(),e._e(),e._e(),e._e(),e._e(),r("el-table-column",{attrs:{align:"center","class-name":"small-padding fixed-width",label:"操作"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:clientsinfo:edit"],expression:"['system:clientsinfo:edit']"}],staticStyle:{"margin-right":"-8px"},attrs:{icon:"el-icon-edit",size:"mini",type:"success"},on:{click:function(r){return e.handleUpdate(t.row)}}},[e._v("修改 ")]),r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:clientsinfo:remove"],expression:"['system:clientsinfo:remove']"}],staticStyle:{"margin-right":"-8px"},attrs:{icon:"el-icon-delete",size:"mini",type:"danger"},on:{click:function(r){return e.handleDelete(t.row)}}},[e._v("删除 ")])]}}])})],1),r("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total>0"}],attrs:{limit:e.queryParams.pageSize,page:e.queryParams.pageNum,total:e.total},on:{"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},pagination:e.getList}})],1)],1),r("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"},{name:"dialogDragWidth",rawName:"v-dialogDragWidth"}],attrs:{"close-on-click-modal":!1,"modal-append-to-body":!1,title:e.title,visible:e.open,"append-to-body":"",width:"500px"},on:{"update:visible":function(t){e.open=t}}},[r("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"80px"}},[r("el-form-item",{attrs:{label:"速查标记",prop:"searchMark"}},[r("el-input",{attrs:{placeholder:"速查标记"},model:{value:e.form.searchMark,callback:function(t){e.$set(e.form,"searchMark",t)},expression:"form.searchMark"}})],1),r("el-form-item",{attrs:{label:"服务类型",prop:"serviceTypeId"}},[r("el-input",{attrs:{placeholder:"服务类型"},model:{value:e.form.serviceTypeId,callback:function(t){e.$set(e.form,"serviceTypeId",t)},expression:"form.serviceTypeId"}})],1),r("el-form-item",{attrs:{label:"装运区域",prop:"precarriageRegionId"}},[r("el-input",{attrs:{placeholder:"装运区域"},model:{value:e.form.precarriageRegionId,callback:function(t){e.$set(e.form,"precarriageRegionId",t)},expression:"form.precarriageRegionId"}})],1),r("el-form-item",{attrs:{label:"启运港",prop:"polId"}},[r("el-input",{attrs:{placeholder:"启运港"},model:{value:e.form.polId,callback:function(t){e.$set(e.form,"polId",t)},expression:"form.polId"}})],1),r("el-form-item",{attrs:{label:"目的港",prop:"destinationPortId"}},[r("el-input",{attrs:{placeholder:"目的港"},model:{value:e.form.destinationPortId,callback:function(t){e.$set(e.form,"destinationPortId",t)},expression:"form.destinationPortId"}})],1),r("el-form-item",{attrs:{label:"派送区域",prop:"dispatchRegionId"}},[r("el-input",{attrs:{placeholder:"派送区域"},model:{value:e.form.dispatchRegionId,callback:function(t){e.$set(e.form,"dispatchRegionId",t)},expression:"form.dispatchRegionId"}})],1),r("el-form-item",{attrs:{label:"发货人简称",prop:"shipperShortName"}},[r("el-input",{attrs:{placeholder:"发货人简称"},model:{value:e.form.shipperShortName,callback:function(t){e.$set(e.form,"shipperShortName",t)},expression:"form.shipperShortName"}})],1),r("el-form-item",{attrs:{label:"发货人明细",prop:"bookingShipper"}},[r("el-input",{attrs:{placeholder:"发货人明细"},model:{value:e.form.bookingShipper,callback:function(t){e.$set(e.form,"bookingShipper",t)},expression:"form.bookingShipper"}})],1),r("el-form-item",{attrs:{label:"收货人简称",prop:"consigneeShortName"}},[r("el-input",{attrs:{placeholder:"收货人简称"},model:{value:e.form.consigneeShortName,callback:function(t){e.$set(e.form,"consigneeShortName",t)},expression:"form.consigneeShortName"}})],1),r("el-form-item",{attrs:{label:"收货人明细",prop:"bookingConsignee"}},[r("el-input",{attrs:{placeholder:"收货人明细"},model:{value:e.form.bookingConsignee,callback:function(t){e.$set(e.form,"bookingConsignee",t)},expression:"form.bookingConsignee"}})],1),r("el-form-item",{attrs:{label:"通知人简称",prop:"notifyPartyShortName"}},[r("el-input",{attrs:{placeholder:"通知人简称"},model:{value:e.form.notifyPartyShortName,callback:function(t){e.$set(e.form,"notifyPartyShortName",t)},expression:"form.notifyPartyShortName"}})],1),r("el-form-item",{attrs:{label:"通知人明细",prop:"bookingNotifyParty"}},[r("el-input",{attrs:{placeholder:"通知人明细"},model:{value:e.form.bookingNotifyParty,callback:function(t){e.$set(e.form,"bookingNotifyParty",t)},expression:"form.bookingNotifyParty"}})],1),r("el-form-item",{attrs:{label:"装运详址",prop:"precarriageAddress"}},[r("el-input",{attrs:{placeholder:"装运详址"},model:{value:e.form.precarriageAddress,callback:function(t){e.$set(e.form,"precarriageAddress",t)},expression:"form.precarriageAddress"}})],1),r("el-form-item",{attrs:{label:"装运联系人",prop:"precarriageContact"}},[r("el-input",{attrs:{placeholder:"装运联系人"},model:{value:e.form.precarriageContact,callback:function(t){e.$set(e.form,"precarriageContact",t)},expression:"form.precarriageContact"}})],1),r("el-form-item",{attrs:{label:"装运电话",prop:"precarriageTel"}},[r("el-input",{attrs:{placeholder:"装运电话"},model:{value:e.form.precarriageTel,callback:function(t){e.$set(e.form,"precarriageTel",t)},expression:"form.precarriageTel"}})],1),r("el-form-item",{attrs:{label:"装运备注",prop:"precarriageRemark"}},[r("el-input",{attrs:{placeholder:"装运备注"},model:{value:e.form.precarriageRemark,callback:function(t){e.$set(e.form,"precarriageRemark",t)},expression:"form.precarriageRemark"}})],1),r("el-form-item",{attrs:{label:"派送详址",prop:"dispatchAddress"}},[r("el-input",{attrs:{placeholder:"派送详址"},model:{value:e.form.dispatchAddress,callback:function(t){e.$set(e.form,"dispatchAddress",t)},expression:"form.dispatchAddress"}})],1),r("el-form-item",{attrs:{label:"派送联系人",prop:"dispatchContact"}},[r("el-input",{attrs:{placeholder:"派送联系人"},model:{value:e.form.dispatchContact,callback:function(t){e.$set(e.form,"dispatchContact",t)},expression:"form.dispatchContact"}})],1),r("el-form-item",{attrs:{label:"派送电话",prop:"dispatchTel"}},[r("el-input",{attrs:{placeholder:"派送电话"},model:{value:e.form.dispatchTel,callback:function(t){e.$set(e.form,"dispatchTel",t)},expression:"form.dispatchTel"}})],1),r("el-form-item",{attrs:{label:"派送备注",prop:"dispatchRemark"}},[r("el-input",{attrs:{placeholder:"派送备注"},model:{value:e.form.dispatchRemark,callback:function(t){e.$set(e.form,"dispatchRemark",t)},expression:"form.dispatchRemark"}})],1)],1),r("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[r("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("确 定")]),r("el-button",{on:{click:e.cancel}},[e._v("取 消")])],1)],1)],1)},n=[],l=r("5530"),o=(r("d81d"),r("7358a")),i={name:"commonUsedSelect",props:["commonUsedSelectData"],data:function(){return{showLeft:0,showRight:24,loading:!0,ids:[],single:!0,multiple:!0,showSearch:!0,total:0,clientsinfoList:[],title:"",open:!1,queryParams:{pageNum:1,pageSize:20,clientId:null,searchMark:null,serviceTypeId:null,precarriageRegionId:null,polId:null,destinationPortId:null,dispatchRegionId:null,shipperShortName:null,bookingShipper:null,consigneeShortName:null,bookingConsignee:null,notifyPartyShortName:null,bookingNotifyParty:null,precarriageAddress:null,precarriageContact:null,precarriageTel:null,precarriageRemark:null,dispatchAddress:null,dispatchContact:null,dispatchTel:null,dispatchRemark:null},form:{},rules:{}}},watch:{showSearch:function(e){!0===e?(this.showRight=21,this.showLeft=3):(this.showRight=24,this.showLeft=0)}},created:function(){this.getList()},methods:{dbclick:function(e){this.$emit("return",e)},getList:function(){var e=this;this.loading=!0,this.queryParams.clientId=this.commonUsedSelectData.clientId?this.commonUsedSelectData.clientId:null,Object(o["e"])(this.queryParams).then((function(t){e.clientsinfoList=t.rows,e.total=t.total,e.loading=!1}))},cancel:function(){this.open=!1,this.reset()},reset:function(){this.form={clientsInfoId:null,clientId:null,searchMark:null,serviceTypeId:null,precarriageRegionId:null,polId:null,destinationPortId:null,dispatchRegionId:null,shipperShortName:null,bookingShipper:null,consigneeShortName:null,bookingConsignee:null,notifyPartyShortName:null,bookingNotifyParty:null,precarriageAddress:null,precarriageContact:null,precarriageTel:null,precarriageRemark:null,dispatchAddress:null,dispatchContact:null,dispatchTel:null,dispatchRemark:null},this.resetForm("form")},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.resetForm("queryForm"),this.handleQuery()},handleStatusChange:function(e){var t=this,r="0"===e.status?"启用":"停用";this.$confirm('确认要"'+r+"吗？","提示",{customClass:"modal-confirm"}).then((function(){return Object(o["b"])(e.clientsInfoId,e.status)})).then((function(){t.$modal.msgSuccess(r+"成功")})).catch((function(){e.status="0"===e.status?"1":"0"}))},handleSelectionChange:function(e){this.ids=e.map((function(e){return e.clientsInfoId})),this.single=1!==e.length,this.multiple=!e.length},handleAdd:function(){this.reset(),this.open=!0,this.title="添加用来记录客户常用的信息，避免重复劳动、错漏"},handleUpdate:function(e){var t=this;this.reset();var r=e.clientsInfoId||this.ids;Object(o["d"])(r).then((function(e){t.form=e.data,t.open=!0,t.title="修改用来记录客户常用的信息，避免重复劳动、错漏"}))},submitForm:function(){var e=this;this.$refs["form"].validate((function(t){t&&(null!=e.form.clientsInfoId?Object(o["f"])(e.form).then((function(t){e.$modal.msgSuccess("修改成功"),e.open=!1,e.getList()})):Object(o["a"])(e.form).then((function(t){e.$modal.msgSuccess("新增成功"),e.open=!1,e.getList()})))}))},handleDelete:function(e){var t=this,r=e.clientsInfoId||this.ids;this.$confirm('是否确认删除用来记录客户常用的信息，避免重复劳动、错漏编号为"'+r+'"的数据项？',"提示",{customClass:"modal-confirm"}).then((function(){return Object(o["c"])(r)})).then((function(){t.getList(),t.$modal.msgSuccess("删除成功")})).catch((function(){}))},handleExport:function(){this.download("system/clientsinfo/export",Object(l["a"])({},this.queryParams),"clientsinfo_".concat((new Date).getTime(),".xlsx"))}}},s=i,c=(r("645b"),r("2877")),p=Object(c["a"])(s,a,n,!1,null,"d7791090",null);t["default"]=p.exports},d4c0:function(e,t,r){}}]);