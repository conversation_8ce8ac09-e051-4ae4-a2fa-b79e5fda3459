{"remainingRequest": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js??vue-loader-options!C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\inventory\\index.vue?vue&type=script&lang=js&", "dependencies": [{"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\src\\views\\system\\inventory\\index.vue", "mtime": 1750818094548}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\babel-loader\\lib\\index.js", "mtime": 456789000000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\cache-loader\\dist\\cjs.js", "mtime": 499162500000}, {"path": "C:\\Users\\<USER>\\IdeaProjects\\rich-test\\rich-ui\\node_modules\\vue-loader\\lib\\index.js", "mtime": 499162500000}], "contextDependencies": [], "result": [{"type": "<PERSON><PERSON><PERSON>", "data": "base64:Ly8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KLy8KCmltcG9ydCB7CiAgYWRkSW52ZW50b3J5LCBjYW5jZWxQa2csCiAgY2hhbmdlU3RhdHVzLAogIGRlbEludmVudG9yeSwKICBnZXRJbnZlbnRvcnksIGdldFBhY2thZ2UsIGxpc3RBZ2dyZWdhdG9yUnNJbnZlbnRvcnksCiAgbGlzdEludmVudG9yeSwgcGFja1VwLAogIHVwZGF0ZUludmVudG9yeQp9IGZyb20gIkAvYXBpL3N5c3RlbS9pbnZlbnRvcnkiCmltcG9ydCB7ZGVmYXVsdEVsZW1lbnRUeXBlUHJvdmlkZXIsIGhpcHJpbnR9IGZyb20gIkAiCmltcG9ydCB3YXJlaG91c2VSZWNlaXB0IGZyb20gIkAvcHJpbnQtdGVtcGxhdGUvd2FyZWhvdXNlUmVjZWlwdCIKaW1wb3J0IHdhcmVob3VzZVJlY2VpcHROZXcgZnJvbSAiQC9wcmludC10ZW1wbGF0ZS93YXJlaG91c2VSZWNlaXB0TmV3IgppbXBvcnQgcHJpbnRQcmV2aWV3IGZyb20gIkAvdmlld3MvcHJpbnQvZGVtby9kZXNpZ24vcHJldmlldy52dWUiCmltcG9ydCBQcmludFRlbXBsYXRlIGZyb20gIkAvdmlld3Mvc3lzdGVtL3ByaW50L1ByaW50VGVtcGxhdGUudnVlIgppbXBvcnQgY3VycmVuY3kgZnJvbSAiY3VycmVuY3kuanMiCmltcG9ydCBsb2cgZnJvbSAiQC92aWV3cy9tb25pdG9yL2pvYi9sb2cudnVlIgppbXBvcnQgbW9tZW50IGZyb20gIm1vbWVudC9tb21lbnQiCmltcG9ydCBzdG9yZSBmcm9tICJAL3N0b3JlIgppbXBvcnQge3BhcnNlVGltZX0gZnJvbSAiLi4vLi4vLi4vdXRpbHMvcmljaCIKaW1wb3J0IERhdGFBZ2dyZWdhdG9yQmFja0dyb3VuZCBmcm9tICJAL3ZpZXdzL3N5c3RlbS9EYXRhQWdncmVnYXRvckJhY2tHcm91bmQvaW5kZXgudnVlIgppbXBvcnQge3JjdEZpZWxkTGFiZWxNYXB9IGZyb20gIkAvY29uZmlnL3JjdEZpZWxkTGFiZWxNYXAiCmltcG9ydCB7cnNJbnZlbnRvcnlGaWVsZExhYmVsTWFwfSBmcm9tICJAL2NvbmZpZy9yc0ludmVudG9yeUZpZWxkTGFiZWxNYXAiCmltcG9ydCB7bGlzdEFnZ3JlZ2F0b3JSY3R9IGZyb20gIkAvYXBpL3N5c3RlbS9yY3QiCmltcG9ydCB7Z2V0VG9rZW59IGZyb20gIkAvdXRpbHMvYXV0aCIKCmxldCBoaXByaW50VGVtcGxhdGUKZXhwb3J0IGRlZmF1bHQgewogIG5hbWU6ICJJbnZlbnRvcnkiLAogIGRhdGEoKSB7CiAgICBjb25zdCB2YWxpZGF0ZUluYm91bmRTZXJpYWxObyA9IChydWxlLCB2YWx1ZSwgY2FsbGJhY2spID0+IHsKICAgICAgY29uc29sZS5sb2codmFsdWUpCiAgICAgIC8qIGlmICh2YWx1ZSA9PT0gJycpIHsKICAgICAgICBjYWxsYmFjayhuZXcgRXJyb3IoJ+ivt+i+k+WFpea1geawtOWPtycpKTsKICAgICAgfSBlbHNlIHsKICAgICAgICBpZiAodGhpcy5mb3JtLmluYm91bmRTZXJpYWxOb1N1YiAhPT0gJycpIHsKICAgICAgICAgIHRoaXMuJHJlZnMuZm9ybS52YWxpZGF0ZUZpZWxkKCdpbmJvdW5kU2VyaWFsTm8nKTsKICAgICAgICB9CiAgICAgICAgY2FsbGJhY2soKTsKICAgICAgfSAqLwogICAgfQogICAgcmV0dXJuIHsKICAgICAgY2FyZ29EZXRhaWxPcGVuOiBmYWxzZSwKICAgICAgY2FyZ29EZXRhaWxSb3c6IHsKICAgICAgICBjYXJnb0RldGFpbHNJZDogbnVsbCwKICAgICAgICBpbmJvdW5kU2VyaWFsTm86IG51bGwsCiAgICAgICAgaW5ib3VuZFNlcmlhbFNwbGl0OiBudWxsLAogICAgICAgIGNsaWVudENvZGU6IG51bGwsCiAgICAgICAgc2hpcHBpbmdNYXJrOiBudWxsLAogICAgICAgIGl0ZW1OYW1lOiBudWxsLAogICAgICAgIGJveENvdW50OiBudWxsLAogICAgICAgIGJveEl0ZW1Db3VudDogbnVsbCwKICAgICAgICBzdWJ0b3RhbEl0ZW1Db3VudDogbnVsbCwKICAgICAgICBleHByZXNzRGF0ZTogbW9tZW50KCkuZm9ybWF0KCJ5eXl5LU1NLUREIiksCiAgICAgICAgYWRkaXRpb25hbEZlZTogbnVsbCwKICAgICAgICBwYWNrYWdlVHlwZTogIue6uOeusSIsCiAgICAgICAgdW5pdEdyb3NzV2VpZ2h0OiBudWxsLAogICAgICAgIHVuaXRMZW5ndGg6IG51bGwsCiAgICAgICAgdW5pdFdpZHRoOiBudWxsLAogICAgICAgIHVuaXRIZWlnaHQ6IG51bGwsCiAgICAgICAgdW5pdFZvbHVtZTogbnVsbCwKICAgICAgICBkYW1hZ2VTdGF0dXM6ICIwIiwKICAgICAgICBiYXJjb2RlOiBudWxsCiAgICAgIH0sCiAgICAgIHNob3dMZWZ0OiAwLAogICAgICBzaG93UmlnaHQ6IDI0LAogICAgICAvLyDpga7nvanlsYIKICAgICAgbG9hZGluZzogdHJ1ZSwKICAgICAgLy8g6YCJ5Lit5pWw57uECiAgICAgIGlkczogW10sCiAgICAgIGFnZ3JlZ2F0b3JSY3RMaXN0OiBbXSwKICAgICAgZmllbGRMYWJlbE1hcDogcnNJbnZlbnRvcnlGaWVsZExhYmVsTWFwLAogICAgICAvLyDpnZ7ljZXkuKrnpoHnlKgKICAgICAgc2luZ2xlOiB0cnVlLAogICAgICBvcGVuQWdncmVnYXRvcjogZmFsc2UsCiAgICAgIC8vIOmdnuWkmuS4quemgeeUqAogICAgICBtdWx0aXBsZTogdHJ1ZSwKICAgICAgc2VsZWN0ZWRJbnZlbnRvcnlMaXN0OiBbXSwKICAgICAgc2VsZWN0ZWRQa2dMaXN0OiBbXSwKICAgICAgLy8g5pi+56S65pCc57Si5p2h5Lu2CiAgICAgIHNob3dTZWFyY2g6IGZhbHNlLAogICAgICAvLyDmgLvmnaHmlbAKICAgICAgdG90YWw6IDAsCiAgICAgIC8vIOW6k+WtmOihqOagvOaVsOaNrgogICAgICBpbnZlbnRvcnlMaXN0OiBbXSwKICAgICAgb3V0Ym91bmRMaXN0OiBbXSwKICAgICAgLy8g5by55Ye65bGC5qCH6aKYCiAgICAgIHRpdGxlOiAiIiwKICAgICAgb3BlblBrZ1RvOiBmYWxzZSwKCiAgICAgIG91dGJvdW5kVHlwZTogbnVsbCwKCiAgICAgIC8vIOaYr+WQpuaYvuekuuW8ueWHuuWxggogICAgICBvcGVuOiBmYWxzZSwKICAgICAgb3BlblBrZzogZmFsc2UsCiAgICAgIHBrZ0xpc3Q6IFtdLAogICAgICAvLyDnlKjmiLflr7zlhaXlj4LmlbAKICAgICAgdXBsb2FkOiB7CiAgICAgICAgLy8g5piv5ZCm5pi+56S65by55Ye65bGC77yI55So5oi35a+85YWl77yJCiAgICAgICAgb3BlbjogZmFsc2UsCiAgICAgICAgLy8g5by55Ye65bGC5qCH6aKY77yI55So5oi35a+85YWl77yJCiAgICAgICAgdGl0bGU6ICIiLAogICAgICAgIC8vIOaYr+WQpuemgeeUqOS4iuS8oAogICAgICAgIGlzVXBsb2FkaW5nOiBmYWxzZSwKICAgICAgICAvLyDmmK/lkKbmm7TmlrDlt7Lnu4/lrZjlnKjnmoTnlKjmiLfmlbDmja4KICAgICAgICB1cGRhdGVTdXBwb3J0OiB0cnVlLAogICAgICAgIC8vIOiuvue9ruS4iuS8oOeahOivt+axguWktOmDqAogICAgICAgIGhlYWRlcnM6IHtBdXRob3JpemF0aW9uOiAiQmVhcmVyICIgKyBnZXRUb2tlbigpfSwKICAgICAgICAvLyDkuIrkvKDnmoTlnLDlnYAKICAgICAgICB1cmw6IHByb2Nlc3MuZW52LlZVRV9BUFBfQkFTRV9BUEkgKyAiL3N5c3RlbS9pbnZlbnRvcnkvaW1wb3J0RGF0YSIKICAgICAgfSwKICAgICAgLy8g5p+l6K+i5Y+C5pWwCiAgICAgIHF1ZXJ5UGFyYW1zOiB7CiAgICAgICAgcGFnZU51bTogMSwKICAgICAgICBwYWdlU2l6ZTogMjAsCiAgICAgICAgaW52ZW50b3J5U3RhdHVzOiBudWxsLAogICAgICAgIGluYm91bmRTZXJpYWxObzogbnVsbCwKICAgICAgICBpbmJvdW5kU2VyaWFsU3BsaXQ6IG51bGwsCiAgICAgICAgaW5ib3VuZERhdGU6IG51bGwsCiAgICAgICAgb3V0Ym91bmRObzogbnVsbCwKICAgICAgICBmb3J3YXJkZXJObzogbnVsbCwKICAgICAgICByZW50YWxTZXR0bGVtZW50RGF0ZTogbnVsbCwKICAgICAgICBvdXRib3VuZERhdGU6IG51bGwsCiAgICAgICAgY2xpZW50Q29kZTogbnVsbCwKICAgICAgICBzdWJPcmRlck5vOiBudWxsLAogICAgICAgIHN1cHBsaWVyOiBudWxsLAogICAgICAgIGRyaXZlckluZm86IG51bGwsCiAgICAgICAgc3FkU2hpcHBpbmdNYXJrOiBudWxsLAogICAgICAgIGNhcmdvTmFtZTogbnVsbCwKICAgICAgICB0b3RhbEJveGVzOiBudWxsLAogICAgICAgIHBhY2thZ2VUeXBlOiBudWxsLAogICAgICAgIHRvdGFsR3Jvc3NXZWlnaHQ6IG51bGwsCiAgICAgICAgdG90YWxWb2x1bWU6IG51bGwsCiAgICAgICAgZGFtYWdlU3RhdHVzOiBudWxsLAogICAgICAgIHN0b3JhZ2VMb2NhdGlvbjE6IG51bGwsCiAgICAgICAgc3RvcmFnZUxvY2F0aW9uMjogbnVsbCwKICAgICAgICBzdG9yYWdlTG9jYXRpb24zOiBudWxsLAogICAgICAgIHJlY2VpdmVkU3RvcmFnZUZlZTogbnVsbCwKICAgICAgICB1bnBhaWRVbmxvYWRpbmdGZWU6IG51bGwsCiAgICAgICAgbG9naXN0aWNzQWR2YW5jZUZlZTogbnVsbCwKICAgICAgICByZW50YWxCYWxhbmNlRmVlOiBudWxsLAogICAgICAgIGZyZWVTdGFja1BlcmlvZDogbnVsbCwKICAgICAgICBvdmVyZHVlUmVudGFsVW5pdFByaWNlOiBudWxsLAogICAgICAgIG92ZXJkdWVSZW50YWxGZWU6IG51bGwsCiAgICAgICAgbm90ZXM6IG51bGwsCiAgICAgICAgaXNUb3BMZXZlbDogdHJ1ZSAvLyDpu5jorqTlj6rmn6Xor6LpobblsYLmlbDmja4KICAgICAgfSwKICAgICAgLy8g6KGo5Y2V5Y+C5pWwCiAgICAgIGZvcm06IHt9LAogICAgICBwa2dEZXRhaWxzTGlzdDogW10sCiAgICAgIC8vIOihqOWNleagoemqjAogICAgICBydWxlczogewogICAgICAgIC8vIGNhcmdvTmFtZTogWwogICAgICAgIC8vICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuivt+i+k+WFpei0p+eJqeaPj+i/sCIsIHRyaWdnZXI6ICJibHVyIiB9CiAgICAgICAgLy8gXSwKICAgICAgICAvLyBjb25zaWduZWVOYW1lOiBbCiAgICAgICAgLy8gICB7IHJlcXVpcmVkOiB0cnVlLCBtZXNzYWdlOiAi6K+36L6T5YWl5pS26LSn5Lq65ZCN56ewIiwgdHJpZ2dlcjogImJsdXIiIH0KICAgICAgICAvLyBdLAogICAgICAgIC8vIGNvbnNpZ25lZVRlbDogWwogICAgICAgIC8vICAgeyByZXF1aXJlZDogdHJ1ZSwgbWVzc2FnZTogIuivt+i+k+WFpeaUtui0p+S6uueUteivnSIsIHRyaWdnZXI6ICJibHVyIiB9CiAgICAgICAgLy8gXSwKICAgICAgICBzdWJPcmRlck5vOiBbCiAgICAgICAgICB7CiAgICAgICAgICAgIHZhbGlkYXRvcjogKHJ1bGUsIHZhbHVlLCBjYWxsYmFjaykgPT4gewogICAgICAgICAgICAgIGlmICghdmFsdWUpIHsKICAgICAgICAgICAgICAgIGNhbGxiYWNrKCkKICAgICAgICAgICAgICAgIHJldHVybgogICAgICAgICAgICAgIH0KICAgICAgICAgICAgICBpZiAoIXRoaXMuZm9ybS5jbGllbnRDb2RlKSB7CiAgICAgICAgICAgICAgICBjYWxsYmFjayhuZXcgRXJyb3IoIuivt+WFiOmAieaLqeWuouaIt+S7o+eggSIpKQogICAgICAgICAgICAgICAgcmV0dXJuCiAgICAgICAgICAgICAgfQogICAgICAgICAgICAgIGNvbnN0IHByZWZpeCA9IGAke3RoaXMuZm9ybS5jbGllbnRDb2RlfS1gCiAgICAgICAgICAgICAgaWYgKCF2YWx1ZS5zdGFydHNXaXRoKHByZWZpeCkpIHsKICAgICAgICAgICAgICAgIGNhbGxiYWNrKG5ldyBFcnJvcihg5YiG5Y2V5Y+35b+F6aG75LulICR7cHJlZml4fSDlvIDlpLRgKSkKICAgICAgICAgICAgICAgIHJldHVybgogICAgICAgICAgICAgIH0KICAgICAgICAgICAgICBjYWxsYmFjaygpCiAgICAgICAgICAgIH0sCiAgICAgICAgICAgIHRyaWdnZXI6ICJibHVyIgogICAgICAgICAgfQogICAgICAgIF0KICAgICAgfSwKICAgICAgc2VsZWN0ZWRDbGllbnQ6IG51bGwKICAgIH0KICB9LAogIGNvbXBvbmVudHM6IHsKICAgIERhdGFBZ2dyZWdhdG9yQmFja0dyb3VuZCwKICAgIHByaW50UHJldmlldywKICAgIFByaW50VGVtcGxhdGUKICB9LAogIHdhdGNoOiB7CiAgICBzaG93U2VhcmNoKG4pIHsKICAgICAgaWYgKG4gPT09IHRydWUpIHsKICAgICAgICB0aGlzLnNob3dSaWdodCA9IDIxCiAgICAgICAgdGhpcy5zaG93TGVmdCA9IDMKICAgICAgfSBlbHNlIHsKICAgICAgICB0aGlzLnNob3dSaWdodCA9IDI0CiAgICAgICAgdGhpcy5zaG93TGVmdCA9IDAKICAgICAgfQogICAgfQogIH0sCiAgbW91bnRlZCgpIHsKICAgIHRoaXMuaW5pdFByaW50KCkKICB9LAogIGNyZWF0ZWQoKSB7CiAgICB0aGlzLmdldExpc3QoKQogIH0sCiAgbWV0aG9kczogewogICAgLy8g5o+Q5Lqk5LiK5Lyg5paH5Lu2CiAgICBzdWJtaXRGaWxlRm9ybSgpIHsKICAgICAgdGhpcy4kcmVmcy51cGxvYWQuc3VibWl0KCkKICAgIH0sCiAgICAvLyDmlofku7bkuIrkvKDkuK3lpITnkIYKICAgIGhhbmRsZUZpbGVVcGxvYWRQcm9ncmVzcyhldmVudCwgZmlsZSwgZmlsZUxpc3QpIHsKICAgICAgdGhpcy51cGxvYWQuaXNVcGxvYWRpbmcgPSB0cnVlCiAgICB9LAogICAgLy8g5paH5Lu25LiK5Lyg5oiQ5Yqf5aSE55CGCiAgICBoYW5kbGVGaWxlU3VjY2VzcyhyZXNwb25zZSwgZmlsZSwgZmlsZUxpc3QpIHsKICAgICAgdGhpcy51cGxvYWQub3BlbiA9IGZhbHNlCiAgICAgIHRoaXMudXBsb2FkLmlzVXBsb2FkaW5nID0gZmFsc2UKICAgICAgdGhpcy4kcmVmcy51cGxvYWQuY2xlYXJGaWxlcygpCiAgICAgIHRoaXMuJG1lc3NhZ2UuaW5mbyhyZXNwb25zZS5tc2cpCiAgICAgIGlmIChyZXNwb25zZS5tc2cgIT0gIuWFqOmDqOS4iuS8oOaIkOWKnyIpIHsKICAgICAgICB0aGlzLmRvd25sb2FkKCJzeXN0ZW0vaW52ZW50b3J5L2ZhaWxMaXN0Iiwge30sIGDkuIrkvKDlpLHotKXliJfooagueGxzeGApCiAgICAgIH0KICAgICAgdGhpcy5nZXRMaXN0KCkKICAgIH0sCiAgICBsb2FkUGtnRGV0YWlsKCkgewogICAgICBpZiAodGhpcy5mb3JtLmNsaWVudENvZGUpIHsKICAgICAgICBsaXN0SW52ZW50b3J5KHtwYWNrYWdlVG86IHRoaXMuZm9ybS5pbnZlbnRvcnlJZH0pLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgICAgdGhpcy5wa2dEZXRhaWxzTGlzdCA9IHJlc3BvbnNlLnJvd3MKICAgICAgICB9KQogICAgICB9CiAgICB9LAogICAgLy8g5Yqg6L295a2Q6IqC54K55pWw5o2uCiAgICBsb2FkQ2hpbGRJbnZlbnRvcnkodHJlZSwgdHJlZU5vZGUsIHJlc29sdmUpIHsKICAgICAgLy8g5L2/55SocGFja2FnZVRv5a2X5q615p+l6K+i5a2Q6IqC54K5CiAgICAgIGxpc3RJbnZlbnRvcnkoe3BhY2thZ2VUbzogdHJlZS5pbnZlbnRvcnlJZH0pLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgIGNvbnN0IHJvd3MgPSByZXNwb25zZS5yb3dzCgogICAgICAgIC8vIOWFiOWwhuaVsOaNruS8oOmAkue7meihqOagvO+8jOehruS/neWtkOiKgueCuea4suafkwogICAgICAgIHJlc29sdmUocm93cykKICAgICAgICB0cmVlLmNoaWxkcmVuID0gcm93cwoKICAgICAgICAvLyDlpoLmnpzniLbpobnooqvpgInkuK3vvIzlnKjlrZDoioLngrnmuLLmn5PlrozmiJDlkI7pgInkuK3lroPku6wKICAgICAgICBpZiAodGhpcy5pZHMuaW5jbHVkZXModHJlZS5pbnZlbnRvcnlJZCkpIHsKICAgICAgICAgIHNldFRpbWVvdXQoKCkgPT4gewogICAgICAgICAgICByb3dzLmZvckVhY2goY2hpbGQgPT4gewogICAgICAgICAgICAgIGlmICghdGhpcy5pZHMuaW5jbHVkZXMoY2hpbGQuaW52ZW50b3J5SWQpKSB7CiAgICAgICAgICAgICAgICB0aGlzLmlkcy5wdXNoKGNoaWxkLmludmVudG9yeUlkKQogICAgICAgICAgICAgICAgdGhpcy5zZWxlY3RlZEludmVudG9yeUxpc3QucHVzaChjaGlsZCkKICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgLy8g5ZyoVUnkuIrpgInkuK3lrZDpobkKICAgICAgICAgICAgICB0aGlzLiRyZWZzLmludmVudG9yeVRhYmxlLnRvZ2dsZVJvd1NlbGVjdGlvbihjaGlsZCwgdHJ1ZSkKICAgICAgICAgICAgfSkKICAgICAgICAgIH0sIDUwKSAvLyDnrYnlvoVET03mm7TmlrAKICAgICAgICB9CiAgICAgIH0pCiAgICB9LAogICAgaGFuZGxlQWRkUGtnKCkgewogICAgICB0aGlzLnJlc2V0KCkKICAgICAgdGhpcy5vcGVuUGtnID0gdHJ1ZQogICAgfSwKICAgIGhhbmRsZVBhY2tpbmdUbygpIHsKICAgICAgLy8g5qOA5p+l5piv5ZCm6YCJ5oup5LqG6LSn54mpCiAgICAgIGlmICh0aGlzLmlkcy5sZW5ndGggPT09IDApIHsKICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoIuivt+WFiOmAieaLqemcgOimgeaJk+WMheeahOi0p+eJqSIpCiAgICAgICAgcmV0dXJuCiAgICAgIH0KCiAgICAgIC8vIOajgOafpeaYr+WQpuS4uuWQjOS4gOWuouaIt+eahOi0p+eJqQogICAgICBjb25zdCBmaXJzdENsaWVudENvZGUgPSB0aGlzLnNlbGVjdGVkSW52ZW50b3J5TGlzdFswXS5jbGllbnRDb2RlCiAgICAgIGNvbnN0IGlzU2FtZUNsaWVudCA9IHRoaXMuc2VsZWN0ZWRJbnZlbnRvcnlMaXN0LmV2ZXJ5KGl0ZW0gPT4gaXRlbS5jbGllbnRDb2RlID09PSBmaXJzdENsaWVudENvZGUpCiAgICAgIGNvbnN0IGlzUGFja2FnZWQgPSB0aGlzLnNlbGVjdGVkSW52ZW50b3J5TGlzdC5ldmVyeShpdGVtID0+IGl0ZW0ucGFja2FnZUludG9ObyA9PSBudWxsKQoKICAgICAgaWYgKCFpc1NhbWVDbGllbnQpIHsKICAgICAgICB0aGlzLiRtZXNzYWdlLndhcm5pbmcoIuWPquiDveaJk+WMheWQjOS4gOS4quWuouaIt+eahOi0p+eJqSIpCiAgICAgICAgcmV0dXJuCiAgICAgIH0KICAgICAgaWYgKCFpc1BhY2thZ2VkKSB7CiAgICAgICAgdGhpcy4kbWVzc2FnZS53YXJuaW5nKCLmnInotKfnianlt7LooqvmiZPljIUiKQogICAgICAgIHJldHVybgogICAgICB9CgogICAgICAvLyDmiZPlvIDmiZPljIXnrrHpgInmi6nlr7nor53moYYKICAgICAgdGhpcy5yZXNldCgpCiAgICAgIHRoaXMuZm9ybS5jbGllbnRDb2RlID0gZmlyc3RDbGllbnRDb2RlCiAgICAgIHRoaXMuZm9ybS5jbGllbnROYW1lID0gdGhpcy5zZWxlY3RlZEludmVudG9yeUxpc3RbMF0uY2xpZW50TmFtZQogICAgICB0aGlzLmZvcm0ucmVwYWNraW5nU3RhdHVzID0gIuaJk+WMheS4rSIKCiAgICAgIC8vIOS9v+eUqOWHhuehrueahOW9k+WJjeaXtumXtO+8jOS4jei/m+ihjOWPluaVtAogICAgICBjb25zdCBub3cgPSBuZXcgRGF0ZSgpCiAgICAgIHRoaXMuZm9ybS5hY3R1YWxJbmJvdW5kVGltZSA9IG5vdwoKICAgICAgdGhpcy5mb3JtLmludmVudG9yeVN0YXR1cyA9ICIwIgogICAgICB0aGlzLmZvcm0uc3ViT3JkZXJObyA9IGZpcnN0Q2xpZW50Q29kZSArICItIgoKICAgICAgLy8g6K6w5b2V6KKr5omT5YyF55qE6LSn54mpSUQKICAgICAgdGhpcy5mb3JtLnBhY2tpbmdTb3VyY2VJZHMgPSB0aGlzLmlkcwoKICAgICAgdGhpcy50aXRsZSA9ICLmiZPljIXoo4XnrrHoh7MiCiAgICAgIHRoaXMub3BlblBrZ1RvID0gdHJ1ZQogICAgfSwKICAgIHBrZ0NhbmNlbCgpIHsKICAgICAgY2FuY2VsUGtnKHRoaXMuc2VsZWN0ZWRQa2dMaXN0KS50aGVuKHJlc3BvbnNlID0+IHsKICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoIuenu+WHuuaIkOWKnyIpCiAgICAgICAgdGhpcy5vcGVuUGtnID0gZmFsc2UKICAgICAgICB0aGlzLmdldExpc3QoKQogICAgICB9KQogICAgfSwKICAgIGxvYWRQa2dUb0xpc3QoKSB7CiAgICAgIGdldFBhY2thZ2Uoe2NsaWVudENvZGU6IHRoaXMuZm9ybS5jbGllbnRDb2RlLCByZXBhY2tpbmdTdGF0dXM6ICLmiZPljIXkuK0ifSkudGhlbihyZXNwb25zZSA9PiB7CiAgICAgICAgdGhpcy5wa2dMaXN0ID0gcmVzcG9uc2UuZGF0YQogICAgICB9KQogICAgfSwKICAgIHBhY2tpbmdUbygpIHsKICAgICAgdGhpcy5zZWxlY3RlZEludmVudG9yeUxpc3QuZm9yRWFjaChpdGVtID0+IHsKICAgICAgICBpdGVtLnBhY2thZ2VUbyA9IHRoaXMuZm9ybS5wYWNrYWdlVG8KICAgICAgICBpdGVtLnJlcGFja2luZ1N0YXR1cyA9ICLooqvmiZPljIUiCiAgICAgIH0pCgogICAgICBwYWNrVXAodGhpcy5zZWxlY3RlZEludmVudG9yeUxpc3QpLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgIHRoaXMuJG1lc3NhZ2Uuc3VjY2Vzcygi5omT5YyF5oiQ5YqfIikKICAgICAgICB0aGlzLm9wZW5Qa2dUbyA9IGZhbHNlCiAgICAgICAgdGhpcy5nZXRMaXN0KCkKICAgICAgfSkKICAgIH0sCiAgICBwYXJzZVRpbWUsCiAgICBoYW5kbGVCbHVyKCkgewogICAgICAvLyDliKTmlq3plb/luqbmmK/lkKblsI/kuo405L2N77yM6Iul5piv5YiZ6KGl6b2QMAogICAgICB0aGlzLmZvcm0uaW5ib3VuZFNlcmlhbE5vU3ViID0gdGhpcy5mb3JtLmluYm91bmRTZXJpYWxOb1N1Yi5wYWRTdGFydCg0LCAiMCIpCiAgICB9LAogICAgc2VsZWN0SW5ib3VuZEZlZSgpIHsKICAgICAgc3dpdGNoICh0aGlzLmZvcm0ucmVjb3JkVHlwZSkgewogICAgICAgIGNhc2UgIuagh+WHhiI6CiAgICAgICAgICB0aGlzLmZvcm0uaW5ib3VuZEZlZSA9IHRoaXMuc2VsZWN0ZWRDbGllbnQuc3RhbmRhcmRJbmJvdW5kRmVlCiAgICAgICAgICBicmVhawogICAgICAgIGNhc2UgIueyvuehriI6CiAgICAgICAgICB0aGlzLmZvcm0uaW5ib3VuZEZlZSA9IHRoaXMuc2VsZWN0ZWRDbGllbnQucHJlY2lzZUluYm91bmRGZWUKICAgICAgICAgIGJyZWFrCiAgICAgICAgY2FzZSAi5b+r6YCSIjoKICAgICAgICAgIHRoaXMuZm9ybS5pbmJvdW5kRmVlID0gdGhpcy5zZWxlY3RlZENsaWVudC5leHByZXNzSW5ib3VuZEZlZQogICAgICAgICAgYnJlYWsKICAgICAgfQogICAgfSwKICAgIGNtVG9DYm0oY21Wb2x1bWUpIHsKICAgICAgcmV0dXJuIGN1cnJlbmN5KGNtVm9sdW1lKS5kaXZpZGUoMV8wMDBfMDAwKS52YWx1ZSAvLyAxIENCTSA9IDEsMDAwLDAwMCBjbcKzCiAgICB9LAogICAgLy8g5qC85byP5YyW5Li65bim6YCX5Y+35ZKM5bCP5pWw55qE5a2X56ym5LiyCiAgICBmb3JtYXROdW1iZXIodmFsdWUpIHsKICAgICAgcmV0dXJuIGN1cnJlbmN5KHZhbHVlLCB7c3ltYm9sOiAiIiwgcHJlY2lzaW9uOiAyfSkuZm9ybWF0KCkgLy8gZWc6IDEyMzQuNTYgPT4gIjEsMjM0LjU2IgogICAgfSwKICAgIC8vIOW9k+eUqOaIt+i+k+WFpeaXtuWunuaXtuagvOW8j+WMlu+8jOS9huS/neeVmeWFieagh+S9jee9rgogICAgZm9ybWF0SW5wdXQoZSkgewogICAgICBjb25zdCByYXdWYWx1ZSA9IGUucmVwbGFjZSgvLC9nLCAiIikgLy8g5Y676Zmk6YCX5Y+3CiAgICAgIGlmICghaXNOYU4ocmF3VmFsdWUpKSB7CiAgICAgICAgdGhpcy5hbW91bnQgPSBwYXJzZUZsb2F0KHJhd1ZhbHVlKSAvLyDmm7TmlrDljp/lp4vlgLwKICAgICAgICByZXR1cm4gdGhpcy5mb3JtYXROdW1iZXIocmF3VmFsdWUpCiAgICAgIH0KICAgIH0sCiAgICBsb2FkQ2FyZ29EZXRhaWwoKSB7CiAgICAgIHRoaXMuZm9ybS5yc0NhcmdvRGV0YWlsc0xpc3QubWFwKGl0ZW0gPT4gewogICAgICAgIHRoaXMudXBkYXRlRm9ybWF0dGVyKGl0ZW0pCiAgICAgIH0pCgogICAgICB0aGlzLiRmb3JjZVVwZGF0ZSgpCiAgICB9LAogICAgdXBkYXRlRm9ybWF0dGVyKHJvdykgewogICAgICByb3cuc2luZ2xlUGllY2VXZWlnaHRGb3JtYXR0ZXIgPSB0aGlzLmZvcm1hdE51bWJlcihyb3cuc2luZ2xlUGllY2VXZWlnaHQpCiAgICAgIHJvdy51bml0TGVuZ3RoRm9ybWF0dGVyID0gdGhpcy5mb3JtYXROdW1iZXIocm93LnVuaXRMZW5ndGgpCiAgICAgIHJvdy51bml0SGVpZ2h0Rm9ybWF0dGVyID0gdGhpcy5mb3JtYXROdW1iZXIocm93LnVuaXRIZWlnaHQpCiAgICAgIHJvdy5zaW5nbGVQaWVjZVZvbHVtZUZvcm1hdHRlciA9IHRoaXMuZm9ybWF0TnVtYmVyKHJvdy5zaW5nbGVQaWVjZVZvbHVtZSkKICAgICAgcm93LnVuaXRHcm9zc1dlaWdodEZvcm1hdHRlciA9IHRoaXMuZm9ybWF0TnVtYmVyKHJvdy51bml0R3Jvc3NXZWlnaHQpCiAgICAgIHJvdy51bml0Vm9sdW1lRm9ybWF0dGVyID0gdGhpcy5mb3JtYXROdW1iZXIocm93LnVuaXRWb2x1bWUpCiAgICAgIHJvdy51bml0V2lkdGhGb3JtYXR0ZXIgPSB0aGlzLmZvcm1hdE51bWJlcihyb3cudW5pdFdpZHRoKQogICAgICByb3cuYm94SXRlbUNvdW50Rm9ybWF0dGVyID0gTnVtYmVyKHJvdy5ib3hJdGVtQ291bnQpID8gTnVtYmVyKHJvdy5ib3hJdGVtQ291bnQpIDogMAogICAgICByb3cuc3VidG90YWxJdGVtQ291bnRGb3JtYXR0ZXIgPSBOdW1iZXIocm93LnN1YnRvdGFsSXRlbUNvdW50KSA/IE51bWJlcihyb3cuc3VidG90YWxJdGVtQ291bnQpIDogMAogICAgfSwKICAgIC8vIOi+k+WFpeWkseeEpuaXtuehruS/neagvOW8j+ato+ehrgogICAgcGFyc2VJbnB1dChyb3csIHR5cGUpIHsKICAgICAgaWYgKCFyb3cgfHwgdHlwZW9mIHJvdyAhPT0gIm9iamVjdCIpIHJldHVybiAvLyDnqbrlgLzmoKHpqozvvJpyb3cg5LiN5a2Y5Zyo5oiW6Z2e5a+56LGh55u05o6l6L+U5ZueCiAgICAgIHN3aXRjaCAodHlwZSkgewogICAgICAgIGNhc2UgInNpbmdsZVBpZWNlV2VpZ2h0IjoKICAgICAgICAgIGNvbnN0IHNpbmdsZVBpZWNlV2VpZ2h0Rm9ybWF0dGVyVmFsdWUgPSBTdHJpbmcocm93LnNpbmdsZVBpZWNlV2VpZ2h0Rm9ybWF0dGVyIHx8ICIiKS5yZXBsYWNlKC8sL2csICIiKSAvLyDnqbrlgLzlronlhajlpITnkIYKICAgICAgICAgIGlmICghaXNOYU4oc2luZ2xlUGllY2VXZWlnaHRGb3JtYXR0ZXJWYWx1ZSkgJiYgc2luZ2xlUGllY2VXZWlnaHRGb3JtYXR0ZXJWYWx1ZSAhPT0gIiIpIHsKICAgICAgICAgICAgcm93LnNpbmdsZVBpZWNlV2VpZ2h0Rm9ybWF0dGVyID0gdGhpcy5mb3JtYXROdW1iZXIoc2luZ2xlUGllY2VXZWlnaHRGb3JtYXR0ZXJWYWx1ZSkKICAgICAgICAgIH0KICAgICAgICAgIGJyZWFrCiAgICAgICAgY2FzZSAidW5pdExlbmd0aCI6CiAgICAgICAgICBjb25zdCB1bml0TGVuZ3RoVmFsdWUgPSBTdHJpbmcocm93LnVuaXRMZW5ndGhGb3JtYXR0ZXIgfHwgIiIpLnJlcGxhY2UoLywvZywgIiIpIC8vIOepuuWAvOWuieWFqOWkhOeQhgogICAgICAgICAgaWYgKCFpc05hTih1bml0TGVuZ3RoVmFsdWUpICYmIHVuaXRMZW5ndGhWYWx1ZSAhPT0gIiIpIHsKICAgICAgICAgICAgcm93LnVuaXRMZW5ndGhGb3JtYXR0ZXIgPSB0aGlzLmZvcm1hdE51bWJlcih1bml0TGVuZ3RoVmFsdWUpCiAgICAgICAgICB9CiAgICAgICAgICBicmVhawogICAgICAgIGNhc2UgInVuaXRXaWR0aCI6CiAgICAgICAgICBjb25zdCB1bml0V2lkdGhWYWx1ZSA9IFN0cmluZyhyb3cudW5pdFdpZHRoRm9ybWF0dGVyIHx8ICIiKS5yZXBsYWNlKC8sL2csICIiKSAvLyDnqbrlgLzlronlhajlpITnkIYKICAgICAgICAgIGlmICghaXNOYU4odW5pdFdpZHRoVmFsdWUpICYmIHVuaXRXaWR0aFZhbHVlICE9PSAiIikgewogICAgICAgICAgICByb3cudW5pdFdpZHRoRm9ybWF0dGVyID0gdGhpcy5mb3JtYXROdW1iZXIodW5pdFdpZHRoVmFsdWUpCiAgICAgICAgICB9CiAgICAgICAgICBicmVhawogICAgICAgIGNhc2UgInVuaXRIZWlnaHQiOgogICAgICAgICAgY29uc3QgdW5pdEhlaWdodEZvcm1hdHRlclZhbHVlID0gU3RyaW5nKHJvdy51bml0SGVpZ2h0Rm9ybWF0dGVyIHx8ICIiKS5yZXBsYWNlKC8sL2csICIiKSAvLyDnqbrlgLzlronlhajlpITnkIYKICAgICAgICAgIGlmICghaXNOYU4odW5pdEhlaWdodEZvcm1hdHRlclZhbHVlKSAmJiB1bml0SGVpZ2h0Rm9ybWF0dGVyVmFsdWUgIT09ICIiKSB7CiAgICAgICAgICAgIHJvdy51bml0SGVpZ2h0Rm9ybWF0dGVyID0gdGhpcy5mb3JtYXROdW1iZXIodW5pdEhlaWdodEZvcm1hdHRlclZhbHVlKQogICAgICAgICAgfQogICAgICAgICAgYnJlYWsKICAgICAgICBjYXNlICJzaW5nbGVQaWVjZVZvbHVtZSI6CiAgICAgICAgICBjb25zdCBzaW5nbGVQaWVjZVZvbHVtZUZvcm1hdHRlclZhbHVlID0gU3RyaW5nKHJvdy5zaW5nbGVQaWVjZVZvbHVtZUZvcm1hdHRlciB8fCAiIikucmVwbGFjZSgvLC9nLCAiIikgLy8g56m65YC85a6J5YWo5aSE55CGCiAgICAgICAgICBpZiAoIWlzTmFOKHNpbmdsZVBpZWNlVm9sdW1lRm9ybWF0dGVyVmFsdWUpICYmIHNpbmdsZVBpZWNlVm9sdW1lRm9ybWF0dGVyVmFsdWUgIT09ICIiKSB7CiAgICAgICAgICAgIHJvdy5zaW5nbGVQaWVjZVZvbHVtZUZvcm1hdHRlciA9IHRoaXMuZm9ybWF0TnVtYmVyKHNpbmdsZVBpZWNlVm9sdW1lRm9ybWF0dGVyVmFsdWUpCiAgICAgICAgICB9CiAgICAgICAgICBicmVhawogICAgICAgIGNhc2UgInVuaXRHcm9zc1dlaWdodCI6CiAgICAgICAgICBjb25zdCB1bml0R3Jvc3NXZWlnaHRGb3JtYXR0ZXJWYWx1ZSA9IFN0cmluZyhyb3cudW5pdEdyb3NzV2VpZ2h0Rm9ybWF0dGVyIHx8ICIiKS5yZXBsYWNlKC8sL2csICIiKSAvLyDnqbrlgLzlronlhajlpITnkIYKICAgICAgICAgIGlmICghaXNOYU4odW5pdEdyb3NzV2VpZ2h0Rm9ybWF0dGVyVmFsdWUpICYmIHVuaXRHcm9zc1dlaWdodEZvcm1hdHRlclZhbHVlICE9PSAiIikgewogICAgICAgICAgICByb3cudW5pdEdyb3NzV2VpZ2h0Rm9ybWF0dGVyID0gdGhpcy5mb3JtYXROdW1iZXIodW5pdEdyb3NzV2VpZ2h0Rm9ybWF0dGVyVmFsdWUpCiAgICAgICAgICB9CiAgICAgICAgICBicmVhawogICAgICAgIGNhc2UgInVuaXRWb2x1bWUiOgogICAgICAgICAgY29uc3QgdW5pdFZvbHVtZUZvcm1hdHRlclZhbHVlID0gU3RyaW5nKHJvdy51bml0Vm9sdW1lRm9ybWF0dGVyIHx8ICIiKS5yZXBsYWNlKC8sL2csICIiKSAvLyDnqbrlgLzlronlhajlpITnkIYKICAgICAgICAgIGlmICghaXNOYU4odW5pdFZvbHVtZUZvcm1hdHRlclZhbHVlKSAmJiB1bml0Vm9sdW1lRm9ybWF0dGVyVmFsdWUgIT09ICIiKSB7CiAgICAgICAgICAgIHJvdy51bml0Vm9sdW1lRm9ybWF0dGVyID0gdGhpcy5mb3JtYXROdW1iZXIodW5pdFZvbHVtZUZvcm1hdHRlclZhbHVlKQogICAgICAgICAgfQogICAgICAgICAgYnJlYWsKICAgICAgfQogICAgfSwKICAgIGNvdW50Q2FyZ29tZWFzdXJlKHJvdywgdHlwZSkgewogICAgICBpZiAoIXJvdyB8fCB0eXBlb2Ygcm93ICE9PSAib2JqZWN0IikgcmV0dXJuIC8vIOepuuWAvOagoemqjO+8mnJvdyDkuI3lrZjlnKjmiJbpnZ7lr7nosaHnm7TmjqXov5Tlm54KICAgICAgc3dpdGNoICh0eXBlKSB7CiAgICAgICAgY2FzZSAic2luZ2xlUGllY2VXZWlnaHQiOgogICAgICAgICAgY29uc3Qgc2luZ2xlUGllY2VXZWlnaHRGb3JtYXR0ZXJWYWx1ZSA9IFN0cmluZyhyb3cuc2luZ2xlUGllY2VXZWlnaHRGb3JtYXR0ZXIgfHwgIiIpLnJlcGxhY2UoLywvZywgIiIpIC8vIOepuuWAvOWuieWFqOWkhOeQhgogICAgICAgICAgaWYgKCFpc05hTihzaW5nbGVQaWVjZVdlaWdodEZvcm1hdHRlclZhbHVlKSAmJiBzaW5nbGVQaWVjZVdlaWdodEZvcm1hdHRlclZhbHVlICE9PSAiIikgewogICAgICAgICAgICByb3cuc2luZ2xlUGllY2VXZWlnaHQgPSBwYXJzZUZsb2F0KHNpbmdsZVBpZWNlV2VpZ2h0Rm9ybWF0dGVyVmFsdWUpIC8vIOabtOaWsOWOn+Wni+WAvAogICAgICAgICAgfQogICAgICAgICAgYnJlYWsKICAgICAgICBjYXNlICJ1bml0TGVuZ3RoIjoKICAgICAgICAgIGNvbnN0IHVuaXRMZW5ndGhWYWx1ZSA9IFN0cmluZyhyb3cudW5pdExlbmd0aEZvcm1hdHRlciB8fCAiIikucmVwbGFjZSgvLC9nLCAiIikgLy8g56m65YC85a6J5YWo5aSE55CGCiAgICAgICAgICBpZiAoIWlzTmFOKHVuaXRMZW5ndGhWYWx1ZSkgJiYgdW5pdExlbmd0aFZhbHVlICE9PSAiIikgewogICAgICAgICAgICByb3cudW5pdExlbmd0aCA9IHBhcnNlRmxvYXQodW5pdExlbmd0aFZhbHVlKSAvLyDmm7TmlrDljp/lp4vlgLwKICAgICAgICAgIH0KICAgICAgICAgIGJyZWFrCiAgICAgICAgY2FzZSAidW5pdFdpZHRoIjoKICAgICAgICAgIGNvbnN0IHVuaXRXaWR0aFZhbHVlID0gU3RyaW5nKHJvdy51bml0V2lkdGhGb3JtYXR0ZXIgfHwgIiIpLnJlcGxhY2UoLywvZywgIiIpIC8vIOepuuWAvOWuieWFqOWkhOeQhgogICAgICAgICAgaWYgKCFpc05hTih1bml0V2lkdGhWYWx1ZSkgJiYgdW5pdFdpZHRoVmFsdWUgIT09ICIiKSB7CiAgICAgICAgICAgIHJvdy51bml0V2lkdGggPSBwYXJzZUZsb2F0KHVuaXRXaWR0aFZhbHVlKSAvLyDmm7TmlrDljp/lp4vlgLwKICAgICAgICAgIH0KICAgICAgICAgIGJyZWFrCiAgICAgICAgY2FzZSAidW5pdEhlaWdodCI6CiAgICAgICAgICBjb25zdCB1bml0SGVpZ2h0Rm9ybWF0dGVyVmFsdWUgPSBTdHJpbmcocm93LnVuaXRIZWlnaHRGb3JtYXR0ZXIgfHwgIiIpLnJlcGxhY2UoLywvZywgIiIpIC8vIOepuuWAvOWuieWFqOWkhOeQhgogICAgICAgICAgaWYgKCFpc05hTih1bml0SGVpZ2h0Rm9ybWF0dGVyVmFsdWUpICYmIHVuaXRIZWlnaHRGb3JtYXR0ZXJWYWx1ZSAhPT0gIiIpIHsKICAgICAgICAgICAgcm93LnVuaXRIZWlnaHQgPSBwYXJzZUZsb2F0KHVuaXRIZWlnaHRGb3JtYXR0ZXJWYWx1ZSkgLy8g5pu05paw5Y6f5aeL5YC8CiAgICAgICAgICB9CiAgICAgICAgICBicmVhawogICAgICAgIGNhc2UgInNpbmdsZVBpZWNlVm9sdW1lIjoKICAgICAgICAgIGNvbnN0IHNpbmdsZVBpZWNlVm9sdW1lRm9ybWF0dGVyVmFsdWUgPSBTdHJpbmcocm93LnNpbmdsZVBpZWNlVm9sdW1lRm9ybWF0dGVyIHx8ICIiKS5yZXBsYWNlKC8sL2csICIiKSAvLyDnqbrlgLzlronlhajlpITnkIYKICAgICAgICAgIGlmICghaXNOYU4oc2luZ2xlUGllY2VWb2x1bWVGb3JtYXR0ZXJWYWx1ZSkgJiYgc2luZ2xlUGllY2VWb2x1bWVGb3JtYXR0ZXJWYWx1ZSAhPT0gIiIpIHsKICAgICAgICAgICAgcm93LnNpbmdsZVBpZWNlVm9sdW1lID0gcGFyc2VGbG9hdChzaW5nbGVQaWVjZVZvbHVtZUZvcm1hdHRlclZhbHVlKSAvLyDmm7TmlrDljp/lp4vlgLwKICAgICAgICAgIH0KICAgICAgICAgIGJyZWFrCiAgICAgICAgY2FzZSAidW5pdEdyb3NzV2VpZ2h0IjoKICAgICAgICAgIGNvbnN0IHVuaXRHcm9zc1dlaWdodEZvcm1hdHRlclZhbHVlID0gU3RyaW5nKHJvdy51bml0R3Jvc3NXZWlnaHRGb3JtYXR0ZXIgfHwgIiIpLnJlcGxhY2UoLywvZywgIiIpIC8vIOepuuWAvOWuieWFqOWkhOeQhgogICAgICAgICAgaWYgKCFpc05hTih1bml0R3Jvc3NXZWlnaHRGb3JtYXR0ZXJWYWx1ZSkgJiYgdW5pdEdyb3NzV2VpZ2h0Rm9ybWF0dGVyVmFsdWUgIT09ICIiKSB7CiAgICAgICAgICAgIHJvdy51bml0R3Jvc3NXZWlnaHQgPSBwYXJzZUZsb2F0KHVuaXRHcm9zc1dlaWdodEZvcm1hdHRlclZhbHVlKSAvLyDmm7TmlrDljp/lp4vlgLwKICAgICAgICAgIH0KICAgICAgICAgIGJyZWFrCiAgICAgICAgY2FzZSAidW5pdFZvbHVtZSI6CiAgICAgICAgICBjb25zdCB1bml0Vm9sdW1lRm9ybWF0dGVyVmFsdWUgPSBTdHJpbmcocm93LnVuaXRWb2x1bWVGb3JtYXR0ZXIgfHwgIiIpLnJlcGxhY2UoLywvZywgIiIpIC8vIOepuuWAvOWuieWFqOWkhOeQhgogICAgICAgICAgaWYgKCFpc05hTih1bml0Vm9sdW1lRm9ybWF0dGVyVmFsdWUpICYmIHVuaXRWb2x1bWVGb3JtYXR0ZXJWYWx1ZSAhPT0gIiIpIHsKICAgICAgICAgICAgcm93LnVuaXRWb2x1bWUgPSBwYXJzZUZsb2F0KHVuaXRWb2x1bWVGb3JtYXR0ZXJWYWx1ZSkgLy8g5pu05paw5Y6f5aeL5YC8CiAgICAgICAgICB9CiAgICAgICAgICBicmVhawogICAgICB9CgogICAgICAvLyDmm7TmlrDnm7jlhbPlrZfmrrUKICAgICAgdGhpcy5mb3JtLnJzQ2FyZ29EZXRhaWxzTGlzdCA9IHRoaXMuZm9ybS5yc0NhcmdvRGV0YWlsc0xpc3QubWFwKGl0ZW0gPT4gewogICAgICAgIGlmIChyb3cgPT09IGl0ZW0pIHsKICAgICAgICAgIGlmIChpdGVtLnVuaXRMZW5ndGggJiYgaXRlbS51bml0V2lkdGggJiYgaXRlbS51bml0SGVpZ2h0KSB7CiAgICAgICAgICAgIGNvbnN0IGNtID0gY3VycmVuY3koaXRlbS51bml0TGVuZ3RoKS5tdWx0aXBseShpdGVtLnVuaXRXaWR0aCkubXVsdGlwbHkoaXRlbS51bml0SGVpZ2h0KS52YWx1ZQogICAgICAgICAgICBpdGVtLnNpbmdsZVBpZWNlVm9sdW1lID0gdGhpcy5jbVRvQ2JtKGNtKQogICAgICAgICAgICBpdGVtLnNpbmdsZVBpZWNlVm9sdW1lRm9ybWF0dGVyID0gdGhpcy5mb3JtYXROdW1iZXIoaXRlbS5zaW5nbGVQaWVjZVZvbHVtZSkKICAgICAgICAgIH0KICAgICAgICAgIGlmIChpdGVtLnNpbmdsZVBpZWNlVm9sdW1lICYmIGl0ZW0uYm94Q291bnQpIHsKICAgICAgICAgICAgaXRlbS51bml0Vm9sdW1lID0gY3VycmVuY3koaXRlbS5zaW5nbGVQaWVjZVZvbHVtZSkubXVsdGlwbHkoaXRlbS5ib3hDb3VudCkudmFsdWUKICAgICAgICAgICAgaXRlbS51bml0Vm9sdW1lRm9ybWF0dGVyID0gdGhpcy5mb3JtYXROdW1iZXIoaXRlbS51bml0Vm9sdW1lKQogICAgICAgICAgfQogICAgICAgICAgaWYgKGl0ZW0uc2luZ2xlUGllY2VXZWlnaHQgJiYgaXRlbS5ib3hDb3VudCkgewogICAgICAgICAgICBpdGVtLnVuaXRHcm9zc1dlaWdodCA9IGN1cnJlbmN5KGl0ZW0uc2luZ2xlUGllY2VXZWlnaHQpLm11bHRpcGx5KGl0ZW0uYm94Q291bnQpLnZhbHVlCiAgICAgICAgICAgIGl0ZW0udW5pdEdyb3NzV2VpZ2h0Rm9ybWF0dGVyID0gdGhpcy5mb3JtYXROdW1iZXIoaXRlbS51bml0R3Jvc3NXZWlnaHQpCiAgICAgICAgICB9CiAgICAgICAgICAvLyDku7bmlbDlsI/orqEKICAgICAgICAgIGlmIChpdGVtLmJveEl0ZW1Db3VudCkgewogICAgICAgICAgICBpZiAoaXRlbS5ib3hDb3VudCkgewogICAgICAgICAgICAgIGl0ZW0uc3VidG90YWxJdGVtQ291bnQgPSBjdXJyZW5jeShpdGVtLmJveEl0ZW1Db3VudCkubXVsdGlwbHkoaXRlbS5ib3hDb3VudCkudmFsdWUKICAgICAgICAgICAgfSBlbHNlIHsKICAgICAgICAgICAgICBpdGVtLnN1YnRvdGFsSXRlbUNvdW50ID0gY3VycmVuY3koaXRlbS5ib3hJdGVtQ291bnQpLnZhbHVlCiAgICAgICAgICAgIH0KICAgICAgICAgIH0KICAgICAgICB9CiAgICAgICAgcmV0dXJuIGl0ZW0KICAgICAgfSkKCiAgICAgIC8vIHRoaXMuJGZvcmNlVXBkYXRlKCkKICAgIH0sCiAgICBzZWxlY3RXYXJlaG91c2VDbGllbnQocm93KSB7CiAgICAgIHRoaXMuZm9ybS5jbGllbnRDb2RlID0gcm93LmNsaWVudENvZGUKICAgICAgdGhpcy5mb3JtLmNsaWVudE5hbWUgPSByb3cuY2xpZW50TmFtZQogICAgICB0aGlzLmZvcm0ub3ZlcmR1ZVJlbnRhbFVuaXRQcmljZSA9IHJvdy5vdmVyZHVlUmVudAogICAgICB0aGlzLmZvcm0uZnJlZVN0YWNrUGVyaW9kID0gcm93LmZyZWVTdGFja1BlcmlvZAogICAgICB0aGlzLnNlbGVjdGVkQ2xpZW50ID0gcm93CiAgICAgIC8qIGlmIChyb3cuY2xpZW50VHlwZSA9PT0gIuebtOWuoiIpIHsKICAgICAgICB0aGlzLmZvcm0uaW5ib3VuZFNlcmlhbE5vUHJlID0gIk5vLjkwIgogICAgICB9IGVsc2UgewogICAgICAgIHRoaXMuZm9ybS5pbmJvdW5kU2VyaWFsTm9QcmUgPSAiTm8uODAiCiAgICAgIH0gKi8KICAgICAgaWYgKHJvdy5pbmNsdWRlc1VubG9hZGluZ0ZlZSAhPSBudWxsKSB7CiAgICAgICAgdGhpcy5mb3JtLmluY2x1ZGVzVW5sb2FkaW5nRmVlID0gcm93LmluY2x1ZGVzVW5sb2FkaW5nRmVlCiAgICAgICAgLyogaWYgKHJvdy5pbmNsdWRlc1VubG9hZGluZ0ZlZSA9PSAxKSB7CiAgICAgICAgICB0aGlzLmZvcm0udW5wYWlkVW5sb2FkaW5nRmVlID0gMAogICAgICAgIH0gKi8KICAgICAgfQogICAgICBpZiAocm93LmluY2x1ZGVzUGFja2luZ0ZlZSAhPSBudWxsKSB7CiAgICAgICAgdGhpcy5mb3JtLmluY2x1ZGVzUGFja2luZ0ZlZSA9IHJvdy5pbmNsdWRlc1BhY2tpbmdGZWUKICAgICAgICBpZiAocm93LmluY2x1ZGVzUGFja2luZ0ZlZSA9PSAxKSB7CiAgICAgICAgICB0aGlzLmZvcm0udW5wYWlkUGFja2luZ0ZlZSA9IDAKICAgICAgICB9CiAgICAgIH0KICAgICAgaWYgKHJvdy5pbmNsdWRlc0luYm91bmRGZWUgIT0gbnVsbCkgewogICAgICAgIHRoaXMuZm9ybS5pbmNsdWRlc0luYm91bmRGZWUgPSByb3cuaW5jbHVkZXNJbmJvdW5kRmVlCiAgICAgICAgaWYgKHJvdy5pbmNsdWRlc0luYm91bmRGZWUgPT0gMSkgewogICAgICAgICAgdGhpcy5mb3JtLmluYm91bmRGZWUgPSAwCiAgICAgICAgfQogICAgICB9CiAgICAgIGlmIChyb3cuaW1tZWRpYXRlUGF5bWVudEZlZSkgewogICAgICAgIHRoaXMuZm9ybS5pbW1lZGlhdGVQYXltZW50RmVlID0gcm93LmltbWVkaWF0ZVBheW1lbnRGZWUKICAgICAgfQogICAgICAvLyDmoLnmja7orrDlvZXmlrnlvI/pgInmi6nlhaXku5PmoIflh4botLkKICAgICAgaWYgKHRoaXMuZm9ybS5yZWNvcmRUeXBlICYmIHJvdy5pbmNsdWRlc0luYm91bmRGZWUgPT0gMCkgewogICAgICAgIHN3aXRjaCAodGhpcy5mb3JtLnJlY29yZFR5cGUpIHsKICAgICAgICAgIGNhc2UgIuagh+WHhiI6CiAgICAgICAgICAgIHRoaXMuZm9ybS5pbmJvdW5kRmVlID0gcm93LnN0YW5kYXJkSW5ib3VuZEZlZQogICAgICAgICAgICBicmVhawogICAgICAgICAgY2FzZSAi57K+56GuIjoKICAgICAgICAgICAgdGhpcy5mb3JtLmluYm91bmRGZWUgPSByb3cucHJlY2lzZUluYm91bmRGZWUKICAgICAgICAgICAgYnJlYWsKICAgICAgICAgIGNhc2UgIuW/q+mAkiI6CiAgICAgICAgICAgIHRoaXMuZm9ybS5pbmJvdW5kRmVlID0gcm93LmV4cHJlc3NJbmJvdW5kRmVlCiAgICAgICAgICAgIGJyZWFrCiAgICAgICAgfQogICAgICB9CgogICAgICAvLyDlvZPlrqLmiLfku6PnoIHmlLnlj5jml7Ys5riF56m65YiG5Y2V5Y+3CiAgICAgIHRoaXMuZm9ybS5zdWJPcmRlck5vID0gIiIKICAgICAgdGhpcy5mb3JtLnN1Yk9yZGVyTm8gPSByb3cuY2xpZW50Q29kZSArICItIgogICAgfSwKICAgIGdldFN1bW1hcmllc0ludmVudG9yeShwYXJhbSkgewogICAgICBjb25zdCB7Y29sdW1ucywgZGF0YX0gPSBwYXJhbQogICAgICBjb25zdCBzdW1zID0gW10KICAgICAgY29uc3Qgc3RhdGlzdGljYWxGaWVsZCA9IFsidG90YWxCb3hlcyIsICJ0b3RhbEdyb3NzV2VpZ2h0IiwgInRvdGFsVm9sdW1lIiwKICAgICAgICAiaW5ib3VuZEZlZSIsICJyZWNlaXZlZFN0b3JhZ2VGZWUiLCAidW5wYWlkVW5sb2FkaW5nRmVlIiwgInJlY2VpdmVkVW5sb2FkaW5nRmVlIiwKICAgICAgICAidW5wYWlkUGFja2luZ0ZlZSIsICJyZWNlaXZlZFBhY2tpbmdGZWUiLCAibG9naXN0aWNzQWR2YW5jZUZlZSJdCgogICAgICBjb2x1bW5zLmZvckVhY2goKGNvbHVtbiwgaW5kZXgpID0+IHsKICAgICAgICBpZiAoaW5kZXggPT09IDApIHsKICAgICAgICAgIHN1bXNbaW5kZXhdID0gIuaAu+iuoToiCiAgICAgICAgICByZXR1cm4KICAgICAgICB9CgogICAgICAgIC8vIOetm+mAieWHuuiiq+e7n+iuoeeahOWtl+autQogICAgICAgIGlmIChzdGF0aXN0aWNhbEZpZWxkLmluY2x1ZGVzKGNvbHVtbi5wcm9wZXJ0eSkpIHsKICAgICAgICAgIC8vIOWwhuaVsOaNrui9rOS4uuacieaViOaVsOWtlwogICAgICAgICAgY29uc3QgdmFsdWVzID0gZGF0YQogICAgICAgICAgICAubWFwKGl0ZW0gPT4gTnVtYmVyKGl0ZW1bY29sdW1uLnByb3BlcnR5XSkpCiAgICAgICAgICAgIC5maWx0ZXIodmFsdWUgPT4gIWlzTmFOKHZhbHVlKSkgLy8g5o6S6Zmk6Z2e5pWw5a2XCgogICAgICAgICAgaWYgKHZhbHVlcy5sZW5ndGggPiAwKSB7CiAgICAgICAgICAgIC8vIOiuoeeul+aAu+WSjOW5tuagvOW8j+WMlgogICAgICAgICAgICBjb25zdCB0b3RhbCA9IHZhbHVlcy5yZWR1Y2UoKHByZXYsIGN1cnIpID0+IHByZXYgKyBjdXJyLCAwKQogICAgICAgICAgICBzdW1zW2luZGV4XSA9CiAgICAgICAgICAgICAgdGhpcy5mb3JtYXROdW1iZXJGaXhlZCh0b3RhbCkgKwogICAgICAgICAgICAgIChjb2x1bW4ucHJvcGVydHkgPT09ICJ0b3RhbFZvbHVtZSIKICAgICAgICAgICAgICAgID8gIiBDQk0iCiAgICAgICAgICAgICAgICA6IGNvbHVtbi5wcm9wZXJ0eSA9PT0gInRvdGFsR3Jvc3NXZWlnaHQiCiAgICAgICAgICAgICAgICAgID8gIiBLR1MiCiAgICAgICAgICAgICAgICAgIDogY29sdW1uLnByb3BlcnR5ID09PSAidG90YWxCb3hlcyIKICAgICAgICAgICAgICAgICAgICA/ICIg5Lu2IgogICAgICAgICAgICAgICAgICAgIDogIiIpCiAgICAgICAgICB9IGVsc2UgewogICAgICAgICAgICBzdW1zW2luZGV4XSA9ICIgIgogICAgICAgICAgfQogICAgICAgIH0gZWxzZSB7CiAgICAgICAgICBzdW1zW2luZGV4XSA9ICIgIgogICAgICAgIH0KICAgICAgfSkKCiAgICAgIHJldHVybiBzdW1zCiAgICB9LAogICAgZ2V0U3VtbWFyaWVzKHBhcmFtKSB7CiAgICAgIGNvbnN0IHtjb2x1bW5zLCBkYXRhfSA9IHBhcmFtCiAgICAgIGNvbnN0IHN1bXMgPSBbXQogICAgICBjb25zdCBzdGF0aXN0aWNhbEZpZWxkID0gWyJ1bml0Vm9sdW1lIiwgInVuaXRHcm9zc1dlaWdodCIsICJib3hDb3VudCJdCgogICAgICBjb2x1bW5zLmZvckVhY2goKGNvbHVtbiwgaW5kZXgpID0+IHsKICAgICAgICBpZiAoaW5kZXggPT09IDApIHsKICAgICAgICAgIHN1bXNbaW5kZXhdID0gIuaAu+iuoToiCiAgICAgICAgICByZXR1cm4KICAgICAgICB9CgogICAgICAgIC8vIOetm+mAieWHuuiiq+e7n+iuoeeahOWtl+autQogICAgICAgIGlmIChzdGF0aXN0aWNhbEZpZWxkLmluY2x1ZGVzKGNvbHVtbi5wcm9wZXJ0eSkpIHsKICAgICAgICAgIC8vIOWwhuaVsOaNrui9rOS4uuacieaViOaVsOWtlwogICAgICAgICAgY29uc3QgdmFsdWVzID0gZGF0YQogICAgICAgICAgICAubWFwKGl0ZW0gPT4gTnVtYmVyKGl0ZW1bY29sdW1uLnByb3BlcnR5XSkpCiAgICAgICAgICAgIC5maWx0ZXIodmFsdWUgPT4gIWlzTmFOKHZhbHVlKSkgLy8g5o6S6Zmk6Z2e5pWw5a2XCgogICAgICAgICAgaWYgKHZhbHVlcy5sZW5ndGggPiAwKSB7CiAgICAgICAgICAgIC8vIOiuoeeul+aAu+WSjOW5tuagvOW8j+WMlgogICAgICAgICAgICBjb25zdCB0b3RhbCA9IHZhbHVlcy5yZWR1Y2UoKHByZXYsIGN1cnIpID0+IHByZXYgKyBjdXJyLCAwKQogICAgICAgICAgICBzdW1zW2luZGV4XSA9CiAgICAgICAgICAgICAgdGhpcy5mb3JtYXROdW1iZXJGaXhlZCh0b3RhbCkgKwogICAgICAgICAgICAgIChjb2x1bW4ucHJvcGVydHkgPT09ICJ1bml0Vm9sdW1lIgogICAgICAgICAgICAgICAgPyAiIENCTSIKICAgICAgICAgICAgICAgIDogY29sdW1uLnByb3BlcnR5ID09PSAidW5pdEdyb3NzV2VpZ2h0IgogICAgICAgICAgICAgICAgICA/ICIgS0dTIgogICAgICAgICAgICAgICAgICA6IGNvbHVtbi5wcm9wZXJ0eSA9PT0gImJveENvdW50IgogICAgICAgICAgICAgICAgICAgID8gIiDku7YiCiAgICAgICAgICAgICAgICAgICAgOiAiIikKICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgIHN1bXNbaW5kZXhdID0gIiAiCiAgICAgICAgICB9CiAgICAgICAgfSBlbHNlIHsKICAgICAgICAgIHN1bXNbaW5kZXhdID0gIiAiCiAgICAgICAgfQogICAgICB9KQoKICAgICAgcmV0dXJuIHN1bXMKICAgIH0sCiAgICBmb3JtYXROdW1iZXJGaXhlZCh2YWx1ZSkgewogICAgICByZXR1cm4gTnVtYmVyKHZhbHVlKS50b0ZpeGVkKDIpIC8vIOS/neeVmeS4pOS9jeWwj+aVsAogICAgfSwKICAgIGhhbmRsZU9wT3V0Ym91bmQoKSB7CiAgICAgIHRoaXMub3Blbk91dGJvdW5kID0gdHJ1ZQogICAgfSwKICAgIGluaXRQcmludCgpIHsKICAgICAgaGlwcmludC5pbml0KHsKICAgICAgICBwcm92aWRlcnM6IFtuZXcgZGVmYXVsdEVsZW1lbnRUeXBlUHJvdmlkZXIoKV0KICAgICAgfSkKICAgIH0sCiAgICBwcmludEluYm91bmRCaWxsKHR5cGUpIHsKICAgICAgbGV0IGRhdGEgPSB7fQogICAgICBkYXRhLmNsaWVudENvZGUgPSB0aGlzLmZvcm0uY2xpZW50Q29kZQogICAgICBkYXRhLnN1Yk9yZGVyTm8gPSB0aGlzLmZvcm0uc3ViT3JkZXJObwogICAgICBkYXRhLmluYm91bmRTZXJpYWxObyA9IHRoaXMuZm9ybS5pbmJvdW5kU2VyaWFsTm8KICAgICAgZGF0YS5mb3J3YXJkZXJObyA9IHRoaXMuZm9ybS5mb3J3YXJkZXJObwogICAgICBkYXRhLmFjdHVhbEluYm91bmRUaW1lID0gbW9tZW50KHRoaXMuZm9ybS5hY3R1YWxJbmJvdW5kVGltZSkuZm9ybWF0KCJ5eXl5LU1NLUREIEhIOm1tOnNzIikKICAgICAgZGF0YS5zcWRTaGlwcGluZ01hcmsgPSB0aGlzLmZvcm0uc3FkU2hpcHBpbmdNYXJrCiAgICAgIGRhdGEuY2FyZ29OYW1lID0gdGhpcy5mb3JtLmNhcmdvTmFtZQogICAgICBkYXRhLnRvdGFsQm94ZXMgPSB0aGlzLmZvcm0udG90YWxCb3hlcwogICAgICBkYXRhLnRvdGFsR3Jvc3NXZWlnaHQgPSB0aGlzLmZvcm0udG90YWxHcm9zc1dlaWdodAogICAgICBkYXRhLnRvdGFsVm9sdW1lID0gdGhpcy5mb3JtLnRvdGFsVm9sdW1lCiAgICAgIGRhdGEubm90ZXMgPSB0aGlzLmZvcm0ubm90ZXMKICAgICAgZGF0YS5zdXBwbGllciA9IHRoaXMuZm9ybS5zdXBwbGllcgogICAgICBkYXRhLmRyaXZlckluZm8gPSB0aGlzLmZvcm0uZHJpdmVySW5mbwogICAgICBkYXRhLmluYm91bmRBZGRyID0gdGhpcy5mb3JtLmluYm91bmRBZGRyCiAgICAgIGRhdGEuY29uc2lnbmVlTmFtZSA9IHRoaXMuZm9ybS5jb25zaWduZWVOYW1lCiAgICAgIGRhdGEuaW5ib3VuZFNlcmlhbE5vID0gdGhpcy5mb3JtLmluYm91bmRTZXJpYWxObwoKICAgICAgZGF0YS5leHByZXNzTm8gPSB0aGlzLmZvcm0uZHJpdmVySW5mbwoKICAgICAgaWYgKHR5cGUgPT09ICLml6fmqKHmnb8iKSB7CiAgICAgICAgaGlwcmludFRlbXBsYXRlID0gbmV3IGhpcHJpbnQuUHJpbnRUZW1wbGF0ZSh7dGVtcGxhdGU6IHdhcmVob3VzZVJlY2VpcHR9KQogICAgICB9IGVsc2UgewogICAgICAgIGhpcHJpbnRUZW1wbGF0ZSA9IG5ldyBoaXByaW50LlByaW50VGVtcGxhdGUoe3RlbXBsYXRlOiB3YXJlaG91c2VSZWNlaXB0TmV3fSkKICAgICAgfQogICAgICAvLyDmiZPlvIDpooTop4jnu4Tku7YKICAgICAgLy8gdGhpcy4kcmVmcy5wcmVWaWV3LnNob3coaGlwcmludFRlbXBsYXRlLCBkYXRhKQogICAgICAvLyDnm7TmjqXmiZPljbAKICAgICAgdGhpcy4kcmVmcy5wcmVWaWV3LnByaW50KGhpcHJpbnRUZW1wbGF0ZSwgZGF0YSkKICAgIH0sCiAgICBkZWxldGVDYXJnb0RldGFpbChyb3cpIHsKICAgICAgdGhpcy5mb3JtLnJzQ2FyZ29EZXRhaWxzTGlzdCA9IHRoaXMuZm9ybS5yc0NhcmdvRGV0YWlsc0xpc3QuZmlsdGVyKGl0ZW0gPT4gaXRlbSAhPT0gcm93KQogICAgICB0aGlzLiRmb3JjZVVwZGF0ZSgpCiAgICB9LAogICAgaGFuZGxlVXBkYXRlQ2FyZ29EZXRhaWwocm93KSB7CiAgICAgIHRoaXMuY2FyZ29EZXRhaWxPcGVuID0gdHJ1ZQogICAgICB0aGlzLmNhcmdvRGV0YWlsUm93ID0gcm93CiAgICB9LAogICAgaGFuZGxlUGtnU2VsZWN0aW9uQ2hhbmdlKHNlbGVjdGlvbikgewogICAgICB0aGlzLnNlbGVjdGVkUGtnTGlzdCA9IHNlbGVjdGlvbgogICAgfSwKICAgIHBrZ0ZpbmlzaCgpIHsKICAgICAgaWYgKHRoaXMuZm9ybS5yZXBhY2tpbmdTdGF0dXMgPT09ICLmiZPljIXlrowiKSB7CiAgICAgICAgdGhpcy5mb3JtLnJlcGFja2luZ1N0YXR1cyA9ICLmiZPljIXkuK0iCiAgICAgIH0gZWxzZSB7CiAgICAgICAgdGhpcy5mb3JtLnJlcGFja2luZ1N0YXR1cyA9ICLmiZPljIXlrowiCiAgICAgIH0KCiAgICAgIHVwZGF0ZUludmVudG9yeSh0aGlzLmZvcm0pLnRoZW4ocmVzID0+IHsKICAgICAgICB0aGlzLiRtZXNzYWdlLnN1Y2Nlc3MoIuS/ruaUueaIkOWKnyIpCiAgICAgIH0pCiAgICB9LAogICAgYWRkQ2FyZ29EZXRhaWxSb3cocm93KSB7CgogICAgfSwKICAgIGFkZENhcmdvRGV0YWlsKCkgewogICAgICB0aGlzLmZvcm0ucnNDYXJnb0RldGFpbHNMaXN0LnB1c2godGhpcy5fLmNsb25lRGVlcCh0aGlzLmNhcmdvRGV0YWlsUm93KSkKICAgICAgdGhpcy5jYXJnb0RldGFpbE9wZW4gPSB0cnVlCiAgICB9LAogICAgbGlzdEFnZ3JlZ2F0b3JSc0ludmVudG9yeShwYXJhbXMpIHsKICAgICAgcGFyYW1zLmNvbmZpZyA9IEpTT04uc3RyaW5naWZ5KHBhcmFtcy5jb25maWcpCiAgICAgIHRoaXMucXVlcnlQYXJhbXMucGFyYW1zID0gcGFyYW1zCiAgICAgIHJldHVybiBsaXN0QWdncmVnYXRvclJzSW52ZW50b3J5KHRoaXMucXVlcnlQYXJhbXMpCiAgICB9LAogICAgLyoqIOafpeivouW6k+WtmOWIl+ihqCAqLwogICAgZ2V0TGlzdCgpIHsKICAgICAgdGhpcy5sb2FkaW5nID0gdHJ1ZQogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnBlcm1pc3Npb25MZXZlbCA9IHRoaXMuJHN0b3JlLnN0YXRlLnVzZXIucGVybWlzc2lvbkxldmVsTGlzdC5DCiAgICAgIC8vIOa3u+WKoOadoeS7tu+8jOWPquafpeivoumhtuWxguaVsOaNru+8iOayoeacieeItue6p+eahOaVsOaNru+8iQogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLmlzVG9wTGV2ZWwgPSB0cnVlCiAgICAgIGxpc3RJbnZlbnRvcnkodGhpcy5xdWVyeVBhcmFtcykudGhlbihyZXNwb25zZSA9PiB7CiAgICAgICAgLy8g5aSE55CG5pWw5o2u77yM5qCH6K6w5pyJ5a2Q6IqC54K555qE5pWw5o2uCiAgICAgICAgbGV0IHJvd3MgPSByZXNwb25zZS5yb3dzCgogICAgICAgIC8vIOWmguaenOWQjuerr+S4jeaUr+aMgWlzVG9wTGV2ZWzlj4LmlbDvvIzlnKjliY3nq6/ov5vooYzov4fmu6QKICAgICAgICAvLyDku4XlvZPlv6vpgJLljZXlj7fmnKrloavlhpnml7bmiY3ov4fmu6RwYWNrYWdlVG/vvIzkv53or4Hlv6vpgJLmn6Xor6Lml7bog73mmL7npLrmiYDmnInljLnphY3nmoTorrDlvZUKICAgICAgICBpZiAodGhpcy5xdWVyeVBhcmFtcy5pc1RvcExldmVsICYmICF0aGlzLnF1ZXJ5UGFyYW1zLmRyaXZlckluZm8pIHsKICAgICAgICAgIHJvd3MgPSByb3dzLmZpbHRlcihpdGVtID0+ICFpdGVtLnBhY2thZ2VUbykKICAgICAgICB9CgogICAgICAgIHRoaXMuaW52ZW50b3J5TGlzdCA9IHJvd3MubWFwKGl0ZW0gPT4gewogICAgICAgICAgLy8g5aaC5p6c5piv5omT5YyF566x77yM5qCH6K6w5Li65pyJ5a2Q6IqC54K5CiAgICAgICAgICBpZiAoaXRlbS5wYWNrYWdlUmVjb3JkID09PSAiMSIpIHsKICAgICAgICAgICAgaXRlbS5oYXNDaGlsZHJlbiA9IHRydWUKICAgICAgICAgIH0KICAgICAgICAgIHJldHVybiBpdGVtCiAgICAgICAgfSkKICAgICAgICB0aGlzLnRvdGFsID0gcmVzcG9uc2UudG90YWwKICAgICAgICB0aGlzLmxvYWRpbmcgPSBmYWxzZQogICAgICB9KQogICAgfSwKICAgIC8vIOWPlua2iOaMiemSrgogICAgY2FuY2VsKCkgewogICAgICB0aGlzLm9wZW4gPSBmYWxzZQogICAgICB0aGlzLnJlc2V0KCkKICAgIH0sCiAgICAvLyDooajljZXph43nva4KICAgIHJlc2V0KCkgewogICAgICB0aGlzLnBrZ0RldGFpbHNMaXN0ID0gW10KICAgICAgdGhpcy5mb3JtID0gewogICAgICAgIHJlcGFja2luZ1N0YXR1czogIi0iLAogICAgICAgIGludmVudG9yeUlkOiBudWxsLAogICAgICAgIGludmVudG9yeVN0YXR1czogIjAiLAogICAgICAgIGluYm91bmRTZXJpYWxObzogbnVsbCwKICAgICAgICBpbmJvdW5kU2VyaWFsTm9QcmU6IG51bGwsCiAgICAgICAgaW5ib3VuZFNlcmlhbFNwbGl0OiBudWxsLAogICAgICAgIGluYm91bmREYXRlOiBudWxsLAogICAgICAgIG91dGJvdW5kTm86IG51bGwsCiAgICAgICAgZm9yd2FyZGVyTm86IG51bGwsCiAgICAgICAgcmVudGFsU2V0dGxlbWVudERhdGU6IG51bGwsCiAgICAgICAgb3V0Ym91bmREYXRlOiBudWxsLAogICAgICAgIGNsaWVudENvZGU6IG51bGwsCiAgICAgICAgc3ViT3JkZXJObzogbnVsbCwKICAgICAgICBzdXBwbGllcjogbnVsbCwKICAgICAgICBkcml2ZXJJbmZvOiBudWxsLAogICAgICAgIHNxZFNoaXBwaW5nTWFyazogbnVsbCwKICAgICAgICBjYXJnb05hbWU6IG51bGwsCiAgICAgICAgdG90YWxCb3hlczogbnVsbCwKICAgICAgICBwYWNrYWdlVHlwZTogbnVsbCwKICAgICAgICB0b3RhbEdyb3NzV2VpZ2h0OiBudWxsLAogICAgICAgIHRvdGFsVm9sdW1lOiBudWxsLAogICAgICAgIGRhbWFnZVN0YXR1czogIjAiLAogICAgICAgIHN0b3JhZ2VMb2NhdGlvbjE6IG51bGwsCiAgICAgICAgc3RvcmFnZUxvY2F0aW9uMjogbnVsbCwKICAgICAgICBzdG9yYWdlTG9jYXRpb24zOiBudWxsLAogICAgICAgIHJlY2VpdmVkU3RvcmFnZUZlZTogbnVsbCwKICAgICAgICB1bnBhaWRVbmxvYWRpbmdGZWU6IG51bGwsCiAgICAgICAgbG9naXN0aWNzQWR2YW5jZUZlZTogbnVsbCwKICAgICAgICByZW50YWxCYWxhbmNlRmVlOiBudWxsLAogICAgICAgIGZyZWVTdGFja1BlcmlvZDogbnVsbCwKICAgICAgICBvdmVyZHVlUmVudGFsVW5pdFByaWNlOiBudWxsLAogICAgICAgIG92ZXJkdWVSZW50YWxGZWU6IG51bGwsCiAgICAgICAgbm90ZXM6IG51bGwsCiAgICAgICAgd2FyZWhvdXNlQ29kZTogbnVsbCwKICAgICAgICByZWNvcmRUeXBlOiBudWxsLAogICAgICAgIGluYm91bmRUeXBlOiBudWxsLAogICAgICAgIGNhcmdvTmF0dXJlOiBudWxsLAogICAgICAgIGNyZWF0ZWRBdDogbnVsbCwKICAgICAgICBwcmVPdXRib3VuZEZsYWc6IG51bGwsCiAgICAgICAgb3V0Ym91bmRSZXF1ZXN0RmxhZzogbnVsbCwKICAgICAgICBzcWRQbGFubmVkT3V0Ym91bmREYXRlOiBudWxsLAogICAgICAgIGNvbmZpcm1JbmJvdW5kUmVxdWVzdEZsYWc6IG51bGwsCiAgICAgICAgY29uZmlybU91dGJvdW5kUmVxdWVzdEZsYWc6IG51bGwsCiAgICAgICAgc3FkSW5ib3VuZEhhbmRsZXI6IG51bGwsCiAgICAgICAgcGFydGlhbE91dGJvdW5kRmxhZzogbnVsbCwKICAgICAgICBvdXRib3VuZFJlY29yZElkOiBudWxsLAogICAgICAgIGFjdHVhbEluYm91bmRUaW1lOiBtb21lbnQoKS5mb3JtYXQoInl5eXktTU0tREQgSEg6bW06c3MiKSwKICAgICAgICBhY3R1YWxPdXRib3VuZFRpbWU6IG51bGwsCiAgICAgICAgY2FyZ29EZXRhaWxSb3dzOiBudWxsLAogICAgICAgIGluY2x1ZGVzVW5sb2FkaW5nRmVlOiBudWxsLAogICAgICAgIHVucGFpZFBhY2tpbmdGZWU6IG51bGwsCiAgICAgICAgaW5ib3VuZEZlZTogbnVsbCwKICAgICAgICBzaW5nbGVQaWVjZVdlaWdodEZvcm1hdHRlcjogbnVsbCwKICAgICAgICB1bml0TGVuZ3RoRm9ybWF0dGVyOiBudWxsLAogICAgICAgIHVuaXRXaWR0aEZvcm1hdHRlcjogbnVsbCwKICAgICAgICB1bml0SGVpZ2h0Rm9ybWF0dGVyOiBudWxsLAogICAgICAgIHNpbmdsZVBpZWNlVm9sdW1lRm9ybWF0dGVyOiBudWxsLAogICAgICAgIHVuaXRHcm9zc1dlaWdodEZvcm1hdHRlcjogbnVsbCwKICAgICAgICB1bml0Vm9sdW1lRm9ybWF0dGVyOiBudWxsLAogICAgICAgIHJzQ2FyZ29EZXRhaWxzTGlzdDogWwogICAgICAgICAgewogICAgICAgICAgICBzaGlwcGluZ01hcms6ICIiLAogICAgICAgICAgICBpdGVtTmFtZTogIiIsCiAgICAgICAgICAgIGJveENvdW50OiAwLAogICAgICAgICAgICBwYWNrYWdlVHlwZTogIue6uOeusSIsCiAgICAgICAgICAgIHNpbmdsZVBpZWNlV2VpZ2h0Rm9ybWF0dGVyOiAiIiwKICAgICAgICAgICAgdW5pdExlbmd0aEZvcm1hdHRlcjogIiIsCiAgICAgICAgICAgIHVuaXRXaWR0aEZvcm1hdHRlcjogIiIsCiAgICAgICAgICAgIHVuaXRIZWlnaHRGb3JtYXR0ZXI6ICIiLAogICAgICAgICAgICBzaW5nbGVQaWVjZVZvbHVtZUZvcm1hdHRlcjogIiIsCiAgICAgICAgICAgIHVuaXRHcm9zc1dlaWdodEZvcm1hdHRlcjogIiIsCiAgICAgICAgICAgIHVuaXRWb2x1bWVGb3JtYXR0ZXI6ICIiLAogICAgICAgICAgICBkYW1hZ2VTdGF0dXM6ICIiLAogICAgICAgICAgICBib3hJdGVtQ291bnQ6IDAsCiAgICAgICAgICAgIHN1YnRvdGFsSXRlbUNvdW50OiAwLAogICAgICAgICAgICBleHByZXNzRGF0ZTogbW9tZW50KCkuZm9ybWF0KCJ5eXl5LU1NLUREIiksCiAgICAgICAgICAgIGFkZGl0aW9uYWxGZWU6IDAKICAgICAgICAgIH0KICAgICAgICBdCiAgICAgIH0KICAgICAgdGhpcy5yZXNldEZvcm0oImZvcm0iKQogICAgfSwKICAgIC8qKiDmkJzntKLmjInpkq7mk43kvZwgKi8KICAgIGhhbmRsZVF1ZXJ5KCkgewogICAgICB0aGlzLnF1ZXJ5UGFyYW1zLnBhZ2VOdW0gPSAxCiAgICAgIHRoaXMuZ2V0TGlzdCgpCiAgICB9LAogICAgLyoqIOmHjee9ruaMiemSruaTjeS9nCAqLwogICAgcmVzZXRRdWVyeSgpIHsKICAgICAgdGhpcy5yZXNldEZvcm0oInF1ZXJ5Rm9ybSIpCiAgICAgIC8vIOS/neeVmWlzVG9wTGV2ZWzlj4LmlbDvvIznoa7kv53ph43nva7lkI7ku43nhLblj6rmn6Xor6LpobblsYLmlbDmja4KICAgICAgdGhpcy5xdWVyeVBhcmFtcy5pc1RvcExldmVsID0gdHJ1ZQogICAgICB0aGlzLmhhbmRsZVF1ZXJ5KCkKICAgIH0sCiAgICBoYW5kbGVTdGF0dXNDaGFuZ2Uocm93KSB7CiAgICAgIGxldCB0ZXh0ID0gcm93LnN0YXR1cyA9PT0gIjAiID8gIuWQr+eUqCIgOiAi5YGc55SoIgogICAgICB0aGlzLiRtb2RhbC5jb25maXJtKCLnoa7orqTopoFcIiIgKyB0ZXh0ICsgIuWQl++8nyIpLnRoZW4oZnVuY3Rpb24gKCkgewogICAgICAgIHJldHVybiBjaGFuZ2VTdGF0dXMocm93LmludmVudG9yeUlkLCByb3cuc3RhdHVzKQogICAgICB9KS50aGVuKCgpID0+IHsKICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKHRleHQgKyAi5oiQ5YqfIikKICAgICAgfSkuY2F0Y2goZnVuY3Rpb24gKCkgewogICAgICAgIHJvdy5zdGF0dXMgPSByb3cuc3RhdHVzID09PSAiMCIgPyAiMSIgOiAiMCIKICAgICAgfSkKICAgIH0sCiAgICAvLyDlpJrpgInmoYbpgInkuK3mlbDmja4KICAgIGhhbmRsZVNlbGVjdGlvbkNoYW5nZShzZWxlY3Rpb24pIHsKICAgICAgLy8g5q2j56Gu6I635Y+W6KGo5qC85pWw5o2uIC0g6YCa6L+HZGF0YeWxnuaApwogICAgICBjb25zdCB0cmVlRGF0YSA9IHRoaXMuJHJlZnMuaW52ZW50b3J5VGFibGUuc3RvcmUuc3RhdGVzLmRhdGEKCiAgICAgIC8vIOiOt+WPluS5i+WJjeeahOmAieaLqeeKtuaAge+8jOeUqOS6juavlOi+g+WPmOWMlgogICAgICBjb25zdCBwcmV2aW91c0lkcyA9IFsuLi50aGlzLmlkc10KCiAgICAgIC8vIOa4heepuuW9k+WJjemAieaLqQogICAgICB0aGlzLmlkcyA9IFtdCiAgICAgIHRoaXMuc2VsZWN0ZWRJbnZlbnRvcnlMaXN0ID0gW10KCiAgICAgIC8vIOmHjeaWsOWhq+WFhemAieaLqeaVsOaNrgogICAgICB0aGlzLmlkcyA9IHNlbGVjdGlvbi5tYXAoaXRlbSA9PiBpdGVtLmludmVudG9yeUlkKQogICAgICB0aGlzLnNpbmdsZSA9IHNlbGVjdGlvbi5sZW5ndGggIT09IDEKICAgICAgdGhpcy5tdWx0aXBsZSA9ICFzZWxlY3Rpb24ubGVuZ3RoCiAgICAgIHRoaXMuc2VsZWN0ZWRJbnZlbnRvcnlMaXN0ID0gc2VsZWN0aW9uCgogICAgICAvLyDmib7lh7rmlrDpgInkuK3lkozlj5bmtojpgInkuK3nmoTpobkKICAgICAgY29uc3QgbmV3bHlTZWxlY3RlZCA9IHRoaXMuaWRzLmZpbHRlcihpZCA9PiAhcHJldmlvdXNJZHMuaW5jbHVkZXMoaWQpKQogICAgICBjb25zdCBuZXdseURlc2VsZWN0ZWQgPSBwcmV2aW91c0lkcy5maWx0ZXIoaWQgPT4gIXRoaXMuaWRzLmluY2x1ZGVzKGlkKSkKCiAgICAgIC8vIOWkhOeQhuaWsOmAieS4reeahOaJk+WMheeuse+8muiHquWKqOmAieS4reWFtuWtkOmhuQogICAgICBzZWxlY3Rpb24uZm9yRWFjaChpdGVtID0+IHsKICAgICAgICBpZiAoaXRlbS5wYWNrYWdlUmVjb3JkID09PSAiMSIgJiYgbmV3bHlTZWxlY3RlZC5pbmNsdWRlcyhpdGVtLmludmVudG9yeUlkKSkgewogICAgICAgICAgLy8g5aaC5p6c5piv5paw6YCJ5Lit55qE5omT5YyF566x6IqC54K5CgogICAgICAgICAgLy8g5Zyo5qCR5b2i6KGo5qC85pWw5o2u5Lit5om+5Yiw5a+55bqU55qE6IqC54K5CiAgICAgICAgICBjb25zdCBwYXJlbnROb2RlID0gdHJlZURhdGEuZmluZChub2RlID0+IG5vZGUuaW52ZW50b3J5SWQgPT09IGl0ZW0uaW52ZW50b3J5SWQpCgogICAgICAgICAgLy8g5qOA5p+l6IqC54K55piv5ZCm5bey5bGV5byAKOW3suaciWNoaWxkcmVu5bGe5oCn5LiU5pyJ5YaF5a65KQogICAgICAgICAgaWYgKHBhcmVudE5vZGUgJiYgcGFyZW50Tm9kZS5jaGlsZHJlbiAmJiBwYXJlbnROb2RlLmNoaWxkcmVuLmxlbmd0aCA+IDApIHsKICAgICAgICAgICAgLy8g5aaC5p6c6IqC54K55bey5bGV5byA77yM55u05o6l6YCJ5Lit5YW25omA5pyJ5a2Q6aG5CiAgICAgICAgICAgIHNldFRpbWVvdXQoKCkgPT4gewogICAgICAgICAgICAgIHBhcmVudE5vZGUuY2hpbGRyZW4uZm9yRWFjaChjaGlsZCA9PiB7CiAgICAgICAgICAgICAgICBpZiAoIXRoaXMuaWRzLmluY2x1ZGVzKGNoaWxkLmludmVudG9yeUlkKSkgewogICAgICAgICAgICAgICAgICB0aGlzLmlkcy5wdXNoKGNoaWxkLmludmVudG9yeUlkKQogICAgICAgICAgICAgICAgICB0aGlzLnNlbGVjdGVkSW52ZW50b3J5TGlzdC5wdXNoKGNoaWxkKQogICAgICAgICAgICAgICAgICB0aGlzLiRyZWZzLmludmVudG9yeVRhYmxlLnRvZ2dsZVJvd1NlbGVjdGlvbihjaGlsZCwgdHJ1ZSkKICAgICAgICAgICAgICAgIH0KICAgICAgICAgICAgICB9KQogICAgICAgICAgICB9LCA1MCkgLy8g57uZ5LiA54K55pe26Ze06K6pVUnmm7TmlrAKICAgICAgICAgIH0gZWxzZSBpZiAocGFyZW50Tm9kZSAmJiAhcGFyZW50Tm9kZS5jaGlsZHJlbkxvYWRlZCAmJiBwYXJlbnROb2RlLmhhc0NoaWxkcmVuKSB7CiAgICAgICAgICAgIC8vIOWmguaenOiKgueCueacquWxleW8gOS4lOacquWKoOi9vei/h+S9huacieWtkOiKgueCueagh+iusAogICAgICAgICAgICBwYXJlbnROb2RlLmNoaWxkcmVuTG9hZGVkID0gdHJ1ZQoKICAgICAgICAgICAgLy8g5omL5Yqo5bGV5byA6KGM77yM6Kem5Y+R5oeS5Yqg6L29CiAgICAgICAgICAgIHRoaXMuJHJlZnMuaW52ZW50b3J5VGFibGUudG9nZ2xlUm93RXhwYW5zaW9uKHBhcmVudE5vZGUsIHRydWUpCgogICAgICAgICAgICAvLyDnm5HlkKzlrZDoioLngrnliqDovb3lrozmiJDlkI7lho3pgInkuK3lroPku6wKICAgICAgICAgICAgLy8g6L+Z6YeM5Yip55So5LqGbG9hZENoaWxkSW52ZW50b3J55pa55rOV5Lit55qE6YC76L6R77yM5a6D5Lya5Zyo5a2Q6IqC54K55Yqg6L295ZCO5aSE55CG6YCJ5Lit54q25oCBCiAgICAgICAgICB9CiAgICAgICAgfQogICAgICB9KQoKICAgICAgLy8g5aSE55CG5Y+W5raI6YCJ5Lit55qE5omT5YyF566x77ya5Y+W5raI6YCJ5Lit5YW25a2Q6aG5CiAgICAgIG5ld2x5RGVzZWxlY3RlZC5mb3JFYWNoKHBhcmVudElkID0+IHsKICAgICAgICAvLyDmib7lh7rlr7nlupTnmoTniLboioLngrkKICAgICAgICBjb25zdCBwYXJlbnROb2RlID0gdHJlZURhdGEuZmluZChub2RlID0+CiAgICAgICAgICBub2RlLmludmVudG9yeUlkID09PSBwYXJlbnRJZCAmJiBub2RlLnBhY2thZ2VSZWNvcmQgPT09ICIxIgogICAgICAgICkKCiAgICAgICAgaWYgKHBhcmVudE5vZGUgJiYgcGFyZW50Tm9kZS5jaGlsZHJlbiAmJiBwYXJlbnROb2RlLmNoaWxkcmVuLmxlbmd0aCA+IDApIHsKICAgICAgICAgIC8vIOWPlua2iOmAieS4reaJgOacieWtkOmhuQogICAgICAgICAgcGFyZW50Tm9kZS5jaGlsZHJlbi5mb3JFYWNoKGNoaWxkID0+IHsKICAgICAgICAgICAgY29uc3QgY2hpbGRJbmRleCA9IHRoaXMuaWRzLmluZGV4T2YoY2hpbGQuaW52ZW50b3J5SWQpCiAgICAgICAgICAgIGlmIChjaGlsZEluZGV4ID4gLTEpIHsKICAgICAgICAgICAgICAvLyDku47pgInkuK3liJfooajkuK3np7vpmaQKICAgICAgICAgICAgICB0aGlzLmlkcy5zcGxpY2UoY2hpbGRJbmRleCwgMSkKICAgICAgICAgICAgICBjb25zdCBpdGVtSW5kZXggPSB0aGlzLnNlbGVjdGVkSW52ZW50b3J5TGlzdC5maW5kSW5kZXgoCiAgICAgICAgICAgICAgICBpdGVtID0+IGl0ZW0uaW52ZW50b3J5SWQgPT09IGNoaWxkLmludmVudG9yeUlkCiAgICAgICAgICAgICAgKQogICAgICAgICAgICAgIGlmIChpdGVtSW5kZXggPiAtMSkgewogICAgICAgICAgICAgICAgdGhpcy5zZWxlY3RlZEludmVudG9yeUxpc3Quc3BsaWNlKGl0ZW1JbmRleCwgMSkKICAgICAgICAgICAgICB9CiAgICAgICAgICAgICAgLy8g5ZyoVUnkuIrlj5bmtojpgInkuK0KICAgICAgICAgICAgICB0aGlzLiRyZWZzLmludmVudG9yeVRhYmxlLnRvZ2dsZVJvd1NlbGVjdGlvbihjaGlsZCwgZmFsc2UpCiAgICAgICAgICAgIH0KICAgICAgICAgIH0pCiAgICAgICAgfQogICAgICB9KQogICAgfSwKICAgIGhhbmRsZU9wZW5BZ2dyZWdhdG9yKCkgewogICAgICAvLyBsaXN0QWdncmVnYXRvclJjdCh0aGlzLnF1ZXJ5UGFyYW1zKS50aGVuKHJlc3BvbnNlID0+IHsKICAgICAgLy8gICB0aGlzLmFnZ3JlZ2F0b3JSY3RMaXN0ID0gcmVzcG9uc2UKICAgICAgLy8gfSkKCiAgICAgIHRoaXMub3BlbkFnZ3JlZ2F0b3IgPSB0cnVlCiAgICB9LAogICAgLyoqIOaWsOWinuaMiemSruaTjeS9nCAqLwogICAgaGFuZGxlQWRkKCkgewogICAgICB0aGlzLnJlc2V0KCkKICAgICAgdGhpcy5vcGVuID0gdHJ1ZQogICAgICB0aGlzLnRpdGxlID0gIuWFpeS7kyIKICAgICAgdGhpcy5mb3JtLnNxZEluYm91bmRIYW5kbGVyID0gdGhpcy4kc3RvcmUuc3RhdGUudXNlci5uYW1lLnNwbGl0KCIgIilbMV0KICAgICAgdGhpcy5mb3JtLmFjdHVhbEluYm91bmRUaW1lID0gbmV3IERhdGUoKQogICAgICB0aGlzLmZvcm0uaW52ZW50b3J5U3RhdHVzID0gIjAiCiAgICAgIC8vIHRoaXMuZm9ybS5yc0NhcmdvRGV0YWlsc0xpc3QgPyBudWxsIDogdGhpcy5mb3JtLnJzQ2FyZ29EZXRhaWxzTGlzdCA9IFtdCiAgICAgIC8vIHRoaXMuZm9ybS5yc0NhcmdvRGV0YWlsc0xpc3QucHVzaCh0aGlzLl8uY2xvbmVEZWVwKHRoaXMuY2FyZ29EZXRhaWxSb3cpKQogICAgICB0aGlzLiRuZXh0VGljaygoKSA9PiB7CiAgICAgICAgdGhpcy5mb3JtLmluYm91bmRTZXJpYWxOb1ByZSA9ICJSUy45MSIKICAgICAgICB0aGlzLmZvcm0uY2FyZ29OYXR1cmUgPSAi5pmu6LSnIgogICAgICAgIHRoaXMuZm9ybS5yZWNvcmRUeXBlID0gIuagh+WHhiIKICAgICAgICB0aGlzLmZvcm0uaW5ib3VuZFR5cGUgPSAi5YWl5LuTIgogICAgICB9KQoKICAgIH0sCiAgICAvKiog5L+u5pS55oyJ6ZKu5pON5L2cICovCiAgICBoYW5kbGVVcGRhdGUocm93KSB7CiAgICAgIHRoaXMucmVzZXQoKQogICAgICBjb25zdCBpbnZlbnRvcnlJZCA9IHJvdy5pbnZlbnRvcnlJZCB8fCB0aGlzLmlkcwogICAgICBnZXRJbnZlbnRvcnkoaW52ZW50b3J5SWQpLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgIHRoaXMuZm9ybSA9IHJlc3BvbnNlLmRhdGEKICAgICAgICB0aGlzLmZvcm0ucnNDYXJnb0RldGFpbHNMaXN0ID0gcmVzcG9uc2UuZGF0YS5yc0NhcmdvRGV0YWlsc0xpc3QubWFwKGl0ZW0gPT4gKHsKICAgICAgICAgIC4uLml0ZW0sCiAgICAgICAgICBzaGlwcGluZ01hcms6IGl0ZW0uc2hpcHBpbmdNYXJrIHx8ICIiLAogICAgICAgICAgaXRlbU5hbWU6IGl0ZW0uaXRlbU5hbWUgfHwgIiIsCiAgICAgICAgICBib3hDb3VudDogaXRlbS5ib3hDb3VudCB8fCAwLAogICAgICAgICAgcGFja2FnZVR5cGU6IGl0ZW0ucGFja2FnZVR5cGUgfHwgIiIsCiAgICAgICAgICBzaW5nbGVQaWVjZVdlaWdodDogaXRlbS5zaW5nbGVQaWVjZVdlaWdodCB8fCAwLAogICAgICAgICAgc2luZ2xlUGllY2VXZWlnaHRGb3JtYXR0ZXI6IHRoaXMuZm9ybWF0TnVtYmVyKGl0ZW0uc2luZ2xlUGllY2VXZWlnaHQgfHwgMCksCiAgICAgICAgICB1bml0TGVuZ3RoOiBpdGVtLnVuaXRMZW5ndGggfHwgMCwKICAgICAgICAgIHVuaXRMZW5ndGhGb3JtYXR0ZXI6IHRoaXMuZm9ybWF0TnVtYmVyKGl0ZW0udW5pdExlbmd0aCB8fCAwKSwKICAgICAgICAgIHVuaXRXaWR0aDogaXRlbS51bml0V2lkdGggfHwgMCwKICAgICAgICAgIHVuaXRXaWR0aEZvcm1hdHRlcjogdGhpcy5mb3JtYXROdW1iZXIoaXRlbS51bml0V2lkdGggfHwgMCksCiAgICAgICAgICB1bml0SGVpZ2h0OiBpdGVtLnVuaXRIZWlnaHQgfHwgMCwKICAgICAgICAgIHVuaXRIZWlnaHRGb3JtYXR0ZXI6IHRoaXMuZm9ybWF0TnVtYmVyKGl0ZW0udW5pdEhlaWdodCB8fCAwKSwKICAgICAgICAgIHNpbmdsZVBpZWNlVm9sdW1lOiBpdGVtLnNpbmdsZVBpZWNlVm9sdW1lIHx8IDAsCiAgICAgICAgICBzaW5nbGVQaWVjZVZvbHVtZUZvcm1hdHRlcjogdGhpcy5mb3JtYXROdW1iZXIoaXRlbS5zaW5nbGVQaWVjZVZvbHVtZSB8fCAwKSwKICAgICAgICAgIHVuaXRHcm9zc1dlaWdodDogaXRlbS51bml0R3Jvc3NXZWlnaHQgfHwgMCwKICAgICAgICAgIHVuaXRHcm9zc1dlaWdodEZvcm1hdHRlcjogdGhpcy5mb3JtYXROdW1iZXIoaXRlbS51bml0R3Jvc3NXZWlnaHQgfHwgMCksCiAgICAgICAgICB1bml0Vm9sdW1lOiBpdGVtLnVuaXRWb2x1bWUgfHwgMCwKICAgICAgICAgIHVuaXRWb2x1bWVGb3JtYXR0ZXI6IHRoaXMuZm9ybWF0TnVtYmVyKGl0ZW0udW5pdFZvbHVtZSB8fCAwKSwKICAgICAgICAgIGRhbWFnZVN0YXR1czogaXRlbS5kYW1hZ2VTdGF0dXMgfHwgIiIKICAgICAgICB9KSkKICAgICAgICBpZiAocmVzcG9uc2UuZGF0YS5wYWNrYWdlUmVjb3JkID09PSAiMCIpIHsKICAgICAgICAgIHRoaXMub3BlbiA9IHRydWUKICAgICAgICB9IGVsc2UgewogICAgICAgICAgdGhpcy5vcGVuUGtnID0gdHJ1ZQogICAgICAgIH0KCiAgICAgICAgdGhpcy50aXRsZSA9ICLkv67mlLnlupPlrZgiCiAgICAgIH0pCiAgICB9LAogICAgLyoqIOaPkOS6pOaMiemSriAqLwogICAgc3VibWl0Rm9ybSh0eXBlKSB7CiAgICAgIHRoaXMuJHJlZnNbImZvcm0iXS52YWxpZGF0ZSh2YWxpZCA9PiB7CiAgICAgICAgaWYgKHZhbGlkKSB7CiAgICAgICAgICAvLyDmo4Dmn6XliIbljZXlj7fmoLzlvI8KICAgICAgICAgIGlmICh0aGlzLmZvcm0uc3ViT3JkZXJObyAmJiAhdGhpcy5mb3JtLnN1Yk9yZGVyTm8uc3RhcnRzV2l0aChgJHt0aGlzLmZvcm0uY2xpZW50Q29kZX0tYCkpIHsKICAgICAgICAgICAgdGhpcy4kbW9kYWwubXNnRXJyb3IoYOWIhuWNleWPt+W/hemhu+S7pSAke3RoaXMuZm9ybS5jbGllbnRDb2RlfS0g5byA5aS0YCkKICAgICAgICAgICAgcmV0dXJuCiAgICAgICAgICB9CgogICAgICAgICAgdGhpcy5mb3JtLmluYm91bmREYXRlID0gdGhpcy5mb3JtLmluYm91bmREYXRlID8gbW9tZW50KHRoaXMuZm9ybS5pbmJvdW5kRGF0ZSkuZm9ybWF0KCJ5eXl5LU1NLUREIEhIOm1tOnNzIikgOiBudWxsCiAgICAgICAgICB0aGlzLmZvcm0uYWN0dWFsSW5ib3VuZFRpbWUgPSB0aGlzLmZvcm0uYWN0dWFsSW5ib3VuZFRpbWUgPyBtb21lbnQodGhpcy5mb3JtLmFjdHVhbEluYm91bmRUaW1lKS5mb3JtYXQoInl5eXktTU0tREQgSEg6bW06c3MiKSA6IG51bGwKCiAgICAgICAgICAvLyB0aGlzLmZvcm0uaW5ib3VuZFNlcmlhbE5vID0gdGhpcy5mb3JtLmluYm91bmRTZXJpYWxOb1ByZSArIHRoaXMuZm9ybS5pbmJvdW5kU2VyaWFsTm9TdWIucGFkU3RhcnQoMywgIjAiKQogICAgICAgICAgdGhpcy5mb3JtLmFjdHVhbEluYm91bmRUaW1lID0gbW9tZW50KHRoaXMuZm9ybS5hY3R1YWxJbmJvdW5kVGltZSkuZm9ybWF0KCJ5eXl5LU1NLUREIEhIOm1tOnNzIikKICAgICAgICAgIGlmICh0eXBlICE9PSAicGtnIikgewogICAgICAgICAgICB0aGlzLmZvcm0udG90YWxCb3hlcyA9IDAKICAgICAgICAgICAgdGhpcy5mb3JtLnRvdGFsR3Jvc3NXZWlnaHQgPSAwCiAgICAgICAgICAgIHRoaXMuZm9ybS50b3RhbFZvbHVtZSA9IDAKICAgICAgICAgICAgdGhpcy5mb3JtLnJzQ2FyZ29EZXRhaWxzTGlzdC5mb3JFYWNoKChpdGVtLCBpbmRleCkgPT4gewogICAgICAgICAgICAgIC8vIOaxh+aAu+iuoeeulwogICAgICAgICAgICAgIHRoaXMuZm9ybS50b3RhbEJveGVzID0gY3VycmVuY3koaXRlbS5ib3hDb3VudCB8fCAwKS5hZGQodGhpcy5mb3JtLnRvdGFsQm94ZXMpLnZhbHVlCiAgICAgICAgICAgICAgdGhpcy5mb3JtLnRvdGFsR3Jvc3NXZWlnaHQgPSBjdXJyZW5jeShpdGVtLnVuaXRHcm9zc1dlaWdodCB8fCAwKS5hZGQodGhpcy5mb3JtLnRvdGFsR3Jvc3NXZWlnaHQpLnZhbHVlCiAgICAgICAgICAgICAgdGhpcy5mb3JtLnRvdGFsVm9sdW1lID0gY3VycmVuY3koaXRlbS51bml0Vm9sdW1lIHx8IDApLmFkZCh0aGlzLmZvcm0udG90YWxWb2x1bWUpLnZhbHVlCiAgICAgICAgICAgIH0pCiAgICAgICAgICB9CgogICAgICAgICAgLy8g57uf6K6h5ZSb5aS0CiAgICAgICAgICBsZXQgbWFyayA9IFtdCiAgICAgICAgICB0aGlzLmZvcm0ucnNDYXJnb0RldGFpbHNMaXN0Lm1hcChpdGVtID0+IHsKICAgICAgICAgICAgbWFyay5wdXNoKGl0ZW0uc2hpcHBpbmdNYXJrKQogICAgICAgICAgfSkKICAgICAgICAgIHRoaXMuZm9ybS5zcWRTaGlwcGluZ01hcmsgPSBtYXJrLmpvaW4oIiwiKQoKICAgICAgICAgIGlmICh0aGlzLmZvcm0uaW52ZW50b3J5SWQgIT0gbnVsbCkgewogICAgICAgICAgICB0aGlzLmZvcm0ucmVudGFsU2V0dGxlbWVudERhdGUgPyBudWxsIDogdGhpcy5mb3JtLnJlbnRhbFNldHRsZW1lbnREYXRlID0gdGhpcy5mb3JtLmFjdHVhbEluYm91bmRUaW1lCiAgICAgICAgICAgIHVwZGF0ZUludmVudG9yeSh0aGlzLmZvcm0pLnRoZW4ocmVzcG9uc2UgPT4gewogICAgICAgICAgICAgIHRoaXMuJG1vZGFsLm1zZ1N1Y2Nlc3MoIuS/ruaUueaIkOWKnyIpCiAgICAgICAgICAgICAgdGhpcy5nZXRMaXN0KCkKICAgICAgICAgICAgfSkKICAgICAgICAgIH0gZWxzZSB7CiAgICAgICAgICAgIGlmICh0eXBlID09PSAicGtnIikgewogICAgICAgICAgICAgIHRoaXMuZm9ybS5wYWNrYWdlUmVjb3JkID0gIjEiCiAgICAgICAgICAgICAgdGhpcy5mb3JtLnJlcGFja2luZ1N0YXR1cyA9ICLmiZPljIXkuK0iCgogICAgICAgICAgICB9CiAgICAgICAgICAgIHRoaXMuZm9ybS5yZW50YWxTZXR0bGVtZW50RGF0ZSA9IHRoaXMuZm9ybS5hY3R1YWxJbmJvdW5kVGltZQogICAgICAgICAgICBhZGRJbnZlbnRvcnkodGhpcy5mb3JtKS50aGVuKHJlc3BvbnNlID0+IHsKICAgICAgICAgICAgICB0aGlzLmZvcm0gPSByZXNwb25zZS5kYXRhCiAgICAgICAgICAgICAgdGhpcy4kbW9kYWwubXNnU3VjY2Vzcygi5paw5aKe5oiQ5YqfIikKICAgICAgICAgICAgICB0aGlzLmdldExpc3QoKQogICAgICAgICAgICB9KQogICAgICAgICAgfQogICAgICAgIH0KICAgICAgfSkKICAgIH0sCiAgICAvKiog5Yig6Zmk5oyJ6ZKu5pON5L2cICovCiAgICBoYW5kbGVEZWxldGUocm93KSB7CiAgICAgIGNvbnN0IGludmVudG9yeUlkcyA9IHJvdy5pbnZlbnRvcnlJZCB8fCB0aGlzLmlkcwogICAgICB0aGlzLiRtb2RhbC5jb25maXJtKCLmmK/lkKbnoa7orqTliKDpmaTlupPlrZjnvJblj7fkuLpcIiIgKyBpbnZlbnRvcnlJZHMgKyAiXCLnmoTmlbDmja7pobnvvJ8iKS50aGVuKGZ1bmN0aW9uICgpIHsKICAgICAgICByZXR1cm4gZGVsSW52ZW50b3J5KGludmVudG9yeUlkcykKICAgICAgfSkudGhlbigoKSA9PiB7CiAgICAgICAgdGhpcy5nZXRMaXN0KCkKICAgICAgICB0aGlzLiRtb2RhbC5tc2dTdWNjZXNzKCLliKDpmaTmiJDlip8iKQogICAgICB9KS5jYXRjaCgoKSA9PiB7CiAgICAgIH0pCiAgICB9LAogICAgLyoqIOWvvOWFpeaMiemSruaTjeS9nCAqLwogICAgaGFuZGxlSW1wb3J0KCkgewogICAgICB0aGlzLnVwbG9hZC50aXRsZSA9ICLnlKjmiLflr7zlhaUiCiAgICAgIHRoaXMudXBsb2FkLm9wZW4gPSB0cnVlCiAgICB9LAogICAgLyoqIOWvvOWHuuaMiemSruaTjeS9nCAqLwogICAgaGFuZGxlRXhwb3J0KCkgewoKICAgICAgdGhpcy5kb3dubG9hZCgic3lzdGVtL2ludmVudG9yeS9leHBvcnQiLCB7CiAgICAgICAgLi4udGhpcy5xdWVyeVBhcmFtcywgcGFnZVNpemU6IDk5OQogICAgICB9LCBgaW52ZW50b3J5XyR7bmV3IERhdGUoKS5nZXRUaW1lKCl9Lnhsc3hgKQogICAgfQogIH0KfQo="}, {"version": 3, "sources": ["index.vue"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAi5BA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA", "file": "index.vue", "sourceRoot": "src/views/system/inventory", "sourcesContent": ["<template>\n  <div class=\"app-container\">\n    <el-row :gutter=\"20\">\n      <el-col :span=\"showLeft\">\n        <el-form v-show=\"showSearch\" ref=\"queryForm\" :inline=\"true\" :model=\"queryParams\" class=\"query\"\n                 size=\"mini\"\n        >\n          <el-form-item label=\"流水\" prop=\"inboundSerialNo\">\n            <el-input\n              v-model=\"queryParams.inboundSerialNo\"\n              clearable\n              placeholder=\"入仓流水号\"\n              @keyup.enter.native=\"handleQuery\"\n            />\n          </el-form-item>\n\n          <el-form-item label=\"快递\" prop=\"inboundSerialNo\">\n            <el-input\n              v-model=\"queryParams.driverInfo\"\n              clearable\n              placeholder=\"入仓快递单号\"\n              @keyup.enter.native=\"handleQuery\"\n            />\n          </el-form-item>\n          <el-form-item label=\"打包\">\n            <el-select v-model=\"queryParams.packageRecord\" clearable @change=\"handleQuery\">\n              <el-option label=\"打包箱\" value=\"1\"/>\n              <el-option label=\"-\" value=\"0\"/>\n            </el-select>\n          </el-form-item>\n\n          <el-form-item label=\"日期\" prop=\"inboundDate\">\n            <el-date-picker v-model=\"queryParams.inboundDate\"\n                            clearable\n                            placeholder=\"入仓日期\"\n                            type=\"date\"\n                            style=\"width: 100%\"\n                            value-format=\"yyyy-MM-dd\"\n            >\n            </el-date-picker>\n          </el-form-item>\n          <el-form-item label=\"单号\" prop=\"forwarderNo\">\n            <el-input\n              v-model=\"queryParams.forwarderNo\"\n              clearable\n              placeholder=\"货代单号\"\n              @keyup.enter.native=\"handleQuery\"\n            />\n          </el-form-item>\n          <el-form-item label=\"唛头\" prop=\"forwarderNo\">\n            <el-input\n              v-model=\"queryParams.sqdShippingMark\"\n              clearable\n              placeholder=\"唛头\"\n              @keyup.enter.native=\"handleQuery\"\n            />\n          </el-form-item>\n          <el-form-item label=\"结算\" prop=\"rentalSettlementDate\">\n            <el-date-picker v-model=\"queryParams.rentalSettlementDate\"\n                            clearable\n                            placeholder=\"仓租结算至\"\n                            style=\"width: 100%\" type=\"date\"\n                            value-format=\"yyyy-MM-dd\"\n            >\n            </el-date-picker>\n          </el-form-item>\n          <el-form-item label=\"代码\" prop=\"clientCode\">\n            <el-input\n              v-model=\"queryParams.clientCode\"\n              clearable\n              placeholder=\"客户代码\"\n              @keyup.enter.native=\"handleQuery\"\n            />\n          </el-form-item>\n          <el-form-item label=\"分单\" prop=\"subOrderNo\">\n            <el-input\n              v-model=\"queryParams.subOrderNo\"\n              clearable\n              placeholder=\"分单号\"\n              @keyup.enter.native=\"handleQuery\"\n            />\n          </el-form-item>\n          <el-form-item label=\"供货\" prop=\"supplier\">\n            <el-input\n              v-model=\"queryParams.supplier\"\n              clearable\n              placeholder=\"供货商\"\n              @keyup.enter.native=\"handleQuery\"\n            />\n          </el-form-item>\n          <el-form-item label=\"货名\" prop=\"cargoName\">\n            <el-input\n              v-model=\"queryParams.cargoName\"\n              clearable\n              placeholder=\"总货名\"\n              @keyup.enter.native=\"handleQuery\"\n            />\n          </el-form-item>\n          <el-form-item>\n            <el-button icon=\"el-icon-search\" size=\"mini\" type=\"primary\" @click=\"handleQuery\">搜索</el-button>\n            <el-button icon=\"el-icon-refresh\" size=\"mini\" @click=\"resetQuery\">重置</el-button>\n          </el-form-item>\n        </el-form>\n      </el-col>\n      <el-col :span=\"showRight\">\n        <el-row :gutter=\"10\" class=\"mb8\">\n          <el-col :span=\"1.5\">\n            <el-button\n              v-hasPermi=\"['system:inventory:add']\"\n              icon=\"el-icon-plus\"\n              plain\n              size=\"mini\"\n              type=\"primary\"\n              @click=\"handleAdd\"\n            >入仓\n            </el-button>\n          </el-col>\n          <el-col :span=\"1.5\">\n            <el-button\n              v-hasPermi=\"['system:inventory:add']\"\n              icon=\"el-icon-plus\"\n              plain\n              size=\"mini\"\n              type=\"primary\"\n              @click=\"handleAddPkg\"\n            >添加打包箱\n            </el-button>\n          </el-col>\n          <el-col :span=\"1.5\">\n            <el-button\n              v-hasPermi=\"['system:inventory:add']\"\n              :disabled=\"ids.length === 0\"\n              icon=\"el-icon-plus\"\n              plain\n              size=\"mini\"\n              type=\"primary\"\n              @click=\"handlePackingTo\"\n            >打包至\n            </el-button>\n          </el-col>\n          <el-col :span=\"1.5\">\n            <el-button @click=\"handleOpenAggregator\">数据汇总</el-button>\n            <el-dialog v-dialogDrag v-dialogDragWidth\n                       :visible.sync=\"openAggregator\" append-to-body width=\"80%\"\n            >\n              <!--<data-aggregator :data-source=\"aggregatorRctList\" :field-label-map=\"fieldLabelMap\"/>-->\n              <data-aggregator-back-ground :aggregate-function=\"listAggregatorRsInventory\"\n                                           :config-type=\"'warehouse-agg'\"\n                                           :data-source=\"aggregatorRctList\"\n                                           :data-source-type=\"'warehouse'\"\n                                           :field-label-map=\"fieldLabelMap\"\n              />\n            </el-dialog>\n          </el-col>\n          <el-col :span=\"1.5\">\n            <el-button\n              v-hasPermi=\"['system:inventory:import']\"\n              icon=\"el-icon-upload2\"\n              plain\n              size=\"mini\"\n              type=\"info\"\n              @click=\"handleImport\"\n            >导入\n            </el-button>\n          </el-col>\n          <el-col :span=\"1.5\">\n            <el-button\n              v-hasPermi=\"['system:inventory:export']\"\n              icon=\"el-icon-download\"\n              plain\n              size=\"mini\"\n              type=\"warning\"\n              @click=\"handleExport\"\n            >导出\n            </el-button>\n          </el-col>\n          <right-toolbar :showSearch.sync=\"showSearch\" @queryTable=\"getList\"></right-toolbar>\n        </el-row>\n\n        <el-table v-loading=\"loading\" :data=\"inventoryList\" @selection-change=\"handleSelectionChange\"\n                  :summary-method=\"getSummariesInventory\" @row-dblclick=\"handleUpdate\"\n                  show-summary\n                  ref=\"inventoryTable\"\n                  :load=\"loadChildInventory\"\n                  :tree-props=\"{children: 'children', hasChildren: 'hasChildren'}\"\n                  lazy\n                  row-key=\"inventoryId\"\n                  style=\"width: 100%;\"\n        >\n          <el-table-column align=\"center\" type=\"selection\" width=\"28\"/>\n          <el-table-column align=\"center\" label=\"入仓流水号\" prop=\"inboundSerialNo\" width=\"120\"/>\n          <el-table-column align=\"center\" label=\"入仓日期\" prop=\"actualInboundTime\" width=\"80\">\n            <template #default=\"scope\">\n              <span>{{ parseTime(scope.row.actualInboundTime, \"{y}-{m}-{d}\") }}</span>\n            </template>\n          </el-table-column>\n          <el-table-column align=\"center\" label=\"库存标志\" prop=\"inventoryStatus\" width=\"60\">\n            <template #default=\"scope\">\n              <span>{{\n                  scope.row.inventoryStatus == 0 ? \"在库\" : scope.row.inventoryStatus == 1 ? \"出库\" : \"被打包\"\n                }}</span>\n            </template>\n          </el-table-column>\n          <el-table-column align=\"center\" label=\"被打包至\" prop=\"packageIntoNo\" show-overflow-tooltip width=\"100\"/>\n          <!--<el-table-column align=\"center\" label=\"打包标志\" prop=\"repackedInto\" show-overflow-tooltip width=\"100\">\n            <template #default=\"scope\">\n              <span>{{\n                  scope.row.packageRecord == 0 ? \"-\" : \"打包箱\"\n                }}</span>\n            </template>\n          </el-table-column>-->\n          <el-table-column align=\"center\" label=\"打包标志\" prop=\"repackingStatus\" show-overflow-tooltip width=\"100\"/>\n          <!--<el-table-column align=\"center\" label=\"货代单号\" prop=\"forwarderNo\" show-overflow-tooltip/>-->\n          <el-table-column align=\"center\" label=\"仓租结算至\" prop=\"rentalSettlementDate\" width=\"80\">\n            <template #default=\"scope\">\n              <span>{{ parseTime(scope.row.rentalSettlementDate, \"{y}-{m}-{d}\") }}</span>\n            </template>\n          </el-table-column>\n          <el-table-column align=\"center\" label=\"客户代码\" prop=\"clientCode\" show-overflow-tooltip width=\"60\"/>\n          <el-table-column align=\"center\" label=\"分单号\" prop=\"subOrderNo\" show-overflow-tooltip width=\"200\"/>\n          <el-table-column align=\"center\" label=\"收货人名称\" prop=\"consigneeName\" show-overflow-tooltip width=\"100\"/>\n          <el-table-column align=\"center\" label=\"收货人电话\" prop=\"consigneeTel\" show-overflow-tooltip width=\"100\"/>\n          <el-table-column align=\"center\" label=\"供货商\" prop=\"supplier\" show-overflow-tooltip width=\"60\"/>\n          <el-table-column align=\"center\" label=\"司机信息\" prop=\"driverInfo\" show-overflow-tooltip width=\"120\"/>\n          <el-table-column align=\"center\" label=\"唛头\" prop=\"sqdShippingMark\" show-overflow-tooltip width=\"80\"/>\n          <el-table-column align=\"center\" label=\"总货名\" prop=\"cargoName\" show-overflow-tooltip/>\n          <el-table-column align=\"center\" label=\"总箱数\" prop=\"totalBoxes\"/>\n          <!--<el-table-column align=\"center\" label=\"包装类型\" prop=\"packageType\"/>-->\n          <el-table-column align=\"center\" label=\"总毛重\" prop=\"totalGrossWeight\"/>\n          <el-table-column align=\"center\" label=\"总体积\" prop=\"totalVolume\"/>\n          <el-table-column align=\"center\" label=\"已收供应商\" prop=\"receivedSupplier\"/>\n          <el-table-column align=\"center\" label=\"入仓费标准\" prop=\"inboundFee\"/>\n          <el-table-column align=\"center\" label=\"已收入仓费\" prop=\"receivedStorageFee\"/>\n          <el-table-column align=\"center\" label=\"未收卸货费\" prop=\"unpaidUnloadingFee\"/>\n          <el-table-column align=\"center\" label=\"实付卸货费\" prop=\"receivedUnloadingFee\"/>\n          <el-table-column align=\"center\" label=\"未收打包费\" prop=\"unpaidPackingFee\"/>\n          <el-table-column align=\"center\" label=\"实付打包费\" prop=\"receivedPackingFee\"/>\n          <el-table-column align=\"center\" label=\"物流代垫费\" prop=\"logisticsAdvanceFee\"/>\n          <el-table-column align=\"center\" label=\"免堆期\" prop=\"freeStackPeriod\"/>\n          <el-table-column align=\"center\" label=\"超期租金单价\" prop=\"overdueRentalUnitPrice\"/>\n          <el-table-column align=\"center\" label=\"超期租金\" prop=\"overdueRentalFee\"/>\n          <el-table-column align=\"center\" label=\"备注\" prop=\"notes\" show-overflow-tooltip/>\n          <el-table-column align=\"center\" class-name=\"small-padding fixed-width\" label=\"操作\">\n            <template #default=\"scope\">\n              <el-button\n                v-hasPermi=\"['system:inventory:edit']\"\n                icon=\"el-icon-edit\"\n                size=\"mini\"\n                style=\"margin-right: -8px\"\n                type=\"success\"\n                @click=\"handleUpdate(scope.row)\"\n              >修改\n              </el-button>\n              <el-button\n                v-hasPermi=\"['system:inventory:remove']\"\n                icon=\"el-icon-delete\"\n                size=\"mini\"\n                style=\"margin-right: -8px\"\n                type=\"danger\"\n                @click=\"handleDelete(scope.row)\"\n              >删除\n              </el-button>\n            </template>\n          </el-table-column>\n        </el-table>\n        <pagination\n          v-show=\"total>0\"\n          :limit.sync=\"queryParams.pageSize\"\n          :page.sync=\"queryParams.pageNum\"\n          :total=\"total\"\n          @pagination=\"getList\"\n        />\n      </el-col>\n    </el-row>\n    <!-- 添加或修改库存对话框 -->\n    <el-dialog\n      v-dialogDrag\n      v-dialogDragWidth :close-on-click-modal=\"false\" :title=\"title\"\n      :visible.sync=\"open\"\n      append-to-body width=\"70%\"\n      @open=\"loadCargoDetail\"\n    >\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" class=\"edit\" label-width=\"80px\">\n        <el-row :gutter=\"10\">\n          <el-col :span=\"5\">\n            <el-form-item label=\"流水号\" prop=\"inboundSerialNo\">\n              <el-input v-model=\"form.inboundSerialNo\" class=\"disable-form\" disabled placeholder=\"入仓流水号\"/>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"5\">\n            <el-form-item label=\"客户代码\" prop=\"clientCode\">\n              <tree-select :flat=\"false\" :multiple=\"false\" :pass=\"form.clientCode\"\n                           :placeholder=\"'客户代码'\" :type=\"'warehouseClient'\"\n                           @returnData=\"selectWarehouseClient($event)\"\n              />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"5\">\n            <el-form-item label=\"客户名称\" prop=\"clientCode\">\n              <el-input v-model=\"form.clientName\" class=\"disable-form\" disabled placeholder=\"客户名\"/>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"4\">\n            <el-form-item label=\"入仓时间\" prop=\"inboundDate\">\n              <el-date-picker v-model=\"form.actualInboundTime\"\n                              :default-value=\"new Date()\"\n                              clearable\n                              placeholder=\"实际入仓日期\"\n                              style=\"width: 100%;\"\n                              type=\"datetime\"\n                              value-format=\"yyyy-MM-dd HH:mm:ss\"\n              >\n              </el-date-picker>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"5\">\n            <el-form-item label=\"入仓费\" prop=\"forwarderNo\">\n              <el-row>\n                <el-col :span=\"12\">\n                  <el-input v-model=\"form.inboundFee\" class=\"disable-form number\" disabled placeholder=\"入仓费标准\"/>\n                </el-col>\n                <el-col :span=\"12\">\n                  <el-input v-model=\"form.receivedStorageFee\" class=\"number\" placeholder=\"已收入仓费\"/>\n                </el-col>\n              </el-row>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row :gutter=\"10\">\n          <el-col :span=\"10\">\n            <el-form-item label=\"分单号\" prop=\"subOrderNo\">\n              <el-input v-model=\"form.subOrderNo\" placeholder=\"分单号(最多只能输入八个字符)\"/>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"5\">\n            <el-form-item label=\"收货人名称\" prop=\"consigneeName\">\n              <el-input v-model=\"form.consigneeName\" placeholder=\"收货人名称\"/>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"4\">\n            <el-form-item label=\"收货人电话\" prop=\"consigneeTel\">\n              <el-input v-model=\"form.consigneeTel\" placeholder=\"收货人电话\"/>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"5\">\n            <el-form-item label=\"卸货费\" prop=\"supplier\">\n              <el-row>\n                <el-col :span=\"12\">\n                  <el-input v-model=\"form.unpaidUnloadingFee\"\n                            class=\"number\" placeholder=\"未收卸货费\"\n                  />\n                </el-col>\n                <el-col :span=\"12\">\n                  <el-input v-model=\"form.receivedUnloadingFee\" class=\"number\" placeholder=\"实付卸货费\"/>\n                </el-col>\n              </el-row>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row :gutter=\"10\">\n          <el-col :span=\"10\">\n            <el-form-item label=\"货物描述\" prop=\"cargoName\">\n              <el-input v-model=\"form.cargoName\" placeholder=\"总货名\"/>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"5\">\n            <el-form-item label=\"货物性质\" prop=\"cargoNature\">\n              <el-select v-model=\"form.cargoNature\" placeholder=\"请选择货物性质\" style=\"width: 100%\">\n                <el-option label=\"普货\" value=\"普货\"></el-option>\n                <el-option label=\"大件\" value=\"大件\"></el-option>\n                <el-option label=\"鲜活\" value=\"鲜活\"></el-option>\n                <el-option label=\"危品\" value=\"危品\"></el-option>\n                <el-option label=\"冷冻\" value=\"冷冻\"></el-option>\n                <el-option label=\"标记\" value=\"标记\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"4\">\n            <el-form-item label=\"记录方式\" prop=\"recordType\">\n              <el-select v-model=\"form.recordType\" placeholder=\"请选择记录方式\" @change=\"selectInboundFee\">\n                <el-option label=\"标准\" value=\"标准\"></el-option>\n                <el-option label=\"精确\" value=\"精确\"></el-option>\n                <el-option label=\"快递\" value=\"快递\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"5\">\n            <el-form-item label=\"打包费\">\n              <el-row>\n                <el-col :span=\"12\">\n                  <el-input v-model=\"form.unpaidPackingFee\" :class=\"form.includesPackingFee==1?'disable-form':''\"\n                            :disabled=\"form.includesPackingFee==1\" class=\"number\" placeholder=\"未收打包费\"\n                  />\n                </el-col>\n                <el-col :span=\"12\">\n                  <el-input v-model=\"form.receivedPackingFee\" class=\"number\" placeholder=\"实付打包费\"/>\n                </el-col>\n              </el-row>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row :gutter=\"10\">\n          <el-col :span=\"10\">\n            <el-form-item label=\"送货人信息\" prop=\"driverInfo\">\n              <el-input v-model=\"form.driverInfo\" placeholder=\"内容\"/>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"5\">\n            <el-form-item label=\"入仓方式\" prop=\"inboundType\">\n              <el-select v-model=\"form.inboundType\" placeholder=\"请选择包装类型\" style=\"width: 100%\">\n                <el-option label=\"入仓\" value=\"入仓\"></el-option>\n                <el-option label=\"外置\" value=\"外置\"></el-option>\n                <el-option label=\"对装\" value=\"对装\"></el-option>\n                <el-option label=\"自提\" value=\"自提\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"4\">\n            <el-form-item label=\"库存标志\" prop=\"inventoryStatus\">\n              <el-select v-model=\"form.inventoryStatus\" class=\"disable-form\" disabled placeholder=\"请选择库存标志\">\n                <el-option label=\"在仓\" value=\"0\"></el-option>\n                <el-option label=\"出库\" value=\"1\"></el-option>\n                <el-option label=\"被打包\" value=\"-1\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"5\">\n            <el-form-item label=\"物流代垫费\" prop=\"logisticsAdvanceFee\">\n              <el-input v-model=\"form.logisticsAdvanceFee\" class=\"number\" placeholder=\"物流代垫费\"/>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row :gutter=\"10\">\n          <el-col :span=\"10\">\n            <el-form-item label=\"入仓备注\" prop=\"notes\">\n              <el-input v-model=\"form.notes\" :autosize=\"{ minRows: 1}\" maxlength=\"150\" placeholder=\"内容\"\n                        show-word-limit\n                        type=\"textarea\"\n              />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"5\">\n            <el-form-item label=\"被打包至\">\n              <el-input v-model=\"form.packageIntoNo\" class=\"disable-form\" disabled\n              />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"4\">\n            <el-form-item class=\"disable-form\" disabled label=\"父级流水号\">\n              <el-input v-model=\"form.repackedInto\" class=\"disable-form\" disabled\n              />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"5\">\n            <el-form-item label=\"已收供应商\" prop=\"receivedSupplier\">\n              <el-input v-model=\"form.receivedSupplier\"\n                        :class=\"form.includesUnloadingFee==1?'disable-form':''\"\n                        :disabled=\"form.includesUnloadingFee==1\" placeholder=\"已收供应商总额\"\n              />\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row>\n          <el-col>\n            货物明细：<span style=\"color: #b7bbc2; margin: 0;\"\n          >(填写毛重/体积小计时如果单件毛重/长宽高不为0则小计不可更改,系统自动计算)</span>\n            <el-table\n              :data=\"form.rsCargoDetailsList\"\n              :summary-method=\"getSummaries\" border\n              show-summary\n              stripe style=\"width: 100%\" @row-dblclick=\"handleUpdateCargoDetail\"\n            >\n              <el-table-column align=\"center\" type=\"selection\" width=\"30\"/>\n              <el-table-column\n                align=\"center\" label=\"唛头\"\n                prop=\"shippingMark\"\n                width=\"100\"\n              >\n                <template slot-scope=\"scope\">\n                  <el-input v-model=\"scope.row.shippingMark\"></el-input>\n                </template>\n              </el-table-column>\n              <el-table-column\n                align=\"center\" label=\"货名\"\n                prop=\"itemName\"\n                width=\"100\"\n              >\n                <template slot-scope=\"scope\">\n                  <el-input v-model=\"scope.row.itemName\"></el-input>\n                </template>\n              </el-table-column>\n              <el-table-column\n                align=\"center\" label=\"英文货名\"\n                prop=\"itemName\"\n                width=\"100\"\n              >\n                <template slot-scope=\"scope\">\n                  <el-input v-model=\"scope.row.itemEnName\"></el-input>\n                </template>\n              </el-table-column>\n              <el-table-column\n                align=\"right\" class=\"no-spinner\"\n                label=\"件数\"\n                prop=\"boxCount\"\n              >\n                <template slot-scope=\"scope\">\n                  <el-row>\n                    <el-col :span=\"12\">\n                      <el-input v-model=\"scope.row.boxCount\" class=\"number\" @input=\"countCargomeasure(scope.row,'')\"/>\n                    </el-col>\n                    <el-col :span=\"12\">\n                      <el-select v-model=\"scope.row.packageType\" placeholder=\"请选择包装类型\">\n                        <el-option label=\"纸箱\" value=\"纸箱\"></el-option>\n                        <el-option label=\"木箱\" value=\"木箱\"></el-option>\n                        <el-option label=\"托盘\" value=\"托盘\"></el-option>\n                        <el-option label=\"吨袋\" value=\"吨袋\"></el-option>\n                      </el-select>\n                    </el-col>\n                  </el-row>\n                </template>\n              </el-table-column>\n              <el-table-column\n                align=\"right\" class=\"no-spinner\"\n                label=\"单箱件数\" prop=\"boxItemCount\"\n                width=\"60\"\n              >\n                <template slot-scope=\"scope\">\n                  <el-input v-model=\"scope.row.boxItemCount\" class=\"number\"\n                            @blur=\"parseInput(scope.row,'boxItemCount')\"\n                            @input=\"countCargomeasure(scope.row,'boxItemCount')\"\n                  />\n                </template>\n              </el-table-column>\n              <el-table-column\n                align=\"right\" label=\"单件毛重(KGS)\"\n                prop=\"singlePieceWeight\"\n              >\n                <template slot-scope=\"scope\">\n                  <el-input v-model=\"scope.row.singlePieceWeightFormatter\" class=\"number\"\n                            @blur=\"parseInput(scope.row,'singlePieceWeight')\"\n                            @input=\"countCargomeasure(scope.row,'singlePieceWeight')\"\n                  />\n                </template>\n              </el-table-column>\n              <el-table-column\n                align=\"right\" class=\"no-spinner\"\n                label=\"单件长(cm)\" prop=\"unitLength\"\n                width=\"70\"\n              >\n                <template slot-scope=\"scope\">\n                  <el-input v-model=\"scope.row.unitLengthFormatter\" class=\"number\"\n                            @blur=\"parseInput(scope.row,'unitLength')\"\n                            @input=\"countCargomeasure(scope.row,'unitLength')\"\n                  />\n                </template>\n              </el-table-column>\n              <el-table-column\n                align=\"right\" class=\"no-spinner\"\n                label=\"单件宽(cm)\" prop=\"unitWidth\"\n                width=\"70\"\n              >\n                <template slot-scope=\"scope\">\n                  <el-input v-model=\"scope.row.unitWidthFormatter\" @blur=\"parseInput(scope.row,'unitWidth')\"\n                            class=\"number\" @input=\"countCargomeasure(scope.row,'unitWidth')\"\n                  />\n                </template>\n              </el-table-column>\n              <el-table-column\n                align=\"right\" class=\"no-spinner\"\n                label=\"单件高(cm)\" prop=\"unitHeight\"\n                width=\"70\"\n              >\n                <template slot-scope=\"scope\">\n                  <el-input v-model=\"scope.row.unitHeightFormatter\" class=\"number\"\n                            @blur=\"parseInput(scope.row,'unitHeight')\"\n                            @input=\"countCargomeasure(scope.row,'unitHeight')\"\n                  />\n                </template>\n              </el-table-column>\n              <el-table-column\n                align=\"right\" class=\"no-spinner\"\n                label=\"单件体积(CBM)\"\n                prop=\"singlePieceVolume\"\n              >\n                <template slot-scope=\"scope\">\n                  <el-input v-model=\"scope.row.singlePieceVolumeFormatter\" class=\"number\"\n                            @blur=\"parseInput(scope.row,'singlePieceVolume')\"\n                            @input=\"countCargomeasure(scope.row,'singlePieceVolume')\"\n                  />\n                </template>\n              </el-table-column>\n              <el-table-column\n                align=\"right\" class=\"no-spinner\"\n                label=\"件数小计\" prop=\"subtotalItemCount\"\n                width=\"60\"\n              >\n                <template slot-scope=\"scope\">\n                  <el-input v-model=\"scope.row.subtotalItemCount\" class=\"number\"\n                            @blur=\"parseInput(scope.row,'subtotalItemCount')\"\n                            @input=\"countCargomeasure(scope.row,'subtotalItemCount')\"\n                  />\n                </template>\n              </el-table-column>\n              <el-table-column\n                align=\"right\" class=\"no-spinner\"\n                label=\"毛重小计(KGS)\"\n                prop=\"unitGrossWeight\"\n              >\n                <template slot-scope=\"scope\">\n                  <el-input v-model=\"scope.row.unitGrossWeightFormatter\" class=\"number\"\n                            @blur=\"parseInput(scope.row,'unitGrossWeight')\"\n                            @input=\"countCargomeasure(scope.row,'unitGrossWeight')\"\n                  />\n                </template>\n              </el-table-column>\n              <el-table-column\n                align=\"right\" class=\"no-spinner\"\n                label=\"体积小计(CBM)\"\n                prop=\"unitVolume\"\n              >\n                <template slot-scope=\"scope\">\n                  <el-input v-model=\"scope.row.unitVolumeFormatter\" class=\"number\"\n                            @blur=\"parseInput(scope.row,'unitVolume')\"\n                            @input=\"countCargomeasure(scope.row,'unitVolume')\"\n                  />\n                </template>\n              </el-table-column>\n              <el-table-column\n                align=\"right\" label=\"破损标志\"\n                prop=\"damageStatus\" width=\"60\"\n              >\n                <template slot-scope=\"scope\">\n                  <el-input v-model=\"scope.row.damageStatus\"></el-input>\n                </template>\n              </el-table-column>\n              <el-table-column align=\"center\" class-name=\"small-padding fixed-width\" label=\"操作\" width=\"48px\">\n                <template slot-scope=\"scope\">\n                  <div style=\"height: 15px;padding: 0;margin: 0\">\n                    <el-button icon=\"el-icon-delete\"\n                               style=\"display: flex\" type=\"danger\"\n                               @click=\"deleteCargoDetail(scope.row)\"\n                    >删除\n                    </el-button>\n                  </div>\n                </template>\n              </el-table-column>\n            </el-table>\n          </el-col>\n          <el-button style=\"padding: 0\"\n                     type=\"text\"\n                     @click=\"addCargoDetail\"\n          >[＋]\n          </el-button>\n        </el-row>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <div style=\"float: left\">\n          <span>仓管：{{ form.sqdInboundHandler }}</span>\n        </div>\n\n        <el-button type=\"primary\" @click=\"submitForm\">保 存</el-button>\n        <el-button type=\"primary\" @click=\"printInboundBill('旧模板')\">打印旧版入仓单</el-button>\n        <el-button type=\"primary\" @click=\"printInboundBill('新模板')\">打印新版入仓单</el-button>\n        <el-button @click=\"cancel\">关 闭</el-button>\n      </div>\n    </el-dialog>\n\n    <!-- 添加或修改打包对话框 -->\n    <el-dialog\n      v-dialogDrag\n      v-dialogDragWidth :close-on-click-modal=\"false\" :title=\"title\"\n      :visible.sync=\"openPkg\"\n      append-to-body width=\"70%\"\n      @open=\"loadPkgDetail\"\n    >\n      <el-form ref=\"form\" :model=\"form\" :rules=\"rules\" class=\"edit\" label-width=\"80px\">\n        <el-row :gutter=\"10\">\n          <el-col :span=\"5\">\n            <el-form-item label=\"流水号\" prop=\"inboundSerialNo\">\n              <el-input v-model=\"form.inboundSerialNo\" class=\"disable-form\" disabled placeholder=\"入仓流水号\"/>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"5\">\n            <el-form-item label=\"客户代码\" prop=\"clientCode\">\n              <tree-select :flat=\"false\" :multiple=\"false\" :pass=\"form.clientCode\"\n                           :placeholder=\"'客户代码'\" :type=\"'warehouseClient'\"\n                           @returnData=\"selectWarehouseClient($event)\"\n              />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"5\">\n            <el-form-item label=\"客户名称\" prop=\"clientCode\">\n              <el-input v-model=\"form.clientName\" class=\"disable-form\" disabled placeholder=\"客户名\"/>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"5\">\n            <el-form-item label=\"分单号\" prop=\"subOrderNo\">\n              <el-input v-model=\"form.subOrderNo\" :class=\"this.form.inventoryId!==null?'disable-form':''\"\n                        :disabled=\"this.form.inventoryId!==null\" placeholder=\"分单号(最多只能输入八个字符)\"\n              />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"4\">\n            <el-form-item label=\"生成时间\" prop=\"inboundDate\">\n              <el-date-picker v-model=\"form.actualInboundTime\"\n                              :default-value=\"new Date()\"\n                              clearable\n                              placeholder=\"实际入仓日期\"\n                              style=\"width: 100%;\"\n                              type=\"datetime\"\n                              value-format=\"yyyy-MM-dd HH:mm:ss\"\n              >\n              </el-date-picker>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row :gutter=\"10\">\n          <el-col :span=\"10\">\n            <el-form-item label=\"入仓备注\" prop=\"notes\">\n              <el-input v-model=\"form.notes\" :autosize=\"{ minRows: 1}\" maxlength=\"150\" placeholder=\"内容\"\n                        show-word-limit\n                        type=\"textarea\"\n              />\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"5\">\n            <el-form-item label=\"打包标志\" prop=\"inventoryStatus\">\n              <el-input v-model=\"form.repackingStatus\" class=\"disable-form\" disabled/>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"5\">\n            <el-button type=\"primary\" @click=\"pkgFinish\"\n            >{{ form.repackingStatus === \"打包完\" ? \"取消打包\" : \"打包完成\" }}\n            </el-button>\n          </el-col>\n          <el-col :span=\"4\">\n            <el-form-item label=\"库存标志\" prop=\"inventoryStatus\">\n              <el-select v-model=\"form.inventoryStatus\" class=\"disable-form\" disabled placeholder=\"请选择库存标志\">\n                <el-option label=\"在仓\" value=\"0\"></el-option>\n                <el-option label=\"出库\" value=\"1\"></el-option>\n                <el-option label=\"被打包\" value=\"-1\"></el-option>\n              </el-select>\n            </el-form-item>\n          </el-col>\n        </el-row>\n        <el-row :gutter=\"10\">\n          <el-col :span=\"5\">\n            <el-form-item label=\"打包箱重量\" prop=\"receivedSupplier\">\n              <el-input v-model=\"form.totalGrossWeight\" placeholder=\"打包箱重量\"/>\n            </el-form-item>\n          </el-col>\n          <el-col :span=\"5\">\n            <el-form-item label=\"打包箱体积\" prop=\"receivedSupplier\">\n              <el-input v-model=\"form.totalVolume\" placeholder=\"打包箱体积\"/>\n            </el-form-item>\n          </el-col>\n        </el-row>\n\n        <el-row>\n          <el-col>\n            包装箱明细：\n            <el-table\n              :data=\"pkgDetailsList\"\n              :summary-method=\"getSummaries\" border\n              show-summary @selection-change=\"handlePkgSelectionChange\"\n              stripe style=\"width: 100%\" @row-dblclick=\"handleUpdateCargoDetail\"\n            >\n              <el-table-column align=\"center\" type=\"selection\" width=\"30\"/>\n              <el-table-column align=\"center\" label=\"入仓流水号\" prop=\"inboundSerialNo\" width=\"100\"/>\n              <el-table-column align=\"center\" label=\"入仓日期\" prop=\"actualInboundTime\" width=\"80\">\n                <template slot-scope=\"scope\">\n                  <span>{{ parseTime(scope.row.actualInboundTime, \"{y}-{m}-{d}\") }}</span>\n                </template>\n              </el-table-column>\n              <el-table-column align=\"center\" label=\"库存标志\" prop=\"inventoryStatus\" width=\"60\">\n                <template slot-scope=\"scope\">\n              <span>{{\n                  scope.row.inventoryStatus == 0 ? \"在库\" : scope.row.inventoryStatus == 1 ? \"出库\" : \"被打包\"\n                }}</span>\n                </template>\n              </el-table-column>\n              <el-table-column align=\"center\" label=\"被打包至\" prop=\"packageIntoNo\" show-overflow-tooltip width=\"100\"/>\n              <el-table-column align=\"center\" label=\"打包标志\" prop=\"repackedInto\" show-overflow-tooltip width=\"100\">\n                <template slot-scope=\"scope\">\n              <span>{{\n                  scope.row.packageRecord == 0 ? \"-\" : \"打包箱\"\n                }}</span>\n                </template>\n              </el-table-column>\n              <!--<el-table-column align=\"center\" label=\"货代单号\" prop=\"forwarderNo\" show-overflow-tooltip/>-->\n              <el-table-column align=\"center\" label=\"仓租结算至\" prop=\"rentalSettlementDate\" width=\"80\">\n                <template slot-scope=\"scope\">\n                  <span>{{ parseTime(scope.row.rentalSettlementDate, \"{y}-{m}-{d}\") }}</span>\n                </template>\n              </el-table-column>\n              <el-table-column align=\"center\" label=\"客户代码\" prop=\"clientCode\" show-overflow-tooltip width=\"60\"/>\n              <el-table-column align=\"center\" label=\"分单号\" prop=\"subOrderNo\" show-overflow-tooltip width=\"200\"/>\n              <el-table-column align=\"center\" label=\"收货人名称\" prop=\"consigneeName\" show-overflow-tooltip\n                               width=\"100\"\n              />\n              <el-table-column align=\"center\" label=\"收货人电话\" prop=\"consigneeTel\" show-overflow-tooltip width=\"100\"/>\n              <el-table-column align=\"center\" label=\"供货商\" prop=\"supplier\" show-overflow-tooltip width=\"60\"/>\n              <el-table-column align=\"center\" label=\"司机信息\" prop=\"driverInfo\" show-overflow-tooltip width=\"120\"/>\n              <el-table-column align=\"center\" label=\"唛头\" prop=\"sqdShippingMark\" show-overflow-tooltip width=\"80\"/>\n              <el-table-column align=\"center\" label=\"货物性质\" prop=\"cargoNature\" show-overflow-tooltip/>\n              <el-table-column align=\"center\" label=\"总货名\" prop=\"cargoName\" show-overflow-tooltip/>\n              <el-table-column align=\"center\" label=\"总箱数\" prop=\"totalBoxes\"/>\n              <!--<el-table-column align=\"center\" label=\"包装类型\" prop=\"packageType\"/>-->\n              <el-table-column align=\"center\" label=\"总毛重\" prop=\"totalGrossWeight\"/>\n              <el-table-column align=\"center\" label=\"总体积\" prop=\"totalVolume\"/>\n              <el-table-column align=\"center\" label=\"已收供应商\" prop=\"receivedSupplier\"/>\n              <el-table-column align=\"center\" label=\"入仓费标准\" prop=\"inboundFee\"/>\n              <el-table-column align=\"center\" label=\"已收入仓费\" prop=\"receivedStorageFee\"/>\n              <el-table-column align=\"center\" label=\"未收卸货费\" prop=\"unpaidUnloadingFee\"/>\n              <el-table-column align=\"center\" label=\"实付卸货费\" prop=\"receivedUnloadingFee\"/>\n              <el-table-column align=\"center\" label=\"未收打包费\" prop=\"unpaidPackingFee\"/>\n              <el-table-column align=\"center\" label=\"实付打包费\" prop=\"receivedPackingFee\"/>\n              <el-table-column align=\"center\" label=\"物流代垫费\" prop=\"logisticsAdvanceFee\"/>\n              <el-table-column align=\"center\" label=\"免堆期\" prop=\"freeStackPeriod\"/>\n              <el-table-column align=\"center\" label=\"超期租金单价\" prop=\"overdueRentalUnitPrice\"/>\n              <el-table-column align=\"center\" label=\"超期租金\" prop=\"overdueRentalFee\"/>\n              <el-table-column align=\"center\" label=\"备注\" prop=\"notes\" show-overflow-tooltip/>\n            </el-table>\n          </el-col>\n          <el-button style=\"padding: 0\"\n                     type=\"text\"\n                     @click=\"addCargoDetail\"\n          >[＋]\n          </el-button>\n        </el-row>\n      </el-form>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <div style=\"float: left\">\n          <span>仓管：{{ form.sqdInboundHandler }}</span>\n        </div>\n\n        <el-button :class=\"form.repackingStatus==='打包完'?'disable-form':''\"\n                   :disabled=\"form.repackingStatus==='打包完'\" type=\"danger\"\n                   @click=\"pkgCancel\"\n        >移出打包箱\n        </el-button>\n        <el-button type=\"primary\" @click=\"submitForm('pkg')\">保 存</el-button>\n        <el-button @click=\"openPkg=false\">关 闭</el-button>\n      </div>\n    </el-dialog>\n\n    <!-- 添加打包对话框 -->\n    <el-dialog\n      v-dialogDrag\n      v-dialogDragWidth :close-on-click-modal=\"false\" :title=\"title\"\n      :visible.sync=\"openPkgTo\"\n      append-to-body width=\"30%\"\n      @open=\"loadPkgToList\"\n    >\n\n      <el-select\n        v-model=\"form.packageTo\"\n        class=\"field-select\"\n        filterable\n        placeholder=\"请选择搜索字段\"\n      >\n        <el-option\n          v-for=\"pkg in pkgList\"\n          :key=\"pkg.inventoryId\"\n          :label=\"pkg.subOrderNo\"\n          :value=\"pkg.inventoryId\"\n        />\n      </el-select>\n\n      <el-button type=\"primary\" @click=\"packingTo\">保 存</el-button>\n    </el-dialog>\n\n    <!-- 预览 -->\n    <print-preview ref=\"preView\"/>\n\n    <!--    // excel 上传导入组件-->\n    <el-dialog\n      v-dialogDrag\n      v-dialogDragWidth :close-on-click-modal=\"false\" :modal-append-to-body=\"false\" :title=\"upload.title\"\n      :visible.sync=\"upload.open\"\n      append-to-body width=\"400px\"\n    >\n      <el-upload\n        ref=\"upload\"\n        :action=\"upload.url + '?updateSupport=' + upload.updateSupport\"\n        :auto-upload=\"false\"\n        :disabled=\"upload.isUploading\"\n        :headers=\"upload.headers\"\n        :limit=\"1\"\n        :on-progress=\"handleFileUploadProgress\"\n        :on-success=\"handleFileSuccess\"\n        accept=\".xlsx, .xls\"\n        drag\n      >\n        <i class=\"el-icon-upload\"></i>\n        <div class=\"el-upload__text\">将文件拖到此处，或<em>点击上传</em></div>\n        <div slot=\"tip\" class=\"el-upload__tip text-center\">\n          <div slot=\"tip\" class=\"el-upload__tip\">\n            <el-checkbox v-model=\"upload.updateSupport\"/>\n            是否更新已经存在的用户数据\n          </div>\n          <span>仅允许导入xls、xlsx格式文件。</span>\n        </div>\n      </el-upload>\n      <div slot=\"footer\" class=\"dialog-footer\">\n        <el-button type=\"primary\" @click=\"submitFileForm\">确 定</el-button>\n        <el-button @click=\"upload.open = false\">取 消</el-button>\n      </div>\n    </el-dialog>\n  </div>\n</template>\n\n<script>\nimport {\n  addInventory, cancelPkg,\n  changeStatus,\n  delInventory,\n  getInventory, getPackage, listAggregatorRsInventory,\n  listInventory, packUp,\n  updateInventory\n} from \"@/api/system/inventory\"\nimport {defaultElementTypeProvider, hiprint} from \"@\"\nimport warehouseReceipt from \"@/print-template/warehouseReceipt\"\nimport warehouseReceiptNew from \"@/print-template/warehouseReceiptNew\"\nimport printPreview from \"@/views/print/demo/design/preview.vue\"\nimport PrintTemplate from \"@/views/system/print/PrintTemplate.vue\"\nimport currency from \"currency.js\"\nimport log from \"@/views/monitor/job/log.vue\"\nimport moment from \"moment/moment\"\nimport store from \"@/store\"\nimport {parseTime} from \"../../../utils/rich\"\nimport DataAggregatorBackGround from \"@/views/system/DataAggregatorBackGround/index.vue\"\nimport {rctFieldLabelMap} from \"@/config/rctFieldLabelMap\"\nimport {rsInventoryFieldLabelMap} from \"@/config/rsInventoryFieldLabelMap\"\nimport {listAggregatorRct} from \"@/api/system/rct\"\nimport {getToken} from \"@/utils/auth\"\n\nlet hiprintTemplate\nexport default {\n  name: \"Inventory\",\n  data() {\n    const validateInboundSerialNo = (rule, value, callback) => {\n      console.log(value)\n      /* if (value === '') {\n        callback(new Error('请输入流水号'));\n      } else {\n        if (this.form.inboundSerialNoSub !== '') {\n          this.$refs.form.validateField('inboundSerialNo');\n        }\n        callback();\n      } */\n    }\n    return {\n      cargoDetailOpen: false,\n      cargoDetailRow: {\n        cargoDetailsId: null,\n        inboundSerialNo: null,\n        inboundSerialSplit: null,\n        clientCode: null,\n        shippingMark: null,\n        itemName: null,\n        boxCount: null,\n        boxItemCount: null,\n        subtotalItemCount: null,\n        expressDate: moment().format(\"yyyy-MM-DD\"),\n        additionalFee: null,\n        packageType: \"纸箱\",\n        unitGrossWeight: null,\n        unitLength: null,\n        unitWidth: null,\n        unitHeight: null,\n        unitVolume: null,\n        damageStatus: \"0\",\n        barcode: null\n      },\n      showLeft: 0,\n      showRight: 24,\n      // 遮罩层\n      loading: true,\n      // 选中数组\n      ids: [],\n      aggregatorRctList: [],\n      fieldLabelMap: rsInventoryFieldLabelMap,\n      // 非单个禁用\n      single: true,\n      openAggregator: false,\n      // 非多个禁用\n      multiple: true,\n      selectedInventoryList: [],\n      selectedPkgList: [],\n      // 显示搜索条件\n      showSearch: false,\n      // 总条数\n      total: 0,\n      // 库存表格数据\n      inventoryList: [],\n      outboundList: [],\n      // 弹出层标题\n      title: \"\",\n      openPkgTo: false,\n\n      outboundType: null,\n\n      // 是否显示弹出层\n      open: false,\n      openPkg: false,\n      pkgList: [],\n      // 用户导入参数\n      upload: {\n        // 是否显示弹出层（用户导入）\n        open: false,\n        // 弹出层标题（用户导入）\n        title: \"\",\n        // 是否禁用上传\n        isUploading: false,\n        // 是否更新已经存在的用户数据\n        updateSupport: true,\n        // 设置上传的请求头部\n        headers: {Authorization: \"Bearer \" + getToken()},\n        // 上传的地址\n        url: process.env.VUE_APP_BASE_API + \"/system/inventory/importData\"\n      },\n      // 查询参数\n      queryParams: {\n        pageNum: 1,\n        pageSize: 20,\n        inventoryStatus: null,\n        inboundSerialNo: null,\n        inboundSerialSplit: null,\n        inboundDate: null,\n        outboundNo: null,\n        forwarderNo: null,\n        rentalSettlementDate: null,\n        outboundDate: null,\n        clientCode: null,\n        subOrderNo: null,\n        supplier: null,\n        driverInfo: null,\n        sqdShippingMark: null,\n        cargoName: null,\n        totalBoxes: null,\n        packageType: null,\n        totalGrossWeight: null,\n        totalVolume: null,\n        damageStatus: null,\n        storageLocation1: null,\n        storageLocation2: null,\n        storageLocation3: null,\n        receivedStorageFee: null,\n        unpaidUnloadingFee: null,\n        logisticsAdvanceFee: null,\n        rentalBalanceFee: null,\n        freeStackPeriod: null,\n        overdueRentalUnitPrice: null,\n        overdueRentalFee: null,\n        notes: null,\n        isTopLevel: true // 默认只查询顶层数据\n      },\n      // 表单参数\n      form: {},\n      pkgDetailsList: [],\n      // 表单校验\n      rules: {\n        // cargoName: [\n        //   { required: true, message: \"请输入货物描述\", trigger: \"blur\" }\n        // ],\n        // consigneeName: [\n        //   { required: true, message: \"请输入收货人名称\", trigger: \"blur\" }\n        // ],\n        // consigneeTel: [\n        //   { required: true, message: \"请输入收货人电话\", trigger: \"blur\" }\n        // ],\n        subOrderNo: [\n          {\n            validator: (rule, value, callback) => {\n              if (!value) {\n                callback()\n                return\n              }\n              if (!this.form.clientCode) {\n                callback(new Error(\"请先选择客户代码\"))\n                return\n              }\n              const prefix = `${this.form.clientCode}-`\n              if (!value.startsWith(prefix)) {\n                callback(new Error(`分单号必须以 ${prefix} 开头`))\n                return\n              }\n              callback()\n            },\n            trigger: \"blur\"\n          }\n        ]\n      },\n      selectedClient: null\n    }\n  },\n  components: {\n    DataAggregatorBackGround,\n    printPreview,\n    PrintTemplate\n  },\n  watch: {\n    showSearch(n) {\n      if (n === true) {\n        this.showRight = 21\n        this.showLeft = 3\n      } else {\n        this.showRight = 24\n        this.showLeft = 0\n      }\n    }\n  },\n  mounted() {\n    this.initPrint()\n  },\n  created() {\n    this.getList()\n  },\n  methods: {\n    // 提交上传文件\n    submitFileForm() {\n      this.$refs.upload.submit()\n    },\n    // 文件上传中处理\n    handleFileUploadProgress(event, file, fileList) {\n      this.upload.isUploading = true\n    },\n    // 文件上传成功处理\n    handleFileSuccess(response, file, fileList) {\n      this.upload.open = false\n      this.upload.isUploading = false\n      this.$refs.upload.clearFiles()\n      this.$message.info(response.msg)\n      if (response.msg != \"全部上传成功\") {\n        this.download(\"system/inventory/failList\", {}, `上传失败列表.xlsx`)\n      }\n      this.getList()\n    },\n    loadPkgDetail() {\n      if (this.form.clientCode) {\n        listInventory({packageTo: this.form.inventoryId}).then(response => {\n          this.pkgDetailsList = response.rows\n        })\n      }\n    },\n    // 加载子节点数据\n    loadChildInventory(tree, treeNode, resolve) {\n      // 使用packageTo字段查询子节点\n      listInventory({packageTo: tree.inventoryId}).then(response => {\n        const rows = response.rows\n\n        // 先将数据传递给表格，确保子节点渲染\n        resolve(rows)\n        tree.children = rows\n\n        // 如果父项被选中，在子节点渲染完成后选中它们\n        if (this.ids.includes(tree.inventoryId)) {\n          setTimeout(() => {\n            rows.forEach(child => {\n              if (!this.ids.includes(child.inventoryId)) {\n                this.ids.push(child.inventoryId)\n                this.selectedInventoryList.push(child)\n              }\n              // 在UI上选中子项\n              this.$refs.inventoryTable.toggleRowSelection(child, true)\n            })\n          }, 50) // 等待DOM更新\n        }\n      })\n    },\n    handleAddPkg() {\n      this.reset()\n      this.openPkg = true\n    },\n    handlePackingTo() {\n      // 检查是否选择了货物\n      if (this.ids.length === 0) {\n        this.$message.warning(\"请先选择需要打包的货物\")\n        return\n      }\n\n      // 检查是否为同一客户的货物\n      const firstClientCode = this.selectedInventoryList[0].clientCode\n      const isSameClient = this.selectedInventoryList.every(item => item.clientCode === firstClientCode)\n      const isPackaged = this.selectedInventoryList.every(item => item.packageIntoNo == null)\n\n      if (!isSameClient) {\n        this.$message.warning(\"只能打包同一个客户的货物\")\n        return\n      }\n      if (!isPackaged) {\n        this.$message.warning(\"有货物已被打包\")\n        return\n      }\n\n      // 打开打包箱选择对话框\n      this.reset()\n      this.form.clientCode = firstClientCode\n      this.form.clientName = this.selectedInventoryList[0].clientName\n      this.form.repackingStatus = \"打包中\"\n\n      // 使用准确的当前时间，不进行取整\n      const now = new Date()\n      this.form.actualInboundTime = now\n\n      this.form.inventoryStatus = \"0\"\n      this.form.subOrderNo = firstClientCode + \"-\"\n\n      // 记录被打包的货物ID\n      this.form.packingSourceIds = this.ids\n\n      this.title = \"打包装箱至\"\n      this.openPkgTo = true\n    },\n    pkgCancel() {\n      cancelPkg(this.selectedPkgList).then(response => {\n        this.$message.success(\"移出成功\")\n        this.openPkg = false\n        this.getList()\n      })\n    },\n    loadPkgToList() {\n      getPackage({clientCode: this.form.clientCode, repackingStatus: \"打包中\"}).then(response => {\n        this.pkgList = response.data\n      })\n    },\n    packingTo() {\n      this.selectedInventoryList.forEach(item => {\n        item.packageTo = this.form.packageTo\n        item.repackingStatus = \"被打包\"\n      })\n\n      packUp(this.selectedInventoryList).then(response => {\n        this.$message.success(\"打包成功\")\n        this.openPkgTo = false\n        this.getList()\n      })\n    },\n    parseTime,\n    handleBlur() {\n      // 判断长度是否小于4位，若是则补齐0\n      this.form.inboundSerialNoSub = this.form.inboundSerialNoSub.padStart(4, \"0\")\n    },\n    selectInboundFee() {\n      switch (this.form.recordType) {\n        case \"标准\":\n          this.form.inboundFee = this.selectedClient.standardInboundFee\n          break\n        case \"精确\":\n          this.form.inboundFee = this.selectedClient.preciseInboundFee\n          break\n        case \"快递\":\n          this.form.inboundFee = this.selectedClient.expressInboundFee\n          break\n      }\n    },\n    cmToCbm(cmVolume) {\n      return currency(cmVolume).divide(1_000_000).value // 1 CBM = 1,000,000 cm³\n    },\n    // 格式化为带逗号和小数的字符串\n    formatNumber(value) {\n      return currency(value, {symbol: \"\", precision: 2}).format() // eg: 1234.56 => \"1,234.56\"\n    },\n    // 当用户输入时实时格式化，但保留光标位置\n    formatInput(e) {\n      const rawValue = e.replace(/,/g, \"\") // 去除逗号\n      if (!isNaN(rawValue)) {\n        this.amount = parseFloat(rawValue) // 更新原始值\n        return this.formatNumber(rawValue)\n      }\n    },\n    loadCargoDetail() {\n      this.form.rsCargoDetailsList.map(item => {\n        this.updateFormatter(item)\n      })\n\n      this.$forceUpdate()\n    },\n    updateFormatter(row) {\n      row.singlePieceWeightFormatter = this.formatNumber(row.singlePieceWeight)\n      row.unitLengthFormatter = this.formatNumber(row.unitLength)\n      row.unitHeightFormatter = this.formatNumber(row.unitHeight)\n      row.singlePieceVolumeFormatter = this.formatNumber(row.singlePieceVolume)\n      row.unitGrossWeightFormatter = this.formatNumber(row.unitGrossWeight)\n      row.unitVolumeFormatter = this.formatNumber(row.unitVolume)\n      row.unitWidthFormatter = this.formatNumber(row.unitWidth)\n      row.boxItemCountFormatter = Number(row.boxItemCount) ? Number(row.boxItemCount) : 0\n      row.subtotalItemCountFormatter = Number(row.subtotalItemCount) ? Number(row.subtotalItemCount) : 0\n    },\n    // 输入失焦时确保格式正确\n    parseInput(row, type) {\n      if (!row || typeof row !== \"object\") return // 空值校验：row 不存在或非对象直接返回\n      switch (type) {\n        case \"singlePieceWeight\":\n          const singlePieceWeightFormatterValue = String(row.singlePieceWeightFormatter || \"\").replace(/,/g, \"\") // 空值安全处理\n          if (!isNaN(singlePieceWeightFormatterValue) && singlePieceWeightFormatterValue !== \"\") {\n            row.singlePieceWeightFormatter = this.formatNumber(singlePieceWeightFormatterValue)\n          }\n          break\n        case \"unitLength\":\n          const unitLengthValue = String(row.unitLengthFormatter || \"\").replace(/,/g, \"\") // 空值安全处理\n          if (!isNaN(unitLengthValue) && unitLengthValue !== \"\") {\n            row.unitLengthFormatter = this.formatNumber(unitLengthValue)\n          }\n          break\n        case \"unitWidth\":\n          const unitWidthValue = String(row.unitWidthFormatter || \"\").replace(/,/g, \"\") // 空值安全处理\n          if (!isNaN(unitWidthValue) && unitWidthValue !== \"\") {\n            row.unitWidthFormatter = this.formatNumber(unitWidthValue)\n          }\n          break\n        case \"unitHeight\":\n          const unitHeightFormatterValue = String(row.unitHeightFormatter || \"\").replace(/,/g, \"\") // 空值安全处理\n          if (!isNaN(unitHeightFormatterValue) && unitHeightFormatterValue !== \"\") {\n            row.unitHeightFormatter = this.formatNumber(unitHeightFormatterValue)\n          }\n          break\n        case \"singlePieceVolume\":\n          const singlePieceVolumeFormatterValue = String(row.singlePieceVolumeFormatter || \"\").replace(/,/g, \"\") // 空值安全处理\n          if (!isNaN(singlePieceVolumeFormatterValue) && singlePieceVolumeFormatterValue !== \"\") {\n            row.singlePieceVolumeFormatter = this.formatNumber(singlePieceVolumeFormatterValue)\n          }\n          break\n        case \"unitGrossWeight\":\n          const unitGrossWeightFormatterValue = String(row.unitGrossWeightFormatter || \"\").replace(/,/g, \"\") // 空值安全处理\n          if (!isNaN(unitGrossWeightFormatterValue) && unitGrossWeightFormatterValue !== \"\") {\n            row.unitGrossWeightFormatter = this.formatNumber(unitGrossWeightFormatterValue)\n          }\n          break\n        case \"unitVolume\":\n          const unitVolumeFormatterValue = String(row.unitVolumeFormatter || \"\").replace(/,/g, \"\") // 空值安全处理\n          if (!isNaN(unitVolumeFormatterValue) && unitVolumeFormatterValue !== \"\") {\n            row.unitVolumeFormatter = this.formatNumber(unitVolumeFormatterValue)\n          }\n          break\n      }\n    },\n    countCargomeasure(row, type) {\n      if (!row || typeof row !== \"object\") return // 空值校验：row 不存在或非对象直接返回\n      switch (type) {\n        case \"singlePieceWeight\":\n          const singlePieceWeightFormatterValue = String(row.singlePieceWeightFormatter || \"\").replace(/,/g, \"\") // 空值安全处理\n          if (!isNaN(singlePieceWeightFormatterValue) && singlePieceWeightFormatterValue !== \"\") {\n            row.singlePieceWeight = parseFloat(singlePieceWeightFormatterValue) // 更新原始值\n          }\n          break\n        case \"unitLength\":\n          const unitLengthValue = String(row.unitLengthFormatter || \"\").replace(/,/g, \"\") // 空值安全处理\n          if (!isNaN(unitLengthValue) && unitLengthValue !== \"\") {\n            row.unitLength = parseFloat(unitLengthValue) // 更新原始值\n          }\n          break\n        case \"unitWidth\":\n          const unitWidthValue = String(row.unitWidthFormatter || \"\").replace(/,/g, \"\") // 空值安全处理\n          if (!isNaN(unitWidthValue) && unitWidthValue !== \"\") {\n            row.unitWidth = parseFloat(unitWidthValue) // 更新原始值\n          }\n          break\n        case \"unitHeight\":\n          const unitHeightFormatterValue = String(row.unitHeightFormatter || \"\").replace(/,/g, \"\") // 空值安全处理\n          if (!isNaN(unitHeightFormatterValue) && unitHeightFormatterValue !== \"\") {\n            row.unitHeight = parseFloat(unitHeightFormatterValue) // 更新原始值\n          }\n          break\n        case \"singlePieceVolume\":\n          const singlePieceVolumeFormatterValue = String(row.singlePieceVolumeFormatter || \"\").replace(/,/g, \"\") // 空值安全处理\n          if (!isNaN(singlePieceVolumeFormatterValue) && singlePieceVolumeFormatterValue !== \"\") {\n            row.singlePieceVolume = parseFloat(singlePieceVolumeFormatterValue) // 更新原始值\n          }\n          break\n        case \"unitGrossWeight\":\n          const unitGrossWeightFormatterValue = String(row.unitGrossWeightFormatter || \"\").replace(/,/g, \"\") // 空值安全处理\n          if (!isNaN(unitGrossWeightFormatterValue) && unitGrossWeightFormatterValue !== \"\") {\n            row.unitGrossWeight = parseFloat(unitGrossWeightFormatterValue) // 更新原始值\n          }\n          break\n        case \"unitVolume\":\n          const unitVolumeFormatterValue = String(row.unitVolumeFormatter || \"\").replace(/,/g, \"\") // 空值安全处理\n          if (!isNaN(unitVolumeFormatterValue) && unitVolumeFormatterValue !== \"\") {\n            row.unitVolume = parseFloat(unitVolumeFormatterValue) // 更新原始值\n          }\n          break\n      }\n\n      // 更新相关字段\n      this.form.rsCargoDetailsList = this.form.rsCargoDetailsList.map(item => {\n        if (row === item) {\n          if (item.unitLength && item.unitWidth && item.unitHeight) {\n            const cm = currency(item.unitLength).multiply(item.unitWidth).multiply(item.unitHeight).value\n            item.singlePieceVolume = this.cmToCbm(cm)\n            item.singlePieceVolumeFormatter = this.formatNumber(item.singlePieceVolume)\n          }\n          if (item.singlePieceVolume && item.boxCount) {\n            item.unitVolume = currency(item.singlePieceVolume).multiply(item.boxCount).value\n            item.unitVolumeFormatter = this.formatNumber(item.unitVolume)\n          }\n          if (item.singlePieceWeight && item.boxCount) {\n            item.unitGrossWeight = currency(item.singlePieceWeight).multiply(item.boxCount).value\n            item.unitGrossWeightFormatter = this.formatNumber(item.unitGrossWeight)\n          }\n          // 件数小计\n          if (item.boxItemCount) {\n            if (item.boxCount) {\n              item.subtotalItemCount = currency(item.boxItemCount).multiply(item.boxCount).value\n            } else {\n              item.subtotalItemCount = currency(item.boxItemCount).value\n            }\n          }\n        }\n        return item\n      })\n\n      // this.$forceUpdate()\n    },\n    selectWarehouseClient(row) {\n      this.form.clientCode = row.clientCode\n      this.form.clientName = row.clientName\n      this.form.overdueRentalUnitPrice = row.overdueRent\n      this.form.freeStackPeriod = row.freeStackPeriod\n      this.selectedClient = row\n      /* if (row.clientType === \"直客\") {\n        this.form.inboundSerialNoPre = \"No.90\"\n      } else {\n        this.form.inboundSerialNoPre = \"No.80\"\n      } */\n      if (row.includesUnloadingFee != null) {\n        this.form.includesUnloadingFee = row.includesUnloadingFee\n        /* if (row.includesUnloadingFee == 1) {\n          this.form.unpaidUnloadingFee = 0\n        } */\n      }\n      if (row.includesPackingFee != null) {\n        this.form.includesPackingFee = row.includesPackingFee\n        if (row.includesPackingFee == 1) {\n          this.form.unpaidPackingFee = 0\n        }\n      }\n      if (row.includesInboundFee != null) {\n        this.form.includesInboundFee = row.includesInboundFee\n        if (row.includesInboundFee == 1) {\n          this.form.inboundFee = 0\n        }\n      }\n      if (row.immediatePaymentFee) {\n        this.form.immediatePaymentFee = row.immediatePaymentFee\n      }\n      // 根据记录方式选择入仓标准费\n      if (this.form.recordType && row.includesInboundFee == 0) {\n        switch (this.form.recordType) {\n          case \"标准\":\n            this.form.inboundFee = row.standardInboundFee\n            break\n          case \"精确\":\n            this.form.inboundFee = row.preciseInboundFee\n            break\n          case \"快递\":\n            this.form.inboundFee = row.expressInboundFee\n            break\n        }\n      }\n\n      // 当客户代码改变时,清空分单号\n      this.form.subOrderNo = \"\"\n      this.form.subOrderNo = row.clientCode + \"-\"\n    },\n    getSummariesInventory(param) {\n      const {columns, data} = param\n      const sums = []\n      const statisticalField = [\"totalBoxes\", \"totalGrossWeight\", \"totalVolume\",\n        \"inboundFee\", \"receivedStorageFee\", \"unpaidUnloadingFee\", \"receivedUnloadingFee\",\n        \"unpaidPackingFee\", \"receivedPackingFee\", \"logisticsAdvanceFee\"]\n\n      columns.forEach((column, index) => {\n        if (index === 0) {\n          sums[index] = \"总计:\"\n          return\n        }\n\n        // 筛选出被统计的字段\n        if (statisticalField.includes(column.property)) {\n          // 将数据转为有效数字\n          const values = data\n            .map(item => Number(item[column.property]))\n            .filter(value => !isNaN(value)) // 排除非数字\n\n          if (values.length > 0) {\n            // 计算总和并格式化\n            const total = values.reduce((prev, curr) => prev + curr, 0)\n            sums[index] =\n              this.formatNumberFixed(total) +\n              (column.property === \"totalVolume\"\n                ? \" CBM\"\n                : column.property === \"totalGrossWeight\"\n                  ? \" KGS\"\n                  : column.property === \"totalBoxes\"\n                    ? \" 件\"\n                    : \"\")\n          } else {\n            sums[index] = \" \"\n          }\n        } else {\n          sums[index] = \" \"\n        }\n      })\n\n      return sums\n    },\n    getSummaries(param) {\n      const {columns, data} = param\n      const sums = []\n      const statisticalField = [\"unitVolume\", \"unitGrossWeight\", \"boxCount\"]\n\n      columns.forEach((column, index) => {\n        if (index === 0) {\n          sums[index] = \"总计:\"\n          return\n        }\n\n        // 筛选出被统计的字段\n        if (statisticalField.includes(column.property)) {\n          // 将数据转为有效数字\n          const values = data\n            .map(item => Number(item[column.property]))\n            .filter(value => !isNaN(value)) // 排除非数字\n\n          if (values.length > 0) {\n            // 计算总和并格式化\n            const total = values.reduce((prev, curr) => prev + curr, 0)\n            sums[index] =\n              this.formatNumberFixed(total) +\n              (column.property === \"unitVolume\"\n                ? \" CBM\"\n                : column.property === \"unitGrossWeight\"\n                  ? \" KGS\"\n                  : column.property === \"boxCount\"\n                    ? \" 件\"\n                    : \"\")\n          } else {\n            sums[index] = \" \"\n          }\n        } else {\n          sums[index] = \" \"\n        }\n      })\n\n      return sums\n    },\n    formatNumberFixed(value) {\n      return Number(value).toFixed(2) // 保留两位小数\n    },\n    handleOpOutbound() {\n      this.openOutbound = true\n    },\n    initPrint() {\n      hiprint.init({\n        providers: [new defaultElementTypeProvider()]\n      })\n    },\n    printInboundBill(type) {\n      let data = {}\n      data.clientCode = this.form.clientCode\n      data.subOrderNo = this.form.subOrderNo\n      data.inboundSerialNo = this.form.inboundSerialNo\n      data.forwarderNo = this.form.forwarderNo\n      data.actualInboundTime = moment(this.form.actualInboundTime).format(\"yyyy-MM-DD HH:mm:ss\")\n      data.sqdShippingMark = this.form.sqdShippingMark\n      data.cargoName = this.form.cargoName\n      data.totalBoxes = this.form.totalBoxes\n      data.totalGrossWeight = this.form.totalGrossWeight\n      data.totalVolume = this.form.totalVolume\n      data.notes = this.form.notes\n      data.supplier = this.form.supplier\n      data.driverInfo = this.form.driverInfo\n      data.inboundAddr = this.form.inboundAddr\n      data.consigneeName = this.form.consigneeName\n      data.inboundSerialNo = this.form.inboundSerialNo\n\n      data.expressNo = this.form.driverInfo\n\n      if (type === \"旧模板\") {\n        hiprintTemplate = new hiprint.PrintTemplate({template: warehouseReceipt})\n      } else {\n        hiprintTemplate = new hiprint.PrintTemplate({template: warehouseReceiptNew})\n      }\n      // 打开预览组件\n      // this.$refs.preView.show(hiprintTemplate, data)\n      // 直接打印\n      this.$refs.preView.print(hiprintTemplate, data)\n    },\n    deleteCargoDetail(row) {\n      this.form.rsCargoDetailsList = this.form.rsCargoDetailsList.filter(item => item !== row)\n      this.$forceUpdate()\n    },\n    handleUpdateCargoDetail(row) {\n      this.cargoDetailOpen = true\n      this.cargoDetailRow = row\n    },\n    handlePkgSelectionChange(selection) {\n      this.selectedPkgList = selection\n    },\n    pkgFinish() {\n      if (this.form.repackingStatus === \"打包完\") {\n        this.form.repackingStatus = \"打包中\"\n      } else {\n        this.form.repackingStatus = \"打包完\"\n      }\n\n      updateInventory(this.form).then(res => {\n        this.$message.success(\"修改成功\")\n      })\n    },\n    addCargoDetailRow(row) {\n\n    },\n    addCargoDetail() {\n      this.form.rsCargoDetailsList.push(this._.cloneDeep(this.cargoDetailRow))\n      this.cargoDetailOpen = true\n    },\n    listAggregatorRsInventory(params) {\n      params.config = JSON.stringify(params.config)\n      this.queryParams.params = params\n      return listAggregatorRsInventory(this.queryParams)\n    },\n    /** 查询库存列表 */\n    getList() {\n      this.loading = true\n      this.queryParams.permissionLevel = this.$store.state.user.permissionLevelList.C\n      // 添加条件，只查询顶层数据（没有父级的数据）\n      this.queryParams.isTopLevel = true\n      listInventory(this.queryParams).then(response => {\n        // 处理数据，标记有子节点的数据\n        let rows = response.rows\n\n        // 如果后端不支持isTopLevel参数，在前端进行过滤\n        // 仅当快递单号未填写时才过滤packageTo，保证快递查询时能显示所有匹配的记录\n        if (this.queryParams.isTopLevel && !this.queryParams.driverInfo) {\n          rows = rows.filter(item => !item.packageTo)\n        }\n\n        this.inventoryList = rows.map(item => {\n          // 如果是打包箱，标记为有子节点\n          if (item.packageRecord === \"1\") {\n            item.hasChildren = true\n          }\n          return item\n        })\n        this.total = response.total\n        this.loading = false\n      })\n    },\n    // 取消按钮\n    cancel() {\n      this.open = false\n      this.reset()\n    },\n    // 表单重置\n    reset() {\n      this.pkgDetailsList = []\n      this.form = {\n        repackingStatus: \"-\",\n        inventoryId: null,\n        inventoryStatus: \"0\",\n        inboundSerialNo: null,\n        inboundSerialNoPre: null,\n        inboundSerialSplit: null,\n        inboundDate: null,\n        outboundNo: null,\n        forwarderNo: null,\n        rentalSettlementDate: null,\n        outboundDate: null,\n        clientCode: null,\n        subOrderNo: null,\n        supplier: null,\n        driverInfo: null,\n        sqdShippingMark: null,\n        cargoName: null,\n        totalBoxes: null,\n        packageType: null,\n        totalGrossWeight: null,\n        totalVolume: null,\n        damageStatus: \"0\",\n        storageLocation1: null,\n        storageLocation2: null,\n        storageLocation3: null,\n        receivedStorageFee: null,\n        unpaidUnloadingFee: null,\n        logisticsAdvanceFee: null,\n        rentalBalanceFee: null,\n        freeStackPeriod: null,\n        overdueRentalUnitPrice: null,\n        overdueRentalFee: null,\n        notes: null,\n        warehouseCode: null,\n        recordType: null,\n        inboundType: null,\n        cargoNature: null,\n        createdAt: null,\n        preOutboundFlag: null,\n        outboundRequestFlag: null,\n        sqdPlannedOutboundDate: null,\n        confirmInboundRequestFlag: null,\n        confirmOutboundRequestFlag: null,\n        sqdInboundHandler: null,\n        partialOutboundFlag: null,\n        outboundRecordId: null,\n        actualInboundTime: moment().format(\"yyyy-MM-DD HH:mm:ss\"),\n        actualOutboundTime: null,\n        cargoDetailRows: null,\n        includesUnloadingFee: null,\n        unpaidPackingFee: null,\n        inboundFee: null,\n        singlePieceWeightFormatter: null,\n        unitLengthFormatter: null,\n        unitWidthFormatter: null,\n        unitHeightFormatter: null,\n        singlePieceVolumeFormatter: null,\n        unitGrossWeightFormatter: null,\n        unitVolumeFormatter: null,\n        rsCargoDetailsList: [\n          {\n            shippingMark: \"\",\n            itemName: \"\",\n            boxCount: 0,\n            packageType: \"纸箱\",\n            singlePieceWeightFormatter: \"\",\n            unitLengthFormatter: \"\",\n            unitWidthFormatter: \"\",\n            unitHeightFormatter: \"\",\n            singlePieceVolumeFormatter: \"\",\n            unitGrossWeightFormatter: \"\",\n            unitVolumeFormatter: \"\",\n            damageStatus: \"\",\n            boxItemCount: 0,\n            subtotalItemCount: 0,\n            expressDate: moment().format(\"yyyy-MM-DD\"),\n            additionalFee: 0\n          }\n        ]\n      }\n      this.resetForm(\"form\")\n    },\n    /** 搜索按钮操作 */\n    handleQuery() {\n      this.queryParams.pageNum = 1\n      this.getList()\n    },\n    /** 重置按钮操作 */\n    resetQuery() {\n      this.resetForm(\"queryForm\")\n      // 保留isTopLevel参数，确保重置后仍然只查询顶层数据\n      this.queryParams.isTopLevel = true\n      this.handleQuery()\n    },\n    handleStatusChange(row) {\n      let text = row.status === \"0\" ? \"启用\" : \"停用\"\n      this.$modal.confirm(\"确认要\\\"\" + text + \"吗？\").then(function () {\n        return changeStatus(row.inventoryId, row.status)\n      }).then(() => {\n        this.$modal.msgSuccess(text + \"成功\")\n      }).catch(function () {\n        row.status = row.status === \"0\" ? \"1\" : \"0\"\n      })\n    },\n    // 多选框选中数据\n    handleSelectionChange(selection) {\n      // 正确获取表格数据 - 通过data属性\n      const treeData = this.$refs.inventoryTable.store.states.data\n\n      // 获取之前的选择状态，用于比较变化\n      const previousIds = [...this.ids]\n\n      // 清空当前选择\n      this.ids = []\n      this.selectedInventoryList = []\n\n      // 重新填充选择数据\n      this.ids = selection.map(item => item.inventoryId)\n      this.single = selection.length !== 1\n      this.multiple = !selection.length\n      this.selectedInventoryList = selection\n\n      // 找出新选中和取消选中的项\n      const newlySelected = this.ids.filter(id => !previousIds.includes(id))\n      const newlyDeselected = previousIds.filter(id => !this.ids.includes(id))\n\n      // 处理新选中的打包箱：自动选中其子项\n      selection.forEach(item => {\n        if (item.packageRecord === \"1\" && newlySelected.includes(item.inventoryId)) {\n          // 如果是新选中的打包箱节点\n\n          // 在树形表格数据中找到对应的节点\n          const parentNode = treeData.find(node => node.inventoryId === item.inventoryId)\n\n          // 检查节点是否已展开(已有children属性且有内容)\n          if (parentNode && parentNode.children && parentNode.children.length > 0) {\n            // 如果节点已展开，直接选中其所有子项\n            setTimeout(() => {\n              parentNode.children.forEach(child => {\n                if (!this.ids.includes(child.inventoryId)) {\n                  this.ids.push(child.inventoryId)\n                  this.selectedInventoryList.push(child)\n                  this.$refs.inventoryTable.toggleRowSelection(child, true)\n                }\n              })\n            }, 50) // 给一点时间让UI更新\n          } else if (parentNode && !parentNode.childrenLoaded && parentNode.hasChildren) {\n            // 如果节点未展开且未加载过但有子节点标记\n            parentNode.childrenLoaded = true\n\n            // 手动展开行，触发懒加载\n            this.$refs.inventoryTable.toggleRowExpansion(parentNode, true)\n\n            // 监听子节点加载完成后再选中它们\n            // 这里利用了loadChildInventory方法中的逻辑，它会在子节点加载后处理选中状态\n          }\n        }\n      })\n\n      // 处理取消选中的打包箱：取消选中其子项\n      newlyDeselected.forEach(parentId => {\n        // 找出对应的父节点\n        const parentNode = treeData.find(node =>\n          node.inventoryId === parentId && node.packageRecord === \"1\"\n        )\n\n        if (parentNode && parentNode.children && parentNode.children.length > 0) {\n          // 取消选中所有子项\n          parentNode.children.forEach(child => {\n            const childIndex = this.ids.indexOf(child.inventoryId)\n            if (childIndex > -1) {\n              // 从选中列表中移除\n              this.ids.splice(childIndex, 1)\n              const itemIndex = this.selectedInventoryList.findIndex(\n                item => item.inventoryId === child.inventoryId\n              )\n              if (itemIndex > -1) {\n                this.selectedInventoryList.splice(itemIndex, 1)\n              }\n              // 在UI上取消选中\n              this.$refs.inventoryTable.toggleRowSelection(child, false)\n            }\n          })\n        }\n      })\n    },\n    handleOpenAggregator() {\n      // listAggregatorRct(this.queryParams).then(response => {\n      //   this.aggregatorRctList = response\n      // })\n\n      this.openAggregator = true\n    },\n    /** 新增按钮操作 */\n    handleAdd() {\n      this.reset()\n      this.open = true\n      this.title = \"入仓\"\n      this.form.sqdInboundHandler = this.$store.state.user.name.split(\" \")[1]\n      this.form.actualInboundTime = new Date()\n      this.form.inventoryStatus = \"0\"\n      // this.form.rsCargoDetailsList ? null : this.form.rsCargoDetailsList = []\n      // this.form.rsCargoDetailsList.push(this._.cloneDeep(this.cargoDetailRow))\n      this.$nextTick(() => {\n        this.form.inboundSerialNoPre = \"RS.91\"\n        this.form.cargoNature = \"普货\"\n        this.form.recordType = \"标准\"\n        this.form.inboundType = \"入仓\"\n      })\n\n    },\n    /** 修改按钮操作 */\n    handleUpdate(row) {\n      this.reset()\n      const inventoryId = row.inventoryId || this.ids\n      getInventory(inventoryId).then(response => {\n        this.form = response.data\n        this.form.rsCargoDetailsList = response.data.rsCargoDetailsList.map(item => ({\n          ...item,\n          shippingMark: item.shippingMark || \"\",\n          itemName: item.itemName || \"\",\n          boxCount: item.boxCount || 0,\n          packageType: item.packageType || \"\",\n          singlePieceWeight: item.singlePieceWeight || 0,\n          singlePieceWeightFormatter: this.formatNumber(item.singlePieceWeight || 0),\n          unitLength: item.unitLength || 0,\n          unitLengthFormatter: this.formatNumber(item.unitLength || 0),\n          unitWidth: item.unitWidth || 0,\n          unitWidthFormatter: this.formatNumber(item.unitWidth || 0),\n          unitHeight: item.unitHeight || 0,\n          unitHeightFormatter: this.formatNumber(item.unitHeight || 0),\n          singlePieceVolume: item.singlePieceVolume || 0,\n          singlePieceVolumeFormatter: this.formatNumber(item.singlePieceVolume || 0),\n          unitGrossWeight: item.unitGrossWeight || 0,\n          unitGrossWeightFormatter: this.formatNumber(item.unitGrossWeight || 0),\n          unitVolume: item.unitVolume || 0,\n          unitVolumeFormatter: this.formatNumber(item.unitVolume || 0),\n          damageStatus: item.damageStatus || \"\"\n        }))\n        if (response.data.packageRecord === \"0\") {\n          this.open = true\n        } else {\n          this.openPkg = true\n        }\n\n        this.title = \"修改库存\"\n      })\n    },\n    /** 提交按钮 */\n    submitForm(type) {\n      this.$refs[\"form\"].validate(valid => {\n        if (valid) {\n          // 检查分单号格式\n          if (this.form.subOrderNo && !this.form.subOrderNo.startsWith(`${this.form.clientCode}-`)) {\n            this.$modal.msgError(`分单号必须以 ${this.form.clientCode}- 开头`)\n            return\n          }\n\n          this.form.inboundDate = this.form.inboundDate ? moment(this.form.inboundDate).format(\"yyyy-MM-DD HH:mm:ss\") : null\n          this.form.actualInboundTime = this.form.actualInboundTime ? moment(this.form.actualInboundTime).format(\"yyyy-MM-DD HH:mm:ss\") : null\n\n          // this.form.inboundSerialNo = this.form.inboundSerialNoPre + this.form.inboundSerialNoSub.padStart(3, \"0\")\n          this.form.actualInboundTime = moment(this.form.actualInboundTime).format(\"yyyy-MM-DD HH:mm:ss\")\n          if (type !== \"pkg\") {\n            this.form.totalBoxes = 0\n            this.form.totalGrossWeight = 0\n            this.form.totalVolume = 0\n            this.form.rsCargoDetailsList.forEach((item, index) => {\n              // 汇总计算\n              this.form.totalBoxes = currency(item.boxCount || 0).add(this.form.totalBoxes).value\n              this.form.totalGrossWeight = currency(item.unitGrossWeight || 0).add(this.form.totalGrossWeight).value\n              this.form.totalVolume = currency(item.unitVolume || 0).add(this.form.totalVolume).value\n            })\n          }\n\n          // 统计唛头\n          let mark = []\n          this.form.rsCargoDetailsList.map(item => {\n            mark.push(item.shippingMark)\n          })\n          this.form.sqdShippingMark = mark.join(\",\")\n\n          if (this.form.inventoryId != null) {\n            this.form.rentalSettlementDate ? null : this.form.rentalSettlementDate = this.form.actualInboundTime\n            updateInventory(this.form).then(response => {\n              this.$modal.msgSuccess(\"修改成功\")\n              this.getList()\n            })\n          } else {\n            if (type === \"pkg\") {\n              this.form.packageRecord = \"1\"\n              this.form.repackingStatus = \"打包中\"\n\n            }\n            this.form.rentalSettlementDate = this.form.actualInboundTime\n            addInventory(this.form).then(response => {\n              this.form = response.data\n              this.$modal.msgSuccess(\"新增成功\")\n              this.getList()\n            })\n          }\n        }\n      })\n    },\n    /** 删除按钮操作 */\n    handleDelete(row) {\n      const inventoryIds = row.inventoryId || this.ids\n      this.$modal.confirm(\"是否确认删除库存编号为\\\"\" + inventoryIds + \"\\\"的数据项？\").then(function () {\n        return delInventory(inventoryIds)\n      }).then(() => {\n        this.getList()\n        this.$modal.msgSuccess(\"删除成功\")\n      }).catch(() => {\n      })\n    },\n    /** 导入按钮操作 */\n    handleImport() {\n      this.upload.title = \"用户导入\"\n      this.upload.open = true\n    },\n    /** 导出按钮操作 */\n    handleExport() {\n\n      this.download(\"system/inventory/export\", {\n        ...this.queryParams, pageSize: 999\n      }, `inventory_${new Date().getTime()}.xlsx`)\n    }\n  }\n}\n</script>\n<style lang=\"scss\" scoped>\n/* 针对不同浏览器的 spinner 样式隐藏 */\n.no-spinner ::-webkit-inner-spin-button,\n.no-spinner ::-webkit-outer-spin-button {\n  -webkit-appearance: none;\n  margin: 0;\n}\n\n.no-spinner {\n  -moz-appearance: textfield; /* 针对 Firefox */\n}\n\n::v-deep .edit .number .el-input__inner {\n  text-align: right;\n}\n\n</style>\n"]}]}