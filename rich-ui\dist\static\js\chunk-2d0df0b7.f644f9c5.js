(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d0df0b7"],{"87df":function(e,t,r){"use strict";r.r(t);var n=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",[r("h6",{staticStyle:{margin:"0"}},[e._v(e._s(e.scope.row.demurrage))]),r("h6",{staticStyle:{margin:"0"}},[e._v(e._s(e.scope.row.demurrageUnit))])])},s=[],a={name:"demurrage",props:["scope"],data:function(){return{size:this.$store.state.app.size||"mini"}}},c=a,i=r("2877"),o=Object(i["a"])(c,n,s,!1,null,"cc5b9c1a",null);t["default"]=o.exports}}]);