(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-03b230e2","chunk-2d0d69a4"],{"2a7b":function(e,t,r){"use strict";r.r(t);var n=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",[r("el-table",{staticStyle:{width:"100%"},attrs:{data:e.tableData,border:"",stripe:""}},[r("el-table-column",{attrs:{label:"序号",type:"index"}}),r("el-table-column",{attrs:{align:"left"},scopedSlots:e._u([{key:"header",fn:function(t){return[r("el-input",{attrs:{placeholder:"操作单号",size:"mini"},nativeOn:{keydown:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.getList(t)}},model:{value:e.queryParams.rctNo,callback:function(t){e.$set(e.queryParams,"rctNo",t)},expression:"queryParams.rctNo"}})]}},{key:"default",fn:function(t){return[e._v(" "+e._s(t.row.rctNo)+" ")]}}])}),r("el-table-column",{scopedSlots:e._u([{key:"header",fn:function(t){return[r("location-select",{attrs:{"load-options":e.searchData.locationOptions,multiple:!0,"no-parent":!0,pass:e.queryParams.polIds,placeholder:"启运港"},on:{return:function(t){return e.handleQueryPol(t)}}})]}},{key:"default",fn:function(t){return[e._v(" "+e._s(t.row.pol)+" ")]}}])}),r("el-table-column",{scopedSlots:e._u([{key:"header",fn:function(t){return[r("location-select",{attrs:{en:!0,"load-options":e.searchData.locationOptions,multiple:!0,pass:e.queryParams.destinationPortIds,placeholder:"目的港"},on:{return:function(t){return e.handleQueryDestinationPortId(t)}}})]}},{key:"default",fn:function(t){return[e._v(" "+e._s(t.row.destinationPort)+" ")]}}])}),r("el-table-column",{scopedSlots:e._u([{key:"header",fn:function(t){return[r("el-input",{attrs:{placeholder:"柜型",size:"mini"},nativeOn:{keydown:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.getList(t)}},model:{value:e.queryParams.revenueTon,callback:function(t){e.$set(e.queryParams,"revenueTon",t)},expression:"queryParams.revenueTon"}})]}},{key:"default",fn:function(t){return[e._v(" "+e._s(t.row.revenueTon)+" ")]}}])}),r("el-table-column",{scopedSlots:e._u([{key:"header",fn:function(t){return[r("treeselect",{staticStyle:{overflow:"visible"},attrs:{"disable-branch-nodes":!0,"disabled-fuzzy-matching":!0,flat:!1,"flatten-search-results":!0,multiple:!1,normalizer:e.carrierNormalizer,options:e.carrierList,"show-count":!0,placeholder:"选择承运人"},on:{select:function(t){return e.handleQueryCarrier(t)}},scopedSlots:e._u([{key:"value-label",fn:function(t){var n=t.node;return r("div",{},[e._v(" "+e._s(null!=n.raw.carrierIntlCode?n.raw.carrierIntlCode:" ")+" ")])}},{key:"option-label",fn:function(t){var n=t.node,a=t.shouldShowCount,s=t.count,o=t.labelClassName,i=t.countClassName;return r("label",{class:o},[e._v(" "+e._s(-1!=n.label.indexOf(",")?n.label.substring(0,n.label.indexOf(",")):n.label)+" "),a?r("span",{class:i},[e._v("("+e._s(s)+")")]):e._e()])}}],null,!0),model:{value:e.queryParams.carrierId,callback:function(t){e.$set(e.queryParams,"carrierId",t)},expression:"queryParams.carrierId"}})]}}])}),r("el-table-column",{scopedSlots:e._u([{key:"header",fn:function(t){return[r("company-select",{attrs:{multiple:!1,"no-parent":!0,pass:e.queryParams.clientId,placeholder:"订舱口","role-control":!1},on:{return:function(t){return e.querySupplier(t)}}})]}},{key:"default",fn:function(t){return[e._v(" "+e._s(t.row.supplierName)+" ")]}}])}),r("el-table-column",{scopedSlots:e._u([{key:"header",fn:function(t){return[r("el-input",{attrs:{placeholder:"录入人",size:"mini"},model:{value:e.queryParams.opId,callback:function(t){e.$set(e.queryParams,"opId",t)},expression:"queryParams.opId"}})]}},{key:"default",fn:function(t){return[e._v(" "+e._s(e.getName(t.row.opId))+" ")]}}])}),r("el-table-column",{scopedSlots:e._u([{key:"header",fn:function(t){return[r("el-input",{attrs:{placeholder:"审核日期",size:"mini"},model:{value:e.queryParams.verifyTime,callback:function(t){e.$set(e.queryParams,"verifyTime",t)},expression:"queryParams.verifyTime"}})]}},{key:"default",fn:function(t){return[e._v(" "+e._s(t.row.psaVerifyTime)+" ")]}}])}),r("el-table-column",{attrs:{label:"操作"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("el-popover",{attrs:{placement:"left",trigger:"click",width:"400"}},[r("el-table",{ref:"chargesTable",attrs:{data:e.charges},on:{"selection-change":e.handleSelectionChange}},[r("el-table-column",{attrs:{type:"selection",width:"55"}}),r("el-table-column",{attrs:{align:"center",label:"费用",prop:"costChargeId",width:"80px"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("div",[e._v(" "+e._s(t.row.chargeName)+" ")])]}}],null,!0)}),r("el-table-column",{attrs:{align:"center",label:"货币",prop:"costCurrencyId",width:"70px"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("div",[e._v(" "+e._s(t.row.dnCurrencyCode)+" ")])]}}],null,!0)}),r("el-table-column",{attrs:{align:"center",label:"单价",prop:"inquiryRate",width:"68px"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("div",[e._v(" "+e._s("RMB"===t.row.dnCurrencyCode?e.currency(t.row.dnUnitRate,{separator:",",symbol:"¥"}).format():"USD"===t.row.dnCurrencyCode?e.currency(t.row.dnUnitRate,{separator:",",symbol:"$"}).format():t.row.dnUnitRate)+" ")])]}}],null,!0)}),r("el-table-column",{attrs:{align:"center",label:"单位",prop:"costUnitId",width:"50px"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("div",[e._v(" "+e._s(t.row.dnUnitCode)+" ")])]}}],null,!0)}),r("el-table-column",{attrs:{align:"center",label:"数量",prop:"costAmount",width:"48px"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("div",[e._v(" "+e._s(t.row.dnAmount)+" ")])]}}],null,!0)}),r("el-table-column",{attrs:{align:"center",label:"汇率",prop:"costExchangeRate",width:"60px"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("div",[e._v(" "+e._s(e.currency(t.row.basicCurrencyRate).value)+" ")])]}}],null,!0)}),r("el-table-column",{attrs:{align:"center",label:"税率",prop:"costTaxRate",width:"68px"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("div",{staticStyle:{display:"flex","justify-content":"center"}},[r("div",[e._v(" "+e._s(t.row.dutyRate)+" ")]),r("div",[e._v("%")])])]}}],null,!0)}),r("el-table-column",{attrs:{align:"center",label:"小计",prop:"costTotal",width:"65"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("div",[e._v(" "+e._s(e.currency(t.row.subtotal,{separator:",",symbol:"¥"}).format())+" ")])]}}],null,!0)}),r("el-table-column",{attrs:{align:"center",label:"费用备注"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("input",{directives:[{name:"model",rawName:"v-model",value:t.row.chargeRemark,expression:"scope.row.chargeRemark"}],staticStyle:{border:"none"},attrs:{disabled:"1"==t.row.isAccountConfirmed},domProps:{value:t.row.chargeRemark},on:{input:function(r){r.target.composing||e.$set(t.row,"chargeRemark",r.target.value)}}})]}}],null,!0)}),r("el-table-column",{attrs:{align:"center",label:"审核状态"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(1==t.row.isAccountConfirmed?"已审核":"未审核")+" ")]}}],null,!0)}),r("el-table-column",{attrs:{align:"center",label:"已付金额"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(e.currency(e.currency(t.row.dnUnitRate).multiply(t.row.dnAmount)).subtract(t.row.sqdDnCurrencyBalance))+" ")]}}],null,!0)}),r("el-table-column",{attrs:{align:"center",label:"未付余额"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(t.row.sqdDnCurrencyBalance)+" ")]}}],null,!0)})],1),r("el-button",{attrs:{slot:"reference",type:"text"},nativeOn:{click:function(r){return e.getCharges(t.row)}},slot:"reference"},[e._v("[浏览费用明细]")])],1),r("el-button",{staticStyle:{float:"right"},attrs:{type:"text"},nativeOn:{click:function(t){return e.copyCharge(t)}}},[e._v("[复 制]")])]}}])})],1)],1)},a=[],s=r("c7eb"),o=r("1da1"),i=(r("4ec9"),r("d3b7"),r("3ca3"),r("ddb0"),r("4de4"),r("159b"),r("c2aa")),l=r("6e71"),u=r("72f9"),c=r.n(u),d=(r("0a37"),r("ca17")),f=r.n(d),p=r("b0b8"),m=r.n(p),h=r("4360"),y={name:"chargeSelect",components:{Treeselect:f.a,CompanySelect:l["a"]},props:["serviceTypeId","searchData","clientId"],mounted:function(){this.loadCarrier()},data:function(){return{carrierList:[],charges:[],selectedCharges:[],queryParams:{pageNum:1,pageSize:20,rctNo:null,supplierId:this.searchData.supplierId,polIds:[this.searchData.polId?this.searchData.polId:null],destinationPortIds:[this.searchData.destinationPortId?this.searchData.destinationPortId:null],carrierId:this.searchData.carrierId,params:new Map},tableData:[]}},methods:{getName:function(e){if(e){var t=this.$store.state.data.allRsStaffList.filter((function(t){return t.staffId==e}))[0];if(t)return t.staffFamilyLocalName+t.staffGivingLocalName+t.staffShortName}},querySupplier:function(e){this.getList()},loadCarrier:function(){var e=this;0==this.$store.state.data.carrierList.length||this.$store.state.data.redisList.carrier?h["a"].dispatch("getCarrierList").then((function(){e.carrierList=e.$store.state.data.carrierList})):this.carrierList=this.$store.state.data.carrierList},carrierNormalizer:function(e){return{id:e.carrierId,label:(null!=e.carrierShortName?e.carrierShortName:"")+" "+(null!=e.carrierLocalName?e.carrierLocalName:"")+" "+(null!=e.carrierEnName?e.carrierEnName:"")+","+m.a.getFullChars((null!=e.carrierShortName?e.carrierShortName:"")+" "+(null!=e.carrierLocalName?e.carrierLocalName:""))}},currency:c.a,getServiceObject:function(e,t){return 1===e?t.rsOpSeaFclList?t.rsOpSeaFclList[0]:null:2===e?t.rsOpSeaLclList?t.rsOpSeaLclList[0]:null:10===e?t.rsOpAirList?t.rsOpAirList[0]:null:20===e?t.rsOpRailFCL:21===e?t.rsOpRailLCL:40===e?t.rsOpExpress:50===e?t.rsOpCtnrTruckList?t.rsOpCtnrTruckList[0]:null:51===e?t.rsOpBulkTruckList?t.rsOpBulkTruckList[0]:null:60===e?t.rsOpDocDeclareList?t.rsOpDocDeclareList[0]:null:61===e?t.rsOpFreeDeclareList?t.rsOpFreeDeclareList[0]:null:70===e?t.rsOpDOAgent:71===e?t.rsOpClearAgent:80===e?t.rsOpWHS:90===e?t.rsOp3rdCert:100===e?t.rsOpINS:101===e?t.rsOpTrading:102===e?t.rsOpFumigation:103===e?t.rsOpCO:104===e?t.rsOpOther:void 0},getCharges:function(e){var t=this;return Object(o["a"])(Object(s["a"])().mark((function r(){var n,a;return Object(s["a"])().wrap((function(r){while(1)switch(r.prev=r.next){case 0:return n=!1,r.next=3,Object(i["i"])(e.rctId).then((function(e){t.serviceTypeId?a=t.getServiceObject(t.serviceTypeId,e.data):(n=!0,a=e.data.rsClientMessage)}));case 3:console.log(a),a&&(t.charges=a.rsChargeList,t.$nextTick((function(){t.charges.forEach((function(e){n&&(e.clearingCompanyId=t.clientId),t.$refs.chargesTable.toggleRowSelection(e)}))})));case 5:case"end":return r.stop()}}),r)})))()},handleSelectionChange:function(e){this.selectedCharges=e},search:function(){},copyCharge:function(){this.$emit("returnCharge",this.selectedCharges)},handleQueryPol:function(e){this.queryParams.polIds=e,this.getList()},handleQueryDestinationPortId:function(e){this.queryParams.destinationPortIds=e,this.getList()},handleQueryCarrier:function(e){this.queryParams.carrierId=e.carrierId,this.getList()},getList:function(){var e=this;return Object(o["a"])(Object(s["a"])().mark((function t(){return Object(s["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.loading=!0,e.queryParams.params.op=1,t.next=4,Object(i["o"])(e.queryParams).then((function(t){e.tableData=t.rows,e.total=t.total,e.loading=!1}));case 4:case"end":return t.stop()}}),t)})))()}}},b=y,v=r("2877"),g=Object(v["a"])(b,n,a,!1,null,"310cdcdc",null);t["default"]=g.exports},"72f9":function(e,t,r){(function(t,r){e.exports=r()})(0,(function(){function e(s,o){if(!(this instanceof e))return new e(s,o);o=Object.assign({},r,o);var i=Math.pow(10,o.precision);this.intValue=s=t(s,o),this.value=s/i,o.increment=o.increment||1/i,o.groups=o.useVedic?a:n,this.s=o,this.p=i}function t(t,r){var n=!(2<arguments.length&&void 0!==arguments[2])||arguments[2],a=r.decimal,s=r.errorOnInvalid,o=r.fromCents,i=Math.pow(10,r.precision),l=t instanceof e;if(l&&o)return t.intValue;if("number"===typeof t||l)a=l?t.value:t;else if("string"===typeof t)s=new RegExp("[^-\\d"+a+"]","g"),a=new RegExp("\\"+a,"g"),a=(a=t.replace(/\((.*)\)/,"-$1").replace(s,"").replace(a,"."))||0;else{if(s)throw Error("Invalid Input");a=0}return o||(a=(a*i).toFixed(4)),n?Math.round(a):a}var r={symbol:"$",separator:",",decimal:".",errorOnInvalid:!1,precision:2,pattern:"!#",negativePattern:"-!#",format:function(e,t){var r=t.pattern,n=t.negativePattern,a=t.symbol,s=t.separator,o=t.decimal;t=t.groups;var i=(""+e).replace(/^-/,"").split("."),l=i[0];return i=i[1],(0<=e.value?r:n).replace("!",a).replace("#",l.replace(t,"$1"+s)+(i?o+i:""))},fromCents:!1},n=/(\d)(?=(\d{3})+\b)/g,a=/(\d)(?=(\d\d)+\d\b)/g;return e.prototype={add:function(r){var n=this.s,a=this.p;return e((this.intValue+t(r,n))/(n.fromCents?1:a),n)},subtract:function(r){var n=this.s,a=this.p;return e((this.intValue-t(r,n))/(n.fromCents?1:a),n)},multiply:function(t){var r=this.s;return e(this.intValue*t/(r.fromCents?1:Math.pow(10,r.precision)),r)},divide:function(r){var n=this.s;return e(this.intValue/t(r,n,!1),n)},distribute:function(t){var r=this.intValue,n=this.p,a=this.s,s=[],o=Math[0<=r?"floor":"ceil"](r/t),i=Math.abs(r-o*t);for(n=a.fromCents?1:n;0!==t;t--){var l=e(o/n,a);0<i--&&(l=l[0<=r?"add":"subtract"](1/n)),s.push(l)}return s},dollars:function(){return~~this.value},cents:function(){return~~(this.intValue%this.p)},format:function(e){var t=this.s;return"function"===typeof e?e(this,t):t.format(this,Object.assign({},t,e))},toString:function(){var e=this.s,t=e.increment;return(Math.round(this.intValue/this.p/t)*t).toFixed(e.precision)},toJSON:function(){return this.value}},e}))},c2aa:function(e,t,r){"use strict";r.d(t,"o",(function(){return a})),r.d(t,"n",(function(){return s})),r.d(t,"p",(function(){return o})),r.d(t,"r",(function(){return i})),r.d(t,"q",(function(){return l})),r.d(t,"i",(function(){return u})),r.d(t,"g",(function(){return c})),r.d(t,"v",(function(){return d})),r.d(t,"w",(function(){return f})),r.d(t,"h",(function(){return p})),r.d(t,"c",(function(){return m})),r.d(t,"a",(function(){return h})),r.d(t,"f",(function(){return y})),r.d(t,"d",(function(){return b})),r.d(t,"e",(function(){return v})),r.d(t,"k",(function(){return g})),r.d(t,"j",(function(){return O})),r.d(t,"m",(function(){return w})),r.d(t,"t",(function(){return _})),r.d(t,"u",(function(){return k})),r.d(t,"l",(function(){return C})),r.d(t,"s",(function(){return S}));var n=r("b775");function a(e){return Object(n["a"])({url:"/system/rct/list",method:"get",params:e})}function s(e){return Object(n["a"])({url:"/system/rct/aggregator",method:"get",params:e})}function o(e){return Object(n["a"])({url:"/system/rct/listVerifyAggregatorList",method:"get",params:e})}function i(e){return Object(n["a"])({url:"/system/rct/op",method:"get",params:e})}function l(e){return Object(n["a"])({url:"/system/rct/listVerifyList",method:"get",params:e})}function u(e){return Object(n["a"])({url:"/system/rct/"+e,method:"get"})}function c(e){return Object(n["a"])({url:"/system/rct",method:"post",data:e})}function d(e){return Object(n["a"])({url:"/system/rct/saveAs",method:"post",data:e})}function f(e){return Object(n["a"])({url:"/system/rct",method:"put",data:e})}function p(e){return Object(n["a"])({url:"/system/rct/"+e,method:"delete"})}function m(e){return Object(n["a"])({url:"/system/rct/saveClientMessage",method:"post",data:e})}function h(e){return Object(n["a"])({url:"/system/rct/saveBasicLogistics",method:"post",data:e})}function y(e){return Object(n["a"])({url:"/system/rct/savePreCarriage",method:"post",data:e})}function b(e){return Object(n["a"])({url:"/system/rct/saveExportDeclaration",method:"post",data:e})}function v(e){return Object(n["a"])({url:"/system/rct/saveImportClearance",method:"post",data:e})}function g(){return Object(n["a"])({url:"/system/rct/mon",method:"get"})}function O(){return Object(n["a"])({url:"/system/rct/CFmon",method:"get"})}function w(){return Object(n["a"])({url:"/system/rct/RSWHMon",method:"get"})}function _(e){return Object(n["a"])({url:"/system/rct/saveAllService",method:"post",data:e})}function k(e){return Object(n["a"])({url:"/system/rct/saveAsAllService",method:"post",data:e})}function C(e){return Object(n["a"])({url:"/system/rct/listRctNoByCompany/"+e,method:"get"})}function S(e){return Object(n["a"])({url:"/system/rct/writeoff",method:"post",data:e})}}}]);