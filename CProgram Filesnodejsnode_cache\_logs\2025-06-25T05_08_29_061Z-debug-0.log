0 verbose cli /usr/bin/node /usr/bin/npm
1 info using npm@10.9.2
2 info using node@v22.16.0
3 silly config load:file:/usr/lib/node_modules/npm/npmrc
4 silly config load:file:/mnt/c/Users/<USER>/.npmrc
5 error config prefix cannot be changed from project config: /mnt/c/Users/<USER>/.npmrc.
6 silly config load:file:/home/<USER>/.npmrc
7 silly config load:file:/mnt/c/Users/<USER>/IdeaProjects/rich-test/C:\Program Files\nodejs\node_global/etc/npmrc
8 verbose title npm view @anthropic-ai/claude-code@latest version
9 verbose argv "view" "@anthropic-ai/claude-code@latest" "version"
10 verbose logfile logs-max:10 dir:/mnt/c/Users/<USER>/IdeaProjects/rich-test/C:\Program Files\nodejs\node_cache/_logs/2025-06-25T05_08_29_061Z-
11 verbose logfile /mnt/c/Users/<USER>/IdeaProjects/rich-test/C:\Program Files\nodejs\node_cache/_logs/2025-06-25T05_08_29_061Z-debug-0.log
12 silly logfile start cleaning logs, removing 1 files
13 silly logfile done cleaning log files
14 http fetch GET 200 https://registry.npmmirror.com/@anthropic-ai%2fclaude-code 272ms (cache revalidated)
15 verbose cwd /mnt/c/Users/<USER>/IdeaProjects/rich-test
16 verbose os Linux 6.6.87.2-microsoft-standard-WSL2
17 verbose node v22.16.0
18 verbose npm  v10.9.2
19 verbose exit 0
20 info ok
