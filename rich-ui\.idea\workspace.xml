<?xml version="1.0" encoding="UTF-8"?>
<project version="4">
  <component name="AutoImportSettings">
    <option name="autoReloadType" value="SELECTIVE" />
  </component>
  <component name="ChangeListManager">
    <list default="true" id="9a7c06ae-b731-43a8-a198-add70f29313d" name="Changes" comment="2025-04-25 提交">
      <change beforePath="$PROJECT_DIR$/../CProgram Filesnodejsnode_cache/_cacache/index-v5/16/d1/e95c734eb8ea87454bc6c85a6a46b38a4b8425c0c498d8f45e1c2f3d80e2" beforeDir="false" afterPath="$PROJECT_DIR$/../CProgram Filesnodejsnode_cache/_cacache/index-v5/16/d1/e95c734eb8ea87454bc6c85a6a46b38a4b8425c0c498d8f45e1c2f3d80e2" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../CProgram Filesnodejsnode_cache/_cacache/index-v5/6e/16/30ffdffe95a67c3aa6860972f4a11e64556023aec022bfc60ce8d4d0c74f" beforeDir="false" afterPath="$PROJECT_DIR$/../CProgram Filesnodejsnode_cache/_cacache/index-v5/6e/16/30ffdffe95a67c3aa6860972f4a11e64556023aec022bfc60ce8d4d0c74f" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../CProgram Filesnodejsnode_cache/_logs/2025-06-24T07_05_56_966Z-debug-0.log" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../CProgram Filesnodejsnode_cache/_logs/2025-06-24T07_18_05_524Z-debug-0.log" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../CProgram Filesnodejsnode_cache/_logs/2025-06-24T07_24_50_840Z-debug-0.log" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../CProgram Filesnodejsnode_cache/_logs/2025-06-24T07_25_02_955Z-debug-0.log" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../CProgram Filesnodejsnode_cache/_logs/2025-06-24T07_29_08_813Z-debug-0.log" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../CProgram Filesnodejsnode_cache/_logs/2025-06-24T07_29_19_337Z-debug-0.log" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../CProgram Filesnodejsnode_cache/_logs/2025-06-24T09_18_53_654Z-debug-0.log" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../CProgram Filesnodejsnode_cache/_logs/2025-06-24T09_19_18_082Z-debug-0.log" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../CProgram Filesnodejsnode_cache/_logs/2025-06-24T09_25_36_637Z-debug-0.log" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../CProgram Filesnodejsnode_cache/_logs/2025-06-24T09_26_55_679Z-debug-0.log" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../CProgram Filesnodejsnode_cache/_logs/2025-06-24T09_56_55_787Z-debug-0.log" beforeDir="false" />
      <change beforePath="$PROJECT_DIR$/../rich-app/src/config.js" beforeDir="false" afterPath="$PROJECT_DIR$/../rich-app/src/config.js" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../rich-app/src/packageA/inventory/edit.vue" beforeDir="false" afterPath="$PROJECT_DIR$/../rich-app/src/packageA/inventory/edit.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../rich-system/src/main/java/com/rich/system/service/impl/MpInventoryServiceImpl.java" beforeDir="false" afterPath="$PROJECT_DIR$/../rich-system/src/main/java/com/rich/system/service/impl/MpInventoryServiceImpl.java" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../rich-system/src/main/resources/mapper/system/MpInventoryMapper.xml" beforeDir="false" afterPath="$PROJECT_DIR$/../rich-system/src/main/resources/mapper/system/MpInventoryMapper.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/../rich-system/src/main/resources/mapper/system/RsInventoryMapper.xml" beforeDir="false" afterPath="$PROJECT_DIR$/../rich-system/src/main/resources/mapper/system/RsInventoryMapper.xml" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/views/system/inventory/index.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/views/system/inventory/index.vue" afterDir="false" />
      <change beforePath="$PROJECT_DIR$/src/views/system/outboundRecord/index.vue" beforeDir="false" afterPath="$PROJECT_DIR$/src/views/system/outboundRecord/index.vue" afterDir="false" />
    </list>
    <option name="SHOW_DIALOG" value="false" />
    <option name="HIGHLIGHT_CONFLICTS" value="true" />
    <option name="HIGHLIGHT_NON_ACTIVE_CHANGELIST" value="false" />
    <option name="LAST_RESOLUTION" value="IGNORE" />
  </component>
  <component name="FileTemplateManagerImpl">
    <option name="RECENT_TEMPLATES">
      <list>
        <option value="Vue Composition API Component" />
        <option value="JavaScript File" />
        <option value="Vue Options API Component" />
      </list>
    </option>
  </component>
  <component name="Git.Settings">
    <option name="RECENT_BRANCH_BY_REPOSITORY">
      <map>
        <entry key="$PROJECT_DIR$/.." value="master" />
      </map>
    </option>
    <option name="RECENT_GIT_ROOT_PATH" value="$PROJECT_DIR$/.." />
  </component>
  <component name="HighlightingSettingsPerFile">
    <setting file="file://$PROJECT_DIR$/src/views/system/freight/index.vue" root0="FORCE_HIGHLIGHTING" />
  </component>
  <component name="MarkdownSettingsMigration">
    <option name="stateVersion" value="1" />
  </component>
  <component name="ProblemsViewState">
    <option name="selectedTabId" value="CurrentFile" />
  </component>
  <component name="ProjectColorInfo">{
  &quot;associatedIndex&quot;: 2
}</component>
  <component name="ProjectId" id="2WSaCTacbYypczSYLtFcYm77e2o" />
  <component name="ProjectLevelVcsManager">
    <ConfirmationsSetting value="2" id="Add" />
  </component>
  <component name="ProjectViewState">
    <option name="autoscrollToSource" value="true" />
    <option name="hideEmptyMiddlePackages" value="true" />
    <option name="openDirectoriesWithSingleClick" value="true" />
    <option name="showLibraryContents" value="true" />
  </component>
  <component name="PropertiesComponent">{
  &quot;keyToString&quot;: {
    &quot;ASKED_ADD_EXTERNAL_FILES&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.OpenProjectViewOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.ShowReadmeOnStart&quot;: &quot;true&quot;,
    &quot;RunOnceActivity.git.unshallow&quot;: &quot;true&quot;,
    &quot;WebServerToolWindowFactoryState&quot;: &quot;false&quot;,
    &quot;git-widget-placeholder&quot;: &quot;master&quot;,
    &quot;ignore.virus.scanning.warn.message&quot;: &quot;true&quot;,
    &quot;js.last.introduce.type&quot;: &quot;LET&quot;,
    &quot;last_opened_file_path&quot;: &quot;C:/Users/<USER>/IdeaProjects/rich-test/rich-ui/src/print-template&quot;,
    &quot;node.js.detected.package.eslint&quot;: &quot;true&quot;,
    &quot;node.js.detected.package.tslint&quot;: &quot;true&quot;,
    &quot;node.js.selected.package.eslint&quot;: &quot;(autodetect)&quot;,
    &quot;node.js.selected.package.tslint&quot;: &quot;(autodetect)&quot;,
    &quot;nodejs_package_manager_path&quot;: &quot;npm&quot;,
    &quot;npm.build:prod.executor&quot;: &quot;Run&quot;,
    &quot;npm.dev.executor&quot;: &quot;Run&quot;,
    &quot;settings.editor.selected.configurable&quot;: &quot;settings.vue&quot;,
    &quot;ts.external.directory.path&quot;: &quot;C:\\Program Files\\JetBrains\\WebStorm 2024.3.5\\plugins\\javascript-plugin\\jsLanguageServicesImpl\\external&quot;,
    &quot;vue.rearranger.settings.migration&quot;: &quot;true&quot;
  },
  &quot;keyToStringList&quot;: {
    &quot;vue.recent.templates&quot;: [
      &quot;Vue Composition API Component&quot;,
      &quot;Vue Options API Component&quot;
    ]
  }
}</component>
  <component name="RecentsManager">
    <key name="CopyFile.RECENT_KEYS">
      <recent name="C:\Users\<USER>\IdeaProjects\rich-test\rich-ui\src\print-template" />
      <recent name="C:\Users\<USER>\IdeaProjects\rich-test\rich-ui\src\config" />
      <recent name="C:\Users\<USER>\IdeaProjects\rich-test\rich-ui\src\views\system" />
      <recent name="C:\Users\<USER>\IdeaProjects\rich-test\rich-ui\src\assets\images" />
      <recent name="C:\Users\<USER>\IdeaProjects\rich-test\rich-ui\src\views\system\quotation" />
    </key>
    <key name="MoveFile.RECENT_KEYS">
      <recent name="C:\Users\<USER>\IdeaProjects\rich-test\rich-ui\src\config" />
      <recent name="C:\Users\<USER>\IdeaProjects\rich-test\rich-ui\src\views\system\bookingList" />
      <recent name="C:\Users\<USER>\IdeaProjects\rich-test\rich-ui\src\views\print" />
      <recent name="C:\Users\<USER>\IdeaProjects\rich-test\rich-ui\src\views\system" />
      <recent name="C:\Users\<USER>\IdeaProjects\rich-test\rich-ui\src\assets\icons\svg" />
    </key>
  </component>
  <component name="RunManager" selected="npm.build:prod">
    <configuration name="build" type="js.build_tools.npm" nameIsGenerated="true">
      <package-json value="$PROJECT_DIR$/package.json" />
      <command value="build" />
      <node-interpreter value="project" />
      <envs />
      <method v="2" />
    </configuration>
    <configuration name="build:prod" type="js.build_tools.npm" temporary="true" nameIsGenerated="true">
      <package-json value="$PROJECT_DIR$/package.json" />
      <command value="run" />
      <scripts>
        <script value="build:prod" />
      </scripts>
      <node-interpreter value="project" />
      <envs />
      <method v="2" />
    </configuration>
    <configuration name="dev" type="js.build_tools.npm" nameIsGenerated="true">
      <package-json value="$PROJECT_DIR$/package.json" />
      <command value="run" />
      <scripts>
        <script value="dev" />
      </scripts>
      <node-interpreter value="project" />
      <envs />
      <method v="2" />
    </configuration>
    <list>
      <item itemvalue="npm.build" />
      <item itemvalue="npm.dev" />
      <item itemvalue="npm.build:prod" />
    </list>
    <recent_temporary>
      <list>
        <item itemvalue="npm.build:prod" />
        <item itemvalue="npm.build:prod" />
      </list>
    </recent_temporary>
  </component>
  <component name="SharedIndexes">
    <attachedChunks>
      <set>
        <option value="bundled-js-predefined-d6986cc7102b-1632447f56bf-JavaScript-WS-243.26053.12" />
      </set>
    </attachedChunks>
  </component>
  <component name="SpellCheckerSettings" RuntimeDictionaries="0" Folders="0" CustomDictionaries="0" DefaultDictionary="application-level" UseSingleDictionary="true" transferred="true" />
  <component name="TaskManager">
    <task active="true" id="Default" summary="Default task">
      <changelist id="9a7c06ae-b731-43a8-a198-add70f29313d" name="Changes" comment="" />
      <created>1696728308993</created>
      <option name="number" value="Default" />
      <option name="presentableId" value="Default" />
      <updated>1696728308993</updated>
      <workItem from="1696728311871" duration="16197000" />
      <workItem from="1696812765530" duration="3996000" />
      <workItem from="1696818012794" duration="3132000" />
      <workItem from="1696823178768" duration="20093000" />
      <workItem from="1696898712832" duration="33432000" />
      <workItem from="1696986434224" duration="33933000" />
      <workItem from="1697071918119" duration="47754000" />
      <workItem from="1697417622955" duration="68000" />
      <workItem from="1697417700466" duration="52000" />
      <workItem from="1697417761567" duration="79606000" />
      <workItem from="1698022537616" duration="54554000" />
      <workItem from="1698387983502" duration="9957000" />
      <workItem from="1698625931039" duration="67899000" />
      <workItem from="1699230636634" duration="16764000" />
      <workItem from="1699318313607" duration="61015000" />
      <workItem from="1699576450490" duration="17012000" />
      <workItem from="1699836556489" duration="115355000" />
      <workItem from="1700441387006" duration="28854000" />
      <workItem from="1700527810626" duration="52305000" />
      <workItem from="1700732794847" duration="1970000" />
      <workItem from="1700787653926" duration="7863000" />
      <workItem from="1701045856533" duration="11256000" />
      <workItem from="1701151990232" duration="41334000" />
      <workItem from="1701650069286" duration="88714000" />
      <workItem from="1702255538010" duration="44036000" />
      <workItem from="1702513497914" duration="14423000" />
      <workItem from="1702601492282" duration="5245000" />
      <workItem from="1702861315042" duration="11317000" />
      <workItem from="1702946862719" duration="17293000" />
      <workItem from="1703034819801" duration="55244000" />
      <workItem from="1703464674187" duration="91136000" />
      <workItem from="1704329611733" duration="26637000" />
      <workItem from="1704676215903" duration="49310000" />
      <workItem from="1704933740561" duration="45069000" />
      <workItem from="1705280002006" duration="19601000" />
      <workItem from="1705365988601" duration="73607000" />
      <workItem from="1705884765019" duration="11169000" />
      <workItem from="1705973102815" duration="19543000" />
      <workItem from="1706059981466" duration="10764000" />
      <workItem from="1706145697386" duration="9415000" />
      <workItem from="1706167846943" duration="5397000" />
      <workItem from="1706230302339" duration="18716000" />
      <workItem from="1706490656391" duration="22123000" />
      <workItem from="1706575716226" duration="20478000" />
      <workItem from="1706661595045" duration="22668000" />
      <workItem from="1706748329587" duration="21851000" />
      <workItem from="1706835261750" duration="20915000" />
      <workItem from="1707007683857" duration="17434000" />
      <workItem from="1707093954045" duration="11513000" />
      <workItem from="1707181280111" duration="18607000" />
      <workItem from="1707267459811" duration="3706000" />
      <workItem from="1707354518893" duration="4999000" />
      <workItem from="1708217012137" duration="15196000" />
      <workItem from="1708304325083" duration="14338000" />
      <workItem from="1708390518776" duration="17287000" />
      <workItem from="1708476880697" duration="8259000" />
      <workItem from="1708563124054" duration="18247000" />
      <workItem from="1708649587602" duration="21519000" />
      <workItem from="1708908757146" duration="4257000" />
      <workItem from="1708922778696" duration="11122000" />
      <workItem from="1708994903790" duration="8666000" />
      <workItem from="1709081093189" duration="11257000" />
      <workItem from="1709168479472" duration="20054000" />
      <workItem from="1709254477839" duration="290000" />
      <workItem from="1709256782473" duration="16346000" />
      <workItem from="1709513811844" duration="22787000" />
      <workItem from="1709600384139" duration="21271000" />
      <workItem from="1709777183271" duration="10604000" />
      <workItem from="1709858618626" duration="19056000" />
      <workItem from="1710117713884" duration="16637000" />
      <workItem from="1710204120215" duration="11189000" />
      <workItem from="1710290780156" duration="18505000" />
      <workItem from="1710377897396" duration="5871000" />
      <workItem from="1710384864895" duration="18331000" />
      <workItem from="1710463583051" duration="7225000" />
      <workItem from="1710723018645" duration="11965000" />
      <workItem from="1710809481394" duration="13894000" />
      <workItem from="1710842599130" duration="8000" />
      <workItem from="1710896242743" duration="17287000" />
      <workItem from="1710981924192" duration="19459000" />
      <workItem from="1711069297561" duration="21721000" />
      <workItem from="1711101122350" duration="842000" />
      <workItem from="1711327940489" duration="21029000" />
      <workItem from="1711414048890" duration="14820000" />
      <workItem from="1711502592864" duration="15023000" />
      <workItem from="1711587171137" duration="9993000" />
      <workItem from="1711674031567" duration="10788000" />
      <workItem from="1711932772016" duration="16645000" />
      <workItem from="1712018951054" duration="21753000" />
      <workItem from="1712105222955" duration="21350000" />
      <workItem from="1712452110769" duration="21015000" />
      <workItem from="1712537086054" duration="20247000" />
      <workItem from="1712623436281" duration="19409000" />
      <workItem from="1712710696954" duration="16267000" />
      <workItem from="1712796242724" duration="12769000" />
      <workItem from="1712881340372" duration="17040000" />
      <workItem from="1713142068541" duration="19813000" />
      <workItem from="1713228666997" duration="20917000" />
      <workItem from="1713263611921" duration="1731000" />
      <workItem from="1713314607187" duration="24641000" />
      <workItem from="1713401430276" duration="20867000" />
      <workItem from="1713488121927" duration="15110000" />
      <workItem from="1713747365564" duration="19204000" />
      <workItem from="1713835301891" duration="18151000" />
      <workItem from="1713919909280" duration="15803000" />
      <workItem from="1714006070412" duration="21324000" />
      <workItem from="1714040311825" duration="16973000" />
      <workItem from="1714351552997" duration="19153000" />
      <workItem from="1714438008375" duration="19506000" />
      <workItem from="1714956422550" duration="18463000" />
      <workItem from="1715043181648" duration="23135000" />
      <workItem from="1715128991875" duration="24232000" />
      <workItem from="1715215304014" duration="25665000" />
      <workItem from="1715301679694" duration="18295000" />
      <workItem from="1715388421834" duration="22259000" />
      <workItem from="1715560945939" duration="19463000" />
      <workItem from="1715647249450" duration="26122000" />
      <workItem from="1715733353121" duration="19089000" />
      <workItem from="1715768238366" duration="249000" />
      <workItem from="1715820215622" duration="23175000" />
      <workItem from="1715906539654" duration="16665000" />
      <workItem from="1716167426656" duration="22636000" />
      <workItem from="1716253490006" duration="19968000" />
      <workItem from="1716338427290" duration="20063000" />
      <workItem from="1716424628674" duration="22513000" />
      <workItem from="1716510442325" duration="19956000" />
      <workItem from="1716769235167" duration="16322000" />
      <workItem from="1716804548659" duration="455000" />
      <workItem from="1716857190879" duration="12497000" />
      <workItem from="1716943964269" duration="20048000" />
      <workItem from="1717029633920" duration="21507000" />
      <workItem from="1717113524043" duration="21053000" />
      <workItem from="1717375311601" duration="20986000" />
      <workItem from="1717461772933" duration="18558000" />
      <workItem from="1717549634415" duration="17524000" />
      <workItem from="1717633817723" duration="22648000" />
      <workItem from="1717720752962" duration="14754000" />
      <workItem from="1718067155380" duration="20687000" />
      <workItem from="1718153768082" duration="19307000" />
      <workItem from="1718239504045" duration="13209000" />
      <workItem from="1718326099411" duration="20596000" />
      <workItem from="1718584771580" duration="15425000" />
      <workItem from="1718671349341" duration="16305000" />
      <workItem from="1718760242642" duration="19210000" />
      <workItem from="1718843723133" duration="21288000" />
      <workItem from="1718930753824" duration="16443000" />
      <workItem from="1719189795638" duration="12321000" />
      <workItem from="1719276846503" duration="24763000" />
      <workItem from="1719362233744" duration="19915000" />
      <workItem from="1719448780454" duration="20292000" />
      <workItem from="1719535023936" duration="17796000" />
      <workItem from="1719794837581" duration="20336000" />
      <workItem from="1719880906014" duration="19738000" />
      <workItem from="1719966295415" duration="13932000" />
      <workItem from="1720053706749" duration="35179000" />
      <workItem from="1720399717231" duration="19288000" />
      <workItem from="1720485793380" duration="15050000" />
      <workItem from="1720572053474" duration="15657000" />
      <workItem from="1720658575708" duration="13747000" />
      <workItem from="1720744763850" duration="22094000" />
      <workItem from="1721004143864" duration="18481000" />
      <workItem from="1721090310013" duration="17507000" />
      <workItem from="1721176483999" duration="23047000" />
      <workItem from="1721262893283" duration="36249000" />
      <workItem from="1721608780858" duration="16082000" />
      <workItem from="1721639743732" duration="2619000" />
      <workItem from="1721694988031" duration="19033000" />
      <workItem from="1721781741531" duration="17491000" />
      <workItem from="1721868340190" duration="29364000" />
      <workItem from="1722214028835" duration="11077000" />
      <workItem from="1722299949754" duration="12897000" />
      <workItem from="1722332277629" duration="901000" />
      <workItem from="1722386333350" duration="9988000" />
      <workItem from="1722473082399" duration="30854000" />
      <workItem from="1722818601807" duration="15910000" />
      <workItem from="1722905503765" duration="13434000" />
      <workItem from="1722991561304" duration="12840000" />
      <workItem from="1723077719875" duration="18054000" />
      <workItem from="1723164025890" duration="19024000" />
      <workItem from="1723423071932" duration="18651000" />
      <workItem from="1723509887160" duration="19482000" />
      <workItem from="1723596346557" duration="15353000" />
      <workItem from="1723682106017" duration="16078000" />
      <workItem from="1723767857368" duration="12222000" />
      <workItem from="1724027760333" duration="14994000" />
      <workItem from="1724114731768" duration="6674000" />
      <workItem from="1724137653225" duration="8624000" />
      <workItem from="1724201015122" duration="19890000" />
      <workItem from="1724287298321" duration="25153000" />
      <workItem from="1724720074226" duration="14017000" />
      <workItem from="1724805555097" duration="14962000" />
      <workItem from="1724893361336" duration="30872000" />
      <workItem from="1725238049046" duration="15715000" />
      <workItem from="1725323846131" duration="20704000" />
      <workItem from="1725411120631" duration="12211000" />
      <workItem from="1725496505788" duration="34954000" />
      <workItem from="1725843084869" duration="9885000" />
      <workItem from="1725929386169" duration="15183000" />
      <workItem from="1726016119027" duration="18404000" />
      <workItem from="1726102090213" duration="18992000" />
      <workItem from="1726188447202" duration="17985000" />
      <workItem from="1726274049318" duration="17407000" />
      <workItem from="1726621089151" duration="12680000" />
      <workItem from="1726708183152" duration="19801000" />
      <workItem from="1726792490808" duration="10450000" />
      <workItem from="1727052108172" duration="5998000" />
      <workItem from="1727138889056" duration="15758000" />
      <workItem from="1727225547229" duration="18759000" />
      <workItem from="1727311195792" duration="10784000" />
      <workItem from="1727397120004" duration="18512000" />
      <workItem from="1727570203289" duration="14484000" />
      <workItem from="1727656540990" duration="5957000" />
      <workItem from="1728348629673" duration="19309000" />
      <workItem from="1728434706245" duration="20079000" />
      <workItem from="1728521388135" duration="8433000" />
      <workItem from="1728530514748" duration="14735000" />
      <workItem from="1728607262000" duration="21278000" />
      <workItem from="1728693997481" duration="21923000" />
      <workItem from="1728867047056" duration="18425000" />
      <workItem from="1728953499447" duration="2946000" />
      <workItem from="1728972219984" duration="12401000" />
      <workItem from="1729039197119" duration="12111000" />
      <workItem from="1729125133498" duration="17644000" />
      <workItem from="1729212320043" duration="15518000" />
      <workItem from="1729471124813" duration="760000" />
      <workItem from="1729471910265" duration="21340000" />
      <workItem from="1729557830025" duration="12664000" />
      <workItem from="1729644713138" duration="11699000" />
      <workItem from="1729730421751" duration="14905000" />
      <workItem from="1729816432344" duration="15353000" />
      <workItem from="1730076206554" duration="15222000" />
      <workItem from="1730162437997" duration="10895000" />
      <workItem from="1730249139217" duration="15081000" />
      <workItem from="1730335073102" duration="15891000" />
      <workItem from="1730421919613" duration="17960000" />
      <workItem from="1730681147596" duration="15379000" />
      <workItem from="1730767188796" duration="10877000" />
      <workItem from="1730854212088" duration="11374000" />
      <workItem from="1730940637073" duration="15277000" />
      <workItem from="1731026806673" duration="17355000" />
      <workItem from="1731286912435" duration="10716000" />
      <workItem from="1731371975015" duration="14209000" />
      <workItem from="1731457967022" duration="13832000" />
      <workItem from="1731545067358" duration="26832000" />
      <workItem from="1731584684161" duration="71000" />
      <workItem from="1731631259100" duration="20193000" />
      <workItem from="1731890496035" duration="21779000" />
      <workItem from="1731977151541" duration="17131000" />
      <workItem from="1732063604296" duration="23145000" />
      <workItem from="1732150095782" duration="17486000" />
      <workItem from="1732236343881" duration="19924000" />
      <workItem from="1732495616187" duration="12498000" />
      <workItem from="1732581708607" duration="11811000" />
      <workItem from="1732668274368" duration="11037000" />
      <workItem from="1732754336540" duration="13273000" />
      <workItem from="1732841015645" duration="21677000" />
      <workItem from="1733100048621" duration="15165000" />
      <workItem from="1733186333972" duration="15673000" />
      <workItem from="1733272857493" duration="18127000" />
      <workItem from="1733359237659" duration="19624000" />
      <workItem from="1733445287828" duration="16396000" />
      <workItem from="1733705613361" duration="15203000" />
      <workItem from="1733791423489" duration="20589000" />
      <workItem from="1733877655836" duration="20535000" />
      <workItem from="1733964295761" duration="16214000" />
      <workItem from="1734050832190" duration="12714000" />
      <workItem from="1734309327274" duration="13196000" />
      <workItem from="1734396082050" duration="11587000" />
      <workItem from="1734482216403" duration="9990000" />
      <workItem from="1734568985744" duration="21700000" />
      <workItem from="1734655251765" duration="12285000" />
      <workItem from="1734914869214" duration="12011000" />
      <workItem from="1735001049083" duration="14899000" />
      <workItem from="1735087556691" duration="12067000" />
      <workItem from="1735173817662" duration="21960000" />
      <workItem from="1735260023277" duration="10811000" />
      <workItem from="1735519192500" duration="14510000" />
      <workItem from="1735605834301" duration="9471000" />
      <workItem from="1735778143758" duration="19595000" />
      <workItem from="1735817986454" duration="579000" />
      <workItem from="1735865085298" duration="26526000" />
      <workItem from="1735956074177" duration="17998000" />
      <workItem from="1736124277900" duration="21482000" />
      <workItem from="1736210207048" duration="20850000" />
      <workItem from="1736297166747" duration="23751000" />
      <workItem from="1736383646973" duration="14351000" />
      <workItem from="1736469704267" duration="21305000" />
      <workItem from="1736729264756" duration="19129000" />
      <workItem from="1736815522242" duration="26185000" />
      <workItem from="1736901763251" duration="22556000" />
      <workItem from="1736988138757" duration="27036000" />
      <workItem from="1737075080599" duration="18611000" />
      <workItem from="1737333673559" duration="30081000" />
      <workItem from="1737420053481" duration="28969000" />
      <workItem from="1737505876013" duration="18317000" />
      <workItem from="1737609557017" duration="13865000" />
      <workItem from="1737678932559" duration="22410000" />
      <workItem from="1737764851504" duration="14428000" />
      <workItem from="1738716416050" duration="10183000" />
      <workItem from="1738802442318" duration="10640000" />
      <workItem from="1738825220300" duration="12640000" />
      <workItem from="1738889299698" duration="15733000" />
      <workItem from="1738974929072" duration="20033000" />
      <workItem from="1739148795716" duration="20345000" />
      <workItem from="1739185203244" duration="417000" />
      <workItem from="1739185647447" duration="301000" />
      <workItem from="1739234305000" duration="16118000" />
      <workItem from="1739320801768" duration="18665000" />
      <workItem from="1739407214505" duration="20350000" />
      <workItem from="1739492840931" duration="15963000" />
      <workItem from="1739753033302" duration="14097000" />
      <workItem from="1739840508778" duration="20920000" />
      <workItem from="1739925734690" duration="20912000" />
      <workItem from="1740011884631" duration="14793000" />
      <workItem from="1740039992248" duration="3712000" />
      <workItem from="1740098657446" duration="20214000" />
      <workItem from="1740357613978" duration="20581000" />
      <workItem from="1740444007119" duration="10968000" />
      <workItem from="1740530729707" duration="17348000" />
      <workItem from="1740617554575" duration="15876000" />
      <workItem from="1740703285230" duration="16862000" />
      <workItem from="1740962500899" duration="15781000" />
      <workItem from="1741049091181" duration="21030000" />
      <workItem from="1741135301697" duration="12818000" />
      <workItem from="1741221944814" duration="19317000" />
      <workItem from="1741308459522" duration="17553000" />
      <workItem from="1741567488825" duration="16707000" />
      <workItem from="1741653722209" duration="20013000" />
      <workItem from="1741740477429" duration="17137000" />
      <workItem from="1741826632365" duration="17049000" />
      <workItem from="1741918768512" duration="13192000" />
      <workItem from="1742171931724" duration="18417000" />
      <workItem from="1742258599931" duration="13110000" />
      <workItem from="1742345044892" duration="18792000" />
      <workItem from="1742431058567" duration="17621000" />
      <workItem from="1742517440702" duration="6066000" />
      <workItem from="1742539338849" duration="2539000" />
      <workItem from="1742777439791" duration="4663000" />
      <workItem from="1742786887420" duration="991000" />
      <workItem from="1742792901851" duration="9138000" />
      <workItem from="1742862898080" duration="10830000" />
      <workItem from="1742949867507" duration="14497000" />
      <workItem from="1743035909418" duration="13774000" />
      <workItem from="1743122549727" duration="11613000" />
      <workItem from="1743381951337" duration="12982000" />
      <workItem from="1743468261346" duration="65000" />
      <workItem from="1743468354307" duration="365000" />
      <workItem from="1743469356203" duration="15907000" />
      <workItem from="1743554250838" duration="20699000" />
      <workItem from="1743641153396" duration="16090000" />
      <workItem from="1743986782124" duration="13020000" />
      <workItem from="1744072823401" duration="20572000" />
      <workItem from="1744159265746" duration="16904000" />
      <workItem from="1744245264107" duration="16371000" />
      <workItem from="1744332433611" duration="16443000" />
      <workItem from="1744591466338" duration="14470000" />
      <workItem from="1744626489000" duration="2834000" />
      <workItem from="1744677670661" duration="20795000" />
      <workItem from="1744763767303" duration="20603000" />
      <workItem from="1744850648023" duration="16396000" />
      <workItem from="1744936843678" duration="16509000" />
      <workItem from="1745196724304" duration="17912000" />
      <workItem from="1745282029379" duration="17864000" />
      <workItem from="1745369065700" duration="20047000" />
      <workItem from="1745454479358" duration="12464000" />
      <workItem from="1745541680110" duration="15911000" />
      <workItem from="1745714667915" duration="16799000" />
      <workItem from="1745800914920" duration="20465000" />
      <workItem from="1745887099915" duration="13400000" />
      <workItem from="1745973476061" duration="8371000" />
      <workItem from="1746491947177" duration="6191000" />
      <workItem from="1746578914528" duration="2111000" />
      <workItem from="1746584060033" duration="3424000" />
      <workItem from="1746664963736" duration="9374000" />
      <workItem from="1746750997444" duration="7909000" />
      <workItem from="1747010478674" duration="3645000" />
      <workItem from="1747097310525" duration="7525000" />
      <workItem from="1747183860972" duration="3291000" />
      <workItem from="1747270237043" duration="4948000" />
      <workItem from="1747356165596" duration="16369000" />
      <workItem from="1747615474573" duration="10582000" />
      <workItem from="1747702164898" duration="9653000" />
      <workItem from="1747787834326" duration="13432000" />
      <workItem from="1747873924740" duration="8382000" />
      <workItem from="1747961155499" duration="14396000" />
      <workItem from="1748002178814" duration="332000" />
      <workItem from="1748220477115" duration="15212000" />
      <workItem from="1748255016536" duration="206000" />
      <workItem from="1748306273610" duration="9862000" />
      <workItem from="1748392787212" duration="18081000" />
      <workItem from="1748429098811" duration="2698000" />
      <workItem from="1748479199135" duration="19636000" />
      <workItem from="1748565355711" duration="11260000" />
      <workItem from="1748911251360" duration="10054000" />
      <workItem from="1748997651107" duration="10380000" />
      <workItem from="1749084318588" duration="5696000" />
      <workItem from="1749169697801" duration="11122000" />
      <workItem from="1749430167855" duration="15306000" />
      <workItem from="1749515592315" duration="14499000" />
      <workItem from="1749602308156" duration="12291000" />
      <workItem from="1749688992126" duration="21041000" />
      <workItem from="1749775300846" duration="30842000" />
      <workItem from="1750035250464" duration="10060000" />
      <workItem from="1750121739369" duration="14382000" />
      <workItem from="1750207134432" duration="13347000" />
      <workItem from="1750293662223" duration="7327000" />
      <workItem from="1750380408867" duration="10724000" />
      <workItem from="1750639427324" duration="10983000" />
      <workItem from="1750725289869" duration="2839000" />
      <workItem from="1750730832585" duration="4119000" />
      <workItem from="1750812407996" duration="7043000" />
    </task>
    <task id="LOCAL-00001" summary="2023-10-9 提交">
      <option name="closed" value="true" />
      <created>1696848436774</created>
      <option name="number" value="00001" />
      <option name="presentableId" value="LOCAL-00001" />
      <option name="project" value="LOCAL" />
      <updated>1696848436774</updated>
    </task>
    <task id="LOCAL-00002" summary="2024-01-16 提交">
      <option name="closed" value="true" />
      <created>1705397381707</created>
      <option name="number" value="00002" />
      <option name="presentableId" value="LOCAL-00002" />
      <option name="project" value="LOCAL" />
      <updated>1705397381707</updated>
    </task>
    <task id="LOCAL-00003" summary="2024-06-02 提交">
      <option name="closed" value="true" />
      <created>1718160666776</created>
      <option name="number" value="00003" />
      <option name="presentableId" value="LOCAL-00003" />
      <option name="project" value="LOCAL" />
      <updated>1718160666776</updated>
    </task>
    <task id="LOCAL-00004" summary="2025-04-24 提交">
      <option name="closed" value="true" />
      <created>1745488718840</created>
      <option name="number" value="00004" />
      <option name="presentableId" value="LOCAL-00004" />
      <option name="project" value="LOCAL" />
      <updated>1745488718840</updated>
    </task>
    <task id="LOCAL-00005" summary="2025-04-25 提交">
      <option name="closed" value="true" />
      <created>1745575738051</created>
      <option name="number" value="00005" />
      <option name="presentableId" value="LOCAL-00005" />
      <option name="project" value="LOCAL" />
      <updated>1745575738051</updated>
    </task>
    <option name="localTasksCounter" value="6" />
    <servers />
  </component>
  <component name="TypeScriptGeneratedFilesManager">
    <option name="version" value="3" />
  </component>
  <component name="Vcs.Log.History.Properties">
    <option name="COLUMN_ID_ORDER">
      <list>
        <option value="Default.Root" />
        <option value="Default.Author" />
        <option value="Default.Date" />
        <option value="Default.Subject" />
        <option value="Space.CommitStatus" />
      </list>
    </option>
  </component>
  <component name="Vcs.Log.Tabs.Properties">
    <option name="TAB_STATES">
      <map>
        <entry key="MAIN">
          <value>
            <State />
          </value>
        </entry>
      </map>
    </option>
  </component>
  <component name="VcsManagerConfiguration">
    <option name="ADD_EXTERNAL_FILES_SILENTLY" value="true" />
    <MESSAGE value="2023-10-9 提交" />
    <MESSAGE value="2024-01-16 提交" />
    <MESSAGE value="2024-06-02 提交" />
    <MESSAGE value="2025-04-24 提交" />
    <MESSAGE value="2025-04-25 提交" />
    <option name="LAST_COMMIT_MESSAGE" value="2025-04-25 提交" />
  </component>
</project>