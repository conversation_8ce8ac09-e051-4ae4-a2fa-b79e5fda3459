<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper
        PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.rich.system.mapper.RsInventoryMapper">
    <resultMap type="RsInventory" id="RsInventoryResult">
        <id property="inventoryId" column="inventory_id"/>
        <result property="inventoryStatus" column="inventory_status"/>
        <result property="inboundSerialNo" column="inbound_serial_no"/>
        <result property="inboundSerialSplit" column="inbound_serial_split"/>
        <result property="inboundDate" column="inbound_date"/>
        <result property="outboundNo" column="outbound_no"/>
        <result property="forwarderNo" column="forwarder_no"/>
        <result property="rentalSettlementDate" column="rental_settlement_date"/>
        <result property="outboundDate" column="outbound_date"/>
        <result property="clientCode" column="client_code"/>
        <result property="clientName" column="client_name"/>
        <result property="subOrderNo" column="sub_order_no"/>
        <result property="supplier" column="supplier"/>
        <result property="driverInfo" column="driver_info"/>
        <result property="sqdShippingMark" column="sqd_shipping_mark"/>
        <result property="cargoName" column="cargo_name"/>
        <result property="totalBoxes" column="total_boxes"/>
<!--        <result property="packageType" column="package_type"/>-->
        <result property="totalGrossWeight" column="total_gross_weight"/>
        <result property="totalVolume" column="total_volume"/>
        <result property="damageStatus" column="damage_status"/>
        <result property="storageLocation1" column="storage_location1"/>
        <result property="storageLocation2" column="storage_location2"/>
        <result property="storageLocation3" column="storage_location3"/>
        <result property="receivedStorageFee" column="received_storage_fee"/>
        <result property="unpaidUnloadingFee" column="unpaid_unloading_fee"/>
        <result property="logisticsAdvanceFee" column="logistics_advance_fee"/>
        <result property="rentalBalanceFee" column="rental_balance_fee"/>
        <result property="freeStackPeriod" column="free_stack_period"/>
        <result property="overdueRentalUnitPrice" column="overdue_rental_unit_price"/>
        <result property="overdueRentalFee" column="overdue_rental_fee"/>
        <result property="notes" column="notes"/>
        <result property="warehouseCode" column="warehouse_code"/>
        <result property="recordType" column="record_type"/>
        <result property="inboundType" column="inbound_type"/>
        <result property="cargoNature" column="cargo_nature"/>
        <result property="createdAt" column="created_at"/>
        <result property="preOutboundFlag" column="pre_outbound_flag"/>
        <result property="outboundRequestFlag" column="outbound_request_flag"/>
        <result property="sqdPlannedOutboundDate" column="sqd_planned_outbound_date"/>
        <result property="confirmInboundRequestFlag" column="confirm_inbound_request_flag"/>
        <result property="confirmOutboundRequestFlag" column="confirm_outbound_request_flag"/>
        <result property="sqdInboundHandler" column="sqd_inbound_handler"/>
        <result property="partialOutboundFlag" column="partial_outbound_flag"/>
        <result property="outboundRecordId" column="outbound_record_id"/>
        <result property="preOutboundRecordId" column="pre_outbound_record_id"/>
        <result property="actualInboundTime" column="actual_inbound_time"/>
        <result property="actualOutboundTime" column="actual_outbound_time"/>
        <result property="cargoDetailRows" column="cargo_detail_rows"/>
        <result property="includesUnloadingFee" column="includes_unloading_fee"/>
        <result property="includesInboundFee" column="includes_inbound_fee"/>
        <result property="includesPackingFee" column="includes_packing_fee"/>
        <result property="unpaidPackingFee" column="unpaid_packing_fee"/>
        <result property="unpaidInboundFee" column="unpaid_inbound_fee"/>
        <result property="inboundFee" column="inbound_fee"/>
        <result property="immediatePaymentFee" column="immediate_payment_fee"/>
        <result property="outboundType" column="outbound_type"/>
        <result property="rentalDays" column="rental_days"/>
        <result property="receivedUnloadingFee" column="received_unloading_fee"/>
        <result property="receivedPackingFee" column="received_packing_fee"/>
        <result property="receivedSupplier" column="received_supplier"/>
        <result property="contractType" column="contract_type"/>
        <result property="consigneeName" column="consignee_name"/>
        <result property="consigneeTel" column="consignee_tel"/>
        <result property="packageRecord"    column="package_record"    />
        <result property="repackingStatus"    column="repacking_status"    />
        <result property="repackedInto"    column="repacked_into"    />
        <result property="packageTo"    column="package_to"    />
        <result property="packageIntoNo"    column="package_into_no"    />
        <result property="purchaseNo"    column="purchase_no"    />
        <result property="createdBy"    column="created_by"    />
        <association property="midOutboundSettlement" resultMap="MidOutboundSettlementResult"/>
        <collection property="rsCargoDetailsList"
                    resultMap="com.rich.system.mapper.RsCargoDetailsMapper.RsCargoDetailsResult"/>
    </resultMap>

    <resultMap type="MidOutboundSettlement" id="MidOutboundSettlementResult">
        <result property="outboundRecordId"    column="outbound_record_id"    />
        <result property="InventoryId"    column="Inventory_id"    />
        <result property="settlementDate"    column="settlement_date"    />
        <result property="settlementInboundDate"    column="settlement_inbound_date"    />
        <result property="settlementRate"    column="settlement_rate"    />
    </resultMap>

    <sql id="selectRsInventoryVo">
        select ri.inventory_id,
               ri.inventory_status,
               ri.inbound_serial_no,
               ri.inbound_serial_split,
               ri.inbound_date,
               ri.outbound_no,
               ri.forwarder_no,
               ri.rental_settlement_date,
               ri.outbound_date,
               ri.client_code,
               ri.client_name,
               ri.sub_order_no,
               ri.supplier,
               ri.driver_info,
               ri.sqd_shipping_mark,
               ri.cargo_name,
               ri.total_boxes,
               ri.total_gross_weight,
               ri.total_volume,
               ri.damage_status,
               ri.storage_location1,
               ri.storage_location2,
               ri.storage_location3,
               ri.received_storage_fee,
               ri.unpaid_unloading_fee,
               ri.logistics_advance_fee,
               ri.rental_balance_fee,
               ri.free_stack_period,
               ri.overdue_rental_unit_price,
               ri.overdue_rental_fee,
               ri.notes,
               ri.outbound_type,
               ri.warehouse_code,
               ri.record_type,
               ri.inbound_type,
               ri.cargo_nature,
               ri.created_at,
               ri.pre_outbound_flag,
               ri.outbound_request_flag,
               ri.sqd_planned_outbound_date,
               ri.sqd_inbound_handler,
               ri.partial_outbound_flag,
               ri.actual_inbound_time,
               ri.actual_outbound_time,
               ri.cargo_detail_rows,
               ri.includes_unloading_fee,
               ri.includes_inbound_fee,
               ri.includes_packing_fee,
               ri.unpaid_packing_fee,
               ri.inbound_fee,
               ri.immediate_payment_fee,
               ri.unpaid_inbound_fee,
               ri.rental_days,
               ri.received_unloading_fee,
               ri.contract_type,
               ri.received_packing_fee,
               ri.received_supplier,
               ri.consignee_name,
               ri.consignee_tel,
               ri.package_record,
               ri.repacking_status,
               ri.repacked_into,
               ri.package_to,
               ri.purchase_no,
               ri.pre_outbound_record_id,
               ri2.sub_order_no as package_into_no,
               rcd.cargo_details_id,
               rcd.inventory_id,
               rcd.inbound_serial_no,
               rcd.inbound_serial_split,
               rcd.client_code,
               rcd.shipping_mark,
               rcd.item_name,
               rcd.item_en_name,
               rcd.box_count,
               rcd.unit_gross_weight,
               rcd.unit_length,
               rcd.unit_width,
               rcd.unit_height,
               rcd.unit_volume,
               rcd.damage_status,
               rcd.barcode,
               rcd.inventory_status,
               rcd.outbound_record_id,
               rcd.single_piece_weight,
               rcd.single_piece_volume,
               rcd.package_type,
               rcd.pre_outbound_flag,
               rcd.single_piece_weight,
               rcd.single_piece_volume,
               rcd.box_item_count,
               rcd.subtotal_item_count,
               rcd.express_date,
               rcd.express_no,
               rcd.additional_fee,
               rwc.sales_id
        from rs_inventory ri
                 left join rich.rs_cargo_details rcd on ri.inventory_id = rcd.inventory_id
                 left join rich.rs_warehouse_client rwc on rwc.client_code = ri.client_code
                 left join rs_inventory ri2 on ri2.inventory_id = ri.package_to
    </sql>

    <sql id="selectRsInventoryVoOne">
        select inventory_id,
               inventory_status,
               inbound_serial_no,
               client_name,
               inbound_serial_split,
               inbound_date,
               consignee_tel,
               consignee_name,
               outbound_no,
               forwarder_no,
               rental_settlement_date,
               outbound_date,
               client_code,
               sub_order_no,
               supplier,
               driver_info,
               sqd_shipping_mark,
               cargo_name,
               total_boxes,
               package_type,
               total_gross_weight,
               total_volume,
               damage_status,
               pre_outbound_record_id,
               storage_location1,
               storage_location2,
               storage_location3,
               received_storage_fee,
               unpaid_unloading_fee,
               logistics_advance_fee,
               rental_balance_fee,
               free_stack_period,
               overdue_rental_unit_price,
               overdue_rental_fee,
               notes,
               outbound_type,
               purchase_no,
               partial_outbound_flag
        from rs_inventory
    </sql>

    <select id="selectRsInventoryList" parameterType="RsInventory" resultMap="RsInventoryResult">
        <include refid="selectRsInventoryVo"/>
        <where>
            ri.delete_status = 0
            <if test="purchaseNo != null  and purchaseNo != ''">
                and ri.purchase_no = #{purchaseNo}
            </if>
            <if test="inventoryStatus != null  and inventoryStatus != ''">
                and ri.inventory_status = #{inventoryStatus}
            </if>
            <if test="packageTo != null  and packageTo != ''">
                and ri.package_to = #{packageTo}
            </if>
            <if test="driverInfo == null  or driverInfo == ''">
                and ri.package_to is null
            </if>
            <if test="inboundSerialNo != null  and inboundSerialNo != ''">
                and ri.inbound_serial_no = #{inboundSerialNo}
            </if>
            <if test="inboundSerialSplit != null  and inboundSerialSplit != ''">
                and ri.inbound_serial_split = #{inboundSerialSplit}
            </if>
            <if test="inboundDate != null ">
                and ri.inbound_date = #{inboundDate}
            </if>
            <if test="outboundNo != null  and outboundNo != ''">
                and ri.outbound_no = #{outboundNo}
            </if>
            <if test="forwarderNo != null  and forwarderNo != ''">
                and ri.forwarder_no = #{forwarderNo}
            </if>
            <if test="rentalSettlementDate != null ">
                and ri.rental_settlement_date = #{rentalSettlementDate}
            </if>
            <if test="outboundDate != null ">
                and ri.outbound_date = #{outboundDate}
            </if>
            <if test="clientCode != null  and clientCode != ''">
                and ri.client_code = #{clientCode}
            </if>
            <if test="subOrderNo != null  and subOrderNo != ''">
                and ri.sub_order_no = #{subOrderNo}
            </if>
            <if test="supplier != null  and supplier != ''">
                and ri.supplier = #{supplier}
            </if>
            <if test="driverInfo != null  and driverInfo != ''">
                and ri.driver_info = #{driverInfo}
            </if>
            <if test="sqdShippingMark != null  and sqdShippingMark != ''">
                and  find_in_set(#{sqdShippingMark},sqd_shipping_mark)
            </if>
            <if test="cargoName != null  and cargoName != ''">
                and cargo_name like concat('%', #{cargoName}, '%')
            </if>
            <if test="totalBoxes != null ">
                and total_boxes = #{totalBoxes}
            </if>
            <if test="packageType != null  and packageType != ''">
                and ri.package_type = #{packageType}
            </if>
            <if test="totalGrossWeight != null ">
                and total_gross_weight = #{totalGrossWeight}
            </if>
            <if test="totalVolume != null ">
                and total_volume = #{totalVolume}
            </if>
            <if test="damageStatus != null  and damageStatus != ''">
                and ri.damage_status = #{damageStatus}
            </if>
            <if test="storageLocation1 != null  and storageLocation1 != ''">
                and storage_location1 = #{storageLocation1}
            </if>
            <if test="storageLocation2 != null  and storageLocation2 != ''">
                and storage_location2 = #{storageLocation2}
            </if>
            <if test="storageLocation3 != null  and storageLocation3 != ''">
                and storage_location3 = #{storageLocation3}
            </if>
            <if test="receivedStorageFee != null ">
                and received_storage_fee = #{receivedStorageFee}
            </if>
            <if test="unpaidUnloadingFee != null ">
                and unpaid_unloading_fee = #{unpaidUnloadingFee}
            </if>
            <if test="logisticsAdvanceFee != null ">
                and logistics_advance_fee = #{logisticsAdvanceFee}
            </if>
            <if test="rentalBalanceFee != null ">
                and rental_balance_fee = #{rentalBalanceFee}
            </if>
            <if test="freeStackPeriod != null ">
                and free_stack_period = #{freeStackPeriod}
            </if>
            <if test="overdueRentalUnitPrice != null ">
                and overdue_rental_unit_price = #{overdueRentalUnitPrice}
            </if>
            <if test="overdueRentalFee != null ">
                and overdue_rental_fee = #{overdueRentalFee}
            </if>
            <if test="notes != null  and notes != ''">
                and notes = #{notes}
            </if>
            <if test="partialOutboundFlag != null  and partialOutboundFlag != ''">
                and partial_outbound_flag = #{partialOutboundFlag}
            </if>
            <if test="outboundRecordId != null  and outboundRecordId != ''">
                and outbound_record_id = #{outboundRecordId}
            </if>
            <if test="preOutboundFlag != null">and ri.pre_outbound_flag = #{preOutboundFlag}</if>
            <if test="permissionLevel != null and permissionLevel.length > 0 ">
                    and sales_id in
                    <foreach item="staffId" collection="permissionLevel" open="(" separator="," close=")">
                        #{staffId}
                    </foreach>
            </if>
            <if test="packageRecord != null  and packageRecord != ''"> and ri.package_record = #{packageRecord}</if>
            <if test="repackingStatus != null  and repackingStatus != ''"> and repacking_status = #{repackingStatus}</if>
            <if test="repackedInto != null  and repackedInto != ''"> and ri.repacked_into = #{repackedInto}</if>
            <if test="preOutboundRecordId != null  and preOutboundRecordId != ''"> and ri.pre_outbound_record_id = #{preOutboundRecordId}</if>
        </where>
        order by ri.actual_inbound_time desc
    </select>

    <select id="selectRsInventoryByInventoryId" parameterType="Long" resultMap="RsInventoryResult">
        <include refid="selectRsInventoryVo"/>
        where ri.inventory_id = #{inventoryId}
    </select>

    <insert id="insertRsInventory" parameterType="RsInventory" useGeneratedKeys="true" keyProperty="inventoryId">
        insert into rs_inventory
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="inventoryStatus != null">inventory_status,</if>
            <if test="inboundSerialNo != null">inbound_serial_no,</if>
            <if test="inboundSerialSplit != null">inbound_serial_split,</if>
            <if test="inboundDate != null">inbound_date,</if>
            <if test="outboundNo != null">outbound_no,</if>
            <if test="forwarderNo != null">forwarder_no,</if>
            <if test="rentalSettlementDate != null">rental_settlement_date,</if>
            <if test="outboundDate != null">outbound_date,</if>
            <if test="clientCode != null">client_code,</if>
            <if test="clientName != null">client_name,</if>
            <if test="subOrderNo != null">sub_order_no,</if>
            <if test="supplier != null">supplier,</if>
            <if test="driverInfo != null">driver_info,</if>
            <if test="sqdShippingMark != null">sqd_shipping_mark,</if>
            <if test="cargoName != null">cargo_name,</if>
            <if test="totalBoxes != null">total_boxes,</if>
            <if test="packageType != null">package_type,</if>
            <if test="totalGrossWeight != null">total_gross_weight,</if>
            <if test="totalVolume != null">total_volume,</if>
            <if test="damageStatus != null">damage_status,</if>
            <if test="storageLocation1 != null">storage_location1,</if>
            <if test="storageLocation2 != null">storage_location2,</if>
            <if test="storageLocation3 != null">storage_location3,</if>
            <if test="receivedStorageFee != null">received_storage_fee,</if>
            <if test="unpaidUnloadingFee != null">unpaid_unloading_fee,</if>
            <if test="logisticsAdvanceFee != null">logistics_advance_fee,</if>
            <if test="rentalBalanceFee != null">rental_balance_fee,</if>
            <if test="freeStackPeriod != null">free_stack_period,</if>
            <if test="overdueRentalUnitPrice != null">overdue_rental_unit_price,</if>
            <if test="overdueRentalFee != null">overdue_rental_fee,</if>
            <if test="notes != null">notes,</if>
            <if test="warehouseCode != null">warehouse_code,</if>
            <if test="recordType != null">record_type,</if>
            <if test="inboundType != null">inbound_type,</if>
            <if test="cargoNature != null">cargo_nature,</if>
            <if test="createdAt != null">created_at,</if>
            <if test="createdBy != null">created_by,</if>
            <if test="preOutboundFlag != null">pre_outbound_flag,</if>
            <if test="outboundRequestFlag != null">outbound_request_flag,</if>
            <if test="sqdPlannedOutboundDate != null">sqd_planned_outbound_date,</if>
            <if test="confirmInboundRequestFlag != null">confirm_inbound_request_flag,</if>
            <if test="confirmOutboundRequestFlag != null">confirm_outbound_request_flag,</if>
            <if test="sqdInboundHandler != null">sqd_inbound_handler,</if>
            <if test="partialOutboundFlag != null">partial_outbound_flag,</if>
            <if test="outboundRecordId != null">outbound_record_id,</if>
            <if test="actualInboundTime != null">actual_inbound_time,</if>
            <if test="actualOutboundTime != null">actual_outbound_time,</if>
            <if test="cargoDetailRows != null">cargo_detail_rows,</if>
            <if test="unpaidPackingFee != null">unpaid_packing_fee,</if>
            <if test="unpaidInboundFee != null">unpaid_inbound_fee,</if>
            <if test="inboundFee != null">inbound_fee,</if>
            <if test="immediatePaymentFee != null">immediate_payment_fee,</if>
            <if test="includesUnloadingFee != null">includes_unloading_fee,</if>
            <if test="includesInboundFee != null">includes_inbound_fee,</if>
            <if test="includesPackingFee != null">includes_packing_fee,</if>
            <if test="outboundType != null">outbound_type,</if>
            <if test="rentalDays != null">rental_days,</if>
            <if test="receivedUnloadingFee != null">received_unloading_fee,</if>
            <if test="receivedPackingFee != null">received_packing_fee,</if>
            <if test="contractType != null">contract_type,</if>
            <if test="receivedSupplier != null">received_supplier,</if>
            <if test="consigneeName != null">consignee_name,</if>
            <if test="consigneeTel != null">consignee_tel,</if>
            <if test="packageRecord != null">package_record,</if>
            <if test="repackingStatus != null">repacking_status,</if>
            <if test="repackedInto != null">repacked_into,</if>
            <if test="packageTo != null">package_to,</if>
            <if test="purchaseNo != null">purchase_no,</if>
            <if test="preOutboundRecordId != null">pre_outbound_record_id,</if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="inventoryStatus != null">#{inventoryStatus},</if>
            <if test="inboundSerialNo != null">#{inboundSerialNo},</if>
            <if test="inboundSerialSplit != null">#{inboundSerialSplit},</if>
            <if test="inboundDate != null">#{inboundDate},</if>
            <if test="outboundNo != null">#{outboundNo},</if>
            <if test="forwarderNo != null">#{forwarderNo},</if>
            <if test="rentalSettlementDate != null">#{rentalSettlementDate},</if>
            <if test="outboundDate != null">#{outboundDate},</if>
            <if test="clientCode != null">#{clientCode},</if>
            <if test="clientName != null">#{clientName},</if>
            <if test="subOrderNo != null">#{subOrderNo},</if>
            <if test="supplier != null">#{supplier},</if>
            <if test="driverInfo != null">#{driverInfo},</if>
            <if test="sqdShippingMark != null">#{sqdShippingMark},</if>
            <if test="cargoName != null">#{cargoName},</if>
            <if test="totalBoxes != null">#{totalBoxes},</if>
            <if test="packageType != null">#{packageType},</if>
            <if test="totalGrossWeight != null">#{totalGrossWeight},</if>
            <if test="totalVolume != null">#{totalVolume},</if>
            <if test="damageStatus != null">#{damageStatus},</if>
            <if test="storageLocation1 != null">#{storageLocation1},</if>
            <if test="storageLocation2 != null">#{storageLocation2},</if>
            <if test="storageLocation3 != null">#{storageLocation3},</if>
            <if test="receivedStorageFee != null">#{receivedStorageFee},</if>
            <if test="unpaidUnloadingFee != null">#{unpaidUnloadingFee},</if>
            <if test="logisticsAdvanceFee != null">#{logisticsAdvanceFee},</if>
            <if test="rentalBalanceFee != null">#{rentalBalanceFee},</if>
            <if test="freeStackPeriod != null">#{freeStackPeriod},</if>
            <if test="overdueRentalUnitPrice != null">#{overdueRentalUnitPrice},</if>
            <if test="overdueRentalFee != null">#{overdueRentalFee},</if>
            <if test="notes != null">#{notes},</if>
            <if test="warehouseCode != null">#{warehouseCode},</if>
            <if test="recordType != null">#{recordType},</if>
            <if test="inboundType != null">#{inboundType},</if>
            <if test="cargoNature != null">#{cargoNature},</if>
            <if test="createdAt != null">#{createdAt},</if>
            <if test="createdBy != null">#{createdBy},</if>
            <if test="preOutboundFlag != null">#{preOutboundFlag},</if>
            <if test="outboundRequestFlag != null">#{outboundRequestFlag},</if>
            <if test="sqdPlannedOutboundDate != null">#{sqdPlannedOutboundDate},</if>
            <if test="confirmInboundRequestFlag != null">#{confirmInboundRequestFlag},</if>
            <if test="confirmOutboundRequestFlag != null">#{confirmOutboundRequestFlag},</if>
            <if test="sqdInboundHandler != null">#{sqdInboundHandler},</if>
            <if test="partialOutboundFlag != null">#{partialOutboundFlag},</if>
            <if test="outboundRecordId != null">#{outboundRecordId},</if>
            <if test="actualInboundTime != null">#{actualInboundTime},</if>
            <if test="actualOutboundTime != null">#{actualOutboundTime},</if>
            <if test="cargoDetailRows != null">#{cargoDetailRows},</if>
            <if test="unpaidPackingFee != null">#{unpaidPackingFee},</if>
            <if test="unpaidInboundFee != null">#{unpaidInboundFee},</if>
            <if test="inboundFee != null">#{inboundFee},</if>
            <if test="immediatePaymentFee != null">#{immediatePaymentFee},</if>
            <if test="includesUnloadingFee != null">#{includesUnloadingFee},</if>
            <if test="includesInboundFee != null">#{includesInboundFee},</if>
            <if test="includesPackingFee != null">#{includesPackingFee},</if>
            <if test="outboundType != null">#{outboundType},</if>
            <if test="rentalDays != null">#{rentalDays},</if>
            <if test="receivedUnloadingFee != null">#{receivedUnloadingFee},</if>
            <if test="receivedPackingFee != null">#{receivedPackingFee},</if>
            <if test="contractType != null">#{contractType},</if>
            <if test="receivedSupplier != null">#{receivedSupplier},</if>
            <if test="consigneeName != null">#{consigneeName},</if>
            <if test="consigneeTel != null">#{consigneeTel},</if>
            <if test="packageRecord != null">#{packageRecord},</if>
            <if test="repackingStatus != null">#{repackingStatus},</if>
            <if test="repackedInto != null">#{repackedInto},</if>
            <if test="packageTo != null">#{packageTo},</if>
            <if test="purchaseNo != null">#{purchaseNo},</if>
            <if test="preOutboundRecordId != null">#{preOutboundRecordId},</if>
        </trim>
    </insert>

    <update id="updateRsInventory" parameterType="RsInventory">
        update rs_inventory
        <trim prefix="SET" suffixOverrides=",">
            <if test="inventoryStatus != null">inventory_status = #{inventoryStatus},</if>
            <if test="inboundSerialNo != null">inbound_serial_no = #{inboundSerialNo},</if>
            <if test="inboundSerialSplit != null">inbound_serial_split = #{inboundSerialSplit},</if>
            <if test="inboundDate != null">inbound_date = #{inboundDate},</if>
            <if test="outboundNo != null">outbound_no = #{outboundNo},</if>
            <if test="forwarderNo != null">forwarder_no = #{forwarderNo},</if>
            <if test="rentalSettlementDate != null">rental_settlement_date = #{rentalSettlementDate},</if>
            <if test="outboundDate != null">outbound_date = #{outboundDate},</if>
            <if test="clientCode != null">client_code = #{clientCode},</if>
            <if test="clientName != null">client_name = #{clientName},</if>
            <if test="subOrderNo != null">sub_order_no = #{subOrderNo},</if>
            <if test="supplier != null">supplier = #{supplier},</if>
            <if test="driverInfo != null">driver_info = #{driverInfo},</if>
            <if test="sqdShippingMark != null">sqd_shipping_mark = #{sqdShippingMark},</if>
            <if test="cargoName != null">cargo_name = #{cargoName},</if>
            <if test="totalBoxes != null">total_boxes = #{totalBoxes},</if>
            <if test="packageType != null">package_type = #{packageType},</if>
            <if test="totalGrossWeight != null">total_gross_weight = #{totalGrossWeight},</if>
            <if test="totalVolume != null">total_volume = #{totalVolume},</if>
            <if test="damageStatus != null">damage_status = #{damageStatus},</if>
            <if test="storageLocation1 != null">storage_location1 = #{storageLocation1},</if>
            <if test="storageLocation2 != null">storage_location2 = #{storageLocation2},</if>
            <if test="storageLocation3 != null">storage_location3 = #{storageLocation3},</if>
            <if test="receivedStorageFee != null">received_storage_fee = #{receivedStorageFee},</if>
            <if test="unpaidUnloadingFee != null">unpaid_unloading_fee = #{unpaidUnloadingFee},</if>
            <if test="logisticsAdvanceFee != null">logistics_advance_fee = #{logisticsAdvanceFee},</if>
            <if test="rentalBalanceFee != null">rental_balance_fee = #{rentalBalanceFee},</if>
            <if test="freeStackPeriod != null">free_stack_period = #{freeStackPeriod},</if>
            <if test="overdueRentalUnitPrice != null">overdue_rental_unit_price = #{overdueRentalUnitPrice},</if>
            <if test="overdueRentalFee != null">overdue_rental_fee = #{overdueRentalFee},</if>
            <if test="notes != null">notes = #{notes},</if>
            <if test="warehouseCode != null">warehouse_code = #{warehouseCode},</if>
            <if test="recordType != null">record_type = #{recordType},</if>
            <if test="inboundType != null">inbound_type = #{inboundType},</if>
            <if test="cargoNature != null">cargo_nature = #{cargoNature},</if>
            <if test="createdAt != null">created_at = #{createdAt},</if>
            <if test="preOutboundFlag != null">pre_outbound_flag = #{preOutboundFlag},</if>
            <if test="outboundRequestFlag != null">outbound_request_flag = #{outboundRequestFlag},</if>
            <if test="sqdPlannedOutboundDate != null">sqd_planned_outbound_date = #{sqdPlannedOutboundDate},</if>
            <if test="confirmInboundRequestFlag != null">confirm_inbound_request_flag = #{confirmInboundRequestFlag},</if>
            <if test="confirmOutboundRequestFlag != null">confirm_outbound_request_flag = #{confirmOutboundRequestFlag},</if>
            <if test="sqdInboundHandler != null">sqd_inbound_handler = #{sqdInboundHandler},</if>
            <if test="partialOutboundFlag != null">partial_outbound_flag = #{partialOutboundFlag},</if>
            <if test="outboundRecordId != null">outbound_record_id = #{outboundRecordId},</if>
            <if test="actualInboundTime != null">actual_inbound_time = #{actualInboundTime},</if>
            <if test="actualOutboundTime != null">actual_outbound_time = #{actualOutboundTime},</if>
            <if test="cargoDetailRows != null">cargo_detail_rows = #{cargoDetailRows},</if>
            <if test="unpaidPackingFee != null">unpaid_packing_fee = #{unpaidPackingFee},</if>
            <if test="unpaidInboundFee != null">unpaid_inbound_fee = #{unpaidInboundFee},</if>
            <if test="inboundFee != null">inbound_fee = #{inboundFee},</if>
            <if test="immediatePaymentFee != null">immediate_payment_fee = #{immediatePaymentFee},</if>
            <if test="includesUnloadingFee != null">includes_unloading_fee = #{includesUnloadingFee},</if>
            <if test="includesInboundFee != null">includes_inbound_fee = #{includesInboundFee},</if>
            <if test="includesPackingFee != null">includes_packing_fee = #{includesPackingFee},</if>
            <if test="outboundType != null">outbound_type = #{outboundType},</if>
            <if test="rentalDays != null">rental_days = #{rentalDays},</if>
            <if test="receivedUnloadingFee != null">received_unloading_fee = #{receivedUnloadingFee},</if>
            <if test="receivedPackingFee != null">received_packing_fee = #{receivedPackingFee},</if>
            <if test="contractType != null">contract_type = #{contractType},</if>
            <if test="receivedSupplier != null">received_supplier = #{receivedSupplier},</if>
            <if test="consigneeName != null">consignee_name = #{consigneeName},</if>
            <if test="consigneeTel != null">consignee_tel = #{consigneeTel},</if>
            <if test="packageRecord != null">package_record = #{packageRecord},</if>
            <if test="repackingStatus != null">repacking_status = #{repackingStatus},</if>
            <if test="repackedInto != null">repacked_into = #{repackedInto},</if>
            <if test="repackedInto == null">repacked_into = null,</if>
            <if test="packageTo != null">package_to = #{packageTo},</if>
            <if test="packageTo == null">package_to = null,</if>
            <if test="deleteStatus != null">delete_status = #{deleteStatus},</if>
            <if test="purchaseNo != null">purchase_no = #{purchaseNo},</if>
            <if test="preOutboundRecordId != null">pre_outbound_record_id = #{preOutboundRecordId},</if>
        </trim>
        where inventory_id = #{inventoryId}
    </update>

    <delete id="deleteRsInventoryByInventoryId" parameterType="Long">
        update rs_inventory
        set delete_status = '1'
        where inventory_id = #{inventoryId}
    </delete>

    <delete id="deleteRsInventoryByInventoryIds" parameterType="String">
        update rs_inventory set delete_status = '1' where inventory_id in
        <foreach item="inventoryId" collection="array" open="(" separator="," close=")">
            #{inventoryId}
        </foreach>
    </delete>

    <select id="getInboundNo" resultType="int">
        select count(*)
        from rich.rs_inventory
        where rs_inventory.inbound_serial_no like concat(
                '',
                'RS.',
                RIGHT(YEAR(CURRENT_DATE), 2),
                LPAD(MONTH(CURRENT_DATE), 2, '0'),
                '%'
                                 ) and  inbound_serial_no is not null
    </select>

    <select id="getPackages" resultMap="RsInventoryResult">
        <include refid="selectRsInventoryVoOne"/>
        <where>
            <if test="clientCode != null  and clientCode != ''">
                and client_code = #{clientCode}
            </if>
            and package_record = '1' and repacking_status != '打包完'
        </where>
    </select>
    
    <insert id="batchInsertInventory" parameterType="java.util.List" useGeneratedKeys="true" keyProperty="inventoryId">
        insert into rs_inventory
        (inventory_status, inbound_serial_no, inbound_serial_split, inbound_date, outbound_no, 
        forwarder_no, rental_settlement_date, outbound_date, client_code, client_name, 
        sub_order_no, supplier, driver_info, sqd_shipping_mark, cargo_name, 
        total_boxes, package_type, total_gross_weight, total_volume, damage_status, 
        storage_location1, storage_location2, storage_location3, received_storage_fee, 
        unpaid_unloading_fee, logistics_advance_fee, rental_balance_fee, free_stack_period, 
        overdue_rental_unit_price, overdue_rental_fee, notes, outbound_type, warehouse_code, 
        record_type, inbound_type, cargo_nature, actual_inbound_time, consignee_name, 
        consignee_tel, package_to,   repacking_status)
        values
        <foreach collection="list" item="item" separator=",">
            (#{item.inventoryStatus}, #{item.inboundSerialNo}, #{item.inboundSerialSplit}, #{item.inboundDate}, #{item.outboundNo}, 
            #{item.forwarderNo}, #{item.rentalSettlementDate}, #{item.outboundDate}, #{item.clientCode}, #{item.clientName}, 
            #{item.subOrderNo}, #{item.supplier}, #{item.driverInfo}, #{item.sqdShippingMark}, #{item.cargoName}, 
            #{item.totalBoxes}, #{item.packageType}, #{item.totalGrossWeight}, #{item.totalVolume}, #{item.damageStatus}, 
            #{item.storageLocation1}, #{item.storageLocation2}, #{item.storageLocation3}, #{item.receivedStorageFee}, 
            #{item.unpaidUnloadingFee}, #{item.logisticsAdvanceFee}, #{item.rentalBalanceFee}, #{item.freeStackPeriod}, 
            #{item.overdueRentalUnitPrice}, #{item.overdueRentalFee}, #{item.notes}, #{item.outboundType}, #{item.warehouseCode}, 
            #{item.recordType}, #{item.inboundType}, #{item.cargoNature}, #{item.actualInboundTime}, #{item.consigneeName}, 
            #{item.consigneeTel}, #{item.packageTo},   #{item.repackingStatus})
        </foreach>
    </insert>

    <update id="updateRsInventoryByPackageTo">
        update rs_inventory
        set inventory_status=1
        where package_to = #{packageTo}
    </update>
</mapper>