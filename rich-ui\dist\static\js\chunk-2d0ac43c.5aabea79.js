(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d0ac43c"],{1997:function(e,t,r){"use strict";r.r(t);var a=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",{staticClass:"app-container"},[r("el-row",{attrs:{gutter:20}},[r("el-col",{attrs:{span:e.showLeft}},[r("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],ref:"queryForm",staticClass:"query",attrs:{inline:!0,model:e.queryParams,"label-width":"68px",size:"mini"}},[r("el-form-item",{attrs:{label:"箱型类型名缩写",prop:"ctnrTypeShortName"}},[r("el-input",{attrs:{clearable:"",placeholder:"箱型类型名缩写"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.ctnrTypeShortName,callback:function(t){e.$set(e.queryParams,"ctnrTypeShortName",t)},expression:"queryParams.ctnrTypeShortName"}})],1),r("el-form-item",{attrs:{label:"箱型类型中文名",prop:"ctnrTypeLocalName"}},[r("el-input",{attrs:{clearable:"",placeholder:"箱型类型中文名"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.ctnrTypeLocalName,callback:function(t){e.$set(e.queryParams,"ctnrTypeLocalName",t)},expression:"queryParams.ctnrTypeLocalName"}})],1),r("el-form-item",{attrs:{label:"箱型类型英文名",prop:"ctnrTypeEnName"}},[r("el-input",{attrs:{clearable:"",placeholder:"箱型类型英文名"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.ctnrTypeEnName,callback:function(t){e.$set(e.queryParams,"ctnrTypeEnName",t)},expression:"queryParams.ctnrTypeEnName"}})],1),r("el-form-item",{attrs:{label:"箱型类型等级",prop:"ctnrTypeLevel"}},[r("el-input",{attrs:{clearable:"",placeholder:"箱型类型等级"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.ctnrTypeLevel,callback:function(t){e.$set(e.queryParams,"ctnrTypeLevel",t)},expression:"queryParams.ctnrTypeLevel"}})],1),r("el-form-item",{attrs:{label:"是否上锁",prop:"isLocked"}},[r("el-input",{attrs:{clearable:"",placeholder:"是否上锁"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.isLocked,callback:function(t){e.$set(e.queryParams,"isLocked",t)},expression:"queryParams.isLocked"}})],1),r("el-form-item",{attrs:{label:"上下层排序",prop:"verticalSort"}},[r("el-input",{attrs:{clearable:"",placeholder:"上下层排序"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.verticalSort,callback:function(t){e.$set(e.queryParams,"verticalSort",t)},expression:"queryParams.verticalSort"}})],1),r("el-form-item",{attrs:{label:"排序",prop:"orderNum"}},[r("el-input",{attrs:{clearable:"",placeholder:"排序"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.orderNum,callback:function(t){e.$set(e.queryParams,"orderNum",t)},expression:"queryParams.orderNum"}})],1),r("el-form-item",{attrs:{label:"删除时间",prop:"deleteTime"}},[r("el-date-picker",{attrs:{clearable:"",placeholder:"删除时间",type:"date","value-format":"yyyy-MM-dd"},model:{value:e.queryParams.deleteTime,callback:function(t){e.$set(e.queryParams,"deleteTime",t)},expression:"queryParams.deleteTime"}})],1),r("el-form-item",{attrs:{label:"${comment}",prop:"deleteBy"}},[r("el-input",{attrs:{clearable:"",placeholder:"${comment}"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.deleteBy,callback:function(t){e.$set(e.queryParams,"deleteBy",t)},expression:"queryParams.deleteBy"}})],1),r("el-form-item",[r("el-button",{attrs:{icon:"el-icon-search",size:"mini",type:"primary"},on:{click:e.handleQuery}},[e._v("搜索")]),r("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1)],1),r("el-col",{attrs:{span:e.showRight}},[r("el-row",{staticClass:"mb8",attrs:{gutter:10}},[r("el-col",{attrs:{span:1.5}},[r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:ctnrtype:add"],expression:"['system:ctnrtype:add']"}],attrs:{icon:"el-icon-plus",plain:"",size:"mini",type:"primary"},on:{click:e.handleAdd}},[e._v("新增 ")])],1),r("el-col",{attrs:{span:1.5}},[r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:ctnrtype:edit"],expression:"['system:ctnrtype:edit']"}],attrs:{disabled:e.single,icon:"el-icon-edit",plain:"",size:"mini",type:"success"},on:{click:e.handleUpdate}},[e._v("修改 ")])],1),r("el-col",{attrs:{span:1.5}},[r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:ctnrtype:remove"],expression:"['system:ctnrtype:remove']"}],attrs:{disabled:e.multiple,icon:"el-icon-delete",plain:"",size:"mini",type:"danger"},on:{click:e.handleDelete}},[e._v("删除 ")])],1),r("el-col",{attrs:{span:1.5}},[r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:ctnrtype:export"],expression:"['system:ctnrtype:export']"}],attrs:{icon:"el-icon-download",plain:"",size:"mini",type:"warning"},on:{click:e.handleExport}},[e._v("导出 ")])],1),r("right-toolbar",{attrs:{showSearch:e.showSearch},on:{"update:showSearch":function(t){e.showSearch=t},"update:show-search":function(t){e.showSearch=t},queryTable:e.getList}})],1),r("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.ctnrtypeList},on:{"selection-change":e.handleSelectionChange}},[r("el-table-column",{attrs:{align:"center",type:"selection",width:"28"}}),r("el-table-column",{attrs:{align:"center",label:"ID",prop:"ctnrTypeId"}}),r("el-table-column",{attrs:{align:"center",label:"箱型类型名缩写",prop:"ctnrTypeShortName"}}),r("el-table-column",{attrs:{align:"center",label:"箱型类型中文名",prop:"ctnrTypeLocalName"}}),r("el-table-column",{attrs:{align:"center",label:"箱型类型英文名",prop:"ctnrTypeEnName"}}),r("el-table-column",{attrs:{align:"center",label:"箱型类型等级",prop:"ctnrTypeLevel"}}),r("el-table-column",{attrs:{align:"center",label:"${comment}",prop:"featureType"}}),r("el-table-column",{attrs:{align:"center",label:"是否上锁",prop:"isLocked"}}),r("el-table-column",{attrs:{align:"center",label:"上下层排序",prop:"verticalSort"}}),r("el-table-column",{attrs:{align:"center",label:"排序",prop:"orderNum"}}),r("el-table-column",{attrs:{align:"center",label:"状态",prop:"status"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("el-switch",{attrs:{"active-value":"0","inactive-value":"1"},on:{change:function(r){return e.handleStatusChange(t.row)}},model:{value:t.row.status,callback:function(r){e.$set(t.row,"status",r)},expression:"scope.row.status"}})]}}])}),r("el-table-column",{attrs:{align:"center",label:"备注",prop:"remark"}}),r("el-table-column",{attrs:{align:"center",label:"删除时间",prop:"deleteTime",width:"180"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("span",[e._v(e._s(e.parseTime(t.row.deleteTime,"{y}-{m}-{d}")))])]}}])}),r("el-table-column",{attrs:{align:"center",label:"数据状态",prop:"deleteStatus"}}),r("el-table-column",{attrs:{align:"center",label:"${comment}",prop:"deleteBy"}}),r("el-table-column",{attrs:{align:"center","class-name":"small-padding fixed-width",label:"操作"},scopedSlots:e._u([{key:"default",fn:function(t){return[r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:ctnrtype:edit"],expression:"['system:ctnrtype:edit']"}],staticStyle:{"margin-right":"-8px"},attrs:{icon:"el-icon-edit",size:"mini",type:"success"},on:{click:function(r){return e.handleUpdate(t.row)}}},[e._v("修改 ")]),r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:ctnrtype:remove"],expression:"['system:ctnrtype:remove']"}],staticStyle:{"margin-right":"-8px"},attrs:{icon:"el-icon-delete",size:"mini",type:"danger"},on:{click:function(r){return e.handleDelete(t.row)}}},[e._v("删除 ")])]}}])})],1),r("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total>0"}],attrs:{limit:e.queryParams.pageSize,page:e.queryParams.pageNum,total:e.total},on:{"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},pagination:e.getList}})],1)],1),r("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"},{name:"dialogDragWidth",rawName:"v-dialogDragWidth"}],attrs:{"close-on-click-modal":!1,"modal-append-to-body":!1,title:e.title,visible:e.open,"append-to-body":"",width:"500px"},on:{"update:visible":function(t){e.open=t}}},[r("el-form",{ref:"form",attrs:{model:e.form,rules:e.rules,"label-width":"80px"}},[r("el-form-item",{attrs:{label:"箱型代码",prop:"ctnrTypeShortName"}},[r("el-input",{attrs:{placeholder:"箱型代码"},model:{value:e.form.ctnrTypeCode,callback:function(t){e.$set(e.form,"ctnrTypeCode",t)},expression:"form.ctnrTypeCode"}})],1),r("el-form-item",{attrs:{label:"箱型简称",prop:"ctnrTypeShortName"}},[r("el-input",{attrs:{placeholder:"箱型类型名缩写"},model:{value:e.form.ctnrTypeShortName,callback:function(t){e.$set(e.form,"ctnrTypeShortName",t)},expression:"form.ctnrTypeShortName"}})],1),r("el-form-item",{attrs:{label:"箱型全程",prop:"ctnrTypeLocalName"}},[r("el-input",{attrs:{placeholder:"箱型类型中文名"},model:{value:e.form.ctnrTypeLocalName,callback:function(t){e.$set(e.form,"ctnrTypeLocalName",t)},expression:"form.ctnrTypeLocalName"}})],1),r("el-form-item",{attrs:{label:"箱型英文名",prop:"ctnrTypeEnName"}},[r("el-input",{attrs:{placeholder:"箱型类型英文名"},model:{value:e.form.ctnrTypeEnName,callback:function(t){e.$set(e.form,"ctnrTypeEnName",t)},expression:"form.ctnrTypeEnName"}})],1),r("el-form-item",{attrs:{label:"箱型等级",prop:"ctnrTypeLevel"}},[r("el-input",{attrs:{placeholder:"箱型类型等级"},model:{value:e.form.ctnrTypeLevel,callback:function(t){e.$set(e.form,"ctnrTypeLevel",t)},expression:"form.ctnrTypeLevel"}})],1),r("el-form-item",{attrs:{label:"是否上锁",prop:"isLocked"}},[r("el-input",{attrs:{placeholder:"是否上锁"},model:{value:e.form.isLocked,callback:function(t){e.$set(e.form,"isLocked",t)},expression:"form.isLocked"}})],1),r("el-form-item",{attrs:{label:"上下层排序",prop:"verticalSort"}},[r("el-input",{attrs:{placeholder:"上下层排序"},model:{value:e.form.verticalSort,callback:function(t){e.$set(e.form,"verticalSort",t)},expression:"form.verticalSort"}})],1),r("el-form-item",{attrs:{label:"排序",prop:"orderNum"}},[r("el-input",{attrs:{placeholder:"排序"},model:{value:e.form.orderNum,callback:function(t){e.$set(e.form,"orderNum",t)},expression:"form.orderNum"}})],1),r("el-form-item",{attrs:{label:"备注",prop:"remark"}},[r("el-input",{attrs:{placeholder:"备注"},model:{value:e.form.remark,callback:function(t){e.$set(e.form,"remark",t)},expression:"form.remark"}})],1)],1),r("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[r("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("确 定")]),r("el-button",{on:{click:e.cancel}},[e._v("取 消")])],1)],1)],1)},l=[],n=r("5530"),o=(r("d81d"),r("0c8e")),s={name:"Ctnrtype",data:function(){return{showLeft:0,showRight:24,loading:!0,ids:[],single:!0,multiple:!0,showSearch:!0,total:0,ctnrtypeList:[],title:"",open:!1,queryParams:{pageNum:1,pageSize:20,ctnrTypeShortName:null,ctnrTypeLocalName:null,ctnrTypeEnName:null,ctnrTypeLevel:null,featureType:null,isLocked:null,verticalSort:null,orderNum:null,status:null,deleteTime:null,deleteStatus:null,deleteBy:null},form:{},rules:{}}},watch:{showSearch:function(e){!0===e?(this.showRight=21,this.showLeft=3):(this.showRight=24,this.showLeft=0)}},created:function(){this.getList()},methods:{getList:function(){var e=this;this.loading=!0,Object(o["e"])(this.queryParams).then((function(t){e.ctnrtypeList=t.rows,e.total=t.total,e.loading=!1}))},cancel:function(){this.open=!1,this.reset()},reset:function(){this.form={ctnrTypeId:null,ctnrTypeShortName:null,ctnrTypeLocalName:null,ctnrTypeEnName:null,ctnrTypeLevel:null,featureType:null,isLocked:null,verticalSort:null,orderNum:null,status:"0",remark:null,createTime:null,updateTime:null,deleteTime:null,deleteStatus:"0",deleteBy:null,updateBy:null,createBy:null},this.resetForm("form")},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.resetForm("queryForm"),this.handleQuery()},handleStatusChange:function(e){var t=this,r="0"===e.status?"启用":"停用";this.$confirm('确认要"'+r+"吗？","提示",{customClass:"modal-confirm"}).then((function(){return Object(o["b"])(e.ctnrTypeId,e.status)})).then((function(){t.$modal.msgSuccess(r+"成功")})).catch((function(){e.status="0"===e.status?"1":"0"}))},handleSelectionChange:function(e){this.ids=e.map((function(e){return e.ctnrTypeId})),this.single=1!==e.length,this.multiple=!e.length},handleAdd:function(){this.reset(),this.open=!0,this.title="添加箱型特征"},handleUpdate:function(e){var t=this;this.reset();var r=e.ctnrTypeId||this.ids;Object(o["d"])(r).then((function(e){t.form=e.data,t.open=!0,t.title="修改箱型特征"}))},submitForm:function(){var e=this;this.$refs["form"].validate((function(t){t&&(null!=e.form.ctnrTypeId?Object(o["g"])(e.form).then((function(t){e.$modal.msgSuccess("修改成功"),e.open=!1,e.getList()})):Object(o["a"])(e.form).then((function(t){e.$modal.msgSuccess("新增成功"),e.open=!1,e.getList()})))}))},handleDelete:function(e){var t=this,r=e.ctnrTypeId||this.ids;this.$confirm('是否确认删除箱型特征编号为"'+r+'"的数据项？',"提示",{customClass:"modal-confirm"}).then((function(){return Object(o["c"])(r)})).then((function(){t.getList(),t.$modal.msgSuccess("删除成功")})).catch((function(){}))},handleExport:function(){this.download("system/ctnrtype/export",Object(n["a"])({},this.queryParams),"ctnrtype_".concat((new Date).getTime(),".xlsx"))}}},i=s,c=r("2877"),u=Object(c["a"])(i,a,l,!1,null,null,null);t["default"]=u.exports}}]);