(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d0e24da"],{"7db9":function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:e.showLeft}},[a("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],ref:"queryForm",staticClass:"query",attrs:{inline:!0,model:e.queryParams,size:"mini"}},[a("el-form-item",{attrs:{label:"搜索",prop:"chargeQuery"}},[a("el-input",{staticStyle:{width:"100%"},attrs:{clearable:"",placeholder:"中英文名"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.chargeQuery,callback:function(t){e.$set(e.queryParams,"chargeQuery",t)},expression:"queryParams.chargeQuery"}})],1),a("el-form-item",{attrs:{label:"类型",prop:"chargeTypeId"}},[a("tree-select",{staticStyle:{width:"100%"},attrs:{pass:e.queryParams.chargeTypeId,placeholder:"费用类型",type:"chargeType",dbn:!1}})],1),a("el-form-item",[a("el-button",{attrs:{icon:"el-icon-search",size:"mini",type:"primary"},on:{click:e.handleQuery}},[e._v("搜索")]),a("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1)],1),a("el-col",{attrs:{span:e.showRight}},[a("el-row",{staticClass:"mb8",attrs:{gutter:10}},[a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:charge:add"],expression:"['system:charge:add']"}],attrs:{icon:"el-icon-plus",plain:"",size:"mini",type:"primary"},on:{click:e.handleAdd}},[e._v("新增 ")])],1),a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:charge:remove"],expression:"['system:charge:remove']"}],attrs:{disabled:e.multiple,icon:"el-icon-delete",plain:"",size:"mini",type:"danger"},on:{click:e.handleDelete}},[e._v("删除 ")])],1),a("right-toolbar",{attrs:{showSearch:e.showSearch},on:{"update:showSearch":function(t){e.showSearch=t},"update:show-search":function(t){e.showSearch=t},queryTable:e.getList}})],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.chargeList},on:{"selection-change":e.handleSelectionChange}},[a("el-table-column",{attrs:{type:"selection",width:"28",align:"center"}}),a("el-table-column",{key:"chargeTypeName",attrs:{align:"left",label:"费用类别",prop:"chargeTypeName",width:"170"}}),a("el-table-column",{key:"chargeName",attrs:{align:"left",label:"费用名称",width:"200"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(t.row.chargeShortName)+" "),a("a",{staticStyle:{margin:"0","font-weight":"bold","font-size":"small"}},[e._v(e._s(t.row.chargeLocalName))]),e._v(" "+e._s(t.row.chargeEnName)+" ")]}}])}),a("el-table-column",{key:"units",attrs:{align:"center",label:"计费单位",prop:"units",width:"68"}}),a("el-table-column",{key:"currencies",attrs:{align:"center",label:"币种",prop:"currencyCode",width:"68"}}),a("el-table-column",{key:"remark",attrs:{align:"center",label:"备注",prop:"remark"}}),a("el-table-column",{key:"orderNum",attrs:{align:"center",label:"排序",prop:"orderNum",width:"48"}}),a("el-table-column",{key:"status",attrs:{align:"center",label:"状态",prop:"status",width:"68"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-switch",{attrs:{"active-value":"0","inactive-value":"1"},on:{change:function(a){return e.handleStatusChange(t.row)}},model:{value:t.row.status,callback:function(a){e.$set(t.row,"status",a)},expression:"scope.row.status"}})]}}])}),a("el-table-column",{attrs:{align:"center","class-name":"small-padding fixed-width",label:"操作",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:charge:edit"],expression:"['system:charge:edit']"}],staticStyle:{"margin-right":"-8px"},attrs:{icon:"el-icon-edit",size:"mini",type:"success"},on:{click:function(a){return e.handleUpdate(t.row)}}},[e._v("修改 ")]),a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:charge:remove"],expression:"['system:charge:remove']"}],staticStyle:{"margin-right":"-8px"},attrs:{icon:"el-icon-delete",size:"mini",type:"danger"},on:{click:function(a){return e.handleDelete(t.row)}}},[e._v("删除 ")])]}}])})],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total>0"}],attrs:{limit:e.queryParams.pageSize,page:e.queryParams.pageNum,total:e.total},on:{"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},pagination:e.getList}})],1)],1),a("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"},{name:"dialogDragWidth",rawName:"v-dialogDragWidth"}],attrs:{"close-on-click-modal":!1,"modal-append-to-body":!1,title:e.title,visible:e.open,"append-to-body":"",width:"500px"},on:{"update:visible":function(t){e.open=t}}},[a("el-form",{ref:"form",staticClass:"edit",attrs:{model:e.form,rules:e.rules,"label-width":"80px"}},[a("el-form-item",{attrs:{label:"费用类别",prop:"chargeTypeId"}},[a("tree-select",{attrs:{dbn:!0,multiple:!1,pass:e.form.chargeTypeId,type:"chargeType"},on:{return:e.getChargeTypeId}})],1),a("el-form-item",{attrs:{label:"简称",prop:"chargeShortName"}},[a("el-input",{attrs:{placeholder:"已有通用英文简称的直接用英文，中文习惯用中文，方便录入为准"},model:{value:e.form.chargeShortName,callback:function(t){e.$set(e.form,"chargeShortName",t)},expression:"form.chargeShortName"}})],1),a("el-form-item",{attrs:{label:"中文",prop:"chargeLocalName"}},[a("el-input",{attrs:{placeholder:"不一定写全称，内行能看懂就用英文或中文简称"},model:{value:e.form.chargeLocalName,callback:function(t){e.$set(e.form,"chargeLocalName",t)},expression:"form.chargeLocalName"}})],1),a("el-form-item",{attrs:{label:"英文",prop:"chargeEnName"}},[a("el-input",{attrs:{placeholder:"不一定写全称，内行能看懂就用英文简称"},model:{value:e.form.chargeEnName,callback:function(t){e.$set(e.form,"chargeEnName",t)},expression:"form.chargeEnName"}})],1),a("el-form-item",{attrs:{label:"币种",prop:"currencyIds"}},[a("tree-select",{attrs:{multiple:!1,pass:e.form.currencyCode,placeholder:"默认的币种列表，多选",type:"currency"},on:{return:e.getCurrencyIds}})],1),a("el-form-item",{attrs:{label:"计费单位",prop:"unitIds"}},[a("tree-select",{attrs:{multiple:!1,pass:e.form.unitCode,placeholder:"默认的计费单位列表，多选",type:"unit"},on:{return:e.getUnitIds}})],1),a("el-form-item",{attrs:{label:"排序",prop:"orderNum"}},[a("el-input-number",{staticStyle:{width:"100%"},attrs:{controls:!1,placeholder:"排序"},model:{value:e.form.orderNum,callback:function(t){e.$set(e.form,"orderNum",t)},expression:"form.orderNum"}})],1),a("el-form-item",{attrs:{label:"备注",prop:"remark"}},[a("el-input",{attrs:{autosize:{minRows:5,maxRows:20},maxlength:"150",placeholder:"备注","show-word-limit":"",type:"textarea"},model:{value:e.form.remark,callback:function(t){e.$set(e.form,"remark",t)},expression:"form.remark"}})],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("确 定")]),a("el-button",{on:{click:e.cancel}},[e._v("取 消")])],1)],1)],1)},n=[],s=a("5530"),l=(a("d81d"),a("0a37")),o={name:"Charge",data:function(){return{showLeft:3,showRight:21,loading:!0,ids:[],single:!0,multiple:!0,showSearch:!1,total:0,chargeList:[],title:"",open:!1,queryParams:{pageNum:1,pageSize:20,chargeQuery:null,chargeTypeId:null},form:{},rules:{}}},watch:{showSearch:function(e){1==e?(this.showRight=21,this.showLeft=3):(this.showRight=24,this.showLeft=0)}},created:function(){this.getList()},methods:{getList:function(){var e=this;this.loading=!0,Object(l["e"])(this.queryParams).then((function(t){e.chargeList=t.rows,e.total=t.total,e.loading=!1}))},cancel:function(){this.open=!1,this.reset()},reset:function(){this.form={chargeId:null,chargeShortName:null,chargeEnName:null,chargeLocalName:null,currencyCodeList:null,chargeUnitCodeList:null,chargeTypeId:null,orderNum:null,remark:null},this.resetForm("form")},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.resetForm("queryForm"),this.handleQuery()},handleSelectionChange:function(e){this.ids=e.map((function(e){return e.chargeId})),this.single=1!=e.length,this.multiple=!e.length},handleAdd:function(){this.reset(),this.open=!0,this.title="添加费用"},handleUpdate:function(e){var t=this;this.reset();var a=e.chargeId||this.ids;Object(l["d"])(a).then((function(e){t.form=e.data,t.open=!0,t.title="修改费用"}))},submitForm:function(){var e=this;this.$refs["form"].validate((function(t){t&&(null!=e.form.chargeId?Object(l["g"])(e.form).then((function(t){e.$modal.msgSuccess("修改成功"),e.open=!1,e.getList()})):Object(l["a"])(e.form).then((function(t){e.$modal.msgSuccess("新增成功"),e.open=!1,e.getList()})))}))},handleDelete:function(e){var t=this,a=e.chargeId||this.ids;this.$confirm('是否确认删除费用编号为"'+a+'"的数据项？',"提示",{customClass:"modal-confirm"}).then((function(){return Object(l["c"])(a)})).then((function(){t.getList(),t.$modal.msgSuccess("删除成功")})).catch((function(){}))},handleStatusChange:function(e){var t=this,a="0"==e.status?"启用":"停用";this.$confirm('确认要"'+a+'""'+e.chargeLocalName+'"吗？',"提示",{customClass:"modal-confirm"}).then((function(){return Object(l["b"])(e.chargeId,e.status)})).then((function(){t.$modal.msgSuccess(a+"成功")})).catch((function(){e.status="0"==e.status?"1":"0"}))},handleExport:function(){this.download("system/charge/export",Object(s["a"])({},this.queryParams),"charge_".concat((new Date).getTime(),".xlsx"))},getCurrencyIds:function(e){this.form.currencyCode=e},getUnitIds:function(e){this.form.unitCode=e},getChargeTypeId:function(e){this.form.chargeTypeId=e}}},i=o,c=a("2877"),u=Object(c["a"])(i,r,n,!1,null,null,null);t["default"]=u.exports}}]);