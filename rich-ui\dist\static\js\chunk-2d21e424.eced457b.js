(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d21e424"],{d596:function(e,t,a){"use strict";a.r(t);var l=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:e.showLeft}},[a("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],ref:"queryForm",staticClass:"query",attrs:{model:e.queryParams,size:"mini",inline:!0,"label-width":"68px"}},[a("el-form-item",{attrs:{label:"原币种",prop:"overseaCurrency"}},[a("el-input",{attrs:{placeholder:"原币种",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.overseaCurrency,callback:function(t){e.$set(e.queryParams,"overseaCurrency",t)},expression:"queryParams.overseaCurrency"}})],1),a("el-form-item",{attrs:{label:"本位币",prop:"localCurrency"}},[a("el-input",{attrs:{placeholder:"本位币",clearable:""},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.localCurrency,callback:function(t){e.$set(e.queryParams,"localCurrency",t)},expression:"queryParams.localCurrency"}})],1),a("el-form-item",[a("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"mini"},on:{click:e.handleQuery}},[e._v("搜索")]),a("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1)],1),a("el-col",{attrs:{span:e.showRight}},[a("el-row",{staticClass:"mb8",attrs:{gutter:10}},[a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:exchangerate:add"],expression:"['system:exchangerate:add']"}],attrs:{type:"primary",plain:"",icon:"el-icon-plus",size:"mini"},on:{click:e.handleAdd}},[e._v("新增 ")])],1),a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:exchangerate:edit"],expression:"['system:exchangerate:edit']"}],attrs:{type:"success",plain:"",icon:"el-icon-edit",size:"mini",disabled:e.single},on:{click:e.handleUpdate}},[e._v("修改 ")])],1),a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:exchangerate:remove"],expression:"['system:exchangerate:remove']"}],attrs:{type:"danger",plain:"",icon:"el-icon-delete",size:"mini",disabled:e.multiple},on:{click:e.handleDelete}},[e._v("删除 ")])],1),a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:exchangerate:export"],expression:"['system:exchangerate:export']"}],attrs:{type:"warning",plain:"",icon:"el-icon-download",size:"mini"},on:{click:e.handleExport}},[e._v("导出 ")])],1),a("right-toolbar",{attrs:{showSearch:e.showSearch},on:{"update:showSearch":function(t){e.showSearch=t},"update:show-search":function(t){e.showSearch=t},queryTable:e.getList}})],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.exchangerateList},on:{"selection-change":e.handleSelectionChange}},[a("el-table-column",{attrs:{type:"selection",width:"28",align:"center"}}),a("el-table-column",{attrs:{label:"原币种",align:"center",prop:"currency",width:"68"}}),a("el-table-column",{attrs:{label:"本位币",align:"center",prop:"basicCurrency",width:"68"}}),a("el-table-column",{attrs:{label:"基数",align:"center",prop:"base",width:"100"}}),a("el-table-column",{attrs:{align:"center",label:"买入价",prop:"buyRate",width:"100"}}),a("el-table-column",{attrs:{align:"center",label:"结算价",prop:"settleRate",width:"100"}}),a("el-table-column",{attrs:{align:"center",label:"卖出价",prop:"sellRate",width:"100"}}),a("el-table-column",{attrs:{label:"有效期",align:"center",width:"125"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("h6",{staticStyle:{margin:"0"}},[e._v(e._s(e.parseTime(t.row.validFrom,"{y}.{m}.{d}"))+"-"+e._s(e.parseTime(t.row.validTo,"{y}.{m}.{d}")))])]}}])}),a("el-table-column",{attrs:{label:"备注",align:"center",prop:"remark"}}),a("el-table-column",{attrs:{label:"录入人",align:"center",prop:"createBy",width:"68"}}),a("el-table-column",{attrs:{label:"录入时间",align:"center",prop:"createTime",width:"125"}}),a("el-table-column",{attrs:{label:"状态",align:"center",prop:"status",width:"58"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-switch",{attrs:{"active-value":"0","inactive-value":"1"},on:{change:function(a){return e.handleStatusChange(t.row)}},model:{value:t.row.status,callback:function(a){e.$set(t.row,"status",a)},expression:"scope.row.status"}})]}}])}),a("el-table-column",{attrs:{label:"操作",align:"center","class-name":"small-padding fixed-width",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:exchangerate:edit"],expression:"['system:exchangerate:edit']"}],staticStyle:{"margin-right":"-8px"},attrs:{size:"mini",type:"success",icon:"el-icon-edit"},on:{click:function(a){return e.handleUpdate(t.row)}}},[e._v("修改 ")]),a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:exchangerate:remove"],expression:"['system:exchangerate:remove']"}],staticStyle:{"margin-right":"-8px"},attrs:{size:"mini",type:"danger",icon:"el-icon-delete"},on:{click:function(a){return e.handleDelete(t.row)}}},[e._v("删除 ")])]}}])})],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total>0"}],attrs:{total:e.total,page:e.queryParams.pageNum,limit:e.queryParams.pageSize},on:{"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},pagination:e.getList}})],1)],1),a("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"},{name:"dialogDragWidth",rawName:"v-dialogDragWidth"}],attrs:{"close-on-click-modal":!1,"modal-append-to-body":!1,title:e.title,visible:e.open,width:"500px","append-to-body":""},on:{"update:visible":function(t){e.open=t}}},[a("el-form",{ref:"form",staticClass:"edit",attrs:{model:e.form,rules:e.rules,"label-width":"80px"}},[a("el-form-item",{attrs:{label:"原币种",prop:"overseaCurrency"}},[a("tree-select",{attrs:{pass:e.form.overseaCurrency,type:"currency"},on:{return:e.getCurrencyId}})],1),a("el-form-item",{attrs:{label:"本位币",prop:"localCurrency"}},[a("tree-select",{attrs:{pass:e.form.localCurrency,type:"currency"},on:{return:e.getBasicCurrencyId}})],1),a("el-form-item",{attrs:{label:"基数",prop:"baseShow"}},[a("el-input",{staticStyle:{width:"100%"},attrs:{placeholder:"基数"},nativeOn:{change:function(t){return e.autoCompletion("base")}},model:{value:e.form.baseShow,callback:function(t){e.$set(e.form,"baseShow",t)},expression:"form.baseShow"}})],1),a("el-form-item",{attrs:{label:"买入价",prop:"exchangeRateShow"}},[a("el-input",{staticStyle:{width:"100%"},attrs:{placeholder:"汇率"},nativeOn:{change:function(t){return e.autoCompletion("buyRate")}},model:{value:e.form.buyRateShow,callback:function(t){e.$set(e.form,"buyRateShow",t)},expression:"form.buyRateShow"}})],1),a("el-form-item",{attrs:{label:"汇率",prop:"exchangeRateShow"}},[a("el-input",{staticStyle:{width:"100%"},attrs:{placeholder:"汇率"},nativeOn:{change:function(t){return e.autoCompletion("settleRate")}},model:{value:e.form.settleRateShow,callback:function(t){e.$set(e.form,"settleRateShow",t)},expression:"form.settleRateShow"}})],1),a("el-form-item",{attrs:{label:"卖出价",prop:"exchangeRateShow"}},[a("el-input",{staticStyle:{width:"100%"},attrs:{placeholder:"汇率"},nativeOn:{change:function(t){return e.autoCompletion("sellRate")}},model:{value:e.form.sellRateShow,callback:function(t){e.$set(e.form,"sellRateShow",t)},expression:"form.sellRateShow"}})],1),a("el-form-item",{attrs:{prop:"validTime",label:"有效期"}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{"default-time":["00:00:00","23:59:59"],type:"daterange","value-format":"yyyy-MM-dd HH:mm:ss","range-separator":"至","start-placeholder":"开始日期","end-placeholder":"结束日期"},on:{change:e.changeTime},model:{value:e.validTime,callback:function(t){e.validTime=t},expression:"validTime"}})],1),a("el-form-item",{attrs:{label:"备注",prop:"remark"}},[a("el-input",{attrs:{placeholder:"备注"},model:{value:e.form.remark,callback:function(t){e.$set(e.form,"remark",t)},expression:"form.remark"}})],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("确 定")]),a("el-button",{on:{click:e.cancel}},[e._v("取 消")])],1)],1)],1)},r=[],n=a("5530"),s=(a("d81d"),a("ac1f"),a("00b4"),a("5319"),a("99af"),a("c1b9")),o={name:"exchangerate",data:function(){return{showLeft:3,showRight:21,validTime:[],loading:!0,ids:[],single:!0,multiple:!0,showSearch:!0,total:0,exchangerateList:[],title:"",open:!1,queryParams:{pageNum:1,pageSize:20,overseaCurrency:null,localCurrency:null,base:null,baseShow:null,buyRateShow:null,settleRateShow:null,exchangeRate:null,exchangeRateShow:null,status:null},form:{baseShow:null,exchangeRateShow:null,buyRateShow:null,settleRateShow:null},rules:{}}},watch:{showSearch:function(e){!0===e?(this.showRight=21,this.showLeft=3):(this.showRight=24,this.showLeft=0)}},created:function(){this.getList()},methods:{changeTime:function(e){void 0==e&&(this.form.validFrom=null,this.form.validTo=null),this.form.validFrom=e[0],this.form.validTo=e[1]},getList:function(){var e=this;this.loading=!0,Object(s["e"])(this.queryParams).then((function(t){e.exchangerateList=t.rows,e.total=t.total,e.loading=!1}))},cancel:function(){this.open=!1,this.reset()},reset:function(){this.form={exchangeRateId:null,overseaCurrency:null,localCurrency:null,base:null,exchangeRate:null,status:"0",remark:null,createBy:null,createTime:null,updateBy:null,updateTime:null,deleteBy:null,deleteTime:null,deleteStatus:"0"},this.resetForm("form")},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.resetForm("queryForm"),this.handleQuery()},handleStatusChange:function(e){var t=this,a="0"===e.status?"启用":"停用";this.$confirm('确认要"'+a+"吗？","提示",{customClass:"modal-confirm"}).then((function(){return Object(s["b"])(e.exchangeRateId,e.status)})).then((function(){t.$modal.msgSuccess(a+"成功")})).catch((function(){e.status="0"===e.status?"1":"0"}))},handleSelectionChange:function(e){this.ids=e.map((function(e){return e.exchangeRateId})),this.single=1!==e.length,this.multiple=!e.length},handleAdd:function(){this.reset(),this.open=!0,this.title="添加汇率"},handleUpdate:function(e){var t=this;this.reset();var a=e.exchangeRateId||this.ids;Object(s["d"])(a).then((function(e){t.form=e.data,t.validTime[0]=e.data.validFrom,t.validTime[1]=e.data.validTo;var a=e.data.base;t.form.baseShow=a,t.form.exchangeRateShow=e.data.exchangeRate,t.form.sellRateShow=e.data.sellRate,t.form.buyRateShow=e.data.buyRate,t.form.settleRateShow=e.data.settleRate,t.open=!0,t.title="修改汇率"}))},submitForm:function(){var e=this;this.$refs["form"].validate((function(t){t&&(null!=e.form.exchangeRateId?Object(s["g"])(e.form).then((function(t){e.$modal.msgSuccess("修改成功"),e.open=!1,e.getList()})):Object(s["a"])(e.form).then((function(t){e.$modal.msgSuccess("新增成功"),e.open=!1,e.getList()})))}))},handleDelete:function(e){var t=this,a=e.exchangeRateId||this.ids;this.$confirm('是否确认删除汇率编号为"'+a+'"的数据项？',"提示",{customClass:"modal-confirm"}).then((function(){return Object(s["c"])(a)})).then((function(){t.getList(),t.$modal.msgSuccess("删除成功")})).catch((function(){}))},handleExport:function(){this.download("system/exchangerate/export",Object(n["a"])({},this.queryParams),"exchangerate_".concat((new Date).getTime(),".xlsx"))},getCurrencyId:function(e){this.form.overseaCurrency=e},getBasicCurrencyId:function(e){this.form.localCurrency=e},autoCompletion:function(e){var t=/\d{1,3}(?=(\d{3})+$)/g,a=/[0-9]+/g;if("base"==e)if(a.test(this.form.baseShow)){this.form.base=this.form.baseShow;var l=this.form.baseShow.split("."),r=l[0].replace(t,"$&,");this.form.baseShow=l.length>1&&l[1]?"".concat(r,".").concat(l[1]):"".concat(r,".00")}else this.$message.warning("请输入数字");if("sellRate"==e)if(a.test(this.form.sellRateShow)){this.form.sellRate=this.form.sellRateShow;var n=this.form.sellRateShow.split("."),s=n[0].replace(t,"$&,");this.form.sellRateShow=n.length>1&&n[1]?"".concat(s,".").concat(n[1]):"".concat(s,".00")}else this.$message.warning("请输入数字");if("buyRate"==e)if(a.test(this.form.buyRateShow)){this.form.buyRate=this.form.buyRateShow;var o=this.form.buyRateShow.split("."),i=o[0].replace(t,"$&,");this.form.buyRateShow=o.length>1&&o[1]?"".concat(i,".").concat(o[1]):"".concat(i,".00")}else this.$message.warning("请输入数字");if("settleRate"==e)if(a.test(this.form.settleRateShow)){this.form.settleRate=this.form.settleRateShow;var c=this.form.settleRateShow.split("."),u=c[0].replace(t,"$&,");this.form.settleRateShow=c.length>1&&c[1]?"".concat(u,".").concat(c[1]):"".concat(u,".00")}else this.$message.warning("请输入数字")}}},i=o,c=a("2877"),u=Object(c["a"])(i,l,r,!1,null,null,null);t["default"]=u.exports}}]);