(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-305ce088"],{"4ae4":function(t,a,e){},a03a:function(t,a,e){"use strict";e("4ae4")},cc6c:function(t,a,e){t.exports=e.p+"static/img/401.gif"},ec55:function(t,a,e){"use strict";e.r(a);var s=function(){var t=this,a=t.$createElement,e=t._self._c||a;return e("div",{staticClass:"errPage-container"},[e("el-button",{staticClass:"pan-back-btn",attrs:{icon:"arrow-left"},on:{click:t.back}},[t._v(" 返回 ")]),e("el-row",[e("el-col",{attrs:{span:12}},[e("h1",{staticClass:"text-jumbo text-ginormous"},[t._v(" 401错误! ")]),e("h2",[t._v("您没有访问权限！")]),e("h6",[t._v("对不起，您没有访问权限，请不要进行非法操作！您可以返回主页面")]),e("ul",{staticClass:"list-unstyled"},[e("li",{staticClass:"link-type"},[e("router-link",{attrs:{to:"/"}},[t._v(" 回首页 ")])],1)])]),e("el-col",{attrs:{span:12}},[e("img",{attrs:{src:t.errGif,alt:"Girl has dropped her ice cream.",height:"428",width:"313"}})])],1)],1)},c=[],n=(e("14d9"),e("cc6c")),r=e.n(n),i={name:"Page401",data:function(){return{errGif:r.a+"?"+ +new Date}},methods:{back:function(){this.$route.query.noGoBack?this.$router.push({path:"/"}):this.$router.go(-1)}}},o=i,l=(e("a03a"),e("2877")),u=Object(l["a"])(o,s,c,!1,null,"64876663",null);a["default"]=u.exports}}]);