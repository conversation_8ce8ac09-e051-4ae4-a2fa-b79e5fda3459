(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d0e68d4"],{"98dc":function(e,o,t){"use strict";t.r(o);var s=function(){var e=this,o=e.$createElement,t=e._self._c||o;return t("div",[t("el-tooltip",{attrs:{placement:"top",disabled:(null==e.scope.row.cargoType||e.scope.row.cargoType.length<3)&&(e.scope.row.grossWeight+e.scope.row.cargoUnit).length<5}},[t("div",{attrs:{slot:"content"},slot:"content"},[t("h6",{staticStyle:{margin:"0"}},[e._v(e._s(e.scope.row.cargoType))]),t("h6",{staticStyle:{margin:"0"}},[e._v(e._s(e.scope.row.grossWeight)+" "+e._s(e.scope.row.cargoUnit))])]),t("div",[t("h6",{staticStyle:{margin:"0",overflow:"hidden","text-overflow":"ellipsis","white-space":"nowrap"}},[e._v(" "+e._s(e.scope.row.cargoType))]),t("h6",{staticStyle:{margin:"0",overflow:"hidden","text-overflow":"ellipsis","white-space":"nowrap"}},[e._v(e._s(0!=e.scope.row.grossWeight&&null!=e.scope.row.grossWeight?e.scope.row.grossWeight+e.scope.row.cargoUnit:""))])])])],1)},r=[],c={name:"cargoType",props:["scope"],data:function(){return{size:this.$store.state.app.size||"mini"}}},i=c,n=t("2877"),p=Object(n["a"])(i,s,r,!1,null,"2cfbed42",null);o["default"]=p.exports}}]);