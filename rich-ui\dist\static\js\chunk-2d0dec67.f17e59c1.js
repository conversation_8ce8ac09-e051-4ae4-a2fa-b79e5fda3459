(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d0dec67"],{"86a4":function(e,t,o){"use strict";o.r(t);var s=function(){var e=this,t=e.$createElement,o=e._self._c||t;return o("div",[o("el-tooltip",{attrs:{placement:"top",disabled:null==e.scope.row.updateByName||e.scope.row.updateByName.length<8}},[o("div",{attrs:{slot:"content"},slot:"content"},[o("h6",{staticStyle:{margin:"0"}},[e._v(e._s(e.scope.row.updateByName))]),o("h6",{staticStyle:{margin:"0"}},[e._v(e._s(e.scope.row.updateTime))])]),o("div",[o("h6",{staticStyle:{margin:"0",overflow:"hidden","text-overflow":"ellipsis","white-space":"nowrap"}},[e._v(" "+e._s(e.scope.row.updateByName))]),o("h6",{staticStyle:{margin:"0",overflow:"hidden","text-overflow":"ellipsis","white-space":"nowrap"}},[e._v(" "+e._s(e.parseTime(e.scope.row.updateTime,"{y}-{m}-{d}"))+" ")])])])],1)},a=[],i={name:"recorder",props:["scope"],data:function(){return{size:this.$store.state.app.size||"mini"}}},r=i,n=o("2877"),p=Object(n["a"])(r,s,a,!1,null,"6625ec98",null);t["default"]=p.exports}}]);