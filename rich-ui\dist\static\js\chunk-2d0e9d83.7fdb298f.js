(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d0e9d83"],{"8eee":function(e,t,s){"use strict";s.r(t);var n=function(){var e=this,t=e.$createElement,s=e._self._c||t;return s("div",[s("h6",{staticStyle:{margin:"0"}},[e._v(e._s(e.scope.row.freeStorage))]),s("h6",{staticStyle:{margin:"0"}},[e._v(e._s(e.scope.row.storageUnit))])])},r=[],a={name:"freeStorage",props:["scope"],data:function(){return{size:this.$store.state.app.size||"mini"}}},i=a,o=s("2877"),c=Object(o["a"])(i,n,r,!1,null,"41ed8a79",null);t["default"]=c.exports}}]);