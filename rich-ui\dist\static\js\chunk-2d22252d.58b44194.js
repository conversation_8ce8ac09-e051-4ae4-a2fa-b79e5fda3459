(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d22252d"],{cdb8:function(t,e,o){"use strict";o.r(e);var n=function(){var t=this,e=t.$createElement,o=t._self._c||e;return o("el-col",{style:{display:t.openReceivablePayable?"":"none"},attrs:{span:21.5}},[o("div",{class:{inactive:0==t.openReceivablePayable,active:t.openReceivablePayable}},[o("el-table",{staticClass:"pd0",attrs:{data:t.receivablePayable,"row-class-name":t.rowIndex,border:""}},[o("el-table-column",{attrs:{label:"应收明细"}},[o("el-table-column",{attrs:{label:"客户关联单位"},scopedSlots:t._u([{key:"default",fn:function(e){return[e.row.showClient?t._e():o("div",{on:{click:function(t){e.row.showClient=!0}}},[t._v(" "+t._s(e.row.client)+" ")]),t.$store.state.data.clientList.length>0&&e.row.showClient?o("tree-select",{attrs:{flat:!1,multiple:!1,pass:e.row.clientId,placeholder:"客户",type:"client"},on:{return:function(t){e.row.clientId=t}}}):t._e()]}}])}),o("el-table-column",{attrs:{align:"center",label:"费用",prop:"quotationChargeId",width:"80px"},scopedSlots:t._u([{key:"default",fn:function(e){return[e.row.showQuotationCharge?t._e():o("div",{on:{click:function(t){e.row.showQuotationCharge=!0}}},[t._v(" "+t._s(e.row.quotationCharge)+" ")]),e.row.showQuotationCharge?o("tree-select",{attrs:{dbn:!0,flat:!1,multiple:!1,pass:e.row.quotationChargeId,placeholder:"运费",type:"charge"},on:{return:function(t){e.row.quotationChargeId=t}}}):t._e()]}}])}),o("el-table-column",{attrs:{align:"center",label:"策略",prop:"quotationStrategyId",width:"58"},scopedSlots:t._u([{key:"default",fn:function(e){return[o("el-select",{attrs:{clearable:"",filterable:""},on:{change:function(t){1==e.row.quotationStrategyId&&(e.row.quotationRate=0)}},model:{value:e.row.quotationStrategyId,callback:function(o){t.$set(e.row,"quotationStrategyId",o)},expression:"scope.row.quotationStrategyId"}},[o("el-option",{attrs:{label:"已含",value:"1"}},[t._v("已含")]),o("el-option",{attrs:{label:"未含",value:"0"}},[t._v("未含")])],1)]}}])}),o("el-table-column",{attrs:{align:"center",label:"货币",prop:"quotationCurrencyId",width:"70"},scopedSlots:t._u([{key:"default",fn:function(e){return[e.row.showQuotationCurrency?t._e():o("div",{staticStyle:{width:"69px",height:"23px"},on:{click:function(t){e.row.showQuotationCurrency=!0}}},[t._v(" "+t._s(e.row.quotationCurrency)+" ")]),e.row.showQuotationCurrency?o("tree-select",{attrs:{pass:e.row.quotationCurrencyCode,type:"currency"},on:{return:function(t){e.row.currencyId=t}}}):t._e()]}}])}),o("el-table-column",{attrs:{align:"center",label:"单价",prop:"quotationRate",width:"68px"},scopedSlots:t._u([{key:"default",fn:function(e){return[o("el-input-number",{staticStyle:{display:"flex",width:"100%"},attrs:{controls:!1,min:0},on:{change:function(o){return t.countProfit(e.row)}},model:{value:e.row.quotationRate,callback:function(o){t.$set(e.row,"quotationRate",o)},expression:"scope.row.quotationRate"}})]}}])}),o("el-table-column",{attrs:{label:"单位",lign:"center",prop:"quotationUnitId",width:"70"},scopedSlots:t._u([{key:"default",fn:function(e){return[e.row.showQuotationUnit?t._e():o("div",{on:{click:function(t){e.row.showQuotationUnit=!0}}},[t._v(" "+t._s(e.row.quotationUnitCode)+" ")]),e.row.showQuotationUnit?o("tree-select",{attrs:{pass:e.row.quotationUnitId,type:"unit"},on:{return:function(t){e.row.quotationUnitCode=t}}}):t._e()]}}])}),o("el-table-column",{attrs:{align:"center",label:"数量",prop:"quotationAmount",width:"48px"},scopedSlots:t._u([{key:"default",fn:function(e){return[o("el-input-number",{staticStyle:{display:"flex",width:"100%"},attrs:{controls:!1,min:1,placeholder:"数量"},on:{change:function(o){return t.countProfit(e.row)}},model:{value:e.row.quotationAmount,callback:function(o){t.$set(e.row,"quotationAmount",o)},expression:"scope.row.quotationAmount"}})]}}])}),o("el-table-column",{attrs:{align:"center",label:"汇率",prop:"quotationExchangeRate",width:"60"},scopedSlots:t._u([{key:"default",fn:function(e){return[o("el-input-number",{staticStyle:{width:"100%"},attrs:{controls:!1,min:1,precision:4,step:1e-4},on:{change:function(o){return t.countProfit(e.row)}},model:{value:e.row.quotationExchangeRate,callback:function(o){t.$set(e.row,"quotationExchangeRate",o)},expression:"scope.row.quotationExchangeRate"}})]}}])}),o("el-table-column",{attrs:{align:"center",label:"税率",prop:"quotationTaxRate",width:"68px"},scopedSlots:t._u([{key:"default",fn:function(e){return[o("el-input-number",{staticStyle:{width:"75%"},attrs:{controls:!1,min:0},on:{change:function(o){return t.countProfit(e.row)}},model:{value:e.row.quotationTaxRate,callback:function(o){t.$set(e.row,"quotationTaxRate",o)},expression:"scope.row.quotationTaxRate"}}),t._v(" % ")]}}])}),o("el-table-column",{attrs:{label:"小计",prop:"quotationTotal",width:"48"}})],1),o("el-table-column",{attrs:{"class-name":"showBorder",width:"20px"}}),o("el-table-column",{attrs:{label:"应付明细"}},[o("el-table-column",{attrs:{label:"供应商关联单位",width:"90"},scopedSlots:t._u([{key:"default",fn:function(e){return[e.row.showSupplier?t._e():o("div",{on:{click:function(t){e.row.showSupplier=!0}}},[t._v(" "+t._s(e.row.supplier)+" ")]),t.$store.state.data.supplierList.length>0&&e.row.showSupplier?o("tree-select",{attrs:{flat:!1,multiple:!1,pass:e.row.supplierId,placeholder:"供应商",type:"supplier"},on:{return:function(t){e.row.supplierId=t}}}):t._e()]}}])}),o("el-table-column",{attrs:{align:"center",label:"费用",prop:"costChargeId",width:"80px"},scopedSlots:t._u([{key:"default",fn:function(e){return[e.row.showCostCharge?t._e():o("div",{on:{click:function(t){e.row.showCostCharge=!0}}},[t._v(" "+t._s(e.row.costCharge)+" ")]),e.row.showCostCharge?o("tree-select",{attrs:{dbn:!0,flat:!1,multiple:!1,pass:e.row.inquiryChargeId,placeholder:"运费",type:"charge"},on:{return:function(t){e.row.inquiryChargeId=t}}}):t._e()]}}])}),o("el-table-column",{attrs:{align:"center",label:"策略",prop:"costStrategyId",width:"58px"},scopedSlots:t._u([{key:"default",fn:function(e){return[o("el-select",{attrs:{clearable:"",filterable:""},on:{change:function(t){1==e.row.inquiryStrategyId&&(e.row.inquiryRate=0)}},model:{value:e.row.inquiryStrategyId,callback:function(o){t.$set(e.row,"inquiryStrategyId",o)},expression:"scope.row.inquiryStrategyId"}},[o("el-option",{attrs:{label:"已含",value:"1"}},[t._v("已含")]),o("el-option",{attrs:{label:"未含",value:"0"}},[t._v("未含")])],1)]}}])}),o("el-table-column",{attrs:{align:"center",label:"货币",prop:"costCurrencyId",width:"70px"},scopedSlots:t._u([{key:"default",fn:function(e){return[e.row.showCostCurrency?t._e():o("div",{staticStyle:{width:"69px",height:"23px"},on:{click:function(t){e.row.showCostCurrency=!0}}},[t._v(" "+t._s(e.row.inquiryCurrencyCode)+" ")]),e.row.showCostCurrency?o("tree-select",{attrs:{pass:e.row.inquiryCurrencyCode,type:"currency"},on:{return:function(t){e.row.inquiryCurrencyCode=t}}}):t._e()]}}])}),o("el-table-column",{attrs:{align:"center",label:"单价",prop:"inquiryRate",width:"68px"},scopedSlots:t._u([{key:"default",fn:function(e){return[o("el-input-number",{staticStyle:{display:"flex",width:"100%"},attrs:{controls:!1,min:0},on:{change:function(o){return t.countProfit(e.row)}},model:{value:e.row.inquiryRate,callback:function(o){t.$set(e.row,"inquiryRate",o)},expression:"scope.row.inquiryRate"}})]}}])}),o("el-table-column",{attrs:{label:"单位",lign:"center",prop:"costUnitId",width:"70px"},scopedSlots:t._u([{key:"default",fn:function(e){return[e.row.showCostUnit?t._e():o("div",{on:{click:function(t){e.row.showCostUnit=!0}}},[t._v(" "+t._s(e.row.inquiryUnitCode)+" ")]),e.row.showCostUnit?o("tree-select",{attrs:{pass:e.row.inquiryUnitCode,type:"unit"},on:{return:function(t){e.row.inquiryUnitCode=t}}}):t._e()]}}])}),o("el-table-column",{attrs:{align:"center",label:"数量",prop:"costAmount",width:"48px"},scopedSlots:t._u([{key:"default",fn:function(e){return[o("el-input-number",{staticStyle:{display:"flex",width:"100%"},attrs:{controls:!1,min:1,placeholder:"数量"},on:{change:function(o){return t.countProfit(e.row)}},model:{value:e.row.inquiryAmount,callback:function(o){t.$set(e.row,"inquiryAmount",o)},expression:"scope.row.inquiryAmount"}})]}}])}),o("el-table-column",{attrs:{align:"center",label:"汇率",prop:"costExchangeRate",width:"60px"},scopedSlots:t._u([{key:"default",fn:function(e){return[o("el-input-number",{staticStyle:{width:"100%"},attrs:{controls:!1,min:1,precision:4,step:1e-4},on:{change:function(o){return t.countProfit(e.row)}},model:{value:e.row.inquiryExchangeRate,callback:function(o){t.$set(e.row,"inquiryExchangeRate",o)},expression:"scope.row.inquiryExchangeRate"}})]}}])}),o("el-table-column",{attrs:{align:"center",label:"税率",prop:"costTaxRate",width:"68px"},scopedSlots:t._u([{key:"default",fn:function(e){return[o("el-input-number",{staticStyle:{width:"75%"},attrs:{controls:!1,min:0},on:{change:function(o){return t.countProfit(e.row)}},model:{value:e.row.inquiryTaxRate,callback:function(o){t.$set(e.row,"inquiryTaxRate",o)},expression:"scope.row.inquiryTaxRate"}}),t._v(" % ")]}}])}),o("el-table-column",{attrs:{label:"小计",prop:"costTotal",width:"48"}})],1),o("el-table-column",{attrs:{"class-name":"showBorder",width:"20px"}}),o("el-table-column",{attrs:{label:"辅助决策"}},[o("el-table-column",{attrs:{align:"center",label:"单项利润",prop:"profit"}})],1),o("el-table-column",{attrs:{align:"center","class-name":"small-padding fixed-width",label:"操作",width:"50"},scopedSlots:t._u([{key:"default",fn:function(e){return[o("el-button",{attrs:{icon:"el-icon-delete",size:"mini",type:"danger"},on:{click:function(o){t.receivablePayable=t.receivablePayable.filter((function(t){return t!=e.row}))}}},[t._v("删除 ")])]}}])})],1)],1),o("el-button",{staticStyle:{padding:"0"},attrs:{type:"text"},on:{click:t.addReceivablePayable}},[t._v("[＋] ")])],1)},r=[],a=(o("14d9"),o("b680"),o("a9e3"),{name:"receivablePayable",props:["receivablePayable","openReceivablePayable"],watch:{receivablePayable:function(t){this.$emit("return",t)}},methods:{rowIndex:function(t){var e=t.row,o=t.rowIndex;e.id=o+1},addReceivablePayable:function(){var t={showClient:!0,showSupplier:!0,showQuotationCharge:!0,showCostCharge:!0,showQuotationCurrency:!0,showCostCurrency:!0,showQuotationUnit:!0,showCostUnit:!0};this.receivablePayable.push(t)},countProfit:function(t){t.profit=(Number(t.quotationRate)*Number(t.quotationAmount)-Number(t.inquiryRate)*Number(t.costAmount)).toFixed(2)}}}),l=a,i=o("2877"),u=Object(i["a"])(l,n,r,!1,null,"2a2d1ebc",null);e["default"]=u.exports}}]);