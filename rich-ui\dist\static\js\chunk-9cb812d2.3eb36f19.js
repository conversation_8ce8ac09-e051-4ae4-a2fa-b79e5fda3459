(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-9cb812d2","chunk-2d0d69a4"],{"4fbf":function(e,t,n){"use strict";n.r(t);var a=function(){var e=this,t=e.$createElement,n=e._self._c||t;return n("el-col",{staticStyle:{margin:"0",padding:"0"},style:{display:e.openChargeList?"":"none"},attrs:{span:21.5}},[n("div",{class:{inactive:0==e.openChargeList,active:e.openChargeList}},[n("el-table",{ref:"chargeTable",staticClass:"pd0",attrs:{data:e.chargeData,"row-class-name":e.rowIndex,border:""},on:{"selection-change":e.handleSelectionChange}},[e.isReceivable?n("el-table-column",{attrs:{label:"应收明细",width:"20px"},scopedSlots:e._u([{key:"header",fn:function(t){return[n("el-row",[n("el-col",{attrs:{span:11}},[n("el-row",[n("el-col",{attrs:{span:4}},[e._v(" 不含税小计： ")]),n("el-col",{attrs:{span:6}},[e._v(" USD "+e._s(e.currency(e.rsClientMessageReceivableUSD,{separator:",",symbol:"$"}).format())+" ")]),n("el-col",{attrs:{offset:2,span:6}},[e._v(" RMB "+e._s(e.currency(e.rsClientMessageReceivableRMB,{separator:",",symbol:"￥"}).format())+" ")])],1)],1),n("el-col",{attrs:{span:11}},[n("el-row",{staticClass:"unHighlight-text"},[n("el-col",{attrs:{span:4}},[e._v(" 含税小计： ")]),n("el-col",{attrs:{span:6}},[e._v(" USD "+e._s(e.currency(e.rsClientMessageReceivableTaxUSD,{separator:",",symbol:"$"}).format())+" ")]),n("el-col",{attrs:{offset:2,span:6}},[e._v(" RMB "+e._s(e.currency(e.rsClientMessageReceivableTaxRMB,{separator:",",symbol:"￥"}).format())+" ")])],1)],1),n("el-col",{attrs:{span:2}},[n("el-button",{attrs:{type:"primary"},nativeOn:{click:function(t){e.profitOpen=!0}}},[e._v("利润")]),n("el-dialog",{attrs:{visible:e.profitOpen,title:"单票利润",width:"30%"},on:{"update:visible":function(t){e.profitOpen=t},open:e.openProfit}},[n("el-table",{staticStyle:{width:"100%"},attrs:{data:e.profitTableData,border:""}},[n("el-table-column",{attrs:{label:"货币",prop:"currencyCode"}}),n("el-table-column",{attrs:{label:"应收",prop:"receivable"}}),n("el-table-column",{attrs:{label:"应付",prop:"payable"}}),n("el-table-column",{staticStyle:{color:"#0d0dfd"},attrs:{label:"不含税利润",prop:"profit"}}),n("el-table-column",{attrs:{label:"含税应收",prop:"receivableTax"}}),n("el-table-column",{attrs:{label:"含税应付",prop:"payableTax"}}),n("el-table-column",{attrs:{label:"含税利润",prop:"profitTax"}})],1),n("el-row",[n("el-col",{attrs:{span:5}},[n("el-form-item",{attrs:{label:"折合币种",prop:"rctOpDate"}},[n("el-select",{on:{change:function(t){return e.profitCount(e.currencyCode)}},model:{value:e.currencyCode,callback:function(t){e.currencyCode=t},expression:"currencyCode"}},[n("el-option",{attrs:{label:"RMB",value:"RMB"}}),n("el-option",{attrs:{label:"USD",value:"USD"}})],1)],1)],1),n("el-col",{attrs:{span:7}},[n("el-row",[n("el-col",{attrs:{span:12}},[n("div",{staticStyle:{color:"#0d0dfd"}},[e._v("不含税利润")])]),n("el-col",{attrs:{span:12}},[n("el-input",{attrs:{placeholder:"不含税利润"},model:{value:e.profit,callback:function(t){e.profit=t},expression:"profit"}})],1)],1)],1),n("el-col",{attrs:{span:7}},[n("el-row",[n("el-col",{attrs:{span:12}},[n("div",[e._v("含税利润")])]),n("el-col",{attrs:{span:12}},[n("el-input",{attrs:{placeholder:"含税利润"},model:{value:e.profitTax,callback:function(t){e.profitTax=t},expression:"profitTax"}})],1)],1)],1),n("el-col",{attrs:{span:5}},[n("el-row",[n("el-col",{attrs:{span:12}},[n("div",[e._v("折算汇率")])]),n("el-col",{attrs:{span:12}},[n("el-input",{attrs:{placeholder:"含税利润"},model:{value:e.exchangeRate,callback:function(t){e.exchangeRate=t},expression:"exchangeRate"}})],1)],1)],1)],1)],1)],1)],1)]}}],null,!1,**********)},[n("el-table-column",{attrs:{align:"center",type:"selection"}}),n("el-table-column",{attrs:{align:"center",label:"客户"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.showClient?e._e():n("div",{staticStyle:{width:"50px",height:"20px"},on:{click:function(n){!e.disabled&&"1"!=t.row.isAccountConfirmed&&(t.row.showClient=!0)}}},[e._v(" "+e._s(t.row.companyName)+" ")]),e.companyList&&e.companyList.length>0&&t.row.showClient?n("tree-select",{attrs:{disabled:e.disabled||"1"==t.row.isAccountConfirmed,multiple:!1,pass:t.row.clearingCompanyId,placeholder:"客户","custom-options":e.companyList,flat:!1,type:"clientCustom"},on:{return:function(e){t.row.clearingCompanyId=e},close:function(n){e.showClientName==t.row.companyName&&(t.row.showClient=!1)},returnData:function(t){e.showClientName=t.companyShortName&&""!==t.companyShortName?t.companyShortName:t.companyEnShortName}}}):e._e()]}}],null,!1,**********)}),n("el-table-column",{attrs:{align:"center",label:"费用",prop:"quotationChargeId",width:"80px"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.showQuotationCharge?e._e():n("div",{on:{click:function(n){!e.disabled&&"1"!=t.row.isAccountConfirmed&&(t.row.showQuotationCharge=!0)}}},[e._v(" "+e._s(t.row.chargeName)+" ")]),n("tree-select",{directives:[{name:"show",rawName:"v-show",value:t.row.showQuotationCharge,expression:"scope.row.showQuotationCharge"}],attrs:{dbn:!0,flat:!1,multiple:!1,pass:t.row.dnChargeNameId,placeholder:"运费",type:"charge",disabled:e.disabled||"1"==t.row.isAccountConfirmed},on:{return:function(e){t.row.dnChargeNameId=e},returnData:function(n){return e.handleChargeSelect(t.row,n)}}})]}}],null,!1,*********)}),n("el-table-column",{attrs:{align:"center",label:"货币",prop:"quotationCurrencyId",width:"70"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.showQuotationCurrency?e._e():n("div",{staticStyle:{width:"69px",height:"23px"},on:{click:function(n){!e.disabled&&"1"!=t.row.isAccountConfirmed&&(t.row.showQuotationCurrency=!0)}}},[e._v(" "+e._s(t.row.dnCurrencyCode)+" ")]),n("tree-select",{directives:[{name:"show",rawName:"v-show",value:t.row.showQuotationCurrency,expression:"scope.row.showQuotationCurrency"}],attrs:{pass:t.row.dnCurrencyCode,disabled:e.disabled||"1"==t.row.isAccountConfirmed,type:"currency"},on:{return:function(n){return e.changeCurrency(t.row,n)}}})]}}],null,!1,**********)}),n("el-table-column",{attrs:{align:"center",label:"单价",prop:"quotationRate",width:"80px"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.showUnitRate?e._e():n("div",{on:{click:function(n){!e.disabled&&"1"!=t.row.isAccountConfirmed&&(t.row.showUnitRate=!0)}}},[e._v(" "+e._s("RMB"===t.row.dnCurrencyCode?e.currency(t.row.dnUnitRate,{separator:",",precision:2,symbol:"¥"}).format():"USD"===t.row.dnCurrencyCode?e.currency(t.row.dnUnitRate,{separator:",",precision:2,symbol:"$"}).format():t.row.dnUnitRate)+" ")]),n("el-input-number",{directives:[{name:"show",rawName:"v-show",value:t.row.showUnitRate,expression:"scope.row.showUnitRate"}],staticStyle:{display:"flex",width:"100%"},attrs:{controls:!1,min:1e-4,disabled:e.disabled||"1"==t.row.isAccountConfirmed,precision:4},on:{blur:function(e){t.row.showUnitRate=!1},change:function(n){return e.countProfit(t.row,"unitRate")},input:function(n){return e.countProfit(t.row,"unitRate")}},nativeOn:{focusout:function(e){t.row.showUnitRate=!1}},model:{value:t.row.dnUnitRate,callback:function(n){e.$set(t.row,"dnUnitRate",n)},expression:"scope.row.dnUnitRate"}})]}}],null,!1,**********)}),n("el-table-column",{attrs:{align:"center",label:"单位",prop:"quotationUnitId",width:"50"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.showQuotationUnit?e._e():n("div",{on:{click:function(n){!e.disabled&&"1"!=t.row.isAccountConfirmed&&(t.row.showQuotationUnit=!0)}}},[e._v(" "+e._s(t.row.dnUnitCode)+" ")]),n("tree-select",{directives:[{name:"show",rawName:"v-show",value:t.row.showQuotationUnit,expression:"scope.row.showQuotationUnit"}],attrs:{disabled:e.disabled||"1"==t.row.isAccountConfirmed,pass:t.row.dnUnitCode,type:"unit"},on:{return:function(n){return e.changeUnit(t.row,n)}}})]}}],null,!1,**********)}),n("el-table-column",{attrs:{align:"center",label:"数量",prop:"quotationAmount",width:"48px"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.showAmount?e._e():n("div",{on:{click:function(n){!e.disabled&&"1"!=t.row.isAccountConfirmed&&(t.row.showAmount=!0)}}},[e._v(" "+e._s(t.row.dnAmount)+" ")]),t.row.showAmount?n("el-input-number",{staticStyle:{display:"flex",width:"100%"},attrs:{controls:!1,disabled:e.disabled||"1"==t.row.isAccountConfirmed,placeholder:"数量",min:0},on:{blur:function(e){t.row.showAmount=!1},change:function(n){return e.countProfit(t.row,"amount")},input:function(n){return e.countProfit(t.row,"amount")}},model:{value:t.row.dnAmount,callback:function(n){e.$set(t.row,"dnAmount",n)},expression:"scope.row.dnAmount"}}):e._e()]}}],null,!1,**********)}),n("el-table-column",{attrs:{align:"center",label:"汇率",prop:"quotationExchangeRate",width:"60"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.showCurrencyRate?e._e():n("div",{on:{click:function(n){!e.disabled&&"1"!=t.row.isAccountConfirmed&&(t.row.showCurrencyRate=!0)}}},[e._v(" "+e._s(e.currency(t.row.basicCurrencyRate,{precision:4}).value)+" ")]),n("el-input-number",{directives:[{name:"show",rawName:"v-show",value:t.row.showCurrencyRate,expression:"scope.row.showCurrencyRate"}],staticStyle:{width:"100%"},attrs:{controls:!1,disabled:e.disabled||"1"==t.row.isAccountConfirmed,precision:4,step:1e-4,min:1e-4},on:{blur:function(e){t.row.showCurrencyRate=!1},change:function(n){return e.countProfit(t.row,"currencyRate")},input:function(n){return e.countProfit(t.row,"currencyRate")}},model:{value:t.row.basicCurrencyRate,callback:function(n){e.$set(t.row,"basicCurrencyRate",n)},expression:"scope.row.basicCurrencyRate"}})]}}],null,!1,**********)}),n("el-table-column",{attrs:{align:"center",label:"税率",prop:"quotationTaxRate",width:"50px"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("div",{staticStyle:{display:"flex","justify-content":"center"}},[t.row.showDutyRate?e._e():n("div",{on:{click:function(n){!e.disabled&&"1"!=t.row.isAccountConfirmed&&(t.row.showDutyRate=!0)}}},[e._v(" "+e._s(t.row.dutyRate)+" ")]),t.row.showDutyRate?n("el-input-number",{staticStyle:{width:"75%"},attrs:{controls:!1,disabled:e.disabled||"1"==t.row.isAccountConfirmed,min:0},on:{blur:function(e){t.row.showDutyRate=!1},change:function(n){return e.countProfit(t.row,"dutyRate")},input:function(n){return e.countProfit(t.row,"dutyRate")}},model:{value:t.row.dutyRate,callback:function(n){e.$set(t.row,"dutyRate",n)},expression:"scope.row.dutyRate"}}):e._e(),n("div",[e._v("%")])],1)]}}],null,!1,*********)}),n("el-table-column",{attrs:{label:"小计",prop:"subtotal",width:"75"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("div",[e._v(" "+e._s(e.currency(t.row.subtotal,{separator:",",precision:2,symbol:"RMB"===t.row.dnCurrencyCode?"¥":"$"}).format())+" ")])]}}],null,!1,**********)}),n("el-table-column",{attrs:{align:"center",label:"费用备注"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("input",{directives:[{name:"model",rawName:"v-model",value:t.row.chargeRemark,expression:"scope.row.chargeRemark"}],staticStyle:{border:"none",width:"100%",height:"100%"},attrs:{disabled:e.disabled||"1"==t.row.isAccountConfirmed},domProps:{value:t.row.chargeRemark},on:{input:function(n){n.target.composing||e.$set(t.row,"chargeRemark",n.target.value)}}})]}}],null,!1,*********)}),n("el-table-column",{attrs:{align:"center",label:"审核状态"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(e.auditStatus(t.row.isAccountConfirmed))+" ")]}}],null,!1,**********)}),n("el-table-column",{attrs:{align:"center",label:"已收金额"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(t.row.sqdDnCurrencyPaid)+" ")]}}],null,!1,*********)}),n("el-table-column",{attrs:{align:"center",label:"未收余额"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(t.row.sqdDnCurrencyBalance)+" ")]}}],null,!1,**********)}),n("el-table-column",{attrs:{label:"所属服务"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("div",[e._v(" "+e._s(t.row.serviceName?t.row.serviceName:e.getServiceName(t.row.sqdServiceTypeId))+" ")])]}}],null,!1,**********)})],1):n("el-table-column",{attrs:{label:"应付明细",width:"20px"},scopedSlots:e._u([{key:"header",fn:function(t){return[n("el-row",[n("el-col",{attrs:{span:4}},[n("el-row",[n("el-col",{attrs:{span:12}},[e._v(e._s(e.currency(e.payTotalUSD,{separator:",",symbol:"$"}).format()))]),n("el-col",{attrs:{span:12}},[e._v(e._s(e.currency(e.payTotalRMB,{separator:",",symbol:"￥"}).format()))])],1)],1),n("el-col",{attrs:{span:10}},[n("el-row",[n("el-col",{attrs:{span:5}},[e._v(" 不含税小计： ")]),n("el-col",{attrs:{span:6}},[e._v(" USD "+e._s(e.currency(e.payDetailUSD,{separator:",",symbol:"$"}).format()))]),n("el-col",{attrs:{offset:2,span:6}},[e._v(" RMB "+e._s(e.currency(e.payDetailRMB,{separator:",",symbol:"￥"}).format())+" ")])],1)],1),n("el-col",{attrs:{span:10}},[n("el-row",{staticClass:"unHighlight-text"},[n("el-col",{attrs:{span:4}},[e._v(" 含税小计： ")]),n("el-col",{attrs:{span:6}},[e._v(" USD "+e._s(e.currency(e.payDetailUSDTax,{separator:",",symbol:"$"}).format())+" ")]),n("el-col",{attrs:{offset:2,span:6}},[e._v(" RMB "+e._s(e.currency(e.payDetailRMBTax,{separator:",",symbol:"￥"}).format())+" ")])],1)],1)],1)]}}])},[n("el-table-column",{attrs:{align:"center",type:"selection"}}),e.hiddenSupplier?e._e():n("el-table-column",{attrs:{align:"center",label:"供应商",width:"90"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.showSupplier?e._e():n("div",{on:{click:function(n){!e.disabled&&"1"!=t.row.isAccountConfirmed&&(t.row.showSupplier=!0)}}},[e._v(" "+e._s(t.row.companyName)+" ")]),e.companyList&&e.companyList.length>0&&t.row.showSupplier?n("company-select",{class:e.disabled||"1"==t.row.isAccountConfirmed?"disable-form":"",attrs:{disabled:e.disabled||"1"==t.row.isAccountConfirmed,"load-options":e.companyList,multiple:!1,"no-parent":!0,pass:t.row.clearingCompanyId,placeholder:"供应商"},on:{return:function(e){t.row.clearingCompanyId=e},returnData:function(e){e.companyShortName==t.row.companyName&&(t.row.showSupplier=!1)}}}):e._e()]}}],null,!1,*********)}),n("el-table-column",{attrs:{align:"center",label:"费用",prop:"costChargeId",width:"80px"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.showCostCharge?e._e():n("div",{on:{click:function(n){!e.disabled&&"1"!=t.row.isAccountConfirmed&&(t.row.showCostCharge=!0)}}},[e._v(" "+e._s(t.row.chargeName)+" ")]),t.row.showCostCharge?n("tree-select",{attrs:{dbn:!0,flat:!1,multiple:!1,pass:t.row.dnChargeNameId,placeholder:"运费",type:"charge",disabled:e.disabled||"1"==t.row.isAccountConfirmed},on:{return:function(e){t.row.dnChargeNameId=e},returnData:function(e){e.chargeLocalName==t.row.chargeName&&(t.row.showCostCharge=!1)}}}):e._e()]}}])}),n("el-table-column",{attrs:{align:"center",label:"货币",prop:"costCurrencyId",width:"70px"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.showQuotationCurrency?e._e():n("div",{staticStyle:{width:"69px",height:"23px"},on:{click:function(n){!e.disabled&&"1"!=t.row.isAccountConfirmed&&(t.row.showQuotationCurrency=!0)}}},[e._v(" "+e._s(t.row.dnCurrencyCode)+" ")]),n("tree-select",{directives:[{name:"show",rawName:"v-show",value:t.row.showQuotationCurrency,expression:"scope.row.showQuotationCurrency"}],attrs:{pass:t.row.dnCurrencyCode,disabled:e.disabled||"1"==t.row.isAccountConfirmed,type:"currency"},on:{return:function(n){return e.changeCurrency(t.row,n)}}})]}}])}),n("el-table-column",{attrs:{align:"center",label:"单价",prop:"inquiryRate",width:"80px"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.showUnitRate?e._e():n("div",{on:{click:function(n){!e.disabled&&"1"!=t.row.isAccountConfirmed&&(t.row.showUnitRate=!0)}}},[e._v(" "+e._s("RMB"===t.row.dnCurrencyCode?e.currency(t.row.dnUnitRate,{separator:",",precision:2,symbol:"¥"}).format():"USD"===t.row.dnCurrencyCode?e.currency(t.row.dnUnitRate,{separator:",",precision:2,symbol:"$"}).format():t.row.dnUnitRate)+" ")]),t.row.showUnitRate?n("el-input-number",{staticStyle:{display:"flex",width:"100%"},attrs:{controls:!1,min:0,precision:4,disabled:e.disabled||"1"==t.row.isAccountConfirmed},on:{blur:function(e){t.row.showUnitRate=!1},change:function(n){return e.countProfit(t.row,"unitRate")},input:function(n){return e.countProfit(t.row,"unitRate")}},model:{value:t.row.dnUnitRate,callback:function(n){e.$set(t.row,"dnUnitRate",n)},expression:"scope.row.dnUnitRate"}}):e._e()]}}])}),n("el-table-column",{attrs:{align:"center",label:"单位",prop:"costUnitId",width:"50px"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.showCostUnit?e._e():n("div",{on:{click:function(n){!e.disabled&&"1"!=t.row.isAccountConfirmed&&(t.row.showCostUnit=!0)}}},[e._v(" "+e._s(t.row.dnUnitCode)+" ")]),n("tree-select",{directives:[{name:"show",rawName:"v-show",value:t.row.showCostUnit,expression:"scope.row.showCostUnit"}],attrs:{disabled:e.disabled||"1"==t.row.isAccountConfirmed,pass:t.row.dnUnitCode,type:"unit"},on:{return:function(n){return e.changeUnitCost(t.row,n)}}})]}}])}),n("el-table-column",{attrs:{align:"center",label:"数量",prop:"costAmount",width:"48px"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.showAmount?e._e():n("div",{on:{click:function(n){!e.disabled&&"1"!=t.row.isAccountConfirmed&&(t.row.showAmount=!0)}}},[e._v(" "+e._s(t.row.dnAmount)+" ")]),t.row.showAmount?n("el-input-number",{staticStyle:{display:"flex",width:"100%"},attrs:{controls:!1,disabled:e.disabled||"1"==t.row.isAccountConfirmed,placeholder:"数量",min:0},on:{blur:function(e){t.row.showAmount=!1},change:function(n){return e.countProfit(t.row,"amount")},input:function(n){return e.countProfit(t.row,"amount")}},model:{value:t.row.dnAmount,callback:function(n){e.$set(t.row,"dnAmount",n)},expression:"scope.row.dnAmount"}}):e._e()]}}])}),n("el-table-column",{attrs:{align:"center",label:"汇率",prop:"costExchangeRate",width:"60px"},scopedSlots:e._u([{key:"default",fn:function(t){return[t.row.showCurrencyRate?e._e():n("div",{on:{click:function(n){!e.disabled&&"1"!=t.row.isAccountConfirmed&&(t.row.showCurrencyRate=!0)}}},[e._v(" "+e._s(e.currency(t.row.basicCurrencyRate,{precision:4}).value)+" ")]),t.row.showCurrencyRate?n("el-input-number",{staticStyle:{width:"100%"},attrs:{controls:!1,disabled:e.disabled||"1"==t.row.isAccountConfirmed,precision:4,step:1e-4,min:1e-4},on:{blur:function(e){t.row.showCurrencyRate=!1},change:function(n){return e.countProfit(t.row,"currencyRate")},input:function(n){return e.countProfit(t.row,"currencyRate")}},model:{value:t.row.basicCurrencyRate,callback:function(n){e.$set(t.row,"basicCurrencyRate",n)},expression:"scope.row.basicCurrencyRate"}}):e._e()]}}])}),n("el-table-column",{attrs:{align:"center",label:"税率",prop:"costTaxRate",width:"68px"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("div",{staticStyle:{display:"flex","justify-content":"center"}},[t.row.showDutyRate?e._e():n("div",{on:{click:function(n){!e.disabled&&"1"!=t.row.isAccountConfirmed&&(t.row.showDutyRate=!0)}}},[e._v(" "+e._s(t.row.dutyRate)+" ")]),t.row.showDutyRate?n("el-input-number",{staticStyle:{width:"75%"},attrs:{controls:!1,disabled:e.disabled||"1"==t.row.isAccountConfirmed,min:0},on:{blur:function(e){t.row.showDutyRate=!1},change:function(n){return e.countProfit(t.row,"dutyRate")},input:function(n){return e.countProfit(t.row,"dutyRate")}},model:{value:t.row.dutyRate,callback:function(n){e.$set(t.row,"dutyRate",n)},expression:"scope.row.dutyRate"}}):e._e(),n("div",[e._v("%")])],1)]}}])}),n("el-table-column",{attrs:{align:"center",label:"小计",prop:"costTotal",width:"65"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("div",[e._v(" "+e._s(e.currency(t.row.subtotal,{separator:",",precision:2,symbol:"RMB"===t.row.dnCurrencyCode?"¥":"$"}).format())+" ")])]}}])}),n("el-table-column",{attrs:{align:"center",label:"费用备注"},scopedSlots:e._u([{key:"default",fn:function(t){return[n("input",{directives:[{name:"model",rawName:"v-model",value:t.row.chargeRemark,expression:"scope.row.chargeRemark"}],staticStyle:{border:"none"},attrs:{disabled:e.disabled||"1"==t.row.isAccountConfirmed},domProps:{value:t.row.chargeRemark},on:{input:function(n){n.target.composing||e.$set(t.row,"chargeRemark",n.target.value)}}})]}}])}),n("el-table-column",{attrs:{align:"center",label:"审核状态"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(e.auditStatus(t.row.isAccountConfirmed))+" ")]}}])}),n("el-table-column",{attrs:{align:"center",label:"已付金额"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(t.row.sqdDnCurrencyPaid)+" ")]}}])}),n("el-table-column",{attrs:{align:"center",label:"未付余额"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(t.row.sqdDnCurrencyBalance)+" ")]}}])}),n("el-table-column",{attrs:{align:"center",label:"生成应收",prop:"costTotal",width:"65"},scopedSlots:e._u([{key:"header",fn:function(t){return[n("el-button",{attrs:{disabled:e.disabled,size:"mini",type:"text"},on:{click:function(t){return e.copyAllFreight()}}},[e._v("生成应收 ")])]}},{key:"default",fn:function(t){return[n("el-button",{attrs:{disabled:e.disabled||"1"==t.row.isAccountConfirmed,size:"mini",type:"text"},on:{click:function(n){return e.copyFreight(t.row)}}},[e._v("复制到应收 ")])]}}])})],1),n("el-table-column",{attrs:{align:"center","class-name":"small-padding fixed-width",label:"操作",width:"50"},scopedSlots:e._u([{key:"header",fn:function(t){return[n("el-button",{staticStyle:{color:"red"},attrs:{disabled:e.disabled||e.hasConfirmRow,size:"mini",type:"text"},on:{click:function(t){return e.deleteAllItem()}}},[e._v("全部删除 ")])]}},{key:"default",fn:function(t){return[n("el-button",{attrs:{icon:"el-icon-delete",size:"mini",type:"danger",disabled:e.disabled||"1"==t.row.isAccountConfirmed},on:{click:function(n){return e.deleteItem(t.row)}}},[e._v("删除 ")])]}}])})],1)],1),n("el-button",{staticStyle:{padding:"0"},attrs:{type:"text",disabled:e.disabled},on:{click:e.addReceivablePayable}},[e._v("[＋] ")])],1)},o=[],r=n("5530"),i=n("b85c"),l=(n("d3b7"),n("159b"),n("d81d"),n("14d9"),n("72f9")),s=n.n(l),c=n("ca17"),u=n.n(c),d=n("b0b8"),p=n.n(d),h=n("6e71"),f=n("fba1"),w={name:"charges",components:{CompanySelect:h["a"],Treeselect:u.a},props:["chargeData","companyList","openChargeList","isReceivable","disabled","serviceTypeId","serviceId","hiddenSupplier","rsClientMessageReceivableTaxUSD","rsClientMessageReceivableTaxRMB","rsClientMessagePayableTaxUSD","rsClientMessagePayableTaxRMB","rsClientMessageReceivableRMB","rsClientMessageReceivableUSD","rsClientMessagePayableRMB","rsClientMessagePayableUSD","rsClientMessageProfit","rsClientMessageProfitNoTax","payDetailRMB","payDetailUSD","payDetailRMBTax","payDetailUSDTax","rsClientMessageProfitUSD","rsClientMessageProfitRMB","rsClientMessageProfitTaxRMB","rsClientMessageProfitTaxUSD","ATD"],watch:{chargeData:{handler:function(e,t){t?(e&&e.forEach((function(e,n){var a=t[n];if(e.currency&&e.amount&&a&&"RMB"===a.currency&&"USD"===e.currency&&e.exchangeRate&&0!==e.exchangeRate)try{var o=s()(1,{precision:4}).divide(e.exchangeRate).value;e.subtotal=s()(e.dnUnitRate||0,{precision:4}).multiply(e.amount).multiply(o).multiply(s()(1).add(s()(e.dutyRate||0).divide(100))).value}catch(r){console.error("计算小计出错:",r),e.subtotal=0}})),this.$emit("return",e||[])):this.$emit("return",e)},deep:!0,immediate:!0}},mounted:function(){var e=this;this.chargeData&&this.chargeData.length>0&&this.chargeData.map((function(t){return e.$refs.chargeTable.toggleRowSelection(t,!0)})),this.profitCount("RMB")},computed:{hasConfirmRow:function(){var e=!1;return this.chargeData&&this.chargeData.length>0&&this.chargeData.map((function(t){"1"===t.isAccountConfirmed&&(e=!0)})),e}},data:function(){return{payTotalRMB:0,payTotalUSD:0,showClientName:null,currencyCode:"RMB",profit:0,profitTax:0,exchangeRate:0,services:[{value:1,label:"海运"},{value:10,label:"空运"},{value:20,label:"铁路"},{value:40,label:"快递"},{value:50,label:"拖车"},{value:60,label:"报关"},{value:70,label:"清关派送"},{value:80,label:"码头仓储"},{value:90,label:"检验证书"},{value:100,label:"保险"},{value:101,label:"扩展服务"}],service:[{value:1,label:"基础服务"},{value:4,label:"前程运输"},{value:5,label:"出口报关"},{value:6,label:"进口清关"},{value:2,label:"海运"},{value:3,label:"陆运"},{value:4,label:"铁路"},{value:5,label:"空运"},{value:6,label:"快递"},{value:21,label:"整柜海运"},{value:22,label:"拼柜海运"},{value:23,label:"散杂船"},{value:24,label:"滚装船"},{value:41,label:"整柜铁路"},{value:42,label:"拼柜铁路"},{value:43,label:"铁路车皮"},{value:51,label:"空运普舱"},{value:52,label:"空运包板"},{value:53,label:"空运包机"},{value:54,label:"空运行李"},{value:961,label:"前程运输"},{value:964,label:"进口清关"},{value:7,label:"出口报关"}],chargeRemark:null,profitOpen:!1,profitTableData:[]}},methods:{openProfit:function(){this.profitTableData=[];var e={currencyCode:"RMB"};e.receivable=this.rsClientMessageReceivableRMB,e.payable=this.rsClientMessagePayableRMB,e.profit=this.rsClientMessageProfitRMB,e.receivableTax=this.rsClientMessageReceivableTaxRMB,e.payableTax=this.rsClientMessagePayableTaxRMB,e.profitTax=this.rsClientMessageProfitTaxRMB;var t={currencyCode:"USD"};t.receivable=this.rsClientMessageReceivableUSD,t.payable=this.rsClientMessagePayableUSD,t.profit=this.rsClientMessageProfitUSD,t.receivableTax=this.rsClientMessageReceivableTaxUSD,t.payableTax=this.rsClientMessagePayableTaxUSD,t.profitTax=this.rsClientMessageProfitTaxUSD,this.profitTableData.push(e),this.profitTableData.push(t),this.profitCount("RMB")},profitCount:function(e){var t,n,a=Object(i["a"])(this.$store.state.data.exchangeRateList);try{for(a.s();!(n=a.n()).done;){var o=n.value;this.ATD&&"RMB"===o.localCurrency&&"USD"==o.overseaCurrency&&Object(f["f"])(o.validFrom)<=Object(f["f"])(this.ATD)&&Object(f["f"])(this.ATD)<=Object(f["f"])(o.validTo)&&(t=s()(o.settleRate).divide(o.base).value),t||"RMB"===o.localCurrency&&"USD"==o.overseaCurrency&&Object(f["f"])(o.validFrom)<=Object(f["f"])(new Date)&&Object(f["f"])(new Date)<=Object(f["f"])(o.validTo)&&(t=s()(o.settleRate).divide(o.base).value)}}catch(r){a.e(r)}finally{a.f()}this.exchangeRate=t,"RMB"===e?(this.profit=s()(this.rsClientMessageProfitUSD).multiply(t).add(this.rsClientMessageProfitRMB).value,this.profitTax=s()(this.rsClientMessageProfitTaxUSD).multiply(t).add(this.rsClientMessageProfitTaxRMB).value):(this.profit=s()(this.rsClientMessageProfitRMB).divide(t).add(this.rsClientMessageProfitUSD).value,this.profitTax=s()(this.rsClientMessageProfitTaxRMB).divide(t).add(this.rsClientMessageProfitTaxUSD).value)},auditStatus:function(e){return 1==e?"已审核":"未审核"},selectCharge:function(e,t){t.dnChargeNameId=e.chargeId,t.chargeName=e.chargeLocalName},handleSelectionChange:function(e){var t=this;this.$emit("selectRow",e),this.payTotalUSD=0,this.payTotalRMB=0,e&&e.map((function(e){1==e.isRecievingOrPaying&&("USD"===e.dnCurrencyCode?t.payTotalUSD=s()(t.payTotalUSD).add(e.subtotal):t.payTotalRMB=s()(t.payTotalRMB).add(e.subtotal))}))},currency:s.a,getServiceName:function(e){var t="";return this.services.map((function(n){n.value===e&&(t=n.label)})),t},copyFreight:function(e){this.companyList.length>0&&(e.payClearingCompanyId=this.companyList[0].companyId,e.payCompanyName=this.companyList[0].companyShortName),e.isAccountConfirmed=0;var t=this._.cloneDeep(e);this.$emit("copyFreight",Object(r["a"])(Object(r["a"])({},t),{},{chargeId:null}))},copyAllFreight:function(){var e=this;!this.companyList.length>0?this.$modal.alertWarning("请先选择委托单位或关联单位"):this.chargeData.map((function(t){t.payClearingCompanyId=e.companyList[0].companyId,t.payCompanyName=e.companyList[0].companyShortName,t.isRecievingOrPaying=0,t.isAccountConfirmed=0,t.chargeId=null,e.$emit("copyFreight",e._.cloneDeep(t))}))},changeUnitCost:function(e,t){e.dnUnitCode=t,this.$nextTick((function(){e.showCostUnit=!1}))},changeUnit:function(e,t){e.dnUnitCode=t,this.$nextTick((function(){e.showQuotationUnit=!1}))},handleChargeSelect:function(e,t){e.chargeLocalName===t.chargeName&&(e.chargeName=t.chargeLocalName,e.showQuotationCharge=!1),null==e.currencyCode&&t.currencyCode&&(e.dnCurrencyCode=t.currencyCode)},changeCurrency:function(e,t){e.dnCurrencyCode=t,this.$nextTick((function(){e.showQuotationCurrency=!1}))},rowIndex:function(e){var t=e.row,n=e.rowIndex;t.id=n+1},addReceivablePayable:function(){var e={showClient:!0,showSupplier:!0,showQuotationCharge:!0,showCostCharge:!0,showQuotationCurrency:!0,showCostCurrency:!0,showQuotationUnit:!0,showCostUnit:!0,showStrategy:!0,showUnitRate:!0,showAmount:!0,showCurrencyRate:!0,showDutyRate:!0,basicCurrencyRate:1,dutyRate:0,dnAmount:1,isRecievingOrPaying:this.isReceivable?0:1,clearingCompanyId:this.chargeData.length>0?this.chargeData[this.chargeData.length-1].clearingCompanyId:null};1===this.serviceTypeId&&(e.sqdServiceTypeId=1),10===this.serviceTypeId&&(e.sqdServiceTypeId=10),20===this.serviceTypeId&&(e.sqdServiceTypeId=20),40===this.serviceTypeId&&(e.sqdServiceTypeId=40),50===this.serviceTypeId&&(e.sqdServiceTypeId=50),60===this.serviceTypeId&&(e.sqdServiceTypeId=60),70===this.serviceTypeId&&(e.sqdServiceTypeId=70),80===this.serviceTypeId&&(e.sqdServiceTypeId=80),90===this.serviceTypeId&&(e.sqdServiceTypeId=90),100===this.serviceTypeId&&(e.sqdServiceTypeId=100),101===this.serviceTypeId&&(e.sqdServiceTypeId=101),this.chargeData.push(e)},countProfit:function(e,t){if(e){var n=e.dnUnitRate||0,a=e.dnAmount||0,o=s()(e.basicCurrencyRate||1,{precision:4}).value,r=s()(e.dutyRate||0).value;try{var i=s()(n,{precision:4}).multiply(a).multiply(o).multiply(s()(1).add(s()(r).divide(100))).value;switch(e.subtotal=s()(i,{precision:2}).value,e.sqdDnCurrencyBalance="0"===e.isAccountConfirmed?s()(i,{precision:2}).value:e.sqdDnCurrencyBalance,t){case"strategy":e.showStrategy=!1;break;case"unitRate":break;case"amount":break;case"currencyRate":break;case"dutyRate":break}this.$emit("return",this.chargeData)}catch(l){console.error("计算小计时出错:",l),this.$message.error("计算小计时出错,请检查输入值是否正确")}}},deleteItem:function(e){this.$emit("deleteItem",e)},deleteAllItem:function(e){this.$emit("deleteAll")},companyNormalizer:function(e){return{id:e.companyId,label:(null!=e.companyShortName?e.companyShortName:"")+" "+(null!=e.companyLocalName?e.companyLocalName:"")+","+p.a.getFullChars((null!=e.companyShortName?e.companyShortName:"")+" "+(null!=e.companyLocalName?e.companyLocalName:""))}}}},m=w,y=(n("552d"),n("2877")),b=Object(y["a"])(m,a,o,!1,null,"67f4abf0",null);t["default"]=b.exports},"552d":function(e,t,n){"use strict";n("8c4fd")},"72f9":function(e,t,n){(function(t,n){e.exports=n()})(0,(function(){function e(r,i){if(!(this instanceof e))return new e(r,i);i=Object.assign({},n,i);var l=Math.pow(10,i.precision);this.intValue=r=t(r,i),this.value=r/l,i.increment=i.increment||1/l,i.groups=i.useVedic?o:a,this.s=i,this.p=l}function t(t,n){var a=!(2<arguments.length&&void 0!==arguments[2])||arguments[2],o=n.decimal,r=n.errorOnInvalid,i=n.fromCents,l=Math.pow(10,n.precision),s=t instanceof e;if(s&&i)return t.intValue;if("number"===typeof t||s)o=s?t.value:t;else if("string"===typeof t)r=new RegExp("[^-\\d"+o+"]","g"),o=new RegExp("\\"+o,"g"),o=(o=t.replace(/\((.*)\)/,"-$1").replace(r,"").replace(o,"."))||0;else{if(r)throw Error("Invalid Input");o=0}return i||(o=(o*l).toFixed(4)),a?Math.round(o):o}var n={symbol:"$",separator:",",decimal:".",errorOnInvalid:!1,precision:2,pattern:"!#",negativePattern:"-!#",format:function(e,t){var n=t.pattern,a=t.negativePattern,o=t.symbol,r=t.separator,i=t.decimal;t=t.groups;var l=(""+e).replace(/^-/,"").split("."),s=l[0];return l=l[1],(0<=e.value?n:a).replace("!",o).replace("#",s.replace(t,"$1"+r)+(l?i+l:""))},fromCents:!1},a=/(\d)(?=(\d{3})+\b)/g,o=/(\d)(?=(\d\d)+\d\b)/g;return e.prototype={add:function(n){var a=this.s,o=this.p;return e((this.intValue+t(n,a))/(a.fromCents?1:o),a)},subtract:function(n){var a=this.s,o=this.p;return e((this.intValue-t(n,a))/(a.fromCents?1:o),a)},multiply:function(t){var n=this.s;return e(this.intValue*t/(n.fromCents?1:Math.pow(10,n.precision)),n)},divide:function(n){var a=this.s;return e(this.intValue/t(n,a,!1),a)},distribute:function(t){var n=this.intValue,a=this.p,o=this.s,r=[],i=Math[0<=n?"floor":"ceil"](n/t),l=Math.abs(n-i*t);for(a=o.fromCents?1:a;0!==t;t--){var s=e(i/a,o);0<l--&&(s=s[0<=n?"add":"subtract"](1/a)),r.push(s)}return r},dollars:function(){return~~this.value},cents:function(){return~~(this.intValue%this.p)},format:function(e){var t=this.s;return"function"===typeof e?e(this,t):t.format(this,Object.assign({},t,e))},toString:function(){var e=this.s,t=e.increment;return(Math.round(this.intValue/this.p/t)*t).toFixed(e.precision)},toJSON:function(){return this.value}},e}))},"8c4fd":function(e,t,n){}}]);