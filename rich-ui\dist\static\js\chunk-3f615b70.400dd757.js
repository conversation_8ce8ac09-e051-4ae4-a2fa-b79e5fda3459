(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-3f615b70"],{"4b6b":function(t,e,a){"use strict";a("babb")},"58cf":function(t,e,a){"use strict";a.d(e,"a",(function(){return i}));var i={rctNo:{name:"操作单号",display:"text",aggregated:!1,align:"left",width:"120"},rctCreateTime:{name:"操作日期",display:"date",aggregated:!1,align:"left",width:"120"},emergencyLevel:{name:"紧急",display:"text",aggregated:!1,align:"left",width:"80"},difficultyLevel:{name:"难度",display:"text",aggregated:!1,align:"left",width:"80"},clientId:{name:"委托单位",display:"text",aggregated:!1,align:"left",width:"120"},clientSummary:{name:"委托单位全称",display:"text",aggregated:!1,align:"left",width:"150"},clientRoleId:{name:"客户角色",display:"text",aggregated:!1,align:"left",width:"100"},clientJobNo:{name:"客户单号",display:"text",aggregated:!1,align:"left",width:"120"},relationClientIdList:{name:"相关单位",display:"text",aggregated:!1,align:"left",width:"120"},orderBelongsTo:{name:"订单所属",display:"text",aggregated:!1,align:"left",width:"100"},goodsNameSummary:{name:"货名概要",display:"text",aggregated:!1,align:"left",width:"150"},cargoTypeCodeSum:{name:"货物特征",display:"text",aggregated:!1,align:"left",width:"100"},packageQuantity:{name:"件数",display:"number",aggregated:!0,align:"right",width:"100"},grossWeight:{name:"毛重",display:"number",aggregated:!0,align:"right",width:"100"},goodsVolume:{name:"体积",display:"number",aggregated:!0,align:"right",width:"100"},goodsCurrencyCode:{name:"货值币种",display:"text",aggregated:!1,align:"left",width:"80"},goodsValue:{name:"货值",display:"number",aggregated:!0,align:"right",width:"100"},logisticsTypeId:{name:"物流类型",display:"text",aggregated:!1,align:"left",width:"100"},impExpType:{name:"进出口类型",display:"text",aggregated:!1,align:"left",width:"100"},logisticsTerms:{name:"运输条款",display:"text",aggregated:!1,align:"left",width:"100"},tradingTerms:{name:"贸易条款",display:"text",aggregated:!1,align:"left",width:"100"},tradingPaymentChannel:{name:"收汇方式",display:"text",aggregated:!1,align:"left",width:"100"},freightPaidWayCode:{name:"运费付于",display:"text",aggregated:!1,align:"left",width:"100"},pol:{name:"启运港",display:"text",aggregated:!1,align:"left",width:"100"},pod:{name:"卸货港",display:"text",aggregated:!1,align:"left",width:"100"},destinationPort:{name:"目的港",display:"text",aggregated:!1,align:"left",width:"100"},revenueTon:{name:"计费货量",display:"text",aggregated:!0,align:"left",width:"100"},ctnrTypeCode:{name:"箱型特征",display:"text",aggregated:!1,align:"left",width:"100"},serviceTypeIdList:{name:"服务类型",display:"text",aggregated:!1,align:"left",width:"100"},noAgreementShowed:{name:"不可套约",display:"boolean",aggregated:!1,align:"center",width:"80"},isCustomsIntransitShowed:{name:"属地清关",display:"boolean",aggregated:!1,align:"center",width:"80"},sqdExportCustomsType:{name:"报关方式",display:"text",aggregated:!1,align:"left",width:"100"},sqdTrailerType:{name:"拖车方式",display:"text",aggregated:!1,align:"left",width:"100"},sqdInsuranceType:{name:"投保方式",display:"text",aggregated:!1,align:"left",width:"100"},blTypeCode:{name:"提单类别",display:"text",aggregated:!1,align:"left",width:"100"},blFormCode:{name:"提单形式",display:"text",aggregated:!1,align:"left",width:"100"},sqdPodHandleAgent:{name:"换单代理",display:"text",aggregated:!1,align:"left",width:"120"},psaNo:{name:"订舱单号",display:"text",aggregated:!1,align:"left",width:"120"},carrierCode:{name:"承运人",display:"text",aggregated:!1,align:"left",width:"120"},polBookingAgent:{name:"订舱口",display:"text",aggregated:!1,align:"left",width:"100"},agreementTypeCode:{name:"合约类型",display:"text",aggregated:!1,align:"left",width:"100"},warehousingNo:{name:"入仓号",display:"text",aggregated:!1,align:"left",width:"120"},soNo:{name:"SO号码",display:"text",aggregated:!1,align:"left",width:"120"},blNoSum:{name:"提单号码",display:"text",aggregated:!1,align:"left",width:"120"},containersSum:{name:"柜号汇总",display:"text",aggregated:!1,align:"left",width:"150"},firstVessel:{name:"船名",display:"text",aggregated:!1,align:"left",width:"120"},firstVoyage:{name:"航次",display:"text",aggregated:!1,align:"left",width:"100"},firstCyOpenTime:{name:"开舱",display:"date",aggregated:!1,align:"left",width:"120"},firstCyClosingTime:{name:"截重",display:"date",aggregated:!1,align:"left",width:"120"},cvClosingTime:{name:"截关",display:"date",aggregated:!1,align:"left",width:"120"},siClosingTime:{name:"截补料",display:"date",aggregated:!1,align:"left",width:"120"},etd:{name:"ETD",display:"date",aggregated:!1,align:"left",width:"120"},podEta:{name:"ATD",display:"date",aggregated:!1,align:"left",width:"120"},eta:{name:"ETA",display:"date",aggregated:!1,align:"left",width:"120"},destinationPortAta:{name:"ATA",display:"date",aggregated:!1,align:"left",width:"120"},precarriageSupplierNo:{name:"拖车公司",display:"text",aggregated:!1,align:"left",width:"120"},precarriageRegionId:{name:"装运区域",display:"text",aggregated:!1,align:"left",width:"100"},precarriageAddress:{name:"装运详址",display:"text",aggregated:!1,align:"left",width:"150"},rctProcessStatus:{name:"订单进度",display:"text",aggregated:!1,align:"left",width:"100"},processStatusId:{name:"物流进度",display:"text",aggregated:!1,align:"left",width:"100"},processStatusTime:{name:"进度时间",display:"date",aggregated:!1,align:"left",width:"120"},transportStatusA:{name:"物流进度a",display:"text",aggregated:!1,align:"left",width:"100"},transportStatusB:{name:"物流进度b",display:"text",aggregated:!1,align:"left",width:"100"},warehousingTime:{name:"入仓",display:"date",aggregated:!1,align:"left",width:"120"},bookingTime:{name:"订舱",display:"date",aggregated:!1,align:"left",width:"120"},spaceCfmTime:{name:"放舱",display:"date",aggregated:!1,align:"left",width:"120"},trailerBookedTime:{name:"约车",display:"date",aggregated:!1,align:"left",width:"120"},containerCfmTime:{name:"约柜",display:"date",aggregated:!1,align:"left",width:"120"},containerLoadedTime:{name:"装柜",display:"date",aggregated:!1,align:"left",width:"120"},vesselCfmTime:{name:"配船",display:"date",aggregated:!1,align:"left",width:"120"},vgmSentTime:{name:"VGM",display:"date",aggregated:!1,align:"left",width:"120"},customDocsCfmTime:{name:"单证",display:"date",aggregated:!1,align:"left",width:"120"},customAuthorizedTime:{name:"授权",display:"date",aggregated:!1,align:"left",width:"120"},customExamineTime:{name:"查验",display:"date",aggregated:!1,align:"left",width:"120"},customReleasedTime:{name:"放行",display:"date",aggregated:!1,align:"left",width:"120"},siVerifyTime:{name:"对单",display:"date",aggregated:!1,align:"left",width:"120"},siPostedTime:{name:"补料",display:"date",aggregated:!1,align:"left",width:"120"},amsEnsPostedTime:{name:"AMS/ENS",display:"date",aggregated:!1,align:"left",width:"120"},isfEmnfPostedTime:{name:"ISF/EMNF",display:"date",aggregated:!1,align:"left",width:"120"},docStatus:{name:"文件进度",display:"text",aggregated:!1,align:"left",width:"100"},dnCompletedTime:{name:"录完应收",display:"date",aggregated:!1,align:"left",width:"120"},cnCompletedTime:{name:"录完应付",display:"date",aggregated:!1,align:"left",width:"120"},dnCfmTime:{name:"应收确认",display:"date",aggregated:!1,align:"left",width:"120"},cnCfmTime:{name:"应付确认",display:"date",aggregated:!1,align:"left",width:"120"},clientBlReleaseType:{name:"放单方式",display:"text",aggregated:!1,align:"left",width:"100"},supplierBlReleaseType:{name:"赎单方式",display:"text",aggregated:!1,align:"left",width:"100"},clientPaymentNode:{name:"收款节点",display:"text",aggregated:!1,align:"left",width:"100"},supplierPaymentNode:{name:"付款节点",display:"text",aggregated:!1,align:"left",width:"100"},estimatedRecieveTime:{name:"预收款日",display:"date",aggregated:!1,align:"left",width:"120"},estimatedPayTime:{name:"预付款日",display:"date",aggregated:!1,align:"left",width:"120"},cnAccCfmTime:{name:"应收审核",display:"date",aggregated:!1,align:"left",width:"120"},dnAccCfmTime:{name:"应付审核",display:"date",aggregated:!1,align:"left",width:"120"},cnInvIssuedTime:{name:"进项发票",display:"date",aggregated:!1,align:"left",width:"120"},dnInvIssuedTime:{name:"销项发票",display:"date",aggregated:!1,align:"left",width:"120"},dnReceiveSlipTime:{name:"应收水单",display:"date",aggregated:!1,align:"left",width:"120"},cnPaySlipTime:{name:"应付水单",display:"date",aggregated:!1,align:"left",width:"120"},dnInRmbBalance:{name:"折合未收",display:"number",aggregated:!0,align:"right",width:"120"},mainServicePaidStatus:{name:"主服务付款",display:"text",aggregated:!1,align:"left",width:"100"},allowReleaseBl:{name:"准许放单",display:"boolean",aggregated:!1,align:"center",width:"80"},allowGettingBl:{name:"准许赎单",display:"boolean",aggregated:!1,align:"center",width:"80"},accPromissBlReleaseTime:{name:"预计放单",display:"date",aggregated:!1,align:"left",width:"120"},accPromissBlGetTime:{name:"预计赎单",display:"date",aggregated:!1,align:"left",width:"120"},opAskingBlReleaseTime:{name:"期望放单",display:"date",aggregated:!1,align:"left",width:"120"},opAskingBlGetTime:{name:"期望赎单",display:"date",aggregated:!1,align:"left",width:"120"},actualBlReleaseTime:{name:"提单交付",display:"date",aggregated:!1,align:"left",width:"120"},actualBlGotTime:{name:"提单赎回",display:"date",aggregated:!1,align:"left",width:"120"},docDeliveryWay:{name:"交单方式",display:"text",aggregated:!1,align:"left",width:"100"},docTrackingRefer:{name:"邮递信息",display:"text",aggregated:!1,align:"left",width:"150"},agentNoticeTime:{name:"通知代理",display:"date",aggregated:!1,align:"left",width:"120"},qoutationInRmb:{name:"折合报价",display:"number",aggregated:!0,align:"right",width:"120"},inquiryInRmb:{name:"折合询价",display:"number",aggregated:!0,align:"right",width:"120"},estimatedProfitInRmb:{name:"预期利润",display:"number",aggregated:!0,align:"right",width:"120"},dnUsd:{name:"应收USD",display:"number",aggregated:!0,align:"right",width:"120"},dnRmb:{name:"应收RMB",display:"number",aggregated:!0,align:"right",width:"120"},dnUsdBalance:{name:"未收USD",display:"number",aggregated:!0,align:"right",width:"120"},dnRmbBalance:{name:"未收RMB",display:"number",aggregated:!0,align:"right",width:"120"},dnInRmb:{name:"折合应收",display:"number",aggregated:!0,align:"right",width:"120"},cnUsd:{name:"应付USD",display:"number",aggregated:!0,align:"right",width:"120"},cnRmb:{name:"应付RMB",display:"number",aggregated:!0,align:"right",width:"120"},cnUsdBalance:{name:"未付USD",display:"number",aggregated:!0,align:"right",width:"120"},cnRmbBalance:{name:"未付RMB",display:"number",aggregated:!0,align:"right",width:"120"},cnInRmb:{name:"折合应付",display:"number",aggregated:!0,align:"right",width:"120"},cnInRmbBalance:{name:"折合未付",display:"number",aggregated:!0,align:"right",width:"120"},profitUsd:{name:"USD利润",display:"number",aggregated:!0,align:"right",width:"120"},profitRmb:{name:"RMB利润",display:"number",aggregated:!0,align:"right",width:"120"},profitInRmb:{name:"折合利润",display:"number",aggregated:!0,align:"right",width:"120"},differenceInRmb:{name:"利润差额",display:"number",aggregated:!0,align:"right",width:"120"},profitRate:{name:"毛利率",display:"percentage",aggregated:!0,align:"right",width:"100"},profitRateAgg:{name:"统计毛利率",display:"percentage",aggregated:!0,align:"right",width:"100"},salesDept:{name:"所属部门",display:"getDept",aggregated:!1,align:"left",width:"100"},salesId:{name:"业务员",display:"getName",aggregated:!1,align:"left",width:"100"},salesAssistantId:{name:"业务助理",display:"getName",aggregated:!1,align:"left",width:"100"},salesObserverId:{name:"协助业务员",display:"getName",aggregated:!1,align:"left",width:"120"},statisticsSalesId:{name:"统计业务",display:"getName",aggregated:!1,align:"left",width:"120"},qoutationNo:{name:"报价单号",display:"text",aggregated:!1,align:"left",width:"120"},qoutationTime:{name:"报价日期",display:"date",aggregated:!1,align:"left",width:"120"},newBookingNo:{name:"订舱单号",display:"text",aggregated:!1,align:"left",width:"120"},newBookingTime:{name:"订舱日期",display:"date",aggregated:!1,align:"left",width:"120"},verifyPsaId:{name:"审核商务",display:"getName",aggregated:!1,align:"left",width:"100"},psaVerifyTime:{name:"审核时间",display:"date",aggregated:!1,align:"left",width:"120"},verifyOpLeaderId:{name:"操作主管",display:"text",aggregated:!1,align:"left",width:"100"},opLeaderVerifyTime:{name:"批示时间",display:"date",aggregated:!1,align:"left",width:"120"},opId:{name:"操作员",display:"getName",aggregated:!1,align:"left",width:"100"},opInnerRemark:{name:"操作备注",display:"text",aggregated:!1,align:"left",width:"150"},statusUpdateTime:{name:"状态日期",display:"date",aggregated:!1,align:"left",width:"120"},deleteStatus:{name:"数据状态",display:"text",aggregated:!1,align:"left",width:"100"}}},babb:function(t,e,a){},c2aa:function(t,e,a){"use strict";a.d(e,"o",(function(){return s})),a.d(e,"n",(function(){return l})),a.d(e,"p",(function(){return n})),a.d(e,"r",(function(){return r})),a.d(e,"q",(function(){return o})),a.d(e,"i",(function(){return d})),a.d(e,"g",(function(){return g})),a.d(e,"v",(function(){return c})),a.d(e,"w",(function(){return u})),a.d(e,"h",(function(){return m})),a.d(e,"c",(function(){return p})),a.d(e,"a",(function(){return f})),a.d(e,"f",(function(){return h})),a.d(e,"d",(function(){return y})),a.d(e,"e",(function(){return w})),a.d(e,"k",(function(){return b})),a.d(e,"j",(function(){return v})),a.d(e,"m",(function(){return x})),a.d(e,"t",(function(){return S})),a.d(e,"u",(function(){return _})),a.d(e,"l",(function(){return C})),a.d(e,"s",(function(){return T}));var i=a("b775");function s(t){return Object(i["a"])({url:"/system/rct/list",method:"get",params:t})}function l(t){return Object(i["a"])({url:"/system/rct/aggregator",method:"get",params:t})}function n(t){return Object(i["a"])({url:"/system/rct/listVerifyAggregatorList",method:"get",params:t})}function r(t){return Object(i["a"])({url:"/system/rct/op",method:"get",params:t})}function o(t){return Object(i["a"])({url:"/system/rct/listVerifyList",method:"get",params:t})}function d(t){return Object(i["a"])({url:"/system/rct/"+t,method:"get"})}function g(t){return Object(i["a"])({url:"/system/rct",method:"post",data:t})}function c(t){return Object(i["a"])({url:"/system/rct/saveAs",method:"post",data:t})}function u(t){return Object(i["a"])({url:"/system/rct",method:"put",data:t})}function m(t){return Object(i["a"])({url:"/system/rct/"+t,method:"delete"})}function p(t){return Object(i["a"])({url:"/system/rct/saveClientMessage",method:"post",data:t})}function f(t){return Object(i["a"])({url:"/system/rct/saveBasicLogistics",method:"post",data:t})}function h(t){return Object(i["a"])({url:"/system/rct/savePreCarriage",method:"post",data:t})}function y(t){return Object(i["a"])({url:"/system/rct/saveExportDeclaration",method:"post",data:t})}function w(t){return Object(i["a"])({url:"/system/rct/saveImportClearance",method:"post",data:t})}function b(){return Object(i["a"])({url:"/system/rct/mon",method:"get"})}function v(){return Object(i["a"])({url:"/system/rct/CFmon",method:"get"})}function x(){return Object(i["a"])({url:"/system/rct/RSWHMon",method:"get"})}function S(t){return Object(i["a"])({url:"/system/rct/saveAllService",method:"post",data:t})}function _(t){return Object(i["a"])({url:"/system/rct/saveAsAllService",method:"post",data:t})}function C(t){return Object(i["a"])({url:"/system/rct/listRctNoByCompany/"+t,method:"get"})}function T(t){return Object(i["a"])({url:"/system/rct/writeoff",method:"post",data:t})}},f162:function(t,e,a){"use strict";a.r(e);var i=function(){var t=this,e=t.$createElement,a=t._self._c||e;return a("div",{attrs:{id:"app"}},[a("el-row",{staticStyle:{margin:"0",padding:"0"},attrs:{gutter:20}},[a("el-col",{attrs:{span:t.showLeft}},[a("el-form",{directives:[{name:"show",rawName:"v-show",value:t.showSearch,expression:"showSearch"}],ref:"queryForm",staticClass:"query",attrs:{model:t.queryParams,size:"mini",inline:!0}},[a("el-form-item",{attrs:{label:"单号",prop:"rctNo"}},[a("el-input",{attrs:{placeholder:"操作单号"},nativeOn:{keydown:function(e){return!e.type.indexOf("key")&&t._k(e.keyCode,"enter",13,e.key,"Enter")?null:t.handleQuery(e)}},model:{value:t.queryParams.rctNo,callback:function(e){t.$set(t.queryParams,"rctNo",e)},expression:"queryParams.rctNo"}})],1),a("el-form-item",{attrs:{label:"操作",prop:"rctOpDate"}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{clearable:"",placeholder:"操作日期",type:"daterange","default-time":["00:00:00","23:59:59"],"value-format":"yyyy-MM-dd HH:mm:ss"},model:{value:t.queryParams.rctOpDate,callback:function(e){t.$set(t.queryParams,"rctOpDate",e)},expression:"queryParams.rctOpDate"}})],1),a("el-form-item",{attrs:{label:"紧急",prop:"urgencyDegree"}},[a("el-input",{attrs:{placeholder:"紧急程度"},model:{value:t.queryParams.urgencyDegree,callback:function(e){t.$set(t.queryParams,"urgencyDegree",e)},expression:"queryParams.urgencyDegree"}})],1),a("el-form-item",{attrs:{label:"审核",prop:"pasVerifyTime"}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{clearable:"",placeholder:"商务审核时间","default-time":["00:00:00","23:59:59"],"value-format":"yyyy-MM-dd HH:mm:ss"},model:{value:t.queryParams.pasVerifyTime,callback:function(e){t.$set(t.queryParams,"pasVerifyTime",e)},expression:"queryParams.pasVerifyTime"}})],1),a("el-form-item",{attrs:{label:"标记",prop:"isOpAllotted"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{clearable:"",placeholder:"操作分配标记"},model:{value:t.queryParams.isOpAllotted,callback:function(e){t.$set(t.queryParams,"isOpAllotted",e)},expression:"queryParams.isOpAllotted"}},[a("el-option",{attrs:{label:"未分配",value:"0"}},[t._v("未分配")]),a("el-option",{attrs:{label:"已分配",value:"1"}},[t._v("已分配")])],1)],1),a("el-form-item",{attrs:{label:"客户",prop:"clientId"}},[a("company-select",{attrs:{multiple:!1,"no-parent":!0,pass:t.queryParams.clientId,placeholder:"客户","role-client":"1","role-control":!0,roleTypeId:1},on:{return:function(e){t.queryParams.clientId=e}}})],1),a("el-form-item",{attrs:{label:"放货",prop:"releaseTypeId"}},[a("tree-select",{attrs:{"d-load":!0,flat:!1,multiple:!1,pass:t.queryParams.releaseTypeId,placeholder:"放货方式",type:"releaseType"},on:{return:function(e){t.queryParams.releaseTypeId=e}}})],1),a("el-form-item",{attrs:{label:"进度",prop:"processStatusId"}},[a("tree-select",{attrs:{"d-load":!0,flat:!1,multiple:!1,pass:t.queryParams.processStatusId,placeholder:"进度状态",type:"processStatus"},on:{return:function(e){t.queryParams.processStatusId=e}}})],1),a("el-form-item",{attrs:{label:"物流",prop:"logisticsTypeId"}},[a("tree-select",{attrs:{"d-load":!0,flat:!1,multiple:!1,pass:t.queryParams.logisticsTypeId,placeholder:"物流类型",type:"serviceType"},on:{return:function(e){t.queryParams.logisticsTypeId=e}}})],1),a("el-form-item",{attrs:{label:"启运",prop:"polIds"}},[a("location-select",{attrs:{multiple:!0,"no-parent":!0,pass:t.queryParams.polIds,placeholder:"启运港"},on:{return:function(e){t.queryParams.polIds=e}}})],1),a("el-form-item",{attrs:{label:"目的",prop:"destinationPortIds"}},[a("location-select",{attrs:{en:!0,multiple:!0,pass:t.queryParams.destinationPortIds,placeholder:"目的港"},on:{return:function(e){t.queryParams.destinationPortIds=e}}})],1),a("el-form-item",{attrs:{label:"货量",prop:"revenueTons"}},[a("el-input",{staticStyle:{width:"100%"},attrs:{placeholder:"计费货量"},model:{value:t.queryParams.revenueTons,callback:function(e){t.$set(t.queryParams,"revenueTons",e)},expression:"queryParams.revenueTons"}})],1),a("el-form-item",{attrs:{label:"业务",prop:"salesId"}},[a("treeselect",{attrs:{"disable-branch-nodes":!0,"disabled-fuzzy-matching":!0,"flatten-search-results":!0,normalizer:t.staffNormalizer,options:t.belongList,"show-count":!0,placeholder:"业务员"},on:{open:t.loadSales,select:function(e){t.queryParams.salesId=e.staffId},input:function(e){void 0==e&&(t.queryParams.salesId=null)}},scopedSlots:t._u([{key:"value-label",fn:function(e){var i=e.node;return a("div",{},[t._v(" "+t._s(void 0!=i.raw.staff?i.raw.staff.staffFamilyLocalName+i.raw.staff.staffGivingLocalName+" "+i.raw.staff.staffGivingEnName:"")+" ")])}},{key:"option-label",fn:function(e){var i=e.node,s=e.shouldShowCount,l=e.count,n=e.labelClassName,r=e.countClassName;return a("label",{class:n},[t._v(" "+t._s(-1!=i.label.indexOf(",")?i.label.substring(0,i.label.indexOf(",")):i.label)+" "),s?a("span",{class:r},[t._v("("+t._s(l)+")")]):t._e()])}}]),model:{value:t.salesId,callback:function(e){t.salesId=e},expression:"salesId"}})],1),a("el-form-item",{attrs:{label:"助理",prop:"salesAssistantId"}},[a("treeselect",{attrs:{"disable-branch-nodes":!0,"disabled-fuzzy-matching":!0,"flatten-search-results":!0,normalizer:t.staffNormalizer,options:t.belongList,"show-count":!0,placeholder:"业务助理"},on:{open:t.loadSales,select:function(e){t.queryParams.salesAssistantId=e.staffId},input:function(e){void 0==e&&(t.queryParams.salesAssistantId=null)}},scopedSlots:t._u([{key:"value-label",fn:function(e){var i=e.node;return a("div",{},[t._v(" "+t._s(void 0!=i.raw.staff?i.raw.staff.staffFamilyLocalName+i.raw.staff.staffGivingLocalName+" "+i.raw.staff.staffGivingEnName:"")+" ")])}},{key:"option-label",fn:function(e){var i=e.node,s=e.shouldShowCount,l=e.count,n=e.labelClassName,r=e.countClassName;return a("label",{class:n},[t._v(" "+t._s(-1!=i.label.indexOf(",")?i.label.substring(0,i.label.indexOf(",")):i.label)+" "),s?a("span",{class:r},[t._v("("+t._s(l)+")")]):t._e()])}}]),model:{value:t.salesAssistantId,callback:function(e){t.salesAssistantId=e},expression:"salesAssistantId"}})],1),a("el-form-item",{attrs:{label:"标记",prop:"isPsaVerified"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{clearable:"",placeholder:"商务审核标记"},model:{value:t.queryParams.isPsaVerified,callback:function(e){t.$set(t.queryParams,"isPsaVerified",e)},expression:"queryParams.isPsaVerified"}},[a("el-option",{attrs:{label:"已审",value:"0"}},[t._v("已审")]),a("el-option",{attrs:{label:"未审",value:"1"}},[t._v("未审")])],1)],1),a("el-form-item",{attrs:{label:"商务",prop:"verifyPsaId"}},[a("treeselect",{attrs:{"disable-branch-nodes":!0,"disabled-fuzzy-matching":!0,"flatten-search-results":!0,normalizer:t.staffNormalizer,options:t.businessList,"show-count":!0,placeholder:"商务"},on:{open:t.loadBusinesses,select:function(e){t.queryParams.verifyPsaId=e.staffId},input:function(e){void 0==e&&(t.queryParams.verifyPsaId=null)}},scopedSlots:t._u([{key:"value-label",fn:function(e){var i=e.node;return a("div",{},[t._v(" "+t._s(void 0!=i.raw.staff?i.raw.staff.staffFamilyLocalName+i.raw.staff.staffGivingLocalName+" "+i.raw.staff.staffGivingEnName:"")+" ")])}},{key:"option-label",fn:function(e){var i=e.node,s=e.shouldShowCount,l=e.count,n=e.labelClassName,r=e.countClassName;return a("label",{class:n},[t._v(" "+t._s(-1!=i.label.indexOf(",")?i.label.substring(0,i.label.indexOf(",")):i.label)+" "),s?a("span",{class:r},[t._v("("+t._s(l)+")")]):t._e()])}}]),model:{value:t.verifyPsaId,callback:function(e){t.verifyPsaId=e},expression:"verifyPsaId"}})],1),a("el-form-item",{attrs:{label:"操作",prop:"opId"}},[a("treeselect",{attrs:{"disable-branch-nodes":!0,"disabled-fuzzy-matching":!0,"flatten-search-results":!0,normalizer:t.staffNormalizer,options:t.opList.filter((function(t){return"操作员"==t.role.roleLocalName})),"show-count":!0,placeholder:"操作员"},on:{open:t.loadOp,select:function(e){t.queryParams.opId=e.staffId},input:function(e){void 0==e&&(t.queryParams.opId=null)}},scopedSlots:t._u([{key:"value-label",fn:function(e){var i=e.node;return a("div",{},[t._v(" "+t._s(void 0!=i.raw.staff?i.raw.staff.staffFamilyLocalName+i.raw.staff.staffGivingLocalName+" "+i.raw.staff.staffGivingEnName:"")+" ")])}},{key:"option-label",fn:function(e){var i=e.node,s=e.shouldShowCount,l=e.count,n=e.labelClassName,r=e.countClassName;return a("label",{class:n},[t._v(" "+t._s(-1!=i.label.indexOf(",")?i.label.substring(0,i.label.indexOf(",")):i.label)+" "),s?a("span",{class:r},[t._v("("+t._s(l)+")")]):t._e()])}}]),model:{value:t.opId,callback:function(e){t.opId=e},expression:"opId"}})],1),a("el-form-item",[a("el-button",{attrs:{type:"primary",icon:"el-icon-search",size:"mini"},on:{click:t.handleQuery}},[t._v("搜索")]),a("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:t.resetQuery}},[t._v("重置")])],1)],1)],1),a("el-col",{attrs:{span:t.showRight}},[a("el-row",{staticClass:"mb8",attrs:{gutter:10}},[a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:booking:add"],expression:"['system:booking:add']"}],attrs:{icon:"el-icon-plus",plain:"",size:"mini",type:"primary"},on:{click:t.handleExport}},[t._v("导出 ")])],1),a("el-col",{attrs:{span:1.5}},[a("el-col",{attrs:{span:2}},[a("el-popover",{attrs:{placement:"right",trigger:"click",width:"400"}},[a("el-table",{attrs:{data:t.statisticsOp}},[a("el-table-column",{attrs:{label:"操作",property:"opName",width:"100"}}),a("el-table-column",{attrs:{label:"已派单",property:"number",width:"300"}})],1),"psa"===t.type?a("el-button",{attrs:{slot:"reference"},on:{click:t.handleStatistics},slot:"reference"},[t._v("派单统计")]):t._e()],1)],1)],1),a("el-col",{attrs:{span:1.5}},[a("el-button",{on:{click:t.handleOpenAggregator}},[t._v("数据汇总")]),a("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"},{name:"dialogDragWidth",rawName:"v-dialogDragWidth"}],attrs:{visible:t.openAggregator,"append-to-body":"",width:"80%"},on:{"update:visible":function(e){t.openAggregator=e},open:t.handleOpenAggregator}},[a("data-aggregator",{attrs:{"data-source":t.aggregatorRctList,"field-label-map":t.fieldLabelMap}})],1)],1),a("right-toolbar",{attrs:{showSearch:t.showSearch},on:{"update:showSearch":function(e){t.showSearch=e},"update:show-search":function(e){t.showSearch=e},queryTable:t.getList}})],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:t.loading,expression:"loading"}],attrs:{data:t.rctList,stripe:""},on:{"selection-change":t.handleSelectionChange,"row-dblclick":t.dbclick}},[a("el-table-column",{attrs:{align:"left",label:"序号",type:"index",width:"50"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("el-badge",{staticClass:"item",attrs:{value:t.getBadge(e.row)}},[a("div",{staticStyle:{width:"15px"}},[t._v(t._s(e.$index+1))])])]}}])}),a("el-table-column",{attrs:{align:"left",label:"操作单号",prop:"clientId","show-overflow-tooltip":"",width:"120"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("div",{staticClass:"column-text highlight-text",staticStyle:{"font-size":"18px",height:"23px"}},[t._v(t._s(e.row.rctNo)+" ")]),a("div",{staticClass:"unHighlight-text",staticStyle:{width:"100px"}},[t._v(t._s(t.parseTime(e.row.rctCreateTime,"{y}.{m}.{d}")+" "+t.emergencyLevel(e.row.emergencyLevel))+" ")])]}}])}),a("el-table-column",{attrs:{align:"left",label:"委托单位","show-overflow-tooltip":"",width:"100"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("div",{staticClass:"column-text highlight-text"},[t._v(" "+t._s(e.row.clientSummary?e.row.clientSummary.split("/")[1]:null)+" ")]),a("div",{staticClass:"unHighlight-text",staticStyle:{height:"23px"}},[t._v(" "+t._s((e.row.orderBelongsTo?e.row.orderBelongsTo:"")+" "+(e.row.releaseType?t.getReleaseType(e.row.releaseType):"")+" "+e.row.paymentNode)+" ")])]}}])}),a("el-table-column",{attrs:{align:"left",label:"物流类型",width:"50"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("div",{staticClass:"flex-box"},[a("p",{staticClass:"column-text top-box",staticStyle:{"font-weight":"600"}},[t._v(t._s(e.row.logisticsTypeEnName))]),a("p",{staticClass:"column-text bottom-box",staticStyle:{color:"#b7bbc2",height:"23px"}},[t._v(" "+t._s("1"===e.row.impExpType?"出口":"")+" "+t._s("2"===e.row.impExpType?"进口":"")+" ")])])]}}])}),a("el-table-column",{attrs:{align:"left",label:"启运港","show-overflow-tooltip":"",width:"60"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("div",{staticStyle:{width:"55px",overflow:"hidden"}},[a("p",{staticClass:"column-text top-box ",staticStyle:{"font-size":"15px"}},[t._v(" "+t._s(e.row.pol?e.row.pol.split("(")[0]:e.row.pol)+" ")]),a("p",{staticClass:"unHighlight-text"},[t._v(" "+t._s(e.row.pol?"("+e.row.pol.split("(")[1]:"")+" ")])])]}}])}),a("el-table-column",{attrs:{align:"left",label:"目的港","show-overflow-tooltip":"",width:"100"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("div",{staticStyle:{width:"95px",overflow:"hidden"}},[a("p",{staticClass:"column-text bottom-box highlight-text",staticStyle:{}},[t._v(" "+t._s(e.row.destinationPort?e.row.destinationPort.split("(")[0]:e.row.destinationPort)+" ")]),a("p",{staticClass:"unHighlight-text"},[t._v(" "+t._s(e.row.destinationPort?"("+e.row.destinationPort.split("(")[1]:"")+" ")])])]}}])}),a("el-table-column",{attrs:{align:"left",label:"计费货量",width:"140"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("div",{staticClass:"flex-box"},[a("p",{staticClass:"column-text top-box highlight-text",staticStyle:{}},[t._v(t._s(e.row.revenueTon))]),a("p",{staticClass:"column-text bottom-box unHighlight-text",staticStyle:{}},[t._v(t._s(e.row.goodsNameSummary))])])]}}])}),a("el-table-column",{attrs:{align:"left",label:"提单",width:"60"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("div",{staticClass:"flex-box"},[a("p",{staticClass:"column-text top-box",staticStyle:{}},[t._v(" "+t._s(e.row.blTypeCode?e.row.blTypeCode:"")+" ")]),a("p",{staticClass:"column-text bottom-box unHighlight-text",staticStyle:{height:"23px"}},[t._v(" "+t._s(e.row.blFormCode?e.row.blFormCode:"")+" ")])])]}}])}),a("el-table-column",{attrs:{align:"left",label:"订舱","show-overflow-tooltip":"",width:"90"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("div",{staticClass:"flex-box"},[a("p",{staticClass:"column-text bottom-box",staticStyle:{"text-overflow":"ellipsis","white-space":"nowrap","font-weight":"600","font-size":"13px"}},[t._v(t._s(e.row.carrierEnName)+" "),a("span",{staticClass:"column-text unHighlight-text",staticStyle:{"font-size":"12px"}},[t._v(t._s("("+e.row.agreementTypeCode+")"))])]),a("p",{staticClass:"column-text top-box",staticStyle:{height:"23px"}},[t._v(t._s(e.row.supplierName))])])]}}])}),a("el-table-column",{attrs:{align:"left",label:"入仓与SO号"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("div",{staticClass:"flex-box"},[a("p",{staticClass:"column-text top-box",staticStyle:{}},[t._v(t._s(e.row.warehousingNo))]),a("p",{staticClass:"column-text bottom-box",staticStyle:{}},[t._v(t._s(e.row.soNo))])])]}}])}),a("el-table-column",{attrs:{align:"left",label:"提单与柜号","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("div",{staticClass:"flex-box"},[a("p",{staticClass:"column-text top-box",staticStyle:{}},[t._v(t._s(e.row.blNo))]),a("p",{staticClass:"column-text bottom-box",staticStyle:{height:"23px"}},[t._v(t._s(e.row.sqdContainersSealsSum))])])]}}])}),a("el-table-column",{attrs:{align:"left",label:"订单状态","show-overflow-tooltip":"",width:"50"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("div",{staticClass:"flex-box"},[a("p",{staticClass:"column-text top-box",staticStyle:{}},[t._v(" "+t._s(t.processStatus(e.row.processStatusId)))])])]}}])}),a("el-table-column",{attrs:{align:"left",label:"物流进度","show-overflow-tooltip":""},scopedSlots:t._u([{key:"default",fn:function(e){return[a("div",{staticClass:"flex-box"},[a("p",{staticClass:"column-text top-box",staticStyle:{}},[t._v(t._s(e.row.podEta?"ATD: "+t.parseTime(e.row.podEta,"{m}-{d}"):"ETD: "+t.parseTime(e.row.etd,"{m}-{d}")))]),a("p",{staticClass:"column-text bottom-box",staticStyle:{height:"23px"}},[t._v(t._s(e.row.destinationPortEta?"ATA: "+t.parseTime(e.row.destinationPortEta,"{m}-{d}"):"ETA: "+t.parseTime(e.row.eta,"{m}-{d}")))])])]}}])}),a("el-table-column",{attrs:{align:"left",label:"文件进度"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("div",{staticClass:"flex-box"},[a("p",{staticClass:"column-text top-box",staticStyle:{}},[t._v(t._s(e.row.docStatusA?e.row.docStatusA.split(":")[0]+": "+t.moment(e.row.docStatusA).format("MM.DD"):""))]),a("p",{staticClass:"column-text bottom-box",staticStyle:{}},[t._v(" "+t._s(e.row.docStatusB?e.row.docStatusB.split(":")[0]+": "+t.moment(e.row.docStatusB).format("MM.DD"):""))])])]}}])}),a("el-table-column",{attrs:{align:"right",label:"收款","show-overflow-tooltip":"",width:"100"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("div",{staticClass:"flex-box"},[a("p",{staticClass:"column-text top-box",staticStyle:{}},[t._v(" "+t._s(t.currency(e.row.dnInRmb,{separator:",",symbol:"¥",precision:2}).format())+" ")]),a("p",{staticClass:"column-text bottom-box unHighlight-text",staticStyle:{height:"23px"}},[t._v(t._s(t.currency(t.currency(e.row.dnUsdBalance).value,{separator:",",symbol:"$",precision:2}).format()+" / "+t.currency(t.currency(e.row.dnRmbBalance).value,{separator:",",symbol:"¥",precision:2}).format()))])])]}}])}),a("el-table-column",{attrs:{align:"right",label:"主服务付款","show-overflow-tooltip":"",width:"100"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("div",{staticClass:"flex-box "},[a("p",{staticClass:"column-text top-box",staticStyle:{}},[t._v(" "+t._s(t.currency(e.row.cnInRmb,{separator:",",symbol:"¥",precision:2}).format())+" ")])])]}}])}),a("el-table-column",{attrs:{align:"right",label:"业绩",width:"100"},scopedSlots:t._u([{key:"header",fn:function(e){return[a("div",{staticStyle:{"margin-right":"5px"}},[t._v(" 业绩 ")])]}},{key:"default",fn:function(e){return[a("div",{staticClass:"flex-box",class:t.currency(e.row.sqdProfitRmbSumVat).divide(e.row.sqdDnRmbSumVat).value<0?"warning":"",staticStyle:{"margin-right":"5px"}},[a("p",{staticClass:"column-text top-box",staticStyle:{height:"23px"}},[t._v(t._s(t.currency(e.row.profitInRmb,{separator:",",symbol:"¥",precision:2}).format()))]),a("p",{staticClass:"column-text bottom-box unHighlight-text",staticStyle:{height:"23px"}},[t._v(t._s(t.currency(e.row.profitInRmb).divide(e.row.cnInRmb).multiply(100).value+"%"))])])]}}])}),a("el-table-column",{attrs:{align:"left",label:"业务/助理","show-overflow-tooltip":"",width:"60"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("div",{staticClass:"flex-box",staticStyle:{width:"55px",overflow:"hidden"}},[a("p",{staticClass:"column-text top-box",staticStyle:{overflow:"hidden"}},[t._v(t._s(t.getName(e.row.salesId)+(e.row.salesId?"/"+t.getName(e.row.salesAssistantId):t.getName(e.row.salesAssistantId))?t.getName(e.row.salesId)+(e.row.salesId?"/"+t.getName(e.row.salesAssistantId):t.getName(e.row.salesAssistantId)):null))]),a("p",{staticClass:"column-text bottom-box unHighlight-text",staticStyle:{height:"23px"}},[t._v(" "+t._s(t.parseTime(e.row.newBookingTime,"{m}.{d}"))+" ")])])]}}])}),a("el-table-column",{attrs:{align:"left",label:"商务审核","show-overflow-tooltip":"",width:"60"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("div",{staticClass:"flex-box",staticStyle:{width:"55px",overflow:"hidden"}},[a("p",{staticClass:"column-text top-box",staticStyle:{width:"55px",overflow:"hidden"}},[t._v(t._s(t.getName(e.row.verifyPsaId)))]),a("p",{staticClass:"column-text bottom-box unHighlight-text",staticStyle:{}},[t._v(" "+t._s(t.parseTime(e.row.psaVerifyTime,"{m}.{d}")+" "+t.processStatus(e.row.psaVerifyStatusId)))])])]}}])}),a("el-table-column",{attrs:{align:"left",label:"操作员","show-overflow-tooltip":"",width:"60"},scopedSlots:t._u([{key:"default",fn:function(e){return[a("div",{staticClass:"flex-box"},[a("p",{staticClass:"column-text top-box",staticStyle:{}},[t._v(t._s(t.getName(e.row.opId)))]),a("p",{staticClass:"column-text bottom-box unHighlight-text",staticStyle:{}},[t._v(" "+t._s(t.parseTime(e.row.rctCreateTime,"{m}.{d}")+" "+t.processStatus(e.row.processStatusId)))])])]}}])}),"booking"===t.type?a("el-table-column",{attrs:{align:"left","class-name":"small-padding fixed-width",label:"操作",width:"50"},scopedSlots:t._u([{key:"default",fn:function(e){return[t.hiddenDelete(e.row)?a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:booking:remove"],expression:"['system:booking:remove']"}],staticStyle:{"margin-right":"-8px"},attrs:{size:"mini",type:"danger",icon:"el-icon-delete"},on:{click:function(a){return t.handleDelete(e.row)}}},[t._v("删除 ")]):t._e()]}}],null,!1,3019058727)}):t._e()],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:t.total>0,expression:"total>0"}],attrs:{total:t.total,page:t.queryParams.pageNum,limit:t.queryParams.pageSize},on:{"update:page":function(e){return t.$set(t.queryParams,"pageNum",e)},"update:limit":function(e){return t.$set(t.queryParams,"pageSize",e)},pagination:t.getList}})],1)],1)],1)},s=[],l=a("5530"),n=a("c7eb"),r=a("1da1"),o=(a("4de4"),a("d3b7"),a("d81d"),a("ca17")),d=a.n(o),g=a("4360"),c=a("c2aa"),u=a("b0b8"),m=a.n(u),p=a("72f9"),f=a.n(p),h=a("fba1"),y=a("c1df"),w=a.n(y),b=a("6e71"),v=a("de7d"),x=a("58cf"),S={name:"bookingList",components:{DataAggregator:v["default"],CompanySelect:b["a"],Treeselect:d.a},data:function(){return{showLeft:3,showRight:24,loading:!0,ids:[],single:!0,multiple:!0,showSearch:!1,total:0,salesId:null,verifyPsaId:null,salesAssistantId:null,opId:null,belongList:[],opList:[],businessList:[],rctList:[],queryParams:{pageNum:1,pageSize:20},form:{},statisticsOp:[],rules:{},openAggregator:!1,fieldLabelMap:x["a"],aggregatorRctList:[]}},watch:{showSearch:function(t){!0===t?(this.showRight=21,this.showLeft=3):(this.showRight=24,this.showLeft=0)}},mounted:function(){var t=!1;this.$route.query.no?(this.queryParams.newBookingNo=this.$route.query.no,this.getList().then((function(){t=!0}))):this.getList().then((function(){t=!0})),t&&(this.loadSales(),this.loadOp(),this.loadBusinesses()),this.loadStaffList()},computed:{moment:function(){return w.a}},props:["type"],methods:{handleOpenAggregator:function(){var t=this;"psa"===this.type?Object(c["p"])(this.queryParams).then((function(e){t.aggregatorRctList=e.rows})):Object(c["n"])(this.queryParams).then((function(e){t.aggregatorRctList=e})),this.openAggregator=!0},handleStatistics:function(){var t=this;Object(c["r"])().then((function(e){t.statisticsOp=e.data}))},parseTime:h["f"],getBadge:function(t){return"booking"===this.type&&"0"==t.sqdShippingBookingStatus||"psa"===this.type&&"1"==t.sqdShippingBookingStatus&&"0"==t.psaVerify?"new":""},hiddenDelete:function(t){return"booking"===this.type&&0==t.sqdShippingBookingStatus||"psa"!==this.type&&void 0},currency:f.a,getReleaseType:function(t){return 1==t?"月结":2==t?"押放":3==t?"票结":4==t?"签放":5==t?"订金":6==t?"预付":7==t?"扣货":9==t?"居间":""},handleVerify:function(t){this.$tab.openPage("操作单","/opprocess/opdetail",{rId:this.ids[0],psaVerify:!0})},getName:function(t){if(t){var e=this.$store.state.data.allRsStaffList.filter((function(e){return e.staffId==t}))[0];if(e)return e.staffFamilyLocalName+e.staffGivingLocalName+e.staffShortName}},sqdDocDeliveryWay:function(t){return 1==t?" 境外快递":2==t?" 境内快递":3==t?" 跑腿":4==t?" 业务送达":5==t?" 客户自取":6==t?" QQ":7==t?" 微信":8==t?" 电邮":9==t?" 公众号":10==t?" 承运人系统":11==t?" 订舱口系统":12==t?" 第三方系统":void 0},logisticsPaymentTerms:function(t){return 1==t?"月结":2==t?"押单":3==t?"此票结清":4==t?"经理签单":5==t?"预收订金":6==t?"全额预付":7==t?"扣货":8==t?"背靠背":void 0},emergencyLevel:function(t){return 0==t?"预定":1==t?"当天":2==t?"常规":3==t?"紧急":4==t?"立即":void 0},difficultyLevel:function(t){return 0==t?"简易":1==t?"标准":2==t?"高级":3==t?"特别":void 0},processStatus:function(t){return 1==t?"等待":2==t?"进行":3==t?"变更":4==t?"异常":5==t?"质押":6==t?"确认":7==t?"完成":8==t?"取消":9==t?"驳回":10==t?"回收":void 0},loadSales:function(){var t=this;0==this.$store.state.data.salesList.length||this.$store.state.data.redisList.salesList?g["a"].dispatch("getSalesList").then((function(){t.belongList=t.$store.state.data.salesList})):this.belongList=this.$store.state.data.salesList},loadBusinesses:function(){var t=this;0==this.$store.state.data.businessesList.length||this.$store.state.data.redisList.businessesList?g["a"].dispatch("getBusinessesList").then((function(){t.businessList=t.$store.state.data.businessesList})):this.businessList=this.$store.state.data.businessesList},loadOp:function(){var t=this;0==this.$store.state.data.opList.length||this.$store.state.data.redisList.opList?g["a"].dispatch("getOpList").then((function(){t.opList=t.$store.state.data.opList})):this.opList=this.$store.state.data.opList},loadStaffList:function(){var t=this;0==this.$store.state.data.allRsStaffList.length||this.$store.state.data.redisList.allRsStaffList?g["a"].dispatch("getAllRsStaffList").then((function(){t.staffList=t.$store.state.data.allRsStaffList})):this.staffList=this.$store.state.data.allRsStaffList},getList:function(){var t=this;return Object(r["a"])(Object(n["a"])().mark((function e(){return Object(n["a"])().wrap((function(e){while(1)switch(e.prev=e.next){case 0:return t.loading=!0,"psa"===t.type&&(t.queryParams.sqdShippingBookingStatus=1),t.queryParams.permissionLevel=t.$store.state.user.permissionLevelList.C,e.next=5,Object(c["q"])(t.queryParams).then((function(e){t.rctList=e.rows,t.total=e.total,t.loading=!1}));case 5:case"end":return e.stop()}}),e)})))()},handleQuery:function(){this.queryParams.pageNum=1,this.queryParams.searchValue=1,this.getList()},resetQuery:function(){this.resetForm("queryForm"),this.queryParams.searchValue=null,this.getList()},handleSelectionChange:function(t){this.ids=t.map((function(t){return t.rctId})),this.single=1!==t.length,this.multiple=!t.length},handleAdd:function(){this.$tab.openPage("操作单","/opprocess/opdetail",{})},handleUpdate:function(t){this.$tab.openPage("操作单","/opprocess/opdetail",{rId:t.rctId})},dbclick:function(t,e,a){"booking"===this.type&&this.$tab.openPage("订舱单明细","/salesquotation/bookingDetail",{rId:t.rctId,booking:!0}),"psa"===this.type&&this.$tab.openPage("商务审核明细","/psaVerify/psaDetail",{rId:t.rctId,psaVerify:!0})},tableRowClassName:function(t){var e=t.row;t.rowIndex;return"booking"===this.type&&0==e.sqdShippingBookingStatus||"psa"===this.type&&1!=e.psaVerify?"unconfirmed":""},handleDelete:function(t){var e=this,a=t.rctId||this.ids;this.$confirm('是否确认删除操作单列表编号为"'+a+'"的数据项？',"提示",{customClass:"modal-confirm"}).then((function(){return Object(c["h"])(a)})).then((function(){e.getList(),e.$modal.msgSuccess("删除成功")})).catch((function(){}))},handleExport:function(){this.download("system/rct/bookingExport",Object(l["a"])({},this.queryParams),"rct_".concat((new Date).getTime(),".xlsx"))},staffNormalizer:function(t){var e;return t.children&&!t.children.length&&delete t.children,t.staff&&(e=null==t.staff.staffFamilyLocalName&&null==t.staff.staffGivingLocalName?null!=t.role.roleLocalName?t.role.roleLocalName+","+m.a.getFullChars(t.role.roleLocalName):t.dept.deptLocalName+","+m.a.getFullChars(t.dept.deptLocalName):t.staff.staffCode+" "+t.staff.staffFamilyLocalName+t.staff.staffGivingLocalName+" "+t.staff.staffGivingEnName+","+m.a.getFullChars(t.staff.staffFamilyLocalName+t.staff.staffGivingLocalName)),t.roleId?{id:t.roleId,label:e,children:t.children,isDisabled:null==t.staffId&&void 0==t.children}:{id:t.deptId,label:e,children:t.children,isDisabled:null==t.staffId&&void 0==t.children}}}},_=S,C=(a("4b6b"),a("2877")),T=Object(C["a"])(_,i,s,!1,null,"702a4c89",null);e["default"]=T.exports}}]);