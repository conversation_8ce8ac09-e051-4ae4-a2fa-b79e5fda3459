(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d0c1231"],{4582:function(e,o,l){"use strict";l.r(o);var t=function(){var e=this,o=e.$createElement,l=e._self._c||o;return l("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"},{name:"dialogDragWidth",rawName:"v-dialogDragWidth"}],attrs:{"close-on-click-modal":!1,"modal-append-to-body":!1,visible:e.oopen,"append-to-body":"",width:"1500px"},on:{"update:visible":function(o){e.oopen=o}}},[l("div",{staticStyle:{display:"flex"}},[l("h2",{staticStyle:{"font-weight":"bold",margin:"10px"}},[e._v("新增编号信息")]),l("div",{staticStyle:{"vertical-align":"middle","line-height":"41px"}},[l("el-button",{attrs:{type:"primary"},on:{click:function(o){e.open=!0}}},[e._v("新增")])],1)]),l("el-table",{attrs:{border:"",data:e.logisticsNoInfo,"row-class-name":e.rowIndex}},[l("el-table-column",{attrs:{"header-align":"center",label:"SO号码",prop:"soNo"}}),l("el-table-column",{attrs:{"header-align":"center",label:"主提单号",prop:"mblNo"}}),l("el-table-column",{attrs:{"header-align":"center",label:"货代单号",prop:"hblNo"}}),l("el-table-column",{attrs:{"header-align":"center",label:"柜号信息",prop:"containersInfo"}}),l("el-table-column",{attrs:{"header-align":"center",label:"发货人",prop:"shipper"}}),l("el-table-column",{attrs:{"header-align":"center",label:"收货人",prop:"consignee"}}),l("el-table-column",{attrs:{"header-align":"center",label:"通知人",prop:"notifyParty"}}),l("el-table-column",{attrs:{"header-align":"center",label:"启运港放舱代理",prop:"polBookingAgent"}}),l("el-table-column",{attrs:{"header-align":"center",label:"目的港换单代理",prop:"podHandleAgent"}}),l("el-table-column",{attrs:{"header-align":"center",label:"唛头",prop:"shippingMark"}}),l("el-table-column",{attrs:{"header-align":"center",label:"货描",prop:"goodsDescription"}}),l("el-table-column",{attrs:{"header-align":"center",label:"签单日期",prop:"blIssueDate"}}),l("el-table-column",{attrs:{"header-align":"center",label:"签单地点",prop:"blIssueLocation"}}),l("el-table-column",{attrs:{"header-align":"center",align:"center","class-name":"small-padding fixed-width",label:"操作",width:"100"},scopedSlots:e._u([{key:"default",fn:function(o){return[l("el-button",{staticStyle:{"margin-right":"-8px"},attrs:{icon:"el-icon-edit",size:"mini",type:"success"},on:{click:function(l){return e.handleUpdate(o.row)}}},[e._v("修改 ")]),l("el-button",{staticStyle:{"margin-right":"-8px"},attrs:{icon:"el-icon-delete",size:"mini",type:"danger"},on:{click:function(l){return e.handleDelete(o.row)}}},[e._v("删除 ")])]}}])})],1),l("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"},{name:"dialogDragWidth",rawName:"v-dialogDragWidth"}],attrs:{"close-on-click-modal":!1,"modal-append-to-body":!1,visible:e.open,"append-to-body":"",width:"500px",title:"新增编号信息"},on:{"update:visible":function(o){e.open=o}}},[l("el-form",{attrs:{border:"",data:e.form,"label-width":"105px"}},[l("el-form-item",{attrs:{label:"SO号码",prop:"soNo"}},[l("el-input",{attrs:{placeholder:"SO号码"},model:{value:e.form.soNo,callback:function(o){e.$set(e.form,"soNo",o)},expression:"form.soNo"}})],1),l("el-form-item",{attrs:{label:"主提单号",prop:"mblNo"}},[l("el-input",{attrs:{placeholder:"主提单号"},model:{value:e.form.mblNo,callback:function(o){e.$set(e.form,"mblNo",o)},expression:"form.mblNo"}})],1),l("el-form-item",{attrs:{label:"货代单号",prop:"hblNo"}},[l("el-input",{attrs:{placeholder:"货代单号"},model:{value:e.form.hblNo,callback:function(o){e.$set(e.form,"hblNo",o)},expression:"form.hblNo"}})],1),l("el-form-item",{attrs:{label:"柜号信息",prop:"containersInfo"}},[l("el-input",{attrs:{placeholder:"柜号信息"},model:{value:e.form.containersInfo,callback:function(o){e.$set(e.form,"containersInfo",o)},expression:"form.containersInfo"}})],1),l("el-form-item",{attrs:{label:"发货人",prop:"shipper"}},[l("el-input",{attrs:{placeholder:"发货人"},model:{value:e.form.shipper,callback:function(o){e.$set(e.form,"shipper",o)},expression:"form.shipper"}})],1),l("el-form-item",{attrs:{label:"收货人",prop:"consignee"}},[l("el-input",{attrs:{placeholder:"收货人"},model:{value:e.form.consignee,callback:function(o){e.$set(e.form,"consignee",o)},expression:"form.consignee"}})],1),l("el-form-item",{attrs:{label:"通知人",prop:"notifyParty"}},[l("el-input",{attrs:{placeholder:"通知人"},model:{value:e.form.notifyParty,callback:function(o){e.$set(e.form,"notifyParty",o)},expression:"form.notifyParty"}})],1),l("el-form-item",{attrs:{label:"启运港放舱代理",prop:"polBookingAgent"}},[l("el-input",{attrs:{placeholder:"启运港放舱代理"},model:{value:e.form.polBookingAgent,callback:function(o){e.$set(e.form,"polBookingAgent",o)},expression:"form.polBookingAgent"}})],1),l("el-form-item",{attrs:{label:"目的港换单代理",prop:"podHandleAgent"}},[l("el-input",{attrs:{placeholder:"目的港换单代理"},model:{value:e.form.podHandleAgent,callback:function(o){e.$set(e.form,"podHandleAgent",o)},expression:"form.podHandleAgent"}})],1),l("el-form-item",{attrs:{label:"唛头",prop:"shippingMark"}},[l("el-input",{attrs:{placeholder:"唛头"},model:{value:e.form.shippingMark,callback:function(o){e.$set(e.form,"shippingMark",o)},expression:"form.shippingMark"}})],1),l("el-form-item",{attrs:{label:"货描",prop:"goodsDescription"}},[l("el-input",{attrs:{placeholder:"货描"},model:{value:e.form.goodsDescription,callback:function(o){e.$set(e.form,"goodsDescription",o)},expression:"form.goodsDescription"}})],1),l("el-form-item",{attrs:{label:"签单日期",prop:"blIssueDate"}},[l("el-input",{attrs:{placeholder:"签单日期"},model:{value:e.form.blIssueDate,callback:function(o){e.$set(e.form,"blIssueDate",o)},expression:"form.blIssueDate"}})],1),l("el-form-item",{attrs:{label:"签单地点",prop:"blIssueLocation"}},[l("el-input",{attrs:{placeholder:"签单地点"},model:{value:e.form.blIssueLocation,callback:function(o){e.$set(e.form,"blIssueLocation",o)},expression:"form.blIssueLocation"}})],1)],1),l("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[l("el-button",{attrs:{type:"primary"},on:{click:e.submitForm}},[e._v("确 定")]),l("el-button",{on:{click:e.cancel}},[e._v("取 消")])],1)],1)],1)},n=[],a=(l("4de4"),l("d3b7"),l("14d9"),{name:"logisticsNoInfo",props:["openLogisticsNoInfo"],watch:{logisticsNoInfo:function(){this.$emit("return",this.logisticsNoInfo)},openLogisticsNoInfo:function(e){this.oopen=e},oopen:function(e){0==e&&this.$emit("close")}},data:function(){return{open:!1,oopen:!1,logisticsNoInfo:[],form:{}}},methods:{rowIndex:function(e){var o=e.row,l=e.rowIndex;o.id=l+1},handleUpdate:function(e){this.form=e,this.open=!0},handleDelete:function(e){this.logisticsNoInfo=this.logisticsNoInfo.filter((function(o){return o.id!=e.id}))},submitForm:function(){null!=this.form.id?(this.reset(),this.open=!1):(this.logisticsNoInfo.push(this.form),this.reset(),this.open=!1)},reset:function(){this.form={id:null,soNo:null,mblNo:null,hblNo:null,containersInfo:null,shipper:null,consignee:null,notifyParty:null,polBookingAgent:null,podHandleAgent:null,shippingMark:null,goodsDescription:null,blIssueDate:null,blIssueLocation:null},this.resetForm("form")},cancel:function(){this.open=!1}}}),r=a,i=l("2877"),s=Object(i["a"])(r,t,n,!1,null,null,null);o["default"]=s.exports}}]);