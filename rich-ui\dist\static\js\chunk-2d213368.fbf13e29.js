(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d213368"],{ac5e:function(e,t,r){"use strict";r.r(t);var o=function(){var e=this,t=e.$createElement,r=e._self._c||t;return r("div",[r("el-row",[r("el-col",{attrs:{span:18}},[r("h6",{staticStyle:{margin:"0",float:"left"}},[e._v(" "+e._s(null!=e.scope.row.agreementRecord?(null!=e.scope.row.agreementRecord.creditDays?e.scope.row.agreementRecord.creditDays:"")+"月":" ")+" ")])]),r("el-col",{attrs:{span:6}},[r("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:company:list"],expression:"['system:company:list']"}],staticStyle:{padding:"0",display:"flex",margin:"auto",float:"right"},attrs:{size:e.size,type:"text"},on:{click:function(t){return e.checkAgreement(e.scope.row)}}},[e._v(" "+e._s("[···]")+" ")])],1)],1),r("h6",{staticStyle:{margin:"0","font-weight":"bold"}},[e._v(" "+e._s(null!=e.scope.row.agreementRecord?(null!=e.scope.row.agreementRecord.currencyCode?e.scope.row.agreementRecord.currencyCode:"")+" "+(null!=e.scope.row.agreementRecord.creditLimit?e.scope.row.agreementRecord.creditLimit+"W":" "):" ")+" ")]),null!=e.scope.row.agreementRecord&&(new Date(e.scope.row.agreementRecord.agreementEndDate)-new Date(Date.now()))/1e3/60/60/24<30?r("h6",{staticStyle:{margin:"0",color:"red"}},[e._v(" "+e._s(null!=e.scope.row.agreementRecord?e.scope.row.agreementRecord.agreementEndDate:" ")+" ")]):r("h6",{staticStyle:{margin:"0",color:"darkgray"}},[e._v(" "+e._s(null!=e.scope.row.agreementRecord?e.scope.row.agreementRecord.agreementEndDate:" ")+" ")])],1)},n=[],a={name:"agreement",props:["scope"],data:function(){return{size:this.$store.state.app.size||"mini"}},methods:{checkAgreement:function(e){this.$emit("return",{key:"agreement",value:e})}}},c=a,s=r("2877"),i=Object(s["a"])(c,o,n,!1,null,"09b251b0",null);t["default"]=i.exports}}]);