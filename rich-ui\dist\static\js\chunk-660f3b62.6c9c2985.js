(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-660f3b62","chunk-68702101","chunk-2d0a3aa6","chunk-2d0d69a4"],{"0062":function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],ref:"queryForm",staticClass:"query",attrs:{inline:!0,model:e.queryParams,"label-width":"68px",size:"mini"}},[a("el-form-item",{attrs:{label:"任务名称",prop:"jobName"}},[a("el-input",{staticStyle:{width:"240px"},attrs:{clearable:"",placeholder:"任务名称"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.jobName,callback:function(t){e.$set(e.queryParams,"jobName",t)},expression:"queryParams.jobName"}})],1),a("el-form-item",{attrs:{label:"任务组名",prop:"jobGroup"}},[a("el-select",{staticStyle:{width:"240px"},attrs:{clearable:"",placeholder:"任务组名"},model:{value:e.queryParams.jobGroup,callback:function(t){e.$set(e.queryParams,"jobGroup",t)},expression:"queryParams.jobGroup"}},e._l(e.dict.type.sys_job_group,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),a("el-form-item",{attrs:{label:"执行状态",prop:"status"}},[a("el-select",{staticStyle:{width:"240px"},attrs:{clearable:"",placeholder:"执行状态"},model:{value:e.queryParams.status,callback:function(t){e.$set(e.queryParams,"status",t)},expression:"queryParams.status"}},e._l(e.dict.type.sys_common_status,(function(e){return a("el-option",{key:e.value,attrs:{label:e.label,value:e.value}})})),1)],1),a("el-form-item",{attrs:{label:"执行时间"}},[a("el-date-picker",{staticStyle:{width:"240px"},attrs:{"end-placeholder":"结束日期","range-separator":"-","start-placeholder":"开始日期",type:"daterange","default-time":["00:00:00","23:59:59"],"value-format":"yyyy-MM-dd HH:mm:ss"},model:{value:e.dateRange,callback:function(t){e.dateRange=t},expression:"dateRange"}})],1),a("el-form-item",[a("el-button",{attrs:{icon:"el-icon-search",size:"mini",type:"primary"},on:{click:e.handleQuery}},[e._v("搜索")]),a("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1),a("el-row",{staticClass:"mb8",attrs:{gutter:10}},[a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["monitor:job:remove"],expression:"['monitor:job:remove']"}],attrs:{disabled:e.multiple,icon:"el-icon-delete",plain:"",size:"mini",type:"danger"},on:{click:e.handleDelete}},[e._v("删除 ")])],1),a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["monitor:job:remove"],expression:"['monitor:job:remove']"}],attrs:{icon:"el-icon-delete",plain:"",size:"mini",type:"danger"},on:{click:e.handleClean}},[e._v("清空 ")])],1),a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["monitor:job:export"],expression:"['monitor:job:export']"}],attrs:{icon:"el-icon-download",plain:"",size:"mini",type:"warning"},on:{click:e.handleExport}},[e._v("导出 ")])],1),a("el-col",{attrs:{span:1.5}},[a("el-button",{attrs:{icon:"el-icon-close",plain:"",size:"mini",type:"warning"},on:{click:e.handleClose}},[e._v("关闭 ")])],1),a("right-toolbar",{attrs:{showSearch:e.showSearch},on:{"update:showSearch":function(t){e.showSearch=t},"update:show-search":function(t){e.showSearch=t},queryTable:e.getList}})],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.jobLogList},on:{"selection-change":e.handleSelectionChange}},[a("el-table-column",{attrs:{type:"selection",width:"28",align:"center"}}),a("el-table-column",{attrs:{align:"center",label:"日志编号",prop:"jobLogId",width:"80"}}),a("el-table-column",{attrs:{"show-tooltip-when-overflow":!0,align:"center",label:"任务名称",prop:"jobName"}}),a("el-table-column",{attrs:{"show-tooltip-when-overflow":!0,align:"center",label:"任务组名",prop:"jobGroup"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("dict-tag",{attrs:{options:e.dict.type.sys_job_group,value:t.row.jobGroup}})]}}])}),a("el-table-column",{attrs:{"show-tooltip-when-overflow":!0,align:"center",label:"调用目标字符串",prop:"invokeTarget"}}),a("el-table-column",{attrs:{"show-tooltip-when-overflow":!0,align:"center",label:"日志信息",prop:"jobMessage"}}),a("el-table-column",{attrs:{align:"center",label:"执行状态",prop:"status"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("dict-tag",{attrs:{options:e.dict.type.sys_common_status,value:t.row.status}})]}}])}),a("el-table-column",{attrs:{align:"center",label:"执行时间",prop:"createTime",width:"180"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(e.parseTime(t.row.createTime)))])]}}])}),a("el-table-column",{attrs:{align:"center","class-name":"small-padding fixed-width",label:"操作"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["monitor:job:query"],expression:"['monitor:job:query']"}],attrs:{icon:"el-icon-view",size:"mini",type:"text"},on:{click:function(a){return e.handleView(t.row)}}},[e._v("详细 ")])]}}])})],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total>0"}],attrs:{limit:e.queryParams.pageSize,page:e.queryParams.pageNum,total:e.total},on:{"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},pagination:e.getList}}),a("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"},{name:"dialogDragWidth",rawName:"v-dialogDragWidth"}],attrs:{"close-on-click-modal":!1,"modal-append-to-body":!1,visible:e.open,"append-to-body":"",title:"调度日志详细",width:"700px"},on:{"update:visible":function(t){e.open=t}}},[a("el-form",{ref:"form",attrs:{model:e.form,"label-width":"100px",size:"mini"}},[a("el-row",[a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"日志序号："}},[e._v(e._s(e.form.jobLogId))]),a("el-form-item",{attrs:{label:"任务名称："}},[e._v(e._s(e.form.jobName))])],1),a("el-col",{attrs:{span:12}},[a("el-form-item",{attrs:{label:"任务分组："}},[e._v(e._s(e.form.jobGroup))]),a("el-form-item",{attrs:{label:"执行时间："}},[e._v(e._s(e.form.createTime))])],1),a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"调用方法："}},[e._v(e._s(e.form.invokeTarget))])],1),a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"日志信息："}},[e._v(e._s(e.form.jobMessage))])],1),a("el-col",{attrs:{span:24}},[a("el-form-item",{attrs:{label:"执行状态："}},[0==e.form.status?a("div",[e._v("正常")]):1==e.form.status?a("div",[e._v("失败")]):e._e()])],1),a("el-col",{attrs:{span:24}},[1==e.form.status?a("el-form-item",{attrs:{label:"异常信息："}},[e._v(e._s(e.form.exceptionInfo))]):e._e()],1)],1)],1),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{on:{click:function(t){e.open=!1}}},[e._v("关 闭")])],1)],1)],1)},n=[],i=a("5530"),o=(a("d81d"),a("a159")),l=a("b775");function s(e){return Object(l["a"])({url:"/monitor/jobLog/list",method:"get",params:e})}function c(e){return Object(l["a"])({url:"/monitor/jobLog/"+e,method:"delete"})}function d(){return Object(l["a"])({url:"/monitor/jobLog/clean",method:"delete"})}var u={name:"JobLog",dicts:["sys_common_status","sys_job_group"],data:function(){return{loading:!0,ids:[],multiple:!0,showSearch:!0,total:0,jobLogList:[],open:!1,dateRange:[],form:{},queryParams:{pageNum:1,pageSize:10,jobName:void 0,jobGroup:void 0,status:void 0}}},created:function(){var e=this,t=this.$route.params&&this.$route.params.jobId;void 0!=t&&0!=t?Object(o["d"])(t).then((function(t){e.queryParams.jobName=t.data.jobName,e.queryParams.jobGroup=t.data.jobGroup,e.getList()})):this.getList()},methods:{getList:function(){var e=this;this.loading=!0,s(this.addDateRange(this.queryParams,this.dateRange)).then((function(t){e.jobLogList=t.rows,e.total=t.total,e.loading=!1}))},handleClose:function(){var e={path:"/monitor/job"};this.$tab.closeOpenPage(e)},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.dateRange=[],this.resetForm("queryForm"),this.handleQuery()},handleSelectionChange:function(e){this.ids=e.map((function(e){return e.jobLogId})),this.multiple=!e.length},handleView:function(e){this.open=!0,this.form=e},handleDelete:function(e){var t=this,a=this.ids;this.$confirm('是否确认删除调度日志编号为"'+a+'"的数据项？',"提示",{customClass:"modal-confirm"}).then((function(){return c(a)})).then((function(){t.getList(),t.$modal.msgSuccess("删除成功")})).catch((function(){}))},handleClean:function(){var e=this;this.$confirm("是否确认清空所有调度日志数据项？","提示",{customClass:"modal-confirm"}).then((function(){return d()})).then((function(){e.getList(),e.$modal.msgSuccess("清空成功")})).catch((function(){}))},handleExport:function(){this.download("/monitor/jobLog/export",Object(i["a"])({},this.queryParams),"log_".concat((new Date).getTime(),".xlsx"))}}},f=u,m=a("2877"),p=Object(m["a"])(f,r,n,!1,null,null,null);t["default"]=p.exports},"02c1":function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",[e.scope.row.slipFile?a("el-button",{attrs:{size:"mini",type:"text",icon:"el-icon-search"},on:{click:function(t){e.previewImgOpen=!0}}}):e._e(),a("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"},{name:"dialogDragWidth",rawName:"v-dialogDragWidth"}],attrs:{"close-on-click-modal":!1,"modal-append-to-body":!1,title:"银行水单",visible:e.previewImgOpen,"append-to-body":"",height:"50%",width:"50%"},on:{"update:visible":function(t){e.previewImgOpen=t}}},[a("el-image",{attrs:{src:e.scope.row.slipFile}})],1)],1)},n=[],i={name:"imgPreview",props:["scope"],data:function(){return{previewImgOpen:!1}}},o=i,l=a("2877"),s=Object(l["a"])(o,r,n,!1,null,"6d267c04",null);t["default"]=s.exports},3626:function(e,t,a){"use strict";a.r(t);var r=function(){var e=this,t=e.$createElement,a=e._self._c||t;return a("div",{staticClass:"app-container"},[a("el-row",{attrs:{gutter:20}},[a("el-col",{attrs:{span:e.showLeft}},[a("el-form",{directives:[{name:"show",rawName:"v-show",value:e.showSearch,expression:"showSearch"}],ref:"queryForm",staticClass:"query",attrs:{inline:!0,model:e.queryParams,size:"mini"}},[a("el-form-item",{attrs:{label:"流水",prop:"isRecievingOrPaying"}},[a("el-input",{attrs:{clearable:"",placeholder:"流水号"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.bankRecordNo,callback:function(t){e.$set(e.queryParams,"bankRecordNo",t)},expression:"queryParams.bankRecordNo"}})],1),a("el-form-item",{attrs:{label:"单号",prop:"isRecievingOrPaying"}},[a("el-input",{attrs:{clearable:"",placeholder:"收支"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.sqdRaletiveRctList,callback:function(t){e.$set(e.queryParams,"sqdRaletiveRctList",t)},expression:"queryParams.sqdRaletiveRctList"}})],1),a("el-form-item",{attrs:{label:"收支",prop:"isRecievingOrPaying"}},[a("el-select",{staticStyle:{width:"100%"},attrs:{clearable:"",placeholder:"商务审核标记"},on:{change:e.handleQuery},model:{value:e.queryParams.isRecievingOrPaying,callback:function(t){e.$set(e.queryParams,"isRecievingOrPaying",t)},expression:"queryParams.isRecievingOrPaying"}},[a("el-option",{attrs:{label:"收",value:"0"}},[e._v("收")]),a("el-option",{attrs:{label:"付",value:"1"}},[e._v("付")])],1)],1),a("el-form-item",{attrs:{label:"所属",prop:"sqdPaymentTitleCode"}},[a("el-input",{attrs:{clearable:"",placeholder:"所属公司"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.sqdPaymentTitleCode,callback:function(t){e.$set(e.queryParams,"sqdPaymentTitleCode",t)},expression:"queryParams.sqdPaymentTitleCode"}})],1),a("el-form-item",{attrs:{label:"费用",prop:"sqdPaymentTitleCode"}},[a("tree-select",{staticStyle:{width:"100%"},attrs:{pass:e.queryParams.chargeTypeId,placeholder:"费用类型",type:"chargeType"},on:{return:function(t){e.queryParams.chargeTypeId=t}}})],1),a("el-form-item",{attrs:{label:"账户",prop:"bankAccountCode"}},[a("el-input",{attrs:{clearable:"",placeholder:"银行账户"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.bankAccountCode,callback:function(t){e.$set(e.queryParams,"bankAccountCode",t)},expression:"queryParams.bankAccountCode"}})],1),a("el-form-item",{attrs:{label:"公司",prop:"sqdClearingCompanyShortname"}},[a("el-input",{attrs:{clearable:"",placeholder:"结算公司简称"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.sqdClearingCompanyShortname,callback:function(t){e.$set(e.queryParams,"sqdClearingCompanyShortname",t)},expression:"queryParams.sqdClearingCompanyShortname"}})],1),a("el-form-item",{attrs:{label:"币种",prop:"bankCurrencyCode"}},[a("el-input",{attrs:{clearable:"",placeholder:"银行币种"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.bankCurrencyCode,callback:function(t){e.$set(e.queryParams,"bankCurrencyCode",t)},expression:"queryParams.bankCurrencyCode"}})],1),a("el-form-item",{attrs:{label:"时间",prop:"bankRecordTime"}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{clearable:"",placeholder:"银行流水发生时间",type:"date","value-format":"yyyy-MM-dd"},model:{value:e.queryParams.bankRecordTime,callback:function(t){e.$set(e.queryParams,"bankRecordTime",t)},expression:"queryParams.bankRecordTime"}})],1),a("el-form-item",{attrs:{label:"录入",prop:"bankRecordByStaffId"}},[a("el-input",{attrs:{clearable:"",placeholder:"银行流水录入人"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.bankRecordByStaffId,callback:function(t){e.$set(e.queryParams,"bankRecordByStaffId",t)},expression:"queryParams.bankRecordByStaffId"}})],1),a("el-form-item",{attrs:{label:"录入",prop:"bankRecordUpdateTime"}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{clearable:"",placeholder:"银行流水录入时间 ,",type:"date","value-format":"yyyy-MM-dd"},model:{value:e.queryParams.bankRecordUpdateTime,callback:function(t){e.$set(e.queryParams,"bankRecordUpdateTime",t)},expression:"queryParams.bankRecordUpdateTime"}})],1),a("el-form-item",{attrs:{label:"销账",prop:"writeoffStaffId"}},[a("el-input",{attrs:{clearable:"",placeholder:"销账人"},nativeOn:{keyup:function(t){return!t.type.indexOf("key")&&e._k(t.keyCode,"enter",13,t.key,"Enter")?null:e.handleQuery(t)}},model:{value:e.queryParams.writeoffStaffId,callback:function(t){e.$set(e.queryParams,"writeoffStaffId",t)},expression:"queryParams.writeoffStaffId"}})],1),a("el-form-item",{attrs:{label:"销账",prop:"writeoffTime"}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{clearable:"",placeholder:"销账时间",type:"date","value-format":"yyyy-MM-dd"},model:{value:e.queryParams.writeoffTime,callback:function(t){e.$set(e.queryParams,"writeoffTime",t)},expression:"queryParams.writeoffTime"}})],1),a("el-form-item",[a("el-button",{attrs:{icon:"el-icon-search",size:"mini",type:"primary"},on:{click:e.handleQuery}},[e._v("搜索")]),a("el-button",{attrs:{icon:"el-icon-refresh",size:"mini"},on:{click:e.resetQuery}},[e._v("重置")])],1)],1)],1),a("el-col",{attrs:{span:e.showRight}},[a("el-row",{staticClass:"mb8",attrs:{gutter:10}},[a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:bankrecord:add"],expression:"['system:bankrecord:add']"}],attrs:{icon:"el-icon-plus",plain:"",size:"mini",type:"primary"},on:{click:e.handleAdd}},[e._v("新增 ")])],1),a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:bankrecord:edit"],expression:"['system:bankrecord:edit']"}],attrs:{disabled:e.single,icon:"el-icon-edit",plain:"",size:"mini",type:"success"},on:{click:e.handleUpdate}},[e._v("修改 ")])],1),a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:bankrecord:add"],expression:"['system:bankrecord:add']"}],attrs:{icon:"el-icon-plus",plain:"",size:"mini",type:"primary"},on:{click:e.handleImport}},[e._v("导入 ")])],1),a("el-col",{attrs:{span:1.5}},[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:bankrecord:add"],expression:"['system:bankrecord:add']"}],attrs:{icon:"el-icon-download",plain:"",size:"mini",type:"primary"},on:{click:e.handleExport}},[e._v("导出 ")])],1),a("el-col",{attrs:{span:1.5}},[a("el-button",{attrs:{icon:"el-icon-data-analysis",plain:"",size:"mini",type:"primary"},on:{click:e.accountFundStatistics}},[e._v("账户资金统计 ")])],1),a("right-toolbar",{attrs:{showSearch:e.showSearch},on:{"update:showSearch":function(t){e.showSearch=t},"update:show-search":function(t){e.showSearch=t}}})],1),a("el-table",{directives:[{name:"loading",rawName:"v-loading",value:e.loading,expression:"loading"}],attrs:{data:e.bankrecordList,"highlight-current-row":"",stripe:""},on:{"selection-change":e.handleSelectionChange,"row-dblclick":e.handledbClick}},[a("el-table-column",{attrs:{align:"center",type:"selection",width:"28"}}),a("el-table-column",{attrs:{align:"center",label:"银行流水",prop:"bankRecordNo"}}),a("el-table-column",{attrs:{align:"center",label:"收支标志",prop:"isRecievingOrPaying"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(1==t.row.isRecievingOrPaying?"付":"收"))])]}}])}),a("el-table-column",{attrs:{align:"center",label:"所属公司",prop:"sqdPaymentTitleCode"}}),a("el-table-column",{attrs:{align:"center",label:"费用类型",prop:"chargeName"}}),a("el-table-column",{attrs:{align:"center",label:"结算公司",prop:"sqdClearingCompanyShortname"}}),a("el-table-column",{attrs:{align:"center",label:"银行时间",prop:"bankRecordTime",width:"80"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(e.parseTime(t.row.bankRecordTime,"{y}-{m}-{d}")))])]}}])}),a("el-table-column",{attrs:{align:"center",label:"银行账户",prop:"bankAccountCode"}}),a("el-table-column",{attrs:{align:"center",label:"币种",prop:"bankCurrencyCode"}}),a("el-table-column",{attrs:{align:"center",label:"水单金额",prop:"slipAmount"}}),a("el-table-column",{attrs:{align:"center",label:"实收金额",prop:"actualBankRecievedAmount"}}),a("el-table-column",{attrs:{align:"center",label:"收款手续费",prop:"bankRecievedHandlingFee"}}),a("el-table-column",{attrs:{align:"center",label:"实付金额",prop:"actualBankPaidAmount"}}),a("el-table-column",{attrs:{align:"center",label:"付款手续费",prop:"bankPaidHandlingFee"}}),a("el-table-column",{attrs:{align:"center",label:"销账状态 ",prop:"writeoffStatus"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(1==t.row.writeoffStatus?"=":0==t.row.writeoffStatus?"√":"-"))])]}}])}),a("el-table-column",{attrs:{align:"center",label:"相关操作单号",prop:"sqdRaletiveRctList","show-overflow-tooltip":""}}),a("el-table-column",{attrs:{align:"center",label:"销账人",prop:"writeoffStaffId"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(e.getName(t.row.writeoffStaffId))+" ")]}}])}),a("el-table-column",{attrs:{align:"center",label:"销账备注",prop:"bankRecordRemark","show-overflow-tooltip":""}}),a("el-table-column",{attrs:{align:"center",label:"销账时间",prop:"writeoffTime",width:"80"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",[e._v(e._s(e.parseTime(t.row.writeoffTime,"{y}-{m}-{d}")))])]}}])}),a("el-table-column",{attrs:{align:"center","class-name":"small-padding fixed-width",label:"操作"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:bankrecord:edit"],expression:"['system:bankrecord:edit']"}],staticStyle:{"margin-right":"-8px"},attrs:{icon:"el-icon-edit",size:"mini",type:"success"},on:{click:function(a){return e.handleUpdate(t.row)}}},[e._v("修改 ")]),a("el-button",{directives:[{name:"hasPermi",rawName:"v-hasPermi",value:["system:bankrecord:remove"],expression:"['system:bankrecord:remove']"}],staticStyle:{"margin-right":"-8px"},attrs:{icon:"el-icon-delete",size:"mini",type:"danger"},on:{click:function(a){return e.handleDelete(t.row)}}},[e._v("删除 ")])]}}])})],1),a("pagination",{directives:[{name:"show",rawName:"v-show",value:e.total>0,expression:"total>0"}],attrs:{limit:e.queryParams.pageSize,page:e.queryParams.pageNum,total:e.total},on:{"update:limit":function(t){return e.$set(e.queryParams,"pageSize",t)},"update:page":function(t){return e.$set(e.queryParams,"pageNum",t)},pagination:e.getList}})],1)],1),a("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"},{name:"dialogDragWidth",rawName:"v-dialogDragWidth"}],attrs:{"close-on-click-modal":!1,"modal-append-to-body":!1,title:e.title,visible:e.open,"append-to-body":"",height:"60%",width:"60%"},on:{"update:visible":function(t){e.open=t},close:function(t){e.showDetail=!1}}},[e.open?a("el-form",{ref:"form",staticClass:"edit",attrs:{model:e.form,rules:e.rules,"label-width":"80px"}},[a("el-row",{attrs:{gutter:12}},[a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"银行流水",prop:"voucherNo"}},[a("el-input",{staticClass:"disable-form",attrs:{value:e.form.bankRecordNo,disabled:"",placeholder:"银行流水"}})],1)],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"收支标志",prop:"isRecievingOrPaying"}},[a("treeselect",{ref:"treeSelect",class:e.isLocked||e.isBankSlipConfirmed?"disable-form":"",attrs:{"auto-focus":!0,"disable-branch-nodes":!0,"disable-fuzzy-matching":!0,disabled:e.isLocked||e.isBankSlipConfirmed,"flatten-search-results":!0,normalizer:e.isRecievingOrPayingNormalizer,options:[{label:"实收",value:"0"},{label:"实付",value:"1"}],"show-count":!0,placeholder:"选择收付信息"},on:{select:function(t){e.form.isRecievingOrPaying=t.value}},model:{value:e.form.isRecievingOrPaying,callback:function(t){e.$set(e.form,"isRecievingOrPaying",t)},expression:"form.isRecievingOrPaying"}})],1)],1),a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"结算公司",prop:"clearingCompanyId"}},[a("company-select",{class:e.isLocked||e.isBankSlipConfirmed?"disable-form":"",attrs:{disabled:e.isLocked||e.isBankSlipConfirmed,"load-options":e.companyList,multiple:!1,"no-parent":!0,pass:e.form.clearingCompanyId,placeholder:""},on:{returnData:function(t){return e.selectCompany(t)}}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"费用描述",prop:"voucherNo"}},[a("el-row",[a("el-col",{attrs:{span:10}},[a("tree-select",{class:e.isLocked?"disable-form":"",staticStyle:{width:"100%"},attrs:{disabled:e.isLocked,pass:e.form.chargeTypeId,placeholder:"费用类型",type:"chargeType"},on:{return:function(t){e.form.chargeTypeId=t}}})],1),a("el-col",{attrs:{span:14}},[a("el-input",{class:e.isLocked?"disable-form":"",attrs:{disabled:e.isLocked,placeholder:"费用描述"},model:{value:e.form.chargeDescription,callback:function(t){e.$set(e.form,"chargeDescription",t)},expression:"form.chargeDescription"}})],1)],1)],1)],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"银行账户",prop:"bankAccountCode"}},[a("tree-select",{class:e.isLocked||e.isBankSlipConfirmed?"disable-form":"",attrs:{disabled:e.isLocked||e.isBankSlipConfirmed,flat:!1,multiple:!1,pass:e.form.bankAccountCode,placeholder:"银行账户",type:"companyAccount"},on:{return:function(t){e.form.bankAccountCode=t},returnData:e.selectBankAccount}})],1)],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"水单金额",prop:"sqdBillRecievedAmount"}},[a("el-row",[a("el-col",{attrs:{span:10}},[a("tree-select",{class:e.isBankSlipConfirmed?"disable-form":"",staticStyle:{width:"100%"},attrs:{disabled:e.isBankSlipConfirmed,pass:e.form.bankCurrencyCode,placeholder:"币种",type:"currency"},on:{return:function(t){e.form.bankCurrencyCode=t}}})],1),a("el-col",{attrs:{span:14}},[a("el-input",{class:e.isBankSlipConfirmed?"disable-form":"",attrs:{disabled:e.isBankSlipConfirmed,placeholder:"水单金额"},model:{value:e.form.slipAmount,callback:function(t){e.$set(e.form,"slipAmount",t)},expression:"form.slipAmount"}})],1)],1)],1)],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"水单文件",prop:"sqdBillRecievedAmount"}},[a("div",[a("div",{staticStyle:{display:"flex"}},[0==e.form.slipConfirmed?a("el-upload",{ref:"bankSlipUpload",staticClass:"upload-demo",staticStyle:{flex:"1"},attrs:{"auto-upload":!1,disabled:!e.form.bankRecordNo,"http-request":e.customHttpRequest,"on-change":e.handleChange,"on-error":e.handleError,"on-success":e.handleSuccess,"show-file-list":!1,action:"xxx"}},[a("el-button",{staticStyle:{color:"rgb(103, 194, 58)"},attrs:{icon:"el-icon-top-right",type:"text"}})],1):e._e(),e.form.slipFile||e.form.slipFile&&e.form.slipConfirmed?a("img-preview",{staticStyle:{flex:"1"},attrs:{scope:{row:{slipFile:e.form.slipFile}}}}):e._e(),e.form.slipFile&&0==e.form.slipConfirmed?a("el-button",{staticStyle:{flex:"1",color:"red"},attrs:{icon:"el-icon-delete",type:"text"},on:{click:function(t){return e.deleteBankSlip(e.form)}}}):e._e()],1)])])],1),a("el-col",{attrs:{span:11}},[a("el-form-item",{attrs:{label:"相关单号",prop:"voucherNo"}},[a("el-input",{class:e.isBankSlipConfirmed?"disable-form":"",attrs:{disabled:e.isBankSlipConfirmed,placeholder:"相关操作单号List"},model:{value:e.form.sqdRaletiveRctList,callback:function(t){e.$set(e.form,"sqdRaletiveRctList",t)},expression:"form.sqdRaletiveRctList"}})],1)],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"水单日期",prop:"sqdBillRecievedAmount"}},[a("el-date-picker",{class:e.isBankSlipConfirmed?"disable-form":"",staticStyle:{width:"100%"},attrs:{disabled:e.isBankSlipConfirmed,clearable:"","default-time":"12:00:00",placeholder:"银行时间",type:"datetime","value-format":"yyyy-MM-dd HH:mm:ss"},model:{value:e.form.slipDate,callback:function(t){e.$set(e.form,"slipDate",t)},expression:"form.slipDate"}})],1)],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:5}},["0"===e.form.isRecievingOrPaying?a("el-form-item",{attrs:{label:"实收金额",prop:"actualBankRecievedAmount"}},[a("el-row",[a("el-col",{attrs:{span:10}},[a("tree-select",{class:e.isLocked||e.isBankSlipConfirmed?"disable-form":"",staticStyle:{width:"100%"},attrs:{disabled:e.isLocked||e.isBankSlipConfirmed,pass:e.form.bankCurrencyCode,placeholder:"币种",type:"currency"},on:{return:function(t){e.form.bankCurrencyCode=t}}})],1),a("el-col",{attrs:{span:14}},[a("el-input",{class:e.isLocked?"disable-form":"",attrs:{disabled:e.isLocked,placeholder:"实收金额"},model:{value:e.form.actualBankRecievedAmount,callback:function(t){e.$set(e.form,"actualBankRecievedAmount",t)},expression:"form.actualBankRecievedAmount"}})],1)],1)],1):e._e(),"1"===e.form.isRecievingOrPaying?a("el-form-item",{attrs:{label:"实付金额",prop:"actualBankPaidAmount"}},[a("el-row",[a("el-col",{attrs:{span:10}},[a("tree-select",{class:e.isLocked?"disable-form":"",staticStyle:{width:"100%"},attrs:{disabled:e.isLocked,pass:e.form.bankCurrencyCode,placeholder:"币种",type:"currency"},on:{return:function(t){e.form.bankCurrencyCode=t}}})],1),a("el-col",{attrs:{span:14}},[a("el-input",{class:e.isLocked?"disable-form":"",attrs:{disabled:e.isLocked,placeholder:"实付金额"},model:{value:e.form.actualBankPaidAmount,callback:function(t){e.$set(e.form,"actualBankPaidAmount",t)},expression:"form.actualBankPaidAmount"}})],1)],1)],1):e._e()],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"结算方式",prop:"paymentTypeCode"}},[a("tree-select",{class:e.isLocked?"disable-form":"",attrs:{disabled:e.isLocked,flat:!1,multiple:!1,pass:e.form.paymentTypeCode,placeholder:"结算方式",type:"paymentChannelsCode"},on:{return:function(t){e.form.paymentTypeCode=t}}})],1)],1),a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"凭证号",prop:"voucherNo"}},[a("el-input",{class:e.isLocked?"disable-form":"",attrs:{disabled:e.isLocked,placeholder:"凭证号"},model:{value:e.form.voucherNo,callback:function(t){e.$set(e.form,"voucherNo",t)},expression:"form.voucherNo"}})],1)],1),a("el-col",{attrs:{span:6}},[a("el-form-item",{attrs:{label:"银行备注",prop:"bankRecordRemark"}},[a("el-input",{class:e.isLocked?"disable-form":"",attrs:{disabled:e.isLocked,placeholder:"银行备注"},model:{value:e.form.bankRecordRemark,callback:function(t){e.$set(e.form,"bankRecordRemark",t)},expression:"form.bankRecordRemark"}})],1)],1),a("el-col",{attrs:{span:4}},[a("el-form-item",{attrs:{label:"银行时间",prop:"bankRecordTime"}},[a("el-date-picker",{class:e.isLocked?"disable-form":"",staticStyle:{width:"100%"},attrs:{disabled:e.isLocked,clearable:"","default-time":"12:00:00",placeholder:"银行时间",type:"datetime","value-format":"yyyy-MM-dd HH:mm:ss"},model:{value:e.form.bankRecordTime,callback:function(t){e.$set(e.form,"bankRecordTime",t)},expression:"form.bankRecordTime"}})],1)],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:5}},["0"===e.form.isRecievingOrPaying?a("el-form-item",{attrs:{label:"手续费",prop:"bankRecievedExchangeLost"}},[a("el-input",{attrs:{placeholder:"手续费"},model:{value:e.form.bankRecievedHandlingFee,callback:function(t){e.$set(e.form,"bankRecievedHandlingFee",t)},expression:"form.bankRecievedHandlingFee"}})],1):e._e()],1),a("el-col",{attrs:{span:4}},["0"===e.form.isRecievingOrPaying?a("el-form-item",{attrs:{label:"收款记账",prop:"sqdBillRecievedAmount"}},[a("el-input",{class:"disable-form",attrs:{disabled:"",placeholder:"收款记账"},model:{value:e.form.sqdBillRecievedAmount,callback:function(t){e.$set(e.form,"sqdBillRecievedAmount",t)},expression:"form.sqdBillRecievedAmount"}})],1):e._e()],1),a("el-col",{attrs:{span:5}},["0"===e.form.isRecievingOrPaying?a("el-form-item",{attrs:{label:"收账已销",prop:"billRecievedWriteoffAmount"}},[a("el-input",{class:"disable-form",attrs:{disabled:"",placeholder:"收账已销"},model:{value:e.form.billRecievedWriteoffAmount,callback:function(t){e.$set(e.form,"billRecievedWriteoffAmount",t)},expression:"form.billRecievedWriteoffAmount"}})],1):e._e()],1),a("el-col",{attrs:{span:6}},["0"===e.form.isRecievingOrPaying?a("el-form-item",{attrs:{label:"收款损益",prop:"billRecievedWriteoffAmount"}},[a("el-input",{class:e.isLocked?"":"disable-form",attrs:{disabled:!e.isLocked,placeholder:"收款损益"},model:{value:e.form.bankRecievedExchangeLost,callback:function(t){e.$set(e.form,"bankRecievedExchangeLost",t)},expression:"form.bankRecievedExchangeLost"}})],1):e._e()],1),a("el-col",{attrs:{span:4}},["0"===e.form.isRecievingOrPaying?a("el-form-item",{attrs:{disabled:e.isLocked,label:"收账未销",prop:"sqdBillRecievedWriteoffBalance"}},[a("el-row",[a("el-col",{attrs:{span:20}},[a("el-input",{class:"disable-form",attrs:{disabled:"",placeholder:"收账未销"},model:{value:e.form.sqdBillRecievedWriteoffBalance,callback:function(t){e.$set(e.form,"sqdBillRecievedWriteoffBalance",t)},expression:"form.sqdBillRecievedWriteoffBalance"}})],1),a("el-col",{attrs:{span:4}},[a("el-input",{class:"disable-form",attrs:{value:e.form.sqdBillRecievedWriteoffBalance===e.form.sqdBillRecievedAmount?"-":0===e.form.sqdBillRecievedWriteoffBalance?"√":"="}})],1)],1)],1):e._e()],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:5}},["1"===e.form.isRecievingOrPaying?a("el-form-item",{attrs:{label:"手续费",prop:"bankPaidExchangeLost"}},[a("el-input",{attrs:{placeholder:"手续费"},model:{value:e.form.bankPaidHandlingFee,callback:function(t){e.$set(e.form,"bankPaidHandlingFee",t)},expression:"form.bankPaidHandlingFee"}})],1):e._e()],1),a("el-col",{attrs:{span:4}},["1"===e.form.isRecievingOrPaying?a("el-form-item",{attrs:{label:"付款记账",prop:"sqdBillPaidAmount"}},[a("el-input",{staticClass:"disable-form",attrs:{disabled:"",placeholder:"付款记账"},model:{value:e.form.sqdBillPaidAmount,callback:function(t){e.$set(e.form,"sqdBillPaidAmount",t)},expression:"form.sqdBillPaidAmount"}})],1):e._e()],1),a("el-col",{attrs:{span:5}},["1"===e.form.isRecievingOrPaying?a("el-form-item",{attrs:{label:"付账已销",prop:"billPaidWriteoffAmount"}},[a("el-input",{class:"disable-form",attrs:{disabled:"",placeholder:"付账已销"},model:{value:e.form.billPaidWriteoffAmount,callback:function(t){e.$set(e.form,"billPaidWriteoffAmount",t)},expression:"form.billPaidWriteoffAmount"}})],1):e._e()],1),a("el-col",{attrs:{span:6}},["1"===e.form.isRecievingOrPaying?a("el-form-item",{attrs:{label:"付款汇损",prop:"bankPaidExchangeLost"}},[a("el-input",{class:e.isLocked?"":"disable-form",attrs:{disabled:!e.isLocked,placeholder:"付款损益"},model:{value:e.form.bankPaidExchangeLost,callback:function(t){e.$set(e.form,"bankPaidExchangeLost",t)},expression:"form.bankPaidExchangeLost"}})],1):e._e()],1),a("el-col",{attrs:{span:4}},["1"===e.form.isRecievingOrPaying?a("el-form-item",{attrs:{label:"付账未销",prop:"sqdBillPaidWriteoffBalance"}},[a("el-row",[a("el-col",{attrs:{span:20}},[a("el-input",{class:"disable-form",attrs:{disabled:"",placeholder:"付账未销"},model:{value:e.form.sqdBillPaidWriteoffBalance,callback:function(t){e.$set(e.form,"sqdBillPaidWriteoffBalance",t)},expression:"form.sqdBillPaidWriteoffBalance"}})],1),a("el-col",{attrs:{span:4}},[a("el-input",{class:"disable-form",attrs:{value:e.form.sqdBillPaidAmount===e.form.sqdBillPaidWriteoffBalance?"-":0===e.form.sqdBillPaidWriteoffBalance?"√":"="}})],1)],1)],1):e._e()],1)],1),a("el-row",{attrs:{gutter:10}},[a("el-col",{attrs:{span:9}},[a("el-form-item",{attrs:{label:"发票号码",prop:"voucherNo"}},[a("el-input",{class:e.isLocked?"disable-form":"",attrs:{disabled:e.isLocked,placeholder:"发票号码"},model:{value:e.form.invoiceNo,callback:function(t){e.$set(e.form,"invoiceNo",t)},expression:"form.invoiceNo"}})],1)],1),a("el-col",{attrs:{span:5}},[a("el-form-item",{attrs:{label:"所属员工"}},[a("el-select",{class:e.isLocked||e.isBankSlipConfirmed?"disable-form":"",staticStyle:{width:"100%"},attrs:{disabled:e.isLocked||e.isBankSlipConfirmed,filterable:"",placeholder:"选择员工"},model:{value:e.form.sqdRsStaffId,callback:function(t){e.$set(e.form,"sqdRsStaffId",t)},expression:"form.sqdRsStaffId"}},e._l(e.staffList,(function(e){return a("el-option",{key:e.staffId,attrs:{label:e.staffFamilyLocalName+e.staffGivingLocalName+" "+e.staffGivingEnName,value:e.staffId}})})),1)],1)],1),a("el-col",{attrs:{span:1.5}},[a("el-button",{class:e.isLocked?"disable-form":"",staticStyle:{float:"right"},attrs:{disabled:e.isLocked,type:"primary"},on:{click:e.submitForm}},[e._v(e._s("保存")+" ")])],1),a("el-col",{attrs:{span:2.5}},[null!==this.form.bankRecordId?a("el-button",{attrs:{icon:1==e.form.slipConfirmed?"el-icon-check":"",type:"primary"},on:{click:e.updateSlipSatus}},[e._v(" "+e._s(1==e.form.slipConfirmed?"水单已确认":"确认水单")+" ")]):e._e()],1),a("el-col",{attrs:{span:3}},[a("el-tooltip",{attrs:{placement:"top"}},[a("div",{attrs:{slot:"content"},slot:"content"},[1==e.form.isBankRecordLocked?a("div",[a("div",{staticStyle:{float:"right",color:"#b7bbc2","font-size":"12px"}},[e._v(" "+e._s(e.form.verifyId?e.getName(e.form.verifyId):"")+" ")]),a("div",{staticStyle:{float:"right",color:"#b7bbc2","font-size":"12px"}},[e._v(" "+e._s(e.parseTime(e.form.verifyTime,"{y}-{m}-{d}"))+" ")])]):e._e()]),a("el-button",{attrs:{icon:1==e.form.isBankRecordLocked?"el-icon-check":"",type:"primary"},on:{click:e.verify}},[e._v(" "+e._s(1==e.form.isBankRecordLocked?"流水已审核":"审核流水")+" ")])],1)],1),a("el-col",{attrs:{span:3}},[e.searchAble?a("el-button",{staticStyle:{float:"right"},attrs:{disabled:!e.isLocked,loading:e.loadingCharge,type:"primary"},on:{click:function(t){return e.getWriteOffType(e.writeoffType)}}},[e._v(" 调取相关费用明细 ")]):e._e()],1)],1)],1)],1):e._e(),e.showDetail&&"chargeType"===e.writeoffType?a("el-table",{ref:"writeOffChargeTable",staticStyle:{width:"100%"},attrs:{data:e.writeOffList,border:"","max-height":"315px",size:"mini"},on:{"selection-change":e.handleSelectionChange}},[a("el-table-column",{attrs:{type:"index",width:"20"}}),a("el-table-column",{attrs:{selectable:e.checkSelectable,type:"selection",width:"35"}}),a("el-table-column",{attrs:{align:"center",label:"审核",prop:"sqdRctNo",width:"30"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s("1"===t.row.isAccountConfirmed?"√":"-")+" ")]}}],null,!1,**********)}),a("el-table-column",{attrs:{label:"Rct号",prop:"sqdRctNo",width:"150",sortable:""}}),a("el-table-column",{attrs:{label:"费用名称",prop:"chargeName",width:"80"}}),a("el-table-column",{attrs:{label:"备注",prop:"address"}}),a("el-table-column",{attrs:{label:"收付标志",prop:"dnCurrencyCode",width:"100",sortable:""},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(1==t.row.isRecievingOrPaying?"应付"+t.row.dnCurrencyCode:"应收"+t.row.dnCurrencyCode)+" ")]}}],null,!1,**********)}),a("el-table-column",{attrs:{align:"right",label:"应收/付金额",prop:"subtotal"}}),a("el-table-column",{attrs:{align:"right",label:"销账余额"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(t.row.sqdDnCurrencyBalance)+" ")]}}],null,!1,**********)}),a("el-table-column",{attrs:{align:"right",label:"本次拟销账金额",prop:"sqdDnCurrencyBalance"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(t.row.writeoffFromDnBalance)+" ")]}}],null,!1,171030997)}),a("el-table-column",{attrs:{align:"right",label:"汇率展示",prop:"dnCurrencyCode"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-input",{on:{blur:function(a){return e.validateExchangeRate(t.row)},input:function(a){return e.handleExchangeRateInput(t.row,a)}},model:{value:t.row.exchangeRateShow,callback:function(a){e.$set(t.row,"exchangeRateShow",a)},expression:"scope.row.exchangeRateShow"}})]}}],null,!1,**********)}),a("el-table-column",{attrs:{align:"right",label:"折算记账金额",prop:"dnCurrencyCode"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-input-number",{staticStyle:{width:"100%"},attrs:{controls:!1,min:0,precision:2,step:.01,autocomplete:"off"},on:{change:function(a){return e.handleWriteoffChange(t.row,a)}},model:{value:t.row.writeoffFromBankBalance,callback:function(a){e.$set(t.row,"writeoffFromBankBalance",a)},expression:"scope.row.writeoffFromBankBalance"}})]}}],null,!1,*********)}),a("el-table-column",{attrs:{label:"销账人"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(e.getName(t.row.midChargeBankWriteoff.writeoffStaffId))+" ")]}}],null,!1,**********)}),a("el-table-column",{attrs:{label:"销账时间"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(t.row.midChargeBankWriteoff.writeoffTime)+" ")]}}],null,!1,**********)}),a("el-table-column",{attrs:{align:"center",label:"销账状态",width:"50"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(0==t.row.sqdDnCurrencyBalance?"√":t.row.sqdDnCurrencyBalance>0?"=":"-")+" ")]}}],null,!1,**********)})],1):e._e(),a("el-table",{directives:[{name:"show",rawName:"v-show",value:e.showDetail&&"reimburseType"===e.writeoffType,expression:"showDetail && writeoffType==='reimburseType'"}],ref:"writeOffReimburseTable",staticStyle:{width:"100%"},attrs:{data:e.reimburseList,border:"","max-height":"315px",size:"mini"},on:{"selection-change":e.handleSelectionChange}},[a("el-table-column",{attrs:{align:"center",type:"selection",width:"28"}}),a("el-table-column",{attrs:{align:"center",label:"ID",prop:"reimburseId",width:"38"}}),a("el-table-column",{attrs:{align:"center",label:"报销人",prop:"staffName",width:"68"}}),a("el-table-column",{attrs:{align:"center",label:"费用类型",prop:"chargeTypeName","show-tooltip-when-overflow":"",width:"88"}}),a("el-table-column",{attrs:{align:"center",label:"报销概要",prop:"reimburseTitle","show-tooltip-when-overflow":"",width:"68"}}),a("el-table-column",{attrs:{align:"center",label:"报销详情",prop:"reimburseContent","show-tooltip-when-overflow":""}}),a("el-table-column",{attrs:{align:"center",label:"参与人员",prop:"reimburseParticipation","show-tooltip-when-overflow":"",width:"170"}}),a("el-table-column",{attrs:{align:"center",label:"报销金额",prop:"reimbursePrice",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("h1",{staticStyle:{margin:"0","font-weight":"bold"}},[e._v(" "+e._s(t.row.reimbursePrice)+" ")])]}}])}),a("el-table-column",{attrs:{align:"center",label:"日期",prop:"happenDate",width:"142"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("h6",{staticStyle:{margin:"0"}},[e._v(e._s("发生日期："+e.parseTime(t.row.happenDate,"{y}-{m}-{d}")))]),a("h6",{staticStyle:{margin:"0"}},[e._v(e._s("申请日期："+e.parseTime(t.row.applyDate,"{y}-{m}-{d}")))])]}}])}),a("el-table-column",{attrs:{align:"center",label:"部门审批",prop:"deptConfirmed",width:"88"},scopedSlots:e._u([{key:"default",fn:function(t){return[1==t.row.deptConfirmed&&null!=t.row.deptReimburseConfirm&&1==t.row.deptReimburseConfirm.reimburseConfirm?a("div",[a("el-tooltip",{attrs:{"open-delay":500,placement:"top"}},[a("div",{attrs:{slot:"content"},slot:"content"},[a("h6",{staticStyle:{margin:"0"}},[e._v(e._s(t.row.deptConfirmedName))]),a("h6",{staticStyle:{margin:"0"}},[e._v(e._s(e.parseTime(t.row.deptConfirmedDate,"{y}-{m}-{d}")))])]),a("div",[e._v(" "+e._s("已审批")+" ")])])],1):e._e(),0==t.row.deptConfirmed&&null!=t.row.deptReimburseConfirm&&0==t.row.deptReimburseConfirm.reimburseConfirm?a("div",[a("el-tooltip",{attrs:{"open-delay":500,placement:"top"}},[a("div",{attrs:{slot:"content"},slot:"content"},[a("h6",{staticStyle:{margin:"0"}},[e._v(e._s(t.row.deptReimburseConfirm.staffName))]),a("h6",{staticStyle:{margin:"0"}},[e._v(" "+e._s(e.parseTime(t.row.deptReimburseConfirm.createDate,"{y}-{m}-{d}")))]),a("h6",{staticStyle:{margin:"0"}},[e._v(e._s(t.row.deptReimburseConfirm.reimburseContent))])]),a("div",[e._v(" "+e._s("已驳回")+" ")])])],1):e._e(),0==t.row.deptConfirmed&&null==t.row.deptReimburseConfirm?a("div",[e._v(" "+e._s("未审批")+" ")]):e._e(),0==t.row.deptConfirmed&&null!=t.row.deptReimburseConfirm&&1==t.row.deptReimburseConfirm.reimburseConfirm?a("div",[e._v(" "+e._s("已驳回")+" ")]):e._e()]}}])}),a("el-table-column",{attrs:{align:"center",label:"人事审批",prop:"hrConfirmed",width:"88"},scopedSlots:e._u([{key:"default",fn:function(t){return[1==t.row.hrConfirmed&&null!=t.row.hrReimburseConfirm&&1==t.row.hrReimburseConfirm.reimburseConfirm?a("div",[a("el-tooltip",{attrs:{"open-delay":500,placement:"top"}},[a("div",{attrs:{slot:"content"},slot:"content"},[a("h6",{staticStyle:{margin:"0"}},[e._v(e._s(t.row.hrConfirmedName))]),a("h6",{staticStyle:{margin:"0"}},[e._v(e._s(e.parseTime(t.row.hrConfirmedDate,"{y}-{m}-{d}")))])]),a("div",[e._v(" "+e._s("已审批")+" ")])])],1):e._e(),0==t.row.hrConfirmed&&null!=t.row.hrReimburseConfirm&&0==t.row.hrReimburseConfirm.reimburseConfirm?a("div",[a("el-tooltip",{attrs:{"open-delay":500,placement:"top"}},[a("div",{attrs:{slot:"content"},slot:"content"},[a("h6",{staticStyle:{margin:"0"}},[e._v(e._s(t.row.hrReimburseConfirm.staffName))]),a("h6",{staticStyle:{margin:"0"}},[e._v(" "+e._s(e.parseTime(t.row.hrReimburseConfirm.createDate,"{y}-{m}-{d}")))]),a("h6",{staticStyle:{margin:"0"}},[e._v(e._s(t.row.hrReimburseConfirm.reimburseContent))])]),a("div",[e._v(" "+e._s("已驳回")+" ")])])],1):e._e(),0==t.row.hrConfirmed&&null==t.row.hrReimburseConfirm?a("div",[e._v(" "+e._s("未审批")+" ")]):e._e(),0==t.row.hrConfirmed&&null!=t.row.hrReimburseConfirm&&1==t.row.hrReimburseConfirm.reimburseConfirm?a("div",[e._v(" "+e._s("已驳回")+" ")]):e._e()]}}])}),a("el-table-column",{attrs:{align:"center",label:"总经办审批",prop:"ceoConfirmed",width:"88"},scopedSlots:e._u([{key:"default",fn:function(t){return[1==t.row.ceoConfirmed&&null!=t.row.ceoReimburseConfirm&&1==t.row.ceoReimburseConfirm.reimburseConfirm?a("div",[a("el-tooltip",{attrs:{"open-delay":500,placement:"top"}},[a("div",{attrs:{slot:"content"},slot:"content"},[a("h6",{staticStyle:{margin:"0"}},[e._v(e._s(t.row.ceoConfirmedName))]),a("h6",{staticStyle:{margin:"0"}},[e._v(e._s(e.parseTime(t.row.ceoConfirmedDate,"{y}-{m}-{d}")))])]),a("div",[e._v(" "+e._s("已审批")+" ")])])],1):e._e(),0==t.row.ceoConfirmed&&null!=t.row.ceoReimburseConfirm&&0==t.row.ceoReimburseConfirm.reimburseConfirm?a("div",[a("el-tooltip",{attrs:{"open-delay":500,placement:"top"}},[a("div",{attrs:{slot:"content"},slot:"content"},[a("h6",{staticStyle:{margin:"0"}},[e._v(e._s(t.row.ceoReimburseConfirm.staffName))]),a("h6",{staticStyle:{margin:"0"}},[e._v(" "+e._s(e.parseTime(t.row.ceoReimburseConfirm.createDate,"{y}-{m}-{d}")))]),a("h6",{staticStyle:{margin:"0"}},[e._v(e._s(t.row.ceoReimburseConfirm.reimburseContent))])]),a("div",[e._v(" "+e._s("已驳回")+" ")])])],1):e._e(),0==t.row.ceoConfirmed&&null==t.row.ceoReimburseConfirm?a("div",[e._v(" "+e._s("未审批")+" ")]):e._e(),0==t.row.ceoConfirmed&&null!=t.row.ceoReimburseConfirm&&1==t.row.ceoReimburseConfirm.reimburseConfirm?a("div",[e._v(" "+e._s("已驳回")+" ")]):e._e()]}}])}),a("el-table-column",{attrs:{align:"center",label:"财务确认",prop:"financeConfirmed",width:"88"},scopedSlots:e._u([{key:"default",fn:function(t){return[1==t.row.financeConfirmed&&null!=t.row.financeReimburseConfirm&&1==t.row.financeReimburseConfirm.reimburseConfirm?a("div",[a("el-tooltip",{attrs:{"open-delay":500,placement:"top"}},[a("div",{attrs:{slot:"content"},slot:"content"},[a("h6",{staticStyle:{margin:"0"}},[e._v(e._s(t.row.financeConfirmedName))]),a("h6",{staticStyle:{margin:"0"}},[e._v(e._s(e.parseTime(t.row.financeConfirmedDate,"{y}-{m}-{d}")))])]),a("div",[e._v(" "+e._s("已审批")+" ")])])],1):e._e(),0==t.row.financeConfirmed&&null!=t.row.financeReimburseConfirm&&0==t.row.financeReimburseConfirm.reimburseConfirm?a("div",[a("el-tooltip",{attrs:{"open-delay":500,placement:"top"}},[a("div",{attrs:{slot:"content"},slot:"content"},[a("h6",{staticStyle:{margin:"0"}},[e._v(e._s(t.row.financeReimburseConfirm.staffName))]),a("h6",{staticStyle:{margin:"0"}},[e._v(" "+e._s(e.parseTime(t.row.financeReimburseConfirm.createDate,"{y}-{m}-{d}")))]),a("h6",{staticStyle:{margin:"0"}},[e._v(e._s(t.row.financeReimburseConfirm.reimburseContent))])]),a("div",[e._v(" "+e._s("已驳回")+" ")])])],1):e._e(),0==t.row.financeConfirmed&&null==t.row.financeReimburseConfirm?a("div",[e._v(" "+e._s("未审批")+" ")]):e._e(),0==t.row.financeConfirmed&&null!=t.row.financeReimburseConfirm&&1==t.row.financeReimburseConfirm.reimburseConfirm?a("div",[e._v(" "+e._s("已驳回")+" ")]):e._e()]}}])}),a("el-table-column",{attrs:{align:"center",label:"单据附件",width:"70"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{staticStyle:{padding:"0"},attrs:{size:e.size,type:"info"},on:{click:function(a){return e.carousel(t.row.reimburseAppendix)}}},[e._v(" 查看图片 ")])]}}])}),a("el-table-column",{attrs:{align:"center",label:"备注",prop:"remark","show-tooltip-when-overflow":"",width:"100"}})],1),e.showDetail?a("div",{staticClass:"total"},[a("div",{staticStyle:{width:"30%"}},[e._v("全部总计: "+e._s(e.totalAmount))]),a("div",{staticStyle:{width:"30%"}},[e._v("已选总计: "+e._s(0==this.form.isRecievingOrPaying?this.form.billRecievedWriteoffAmount:this.form.billPaidWriteoffAmount)+" ")]),a("div",{staticStyle:{width:"30%"}},[a("el-row",[a("el-col",{attrs:{span:12}},[e._v(" 已选余额总计： ")]),a("el-col",{attrs:{span:12}},[a("div",[e._v(" "+e._s("RMB "+e.selectedBalanceAmountRMB)+" ")])]),a("el-col",{attrs:{span:12}},[a("div",[e._v(" "+e._s("USD "+e.selectedBalanceAmountUSD)+" ")])])],1)],1)]):e._e(),e.showDetail?a("div",{staticClass:"table-btn-group"},[a("div",{staticClass:"table-btn-left"},[a("el-button",{attrs:{type:"primary"},on:{click:e.invertSelection}},[e._v("反选")]),a("el-button",{attrs:{type:"primary"},on:{click:e.autoSelection}},[e._v("智选")]),a("el-popover",{attrs:{placement:"bottom",title:"添加对冲费用",trigger:"click",width:"800"}},[a("el-table",{attrs:{data:e.hedgingData},on:{"selection-change":e.handleHedgingSelectionChange}},[a("el-table-column",{attrs:{type:"index",width:"20"}}),a("el-table-column",{attrs:{align:"center",label:"审核",prop:"sqdRctNo",width:"30"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s("1"===t.row.isAccountConfirmed?"√":"-")+" ")]}}],null,!1,**********)}),a("el-table-column",{attrs:{label:"Rct号",prop:"sqdRctNo",width:"150"}}),a("el-table-column",{attrs:{label:"费用名称",prop:"chargeName",width:"80"}}),a("el-table-column",{attrs:{label:"备注",prop:"address"}}),a("el-table-column",{attrs:{label:"收付标志",prop:"dnCurrencyCode",width:"100"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(1==t.row.isRecievingOrPaying?"应付"+t.row.dnCurrencyCode:"应收"+t.row.dnCurrencyCode)+" ")]}}],null,!1,**********)}),a("el-table-column",{attrs:{align:"right",label:"应收/付金额",prop:"subtotal"}}),a("el-table-column",{attrs:{align:"right",label:"销账余额"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(t.row.sqdDnCurrencyBalance)+" ")]}}],null,!1,**********)}),a("el-table-column",{attrs:{align:"center",label:"销账状态",width:"50"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(0==t.row.sqdDnCurrencyBalance?"√":t.row.sqdDnCurrencyBalance>0?"=":"-")+" ")]}}],null,!1,**********)}),a("el-table-column",{attrs:{align:"center",label:"操作",width:"50"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("el-button",{attrs:{type:"text"},on:{click:function(a){return e.handleAddHedging(t.row)}}},[e._v("[添加]")])]}}],null,!1,1630755945)})],1),a("el-button",{attrs:{slot:"reference",type:"primary"},on:{click:e.addHedging},slot:"reference"},[e._v("增加对冲")])],1),a("el-button",{attrs:{type:"primary"},on:{click:e.projectRemove}},[e._v("项目去除")]),a("el-button",{attrs:{type:"primary"},on:{click:e.print}},[e._v("打印")])],1),a("div",{staticClass:"table-btn-right"},[a("el-button",{staticStyle:{float:"right"},attrs:{type:"primary"},on:{click:e.writeOffConfirm}},[e._v("确定销账")])],1)]):e._e(),a("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"},{name:"dialogDragWidth",rawName:"v-dialogDragWidth"}],attrs:{"close-on-click-modal":!1,"modal-append-to-body":!1,visible:e.bankSlipPreview,"append-to-body":"","destroy-on-close":"",height:"50%",width:"50%"},on:{"update:visible":function(t){e.bankSlipPreview=t}}},[a("el-image",{staticStyle:{"margin-top":"20px"},attrs:{src:e.form.slipFile}})],1)],1),a("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"},{name:"dialogDragWidth",rawName:"v-dialogDragWidth"}],attrs:{"close-on-click-modal":!1,"modal-append-to-body":!1,title:e.upload.title,visible:e.upload.open,"append-to-body":"",width:"400px"},on:{"update:visible":function(t){return e.$set(e.upload,"open",t)}}},[a("el-upload",{ref:"upload",attrs:{action:e.upload.url+"?chargeTypeId=2","auto-upload":!1,disabled:e.upload.isUploading,headers:e.upload.headers,limit:1,"on-progress":e.handleFileUploadProgress,"on-success":e.handleFileSuccess,accept:".xlsx, .xls",drag:""}},[a("i",{staticClass:"el-icon-upload"}),a("div",{staticClass:"el-upload__text"},[e._v("将文件拖到此处，或"),a("em",[e._v("点击上传")])]),a("div",{staticClass:"el-upload__tip text-center",attrs:{slot:"tip"},slot:"tip"},[a("span",[e._v("仅允许导入xls、xlsx格式文件。")])])]),a("div",{staticClass:"dialog-footer",attrs:{slot:"footer"},slot:"footer"},[a("el-button",{attrs:{type:"primary"},on:{click:e.submitFileForm}},[e._v("确 定")]),a("el-button",{on:{click:function(t){e.upload.open=!1}}},[e._v("取 消")])],1)],1),a("el-dialog",{directives:[{name:"dialogDrag",rawName:"v-dialogDrag"},{name:"dialogDragWidth",rawName:"v-dialogDragWidth"}],attrs:{"close-on-click-modal":!1,"modal-append-to-body":!1,visible:e.statisticsOpen,"append-to-body":"",height:"40%",width:"40%"},on:{"update:visible":function(t){e.statisticsOpen=t}}},[a("el-row",[a("el-form",{staticClass:"edit",attrs:{"label-width":"50px"}},[a("el-col",{attrs:{span:5}},[a("el-radio-group",{model:{value:e.timeStatistics,callback:function(t){e.timeStatistics=t},expression:"timeStatistics"}},[a("el-radio",{attrs:{label:1}},[e._v("筛选天")]),a("el-radio",{attrs:{label:2}},[e._v("筛选月")]),a("el-radio",{attrs:{label:3}},[e._v("筛选年")]),a("el-radio",{attrs:{label:4}},[e._v("筛选范围")])],1)],1),1===e.timeStatistics?a("el-col",{attrs:{span:8}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{clearable:"",placeholder:"银行流水发生时间",type:"date","value-format":"yyyy-MM-dd"},on:{change:e.statisticsTimeSelect},model:{value:e.queryParams.params.day,callback:function(t){e.$set(e.queryParams.params,"day",t)},expression:"queryParams.params.day"}})],1):e._e(),2===e.timeStatistics?a("el-col",{attrs:{span:8}},[a("el-date-picker",{attrs:{placeholder:"选择月",type:"month","value-format":"yyyy-MM"},on:{change:e.statisticsTimeSelect},model:{value:e.queryParams.params.month,callback:function(t){e.$set(e.queryParams.params,"month",t)},expression:"queryParams.params.month"}})],1):e._e(),3===e.timeStatistics?a("el-col",{attrs:{span:8}},[a("el-date-picker",{staticStyle:{width:"100%"},attrs:{clearable:"",placeholder:"银行流水发生时间",type:"year","value-format":"yyyy"},on:{change:e.statisticsTimeSelect},model:{value:e.queryParams.params.year,callback:function(t){e.$set(e.queryParams.params,"year",t)},expression:"queryParams.params.year"}})],1):e._e(),4===e.timeStatistics?a("el-col",{attrs:{span:8}},[a("el-date-picker",{attrs:{"end-placeholder":"结束日期","range-separator":"至","start-placeholder":"开始日期","default-time":["00:00:00","23:59:59"],type:"daterange","value-format":"yyyy-MM-dd HH:mm:ss"},on:{change:e.statisticsTimeSelect},model:{value:e.queryParams.timeArr,callback:function(t){e.$set(e.queryParams,"timeArr",t)},expression:"queryParams.timeArr"}})],1):e._e()],1)],1),a("el-table",{staticStyle:{width:"100%","margin-top":"20px"},attrs:{data:e.statisticsList,border:""},on:{"row-dblclick":e.dbclick}},[a("el-table-column",{attrs:{label:"银行账户",prop:"bankAccSummary",width:"200"}}),a("el-table-column",{attrs:{label:"账户代码",prop:"bankAccountCode",width:"100"}}),a("el-table-column",{attrs:{label:"账户币种",prop:"bankCurrencyCode",width:"80"}}),a("el-table-column",{attrs:{label:"收入",prop:"totalRecieved"}}),a("el-table-column",{attrs:{label:"支出",prop:"totalPaid"}}),a("el-table-column",{attrs:{label:"余额"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(e.currency(t.row.totalRecieved).subtract(t.row.totalPaid).value)+" ")]}}])})],1),e.statisticsList.length>0?a("div",{staticClass:"statistics-total"},[a("div",{staticClass:"total-item"},[e._v("USD 收入总计: "+e._s(e.formatterCurrency(e.totalRecievedUSD,"$"))+" - 支出总计: "+e._s(e.formatterCurrency(e.totalPaidUSD,"$"))+" = "+e._s(e.formatterCurrency(e.currency(e.totalRecievedUSD).subtract(e.totalPaidUSD).value,"$"))+" ")]),a("div",{staticClass:"total-item"},[e._v("RMB 收入总计: "+e._s(e.formatterCurrency(e.totalRecievedRMB,"¥"))+" - 支出总计: "+e._s(e.formatterCurrency(e.totalPaidRMB,"¥"))+" = "+e._s(e.formatterCurrency(e.currency(e.totalRecievedRMB).subtract(e.totalPaidRMB).value,"¥"))+" ")]),a("div",{staticClass:"total-item"},[e._v("RMB 折算总计: "+e._s(e.formatterCurrency(e.currency(e.totalRecievedUSD).subtract(e.totalPaidUSD).multiply(e.exchangeRate).add(e.currency(e.totalRecievedRMB).subtract(e.totalPaidRMB)).value,"¥"))+" ")])]):e._e(),a("h3",[e._v("按付款抬头汇总")]),e.paymentTitleBalances.length>0?a("div",{staticClass:"payment-title-balance"},[a("h3",[e._v("付款抬头余额统计")]),a("el-table",{staticStyle:{width:"100%"},attrs:{data:e.paymentTitleBalances,border:""}},[a("el-table-column",{attrs:{label:"付款抬头",prop:"titleCode"}}),a("el-table-column",{attrs:{label:"人民币(RMB)"}},[a("el-table-column",{attrs:{label:"收入",width:"110"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(t.row.RMB.received.toFixed(2))+" ")]}}],null,!1,2479872625)}),a("el-table-column",{attrs:{label:"支出",width:"110"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(t.row.RMB.paid.toFixed(2))+" ")]}}],null,!1,3390371330)}),a("el-table-column",{attrs:{label:"余额",width:"110"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",{class:{positive:t.row.RMB.balance>0,negative:t.row.RMB.balance<0}},[e._v(" "+e._s(t.row.RMB.balance.toFixed(2))+" ")])]}}],null,!1,4153073693)})],1),a("el-table-column",{attrs:{label:"美元(USD)"}},[a("el-table-column",{attrs:{label:"收入",width:"110"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(t.row.USD.received.toFixed(2))+" ")]}}],null,!1,4038001902)}),a("el-table-column",{attrs:{label:"支出",width:"110"},scopedSlots:e._u([{key:"default",fn:function(t){return[e._v(" "+e._s(t.row.USD.paid.toFixed(2))+" ")]}}],null,!1,1661571229)}),a("el-table-column",{attrs:{label:"余额",width:"110"},scopedSlots:e._u([{key:"default",fn:function(t){return[a("span",{class:{positive:t.row.USD.balance>0,negative:t.row.USD.balance<0}},[e._v(" "+e._s(t.row.USD.balance.toFixed(2))+" ")])]}}],null,!1,3045374370)})],1)],1)],1):e._e()],1)],1)},n=[],i=a("b85c"),o=a("5530"),l=a("c7eb"),s=a("1da1"),c=(a("d3b7"),a("159b"),a("d81d"),a("25f0"),a("caad"),a("2532"),a("b680"),a("a9e3"),a("4e82"),a("ac1f"),a("466d"),a("4de4"),a("14d9"),a("4ec9"),a("3ca3"),a("ddb0"),a("a630"),a("b0c0"),a("99af"),a("36dc")),d=a("6e71"),u=a("ca17"),f=a.n(u),m=a("b0b8"),p=a.n(m),h=a("4360"),b=a("72f9"),g=a.n(b),y=a("fff5"),v=a("c1b9"),k=a("fba1"),w=(a("0062"),a("c1df")),R=a.n(w),C=a("b775"),j=a("02c1"),S=a("5f87"),_=a("c2aa"),B=a("bc88"),P={name:"otherBankRecord",components:{ImgPreview:j["default"],Treeselect:f.a,CompanySelect:d["a"]},data:function(){return{writeOffList:[],reimburseList:[],size:this.$store.state.app.size||"mini",salesId:null,belongList:[],staffList:[],showLeft:0,showRight:24,loading:!0,ids:[],single:!0,multiple:!0,showSearch:!1,total:0,bankrecordList:[],title:"",open:!1,queryParams:{params:{},pageNum:1,pageSize:20,isRecievingOrPaying:null,sqdPaymentTitleCode:null,bankAccountCode:null,clearingCompanyId:null,sqdClearingCompanyShortname:null,chargeTypeId:null,chargeDescription:null,bankCurrencyCode:null,actualBankRecievedAmount:null,actualBankPaidAmount:null,bankRecievedHandlingFee:null,bankPaidHandlingFee:null,bankRecievedExchangeLost:null,bankPaidExchangeLost:null,sqdBillRecievedAmount:null,sqdBillPaidAmount:null,billRecievedWriteoffAmount:null,billPaidWriteoffAmount:null,sqdBillRecievedWriteoffBalance:null,sqdBillPaidWriteoffBalance:null,writeoffStatus:null,bankRecordTime:null,paymentTypeCode:null,voucherNo:null,bankRecordRemark:null,bankRecordByStaffId:null,bankRecordUpdateTime:null,isBankRecordLocked:null,isWriteoffLocked:null,sqdChargeIdList:null,sqdRaletiveRctList:null,sqdRaletiveInvoiceList:null,sqdRsStaffId:null,writeoffRemark:null,writeoffStaffId:null,writeoffTime:null,timeArr:null},statisticsList:[],form:{chargeTypeId:null},searchAble:!1,writeoffType:null,rules:{actualBankRecievedAmount:[{required:!0,message:"请输入实收信息",trigger:"blur"}],bankRecordTime:[{required:!0,message:"请输入银行时间",trigger:"blur"}],actualBankPaidAmount:[{required:!0,message:"请输入实付信息",trigger:"blur"}]},statisticsOpen:!1,totalRecievedUSD:0,totalRecievedRMB:0,totalPaidUSD:0,totalPaidRMB:0,exchangeRate:0,paymentTitleBalances:0,timeStatistics:null,add:!1,upload:{open:!1,title:"",isUploading:!1,updateSupport:!1,headers:{Authorization:"Bearer "+Object(S["a"])()},url:"/prod-api/system/bankrecord/importData"},showDetail:!1,selectedCharges:[],totalAmount:null,selectedAmount:null,selectedBalanceAmount:null,selectedBalanceAmountRMB:0,selectedBalanceAmountUSD:0,loadingCharge:!1,staffId:null,alreadyWriteoffList:[],turnBackWriteoffList:[],showCompany:!1,companyList:[],bankSlipPreview:!1,imageFile:null,hedgingData:[],exchangeRateList:[]}},watch:{showSearch:function(e){!0===e?(this.showRight=21,this.showLeft=3):(this.showRight=24,this.showLeft=0)},"form.chargeTypeId":function(e){var t;this.$store.state.data.chargeTypeList.forEach((function(a){a.chargeTypeId==e?t=a:a.children&&a.children.forEach((function(r){r.chargeTypeId==e&&(t=a)}))})),t?2===t.chargeTypeId||2===t.parentId?(this.searchAble=!0,this.writeoffType="chargeType"):4!==t.chargeTypeId&&4!==t.parentId||(this.searchAble=!0,this.writeoffType="reimburseType"):this.searchAble=!1},"form.actualBankRecievedAmount":function(e){this.form.sqdBillRecievedAmount=g()(this.form.actualBankRecievedAmount).add(this.form.bankRecievedHandlingFee).value},"form.bankRecievedHandlingFee":function(e){this.form.sqdBillRecievedAmount=g()(this.form.actualBankRecievedAmount).add(this.form.bankRecievedHandlingFee).value},"form.bankRecievedExchangeLost":function(e){this.form.sqdBillRecievedWriteoffBalance=g()(this.form.sqdBillRecievedAmount).subtract(this.form.billRecievedWriteoffAmount).subtract(this.form.bankRecievedExchangeLost).value},"form.actualBankPaidAmount":function(e){this.form.sqdBillPaidAmount=g()(this.form.actualBankPaidAmount).subtract(this.form.bankPaidHandlingFee).value},"form.bankPaidHandlingFee":function(e){this.form.sqdBillPaidAmount=g()(this.form.actualBankPaidAmount).subtract(this.form.bankPaidHandlingFee).value},"form.bankPaidExchangeLost":function(e){this.form.sqdBillPaidWriteoffBalance=g()(this.form.sqdBillPaidAmount).subtract(this.form.billPaidWriteoffAmount).subtract(this.form.bankPaidExchangeLost).value},"form.sqdBillRecievedAmount":function(e){this.form.sqdBillRecievedWriteoffBalance=g()(this.form.sqdBillRecievedAmount).subtract(this.form.billRecievedWriteoffAmount).subtract(this.form.bankRecievedExchangeLost).value},"form.sqdBillPaidAmount":function(e){this.form.sqdBillPaidWriteoffBalance=g()(this.form.sqdBillPaidAmount).subtract(this.form.billPaidWriteoffAmount).subtract(this.form.bankPaidExchangeLost).value},"form.billRecievedWriteoffAmount":function(e){this.form.sqdBillRecievedWriteoffBalance=g()(this.form.sqdBillRecievedAmount).subtract(this.form.billRecievedWriteoffAmount).subtract(this.form.bankRecievedExchangeLost).value},"form.billPaidWriteoffAmount":function(e){this.form.sqdBillPaidWriteoffBalance=g()(this.form.sqdBillPaidAmount).subtract(this.form.billPaidWriteoffAmount).subtract(this.form.bankPaidExchangeLost).value},selectedCharges:{handler:function(e,t){var a=this;this.form.billRecievedWriteoffAmount=0,this.form.billPaidWriteoffAmount=0,this.selectedBalanceAmount=0;var r=0,n=0,i=0,o=0,l=!0;e.forEach((function(e){a.form.isRecievingOrPaying==e.isRecievingOrPaying&&(l=!1)})),e.map((function(e){if(e.exchangeRate){var t=e.subtotal.toString(),s=t.includes(".")?t.split(".")[1].length:0,c=g()(e.writeoffFromBankBalance).divide(e.exchangeRate).value;e.writeoffFromDnBalance=Number(c).toFixed(s)}a.form.isRecievingOrPaying==e.isRecievingOrPaying||l?(r=g()(e.writeoffFromBankBalance,{precision:4}).add(r).value,n=g()(e.writeoffFromBankBalance,{precision:4}).add(n).value,"RMB"===e.dnCurrencyCode?o=g()(o,{precision:4}).add(e.sqdDnCurrencyBalance).value:i=g()(i,{precision:4}).add(e.sqdDnCurrencyBalance).value):(r=g()(r,{precision:4}).subtract(e.writeoffFromBankBalance).value,n=g()(n,{precision:4}).subtract(e.writeoffFromBankBalance).value,"RMB"===e.dnCurrencyCode?o=g()(o,{precision:4}).subtract(e.sqdDnCurrencyBalance).value:i=g()(i,{precision:4}).subtract(e.sqdDnCurrencyBalance).value),a.selectedAmount=null,g()(e.dnUnitRate).multiply(e.dnAmount).value===e.writeoffFromDnBalance?e.writeoffStatus="0":e.writeoffFromDnBalance>0?e.writeoffStatus="1":e.writeoffStatus="-1"})),this.form.billRecievedWriteoffAmount=g()(r,{precision:2}).value,this.form.billPaidWriteoffAmount=g()(n,{precision:2}).value,this.selectedBalanceAmountUSD=g()(i,{precision:2}).value,this.selectedBalanceAmountRMB=g()(o,{precision:2}).value},deep:!0}},created:function(){this.getList(),h["a"].dispatch("getChargeTypeList"),(0==this.$store.state.data.exchangeRateList.length||this.$store.state.data.redisList.exchangeRateList)&&h["a"].dispatch("getExchangeRate")},mounted:function(){var e=this;return Object(s["a"])(Object(l["a"])().mark((function t(){return Object(l["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return t.next=2,Object(v["f"])();case 2:e.exchangeRateList=t.sent;case 3:case"end":return t.stop()}}),t)})))()},beforeMount:function(){this.loadStaff()},methods:{carousel:function(e){null!=e?(this.openCarousel=!0,this.pics=e):this.$message.info("没有附件")},getWriteOffType:function(e){switch(e){case"chargeType":this.getCompanyCharges();break;case"reimburseType":this.getReimburseCharges();break}},getReimburseCharges:function(){var e=this;this.reimburseList=[],this.loadingCharge=!0,Object(B["e"])({staffId:this.form.sqdRsStaffId,sqdRaletiveRctList:this.form.sqdRaletiveRctList,bankRecordId:this.form.bankRecordId}).then((function(t){e.reimburseList=t.rows,e.loadingCharge=!1,e.showDetail=!0,e.totalAmount=0,e.$nextTick((function(){e.reimburseList.map((function(t){e.totalAmount=g()(t.reimbursePrice).add(e.totalAmount).value,t.bankRecordId===e.form.bankRecordId&&e.$refs.writeOffReimburseTable.toggleRowSelection(t,!0)}))})),e.reimburseList.sort((function(t,a){return t.bankRecordId===e.form.bankRecordId&&a.bankRecordId!==e.form.bankRecordId?-1:t.bankRecordId!==e.form.bankRecordId&&a.bankRecordId===e.form.bankRecordId?1:0}))}))},handleExchangeRateInput:function(e,t){e.exchangeRateShow=t},isNumeric:function(e){return!isNaN(Number(e))},validateExchangeRate:function(e){var t=.01,a=100,r=e.exchangeRateShow||"";if(this.isNumeric(r)){var n=parseFloat(e.exchangeRateShow);isNaN(n)||n<t||n>a?(this.$message.error("汇率必须在 ".concat(t," 和 ").concat(a," 之间")),e.exchangeRateShow=e.exchangeRate.toString()):e.exchangeRate=n}else{var i=r.match(/^1\/(\d+(\.\d+)?)$/);if(i){var o=parseFloat(i[1]);o>0&&(e.exchangeRate=(1/o).toFixed(4))}}},deleteBankSlip:function(e){var t=this;return Object(s["a"])(Object(l["a"])().mark((function a(){return Object(l["a"])().wrap((function(a){while(1)switch(a.prev=a.next){case 0:return a.prev=0,a.next=3,Object(c["d"])({url:e.slipFile});case 3:a.next=9;break;case 5:return a.prev=5,a.t0=a["catch"](0),a.next=9,Object(c["h"])({bankRecordId:e.bankRecordId,slipFile:null,isRecievingOrPaying:"pay"===t.type?"1":"0",bankRecordNo:e.bankRecordNo});case 9:return a.next=11,t.getList();case 11:case"end":return a.stop()}}),a,null,[[0,5]])})))()},handleSearch:function(e){switch(e){case"common":this.getList({});break;default:break}},parseTime:k["f"],updateSlipSatus:function(){var e=this;this.clearReceiveOrPay(),1==this.form.slipConfirmed?(this.form.slipConfirmed="0",Object(c["h"])(this.form).then((function(t){e.$message.success("水单取消确认")}))):(this.form.slipConfirmed="1",Object(c["h"])(this.form).then((function(t){e.$message.success("水单确认")})))},writeOffConfirm:function(){var e=this;this.clearReceiveOrPay(),this.form.writeoffTime=R()().format("yyyy-MM-DD"),Object(c["h"])(this.form).then((function(t){e.$modal.msgSuccess("修改成功"),e.open=!1,e.getList()}));var t=this.alreadyWriteoffList.filter((function(t){return!e.selectedCharges.some((function(e){return e.chargeId===t.chargeId}))}));Object(y["k"])({rsChargeList:t});var a=[];this.selectedCharges=this.selectedCharges.map((function(t){t.sqdDnCurrencyBalance=g()(g()(t.dnUnitRate).multiply(t.dnAmount)).subtract(t.writeoffFromDnBalance).value,t.clearingCurrencyCode=e.form.bankCurrencyCode,t.writeoffStatus=0===g()(t.sqdDnCurrencyBalance).value?"1":"0";var r={};return r.chargeId=t.chargeId,r.bankRecordId=e.form.bankRecordId,r.writeoffFromDnBalance=t.writeoffFromDnBalance,r.exchangeRateShowing=t.exchangeRateShow,r.writeoffFromBankBalance=t.writeoffFromBankBalance,r.dnBasicRate=t.exchangeRate,a.push(r),t})),Object(y["d"])({rsChargeList:this.selectedCharges,midChargeBankWriteoffList:a}).then((function(t){Object(_["s"])(e.form.sqdRaletiveRctList.split(","))}))},formatterCurrency:function(e,t){return g()(e,{separator:",",symbol:t,precision:0}).format()},invertSelection:function(){},autoSelection:function(){},addHedging:function(){var e=this,t="0"===this.form.isRecievingOrPaying,a=t?"1":"0";Object(y["f"])(Object(o["a"])(Object(o["a"])({},this.form),{},{isRecievingOrPaying:a})).then((function(t){e.hedgingData=t?t.filter((function(t){return!e.writeOffList.some((function(e){return e.chargeId===t.chargeId}))})):[]}))},projectRemove:function(){},print:function(){},currency:g.a,getCompanyCharges:function(){var e=this;return Object(s["a"])(Object(l["a"])().mark((function t(){var a,r,n,o,s,c;return Object(l["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:return e.writeOffList=[],e.alreadyWriteoffList=[],e.loadingCharge=!0,"0"===e.form.isRecievingOrPaying&&0===e.form.sqdBillRecievedWriteoffBalance||"1"===e.form.isRecievingOrPaying&&0===e.form.sqdBillPaidWriteoffBalance?a="ALL":null===e.form.billRecievedWriteoffAmount&&null===e.form.billPaidWriteoffAmount||(a="Part"),t.next=6,Object(y["j"])({clearingCompanyId:e.form.clearingCompanyId,isRecievingOrPaying:e.form.isRecievingOrPaying,bankRecordId:e.form.bankRecordId,writeoffStatus:a,sqdRaletiveRctList:e.form.sqdRaletiveRctList});case 6:if(r=t.sent,!(r&&r.length>0)){t.next=39;break}n=Object(i["a"])(r),t.prev=9,n.s();case 11:if((o=n.n()).done){t.next=29;break}if(s=o.value,e.form.bankCurrencyCode!==s.dnCurrencyCode){t.next=18;break}s.exchangeRateShow=1,s.exchangeRate=1,t.next=27;break;case 18:return t.next=20,e.getExchangeRate(e.form.bankCurrencyCode,s.dnCurrencyCode,"");case 20:if(c=t.sent,c){t.next=25;break}return e.$message.error("系统中没有对应的汇率"),e.loadingCharge=!1,t.abrupt("return");case 25:s.exchangeRate=1===c[0]?Number(c[1]).toFixed(4):Number(e.currencyWithPrecision(1).divide(c[1]).value).toFixed(4),s.exchangeRateShow=1===c[0]?c[1]:"1/"+c[1];case 27:t.next=11;break;case 29:t.next=34;break;case 31:t.prev=31,t.t0=t["catch"](9),n.e(t.t0);case 34:return t.prev=34,n.f(),t.finish(34);case 37:e.writeOffList=r,setTimeout((function(){e.writeOffList.map((function(t){null!==t.midChargeBankWriteoff.midChargeBankId&&(t.writeoffFromDnBalance=t.midChargeBankWriteoff.writeoffFromDnBalance,t.sqdDnCurrencyBalance=t.midChargeBankWriteoff.sqdDnCurrencyBalance,t.writeoffFromBankBalance=t.midChargeBankWriteoff.writeoffFromBankBalance,e.selectedCharges.push(t),e.alreadyWriteoffList.push(t),"1"!==t.isAccountConfirmed&&(t.isAccountConfirmed="1"),e.$nextTick((function(){e.$refs.writeOffChargeTable.toggleRowSelection(t,!0)})))})),e.writeOffList.sort((function(e,t){return"0"===e.isAccountConfirmed&&"1"===t.isAccountConfirmed?1:"1"===e.isAccountConfirmed&&"0"===t.isAccountConfirmed?-1:null===e.midChargeBankWriteoff.midChargeBankId&&null!==t.midChargeBankWriteoff.midChargeBankId?1:null!==e.midChargeBankWriteoff.midChargeBankId&&null===t.midChargeBankWriteoff.midChargeBankId?-1:e.midChargeBankWriteoff.midChargeBankId&&t.midChargeBankWriteoff.midChargeBankId?e.midChargeBankWriteoff.sqdDnCurrencyBalance-t.midChargeBankWriteoff.sqdDnCurrencyBalance:0})),e.$nextTick((function(){e.alreadyWriteoffList.forEach((function(t){e.$refs.writeOffChargeTable.toggleRowSelection(t,!0)}))}))}),0);case 39:e.loadingCharge=!1,e.showDetail=!0;case 41:case"end":return t.stop()}}),t,null,[[9,31,34,37]])})))()},currencyWithPrecision:function(e){var t=arguments.length>1&&void 0!==arguments[1]?arguments[1]:4;return g()(e,{precision:t})},verify:function(){var e=this;1==this.form.isBankRecordLocked?(this.form.isBankRecordLocked=0,this.form.verifyId=null,this.form.verifyTime=null):(this.form.isBankRecordLocked=1,this.form.verifyId=this.$store.state.user.sid,this.form.verifyTime=R()().format("yyyy-MM-DD")),this.clearReceiveOrPay(),Object(c["h"])(this.form).then((function(t){e.$message.success("修改成功")}))},getList:function(){var e=this;this.loading=!0,Object(c["g"])(this.queryParams).then((function(t){e.bankrecordList=t.rows,e.total=t.total,e.loading=!1}))},cancel:function(){this.open=!1,this.reset()},reset:function(){this.form={bankRecordId:null,isRecievingOrPaying:null,sqdPaymentTitleCode:null,bankAccountCode:null,clearingCompanyId:null,sqdClearingCompanyShortname:null,chargeTypeId:null,chargeDescription:null,bankCurrencyCode:null,actualBankRecievedAmount:null,actualBankPaidAmount:null,bankRecievedHandlingFee:null,bankPaidHandlingFee:null,bankRecievedExchangeLost:null,bankPaidExchangeLost:null,sqdBillRecievedAmount:null,sqdBillPaidAmount:null,billRecievedWriteoffAmount:null,billPaidWriteoffAmount:null,sqdBillRecievedWriteoffBalance:null,sqdBillPaidWriteoffBalance:null,writeoffStatus:"0",bankRecordTime:null,paymentTypeCode:null,voucherNo:null,invoiceNo:null,bankRecordRemark:null,bankRecordByStaffId:null,bankRecordUpdateTime:null,isBankRecordLocked:null,isWriteoffLocked:null,sqdChargeIdList:null,sqdRaletiveRctList:null,sqdRaletiveInvoiceList:null,sqdRsStaffId:null,writeoffRemark:null,writeoffStaffId:null,writeoffTime:null,chargeType:"订单"},this.staffId=null,this.selectedBalanceAmount=0,this.resetForm("form")},handleQuery:function(){this.queryParams.pageNum=1,this.getList()},resetQuery:function(){this.resetForm("queryForm"),this.handleQuery()},handleStatusChange:function(e){var t=this,a="0"===e.status?"启用":"停用";this.$confirm('确认要"'+a+"吗？").then((function(){return Object(c["b"])(e.bankRecordId,e.status)})).then((function(){t.$modal.msgSuccess(a+"成功")})).catch((function(){e.status="0"===e.status?"1":"0"}))},handleHedgingSelectionChange:function(e){},handleAddHedging:function(e){var t=this;return Object(s["a"])(Object(l["a"])().mark((function a(){var r,n;return Object(l["a"])().wrap((function(a){while(1)switch(a.prev=a.next){case 0:if(r=Object(o["a"])({},e),t.form.bankCurrencyCode!==r.dnCurrencyCode){a.next=6;break}r.exchangeRateShow="1",r.exchangeRate=1,a.next=14;break;case 6:return a.next=8,t.getExchangeRate(t.form.bankCurrencyCode,r.dnCurrencyCode,"");case 8:if(n=a.sent,n){a.next=12;break}return t.$message.error("系统中没有对应的汇率"),a.abrupt("return");case 12:r.exchangeRate=1===n[0]?Number(n[1]).toFixed(4):Number(t.currencyWithPrecision(1).divide(n[1]).value).toFixed(4),r.exchangeRateShow=1===n[0]?n[1].toString():"1/"+n[1];case 14:r.writeoffFromDnBalance=g()(r.sqdDnCurrencyBalance).value,r.writeoffFromBankBalance=g()(r.sqdDnCurrencyBalance).multiply(r.exchangeRate).value,r.midChargeBankWriteoff||(r.midChargeBankWriteoff={midChargeBankId:null,writeoffStaffId:null,writeoffTime:null}),t.writeOffList.push(r),t.hedgingData=t.hedgingData.filter((function(e){return e.chargeId!==r.chargeId}));case 19:case"end":return a.stop()}}),a)})))()},handleSelectionChange:function(e){var t=this;this.single=1!==e.length,this.multiple=!e.length,this.selectedAmount=0,this.turnBackWriteoffList=[],e.map((function(e){-1===t.selectedCharges.indexOf(e)&&(null!==e.sqdDnCurrencyBalance&&e.sqdDnCurrencyBalance>0?(e.writeoffFromDnBalance=g()(e.sqdDnCurrencyBalance).value,e.writeoffFromBankBalance=g()(e.sqdDnCurrencyBalance).multiply(e.exchangeRate).value):(e.writeoffFromDnBalance=g()(e.dnUnitRate).multiply(e.dnAmount).value,e.writeoffFromBankBalance=g()(g()(e.dnUnitRate).multiply(e.dnAmount)).multiply(e.exchangeRate).value))})),this.selectedCharges.map((function(a){-1===e.indexOf(a)&&(a.writeoffFromDnBalance=0,a.writeoffFromBankBalance=0,a.sqdDnCurrencyBalanceShow=g()(a.sqdDnCurrencyBalance).value,-1!==t.alreadyWriteoffList.indexOf(a)&&t.turnBackWriteoffList.push(a))})),this.selectedCharges=e},dbclick:function(e){var t=this;this.queryParams.bankAccountCode=e.bankAccountCode,Object(c["g"])(this.queryParams).then((function(e){t.bankrecordList=e.rows,t.total=e.total,t.loading=!1,t.statisticsOpen=!1}))},statisticsTimeSelect:function(){var e={};switch(this.timeStatistics){case 1:e.day=this.queryParams.params.day,this.searchAccountFundStatistics({params:e});break;case 2:e.month=this.queryParams.params.month,this.searchAccountFundStatistics({params:e});break;case 3:e.year=this.queryParams.params.year,this.searchAccountFundStatistics({params:e});break;case 4:e.startTime=this.queryParams.timeArr[0]?this.queryParams.timeArr[0]:null,e.endTime=this.queryParams.timeArr[1]?this.queryParams.timeArr[1]:null,this.searchAccountFundStatistics({params:e});break}},accountFundStatistics:function(){this.statisticsOpen=!0,this.searchAccountFundStatistics()},searchAccountFundStatistics:function(e){var t=this;e||(e={}),Object(c["e"])(e).then((function(e){var a;t.statisticsList=e.data,t.totalRecievedUSD=0,t.totalRecievedRMB=0,t.totalPaidUSD=0,t.totalPaidRMB=0;var r,n=Object(i["a"])(t.$store.state.data.exchangeRateList);try{for(n.s();!(r=n.n()).done;){var o=r.value;a||"RMB"===o.localCurrency&&"USD"==o.overseaCurrency&&Object(k["f"])(o.validFrom)<=Object(k["f"])(new Date)&&Object(k["f"])(new Date)<=Object(k["f"])(o.validTo)&&(a=g()(o.settleRate).divide(o.base).value)}}catch(l){n.e(l)}finally{n.f()}t.exchangeRate=a,e.data&&(e.data.forEach((function(e){"RMB"===e.bankCurrencyCode&&(t.totalRecievedRMB+=e.totalRecieved,t.totalPaidRMB+=e.totalPaid),"USD"===e.bankCurrencyCode&&(t.totalRecievedUSD+=e.totalRecieved,t.totalPaidUSD+=e.totalPaid)})),t.paymentTitleBalances=t.calculatePaymentTitleBalances())}))},calculatePaymentTitleBalances:function(){if(!this.statisticsList||0===this.statisticsList.length)return[];var e=new Map;return this.statisticsList.forEach((function(t){var a=t.sqdPaymentTitleCode||"未知",r=t.bankCurrencyCode||"未知",n=Number(t.totalRecieved)||0,i=Number(t.totalPaid)||0;e.has(a)||e.set(a,{titleCode:a,titleName:t.sqdPaymentTitleName||"未知抬头",RMB:{received:0,paid:0,balance:0},USD:{received:0,paid:0,balance:0}});var o=e.get(a);"RMB"===r?(o.RMB.received+=n,o.RMB.paid+=i,o.RMB.balance=o.RMB.received-o.RMB.paid):"USD"===r&&(o.USD.received+=n,o.USD.paid+=i,o.USD.balance=o.USD.received-o.USD.paid)})),Array.from(e.values())},handleImport:function(){this.upload.title="用户导入",this.upload.open=!0},handleFileUploadProgress:function(e,t,a){this.upload.isUploading=!0},handleFileSuccess:function(e,t,a){this.upload.open=!1,this.upload.isUploading=!1,this.$refs.upload.clearFiles(),this.$message.info(e.msg),"全部上传成功"!=e.msg&&this.download("system/freight/failList",{},"上传失败列表.xlsx"),this.getList()},submitFileForm:function(){this.$refs.upload.submit()},handleAdd:function(){this.reset(),this.showDetail=!1,this.open=!0,this.title="新建银行流水",this.add=!0,this.form.isRecievingOrPaying="0",this.form.paymentTypeCode="T/T",this.form.chargeType="订单",this.form.chargeTypeId=2},handleUpdate:function(e){var t=this;this.add=!1,this.reset();var a=e.bankRecordId||this.ids;Object(c["f"])(a).then((function(e){t.form=e.data,t.form.chargeType="订单",t.open=!0,t.title="查看银行流水-销账明细",t.companyList=[e.companyList],t.$nextTick((function(){if(0==t.form.writeoffStatus)switch(t.writeoffType){case"chargeType":t.getCompanyCharges();break;case"reimburseType":t.getReimburseCharges();break}}))}))},submitForm:function(){var e=this;this.$refs["form"].validate(function(){var t=Object(s["a"])(Object(l["a"])().mark((function t(a){return Object(l["a"])().wrap((function(t){while(1)switch(t.prev=t.next){case 0:if(e.form.chargeTypeId=2,!a){t.next=8;break}if(!e.imageFile){t.next=6;break}return console.log("upload"),t.next=6,e.uploadImage();case 6:e.clearReceiveOrPay(),null!=e.form.bankRecordId?Object(c["h"])(e.form).then((function(t){e.$modal.msgSuccess("修改成功"),e.getList()})):Object(c["a"])(e.form).then((function(t){e.form=t.data,e.$modal.msgSuccess("新增成功"),e.getList()}));case 8:case"end":return t.stop()}}),t)})));return function(e){return t.apply(this,arguments)}}())},uploadImage:function(){var e=this;return new Promise((function(t,a){e.customHttpRequest({file:e.imageFile,onSuccess:function(a){e.handleSuccess(a),t(a)},onError:function(t){e.handleError(t),a(t)}})}))},clearReceiveOrPay:function(){"0"===this.form.isRecievingOrPaying?(this.form.actualBankPaidAmount=0,this.form.bankPaidHandlingFee=0,this.form.sqdBillPaidAmount=0,this.form.billPaidWriteoffAmount=0,this.form.bankPaidExchangeLost=0,this.form.sqdBillPaidWriteoffBalance=0,this.form.sqdBillRecievedWriteoffBalance===this.form.sqdBillRecievedAmount?this.form.writeoffStatus=-1:0===this.form.sqdBillRecievedWriteoffBalance?this.form.writeoffStatus=0:this.form.writeoffStatus=1):(this.form.actualBankRecievedAmount=0,this.form.bankRecievedHandlingFee=0,this.form.sqdBillRecievedAmount=0,this.form.billRecievedWriteoffAmount=0,this.form.bankRecievedExchangeLost=0,this.form.sqdBillRecievedWriteoffBalance=0,this.form.sqdBillPaidAmount===this.form.sqdBillPaidWriteoffBalance?this.form.writeoffStatus=-1:0===this.form.sqdBillPaidWriteoffBalance?this.form.writeoffStatus=0:this.form.writeoffStatus=1)},handleDelete:function(e){var t=this,a=e.bankRecordId||this.ids;this.$confirm('是否确认删除记录公司账户出入账明细编号为"'+a+'"的数据项？').then((function(){return Object(c["c"])(a)})).then((function(){t.getList(),t.$modal.msgSuccess("删除成功")})).catch((function(){}))},handleExport:function(){this.download("system/bankrecord/export",Object(o["a"])({},this.queryParams),"bankrecord_".concat((new Date).getTime(),".xlsx"))},staffNormalizer:function(e){var t;return e.children&&!e.children.length&&delete e.children,e.staff&&(t=null==e.staff.staffFamilyLocalName&&null==e.staff.staffGivingLocalName?null!=e.role.roleLocalName?e.role.roleLocalName+","+p.a.getFullChars(e.role.roleLocalName):e.dept.deptLocalName+","+p.a.getFullChars(e.dept.deptLocalName):e.staff.staffCode+" "+e.staff.staffFamilyLocalName+e.staff.staffGivingLocalName+" "+e.staff.staffGivingEnName+","+p.a.getFullChars(e.staff.staffFamilyLocalName+e.staff.staffGivingLocalName)),e.roleId?{id:e.roleId,label:t,children:e.children,isDisabled:null==e.staffId&&void 0==e.children}:{id:e.deptId,label:t,children:e.children,isDisabled:null==e.staffId&&void 0==e.children}},loadSales:function(){var e=this;0==this.$store.state.data.salesList.length||this.$store.state.data.redisList.salesList?h["a"].dispatch("getSalesList").then((function(){e.belongList=e.$store.state.data.salesList})):this.belongList=this.$store.state.data.salesList},loadStaff:function(){var e=this;0==this.$store.state.data.allRsStaffList.length||this.$store.state.data.redisList.allRsStaffList?h["a"].dispatch("getAllRsStaffList").then((function(){e.staffList=e.$store.state.data.allRsStaffList})):this.staffList=this.$store.state.data.allRsStaffList},handledbClick:function(e){this.handleUpdate(e)},selectCompany:function(e){this.form.clearingCompanyId=e.companyId,this.form.sqdClearingCompanyShortname=e.companyShortName,this.showCompany=!1},getExchangeRate:function(e,t,a){var r=this;return Object(s["a"])(Object(l["a"])().mark((function a(){var n,o,s,c,d;return Object(l["a"])().wrap((function(a){while(1)switch(a.prev=a.next){case 0:if(!(r.exchangeRateList&&r.exchangeRateList.length>0)){a.next=4;break}o=r.exchangeRateList,a.next=8;break;case 4:return a.next=6,Object(v["f"])();case 6:r.exchangeRateList=a.sent,o=r.exchangeRateList;case 8:if(null===e||null===t){a.next=12;break}s=Object(i["a"])(o.data);try{for(s.s();!(c=s.n()).done;)d=c.value,n=e===d.overseaCurrency&&t===d.localCurrency?[0,g()(d.buyRate).divide(d.base).value]:[1,g()(d.sellRate).divide(d.base).value]}catch(l){s.e(l)}finally{s.f()}return a.abrupt("return",n);case 12:case"end":return a.stop()}}),a)})))()},getBillDataExchangeRate:function(e,t,a){return Object(s["a"])(Object(l["a"])().mark((function r(){var n,o,s,c,d;return Object(l["a"])().wrap((function(r){while(1)switch(r.prev=r.next){case 0:return n=[0,1],r.next=3,Object(v["f"])();case 3:if(o=r.sent,null===e||null===t){r.next=8;break}s=Object(i["a"])(o.data);try{for(s.s();!(c=s.n()).done;)d=c.value,(d.localCurrency==t||d.currency==t)&&(d.currency==e||d.localCurrency==e)&&Object(k["f"])(d.validFrom)<=Object(k["f"])(a)&&Object(k["f"])(a)<=Object(k["f"])(d.validTo)&&(n=e===d.overseaCurrency&&t===d.localCurrency?[0,g()(d.buyRate).divide(d.base).value]:[1,g()(d.sellRate).divide(d.base).value])}catch(l){s.e(l)}finally{s.f()}return r.abrupt("return",n);case 8:case"end":return r.stop()}}),r)})))()},exchangeRateShow:function(e,t){var a=this;return Object(s["a"])(Object(l["a"])().mark((function r(){return Object(l["a"])().wrap((function(r){while(1)switch(r.prev=r.next){case 0:if(e!==t){r.next=2;break}return r.abrupt("return",1);case 2:return r.next=4,a.getExchangeRate(e,t,"");case 4:r.sent;case 5:case"end":return r.stop()}}),r)})))()},getName:function(e){if(!e)return"";if(e){var t=this.$store.state.data.allRsStaffList.filter((function(t){return t.staffId==e}))[0];return t?t.staffFamilyLocalName+t.staffGivingLocalName+t.staffShortName:""}},selectStaff:function(e){this.form.sqdRsStaffId=e.staff.staffId},checkSelectable:function(e){return"1"===e.isAccountConfirmed},handleDialogOpened:function(){var e=this;this.$nextTick((function(){var t=e.$refs.treeSelect.getInputElement();t&&t.focus()}))},isRecievingOrPayingNormalizer:function(e){return{id:e.value,label:e.label}},selectBankAccount:function(e){this.form.sqdPaymentTitleCode=e.sqdBelongToCompanyCode},customHttpRequest:function(e){var t=this,a=new FormData;a.append("file",e.file),Object(C["a"])({url:"/system/bankrecord/uploadImg",method:"post",data:a}).then((function(a){e.onSuccess(a,e.file),t.form.slipFile=a.url})).catch((function(t){e.onError(t)}))},handleChange:function(e,t){var a=e.name.substring(e.name.lastIndexOf(".")),r="".concat(this.form.bankRecordNo).concat(a);this.imageFile=new File([e.raw],r,{type:e.type})},handleSuccess:function(e,t,a){this.form.slipFile=e.url},handleError:function(e,t,a){this.$message.error("Upload failed:",e)},handleWriteoffChange:function(e,t){if(e.writeoffFromBankBalance=Number(t).toFixed(2),e.exchangeRate){var a=e.subtotal.toString(),r=a.includes(".")?a.split(".")[1].length:0,n=g()(t).divide(e.exchangeRate).value;e.writeoffFromDnBalance=Number(n).toFixed(r)}}},computed:{receiveRate:function(){return this.form.actualBankRecievedAmount+this.form.bankRecievedHandlingFee+this.form.bankRecievedExchangeLost},paidRate:function(){return this.form.actualBankPaidAmount+this.form.bankPaidHandlingFee+this.form.bankPaidExchangeLost},isLocked:function(){return 1==this.form.isBankRecordLocked},isBankSlipConfirmed:function(){return 1==this.form.slipConfirmed}}},x=P,q=(a("6bb9"),a("2877")),O=Object(q["a"])(x,r,n,!1,null,"47fbafe8",null);t["default"]=O.exports},"36dc":function(e,t,a){"use strict";a.d(t,"g",(function(){return n})),a.d(t,"f",(function(){return i})),a.d(t,"a",(function(){return o})),a.d(t,"h",(function(){return l})),a.d(t,"c",(function(){return s})),a.d(t,"b",(function(){return c})),a.d(t,"d",(function(){return d})),a.d(t,"e",(function(){return u}));var r=a("b775");function n(e){return Object(r["a"])({url:"/system/bankrecord/list",method:"get",params:e})}function i(e){return Object(r["a"])({url:"/system/bankrecord/"+e,method:"get"})}function o(e){return Object(r["a"])({url:"/system/bankrecord",method:"post",data:e})}function l(e){return Object(r["a"])({url:"/system/bankrecord",method:"put",data:e})}function s(e){return Object(r["a"])({url:"/system/bankrecord/"+e,method:"delete"})}function c(e,t){var a={bankRecordId:e,status:t};return Object(r["a"])({url:"/system/bankrecord/changeStatus",method:"put",data:a})}function d(e){return Object(r["a"])({url:"/system/bankrecord/deleteImg",method:"delete",params:e})}function u(e){return Object(r["a"])({url:"/system/bankrecord/statistics",method:"post",data:e})}},4678:function(e,t,a){var r={"./af":"2bfb","./af.js":"2bfb","./ar":"8e73","./ar-dz":"a356","./ar-dz.js":"a356","./ar-kw":"423e","./ar-kw.js":"423e","./ar-ly":"1cfd","./ar-ly.js":"1cfd","./ar-ma":"0a84","./ar-ma.js":"0a84","./ar-ps":"4c98","./ar-ps.js":"4c98","./ar-sa":"8230","./ar-sa.js":"8230","./ar-tn":"6d83","./ar-tn.js":"6d83","./ar.js":"8e73","./az":"485c","./az.js":"485c","./be":"1fc1","./be.js":"1fc1","./bg":"84aa","./bg.js":"84aa","./bm":"a7fa","./bm.js":"a7fa","./bn":"9043","./bn-bd":"9686","./bn-bd.js":"9686","./bn.js":"9043","./bo":"d26a","./bo.js":"d26a","./br":"6887","./br.js":"6887","./bs":"25548","./bs.js":"25548","./ca":"d716","./ca.js":"d716","./cs":"3c0d","./cs.js":"3c0d","./cv":"03ec","./cv.js":"03ec","./cy":"9797","./cy.js":"9797","./da":"0f14","./da.js":"0f14","./de":"b469","./de-at":"b3eb","./de-at.js":"b3eb","./de-ch":"bb71","./de-ch.js":"bb71","./de.js":"b469","./dv":"598a","./dv.js":"598a","./el":"8d47","./el.js":"8d47","./en-au":"0e6b","./en-au.js":"0e6b","./en-ca":"3886","./en-ca.js":"3886","./en-gb":"39a6","./en-gb.js":"39a6","./en-ie":"e1d3","./en-ie.js":"e1d3","./en-il":"7333","./en-il.js":"7333","./en-in":"ec2e","./en-in.js":"ec2e","./en-nz":"6f50","./en-nz.js":"6f50","./en-sg":"b7e9","./en-sg.js":"b7e9","./eo":"65db","./eo.js":"65db","./es":"898b","./es-do":"0a3c","./es-do.js":"0a3c","./es-mx":"b5b7","./es-mx.js":"b5b7","./es-us":"55c9","./es-us.js":"55c9","./es.js":"898b","./et":"ec18","./et.js":"ec18","./eu":"0ff2","./eu.js":"0ff2","./fa":"8df48","./fa.js":"8df48","./fi":"81e9","./fi.js":"81e9","./fil":"d69a","./fil.js":"d69a","./fo":"0721","./fo.js":"0721","./fr":"9f26","./fr-ca":"d9f8","./fr-ca.js":"d9f8","./fr-ch":"0e49","./fr-ch.js":"0e49","./fr.js":"9f26","./fy":"7118","./fy.js":"7118","./ga":"5120","./ga.js":"5120","./gd":"f6b46","./gd.js":"f6b46","./gl":"8840","./gl.js":"8840","./gom-deva":"aaf2","./gom-deva.js":"aaf2","./gom-latn":"0caa","./gom-latn.js":"0caa","./gu":"e0c5","./gu.js":"e0c5","./he":"c7aa","./he.js":"c7aa","./hi":"dc4d","./hi.js":"dc4d","./hr":"4ba9","./hr.js":"4ba9","./hu":"5b14","./hu.js":"5b14","./hy-am":"d6b6","./hy-am.js":"d6b6","./id":"5038","./id.js":"5038","./is":"0558","./is.js":"0558","./it":"6e98","./it-ch":"6f12","./it-ch.js":"6f12","./it.js":"6e98","./ja":"079e","./ja.js":"079e","./jv":"b540","./jv.js":"b540","./ka":"201b","./ka.js":"201b","./kk":"6d79","./kk.js":"6d79","./km":"e81d","./km.js":"e81d","./kn":"3e92","./kn.js":"3e92","./ko":"22f8","./ko.js":"22f8","./ku":"2421","./ku-kmr":"7558","./ku-kmr.js":"7558","./ku.js":"2421","./ky":"9609","./ky.js":"9609","./lb":"440c","./lb.js":"440c","./lo":"b29d","./lo.js":"b29d","./lt":"26f9","./lt.js":"26f9","./lv":"b97c","./lv.js":"b97c","./me":"293c","./me.js":"293c","./mi":"688b","./mi.js":"688b","./mk":"6909","./mk.js":"6909","./ml":"02fb","./ml.js":"02fb","./mn":"958b","./mn.js":"958b","./mr":"39bd","./mr.js":"39bd","./ms":"ebe4","./ms-my":"6403","./ms-my.js":"6403","./ms.js":"ebe4","./mt":"1b45","./mt.js":"1b45","./my":"8689","./my.js":"8689","./nb":"6ce3","./nb.js":"6ce3","./ne":"3a39","./ne.js":"3a39","./nl":"facd","./nl-be":"db29","./nl-be.js":"db29","./nl.js":"facd","./nn":"b84c","./nn.js":"b84c","./oc-lnc":"167b","./oc-lnc.js":"167b","./pa-in":"f3ff","./pa-in.js":"f3ff","./pl":"8d57","./pl.js":"8d57","./pt":"f260","./pt-br":"d2d4","./pt-br.js":"d2d4","./pt.js":"f260","./ro":"972c","./ro.js":"972c","./ru":"957c","./ru.js":"957c","./sd":"6784","./sd.js":"6784","./se":"ffff","./se.js":"ffff","./si":"eda5","./si.js":"eda5","./sk":"7be6","./sk.js":"7be6","./sl":"8155","./sl.js":"8155","./sq":"c8f3","./sq.js":"c8f3","./sr":"cf1e","./sr-cyrl":"13e9","./sr-cyrl.js":"13e9","./sr.js":"cf1e","./ss":"52bd","./ss.js":"52bd","./sv":"5fbd","./sv.js":"5fbd","./sw":"74dc","./sw.js":"74dc","./ta":"3de5","./ta.js":"3de5","./te":"5cbb","./te.js":"5cbb","./tet":"576c","./tet.js":"576c","./tg":"3b1b","./tg.js":"3b1b","./th":"10e8","./th.js":"10e8","./tk":"5aff","./tk.js":"5aff","./tl-ph":"0f38","./tl-ph.js":"0f38","./tlh":"cf75","./tlh.js":"cf75","./tr":"0e81","./tr.js":"0e81","./tzl":"cf51","./tzl.js":"cf51","./tzm":"c109","./tzm-latn":"b53d","./tzm-latn.js":"b53d","./tzm.js":"c109","./ug-cn":"6117","./ug-cn.js":"6117","./uk":"ada2","./uk.js":"ada2","./ur":"5294","./ur.js":"5294","./uz":"2e8c","./uz-latn":"010e","./uz-latn.js":"010e","./uz.js":"2e8c","./vi":"2921","./vi.js":"2921","./x-pseudo":"fd7e","./x-pseudo.js":"fd7e","./yo":"7f33","./yo.js":"7f33","./zh-cn":"5c3a","./zh-cn.js":"5c3a","./zh-hk":"49ab","./zh-hk.js":"49ab","./zh-mo":"3a6c","./zh-mo.js":"3a6c","./zh-tw":"90ea","./zh-tw.js":"90ea"};function n(e){var t=i(e);return a(t)}function i(e){if(!a.o(r,e)){var t=new Error("Cannot find module '"+e+"'");throw t.code="MODULE_NOT_FOUND",t}return r[e]}n.keys=function(){return Object.keys(r)},n.resolve=i,e.exports=n,n.id="4678"},"6bb9":function(e,t,a){"use strict";a("856d")},"72f9":function(e,t,a){(function(t,a){e.exports=a()})(0,(function(){function e(i,o){if(!(this instanceof e))return new e(i,o);o=Object.assign({},a,o);var l=Math.pow(10,o.precision);this.intValue=i=t(i,o),this.value=i/l,o.increment=o.increment||1/l,o.groups=o.useVedic?n:r,this.s=o,this.p=l}function t(t,a){var r=!(2<arguments.length&&void 0!==arguments[2])||arguments[2],n=a.decimal,i=a.errorOnInvalid,o=a.fromCents,l=Math.pow(10,a.precision),s=t instanceof e;if(s&&o)return t.intValue;if("number"===typeof t||s)n=s?t.value:t;else if("string"===typeof t)i=new RegExp("[^-\\d"+n+"]","g"),n=new RegExp("\\"+n,"g"),n=(n=t.replace(/\((.*)\)/,"-$1").replace(i,"").replace(n,"."))||0;else{if(i)throw Error("Invalid Input");n=0}return o||(n=(n*l).toFixed(4)),r?Math.round(n):n}var a={symbol:"$",separator:",",decimal:".",errorOnInvalid:!1,precision:2,pattern:"!#",negativePattern:"-!#",format:function(e,t){var a=t.pattern,r=t.negativePattern,n=t.symbol,i=t.separator,o=t.decimal;t=t.groups;var l=(""+e).replace(/^-/,"").split("."),s=l[0];return l=l[1],(0<=e.value?a:r).replace("!",n).replace("#",s.replace(t,"$1"+i)+(l?o+l:""))},fromCents:!1},r=/(\d)(?=(\d{3})+\b)/g,n=/(\d)(?=(\d\d)+\d\b)/g;return e.prototype={add:function(a){var r=this.s,n=this.p;return e((this.intValue+t(a,r))/(r.fromCents?1:n),r)},subtract:function(a){var r=this.s,n=this.p;return e((this.intValue-t(a,r))/(r.fromCents?1:n),r)},multiply:function(t){var a=this.s;return e(this.intValue*t/(a.fromCents?1:Math.pow(10,a.precision)),a)},divide:function(a){var r=this.s;return e(this.intValue/t(a,r,!1),r)},distribute:function(t){var a=this.intValue,r=this.p,n=this.s,i=[],o=Math[0<=a?"floor":"ceil"](a/t),l=Math.abs(a-o*t);for(r=n.fromCents?1:r;0!==t;t--){var s=e(o/r,n);0<l--&&(s=s[0<=a?"add":"subtract"](1/r)),i.push(s)}return i},dollars:function(){return~~this.value},cents:function(){return~~(this.intValue%this.p)},format:function(e){var t=this.s;return"function"===typeof e?e(this,t):t.format(this,Object.assign({},t,e))},toString:function(){var e=this.s,t=e.increment;return(Math.round(this.intValue/this.p/t)*t).toFixed(e.precision)},toJSON:function(){return this.value}},e}))},"856d":function(e,t,a){},a159:function(e,t,a){"use strict";a.d(t,"e",(function(){return n})),a.d(t,"d",(function(){return i})),a.d(t,"a",(function(){return o})),a.d(t,"g",(function(){return l})),a.d(t,"c",(function(){return s})),a.d(t,"b",(function(){return c})),a.d(t,"f",(function(){return d}));var r=a("b775");function n(e){return Object(r["a"])({url:"/monitor/job/list",method:"get",params:e})}function i(e){return Object(r["a"])({url:"/monitor/job/"+e,method:"get"})}function o(e){return Object(r["a"])({url:"/monitor/job",method:"post",data:e})}function l(e){return Object(r["a"])({url:"/monitor/job",method:"put",data:e})}function s(e){return Object(r["a"])({url:"/monitor/job/"+e,method:"delete"})}function c(e,t){var a={jobId:e,status:t};return Object(r["a"])({url:"/monitor/job/changeStatus",method:"put",data:a})}function d(e,t){var a={jobId:e,jobGroup:t};return Object(r["a"])({url:"/monitor/job/run",method:"put",data:a})}},bc88:function(e,t,a){"use strict";a.d(t,"d",(function(){return n})),a.d(t,"e",(function(){return i})),a.d(t,"c",(function(){return o})),a.d(t,"a",(function(){return l})),a.d(t,"h",(function(){return s})),a.d(t,"b",(function(){return c})),a.d(t,"f",(function(){return d})),a.d(t,"g",(function(){return u})),a.d(t,"i",(function(){return f}));var r=a("b775");function n(e){return Object(r["a"])({url:"/system/reimburse/list",method:"get",params:e})}function i(e){return Object(r["a"])({url:"/system/reimburse/listReimburse",method:"get",params:e})}function o(e){return Object(r["a"])({url:"/system/reimburse/"+e,method:"get"})}function l(e){return Object(r["a"])({url:"/system/reimburse",method:"post",data:e})}function s(e){return Object(r["a"])({url:"/system/reimburse",method:"put",data:e})}function c(e){return Object(r["a"])({url:"/system/reimburse/"+e,method:"delete"})}function d(e){return Object(r["a"])({url:"/system/reimburse/approval",method:"put",data:e})}function u(e){return Object(r["a"])({url:"/system/reimburse/cancel",method:"put",data:e})}function f(e){return Object(r["a"])({url:"/system/reimburse/writeoff",method:"put",data:e})}},c2aa:function(e,t,a){"use strict";a.d(t,"o",(function(){return n})),a.d(t,"n",(function(){return i})),a.d(t,"p",(function(){return o})),a.d(t,"r",(function(){return l})),a.d(t,"q",(function(){return s})),a.d(t,"i",(function(){return c})),a.d(t,"g",(function(){return d})),a.d(t,"v",(function(){return u})),a.d(t,"w",(function(){return f})),a.d(t,"h",(function(){return m})),a.d(t,"c",(function(){return p})),a.d(t,"a",(function(){return h})),a.d(t,"f",(function(){return b})),a.d(t,"d",(function(){return g})),a.d(t,"e",(function(){return y})),a.d(t,"k",(function(){return v})),a.d(t,"j",(function(){return k})),a.d(t,"m",(function(){return w})),a.d(t,"t",(function(){return R})),a.d(t,"u",(function(){return C})),a.d(t,"l",(function(){return j})),a.d(t,"s",(function(){return S}));var r=a("b775");function n(e){return Object(r["a"])({url:"/system/rct/list",method:"get",params:e})}function i(e){return Object(r["a"])({url:"/system/rct/aggregator",method:"get",params:e})}function o(e){return Object(r["a"])({url:"/system/rct/listVerifyAggregatorList",method:"get",params:e})}function l(e){return Object(r["a"])({url:"/system/rct/op",method:"get",params:e})}function s(e){return Object(r["a"])({url:"/system/rct/listVerifyList",method:"get",params:e})}function c(e){return Object(r["a"])({url:"/system/rct/"+e,method:"get"})}function d(e){return Object(r["a"])({url:"/system/rct",method:"post",data:e})}function u(e){return Object(r["a"])({url:"/system/rct/saveAs",method:"post",data:e})}function f(e){return Object(r["a"])({url:"/system/rct",method:"put",data:e})}function m(e){return Object(r["a"])({url:"/system/rct/"+e,method:"delete"})}function p(e){return Object(r["a"])({url:"/system/rct/saveClientMessage",method:"post",data:e})}function h(e){return Object(r["a"])({url:"/system/rct/saveBasicLogistics",method:"post",data:e})}function b(e){return Object(r["a"])({url:"/system/rct/savePreCarriage",method:"post",data:e})}function g(e){return Object(r["a"])({url:"/system/rct/saveExportDeclaration",method:"post",data:e})}function y(e){return Object(r["a"])({url:"/system/rct/saveImportClearance",method:"post",data:e})}function v(){return Object(r["a"])({url:"/system/rct/mon",method:"get"})}function k(){return Object(r["a"])({url:"/system/rct/CFmon",method:"get"})}function w(){return Object(r["a"])({url:"/system/rct/RSWHMon",method:"get"})}function R(e){return Object(r["a"])({url:"/system/rct/saveAllService",method:"post",data:e})}function C(e){return Object(r["a"])({url:"/system/rct/saveAsAllService",method:"post",data:e})}function j(e){return Object(r["a"])({url:"/system/rct/listRctNoByCompany/"+e,method:"get"})}function S(e){return Object(r["a"])({url:"/system/rct/writeoff",method:"post",data:e})}},fff5:function(e,t,a){"use strict";a.d(t,"i",(function(){return n})),a.d(t,"b",(function(){return i})),a.d(t,"g",(function(){return o})),a.d(t,"a",(function(){return l})),a.d(t,"l",(function(){return s})),a.d(t,"e",(function(){return c})),a.d(t,"c",(function(){return d})),a.d(t,"h",(function(){return u})),a.d(t,"j",(function(){return f})),a.d(t,"f",(function(){return m})),a.d(t,"d",(function(){return p})),a.d(t,"m",(function(){return h})),a.d(t,"k",(function(){return b}));var r=a("b775");function n(e){return Object(r["a"])({url:"/system/rscharge/list",method:"get",params:e})}function i(e){return Object(r["a"])({url:"/system/rscharge/aggregator",method:"post",data:e})}function o(e){return Object(r["a"])({url:"/system/rscharge/"+e,method:"get"})}function l(e){return Object(r["a"])({url:"/system/rscharge",method:"post",data:e})}function s(e){return Object(r["a"])({url:"/system/rscharge",method:"put",data:e})}function c(e){return Object(r["a"])({url:"/system/rscharge/"+e,method:"delete"})}function d(e,t){var a={chargeId:e,status:t};return Object(r["a"])({url:"/system/rscharge/changeStatus",method:"put",data:a})}function u(e){return Object(r["a"])({url:"/system/rscharge/charges",method:"get",params:e})}function f(e){return Object(r["a"])({url:"/system/rscharge/selectList",method:"get",params:e})}function m(e){return Object(r["a"])({url:"/system/rscharge/findHedging",method:"get",params:e})}function p(e){return Object(r["a"])({url:"/system/rscharge/writeoff",method:"post",data:e})}function h(e){return Object(r["a"])({url:"/system/rscharge/verify",method:"post",data:e})}function b(e){return Object(r["a"])({url:"/system/rscharge/turnback",method:"post",data:e})}}}]);