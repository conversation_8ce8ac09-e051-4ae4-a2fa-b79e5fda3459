(window["webpackJsonp"]=window["webpackJsonp"]||[]).push([["chunk-2d0ba12c"],{"363f":function(e,t,o){"use strict";o.r(t);var s=function(){var e=this,t=e.$createElement,o=e._self._c||t;return o("div",[o("el-tooltip",{attrs:{disabled:null==e.scope.row.noticeForSales||e.scope.row.noticeForSales.length<20,placement:"top"}},[o("div",{attrs:{slot:"content"},slot:"content"},[o("h6",{staticStyle:{margin:"0"}},[e._v(e._s(e.scope.row.noticeForSales))])]),o("div",[o("h6",{staticStyle:{margin:"0",overflow:"hidden","text-overflow":"ellipsis","white-space":"nowrap"}},[e._v(" "+e._s(e.scope.row.noticeForSales)+" ")])])])],1)},n=[],a={name:"salesRemark",props:["scope"],data:function(){return{size:this.$store.state.app.size||"mini"}}},i=a,r=o("2877"),l=Object(r["a"])(i,s,n,!1,null,"2edae423",null);t["default"]=l.exports}}]);